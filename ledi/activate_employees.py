
import pandas as pd
import requests
import json
from datetime import datetime

# Load employee data
employee_files = ['/root/workspace/mairpc/ledi/员工列表导出_20250707134237.csv', '/root/workspace/mairpc/ledi/员工列表导出_20250707134254.csv']
employees_to_activate = pd.DataFrame()

for file in employee_files:
    df = pd.read_csv(file)
    employees_to_activate = pd.concat([employees_to_activate, df])

# Load license data
license_file = '/root/workspace/mairpc/ledi/深圳市乐的文化股份有限公司_接口许可账号.xlsx'
licenses_df = pd.read_excel(license_file)

# Filter for usable licenses (not used and not expired)
# Assuming the columns are '激活码', '状态', and '过期时间'
# and that '状态' == '未使用' means the license is available.
usable_licenses = licenses_df[
    (licenses_df['激活状态'] == '未激活') &
    (pd.to_datetime(licenses_df['激活截止时间']) > datetime.now())
]

# Get lists of user IDs and activation codes
user_ids = employees_to_activate['员工账号'].tolist()
activation_codes = usable_licenses['帐号激活码'].tolist()

# API endpoint and headers
url = "https://aliyunqa-business-api.maiscrm.com/v2/weconnect/accounts/65fa8213e5cdb65d7c45a5ae/proxy?access_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************.KB0PatWyfCuTlzSOUsdNGdLca_ocaukMln4g6k0YU1M"
headers = {
    'Content-Type': 'application/json',
    'Cookie': 'acw_tc=ac11000117518662090317453e007991c4d9acb63d141b79881cad9cd13477'
}

# Iterate and activate
for user_id, active_code in zip(user_ids, activation_codes):
    payload = {
        "quncrmAccountId": "67aeecd558665c1206646f76",
        "weconnectAccountId": "65f80c9ee9b13965d173c4f2",
        "method": "POST",
        "path": "cgi-bin/license/active_account",
        "body": {
            "active_code": active_code,
            "corpid": "wpNq_ECAAAJ6f_R6i8m7G0gAxseI3CYQ",
            "userid": user_id.strip(),
        },
        "wechatCPConfig": {
            "useProviderAccessToken": True
        }
    }

    response = requests.post(url, headers=headers, data=json.dumps(payload))

    print(f"Activating user: {user_id} with code: {active_code}")
    print(f"Response: {response.status_code}")
    print(response.json())
    print("-" * 20)
