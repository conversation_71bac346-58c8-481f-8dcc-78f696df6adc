package share

import (
	"context"
	"fmt"
	mairpc "mairpc/core/client"
	"mairpc/core/extension"
	core_util "mairpc/core/util"
	"mairpc/proto/account"
	pb_client "mairpc/proto/client"
	pb_ec "mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	pb_coupon "mairpc/proto/coupon"
	"mairpc/proto/ec/content"
	"mairpc/proto/ec/marketing"
	pb_ec_order "mairpc/proto/ec/order"
	ec_product "mairpc/proto/ec/product"
	"mairpc/proto/ec/setting"
	ec_store "mairpc/proto/ec/store"
	"mairpc/proto/ec/warehouse"
	"mairpc/proto/member"
	pb_product "mairpc/proto/product"
	"mairpc/service/eccampaign/model/bargain"
	optional_package "mairpc/service/eccampaign/model/optionalPackage"
	period_buy "mairpc/service/eccampaign/model/periodBuy"
	"mairpc/service/share/util"
	"strconv"
	"strings"
	"time"
)

const (
	UPDATE_CAMPAIGN_END_AT_TIME_LAYOUT = "2006.01.02"
)

var (
	CampaignWeightMap = map[string]int64{
		period_buy.CAMPAIGN_TYPE_PERIOD_BUY:             1,
		bargain.CAMPAIGN_TYPE_BARGAIN:                   1,
		optional_package.CAMPAIGN_TYPE_OPTIONAL_PACKAGE: 1,
	}
)

type CampaignApplicableProduct struct {
	ProductId string                  `json:"productId"`
	Name      string                  `json:"name"`
	Number    string                  `json:"number"`
	Skus      []CampaignApplicableSku `json:"skus"`
	Type      string                  `json:"type"`
	Picture   string                  `json:"picture"`
}

type CampaignApplicableSku struct {
	Sku                string   `json:"sku"`
	Properties         []string `json:"properties"`
	Price              int64    `json:"price"`
	Stock              int64    `json:"stock"`
	PlusBuyPrice       int64    `json:"plusBuyPrice"`
	DiscountPrice      int64    `json:"discountPrice"`
	PeriodBuyPrice     int64    `json:"periodBuyPrice"`
	BargainPrice       int64    `json:"bargainPrice"`
	RulePrices         []uint64 `json:"rulePrices"`
	PersonalLimitCount uint64   `json:"personalLimitCount"`
}

func GetDeliverySetting(ctx context.Context) (*setting.GetDeliverySettingResponse, error) {
	resp, err := mairpc.Run(
		"EcService.SettingService.GetDeliverySetting",
		ctx,
		&request.EmptyRequest{},
	)
	if err != nil {
		return nil, err
	}
	return resp.(*setting.GetDeliverySettingResponse), nil
}

func GetProductStockSetting(ctx context.Context) (*warehouse.GetProductStockSettingResponse, error) {
	resp, err := mairpc.Run(
		"EcService.WarehouseService.GetProductStockSetting",
		ctx,
		&request.EmptyRequest{},
	)
	if err != nil {
		return nil, err
	}
	return resp.(*warehouse.GetProductStockSettingResponse), nil
}

func GetEcProduct(ctx context.Context, req *ec_product.GetProductRequest) (*ec_product.ProductResponse, error) {
	resp, err := mairpc.Run(
		"EcService.ProductService.GetProduct",
		ctx,
		req,
	)
	if err != nil {
		return nil, err
	}
	return resp.(*ec_product.ProductResponse), nil
}

func ListEcProducts(ctx context.Context, req *pb_ec.ListProductsRequest) (*ec_product.ListProductsResponse, error) {
	resp, err := mairpc.Run(
		"EcService.ProductService.ListProducts",
		ctx,
		req,
	)
	if err != nil {
		return nil, err
	}
	return resp.(*ec_product.ListProductsResponse), nil
}

func GetChannel(ctx context.Context, channelId string) (*account.ChannelDetailResponse, error) {
	resp, err := mairpc.Run(
		"AccountService.GetChannel",
		ctx,
		&account.ChannelDetailRequest{
			ChannelId: channelId,
		},
	)

	if err != nil {
		return nil, err
	}

	return resp.(*account.ChannelDetailResponse), nil
}

func GetMember(ctx context.Context, memberId string) (*member.MemberDetailResponse, error) {
	resp, err := mairpc.Run(
		"MemberService.GetMember",
		ctx,
		&member.MemberDetailRequest{
			Id:                   memberId,
			ShowSystemProperties: true,
		},
	)
	if err != nil {
		return nil, err
	}

	return resp.(*member.MemberDetailResponse), nil
}

func GetMemberGroupByMemberId(ctx context.Context, memberId string) (*member.GetMemberGroupByMemberResponse, error) {
	req := &member.GetMemberGroupByMemberRequest{MemberId: memberId}
	resp, err := mairpc.Run(
		"MemberService.GetMemberGroupByMember",
		ctx,
		req,
	)
	if err != nil {
		return nil, err
	}
	return resp.(*member.GetMemberGroupByMemberResponse), nil
}

func UpdateMember(ctx context.Context, req *member.UpdateMemberRequest) (*member.MemberDetailResponse, error) {
	resp, err := mairpc.Run(
		"MemberService.UpdateMember",
		ctx,
		req,
	)

	if err != nil {
		return nil, err
	}

	return resp.(*member.MemberDetailResponse), nil
}

func Purchase(ctx context.Context, req *pb_ec.PurchaseRequest) (*pb_ec_order.OrderDetail, error) {
	resp, rpcErr := mairpc.Run(
		"EcService.OrderService.Purchase",
		ctx,
		req,
	)
	if rpcErr != nil {
		return nil, rpcErr
	}
	return resp.(*pb_ec_order.OrderDetail), nil
}

func UpdateRemarks(ctx context.Context, req *pb_ec_order.UpdateRemarksRequest) error {
	_, rpcErr := mairpc.Run(
		"EcService.OrderService.UpdateRemarks",
		ctx,
		req,
	)
	if rpcErr != nil {
		return rpcErr
	}
	return nil
}

func ListBriefStores(ctx context.Context, req *ec_store.ListStoresRequest) (*ec_store.ListBriefStoresResponse, error) {
	resp, rpcErr := mairpc.Run(
		"EcService.StoreService.ListBriefStores",
		ctx,
		req,
	)
	if rpcErr != nil {
		return nil, rpcErr
	}
	return resp.(*ec_store.ListBriefStoresResponse), nil
}

func GenerateUniqueCode(codeType, prefix string) (string, error) {
	dateString := time.Now().Format("20060102")
	key := fmt.Sprintf("ec:%s:%s", codeType, dateString)
	number, err := extension.RedisClient.Incr(key)
	if err != nil {
		return "", err
	}

	nString := strconv.Itoa(number)
	code := fmt.Sprintf("%s%s%s%s", prefix, dateString, strings.Repeat("0", 8-len(nString)), util.Encode(number, util.ENCODE_DIGITS))

	ttl, err := extension.RedisClient.Ttl(key)
	if err != nil {
		return "", err
	}

	if ttl < 0 {
		extension.RedisClient.Expire(key, 24*60*60)
	}

	return code, nil
}

func GetOrder(ctx context.Context, req *pb_ec_order.GetOrderRequest) (*pb_ec_order.OrderDetail, error) {
	resp, rpcErr := mairpc.Run(
		"EcService.OrderService.GetOrder",
		ctx,
		req,
	)
	if rpcErr != nil {
		return nil, rpcErr
	}
	return resp.(*pb_ec_order.OrderDetail), nil
}

func GetOrderRefund(ctx context.Context, req *pb_ec_order.GetOrderRefundRequest) (*pb_ec_order.OrderRefundDetail, error) {
	resp, rpcErr := mairpc.Run(
		"EcService.OrderService.GetOrderRefund",
		ctx,
		req,
	)
	if rpcErr != nil {
		return nil, rpcErr
	}
	return resp.(*pb_ec_order.OrderRefundDetail), nil
}

func IsFirstPayment(ctx context.Context, req *request.MemberIdRequest) (*response.BoolResponse, error) {
	resp, err := mairpc.Run(
		"EcService.OrderService.IsFirstPayment",
		ctx,
		req,
	)
	if err != nil {
		return nil, err
	}
	return resp.(*response.BoolResponse), nil
}

func CalculateEcOrderAmount(ctx context.Context, req *pb_ec.CalculateOrderAmountRequest) (*pb_ec.CalculateOrderAmountResponse, error) {
	resp, err := mairpc.Run(
		"EcService.OrderService.CalculateOrderAmount",
		ctx,
		req,
	)
	if err != nil {
		return nil, err
	}
	return resp.(*pb_ec.CalculateOrderAmountResponse), nil
}

func IsSyncStockEnabled(ctx context.Context) bool {
	resp, _ := mairpc.Run(
		"EcService.SettingService.IsSyncStockEnabled",
		ctx,
		&request.EmptyRequest{},
	)
	return resp.(*response.BoolResponse).Value
}

func BatchIncreaseProductSales(ctx context.Context, req *ec_product.BatchIncreaseProductsSalesRequest) error {
	_, err := mairpc.Run(
		"EcService.ProductService.BatchIncreaseProductSales",
		ctx,
		req,
	)
	if err != nil {
		return err
	}
	return nil
}

func CreateWeappPage(ctx context.Context, req *content.CreateWeappPageRequest) (*content.CreateWeappPageResponse, error) {
	resp, rpcErr := mairpc.Run(
		"EcService.ContentService.CreateWeappPage",
		ctx,
		req,
	)
	if rpcErr != nil {
		return nil, rpcErr
	}
	return resp.(*content.CreateWeappPageResponse), nil
}

func DeleteWeappPage(ctx context.Context, req *content.DeleteWeappPageRequest) error {
	_, rpcErr := mairpc.Run(
		"EcService.ContentService.DeleteWeappPage",
		ctx,
		req,
	)
	if rpcErr != nil {
		return rpcErr
	}
	return nil
}

func ListCampaignReservations(ctx context.Context, req *marketing.ListCampaignReservationsRequest) (*marketing.ListCampaignReservationsResponse, error) {
	resp, rpcErr := mairpc.Run(
		"EcService.MarketingService.ListCampaignReservations",
		ctx,
		req,
	)
	if rpcErr != nil {
		return nil, rpcErr
	}
	return resp.(*marketing.ListCampaignReservationsResponse), nil
}

func MarkCampaignReservationReminded(ctx context.Context, req *marketing.MarkCampaignReservationRemindedRequest) error {
	_, rpcErr := mairpc.Run(
		"EcService.MarketingService.MarkCampaignReservationReminded",
		ctx,
		req,
	)
	if rpcErr != nil {
		return rpcErr
	}
	return nil
}

func UpdateCampaignProduct(ctx context.Context, req *ec_product.UpdateCampaignProductRequest) error {
	_, rpcErr := mairpc.Run(
		"EcService.ProductService.UpdateCampaignProduct",
		ctx,
		req,
	)
	if rpcErr != nil {
		return rpcErr
	}
	return nil
}

func RefundEcOrder(ctx context.Context, req *pb_ec_order.RefundOrderRequest) error {
	_, rpcErr := mairpc.Run(
		"EcService.OrderService.RefundOrder",
		ctx,
		req,
	)
	if rpcErr != nil {
		return rpcErr
	}
	return nil
}

func ListEcOrders(ctx context.Context, req *pb_ec_order.ListOrdersRequest) (*pb_ec_order.ListOrdersResponse, error) {
	resp, rpcErr := mairpc.Run(
		"EcService.OrderService.ListOrders",
		ctx,
		req,
	)
	if rpcErr != nil {
		return nil, rpcErr
	}
	return resp.(*pb_ec_order.ListOrdersResponse), nil
}

func IssueCoupon(ctx context.Context, req *pb_coupon.IssueCouponRequest) (*pb_coupon.IssueCouponResponse, error) {
	resp, rpcErr := mairpc.Run(
		"CouponService.IssueCoupon",
		ctx,
		req,
	)
	if rpcErr != nil {
		return nil, rpcErr
	}
	return resp.(*pb_coupon.IssueCouponResponse), nil
}

func CheckCampaignConflict(ctx context.Context, req *ec_product.CheckCampaignConflictRequest) (*ec_product.CheckCampaignConflictResponse, error) {
	resp, rpcErr := mairpc.Run(
		"EcService.ProductService.CheckCampaignConflict",
		ctx,
		req,
	)
	if rpcErr != nil {
		return nil, rpcErr
	}
	return resp.(*ec_product.CheckCampaignConflictResponse), nil
}

func BatchUpdateMemberProperty(ctx context.Context, req *member.BatchUpdateMemberPropertyRequest) error {
	_, err := mairpc.Run(
		"MemberService.BatchUpdateMemberProperty",
		ctx,
		req,
	)
	if err != nil {
		return err
	}
	return nil
}

func ListProductsByStore(ctx context.Context, req *ec_product.GetProductsRequest) (*ec_product.ListProductsByStoreResponse, error) {
	resp, rpcErr := mairpc.Run(
		"EcService.ProductService.ListProductsByStore",
		ctx,
		req,
	)
	if rpcErr != nil {
		return nil, rpcErr
	}
	return resp.(*ec_product.ListProductsByStoreResponse), nil
}

func AddCampaignCreatedAuditLog(ctx context.Context, campaignType, campaignTitle string) {
	CreateRetailMarketingAuditLog(ctx, []string{fmt.Sprintf("创建【%s】，%s", campaignType, campaignTitle)}, "")
}

func CreateRetailMarketingAuditLog(ctx context.Context, details []string, title string) {
	if len(details) == 0 {
		return
	}
	details = util.StrArrayUnique(details)
	if title != "" {
		for i, _ := range details {
			if strings.Contains(details[i], "结束时间至") {
				// 修改活动结束时间的 title 格式不一样，所以在外层 format，这里不处理
				continue
			}
			details[i] = fmt.Sprintf("%s %s", title, details[i])
		}
	}
	pb_client.GetAccountServiceClient().BatchCreateAuditLog(ctx, &account.BatchCreateAuditLogRequest{
		OperatorId:       core_util.GetUserId(ctx),
		OperationObject:  "营销活动",
		OperationDetails: details,
		AppId:            "retail",
	})
}

func FormatUpdateCampaignEndAtDetail(campaignType, campaignTitle string, endAt time.Time) string {
	return fmt.Sprintf("修改【%s】活动，「%s」结束时间至「%s」",
		campaignType,
		campaignTitle,
		endAt.Format(UPDATE_CAMPAIGN_END_AT_TIME_LAYOUT),
	)
}

func GenDeleteProductSkuDetail(p *ec_product.ProductResponse, deletedSkus []string, skuDetailMap map[string]*pb_product.SpecProdSku) []string {
	if len(p.Product.Skus) == 1 {
		return []string{fmt.Sprintf("删除商品「%s」", p.Product.Number)}
	}
	var details []string
	for _, sku := range deletedSkus {
		details = append(details, fmt.Sprintf("删除商品「%s」%s",
			p.Product.Number,
			FormatSkuDetail(skuDetailMap[sku], p),
		))
	}
	return details
}

func FormatSkuDetail(sku *pb_product.SpecProdSku, product *ec_product.ProductResponse) string {
	if sku == nil || product == nil {
		return ""
	}
	details := make([]string, 0, len(sku.Properties))
	for i, property := range sku.Properties {
		if i < len(product.Product.Specifications) {
			details = append(details, fmt.Sprintf("【%s/%s】", product.Product.Specifications[i].Name, property))
		}
	}
	return strings.Join(details, "、")
}

func GetProductMaps(ctx context.Context, ecProductIds []string) (
	productMap map[string]*ec_product.ProductResponse,
	skuProductMap map[string]*ec_product.ProductResponse,
	skuDetailMap map[string]*pb_product.SpecProdSku,
	err error,
) {
	productMap = make(map[string]*ec_product.ProductResponse, len(ecProductIds))
	skuProductMap = make(map[string]*ec_product.ProductResponse, len(ecProductIds))
	skuDetailMap = make(map[string]*pb_product.SpecProdSku)
	if len(ecProductIds) == 0 {
		return
	}
	resp, rpcErr := pb_client.GetEcProductServiceClient().ListProducts(ctx, &pb_ec.ListProductsRequest{
		EcProductIds: ecProductIds,
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: uint32(len(ecProductIds)),
		},
	})
	if rpcErr != nil {
		err = rpcErr
		return
	}
	for _, item := range resp.Items {
		productMap[item.Ec.Id] = item
		for _, sku := range item.Product.Skus {
			skuProductMap[sku.Sku] = item
			skuDetailMap[sku.Sku] = sku
		}
	}
	return
}
