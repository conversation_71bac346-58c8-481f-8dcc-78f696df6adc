package plus_buy

import (
	"context"
	"fmt"
	"mairpc/core/component"
	"mairpc/core/errors"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/common/types"
	pb_ec_product "mairpc/proto/ec/product"
	pb_plusBuy "mairpc/proto/eccampaign/plusBuy"
	model "mairpc/service/eccampaign/model/plusBuy"
	"mairpc/service/eccampaign/share"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"
	"time"
)

func (PlusBuyService) UpdatePlusBuy(ctx context.Context, req *pb_plusBuy.UpdatePlusBuyRequest) (*pb_plusBuy.UpdatePlusBuyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if req.NoticeBeforeStartTime < 0 {
		return nil, errors.NewInvalidArgumentError("noticeBeforeStartTime")
	}

	plusBuy, err := model.CPlusBuy.GetById(ctx, util.ToMongoId(req.Id))
	if err != nil {
		return nil, errors.NewNotExistsError("id")
	}

	if plusBuy.EndAt.Before(time.Now()) {
		return nil, errors.NewCanNotEditError("id")
	}

	if req.IsStop {
		if plusBuy.StartAt.After(time.Now()) {
			return nil, errors.NewCanNotEditError("id")
		}
		err := plusBuy.Stop(ctx, util.ToMongoId(req.Id))
		if err != nil {
			return nil, err
		}
		productIds := core_util.ExtractArrayStringField("Id", plusBuy.Rule.Products)
		err = share.UpdateCampaignProduct(ctx, &pb_ec_product.UpdateCampaignProductRequest{
			Ids:               productIds,
			CampaignOperation: "delete",
			Campaign: &pb_ec_product.Campaign{
				Id:   req.Id,
				Type: model.CAMPAIGN_TYPE_PLUS_BUY,
			},
		})
		if err != nil {
			return nil, err
		}
		plusBuyProductIds := core_util.ExtractArrayStringField("Id", plusBuy.PlusBuyRule.Products)
		err = share.UpdateCampaignProduct(ctx, &pb_ec_product.UpdateCampaignProductRequest{
			Ids:               plusBuyProductIds,
			CampaignOperation: "delete",
			Campaign: &pb_ec_product.Campaign{
				Id:   req.Id,
				Type: model.PRODUCT_TYPE_PLUS_BUY,
			},
		})
		if err != nil {
			return nil, err
		}
		return &pb_plusBuy.UpdatePlusBuyResponse{}, nil
	}

	if req.IsModalEnabled != nil {
		plusBuy.IsModalEnabled = req.IsModalEnabled.Value
	}

	nPlusBuy := model.PlusBuy{}
	if plusBuy.StartAt.Before(time.Now()) {
		// 已开始的活动支持编辑结束时间、换购商品列表和换购限制
		nPlusBuy = plusBuy
		nPlusBuy.EndAt = core_util.ParseRFC3339(req.EndAt)
		copier.Instance(nil).From(req.PlusBuyRule.Products).CopyTo(&nPlusBuy.PlusBuyRule.Products)
		copier.Instance(nil).From(req.Rule.Products).CopyTo(&nPlusBuy.Rule.Products)
		nPlusBuy.PlusBuyRule.Type = req.PlusBuyRule.Type
		nPlusBuy.ShareDescription = req.ShareDescription
		nPlusBuy.SharePicture = req.SharePicture
		if nPlusBuy.EndAt.Before(time.Now()) {
			return nil, errors.NewInvalidArgumentError("startAt")
		}
	} else {
		copier.Instance(nil).From(req).CopyTo(&nPlusBuy)
		if nPlusBuy.StartAt.Before(time.Now()) || nPlusBuy.StartAt.After(nPlusBuy.EndAt) {
			return nil, errors.NewInvalidArgumentError("startAt")
		}
	}
	isAllConflictPlusBuyProduct, plusBuyProductIds, plusBuyPrice, err := checkArgument(ctx, nPlusBuy, req.PlusBuyRule.Products, req.StartAt, req.EndAt)
	if err != nil {
		return nil, err
	}
	if isAllConflictPlusBuyProduct || len(plusBuyProductIds) != 0 {
		return &pb_plusBuy.UpdatePlusBuyResponse{
			IsAllConflictPlusBuyProduct: isAllConflictPlusBuyProduct,
			PlusBuyProductIds:           plusBuyProductIds,
		}, nil
	}
	productIds := core_util.ExtractArrayStringField("Id", req.Rule.Products)
	resp, err := share.CheckCampaignConflict(ctx, &pb_ec_product.CheckCampaignConflictRequest{
		CampaignType: model.CAMPAIGN_TYPE_PLUS_BUY,
		ProductIds:   productIds,
		TimeRange: &types.StringDateRange{
			Start: req.StartAt,
			End:   req.EndAt,
		},
		IgnoreCampaignIds: []string{req.Id},
	})
	if err != nil {
		return nil, err
	}
	if resp.IsAllProductConflict || len(resp.ProductIds) != 0 {
		return &pb_plusBuy.UpdatePlusBuyResponse{
			IsAllConflictProduct: resp.IsAllProductConflict,
			ProductIds:           resp.ProductIds,
		}, nil
	}

	memberLimit := share_model.MemberFilter{}
	if req.MemberLimit != nil {
		copier.Instance(nil).From(req.MemberLimit).CopyTo(&memberLimit)
	}
	if req.PrepaidCardLimit != nil {
		nPlusBuy.PrepaidCardLimit = share_model.UnMarshalPrepaidCardLimitFromPb(req.PrepaidCardLimit)
	}
	nPlusBuy.MemberLimit = memberLimit
	nPlusBuy.CountdownEnabled = req.CountdownEnabled
	nPlusBuy.NoticeBeforeStartTime = req.NoticeBeforeStartTime

	err = nPlusBuy.Update(ctx)
	if err != nil {
		return nil, err
	}

	createPlusBuyUpdatedAuditLog(ctx, nPlusBuy, plusBuy)

	err = updateCampaignProduct(ctx, plusBuy, nPlusBuy, plusBuyPrice)
	if err != nil {
		return nil, err
	}

	return &pb_plusBuy.UpdatePlusBuyResponse{}, nil
}

func updateCampaignProduct(ctx context.Context, old, new model.PlusBuy, plusBuyPice int64) error {
	oldProductIds := core_util.ExtractArrayStringField("Id", old.Rule.Products)
	oldPlusBuyProductIds := core_util.ExtractArrayStringField("Id", old.PlusBuyRule.Products)
	campaignDisplay := &pb_ec_product.CampaignDisplay{Price: plusBuyPice}
	updateCampaignProductReq := &pb_ec_product.UpdateCampaignProductRequest{
		Ids:               oldProductIds,
		CampaignOperation: "update",
		Campaign: &pb_ec_product.Campaign{
			Id:      new.Id.Hex(),
			Title:   new.Name,
			StartAt: new.StartAt.Format(time.RFC3339),
			EndAt:   new.EndAt.Format(time.RFC3339),
			Type:    model.CAMPAIGN_TYPE_PLUS_BUY,
			Extra:   new.MemberLimit.GetExtraString(),
		},
	}
	newProductIds := core_util.ExtractArrayStringField("Id", new.Rule.Products)
	newPlusBuyProductIds := core_util.ExtractArrayStringField("Id", new.PlusBuyRule.Products)
	updateCampaignProductReq.CampaignOperation = "delete"
	// 移除旧的活动商品
	err := share.UpdateCampaignProduct(ctx, updateCampaignProductReq)
	if err != nil {
		return err
	}
	updateCampaignProductReq.CampaignOperation = "add"
	updateCampaignProductReq.Ids = newProductIds
	// 添加新的活动商品
	err = share.UpdateCampaignProduct(ctx, updateCampaignProductReq)
	if err != nil {
		return err
	}
	updateCampaignProductReq.Campaign.Type = model.PRODUCT_TYPE_PLUS_BUY
	updateCampaignProductReq.Campaign.Display = campaignDisplay
	updateCampaignProductReq.Ids = oldPlusBuyProductIds
	updateCampaignProductReq.CampaignOperation = "delete"
	// 移除旧的换购商品
	err = share.UpdateCampaignProduct(ctx, updateCampaignProductReq)
	if err != nil {
		return err
	}
	updateCampaignProductReq.Ids = newPlusBuyProductIds
	updateCampaignProductReq.CampaignOperation = "add"
	// 添加新的换购商品
	err = share.UpdateCampaignProduct(ctx, updateCampaignProductReq)
	if err != nil {
		return err
	}

	return nil
}

func createPlusBuyUpdatedAuditLog(ctx context.Context, newCampaign, oldCampaign model.PlusBuy) {
	component.GO(ctx, func(ctx context.Context) {
		newProductIds, newPlusProductIds, newSkus, newPlusSkus, newPlusSkuPriceMap := newCampaign.GetSkusInfo()
		oldProductIds, oldPlusProductIds, oldSkus, oldPlusSkus, oldPlusSkuPriceMap := oldCampaign.GetSkusInfo()
		productIds := append(newProductIds, oldProductIds...)
		productIds = append(productIds, newPlusProductIds...)
		productIds = append(productIds, oldPlusProductIds...)
		productMap, skuProductMap, skuDetailMap, err := share.GetProductMaps(ctx, productIds)
		if err != nil {
			return
		}
		var details []string
		deletedProductIds := util.StrArrayDiff(oldProductIds, newProductIds)
		addedProductIds := util.StrArrayDiff(newProductIds, oldProductIds)
		// 活动商品改动
		for _, id := range deletedProductIds {
			p := productMap[id]
			if p == nil || len(p.Product.Skus) > 1 {
				continue
			}
			details = append(details, fmt.Sprintf("删除活动商品「%s」", p.Product.Number))
		}
		for _, id := range addedProductIds {
			p := productMap[id]
			if p == nil || len(p.Product.Skus) > 1 {
				continue
			}
			details = append(details, fmt.Sprintf("新增活动商品「%s」", p.Product.Number))
		}
		for _, deletedSku := range util.StrArrayDiff(oldSkus, newSkus) {
			p := skuProductMap[deletedSku]
			if p == nil {
				continue
			}
			if !util.StrInArray(p.Ec.Id, &deletedProductIds) || len(p.Product.Skus) > 1 {
				details = append(details, fmt.Sprintf("删除活动商品「%s」%s",
					p.Product.Number,
					share.FormatSkuDetail(skuDetailMap[deletedSku], p),
				))
			}
		}
		for _, addedSku := range util.StrArrayDiff(newSkus, oldSkus) {
			p := skuProductMap[addedSku]
			if p == nil {
				continue
			}
			if !util.StrInArray(p.Ec.Id, &addedProductIds) || len(p.Product.Skus) > 1 {
				details = append(details, fmt.Sprintf("新增商品「%s」%s",
					p.Product.Number,
					share.FormatSkuDetail(skuDetailMap[addedSku], p),
				))
			}
		}
		// 换购商品改动
		deletedPlusProductIds := util.StrArrayDiff(oldPlusProductIds, newPlusProductIds)
		addedPlusProductIds := util.StrArrayDiff(newPlusProductIds, oldPlusProductIds)
		for _, id := range deletedPlusProductIds {
			p := productMap[id]
			if p == nil || len(p.Product.Skus) > 1 {
				continue
			}
			details = append(details, fmt.Sprintf("删除换购商品「%s」", p.Product.Number))
		}
		for _, id := range addedPlusProductIds {
			p := productMap[id]
			if p == nil || len(p.Product.Skus) > 1 {
				continue
			}
			details = append(details, fmt.Sprintf("新增换购商品「%s」，换购价为【%.2f】",
				p.Product.Number,
				util.ConvertAmountUnitFromCentToYuan(newPlusSkuPriceMap[p.Product.Skus[0].Sku]),
			))
		}
		for _, deletedSku := range util.StrArrayDiff(oldPlusSkus, newPlusSkus) {
			p := skuProductMap[deletedSku]
			if p == nil {
				continue
			}
			if !util.StrInArray(p.Ec.Id, &deletedPlusProductIds) || len(p.Product.Skus) > 1 {
				details = append(details, fmt.Sprintf("删除换购商品「%s」%s",
					p.Product.Number,
					share.FormatSkuDetail(skuDetailMap[deletedSku], p),
				))
			}
		}
		for _, addedSku := range util.StrArrayDiff(newPlusSkus, oldPlusSkus) {
			p := skuProductMap[addedSku]
			if p == nil {
				continue
			}
			if !util.StrInArray(p.Ec.Id, &addedPlusProductIds) || len(p.Product.Skus) > 1 {
				details = append(details, fmt.Sprintf("新增换购商品「%s」%s，换购价为【%.2f】",
					p.Product.Number,
					share.FormatSkuDetail(skuDetailMap[addedSku], p),
					util.ConvertAmountUnitFromCentToYuan(newPlusSkuPriceMap[addedSku]),
				))
			}
		}
		for _, sku := range core_util.IntersectStringSlice(newPlusSkus, oldPlusSkus) {
			newPrice := newPlusSkuPriceMap[sku]
			oldPrice := oldPlusSkuPriceMap[sku]
			p := skuProductMap[sku]
			if p == nil {
				continue
			}
			if newPrice != oldPrice {
				fmtStr := "换购商品「%s」，换购价从【%.2f】变更为【%.2f】"
				fmtParams := []interface{}{
					p.Product.Number,
				}
				if len(p.Product.Skus) > 1 {
					fmtStr = "换购商品「%s」%s，换购价从【%.2f】变更为【%.2f】"
					fmtParams = append(fmtParams, share.FormatSkuDetail(skuDetailMap[sku], p))
				}
				fmtParams = append(fmtParams,
					util.ConvertAmountUnitFromCentToYuan(oldPrice),
					util.ConvertAmountUnitFromCentToYuan(newPrice),
				)
				details = append(details, fmt.Sprintf(fmtStr, fmtParams...))
			}
		}

		// 添加修改活动结束时间审计日志
		if newCampaign.EndAt.After(time.Now()) && !newCampaign.EndAt.Equal(oldCampaign.EndAt) {
			details = append(details, share.FormatUpdateCampaignEndAtDetail("超值换购", newCampaign.Name, newCampaign.EndAt))
		}
		share.CreateRetailMarketingAuditLog(ctx, details, newCampaign.Name)
	})
}
