package plus_buy

import (
	"context"
	"mairpc/core/extension/bson"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	pb_eccampaign_plus_buy "mairpc/proto/eccampaign/plusBuy"
	plus_buy_model "mairpc/service/eccampaign/model/plusBuy"
)

func (PlusBuyService) GetMemberRecord(ctx context.Context, req *request.DetailWithMemberIdRequest) (*pb_eccampaign_plus_buy.MemberRecordDetail, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	plusBuy, err := plus_buy_model.CPlusBuy.GetById(ctx, bson.ObjectIdHex(req.Id))
	if err != nil {
		return nil, err
	}
	record, err := plus_buy_model.CPlusBuyMemberRecord.MustGet(ctx, plusBuy, bson.ObjectIdHex(req.MemberId))
	if err != nil {
		return nil, err
	}
	resp := &pb_eccampaign_plus_buy.MemberRecordDetail{}
	for _, p := range plusBuy.PlusBuyRule.Products {
		if p.PersonalLimitCount == 0 {
			continue
		}
		limit := record.GetLimitBySku(p.Sku)
		remaining, used := p.PersonalLimitCount, uint64(0)
		if limit != nil {
			remaining, used, _ = limit.CountRemainingWithCount(p.GetPersonalLimitSetting())
		}
		resp.ProductStats = append(resp.ProductStats, &pb_eccampaign_plus_buy.ProductLimitStat{
			Sku:       p.Sku,
			Count:     used,
			Remaining: remaining,
		})
	}
	return resp, nil
}
