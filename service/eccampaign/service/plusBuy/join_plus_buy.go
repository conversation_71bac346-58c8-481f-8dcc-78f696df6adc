package plus_buy

import (
	"context"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	pb_eccampaign_plus_buy "mairpc/proto/eccampaign/plusBuy"
	plus_buy_model "mairpc/service/eccampaign/model/plusBuy"
	"time"
)

func (PlusBuyService) JoinPlusBuy(ctx context.Context, req *pb_eccampaign_plus_buy.JoinPlusBuyRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	plusBuy, err := plus_buy_model.CPlusBuy.GetById(ctx, bson.ObjectIdHex(req.Id))
	if err != nil {
		return nil, err
	}
	if plusBuy.StartAt.After(time.Now()) || plusBuy.EndAt.Before(time.Now()) {
		return nil, errors.NewInvalidArgumentError("time")
	}
	_, dbErr := extension.DBRepository.Transaction(ctx, func(sessCtx context.Context) (interface{}, error) {
		record, err := plus_buy_model.CPlusBuyMemberRecord.MustGet(sessCtx, plusBuy, bson.ObjectIdHex(req.MemberId))
		if err != nil {
			return nil, err
		}
		for _, op := range req.OrderProductInfos {
			plusBuyProduct := plusBuy.PlusBuyRule.GetProductBySku(op.Sku)
			if plusBuyProduct == nil {
				continue
			}
			if plusBuyProduct.PersonalLimitCount > 0 && !record.CheckAndRecord(sessCtx, op.Sku, req.OrderNumber, op.Total, plusBuyProduct.GetPersonalLimitSetting()) {
				return nil, errors.NewInvalidArgumentError("total")
			}
			if err := plusBuy.DecStock(sessCtx, op.Sku, uint64(op.Total)); err != nil {
				return nil, err
			}
		}
		return nil, record.UpdateLimit(sessCtx)
	})
	if dbErr != nil {
		return nil, dbErr
	}
	return &response.EmptyResponse{}, nil
}
