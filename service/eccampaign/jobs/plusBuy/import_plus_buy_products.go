package plus_buy

import (
	"context"
	"encoding/json"
	"fmt"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/account"
	pb_product "mairpc/proto/ec/product"
	"mairpc/service/eccampaign/share"
	async_cache "mairpc/service/share/async_cache"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"
	"os"
	"time"

	"github.com/spf13/cast"

	"github.com/spf13/cobra"
	"github.com/tealeg/xlsx"
)

const (
	DIMENSION_TYPE_ID  = "id"
	DIMENSION_TYPE_SKU = "sku"
)

type PlusBuyCampaignInfo struct {
	Sku                string
	PlusBuyPrice       int64
	PersonalLimitCount uint64
	Stock              uint64
}

func init() {
	PlusBuyCmd.AddCommand(importPlusBuyProducts)
}

var importPlusBuyProducts = &cobra.Command{
	Use: "importPlusBuyProducts",
	RunE: func(cmd *cobra.Command, args []string) error {
		jobOptions := util.GetArgs(args)
		ctx := util.GetContextInJob(args)
		params, err := share.GetReqParams(ctx, jobOptions["request"])
		if err != nil {
			return err
		}
		code := params["code"]
		products, failedRows, totalCount, err := getOptionalPlusBuyCampaignProducts(ctx, params["fileUrl"], params["productType"], params["startAt"], params["endAt"], params["plusBuyId"])
		if err != nil {
			return err
		}
		url := ""
		if len(failedRows) > 0 {
			url, err = job_util.ExportFile(
				ctx,
				func(productType string) string {
					if productType == "plusBuy" {
						return "换购商品导入失败记录_" + util.GetJobTimestamp(time.Now()) + ".xlsx"
					}
					return "超值换购活动商品导入失败记录_" + util.GetJobTimestamp(time.Now()) + ".xlsx"
				}(params["productType"]),
				func(f *os.File) error {
					file := xlsx.NewFile()
					sheet, err := file.AddSheet("Sheet1")
					if err != nil {
						return err
					}
					titleRow := sheet.AddRow()
					titleRow.AddCell().Value = "商品ID"
					titleRow.AddCell().Value = "SKU编号"
					titleRow.AddCell().Value = "外部SKU编号"
					if params["productType"] == "plusBuy" {
						titleRow.AddCell().Value = "换购价（元）"
					}
					titleRow.AddCell().Value = "失败原因"
					for _, row := range failedRows {
						newRow := sheet.AddRow()
						for _, cell := range row {
							newRow.AddCell().Value = cell
						}
					}
					file.Save(f.Name())
					f.Sync()
					return nil
				},
			)
		}
		data, _ := json.Marshal(map[string]interface{}{
			"failedUrl":    url,
			"failedCount":  len(failedRows),
			"succeedCount": totalCount - len(failedRows),
			"products":     products,
		})
		updateReq := &account.UpdateAsyncCacheDataRequest{
			Code:   code,
			Status: "completed",
			Data:   string(data),
		}
		if err != nil {
			log.Warn(ctx, "Filed to import plus buy campaign products", log.Fields{
				"errorMessage": err.Error(),
				"code":         code,
			})
			updateReq.Status = "failed"
			updateReq.FailedReason = err.Error()
		}
		async_cache.UpdateAsyncCacheData(ctx, updateReq)
		return err
	},
}

func getOptionalPlusBuyCampaignProducts(ctx context.Context, fileUrl, productType, startAt, endAt, plusBuyId string) ([]share.CampaignApplicableProduct, [][]string, int, error) {
	var (
		failedRows            [][]string
		totalCount            int
		productDetails        []*pb_product.ProductResponse
		productsMap           = map[string][]string{}
		productExistsMap      = map[string]bool{}
		plusBuyCampaignInfo   []PlusBuyCampaignInfo
		ecProductType         = ""
		productExternalSkuMap = map[string][]string{}
		productSkuMap         = map[string][]string{}
	)
	tableReader, err := util.GetTableReader(ctx, fileUrl)
	if err != nil {
		return nil, nil, 0, err
	}
OUTER_LOOP:
	for i := 1; i < tableReader.RealMaxRow(); i++ {
		number := tableReader.GetCell(i, 0)
		sku := tableReader.GetCell(i, 1)
		externalSku := tableReader.GetCell(i, 2)
		plusBuyPriceString := tableReader.GetCell(i, 3)
		personalLimitCount := cast.ToUint64(tableReader.GetCell(i, 4))
		stock := cast.ToUint64(tableReader.GetCell(i, 5))
		plusBuyPrice := util.ConvertAmountUnitFromYuanToCent[int64](plusBuyPriceString)
		if number == "" && sku == "" {
			continue
		}
		totalCount++
		if number != "" && sku != "" {
			failedRows = append(failedRows, []string{number, sku, externalSku, "商品编码和商品SKU只能填一项"})
			continue
		}
		resp := &pb_product.ProductResponse{}
		productDetail := &pb_product.ProductResponse{}
		if number != "" {
			numberCombineExternalSku := fmt.Sprintf("%s:%s", number, externalSku)
			resp, err, failedRows = share.GetProductsMapByDimension(ctx, numberCombineExternalSku, DIMENSION_TYPE_ID, failedRows, productExistsMap, getCampaignType(productType))
			if err != nil {
				continue
			}
			pType := resp.Product.Type
			if ecProductType == "" {
				ecProductType = pType
			} else if pType != ecProductType && productType != "plusBuy" {
				failedRows = append(failedRows, []string{number, sku, externalSku, "导入商品类型不一致"})
				continue
			}
			if productType == "plusBuy" {
				if externalSku == "" && resp.Ec.LowestPrice < plusBuyPrice {
					failedRows = append(failedRows, []string{number, sku, externalSku, plusBuyPriceString, "换购价大于商品原价"})
					continue
				}
				for _, ecSku := range resp.Ec.Skus {
					if externalSku != "" && ecSku.External != externalSku {
						continue
					}
					if externalSku != "" && ecSku.Price < cast.ToInt64(plusBuyPrice) {
						failedRows = append(failedRows, []string{number, sku, externalSku, plusBuyPriceString, "换购价大于商品原价"})
						continue OUTER_LOOP
					}
					cInfo := PlusBuyCampaignInfo{
						Sku:                ecSku.Sku,
						PlusBuyPrice:       plusBuyPrice,
						PersonalLimitCount: personalLimitCount,
						Stock:              stock,
					}
					plusBuyCampaignInfo = append(plusBuyCampaignInfo, cInfo)
				}
			}
			if externalSku != "" {
				for _, ecSku := range resp.Ec.Skus {
					if ecSku.External == externalSku {
						sku = ecSku.Sku
						break
					}
				}
			}
		} else {
			resp, err, failedRows = share.GetProductsMapByDimension(ctx, sku, DIMENSION_TYPE_SKU, failedRows, productExistsMap, getCampaignType(productType))
			if err != nil {
				continue
			}
			pType := resp.Product.Type
			if ecProductType == "" {
				ecProductType = pType
			} else if pType != ecProductType && productType != "plusBuy" {
				failedRows = append(failedRows, []string{number, sku, externalSku, "导入商品类型不一致"})
				continue
			}
			if productType == "plusBuy" {
				for _, ecSku := range resp.Ec.Skus {
					if ecSku.Sku != sku {
						continue
					}
					if ecSku.Price < cast.ToInt64(plusBuyPrice) {
						failedRows = append(failedRows, []string{number, sku, externalSku, plusBuyPriceString, "换购价大于商品原价"})
						continue OUTER_LOOP
					}
					cInfo := PlusBuyCampaignInfo{
						Sku: sku,
						PlusBuyPrice: func(plusBuyPrice int64) int64 {
							if plusBuyPrice > 0 {
								return plusBuyPrice
							}
							return 0
						}(plusBuyPrice),
						PersonalLimitCount: personalLimitCount,
						Stock:              stock,
					}
					plusBuyCampaignInfo = append(plusBuyCampaignInfo, cInfo)
				}
			}
		}
		if sku != "" {
			productDetail = share.FormatProductsMapByDimensionType(resp, sku, DIMENSION_TYPE_SKU, productsMap)
			productSkuMap[resp.Ec.Id] = append(productSkuMap[resp.Ec.Id], sku)
		} else {
			productDetail = share.FormatProductsMapByDimensionType(resp, "", DIMENSION_TYPE_ID, productsMap)
		}
		if resp.Ec.Source != "ec" {
			if productType != "plusBuy" {
				failedRows = append(failedRows, []string{number, sku, externalSku, "仅经销品(店铺商品)可成功导入"})
			} else {
				failedRows = append(failedRows, []string{number, sku, externalSku, plusBuyPriceString, "仅经销品(店铺商品)可成功导入"})
			}
			continue
		}
		if externalSku != "" {
			productExternalSkuMap[resp.Ec.Id] = append(productExternalSkuMap[resp.Ec.Id], externalSku)
		}
		productDetails = append(productDetails, productDetail)
	}
	productDetails = share.DeduplicateProducts(productDetails)
	conflictType := ""
	if productType == "plusBuy" {
		conflictType = "plusBuyProduct"
	} else if productType == "campaign" {
		conflictType = "plusBuyCampaign"
	}
	products, conflictFailedRows, err := share.FormatCampaignApplicableProducts(ctx, productsMap, productExternalSkuMap, productSkuMap, productDetails, map[string]string{}, startAt, endAt, conflictType, plusBuyId)
	failedRows = append(failedRows, conflictFailedRows...)
	if err != nil {
		return nil, nil, 0, err
	}
	skuToCampaignInfoMapper := core_util.MakeMapper("Sku", plusBuyCampaignInfo)
	formatPlusBuyCampaignProduct(products, skuToCampaignInfoMapper)
	return products, failedRows, totalCount, nil
}

func formatPlusBuyCampaignProduct(products []share.CampaignApplicableProduct, skuToCampaignInfoMapper map[interface{}]interface{}) {
	for _, p := range products {
		for i, sku := range p.Skus {
			if info, ok := skuToCampaignInfoMapper[sku.Sku].(PlusBuyCampaignInfo); ok {
				p.Skus[i].PlusBuyPrice = info.PlusBuyPrice
				p.Skus[i].PersonalLimitCount = info.PersonalLimitCount
				p.Skus[i].Stock = int64(info.Stock)
			}
		}
	}
}

func getCampaignType(productType string) string {
	if productType == "plusBuy" {
		return "plusBuyProduct"
	} else if productType == "campaign" {
		return "plusBuyCampaign"
	}
	return ""
}
