package scan_to_buy

import (
	"context"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	pb_scanToBuy "mairpc/proto/eccampaign/scanToBuy"
	"mairpc/service/share/model"
	"time"
)

const (
	C_SETTING = "eccampaign.scanToBuySetting"
)

var (
	CSetting = &Setting{}
)

type Setting struct {
	Id             bson.ObjectId `bson:"_id,omitempty"`
	AccountId      bson.ObjectId `bson:"accountId,omitempty"`
	CreatedAt      time.Time     `bson:"createdAt"`
	UpdatedAt      time.Time     `bson:"updatedAt"`
	IsDeleted      bool          `bson:"isDeleted"`
	IsEnabled      bool          `bson:"isEnabled"`
	CanPayByOthers bool          `bson:"canPayByOthers"`
	Payments       []Payment     `bson:"payments"`
	Process        string        `bson:"process"`
}

type Payment struct {
	Name    string `bson:"name"`
	Enabled bool   `bson:"enabled"`
	Code    string `bson:"code"`
	Icon    string `bson:"icon"`
}

func (s *Setting) Create(ctx context.Context) error {
	s.AccountId = core_util.ToObjectId(core_util.MustGetAccountId(ctx))
	s.CreatedAt, s.UpdatedAt = time.Now(), time.Now()
	s.IsDeleted = false
	s.IsEnabled = false
	s.Id = bson.NewObjectId()
	_, err := extension.DBRepository.Insert(ctx, C_SETTING, s)
	return err
}

func (s *Setting) GetById(ctx context.Context) (Setting, error) {
	selector := model.Base.GenDefaultCondition(ctx)
	setting := Setting{}
	err := extension.DBRepository.FindOne(ctx, C_SETTING, selector, &setting)
	return setting, err
}

func (s *Setting) UpdateOne(ctx context.Context, req *pb_scanToBuy.UpdateScanToBuySettingRequest) error {
	selector := Common.GenDefaultConditionById(ctx, bson.ObjectIdHex(req.Id))
	setter := bson.M{
		"updatedAt": time.Now(),
		"isEnabled": req.IsEnabled,
	}
	if req.Process != "" {
		setter["process"] = req.Process
	}
	if req.Payments != nil {
		setter["payments"] = req.Payments
	}
	updater := bson.M{
		"$set": setter,
	}
	err := extension.DBRepository.UpdateOne(ctx, C_SETTING, selector, updater)
	return err
}
