package plus_buy

import (
	"context"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/service/share/model"
	"mairpc/service/share/util"
	"time"
)

const (
	C_PLUS_BUY             = "eccampaign.plusBuy"
	CAMPAIGN_TYPE_PLUS_BUY = "plusBuyCampaign"
	PRODUCT_TYPE_PLUS_BUY  = "plusBuyProduct"

	STATUS_CREATED = "created" // 未开始
	STATUS_RUNNING = "running" // 进行中
	STATUS_ENDED   = "ended"   // 已结束

	PLUS_BUY_RULE_TYPE_INCLUDE = "include"
	PLUS_BUY_RULE_TYPE_ALL     = "all"

	PLUS_BUY_RULE_TYPE_SIGNLE = "single"
)

var (
	CPlusBuy = &PlusBuy{}
)

type PlusBuy struct {
	Id                    bson.ObjectId          `bson:"_id,omitempty"`
	AccountId             bson.ObjectId          `bson:"accountId"`
	CreatedAt             time.Time              `bson:"createdAt"`
	UpdatedAt             time.Time              `bson:"updatedAt"`
	IsDeleted             bool                   `bson:"isDeleted"`
	Name                  string                 `bson:"name"`
	StartAt               time.Time              `bson:"startAt"`
	EndAt                 time.Time              `bson:"endAt"`
	Price                 uint64                 `bson:"price"`
	Rule                  Rule                   `bson:"rule"`
	IsFreeShipping        bool                   `bson:"isFreeShipping"`
	PlusBuyRule           PlusBuyRule            `bson:"plusBuyRule"`
	CanUseCoupon          bool                   `bson:"canUseCoupon"`
	CanUsePrepaidCard     bool                   `bson:"canUsePrepaidCard"`
	IsDiscountLimit       bool                   `bson:"isDiscountLimit"`
	MemberLimit           model.MemberFilter     `bson:"memberLimit,omitempty"`
	CountdownEnabled      bool                   `bson:"countdownEnabled"`
	NoticeBeforeStartTime int64                  `bson:"noticeBeforeStartTime"`
	ShareDescription      string                 `bson:"shareDescription"` // 分享描述
	SharePicture          string                 `bson:"sharePicture"`     // 分享图片
	PrepaidCardLimit      model.PrepaidCardLimit `bson:"prepaidCardLimit,omitempty"`
	Stock                 []PlusBuyStock         `bson:"stock"`
	IsModalEnabled        bool                   `bson:"isModalEnabled"`
}

type Rule struct {
	Type     string    `bson:"type"` // 活动类型： include(部分商品参加)，all（全部商品参加）
	Products []Product `bson:"products"`
}

type PlusBuyRule struct {
	Type     string           `bson:"type"` //换购的限制： single(一个)，multiple(多个)
	Products []PlusBuyProduct `bson:"products"`
}

type PlusBuyStock struct {
	ProductId bson.ObjectId `bson:"productId"`
	Sku       string        `bson:"sku"`
	Stock     uint64        `bson:"stock"`
}

type Product struct {
	Id   bson.ObjectId `bson:"id"`
	Skus []string      `bson:"skus"`
}

type PlusBuyProduct struct {
	Id                 bson.ObjectId `bson:"id"`
	Sku                string        `bson:"sku"`
	Price              uint64        `bson:"price"`
	Stock              uint64        `bson:"stock"`
	PersonalLimitCount uint64        `bson:"personalLimitCount"`
}

func (p *PlusBuy) Create(ctx context.Context) error {
	p.AccountId = util.GetAccountIdAsObjectId(ctx)
	p.CreatedAt, p.UpdatedAt = time.Now(), time.Now()
	// 初始化库存
	for i, product := range p.PlusBuyRule.Products {
		if product.PersonalLimitCount == 0 {
			p.PlusBuyRule.Products[i].PersonalLimitCount = 1
		}
		if product.Stock == 0 {
			// 允许不设置库存
			continue
		}
		p.Stock = append(p.Stock, PlusBuyStock{
			ProductId: product.Id,
			Sku:       product.Sku,
			Stock:     product.Stock,
		})
	}
	_, err := extension.DBRepository.Insert(ctx, C_PLUS_BUY, p)
	return err
}

func (p *PlusBuy) GetById(ctx context.Context, id bson.ObjectId) (PlusBuy, error) {
	selector := model.Base.GenDefaultConditionById(ctx, id)
	plusBuy := PlusBuy{}
	err := extension.DBRepository.FindOne(ctx, C_PLUS_BUY, selector, &plusBuy)
	return plusBuy, err
}

func (p *PlusBuy) GetByIds(ctx context.Context, ids []bson.ObjectId) []PlusBuy {
	selector := model.Base.GenDefaultCondition(ctx)
	selector["_id"] = bson.M{"$in": ids}
	plusBuys := []PlusBuy{}
	extension.DBRepository.FindAll(ctx, C_PLUS_BUY, selector, nil, 0, &plusBuys)
	return plusBuys
}

func (p *PlusBuy) Delete(ctx context.Context) error {
	selector := Common.GenDefaultConditionById(ctx, p.Id)
	updater := bson.M{
		"$set": bson.M{
			"isDeleted": true,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_PLUS_BUY, selector, updater)
}

func (p *PlusBuy) Stop(ctx context.Context, id bson.ObjectId) error {
	selector := Common.GenDefaultConditionById(ctx, id)
	updater := bson.M{
		"$set": bson.M{
			"updatedAt": time.Now(),
			"endAt":     time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_PLUS_BUY, selector, updater)
}

func (p *PlusBuy) Update(ctx context.Context) error {
	selector := Common.GenDefaultConditionById(ctx, p.Id)
	updater := bson.M{
		"$set": bson.M{
			"name":              p.Name,
			"startAt":           p.StartAt,
			"endAt":             p.EndAt,
			"updatedAt":         time.Now(),
			"price":             p.Price,
			"rule":              p.Rule,
			"isFreeShipping":    p.IsFreeShipping,
			"plusBuyRule":       p.PlusBuyRule,
			"canUseCoupon":      p.CanUseCoupon,
			"canUsePrepaidCard": p.CanUsePrepaidCard,
			"isDiscountLimit":   p.IsDiscountLimit,
			"memberLimit":       p.MemberLimit,
			"shareDescription":  p.ShareDescription,
			"sharePicture":      p.SharePicture,
			"prepaidCardLimit":  p.PrepaidCardLimit,
			"isModalEnabled":    p.IsModalEnabled,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_PLUS_BUY, selector, updater)
}

func (p *PlusBuy) GetAllByPagination(ctx context.Context, condition bson.M, page, pageSize uint32, orderBys []string) ([]PlusBuy, int) {
	var plusBuys []PlusBuy
	totalCount := Common.GetAllByPagination(ctx, condition, page, pageSize, orderBys, C_PLUS_BUY, &plusBuys)
	return plusBuys, totalCount
}

func (p *PlusBuy) GetSkusInfo() (
	productIds []string,
	plusProductIds []string,
	skus []string,
	plusSkus []string,
	plusSkuPriceMap map[string]int,
) {
	plusSkuPriceMap = make(map[string]int)
	for _, product := range p.Rule.Products {
		productIds = append(productIds, product.Id.Hex())
		skus = append(skus, product.Skus...)
	}
	for _, product := range p.PlusBuyRule.Products {
		plusProductIds = append(plusProductIds, product.Id.Hex())
		plusSkus = append(plusSkus, product.Sku)
		plusSkuPriceMap[product.Sku] = int(product.Price)
	}
	return
}

func (p *PlusBuyRule) GetProductBySku(sku string) *PlusBuyProduct {
	for _, product := range p.Products {
		if product.Sku == sku {
			return &product
		}
	}
	return nil
}

func (p *PlusBuy) DecStock(ctx context.Context, sku string, count uint64) error {
	product := p.PlusBuyRule.GetProductBySku(sku)
	if product == nil {
		return errors.NewNotExistsError("sku")
	}
	if product.Stock == 0 {
		return nil
	}
	condition := Common.GenDefaultConditionById(ctx, p.Id)
	condition["stock"] = bson.M{
		"$elemMatch": bson.M{
			"sku": sku,
			"stock": bson.M{
				"$gte": count,
			},
		},
	}
	updater := bson.M{
		"$inc": bson.M{
			"stock.$.stock": -int64(count),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_PLUS_BUY, condition, updater)
}

func (p *PlusBuyProduct) GetPersonalLimitSetting() model.LimitSettings {
	return model.InitLimitSettings(p.PersonalLimitCount, 0, 0, 0, 0, 0)
}
