package plus_buy

import (
	"context"
	"errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/service/share/util"
	"time"

	share_model "mairpc/service/share/model"

	"github.com/qiniu/qmgo"
)

const (
	C_PLUS_BUY_MEMBER_RECORD = "eccampaign.plusBuyMemberRecord"
)

var (
	CPlusBuyMemberRecord = &PlusBuyMemberRecord{}
)

type PlusBuyMemberRecord struct {
	Id        bson.ObjectId  `bson:"_id"`
	AccountId bson.ObjectId  `bson:"accountId"`
	PlusBuyId bson.ObjectId  `bson:"plusBuyId"`
	MemberId  bson.ObjectId  `bson:"memberId"`
	CreatedAt time.Time      `bson:"createdAt"`
	UpdatedAt time.Time      `bson:"updatedAt"`
	Limit     []ProductLimit `bson:"limit"`
}

type ProductLimit struct {
	ProductId bson.ObjectId            `bson:"productId"`
	Sku       string                   `bson:"sku"`
	Limit     share_model.LimitRemains `bson:"limit"`
}

func (*PlusBuyMemberRecord) CreateDefault(ctx context.Context, plusBuy PlusBuy, memberId bson.ObjectId) (PlusBuyMemberRecord, error) {
	record := PlusBuyMemberRecord{
		Id:        bson.NewObjectId(),
		AccountId: util.GetAccountIdAsObjectId(ctx),
		PlusBuyId: plusBuy.Id,
		MemberId:  memberId,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	for _, p := range plusBuy.PlusBuyRule.Products {
		record.Limit = append(record.Limit, ProductLimit{
			ProductId: p.Id,
			Sku:       p.Sku,
			Limit:     share_model.InitLimitRemains(),
		})
	}
	_, err := extension.DBRepository.Insert(ctx, C_PLUS_BUY_MEMBER_RECORD, record)
	return record, err
}

func (p *PlusBuyMemberRecord) GetLimitBySku(sku string) *share_model.LimitRemains {
	for _, limit := range p.Limit {
		if limit.Sku == sku {
			return &limit.Limit
		}
	}
	return nil
}

func (p *PlusBuyMemberRecord) CheckAndRecord(
	ctx context.Context,
	sku, orderNumber string,
	purchaseCount uint64,
	limitSetting share_model.LimitSettings,
) bool {
	for i, limit := range p.Limit {
		if limit.Sku == sku {
			remaining := limit.Limit.CountRemaining(limitSetting)
			if remaining < purchaseCount {
				return false
			}
			p.Limit[i].Limit.UsePointByIdentifier(purchaseCount, orderNumber)
			break
		}
	}
	return true
}

func (p *PlusBuyMemberRecord) UpdateLimit(ctx context.Context) error {
	condition := bson.M{
		"_id": p.Id,
	}
	updater := bson.M{
		"$set": bson.M{
			"limit":     p.Limit,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_PLUS_BUY_MEMBER_RECORD, condition, updater)
}

func (*PlusBuyMemberRecord) GetByPlusBuyIdAndMemberId(ctx context.Context, plusBuyId, memberId bson.ObjectId) (PlusBuyMemberRecord, error) {
	var record PlusBuyMemberRecord
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"plusBuyId": plusBuyId,
		"memberId":  memberId,
	}
	err := extension.DBRepository.FindOne(ctx, C_PLUS_BUY_MEMBER_RECORD, condition, &record)
	return record, err
}

func (*PlusBuyMemberRecord) MustGet(ctx context.Context, plusBuy PlusBuy, memberId bson.ObjectId) (PlusBuyMemberRecord, error) {
	record, err := CPlusBuyMemberRecord.GetByPlusBuyIdAndMemberId(ctx, plusBuy.Id, memberId)
	if errors.Is(err, qmgo.ErrNoSuchDocuments) {
		record, err = CPlusBuyMemberRecord.CreateDefault(ctx, plusBuy, memberId)
	}
	return record, err
}
