package model

import (
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"time"

	"golang.org/x/net/context"
)

const (
	C_ACCOUNT_QUOTA = "accountQuota"

	ACCOUNT_QUOTA_TYPE_SMS             = "sms"
	ACCOUNT_QUOTA_TYPE_CHATGROUP       = "chatgroup"
	ACCOUNT_QUOTA_TYPE_ORDER           = "order"
	ACCOUNT_QUOTA_TYPE_PRODUCT_CODE    = "product_code"
	ACCOUNT_QUOTA_TYPE_CONSUMER_DOMAIN = "consumer_domain"
	ACCOUNT_QUOTA_TYPE_DIGITAL_SMS     = "digital_sms"
	ACCOUNT_QUOTA_TYPE_VOD             = "vod"
)

var CAccountQuota = &AccountQuota{}

type AccountQuota struct {
	Id                   bson.ObjectId `bson:"_id,omitempty"`
	Type                 string        `bson:"type"`                 // 资源类型 短信消息（sms），社群群点（chatgroup），零售订单（order），营销码（product_code）,C 端域名(consumer_domain)，数字短信（digital_sms），云点播(vod)
	RemainingTotal       int64         `bson:"remainingTotal"`       // 剩余资源数量
	ConsumedTotal        int64         `bson:"consumedTotal"`        // 已消耗资源数量
	HistoryConsumedCount int64         `bson:"historyConsumedTotal"` // 历史消耗（截止上月底）
	CreatedAt            time.Time     `bson:"createdAt"`
	UpdatedAt            time.Time     `bson:"updatedAt"`
	IsDeleted            bool          `bson:"isDeleted"`
}

func (*AccountQuota) GetAllByCondition(ctx context.Context, condition bson.M) ([]AccountQuota, error) {
	accountQuotas := []AccountQuota{}
	err := extension.DBRepository.FindAll(ctx, C_ACCOUNT_QUOTA, condition, []string{}, 0, &accountQuotas)
	return accountQuotas, err
}

func (*AccountQuota) GetByType(ctx context.Context, quotaType string) (AccountQuota, error) {
	accountQuota := AccountQuota{}
	condition := Common.GenDefaultCondition(ctx)
	condition["type"] = quotaType
	err := extension.DBRepository.FindOne(ctx, C_ACCOUNT_QUOTA, condition, &accountQuota)
	return accountQuota, err
}

func (self *AccountQuota) Upsert(ctx context.Context, tranceId string) error {
	condition := Common.GenDefaultCondition(ctx)
	condition["type"] = self.Type
	updator := bson.M{
		"$inc": bson.M{
			"remainingTotal": self.RemainingTotal,
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
		"$setOnInsert": bson.M{
			"createdAt":     time.Now(),
			"consumedTotal": 0,
		},
	}
	_, err := extension.DBRepository.Upsert(ctx, C_ACCOUNT_QUOTA, condition, updator)
	if err != nil {
		return err
	}
	createAccountQuotaLog(ctx, self.Type, OPERATION_CHARGE, self.RemainingTotal, tranceId)
	return err
}

func (*AccountQuota) Dec(ctx context.Context, quotaType string, count int64) error {
	condition := Common.GenDefaultCondition(ctx)
	condition["type"] = quotaType
	updator := bson.M{
		"$inc": bson.M{
			"remainingTotal": -count,
			"consumedTotal":  count,
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
		"$setOnInsert": bson.M{
			"createdAt": time.Now(),
		},
	}
	_, err := extension.DBRepository.Upsert(ctx, C_ACCOUNT_QUOTA, condition, updator)
	if err != nil {
		return err
	}
	createAccountQuotaLog(ctx, quotaType, OPERATION_CONSUME, count, "")
	return nil
}

func (*AccountQuota) RollbackConsumed(ctx context.Context, quotaType string, count int64, traceId string) error {
	condition := Common.GenDefaultCondition(ctx)
	condition["type"] = quotaType
	updator := bson.M{
		"$inc": bson.M{
			"remainingTotal": count,
			"consumedTotal":  -count,
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
		"$setOnInsert": bson.M{
			"createdAt": time.Now(),
		},
	}
	_, err := extension.DBRepository.Upsert(ctx, C_ACCOUNT_QUOTA, condition, updator)
	if err != nil {
		return err
	}
	createAccountQuotaLog(ctx, quotaType, OPERATION_ROLLBACK, count, traceId)
	return nil
}

func (*AccountQuota) IncHistoryConsumeCount(ctx context.Context, quotaType string, incCount int64) error {
	condition := Common.GenDefaultCondition(ctx)
	condition["type"] = quotaType
	updator := bson.M{
		"$inc": bson.M{
			"historyConsumedTotal": incCount,
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
	}
	err := extension.DBRepository.UpdateOne(ctx, C_ACCOUNT_QUOTA, condition, updator)
	if err != nil {
		return err
	}
	return nil
}
