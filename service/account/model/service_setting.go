package model

import (
	"context"
	"time"

	"mairpc/core/extension"

	"mairpc/core/extension/bson"
)

const (
	C_SERVICE_SETTING = "serviceSetting"

	SERVICE_SETTING_TYPE_CDN  = "cdn"
	SERVICE_SETTING_TYPE_FILE = "file"
	SERVICE_SETTING_TYPE_SMS  = "sms"
)

var CServiceSetting = &ServiceSetting{}

type ServiceSetting struct {
	Id             bson.ObjectId     `bson:"_id"`
	AccountId      bson.ObjectId     `bson:"accountId"`
	Provider       string            `bson:"provider"`
	Service        string            `bson:"service"`
	Cdn            StorageService    `bson:"cdn,omitempty"`
	File           StorageService    `bson:"file,omitempty"`
	Printer        PrinterService    `bson:"printer,omitempty"`
	Sms            SmsService        `bson:"sms,omitempty"`
	Setting        map[string]string `bson:"setting,omitempty"`
	ChannelProxies []ChannelProxy    `bson:"channelProxies,omitempty"`
	IsDeleted      bool              `bson:"isDeleted"`
	CreatedAt      time.Time         `bson:"createdAt"`
	UpdatedAt      time.Time         `bson:"updatedAt"`
}

type ChannelProxy struct {
	ChannelId string `bson:"channelId"`
	ProxyURL  string `bson:"proxyURL"`
}

type StorageService struct {
	AccessKeyId     string `bson:"accessKeyId"`
	AccessKeySecret string `bson:"accessKeySecret"`
	Endpoint        string `bson:"endpoint"`
	Bucket          string `bson:"bucket"`
	Domain          string `bson:"domain"`
}

type PrinterService struct {
	AccessKeyId     string `bson:"accessKeyId"`
	AccessKeySecret string `bson:"accessKeySecret"`
}

type SmsService struct {
	AccessKeyId           string            `bson:"accessKeyId"`
	AccessKeySecret       string            `bson:"accessKeySecret"`
	IntlAccessKeyId       string            `bson:"intlAccessKeyId"`
	IntlAccessKeySecret   string            `bson:"intlAccessKeySecret"`
	NotifyAccessKeyId     string            `bson:"notifyAccessKeyId"`
	NotifyAccessKeySecret string            `bson:"notifyAccessKeySecret"`
	TemplateCodes         map[string]string `bson:"templateCodes"`
}

func (*ServiceSetting) GetByCondition(ctx context.Context, condition bson.M) (*ServiceSetting, error) {
	var serviceSetting ServiceSetting
	err := extension.DBRepository.FindOne(ctx, C_SERVICE_SETTING, condition, &serviceSetting)

	if err != nil {
		return nil, err
	}

	return &serviceSetting, nil
}
