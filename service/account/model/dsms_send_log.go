package model

import (
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/service/share/component/dsms/aliyun"
	"mairpc/service/share/util"
	"time"

	"golang.org/x/net/context"
)

const (
	C_DSMS_SEND_LOG = "dsmsSendLog"

	DSMS_SEND_STATUS_PENDING = "pending"
	DSMS_SEND_STATUS_FAILED  = "failed"
	DSMS_SEND_STATUS_SUCCEED = "succeed"
)

var CDsmsSendLog = &DsmsSendLog{}

type DsmsSendLog struct {
	Id            bson.ObjectId     `bson:"_id"`
	AccountId     bson.ObjectId     `bson:"accountId"`
	Provider      string            `bson:"provider,omitempty"`
	Phone         string            `bson:"phone"`
	TemplateCode  string            `bson:"templateCode"`
	Subject       string            `bson:"subject"`
	TemplateParam map[string]string `bson:"templateParam"`
	Result        *SendDsmsResult   `bson:"result"`
	BusinessId    string            `bson:"businessId"`
	BusinessType  string            `bson:"businessType"`
	CreatedAt     time.Time         `bson:"createdAt"`
	UpdatedAt     time.Time         `bson:"updatedAt"`
}

type SendDsmsResult struct {
	Code        string    `bson:"code"`
	Message     string    `bson:"message"`
	BizId       string    `bson:"bizId"`
	RequestId   string    `bson:"requestId"`
	ErrCode     string    `bson:"errCode"`
	ErrMessage  string    `bson:"errMessage"`
	ReceiveDate time.Time `bson:"receiveDate,omitempty"`
	SendDate    time.Time `bson:"sendDate,omitempty"`
	SendStatus  string    `bson:"sendStatus"`
}

func (self *DsmsSendLog) Create(ctx context.Context) {
	_, err := extension.DBRepository.Insert(ctx, C_DSMS_SEND_LOG, self)
	if err != nil {
		log.Warn(ctx, "insert dsms send log error", log.Fields{"err": err})
	}
}

func (*DsmsSendLog) FindByPagination(ctx context.Context, page extension.PagingCondition) (int, []DsmsSendLog) {
	var dsmsSendLogs []DsmsSendLog
	total, _ := extension.DBRepository.FindByPagination(ctx, C_DSMS_SEND_LOG, page, &dsmsSendLogs)
	return total, dsmsSendLogs
}

var dsmsSendStatusMap = map[int64]string{
	1: DSMS_SEND_STATUS_PENDING,
	2: DSMS_SEND_STATUS_FAILED,
	3: DSMS_SEND_STATUS_SUCCEED,
}

func (self *DsmsSendLog) SyncSendResult(ctx context.Context) {
	sendDate := self.CreatedAt.Format(aliyun.REQUEST_DATE_FORMAT)
	detail, err := aliyun.QuerySendDetails(ctx, self.Phone, self.Result.BizId, sendDate)
	if err != nil {
		log.Warn(ctx, "sync dsms send result error", log.Fields{
			"accountId":   util.GetAccountId(ctx),
			"dsmsSendLog": self,
			"err":         err,
		})
		return
	}
	if detail.SendStatus == nil {
		return
	}

	var updateErr error

	status := dsmsSendStatusMap[*detail.SendStatus]
	if status == "" {
		status = DSMS_SEND_STATUS_PENDING
		if detail.ErrCode != nil {
			status = DSMS_SEND_STATUS_FAILED
			if *detail.ErrCode == "DELIVERED" {
				status = DSMS_SEND_STATUS_SUCCEED
			}
		}
	}

	defer func() {
		if status == DSMS_SEND_STATUS_FAILED && updateErr == nil {
			CAccountQuota.RollbackConsumed(ctx, ACCOUNT_QUOTA_TYPE_DIGITAL_SMS, 1, self.Id.Hex())
		}
	}()

	_set := bson.M{
		"result.sendStatus": status,
		"updatedAt":         time.Now(),
	}
	if detail.ErrCode != nil {
		_set["result.errCode"] = *detail.ErrCode
	}
	if detail.ReceiveDate != nil {
		_set["result.receiveDate"] = aliyun.TransStrToTime(*detail.ReceiveDate)
	}
	if detail.SendDate != nil {
		_set["result.sendDate"] = aliyun.TransStrToTime(*detail.SendDate)
	}

	selector := bson.M{
		"_id":       self.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}
	updateErr = extension.DBRepository.UpdateOne(ctx, C_DSMS_SEND_LOG, selector, bson.M{"$set": _set})
}
