package model

import (
	"context"
	"errors"
	"fmt"
	"mairpc/core/log"
	"mairpc/core/util/copier"
	"math"
	"os"
	"sync"
	"time"
	"unicode/utf8"

	"github.com/qiniu/qmgo"

	"mairpc/core/component"
	"mairpc/core/extension"
	core_util "mairpc/core/util"
	"mairpc/proto/account"
	share_component "mairpc/service/share/component"
	share_sms "mairpc/service/share/component/sms"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"

	"github.com/spf13/viper"
)

const (
	C_SMS_SEND_LOG                = "smsSendLog"
	NOTIFICATION_TEMPLATE_CODE    = "SMS_142605078"
	CAPTCHA_TEMPLATE_CODE         = "SMS_139465116"
	MARKETING_TEMPLATE_CODE       = "SMS_464035846"
	GAT_TEMPLATE_CODE             = "SMS_157447300" // 港澳台
	ALIYUN_SMS_SERVICE_NAME       = "aliyunsms"
	STATUS_PENDING                = "PENDING"
	SMS_TYPE_ALIYUNQA_SMS         = "aliyunqa_sms"
	SMS_TYPE_ALIYUNQA_DIGITAL_SMS = "aliyunqa_digital_sms"

	SMS_SEND_STATUS_FAILED  = "FAILED"
	SMS_SEND_STATUS_SUCCEED = "SUCCEED"
)

var (
	SMS_TEMPLATE_CODE_MAP = map[account.SendSmsRequest_TemplateType]string{
		account.SendSmsRequest_NOTICE:            NOTIFICATION_TEMPLATE_CODE,
		account.SendSmsRequest_VERIFICATION_CODE: CAPTCHA_TEMPLATE_CODE,
		account.SendSmsRequest_MARKETING:         MARKETING_TEMPLATE_CODE,
		account.SendSmsRequest_HK_MAC_TW_INT:     GAT_TEMPLATE_CODE,
	}

	EXCLUDE_BUSINESS_TYPES = []string{
		"systemVerificationCode",
	}

	CSmsSendLog = &SmsSendLog{}

	SmsServiceSettingMap = sync.Map{}
)

type SmsSendLog struct {
	Id           bson.ObjectId         `bson:"_id"`
	AccountId    bson.ObjectId         `bson:"accountId"`
	Phone        string                `bson:"phone"`
	Text         string                `bson:"text"`
	SmsTopic     string                `bson:"smsTopic"`
	TemplateCode string                `bson:"templateCode"`
	Operator     string                `bson:"operator"`
	BusinessId   string                `bson:"businessId"`
	BusinessType string                `bson:"businessType"`
	BusinessName string                `bson:"businessName"`
	MemberId     bson.ObjectId         `bson:"memberId,omitempty"`
	CreatedAt    time.Time             `bson:"createdAt"`
	Result       share_sms.SmsResponse `bson:"result"`
	SubType      string                `bson:"subType"`
	Provider     string                `bson:"provider"`
}

type SmsRequest map[string]string

type SmsResponse struct {
	RequestId   string    `bson:"requestId"`
	Code        string    `bson:"code"`
	Message     string    `bson:"message"`
	BizId       string    `bson:"bizId"`
	SendStatus  string    `bson:"sendStatus"`
	ErrCode     string    `bson:"errCode,omitempty"`
	SendDate    time.Time `bson:"sendDate,omitempty"`
	ReceiveDate time.Time `bson:"receiveDate,omitempty"`
	BillCount   int       `bson:"billCount,omitempty"`
}

func (s *SmsSendLog) Create(ctx context.Context) {
	_, err := extension.DBRepository.Insert(ctx, C_SMS_SEND_LOG, s)
	if err != nil {
		log.Warn(ctx, "SmsSendLog", log.Fields{"err": err})
	}
}

func SendSms(ctx context.Context, smsReq *account.SendSmsRequest) error {
	currentAccount := &SimpleAccount{}
	if util.StrInArray(smsReq.SubType, &[]string{share_sms.SMS_TYPE_ALIYUNQA_SMS, share_sms.SMS_TYPE_ALIYUNQA_DIGITAL_SMS}) || smsReq.SmsTopic == "" {
		currentAccount = CSimpleAccount.GetCurrentAccount(ctx)
	}
	if smsReq.SmsTopic == "" {
		smsReq.SmsTopic = currentAccount.GetSmsTopic()
	}
	smsSender := GetSmsSender(ctx, smsReq, currentAccount)
	var (
		smsResponse *share_sms.SmsResponse
		err         error
	)
	if share_sms.IsForeignPhone(smsReq.Phone) && !canSendForeignPhone(ctx, smsSender) {
		smsResponse = &share_sms.SmsResponse{
			Message:    "can not send sms to foreign phone",
			SendStatus: SMS_SEND_STATUS_FAILED,
		}
		err = errors.New(smsResponse.Message)
	} else {
		smsResponse, err = smsSender.Send(ctx, smsReq)
	}
	if err == nil {
		smsResponse.BillCount = calcBillCount(smsReq)
	}
	component.GO(ctx, func(ctx context.Context) {
		createSmsSendLog(ctx, smsReq, smsResponse)
	})
	if err != nil {
		return err
	}
	if !core_util.StrInArray(smsReq.BusinessType, &EXCLUDE_BUSINESS_TYPES) && smsResponse.SendStatus != SMS_SEND_STATUS_FAILED {
		SendAccountQuotaConsumeEvent(ctx, ACCOUNT_QUOTA_TYPE_SMS, int64(smsResponse.BillCount))
	}

	if smsResponse.Code != share_sms.SEND_SMS_SUCCESS_CODE {
		return errors.New(smsResponse.Message)
	}
	return nil
}

func canSendForeignPhone(ctx context.Context, sender share_sms.SmsSender) bool {
	needCheck := false
	switch sender.(type) {
	case *share_sms.AliyunSmsSender:
		needCheck = true
	case *share_sms.BiostimeSmsSender:
		return false
	}
	if !needCheck {
		return true
	}
	if util.StrInArray(viper.GetString("env"), &[]string{
		"ausnutria-production",
		"ausnutria-staging",
		"medela-production",
		"medela-staging",
		"smcp-production",
		"smcp-staging",
	}) {
		return true
	}
	if util.StrInArray(util.GetAccountId(ctx), &[]string{
		"620ef52ce953332d0a730b14", // NIKE Campaign
		"5dcca73ee2f24538ad226e10", // Nike NTC Pro
		"5d888ac98b29464d5a741bdc", // NIKE UAT
		"5c920674580717207a34481c", // Nike总览2
		"5b6123e42e379e647b1d233e", // Nike Digital Studio
		"5b03bda871ded745674cde24", // Nike Supply Chain
		"570e35dc905e88d66f8b4585", // NikeLab
		"570e34d2905e88d46f8b4582", // NikeWomen
		"570e348e905e88d66f8b4578", // NikeRunClub
		"56fa4377ba1b82a4378b4798", // NikeJDI
		"554c6f65ba1b8202108b457c", // Nike Basketball
		"559b7615fd604ab7768b4567", // UFS TW
		"55e417cfd5e2ceb63c8b4567", // UFS HK
	}) {
		return true
	}
	return false
}

func createSmsSendLog(ctx context.Context, smsReq *account.SendSmsRequest, resp *share_sms.SmsResponse) {
	if resp == nil {
		resp = &share_sms.SmsResponse{}
	}
	id := resp.SendLogId
	if !id.Valid() {
		id = bson.NewObjectId()
	}
	log := SmsSendLog{
		Id:           id,
		AccountId:    util.GetAccountIdAsObjectId(ctx),
		CreatedAt:    time.Now(),
		Phone:        smsReq.Phone,
		Text:         fmt.Sprintf("【%s】%s", smsReq.SmsTopic, smsReq.Text),
		SmsTopic:     smsReq.SmsTopic,
		TemplateCode: SMS_TEMPLATE_CODE_MAP[smsReq.MessageType],
		Result:       *resp,
		Operator:     smsReq.Operator,
		BusinessId:   smsReq.BusinessId,
		BusinessType: smsReq.BusinessType,
		BusinessName: smsReq.BusinessName,
		SubType:      smsReq.SubType,
		Provider: func() string {
			if util.StrInArray(smsReq.SubType, &[]string{share_sms.SMS_TYPE_ALIYUNQA_SMS, share_sms.SMS_TYPE_ALIYUNQA_DIGITAL_SMS}) {
				return "aliyunqa"
			}
			smsProvider := viper.GetString("sms-provider")
			smsSetting := GetSmsSetting(ctx)
			if smsSetting.provider != "" {
				smsProvider = smsSetting.provider
			}
			// 默认使用阿里云
			if smsProvider == "" {
				return share_sms.SMS_PROVIDER_ALIYUN
			}
			return smsProvider
		}(),
	}

	if smsReq.MemberId != "" {
		log.MemberId = bson.ObjectIdHex(smsReq.MemberId)
	}

	log.Create(ctx)
}
func calcBillCount(smsReq *account.SendSmsRequest) int {
	// 如果是营销短信则再加上退订后缀
	if smsReq.MessageType == account.SendSmsRequest_MARKETING {
		smsReq.Text = fmt.Sprintf("%s拒收请回复R", smsReq.Text)
	}
	text := fmt.Sprintf("【%s】%s", smsReq.SmsTopic, smsReq.Text)
	textLength := utf8.RuneCountInString(text)
	if textLength <= 70 {
		return 1
	}
	// 参考的 portal 的逻辑
	return int(math.Ceil(float64(textLength) / 67.0))
}

func IsNewAccount(currentAccount *SimpleAccount) bool {
	// 新开租户才需要在 smsQuota 不足时禁止发短信。
	return currentAccount.CreatedAt.After(time.Date(2021, time.January, 1, 11, 0, 0, 0, time.Local))
}

func GetSmsSender(ctx context.Context, req *account.SendSmsRequest, currentAccount *SimpleAccount) share_sms.SmsSender {
	if req == nil {
		return nil
	}

	if util.StrInArray(req.SubType, &[]string{share_sms.SMS_TYPE_ALIYUNQA_SMS, share_sms.SMS_TYPE_ALIYUNQA_DIGITAL_SMS}) {
		if currentAccount == nil || currentAccount.Oem.Type != "aliyunqa" {
			return nil
		}

		creator := ""
		if req.UserId != "" {
			user, _ := CUser.GetByIdWithCache(ctx, req.UserId)
			if user != nil {
				creator = user.ExternalUser.Id
			}
		}
		aliyunqaOem := share_component.AliyunqaOem{}
		copier.Instance(nil).From(currentAccount.Oem).CopyTo(&aliyunqaOem)
		return &share_sms.AliyunqaSmsSender{
			Oem:     aliyunqaOem,
			Creator: creator,
		}
	}
	smsProvider := viper.GetString("sms-provider")
	smsSetting := GetSmsSetting(ctx)
	if smsSetting.provider != "" {
		smsProvider = smsSetting.provider
	}
	aliyunSender := &share_sms.AliyunSmsSender{
		AccessKey:     smsSetting.accessKeyId,
		AccessSecret:  smsSetting.accessKeySecret,
		TemplateCodes: smsSetting.templateCodes,
	}
	switch smsProvider {
	case share_sms.SMS_PROVIDER_ALIYUN:
		return aliyunSender
	case share_sms.SMS_PROVIDER_PICC:
		return &share_sms.PICCSmsSender{}
	case share_sms.SMS_PROVIDER_TENCENT:
		return &share_sms.TencentSmsSender{}
	case share_sms.SMS_PROVIDER_CN_NICE:
		return &share_sms.NiceSmsSender{
			OemType: currentAccount.Oem.Type,
		}
	case share_sms.SMS_PROVIDER_HISENSE:
		return &share_sms.HisenseSmsSender{}
	case share_sms.SMS_PROVIDER_ZRWINFO:
		return &share_sms.ZrwinfoSmsSender{
			AccessKeyId:           smsSetting.accessKeyId,
			AccessKeySecret:       smsSetting.accessKeySecret,
			IntlAccessKeyId:       smsSetting.intlAccessKeyId,
			IntlAccessKeySecret:   smsSetting.intlAccessKeySecret,
			NotifyAccessKeyId:     smsSetting.notifyAccessKeyId,
			NotifyAccessKeySecret: smsSetting.notifyAccessKeySecret,
		}
	case share_sms.SMS_PROVIDER_BIOSTIME:
		return &share_sms.BiostimeSmsSender{
			SpCode:    os.Getenv("BIOSTIME_SMS_SP_CODE"),
			LoginName: os.Getenv("BIOSTIME_SMS_LOGIN_NAME"),
			Password:  os.Getenv("BIOSTIME_SMS_PASSWORD"),
		}
	default:
		return aliyunSender
	}
}

func (s *SmsSendLog) GetAllByCondition(ctx context.Context, selector bson.M, sortor []string, limit int) ([]SmsSendLog, error) {
	smsSendLog := []SmsSendLog{}
	err := extension.DBRepository.FindAll(ctx, C_SMS_SEND_LOG, selector, sortor, limit, &smsSendLog)
	if err != nil {
		return nil, err
	}
	return smsSendLog, nil
}

func (m *SmsSendLog) UpdateResultByIdOrBizId(ctx context.Context) error {
	var selector bson.M
	if !m.AccountId.IsZero() && !m.Id.IsZero() {
		selector = bson.M{
			"accountId": m.AccountId,
			"_id":       m.Id,
		}
	} else {
		selector = bson.M{
			"result.bizId": m.Result.BizId,
		}
		if m.Phone != "" {
			selector["phone"] = m.Phone
		}
	}

	setter := bson.M{
		"result.sendStatus": m.Result.SendStatus,
		"updatedAt":         time.Now(),
	}
	if m.Result.ErrCode != "" {
		setter["result.errCode"] = m.Result.ErrCode
	}
	if !m.Result.ReceiveDate.IsZero() {
		setter["result.receiveDate"] = m.Result.ReceiveDate
	}

	change := qmgo.Change{
		Update: bson.M{
			"$set": setter,
		},
	}
	return extension.DBRepository.FindAndApply(ctx, C_SMS_SEND_LOG, selector, nil, change, &m)
}

type smsSetting struct {
	provider              string
	accessKeyId           string
	accessKeySecret       string
	intlAccessKeyId       string
	intlAccessKeySecret   string
	notifyAccessKeyId     string
	notifyAccessKeySecret string
	templateCodes         map[string]string
}

func GetSmsSetting(ctx context.Context) smsSetting {
	if val, ok := SmsServiceSettingMap.Load(util.GetAccountId(ctx)); ok {
		return val.(smsSetting)
	}
	condition := Common.GenDefaultCondition(ctx)
	condition["service"] = SERVICE_SETTING_TYPE_SMS
	serviceSetting, _ := CServiceSetting.GetByCondition(ctx, condition)
	if serviceSetting != nil {
		setting := smsSetting{
			provider:              serviceSetting.Provider,
			accessKeyId:           serviceSetting.Sms.AccessKeyId,
			accessKeySecret:       serviceSetting.Sms.AccessKeySecret,
			intlAccessKeyId:       serviceSetting.Sms.IntlAccessKeyId,
			intlAccessKeySecret:   serviceSetting.Sms.IntlAccessKeySecret,
			notifyAccessKeyId:     serviceSetting.Sms.NotifyAccessKeyId,
			notifyAccessKeySecret: serviceSetting.Sms.NotifyAccessKeySecret,
			templateCodes:         serviceSetting.Sms.TemplateCodes,
		}
		SmsServiceSettingMap.Store(util.GetAccountId(ctx), setting)
		return setting
	}
	SmsServiceSettingMap.Store(util.GetAccountId(ctx), smsSetting{})
	return smsSetting{}
}
