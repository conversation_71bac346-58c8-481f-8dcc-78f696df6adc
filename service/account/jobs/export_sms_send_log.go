package jobs

import (
	"context"
	"encoding/csv"
	"fmt"
	"github.com/spf13/cobra"
	"mairpc/core/component"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/service/account/model"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"
	"os"
	"time"
)

func init() {
	RootCmd.AddCommand(exportSmsSendLog)
}

type ExportSmsSendLogReq struct {
	StartAt time.Time `json:"startAt"`
	EndAt   time.Time `json:"endAt"`
}

var exportSmsSendLog = &cobra.Command{
	Use: "exportSmsSendLog",
	RunE: func(cmd *cobra.Command, args []string) error {
		var (
			ctx      = core_util.CtxWithReadSecondaryPreferred(util.GetContextInJob(args))
			req      = ExportSmsSendLogReq{}
			logChan  = make(chan model.SmsSendLog, 5)
			lineChan = make(chan []string, 5)
		)
		util.UnmarshalArgs(args, &req)
		component.GO(ctx, func(ctx context.Context) {
			iterateSmsSendLog(ctx, req, logChan)
		})
		component.GO(ctx, func(ctx context.Context) {
			formatSmsSendLog(logChan, lineChan)
		})
		fileUrl, err := job_util.ExportFile(ctx, fmt.Sprintf("export_sms_send_log_%s.csv", util.GetJobTimestamp(time.Now())), func(f *os.File) error {
			writer := csv.NewWriter(f)
			defer writer.Flush()
			for {
				line, ok := <-lineChan
				if !ok {
					break
				}
				writer.Write(line)
			}
			return nil
		})
		if err != nil {
			return err
		}
		log.Warn(ctx, "sms send log exported", log.Fields{
			"url": fileUrl,
		})
		return nil
	},
}

func iterateSmsSendLog(ctx context.Context, req ExportSmsSendLogReq, logChan chan model.SmsSendLog) {
	defer close(logChan)
	var (
		condition = bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
		}
		idCondition = bson.M{}
		startId     bson.ObjectId
		endId       bson.ObjectId
	)
	if !req.StartAt.IsZero() {
		startId = bson.NewObjectIdWithTime(req.StartAt)
	}
	if !req.EndAt.IsZero() {
		endId = bson.NewObjectIdWithTime(req.EndAt)
	}
	for {
		if startId.Valid() {
			idCondition["$gt"] = startId
		}
		if endId.Valid() {
			idCondition["$lt"] = endId
		}
		if len(idCondition) > 0 {
			condition["_id"] = idCondition
		}
		var logs []model.SmsSendLog
		extension.DBRepository.FindAll(ctx, model.C_SMS_SEND_LOG, condition, []string{"_id"}, 200, &logs)
		if len(logs) == 0 {
			break
		}
		startId = logs[len(logs)-1].Id
		for _, sendLog := range logs {
			logChan <- sendLog
		}
	}
}

func formatSmsSendLog(logChan chan model.SmsSendLog, lineChan chan []string) {
	defer close(lineChan)
	lineChan <- []string{
		"手机号",
		"发送时间",
		"发送结果",
		"错误码",
	}
	for {
		smsSendLog, ok := <-logChan
		if !ok {
			break
		}
		lineChan <- []string{
			smsSendLog.Phone,
			smsSendLog.CreatedAt.Local().Format(core_util.COMMON_TIME_LAYOUT),
			smsSendLog.Result.SendStatus,
			smsSendLog.Result.ErrCode,
		}
	}
}
