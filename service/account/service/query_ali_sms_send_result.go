package service

import (
	"context"
	"mairpc/core/component"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/service/account/model"
	"mairpc/service/share/component/sms"
	share_sms "mairpc/service/share/component/sms"
	"mairpc/service/share/util"
	"sync"
	"time"

	openapi "github.com/alibabacloud-go/darabonba-openapi/client"
	alisms "github.com/alibabacloud-go/dysmsapi-********/v2/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/spf13/viper"
)

func (AccountService) QueryAliSmsSendResult(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	component.GO(ctx, queryAliSmsSendResult)
	return &response.EmptyResponse{}, nil
}

func queryAliSmsSendResult(ctx context.Context) {
	var (
		provider   = viper.GetString("sms-provider")
		condition  = model.Common.GenDefaultCondition(ctx)
		maxCount   = 500
		smsSetting *model.ServiceSetting
		wg         = &sync.WaitGroup{}
	)
	condition["service"] = model.SERVICE_SETTING_TYPE_SMS
	smsSetting, _ = model.CServiceSetting.GetByCondition(ctx, condition)
	if smsSetting == nil {
		smsSetting = &model.ServiceSetting{}
	}
	if smsSetting.Provider != "" {
		provider = smsSetting.Provider
	}
	if provider != "" && provider != share_sms.SMS_PROVIDER_ALIYUN {
		return
	}
	logs, err := model.CSmsSendLog.GetAllByCondition(ctx, bson.M{
		"accountId":         util.GetAccountIdAsObjectId(ctx),
		"result.sendStatus": model.STATUS_PENDING,
		"subType": bson.M{"$nin": []string{
			"aliyunqa_sms",
			"aliyunqa_digital_sms",
		}},
	}, nil, maxCount)
	if err != nil {
		log.Warn(ctx, "failed to get sms send log", log.Fields{"err": err})
		return
	}
	if len(logs) == 0 {
		return
	}
	pool, err := util.NewGoroutinePoolWithPanicHandler(5, util.WithContext(ctx))
	if err != nil {
		log.Warn(ctx, "failed to create goroutine pool", log.Fields{"err": err})
		return
	}
	defer pool.Release()
	for _, smsSendLog := range logs {
		wg.Add(1)
		pool.Submit(func() {
			defer wg.Done()
			handleAliSmsSendResult(ctx, &smsSendLog, *smsSetting)
		})
	}
	wg.Wait()
}

func handleAliSmsSendResult(ctx context.Context, smsSendLog *model.SmsSendLog, smsSetting model.ServiceSetting) {
	config := &openapi.Config{
		AccessKeyId:     tea.String(core_util.GetAliyunSmsAKId()),
		AccessKeySecret: tea.String(core_util.GetAliyunSmsAKSecret()),
		RegionId:        tea.String("cn-hangzhou"),
	}
	// 只有使用了直连通道的情况下才使用直连通道的 AK
	if !util.StrInArray(smsSendLog.TemplateCode, &[]string{
		sms.NOTIFICATION_TEMPLATE_CODE,
		sms.CAPTCHA_TEMPLATE_CODE,
		sms.MARKETING_TEMPLATE_CODE,
		sms.GAT_TEMPLATE_CODE,
	}) && smsSetting.Sms.AccessKeyId != "" && smsSetting.Sms.AccessKeySecret != "" {
		config.SetAccessKeyId(smsSetting.Sms.AccessKeyId)
		config.SetAccessKeySecret(smsSetting.Sms.AccessKeySecret)
	}
	client, err := alisms.NewClient(config)
	if err != nil {
		log.Warn(ctx, "failed to create alisms client", log.Fields{"err": err})
		return
	}
	resp, _ := client.QuerySendDetails(&alisms.QuerySendDetailsRequest{
		PhoneNumber: tea.String(smsSendLog.Phone),
		BizId:       tea.String(smsSendLog.Result.BizId),
		SendDate:    tea.String(smsSendLog.CreatedAt.Format("20060102")),
		CurrentPage: tea.Int64(1),
		PageSize:    tea.Int64(1),
	})
	result := alisms.QuerySendDetailsResponseBodySmsSendDetailDTOsSmsSendDetailDTO{}
	if resp != nil && resp.Body != nil && resp.Body.SmsSendDetailDTOs != nil && len(resp.Body.SmsSendDetailDTOs.SmsSendDetailDTO) > 0 {
		result = *resp.Body.SmsSendDetailDTOs.SmsSendDetailDTO[0]
	}
	if resp == nil {
		smsSendLog.Result.SendStatus = model.SMS_SEND_STATUS_SUCCEED
		smsSendLog.Result.ErrCode = "DELIVERED"
		smsSendLog.Result.SendDate = smsSendLog.CreatedAt
		smsSendLog.Result.ReceiveDate = smsSendLog.CreatedAt
	} else {
		switch *result.SendStatus {
		case 1:
			if time.Since(smsSendLog.CreatedAt) > time.Hour*24 {
				smsSendLog.Result.SendStatus = model.SMS_SEND_STATUS_SUCCEED
				smsSendLog.Result.ErrCode = "DELIVERED"
				smsSendLog.Result.SendDate = smsSendLog.CreatedAt
				smsSendLog.Result.ReceiveDate = smsSendLog.CreatedAt
			}
		case 2:
			smsSendLog.Result.SendStatus = model.SMS_SEND_STATUS_FAILED
			smsSendLog.Result.ErrCode = *result.ErrCode
		case 3:
			smsSendLog.Result.SendStatus = model.SMS_SEND_STATUS_SUCCEED
			smsSendLog.Result.ErrCode = *result.ErrCode
			smsSendLog.Result.SendDate, _ = time.Parse(core_util.COMMON_TIME_LAYOUT, *result.SendDate)
			smsSendLog.Result.ReceiveDate, _ = time.Parse(core_util.COMMON_TIME_LAYOUT, *result.ReceiveDate)
		}
	}
	smsSendLog.UpdateResultByIdOrBizId(ctx)
	if smsSendLog.Result.SendStatus == model.SMS_SEND_STATUS_FAILED {
		rollbackSmsQuota(ctx, int64(smsSendLog.Result.BillCount), smsSendLog.Id.Hex())
	}
}
