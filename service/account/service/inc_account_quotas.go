package service

import (
	"mairpc/core/validators"
	"mairpc/proto/account"
	"mairpc/proto/common/response"
	"mairpc/service/account/model"
	account_model "mairpc/service/account/model"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

func (AccountService) IncAccountQuotas(ctx context.Context, req *account.IncAccountQuotasRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	accountQuota := account_model.AccountQuota{
		Type:           req.Type,
		RemainingTotal: req.Count,
	}

	if req.Type == model.ACCOUNT_QUOTA_TYPE_VOD {
		accountQuota.RemainingTotal = gbToByte(accountQuota.RemainingTotal)
	}

	var err error
	if req.IsRollback {
		err = rollbackSmsQuota(ctx, req.Count, req.TraceId)
		if err != nil {
			return nil, err
		}
		return &response.EmptyResponse{}, nil
	} else {
		err = accountQuota.Upsert(ctx, req.TraceId)
	}
	if err != nil {
		return nil, err
	}
	return &response.EmptyResponse{}, nil
}

func rollbackSmsQuota(ctx context.Context, count int64, traceId string) error {
	if !util.StrInArray(util.GetAccountId(ctx), &[]string{
		"61c5231743421661cc4489d3", // CREED
		"61d7feb6ab08ee258b2346f2", // Perfume Box
		"621c41ca32d340686361c4a2", // INSPACE
		"655462a25572f211c21d8922", // SMN
	}) || count <= 0 {
		return nil
	}
	return account_model.CAccountQuota.RollbackConsumed(ctx, account_model.ACCOUNT_QUOTA_TYPE_SMS, count, traceId)
}
