package service

import (
	"fmt"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/util"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/account"
	"mairpc/proto/common/types"
	"mairpc/service/account/model"
	share_util "mairpc/service/share/util"
	"strings"
	"time"

	"github.com/qiniu/qmgo"
	"github.com/spf13/viper"

	"golang.org/x/net/context"
)

const (
	MODULE_COMMON       = "common"
	MODULE_NAVIGATOR    = "navigator"
	MODULE_ORGANIZATION = "organization"
)

var (
	managementMenus = []string{
		"sensitive",
		"channel",
		"service",
		"enterprise",
		"token",
	}
)

func (AccountService) UpsertUserRole(ctx context.Context, req *account.UpsertUserRoleRequest) (*account.UpsertUserRoleResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	if isExistSameName(ctx, req.Id, req.Name) {
		return nil, errors.NewAlreadyExistsError("name")
	}
	formatReqPermissions(ctx, req)

	condition := bson.M{"isDeleted": false}
	apps := model.CApp.GetAllByCondition(ctx, condition, []string{"order"})
	appIds := util.ToStringArray(util.ExtractArrayField("AppId", apps))
	currentAccount := model.CSimpleAccount.GetCurrentAccount(ctx)

	menus, permissions, err := genBasicMenusAndPermissions(ctx, currentAccount, appIds, req)
	if err != nil {
		return nil, err
	}

	_, navigatorExist := menus[MODULE_NAVIGATOR]
	_, organizationExist := menus[MODULE_ORGANIZATION]

	if navigatorExist {
		menus[MODULE_NAVIGATOR] = append(menus[MODULE_NAVIGATOR], getModuleMenus(req, "dashboard")...)
	}
	if organizationExist {
		menus[MODULE_NAVIGATOR] = append(menus[MODULE_NAVIGATOR], getModuleMenus(req, "organization")...)
	}

	permissions["management"] = share_util.StrArrayDiff(permissions["management"], managementMenus)
	for _, commonMenu := range getModuleMenus(req, MODULE_COMMON) {
		if util.StrInArray(commonMenu, &managementMenus) {
			permissions["management"] = append(permissions["management"], commonMenu)
		} else if navigatorExist {
			menus[MODULE_NAVIGATOR] = append(menus[MODULE_NAVIGATOR], commonMenu)
			menus[MODULE_NAVIGATOR] = append(menus[MODULE_NAVIGATOR], getModuleMenus(req, "organization")...)
			menus[MODULE_NAVIGATOR] = append(menus[MODULE_NAVIGATOR], getModuleMenus(req, "coupon")...)
		}
	}

	err = handleFrontendv1Name(ctx, menus, permissions)
	if err != nil {
		return nil, err
	}

	handleCouponModule(permissions, menus)
	// https://gitlab.maiscrm.com/mai/home/<USER>/issues/31320
	handleTradeModule(permissions, menus)

	for key, values := range permissions {
		permissions[key] = share_util.StrArrayUnique(values)
	}
	for key, values := range menus {
		menus[key] = share_util.StrArrayUnique(values)
	}

	selector := bson.M{}
	var currentRoleId bson.ObjectId
	if bson.IsObjectIdHex(req.Id) {
		currentRoleId = bson.ObjectIdHex(req.Id)
	} else {
		currentRoleId = bson.NewObjectId()
	}
	selector["_id"] = currentRoleId

	setter := bson.M{
		"name":        req.Name,
		"description": req.Description,
		"updatedAt":   time.Now(),
		"menus":       menus,
		"permissions": permissions,
	}

	if req.DataPermissions != nil {
		setter["dataPermissions"] = genUserDataPermissions(req.DataPermissions.DataPermissions)
	}
	setOnInsert := bson.M{
		"createdAt": time.Now(),
		"accountId": share_util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	if userId := core_util.GetUserId(ctx); userId != "" {
		setOnInsert["createdBy"] = bson.ObjectIdHex(userId)
	}
	updater := bson.M{
		"$set":         setter,
		"$setOnInsert": setOnInsert,
	}
	change := qmgo.Change{
		Update: updater,
		Upsert: true,
	}
	userRole := model.UserRole{}
	err = extension.DBRepository.FindAndApply(ctx, model.C_USER_ROLE, selector, nil, change, &userRole)
	if err != nil {
		return nil, err
	}

	// 如果 userRole.Id 存在则说明是更新，如果 userRole.Name 和 req.Name 不一致说明 name 发生了更新，需要更新 user.Role.Name
	if userRole.Id.Valid() && userRole.Name != req.Name {
		model.CUser.UpdateRoleName(ctx, userRole.Id, req.Name)
	}
	// ufs 环境更新权限后需重新登陆
	env := viper.GetString("env")
	if strings.Contains(env, "ufs") {
		users := model.CUser.GetUsersByRoleId(ctx, userRole.Id)
		for _, user := range users {
			key := fmt.Sprintf(PORTAL_TOKEN_FORMAT, user.Id.Hex())
			extension.RedisClient.Del(key)
		}
	}

	return &account.UpsertUserRoleResponse{Id: currentRoleId.Hex()}, nil
}

func genBasicMenusAndPermissions(ctx context.Context, account *model.SimpleAccount, appIds []string, req *account.UpsertUserRoleRequest) (map[string][]string, map[string][]string, error) {
	// 处理 organization， 存入 user 的 menus 中
	appIds = append(appIds, "organization", "customer", "coupon", "trade")
	delete(account.Menus, "organization")
	menus := map[string][]string{}
	permissions := map[string][]string{}

	for _, p := range req.Permissions {
		if p.Module == MODULE_COMMON {
			continue
		}
		if util.StrInArray(p.Module, &appIds) {
			if len(p.Menus) > 0 || p.Module == MODULE_NAVIGATOR {
				menus[p.Module] = p.Menus
			}
			for _, child := range p.Children {
				menus[child.Key] = child.Children
			}
		}
		if _, ok := account.Menus[p.Module]; ok || !util.StrInArray(p.Module, &appIds) {
			if len(p.Menus) > 0 {
				permissions[p.Module] = p.Menus
			}
			for _, child := range p.Children {
				permissions[child.Key] = child.Children
			}
		}
	}

	if bson.IsObjectIdHex(req.Id) {
		selector := model.Common.GenDefaultConditionById(ctx, bson.ObjectIdHex(req.Id))
		userRole, err := model.CUserRole.GetByCondition(ctx, selector)
		if err != nil {
			return nil, nil, err
		}
		// 新版管理中心修改角色权限时保留模块原有的 management 权限
		for k := range menus {
			if k == MODULE_NAVIGATOR {
				continue
			}
			if dbMenus, ok := userRole.Menus[k]; ok {
				for _, menu := range dbMenus {
					if strings.HasPrefix(menu, "management.") {
						menus[k] = append(menus[k], menu)
					}
				}
			}
		}
	}

	return menus, permissions, nil
}

func genUserDataPermissions(reqDataPermissions []*account.BusinessDataPermission) []model.BusinessUserDataPermission {
	reqDataPermissions = filterInvalidDataPermissions(reqDataPermissions)
	dataPermissions := []model.BusinessUserDataPermission{}
	copier.Instance(nil).From(reqDataPermissions).CopyTo(&dataPermissions)
	return dataPermissions
}

func getModuleMenus(req *account.UpsertUserRoleRequest, module string) []string {
	for _, p := range req.Permissions {
		if p.Module == module {
			return p.Menus
		}
	}

	return []string{}
}

func isExistSameName(ctx context.Context, id, name string) bool {
	condition := model.Common.GenDefaultCondition(ctx)
	condition["name"] = name
	if id != "" {
		condition["_id"] = bson.M{"$ne": bson.ObjectIdHex(id)}
	}

	userRole, _ := model.CUserRole.GetByCondition(ctx, condition)
	if userRole != nil && userRole.Id.Valid() {
		return true
	}

	return false
}

func handleFrontendv1Name(ctx context.Context, menus, permissions map[string][]string) error {
	businesses := []string{}
	names := []string{}
	for module, tempMenus := range menus {
		businesses = append(businesses, module)
		names = append(names, tempMenus...)
	}
	condition := bson.M{
		"business": bson.M{"$in": businesses},
		"name":     bson.M{"$in": names},
	}
	userMenus, err := model.CUserMenu.GetAllByCondition(ctx, condition)
	if err != nil {
		return err
	}
	if len(userMenus) == 0 {
		return nil
	}

	userMenuMap := map[string]model.UserMenu{}
	for _, userMenu := range userMenus {
		userMenuMap[fmt.Sprintf("%s:%s", userMenu.Business, userMenu.Name)] = userMenu
	}

	for business, tempMenus := range menus {
		for _, menu := range tempMenus {
			userMenu, ok := userMenuMap[fmt.Sprintf("%s:%s", business, menu)]
			if ok && userMenu.Frontendv1Name != "" {
				addFrontendv1Permission(permissions, userMenu.Frontendv1Name)
			}
			if ok && !userMenu.ParentId.Valid() {
				cond := bson.M{
					"parentId": userMenu.Id,
				}
				subUserMenus, err := model.CUserMenu.GetAllByCondition(ctx, cond)
				if err != nil {
					return err
				}
				for _, subUserMenu := range subUserMenus {
					if subUserMenu.Frontendv1Name != "" {
						addFrontendv1Permission(permissions, subUserMenu.Frontendv1Name)
					}
				}
			}
		}
	}

	return nil
}

func addFrontendv1Permission(permissions map[string][]string, frontendv1Name string) {
	frontendv1NameSlice := strings.Split(frontendv1Name, ".")
	if len(frontendv1NameSlice) != 2 {
		return
	}
	module, name := frontendv1NameSlice[0], frontendv1NameSlice[1]
	if _, ok := permissions[module]; ok {
		permissions[module] = append(permissions[module], name)
	} else {
		permissions[module] = []string{name}
	}
}

func handleTradeModule(permissions, menus map[string][]string) {
	tradeMenus, ok := menus["trade"]
	if !ok {
		return
	}
	for _, menu := range tradeMenus {
		if menu == "connection" {
			permissions["management"] = append(permissions["management"], "tradeConnect")
			continue
		}
		if menu == "trade.order.view.manage" {
			permissions["analytic"] = append(permissions["analytic"], "order")
			continue
		}
		permissions["analytic"] = append(permissions["analytic"], menu)
	}
}

func handleCouponModule(permissions, menus map[string][]string) {
	couponMenus, ok := menus["coupon"]
	if !ok {
		return
	}
	for _, menu := range couponMenus {
		if value, ok := couponFrontendv1NamesMap[menu]; ok {
			permissions["coupon"] = append(permissions["coupon"], value)
		}
	}
}

func filterInvalidDataPermissions(reqDataPermissions []*account.BusinessDataPermission) []*account.BusinessDataPermission {
	for i, bdp := range reqDataPermissions {
		permissions := []*types.DataPermission{}
		for j, dp := range bdp.Permissions {
			if dp.Type != "" {
				permissions = append(permissions, bdp.Permissions[j])
			}
		}
		reqDataPermissions[i].Permissions = permissions
	}
	return reqDataPermissions
}

// formatReqPermissions 格式化 req.Permissions，删除 req 中前端传过来的 frontendv1 权限
func formatReqPermissions(ctx context.Context, req *account.UpsertUserRoleRequest) {
	condition := bson.M{
		"frontendv1Name": bson.M{
			"$exists": true,
		},
	}
	hasFrontendv1NameUserMenus, _ := model.CUserMenu.GetAllByCondition(ctx, condition)
	frontendv1Names := []string{}
	for _, userMenu := range hasFrontendv1NameUserMenus {
		frontendv1Names = append(frontendv1Names, userMenu.Frontendv1Name)
	}
	if len(frontendv1Names) == 0 {
		return
	}
	var formattedPermissions []*account.Permission
	ignoreModules := []string{
		"coupon",
		"analytic",
		"childcarescrm",
		"dmsstore",
		"diaptamil",
	}
	for _, p := range req.Permissions {
		// 用户运营-卡券权限，存在 "frontendv1Name" : "coupon.receiveRecord" 的 userMenu, 会将前端选中的权限删除掉，所以这里不作处理
		if util.StrInArray(p.Module, &ignoreModules) {
			formattedPermissions = append(formattedPermissions, p)
			continue
		}
		haveFrontendv1Permission := false
		for _, menu := range p.Menus {
			if util.StrInArray(fmt.Sprintf("%s.%s", p.Module, menu), &frontendv1Names) {
				haveFrontendv1Permission = true
				break
			}
		}
		if !haveFrontendv1Permission {
			formattedPermissions = append(formattedPermissions, p)
		}
	}
	req.Permissions = formattedPermissions
}
