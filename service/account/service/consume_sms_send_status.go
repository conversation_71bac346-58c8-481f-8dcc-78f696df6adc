package service

import (
	"context"
	"encoding/json"

	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	pb_account "mairpc/proto/account"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/service/account/model"
	share_sms "mairpc/service/share/component/sms"
)

const ALL_DSN_ACCOUNT_KEY_TEMPLATE = "account:all-dsn-account"

func (accountService AccountService) ConsumeSmsSendStatus(ctx context.Context, req *pb_account.ConsumeSmsSendStatusRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if (req.AccountId == "" || req.SmsSendLogId == "") && req.BizId == "" {
		return nil, errors.NewInvalidArgumentErrorWithMessage("accountId", "pass on accountId and smsSendLogId or bizId at least one")
	}

	smsSendLog := &model.SmsSendLog{
		Phone: req.Phone,
		Result: share_sms.SmsResponse{
			BizId:       req.BizId,
			SendStatus:  req.SendStatus,
			ErrCode:     req.ErrCode,
			ReceiveDate: core_util.ParseRFC3339(req.ReceiveDate),
		},
	}
	traceId := req.BizId
	if req.AccountId != "" {
		ctx = core_util.DuplicateContextWithAid(ctx, req.AccountId)
		smsSendLog.AccountId = bson.ObjectIdHex(req.AccountId)
	}
	if req.SmsSendLogId != "" {
		smsSendLog.Id = bson.ObjectIdHex(req.SmsSendLogId)
		traceId = smsSendLog.Id.Hex()
	}

	if !smsSendLog.AccountId.IsZero() && !smsSendLog.Id.IsZero() {
		if err := smsSendLog.UpdateResultByIdOrBizId(ctx); err != nil {
			return nil, err
		}
		if req.SendStatus == model.SMS_SEND_STATUS_FAILED {
			rollbackSmsQuota(ctx, int64(smsSendLog.Result.BillCount), traceId)
		}
		return &response.EmptyResponse{}, nil
	}

	resp, err := accountService.getDsnAccount(ctx)
	if err != nil {
		return &response.EmptyResponse{}, nil
	}
	for _, aid := range resp.DsnAccounts {
		ctx := core_util.DuplicateContextWithAid(ctx, aid)
		if err := smsSendLog.UpdateResultByIdOrBizId(ctx); err == nil {
			if req.SendStatus == model.SMS_SEND_STATUS_FAILED {
				rollbackSmsQuota(ctx, int64(smsSendLog.Result.BillCount), traceId)
			}
			break
		}
	}

	return &response.EmptyResponse{}, nil
}

func (accountService AccountService) getDsnAccount(ctx context.Context) (*pb_account.GetDsnAccountResponse, error) {
	redisKey, _ := extension.RedisClient.Get(ALL_DSN_ACCOUNT_KEY_TEMPLATE)
	if resp, ok := loadDsnAccountResponseCache(redisKey); ok {
		return resp, nil
	}
	resp, err := accountService.GetDsnAccount(ctx, &request.EmptyRequest{})
	if err != nil {
		return nil, err
	}
	cacheDsnAccountResponse(redisKey, resp)
	return resp, nil
}

func loadDsnAccountResponseCache(key string) (*pb_account.GetDsnAccountResponse, bool) {
	val, err := extension.RedisClient.GetResponseCache(key)
	if err != nil {
		return nil, false
	}
	cacheStr, err := core_util.UnzipString(val)
	if err != nil {
		return nil, false
	}
	resp := &pb_account.GetDsnAccountResponse{}
	err = json.Unmarshal([]byte(cacheStr), resp)
	if err != nil {
		return nil, false
	}
	return resp, true
}

func cacheDsnAccountResponse(key string, response *pb_account.GetDsnAccountResponse) error {
	b, err := json.Marshal(response)
	if err != nil {
		return err
	}
	s, err := core_util.ZipString(b)
	if err != nil {
		return err
	}
	_, err = extension.RedisClient.SetResponseCache(key, int64(CHANNEL_CACHE_DURATION), s)
	return err
}
