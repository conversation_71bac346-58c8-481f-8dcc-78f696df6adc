package service

import (
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/proto/account"
	"mairpc/service/account/model"
	"mairpc/service/share/component/dsms/common"
	"mairpc/service/share/util"
	"strconv"
	"time"

	"golang.org/x/net/context"
)

func (self *AccountService) TencentZjDsmsSendResult(ctx context.Context, req *account.TencentZjDsmsSendResultRequest) (*account.TencentZjDsmsSendResultResponse, error) {
	log.Warn(ctx, "receive tencent zj dsms send result", log.Fields{"result": req})
	selector := bson.M{
		"accountId":         util.GetAccountIdAsObjectId(ctx),
		"provider":          common.ProviderTencentZj,
		"phone":             req.DestNo,
		"result.bizId":      req.MessageID,
		"result.sendStatus": model.DSMS_SEND_STATUS_PENDING,
	}
	status := model.DSMS_SEND_STATUS_FAILED
	if req.RptStatus == "0" {
		status = model.DSMS_SEND_STATUS_SUCCEED
	}
	errMessage := req.RptMsg
	if req.RptStatus == "-1" {
		errMessage = "珠玑侧未收到短信运营商的状态报告，超时"
	}

	var updateErr error

	defer func() {
		if status == model.DSMS_SEND_STATUS_FAILED && updateErr == nil {
			model.CAccountQuota.RollbackConsumed(ctx, model.ACCOUNT_QUOTA_TYPE_DIGITAL_SMS, 1, req.MessageID)
		}
	}()

	_set := bson.M{
		"result.sendStatus": status,
		"result.errMessage": errMessage,
		"updatedAt":         time.Now(),
	}
	if req.SubmitTime != "" {
		_set["result.receiveDate"] = transStrToTime(req.SubmitTime)
	}
	if req.Timestamp != "" {
		timestamp, err := strconv.ParseInt(req.Timestamp, 10, 64)
		if err == nil {
			_set["result.sendDate"] = time.Unix(timestamp, 0)
		}
	}
	updateErr = extension.DBRepository.UpdateOne(ctx, model.C_DSMS_SEND_LOG, selector, bson.M{"$set": _set})
	return &account.TencentZjDsmsSendResultResponse{
		Code: 0,
		Msg:  "received",
	}, nil
}

func transStrToTime(timeStr string) time.Time {
	timeFormat := "**************"
	location := time.Now().Location()
	t, _ := time.ParseInLocation(timeFormat, timeStr, location)
	return t
}
