/*
* CODE GENERATED AUTOMATICALLY
* THIS FILE MUST NOT BE EDITED BY HAND
 */

package fake

import (
	"context"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/util/copier"

	"github.com/qiniu/qmgo"
	qmgo_options "github.com/qiniu/qmgo/options"
	"github.com/stretchr/testify/mock"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type FakeDBRepository struct {
	mock.Mock
}

func (self *FakeDBRepository) Aggregate(ctx context.Context, collectionName string, pipeline interface{}, one bool, result interface{}) error {
	mockTestArgs := self.Called(ctx, collectionName, pipeline, one, result)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) BatchUpdate(ctx context.Context, collectionName string, docs ...interface{}) (*mongo.BulkWriteResult, *mongo.BulkWriteException) {
	mockTestArgs := self.Called(ctx, collectionName, docs)
	var err *mongo.BulkWriteException
	if mockTestArgs.Get(1) != nil {
		err = mockTestArgs.Get(1).(*mongo.BulkWriteException)
	}
	return mockTestArgs.Get(0).(*mongo.BulkWriteResult), err
}

func (self *FakeDBRepository) BatchUpdateUnordered(ctx context.Context, collectionName string, docs ...interface{}) (*mongo.BulkWriteResult, *mongo.BulkWriteException) {
	mockTestArgs := self.Called(ctx, collectionName, docs)
	var err *mongo.BulkWriteException
	if mockTestArgs.Get(1) != nil {
		err = mockTestArgs.Get(1).(*mongo.BulkWriteException)
	}
	return mockTestArgs.Get(0).(*mongo.BulkWriteResult), err
}

func (self *FakeDBRepository) BatchUpsert(ctx context.Context, collectionName string, docs ...interface{}) (*mongo.BulkWriteResult, *mongo.BulkWriteException) {
	mockTestArgs := self.Called(ctx, collectionName, docs)
	var err *mongo.BulkWriteException
	if mockTestArgs.Get(1) != nil {
		err = mockTestArgs.Get(1).(*mongo.BulkWriteException)
	}
	return mockTestArgs.Get(0).(*mongo.BulkWriteResult), err
}

func (self *FakeDBRepository) Count(ctx context.Context, collectionName string, selector primitive.M) (int, error) {
	mockTestArgs := self.Called(ctx, collectionName, selector)
	return mockTestArgs.Int(0), mockTestArgs.Error(1)
}

func (self *FakeDBRepository) CreateCollection(ctx context.Context, name string) error {
	mockTestArgs := self.Called(ctx, name)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) Distinct(ctx context.Context, collectionName string, selector primitive.M, key string, result interface{}) error {
	mockTestArgs := self.Called(ctx, collectionName, selector, key, result)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) FindAll(ctx context.Context, collectionName string, selector primitive.M, sortor []string, limit int, result interface{}) error {
	mockTestArgs := self.Called(ctx, collectionName, selector, sortor, limit, result)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) FindAllWithFields(ctx context.Context, collectionName string, selector primitive.M, fields primitive.M, sortor []string, limit int, result interface{}) error {
	mockTestArgs := self.Called(ctx, collectionName, selector, fields, sortor, limit, result)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) FindAllWithHint(ctx context.Context, collectionName string, selector primitive.M, sortor []string, limit int, hint string, result interface{}) error {
	mockTestArgs := self.Called(ctx, collectionName, selector, sortor, limit, hint, result)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) FindAndApply(ctx context.Context, collectionName string, selector primitive.M, sort []string, change qmgo.Change, result interface{}) error {
	mockTestArgs := self.Called(ctx, collectionName, selector, sort, change, result)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) FindByPK(ctx context.Context, collectionName string, id interface{}, result interface{}) error {
	mockTestArgs := self.Called(ctx, collectionName, id, result)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) FindByPagination(ctx context.Context, collectionName string, page extension.PagingCondition, result interface{}) (int, error) {
	mockTestArgs := self.Called(ctx, collectionName, page, result)
	return mockTestArgs.Int(0), mockTestArgs.Error(1)
}

func (self *FakeDBRepository) FindByPaginationWithFields(ctx context.Context, collectionName string, page extension.PagingCondition, result interface{}, fields primitive.M) (int, error) {
	mockTestArgs := self.Called(ctx, collectionName, page, result, fields)
	return mockTestArgs.Int(0), mockTestArgs.Error(1)
}

func (self *FakeDBRepository) FindByPaginationWithoutCount(ctx context.Context, collectionName string, page extension.PagingCondition, result interface{}) error {
	mockTestArgs := self.Called(ctx, collectionName, page, result)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) FindByPaginationWithoutCountWithHint(ctx context.Context, collectionName string, page extension.PagingCondition, hint string, result interface{}) error {
	mockTestArgs := self.Called(ctx, collectionName, page, result, hint)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) FindOne(ctx context.Context, collectionName string, selector primitive.M, result interface{}) error {
	mockTestArgs := self.Called(ctx, collectionName, selector, result)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) FindOneWithSortor(ctx context.Context, collectionName string, selector primitive.M, sortor []string, result interface{}) error {
	mockTestArgs := self.Called(ctx, collectionName, selector, sortor, result)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) Insert(ctx context.Context, collectionName string, docs ...interface{}) ([]bson.ObjectId, error) {
	mockTestArgs := self.Called(ctx, collectionName, docs)
	return mockTestArgs.Get(0).([]bson.ObjectId), mockTestArgs.Error(1)
}

func (self *FakeDBRepository) InsertUnordered(ctx context.Context, collectionName string, docs ...interface{}) (*mongo.BulkWriteResult, *mongo.BulkWriteException) {
	mockTestArgs := self.Called(ctx, collectionName, docs)
	return mockTestArgs.Get(0).(*mongo.BulkWriteResult), mockTestArgs.Get(1).(*mongo.BulkWriteException)
}

func (self *FakeDBRepository) Iterate(ctx context.Context, collectionName string, selector primitive.M, sortor []string) (qmgo.CursorI, error) {
	mockTestArgs := self.Called(ctx, collectionName, selector, sortor)
	return mockTestArgs.Get(0).(qmgo.CursorI), mockTestArgs.Error(1)
}

func (self *FakeDBRepository) IterateWithOption(ctx context.Context, collectionName string, selector bson.M, opt extension.IterateOption) (qmgo.CursorI, error) {
	mockTestArgs := self.Called(ctx, collectionName, selector, opt)
	return mockTestArgs.Get(0).(qmgo.CursorI), mockTestArgs.Error(1)
}

func (self *FakeDBRepository) RemoveAll(ctx context.Context, collectionName string, selector primitive.M) (int, error) {
	mockTestArgs := self.Called(ctx, collectionName, selector)
	return mockTestArgs.Int(0), mockTestArgs.Error(1)
}

func (self *FakeDBRepository) RemoveOne(ctx context.Context, collectionName string, selector primitive.M) error {
	mockTestArgs := self.Called(ctx, collectionName, selector)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) Transaction(ctx context.Context, transactionFunc func(sessCtx context.Context) (interface{}, error), opts ...*extension.TransactionOption) (interface{}, error) {
	resp, err := transactionFunc(ctx)
	_ = self.Called(ctx, transactionFunc, opts)
	return resp, err
}

func (self *FakeDBRepository) UpdateAll(ctx context.Context, collectionName string, selector primitive.M, updator primitive.M) (int, error) {
	mockTestArgs := self.Called(ctx, collectionName, selector, updator)
	return mockTestArgs.Int(0), mockTestArgs.Error(1)
}

func (self *FakeDBRepository) UpdateAllWithOptions(ctx context.Context, collectionName string, selector primitive.M, updator primitive.M, opts ...qmgo_options.UpdateOptions) (int, error) {
	mockTestArgs := self.Called(ctx, collectionName, selector, updator, opts)
	return mockTestArgs.Int(0), mockTestArgs.Error(1)
}

func (self *FakeDBRepository) UpdateAllWithAggregation(ctx context.Context, collectionName string, selector primitive.M, pipeline []primitive.M) (int, error) {
	mockTestArgs := self.Called(ctx, collectionName, selector, pipeline)
	return mockTestArgs.Int(0), mockTestArgs.Error(1)
}

func (self *FakeDBRepository) UpdateOne(ctx context.Context, collectionName string, selector primitive.M, updator primitive.M) error {
	mockTestArgs := self.Called(ctx, collectionName, selector, updator)
	return mockTestArgs.Error(0)
}

func (self *FakeDBRepository) Upsert(ctx context.Context, collectionName string, selector primitive.M, updator primitive.M) (interface{}, error) {
	mockTestArgs := self.Called(ctx, collectionName, selector, updator)
	return mockTestArgs.Get(0), mockTestArgs.Error(1)
}

type FakeCursor struct {
	data   []interface{}
	index  int
	length int
}

func NewFakeCursor(d []interface{}) *FakeCursor {
	return &FakeCursor{
		data:   d,
		index:  0,
		length: len(d),
	}
}

// result should be a pointer
func (f *FakeCursor) Next(result interface{}) bool {
	if f.index >= f.length {
		return false
	}
	copier.Instance(nil).From(f.data[f.index]).CopyTo(result)
	f.index = f.index + 1
	return true
}

func (f *FakeCursor) Close() error {
	return nil
}

func (f *FakeCursor) Err() error {
	return nil
}

func (f *FakeCursor) All(results interface{}) error {
	return copier.Instance(nil).From(f.data).CopyTo(results)
}
