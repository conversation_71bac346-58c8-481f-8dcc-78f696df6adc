/*
* CODE GENERATED AUTOMATICALLY
* THIS FILE MUST NOT BE EDITED BY HAND
 */

package fake

import (
	"mairpc/service/share/component"

	"github.com/stretchr/testify/mock"

	"golang.org/x/net/context"
)

type FakeWeConnect struct {
	mock.Mock
}

func (self *FakeWeConnect) CreateQrcode(ctx context.Context, channelId string, data *component.NewQrcode) (*component.Qrcode, error) {
	args := self.Called(ctx, channelId, data)
	return args.Get(0).(*component.Qrcode), args.Error(1)
}

func (self *FakeWeConnect) GetChannel(ctx context.Context, accountId string) (*component.Channel, error) {
	args := self.Called(ctx, accountId)
	return args.Get(0).(*component.Channel), args.Error(1)
}

func (self *FakeWeConnect) GetCorpChainSharedWeAppChannel(ctx context.Context, corpId string) (*component.Channel, error) {
	args := self.Called(ctx, corpId)
	return args.Get(0).(*component.Channel), args.Error(1)
}

func (self *FakeWeConnect) GetChannels(ctx context.Context, accountIds []string, orderBy, ordering string) ([]component.Channel, error) {
	args := self.Called(ctx, accountIds, orderBy, ordering)
	return args.Get(0).([]component.Channel), args.Error(1)
}

func (self *FakeWeConnect) GetFollower(ctx context.Context, channelId string, userId string) (*component.Follower, error) {
	args := self.Called(ctx, channelId, userId)
	return args.Get(0).(*component.Follower), args.Error(1)
}

func (self *FakeWeConnect) GetFollowersWithUnion(ctx context.Context, unionId string, pFilter *map[string]string) ([]component.Follower, uint64, error) {
	args := self.Called(ctx, unionId, pFilter)
	return args.Get(0).([]component.Follower), args.Get(1).(uint64), args.Error(2)
}

func (self *FakeWeConnect) GetMenus(ctx context.Context, channelId string) (*component.Menus, error) {
	args := self.Called(ctx, channelId)
	return args.Get(0).(*component.Menus), args.Error(1)
}

func (self *FakeWeConnect) GetQrcode(ctx context.Context, channelId string, qrcodeId string) (*component.Qrcode, error) {
	args := self.Called(ctx, channelId, qrcodeId)
	return args.Get(0).(*component.Qrcode), args.Error(1)
}

func (self *FakeWeConnect) GetQrcodes(ctx context.Context, channelId string, pFilter *map[string]string, page uint32, pageSize uint32) ([]component.Qrcode, uint64, error) {
	args := self.Called(ctx, channelId, pFilter, page, pageSize)
	return args.Get(0).([]component.Qrcode), args.Get(1).(uint64), args.Error(2)
}

func (self *FakeWeConnect) SendTplMessage(ctx context.Context, channelId string, data *component.TplMessage) error {
	args := self.Called(ctx, channelId, data)
	return args.Error(0)
}

func (self *FakeWeConnect) SendWechatMessage(ctx context.Context, channelId string, userId string, data *component.Message) error {
	args := self.Called(ctx, channelId, userId, data)
	return args.Error(0)
}

func (self *FakeWeConnect) SendMassMessage(ctx context.Context, channelId string, data *component.MassMessage) error {
	args := self.Called(ctx, channelId, data)
	return args.Error(0)
}

func (self *FakeWeConnect) SendSubscribeMessage(ctx context.Context, channelId string, data *component.SubscribeMsg) (*component.SubscribeMsgResult, error) {
	args := self.Called(ctx, channelId, data)
	return args.Get(0).(*component.SubscribeMsgResult), args.Error(1)
}

func (self *FakeWeConnect) UpdateQrcode(ctx context.Context, channelId string, qrcodeId string, qrcodeContent map[string]interface{}) (*component.Qrcode, error) {
	args := self.Called(ctx, channelId, qrcodeId, qrcodeContent)
	return args.Get(0).(*component.Qrcode), args.Error(1)
}

func (self *FakeWeConnect) ProduceMqMessage(ctx context.Context, messageBody *component.MessageBody) (map[string]interface{}, error) {
	args := self.Called(ctx, messageBody)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (self *FakeWeConnect) ProduceCustomerEvent(ctx context.Context, eventBody *component.CustomerEventBody) (map[string]interface{}, error) {
	args := self.Called(ctx, eventBody)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (self *FakeWeConnect) Benchmark(ctx context.Context, body *component.BenchmarkRequest) error {
	args := self.Called(ctx, body)
	return args.Error(1)
}

func (self *FakeWeConnect) DecryptMiniProgram(ctx context.Context, channelId string, data *component.DecryptRequest) (map[string]string, error) {
	args := self.Called(ctx, channelId, data)
	return args.Get(0).(map[string]string), args.Error(1)
}

func (self *FakeWeConnect) GetMiniProgramPhoneByCode(ctx context.Context, req *component.ProxyRequest) (*component.GetMiniProgramPhoneByCodeResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetMiniProgramPhoneByCodeResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) AddMemberLevelSetting(ctx context.Context, req *component.ProxyRequest) (*component.WxCardCommonResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.WxCardCommonResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetMemberLevelSetting(ctx context.Context, req *component.ProxyRequest) (*component.GetMemberLevelSettingResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetMemberLevelSettingResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListMembers(ctx context.Context, req *component.ProxyRequest) (*component.ListMembersResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.ListMembersResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) UpdateMemberLevel(ctx context.Context, req *component.ProxyRequest) (*component.WxCardCommonResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.WxCardCommonResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetMemberScoreHistory(ctx context.Context, req *component.ProxyRequest) (*component.GetMemberScoreHistoryResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetMemberScoreHistoryResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetMemberInfo(ctx context.Context, req *component.ProxyRequest) (*component.GetMemberInfoResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetMemberInfoResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetMemberScore(ctx context.Context, req *component.ProxyRequest) (*component.GetMemberScoreResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetMemberScoreResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) UpdateMemberScore(ctx context.Context, req *component.ProxyRequest) (*component.WxCardCommonResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.WxCardCommonResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) SetWeshopRelatedWxa(ctx context.Context, req *component.ProxyRequest) (*component.WxCardCommonResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.WxCardCommonResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetWeshopRelatedWxa(ctx context.Context, req *component.ProxyRequest) (*component.GetWeshopRelatedWxaResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetWeshopRelatedWxaResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) DeleteWeshopRelatedWxa(ctx context.Context, req *component.ProxyRequest) (*component.WxCardCommonResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.WxCardCommonResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetWxaRelatedWeshops(ctx context.Context, req *component.ProxyRequest) (*component.GetWxaRelatedWeshopsResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetWxaRelatedWeshopsResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) CreateWeshopMember(ctx context.Context, req *component.ProxyRequest) (*component.WxCardCommonResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.WxCardCommonResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) UpdateWeshopMember(ctx context.Context, req *component.ProxyRequest) (*component.WxCardCommonResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.WxCardCommonResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetWeshopMember(ctx context.Context, req *component.ProxyRequest) (*component.GetWeshopMemberResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetWeshopMemberResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetWeshopMembers(ctx context.Context, req *component.ProxyRequest) (*component.GetWeshopMembersResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetWeshopMembersResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) DeleteWeshopMember(ctx context.Context, req *component.ProxyRequest) (*component.WxCardCommonResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.WxCardCommonResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListOrders(ctx context.Context, req *component.ProxyRequest) (*component.ListWeshopOrdersResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.ListWeshopOrdersResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetOrderDetails(ctx context.Context, req *component.ProxyRequest) (*component.GetWeshopOrderDetailsResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetWeshopOrderDetailsResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetAfterSaleOrder(ctx context.Context, req *component.ProxyRequest) (*component.GetAfterSaleOrderResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetAfterSaleOrderResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) OrderRefund(ctx context.Context, origin string, data *component.OrderRefundRequest) (*component.OrderRefundInfo, error) {
	args := self.Called(ctx, origin, data)
	return args.Get(0).(*component.OrderRefundInfo), args.Error(1)
}

func (self *FakeWeConnect) CreateProfitSharingReceiver(ctx context.Context, channelId string, data *component.CreateProfitSharingReceiverRequest) (map[string]interface{}, error) {
	args := self.Called(ctx, channelId, data)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (self *FakeWeConnect) DeleteProfitSharingReceiver(ctx context.Context, channelId string, data *component.DeleteProfitSharingReceiverRequest) (map[string]interface{}, error) {
	args := self.Called(ctx, channelId, data)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (self *FakeWeConnect) FinishProfitSharing(ctx context.Context, channelId string, data *component.FinishProfitSharingRequest) (map[string]interface{}, error) {
	args := self.Called(ctx, channelId, data)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (self *FakeWeConnect) GetWeappVersion(ctx context.Context, accountId string) (*component.GetWeappVersionResponse, error) {
	args := self.Called(ctx, accountId)
	return args.Get(0).(*component.GetWeappVersionResponse), args.Error(1)
}

func (self *FakeWeConnect) GetWeappChangeLogs(ctx context.Context, accountId string) (*component.GetWeappChangeLogsResponse, error) {
	args := self.Called(ctx, accountId)
	return args.Get(0).(*component.GetWeappChangeLogsResponse), args.Error(1)
}

func (self *FakeWeConnect) GetWeappQrCode(ctx context.Context, accountId string, data *component.GetQrCodeRequest) ([]byte, error) {
	args := self.Called(ctx, accountId)
	return args.Get(0).([]byte), args.Error(1)
}

func (self *FakeWeConnect) MultiProfitSharing(ctx context.Context, channelId string, data *component.MultiProfitSharingRequest) (map[string]interface{}, error) {
	args := self.Called(ctx, channelId, data)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (self *FakeWeConnect) GetProfitSharing(ctx context.Context, channelId, transactionId string) (component.OrderMultiProfitSharingResponse, error) {
	args := self.Called(ctx, channelId, transactionId)
	return args.Get(0).(component.OrderMultiProfitSharingResponse), args.Error(1)
}

func (self *FakeWeConnect) GetRefreshedProfitSharing(ctx context.Context, channelId, outOrderNo string) (component.OrderMultiProfitSharingResponse, error) {
	args := self.Called(ctx, channelId, outOrderNo)
	return args.Get(0).(component.OrderMultiProfitSharingResponse), args.Error(1)
}

func (self *FakeWeConnect) OrderRefundStatus(ctx context.Context, origin, outRefundNo string) (*component.OrderRefundInfo, error) {
	args := self.Called(ctx, origin, outRefundNo)
	return args.Get(0).(*component.OrderRefundInfo), args.Error(1)
}

func (self *FakeWeConnect) ReportWeappFormId(ctx context.Context, channelId string, data *component.ReportWeappFormIdRequest) (map[string]interface{}, error) {
	args := self.Called(ctx, channelId, data)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

// SendTplMessageToSingle provides a mock function with given fields: ctx, channelId, data
func (self *FakeWeConnect) SendTplMessageToSingle(ctx context.Context, channelId string, data *component.SingleTargetTplMessage) (*component.SingleTargetTplMessage, error) {
	args := self.Called(ctx, channelId, data)

	if args.Get(0) != nil {
		return args.Get(0).(*component.SingleTargetTplMessage), args.Error(1)
	}

	return nil, args.Error(1)
}

func (self *FakeWeConnect) GetSingleTemplateMessage(ctx context.Context, channelId, messageId string) (*component.SingleTargetTplMessage, error) {
	args := self.Called(ctx, channelId, messageId)

	if args.Get(0) != nil {
		return args.Get(0).(*component.SingleTargetTplMessage), args.Error(1)
	}

	return nil, args.Error(1)
}

func (self *FakeWeConnect) ListTemplates(ctx context.Context, channelId string) ([]component.BriefTemplate, error) {
	args := self.Called(ctx, channelId)

	if args.Get(0) != nil {
		return args.Get(0).([]component.BriefTemplate), args.Error(1)
	}

	return nil, args.Error(1)
}

func (self *FakeWeConnect) AddExpressOrder(ctx context.Context, accountId string, data *component.AddExpressOrderRequest) (*component.ExpressOrderResponse, error) {
	mockTestArgs := self.Called(ctx, accountId, data)
	return mockTestArgs.Get(0).(*component.ExpressOrderResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetDeliveries(ctx context.Context, accountId string) (*component.GetDeliveriesResponse, error) {
	mockTestArgs := self.Called(ctx, accountId)
	return mockTestArgs.Get(0).(*component.GetDeliveriesResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetExpressOrderPath(ctx context.Context, accountId string, data *component.GetExpressOrderPathRequest) (*component.GetExpressOrderPathResponse, error) {
	mockTestArgs := self.Called(ctx, accountId, data)
	return mockTestArgs.Get(0).(*component.GetExpressOrderPathResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetAllExpressAccounts(ctx context.Context, accountId string) (*component.GetAllExpressAccountsResponse, error) {
	mockTestArgs := self.Called(ctx, accountId)
	return mockTestArgs.Get(0).(*component.GetAllExpressAccountsResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) SendTransfer(ctx context.Context, origin string, data *component.SendTransferRequest) (*component.TransferDetailResponse, error) {
	mockTestArgs := self.Called(ctx, origin, data)
	return mockTestArgs.Get(0).(*component.TransferDetailResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) SendV2Transfer(ctx context.Context, origin string, data *component.SendV2TransferRequest) (*component.V2TransferDetailResponse, error) {
	mockTestArgs := self.Called(ctx, origin, data)
	return mockTestArgs.Get(0).(*component.V2TransferDetailResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetTransfer(ctx context.Context, origin, tradeNo string) (*component.TransferDetailResponse, error) {
	mockTestArgs := self.Called(ctx, origin, tradeNo)
	return mockTestArgs.Get(0).(*component.TransferDetailResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetV2Transfer(ctx context.Context, origin, tradeBatchNo, tradeNo string) (*component.V2TransferDetailResponse, error) {
	mockTestArgs := self.Called(ctx, origin, tradeBatchNo, tradeNo)
	return mockTestArgs.Get(0).(*component.V2TransferDetailResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListDepartments(ctx context.Context, channelId string) ([]component.Department, error) {
	mockTestArgs := self.Called(ctx, channelId)
	return mockTestArgs.Get(0).([]component.Department), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListDepartmentsById(ctx context.Context, channelId, departmentId string) ([]component.Department, error) {
	mockTestArgs := self.Called(ctx, channelId, departmentId)
	return mockTestArgs.Get(0).([]component.Department), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListDepartmentUsers(ctx context.Context, channelId, departmentId, fetchChild string) ([]component.User, error) {
	mockTestArgs := self.Called(ctx, channelId, departmentId, fetchChild)
	return mockTestArgs.Get(0).([]component.User), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetUser(ctx context.Context, channelId, userId string) (*component.User, error) {
	mockTestArgs := self.Called(ctx, channelId, userId)
	return mockTestArgs.Get(0).(*component.User), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetContactWay(ctx context.Context, channelId, contactWayId string) (*component.ContactWay, error) {
	mockTestArgs := self.Called(ctx, channelId, contactWayId)
	return mockTestArgs.Get(0).(*component.ContactWay), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) SearchContactWay(ctx context.Context, channelId string, req component.SearchContactWayRequest) (*component.SearchContactWayResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).(*component.SearchContactWayResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListContactWays(ctx context.Context, channelId string, req component.SearchContactWayRequest) (*component.SearchContactWayResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).(*component.SearchContactWayResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetContactWayByState(ctx context.Context, channelId, state string) (*component.ContactWay, error) {
	mockTestArgs := self.Called(ctx, channelId, state)
	return mockTestArgs.Get(0).(*component.ContactWay), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) CreateContactWay(ctx context.Context, channelId string, contactWay component.ContactWayRequest) (*component.ContactWay, error) {
	mockTestArgs := self.Called(ctx, channelId, contactWay)
	return mockTestArgs.Get(0).(*component.ContactWay), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) UpdateContactWay(ctx context.Context, channelId, contactWayId string, contactWay component.ContactWayRequest) (*component.ContactWay, error) {
	mockTestArgs := self.Called(ctx, channelId, contactWayId, contactWay)
	return mockTestArgs.Get(0).(*component.ContactWay), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) DeleteContactWay(ctx context.Context, channelId, contactWayId string) error {
	mockTestArgs := self.Called(ctx, channelId, contactWayId)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListContactWay(ctx context.Context, channelId string, groupIds []string) ([]component.ContactWay, error) {
	mockTestArgs := self.Called(ctx, channelId, groupIds)
	return mockTestArgs.Get(0).([]component.ContactWay), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListWelcomeMessages(ctx context.Context, channelId string, req *component.ListContactWelcomeMsgsRequest) (*component.ListContactWelcomeMsgsResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).(*component.ListContactWelcomeMsgsResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) DeleteWelcomeMessage(ctx context.Context, channelId, userId string) error {
	mockTestArgs := self.Called(ctx, channelId, userId)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) CreateGroupWelcomeTemplate(ctx context.Context, channelId string, req *component.GroupWelcomeTemplate) (*component.GroupWelcomeTemplate, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).(*component.GroupWelcomeTemplate), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) UpdateGroupWelcomeTemplate(ctx context.Context, channelId string, req *component.GroupWelcomeTemplate) (*component.WeconnectResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).(*component.WeconnectResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) DeleteGroupWelcomeTemplate(ctx context.Context, channelId, templateId string) error {
	mockTestArgs := self.Called(ctx, channelId, templateId)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListChats(ctx context.Context, channelId, cursor string, status int, userIds []string, partyIds []int) (string, []component.GroupchatBrief, error) {
	mockTestArgs := self.Called(ctx, channelId, cursor, status, userIds, partyIds)
	return mockTestArgs.Get(1).(string), mockTestArgs.Get(1).([]component.GroupchatBrief), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListChatMembers(ctx context.Context, channelId, contactWayId string) (*component.Groupchat, error) {
	mockTestArgs := self.Called(ctx, channelId, contactWayId)
	return mockTestArgs.Get(0).(*component.Groupchat), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListStatsChats(ctx context.Context, channelId string, req component.ListStatsChatsRequest) (component.ListStatsChatsResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).(component.ListStatsChatsResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) CreateGroupchatJoinWays(ctx context.Context, channelId string, req component.GroupchatJoinWaysRequest) (*component.GroupchatJoinWays, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).(*component.GroupchatJoinWays), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) UpdateGroupchatJoinWays(ctx context.Context, channelId, configId string, req component.GroupchatJoinWaysRequest) (*component.GroupchatJoinWays, error) {
	mockTestArgs := self.Called(ctx, channelId, configId, req)
	return mockTestArgs.Get(0).(*component.GroupchatJoinWays), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetGroupchatJoinWay(ctx context.Context, channelId, configId string) (*component.GroupchatJoinWays, error) {
	mockTestArgs := self.Called(ctx, channelId, configId)
	return mockTestArgs.Get(0).(*component.GroupchatJoinWays), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetCorp(ctx context.Context, corpId string) (*component.CorpDetail, error) {
	mockTestArgs := self.Called(ctx, corpId)
	return mockTestArgs.Get(0).(*component.CorpDetail), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListTags(ctx context.Context, corpId, channelId, appId string, tagIds []string) ([]*component.GroupTagsResponse, error) {
	mockTestArgs := self.Called(ctx, corpId, channelId, tagIds)
	return mockTestArgs.Get(0).([]*component.GroupTagsResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) AddTags(ctx context.Context, corpId, channelId string, req component.AddTagsRequest) (*component.GroupTagsResponse, error) {
	mockTestArgs := self.Called(ctx, corpId, channelId, req)
	return mockTestArgs.Get(0).(*component.GroupTagsResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) EditTag(ctx context.Context, corpId, channelId string, req component.EditTagRequest) error {
	mockTestArgs := self.Called(ctx, corpId, channelId, req)
	return mockTestArgs.Error(0)
}

func (self *FakeWeConnect) DeleteTags(ctx context.Context, corpId, channelId, appId string, req component.DeleteTagsRequest) error {
	mockTestArgs := self.Called(ctx, corpId, appId, req)
	return mockTestArgs.Error(0)
}

func (self *FakeWeConnect) EditMemberTags(ctx context.Context, corpId, channelId, appId string, req component.EditMemberTagsRequest) error {
	mockTestArgs := self.Called(ctx, corpId, appId, req)
	return mockTestArgs.Error(0)
}

func (self *FakeWeConnect) AddWechatTagToUsers(ctx context.Context, channelId string, wxTagId int, openIds []string) (map[string][]string, int, error) {
	mockTestArgs := self.Called(ctx, channelId, wxTagId, openIds)
	return mockTestArgs.Get(0).(map[string][]string), mockTestArgs.Get(1).(int), mockTestArgs.Error(2)
}

func (self *FakeWeConnect) RemoveWechatTagFromUsers(ctx context.Context, channelId string, wxTagId int, openIds []string) (interface{}, int, error) {
	mockTestArgs := self.Called(ctx, channelId, wxTagId, openIds)
	return mockTestArgs.Get(0).(interface{}), mockTestArgs.Get(1).(int), mockTestArgs.Error(2)
}

func (self *FakeWeConnect) GetExternalUser(ctx context.Context, channelId, externalUserId string) (component.GetExternalUserResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, externalUserId)
	return mockTestArgs.Get(0).(component.GetExternalUserResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetSystemExternalUser(ctx context.Context, channelId, externalUserId string) (component.GetSystemExternalUserResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, externalUserId)
	return mockTestArgs.Get(0).(component.GetSystemExternalUserResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListSystemExternalUser(ctx context.Context, channelId string, externalUserIds []string) (*component.ListSystemExternalUserResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, externalUserIds)
	return mockTestArgs.Get(0).(*component.ListSystemExternalUserResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListFollowExternalUsers(ctx context.Context, channelId, externalUserId string) (component.ListFollowExternalUserResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, externalUserId)
	return mockTestArgs.Get(0).(component.ListFollowExternalUserResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListExternalUser(ctx context.Context, channelId, userId, cursor string, limit string) (component.ListExternalUserResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, userId, cursor, limit)
	return mockTestArgs.Get(0).(component.ListExternalUserResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ConvertToOpenId(ctx context.Context, channelId, userId string) (string, error) {
	mockTestArgs := self.Called(ctx, channelId, userId)
	return mockTestArgs.String(0), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetAppDetail(ctx context.Context, channelId string) (component.AppDetail, error) {
	mockTestArgs := self.Called(ctx, channelId)
	return mockTestArgs.Get(0).(component.AppDetail), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) SendAliyunqaSms(ctx context.Context, req component.SendAliyunqaSmsRequest, isDigital bool, oem component.AliyunqaOem) (*component.SendAliyunqaSmsResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.SendAliyunqaSmsResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetAliyunqaSms(ctx context.Context, id string, oem component.AliyunqaOem) (*component.AliyunqaSmsDetailResponse, error) {
	mockTestArgs := self.Called(ctx, id)
	return mockTestArgs.Get(0).(*component.AliyunqaSmsDetailResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) SendWelcomeMessage(ctx context.Context, channelId string, req component.SendWelcomeMessageRequest) error {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) DownloadMediaMeterial(ctx context.Context, channelId, mediaType, mediaId string, isTemporary bool, savePath string) error {
	mockTestArgs := self.Called(ctx, channelId, mediaType, mediaId, isTemporary, savePath)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) CreateTemporaryMediaMaterial(ctx context.Context, channelId, mediaType, mediaUrl string) (*component.CreateTemporaryMediaMaterialResp, error) {
	mockTestArgs := self.Called(ctx, channelId, mediaType, mediaUrl)
	return mockTestArgs.Get(0).(*component.CreateTemporaryMediaMaterialResp), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) CreateGroupMessages(ctx context.Context, channelId string, req component.CreateGroupMessageRequest) (*component.CreateGroupMessageResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).(*component.CreateGroupMessageResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetPaymonetCredentialConfiguration(ctx context.Context, channel string, quncrmAccountId string) (*component.PaymentCredentialConfigurationResponse, error) {
	mockTestArgs := self.Called(ctx, channel, quncrmAccountId)
	return mockTestArgs.Get(0).(*component.PaymentCredentialConfigurationResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListWechatcpAdmins(ctx context.Context, channelId string) (*component.ListWechatcpAdminsResponse, error) {
	mockTestArgs := self.Called(ctx, channelId)
	return mockTestArgs.Get(0).(*component.ListWechatcpAdminsResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListWechatcpAuthUsers(ctx context.Context, channelId string) (*component.ListWechatcpAuthUsersResponse, error) {
	mockTestArgs := self.Called(ctx, channelId)
	return mockTestArgs.Get(0).(*component.ListWechatcpAuthUsersResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) SyncExternalUsers(ctx context.Context, channelId string, userIds []string, force, forceOld bool) error {
	mockTestArgs := self.Called(ctx, channelId, userIds, force, forceOld)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) UpdateWxCard(ctx context.Context, channelId string, req component.UpdateWxCardRequest) error {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) WeconnectProxy(ctx context.Context, channelId, method, path string, params, body, result interface{}) error {
	mockTestArgs := self.Called(ctx, channelId, method, path, params, body, result)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) WeconnectProxyV2(ctx context.Context, origin, channelId, method, path string, body, result interface{}) error {
	mockTestArgs := self.Called(ctx, origin, channelId, method, path, body, result)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) SendExternalContactMassMessage(ctx context.Context, channelId string, data *component.ExternalContactMassMessage) (*component.ExternalContactMassMessageResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, data)
	return mockTestArgs.Get(0).(*component.ExternalContactMassMessageResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetExternalContactMassMessageResult(ctx context.Context, channelId string, req *component.GetExternalContactMassMessage) (*component.ExternalContactMassMessageResult, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).(*component.ExternalContactMassMessageResult), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetMassMessageResultTaskList(ctx context.Context, channelId string, req *component.GetExternalContactMassMessage) (*component.MassMessageTaskList, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).(*component.MassMessageTaskList), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) CreateMassMessageTask(ctx context.Context, channelId string, data *component.ExternalContactMassMessage) (string, error) {
	mockTestArgs := self.Called(ctx, channelId, data)
	return mockTestArgs.Get(0).(string), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetMassMessageTaskResult(ctx context.Context, channelId, taskId string) (*component.MassMessageTaskResult, error) {
	mockTestArgs := self.Called(ctx, channelId, taskId)
	return mockTestArgs.Get(0).(*component.MassMessageTaskResult), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) CancelMassMessageTask(ctx context.Context, channelId, taskId string) error {
	mockTestArgs := self.Called(ctx, channelId, taskId)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) DeleteFollower(ctx context.Context, channelId, openId string) error {
	mockTestArgs := self.Called(ctx, channelId, openId)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetActiveCodeCount(ctx context.Context, channelId string) (uint64, error) {
	mockTestArgs := self.Called(ctx, channelId)
	return mockTestArgs.Get(0).(uint64), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) CreateRenewUserOrder(ctx context.Context, channelId string, req *component.CreateRenewUserOrderRequest) (string, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).(string), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetRenewUserResult(ctx context.Context, channelId, orderId string) (*component.GetRenewUserResultResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, orderId)
	return mockTestArgs.Get(0).(*component.GetRenewUserResultResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) CreateActiveUserTask(ctx context.Context, channelId string, userIds []string) (string, error) {
	mockTestArgs := self.Called(ctx, channelId, userIds)
	return mockTestArgs.Get(0).(string), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetActiveUserTaskResult(ctx context.Context, channelId, taskId string) (*component.ActiveUserTaskResult, error) {
	mockTestArgs := self.Called(ctx, channelId, taskId)
	return mockTestArgs.Get(0).(*component.ActiveUserTaskResult), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) TransferUsers(ctx context.Context, channelId string, requests []component.TransferUsersRequest) ([]component.TransferUsersResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, requests)
	return mockTestArgs.Get(0).([]component.TransferUsersResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetActiveUserDetail(ctx context.Context, channelId, userId string) (*component.ActiveUser, error) {
	mockTestArgs := self.Called(ctx, channelId, userId)
	return mockTestArgs.Get(0).(*component.ActiveUser), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ShareActiveCode(ctx context.Context, channelId string, req component.ShareActiveCodeRequest) (string, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).(string), mockTestArgs.Error(1)
} 

func (self *FakeWeConnect) GetShareActiveCodeResult(ctx context.Context, channelId, taskId string) (component.ShareActiveCodeResult, error) {
	mockTestArgs := self.Called(ctx, channelId, taskId)
	return mockTestArgs.Get(0).(component.ShareActiveCodeResult), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ConvertChatExternalUserIdToUnionId(ctx context.Context, channelId string, req *component.ConvertChatExternalUserIdToUnionIdRequest) ([]component.ConvertChatExternalUserIdToUnionIdResult, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).([]component.ConvertChatExternalUserIdToUnionIdResult), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) CreateWechatCoupon(ctx context.Context, req *component.CreateWechatCouponRequest) (*component.CreateWechatCouponResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.CreateWechatCouponResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) RedeemedWechatCoupon(ctx context.Context, req *component.RedeemedWechatCouponRequest) (*component.RedeemedWechatCouponResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.RedeemedWechatCouponResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) UpdateWechatCoupon(ctx context.Context, req *component.UpdateWechatCouponRequest) (*component.UpdateWechatCouponResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.UpdateWechatCouponResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) UpdateWechatCouponStock(ctx context.Context, req *component.UpdateWechatCouponStockRequest) (*component.UpdateWechatCouponStockResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.UpdateWechatCouponStockResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetWechatCouponSign(ctx context.Context, req map[string]interface{}) (*component.GetWechatCouponSignResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetWechatCouponSignResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ReturnWechatCoupon(ctx context.Context, req *component.ReturnWechatCouponRequest) (*component.ReturnWechatCouponResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.ReturnWechatCouponResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetWechatCoupon(ctx context.Context, req *component.GetWechatCouponRequest) (*component.GetWechatCouponResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetWechatCouponResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) UploadWechatMarketingImage(ctx context.Context, req *component.UploadWechatMarketingImageRequest) (*component.UploadWechatMarketingImageResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.UploadWechatMarketingImageResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) CorrectExternalUser(ctx context.Context, req *component.CorrectExternalUserRequest) error {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ConvertOpenIdAndUnionIdToExternalUserId(ctx context.Context, openId, unionId, channelId string) error {
	mockTestArgs := self.Called(ctx, openId, unionId, channelId)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) DownloadMiniProgramQrcode(ctx context.Context, channelId, savePath string, req *component.DownloadMiniProgramQrcodeRequest) error {
	mockTestArgs := self.Called(ctx, channelId, savePath, req)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) CreateCustomerAcquisitionLink(ctx context.Context, req *component.ProxyRequest) (*component.CreateCustomerAcquisitionLinkResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.CreateCustomerAcquisitionLinkResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetCustomerAcquisitionLink(ctx context.Context, req *component.ProxyRequest) (*component.GetCustomerAcquisitionLinkResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetCustomerAcquisitionLinkResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) UpdateCustomerAcquisitionLink(ctx context.Context, req *component.ProxyRequest) (*component.UpdateCustomerAcquisitionLinkResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.UpdateCustomerAcquisitionLinkResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) DeleteCustomerAcquisitionLink(ctx context.Context, req *component.ProxyRequest) (*component.DeleteCustomerAcquisitionLinkResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.DeleteCustomerAcquisitionLinkResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetCustomerAcquisitionQuota(ctx context.Context, req *component.ProxyRequest) (*component.GetCustomerAcquisitionQuotaResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.GetCustomerAcquisitionQuotaResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetSuiteAppInfo(ctx context.Context, corpId, suiteId string) (*component.GetSuiteAppInfoResponse, error) {
	mockTestArgs := self.Called(ctx, corpId, suiteId)
	return mockTestArgs.Get(0).(*component.GetSuiteAppInfoResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListMerchantCashCoupons(ctx context.Context, req *component.ProxyRequest) (*component.ListMerchantCashCouponsResponse, error) {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Get(0).(*component.ListMerchantCashCouponsResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetPaymentConfigurationByChannel(ctx context.Context, origin, channelId string) (*component.PaymentCredentialConfigurationResponse, error) {
	mockTestArgs := self.Called(ctx, origin, channelId)
	return mockTestArgs.Get(0).(*component.PaymentCredentialConfigurationResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListStatsStaffContact(ctx context.Context, channelId string, req component.ListStatsStaffContactRequest) (component.ListStatsStaffContactResponse, error) {
	mockTestArgs := self.Called(ctx, channelId, req)
	return mockTestArgs.Get(0).(component.ListStatsStaffContactResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) AddOrRemoveCallback(ctx context.Context, req component.AddOrRemoveCallbackRequest) error {
	mockTestArgs := self.Called(ctx, req)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListChatMessageHistories(ctx context.Context, corpId string, req component.ListChatMessageHistoriesRequest) (*component.ListChatMessageHistoriesResponse, error) {
	mockTestArgs := self.Called(ctx, corpId, req)
	return mockTestArgs.Get(0).(*component.ListChatMessageHistoriesResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListChatMessageHistoryPreviews(ctx context.Context, corpId string, req component.ListChatMessageHistoryPreviewsRequest) (*component.ListChatMessageHistoryPreviewsResponse, error) {
	mockTestArgs := self.Called(ctx, corpId, req)
	return mockTestArgs.Get(0).(*component.ListChatMessageHistoryPreviewsResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ManualSyncChatMessageHistories(ctx context.Context, corpId string) error {
	mockTestArgs := self.Called(ctx, corpId)
	return mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetChatMessageHistoriesSyncInfo(ctx context.Context, corpId string) (*component.ChatMessageHistoriesSyncInfo, error) {
	mockTestArgs := self.Called(ctx, corpId)
	return mockTestArgs.Get(0).(*component.ChatMessageHistoriesSyncInfo), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) RedoChatMessageHistoryRecognizeSpeech(ctx context.Context, corpId, messageId string) (*component.ChatMessageHistory, error) {
	mockTestArgs := self.Called(ctx, corpId, messageId)
	return mockTestArgs.Get(0).(*component.ChatMessageHistory), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetMessageAuditStatus(ctx context.Context, corpId string) (*component.MessageAuditStatus, error) {
	mockTestArgs := self.Called(ctx, corpId)
	return mockTestArgs.Get(0).(*component.MessageAuditStatus), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetMessageAuditPermitUsers(ctx context.Context, corpId string) ([]string, error) {
	mockTestArgs := self.Called(ctx, corpId)
	return mockTestArgs.Get(0).([]string), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) ListTemplateMessageResults(ctx context.Context, channelId string, resultIds []string) ([]component.TemplateMessageResult, error) {
	mockTestArgs := self.Called(ctx, channelId, resultIds)
	return mockTestArgs.Get(0).([]component.TemplateMessageResult), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) LoginAuthWeshop(ctx context.Context, providerId, code string) (*component.LoginAuthResponse, error) {
	mockTestArgs := self.Called(ctx, providerId, code)
	return mockTestArgs.Get(0).(*component.LoginAuthResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) GetBoughtServiceInfo(ctx context.Context, providerId, appId string, serviceId uint64) (*component.ServiceInfoResponse, error) {
	mockTestArgs := self.Called(ctx, providerId, appId, serviceId)
	return mockTestArgs.Get(0).(*component.ServiceInfoResponse), mockTestArgs.Error(1)
}

func (self *FakeWeConnect) SyncChannel(ctx context.Context, providerId, appId string) (*component.Channel, error) {
	mockTestArgs := self.Called(ctx, providerId, appId)
	return mockTestArgs.Get(0).(*component.Channel), mockTestArgs.Error(1)
}
