package test

import (
	"fmt"
	"mairpc/core/client"
	"mairpc/core/component"
	core_component "mairpc/core/component"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/interceptor"
	"mairpc/core/log"
	"mairpc/core/validators"
	"mairpc/service/member/model"
	member_service "mairpc/service/member/service"
	share_component "mairpc/service/share/component"
	"mairpc/service/share/util"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"testing"
	"time"

	uuid "github.com/satori/go.uuid"
	conf "github.com/spf13/viper"
	"golang.org/x/net/context"
	"google.golang.org/grpc/metadata"

	"github.com/stretchr/testify/suite"
)

const (
	COMMON_ACCOUNT_ID = "5e7872a773ee1200fb1bec32"
	RETAIL_ACCOUNT_ID = "6278bbad702f413c245257c3"
	V1_SENDER_ACCOUNT_ID = "658e7a7887199f0e0a24ac42"

	ERNOLASZLO_TEST_ACCOUNT_ID = "6389646aca0fb06b442ba732" // 群脉开发测试(奥伦纳素)
)

type BaseFeatureSuite struct {
	*suite.Suite
}

func (suite *BaseFeatureSuite) SetupTest() {
	core_component.InFeatureTest = true
	share_component.InFeatureTest = true
	Setup()
}

func (suite *BaseFeatureSuite) TearDownTest() {
}

func (suite *BaseFeatureSuite) GetCommonAccountCtxV1() context.Context {
	return context.WithValue(suite.GenCtxByAccountId(COMMON_ACCOUNT_ID), member_service.EVENT_TASK_VERSION_KEY, "v1")
}

func (suite *BaseFeatureSuite) GetCommonAccountCtx() context.Context {
	return context.WithValue(suite.GenCtxByAccountId(COMMON_ACCOUNT_ID), member_service.EVENT_TASK_VERSION_KEY, "v2")
}

func (suite *BaseFeatureSuite) GetScoreV2CommonAccountCtx() context.Context {
	ctx := context.WithValue(suite.GenCtxByAccountId(COMMON_ACCOUNT_ID), model.SCORE_VERSION_KEY, "v2")
	return context.WithValue(ctx, member_service.EVENT_TASK_VERSION_KEY, "v2")
}

func (suite *BaseFeatureSuite) GetScoreV2RetailAccountCtx() context.Context {
	ctx := context.WithValue(suite.GenCtxByAccountId(RETAIL_ACCOUNT_ID), model.SCORE_VERSION_KEY, "v2")
	return context.WithValue(ctx, member_service.EVENT_TASK_VERSION_KEY, "v2")
}

func (suite *BaseFeatureSuite) GetRetailAccountCtx() context.Context {
	return suite.GenCtxByAccountId(RETAIL_ACCOUNT_ID)
}

func (suite *BaseFeatureSuite) GetV1SenderAccountCtx() context.Context {
	return suite.GenCtxByAccountId(V1_SENDER_ACCOUNT_ID)
}

func (suite *BaseFeatureSuite) GenCtxByAccountId(accountId string) context.Context {
	reqId := fmt.Sprintf("feature-test-%s", uuid.NewV4().String())
	suite.T().Logf("testReqId: %s", reqId)
	return metadata.NewIncomingContext(
		context.Background(),
		metadata.New(map[string]string{
			client.X_REQUEST_ID: reqId,
			client.ACCOUNT_ID:   accountId,
		}),
	)
}

func (suite *BaseFeatureSuite) IsLocal() bool {
	return conf.GetString("env") == "local"
}

func (suite *BaseFeatureSuite) NotLocal() bool {
	return !suite.IsLocal()
}

func Setup() {
	debug := false
	env := os.Getenv("ENV")
	if env == "" {
		env = "local"
	}
	if env == "local" {
		//debug = true
		redisPort := os.Getenv("REDIS_PORT")
		if redisPort == "" {
			redisPort = "6379"
		}

		os.Setenv("MONGO_MASTER_DSN", "****************************************************************************")
		os.Setenv("MONGO_MASTER_REPLSET", "none")
		os.Setenv("CACHE_HOST", "infras-redis")
		os.Setenv("CACHE_PORT", redisPort)
		os.Setenv("RESQUE_HOST", "infras-redis")
		os.Setenv("RESQUE_PORT", redisPort)
		os.Setenv("ELASTICSEARCH_URL", "http://infras-elasticsearch:9200")

		os.Setenv("MQ_PROVIDER", "rocketmq")
	}

	extension.InDebug = debug
	component.InFeatureTest = true

	projectPath := getProjectPath()
	// Parse the ENV, for example MAIRPC_MODE
	conf.SetEnvPrefix("mairpc")
	conf.AutomaticEnv()

	confFormat := "%s/%s/%s.toml"

	// read service specific config
	conf.SetConfigFile(fmt.Sprintf(confFormat, projectPath, "/conf", env))
	conf.MergeInConfig()

	conf.Set("service", "test")
	conf.Set("env", env)

	extensions := []string{"mgo", "request", "redis", "elastic"}
	extension.LoadExtensionsByName(extensions, debug)
	interceptor.LoadInterceptors(conf.AllSettings(), debug)
	log.InitLogger("error", env, "mairpc")
	validators.RegistValidators()
}

func getProjectPath() string {
	var abPath string
	_, filename, _, ok := runtime.Caller(0)
	if ok {
		abPath = path.Dir(filename)
	}
	return filepath.Dir(filepath.Dir(abPath))
}

func (t *BaseFeatureSuite) GetMemberById(ctx context.Context, memberId string) model.Member {
	member := model.Member{}
	_ = extension.DBRepository.FindOne(ctx, model.C_MEMBER, bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       bson.ObjectIdHex(memberId),
	}, &member)
	return member
}

func (self *BaseFeatureSuite) GetRandomMember(ctx context.Context) model.Member {
	members := self.GetRandomMembers(ctx, 1)
	return members[0]
}

func (self *BaseFeatureSuite) GetRandomMembers(ctx context.Context, size int) []model.Member {
	return self.GetRandomMembersWithSelector(ctx, size, bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	})
}

func (*BaseFeatureSuite) GetRandomMembersWithSelector(ctx context.Context, size int, selector bson.M) []model.Member {
	result := []model.Member{}
	pipeline := []bson.M{
		{
			"$match": selector,
		},
		{
			"$sample": bson.M{
				"size": size,
			},
		},
	}
	extension.DBRepository.Aggregate(ctx, model.C_MEMBER, pipeline, false, &result)

	return result
}

func (*BaseFeatureSuite) GetRandomMemberTags(ctx context.Context, size int, selector bson.M) []model.Tag {
	var result []model.Tag
	if selector == nil {
		selector = bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
		}
	}
	pipeline := []bson.M{
		{
			"$match": selector,
		},
		{
			"$sample": bson.M{
				"size": size,
			},
		},
	}
	extension.DBRepository.Aggregate(ctx, model.C_TAG, pipeline, false, &result)
	return result
}

func (*BaseFeatureSuite) GetRandomEnabledMemberLevels(ctx context.Context, size int) []model.MemberLevel {
	var result []model.MemberLevel
	selector := model.Common.GenDefaultCondition(ctx)
	selector["enabled"] = true
	pipeline := []bson.M{
		{
			"$match": selector,
		},
		{
			"$sample": bson.M{
				"size": size,
			},
		},
	}
	extension.DBRepository.Aggregate(ctx, model.C_MEMBER_LEVEL, pipeline, false, &result)
	return result
}

func LockWarp(accountId, module string, f func(*testing.T)) func(*testing.T) {
	return func(t *testing.T) {
		key := fmt.Sprintf("develop-mairpc-%s-%s", module, accountId)
		deadline := time.Now().Add(time.Second * 60)
		for {
			if time.Now().After(deadline) {
				t.Fatalf("%v", "get group lock failed")
			}
			ok, _ := extension.RedisClient.SetNX(key, "", 60)
			if !ok {
				time.Sleep(time.Millisecond * 200)
			} else {
				break
			}
		}
		defer extension.RedisClient.Del(key)
		f(t)
	}
}
