package service

import (
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	"mairpc/proto/mall"
	"mairpc/service/mall/model"

	"golang.org/x/net/context"
)

func (MallService) UpsertSetting(ctx context.Context, req *mall.UpsertMallSettingRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	mallSetting, err := formatSetting(ctx, req)
	if err != nil {
		return nil, err
	}
	if _, err := mallSetting.Upsert(ctx); err != nil {
		return nil, err
	}

	return &response.EmptyResponse{}, nil
}

func formatSetting(ctx context.Context, req *mall.UpsertMallSettingRequest) (*model.MallSetting, error) {

	mallSetting := model.CMallSetting.GetByAccountId(ctx)

	if req.Goods != nil {
		mallSetting.Goods = model.GoodsSetting{
			AutoOffShlfeOOS: req.Goods.AutoOffShlfeOOS,
			SoldCountVisibleSetting: model.SoldCountVisibleSetting{
				Invisible:        req.Goods.SoldCountVisibleSetting.Invisible,
				ThresholdEnabled: req.Goods.SoldCountVisibleSetting.ThresholdEnabled,
				Threshold:        req.Goods.SoldCountVisibleSetting.Threshold,
			},
			StockVisible:          req.Goods.StockVisible,
			StockWarningEnabled:   req.Goods.StockWarningEnabled,
			StockWarningThreshold: req.Goods.StockWarningThreshold,
		}
	}
	if req.Trade != nil {
		mallSetting.Trade = model.TradeSetting{
			Pay: model.PaySetting{
				IsAllowedExchangeCouponToCash:   req.Trade.Pay.IsAllowedExchangeCouponToCash,
				IsAllowedExchangeCouponToPoints: req.Trade.Pay.IsAllowedExchangeCouponToPoints,
			},
			Refund: model.RefundSetting{
				IsRollbackCashAndPoints: req.Trade.Refund.IsRollbackCashAndPoints,
			},
			Complete: model.CompleteSetting{
				DaysOfAutoCompleteExpressOrder:   req.Trade.Complete.DaysOfAutoCompleteExpressOrder,
				DaysOfAutoCompletePickupOrder:    req.Trade.Complete.DaysOfAutoCompletePickupOrder,
				DaysOfAutoCompleteNoExpressOrder: req.Trade.Complete.DaysOfAutoCompleteNoExpressOrder,
			},
			Purchase: model.PurchaseSetting{
				IsAllowedNonMemberToPurchase: req.Trade.Purchase.IsAllowedNonMemberToPurchase,
				IsScoreLimitationEnabled:     req.Trade.Purchase.IsScoreLimitationEnabled,
				ScoreLimitation: model.ScoreLimitation{
					Type: req.Trade.Purchase.ScoreLimitation.Type,
					Num:  req.Trade.Purchase.ScoreLimitation.Num,
				},
				PurchaseQuantityLimitEnabled: req.Trade.Purchase.PurchaseQuantityLimitEnabled,
				PurchaseQuantityLimitation: model.PurchaseQuantityLimitation{
					Type: req.Trade.Purchase.PurchaseQuantityLimitation.Type,
					Num:  req.Trade.Purchase.PurchaseQuantityLimitation.Num,
				},
			},
			Audit: model.AuditSetting{
				Enabled:                        req.Trade.Audit.Enabled,
				NeedAuditMemberBlockedStatuses: req.Trade.Audit.NeedAuditMemberBlockedStatuses,
			},
		}
	}
	return mallSetting, nil
}
