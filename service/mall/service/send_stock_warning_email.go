package service

import (
	"fmt"
	"mairpc/core/component"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	pb_account "mairpc/proto/account"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"

	account_model "mairpc/service/account/model"
	"mairpc/service/mall/model"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

func (MallService) SendStockWarningEmail(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	component.GO(ctx, func(ctx context.Context) {
		mallSetting := model.CMallSetting.GetByAccountId(ctx)
		if mallSetting == nil {
			log.Warn(ctx, "mall setting is not found", log.Fields{
				"accountId": util.GetAccountId(ctx),
			})
			return
		}

		if !mallSetting.Goods.StockWarningEnabled {
			return
		}

		goodsCondition := model.Common.GenDefaultCondition(ctx)
		goodsCondition["stock"] = bson.M{"$lt": mallSetting.Goods.StockWarningThreshold}
		goodsCondition["status"] = model.MALL_GOODS_STATUS_ON

		goods := model.CMallGoods.GetAllByCondition(ctx, goodsCondition)

		if len(goods) == 0 {
			return
		}

		users := getNeedEmailUsers(ctx)
		goodNames := ""
		for _, good := range goods {
			goodNames += fmt.Sprintf("【%s】【%s】\n", good.Product.Name, good.Product.Number)
		}
		sendStockWarningEmail(ctx, goodNames, users)
	})
	return &response.EmptyResponse{}, nil
}

func sendStockWarningEmail(ctx context.Context, goodNames string, users []account_model.User) {
	for _, u := range users {
		_, err := client.GetAccountServiceClient().SendSystemEmail(ctx, &pb_account.SendSystemEmailRequest{
			To:         []string{u.Email},
			Body:       fmt.Sprintf("%s已达到预警的库存，请及时在积分商城修改可兑换库存。", goodNames),
			Subject:    "群脉积分商城库存预警",
			SenderName: "群脉",
		})
		if err != nil {
			log.Warn(ctx, "Failed to send micro mall stock warning email", log.Fields{
				"goodNames": goodNames,
				"email":     u.Email,
				"errMsg":    err.Error(),
			})
		}
	}
}

func getNeedEmailUsers(ctx context.Context) []account_model.User {
	stockWarningReceivers, err := model.CStockWarningReceiver.FindAll(ctx)
	if err != nil {
		log.Warn(ctx, "Failed to get stock warning receivers", log.Fields{
			"errMsg": err.Error(),
		})
	}

	if len(stockWarningReceivers) > 0 {
		userIds := []bson.ObjectId{}
		for _, receiver := range stockWarningReceivers {
			userIds = append(userIds, receiver.UserId)
		}
		users := []account_model.User{}
		condition := model.Common.GenDefaultCondition(ctx)
		condition["_id"] = bson.M{"$in": userIds}
		model.Common.GetAllByCondition(ctx, condition, nil, 0, account_model.C_USER, &users)
		return users
	}

	condition := model.Common.GenDefaultCondition(ctx)
	condition["$or"] = []bson.M{
		{
			"isOwner": true,
		},
		{
			"consultManager": bson.M{
				"$exists": true,
			},
		},
	}
	users := []account_model.User{}
	model.Common.GetAllByCondition(ctx, condition, nil, 0, account_model.C_USER, &users)
	return users
}
