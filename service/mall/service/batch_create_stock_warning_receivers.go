package service

import (
	"mairpc/core/extension/bson"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	"mairpc/proto/mall"
	"mairpc/service/mall/model"
	"mairpc/service/share/util"
	"time"

	"golang.org/x/net/context"
)

func (MallService) BatchCreateStockWarningReceivers(ctx context.Context, req *mall.BatchCreateStockWarningReceiverRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	receivers := []model.StockWarningReceiver{}
	for _, userId := range util.StrArrayUnique(req.UserIds) {
		receiver := model.StockWarningReceiver{
			AccountId: util.GetAccountIdAsObjectId(ctx),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			UserId:    bson.ObjectIdHex(userId),
		}
		receivers = append(receivers, receiver)
	}

	err := model.CStockWarningReceiver.BatchCreate(ctx, receivers)
	if err != nil {
		return nil, err
	}

	return &response.EmptyResponse{}, nil
}
