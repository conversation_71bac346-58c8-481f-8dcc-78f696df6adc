package service

import (
	"mairpc/core/extension/bson"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/service/mall/model"

	"golang.org/x/net/context"
)

func (MallService) DeleteStockWarningReceiver(ctx context.Context, req *request.DetailRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	err := model.CStockWarningReceiver.Delete(ctx, bson.ObjectIdHex(req.Id))
	if err != nil {
		return nil, err
	}

	return &response.EmptyResponse{}, nil
}
