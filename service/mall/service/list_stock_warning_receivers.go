package service

import (
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/account"
	"mairpc/proto/mall"
	"mairpc/service/mall/model"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

func (MallService) ListStockWarningReceivers(ctx context.Context, req *mall.ListStockWarningReceiversRequest) (*mall.FindStockWarningReceiverResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	selector := model.Common.GenDefaultCondition(ctx)
	page := util.FormatPagingCondition(selector, req.ListCondition)
	receivers, total, err := model.CStockWarningReceiver.FindByPagination(ctx, page)
	if err != nil {
		return nil, err
	}

	return &mall.FindStockWarningReceiverResponse{
		Receivers: formatStockWarningReceivers(ctx, receivers),
		Total:     int32(total),
	}, nil
}

func formatStockWarningReceivers(ctx context.Context, receivers []model.StockWarningReceiver) []*mall.StockWarningReceiver {
	results := []*mall.StockWarningReceiver{}
	if len(receivers) == 0 {
		return results
	}

	copier.Instance(nil).From(receivers).CopyTo(&results)
	userIds := core_util.ExtractArrayStringField("UserId", receivers)
	usersResp, _ := GetUsers(ctx, &account.UserListRequest{
		Unlimited: true,
		Ids:       userIds,
	})
	if usersResp == nil {
		return results
	}

	userMap := core_util.MakeMapper("Id", usersResp.Items)

	for _, receiver := range results {
		u, ok := userMap[receiver.UserId]
		if ok {
			user := u.(*account.UserDetailResponse)
			receiver.Phone = user.Phone
			receiver.Email = user.Email
			if user.Staff != nil && user.Staff.Name != "" {
				receiver.Name = user.Staff.Name
			} else {
				receiver.Name = user.Name
			}
		}
	}

	return results
}
