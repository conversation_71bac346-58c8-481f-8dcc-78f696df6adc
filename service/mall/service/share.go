package service

import (
	"encoding/json"
	"fmt"
	mairpc "mairpc/core/client"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/proto/account"
	pb_coupon "mairpc/proto/coupon"
	"mairpc/proto/mall"
	"mairpc/proto/member"
	"mairpc/proto/product"
	"mairpc/service/mall/codes"
	"mairpc/service/mall/model"
	product_model "mairpc/service/product/model"
	"mairpc/service/share/util"
	"reflect"

	"golang.org/x/net/context"
)

const (
	MEMBER_BLOCKEDSTATUS_BLOCKED = 2
	MEMBER_PRIVILEGE_DISCOUNT    = "消费折扣"
)

type MallService struct{}

func formatGoodsDetail(ctx context.Context, goods *model.MallGoods) (*mall.GoodsDetail, error) {
	goodsDetail := &mall.GoodsDetail{}

	core_util.TransformFields(goods, goodsDetail, map[string]interface{}{
		"StockType":          formatLimitType,
		"PurchaseType":       formatLimitType,
		"PurchaseLimitation": formatPurchaseLimitation,
		"MemberLimitation":   formatMemberLimitation,
		"AreaRestriction":    formatAreaRestriction,
	}, map[string]string{})

	if goods.Product.Type == model.MALL_GOODS_TYPE_PRODUCT || goods.Product.Type == model.MALL_GOODS_TYPE_RESERVATION {
		pbProduct, err := model.GetProduct(ctx, goods.Product.Id.Hex())
		if err != nil {
			return nil, err
		}
		goodsDetail.Specs = formatGoodsSpecs(goods.Specs, pbProduct)
		goodsDetail.Product = formatProduct(goods.Product, pbProduct)
		goodsDetail = formatProductSkus(ctx, goodsDetail)
		pbProductInfo, err := model.GetProductInfo(ctx, goods.Product.Id.Hex())
		if err != nil {
			return nil, err
		}
		goodsDetail.Product.Intro = pbProductInfo.Intro
	} else if goods.Product.Type == model.MALL_GOODS_TYPE_COUPON {
		pbCoupon, err := model.GetCoupon(ctx, goods.Product.Id.Hex())
		if err != nil {
			return nil, err
		}
		goodsDetail.Product = &mall.Product{
			Id:         pbCoupon.Id,
			Name:       pbCoupon.Title,
			Type:       model.MALL_GOODS_TYPE_COUPON,
			CouponType: pbCoupon.Type,
		}
		goodsDetail.Specs = formatGoodsSpecs(goods.Specs, nil)
		core_util.TransformFields(goods.Product, goodsDetail.Product, map[string]interface{}{
			"Pictures": formatProductPictures,
		}, map[string]string{})
		if pbCoupon.StockType == COUPON_STOCK_LIMIT && pbCoupon.Total < int64(goodsDetail.Stock) {
			goodsDetail.Stock = uint64(pbCoupon.Total)
		}
	} else {
		return nil, codes.NewError(codes.UnsupportedGoodsType)
	}

	if core_util.GetUserRole(ctx) == "member" && goodsDetail.ActiveSyncStock {
		goodsDetail.Stock = 0
		for uuid, stock := range goodsDetail.Specs.Stocks {
			if goodsDetail.Specs.SyncStocks[uuid] < stock {
				goodsDetail.Specs.Stocks[uuid] = goodsDetail.Specs.SyncStocks[uuid]
			}
			goodsDetail.Stock += goodsDetail.Specs.Stocks[uuid]
		}
	}

	return goodsDetail, nil
}

func GetUsers(ctx context.Context, req *account.UserListRequest) (*account.UserListResponse, error) {
	resp, err := mairpc.Run(
		"AccountService.GetUsers",
		ctx,
		req,
	)
	if err != nil {
		return nil, err
	}
	return resp.(*account.UserListResponse), nil
}

func formatAreaRestriction(r model.AreaRestriction) *mall.GoodsAreaRestriction {
	return &mall.GoodsAreaRestriction{
		IsAllowed: r.IsAllowed,
		Areas:     r.Areas,
		IsEnabled: r.IsEnabled,
	}
}

func formatLimitType(limitType string) mall.LimitType {
	if limitType == model.LIMIT_TYPE_LIMIT {
		return mall.LimitType_LIMIT
	}

	return mall.LimitType_NOLIMIT
}

func formatGoodsSpecs(dbSpecs model.GoodsSpecs, pbProduct *product.ProductDetailResponse) *mall.GoodsSpecs {
	specs := &mall.GoodsSpecs{}
	core_util.CopyFields(dbSpecs, specs)
	if pbProduct == nil {
		return specs
	}

	specs.ExternalSkus, specs.Skus = dbSpecs.GetExternalSkusAndSkus(pbProduct)
	return specs
}

func formatProduct(dbProduct model.BriefProduct, pbProduct *product.ProductDetailResponse) *mall.Product {
	product := &mall.Product{}
	core_util.TransformFields(dbProduct, product, map[string]interface{}{
		"Pictures": formatProductPictures,
	}, map[string]string{})
	product.Specifications = pbProduct.GetSpecifications()
	product.Category = pbProduct.GetCategory()
	return product
}

func formatBriefProduct(dbProduct model.BriefProduct) *mall.Product {
	product := &mall.Product{}
	core_util.TransformFields(dbProduct, product, map[string]interface{}{
		"Pictures": formatProductPictures,
	}, map[string]string{})

	return product
}

func formatProductPictures(pics []model.Picture) []*mall.Picture {
	pictures := []*mall.Picture{}
	for _, pic := range pics {
		picture := &mall.Picture{}
		core_util.CopyFields(pic, picture)
		pictures = append(pictures, picture)
	}

	return pictures
}

func formatPurchaseLimitation(purchaseLimitation model.PurchaseLimitation) *mall.PurchaseLimitation {
	if reflect.DeepEqual(purchaseLimitation, model.PurchaseLimitation{}) {
		return nil
	}
	limitation := &mall.PurchaseLimitation{}
	core_util.CopyRFC3339(purchaseLimitation, limitation)
	return limitation
}

func formatMemberLimitation(memberLimitation model.MemberLimitation) *mall.MemberLimitation {
	if reflect.DeepEqual(memberLimitation, model.MemberLimitation{}) {
		return nil
	}
	limitation := &mall.MemberLimitation{}
	core_util.CopyRFC3339(memberLimitation, limitation)
	return limitation
}

func getAccountServiceClient(maiConn *mairpc.ClientConn) interface{} {
	client := account.NewAccountServiceClient(maiConn.Conn)
	return &client
}

func getChannel(ctx context.Context, channelId string) (*account.ChannelDetailResponse, error) {
	resp, err := mairpc.RPCProxy.Call(
		getAccountServiceClient,
		"AccountService.GetChannel",
		ctx,
		&account.ChannelDetailRequest{
			ChannelId: channelId,
		},
	)

	if err != nil {
		return nil, err
	}

	return resp.(*account.ChannelDetailResponse), nil
}

func getMemberCouponStats(ctx context.Context, memberId string, couponIds []string) ([]*pb_coupon.MemberCouponStats, error) {
	req := &pb_coupon.GetMemberCouponStatsRequest{
		MemberId:  memberId,
		CouponIds: couponIds,
	}
	resp, err := mairpc.Run(
		"CouponService.GetMemberCouponStats",
		ctx,
		req,
	)
	if err != nil {
		return nil, err
	}
	return resp.(*pb_coupon.GetMemberCouponStatsResponse).MemberCouponStats, nil
}

func GetMember(ctx context.Context, memberId string) (*member.MemberDetailResponse, error) {
	resp, err := mairpc.Run(
		"MemberService.GetMember",
		ctx,
		&member.MemberDetailRequest{
			Id:                   memberId,
			ShowSystemProperties: true,
		},
	)
	if err != nil {
		return nil, err
	}

	return resp.(*member.MemberDetailResponse), nil
}

func ListProductByIds(ctx context.Context, productIds []string) (*product.ProductListResponse, error) {
	resp, err := mairpc.Run(
		"ProductService.SearchProduct",
		ctx,
		&product.SearchProductRequest{
			Ids: productIds,
		},
	)

	if err != nil {
		return nil, err
	}

	return resp.(*product.ProductListResponse), nil
}

func getProductServiceClient(maiConn *mairpc.ClientConn) interface{} {
	client := product.NewProductServiceClient(maiConn.Conn)
	return &client
}

func getUnusedMembershipDiscounts(ctx context.Context, memberId string) ([]*pb_coupon.MembershipDiscount, error) {
	req := &pb_coupon.GetMembershipDiscountsRequest{
		MemberId:    memberId,
		Status:      "unused",
		CouponTypes: []string{COUPON_TYPE_CASH, COUPON_TYPE_CREDIT},
	}
	resp, err := mairpc.Run(
		"CouponService.GetMembershipDiscounts",
		ctx,
		req,
	)
	if err != nil {
		return nil, err
	}
	return resp.(*pb_coupon.MembershipDiscounts).Items, nil
}

func SearchProduct(ctx context.Context, req *product.SearchProductRequest) (*product.ProductListResponse, error) {
	resp, err := mairpc.RPCProxy.Call(
		getProductServiceClient,
		"ProductService.SearchProduct",
		ctx,
		req,
	)

	if err != nil {
		return nil, err
	}

	return resp.(*product.ProductListResponse), nil
}

func formatProductSkus(ctx context.Context, goodsDetail *mall.GoodsDetail) *mall.GoodsDetail {
	condition := bson.M{
		"_id":       bson.ObjectIdHex(goodsDetail.Product.Id),
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}
	product := product_model.Product{}
	extension.DBRepository.FindOne(ctx, product_model.C_PRODUCT, condition, &product)
	core_util.CopyFields(product.Skus, &goodsDetail.Product.Skus)
	return goodsDetail
}

func jsonToMap(jsonStr string) (map[string]string, error) {
	jsonMap := make(map[string]string)
	err := json.Unmarshal([]byte(jsonStr), &jsonMap)
	if err != nil {
		return nil, err
	}

	for key, value := range jsonMap {
		fmt.Printf("%v: %v\n", key, value)
	}

	return jsonMap, nil
}
