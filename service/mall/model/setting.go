package model

import (
	"context"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/service/share/util"
	"reflect"
	"time"
)

const (
	C_MALL_Setting                                      = "micromallSetting"
	DAYS_OF_AUTO_COMPLETE_EXPRESS_ORDER_DEFAULT_TIME    = 7 * 24 * 60 // 单位: min
	DAYS_OF_AUTO_COMPLETE_PICKUP_ORDER_DEFAULT_TIME     = 1 * 24 * 60 // 单位: min
	DAYS_OF_AUTO_COMPLETE_NO_EXPRESS_ORDER_DEFAULT_TIME = 3 * 24 * 60 // 单位: min
)

var (
	CMallSetting = &MallSetting{}
)

type MallSetting struct {
	Id        bson.ObjectId `bson:"_id"`
	AccountId bson.ObjectId `bson:"accountId"`
	IsDeleted bool          `bson:"isDeleted"`
	CreatedAt time.Time     `bson:"createdAt"`
	UpdatedAt time.Time     `bson:"updatedAt"`
	Goods     GoodsSetting  `bson:"goods"`
	Trade     TradeSetting  `bson:"trade"`
}

type GoodsSetting struct {
	AutoOffShlfeOOS         bool                    `bson:"autoOffShlfeOOS"`
	SoldCountVisibleSetting SoldCountVisibleSetting `bson:"soldCountVisibleSetting"`
	StockVisible            bool                    `bson:"stockVisible"`
	StockWarningEnabled     bool                    `bson:"stockWarningEnabled"`
	StockWarningThreshold   uint64                  `bson:"stockWarningThreshold"`
}

type SoldCountVisibleSetting struct {
	Invisible        bool   `bson:"invisible"`
	ThresholdEnabled bool   `bson:"thresholdEnabled"`
	Threshold        uint64 `bson:"threshold"`
}

type TradeSetting struct {
	Pay      PaySetting      `bson:"pay"`
	Refund   RefundSetting   `bson:"refund"`
	Complete CompleteSetting `bson:"complete"`
	Purchase PurchaseSetting `bson:"purchase"`
	Audit    AuditSetting    `bson:"audit"`
}

type PaySetting struct {
	IsAllowedExchangeCouponToCash   bool `bson:"isAllowedExchangeCouponToCash"`
	IsAllowedExchangeCouponToPoints bool `bson:"isAllowedExchangeCouponToPoints"`
}

type RefundSetting struct {
	IsRollbackCashAndPoints bool `bson:"isRollbackCashAndPoints"`
}

type CompleteSetting struct {
	DaysOfAutoCompleteExpressOrder   uint64 `bson:"daysOfAutoCompleteExpressOrder"`
	DaysOfAutoCompletePickupOrder    uint64 `bson:"daysOfAutoCompletePickupOrder"`
	DaysOfAutoCompleteNoExpressOrder uint64 `bson:"daysOfAutoCompleteNoExpressOrder"`
}

type PurchaseSetting struct {
	IsAllowedNonMemberToPurchase bool                       `bson:"isAllowedNonMemberToPurchase"`
	IsScoreLimitationEnabled     bool                       `bson:"isScoreLimitationEnabled"`
	ScoreLimitation              ScoreLimitation            `bson:"scoreLimitation,omitempty"`
	PurchaseQuantityLimitEnabled bool                       `bson:"purchaseQuantityLimitEnabled"`
	PurchaseQuantityLimitation   PurchaseQuantityLimitation `bson:"purchaseQuantityLimitation,omitempty"`
}

type ScoreLimitation struct {
	Type string `bson:"type"`
	Num  uint64 `bson:"num"`
}

type PurchaseQuantityLimitation struct {
	Type string `bson:"type"`
	Num  uint64 `bson:"num"`
}

type AuditSetting struct {
	Enabled                        bool     `bson:"enabled"`
	NeedAuditMemberBlockedStatuses []string `bson:"needAuditMemberBlockedStatuses"`
}

func (*MallSetting) GetByAccountId(ctx context.Context) *MallSetting {
	setting := &MallSetting{}
	err := extension.DBRepository.FindOne(ctx, C_MALL_Setting, bson.M{"accountId": util.GetAccountIdAsObjectId(ctx)}, setting)
	if err != nil {
		return createDefaultSetting(ctx)
	}
	if reflect.DeepEqual(setting.Trade.Complete, CompleteSetting{}) {
		setting.Trade.Complete = CompleteSetting{
			DaysOfAutoCompleteExpressOrder:   DAYS_OF_AUTO_COMPLETE_EXPRESS_ORDER_DEFAULT_TIME,
			DaysOfAutoCompletePickupOrder:    DAYS_OF_AUTO_COMPLETE_PICKUP_ORDER_DEFAULT_TIME,
			DaysOfAutoCompleteNoExpressOrder: DAYS_OF_AUTO_COMPLETE_NO_EXPRESS_ORDER_DEFAULT_TIME,
		}
	}
	return setting
}

func (setting *MallSetting) Upsert(ctx context.Context) (interface{}, error) {
	setting.UpdatedAt = time.Now()
	selector := bson.M{
		"_id":       setting.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"updatedAt": setting.UpdatedAt,
			"goods":     setting.Goods,
			"trade":     setting.Trade,
		},
	}
	updater["$setOnInsert"] = bson.M{
		"createdAt": time.Now(),
	}
	return extension.DBRepository.Upsert(ctx, C_MALL_Setting, selector, updater)
}

func (*MallSetting) GetEnabledMallSettingAccount(ctx context.Context) ([]MallSetting, error) {
	selector := Common.GenDefaultCondition(ctx)

	mallSettings := []MallSetting{}
	err := extension.DBRepository.FindAll(ctx, C_MALL_Setting, selector, []string{}, 0, &mallSettings)
	if err != nil {
		return nil, err
	}

	return mallSettings, nil
}

func createDefaultSetting(ctx context.Context) *MallSetting {
	mallSetting := &MallSetting{
		Id:        bson.NewObjectId(),
		AccountId: util.GetAccountIdAsObjectId(ctx),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		IsDeleted: false,
		Goods: GoodsSetting{
			AutoOffShlfeOOS: false,
		},
		Trade: TradeSetting{
			Pay: PaySetting{
				IsAllowedExchangeCouponToCash:   false,
				IsAllowedExchangeCouponToPoints: false,
			},
			Refund: RefundSetting{
				IsRollbackCashAndPoints: true,
			},
			Complete: CompleteSetting{
				DaysOfAutoCompleteExpressOrder:   DAYS_OF_AUTO_COMPLETE_EXPRESS_ORDER_DEFAULT_TIME,
				DaysOfAutoCompletePickupOrder:    DAYS_OF_AUTO_COMPLETE_PICKUP_ORDER_DEFAULT_TIME,
				DaysOfAutoCompleteNoExpressOrder: DAYS_OF_AUTO_COMPLETE_NO_EXPRESS_ORDER_DEFAULT_TIME,
			},
			Purchase: PurchaseSetting{
				IsAllowedNonMemberToPurchase: false,
				IsScoreLimitationEnabled:     false,
			},
		},
	}
	return mallSetting
}
