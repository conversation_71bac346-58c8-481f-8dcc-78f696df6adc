package model

import (
	"context"
	"crypto/md5"
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	mairpc "mairpc/core/client"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	"mairpc/proto/coupon"
	"mairpc/proto/mall"
	"mairpc/proto/member"
	"mairpc/proto/product"
	"mairpc/service/share/component"
	"mairpc/service/share/model"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"
	coupon_codes "mairpc/service/coupon/codes"
	"mairpc/service/mall/codes"
	product_codes "mairpc/service/product/codes"

	share_model "mairpc/service/share/model"

	"github.com/qiniu/qmgo"
)

const (
	C_MALL_GOODS = "micromallGoods"

	MALL_GOODS_TYPE_COUPON      = "coupon"
	MALL_GOODS_TYPE_PRODUCT     = "product"
	MALL_GOODS_TYPE_RESERVATION = "reservation"

	LIMIT_TYPE_LIMIT   = "limit"
	LIMIT_TYPE_NOLIMIT = "nolimit"

	MALL_GOODS_STATUS_ON        = "ON"
	MALL_GOODS_STATUS_OFF       = "OFF"
	MALL_GOODS_STATUS_SCHEDULED = "SCHEDULED"

	PRICE_TYPE_MONEY = "money"
	PRICE_TYPE_SCORE = "score"
	PRICE_TYPE_MIXED = "mixed"

	PURCHASE_LIMITION_VALID = "valid"

	LIMITION_MEMBER_TYPE_ONE = "ONE"
	LIMITION_MEMBER_TYPE_ALL = "ALL"

	LIMITION_TIME_TYPE_DAILY   = "DAILY"
	LIMITION_TIME_TYPE_WEEKLY  = "WEEKLY"
	LIMITION_TIME_TYPE_MONTHLY = "MONTHLY"
	LIMITION_TIME_TYPE_QUARTER = "QUARTER"
	LIMITION_TIME_TYPE_YEARLY  = "YEARLY"
	LIMITION_TIME_TYPE_ALL     = "ALL"

	MEMBER_LIMIT_TYPE_LEVELS = "levels"
	MEMBER_LIMIT_TYPE_TAGS   = "tags"
	MEMBER_LIMIT_TYPE_GROUPS = "groups"
)

var (
	CMallGoods = &MallGoods{}
)

type MallGoods struct {
	Id                  bson.ObjectId      `bson:"_id"`
	AccountId           bson.ObjectId      `bson:"accountId"`
	Order               uint32             `bson:"order"`
	ShelfId             bson.ObjectId      `bson:"shelfId"`
	Price               uint64             `bson:"price"`
	OriginalPrice       uint64             `bson:"originalPrice"`
	Score               uint64             `bson:"score"`
	OriginalScore       uint64             `bson:"originalScore"`
	StockType           string             `bson:"stockType"`
	Stock               uint64             `bson:"stock"`
	Type                string             `bson:"type"`
	PositivePrice       bool               `bson:"positivePrice"`
	PositiveScore       bool               `bson:"positiveScore"`
	PurchaseType        string             `bson:"purchaseType"`
	PurchaseLimit       uint64             `bson:"purchaseLimit,omitempty"`
	AllowedTags         []string           `bson:"allowedTags"`
	SoldCount           uint64             `bson:"soldCount"`
	OnSaleNumber        string             `bson:"onSaleNumber"`
	AutoTags            []string           `bson:"autoTags"`
	IsDeleted           bool               `bson:"isDeleted"`
	Product             BriefProduct       `bson:"product"`
	Specs               GoodsSpecs         `bson:"specs"`
	CreatedAt           time.Time          `bson:"createdAt"`
	UpdatedAt           time.Time          `bson:"updatedAt"`
	GroupGoodsId        bson.ObjectId      `bson:"groupGoodsId,omitempty"`
	GroupAccountId      bson.ObjectId      `bson:"groupAccountId,omitempty"`
	Status              string             `bson:"status"`
	ShelvedAt           time.Time          `bson:"shelvedAt,omitempty"`
	ScheduledShelveAt   time.Time          `bson:"scheduledShelveAt,omitempty"`
	UnshelvedAt         time.Time          `bson:"unshelvedAt,omitempty"`
	ScheduledUnshelveAt time.Time          `bson:"scheduledUnshelveAt,omitempty"`
	PurchaseLimitation  PurchaseLimitation `bson:"purchaseLimitation,omitempty"`
	OriginPriceType     string             `bson:"originPriceType"`
	ActiveSyncStock     bool               `bson:"activeSyncStock"`
	ApplicableSpecHashs []string           `bson:"applicableSpecHashs,omitempty"`
	MemberLimitation    MemberLimitation   `bson:"memberLimitation,omitempty"`
	AreaRestriction     AreaRestriction    `bson:"areaRestriction,omitempty"`
	Badge               string             `bson:"badge,omitempty"`
	DeliveryMethods     []string           `bson:"deliveryMethods"`
}

type AreaRestriction struct {
	Areas     []string `bson:"areas"`
	IsAllowed bool     `bson:"isAllowed"`
	IsEnabled bool     `bson:"isEnabled"`
}

type BriefProduct struct {
	Id         bson.ObjectId `bson:"id"`
	Number     string        `bson:"number,omitempty"`
	Name       string        `bson:"name"`
	Type       string        `bson:"type"`
	CouponType string        `bson:"couponType,omitempty"`
	Pictures   []Picture     `bson:"pictures"`
	Intro      string        `bson:"intro"`
}

type GoodsSpecs struct {
	Prices         map[string]uint64 `bson:"prices"`
	OriginalPrices map[string]uint64 `bson:"originalPrices"`
	Stocks         map[string]uint64 `bson:"stocks"`
	SyncStocks     map[string]uint64 `bson:"syncStocks"`
	SoldCounts     map[string]uint64 `bson:"soldCounts"`
	Scores         map[string]uint64 `bson:"scores"`
	OriginalScores map[string]uint64 `bson:"originalScores"`
}

type Picture struct {
	ThumbnailUrl string `bson:"thumbnailUrl"`
	Url          string `bson:"url"`
}

type PurchaseLimitation struct {
	IsNumLimitationEnabled      bool                 `bson:"isNumLimitationEnabled"`
	IsDateTimeLimitationEnabled bool                 `bson:"isDateTimeLimitationEnabled"`
	NumLimitation               NumLimitation        `bson:"numLimitation"`
	DateTimeLimitation          []DateTimeLimitation `bson:"dateTimeLimitation"`
}

type NumLimitation struct {
	MemberType string `bson:"memberType"`
	DailyType  string `bson:"dailyType"`
	Num        uint64 `bson:"num"`
}
type TimeRange struct {
	Start string `bson:"start"`
	End   string `bson:"end"`
}

type DateTimeLimitation struct {
	StartDate time.Time   `bson:"startDate"`
	EndDate   time.Time   `bson:"endDate"`
	Times     []TimeRange `bson:"times"`
}

type MemberLimitation struct {
	IsEnable              bool                  `bson:"isEnable"`
	Type                  string                `bson:"type,omitempty"`
	Operator              string                `bson:"operator,omitempty"`
	Tags                  []string              `bson:"tags,omitempty"`
	Labels                []model.MemberLabel   `bson:"labels,omitempty"`
	StaticGroupIds        []string              `bson:"staticGroupIds,omitempty"`
	DynamicGroupIds       []string              `bson:"dynamicGroupIds,omitempty"`
	MemberLevelLimitation MemberLevelLimitation `bson:"memberLevelLimitation,omitempty"`
}

type MemberLevelLimitation struct {
	MemberLevels []uint32 `bson:"memberLevels,omitempty"`
	IsPaidMember bool     `bson:"isPaidMember"`
}

type DateTimeLimitionRange struct {
	StartTime time.Time
	EndTime   time.Time
}

type StockChangeHandler func(ctx context.Context, stockChangeList []StockChange) (successExternalIds []string, err error)
type StockChange struct {
	External string
	Stock    int
}

type SyncStockFromErpRequest struct {
	Number   string
	External string
}

type QueryThirdPartyErpGoodsRequest struct {
	External  string
	StartTime time.Time
	EndTime   time.Time
	Deleted   bool
	PageSize  int
	PageNo    int
	Number    string
}

type ThirdPartyErpGoods struct {
	GoodsNo   string
	GoodsName string
	SpecList  []ThirdSpec
}

type ThirdSpec struct {
	External  string
	SpecId    string
	SpecName  string
	Price     float64
	SaleScore int64
	ImgUrl    string
}

func (g *MallGoods) CheckAreaRestriction(address Address) bool {
	if !g.AreaRestriction.IsEnabled {
		return true
	}

	province := address.Province
	city := fmt.Sprintf("%s:%s", address.Province, address.City)
	if g.AreaRestriction.IsAllowed {
		intersections := core_util.GetArraysIntersection([]string{province, city}, g.AreaRestriction.Areas)
		if len(intersections) > 0 {
			return true
		}
	} else {
		intersections := core_util.GetArraysIntersection([]string{province, city}, g.AreaRestriction.Areas)
		if len(intersections) == 0 {
			return true
		}
	}

	return false
}

func (*MallGoods) GetById(ctx context.Context, goodId bson.ObjectId) *MallGoods {
	goods := &MallGoods{}
	err := extension.DBRepository.FindOne(ctx, C_MALL_GOODS, bson.M{"_id": goodId}, goods)
	if err != nil {
		return nil
	}

	return goods
}

func (*MallGoods) GetByShelfIdAndProductId(ctx context.Context, shelfId bson.ObjectId, productId bson.ObjectId) *MallGoods {
	goods := &MallGoods{}
	selector := bson.M{
		"shelfId":    shelfId,
		"product.id": productId,
		"accountId":  util.GetAccountIdAsObjectId(ctx),
		"isDeleted":  false,
	}
	err := extension.DBRepository.FindOne(ctx, C_MALL_GOODS, selector, goods)
	if err != nil {
		return nil
	}

	return goods
}

func (goods *MallGoods) Create(ctx context.Context) error {
	goods.Id = bson.NewObjectId()
	goods.AccountId = util.GetAccountIdAsObjectId(ctx)
	goods.CreatedAt = time.Now()
	goods.UpdatedAt = goods.CreatedAt
	goods.IsDeleted = false
	_, err := extension.DBRepository.Insert(ctx, C_MALL_GOODS, goods)
	return err
}

func (goods *MallGoods) Update(ctx context.Context) error {
	goods.UpdatedAt = time.Now()
	selector := bson.M{
		"_id":       goods.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}

	updater := bson.M{
		"$set": bson.M{
			"order":               goods.Order,
			"price":               goods.Price,
			"originalPrice":       goods.OriginalPrice,
			"score":               goods.Score,
			"originalScore":       goods.OriginalScore,
			"stockType":           goods.StockType,
			"stock":               goods.Stock,
			"type":                goods.Type,
			"product":             goods.Product,
			"originPriceType":     goods.OriginPriceType,
			"positivePrice":       goods.PositivePrice,
			"positiveScore":       goods.PositiveScore,
			"purchaseType":        goods.PurchaseType,
			"purchaseLimit":       goods.PurchaseLimit,
			"allowedTags":         goods.AllowedTags,
			"specs":               goods.Specs,
			"updatedAt":           goods.UpdatedAt,
			"status":              goods.Status,
			"onSaleNumber":        goods.OnSaleNumber,
			"activeSyncStock":     goods.ActiveSyncStock,
			"applicableSpecHashs": goods.ApplicableSpecHashs,
			"areaRestriction":     goods.AreaRestriction,
			"badge":               goods.Badge,
			"deliveryMethods":     goods.DeliveryMethods,
		},
	}
	unset := bson.M{}
	if !goods.ShelvedAt.IsZero() {
		updater["$set"].(bson.M)["shelvedAt"] = goods.ShelvedAt
	}
	if !goods.ScheduledShelveAt.IsZero() {
		updater["$set"].(bson.M)["scheduledShelveAt"] = goods.ScheduledShelveAt
	}
	if !goods.UnshelvedAt.IsZero() {
		updater["$set"].(bson.M)["unshelvedAt"] = goods.UnshelvedAt
	}
	if !goods.ScheduledUnshelveAt.IsZero() {
		updater["$set"].(bson.M)["scheduledUnshelveAt"] = goods.ScheduledUnshelveAt
	} else {
		unset["scheduledUnshelveAt"] = ""
	}
	if !reflect.DeepEqual(goods.PurchaseLimitation, PurchaseLimitation{}) {
		updater["$set"].(bson.M)["purchaseLimitation"] = goods.PurchaseLimitation
	} else {
		unset["purchaseLimitation"] = ""
	}
	if !reflect.DeepEqual(goods.MemberLimitation, MemberLimitation{}) {
		updater["$set"].(bson.M)["memberLimitation"] = goods.MemberLimitation
	} else {
		unset["memberLimitation"] = ""
	}
	if len(unset) > 0 {
		updater["$unset"] = unset
	}
	return extension.DBRepository.UpdateOne(ctx, C_MALL_GOODS, selector, updater)
}

func (*MallGoods) FindAndApply(ctx context.Context, selector bson.M, change qmgo.Change) (*MallGoods, error) {
	goods := &MallGoods{}
	err := extension.DBRepository.FindAndApply(ctx, C_MALL_GOODS, selector, []string{}, change, goods)
	if err != nil {
		return nil, err
	}
	return goods, err
}

func (goods *MallGoods) CalculateStock(ctx context.Context) {
	var templateStock uint64 = 0
	for uuid, stock := range goods.Specs.Stocks {
		if goods.ActiveSyncStock {
			templateStock += util.MinUint64(stock, goods.Specs.SyncStocks[uuid])
		} else {
			templateStock += stock
		}
	}
	goods.Stock = templateStock
}

func (goods *MallGoods) CheckMemberCanPurchaseBeforePay(ctx context.Context, memberId string, specIdMd5 string, purchaseCount uint64) error {
	member, _ := GetMember(ctx, memberId)
	if member.BlockedStatus == 2 {
		return codes.NewCanNotPurchaseErrorWithCodeAndMessage(codes.CAN_NOT_PURCHASE_REASON_BLOCKED_MEMBER, "", nil)
	}
	setting := CMallSetting.GetByAccountId(ctx)
	if !setting.Trade.Purchase.IsAllowedNonMemberToPurchase {
		if !member.IsActivated {
			return codes.NewCanNotPurchaseErrorWithCodeAndMessage(codes.CAN_NOT_PURCHASE_REASON_MEMBER_NOT_ACTIVATED, "", nil)
		}
	}

	canPurchaseGlobalNum, err := CheckPurchaseQuantityLimitation(ctx, nil, memberId, purchaseCount)
	if err != nil {
		extra := map[string]interface{}{
			"canPurchaseGlobalNum": canPurchaseGlobalNum,
		}
		return codes.NewCanNotPurchaseErrorWithCodeAndMessage(codes.CAN_NOT_PURCHASE_REASON_GLOBAL_QUANTITY_LIMIT, "", extra)
	}

	if len(goods.ApplicableSpecHashs) > 0 && core_util.IndexOfArray(specIdMd5, goods.ApplicableSpecHashs) < 0 {
		return codes.NewCanNotPurchaseErrorWithCodeAndMessage(codes.CAN_NOT_PURCHASE_REASON_LIMITION_NO_VALID_SPECS, "", nil)
	}
	toBePurchasedScores := goods.Specs.Scores[specIdMd5] * purchaseCount
	canPurchaseScore, err := CheckScoreLimitation(ctx, nil, memberId, toBePurchasedScores)
	if err != nil {
		extra := map[string]interface{}{
			"canUseScore": canPurchaseScore,
		}
		return codes.NewCanNotPurchaseErrorWithCodeAndMessage(codes.CAN_NOT_PURCHASE_REASON_SCORE_LIMIT, "", extra)
	}

	reason, canPurchaseNum := goods.CheckPurchaseLimition(ctx, memberId, specIdMd5, purchaseCount)
	if reason != PURCHASE_LIMITION_VALID {
		extra := map[string]interface{}{
			"canPurchaseNum": canPurchaseNum,
		}
		return codes.NewCanNotPurchaseErrorWithCodeAndMessage(reason, "", extra)
	}

	if goods.PurchaseType == LIMIT_TYPE_LIMIT && purchaseCount > goods.PurchaseLimit {
		return codes.NewCanNotPurchaseErrorWithCodeAndMessage(codes.CAN_NOT_PURCHASE_REASON_GOODS_QUANTITY_LIMIT, "", nil)
	}

	return nil
}

func (s *GoodsSpecs) GetExternalSkusAndSkus(pbProduct *product.ProductDetailResponse) (map[string]string, map[string]string) {
	specifications := pbProduct.GetSpecifications()
	if specifications == nil || len(specifications) == 0 {
		return nil, nil
	}

	pbSkus := pbProduct.GetSkus()
	if pbSkus == nil || len(pbSkus) == 0 {
		return nil, nil
	}

	// 获取所有规格组合
	propertySpecIdsMap := getPropertySpecIdsMap(specifications, false)
	// 获取排序后的 properties,计算 specIds 哈希值
	sortedPropertyHashMap := map[string]string{}
	for k, v := range propertySpecIdsMap {
		hashValue := fmt.Sprintf("%x", md5.Sum([]byte(v)))
		sortedPropertyHashMap[k] = hashValue
	}

	// 获取 external sku map
	hashExternalSkuMap := map[string]string{}
	hashSkuMap := map[string]string{}
	for _, pbSku := range pbSkus {
		sort.Strings(pbSku.Properties)
		sortKey := strings.Join(pbSku.Properties, ",")
		if hash, ok := sortedPropertyHashMap[sortKey]; ok {
			hashExternalSkuMap[hash] = pbSku.External
			hashSkuMap[hash] = pbSku.Sku
		}
	}

	return hashExternalSkuMap, hashSkuMap
}

// @params specifications 商品规格组合
// @params revertKey 反转返回值的 key value
// @return map[规格属性组合]规格Id组合
// 假设输入为：
// "specifications" : [
//
//	{
//		"id" : "71518b1d-9ff0-59b4-9678-84363d0cac3d",
//		"name" : "颜色",
//		"properties" : [
//			{
//				"id" : "9bbf185c-30a0-7f88-14ba-7b717cf41fdc",
//				"name" : "红色",
//			},
//			{
//				"id" : "67c97871-ca06-e1f2-0ba9-c7b377769ccf",
//				"name" : "白色",
//			}
//		]
//	},
//	{
//		"id" : "71518b1d-9ff0-59b4-9678-84363d0cac3e",
//		"name" : "容量",
//		"properties" : [
//			{
//				"id" : "9bbf185c-30a0-7f88-14ba-7b717cf41fdg",
//				"name" : "32G",
//			},
//			{
//				"id" : "67c97871-ca06-e1f2-0ba9-c7b377769cch",
//				"name" : "64G",
//			}
//		]
//	}
//
// ],
// 输出：
//
//	{
//		"32G,红色":"9bbf185c-30a0-7f88-14ba-7b717cf41fdc,9bbf185c-30a0-7f88-14ba-7b717cf41fdg",
//		"64G,红色":"67c97871-ca06-e1f2-0ba9-c7b377769ccf,67c97871-ca06-e1f2-0ba9-c7b377769cch",
//		"32G,白色":"67c97871-ca06-e1f2-0ba9-c7b377769ccf,9bbf185c-30a0-7f88-14ba-7b717cf41fdg",
//		"64G,白色":"67c97871-ca06-e1f2-0ba9-c7b377769ccf,67c97871-ca06-e1f2-0ba9-c7b377769cch"
//	}
func getPropertySpecIdsMap(specifications []*product.Specification, revertKey bool) map[string]string {
	propertySpecIdsMap := map[string]string{}
	for len(specifications) > 0 {
		specification := specifications[0]
		if len(propertySpecIdsMap) == 0 {
			properties := specification.GetProperties()
			for _, property := range properties {
				propertySpecIdsMap[property.Name] = property.Id
			}
		} else {
			tmpMap := map[string]string{}
			for key, value := range propertySpecIdsMap {
				properties := specification.GetProperties()
				for _, property := range properties {
					k := fmt.Sprintf("%s,%s", key, property.Name)
					v := fmt.Sprintf("%s,%s", value, property.Id)
					tmpMap[k] = v
				}
			}
			propertySpecIdsMap = tmpMap
		}

		specifications = specifications[1:]
	}

	sortedPropertySpecIdsMap := map[string]string{}
	for key, value := range propertySpecIdsMap {
		sortedPropertySpecIdsMap[util.SortDotString(key)] = util.SortDotString(value)
	}

	if !revertKey {
		return sortedPropertySpecIdsMap
	}

	revertedMap := map[string]string{}
	for key, value := range sortedPropertySpecIdsMap {
		revertedMap[value] = key
	}

	return revertedMap
}

func GetBarcode(propertyIds string, productDetail *product.ProductDetailResponse) string {
	var barCode string
	if productDetail == nil {
		return barCode
	}

	skus := productDetail.GetSkus()
	if skus == nil || len(skus) == 0 {
		return barCode
	}

	specifications := productDetail.GetSpecifications()
	if specifications == nil || len(specifications) == 0 {
		return skus[0].GetBarCode()
	}

	sortKey := util.SortDotString(propertyIds)
	propertyIdsSpecMap := getPropertySpecIdsMap(specifications, true)
	if specs, ok := propertyIdsSpecMap[sortKey]; ok {
		for _, sku := range skus {
			sort.Strings(sku.Properties)
			properties := strings.Join(sku.Properties, ",")
			if properties == specs {
				return sku.BarCode
			}
		}
	}

	return barCode
}

func GetClassificationIds(productDetail *product.ProductDetailResponse) []bson.ObjectId {
	result := []bson.ObjectId{}

	if productDetail == nil {
		return result
	}

	for _, classification := range productDetail.Classifications {
		result = append(result, bson.ObjectIdHex(classification.Id))
	}

	return result
}

func (*MallGoods) GetAllByIds(ctx context.Context, goodsId []bson.ObjectId) []MallGoods {
	goods := []MallGoods{}
	condition := bson.M{"_id": bson.M{"$in": goodsId}}
	extension.DBRepository.FindAll(ctx, C_MALL_GOODS, condition, []string{}, 0, &goods)
	return goods
}

func (*MallGoods) GetAllByCondition(ctx context.Context, selector bson.M) []MallGoods {
	goods := []MallGoods{}
	extension.DBRepository.FindAll(ctx, C_MALL_GOODS, selector, []string{}, 0, &goods)
	return goods
}

type GoodsInfoFactory struct {
	GoodsIds      []bson.ObjectId
	productStrIds []string
	GoodsMap      map[string]*MallGoods
	ProductMap    map[string]*product.ProductDetailResponse
}

func (self *GoodsInfoFactory) ProcessInfo(ctx context.Context) {
	self.GoodsMap = make(map[string]*MallGoods, len(self.GoodsIds))
	goodsItems := CMallGoods.GetAllByIds(ctx, self.GoodsIds)
	for idx, g := range goodsItems {
		self.GoodsMap[g.Id.Hex()] = &goodsItems[idx]
		if g.Product.Type != MALL_GOODS_TYPE_COUPON {
			self.productStrIds = append(self.productStrIds, g.Product.Id.Hex())
		}
	}

	self.ProductMap = make(map[string]*product.ProductDetailResponse, len(self.productStrIds))
	products := &product.ProductListResponse{}
	if len(self.productStrIds) != 0 {
		products, _ = SearchProductByIds(ctx, self.productStrIds)
	}
	for _, productItem := range products.GetItems() {
		self.ProductMap[productItem.GetId()] = productItem
	}
}

func (self *GoodsInfoFactory) GetProductSku(goodsId, propIds string) *product.SpecProdSku {
	goods, ok := self.GoodsMap[goodsId]
	if !ok {
		return nil
	}
	productDetail, ok := self.ProductMap[goods.Product.Id.Hex()]
	if !ok {
		return nil
	}
	propertyIds := strings.Split(propIds, ",")
	propertyNames := []string{}
	for _, spec := range productDetail.Specifications {
		for _, prop := range spec.Properties {
			if util.StrInArray(prop.Id, &propertyIds) {
				propertyNames = append(propertyNames, prop.Name)
				break
			}
		}
	}
	for _, sku := range productDetail.Skus {
		if core_util.StringArrayEqual(&propertyNames, &sku.Properties) {
			return sku
		}
	}
	return nil
}

func (*MallGoods) GetAllByProductIds(ctx context.Context, productIds []bson.ObjectId) []MallGoods {
	goods := []MallGoods{}
	condition := bson.M{"product.id": bson.M{"$in": productIds}}
	extension.DBRepository.FindAll(ctx, C_MALL_GOODS, condition, []string{}, 0, &goods)
	return goods
}

func (*MallGoods) UpdateSpecStock(ctx context.Context, id bson.ObjectId, stock int, key string) error {
	specsStockKey := fmt.Sprintf("specs.stocks.%s", key)
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       id,
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			specsStockKey: stock,
		},
	}

	return extension.DBRepository.UpdateOne(ctx, C_MALL_GOODS, selector, updater)
}

func (*MallGoods) UpdateStock(ctx context.Context, id bson.ObjectId, stock int) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       id,
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"stock": stock,
		},
	}

	return extension.DBRepository.UpdateOne(ctx, C_MALL_GOODS, selector, updater)
}

func GetPropertySpecIdsMap(specifications []*product.Specification, revertKey bool) map[string]string {
	propertyIdsSpecMap := getPropertySpecIdsMap(specifications, revertKey)
	return propertyIdsSpecMap
}

func (goods *MallGoods) CheckPurchaseLimition(ctx context.Context, memberId string, specIdMd5 string, purchaseCount uint64) (string, uint64) {
	// TODO: 优化测试代码，需要改动的测试代码范围较大
	var canPurchaseNum uint64 = 0
	if core_util.GetUserRole(ctx) == "member" {
		response := goods.CheckMemberCanPurchase(ctx, memberId)
		if !response.CanPurchase {
			return response.Reason, 0
		}
	}
	if goods.Status == MALL_GOODS_STATUS_OFF || goods.IsDeleted {
		return codes.CAN_NOT_PURCHASE_REASON_LIMITION_DELETED, 0
	} else if goods.Status == MALL_GOODS_STATUS_SCHEDULED && goods.ScheduledShelveAt.After(time.Now()) {
		return codes.CAN_NOT_PURCHASE_REASON_LIMITION_DELETED, 0
	} else if goods.Status == MALL_GOODS_STATUS_ON && !goods.ScheduledUnshelveAt.IsZero() && goods.ScheduledUnshelveAt.Before(time.Now()) {
		return codes.CAN_NOT_PURCHASE_REASON_LIMITION_DELETED, 0
	}
	if len(goods.ApplicableSpecHashs) > 0 && core_util.IndexOfArray(specIdMd5, goods.ApplicableSpecHashs) < 0 {
		return codes.CAN_NOT_PURCHASE_REASON_LIMITION_NO_VALID_SPECS, 0
	}
	if goods.Product.Type == MALL_GOODS_TYPE_PRODUCT {
		if len(goods.Specs.Prices) == 1 {
			for key := range goods.Specs.Prices {
				specIdMd5 = key
			}
		}
		if goods.ActiveSyncStock && goods.Specs.Stocks[specIdMd5] > goods.Specs.SyncStocks[specIdMd5] {
			goods.Specs.Stocks[specIdMd5] = goods.Specs.SyncStocks[specIdMd5]
		}
		if goods.Specs.Stocks[specIdMd5] < 1 {
			return codes.CAN_NOT_PURCHASE_REASON_LIMITION_SOLDOUT, 0
		}
		if goods.Specs.Stocks[specIdMd5] < purchaseCount {
			return codes.CAN_NOT_PURCHASE_REASON_LIMITION_NOT_ENOUGH, 0
		}
	}
	if goods.StockType == LIMIT_TYPE_LIMIT && goods.Stock < 1 {
		return codes.CAN_NOT_PURCHASE_REASON_LIMITION_SOLDOUT, 0
	}
	if !reflect.DeepEqual(goods.PurchaseLimitation, PurchaseLimitation{}) {
		if goods.PurchaseLimitation.IsDateTimeLimitationEnabled {
			nowTime := time.Now()
			isOverTime := true
			for _, dateTimeLimitation := range goods.PurchaseLimitation.DateTimeLimitation {
				dateTimeLimitation = FormatDateTimeLimitation(dateTimeLimitation)
				if nowTime.After(dateTimeLimitation.StartDate) && nowTime.Before(dateTimeLimitation.EndDate) {
					dateTimeLimitationRanges := GetPurchaseLimitionTimeRange(ctx, dateTimeLimitation, nowTime)
					for _, dateTimeLimitationRange := range dateTimeLimitationRanges {
						if nowTime.Before(dateTimeLimitationRange.EndTime) && nowTime.After(dateTimeLimitationRange.StartTime) {
							isOverTime = false
							break
						}
					}
					if !isOverTime {
						break
					}
				}
			}

			if isOverTime {
				return codes.CAN_NOT_PURCHASE_REASON_LIMITION_OVERTIME, 0
			}
		}

		if goods.PurchaseLimitation.IsNumLimitationEnabled {
			startTime := time.Now()
			purchaseNum := 0

			switch goods.PurchaseLimitation.NumLimitation.DailyType {
			case LIMITION_TIME_TYPE_DAILY:
				startTime = util.GetStartTimeOfDay(startTime)
			case LIMITION_TIME_TYPE_WEEKLY:
				startTime = util.GetStartTimeOfWeek(startTime)
			case LIMITION_TIME_TYPE_MONTHLY:
				startTime = util.GetStartTimeOfMonth(startTime)
			case LIMITION_TIME_TYPE_QUARTER:
				startTime = util.GetStartTimeOfQuarter(startTime)
			case LIMITION_TIME_TYPE_YEARLY:
				startTime = util.GetStartTimeOfYear(startTime)
			}

			if goods.PurchaseLimitation.NumLimitation.DailyType == LIMITION_TIME_TYPE_ALL {
				var zeroTime time.Time
				startTime = zeroTime
			}

			if goods.PurchaseLimitation.NumLimitation.MemberType == LIMITION_MEMBER_TYPE_ONE {
				purchaseNum = GetPurchaseNum(ctx, goods.Id, memberId, startTime)
			} else if goods.PurchaseLimitation.NumLimitation.MemberType == LIMITION_MEMBER_TYPE_ALL {
				purchaseNum = GetPurchaseNum(ctx, goods.Id, "", startTime)
			}
			if uint64(purchaseNum)+purchaseCount > goods.PurchaseLimitation.NumLimitation.Num {
				return codes.CAN_NOT_PURCHASE_REASON_LIMITION_NUMLIMIT, 0
			}
			canPurchaseNum = goods.PurchaseLimitation.NumLimitation.Num - uint64(purchaseNum)
		}
	}
	return PURCHASE_LIMITION_VALID, canPurchaseNum
}

func FormatDateTimeLimitation(dateTimeLimitation DateTimeLimitation) DateTimeLimitation {
	startHour := dateTimeLimitation.StartDate.Hour()
	startMinute := dateTimeLimitation.StartDate.Minute()
	startSecond := dateTimeLimitation.StartDate.Second()
	dateTimeLimitation.StartDate = dateTimeLimitation.StartDate.Add(time.Hour * time.Duration(-startHour)).Add(time.Minute * time.Duration(-startMinute)).Add(time.Second * time.Duration(-startSecond))
	endHour := dateTimeLimitation.EndDate.Hour()
	endMinute := dateTimeLimitation.EndDate.Minute()
	endSecond := dateTimeLimitation.EndDate.Second()
	dateTimeLimitation.EndDate = dateTimeLimitation.EndDate.Add(time.Hour*time.Duration(-endHour)).Add(time.Minute*time.Duration(-endMinute)).Add(time.Second*time.Duration(-endSecond)).AddDate(0, 0, 1)
	return dateTimeLimitation
}

func GetPurchaseNum(ctx context.Context, goodsId bson.ObjectId, memberId string, startTime time.Time) int {
	purchaseNum := 0
	orders := []Order{}
	condition := bson.M{
		"goods.id":  goodsId,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"createdAt": bson.M{
			"$gt": startTime,
		},
		"status": bson.M{
			"$nin": []string{ORDER_STATUS_REFUNDED, ORDER_STATUS_CANCELED},
		},
	}
	if memberId != "" {
		condition["member.id"] = bson.ObjectIdHex(memberId)
	}
	extension.DBRepository.FindAll(ctx, C_MALL_ORDER, condition, []string{}, 0, &orders)
	for _, order := range orders {
		for _, goods := range order.Goods {
			if goods.Id == goodsId {
				purchaseNum += int(goods.Total)
			}
		}

	}
	log.Warn(ctx, "GetPurchaseNum", log.Fields{"purchaseNum": purchaseNum, "condition": condition})
	return purchaseNum
}

func GetPurchaseLimitionTimeRange(ctx context.Context, dateTimeLimitation DateTimeLimitation, nowTime time.Time) []DateTimeLimitionRange {
	dateTimeLimitionRanges := []DateTimeLimitionRange{}
	for _, Time := range dateTimeLimitation.Times {
		StartTimeArr := strings.Split(Time.Start, ":")
		startHour, _ := strconv.Atoi(StartTimeArr[0])
		startMinute, _ := strconv.Atoi(StartTimeArr[1])
		endTimeArr := strings.Split(Time.End, ":")
		endHour, _ := strconv.Atoi(endTimeArr[0])
		endMinute, _ := strconv.Atoi(endTimeArr[1])
		nowTimeHour := nowTime.Hour()
		nowTimeMinute := nowTime.Minute()
		nowTimeSecond := nowTime.Second()
		dateTimeLimitionRange := DateTimeLimitionRange{
			StartTime: nowTime.Add(time.Hour * time.Duration(startHour-nowTimeHour)).Add(time.Minute * time.Duration(startMinute-nowTimeMinute)).Add(time.Second * time.Duration(-nowTimeSecond)),
			EndTime:   nowTime.Add(time.Hour * time.Duration(endHour-nowTimeHour)).Add(time.Minute * time.Duration(endMinute-nowTimeMinute)).Add(time.Second * time.Duration(-nowTimeSecond)),
		}
		dateTimeLimitionRanges = append(dateTimeLimitionRanges, dateTimeLimitionRange)
	}

	return dateTimeLimitionRanges
}

func (goods *MallGoods) CheckMemberCanPurchase(ctx context.Context, memberId string) *mall.CheckMemberCanPurchaseResponse {
	setting := CMallSetting.GetByAccountId(ctx)
	result := &mall.CheckMemberCanPurchaseResponse{
		CanPurchase: true,
		Reason:      "",
	}
	_, err := CheckScoreLimitation(ctx, setting, memberId, 0)
	if err != nil {
		result.CanPurchase = false
		result.Reason = codes.CAN_NOT_PURCHASE_REASON_SCORE_LIMIT
		return result
	}

	_, err = CheckPurchaseQuantityLimitation(ctx, setting, memberId, 0)
	if err != nil {
		result.CanPurchase = false
		result.Reason = codes.CAN_NOT_PURCHASE_REASON_GLOBAL_QUANTITY_LIMIT
		return result
	}

	if !setting.Trade.Purchase.IsAllowedNonMemberToPurchase {
		member, _ := GetMember(ctx, memberId)
		if !member.IsActivated {
			result.CanPurchase = false
			result.Reason = codes.CAN_NOT_PURCHASE_REASON_MEMBER_NOT_ACTIVATED
			return result
		}
	}

	reason := goods.CheckMemberLimitation(ctx, memberId)
	if reason != "" {
		result.CanPurchase = false
		result.Reason = reason
		return result
	}
	return result
}

func (*MallGoods) GetAllByPagination(ctx context.Context, selector bson.M, listCondition *request.ListCondition) []MallGoods {
	pageCondition := util.FormatPagingCondition(selector, listCondition)
	var goodsList []MallGoods
	extension.DBRepository.FindByPagination(ctx, C_MALL_GOODS, pageCondition, &goodsList)
	return goodsList
}

func DeleteIfProductNotExist(ctx context.Context, goods *MallGoods) {
	// 检查商品源数据是否存在
	_, err := client.GetProductServiceClient().GetProduct(ctx, &product.GetProductRequest{
		Id: goods.Product.Id.Hex(),
	})
	if err != nil {
		log.Warn(ctx, "Failed to get product by id", log.Fields{"goodsId": goods.Id, "productId": goods.Product.Id.Hex(), "err": err})
		if mairpcErr := errors.ToMaiRPCError(err); mairpcErr.Code != product_codes.ProductNotFound {
			return
		}
		// 不存在则下架并删除积分商城货架上商品
		deleteSelector := bson.M{
			"_id": goods.Id,
		}
		updator := bson.M{
			"$set": bson.M{
				"status":    MALL_GOODS_STATUS_OFF,
				"isDeleted": true,
			},
		}
		err = extension.DBRepository.UpdateOne(ctx, C_MALL_GOODS, deleteSelector, updator)
		log.Warn(ctx, "Remove goods with a deleted product info", log.Fields{"goodsId": goods.Id})
		if err != nil {
			log.Warn(ctx, "Failed to remove goods when product is deleted", log.Fields{
				"goodsId": goods.Id,
			})
		}
	}
}

func DeleteIfCouponNotExist(ctx context.Context, goods *MallGoods) {
	// 检查优惠券源数据是否存在
	_, err := client.GetCouponServiceClient().GetCoupon(ctx, &coupon.CouponDetailRequest{
		Id: goods.Product.Id.Hex(),
	})
	if err != nil {
		log.Warn(ctx, "Failed to get coupon by id", log.Fields{"goodsId": goods.Id, "couponId": goods.Product.Id.Hex(), "err": err})
		if mairpcErr := errors.ToMaiRPCError(err); mairpcErr.Code != coupon_codes.CouponNotFound {
			return
		}
		// 不存在则下架并删除积分商城货架上商品
		deleteSelector := bson.M{
			"_id": goods.Id,
		}
		updator := bson.M{
			"$set": bson.M{
				"status":    MALL_GOODS_STATUS_OFF,
				"isDeleted": true,
			},
		}
		err = extension.DBRepository.UpdateOne(ctx, C_MALL_GOODS, deleteSelector, updator)
		log.Warn(ctx, "Remove goods with a deleted coupon info", log.Fields{"goodsId": goods.Id})
		if err != nil {
			log.Warn(ctx, "Failed to remove goods when coupon is deleted", log.Fields{
				"goodsId": goods.Id,
			})
		}
	}
}

// 只有 memberLimitation.Type = levels 时，operator 才会有 GTE
func (goods *MallGoods) CheckMemberLimitation(ctx context.Context, memberId string) string {
	memberLimitation := goods.MemberLimitation
	if reflect.DeepEqual(memberLimitation, MemberLimitation{}) || !memberLimitation.IsEnable {
		return ""
	}
	member, _ := GetMember(ctx, memberId)

	switch memberLimitation.Type {
	case MEMBER_LIMIT_TYPE_LEVELS:
		return checkMemberLevelLimitation(ctx, member, &memberLimitation.MemberLevelLimitation, memberLimitation.Operator)
	case MEMBER_LIMIT_TYPE_GROUPS:
		return checkMemberGroupsLimitation(ctx, member, memberLimitation.StaticGroupIds, memberLimitation.DynamicGroupIds, memberLimitation.Operator)
	case MEMBER_LIMIT_TYPE_TAGS:
		var labelNames []string
		labels, err := component.Bigdata.GetLabelsInMember(ctx, memberId)
		if err != nil {
			return err.Error()
		}
		for _, label := range labels {
			labelNames = append(labelNames, fmt.Sprintf("%s_%s", label.Name, label.ValueCN))
		}
		labels, err = component.Bigdata.GetRuleLabelsInMember(ctx, memberId)
		if err != nil {
			return err.Error()
		}
		for _, label := range labels {
			labelNames = append(labelNames, fmt.Sprintf("%s_%s", label.Name, label.ValueCN))
		}

		var labelNamesInRule []string
		for _, lable := range memberLimitation.Labels {
			labelNamesInRule = append(labelNamesInRule, lable.Name)
		}

		log.Warn(ctx, "checkMemberTagsLimitation", log.Fields{"memberId": memberId, "goodsId": goods.Id, "memberTags": append(member.Tags, labelNames...), "ruleTags": append(memberLimitation.Tags, labelNamesInRule...)})
		return checkMemberTagsLimitation(ctx, append(member.Tags, labelNames...), append(memberLimitation.Tags, labelNamesInRule...), memberLimitation.Operator)
	default:
		return ""
	}
}

func checkMemberLevelLimitation(ctx context.Context, member *member.MemberDetailResponse, memberLevelLimitation *MemberLevelLimitation, operator string) string {
	switch operator {
	case share_model.OPERATOR_IN:
		canBuy := true
		if memberLevelLimitation.IsPaidMember {
			canBuy = checkIsPaidMember(ctx, member)
			if !canBuy && len(memberLevelLimitation.MemberLevels) > 0 {
				canBuy = core_util.IndexOfArray(member.Level, memberLevelLimitation.MemberLevels) != -1
			}
		} else {
			if len(memberLevelLimitation.MemberLevels) > 0 {
				canBuy = core_util.IndexOfArray(member.Level, memberLevelLimitation.MemberLevels) != -1
			}
		}
		if canBuy {
			return ""
		}
		if memberLevelLimitation.IsPaidMember && !checkIsPaidMember(ctx, member) {
			return codes.CAN_NOT_PURCHASE_REASON_NON_PAID_MEMBER
		}
		return codes.CAN_NOT_PURCHASE_REASON_MEMBER_LEVEL_NOT_ALLOWED
	case share_model.OPERATOR_NIN:
		canBuy := true
		if !memberLevelLimitation.IsPaidMember {
			canBuy = checkIsPaidMember(ctx, member)
			if !canBuy && len(memberLevelLimitation.MemberLevels) > 0 {
				canBuy = core_util.IndexOfArray(member.Level, memberLevelLimitation.MemberLevels) == -1
			}
		} else {
			if len(memberLevelLimitation.MemberLevels) > 0 {
				canBuy = core_util.IndexOfArray(member.Level, memberLevelLimitation.MemberLevels) == -1
			}
		}
		if canBuy {
			return ""
		}
		if !memberLevelLimitation.IsPaidMember && !checkIsPaidMember(ctx, member) {
			return codes.CAN_NOT_PURCHASE_REASON_NON_PAID_MEMBER
		}
		return codes.CAN_NOT_PURCHASE_REASON_MEMBER_LEVEL_NOT_ALLOWED
	// GTE 情况下，不需要判断 PaidMember
	case share_model.OPERATOR_GTE:
		if len(memberLevelLimitation.MemberLevels) <= 0 {
			return ""
		}
		if member.Level < memberLevelLimitation.MemberLevels[0] {
			return codes.CAN_NOT_PURCHASE_REASON_MEMBER_LEVEL_NOT_ALLOWED
		}
	default:
		return ""
	}
	return ""
}

func checkIsPaidMember(ctx context.Context, member *member.MemberDetailResponse) bool {
	isPaidMember := false
	for _, paidCard := range member.PaidCards {
		if paidCard.CardType != "paidMember" {
			continue
		}
		expireAt := core_util.ParseRFC3339(paidCard.ExpireAt)
		isPaidMember = time.Now().Before(expireAt)
	}
	return isPaidMember
}

func checkMemberGroupsLimitation(ctx context.Context, member *member.MemberDetailResponse, staticGroupIds []string, dynamicGroupIds []string, operator string) string {

	memberGroups, err := getMemberGroupByMemberId(ctx, member.Id)
	if err != nil {
		return codes.CAN_NOT_PURCHASE_REASON_MEMBER_GROUP_NOT_ALLOWED
	}
	memberGroupIds := core_util.ExtractArrayStringField("Id", memberGroups.Items)
	if (len(staticGroupIds) > 0 || len(dynamicGroupIds) > 0) && !share_model.MatchArrays(memberGroupIds, share_model.CompareRule{
		Operator: operator,
		Value:    append(staticGroupIds, dynamicGroupIds...),
	}) {
		return codes.CAN_NOT_PURCHASE_REASON_MEMBER_GROUP_NOT_ALLOWED
	}
	return ""
}

func checkMemberTagsLimitation(ctx context.Context, memberTags []string, memberLimitationTags []string, operator string) string {
	if len(memberLimitationTags) > 0 && !share_model.MatchArrays(memberTags, share_model.CompareRule{
		Value:    memberLimitationTags,
		Operator: operator,
	}) {
		return codes.CAN_NOT_PURCHASE_REASON_MEMBER_TAG_NOT_ALLOWED
	}
	return ""
}

func getMemberGroupByMemberId(ctx context.Context, memberId string) (*member.GetMemberGroupByMemberResponse, error) {
	req := &member.GetMemberGroupByMemberRequest{MemberId: memberId}
	resp, err := mairpc.Run(
		"MemberService.GetMemberGroupByMember",
		ctx,
		req,
	)
	if err != nil {
		return nil, err
	}
	return resp.(*member.GetMemberGroupByMemberResponse), nil
}
