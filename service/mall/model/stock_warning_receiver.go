package model

import (
	"context"
	"time"

	"mairpc/core/extension"
	"mairpc/core/extension/bson"
)

const (
	C_MICROMALL_STOCK_WARNING_RECEIVER = "micromallStockWarningReceiver"
)

var CStockWarningReceiver = &StockWarningReceiver{}

type StockWarningReceiver struct {
	Id        bson.ObjectId `bson:"_id,omitempty"`
	AccountId bson.ObjectId `bson:"accountId"`
	CreatedAt time.Time     `bson:"createdAt"`
	UpdatedAt time.Time     `bson:"updatedAt"`
	IsDeleted bool          `bson:"isDeleted"`
	UserId    bson.ObjectId `bson:"userId"`
}

func (*StockWarningReceiver) BatchCreate(ctx context.Context, receivers []StockWarningReceiver) error {
	docs := []interface{}{}
	for _, receiver := range receivers {
		docs = append(docs, receiver)
	}
	_, err := extension.DBRepository.Insert(ctx, C_MICROMALL_STOCK_WARNING_RECEIVER, docs...)
	return err
}

func (*StockWarningReceiver) Delete(ctx context.Context, id bson.ObjectId) error {
	condition := Common.GenDefaultConditionById(ctx, id)
	updater := bson.M{
		"$set": bson.M{
			"updatedAt": time.Now(),
			"isDeleted": true,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_MICROMALL_STOCK_WARNING_RECEIVER, condition, updater)
}

func (*StockWarningReceiver) FindByPagination(ctx context.Context, page extension.PagingCondition) ([]StockWarningReceiver, int, error) {
	results := []StockWarningReceiver{}
	total, err := extension.DBRepository.FindByPagination(ctx, C_MICROMALL_STOCK_WARNING_RECEIVER, page, &results)
	return results, total, err
}

func (*StockWarningReceiver) FindAll(ctx context.Context) ([]StockWarningReceiver, error) {
	selector := Common.GenDefaultCondition(ctx)
	results := []StockWarningReceiver{}
	err := extension.DBRepository.FindAll(ctx, C_MICROMALL_STOCK_WARNING_RECEIVER, selector, []string{}, 0, &results)
	return results, err
}
