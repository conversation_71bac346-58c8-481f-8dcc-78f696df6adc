@every 10s MallService.PushMallOrdersToOmsJob micromall
@every 5m MallService.SyncMallStatusFromOmsJob micromall
@every 10s MallService.InterceptMallOrdersToOmsJob micromall
@every 1m MallService.CheckAndUpdateGoodsStatus micromall
@every 1m MallService.BatchGetRefundStatus micromall
@every 5m MallService.AutoCompleteOrderJob
@every 1m MallService.DeleteNonexistentSourceGoods micromall
@every 4h MallService.SendStockWarningEmail micromall
