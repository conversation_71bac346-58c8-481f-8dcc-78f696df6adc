package service

import (
	"fmt"
	"mairpc/core/component"
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/client"
	"mairpc/proto/common/response"
	pb_ec_store "mairpc/proto/ec/store"
	pb "mairpc/proto/store"
	"mairpc/service/share/util"
	"mairpc/service/store/model"
	"os"
	"time"

	"golang.org/x/net/context"
)

func (StoreService) UpdateDistributor(ctx context.Context, req *pb.UpdateDistributorRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	if req.IsEnabled != nil || req.Status != nil {
		err := updateDistributorStatus(ctx, req)
		if err != nil {
			return nil, err
		}
		return &response.EmptyResponse{}, nil
	}

	selector := model.Common.GenDefaultConditionById(ctx, bson.ObjectIdHex(req.Id))
	dbDistributor, err := model.CDistributor.GetByCondition(ctx, selector)
	if err != nil {
		return nil, errors.NewInvalidArgumentError("id")
	}

	// 纳爱斯环境通过接口同步组织的时候有很多重复名称的组织，所以不需要做唯一性校验
	if env := os.Getenv("ENV"); !core_util.ContainsString(&[]string{"cnnice-staging", "cnnice-production"}, env) {
		if dbDistributor.Name != req.Name && isDistributorExist(ctx, req.Id, "name", req.Name) {
			return nil, errors.NewAlreadyExistsError("name")
		}
	}

	updater, err := genUpdateDistributorCondition(ctx, req, dbDistributor)
	if err != nil {
		return nil, err
	}

	err = model.CDistributor.UpdateByCondition(ctx, selector, updater)
	if err != nil {
		return nil, err
	}
	newDistributorIds := updater["$set"].(bson.M)["distributorIds"]
	component.GO(ctx, func(ctx context.Context) {
		if newDistributorIds != nil {
			afterUpdateParentDepartment(ctx, dbDistributor.Id, core_util.ToObjectIdArray(newDistributorIds), dbDistributor.DistributorIds)
		}
		if req.Helpdesk != nil && len(req.Helpdesk.StaffIds) > 0 {
			// 将客服与门店关联起来
			updateStaffStoreIds(ctx, req.Helpdesk.StaffIds, req.Id)
		}
		newTags := []string{}
		if req.Tags != nil {
			newTags = req.Tags.Values
		}
		deletedTags := util.StrArrayDiff(dbDistributor.Tags, newTags)
		addedTags := util.StrArrayDiff(newTags, dbDistributor.Tags)
		countMap := map[string]int64{}
		for _, tag := range deletedTags {
			countMap[tag] = -1
		}
		for _, tag := range addedTags {
			countMap[tag] = 1
		}
		if len(countMap) > 0 {
			client.GetEcStoreServiceClient().UpdateTagDistributorCount(ctx, &pb_ec_store.UpdateTagDistributorCountRequest{
				CountMap: countMap,
			})
		}
	})

	return &response.EmptyResponse{}, nil
}

func updateStaffStoreIds(ctx context.Context, staffIds []string, storeId string) {
	staffs, _ := model.CDmsStaff.GetByIds(ctx, util.ToMongoIds(staffIds))
	for _, staff := range staffs {
		storeIds := util.MongoIdsToStrs(staff.AccessibleDistributorIds)
		if util.StrInArray(storeId, &storeIds) {
			continue
		}
		storeIds = append(storeIds, storeId)
		staff.AccessibleDistributorIds = util.ToMongoIds(storeIds)
		staff.Update(ctx)
	}
}

func genUpdateDistributorCondition(ctx context.Context, req *pb.UpdateDistributorRequest, dbDistributor *model.Distributor) (bson.M, error) {
	distributor := &model.Distributor{}
	copier.Instance(nil).From(req).CopyTo(distributor)
	setter := bson.M{
		"updatedAt": time.Now(),
	}
	if dbDistributor.Type == 3 {
		setter["businessHours"] = distributor.BusinessHours
		if len(req.BusinessHours) == 0 {
			setter["status"] = model.STATUS_CLOSED
		}

		if req.AutoCloseAt != nil {
			setter["autoCloseAt"], _ = util.TransStrToTime(req.AutoCloseAt.Value)
		}
		if req.AutoOpenAt != nil {
			setter["autoOpenAt"], _ = util.TransStrToTime(req.AutoOpenAt.Value)
		}
	}

	if req.Name != "" {
		setter["name"] = req.Name
	}
	if req.Info != "" {
		setter["info"] = req.Info
	}
	if req.StoreTypeId != "" {
		setter["storeTypeId"] = distributor.StoreTypeId
	}
	if req.DeliverySetting != nil {
		setter["deliverySetting.isDefault"] = req.DeliverySetting.IsDefault
		setter["deliverySetting.deliveryMethods"] = req.DeliverySetting.DeliveryMethods

		// deliverySetting.isDefault 传 true 时，把原来 true 的设置为 false
		if req.DeliverySetting.IsDefault {
			selector := model.Common.GenDefaultCondition(ctx)
			selector["deliverySetting.isDefault"] = true
			updater := bson.M{
				"$set": bson.M{
					"updatedAt":                       time.Now(),
					"deliverySetting.isDefault":       false,
					"deliverySetting.deliveryMethods": []string{},
				},
			}
			model.CDistributor.UpdateAllByCondition(ctx, selector, updater)
		}
	}
	if req.Location != nil {
		if distributor.Location.FullName == "" {
			distributor.Location.FullName = fmt.Sprintf("%s%s%s%s", distributor.Location.Province, distributor.Location.City, distributor.Location.District, distributor.Location.Name)
		}
		setter["location"] = distributor.Location
	}
	if req.Phone != "" {
		setter["phone"] = req.Phone
	}
	if req.LevelId != "" {
		setter["levelId"] = distributor.LevelId
	}
	if req.OpenedAt != "" {
		setter["openedAt"] = distributor.OpenedAt
	}
	if len(req.CustomInfo) > 0 {
		setter["customInfo"] = distributor.CustomInfo
	}
	if req.Code != "" {
		setter["code"] = req.Code
	}
	if req.ClosedAt != "" {
		setter["closedAt"] = distributor.ClosedAt
	}

	if req.SubType != nil {
		setter["subType"] = int(req.SubType.Value)
	}

	if req.Tags != nil {
		setter["tags"] = req.Tags.Values
	}

	if req.VisibleMemberTags != nil {
		setter["visibleMemberTags"] = req.VisibleMemberTags
	}

	if req.VisibleMemberRuleTags != nil {
		setter["visibleMemberRuleTags"] = req.VisibleMemberRuleTags
	}

	if req.VisibleMemberModelTags != nil {
		setter["visibleMemberModelTags"] = req.VisibleMemberModelTags
	}

	if req.Location != nil && req.Location.Longitude != 0 && req.Location.Latitude != 0 {
		setter["coordinate"] = model.Coordinate{
			Type:        "Point",
			Coordinates: []float64{distributor.Location.Longitude, distributor.Location.Latitude},
		}
	}
	if req.ParentDepartmentId != "" {
		distributor, _ := model.CDistributor.GetByDepartmentId(ctx, req.ParentDepartmentId)
		if distributor.Id.Valid() {
			setter["parentDepartmentId"] = req.ParentDepartmentId
			setter["parentId"] = distributor.Id
			parentDistributorIds, err := getParentDistributorIds(ctx, []bson.ObjectId{distributor.Id})
			if err != nil {
				return nil, err
			}
			setter["ancestorIds"] = parentDistributorIds // 跟旧版保持一致不设置自己的 id
			parentDistributorIds = append(parentDistributorIds, bson.ObjectIdHex(req.Id))
			setter["distributorIds"] = parentDistributorIds
		}
	}

	if req.Helpdesk != nil {
		helpDeskStaffs := []model.HelpDeskStaff{}
		if len(req.Helpdesk.StaffIds) > 0 {
			staffs, _ := model.CDmsStaff.GetByIds(ctx, util.ToMongoIds(req.Helpdesk.StaffIds))
			for _, staff := range staffs {
				helpDeskStaffs = append(helpDeskStaffs, model.HelpDeskStaff{
					Name:    staff.Name,
					StaffId: staff.Id.Hex(),
				})
			}
		}
		setter["helpdesk"] = model.Helpdesk{
			Link:   req.Helpdesk.Link,
			Phone:  req.Helpdesk.Phone,
			Staffs: helpDeskStaffs,
		}
	}
	if req.Introduction != "" {
		setter["introduction"] = req.Introduction
	}
	return bson.M{"$set": setter}, nil
}

func updateDistributorStatus(ctx context.Context, req *pb.UpdateDistributorRequest) error {
	condition := model.Common.GenDefaultConditionById(ctx, bson.ObjectIdHex(req.Id))
	if req.IsEnabled != nil {
		setter := bson.M{
			"updatedAt": time.Now(),
			"isEnabled": req.IsEnabled.Value,
		}
		if !req.IsEnabled.Value {
			setter["status"] = model.STATUS_CLOSED
		}
		updater := bson.M{
			"$set": setter,
		}
		err := model.CDistributor.UpdateByCondition(ctx, condition, updater)
		if err != nil {
			return err
		}
		if req.EnableChild {
			err := enableChildDistributors(ctx, req)
			if err != nil {
				return err
			}
		}
	}
	if req.Status != nil {
		updater := bson.M{
			"$set": bson.M{
				"updatedAt": time.Now(),
				"status":    req.Status.Value,
			},
		}
		err := model.CDistributor.UpdateByCondition(ctx, condition, updater)
		if err != nil {
			return err
		}
	}
	return nil
}

func enableChildDistributors(ctx context.Context, req *pb.UpdateDistributorRequest) error {
	selector := model.Common.GenDefaultConditionById(ctx, bson.ObjectIdHex(req.Id))
	distributor, err := model.CDistributor.GetByCondition(ctx, selector)
	if err != nil {
		return err
	}
	distributors := model.GetChildDistributors(ctx, distributor.DepartmentId, nil)
	if len(distributors) == 0 {
		return nil
	}
	ids := core_util.ToObjectIdArray(core_util.ExtractArrayField("Id", &distributors))
	condition := model.Common.GenDefaultCondition(ctx)
	condition["_id"] = bson.M{"$in": ids}
	setter := bson.M{
		"updatedAt": time.Now(),
		"isEnabled": req.IsEnabled.Value,
		"status":    model.STATUS_CLOSED,
	}
	if req.IsEnabled.Value {
		setter["status"] = model.STATUS_OPEN
	}
	updater := bson.M{
		"$set": setter,
	}
	_, err = model.Common.UpdateAll(ctx, model.C_DISTRIBUTOR, "false", condition, updater)
	return err
}
