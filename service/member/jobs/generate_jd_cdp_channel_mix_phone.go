package jobs

import (
	"context"
	"crypto/md5"
	"fmt"
	"strings"
	"sync"
	"time"

	"mairpc/core/component"
	"mairpc/core/component/jingdong"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_job "mairpc/core/job"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	pb_account "mairpc/proto/account"
	pb_client "mairpc/proto/client"
	pb_request "mairpc/proto/common/request"
	account_model "mairpc/service/account/model"
	"mairpc/service/member/model"
	"mairpc/service/share/constant"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"

	"github.com/spf13/viper"
)

type GenerateJdCdpChannelMixPhoneJob struct {
	core_job.Job
	Options          *GenerateJdCdpChannelMixPhoneCliArg
	jingdongClient   *jingdong.Jingdong
	channel          *pb_account.ChannelDetailResponse
	channelMixPhones chan *model.ChannelMixPhone
}

type GenerateJdCdpChannelMixPhoneCliArg struct {
	core_job.CliArg
	ChannelId               string `json:"channelId,omitempty" valid:"required"`
	PoolSize                int    `json:"poolSize,omitempty" valid:"required"`
	RemindSessionExpiration bool   `json:"remindSessionExpiration,omitempty"`
	// 指定该参数来修复增量 member 数据（向多个 jd:member 渠道上翻）
	FixIncrementalMember bool                                        `json:"fixIncrementalMember,omitempty"`
	Ids                  []string                                    `json:"ids,omitempty"`
	IdGt                 string                                      `json:"idGt,omitempty"`
	CreatedAt            GenerateJdCdpChannelMixPhoneCliArgDateRange `json:"createdAt,omitempty"`
	SubscribedAt         GenerateJdCdpChannelMixPhoneCliArgDateRange `json:"subscribedAt,omitempty"`
}

type GenerateJdCdpChannelMixPhoneCliArgDateRange struct {
	StartAt string `json:"startAt,omitempty"`
	EndAt   string `json:"endAt,omitempty"`
}

func init() {
	job := new(GenerateJdCdpChannelMixPhoneJob)
	job.RegisterToParent(job, RootCmd)
}

func (*GenerateJdCdpChannelMixPhoneJob) JobName() string {
	return "generateJdCdpChannelMixPhone"
}

func (*GenerateJdCdpChannelMixPhoneJob) ShouldIterateAccount() bool {
	return false
}

func (*GenerateJdCdpChannelMixPhoneJob) EnabledMods() []string {
	return []string{}
}

func (job *GenerateJdCdpChannelMixPhoneJob) RunE(ctx context.Context) error {
	job.Options = &GenerateJdCdpChannelMixPhoneCliArg{}
	if err := job.SetCliArgs(job.Options); err != nil {
		return err
	}
	channel, err := model.GetChannelByOrigin(ctx, constant.JD_MEMBER, job.Options.ChannelId)
	if err != nil {
		return err
	}
	if channel == nil {
		return errors.NewNotExistsError("channel")
	}
	job.channel = channel
	if !share_model.CChannel.IsJdCdpWay2(job.channel.IntegrationModes) {
		return fmt.Errorf("Only for jdCdpWay2")
	}
	jingdongClient, err := jingdong.NewJingdong(job.channel.AppId, job.channel.AppSecret, job.channel.Token, job.channel.ChannelId, job.channel.IntegrationModes)
	if err != nil {
		return err
	}
	job.jingdongClient = jingdongClient
	if job.Options.RemindSessionExpiration {
		job.createSessionExpirationReminder(ctx)
	}
	return job.generateJdCdpChannelMixPhone(ctx)
}

func (job *GenerateJdCdpChannelMixPhoneJob) generateJdCdpChannelMixPhone(ctx context.Context) error {
	var member *model.Member
	condition, err := job.genMemberCondition()
	if err != nil {
		return err
	}
	it, err := model.CMember.Iterate(ctx, *condition, []string{"_id"})
	if err != nil {
		return err
	}
	defer it.Close()
	if job.Options.FixIncrementalMember {
		for it.Next(&member) {
			if member.Phone == "" {
				continue
			}
			err := job.fixIncrementalMember(ctx, member)
			if err != nil {
				log.Warn(ctx, "Failed to fix incremental jd cdp member", log.Fields{
					"memberId": member.Id.Hex(),
					"err":      err.Error(),
				})
			}
			continue
		}
		return nil
	}
	pool, err := util.NewGoroutinePoolWithPanicHandler(job.Options.PoolSize)
	if err != nil {
		return err
	}
	defer pool.Release()
	job.channelMixPhones = make(chan *model.ChannelMixPhone, 1000)
	component.GO(ctx, func(ctx context.Context) {
		wg := sync.WaitGroup{}
		for it.Next(&member) {
			if member.Phone == "" {
				continue
			}
			var (
				phone    = member.Phone
				memberId = member.Id
			)
			wg.Add(1)
			pool.Submit(func() {
				defer wg.Done()
				phone, err := extension.Encryption.Decrypt(ctx, phone)
				if err != nil {
					log.Warn(ctx, "Failed to decrypt phone when migrate jd member mix phone", log.Fields{
						"memberId": memberId,
						"phone":    phone,
					})
					return
				}
				if job.isGenerated(ctx, job.channel.ChannelId, phone) {
					return
				}
				resp, err := job.registerToJingdong(ctx, phone)
				if err != nil {
					log.Warn(ctx, "Failed to registerToJingdong when generating jingdong channelMixPhone", log.Fields{
						"memberId": fmt.Sprintf("%+v", memberId),
						"err":      err.Error(),
					})
					return
				}
				if resp.Data.Code != "SUC" || resp.Data.CdpId == "" {
					log.Warn(ctx, "Failed to registerToJingdong when generating jingdong channelMixPhone", log.Fields{
						"memberId": fmt.Sprintf("%+v", memberId),
						"respData": fmt.Sprintf("%+v", resp.Data),
					})
					return
				}
				job.channelMixPhones <- &model.ChannelMixPhone{
					ChannelId: job.channel.ChannelId,
					MixPhone:  resp.Data.CdpId,
					Phone:     phone,
					OpenId:    resp.Data.CrmId,
				}
			})
		}
		wg.Wait()
		close(job.channelMixPhones)
	})
	job.UpsertChannelMixPhones(ctx)
	return nil
}

func (job *GenerateJdCdpChannelMixPhoneJob) isGenerated(ctx context.Context, channelId, phone string) bool {
	condition := model.Common.GenDefaultCondition(ctx)
	condition["channelId"] = channelId
	condition["phone"], _ = model.EncryptStringValue(ctx, phone)
	return model.Common.Exist(ctx, model.C_CHANNEL_MIX_PHONE, condition)
}

func (job *GenerateJdCdpChannelMixPhoneJob) createSessionExpirationReminder(ctx context.Context) {
	expiredAt := time.Now().AddDate(1, 0, 0).Format(core_util.RFC3339Mili)
	reminderReq := &pb_account.UpsertExpirationReminderRequest{
		Title:        "京东 session 过期提醒",
		Content:      fmt.Sprintf("你好！环境：%s，租户 ID：%s，渠道 ID：%s，渠道 ChannelId：%s，session 有效期临近，请提醒续费！", viper.GetString("env"), util.GetAccountId(ctx), job.channel.Id, job.Options.ChannelId),
		AdvancedDays: []uint64{3, 7, 30},
		ExpiredAt:    expiredAt,
		Business:     account_model.REMINDER_BUSINESS_PLATFORM_SESSION,
		BusinessId:   job.Options.ChannelId,
	}
	if err := createSessionExpirationReminder(ctx, reminderReq); err != nil {
		log.Warn(ctx, "Failed to create session expiration reminder for jd channel", log.Fields{
			"channelId": job.Options.ChannelId,
			"error":     err.Error(),
		})
	}
}

func (job *GenerateJdCdpChannelMixPhoneJob) fixIncrementalMember(ctx context.Context, member *model.Member) error {
	_, err := pb_client.GetMemberServiceClient().RegisterMemberWithPhoneToJd(ctx, &pb_request.CustomerEventRequest{
		MemberId: member.Id.Hex(),
	})
	return err
}

func (job *GenerateJdCdpChannelMixPhoneJob) genMemberCondition() (*bson.M, error) {
	condition := bson.M{
		"accountId": util.ToMongoId(job.AccountId),
		"isDeleted": false,
	}
	if job.Options.IdGt != "" {
		condition["_id"] = bson.M{
			"$gt": bson.ObjectIdHex(job.Options.IdGt),
		}
	}
	if len(job.Options.Ids) > 0 {
		condition["_id"] = bson.M{
			"$in": core_util.ToObjectIdArray(job.Options.Ids),
		}
	}
	createdAtCond := bson.M{}
	if job.Options.CreatedAt.StartAt != "" {
		createdAtGte, err := time.Parse(core_util.RFC3339Mili, job.Options.CreatedAt.StartAt)
		if nil != err {
			return nil, err
		}
		createdAtCond["$gte"] = createdAtGte
	}
	if job.Options.CreatedAt.EndAt != "" {
		createdAtLt, err := time.Parse(core_util.RFC3339Mili, job.Options.CreatedAt.EndAt)
		if nil != err {
			return nil, err
		}
		createdAtCond["lt"] = createdAtLt
	}
	if len(createdAtCond) > 0 {
		condition["createdAt"] = createdAtCond
	}
	subscribedAtCond := bson.M{}
	if job.Options.SubscribedAt.StartAt != "" {
		subscribedAtGte, err := time.Parse(core_util.RFC3339Mili, job.Options.SubscribedAt.StartAt)
		if nil != err {
			return nil, err
		}
		subscribedAtCond["$gte"] = subscribedAtGte
	}
	if job.Options.SubscribedAt.EndAt != "" {
		subscribedAtLt, err := time.Parse(core_util.RFC3339Mili, job.Options.SubscribedAt.EndAt)
		if nil != err {
			return nil, err
		}
		subscribedAtCond["lt"] = subscribedAtLt
	}
	if len(subscribedAtCond) > 0 {
		condition["socials.subscribeTime"] = subscribedAtCond
	}
	return &condition, nil
}

func (job *GenerateJdCdpChannelMixPhoneJob) GetUpperMd5(s string) string {
	s = fmt.Sprintf("%x", md5.Sum([]byte(s)))
	return strings.ToUpper(s)
}

func (job *GenerateJdCdpChannelMixPhoneJob) registerToJingdong(ctx context.Context, phone string) (*jingdong.RegisterJdCdpWay2MemberResponse, error) {
	req := &jingdong.RegisterJdCdpWay2MemberRequest{
		CrmId:    jingdong.GenerateCrmId(phone, job.channel.ChannelId),
		MixPhone: jingdong.GenerateMixPhone(phone, job.channel.EncryptionKey, job.channel.ChannelId),
	}
	return job.jingdongClient.Member.RegisterJdCdpWay2Member(ctx, req)
}

func (job *GenerateJdCdpChannelMixPhoneJob) UpsertChannelMixPhones(ctx context.Context) {
	channelMixPhones := []*model.ChannelMixPhone{}
	for channelMixPhone := range job.channelMixPhones {
		channelMixPhones = append(channelMixPhones, channelMixPhone)
		if len(channelMixPhones) >= 500 {
			err := model.CChannelMixPhone.BatchUpsert(ctx, channelMixPhones)
			channelMixPhones = channelMixPhones[0:0]
			if err != nil {
				log.Warn(ctx, "Failed to upsert jd member mix phone", log.Fields{
					"error": err.Error(),
				})
			}
		}
	}
	if len(channelMixPhones) > 0 {
		err := model.CChannelMixPhone.BatchUpsert(ctx, channelMixPhones)
		if err != nil {
			log.Warn(ctx, "Failed to upsert jd member mix phone", log.Fields{
				"error": err.Error(),
			})
		}
	}
}
