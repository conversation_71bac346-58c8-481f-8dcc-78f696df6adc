package jobs

import (
	"context"
	"mairpc/core/component"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/account"
	"mairpc/proto/client"
	"mairpc/proto/common/types"
	pb_member "mairpc/proto/member"
	"mairpc/service/member/model"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"
	"sync"
	"time"

	"github.com/spf13/cast"
	"github.com/spf13/cobra"
)

func init() {
	RootCmd.AddCommand(sendScoreExpireNotification)
}

var now = time.Now()

var sendScoreExpireNotification = &cobra.Command{
	Use: "sendScoreExpireNotification",
	RunE: func(cmd *cobra.Command, args []string) error {
		params := util.GetArgs(args)
		if timeStr := cast.ToString(params["now"]); timeStr != "" {
			now = util.MustTransStrToTime(timeStr).Local()
		}
		component.DryRun = cast.ToBool(params["dryRun"])
		accountIds := cast.ToStringSlice(params["accountIds"])
		pool, err := util.NewGoroutinePoolWithPanicHandler(5)
		if err != nil {
			return err
		}
		defer pool.Release()
		wg := &sync.WaitGroup{}
		util.ExecActivatedAccountsIterative(util.GetContextInJob(args), nil, func(ctx context.Context) error {
			if len(accountIds) > 0 && !util.StrInArray(util.GetAccountId(ctx), &accountIds) {
				return nil
			}
			wg.Add(1)
			err := pool.Submit(func() {
				defer wg.Done()
				sender := GetScoreExpireNotificationSender(ctx)
				if !sender.ShouldSend(ctx) && !component.DryRun {
					return
				}
				cursor := ""
				for {
					members, nextCursor, err := sender.IterateMember(core_util.CtxWithReadSecondaryPreferred(ctx), cursor)
					if err != nil || len(members) == 0 {
						break
					}
					cursor = nextCursor
					for _, member := range members {
						replaceMap, err := sender.GenerateReplaceMap(ctx, member)
						if err != nil || len(replaceMap) == 0 {
							continue
						}
						if component.DryRun {
							log.Warn(ctx, "replace map generated", log.Fields{
								"memberId":   member.Id,
								"score":      member.Score,
								"replaceMap": replaceMap,
							})
							continue
						}
						model.NotifyMember(ctx, &member, nil, constant.MESSAGE_RULE_SCORE_WILL_EXPIRE, replaceMap, "")
					}
				}
			})
			if err != nil {
				wg.Done()
			}
			return nil
		})
		wg.Wait()
		return nil
	},
}

type ScoreExpireNotificationSender interface {
	ShouldSend(ctx context.Context) bool
	IterateMember(ctx context.Context, cursor string) ([]model.Member, string, error)
	GenerateReplaceMap(ctx context.Context, member model.Member) (map[string]string, error)
}

func GetScoreExpireNotificationSender(ctx context.Context) ScoreExpireNotificationSender {
	rule := model.CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	if model.IsVersion2(ctx, rule) {
		return &V2Sender{
			rule: rule,
		}
	}
	return &V1Sender{
		rule: rule,
	}
}

type V1Sender struct {
	rule          *model.ScoreResetRule
	nextResetFrom time.Time
	nextResetTo   time.Time
	from          time.Time
	to            time.Time
	clearAll      bool
}

func GetNotificationSetting(ctx context.Context, business, rule string) (*account.GetNotificationSettingResponse, error) {
	return client.GetAccountServiceClient().GetNotificationSetting(ctx, &account.GetNotificationSettingRequest{
		Business: business,
		Rule:     rule,
	})
}

func (v *V1Sender) SetRule(rule *model.ScoreResetRule) {
	v.rule = rule
}

func (v *V1Sender) ShouldSend(ctx context.Context) bool {
	if !v.rule.NotificationSetting.CanSend(now) || v.rule.ResetType == model.SCORE_RESET_TYPE_NEVER {
		return false
	}
	resp, err := GetNotificationSetting(ctx, model.BUSINESS, constant.MESSAGE_RULE_SCORE_WILL_EXPIRE)
	if err != nil {
		return false
	}
	if !resp.NotificationSetting.Text.Enabled && len(resp.NotificationSetting.TemplateMessage.Data) == 0 && len(resp.NotificationSetting.SubscribeMessage.Data) == 0 {
		return false
	}
	from, to := v.rule.NotificationSetting.GetTimeRange(now)
	switch v.rule.ResetType {
	case model.SCORE_RESET_TYPE_YEAR:
		v.to = util.GetEndTimeOfMonth(from)
		v.from = v.to.AddDate(-1, 0, 0)
		v.nextResetTo = util.GetEndTimeOfMonth(now)
		v.nextResetFrom = v.nextResetTo.AddDate(-1, 0, 0)
	case model.SCORE_RESET_TYPE_END_OF_MONTH:
		v.to = util.GetEndTimeOfMonth(from)
		v.from = v.to.AddDate(0, -v.rule.ResetDate.Month, 0)
		v.nextResetTo = util.GetEndTimeOfMonth(now)
		v.nextResetFrom = v.nextResetTo.AddDate(0, -v.rule.ResetDate.Month, 0)
	case model.SCORE_RESET_TYPE_FIXED_DAY:
		v.to = time.Date(now.Year(), time.Month(v.rule.ResetDate.Month), v.rule.ResetDate.Day, 23, 59, 59, 0, time.Local)
		if now.After(v.to) {
			v.to = v.to.AddDate(1, 0, 0)
		}
		v.clearAll = true
	case model.SCORE_RESET_TYPE_END_OF_YEAR:
		v.to = util.GetEndTimeOfYear(from)
		v.from = v.to.AddDate(int(-v.rule.ResetInterval), 0, 0)
	}
	// 如果按照通知设置将来 N 天内不包含清零时刻，那么返回 false，减少不必要的计算
	if !util.TimeInPeriod(v.to, from, to) {
		return false
	}
	// 小月份的下次过期时间和当前时间 31 天后的可能是同一天
	if (v.nextResetTo.Day() == v.to.Day() && v.nextResetTo.Month() == v.to.Month()) || v.rule.NotificationSetting.Type != model.SCORE_RESET_NOTIFICATION_TYPE_DAILY {
		v.nextResetTo = time.Time{}
		v.nextResetFrom = time.Time{}
	}
	return true
}

func (v *V1Sender) IterateMember(ctx context.Context, cursor string) ([]model.Member, string, error) {
	resp, err := client.GetMemberServiceClient().SearchMemberIds(ctx, &pb_member.SearchMemberRequest{
		UseScroll:  true,
		ScrollSize: 100,
		ScrollId:   cursor,
		Score: &types.IntegerRange{
			Start: 1,
			Type:  types.RangeType_CLOSE_INFINITE,
		},
	})
	if err != nil {
		return nil, "", err
	}
	if len(resp.MemberIds) == 0 {
		return nil, "", nil
	}
	return model.CMember.GetByIds(ctx, util.GetAccountIdAsObjectId(ctx), util.ToMongoIds(resp.MemberIds)), resp.ScrollId, nil

}

func (v *V1Sender) GenerateReplaceMap(ctx context.Context, member model.Member) (map[string]string, error) {
	var (
		expiredScore                      int64
		expiredScoreAtNextResetTime       int64
		needCalculateScoreAtNextResetTime = !v.nextResetFrom.IsZero() && !v.nextResetTo.IsZero()
		totalScore                        int64
		totalScoreAtNextResetTime         int64
		err                               error
	)
	if !v.clearAll {
		totalScore, err = GetValidScoreByHistory(ctx, member.Id, v.from, v.to)
		if err != nil {
			return nil, err
		}
		if needCalculateScoreAtNextResetTime {
			totalScoreAtNextResetTime, err = GetValidScoreByHistory(ctx, member.Id, v.nextResetFrom, v.nextResetTo)
			if err != nil {
				return nil, err
			}
		}
	}
	if totalScore < member.Score {
		expiredScore = member.Score - totalScore
	}
	if totalScoreAtNextResetTime < member.Score && needCalculateScoreAtNextResetTime {
		expiredScoreAtNextResetTime = member.Score - totalScoreAtNextResetTime
	}
	if expiredScore >= expiredScoreAtNextResetTime && needCalculateScoreAtNextResetTime {
		expiredScore -= expiredScoreAtNextResetTime
	}
	if component.DryRun {
		log.Warn(ctx, "replace map generating", log.Fields{
			"memberId":                  member.Id,
			"score":                     member.Score,
			"totalScore":                totalScore,
			"totalScoreAtNextResetTime": totalScoreAtNextResetTime,
			"from":                      v.from,
			"to":                        v.to,
			"nextResetFrom":             v.nextResetFrom,
			"nextResetTo":               v.nextResetTo,
			"expiredScore":              expiredScore,
		})
	}
	// 没有过期积分就不发
	if expiredScore == 0 || member.Score == 0 {
		return nil, nil
	}
	return map[string]string{
		model.PLACEHOLDER_CURRENTSCORE:  cast.ToString(member.Score),
		model.PLACEHOLDER_EXPIRINGSCORE: cast.ToString(expiredScore),
		model.PLACEHOLDER_EXPIRETIME:    v.to.Format("2006-01-02 15:04:05"),
	}, nil
}

type V2Sender struct {
	rule                    *model.ScoreResetRule
	from                    time.Time
	to                      time.Time
	memberRemainingScoreMap map[bson.ObjectId]int64
}

func (v *V2Sender) ShouldSend(ctx context.Context) bool {
	if !v.rule.NotificationSetting.CanSend(now) || v.rule.ResetType == model.SCORE_RESET_TYPE_NEVER {
		return false
	}
	resp, err := GetNotificationSetting(ctx, model.BUSINESS, constant.MESSAGE_RULE_SCORE_WILL_EXPIRE)
	if err != nil {
		return false
	}
	if !resp.NotificationSetting.Text.Enabled && len(resp.NotificationSetting.TemplateMessage.Data) == 0 && len(resp.NotificationSetting.SubscribeMessage.Data) == 0 {
		return false
	}
	v.from, v.to = v.rule.NotificationSetting.GetTimeRange(now)
	return true
}

func (v *V2Sender) IterateMember(ctx context.Context, cursor string) ([]model.Member, string, error) {
	lastMemberId := bson.NilObjectId
	if bson.IsObjectIdHex(cursor) {
		lastMemberId = bson.ObjectIdHex(cursor)
	}
	histories, err := model.CScoreHistory.GetRangeHasExpiredScoreHistoriesByMemberIdCursor(ctx, v.from, v.to.Add(time.Second), lastMemberId, 300)
	if err != nil || len(histories) == 0 {
		return nil, "", err
	}
	lastMemberId = histories[len(histories)-1].MemberId
	var memberIds []bson.ObjectId
	for _, history := range histories {
		memberIds = append(memberIds, history.MemberId)
	}
	err = v.sumRemainingScore(ctx, memberIds)
	return model.CMember.GetAllByIds(ctx, util.MongoIdsToStrs(memberIds)), lastMemberId.Hex(), err
}

func (v *V2Sender) GenerateReplaceMap(ctx context.Context, member model.Member) (map[string]string, error) {
	if expiredScore := v.memberRemainingScoreMap[member.Id]; expiredScore > 0 && member.Score > 0 {
		return map[string]string{
			model.PLACEHOLDER_CURRENTSCORE:  cast.ToString(member.Score),
			model.PLACEHOLDER_EXPIRINGSCORE: cast.ToString(expiredScore),
			model.PLACEHOLDER_EXPIRETIME: func() string {
				// 这两种类型算的是一个月内的过期积分，格式化只到月
				if util.StrInArray(v.rule.ResetType, &[]string{
					model.SCORE_RESET_TYPE_BY_MONTH,
					model.SCORE_RESET_TYPE_WHOLE_YEAR,
				}) {
					return v.to.Format("2006-01")
				}
				return v.to.Format("2006-01-02 15:04:05")
			}(),
		}, nil
	}
	return nil, nil
}

func (v *V2Sender) sumRemainingScore(ctx context.Context, memberIds []bson.ObjectId) error {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"accountId": util.GetAccountIdAsObjectId(ctx),
				"memberId": bson.M{
					"$in": memberIds,
				},
				"changeType": model.SCORE_HISTORY_CHANGE_TYPE_INCREASE,
				"expireAt": bson.M{
					"$lt":  v.to,
					"$gte": v.from,
				},
				"remainingScore": bson.M{"$gt": 0},
			},
		},
		{
			"$group": bson.M{
				"_id": "$memberId",
				"total": bson.M{
					"$sum": "$remainingScore",
				},
			},
		},
	}
	var result []bson.M
	err := extension.DBRepository.Aggregate(ctx, model.C_SCORE_HISTORY, pipeline, false, &result)
	if err != nil {
		return err
	}
	v.memberRemainingScoreMap = make(map[bson.ObjectId]int64, len(result))
	for _, m := range result {
		v.memberRemainingScoreMap[core_util.ToObjectId(m["_id"])] = cast.ToInt64(m["total"])
	}
	return nil
}

func SetJobExecTime(t time.Time) {
	now = t
}
