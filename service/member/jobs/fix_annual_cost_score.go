package jobs

import (
	"context"
	"github.com/spf13/cast"
	"github.com/spf13/cobra"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/service/member/model"
	"mairpc/service/share/util"
	"sync"
	"time"
)

func init() {
	RootCmd.AddCommand(fixAnnualCostScore)
}

var fixAnnualCostScore = &cobra.Command{
	Use: "fixAnnualCostScore",
	RunE: func(cmd *cobra.Command, args []string) error {
		accountIds := cast.ToStringSlice(util.GetArgs(args)["accountIds"])
		pool, err := util.NewGoroutinePoolWithPanicHandler(3)
		if err != nil {
			return err
		}
		defer pool.Release()
		wg := sync.WaitGroup{}
		util.ExecActivatedAccountsIterative(util.GetContextInJob(args), nil, func(ctx context.Context) error {
			if len(accountIds) > 0 && !util.StrInArray(util.GetAccountId(ctx), &accountIds) {
				return nil
			}
			wg.Add(1)
			err := pool.Submit(func() {
				defer wg.Done()
				fixAnnualCostScoreForOneAccount(ctx)
			})
			if err != nil {
				wg.Done()
			}
			return nil
		})
		wg.Wait()
		return nil
	},
}

func fixAnnualCostScoreForOneAccount(ctx context.Context) {
	rule := model.CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	if rule == nil {
		return
	}
	from, to := rule.GetAnnualRange(ctx)
	if from.IsZero() || to.IsZero() {
		return
	}
	pool, err := util.NewGoroutinePoolWithPanicHandler(5)
	if err != nil {
		return
	}
	defer pool.Release()
	wg := sync.WaitGroup{}
	cursor := ""
	for {
		memberIds, nextCursor, err := searchInvalidMemberIds(ctx, cursor)
		if err != nil || len(memberIds) == 0 {
			break
		}
		cursor = nextCursor
		wg.Add(1)
		pool.Submit(func() {
			defer wg.Done()
			var (
				docs       []interface{}
				scoresDocs []interface{}
			)
			for _, memberId := range memberIds {
				total, used, innerErr := getTotalScoreAndUsedScoreByPeriodOfTime(ctx, bson.ObjectIdHex(memberId), from, to)
				if innerErr != nil || total < 0 || used < 0 {
					continue
				}
				docs = append(docs, bson.M{
					"_id": bson.ObjectIdHex(memberId),
				}, bson.M{
					"$set": bson.M{
						"annualAccumulatedScore": total,
						"annualCostScore":        used,
					},
				})
				scoresDocs = append(scoresDocs, bson.M{
					"accountId": util.GetAccountIdAsObjectId(ctx),
					"memberId":  bson.ObjectIdHex(memberId),
					"period":    cast.ToString(time.Now().Year()),
				}, bson.M{
					"$set": bson.M{
						"accumulatedScore": total,
						"costScore":        used,
					},
				})
			}
			if len(docs) > 0 {
				extension.DBRepository.BatchUpdateUnordered(ctx, model.C_MEMBER, docs...)
			}
			if len(scoresDocs) > 0 {
				extension.DBRepository.BatchUpdateUnordered(ctx, model.C_MEMBER_ANNUAL_ACCUMULATED_SCORE, scoresDocs...)
			}
		})
		// 每一批间隔 5 秒
		time.Sleep(time.Second * 5)
	}
	wg.Wait()
}

func searchInvalidMemberIds(ctx context.Context, cursor string) ([]string, string, error) {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"period":    cast.ToString(time.Now().Year()),
		"$or": []bson.M{
			{"accumulatedScore": bson.M{"$lt": 0}},
			{"costScore": bson.M{"$lt": 0}},
		},
	}
	if bson.IsObjectIdHex(cursor) {
		condition["memberId"] = bson.M{"$gt": bson.ObjectIdHex(cursor)}
	}
	var scores []model.MemberAnnualAccumulatedScore
	err := extension.DBRepository.FindAll(ctx, model.C_MEMBER_ANNUAL_ACCUMULATED_SCORE, condition, []string{"memberId"}, 100, &scores)
	if err != nil {
		return nil, "", err
	}
	memberIds := core_util.ExtractArrayStringField("MemberId", scores)
	return memberIds, memberIds[len(memberIds)-1], nil
}
