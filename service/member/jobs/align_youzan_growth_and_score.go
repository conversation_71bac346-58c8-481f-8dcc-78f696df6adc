package jobs

import (
	"context"
	"encoding/csv"
	"fmt"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/account"
	"mairpc/proto/client"
	"mairpc/proto/common/origin"
	"mairpc/proto/common/types"
	pb_member "mairpc/proto/member"
	"mairpc/service/member/model"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"
	"os"
	"time"

	"github.com/spf13/cobra"
)

const (
	ALIGN_YOUZAN_BUSINESS_ID = "align_youzan_growth_and_score"
)

func init() {
	RootCmd.AddCommand(alignYouzanGrowthAndScore)
}

type AlignYouzanGrowthAndScoreRequest struct {
	MemberIds   []string `json:"memberIds" valid:"-"`
	YzChannelId string   `json:"yzChannelId" valid:"-"`
	AlignModes  []string `json:"alignModes" valid:"required,in(growth|score)"`
	AlignFrom   string   `json:"alignFrom" valid:"in(portal|youzan)"`
	UnSafeAlign bool     `json:"unSafeAlign"`
	FileUrl     string   `json:"fileUrl" valid:"-"` // 通过文件，只支持 openId、memberId、phone 为表头的 csv 文件
	AlignAll    bool     `json:"alignAll"`
}

var alignYouzanGrowthAndScore = &cobra.Command{
	Use: "alignYouzanGrowthAndScore",
	RunE: func(cmd *cobra.Command, args []string) error {
		ctx := util.GetContextInJob(args)
		jobOptions := util.GetArgs(args)
		alignReq := AlignYouzanGrowthAndScoreRequest{}
		err := core_util.CopyByJson(jobOptions, &alignReq)
		if err != nil {
			return err
		}
		if err := validators.ValidateRequest(alignReq); err != nil {
			return err
		}
		var (
			yzChannel *account.ChannelDetailResponse
		)
		if alignReq.YzChannelId != "" {
			yzChannel, err = getYouzanChannel(ctx, alignReq.YzChannelId)
			if err != nil {
				return err
			}
		} else {
			yzChannel, err = getYouzanChannels(ctx)
			if err != nil {
				return err
			}
		}
		notExistsMemberIds := []string{}
		if len(alignReq.MemberIds) > 0 {
			notExistsMemberIds = alignReq.alignByMemberIds(ctx, yzChannel, alignReq.MemberIds)
		}
		if alignReq.FileUrl != "" {
			notExistsIds, err := alignReq.alignByFile(ctx, yzChannel)
			if err != nil {
				return err
			}
			notExistsMemberIds = append(notExistsMemberIds, notExistsIds...)
		}
		if alignReq.AlignAll {
			alignReq.alignAll(ctx, yzChannel)
		}

		if len(notExistsMemberIds) > 0 {
			log.Warn(ctx, "Members not found", log.Fields{
				"ids": notExistsMemberIds,
			})
		}
		return nil
	},
}

func (req AlignYouzanGrowthAndScoreRequest) alignByFile(ctx context.Context, yzChannel *account.ChannelDetailResponse) ([]string, error) {
	filePath := fmt.Sprintf("./%s", bson.NewObjectId().Hex())
	err := util.DownloadTo(filePath, req.FileUrl)
	if err != nil {
		return nil, err
	}
	f, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer f.Close()
	reader := csv.NewReader(f)
	reader.Read() // 丢弃 header
	notExistsMemberIds := []string{}
	for {
		memberIds := getBatchMemberIds(reader, 100)
		if len(memberIds) == 0 {
			break
		}
		log.Warn(ctx, "Align by file", log.Fields{
			"memberIds": memberIds,
		})
		notExistsMemberIds = append(notExistsMemberIds, req.alignByMemberIds(ctx, yzChannel, memberIds)...)
	}
	return notExistsMemberIds, nil
}

func getBatchMemberIds(reader *csv.Reader, batchSize int) []string {
	result := []string{}
	readCount := 0
	for {
		line, err := reader.Read()
		if err != nil {
			break
		}
		if len(line) == 0 {
			break
		}
		for _, item := range line {
			if item == "" {
				continue
			}
			result = append(result, item)
			readCount++
			break
		}
		if readCount >= batchSize {
			break
		}
	}
	return result
}

func (req AlignYouzanGrowthAndScoreRequest) alignByMemberIds(ctx context.Context, yzChannel *account.ChannelDetailResponse, memberIds []string) []string {
	if req.AlignFrom == "" {
		req.AlignFrom = constant.PORTAL
	}
	notExistsMemberIds := []string{}
	for _, memberId := range memberIds {
		member := model.CMember.GetByIdCombination(ctx, util.GetAccountId(ctx), memberId)
		if member == nil {
			notExistsMemberIds = append(notExistsMemberIds, memberId)
			continue
		}
		syncStatus := model.MemberScoreSyncStatus{
			AccountId:  member.AccountId,
			MemberId:   member.Id,
			ChannelId:  yzChannel.ChannelId,
			BusinessId: fmt.Sprintf("%s:%s", ALIGN_YOUZAN_BUSINESS_ID, bson.NewObjectId().Hex()),
			From:       req.AlignFrom,
			To: func() string {
				if req.AlignFrom == constant.PORTAL {
					return constant.YOUZAN
				} else {
					return constant.PORTAL
				}
			}(),
			Type:        model.MEMBER_SCORE_SYNC_TYPE_ALIGN,
			Scene:       model.MEMBER_SCORE_SYNC_SCENE_ALIGN,
			Description: model.MEMBER_SCORE_GROWTH_SYNC_DESC_ALIGN,
			Status:      model.MEMBER_SCORE_SYNC_STATUS_UNSYNCED,
			AlignModes:  req.AlignModes,
			SafeAlign:   !req.UnSafeAlign,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
		err := syncStatus.Insert(ctx)
		if err != nil {
			log.Warn(ctx, "Insert member score sync status failed", log.Fields{
				"syncStatus": syncStatus,
				"memberId":   memberId,
				"errMsg":     err.Error(),
			})
		}
	}
	return notExistsMemberIds
}

func (req AlignYouzanGrowthAndScoreRequest) alignAll(ctx context.Context, yzChannel *account.ChannelDetailResponse) {
	var (
		scrollId string
	)
	for {
		resp, err := client.GetMemberServiceClient().SearchMemberIds(ctx, &pb_member.SearchMemberRequest{
			UseScroll:  true,
			ScrollId:   scrollId,
			ScrollSize: 100,
			Socials: []*origin.SocialSearchInfo{
				{
					Channel: &types.StringValue{
						Value: yzChannel.ChannelId,
					},
					Origin: &types.StringValue{
						Value: constant.YOUZAN,
					},
				},
			},
		})
		if err != nil {
			log.Warn(ctx, "failed to search memberIds", log.Fields{
				"err": err.Error(),
			})
			break
		}
		if len(resp.MemberIds) == 0 || resp.ScrollId == "" {
			break
		}
		scrollId = resp.ScrollId
		req.alignByMemberIds(ctx, yzChannel, resp.MemberIds)
	}
}
