package jobs

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"reflect"
	"strconv"
	"strings"
	"time"

	"mairpc/core/client"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/proto/account"
	"mairpc/proto/member"
	"mairpc/service/member/model"
	"mairpc/service/member/service"
	"mairpc/service/share/constant"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"

	"github.com/spf13/cast"
	"github.com/spf13/cobra"
)

var (
	exportScoreHistoryHeader = "客户编号,会员编号,会员姓名,手机号,积分变动,类型,来源/用途,渠道,变动时间\n"

	briefMapping = map[string]string{
		model.ADMIN_ISSUE_SCORE:                "系统奖励",
		model.RULE_ASSIGNER:                    "会员奖励",
		model.EXCHANGE_PROMOTION_CODE_ASSIGNER: "促销码兑换",
		model.SHAKE_SOCRE:                      "活动奖励",
		model.SHARE_SOCRE:                      "活动奖励",
		model.REGISTER_SCORE:                   "活动奖励",
		model.INVITEE_REWARD_SCORE:             "活动奖励",
		model.INVITER_REWARD_SCORE:             "活动奖励",
		model.FISSION_ACTIVITY_REWARD:          "活动奖励",
		model.PRODUCT_REWARD_SCORE:             "产品奖励",
		model.REFUND_ROLLBACK_SCORE:            "积分抵扣返还",
		model.REFUND_REDUCE_SCORE:              "退货扣除",
		model.ADMIN_DEDUCT_SCORE:               "系统扣除",
		model.EXCHANGE_GOODS_ASSIGNER:          "积分兑换",
		model.REWARD_SCORE:                     "积分奖励",
		model.ACTIVITY_CONSUMPTION_SCORE:       "活动消耗",
		model.ASSIGNER_AUTO_ZEROED:             "积分清零",
		model.ASSIGNER_EXPIRED_SCORE:           "积分过期",
		model.PURCHASE_DEDUCT_SCORE:            "积分抵扣",
		model.SYNC_FROM_DOUYIN_HISTORY_MEMBER:  "抖店历史积分同步",
	}

	originNameMapping = map[string]string{
		constant.YOUZAN: "有赞",
		constant.TAOBAO: "天猫",
		constant.DOUYIN: "抖店",
		constant.JD:     "京东",
		constant.WESHOP: "微信小店",
	}
)

func init() {
	RootCmd.AddCommand(exportScoreHistory)
}

var exportScoreHistory = &cobra.Command{
	Use: "exportScoreHistory",
	RunE: func(cmd *cobra.Command, args []string) error {
		// 初始化 job
		jobOptions := util.GetArgs(args)
		ctx := util.GetContextInJob(args)
		ctx = core_util.CtxWithReadSecondaryPreferred(ctx)
		jobId := core_util.GetSreadminJobName()
		err := job_util.BeginToExport(ctx, jobId)
		if err != nil {
			return err
		}

		// 获取遍历积分明细的指针
		req := &member.ScoreHistoryListRequest{}
		byteData, err := json.Marshal(jobOptions["request"])
		if err != nil {
			return err
		}
		err = json.Unmarshal(byteData, req)
		if err != nil {
			return err
		}
		condition, err := service.GenListScoreHistoriesSelector(ctx, service.MemberService{}, req)
		if err != nil {
			return err
		}
		var sorter []string
		if req.ListCondition != nil && len(req.ListCondition.OrderBy) > 0 {
			sorter = util.NormalizeOrderBy(req.ListCondition.OrderBy)
		} else {
			sorter = []string{"-createdAt"}
		}
		it, err := extension.DBRepository.Iterate(ctx, model.C_SCORE_HISTORY, condition, sorter)
		if err != nil {
			return err
		}
		defer it.Close()

		// 导出文件
		job, _ := job_util.GetJobStatus(ctx, jobId)
		desenitize := needDesensitize(ctx, job.CreatorId)
		url, err := job_util.ExportFile(
			ctx,
			job.Name,
			func(f *os.File) error {
				// 写入表格标题
				f.WriteString(exportScoreHistoryHeader)

				cachedData := []desenitizeMemberData{}
				scoreHistory := model.ScoreHistory{}
				writeCounter := 0
				for it.Next(&scoreHistory) {
					data := genExportHistory(scoreHistory)
					cachedData = append(cachedData, data)

					writeCounter = writeCounter + 1
					if writeCounter%100 == 0 {
						printCachedHistory(ctx, f, cachedData, desenitize)
						cachedData = []desenitizeMemberData{}
					}
				}
				printCachedHistory(ctx, f, cachedData, desenitize)
				return nil
			},
		)
		if err != nil {
			return err
		}

		// 记录导出结果
		err = job_util.ExportSucceed(ctx, jobId, url, job.Name)
		if err != nil {
			return err
		}

		return nil
	},
}

func formatScoreHistoryChange(inc int64) string {
	if inc < 0 {
		return strconv.FormatInt(inc, 10)
	}

	return "+" + strconv.FormatInt(inc, 10)
}

type desenitizeMemberData interface {
	// Line 需要格式化并返回导出信息中每一行信息
	Line() string
	// GetMemberId 需要返回每一条数据对应 member 的 _id
	GetMemberId() bson.ObjectId
	// SetName 需要允许往数据中设定 member 的名称
	SetName(name string)
	// SetPhone 需要允许往数据中设定 member 的手机号
	SetPhone(phone string)
	// SetExtra 允许往数据中设置 extra 信息，extra 为 map[string]interface{} 类型，SetExtra 覆盖已有 extra 中相同的 key 对应的 value
	SetExtra(extra map[string]interface{})
}

func genExportHistory(scoreHistory model.ScoreHistory) desenitizeMemberData {
	history := &exportHistory{
		MemberId:  scoreHistory.MemberId,
		Change:    formatScoreHistoryChange(scoreHistory.Increment),
		ChangedAt: formatTime(scoreHistory.CreatedAt),
		reason:    scoreHistory.Description,
		Extra: map[string]interface{}{
			"channelName": scoreHistory.Channel.Name,
			"originName":  constant.OriginNameMap[scoreHistory.Channel.Origin],
		},
	}
	history.Type = formatScoreHistoryBrief(scoreHistory.Brief)
	return history
}

func formatScoreHistoryBrief(brief string) string {
	if briefMapping[brief] != "" {
		return briefMapping[brief]
	}
	if strings.HasPrefix(brief, "sync_from_") {
		return fmt.Sprintf("%s积分同步", originNameMapping[strings.TrimPrefix(brief, "sync_from_")])
	}
	return ""
}

type exportHistory struct {
	MemberId  bson.ObjectId
	Name      string
	Phone     string
	Extra     map[string]interface{}
	Change    string
	Type      string
	reason    string
	ChangedAt string
}

func (e exportHistory) GetMemberId() bson.ObjectId {
	return e.MemberId
}

func (e *exportHistory) SetName(name string) {
	e.Name = name
}

func (e *exportHistory) SetPhone(phone string) {
	e.Phone = phone
}

func (e *exportHistory) SetExtra(extra map[string]interface{}) {
	if e.Extra == nil {
		e.Extra = make(map[string]interface{})
	}
	for key, value := range extra {
		e.Extra[key] = value
	}
}

func (e exportHistory) Line() string {
	return fmt.Sprintf(
		// 客户编号,会员编号,会员姓名,手机号,积分变动,类型,来源/用途,渠道,变动时间
		`%s,%s,%s,	%s,	%s,%s,"%s",%s,%s
`,
		e.MemberId.Hex(),
		cast.ToString(e.Extra["cardNumber"]),
		getExportName(e.Name),
		e.Phone,
		e.Change,
		e.Type,
		e.reason,
		func() string {
			channelName := cast.ToString(e.Extra["channelName"])
			originName := cast.ToString(e.Extra["originName"])
			if originName != "" {
				return fmt.Sprintf("%s（%s）", channelName, originName)
			}
			return channelName
		}(),
		e.ChangedAt,
	)
}

func getExportName(name string) string {
	if strings.Contains(name, ",") {
		return fmt.Sprintf(`"%s"`, removeEscapeCharacter(name))
	}
	return removeEscapeCharacter(name)
}

func formatTime(t time.Time) string {
	if t.IsZero() {
		return ""
	}

	return t.Local().Format("2006-01-02 15:04:05")
}

func desenitizeNameAndPhone(ctx context.Context, data []desenitizeMemberData, desenitize bool) {
	if len(data) == 0 {
		return
	}

	if reflect.ValueOf(data[0]).Kind() != reflect.Ptr {
		// 如果传进来的值不是指针，那么这个方法就无法正常工作。
		// 尽早 panic 以免出岔子。
		panic("desenitizeNameAndPhone data should be pointer slice")
	}

	memberIds := []bson.ObjectId{}
	for _, item := range data {
		memberIds = append(memberIds, item.GetMemberId())
	}
	members := model.CMember.GetAllByIds(ctx, util.MongoIdsToStrs(memberIds))
	memberMap := map[bson.ObjectId]model.Member{}
	for _, m := range members {
		memberMap[m.Id] = m
	}

	for _, item := range data {
		member := memberMap[item.GetMemberId()]
		item.SetExtra(map[string]interface{}{
			"cardNumber": member.CardNumber,
		})
		item.SetName(member.GetName(desenitize))
		item.SetPhone(member.GetPhone(desenitize))
	}
}

func printCachedHistory(ctx context.Context, f *os.File, data []desenitizeMemberData, desenitize bool) {
	if len(data) == 0 {
		return
	}

	if reflect.ValueOf(data[0]).Kind() != reflect.Ptr {
		// 如果传进来的值不是指针，那么这个方法就无法正常工作。
		// 尽早 panic 以免出岔子。
		panic("printCachedHistory data should be pointer slice")
	}

	desenitizeNameAndPhone(ctx, data, desenitize)

	for _, item := range data {
		f.WriteString(item.Line())
	}
	f.Sync()
}

func needDesensitize(ctx context.Context, userId string) bool {
	resp, err := client.Run(
		"AccountService.GetSensitiveOperation",
		ctx,
		&account.GetSensitiveOperationRequest{
			Name: "view_sensitive_info",
		},
	)

	if err != nil {
		return false
	}

	data := resp.(*account.SensitiveOperation)
	if !data.IsActivated {
		return false
	}

	if util.StrInArray(userId, &data.Users) {
		return false
	}

	return true
}

func removeEscapeCharacter(str string) string {
	str = strings.ReplaceAll(str, "\n", "")
	str = strings.ReplaceAll(str, "\r", "")
	return strings.Trim(str, `\"`)
}
