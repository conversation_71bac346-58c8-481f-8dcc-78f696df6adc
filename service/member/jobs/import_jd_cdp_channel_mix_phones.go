package jobs

import (
	"context"
	"fmt"
	"os"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	"mairpc/core/component"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_job "mairpc/core/job"
	"mairpc/core/log"
	"mairpc/core/util"
	core_util "mairpc/core/util"
	pb_member "mairpc/proto/member"
	"mairpc/service/member/model"
	member_service "mairpc/service/member/service"
	job_util "mairpc/service/share/jobs"
	share_util "mairpc/service/share/util"

	"github.com/panjf2000/ants/v2"
	"golang.org/x/time/rate"
)

func init() {
	job := new(ImportJdCdpChannelMixPhonesJob)
	job.RegisterToParent(job, RootCmd)
}

type ImportJdCdpChannelMixPhonesJob struct {
	core_job.ImportJob
	Options       *ImportJdCdpChannelMixPhonesCliArg
	tableReader   share_util.TableReader
	failedHeaders []string
	rawRows       chan []string
	failedRows    chan []string
	pool          *ants.Pool
	memberService *member_service.MemberService
	mergeLimiter  *rate.Limiter
}

type ImportJdCdpChannelMixPhonesCliArg struct {
	core_job.ImportCliArg
	ChannelId      string   `json:"channelId" valid:"required"`
	Headers        []string `json:"headers" valid:"required"`
	FailedFileName string   `json:"failedFileName" valid:"required"`
	PoolSize       int      `json:"poolSize,omitempty"`
	ChanSize       int      `json:"chanSize,omitempty"`
	MergeRateLimit int      `json:"mergeRateLimit,omitempty"` // 单位：次/秒
}

func (*ImportJdCdpChannelMixPhonesJob) JobName() string {
	return "importJdCdpChannelMixPhones"
}

func (job *ImportJdCdpChannelMixPhonesJob) FailedHeadersToExport(ctx context.Context) []string {
	return job.failedHeaders
}

func (job *ImportJdCdpChannelMixPhonesJob) RunE(ctx context.Context) (failedUrl string, err error) {
	job.Options = &ImportJdCdpChannelMixPhonesCliArg{}
	err = job.SetCliArgs(job.Options)
	if err != nil {
		return
	}
	err = job.initJob(ctx)
	if err != nil {
		return
	}
	defer job.pool.Release()
	job.readRawRows(ctx)
	job.handleRawRows(ctx)
	failedUrl, err = job.writeFailedFile(ctx)
	log.Warn(ctx, "Result of importJdCdpChannelMixPhones", log.Fields{
		"failedUrl":    failedUrl,
		"succeedCount": job.GetImportSucceedCount(),
		"failedCount":  job.GetImportFailedCount(),
	})
	return
}

func (job *ImportJdCdpChannelMixPhonesJob) initJob(ctx context.Context) (err error) {
	job.tableReader, err = share_util.GetTableReader(ctx, job.Options.FileUrl)
	if err != nil {
		return
	}
	panicCtx := core_util.DuplicateContext(ctx)
	poolSize := 10
	if job.Options.PoolSize != 0 {
		poolSize = job.Options.PoolSize
	}
	job.pool, err = ants.NewPool(poolSize, ants.WithPanicHandler(func(i interface{}) {
		stack := make([]byte, log.MaxStackSize)
		stack = stack[:runtime.Stack(stack, false)]
		log.ErrorTrace(panicCtx, "Panic when importing jd cdp channelMixPhones", log.Fields{
			"error": fmt.Sprintf("%+v", i),
		}, stack)
	}))
	if err != nil {
		return
	}
	chanSize := 10000
	if job.Options.ChanSize != 0 {
		chanSize = job.Options.ChanSize
	}
	job.rawRows = make(chan []string, chanSize)
	job.failedRows = make(chan []string, chanSize)
	job.failedHeaders = job.tableReader.GetRow(0)
	job.failedHeaders = append(job.failedHeaders, "导入结果")
	job.memberService = &member_service.MemberService{}
	if job.Options.MergeRateLimit > 0 {
		rateLimit := rate.Limit(job.Options.MergeRateLimit)
		job.mergeLimiter = rate.NewLimiter(rateLimit, 10)
	}
	return
}

func (job *ImportJdCdpChannelMixPhonesJob) readRawRows(ctx context.Context) {
	component.GO(ctx, func(ctx context.Context) {
		for i := 1; i < job.tableReader.RealMaxRow(); i++ {
			row := job.getRow(i)
			if len(row) == 0 {
				continue
			}
			job.rawRows <- row
		}
		close(job.rawRows)
	})
}

func (job *ImportJdCdpChannelMixPhonesJob) getRow(num int) []string {
	row := job.tableReader.GetRow(num)
	for idx, val := range row {
		if strings.Contains(val, "E+") {
			if newVal, err := strconv.ParseFloat(val, 64); err == nil {
				row[idx] = fmt.Sprintf("%.0f", newVal)
			}
		}
	}
	return row
}

func (job *ImportJdCdpChannelMixPhonesJob) handleRawRows(ctx context.Context) {
	component.GO(ctx, func(ctx context.Context) {
		wg := &sync.WaitGroup{}
		for {
			row, ok := <-job.rawRows
			if !ok {
				break
			}
			wg.Add(1)
			job.pool.Submit(func() {
				defer wg.Done()
				job.handleRawRow(ctx, row)
			})
		}
		wg.Wait()
		close(job.failedRows)
	})
}

func (job *ImportJdCdpChannelMixPhonesJob) handleRawRow(ctx context.Context, row []string) {
	memberId := job.getRowValueByKey(row, "memberId")
	cdpId := job.getRowValueByKey(row, "cdpId")
	crmId := job.getRowValueByKey(row, "crmId")
	var mixPhone string
	if memberId != "" {
		mixPhone = memberId
	} else {
		mixPhone = cdpId
	}
	if cdpId == "" || crmId == "" {
		job.AddFailedCountAtomically(1)
		job.failedRows <- append(row, "导入失败，cdpId 或 crmId 缺失")
		return
	}
	if mixPhone == "" {
		job.AddFailedCountAtomically(1)
		job.failedRows <- append(row, "导入失败，memberId 缺失")
		return
	}
	channelMixPhone, err := job.memberService.GetChannelMixPhone(ctx, &pb_member.GetChannelMixPhoneRequest{
		ChannelId: job.Options.ChannelId,
		MixPhone:  mixPhone,
	})
	if err != nil {
		job.AddFailedCountAtomically(1)
		job.failedRows <- append(row, fmt.Sprintf("查询 channelMixPhone 失败：%s", err.Error()))
		return
	}
	if memberId == "" && channelMixPhone.OpenId != "" && channelMixPhone.OpenId == crmId {
		job.AddSucceedCountAtomically(1)
		return
	}
	if memberId != "" || channelMixPhone.OpenId == "" || channelMixPhone.OpenId != crmId {
		err = job.UpsertChannelMixPhone(ctx, mixPhone, cdpId, crmId)
		if err != nil {
			log.Warn(ctx, "Failed to upsert channelMixPhone", log.Fields{
				"err":      err.Error(),
				"mixPhone": mixPhone,
				"cdpId":    cdpId,
				"crmId":    crmId,
			})
		}
	}
	if channelMixPhone.OpenId != "" && channelMixPhone.OpenId != crmId {
		log.Warn(ctx, "Found crmId conflicting with the one from jd", log.Fields{
			"crmIdFromMai": channelMixPhone.OpenId,
			"crmIdFromJd":  crmId,
		})
	}
	if channelMixPhone.Phone == "" || channelMixPhone.OpenId != "" && channelMixPhone.OpenId == crmId {
		job.AddSucceedCountAtomically(1)
		return
	}
	if channelMixPhone.OpenId != "" && channelMixPhone.OpenId != crmId {
		oldCrmId := channelMixPhone.OpenId
		newCrmId := crmId
		selector := bson.M{
			"accountId":       bson.ObjectIdHex(job.AccountId),
			"socials.unionId": oldCrmId,
			"isDeleted":       false,
		}
		updator := bson.M{
			"$set": bson.M{
				"idUpdatedAt":       bson.NewObjectId(),
				"updatedAt":         time.Now(),
				"socials.$.unionId": newCrmId,
			},
		}
		err = model.CMember.UpdateOne(ctx, selector, updator)
		if err != nil {
			log.Warn(ctx, "Failed to UpdateOne", log.Fields{
				"selector": core_util.MarshalInterfaceToString(selector),
				"updator":  core_util.MarshalInterfaceToString(updator),
				"err":      err.Error(),
			})
		}
	}
	members := job.getMembersByUniqueIds(ctx, channelMixPhone.Phone, crmId)
	if len(members) <= 1 {
		job.AddSucceedCountAtomically(1)
		return
	}
	getMemberIds := func(members []model.Member) string {
		memberIds := ""
		for _, member := range members {
			memberIds = fmt.Sprintf("%s,%s", memberIds, member.Id.Hex())
		}
		return strings.TrimLeft(memberIds, ",")
	}
	mainMember, subMembers := job.getMainMember(members, channelMixPhone.Phone)
	if mainMember.Id.IsZero() {
		job.AddFailedCountAtomically(1)
		job.failedRows <- append(row, fmt.Sprintf("会员合并时获取 mainMember 失败：%s", getMemberIds(members)))
		return
	}
	if job.mergeLimiter != nil {
		if err := job.mergeLimiter.Wait(ctx); err != nil {
			job.AddFailedCountAtomically(1)
			job.failedRows <- append(row, fmt.Sprintf("合并操作限流异常：%s", err.Error()))
			return
		}
	}
	err = mainMember.MergeMember(ctx, subMembers, "", "phone and unionId")
	log.Warn(ctx, "Merge members of jd cdp", log.Fields{
		"mainMember": mainMember.Id.Hex(),
		"subMembers": getMemberIds(subMembers),
	})
	if err != nil {
		job.AddFailedCountAtomically(1)
		job.failedRows <- append(row, fmt.Sprintf("会员合并失败：%s", err.Error()))
		return
	}
	job.AddSucceedCountAtomically(1)
}

func (job *ImportJdCdpChannelMixPhonesJob) getRowValueByKey(row []string, key string) string {
	value := ""
	if idx := util.IndexOfArray(key, &job.Options.Headers); idx >= 0 && idx <= len(row)-1 {
		value = row[idx]
	}
	return value
}

func (job *ImportJdCdpChannelMixPhonesJob) UpsertChannelMixPhone(ctx context.Context, fromMixPhone, toMixPhone, crmId string) error {
	selector := bson.M{
		"accountId": bson.ObjectIdHex(job.AccountId),
		"isDeleted": false,
		"channelId": job.Options.ChannelId,
		"mixPhone":  fromMixPhone,
	}
	setter := bson.M{
		// 兼容 Henkel 离线上传会员的情况，提供上翻参数给 Henkel 时未发生实际上传，拿不到 cdpId；
		// 故 exportGeneratedJdCdpRegisterParams 中将 mixPhone 存为 memberId，实际上翻后通过当前 job 将表中数据替换为 cdpId；
		// <https://gitlab.maiscrm.com/mai/impl/henkel/henkel-module/-/issues/2481>
		"mixPhone":  toMixPhone,
		"openId":    crmId,
		"updatedAt": time.Now(),
	}
	updater := bson.M{
		"$set": setter,
		"$setOnInsert": bson.M{
			"createdAt": time.Now(),
		},
	}
	_, err := extension.DBRepository.Upsert(ctx, model.C_CHANNEL_MIX_PHONE, selector, updater)
	if err != nil {
		return err
	}
	return nil
}

func (job *ImportJdCdpChannelMixPhonesJob) getMembersByUniqueIds(ctx context.Context, phone, unionId string) []model.Member {
	accountId := bson.ObjectIdHex(job.AccountId)
	or := []bson.M{}
	phoneValue := model.GetValueConditionWithEncrypted(ctx, phone, nil)
	or = append(or, bson.M{
		"accountId": accountId,
		"phone":     phoneValue,
		"isDeleted": false,
	})
	or = append(or, bson.M{
		"accountId": accountId,
		"properties": bson.M{
			"$elemMatch": bson.M{
				"value": phoneValue,
				"name":  model.DEFAULT_PROPERTY_PHONE,
			},
		},
		"isDeleted": false,
	})
	or = append(or, bson.M{
		"accountId":       accountId,
		"isDeleted":       false,
		"socials.unionId": unionId,
	})
	selector := bson.M{"$or": or}
	members := model.CMember.GetAllByConditionWithoutIsDeleted(ctx, selector)
	return members
}

func (job *ImportJdCdpChannelMixPhonesJob) getMainMember(members []model.Member, phone string) (model.Member, []model.Member) {
	var mainMember model.Member
	var subMembers []model.Member
OUTER_FLAG:
	for _, member := range members {
		if member.Phone == phone {
			mainMember = member
			continue OUTER_FLAG
		}
		for _, p := range member.Properties {
			if p.Name != model.PROPERTY_TYPE_PHONE {
				continue
			}
			if p.Value == phone {
				mainMember = member
				continue OUTER_FLAG
			}
		}
		subMembers = append(subMembers, member)
	}
	if len(subMembers) == 0 && mainMember.Id.Valid() {
		for _, m := range members {
			if m.Id == mainMember.Id {
				continue
			}
			subMembers = append(subMembers, m)
		}
	}
	if mainMember.Id.Valid() {
		return mainMember, subMembers
	}
	mainMember = members[:1][0]
	subMembers = members[1:]
	return mainMember, subMembers
}

func (job *ImportJdCdpChannelMixPhonesJob) writeFailedFile(ctx context.Context) (failedUrl string, err error) {
	failedRow, ok := <-job.failedRows
	if !ok {
		return
	}
	failedUrl, err = job_util.ExportFile(
		ctx,
		job.Options.FailedFileName,
		func(f *os.File) error {
			if err := job.NewCSVWriter(ctx, f); err != nil {
				return err
			}
			job.Write(&failedRow)
			for {
				failedRow, ok := <-job.failedRows
				if !ok {
					break
				}
				job.Write(&failedRow)
			}
			job.Flush()
			return nil
		},
	)
	return
}
