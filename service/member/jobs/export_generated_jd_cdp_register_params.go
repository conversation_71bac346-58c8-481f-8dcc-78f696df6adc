package jobs

import (
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"os"
	"sync"
	"time"

	"mairpc/core/component"
	"mairpc/core/component/jingdong"
	core_errors "mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_job "mairpc/core/job"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	pb_account "mairpc/proto/account"
	"mairpc/service/member/model"
	"mairpc/service/share/constant"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"
	share_util "mairpc/service/share/util"
)

type ExportGeneratedJdCdpRegisterParamsJob struct {
	core_job.Job
	Options          *ExportGeneratedJdCdpRegisterParamsCliArg
	channel          *pb_account.ChannelDetailResponse
	channelMixPhones chan *model.ChannelMixPhone
	validCount       uint64
	csvWriter        *csv.Writer
}

type ExportGeneratedJdCdpRegisterParamsCliArg struct {
	core_job.CliArg
	ChannelId    string                                            `json:"channelId,omitempty" valid:"required"`
	PoolSize     int                                               `json:"poolSize,omitempty" valid:"required"`
	Limit        uint64                                            `json:"limit,omitempty"`
	Ids          []string                                          `json:"ids,omitempty"`
	IdGt         string                                            `json:"idGt,omitempty"`
	CreatedAt    ExportGeneratedJdCdpRegisterParamsCliArgDateRange `json:"createdAt,omitempty"`
	SubscribedAt ExportGeneratedJdCdpRegisterParamsCliArgDateRange `json:"subscribedAt,omitempty"`
}

type ExportGeneratedJdCdpRegisterParamsCliArgDateRange struct {
	StartAt string `json:"startAt,omitempty"`
	EndAt   string `json:"endAt,omitempty"`
}

func init() {
	job := new(ExportGeneratedJdCdpRegisterParamsJob)
	job.RegisterToParent(job, RootCmd)
}

// 用于导出 henkel 京东营销云会员上翻参数，以文件形式提供给陆泽进行上翻
func (*ExportGeneratedJdCdpRegisterParamsJob) JobName() string {
	return "exportGeneratedJdCdpRegisterParams"
}

func (*ExportGeneratedJdCdpRegisterParamsJob) ShouldIterateAccount() bool {
	return false
}

func (*ExportGeneratedJdCdpRegisterParamsJob) EnabledMods() []string {
	return []string{}
}

func (job *ExportGeneratedJdCdpRegisterParamsJob) RunE(ctx context.Context) error {
	job.Options = &ExportGeneratedJdCdpRegisterParamsCliArg{}
	if err := job.SetCliArgs(job.Options); err != nil {
		return err
	}
	channel, err := model.GetChannelByOrigin(ctx, constant.JD_MEMBER, job.Options.ChannelId)
	if err != nil {
		return err
	}
	if channel == nil {
		return core_errors.NewNotExistsError("channel")
	}
	job.channel = channel
	return job.export(ctx)
}

func (job *ExportGeneratedJdCdpRegisterParamsJob) export(ctx context.Context) error {
	var member *model.Member
	condition, err := job.genMemberCondition()
	if err != nil {
		return err
	}
	it, err := model.CMember.Iterate(ctx, *condition, []string{"_id"})
	if err != nil {
		return err
	}
	defer it.Close()
	pool, err := util.NewGoroutinePoolWithPanicHandler(job.Options.PoolSize)
	if err != nil {
		return err
	}
	defer pool.Release()
	job.channelMixPhones = make(chan *model.ChannelMixPhone, 1000)
	component.GO(ctx, func(ctx context.Context) {
		wg := sync.WaitGroup{}
		for it.Next(&member) {
			if job.Options.Limit > 0 && job.validCount >= job.Options.Limit {
				break
			}
			if member.Phone == "" {
				continue
			}
			var (
				phone    = member.Phone
				memberId = member.Id
			)
			wg.Add(1)
			pool.Submit(func() {
				defer wg.Done()
				phone, err := extension.Encryption.Decrypt(ctx, phone)
				if err != nil {
					log.Warn(ctx, "Failed to decrypt phone when exporting jd cdp register params", log.Fields{
						"memberId": memberId,
						"phone":    phone,
					})
					return
				}
				job.channelMixPhones <- &model.ChannelMixPhone{
					ChannelId: job.channel.ChannelId,
					// 执行该 job 时陆泽还未进行实际上翻，拿不到 cdpId，这里将 mixPhone 存为 memberId，实际上翻后应将表中数据替换为 cdpId
					// https://gitlab.maiscrm.com/mai/impl/henkel/henkel-module/-/issues/2481#note_5498350
					MixPhone: memberId.Hex(),
					Phone:    phone,
				}
			})
		}
		wg.Wait()
		close(job.channelMixPhones)
	})
	fileName := fmt.Sprintf("京东营销云方式二会员信息_%s.csv", share_util.GetJobTimestamp(time.Now()))
	url, err := job_util.ExportFile(
		ctx,
		fileName,
		func(f *os.File) error {
			job.csvWriter = csv.NewWriter(f)
			if err := job.csvWriter.Write([]string{"会员 ID", "加密手机号", "account"}); err != nil {
				return err
			}
			job.WriteChannelMixPhones(ctx)
			return job.csvWriter.Error()
		},
	)
	log.Warn(ctx, "Succeed to export jd cdp register params", log.Fields{
		"validCount": job.validCount,
		"url":        url,
	})
	return err
}

func (job *ExportGeneratedJdCdpRegisterParamsJob) genMemberCondition() (*bson.M, error) {
	condition := bson.M{
		"accountId": util.ToMongoId(job.AccountId),
		"isDeleted": false,
	}
	if job.Options.IdGt != "" {
		condition["_id"] = bson.M{
			"$gt": bson.ObjectIdHex(job.Options.IdGt),
		}
	}
	if len(job.Options.Ids) > 0 {
		condition["_id"] = bson.M{
			"$in": core_util.ToObjectIdArray(job.Options.Ids),
		}
	}
	createdAtCond := bson.M{}
	if job.Options.CreatedAt.StartAt != "" {
		createdAtGte, err := time.Parse(core_util.RFC3339Mili, job.Options.CreatedAt.StartAt)
		if nil != err {
			return nil, err
		}
		createdAtCond["$gte"] = createdAtGte
	}
	if job.Options.CreatedAt.EndAt != "" {
		createdAtLt, err := time.Parse(core_util.RFC3339Mili, job.Options.CreatedAt.EndAt)
		if nil != err {
			return nil, err
		}
		createdAtCond["$lt"] = createdAtLt
	}
	if len(createdAtCond) > 0 {
		condition["createdAt"] = createdAtCond
	}
	subscribedAtCond := bson.M{}
	if job.Options.SubscribedAt.StartAt != "" {
		subscribedAtGte, err := time.Parse(core_util.RFC3339Mili, job.Options.SubscribedAt.StartAt)
		if nil != err {
			return nil, err
		}
		subscribedAtCond["$gte"] = subscribedAtGte
	}
	if job.Options.SubscribedAt.EndAt != "" {
		subscribedAtLt, err := time.Parse(core_util.RFC3339Mili, job.Options.SubscribedAt.EndAt)
		if nil != err {
			return nil, err
		}
		subscribedAtCond["$lt"] = subscribedAtLt
	}
	if len(subscribedAtCond) > 0 {
		condition["socials.subscribeTime"] = subscribedAtCond
	}
	return &condition, nil
}

func (job *ExportGeneratedJdCdpRegisterParamsJob) WriteChannelMixPhones(ctx context.Context) {
	channelMixPhones := []*model.ChannelMixPhone{}
	lines := [][]string{}
	for channelMixPhone := range job.channelMixPhones {
		channelMixPhones = append(channelMixPhones, channelMixPhone)
		lines = append(lines, []string{
			channelMixPhone.MixPhone,
			jingdong.GenerateMixPhone(channelMixPhone.Phone, job.channel.EncryptionKey, job.channel.ChannelId),
			jingdong.GenerateCrmId(channelMixPhone.Phone, job.channel.ChannelId),
		})
		if len(channelMixPhones) >= 500 {
			err := model.CChannelMixPhone.BatchUpsert(ctx, channelMixPhones)
			channelMixPhones = channelMixPhones[0:0]
			if err != nil {
				log.Warn(ctx, "Failed to upsert jd member mix phone", log.Fields{
					"error": err.Error(),
				})
			}
		}
		if len(lines) >= 500 {
			if err := job.writeAll(&lines); err != nil {
				log.Warn(ctx, "Failed to write records when exporting jd cdp register params", log.Fields{
					"error": err.Error(),
				})
			}
		}
	}
	if len(channelMixPhones) > 0 {
		err := model.CChannelMixPhone.BatchUpsert(ctx, channelMixPhones)
		if err != nil {
			log.Warn(ctx, "Failed to upsert jd member mix phone", log.Fields{
				"error": err.Error(),
			})
		}
	}
	if len(lines) > 0 {
		if err := job.writeAll(&lines); err != nil {
			log.Warn(ctx, "Failed to write records when exporting jd member mix phone", log.Fields{
				"error": err.Error(),
			})
		}
	}
}

func (job *ExportGeneratedJdCdpRegisterParamsJob) writeAll(records *[][]string) error {
	if records == nil {
		return errors.New("Failed to write to csv because of nil pointer")
	}
	if len(*records) == 0 {
		return errors.New("Failed to write to csv because of zero value")
	}
	err := job.csvWriter.WriteAll(*records)
	if err == nil {
		job.validCount += uint64(len(*records))
	}
	*records = (*records)[:0]
	return err
}
