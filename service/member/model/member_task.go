package model

import (
	"context"
	"fmt"
	"mairpc/core/log"
	"mairpc/core/util/algorithm"
	"mairpc/core/util/copier"
	"mairpc/proto/common/types"
	"mairpc/service/member/share"
	"strings"
	"time"

	"github.com/spf13/cast"

	"github.com/qiniu/qmgo"

	"mairpc/core/errors"
	"mairpc/core/extension"
	core_util "mairpc/core/util"
	"mairpc/service/share/component"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"
)

const (
	C_MEMBER_TASK = "memberTask"

	MEMBER_TASK_RECORD_LOCK          = "memberTaskRecordLock:member:%s:task:%s"
	MEMBER_TASK_RECORD_COUNT_LOCK    = "memberTaskRecordCountLock:account%s:task:%s"
	MEMBER_TASK_RECORD_LOCK_DURATION = 3
	MEMBER_TASK_RECORD_WAIT_DURATION = 10

	MEMBER_TASK_TYPE_SCORE_CAMPAIGN_INFOMATION = "scoreCampaignInformation"
	MEMBER_TASK_TYPE_SCORE_RULE                = "scoreTask"

	MEMBER_GROWTH_TYPE_PREFIX        = "member_task_"
	MEMBER_GROWTH_TYPE_CUSTOM        = "custom"
	MEMBER_GROWTH_TYPE_OTHERS        = "others"
	MEMBER_GROWTH_TYPE_REFUND_ORDER  = "refund_order"
	MEMBER_GROWTH_TYPE_SYSTEM        = "system"
	MEMBER_GROWTH_TYPE_LEVEL_UP      = "level_up"      // 升级
	MEMBER_GROWTH_TYPE_DOWNGRADE     = "downgrade"     // 保级失败降级
	MEMBER_GROWTH_TYPE_LEVEL_QUALIFY = "level_qualify" // 保级成功

	MEMBER_GROWTH_REASON_PREFIX_TASK       = "会员任务"
	MEMBER_GROWTH_REASON_PREFIX_INVITATION = "会员激励"

	SCORE_TASK_CODE_FINISH_ORDER_SCORE = "finishOrderScore"
	SCORE_TASK_CODE_PURCHASE_SCORE     = "purchaseScore"

	CODE_PURCHASE = "purchase"
	CODE_ORDER    = "order"

	MEMBER_TASK_BUSINESS_LOYALTY_PURCHASE_PLATFORM     = "loyalty-purchase-platform"
	MEMBER_TASK_BUSINESS_LOYALTY_FINISH_ORDER_PLATFORM = "loyalty-finishOrder-platform"
	MEMBER_TASK_BUSINESS_LOYALTY                       = "loyalty"

	FEATURE_TEST_PRIFIX = "feature-test-"
)

var (
	CMemberTask = &MemberTask{}

	// Deprecated: use default score task business
	DEFAUT_SCORE_TASK_CODES = []string{SCORE_TASK_CODE_FINISH_ORDER_SCORE, SCORE_TASK_CODE_PURCHASE_SCORE}

	DEFAULT_TASK_BUSINESS = []string{
		MEMBER_TASK_BUSINESS_LOYALTY_PURCHASE_PLATFORM,
		MEMBER_TASK_BUSINESS_LOYALTY_FINISH_ORDER_PLATFORM,
	}

	SpecialStoreIdEvents = []string{
		component.MAIEVENT_VIRTUAL_GOODS_COMPELTED,
		component.MAIEVENT_ORDER_COMPLETED,
		component.MAIEVENT_REFUND_ORDER,
		component.MAIEVENT_ORDER_SHIP,
		component.MAIEVENT_PURCHASE_PRODUCT_SERVICE,
		component.MAIEVENT_PURCHASE_PRODUCT,
		component.MAIEVENT_CANCEL_ORDER,
		component.MAIEVENT_ORDER_CREATED,
		component.MAIEVENT_PAID_MEMBERSHIP_OPEN,
	}

	MEMBER_TASK_TYPE_SCORE_CAMPAIGN_MAPPER = map[string]string{
		MEMBER_TASK_TYPE_SCORE_CAMPAIGN_INFOMATION: "完善用户信息",
	}
)

type MemberTask struct {
	Id        bson.ObjectId `bson:"_id"`
	AccountId bson.ObjectId `bson:"accountId"`
	CreatedAt time.Time     `bson:"createdAt"`
	UpdatedAt time.Time     `bson:"updatedAt"`
	CreatedBy bson.ObjectId `bson:"createdBy,omitempty"`
	UpdatedBy bson.ObjectId `bson:"updatedBy,omitempty"`
	IsDeleted bool          `bson:"isDeleted"`
	IsEnabled bool          `bson:"isEnabled"`
	IsDefault bool          `bson:"isDefault"`
	// 所属业务，用于区分这条 memberTask 在哪一种业务场景中生效。
	// 比如属于群脉零售的 memberTask，其 business 就可以是 retail。
	// 比如群脉后台的 memberTask，其 business 就可以是 portal
	Business string `bson:"business"`
	// 类型，用于区分这条 memberTask 在当前业务场景中属于哪一种类型。
	// 比如在群脉零售中，memberTask 的 type 可以是 task, 表示属于任务管理，
	// 也可以是 score，表示属于奖励积分设置。若某个业务场景中不需要对
	// memberTask 进行分组区分，可以不填 type。
	Type string `bson:"type"`
	// 任务名
	Name string `bson:"name"`
	// 任务码，租户唯一。用于标识某一个任务，使用 _id 标识任务
	// 不够直观，不方便开发调试，所以使用任务码来标识任务。
	Code string `bson:"code"`
	// 任务奖励
	Reward  TaskReward   `bson:"reward,omitempty"`
	Rewards []TaskReward `bson:"rewards,omitempty"`
	// 积分明细备注，当 reward 或者 rewards 中配置了积分奖励时设置此字段，会在发放积分时给 scoreHistory 设置 remark
	ScoreRemark string `bson:"scoreRemark,omitempty"`
	// 任务要求，满足要求以后即可获得任务奖励。
	// 一个任务只有一个可生效的任务要求，如果 setting 中多个字段都不为空，
	// 那么会任意选一个字段作为任务要求，忽视其余字段。
	Settings TaskSettings `bson:"settings"`
	// 针对每个会员完成任务并获取奖励次数的限流,
	// 比如每人每天只有前三次完成任务才能获奖。
	PersonalLimitSettings share_model.LimitSettings `bson:"personalLimitSettings,omitempty"`
	// 任务触发限流
	TaskLimitSettings   share_model.LimitSettings `bson:"taskLimitSettings,omitempty"`
	TaskTotalCount      uint64                    `bson:"taskTotalCount"`
	EnablePersonalLimit bool                      `bson:"enablePersonalLimit"`
	// 任务起效时间
	// 可以用来设定周期性的开启任务，比如每个周三开启，每个月第一天开启之类的
	TaskCycle       share_model.Cycle `bson:"taskCycle,omitempty"`
	EnableTaskCycle bool              `bson:"enableTaskCycle"`
	// 记录哪些事件能触发该任务
	EventTrigger []TaskEventTrigger `bson:"eventTrigger,omitempty"`
	// 是否使用开发者模式配置事件
	EnableDeveloperMode bool `bson:"enableDeveloperMode"`
	// 任务描述
	Description string `bson:"description"`
	// 记录哪些事件能回滚该任务
	EventRollbacker []TaskEventTrigger `bson:"eventRollbacker,omitempty"`
	ValidDateRange  ValidDateRange     `bson:"validDateRange,omitempty"`
	// 仅用于积分奖励，且不处理额外积分奖励
	MultiAmountSettings []MultiAmountSetting `bson:"multiAmountSettings,omitempty"`
	Extra               string               `bson:"extra,omitempty"`
	// 任务图标
	Icon string `bson:"icon,omitempty"`
	// 任务类型，memberTaskCategory _id
	CategoryId bson.ObjectId `bson:"categoryId,omitempty"`
	PageId     string        `bson:"pageId,omitempty"`
	TaskCenter TaskCenter    `bson:"taskCenter"`
}

type TaskCenter struct {
	ShowInWeapp      bool   `bson:"showInWeapp"`
	ShowInTaskCenter bool   `bson:"showInTaskCenter"`
	Icon             string `bson:"icon"`
	Description      string `bson:"description"`
	Name             string `bson:"name"`
	Link             Link   `bson:"link"`
}

type Link struct {
	Type     string            `bson:"type"`
	Value    string            `bson:"value"`
	LinkName string            `bson:"linkName"`
	Status   string            `bson:"status"`
	Args     map[string]string `bson:"args,omitempty"`
}

type ValidDateRange struct {
	StartAt time.Time `bson:"startAt"`
	EndAt   time.Time `bson:"endAt"`
	// 用于置空开始时间和结束时间，设置为长期有效，不保存到 db
	SetEmpty bool `bson:"-"`
}

type TaskEventTrigger struct {
	// 事件唯一标识
	EventId string `bson:"eventId"`
	// 若 task 需要根据 amount 计算奖励，会根据
	// amountField 的值从事件的 eventProperties 字段中取
	AmountField string `bson:"amountField"`
	// 触发 task 需要记录 identifier，会根据 identifierField
	// 的值从事件的 eventProperties 字段中取
	IdentifierField string `bson:"identifierField"`
	// 通过制定参数指定值来过滤触发任务的事件
	PropertyFilters []TaskEventPropertyFilter `bson:"propertyFilters,omitempty"`
}

// Compare 会使用 TaskEventTrigger.PropertyFilters 与 eventProperties 做比较，返回比较是否通过
func (t TaskEventTrigger) Compare(eventId string, properties map[string]interface{}) bool {
	_, pass := t.CompareAndGetInValidField(eventId, properties)
	return pass
}

func (t TaskEventTrigger) CompareAndGetInValidField(eventId string, properties map[string]interface{}) (string, bool) {
	for _, filter := range t.PropertyFilters {
		value, ok := properties[filter.Field]
		if strings.Contains(filter.Field, ".") {
			value = core_util.GetValueByFiledName(properties, filter.Field)
		}

		if (!ok && !strings.Contains(filter.Field, ".")) || (strings.Contains(filter.Field, ".") && value == nil) {
			return filter.Field, false
		}

		if util.StrInArray(eventId, &SpecialStoreIdEvents) && filter.Field == "storeId" {
			// 对于这种情况，rule.value 要么是字符串，要么是字符串切片
			newRules := make([]share_model.CompareRule, 0, len(filter.Rules))
			for _, rule := range filter.Rules {
				newRules = append(newRules, formatStoreIdInRule(rule))
			}
			filter.Rules = newRules
		}

		for _, rule := range filter.Rules {
			if !rule.Compare(value) {
				return filter.Field, false
			}
		}
	}

	return "", true
}

func (t TaskEventTrigger) GetFilterRuleString(eventId, field string) string {
	for _, filter := range t.PropertyFilters {
		if filter.Field == field {
			var result []string
			for _, rule := range filter.Rules {
				result = append(result, rule.String())
			}
			return strings.Join(result, "、")
		}
	}

	return ""
}

func formatStoreIdInRule(rule share_model.CompareRule) (newRule share_model.CompareRule) {
	var (
		storeIdsInFilter []string
		validStoreIds    []string
		targetOperator   string
	)
	newRule = rule
	if util.StrInArray(rule.GetOperator(), &[]string{share_model.OPERATOR_IN, share_model.OPERATOR_NIN}) {
		storeIds, err := cast.ToStringSliceE(rule.GetValue())
		if err != nil {
			return
		}
		storeIdsInFilter = append(storeIdsInFilter, storeIds...)
		targetOperator = rule.GetOperator()
	} else if util.StrInArray(rule.GetOperator(), &[]string{share_model.OPERATOR_EQ, share_model.OPERATOR_NE}) {
		storeId, err := cast.ToStringE(rule.GetValue())
		if err != nil {
			return
		}
		storeIdsInFilter = append(storeIdsInFilter, storeId)
		targetOperator = share_model.OPERATOR_NIN
		if rule.GetOperator() == share_model.OPERATOR_EQ {
			targetOperator = share_model.OPERATOR_IN
		}
	} else {
		return
	}
	for _, id := range storeIdsInFilter {
		validStoreIds = append(validStoreIds, strings.Split(id, ":")...)
	}
	newRule.Value = validStoreIds
	newRule.Operator = targetOperator
	return
}

type TaskEventPropertyFilter struct {
	Field string `bson:"field"`
	// 比较规则，只要满足其中一条规则就算通过
	Rules []share_model.CompareRule `bson:"rules"`
}

func (*MemberTask) GetByCode(ctx context.Context, taskCode string) (MemberTask, error) {
	task := MemberTask{}
	selector := Common.GenDefaultCondition(ctx)
	selector["code"] = taskCode
	err := extension.DBRepository.FindOne(ctx, C_MEMBER_TASK, selector, &task)
	if err != nil {
		return MemberTask{}, err
	}
	return task, nil
}

func (*MemberTask) GetByCodes(ctx context.Context, taskCodes []string) ([]MemberTask, error) {
	tasks := []MemberTask{}
	selector := Common.GenDefaultCondition(ctx)
	selector["code"] = bson.M{
		"$in": taskCodes,
	}
	err := extension.DBRepository.FindAll(ctx, C_MEMBER_TASK, selector, []string{}, 0, &tasks)
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (*MemberTask) GetDefaultScoreRule(ctx context.Context) ([]MemberTask, error) {
	tasks := []MemberTask{}
	selector := Common.GenDefaultCondition(ctx)
	selector["type"] = MEMBER_TASK_TYPE_SCORE_RULE
	selector["business"] = bson.M{
		"$in": DEFAULT_TASK_BUSINESS,
	}
	err := extension.DBRepository.FindAll(ctx, C_MEMBER_TASK, selector, []string{}, 0, &tasks)
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (*MemberTask) DeleteByCode(ctx context.Context, taskCode string) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["code"] = taskCode
	updater := bson.M{
		"$set": bson.M{
			"isDeleted": true,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_MEMBER_TASK, selector, updater)
}

// Record 会根据所给条件记录任务进度，如果进度满足发奖条件还会自动发奖
// 并返回奖励内容
// 需要注意的是，如果已经达到个人限流上限，那么不再记录进度。
func (m MemberTask) Record(ctx context.Context, member Member, taskInfo *TaskInfo) (uint64, uint64, error) {
	if !m.IsEnabled || !m.MatchTaskCycle() {
		return 0, 0, errors.NewNotEnabledError("task")
	}

	// 会员任务未开始
	if !m.InValidPerid() {
		return 0, 0, nil
	}

	key := fmt.Sprintf(MEMBER_TASK_RECORD_LOCK, member.Id.Hex(), m.Id.Hex())
	deadline := time.Now().Add(time.Second * MEMBER_TASK_RECORD_WAIT_DURATION)
	for {
		if time.Now().After(deadline) {
			return 0, 0, errors.NewTooManyRequestsError("member")
		}
		ok, _ := extension.RedisClient.SetNX(key, "", MEMBER_TASK_RECORD_LOCK_DURATION)
		if !ok {
			time.Sleep(time.Millisecond * 500)
		} else {
			break
		}
	}

	defer extension.RedisClient.Del(key)

	// 验证任务是否还有奖励
	if !m.CheckTaskLimitCountEnough(ctx) {
		log.Warn(ctx, "task is out of reward", log.Fields{
			"businessId": taskInfo.GetIdentifier(ctx),
			"memberId":   member.Id.Hex(),
		})
		return 0, 0, nil
	}

	// 检查客户的任务完成次数，如果已经达到个人限流上限，那么不再记录进度
	taskRecord, err := CMemberTaskRecord.GetRecord(ctx, member.Id, m.Code)
	// 没有错误，判断是否到限流上限了。
	if err == nil {
		enabled, remainCount := m.GetPersonalLimitRemain(*taskRecord)
		if enabled && remainCount == 0 {
			return 0, 0, errors.NewInvalidArgumentErrorWithMessage("memberId", "exceed personal limit")
		}
	}
	// 如果没找到，说明这个会员第一次完成当前任务，这属于正常业务所以继续执行后面的逻辑
	if taskRecord != nil && taskRecord.ContainsIdentifier(ctx, taskInfo.TriggerIdentifier) {
		return 0, 0, errors.NewAlreadyExistsError("identifier")
	}
	if taskRecord == nil {
		taskRecord = &MemberTaskRecord{}
		taskRecord.TaskCode = m.Code
		taskRecord.MemberId = member.Id
		taskRecord.Identifiers = []string{taskInfo.TriggerIdentifier}
		err = taskRecord.Create(ctx)
	} else {
		err = taskRecord.Record(ctx, taskInfo.TriggerIdentifier)
	}
	if err != nil {
		return 0, 0, err
	}

	canRewardHandler := InitCanRewardHandler(m, InitCanRewardOption{Member: member, Amount: taskInfo.Amount, Identifiers: taskRecord.Identifiers})
	if !canRewardHandler.CanReward(ctx) {
		return 0, 0, nil
	}
	// 先申明一个 MemberTaskReward 对象，并赋值 ID。奖励记录尽可能关联 MemberTaskReward 的
	// ID 方便以后回滚
	taskReward := MemberTaskReward{
		Id:       bson.NewObjectId(),
		MemberId: member.Id,
		TaskInfo: MemberTaskInfo{
			Business:            m.Business,
			Code:                m.Code,
			Reward:              m.Reward,
			Rewards:             m.getRewardsByMember(member, taskInfo),
			Settings:            m.Settings,
			MultiAmountSettings: m.MultiAmountSettings,
		},
	}
	taskInfo.RewardId = taskReward.Id.Hex()
	// 发奖
	scoreInfo, growthInfo, couponRewards, err := m.RewardMember(ctx, member, taskInfo)
	if err != nil {
		return 0, 0, err
	}

	// 更新任务触发次数
	err = m.IncTaskDailyCount(ctx, m.Id)
	if err != nil {
		log.Error(ctx, "increase task daily count failed", log.Fields{"err": err.Error()})
	}
	err = m.IncTaskMonthlyCount(ctx, m.Id)
	if err != nil {
		log.Error(ctx, "increase task monthly count failed", log.Fields{"err": err.Error()})
	}
	err = m.IncTaskTotalCount(ctx)
	if err != nil {
		log.Error(ctx, "increase task total count failed", log.Fields{"err": err.Error()})
	}
	// 发完奖以后需要更新任务进度，以及记录奖励日志
	rewardIdentifiers, recordIdentifiers := m.GetIdentifiers(*taskRecord)
	taskReward.RewardInfo = MemberTaskRewardInfo{
		Identifiers:    rewardIdentifiers,
		Amount:         taskInfo.GetAmount(ctx),
		TriggerEventId: taskInfo.EventId,
		ActualReward: ActualTaskReward{
			Score:                 scoreInfo.GetTotalScore(),
			Growth:                growthInfo.Growth,
			CouponIds:             core_util.ToObjectIdArray(core_util.ExtractArrayField("Id", couponRewards)),
			MemberShipDiscountIds: core_util.CombineArraysV2(core_util.ExtractArrayFieldV2("MemberShipDiscountIds", []string{}, couponRewards)...),
		},
		BonusInfo: TaskRewardBonusInfo{
			LevelScoreMultiplier:          scoreInfo.LevelScoreMultiplier,
			MemberPaidCardScoreMultiplier: scoreInfo.MemberPaidCardScoreMultiplier,
			MarketingScoreMultiplier:      scoreInfo.MarketingScoreMultiplier,
			MarketingScoreMultipliers:     scoreInfo.MarketingScoreMultipliers,
			ScoreBonuses:                  ScoreRewardInfo{}.GetUniqueByScoreBonuses(scoreInfo.ScoreBonuses), // 多规则每满中需要去重
		},
	}
	_, expireAt, _ := member.IsPaidMember(ctx)
	if expireAt.After(time.Now()) {
		taskReward.IsPaidMember = true
	}
	taskReward.MemberLevel = uint64(member.Level)
	taskReward.Create(ctx)
	taskRecord.AfterReward(ctx, recordIdentifiers, rewardIdentifiers, time.Now(), taskInfo.Task)
	if m.NeedSendTaskCompletedEvent(member.Level) {
		taskRecord.SendTaskCompletedEvent(ctx, m, taskReward, member, taskInfo.ChannelId, taskInfo.originEventRequest) // 发送会员任务完成事件
	}
	return scoreInfo.GetTotalScore(), growthInfo.Growth, nil
}

// 默认触发了任务就会发任务完成事件，无论是否获得奖励，但是当奖励限制等级切客户未满足时，不发事件
func (m MemberTask) NeedSendTaskCompletedEvent(level int64) bool {
	if len(m.getRewardsByLevel(level)) > 0 {
		return true
	} else {
		return false
	}
}

func (m MemberTask) IncTaskDailyCount(ctx context.Context, taskId bson.ObjectId) error {
	if !m.TaskLimitSettings.Enabled() {
		return nil
	}
	memberTaskStats := &MemberTaskStats{}
	dailyPeriod := time.Now().Format("2006-01-02")
	defer func() {
		if r := recover(); r != nil {
			memberTaskStats.DescTaskCountByTaskId(ctx, taskId, dailyPeriod)
		}
	}()
	if ok, count := m.TaskLimitSettings.RemainChancesByLimitType(share_model.LIMIT_TYPE_DAILY); ok {
		err := memberTaskStats.IncTaskCountByTaskId(ctx, taskId, dailyPeriod)
		if err != nil {
			log.Warn(ctx, "inc task count failed", log.Fields{"err": err})
			return errors.NewTooManyRequestsError("daily")
		}
		if memberTaskStats.Total > count {
			return errors.NewTooManyRequestsError("daily")
		}
	}
	return nil
}

func (m MemberTask) IncTaskMonthlyCount(ctx context.Context, taskId bson.ObjectId) error {
	if !m.TaskLimitSettings.Enabled() {
		return nil
	}
	memberTaskStats := &MemberTaskStats{}
	monthlyPeriod := time.Now().Format("2006-01")
	defer func() {
		if r := recover(); r != nil {
			memberTaskStats.DescTaskCountByTaskId(ctx, taskId, monthlyPeriod)
		}
	}()
	if ok, count := m.TaskLimitSettings.RemainChancesByLimitType(share_model.LIMIT_TYPE_MONTHLY); ok {
		err := memberTaskStats.IncTaskCountByTaskId(ctx, taskId, monthlyPeriod)
		if err != nil {
			log.Warn(ctx, "inc task count failed", log.Fields{"err": err})
			return errors.NewTooManyRequestsError("monthly")
		}
		if memberTaskStats.Total > count {
			return errors.NewTooManyRequestsError("monthly")
		}
	}
	return nil
}

func (m MemberTask) IncTaskTotalCount(ctx context.Context) error {
	if !m.TaskLimitSettings.Enabled() {
		return nil
	}
	memberTask := &MemberTask{}
	selector, updater := bson.M{"_id": m.Id}, bson.M{
		"$inc": bson.M{
			"taskTotalCount": 1,
		},
	}
	if ok, count := m.TaskLimitSettings.RemainChancesByLimitType(share_model.LIMIT_TYPE_TOTAL); ok {
		selector["taskTotalCount"] = bson.M{
			"$lt": count,
		}
	}
	change := qmgo.Change{
		Update: updater,
	}
	return extension.DBRepository.FindAndApply(ctx, C_MEMBER_TASK, selector, []string{}, change, memberTask)
}

func (m MemberTask) CheckTaskLimitCountEnough(ctx context.Context) bool {
	ok, _ := m.CheckTaskLimitCountEnoughWithLimitedType(ctx)
	return ok
}

func (m MemberTask) CheckTaskLimitCountEnoughWithLimitedType(ctx context.Context) (bool, string) {
	if !m.TaskLimitSettings.Enabled() {
		return true, ""
	}
	selector := bson.M{"_id": m.Id}
	result := &MemberTask{}
	needCheckTotal, total := m.TaskLimitSettings.RemainChancesByLimitType(share_model.LIMIT_TYPE_TOTAL)
	needCheckDaily, daily := m.TaskLimitSettings.RemainChancesByLimitType(share_model.LIMIT_TYPE_DAILY)
	needCheckMonthly, monthly := m.TaskLimitSettings.RemainChancesByLimitType(share_model.LIMIT_TYPE_MONTHLY)
	dailyPeriod := time.Now().Format("2006-01-02")
	monthlyPeriod := time.Now().Format("2006-01")
	if needCheckDaily {
		stats, err := CMemberTaskStats.GetMemberTaskStatsByTaskId(ctx, m.Id, dailyPeriod)
		if (err != nil && err != bson.ErrNotFound) || (stats != nil && stats.Total >= daily) {
			return false, share_model.LIMIT_TYPE_DAILY
		}
	}
	if needCheckMonthly {
		stats, err := CMemberTaskStats.GetMemberTaskStatsByTaskId(ctx, m.Id, monthlyPeriod)
		if (err != nil && err != bson.ErrNotFound) || (stats != nil && stats.Total >= monthly) {
			return false, share_model.LIMIT_TYPE_MONTHLY
		}
	}
	if needCheckTotal {
		if extension.DBRepository.FindOne(ctx, C_MEMBER_TASK, selector, result) != nil {
			return false, ""
		}
		return result.TaskTotalCount < total, share_model.LIMIT_TYPE_TOTAL
	}
	return true, ""
}

func (m MemberTask) GetTaskLimitCount(ctx context.Context) (totalUse, monthlyUse, dailyUse uint64) {
	var (
		dailyPeriod   = time.Now().Format("2006-01-02")
		monthlyPeriod = time.Now().Format("2006-01")
	)

	if !m.TaskLimitSettings.Enabled() {
		return
	}

	stats, _ := CMemberTaskStats.GetMemberTaskStatsByTaskId(ctx, m.Id, dailyPeriod)
	if stats != nil {
		dailyUse = stats.Total
	}

	stats, _ = CMemberTaskStats.GetMemberTaskStatsByTaskId(ctx, m.Id, monthlyPeriod)
	if stats != nil {
		monthlyUse = stats.Total
	}

	totalUse = m.TaskTotalCount
	return
}

func (m MemberTask) GetIdentifiers(taskRecord MemberTaskRecord) (reward, record []string) {
	// 对于累计任务，发奖的时候使用最新的 times 个 identifier
	if m.Settings.Times > 0 {
		reward = taskRecord.Identifiers[len(taskRecord.Identifiers)-int(m.Settings.Times):]
		record = taskRecord.Identifiers[:len(taskRecord.Identifiers)-int(m.Settings.Times)]
		return
	}

	// 对于累计以外的任务，每次发奖都用最新 identifier，并且发完奖以后 memberTaskRecord 里不需要留 identifier 了
	reward = taskRecord.Identifiers[len(taskRecord.Identifiers)-1:]
	record = []string{}
	return
}

func (m MemberTask) GetPersonalLimitRemainAndUsed(taskRecord MemberTaskRecord) (bool, uint64, uint64, string) {
	if !m.PersonalLimitSettings.Enabled() {
		return false, 0, 0, ""
	}

	remain, used, limitedType := taskRecord.PersonalLimit.CountRemainingWithCount(m.PersonalLimitSettings)
	return true, remain, used, limitedType
}

func (m MemberTask) GetPersonalLimitMinUsed(taskRecord MemberTaskRecord) (bool, uint64) {
	if !m.PersonalLimitSettings.Enabled() {
		return false, 0
	}
	limitMap := map[string]uint64{}
	for _, setting := range m.PersonalLimitSettings {
		limitMap[setting.Type] = setting.Value
	}
	// 获取最小周期内的已领次数
	for _, limitType := range []string{
		share_model.LIMIT_TYPE_HOURLY,
		share_model.LIMIT_TYPE_DAILY,
		share_model.LIMIT_TYPE_WEEKLY,
		share_model.LIMIT_TYPE_MONTHLY,
		share_model.LIMIT_TYPE_QUARTERLY,
		share_model.LIMIT_TYPE_YEARLY,
		share_model.LIMIT_TYPE_TOTAL,
	} {
		if limitMap[limitType] > 0 {
			return true, taskRecord.PersonalLimit.CountCurrentPeriodUsedByType(limitType)
		}
	}
	return false, 0
}

// GetPersonalLimitRemain 会计算限流以后某个客户当前还能获取几次任务奖励。
func (m MemberTask) GetPersonalLimitRemain(taskRecord MemberTaskRecord) (bool, uint64) {
	limited, remain, _, _ := m.GetPersonalLimitRemainAndUsed(taskRecord)
	return limited, remain
}

// GetTaskLimitRemain 会计算当前任务还剩的任务奖励次数。
func (m MemberTask) GetTaskLimitRemain(taskRecords *[]MemberTaskRecord) (bool, uint64) {
	if !m.TaskLimitSettings.Enabled() {
		return false, 0
	}
	costMapper := make(map[string]uint64)

	for _, l := range m.TaskLimitSettings {
		var count uint64 = 0
		for _, record := range *taskRecords {
			count += record.PersonalLimit.CountUsedByType(l.Type)
		}
		costMapper[l.Type] = count
	}

	for _, l := range m.TaskLimitSettings {
		if l.Value == 0 {
			continue
		}
		switch l.Type {
		case share_model.LIMIT_TYPE_TOTAL:
			return true, l.Value - costMapper[l.Type]
		default:
			// empty
		}
	}
	return true, 0
}

func (m MemberTask) RewardMember(ctx context.Context, member Member, taskInfo *TaskInfo) (ScoreRewardInfo, GrowthRewardInfo, []RewardCouponInfo, error) {
	var (
		scoreReward  ScoreRewardInfo
		growthReward GrowthRewardInfo
		couponReward []RewardCouponInfo
	)

	scoreReward, err := m.SendScoreReward(ctx, &member, taskInfo)
	if err != nil {
		return scoreReward, growthReward, couponReward, err
	}

	// 发成长值奖励
	levelSetting, err := CMemberLevelSetting.GetMemberLevelSetting(ctx)
	if err != nil {
		return scoreReward, growthReward, couponReward, err
	}
	// 按成长值的会员成长模式才发放成长值
	if levelSetting.GetLevelRule() == LEVEL_RULE_GROWTH {
		growth, errs := m.SendGrowthReward(ctx, member, taskInfo, "")
		growthReward = GrowthRewardInfo{
			Growth: growth,
		}
		if len(errs) > 0 {
			return scoreReward, growthReward, nil, errs[0]
		}
	}

	couponReward = m.SendCouponReward(ctx, member, taskInfo)

	return scoreReward, growthReward, couponReward, nil
}

type ScoreRewardInfo struct {
	// 算入上限的奖励积分
	LimitedScore uint64
	// 不算入上限的奖励积分
	UnlimitedScore uint64
	// 等级倍率
	LevelScoreMultiplier float64
	// 权益卡倍率
	MemberPaidCardScoreMultiplier float64
	// 营销活动倍率
	MarketingScoreMultiplier float64
	// 营销活动倍率
	MarketingScoreMultipliers []float64
	// 营销活动消费额外增送奖励
	ScoreBonus   PrivilegeBonus
	ScoreBonuses []PrivilegeBonus
}

func (s ScoreRewardInfo) Merge(newInfo ScoreRewardInfo, opt SendScoreCacheInfo) ScoreRewardInfo {
	s.LimitedScore += newInfo.LimitedScore
	s.UnlimitedScore += newInfo.UnlimitedScore
	if newInfo.MemberPaidCardScoreMultiplier > 1 {
		s.MemberPaidCardScoreMultiplier = newInfo.MemberPaidCardScoreMultiplier
	}
	if newInfo.LevelScoreMultiplier > 1 {
		s.LevelScoreMultiplier = newInfo.LevelScoreMultiplier
	}

	if opt.canIssueAllPrivilege(share.ISSUE_ALL_PRIVILEGE_MULTISCORE) {
		if newInfo.MarketingScoreMultiplier > 1 {
			s.MarketingScoreMultipliers = append(s.MarketingScoreMultipliers, newInfo.MarketingScoreMultiplier)
		}
	} else if newInfo.MarketingScoreMultiplier > 1 {
		s.MarketingScoreMultiplier = newInfo.MarketingScoreMultiplier
	}

	for _, info := range newInfo.MarketingScoreMultipliers {
		if info > 1 {
			s.MarketingScoreMultipliers = append(s.MarketingScoreMultipliers, info)
		}
	}

	return s
}

func (s ScoreRewardInfo) MergeScoreBonus(newInfo ScoreRewardInfo) ScoreRewardInfo {
	if newInfo.ScoreBonus.Score > 0 && newInfo.ScoreBonus.Amount > 0 {
		s.ScoreBonus = newInfo.ScoreBonus
	}
	return s
}

func (s ScoreRewardInfo) TryMergeScoreBonus(newInfo ScoreRewardInfo, amount uint64) (ScoreRewardInfo, bool) {
	var mergeSuccess bool
	if newInfo.ScoreBonus.GetBonusScore(amount) > s.ScoreBonus.GetBonusScore(amount) {
		s.ScoreBonus = newInfo.ScoreBonus
		mergeSuccess = true
	}
	return s, mergeSuccess
}

func (s ScoreRewardInfo) AppendScoreBonus(newInfo ScoreRewardInfo) ScoreRewardInfo {
	if newInfo.ScoreBonus.Score > 0 && newInfo.ScoreBonus.Amount > 0 {
		s.ScoreBonuses = append(s.ScoreBonuses, newInfo.ScoreBonus)
	}
	if len(newInfo.ScoreBonuses) > 0 {
		s.ScoreBonuses = append(s.ScoreBonuses, newInfo.ScoreBonuses...)
	}

	return s
}

func (s ScoreRewardInfo) GetUniqueByScoreBonuses(origin []PrivilegeBonus) []PrivilegeBonus {
	pbs := algorithm.CSet.InstanceFromStructSlice(origin).ToArray()
	result := make([]PrivilegeBonus, len(pbs))
	for i := range pbs {
		result[i] = pbs[i].(PrivilegeBonus)
	}
	return result
}

func (s ScoreRewardInfo) GetUniqueScoreBonuses() []PrivilegeBonus {
	pbs := algorithm.CSet.InstanceFromStructSlice(s.ScoreBonuses).ToArray()
	result := make([]PrivilegeBonus, len(pbs))
	for i := range pbs {
		result[i] = pbs[i].(PrivilegeBonus)
	}
	return result
}

func (s ScoreRewardInfo) GetTotalScore() uint64 {
	return s.LimitedScore + s.UnlimitedScore
}

type GrowthRewardInfo struct {
	// 奖励成长值
	Growth uint64
}

// 附加到 TaskReward 中
func isMemberLevelSuitable(memberLevel int64, suitableMemberLevels []uint64) bool {
	if len(suitableMemberLevels) == 0 {
		return true
	}
	return core_util.IndexOfArray(cast.ToUint64(memberLevel), suitableMemberLevels) != -1
}

// TODO 附加到 TaskReward 中
func isMemberPaidCardSuitable(suitableMemberTypes []string, paidCards []PaidCard) bool {
	for _, memberType := range suitableMemberTypes {
		for _, paidCard := range paidCards {
			if memberType == paidCard.CardType && paidCard.ExpireAt.After(time.Now()) {
				return true
			}
		}
	}
	return false
}

func (m MemberTask) SendCouponReward(ctx context.Context, member Member, taskInfo *TaskInfo) []RewardCouponInfo {
	rewards := m.getRewardsByMember(member, taskInfo)
	var successRewardCoupons []RewardCouponInfo
	for _, reward := range rewards {
		if !reward.CheckMemberLevelSuitable(ctx, &member) {
			return successRewardCoupons
		}

		couponReward := RewardContentDirect{}
		copier.Instance(nil).RegisterIgnoreTargetFields([]copier.FieldKey{"Score"}).From(reward).CopyTo(&couponReward)
		successRewardCoupons = append(successRewardCoupons, couponReward.Reward(ctx, member, "", fmt.Sprintf("memberTask:%s", m.Code), "").Coupons...)
	}
	return successRewardCoupons
}

func sendScoreByScoreRule(ctx context.Context, member *Member, scoreRules []MemberScoreSetting, scoreInfo *ScoreInfo) (limitScore uint64, unlimitScore uint64, err error) {
	if scoreInfo.Score > 1000000 {
		return 0, 0, errors.NewInvalidArgumentErrorWithMessage("score", "score is suspected to be too big.")
	}

	memberScoreLimit, _ := GetScoreLimit(ctx, member.Id)
	defer func() {
		_, ok := GetMemberScoreSettingByCode(scoreRules, C_SCORE_SETTING_CODE_LIMIT)
		if ok {
			limitScore = uint64(scoreInfo.Score)
			memberScoreLimit.UseScoreByIdentifier(ctx, uint64(scoreInfo.Score), scoreInfo.BusinessId)
		} else {
			unlimitScore = uint64(scoreInfo.Score)
		}
	}()

	scoreInfo.Brief = RULE_ASSIGNER
	scoreInfo.Assigner = SYSTEM_ASSIGNER

	// 没有指定积分规则，直接发积分
	if len(scoreRules) == 0 {
		directSendScore(ctx, scoreInfo, *member)
		return
	}

	limit, ok := GetMemberScoreSettingByCode(scoreRules, C_SCORE_SETTING_CODE_LIMIT)
	if ok && limit.ReceiveLimit.IsLimitSettingEnabled(ctx) {
		// 上限积分
		if limit.ReceiveLimit.Type == C_LIMIT_STATUS_PERIOD {
			remainScore := memberScoreLimit.ScoreLimit.CountRemaining(limit.ReceiveLimit.Limitation)
			if remainScore < uint64(scoreInfo.Score) {
				scoreInfo.Score = int64(remainScore)
			}
		}
		// 溢出积分
		if limit.ReceiveLimit.IsYearLimitationEnabled {
			// empty
		}
	}

	if scoreInfo.Score == 0 {
		return
	}
	storeId := ""
	if len(scoreInfo.eventProperties) > 0 {
		storeId = cast.ToString(scoreInfo.eventProperties["storeId"])
	}
	protection, ok := GetMemberScoreSettingByCode(scoreRules, C_SCORE_SETTING_CODE_PROTECTION)
	if ok {
		rule := protection.Protection.GetRuleByStoreId(storeId)
		if rule.Type == C_PROTECTION_STATUS_AFTERSEND {
			// 发保护期积分，不直接发积分
			sendIntoProtectScore(ctx, rule, scoreInfo, *member)
			// TODO 保护器特殊处理后 return
		}
	}

	directSendScore(ctx, scoreInfo, *member)
	return
}

func directSendScore(ctx context.Context, scoreInfo *ScoreInfo, member Member) {
	var resp *RewardScoreResult

	resp, err := RewardScoreToMembers(ctx, scoreInfo, []Member{member})
	if err != nil || len(resp.FailedMemberIds) > 0 {
		log.Warn(ctx, "reward score to members failed", log.Fields{"err": err, "resp": resp})
		return
	}
}

func sendIntoProtectScore(ctx context.Context, protectionRule ProtectionSettingRule, scoreInfo *ScoreInfo, member Member) {
	protectionScore := MemberProtectionPeriodScore{
		MemberId: member.Id,
		Score:    scoreInfo.Score,
		ScoreInfo: ScoreHistoryInfo{
			SubBrief:     scoreInfo.SubBrief,
			Assigner:     scoreInfo.Assigner,
			Brief:        scoreInfo.Brief,
			Description:  scoreInfo.GetDescription(),
			Remark:       scoreInfo.GetRemark(),
			Channel:      scoreInfo.ChannelInfo,
			BusinessId:   scoreInfo.BusinessId,
			Origin:       scoreInfo.Origin,
			ScoreTrigger: scoreInfo.scoreTrigger,
		},
	}
	protectFrom := time.Now()
	if scoreInfo.OccurredAt > 0 {
		protectFrom = time.Unix(scoreInfo.OccurredAt/1000, 0)
	}
	protectionScore.SetDueDate(protectFrom, protectionRule.Duration)

	scoreInfo.ProtectScoreDueDate = protectionScore.DueDate

	protectionScore.ScoreInfo.ExpireAt = CScoreHistory.GetExpireAt(ctx, scoreInfo, protectionScore.DueDate)
	err := protectionScore.Create(ctx)
	if err != nil {
		log.Warn(ctx, "create protection score to member failed.", log.Fields{"err": err})
		return
	}

	scoreInfo.ProtectScoreId = protectionScore.Id
}

// 发送享受营销奖励的事件
func SendMarketingRewardEvent(ctx context.Context, member Member, marketingId bson.ObjectId, multiply, bonus, reward uint64, coupons []string, freeDelivery bool, channelId string) {
	if !marketingId.Valid() {
		return
	}

	eventBody := component.CustomerEventBody{
		AccountId:  member.AccountId.Hex(),
		MemberId:   member.Id.Hex(),
		ChannelId:  channelId,
		OpenId:     member.GetOpenIdByChannelId(channelId),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_MEMBER_MARKETING_REWARD,
		CreateTime: time.Now().UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"marketingId":   marketingId.Hex(),
			"multiplyScore": multiply,
			"bonusScore":    bonus,
			"rewardScore":   reward,
			"rewardCoupons": coupons,
			"freeDelivery":  freeDelivery,
		},
	}

	eventBody.SendCustomerEvent(ctx)
}

// SendGrowthReward 发送成长值奖励，返回值是成功发了多少成长值
func (m MemberTask) SendGrowthReward(ctx context.Context, member Member, taskInfo *TaskInfo, identifier string) (uint64, []error) {
	rewards := m.getRewardsByMember(member, taskInfo)
	var errs []error
	var totalGrowth uint64
	for _, reward := range rewards {
		// 会员等级不满足跳过
		if !reward.CheckMemberLevelSuitable(ctx, &member) {
			continue
		}

		// 在某些任务条件中，成长值奖励是可以成倍发放的，因此获取倍率
		multiplier := m.GetRewardMultiplier(taskInfo.Amount, reward)
		growth := multiplier * reward.Growth.Value

		// 发放正常的成长值
		if growth > 0 {
			reason := reward.Growth.Reason
			if reward.Growth.Reason == "" {
				reason = fmt.Sprintf("%s-%s", MEMBER_GROWTH_REASON_PREFIX_TASK, m.Name)
			}

			option := IncGrowthOption{
				Growth:            int64(growth),
				Reason:            reason,
				GrowthType:        m.GetGrowthType(),
				BusinessId:        taskInfo.TriggerIdentifier,
				Identifier:        identifier,
				ChannelId:         taskInfo.ChannelId,
				IgnoredChannelIds: nil,
				CreatedAt:         nil,
				NeedCtxOperator:   false,
				GrowthTrigger:     taskInfo.GenerateTrigger(ctx),
				SceneId:           fmt.Sprintf("memberTask:%s", taskInfo.Task.Id.Hex()),
			}

			_, err := member.IncGrowth(ctx, option)
			if err != nil {
				errs = append(errs, err)
				break
			}
			totalGrowth += growth
		}

		// 发放会员营销的每满成长值
		if reward.GrowthAmount > 0 && taskInfo.Amount/cast.ToUint64(reward.GrowthAmount) >= 1 {
			times := taskInfo.Amount / cast.ToUint64(reward.GrowthAmount)
			amountGrowth := times * reward.AmountGrowth.Value
			reason := reward.AmountGrowth.Reason
			if reward.AmountGrowth.Reason == "" {
				reason = fmt.Sprintf("%s-%s", MEMBER_GROWTH_REASON_PREFIX_INVITATION, m.Name)
			}
			option := IncGrowthOption{
				Growth:            int64(amountGrowth),
				Reason:            reason,
				GrowthType:        m.GetGrowthType(),
				BusinessId:        taskInfo.TriggerIdentifier,
				Identifier:        identifier,
				ChannelId:         taskInfo.ChannelId,
				IgnoredChannelIds: nil,
				CreatedAt:         nil,
				NeedCtxOperator:   false,
				GrowthTrigger:     taskInfo.GenerateTrigger(ctx),
			}

			_, err := member.IncGrowth(ctx, option)
			if err != nil {
				errs = append(errs, err)
				break
			}
			totalGrowth += amountGrowth
		}
	}
	return totalGrowth, errs
}

func (m MemberTask) GetRewards() []TaskReward {
	if len(m.Rewards) == 0 {
		return []TaskReward{m.Reward}
	}
	return m.Rewards
}

func (m MemberTask) getRewardsByLevel(level int64) []TaskReward {
	needToReward := []TaskReward{}
	rewards := m.Rewards
	for _, setting := range m.MultiAmountSettings {
		rewards = append(rewards, setting.Reward)
	}
	for i := range rewards {
		if isMemberLevelSuitable(level, rewards[i].SuitableMemberLevels) {
			needToReward = append(needToReward, rewards[i])
		}
	}
	if len(rewards) == 0 {
		if isMemberLevelSuitable(level, m.Reward.SuitableMemberLevels) {
			return []TaskReward{m.Reward}
		} else {
			return nil
		}
	}
	return needToReward
}

func (m MemberTask) getRewardsByMember(member Member, taskInfo *TaskInfo) []TaskReward {
	needToReward := []TaskReward{}

	// 后续可能也需要考虑适用等级，太复杂，暂时不处理
	// multiAmount 的任务奖励在 task.multiAmountSettings 中
	if m.GetSettingType() == TASK_SETTING_TYPE_MULTI_AMOUNT {
		result := core_util.ExtractArrayField("Reward", m.MultiAmountSettings)
		rewards := []TaskReward{}
		for i := range result {
			if value, ok := result[i].(TaskReward); ok &&
				(taskInfo.AmountScoreRewardId == "" || (taskInfo.AmountScoreRewardId != "" && taskInfo.AmountScoreRewardId == value.TaskRewardId)) {
				rewards = append(rewards, value)
			}
		}
		return rewards
	}

	var memberRewards []TaskReward
	var paidCardRewards []TaskReward
	for i, reward := range m.Rewards {
		if core_util.StrInArray("paidMember", &reward.SuitableMemberTypes) {
			paidCardRewards = append(paidCardRewards, m.Rewards[i])
		} else {
			memberRewards = append(memberRewards, m.Rewards[i])
		}
	}

	newRewards := append(paidCardRewards, memberRewards...)
	for i, reward := range newRewards {
		if isMemberPaidCardSuitable(reward.SuitableMemberTypes, member.PaidCards) {
			needToReward = append(needToReward, newRewards[i])
			break
		}
		if (len(reward.SuitableMemberTypes) == 0 || core_util.StrInArray("member", &reward.SuitableMemberTypes)) && isMemberLevelSuitable(member.Level, reward.SuitableMemberLevels) {
			needToReward = append(needToReward, newRewards[i])
		}
	}
	if len(needToReward) == 0 && len(m.Rewards) == 0 {
		return []TaskReward{m.Reward}
	}
	return needToReward
}

// getGrowthType 根据 MemberTask 的信息得出 growth 的 type 属性
func (m MemberTask) GetGrowthType() string {
	if m.IsDefault {
		return MEMBER_GROWTH_TYPE_PREFIX + m.Code
	} else {
		return MEMBER_GROWTH_TYPE_PREFIX + MEMBER_GROWTH_TYPE_CUSTOM
	}
}

// GetRewardMultiplier 会返当前任务应该返回多少倍奖励
func (m MemberTask) GetRewardMultiplier(amount uint64, reward TaskReward) uint64 {
	if m.Settings.Amount > 0 {
		// 判断这一次累计中数量是任务需求的多少倍，省略掉不满整数倍的部分
		times := amount / m.Settings.Amount
		return times
	}

	if m.GetSettingType() == TASK_SETTING_TYPE_MULTI_AMOUNT {
		for _, item := range m.MultiAmountSettings {
			if item.Reward.TaskRewardId == reward.TaskRewardId {
				return amount / item.Amount
			}
		}
	}

	return 1
}

func (m *MemberTask) Create(ctx context.Context) (err error) {
	return m.createRepeat(ctx, 0)
}

func (m *MemberTask) createRepeat(ctx context.Context, repeat int) (err error) {
	defer func() {
		if r := recover(); r != nil {
			var ok bool
			err, ok = r.(error)
			if ok && !qmgo.IsDup(err) {
				panic(err)
			}

			if repeat < 5 {
				err = m.createRepeat(ctx, repeat+1)
			} else {
				err = errors.NewAlreadyExistsError("code")
			}
		}
	}()

	m.Id = bson.NewObjectId()
	m.AccountId = util.GetAccountIdAsObjectId(ctx)
	m.CreatedAt = time.Now()
	m.UpdatedAt = time.Now()

	if m.Code == "" {
		m.Code = util.RandomStr(8)
		if component.InFeatureTest {
			m.Code = fmt.Sprintf("%s%s", FEATURE_TEST_PRIFIX, m.Code)
		}
	}

	if m.Type == MEMBER_TASK_TYPE_SCORE_CAMPAIGN_INFOMATION {
		m.PageId = bson.NewObjectId().Hex()
	}

	operatorId := core_util.GetUserId(ctx)
	if bson.IsObjectIdHex(operatorId) {
		m.UpdatedBy = bson.ObjectIdHex(operatorId)
	}
	if m.ValidDateRange.SetEmpty {
		m.ValidDateRange = ValidDateRange{}
	}
	_, err = extension.DBRepository.Insert(ctx, C_MEMBER_TASK, m)
	if err != nil {
		return
	}
	m.SendTaskCreatedEvent(ctx)
	return nil
}

func (m *MemberTask) UpdateIsEnabled(ctx context.Context) error {
	m.UpdatedAt = time.Now()

	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       m.Id,
	}
	setter := bson.M{
		"isEnabled":              m.IsEnabled,
		"taskCenter.showInWeapp": m.TaskCenter.ShowInWeapp,
		"updatedAt":              m.UpdatedAt,
	}

	operatorId := core_util.GetUserId(ctx)
	if bson.IsObjectIdHex(operatorId) {
		m.UpdatedBy = bson.ObjectIdHex(operatorId)
		setter["updatedBy"] = m.UpdatedBy
	}
	updater := bson.M{
		"$set": setter,
	}

	return extension.DBRepository.UpdateOne(ctx, C_MEMBER_TASK, selector, updater)
}

func (m *MemberTask) Update(ctx context.Context) error {
	m.UpdatedAt = time.Now()

	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       m.Id,
	}
	setter := bson.M{
		"name":                  m.Name,
		"reward":                m.Reward,
		"rewards":               m.Rewards,
		"settings":              m.Settings,
		"personalLimitSettings": m.PersonalLimitSettings,
		"enablePersonalLimit":   m.EnablePersonalLimit,
		"updatedAt":             m.UpdatedAt,
		"isEnabled":             m.IsEnabled,
		"taskCycle":             m.TaskCycle,
		"taskLimitSettings":     m.TaskLimitSettings,
		"enableTaskCycle":       m.EnableTaskCycle,
		"eventTrigger":          m.EventTrigger,
		"enableDeveloperMode":   m.EnableDeveloperMode,
		"description":           m.Description,
		"eventRollbacker":       m.EventRollbacker,
		"extra":                 m.Extra,
		"multiAmountSettings":   m.MultiAmountSettings,
		"icon":                  m.Icon,
		"taskCenter":            m.TaskCenter,
		"scoreRemark":           m.ScoreRemark,
	}

	unset := bson.M{}
	if m.ValidDateRange.SetEmpty {
		unset["validDateRange"] = ""
	} else if !m.ValidDateRange.StartAt.IsZero() && !m.ValidDateRange.EndAt.IsZero() {
		setter["validDateRange"] = m.ValidDateRange
	}
	if m.CategoryId.Valid() {
		setter["categoryId"] = m.CategoryId
	}

	operatorId := core_util.GetUserId(ctx)
	if bson.IsObjectIdHex(operatorId) {
		m.UpdatedBy = bson.ObjectIdHex(operatorId)
		setter["updatedBy"] = m.UpdatedBy
	}
	updater := bson.M{
		"$set": setter,
	}
	if len(unset) > 0 {
		updater["$unset"] = unset
	}

	err := extension.DBRepository.UpdateOne(ctx, C_MEMBER_TASK, selector, updater)
	if err != nil {
		return err
	}
	m.SendTaskUpdatedEvent(ctx)
	return nil
}

func (m *MemberTask) UpdateByTaskSettingType(ctx context.Context) error {
	m.UpdatedAt = time.Now()

	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       m.Id,
	}
	setter := bson.M{
		"settings":        m.Settings,
		"isEnabled":       m.IsEnabled,
		"eventTrigger":    m.EventTrigger,
		"eventRollbacker": m.EventRollbacker,
		"updatedAt":       time.Now(),
	}
	switch m.GetSettingType() {
	case TASK_SETTING_TYPE_MULTI_AMOUNT:
		setter["multiAmountSettings"] = m.MultiAmountSettings
	default:
		if len(m.Rewards) > 0 {
			setter["rewards"] = m.Rewards
		} else {
			setter["reward"] = m.Reward
		}
	}
	updater := bson.M{
		"$set": setter,
	}

	return extension.DBRepository.UpdateOne(ctx, C_MEMBER_TASK, selector, updater)
}

func (m *MemberTask) UpdateWithoutUpdatedAt(ctx context.Context) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       m.Id,
	}
	setter := bson.M{
		"eventTrigger": m.EventTrigger,
	}
	updater := bson.M{
		"$set": setter,
	}

	return extension.DBRepository.UpdateOne(ctx, C_MEMBER_TASK, selector, updater)
}

func (MemberTask) FindByPagination(ctx context.Context, page extension.PagingCondition) ([]MemberTask, int, error) {
	result := []MemberTask{}
	total, err := extension.DBRepository.FindByPagination(ctx, C_MEMBER_TASK, page, &result)
	if err != nil {
		return nil, 0, err
	}

	return result, total, nil
}

func (m MemberTask) MatchTaskCycle() bool {
	if !m.EnableTaskCycle {
		return true
	}

	return m.TaskCycle.MatchToday()
}

func (MemberTask) GetByEventId(ctx context.Context, id string) ([]*MemberTask, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["isEnabled"] = true
	selector["eventTrigger.eventId"] = id

	tasks := []*MemberTask{}
	err := extension.DBRepository.FindAll(ctx, C_MEMBER_TASK, selector, []string{}, 0, &tasks)
	return tasks, err
}

func (MemberTask) GetRollbackByEventId(ctx context.Context, id string) ([]*MemberTask, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["eventRollbacker.eventId"] = id

	tasks := []*MemberTask{}
	err := extension.DBRepository.FindAll(ctx, C_MEMBER_TASK, selector, []string{}, 0, &tasks)
	return tasks, err
}

func (MemberTask) GetTrigger(triggers []TaskEventTrigger, eventId string) (TaskEventTrigger, bool) {
	for _, t := range triggers {
		if t.EventId != eventId {
			continue
		}
		return t, true
	}
	return TaskEventTrigger{}, false
}

func (m MemberTask) GetEventTrigger(eventId string) (TaskEventTrigger, bool) {
	return m.GetTrigger(m.EventTrigger, eventId)
}

func (m MemberTask) GetEventRollbacker(eventId string) (TaskEventTrigger, bool) {
	return m.GetTrigger(m.EventRollbacker, eventId)
}

func (m MemberTask) SendTaskCreatedEvent(ctx context.Context) {
	eventBody := component.CustomerEventBody{
		AccountId: util.GetAccountId(ctx),
		EventProperties: map[string]interface{}{
			"taskCode":    m.Code,
			"taskName":    m.Name,
			"taskType":    m.Type,
			"pageId":      m.PageId,
			"propertyIds": m.Settings.PerfectInformation,
		},
		SubType:    component.MAIEVENT_MEMBER_TASK_CREATED,
		CreateTime: time.Now().UnixNano() / 1e6,
	}
	eventBody.SendBusinessEvent(ctx)
}

func (m MemberTask) SendTaskUpdatedEvent(ctx context.Context) {
	eventBody := component.CustomerEventBody{
		AccountId: util.GetAccountId(ctx),
		EventProperties: map[string]interface{}{
			"taskCode":    m.Code,
			"taskName":    m.Name,
			"taskType":    m.Type,
			"pageId":      m.PageId,
			"propertyIds": m.Settings.PerfectInformation,
		},
		SubType:    component.MAIEVENT_MEMBER_TASK_UPDATED,
		CreateTime: time.Now().UnixNano() / 1e6,
	}
	eventBody.SendBusinessEvent(ctx)
}

func (m MemberTaskRecord) SendTaskCompletedEvent(ctx context.Context, memberTask MemberTask, taskReward MemberTaskReward, member Member, channelId string, originEventTriggerRequest *types.Event) {
	// 历史原因有一种 type 是 invitation，后续合并后移除
	validTaskType := []string{"task", MEMBER_TASK_TYPE_SCORE_CAMPAIGN_INFOMATION, "invitation"}
	if !core_util.Contains(validTaskType, memberTask.Type) {
		return
	}
	eventBody := component.CustomerEventBody{
		AccountId:  m.AccountId.Hex(),
		MemberId:   m.MemberId.Hex(),
		ChannelId:  channelId,
		OpenId:     member.GetOpenIdByChannelId(channelId),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_MEMBER_TASK_COMPLETE,
		CreateTime: time.Now().UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"taskId":                memberTask.Code,
			"taskName":              memberTask.Name,
			"scoreReward":           taskReward.RewardInfo.ActualReward.Score,
			"growthReward":          taskReward.RewardInfo.ActualReward.Growth,
			"couponReward":          taskReward.RewardInfo.ActualReward.CouponIds,
			"membershipDiscountIds": taskReward.RewardInfo.ActualReward.MemberShipDiscountIds,
		},
	}
	if originEventTriggerRequest != nil {
		eventBody.EventProperties["triggerEvent"] = *originEventTriggerRequest
	}

	eventBody.Send(ctx)
}

func (MemberTask) GetByTaskName(ctx context.Context, taskName string) (*MemberTask, error) {
	result := &MemberTask{}
	selector := share_model.Base.GenDefaultCondition(ctx)
	selector["name"] = taskName
	err := extension.DBRepository.FindOne(ctx, C_MEMBER_TASK, selector, result)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (m MemberTask) PushApplyPrivileges(ctx context.Context, privilegeName string) error {
	var err error
	updater := bson.M{}
	switch m.GetSettingType() {
	case TASK_SETTING_TYPE_MULTI_AMOUNT:
		for i := range m.MultiAmountSettings {
			m.MultiAmountSettings[i].Reward.Score.ApplyPrivileges = append(m.MultiAmountSettings[i].Reward.Score.ApplyPrivileges, privilegeName)
		}
		updater["$set"] = bson.M{
			"multiAmountSettings": m.MultiAmountSettings,
		}
	default:
		if len(m.Rewards) > 0 {
			for i := range m.Rewards {
				m.Rewards[i].Score.ApplyPrivileges = append(m.Rewards[i].Score.ApplyPrivileges, privilegeName)
			}
			updater["$set"] = bson.M{
				"rewards": m.Rewards,
			}
		} else {
			updater["$push"] = bson.M{
				"reward.score.applyPrivileges": privilegeName,
			}
		}
	}
	err = extension.DBRepository.UpdateOne(ctx, C_MEMBER_TASK, bson.M{"_id": m.Id}, updater)
	if err != nil {
		return err
	}
	return nil
}

func (m MemberTask) SetApplyPrivileges(ctx context.Context, privilegeNames []string) error {
	var err error
	updater := bson.M{}
	switch m.GetSettingType() {
	case TASK_SETTING_TYPE_MULTI_AMOUNT:
		for i := range m.MultiAmountSettings {
			m.MultiAmountSettings[i].Reward.Score.ApplyPrivileges = privilegeNames
		}
		updater["$set"] = bson.M{
			"multiAmountSettings": m.MultiAmountSettings,
		}
	default:
		if len(m.Rewards) > 0 {
			for i := range m.Rewards {
				m.Rewards[i].Score.ApplyPrivileges = privilegeNames
			}
			updater["$set"] = bson.M{
				"rewards": m.Rewards,
			}
		} else {
			updater["$set"] = bson.M{
				"reward.score.applyPrivileges": privilegeNames,
			}
		}
	}
	updater["$set"].(bson.M)["updatedAt"] = time.Now()
	err = extension.DBRepository.UpdateOne(ctx, C_MEMBER_TASK, bson.M{"_id": m.Id}, updater)
	if err != nil {
		return err
	}
	return nil
}

func (m MemberTask) FindByBusinessAndType(ctx context.Context, business, taskType string) ([]MemberTask, error) {
	selector := Common.GenDefaultCondition(ctx)

	if business != "" {
		selector["business"] = business
	}
	if taskType != "" {
		selector["type"] = taskType
	}

	tasks := []MemberTask{}
	err := extension.DBRepository.FindAll(ctx, C_MEMBER_TASK, selector, []string{}, 0, &tasks)
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (m MemberTask) UpdateBusinessById(ctx context.Context, id bson.ObjectId, business string) error {
	selector := Common.GenDefaultCondition(ctx)

	selector["_id"] = id

	err := extension.DBRepository.UpdateOne(ctx, C_MEMBER_TASK, selector, bson.M{
		"$set": bson.M{
			"business":  business,
			"updatedAt": time.Now(),
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (m MemberTask) FindByBusinessAndTypeByDefault(ctx context.Context, business, taskType string, isDefault bool) ([]MemberTask, error) {
	selector := Common.GenDefaultCondition(ctx)

	selector["business"] = business
	selector["type"] = taskType
	selector["isDefault"] = isDefault

	tasks := []MemberTask{}
	err := extension.DBRepository.FindAll(ctx, C_MEMBER_TASK, selector, []string{}, 0, &tasks)
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (m MemberTask) DisableAllTaskByBusiness(ctx context.Context, business string) error {
	// 关闭统一规则任务
	err := m.DisableDefaultPlatformTaskByBusiness(ctx, business)
	if err != nil {
		return err
	}
	// 关闭自定义平台规则任务
	err = m.DisableCustomPlatformTaskByBusiness(ctx, business)
	if err != nil {
		return err
	}
	return nil
}

func (m MemberTask) EnableAllTaskByBusiness(ctx context.Context, business string) error {
	// 开启统一规则任务
	err := m.EnableDefaultPlatformTaskByBusiness(ctx, business)
	if err != nil {
		return err
	}
	// 开启自定义平台规则任务
	err = m.EnableCustomPlatformTaskByBusiness(ctx, business)
	if err != nil {
		return err
	}
	return nil
}

// 关闭统一规则任务
func (m MemberTask) DisableDefaultPlatformTaskByBusiness(ctx context.Context, business string) error {
	return m.updateTasksIsEnabledByBusiness(ctx, business, true, false)
}

// 开启统一规则任务
func (m MemberTask) EnableDefaultPlatformTaskByBusiness(ctx context.Context, business string) error {
	return m.updateTasksIsEnabledByBusiness(ctx, business, true, true)
}

// 关闭自定义平台规则任务
func (m MemberTask) DisableCustomPlatformTaskByBusiness(ctx context.Context, business string) error {
	return m.updateTasksIsEnabledByBusiness(ctx, business, false, false)
}

// 开启自定义平台规则任务
func (m MemberTask) EnableCustomPlatformTaskByBusiness(ctx context.Context, business string) error {
	return m.updateTasksIsEnabledByBusiness(ctx, business, false, true)
}

func (m MemberTask) updateTasksIsEnabledByBusiness(ctx context.Context, business string, isDefault, isEnabled bool) error {
	selector := Common.GenDefaultCondition(ctx)

	selector["type"] = MEMBER_TASK_TYPE_SCORE_RULE
	selector["business"] = business
	selector["isDefault"] = isDefault

	_, err := extension.DBRepository.UpdateAll(ctx, C_MEMBER_TASK, selector, bson.M{
		"$set": bson.M{
			"updatedAt": time.Now(),
			"isEnabled": isEnabled,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (m MemberTask) EnableTaskByCodes(ctx context.Context, codes []string) error {
	return m.updateTaskIsEnabledByCodes(ctx, codes, true)
}

func (m MemberTask) DisableTaskByCodes(ctx context.Context, codes []string) error {
	return m.updateTaskIsEnabledByCodes(ctx, codes, false)
}

// 更新任务是否开启
func (m MemberTask) updateTaskIsEnabledByCodes(ctx context.Context, codes []string, isEnabled bool) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["code"] = bson.M{"$in": codes}

	_, err := extension.DBRepository.UpdateAll(ctx, C_MEMBER_TASK, selector, bson.M{
		"$set": bson.M{
			"updatedAt": time.Now(),
			"isEnabled": isEnabled,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (m MemberTask) GetOneByCategoryId(ctx context.Context, categoryId bson.ObjectId) *MemberTask {
	selector := bson.M{
		"accountId":  util.GetAccountIdAsObjectId(ctx),
		"categoryId": categoryId,
		"isDeleted":  false,
	}

	task := MemberTask{}
	err := extension.DBRepository.FindOne(ctx, C_MEMBER_TASK, selector, &task)
	if err != nil || !task.Id.Valid() {
		return nil
	}
	return &task
}

func (m MemberTask) IsDefaultScoreRule() bool {
	if core_util.ContainsString(&DEFAUT_SCORE_TASK_CODES, m.Code) {
		return true
	}

	if core_util.ContainsString(&DEFAULT_TASK_BUSINESS, m.Business) {
		return true
	}
	return false
}

// 任务是否处于有效期
func (m MemberTask) InValidPerid() bool {
	// 开始时间或者结束时间必须同时有效
	if m.ValidDateRange.StartAt.IsZero() || m.ValidDateRange.EndAt.IsZero() {
		return true
	}

	return time.Now().Before(m.ValidDateRange.EndAt) && time.Now().After(m.ValidDateRange.StartAt)
}

// 任务是否过期
func (m MemberTask) ValidPeriodOutofDate() bool {
	// 开始时间或者结束时间必须同时有效
	if m.ValidDateRange.StartAt.IsZero() || m.ValidDateRange.EndAt.IsZero() {
		return false
	}

	return time.Now().After(m.ValidDateRange.EndAt)
}

func (m *MemberTask) EnsureScoreDescriptionAndRemark() {
	if m.ScoreRemark == "" {
		m.ScoreRemark = fmt.Sprintf("完成会员激励-%s", m.Name)
	}
	if m.Reward.Score.Value > 0 && m.Reward.Score.Reason == "" {
		m.Reward.Score.Reason = "会员激励-赠送积分"
	}
	for i, reward := range m.Rewards {
		if reward.Score.Value > 0 && reward.Score.Reason == "" {
			m.Rewards[i].Score.Reason = "会员激励-赠送积分"
		}
		if reward.AmountScore.Value > 0 && reward.AmountScore.Reason == "" {
			m.Rewards[i].AmountScore.Reason = "会员激励-赠送积分"
		}
	}
}

func (*MemberTask) ClearFeatureTestTasks(ctx context.Context) {
	if !component.InFeatureTest {
		return
	}
	condition := Common.GenDefaultCondition(ctx)
	condition["code"] = util.GetFuzzySearchStrRegex(FEATURE_TEST_PRIFIX)
	updater := bson.M{
		"$set": bson.M{
			"isDeleted": true,
		},
	}
	extension.DBRepository.UpdateAll(ctx, C_MEMBER_TASK, condition, updater)
}
