package model

import (
	"context"
	"github.com/spf13/cast"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	"os"
	"time"

	"mairpc/core/extension"
	"mairpc/service/member/codes"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"
)

const (
	C_SCORE_RESET_RULE            = "scoreResetRule"
	SCORE_RESET_TYPE_YEAR         = "year"       // 积分指定年后该月末清零积分
	SCORE_RESET_TYPE_FIXED_DAY    = "fixedDay"   // 固定日期统一清零积分
	SCORE_RESET_TYPE_NEVER        = "never"      // 积分不清零
	SCORE_RESET_TYPE_END_OF_YEAR  = "endOfYear"  // 自然年最后一天清零积分
	SCORE_RESET_TYPE_END_OF_MONTH = "endOfMonth" // 指定月数后的月末清零

	// 过期方式固定为 scoreV2
	SCORE_RESET_TYPE_WHOLE_YEAR = "wholeYear" // 一整年（365/366天）后积分过期
	SCORE_RESET_TYPE_BY_MONTH   = "byMonth"   // 自然月过期

	SCORE_RESET_NOTIFICATION_TYPE_DAILY   = "daily"
	SCORE_RESET_NOTIFICATION_TYPE_WEEKLY  = "weekly"
	SCORE_RESET_NOTIFICATION_TYPE_MONTHLY = "monthly"
)

const (
	SCORE_VERSION_KEY = "SCORE_VERSION"
)

var (
	CScoreResetRule = &ScoreResetRule{}

	SCORE_V2_START_TIME = time.Date(2023, 3, 6, 0, 0, 0, 0, time.Local)
)

type ResetDate struct {
	Day   int `bson:"day"`
	Month int `bson:"month"`
}

type ScoreResetRule struct {
	Id                  bson.ObjectId                 `bson:"_id,omitempty"`
	ResetType           string                        `bson:"resetType"`
	ResetInterval       int64                         `bson:"resetInterval"`
	ResetDate           ResetDate                     `bson:"resetDate"`
	AccountId           bson.ObjectId                 `bson:"accountId"`
	CreatedAt           time.Time                     `bson:"createdAt"`
	UpdatedAt           time.Time                     `bson:"updatedAt"`
	BaseModel           BaseModel                     `bson:"basemodel,omitempty"`
	NotificationSetting ScoreResetNotificationSetting `bson:"notificationSetting,omitempty"`
}

type ScoreResetNotificationSetting struct {
	Type string `bson:"type"`
	Day  int64  `bson:"day"`
}

func (*ScoreResetRule) GetByAccount(ctx context.Context, accountId bson.ObjectId) *ScoreResetRule {
	resetRule := new(ScoreResetRule)
	selector := bson.M{"accountId": accountId}
	extension.DBRepository.FindOne(ctx, C_SCORE_RESET_RULE, selector, resetRule)
	if resetRule.Id.Hex() != "" {
		return resetRule
	}

	return nil
}

func (self *ScoreResetRule) Upsert(ctx context.Context) error {
	validResetTypes := []string{
		SCORE_RESET_TYPE_NEVER,
		SCORE_RESET_TYPE_YEAR,
		SCORE_RESET_TYPE_FIXED_DAY,
		SCORE_RESET_TYPE_END_OF_YEAR,
		SCORE_RESET_TYPE_END_OF_MONTH,
		SCORE_RESET_TYPE_WHOLE_YEAR,
	}
	if !util.StrInArray(self.ResetType, &validResetTypes) {
		return codes.NewError(codes.InvalidScoreResetType)
	}

	now := time.Now()
	selector := bson.M{
		"accountId": self.AccountId,
	}

	updator := bson.M{
		"$set": bson.M{
			"resetDate":     self.ResetDate,
			"resetType":     self.ResetType,
			"resetInterval": self.ResetInterval,
			"updatedAt":     time.Now(),
		},
		"$setOnInsert": bson.M{"createdAt": now},
	}

	updatedId, err := extension.DBRepository.Upsert(ctx, C_SCORE_RESET_RULE, selector, updator)
	if err != nil {
		return err
	}

	if id, ok := updatedId.(bson.ObjectId); ok {
		self.Id = id
		self.CreatedAt = now
	}

	return nil
}

func (date *ResetDate) GetNextResetDate() time.Time {
	now := time.Now()
	year := now.Year()
	resetTime := time.Date(year, time.Month(date.Month), date.Day, 23, 59, 59, 0, now.Location())
	if now.After(resetTime) {
		resetTime = resetTime.AddDate(1, 0, 0)
	}

	return resetTime
}

func IsScoreWholeYearExpireEnabled(ctx context.Context) bool {
	scoreResetRule := CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	if scoreResetRule == nil {
		return false
	}
	return scoreResetRule.ResetType == SCORE_RESET_TYPE_WHOLE_YEAR
}

func (*ScoreResetRule) FindAllByResetTypeAndAccountIds(ctx context.Context, resetType string, accountIds []bson.ObjectId) ([]ScoreResetRule, error) {
	resetRules := []ScoreResetRule{}
	selector := bson.M{
		"resetType": resetType,
	}

	if len(accountIds) > 0 {
		selector["accountId"] = bson.M{
			"$in": accountIds,
		}
	}
	err := extension.DBRepository.FindAll(ctx, C_SCORE_RESET_RULE, selector, nil, 0, &resetRules)
	if err != nil {
		return nil, err
	}
	return resetRules, nil
}

func (s *ScoreResetRule) Update(ctx context.Context) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       s.Id,
	}
	updater := bson.M{
		"$set": bson.M{
			"resetType":     s.ResetType,
			"resetInterval": s.ResetInterval,
			"resetDate":     s.ResetDate,
			"updatedAt":     time.Now(),
		},
	}

	return extension.DBRepository.UpdateOne(ctx, C_SCORE_RESET_RULE, selector, updater)
}

func (s *ScoreResetRule) IsScoreWholeYearExpireEnabled() bool {
	if s == nil {
		return false
	}
	switch s.ResetType {
	case SCORE_RESET_TYPE_WHOLE_YEAR:
		return true
	default:
		return false
	}
}

func IsVersion2(ctx context.Context, rule *ScoreResetRule) bool {
	// feature test 中测试某个租户的部分请求走 V2 时在 context 中加 key
	switch cast.ToString(ctx.Value(SCORE_VERSION_KEY)) {
	case "v2":
		return true
	case "v1":
		return false
	}

	// 历史租户中 UA 和 荣威已经使用了 scoreV2
	if isVersion2SpecialAccountId(ctx) {
		return true
	}

	if isVersion1SpecialAccountId(ctx) {
		return false
	}

	// 纳爱斯环境开启了 cnnice b2b 模块默认使用 scoreV1
	if env := os.Getenv("ENV"); core_util.ContainsString(&[]string{"cnnice-staging", "cnnice-production"}, env) {
		resp, err := client.GetAccountServiceClient().GetAccount(ctx, &request.EmptyRequest{})
		if err != nil {
			log.Error(ctx, "get account failed", log.Fields{"accountId": util.GetAccountId(ctx)})
			return true
		}
		if core_util.Contains(resp.EnabledMods, "cnniceb2b") {
			return false
		}
	}

	// 新租户默认使用 scoreV2
	if util.GetAccountIdAsObjectId(ctx).Time().After(SCORE_V2_START_TIME) {
		return true
	}

	// 历史非 UA 和荣威租户通过 scoreResetRule 判断是否走 scoreV2
	if rule == nil {
		rule = CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	}

	if rule == nil {
		return false
	}

	switch rule.ResetType {
	case SCORE_RESET_TYPE_WHOLE_YEAR, SCORE_RESET_TYPE_BY_MONTH:
		return true
	default:
		return false
	}
}

func isVersion2SpecialAccountId(ctx context.Context) bool {
	specialAccountIds := []string{
		"636c6326bb3d7605a9139ea3", // 荣威 feature test 租户
		"62e731db0a7e2620d22bf2b4", // 荣威 staging 租户
		"637750515573ff18354e9522", // 荣威 QA 租户
		"636b604e4017cb6e6a5ee953", // UA feature test
		"6166d86f9526b7705a449df3", // UA staging
		"6180ea32e0df4b1472704228", // UA production
		"5e7873c4c3307000f272c9e2", // 群脉测试 3
		"6306f96f06e79d624e7d9142", // 荣威生产
	}

	if util.StrInArray(util.GetAccountId(ctx), &specialAccountIds) {
		return true
	}

	return false
}

func isVersion1SpecialAccountId(ctx context.Context) bool {
	aids := []string{
		"658e7a7887199f0e0a24ac42", // staging koston 积分测试 v1 租户
	}
	return util.StrInArray(util.GetAccountId(ctx), &aids)
}

func (r *ScoreResetRule) GetAnnualRange(ctx context.Context) (time.Time, time.Time) {
	// 对于 v2 规则和永不过期的两种情况，年度积分计算时间就是自然年一整年
	if IsVersion2(ctx, r) || r.ResetType == SCORE_RESET_TYPE_NEVER {
		return util.GetStartTimeOfYear(time.Now()), util.GetEndTimeOfYear(time.Now())
	}
	switch r.ResetType {
	case SCORE_RESET_TYPE_END_OF_YEAR:
		// 计算 N 年内的
		return time.Now().AddDate(int(-r.ResetInterval), 0, 0), time.Now()
	case SCORE_RESET_TYPE_END_OF_MONTH:
		// 计算 12 个月内的
		return time.Now().AddDate(0, -12, 0), time.Now()
	case SCORE_RESET_TYPE_FIXED_DAY:
		// 今年清零的时间
		resetDate := time.Date(time.Now().Year(), time.Month(r.ResetDate.Month), r.ResetDate.Day, 0, 0, 0, 0, time.Local)
		// 如果今年清零的时间已经过去了，那么年度积分计算时间从清零时间开始
		if time.Now().After(resetDate) {
			return resetDate, time.Now()
		}
		// 如果今年清零时间没过，那么年度积分计算时间从去年清零时间到今年清零时间
		return resetDate.AddDate(-1, 0, 0), resetDate
	case SCORE_RESET_TYPE_YEAR:
		// 算 12 个月内的
		return time.Now().AddDate(-1, 0, 0), time.Now()
	}
	return time.Time{}, time.Time{}
}

func (*ScoreResetRule) UpdateNotificationSetting(ctx context.Context, setting ScoreResetNotificationSetting) error {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}
	updater := bson.M{
		"$set": bson.M{
			"notificationSetting": setting,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_SCORE_RESET_RULE, condition, updater)
}

func (setting *ScoreResetNotificationSetting) CanSend(t time.Time) bool {
	switch setting.Type {
	case SCORE_RESET_NOTIFICATION_TYPE_WEEKLY:
		weekday := int(t.Weekday())
		if weekday == 0 {
			weekday = 7
		}
		return weekday == int(setting.Day)
	case SCORE_RESET_NOTIFICATION_TYPE_MONTHLY:
		lastDay := util.GetEndTimeOfMonth(t)
		// 如果这个月是小月份，那么就最后一天发
		if lastDay.Day() < int(setting.Day) {
			return lastDay.Day() == t.Day()
		}
		return t.Day() == int(setting.Day)
	default:
		return true
	}
}

func (setting *ScoreResetNotificationSetting) GetTimeRange(now time.Time) (time.Time, time.Time) {
	var (
		from = now
		to   = now
	)
	switch setting.Type {
	case SCORE_RESET_NOTIFICATION_TYPE_WEEKLY:
		to = util.GetEndTimeOfDay(now.AddDate(0, 0, 7))
	case SCORE_RESET_NOTIFICATION_TYPE_MONTHLY:
		to = util.GetEndTimeOfDay(now.AddDate(0, 1, 0))
	default:
		from = util.GetStartTimeOfDay(now.AddDate(0, 0, 31))
		to = from.AddDate(0, 0, 1)
	}
	return from, to
}
