package model

import (
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/service/share/component"
	"mairpc/service/share/util"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

const (
	REWARD_PLATFORM_ALL       = "all"       // 统一
	REWARD_PLATFORM_TAOBAO    = "taobao"    // 淘宝
	REWARD_PLATFORM_DOUYIN    = "douyin"    // 抖音
	REWARD_PLATFORM_MICROMALL = "micromall" // 微商城
	REWARD_PLATFORM_YOUZAN    = "youzan"    // 有赞
	REWARD_PLATFORM_WEIMOB    = "weimob"    // 微盟
	REWARD_PLATFORM_WESHOP    = "weshop"    // 微信小店

	SKU_PLATFORM_DOUYIN = "douyin"
	SKU_PLATFORM_TAOBAO = "taobao"
)

type PlatformNoRewardProductSetting struct {
	Platform    string   `bson:"platform"` // 屏蔽的商品平台
	Total       uint64   `bson:"total"`
	Skus        []string `bson:"skus"`
	MinPayRatio float64  `bson:"minPayRatio"` // 最小支付比例
}

type PlatformNoRewardOrderSetting struct {
	Platform       string `bson:"platform"` // 屏蔽的商品平台
	MinOrderAmount uint64 `bson:"minOrderAmount"`
}

type NoRewardProductSetting struct {
	IsEnabled                bool                             `bson:"isEnabled"`         // 屏蔽商品规则是否开启
	IsPlatformEnabled        bool                             `bson:"isPlatformEnabled"` // 平台规则是否开启
	PlatformNoRewardSettings []PlatformNoRewardProductSetting `bson:"platformNoRewardSettings"`
	AmountField              string                           `bson:"amountField"`      // 屏蔽所按的数值字段
	AmountArrayField         string                           `bson:"amountArrayField"` // 计算的数组字段
	AmountValueField         string                           `bson:"amountValueField"` // 计算标识，sku/productId 等
}

type NoRewardOrderSetting struct {
	IsEnabled                bool                           `bson:"isEnabled"`         // 屏蔽订单规则是否开启
	IsPlatformEnabled        bool                           `bson:"isPlatformEnabled"` // 平台规则是否开启
	PlatformNoRewardSettings []PlatformNoRewardOrderSetting `bson:"PlatformNoRewardSettings"`
	IgnoreStoreIds           []string                       `bson:"ignoreStoreIds"`
}

type NoRewardSetting struct {
	ProductSetting NoRewardProductSetting `bson:"productSetting"`
	OrderSetting   NoRewardOrderSetting   `bson:"orderSetting"`
}

func (n NoRewardSetting) IsProductRefund(refundStatus string) bool {
	if core_util.ContainsString(&[]string{"", "waitingAudit", "rejected", "canceled"}, refundStatus) {
		return false
	}
	return true
}

func (n NoRewardSetting) RemovePreventAmount(ctx context.Context, taskInfo *TaskInfo) (bool, error) {
	if !n.NeedCheckProductNoReward(taskInfo.Task.Code, taskInfo.Task.Business) {
		return false, nil
	}

	// 部分任务没有 amountField，例如下单奖励，如果这里不判断，那么下面的商品屏蔽会减扣溢出
	if taskInfo.Amount == 0 || taskInfo.WithoutPreventAmount == 0 {
		return false, nil
	}

	amountArray, ok := taskInfo.EventProperties[n.ProductSetting.AmountArrayField].([]interface{})
	if !ok {
		log.Warn(ctx, "Invalid amount array.", log.Fields{"properties": taskInfo.EventProperties})
	}
	var allItemPrevent bool // 所有 item 是否都被屏蔽了
	for _, item := range amountArray {
		arrayItem := new(map[string]interface{})
		err := core_util.CopyByJson(item, &arrayItem)
		if err != nil {
			return false, err
		}

		var (
			valueAmount              = cast.ToUint64((*arrayItem)[n.ProductSetting.AmountField])
			noRewardValue            = cast.ToString((*arrayItem)[n.ProductSetting.AmountValueField])
			noRewardPlatform         = cast.ToString((*arrayItem)["platform"])
			price                    = cast.ToInt64((*arrayItem)["price"])
			payAmount                = cast.ToInt64((*arrayItem)["payAmount"])
			payRatio         float64 = -1
		)

		// 回滚的元素跳过统计
		if taskInfo.EventId == component.MAIEVENT_ORDER_COMPLETED && n.IsProductRefund(cast.ToString((*arrayItem)["refundStatus"])) {
			continue
		}

		if noRewardPlatform == "" {
			noRewardPlatform = cast.ToString(taskInfo.EventProperties["platform"])
		}
		if price > 0 {
			payRatio = util.DivideFloat(float64(payAmount), float64(price))
		}
		// 开启积分屏蔽、设置了屏蔽商品、默认积分规则需要剔除屏蔽商品
		if n.ShouldNoRewardByProduct(noRewardValue, noRewardPlatform, taskInfo.Task.Code, taskInfo.Task.Business, payRatio) {
			taskInfo.Amount -= valueAmount
			taskInfo.WithoutPreventAmount -= valueAmount
			if taskInfo.Amount == 0 {
				allItemPrevent = true
			}
			continue
		}
	}
	return allItemPrevent, nil
}

func (n NoRewardSetting) NeedCheckProductNoReward(taskCode, business string) bool {
	if !n.ProductSetting.IsEnabled && !n.ProductSetting.IsPlatformEnabled {
		return false
	}
	// 默认积分规则需要检查
	if (MemberTask{Business: business, Code: taskCode}).IsDefaultScoreRule() {
		return true
	}

	return false
}

func (n NoRewardSetting) ShouldNoRewardByProduct(value, platform, taskCode, business string, payRatio float64) bool {
	if !n.NeedCheckProductNoReward(taskCode, business) {
		return false
	}

	productSettings := n.ProductSetting.PlatformNoRewardSettings

	// 判断平台屏蔽商品
	for _, setting := range productSettings {
		if !n.ProductSetting.IsEnabled {
			return false
		}
		if setting.ShouldNoRewardByPlatform(value, platform, setting.Platform != REWARD_PLATFORM_ALL, payRatio) {
			return true
		}
	}
	return false
}

func (p PlatformNoRewardProductSetting) ShouldNoRewardByPlatform(value, platform string, needCheckPlatform bool, payRatio float64) bool {
	if needCheckPlatform && platform != p.Platform {
		return false
	}

	// 如果 value 等于屏蔽 sku，则需要屏蔽
	if core_util.ContainsString(&p.Skus, value) {
		return true
	}

	// 如果支付比例小于最小支付比例，则需要屏蔽
	if p.MinPayRatio > 0 && payRatio > 0 && p.MinPayRatio > payRatio*100 {
		return true
	}

	return false
}

type NoRewardSettingChecker struct {
	TaskCode             string
	TaskBusiness         string
	TaskType             string
	NoScoreRewardSetting *MemberScoreSetting
	EventProperties      map[string]any
	Amount               uint64
}

type NoRewardSettingCheckerResult struct {
	RuleMinOrderAmount uint64
}

func (n NoRewardSettingChecker) NeedCheckNoRewardSettingV2() bool {
	if core_util.Contains(DEFAULT_TASK_BUSINESS, n.TaskBusiness) && n.TaskType != "task" {
		return true
	}

	if core_util.Contains(DEFAUT_SCORE_TASK_CODES, n.TaskCode) {
		return true
	}
	return false
}

func (n NoRewardSettingChecker) CanRewardByStoreId() bool {
	storeId, exists := n.EventProperties["storeId"]
	if exists && n.NoScoreRewardSetting != nil && len(n.NoScoreRewardSetting.NoScoreReward.OrderSetting.IgnoreStoreIds) > 0 {
		if core_util.ContainsString(&n.NoScoreRewardSetting.NoScoreReward.OrderSetting.IgnoreStoreIds, cast.ToString(storeId)) {
			return false
		}
	}
	return true
}

func (n NoRewardSettingChecker) CanRewardByCustomPlatformRule() (NoRewardSettingCheckerResult, bool, bool) {
	platform := n.EventProperties["platform"]
	var (
		canReward, found = false, false
		result           NoRewardSettingCheckerResult
	)
	// 开启多平台屏蔽规则时先检验指定平台
	if n.NoScoreRewardSetting != nil && n.NoScoreRewardSetting.NoScoreReward.OrderSetting.IsPlatformEnabled {
		for _, rule := range n.NoScoreRewardSetting.NoScoreReward.OrderSetting.PlatformNoRewardSettings {
			if rule.Platform != REWARD_PLATFORM_ALL && platform == rule.Platform {
				result.RuleMinOrderAmount = rule.MinOrderAmount
				if rule.MinOrderAmount <= n.Amount {
					canReward, found = true, true
				}
				found = true
				break
			}
		}
	}
	return result, found, canReward
}

func (n NoRewardSettingChecker) CanRewardByPlatformRule() (NoRewardSettingCheckerResult, bool) {
	var (
		canReward = false
		result    NoRewardSettingCheckerResult
	)
	if n.NoScoreRewardSetting != nil && n.NoScoreRewardSetting.NoScoreReward.OrderSetting.IsEnabled {
		for _, rule := range n.NoScoreRewardSetting.NoScoreReward.OrderSetting.PlatformNoRewardSettings {
			if rule.Platform == REWARD_PLATFORM_ALL {
				result.RuleMinOrderAmount = rule.MinOrderAmount
				if rule.MinOrderAmount <= n.Amount {
					canReward = true
				}
				break
			}
		}
	} else {
		canReward = true
	}
	return result, canReward
}
