package model

import (
	"context"
	"time"

	"github.com/qiniu/qmgo"

	"mairpc/core/errors"
	"mairpc/core/extension"
	core_util "mairpc/core/util"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"
)

const (
	C_MEMBER_SCORE_SETTING = "memberScoreSetting"

	C_SCORE_SETTING_CODE_PROTECTION      = "scoreSettingProtection"    // 默认的积分保护期规则
	C_SCORE_SETTING_CODE_LIMIT           = "scoreSettingLimit"         // 默认的积分上限规则
	C_SCORE_SETTING_CODE_DEDUCTION       = "scoreSettingDeduction"     // 默认的积分抵现规则
	C_SCORE_SETTING_CODE_DECRIPTION      = "scoreSettingDescription"   // 积分规则说明设置
	C_SCORE_SETTING_CODE_NO_SCORE_REWARD = "scoreSettingNoScoreReward" // 积分屏蔽设置

	TYPE_NONE = "none"

	C_LIMIT_STATUS_PERIOD = "period"

	C_PROTECTION_STATUS_AFTERSEND = "afterSend"

	DEDUCTION_TYPE_CASH      = "cash"
	DEDUCTION_TYPE_ALL       = "all"
	DEDUCTION_TYPE_BY_LEVELS = "by_levels"
	DEDUCTION_TYPE_BY_TYPES  = "by_types"
)

var (
	CMemberScoreSetting = MemberScoreSetting{}
)

type MemberScoreSetting struct {
	Id        bson.ObjectId `bson:"_id,omitempty"`
	AccountId bson.ObjectId `bson:"accountId"`
	CreatedAt time.Time     `bson:"createdAt"`
	UpdatedAt time.Time     `bson:"updatedAt"`
	UpdatedBy bson.ObjectId `bson:"updatedBy,omitempty"`
	IsDeleted bool          `bson:"isDeleted"`
	IsDefault bool          `bson:"isDefault"`
	Code      string        `bson:"code"`
	// 积分保护期设置
	Protection ProtectionSetting `bson:"protection,omitempty"`
	// 积分上限设置
	ReceiveLimit ReceiveLimitSetting `bson:"receiveLimit,omitempty"`
	// 积分抵现设置
	Deduction DeductionSetting `bson:"deduction,omitempty"`
	// 积分规则说明设置
	Description DescriptionSetting `bson:"description,omitempty"`
	// 积分屏蔽设置
	NoScoreReward NoRewardSetting `bson:"noScoreReward,omitempty"`
}

type DescriptionSetting struct {
	// 积分规则描述文本
	Content string `bson:"content"`
}

type ProtectionSetting struct {
	// 保护期类型， none 表示无保护期，afterSend 表示发放后立即进入保护期
	Type string `bson:"type"`
	// 保护期持续时间
	Duration share_model.Period      `bson:"duration"`
	Rules    []ProtectionSettingRule `bson:"rules,omitempty"`
}

type ProtectionSettingRule struct {
	Type          string             `bson:"type"`
	Duration      share_model.Period `bson:"duration"`
	OrderStoreIds []string           `bson:"orderStoreIds"`
}

type ReceiveLimitSetting struct {
	Type                    string                    `bson:"type"`                    // 短期积分上限类型，none 表示不设限，period 表示每一段时间内有积分上限
	Limitation              share_model.LimitSettings `bson:"limitation"`              // 对获得短期积分的限流
	IsYearLimitationEnabled bool                      `bson:"isYearLimitationEnabled"` // 年积分上限是否开启
	YearLimit               uint64                    `bson:"yearLimit"`               // 年积分上限
	SingleLimit             uint64                    `bson:"singleLimit"`             // 单次任务积分上限
}

type CAPSetting struct {
	IsYearLimitationEnabled bool
	YearLimit               uint64
}

type DeductionSetting struct {
	Type                     string                           `bson:"type"`                     // 抵现类型，none 表示不可抵现，cash 表示可以用一定量积分抵消一定量的现金消费。
	Percentage               float64                          `bson:"percentage"`               // 一笔订单中能用积分抵现百分比
	Value                    uint64                           `bson:"value"`                    // 积分价值。每 Value 个积分可以抵现 1 元
	DeductionMemberType      string                           `bson:"deductionMemberType"`      // all 所有会员统一抵现规则，by_types 不同会员抵现规则不同
	DeductionType            string                           `bson:"deductionType"`            // 抵扣规则类型，all 表示所有等级统一抵现规则， by_levels 表示不同等级会员抵现规则不同
	LevelDeductions          []LevelDeductionSetting          `bson:"levelDeductions"`          // 不同会员等级抵现规则
	MemberPaidCardDeductions []MemberPaidCardDeductionSetting `bson:"memberPaidCardDeductions"` // 付费会员抵现规则
}

type LevelDeductionSetting struct {
	Level      int64   `bson:"level"`      // 会员等级
	Type       string  `bson:"type"`       // 抵现类型，none 表示不可抵现，cash 表示可以用一定量积分抵消一定量的现金消费。
	Percentage float64 `bson:"percentage"` // 一笔订单中能用积分抵现百分比
	Value      uint64  `bson:"value"`      // 积分价值。每 Value 个积分可以抵现 1 元
}

type MemberPaidCardDeductionSetting struct {
	CardType   string  `bson:"cardType"`
	Type       string  `bson:"type"`       // 抵现类型，none 表示不可抵现，cash 表示可以用一定量积分抵消一定量的现金消费。
	Percentage float64 `bson:"percentage"` // 一笔订单中能用积分抵现百分比
	Value      uint64  `bson:"value"`      // 积分价值。每 Value 个积分可以抵现 1 元
}

func (m *MemberScoreSetting) Create(ctx context.Context) (err error) {
	defer func() {
		if r := recover(); r != nil {
			var ok bool
			err, ok = r.(error)
			if ok && !qmgo.IsDup(err) {
				panic(err)
			}

			err = errors.NewAlreadyExistsError("code")
		}
	}()

	m.Id = bson.NewObjectId()
	m.AccountId = util.GetAccountIdAsObjectId(ctx)
	m.CreatedAt = time.Now()
	m.UpdatedAt = time.Now()

	operatorId := core_util.GetUserId(ctx)
	if bson.IsObjectIdHex(operatorId) {
		m.UpdatedBy = bson.ObjectIdHex(operatorId)
	}

	_, err = extension.DBRepository.Insert(ctx, C_MEMBER_SCORE_SETTING, m)
	return
}

func (m *MemberScoreSetting) Update(ctx context.Context) error {
	m.UpdatedAt = time.Now()
	operatorId := core_util.GetUserId(ctx)
	if bson.IsObjectIdHex(operatorId) {
		m.UpdatedBy = bson.ObjectIdHex(operatorId)
	}

	selector := Common.GenDefaultConditionById(ctx, m.Id)
	updater := m.genUpdater()

	return extension.DBRepository.UpdateOne(ctx, C_MEMBER_SCORE_SETTING, selector, updater)
}

func (m MemberScoreSetting) genUpdater() bson.M {
	result := bson.M{}
	data, _ := bson.Marshal(m)
	// TODO 此处会忽略零值
	bson.Unmarshal(data, &result)

	// 删除不需要更新的那些字段
	delete(result, "_id")
	delete(result, "isDeleted")
	delete(result, "code")
	delete(result, "accountId")
	delete(result, "createdAt")

	if m.Code == C_SCORE_SETTING_CODE_LIMIT {
		if _, ok := result["receiveLimit"]; !ok {
			result["receiveLimit"] = bson.M{}
		}
		result["receiveLimit"].(bson.M)["type"] = m.ReceiveLimit.Type
		result["receiveLimit"].(bson.M)["isYearLimitationEnabled"] = m.ReceiveLimit.IsYearLimitationEnabled
		result["receiveLimit"].(bson.M)["yearLimit"] = m.ReceiveLimit.YearLimit
		result["receiveLimit"].(bson.M)["singleLimit"] = m.ReceiveLimit.SingleLimit
	}

	return bson.M{
		"$set": result,
	}
}

func (MemberScoreSetting) GetByCode(ctx context.Context, code string) (MemberScoreSetting, error) {
	result := MemberScoreSetting{}
	selector := Common.GenDefaultCondition(ctx)
	selector["code"] = code
	err := extension.DBRepository.FindOne(ctx, C_MEMBER_SCORE_SETTING, selector, &result)
	if err != nil {
		return MemberScoreSetting{}, err
	}
	return result, nil
}

func (MemberScoreSetting) GetByCodes(ctx context.Context, codes []string) ([]MemberScoreSetting, error) {
	result := []MemberScoreSetting{}
	selector := Common.GenDefaultCondition(ctx)
	selector["code"] = bson.M{"$in": codes}
	err := extension.DBRepository.FindAll(ctx, C_MEMBER_SCORE_SETTING, selector, []string{}, 0, &result)
	if err != nil {
		return []MemberScoreSetting{}, err
	}
	return result, nil
}

func (MemberScoreSetting) DeleteByCode(ctx context.Context, code string) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["code"] = code
	selector["isDefault"] = false
	updater := bson.M{"$set": bson.M{"isDeleted": true}}

	return extension.DBRepository.UpdateOne(ctx, C_MEMBER_SCORE_SETTING, selector, updater)
}

func (MemberScoreSetting) FindByPagination(ctx context.Context, page extension.PagingCondition) ([]MemberScoreSetting, int, error) {
	result := []MemberScoreSetting{}
	total, err := extension.DBRepository.FindByPagination(ctx, C_MEMBER_SCORE_SETTING, page, &result)
	if err != nil {
		return nil, 0, err
	}

	return result, total, nil
}

func GetMemberScoreSettingByCode(settings []MemberScoreSetting, code string) (MemberScoreSetting, bool) {
	for _, setting := range settings {
		if setting.Code == code {
			return setting, true
		}
	}

	return MemberScoreSetting{}, false
}

func (r ReceiveLimitSetting) IsLimitSettingEnabled(ctx context.Context) bool {
	return r.IsYearLimitationEnabled || r.Type != TYPE_NONE
}

func (m *MemberScoreSetting) GetCAPSetting() *CAPSetting {
	return &CAPSetting{IsYearLimitationEnabled: m.ReceiveLimit.IsYearLimitationEnabled, YearLimit: m.ReceiveLimit.YearLimit}
}

func (c *CAPSetting) IsCAPEnabled() bool {
	if c == nil {
		return false
	}
	return c.IsYearLimitationEnabled
}

func (p ProtectionSetting) GetRuleByStoreId(storeId string) ProtectionSettingRule {
	for _, rule := range p.Rules {
		if util.StrInArray(storeId, &rule.OrderStoreIds) {
			return rule
		}
	}
	return ProtectionSettingRule{
		Type:     p.Type,
		Duration: p.Duration,
	}
}
