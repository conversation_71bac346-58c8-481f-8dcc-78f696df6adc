package model

import (
	"context"
	"mairpc/core/errors"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/algorithm"
	"mairpc/core/validators"
	"mairpc/service/share/component"
	"mairpc/service/share/util"

	"github.com/spf13/cast"
)

const (
	TASK_SETTING_TYPE_PERFECT_INFORMATION = "perfectInformation"
	TASK_SETTING_TYPE_AMOUNT              = "amount"
	TASK_SETTING_TYPE_TIMES               = "times"
	TASK_SETTING_TYPE_MULTI_AMOUNT        = "multiAmount"
)

type TaskSettings struct {
	// 完善指定个人信息后发奖，数组中记录客户属性的 name，不回滚
	PerfectInformation []string `bson:"perfectInformation,omitempty"`
	// 指标。每次推进任务时如果 amount 是 memberTask 中定的 amount 的整数倍，
	// 就会发整数倍的奖励。用它可以实现“一笔订单中每消费XX元奖励XXX”或是
	// “一次任务中每跑XX米奖励XXX”这种类型的任务，支持回滚
	Amount uint64 `bson:"amount,omitempty"`
	// 累计值。每次推进任务时都能累计指定数量，当累计的数量大于等于 times 时
	// 就会发一次奖，发完奖以后会把累计的数量减 times 次。用它可以实现
	// “每完成10次打卡奖励XXX”这种类型的任务。支持回滚
	Times uint64 `bson:"times,omitempty"`
	// 仅用于积分奖励，且不处理额外积分奖励
	MultiAmount uint64 `bson:"multiAmount,omitempty"`
}

type MultiAmountSetting struct {
	Amount           uint64     `bson:"amount"`
	Reward           TaskReward `bson:"reward"`
	Values           []string   `bson:"values"`
	AmountField      string     `bson:"amountField"`
	AmountArrayField string     `bson:"amountArrayField"`
	AmountValueField string     `bson:"amountValueField"`

	NoRewardSetting *MemberScoreSetting `bson:"-"`
}

type MultiAmountSettingsOperator []MultiAmountSetting

// 排序，将 other 的规则排在最后
func (m MultiAmountSettingsOperator) SortOtherRuleToEnd() []MultiAmountSetting {
	var (
		other         MultiAmountSetting
		multiSettings []MultiAmountSetting
	)
	for _, rule := range m {
		if rule.Reward.TaskRewardId == "other" {
			other = rule
		} else {
			multiSettings = append(multiSettings, rule)
		}
	}
	multiSettings = append(multiSettings, other)
	return multiSettings
}

func (m MultiAmountSettingsOperator) GetRewards() []TaskReward {
	var (
		rewards []TaskReward
	)
	for _, rule := range m {
		rewards = append(rewards, rule.Reward)
	}
	return rewards
}

// 从 eventProperties 中的 amountArray 筛选出需要累计的 amount
func (m MultiAmountSetting) GetAmountByFilterArray(ctx context.Context, taskInfo *TaskInfo, rewardedValues *algorithm.Set) (uint64, error) {
	amountArray, ok := taskInfo.EventProperties[m.AmountArrayField].([]interface{})
	if !ok {
		return 0, errors.NewInvalidArgumentErrorWithMessage("properties", "Invalid amount array.")
	}
	var itemAmount = uint64(0)
	// 遍历计数数组中的每个元素满足哪个每满规则
	for _, item := range amountArray {
		arrayItem := new(map[string]interface{})
		err := core_util.CopyByJson(item, &arrayItem)
		if err != nil {
			return 0, err
		}

		var (
			valueId               = cast.ToString((*arrayItem)[m.AmountValueField])
			valueAmount           = cast.ToUint64((*arrayItem)[m.AmountField])
			platform              = cast.ToString((*arrayItem)["platform"])
			noRewardValue         = cast.ToString((*arrayItem)[m.NoRewardSetting.NoScoreReward.ProductSetting.AmountValueField])
			price                 = cast.ToInt64((*arrayItem)["price"])
			payAmount             = cast.ToInt64((*arrayItem)["payAmount"])
			payRatio      float64 = -1
		)

		if platform == "" {
			platform = cast.ToString(taskInfo.EventProperties["platform"])
		}

		if price > 0 {
			payRatio = util.DivideFloat(float64(payAmount), float64(price))
		}

		ignore := m.ShouldIgnore(noRewardValue, platform, taskInfo.EventId, taskInfo.Task.Code, taskInfo.Task.Business, cast.ToString((*arrayItem)["refundStatus"]), payRatio)
		if ignore {
			continue
		}

		if len(m.Values) == 0 {
			// 需要 其它规则 排在最后
			var inserted bool
			rewardedValues, inserted = rewardedValues.TryInsert(valueId)
			if inserted || valueId == "" {
				itemAmount += valueAmount
			}
		} else if core_util.StrInArray(valueId, &m.Values) {
			itemAmount += valueAmount
			rewardedValues = rewardedValues.Insert(valueId)
		}
	}
	if util.StrInArray(taskInfo.EventId, &[]string{
		component.MAIEVENT_REFUND_ORDER,
		component.MAIEVENT_REFUND_PRODUCT,
	}) {
		// 退款要支持仅退邮费或纠纷补偿的情况，itemAmount 取 itemAmount 和外层 amountField 的较小者
		if taskInfo.Task != nil {
			rollbacker, ok := taskInfo.Task.GetEventRollbacker(taskInfo.EventId)
			if ok {
				outerAmount, err := cast.ToUint64E(taskInfo.EventProperties[rollbacker.AmountField])
				if err == nil && outerAmount > 0 {
					itemAmount = util.MinUint64(itemAmount, outerAmount)
				}
			}
		}
		reward := taskInfo.GetMemberTaskReward(ctx)
		if reward != nil && reward.RewardInfo.Amount >= itemAmount+reward.CountTotalRollbackAmount() {
			remainAmount := reward.RewardInfo.Amount - itemAmount - reward.CountTotalRollbackAmount()
			// 如果已不足最低发奖限制，那么全部退掉
			if remainAmount < m.Amount {
				itemAmount = reward.RewardInfo.Amount
			}
		}
	}
	return itemAmount, nil
}

// todo 简单适配后续优化
func (m MultiAmountSetting) GetAmountByFilterArrayV2(ctx context.Context, properties map[string]any, eventId string, task *MemberTask, rewardedValues *algorithm.Set, reward *MemberTaskReward) (uint64, error) {
	amountArray, ok := properties[m.AmountArrayField].([]interface{})
	if !ok {
		return 0, errors.NewInvalidArgumentErrorWithMessage("properties", "Invalid amount array.")
	}

	var itemAmount = uint64(0)
	// 遍历计数数组中的每个元素满足哪个每满规则
	for _, item := range amountArray {
		arrayItem := new(map[string]interface{})
		err := core_util.CopyByJson(item, &arrayItem)
		if err != nil {
			return 0, err
		}

		var (
			valueId               = cast.ToString((*arrayItem)[m.AmountValueField])
			valueAmount           = cast.ToUint64((*arrayItem)[m.AmountField])
			platform              = cast.ToString((*arrayItem)["platform"])
			noRewardValue         = cast.ToString((*arrayItem)[m.NoRewardSetting.NoScoreReward.ProductSetting.AmountValueField])
			price                 = cast.ToInt64((*arrayItem)["price"])
			payAmount             = cast.ToInt64((*arrayItem)["payAmount"])
			payRatio      float64 = -1
		)

		if platform == "" {
			platform = cast.ToString(properties["platform"])
		}

		if price > 0 {
			payRatio = util.DivideFloat(float64(payAmount), float64(price))
		}

		ignore := m.ShouldIgnore(noRewardValue, platform, eventId, task.Code, task.Business, cast.ToString((*arrayItem)["refundStatus"]), payRatio)
		if ignore {
			continue
		}

		if len(m.Values) == 0 {
			// 需要 其它规则 排在最后
			var inserted bool
			rewardedValues, inserted = rewardedValues.TryInsert(valueId)
			if inserted || valueId == "" {
				itemAmount += valueAmount
			}
		} else if core_util.StrInArray(valueId, &m.Values) {
			itemAmount += valueAmount
			rewardedValues = rewardedValues.Insert(valueId)
		}
	}
	// 暂时只对订单退款做处理
	if util.StrInArray(eventId, &[]string{
		component.MAIEVENT_REFUND_ORDER,
		component.MAIEVENT_REFUND_PRODUCT,
	}) {
		// 退款要支持仅退邮费或纠纷补偿的情况，itemAmount 取 itemAmount 和外层 amountField 的较小者
		rollbacker, ok := task.GetEventRollbacker(eventId)
		if ok {
			outerAmount, err := cast.ToUint64E(properties[rollbacker.AmountField])
			if err == nil && outerAmount > 0 {
				itemAmount = util.MinUint64(itemAmount, outerAmount)
			}
		}
		if reward != nil && reward.RewardInfo.Amount >= itemAmount+reward.CountTotalRollbackAmount() {
			remainAmount := reward.RewardInfo.Amount - itemAmount - reward.CountTotalRollbackAmount()
			// 如果已不足最低发奖限制，那么全部退掉
			if remainAmount < m.Amount {
				itemAmount = reward.RewardInfo.Amount
			}
		}
	}
	return itemAmount, nil
}

// 判断当前 sku 是否被屏蔽，是否已退款，需要跳过
func (m MultiAmountSetting) ShouldIgnore(noRewardSku, platform, eventId, code, business, refundStatus string, payRatio float64) bool {
	// 回滚的元素跳过统计
	if eventId == component.MAIEVENT_ORDER_COMPLETED && (NoRewardSetting{}).IsProductRefund(refundStatus) {
		return true
	}

	if m.NoRewardSetting != nil {
		// 开启积分屏蔽、设置了屏蔽商品、默认积分规则需要剔除屏蔽商品
		if m.NoRewardSetting.NoScoreReward.ShouldNoRewardByProduct(noRewardSku, platform, code, business, payRatio) {
			return true
		}
	}

	return false
}

func (t TaskSettings) GetSettingType() string {
	if len(t.PerfectInformation) > 0 {
		return TASK_SETTING_TYPE_PERFECT_INFORMATION
	}

	if t.Amount > 0 {
		return TASK_SETTING_TYPE_AMOUNT
	}

	if t.Times > 0 {
		return TASK_SETTING_TYPE_TIMES
	}

	if t.MultiAmount > 0 {
		return TASK_SETTING_TYPE_MULTI_AMOUNT
	}
	return ""
}

func (m MemberTask) GetSettingType() string {
	return m.Settings.GetSettingType()
}

type multiAmountOption func(opts *multiAmountOptions)

type multiAmountOptions struct {
	needPreventAmount  bool
	needMultiAmount    bool
	noRewardSetting    *NoRewardSetting
	multiAmountSetting MultiAmountSetting
	rewardedValues     *algorithm.Set `valid:"Required"`

	eventId         string         `valid:"Required"`
	eventProperties map[string]any `valid:"Required"`
	task            *MemberTask    `valid:"Required"`
}

func WithCalMultiItemAmount(multiAmountSetting MultiAmountSetting, rewardedValues *algorithm.Set) multiAmountOption {
	return func(opts *multiAmountOptions) {
		opts.needMultiAmount = true
		opts.multiAmountSetting = multiAmountSetting
		opts.rewardedValues = rewardedValues
	}
}

func WithTaskOption(task *MemberTask) multiAmountOption {
	return func(opts *multiAmountOptions) {
		opts.task = task
	}
}

func WithEvent(eventId string, eventProperties map[string]any) multiAmountOption {
	return func(opts *multiAmountOptions) {
		opts.eventId = eventId
		opts.eventProperties = eventProperties
	}
}

func WithCalPreventAmount(noRewardSetting *NoRewardSetting) multiAmountOption {
	return func(opts *multiAmountOptions) {
		opts.needPreventAmount = true
		opts.noRewardSetting = noRewardSetting
	}
}

func (m multiAmountOptions) valid() (bool, error) {
	if err := validators.ValidateRequest(m); err != nil {
		return false, err
	}
	return true, nil
}

func loadMultiAmountOptions(options ...multiAmountOption) *multiAmountOptions {
	opts := new(multiAmountOptions)
	for _, option := range options {
		option(opts)
	}
	return opts
}

func GetAmountByMultiSetting(ctx context.Context, multiAmountOptions ...multiAmountOption) (uint64, error) {
	opts := loadMultiAmountOptions(multiAmountOptions...)
	if valid, err := opts.valid(); !valid {
		return 0, err
	}
	var (
		preventAmount uint64

		amountArrayField string
		amountValueField string
		amountField      string
	)

	switch {
	case opts.needPreventAmount:
		if opts.noRewardSetting == nil {
			return 0, nil
		}
		amountArrayField = opts.noRewardSetting.ProductSetting.AmountArrayField
		amountValueField = opts.noRewardSetting.ProductSetting.AmountValueField
		amountField = opts.noRewardSetting.ProductSetting.AmountField
	case opts.needMultiAmount:
		amountArrayField = opts.multiAmountSetting.AmountArrayField
		amountValueField = opts.multiAmountSetting.AmountValueField
		amountField = opts.multiAmountSetting.AmountField
	default:
		return 0, nil
	}

	amountArray := opts.eventProperties[amountArrayField].([]any)

	var itemAmount = uint64(0)
	// 遍历计数数组中的每个元素满足哪个每满规则
	for _, item := range amountArray {
		arrayItem := new(map[string]interface{})

		if core_util.CopyByJson(item, &arrayItem) != nil {
			log.Error(ctx, "can not copy by event properties", log.Fields{"properties": opts.eventProperties})
		}

		var (
			valueId               = cast.ToString((*arrayItem)[amountValueField]) // 订单中唯一标识的字段值（例如 sku、outTradeId）
			valueAmount           = cast.ToUint64((*arrayItem)[amountField])      // 订单中屏蔽的金额（例如 sku abc 的金额是 100 分）
			platform              = cast.ToString((*arrayItem)["platform"])       // 订单中屏蔽的平台（例如屏蔽 mai-retail 中的 sku）
			refundStatus          = cast.ToString((*arrayItem)["refundStatus"])   // 商品当前状态
			price                 = cast.ToInt64((*arrayItem)["price"])
			payAmount             = cast.ToInt64((*arrayItem)["payAmount"])
			payRatio      float64 = -1
			noRewardValue string
		)

		if platform == "" {
			platform = cast.ToString(opts.eventProperties["platform"])
		}

		if price > 0 {
			payRatio = util.DivideFloat(float64(payAmount), float64(price))
		}

		// 回滚的元素跳过统计
		if opts.eventId == component.MAIEVENT_ORDER_COMPLETED && (NoRewardSetting{}).IsProductRefund(refundStatus) {
			continue
		}

		// 如果开启了屏蔽商品，记录本次订单所有待屏蔽字段的集合
		if opts.noRewardSetting != nil {
			noRewardValue = cast.ToString((*arrayItem)[opts.noRewardSetting.ProductSetting.AmountValueField])
			// 开启积分屏蔽、设置了屏蔽商品、默认积分规则需要剔除屏蔽商品
			if opts.noRewardSetting.ShouldNoRewardByProduct(noRewardValue, platform, opts.task.Code, opts.task.Business, payRatio) {
				preventAmount += valueAmount
				continue
			}
		}

		if opts.needMultiAmount {
			if len(opts.multiAmountSetting.Values) == 0 {
				// 需要 其它规则 排在最后
				var inserted bool
				opts.rewardedValues, inserted = opts.rewardedValues.TryInsert(valueId)
				if inserted || valueId == "" {
					itemAmount += valueAmount
				}
			} else if core_util.StrInArray(valueId, &opts.multiAmountSetting.Values) {
				itemAmount += valueAmount
				opts.rewardedValues = opts.rewardedValues.Insert(valueId)
			}
		}

	}

	switch {
	case opts.needPreventAmount:
		return preventAmount, nil
	case opts.needMultiAmount:
		return itemAmount, nil
	default:
		return 0, nil
	}
}
