package model

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	core_err "errors"
	"fmt"
	"math"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"

	mairpc "mairpc/core/client"
	core_codes "mairpc/core/codes"
	core_component "mairpc/core/component"
	"mairpc/core/component/jingdong"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	oauth_component "mairpc/openapi/oauth/component"
	"mairpc/proto/account"
	"mairpc/proto/common/request"
	"mairpc/proto/common/types"
	"mairpc/proto/member"
	member_model "mairpc/service/ec/model/member"
	"mairpc/service/member/codes"
	member_share "mairpc/service/member/share"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"

	"github.com/asaskevich/govalidator"
	"github.com/qiniu/qmgo"
	"github.com/spf13/cast"
)

const (
	C_MEMBER                 = "member" // member mongo collection
	USER_ID_LENGTH           = 24
	OPEN_ID_LENGTH           = 28
	FIRST_CARD_NUMBER        = ********
	OMNI_MAX_CARD_NUMBER     = "omni_max_card_number"
	BLOCKEDSTATUS_NORMAL     = 0
	BLOCKEDSTATUS_SUSPICIOUS = 1
	BLOCKEDSTATUS_BLOCKED    = 2
	//point changed per minute
	SUSPICIOUS_POINT_CHANGED_TIMES = 3

	FEMALE  = "female"
	MALE    = "male"
	UNKNOWN = "unknown"

	EVENT_TYPE_MEMBER_INFO_LOG_ADDED = "member_info_log_added"

	MESSAGE_CHANNEL_TYPE_ONLINE  = "online"
	MESSAGE_CHANNEL_TYPE_OFFLINE = "offline"

	//trigger event type by blocked status updated
	EVENT_BLOCKEDSTATUS_SUSPICIOUS = "member_marked_suspicious"
	EVENT_BLOCKEDSTATUS_BLOCKED    = "member_blocked"
	EVENT_BLOCKEDSTATUS_UNBLOCKED  = "member_unblocked"

	ADD_PHONE_TO_BLACKLIST_REASON = "member is blocked"

	DEFAULT_AVATAR = "/image_hover_default_avatar.png"

	WX_IMAGE_PATTERN = "qlogo.cn"

	SUBSCRIBE_SOURCE_OAUTH = "oauth"

	YOUZAN_DECREASE_SCORE_DESC_BY_REFUNDING = "退款中，积分冻结"
	YOUZAN_DECREASE_SCORE_DESC_BY_MANUAL    = "扣除本店积分"

	LEVEL_UP_MEMBER_GROWTH_REASON_CLEAR  = "会员等级变更，清零成长值。"
	LEVEL_UP_MEMBER_GROWTH_REASON_REDUCE = "会员等级变更，扣除成长值。"

	ALIGN_SCORE_AND_GROWTH_AFTER_MEMBER_MERGED_BUSINESS = "align_score_and_growth_after_member_merged"

	AUDIT_LOG_OBJECT_MEMBER                = "客户"
	AUDIT_LOG_EXPORT_MEMBERS               = "导出客户 「%d」 个 「%s」"
	AUDIT_LOG_EXPORT_MEMBERS_BY_STATIC_TAG = "根据静态标签导出客户 「%d」 个 「%s」"
)

// NEVER update CMember
var (
	CMember = &Member{}

	EditableSocialFields = []string{
		"unionId", "openId", "avatar", "nickname", "gender", "country", "province", "city",
		"authorizeTime", "firstAuthorizeTime", "subscribeTime", "unsubscribeTime", "language",
		"extra", "subscribed", "isOriginal",
	}

	oauthEventSubTypeMap = map[string]string{
		oauth_component.WeappChannelType: component.MAIEVENT_WECHATAPP_OAUTH,
	}
)

type Property struct {
	Id           bson.ObjectId `bson:"id,omitempty"`
	Name         string        `bson:"name"`
	Value        interface{}   `bson:"value"`
	LowerValue   string        `bson:"lowerValue,omitempty"`   // only used for unique input property to store lowercased value
	AddressCache bson.M        `bson:"addressCache,omitempty"` // only used for location property
}

type Location struct {
	City     string `json:"city"`
	Country  string `json:"country"`
	Province string `json:"province"`
	District string `json:"district"`
	Detail   string `json:"detail"`
}

type Social struct {
	Channel             string    `bson:"channel"`
	OpenId              string    `bson:"openId"`
	UnionId             string    `bson:"unionId"`
	Origin              string    `bson:"origin"`
	OriginScene         string    `bson:"originScene"`
	FirstOriginScene    string    `bson:"firstOriginScene"`
	IsOriginal          bool      `bson:"isOriginal"`
	ChannelName         string    `bson:"channelName"`
	Nickname            string    `bson:"nickname"`
	Gender              string    `bson:"gender"`
	City                string    `bson:"city"`
	Province            string    `bson:"province"`
	Country             string    `bson:"country"`
	Avatar              string    `bson:"avatar"`
	Language            string    `bson:"language"`
	Authorized          bool      `bson:"authorized"`
	FirstAuthorizeTime  time.Time `bson:"firstAuthorizeTime,omitempty"`
	AuthorizeTime       time.Time `bson:"authorizeTime,omitempty"`
	Subscribed          bool      `bson:"subscribed"`
	SubscribeTime       time.Time `bson:"subscribeTime,omitempty"`
	UnsubscribeTime     time.Time `bson:"unsubscribeTime,omitempty"`
	FirstSubscribeTime  time.Time `bson:"firstSubscribeTime,omitempty"`
	Extra               string    `bson:"extra,omitempty"`
	IsActivationChannel bool      `bson:"isActivationChannel"`
}

type WxCard struct {
	Id          string `bson:"id"`
	Code        string `bson:"code"`
	Channel     string `bson:"channel"`
	ActivatedAt int64  `bson:"activatedAt"`
	SceneId     string `bson:"sceneId"`
}

// Raw structure got from db
type Member struct {
	Id                     bson.ObjectId `bson:"_id,omitempty"`
	Properties             []Property    `bson:"properties"`
	Avatar                 string        `bson:"avatar"`
	Location               Location      `bson:"location"`
	Growth                 int64         `bson:"growth"`
	Score                  int64         `bson:"score"`
	Level                  int64         `bson:"level"`
	LevelStartedAt         time.Time     `bson:"levelStartedAt,omitempty"`
	ActivatedAt            time.Time     `bson:"activatedAt,omitempty"`
	CreatedAt              time.Time     `bson:"createdAt"`
	UpdatedAt              time.Time     `bson:"updatedAt"`
	Socials                []Social      `bson:"socials"`
	CardId                 bson.ObjectId `bson:"cardId,omitempty"`
	CardNumber             string        `bson:"cardNumber"`
	CardProvideTime        time.Time     `bson:"cardProvideTime,omitempty"`
	PaidCards              []PaidCard    `bson:"paidCards,omitempty"`
	IsDisabled             bool          `bson:"isDisabled"`
	IsDeleted              bool          `bson:"isDeleted"`
	IsActivated            bool          `bson:"isActivated"`
	AccountId              bson.ObjectId `bson:"accountId"`
	TotalScore             uint64        `bson:"totalScore"`
	TotalCostScore         uint64        `bson:"totalCostScore"`
	AnnualAccumulatedScore uint64        `bson:"annualAccumulatedScore"`
	AnnualCostScore        uint64        `bson:"annualCostScore"`
	Tags                   []string      `bson:"tags"`
	Birth                  uint64        `bson:"birth"`
	Phone                  string        `bson:"phone"`
	BlockedStatus          int           `bson:"blockedStatus"`
	BlockedStatusUpdatedAt time.Time     `bson:"blockedStatusUpdatedAt,omitempty"`
	QrcodeViewed           bool          `bson:"qrcodeViewed,omitempty"`
	Remarks                string        `bson:"remarks,omitempty"`
	BaseModel              BaseModel     `bson:"basemodel,omitempty"`
	Nickname               string        `bson:"nickname,omitempty"`
	WxCard                 *WxCard       `bson:"wxCard,omitempty"`
	MemberValue            MemberValue   `bson:"memberValue"`
	Source                 string        `bson:"source"`
	ActivationSource       string        `bson:"activationSource"`
	IdUpdatedAt            bson.ObjectId `bson:"idUpdatedAt,omitempty"`

	// 用于某些不需要更新 isActivated 的场景使 member.updateMember 方法不更新 isActivated，减少并发导致激活状态错误
	IgnoreIsActivated bool `bson:"-"`
	// 用于某些不需要更新 socials 的场景使 member.updateMember 方法不更新 socials，减少并发导致 socials 被覆盖的情况
	IgnoreSocials bool `bson:"-"`
	// 用于会员合并场景下，合并后的 member 的等级来源 memberId
	LevelFrom bson.ObjectId `bson:"-"`
}

type MemberValue struct {
	Stage             string `bson:"stage"`
	InformationGrades int64  `bson:"informationGrades"`
	Engagement        int64  `bson:"engagement"`
	Grades            int64  `bson:"grades"`
}

type JobArgs struct {
	AccountId     string   `json:"accountId"`
	OtherMemberId []string `json:"otherMemberIds"`
	MainMemberId  string   `json:"mainMemberId"`
	OperatorId    string   `json:"operatorId"`
	Remark        string   `json:"remark"`
}

type StaffInfo struct {
	StoreId   bson.ObjectId
	StoreName string
	StaffId   bson.ObjectId
	StaffName string
}

type EventMeta struct {
	Utm              *types.Utm
	StaffInfo        *StaffInfo
	PromoterMemberId string
}

type PaidCard struct {
	StartedAt  time.Time     `bson:"startedAt"`
	ExpireAt   time.Time     `bson:"expireAt"`
	CardId     bson.ObjectId `bson:"cardId"`
	CardType   string        `bson:"cardType"`
	CardNumber string        `bson:"cardNumber"`
}

// generateDefaultCondition 生成默认查询条件
func generateDefaultCondition(accountId string) bson.M {
	return bson.M{
		"accountId": bson.ObjectIdHex(accountId),
		"isDeleted": false,
	}
}

func addDefaultUpdateInfo(updator bson.M) bson.M {
	var setter = bson.M{}
	var ok bool
	if setter, ok = updator["$set"].(bson.M); ok {
		setter["idUpdatedAt"] = bson.NewObjectId()
		setter["updatedAt"] = time.Now()
		updator["$set"] = setter
	} else {
		updator["$set"] = bson.M{
			"idUpdatedAt": bson.NewObjectId(),
			"updatedAt":   time.Now(),
		}
	}
	return updator
}

func (*Member) UpdateOne(ctx context.Context, selector, updator bson.M) error {
	updator = addDefaultUpdateInfo(updator)
	return extension.DBRepository.UpdateOne(ctx, C_MEMBER, selector, updator)
}

func (*Member) FindAndApply(ctx context.Context, selector bson.M, change qmgo.Change) (*Member, error) {
	member := &Member{}
	change.Update = addDefaultUpdateInfo(change.Update.(bson.M))
	err := extension.DBRepository.FindAndApply(ctx, C_MEMBER, selector, []string{}, change, member)
	if err != nil {
		return nil, err
	}
	return member, nil
}

// SetLevel 设置会员的等级，根据后台的设置随着消费或者互动行为升级或者降级
func (self *Member) SetLevel(ctx context.Context, level uint64) error {
	return self.UpdateOne(ctx, bson.M{"_id": self.Id}, bson.M{"$set": bson.M{"level": level, "levelStartedAt": time.Now()}})
}

// SetLevelAndLevelStartedAt 设置会员的等级和等级生效日，根据后台的设置随着消费或者互动行为升级或者降级
func (self *Member) SetLevelAndLevelStartedAt(ctx context.Context, level uint64, levelStartedAt time.Time) error {
	return self.UpdateOne(ctx, bson.M{"_id": self.Id}, bson.M{"$set": bson.M{"level": level, "levelStartedAt": levelStartedAt}})
}

// IncAnnualAccumulatedScore 修改客户的年度消费积分，每次消费积分都会修改，年度消费积分会影响部分权益
func (self *Member) IncAnnualAccumulatedScore(ctx context.Context, annualAccumulatedScore uint64) error {
	return self.UpdateOne(ctx, bson.M{"_id": self.Id}, bson.M{
		"$set": bson.M{"updatedAt": time.Now()},
		"$inc": bson.M{"annualAccumulatedScore": annualAccumulatedScore},
	})
}

// UpdateAll 根据指定条件更新客户信息
func (self *Member) UpdateAll(ctx context.Context, selector, updator bson.M) (int, error) {
	return self.UpdateAllWithDisableUpdatedAt(ctx, selector, updator, false)
}

// UpdateAllWithDisableUpdatedAt 根据指定条件更新客户信息，不追加修改日期等信息
func (*Member) UpdateAllWithDisableUpdatedAt(ctx context.Context, selector, updater bson.M, disableUpdatedAt bool) (int, error) {
	if !disableUpdatedAt {
		updater = addDefaultUpdateInfo(updater)
	}
	return extension.DBRepository.UpdateAll(ctx, C_MEMBER, selector, updater)
}

// GetById 通过唯一标识查询客户详情并解密加密信息
func (*Member) GetById(ctx context.Context, accountId string, id string) *Member {
	// Query mongo
	defaultCondition := generateDefaultCondition(accountId)
	condition := appendQueryCondition(defaultCondition, "_id", bson.ObjectIdHex(id))

	pMember := new(Member)
	extension.DBRepository.FindOne(ctx, C_MEMBER, condition, pMember)
	if pMember.Id.Hex() != "" {
		DecryptMember(ctx, pMember)
		return pMember
	}
	return nil
}

// GetByIdWithoutDecrypt 通过唯一标识查询客户详情，不解密加密信息
func (*Member) GetByIdWithoutDecrypt(ctx context.Context, accountId string, id string) *Member {
	// Query mongo
	defaultCondition := generateDefaultCondition(accountId)
	condition := appendQueryCondition(defaultCondition, "_id", bson.ObjectIdHex(id))

	pMember := new(Member)
	extension.DBRepository.FindOne(ctx, C_MEMBER, condition, pMember)
	if pMember.Id.Hex() != "" {
		return pMember
	}
	return nil
}

func (*Member) Iterate(ctx context.Context, condition bson.M, sorter []string) (extension.IterWrapper, error) {
	return extension.DBRepository.Iterate(ctx, C_MEMBER, condition, sorter)
}

// GetByIdCombination 同时使用唯一标识、手机号、微信渠道标识等信息查询客户，任意条件命中即可
func (self *Member) GetByIdCombination(ctx context.Context, accountId, id string) *Member {
	if id == "" {
		return nil
	}
	condition := GenerateIdQueryCondition(ctx, id)
	return self.GetByCondition(ctx, condition)
}

// GetByCondition 通过指定条件获取客户信息
func (*Member) GetByCondition(ctx context.Context, condition bson.M) *Member {

	member := new(Member)

	// In order to use the index, fields can not be added outside of $or
	if _, ok := condition["$or"]; !ok {
		condition["isDeleted"] = false
	}

	extension.DBRepository.FindOne(ctx, C_MEMBER, condition, member)

	if member.Id.Hex() != "" {
		DecryptMember(ctx, member)
		return member
	}
	return nil
}

// GetOneByConditionWithFields 通过指定条件搜索一个客户并且只返回部分信息
func (self *Member) GetOneByConditionWithFields(ctx context.Context, condition bson.M, fields bson.M, sortor []string) *Member {
	// In order to use the index, fields can not be added outside of $or
	if _, ok := condition["$or"]; !ok {
		condition["isDeleted"] = false
	}

	members, err := self.FindAllWithFields(ctx, condition, fields, sortor, 1)
	if err != nil || len(members) == 0 {
		return nil
	}
	return &members[0]
}

// generateAdditionalCondition 在查询条件中附加额外条件
func generateAdditionalCondition(condition bson.M, excludedMemberIds []bson.ObjectId) bson.M {
	if len(excludedMemberIds) > 0 {
		condition["_id"] = bson.M{
			"$nin": excludedMemberIds,
		}
	}

	return condition
}

// GenerateIdQueryCondition 此方法返回通过手机号、渠道唯一标识符或者客户唯一标识的复合条件
func GenerateIdQueryCondition(ctx context.Context, id string) bson.M {
	defaultCondition := Common.GenDefaultCondition(ctx)
	condition := bson.M{}
	if bson.IsObjectIdHex(id) {
		condition = appendQueryCondition(defaultCondition, "_id", bson.ObjectIdHex(id))
	} else {
		findByOpenId := appendQueryCondition(defaultCondition, "socials.openId", id)
		findByUnionId := appendQueryCondition(defaultCondition, "socials.unionId", id)
		findByPhone := appendQueryCondition(defaultCondition, "phone", GetValueConditionWithEncrypted(ctx, id, nil))

		condition["$or"] = []bson.M{
			findByPhone,
			findByOpenId,
			findByUnionId,
		}
	}
	return condition
}

// appendQueryCondition 此方法在不修改原始条件的基础上返回新的查询条件并追加新的信息
func appendQueryCondition(condition bson.M, field string, value interface{}) bson.M {
	newCondition := bson.M{field: value}
	util.ExtendMap(newCondition, condition)
	return newCondition
}

// GetOriginalSocial 获取客户的来源渠道，客户的所有渠道中只会有一个来源渠道，即客户初次注册的渠道
func (self *Member) GetOriginalSocial() *Social {
	var originalSocial *Social
	for _, social := range self.Socials {
		if social.IsOriginal {
			return &social
		}
	}
	return originalSocial
}

// Block 将一个客户拉入黑名单，黑名单中的客户无法使用积分、成长值等权益
func (self *Member) Block() {
	self.BlockedStatus = BLOCKEDSTATUS_BLOCKED
}

// Isblocked 判断一个客户是否在黑名单中，如果在黑名单中就对一些权益作出限制
func (self *Member) Isblocked() bool {
	return self.BlockedStatus == BLOCKEDSTATUS_BLOCKED
}

// Issuspicious 判断客户是否在可疑名单中，可疑名单中的客户积分等权益没有限制，但是可能会进入黑名单
func (self *Member) Issuspicious() bool {
	return self.BlockedStatus == BLOCKEDSTATUS_SUSPICIOUS
}

// Isnormal 判断客户当前状态是否正常，正常情况下积分、成长值等权益正常
func (self *Member) Isnormal() bool {
	return self.BlockedStatus == BLOCKEDSTATUS_NORMAL
}

// GetProperty 获取客户身上的某一个属性的属性值，可能是手机号、累计消费金额等
func (self Member) GetProperty(propertyName string) *Property {
	for _, property := range self.Properties {
		if property.Name == propertyName {
			return &property
		}
	}

	return nil
}

// GetName 获取客户的昵称，支持脱敏
func (self Member) GetName(desensitize bool) string {
	name := self.GetProperty(DEFAULT_PROPERTY_NAME)
	if name == nil {
		return ""
	}

	if desensitize {
		return util.DesensitizeName(cast.ToString(name.Value))
	}

	return cast.ToString(name.Value)
}

// GetPhone 获取客户的手机号，支持脱敏
func (self Member) GetPhone(desensitize bool) string {
	phone := self.GetProperty(DEFAULT_PROPERTY_PHONE)
	if phone == nil {
		return ""
	}

	if desensitize {
		return util.DesenitizePhone(cast.ToString(phone.Value))
	}

	return cast.ToString(phone.Value)
}

// Activate 将当前客户激活，同时标记激活渠道信息，不允许激活黑名单客户，激活后发出事件和激活的奖励
func (self *Member) Activate(ctx context.Context, channelInfo *ChannelInfo, eventMeta *EventMeta) error {
	if self.IsActivated {
		return nil
	}
	if channelInfo == nil {
		channelInfo = &ChannelInfo{}
	}

	if self.BlockedStatus == BLOCKEDSTATUS_BLOCKED {
		return codes.NewError(codes.MemberBlocked)
	}

	timeNow := time.Now()

	self.ActivatedAt = timeNow
	self.CardProvideTime = timeNow
	self.IsActivated = true

	self.Update(ctx, []MemberProperty{}, []bson.ObjectId{})

	self.AfterActivated(ctx, *channelInfo, eventMeta)

	RewardByRuleName(ctx, self, RULE_FIRST_CARD, channelInfo)

	return nil
}

func getMemberIds(members []Member) []bson.ObjectId {
	memberIds := []bson.ObjectId{}
	for _, member := range members {
		memberIds = append(memberIds, member.Id)
	}

	return memberIds
}

func (*Member) GetAllByCondition(ctx context.Context, condition bson.M, sorter []string, limit int) []Member {
	var members []Member
	// In order to use the index, fields can not be added outside of $or
	if _, ok := condition["$or"]; !ok {
		condition["isDeleted"] = false
	}
	extension.DBRepository.FindAll(ctx, C_MEMBER, condition, sorter, limit, &members)
	DecryptMembers(ctx, members)
	return members
}

func (*Member) GetAllByConditionWithoutDecrypt(ctx context.Context, condition bson.M, sorter []string, limit int) []Member {
	var members []Member
	// In order to use the index, fields can not be added outside of $or
	if _, ok := condition["$or"]; !ok {
		condition["isDeleted"] = false
	}
	extension.DBRepository.FindAll(ctx, C_MEMBER, condition, sorter, limit, &members)
	return members
}

func (*Member) GetAllByConditionWithoutIsDeleted(ctx context.Context, condition bson.M) []Member {
	var members []Member
	extension.DBRepository.FindAll(ctx, C_MEMBER, condition, nil, 0, &members)
	DecryptMembers(ctx, members)
	return members
}

func (m *Member) GetAllByOpenIds(ctx context.Context, openIds []string) []Member {
	condition := bson.M{
		"socials.openId": bson.M{
			"$in": openIds,
		},
		"isDeleted": false,
		"accountId": bson.ObjectIdHex(util.GetAccountId(ctx)),
	}

	var members []Member
	extension.DBRepository.FindAll(ctx, C_MEMBER, condition, nil, 0, &members)
	DecryptMembers(ctx, members)
	return members
}

func (m *Member) GetAllByUnionIds(ctx context.Context, unionIds []string) []Member {
	condition := bson.M{
		"socials.unionId": bson.M{
			"$in": unionIds,
		},
		"isDeleted": false,
		"accountId": bson.ObjectIdHex(util.GetAccountId(ctx)),
	}

	var members []Member
	extension.DBRepository.FindAll(ctx, C_MEMBER, condition, nil, 0, &members)
	DecryptMembers(ctx, members)
	return members
}

func (*Member) GetAllByIds(ctx context.Context, ids []string) []Member {
	var (
		members   []Member
		memberIds []bson.ObjectId
	)

	for _, memberId := range ids {
		if !bson.IsObjectIdHex(memberId) {
			continue
		}

		memberIds = append(memberIds, bson.ObjectIdHex(memberId))
	}

	if len(memberIds) == 0 {
		return members
	}

	condition := bson.M{
		"_id": bson.M{
			"$in": memberIds,
		},
		"accountId": bson.ObjectIdHex(util.GetAccountId(ctx)),
		"isDeleted": false,
	}

	extension.DBRepository.FindAll(ctx, C_MEMBER, condition, nil, 0, &members)
	DecryptMembers(ctx, members)
	return members
}

func (*Member) GetAllByPagination(ctx context.Context, pageCond extension.PagingCondition) []Member {
	var members []Member
	extension.DBRepository.FindByPaginationWithoutCount(ctx, C_MEMBER, pageCond, &members)
	DecryptMembers(ctx, members)
	return members
}

func deleteAll(ctx context.Context, memberIds []bson.ObjectId) (int, error) {
	condition := bson.M{
		"_id": bson.M{"$in": memberIds},
	}
	updator := bson.M{
		"$set": bson.M{
			"isDeleted": true,
			"updatedAt": time.Now(),
		},
	}
	updated, err := CMember.UpdateAll(ctx, condition, updator)

	return updated, err
}

func revertDeletedByIds(ctx context.Context, memberIds []bson.ObjectId) (int, error) {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       bson.M{"$in": memberIds},
		"isDeleted": true,
	}
	updator := bson.M{
		"$set": bson.M{
			"isDeleted": false,
			"updatedAt": time.Now(),
		},
	}
	updated, err := CMember.UpdateAll(ctx, condition, updator)
	return updated, err
}

func (*Member) GetByIds(ctx context.Context, accountId bson.ObjectId, ids []bson.ObjectId) []Member {
	condition := bson.M{
		"isDeleted": false,
		"_id": bson.M{
			"$in": ids,
		},
		"accountId": accountId,
	}

	var members []Member
	extension.DBRepository.FindAll(ctx, C_MEMBER, condition, []string{"-createdAt"}, 0, &members)
	DecryptMembers(ctx, members)
	return members
}

func (*Member) GetGroupByIds(ctx context.Context, accountIds []bson.ObjectId, ids []bson.ObjectId) []Member {
	condition := bson.M{
		"isDeleted": false,
		"_id": bson.M{
			"$in": ids,
		},
		"accountId": bson.M{
			"$in": accountIds,
		},
	}

	var members []Member
	extension.DBRepository.FindAll(ctx, C_MEMBER, condition, []string{"-createdAt"}, 0, &members)
	DecryptMembers(ctx, members)

	return members
}

func (self *Member) GetGroupMembersByIds(ctx context.Context, accountIds, ids []bson.ObjectId) []Member {
	condition := bson.M{
		"isDeleted": false,
		"_id":       bson.M{"$in": ids},
		"accountId": bson.M{"$in": accountIds},
	}

	var members []Member
	extension.DBRepository.FindAll(ctx, C_MEMBER, condition, []string{"-createdAt"}, 0, &members)
	DecryptMembers(ctx, members)

	return members
}

func (self *Member) IncScoreById(ctx context.Context, scoreInfo *ScoreInfo, excludeChannelId []string) (*Member, error) {
	return self.incScoreById(ctx, scoreInfo, excludeChannelId, true)
}

func (self *Member) MustIncScoreById(ctx context.Context, scoreInfo *ScoreInfo, excludeChannelId []string) (*Member, error) {
	return self.incScoreById(ctx, scoreInfo, excludeChannelId, false)
}

// incScoreById 向当前客户发放指定数量的积分，生成对应的积分历史记录和事件
func (self *Member) incScoreById(ctx context.Context, scoreInfo *ScoreInfo, excludeChannelId []string, needCheckMemberBlock bool) (*Member, error) {
	channels, err := getEnableSyncScoreChannels(ctx)
	if err != nil {
		return nil, err
	}

	channelMap := map[string]*account.ChannelDetailResponse{}
	for _, channel := range channels.Channels {
		key := fmt.Sprintf("%s:%s", channel.Origin, channel.ChannelId)
		channelMap[key] = channel
	}
	if needCheckMemberBlock && self.BlockedStatus == BLOCKEDSTATUS_BLOCKED {
		return nil, codes.NewError(codes.MemberBlocked)
	}
	return self.IncScoreByIdIgnoreChannels(ctx, scoreInfo, excludeChannelId, channelMap)
}

// ensureMembersIsNotBlocked 检查当前客户是否被拉入黑名单
func ensureMembersIsNotBlocked(members []Member) error {
	for _, member := range members {
		if member.BlockedStatus == BLOCKEDSTATUS_BLOCKED {
			return codes.NewError(codes.MemberBlocked)
		}
	}
	return nil
}

// ensureMembersIsNotDisabled 检查客户是否被禁用
func ensureMembersIsNotDisabled(members []Member) error {
	for _, member := range members {
		if member.IsDisabled {
			return codes.NewError(codes.MemberDisabled)
		}
	}

	return nil
}

// RewardScoreToMembers 向会员发放积分，无法向黑名单客户发放，返回发放的成功和失败的情况
func RewardScoreToMembers(ctx context.Context, info *ScoreInfo, members []Member) (*RewardScoreResult, error) {
	memberBlockedErr := ensureMembersIsNotBlocked(members)
	if memberBlockedErr != nil && info.GetTrigger().From != REWARD_FROM_TASK {
		return nil, memberBlockedErr
	}

	channelIds := []string{}
	if info.ChannelInfo.Id != "" {
		channelIds = append(channelIds, info.ChannelInfo.Id)
	}

	if strings.Contains(info.Description, YOUZAN_DECREASE_SCORE_DESC_BY_MANUAL) {
		info.Brief = ADMIN_DEDUCT_SCORE
	}
	if !core_util.IsEmpty(reflect.ValueOf(info.RawMeta)) {
		err := bson.UnmarshalJSON([]byte(info.RawMeta), &info.Meta)
		if err != nil {
			log.Warn(ctx, "scoreHistory meta is invalid json", log.Fields{
				"Meta": info.RawMeta,
			})
		}
	}

	var (
		succeedMembers  []RewardScoreSucceedMember
		failedMemberIds []string
		errs            []error
	)
	for i := range members {
		member := members[i]
		info.MemberId = member.Id.Hex()
		successMember, err := member.IncScore(ctx, info, channelIds)
		if err != nil {
			log.Warn(ctx, "Increase score to member failed.", log.Fields{"err": err.Error(), "memberId": member.Id.Hex()})
			failedMemberIds = append(failedMemberIds, member.Id.Hex())
			errs = append(errs, err)
		} else {
			if successMember != nil {
				succeedMembers = append(succeedMembers, *successMember)
			}
		}
	}

	var err error
	if len(errs) > 0 {
		err = errors.NewUnknowError(core_err.New(strings.Join(core_util.ToStringArray(core_util.CallAndCombineArray("Error", core_util.ToInterfaceArray(errs))), ",")))
	}

	return &RewardScoreResult{
		SucceedMemberItems: succeedMembers,
		FailedMemberIds:    failedMemberIds,
	}, err
}

// rollbackScore 回滚发放给会员积分
func (*Member) rollbackScore(ctx context.Context, member *Member, info *ScoreInfo) error {
	err := CMember.UpdateOne(ctx,
		bson.M{"_id": member.Id},
		GenRollbackScoreUpdaterByOrginScoreInfo(ctx, info),
	)

	return err
}

// BatchAddTags 批量为客户打标签，产生标签事件
func (*Member) BatchAddTags(ctx context.Context, accountIds []bson.ObjectId, members []Member, tags []string, disableEvent, fromWechatwork bool) error {
	if len(tags) == 0 {
		return nil
	}
	if len(members) == 0 {
		return codes.NewError(codes.MemberNotFound)
	}

	condition := bson.M{
		"_id":       bson.M{"$in": getMemberIds(members)},
		"accountId": bson.M{"$in": accountIds},
	}
	update := bson.M{
		"$addToSet": bson.M{
			"tags": bson.M{"$each": tags},
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
	}

	_, err := CMember.UpdateAll(ctx, condition, update)
	if err != nil {
		return codes.NewError(codes.UpdateMemberFail)
	}
	core_component.GO(ctx, func(ctx context.Context) {
		batchTagChanged(ctx, members, TagAdder{tagsToAdd: tags}, disableEvent, fromWechatwork)
	})

	return nil
}

// BatchDeleteTags 从客户身上批量删除标签，发事件
func (*Member) BatchDeleteTags(ctx context.Context, accountIds []bson.ObjectId, members []Member, tags []string, disableEvent, fromWechatwork bool) error {
	if len(tags) == 0 {
		return nil
	}
	condition := bson.M{
		"_id":       bson.M{"$in": getMemberIds(members)},
		"accountId": bson.M{"$in": accountIds},
	}
	update := bson.M{
		"$pullAll": bson.M{
			"tags": tags,
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
	}
	_, err := CMember.UpdateAll(ctx, condition, update)

	if err != nil {
		return codes.NewError(codes.UpdateMemberFail)
	}
	core_component.GO(ctx, func(ctx context.Context) {
		batchTagChanged(ctx, members, TagDeleter{tagsToDelete: tags}, disableEvent, fromWechatwork)
	})

	return nil
}

// BatchSetTags 使用新标签覆盖客户的旧有标签
func (*Member) BatchSetTags(ctx context.Context, accountIds []bson.ObjectId, members []Member, tags []string, disableEvent, fromWechatwork bool) error {
	condition := bson.M{
		"_id":       bson.M{"$in": getMemberIds(members)},
		"accountId": bson.M{"$in": accountIds},
	}
	update := bson.M{
		"$set": bson.M{
			"tags":      tags,
			"updatedAt": time.Now(),
		},
	}
	_, err := CMember.UpdateAll(ctx, condition, update)
	if err != nil {
		return codes.NewError(codes.UpdateMemberFail)
	}
	core_component.GO(ctx, func(ctx context.Context) {
		batchTagChanged(ctx, members, TagSetter{newTags: tags}, disableEvent, fromWechatwork)
	})

	return nil
}

/**
 * ITagManager
 * 此接口用于在客户拥有的标签变动后执行相关逻辑
 * 1. 推送改动事件
 * 2. 记录客户信息变动
 **/
type ITagManager interface {
	isTagChanged(oldTags []string) bool
	getNewTags(oldTags []string) []string
}

type TagAdder struct {
	tagsToAdd []string
}

func (t TagAdder) isTagChanged(oldTags []string) bool {
	if len(t.tagsToAdd) == 0 {
		return false
	}
	if len(util.StrArrayDiff(t.tagsToAdd, oldTags)) > 0 {
		return true
	} else {
		return false
	}
}

func (t TagAdder) getNewTags(oldTags []string) []string {
	if len(t.tagsToAdd) == 0 {
		return oldTags
	}

	newTags := append(oldTags, t.tagsToAdd...)
	return util.StrArrayUnique(newTags)
}

type TagDeleter struct {
	tagsToDelete []string
}

func (t TagDeleter) isTagChanged(oldTags []string) bool {
	if len(t.tagsToDelete) == 0 {
		return false
	}
	if len(util.StrArrayDiff(t.tagsToDelete, oldTags)) < len(t.tagsToDelete) {
		return true
	} else {
		return false
	}
}

func (t TagDeleter) getNewTags(oldTags []string) []string {
	if len(t.tagsToDelete) == 0 {
		return oldTags
	}

	newTags := util.StrArrayDiff(oldTags, t.tagsToDelete)
	return util.StrArrayUnique(newTags)
}

type TagSetter struct {
	newTags []string
}

func (t TagSetter) isTagChanged(oldTags []string) bool {
	return len(util.StrArrayDiff(oldTags, t.newTags)) > 0 || len(util.StrArrayDiff(t.newTags, oldTags)) > 0
}

func (t TagSetter) getNewTags(oldTags []string) []string {
	return t.newTags
}

// batchTagChanged 在客户拥有标签发生变动后确认变动并记录，发出事件
func batchTagChanged(ctx context.Context, members []Member, tagManager ITagManager, disableEvent, fromWechatwork bool) {
	for _, member := range members {
		if tagManager.isTagChanged(member.Tags) {
			member.createTagsChangedInfoLog(ctx, tagManager.getNewTags(member.Tags), disableEvent, fromWechatwork)
		}
	}
}

// createTagsChangedInfoLog 创建客户标签变动对应的客户信息修改记录
func (self *Member) createTagsChangedInfoLog(ctx context.Context, newTags []string, disableEvent, fromWechatwork bool) {
	tags := self.Tags
	if len(util.StrArrayDiff(tags, newTags)) > 0 || len(util.StrArrayDiff(newTags, tags)) > 0 {
		infoLog := map[string]interface{}{
			"tags": map[string]interface{}{
				"from":           tags,
				"to":             newTags,
				"fromWechatwork": fromWechatwork,
			},
		}
		operatorId, operatorType := util.GetOperatorIdAndType(ctx)
		options := []MemberInfoLogOption{
			MemberInfoLogWithLogType(INFO_LOG_TYPE_UPDATE_PERSONAL_INFO),
			MemberInfoLogWithDetail(infoLog),
			MemberInfoLogWithOperator(operatorId, operatorType, ""),
		}
		self.AddInfoLog(ctx, true, disableEvent, options...)
	}
}

type MemberInfoLogOption func(infoLog *MemberInfoLog)

// AddInfoLog 创建客户信息修改记录
func (self *Member) AddInfoLog(ctx context.Context, createLog, disableEvent bool, options ...MemberInfoLogOption) {
	memberInfoLog := GenerateMemberInfoLogWithOptions(options...)

	if memberInfoLog.IsDetailEmpty {
		createLog = false
	}

	if createLog {
		memberInfoLog.Id = bson.NewObjectId()
		memberInfoLog.Record(ctx, self)
	}
	if !disableEvent {
		core_component.GO(ctx, func(ctx context.Context) {
			propertyChange := make(map[string]interface{})
			core_util.CopyByJson(memberInfoLog.Detail, &propertyChange)
			// 仅更新零售定位的情况不发事件
			if len(propertyChange) == 1 && propertyChange["群脉零售地理定位"] != nil {
				return
			}
			self.TriggerMemberUpdatedEvent(ctx, memberInfoLog.Id, memberInfoLog.Detail, memberInfoLog.Type)
		})
	}
}

// GenerateMemberInfoLogWithOptions 根据改动的内容生成完整的客户信息修改记录
func GenerateMemberInfoLogWithOptions(options ...MemberInfoLogOption) *MemberInfoLog {
	infoLog := &MemberInfoLog{}
	for _, option := range options {
		option(infoLog)
	}
	return infoLog
}

// MemberInfoLogWithRemark 设置客户信息变动记录的备注
func MemberInfoLogWithRemark(remark string) MemberInfoLogOption {
	return func(infoLog *MemberInfoLog) {
		infoLog.Remark = remark
	}
}

// MemberInfoLogWithLogType 设置客户信息变动记录的类型
func MemberInfoLogWithLogType(logType string) MemberInfoLogOption {
	return func(infoLog *MemberInfoLog) {
		infoLog.Type = logType
	}
}

// MemberInfoLogWithDetail 设置客户信息变动记录的详情
func MemberInfoLogWithDetail(detail interface{}) MemberInfoLogOption {
	return func(memberInfoLog *MemberInfoLog) {
		if core_util.IsEmpty(reflect.ValueOf(detail)) {
			memberInfoLog.IsDetailEmpty = true
		}
		memberInfoLog.Detail = detail
	}
}

// MemberInfoLogWithSubType 设置客户信息变动记录的子类型
func MemberInfoLogWithSubType(subType string) MemberInfoLogOption {
	return func(memberInfoLog *MemberInfoLog) {
		memberInfoLog.SubType = subType
	}
}

// MemberInfoLogWithOperator 设置导致客户信息变动的操作人
func MemberInfoLogWithOperator(operatorId, operatorType, operatorName string) MemberInfoLogOption {
	return func(infoLog *MemberInfoLog) {
		operator := Operator{
			Name: operatorName,
			Type: operatorType,
		}
		if bson.IsObjectIdHex(operatorId) {
			operator.Id = bson.ObjectIdHex(operatorId)
		}
		infoLog.Operator = operator
	}
}

// GenerateCardNumber 在会员激活发卡时为会员卡生成唯一编码
func (*Member) GenerateCardNumber(ctx context.Context) string {
	return GetCardNumberGenerator(ctx).Generate()
}

// generateCardNumber 生成指定长度的会员卡编码
func generateCardNumber(numLength int) string {
	cardNumber := core_util.GenRandomNumber(numLength, 10)
	for strings.HasPrefix(cardNumber, "0") {
		cardNumber = core_util.GenRandomNumber(numLength, 10)
	}

	return cardNumber
}

func (self *Member) FindByCardNumber(ctx context.Context, number string) (*Member, error) {
	selector := bson.M{
		"cardNumber": number,
		// we do not add ``"isDelete": false` nor "accountId" on purpose
		// since the cardNumber is unique index, we'll
		// try to find member by it no matter if the member
		// is deleted.
	}

	dbMember := Member{}
	err := Common.GetByCondition(ctx, selector, C_MEMBER, &dbMember)
	if err != nil {
		return nil, err
	}
	DecryptMember(ctx, &dbMember)
	return &dbMember, nil
}

func (self *Member) Create(ctx context.Context, memberProperties []MemberProperty, isActivated bool, eventMeta *EventMeta) error {
	if isActivated {
		self.ActivatedAt = time.Now()
	}

	err := self.ValidateInfo(ctx, memberProperties, []bson.ObjectId{self.Id})
	if err != nil {
		return err
	}
	self.CountInformationGradesWithProperties(memberProperties)

	// 因为在这里 create 之后，mairpc 还有其他逻辑要更新 member，但是其他项目可能会根据激活事件做更新操作，两者会并发，这里在处理完毕后再发送事件
	return self.Insert(ctx, eventMeta, false)
}

func (*Member) cardNumberIsFromUser(ctx context.Context) bool {
	isFromUser, ok := ctx.Value("cardNumberIsFromUser").(bool)
	return ok && isFromUser
}

// Insert 将客户属性中的手机号等进行加密处理后为当前客户创建一条数据库记录
// 如果会员卡号已存在且卡号是自动生成的就重新生成后重试
// 数据库记录创建后发会员创建和激活事件
func (self *Member) Insert(ctx context.Context, eventMeta *EventMeta, sendEvent bool) (err error) {
	self.IdUpdatedAt = bson.NewObjectId()

	self.GenDefaultValueStage()
	defer func() {
		if reErr := recover(); reErr != nil {
			if mgoErr := reErr.(error); qmgo.IsDup(mgoErr) {
				// 可能手动加了其他唯一索引
				if !strings.Contains(mgoErr.Error(), "cardNumber_1") {
					panic(mgoErr)
				}
				if self.cardNumberIsFromUser(ctx) {
					err = codes.NewError(codes.CardNumberExist)
				} else {
					self.CardNumber = self.GenerateCardNumber(ctx)
					self.Insert(ctx, eventMeta, sendEvent)
				}
			} else {
				panic(mgoErr)
			}
		}
	}()
	if self.BlockedStatus != BLOCKEDSTATUS_BLOCKED && CBlackListIdentifier.IsMemberInBlackList(ctx, *self) {
		self.BlockedStatus = BLOCKEDSTATUS_BLOCKED
		self.BlockedStatusUpdatedAt = time.Now()
	}

	member := &Member{} // 新建变量，防止影响未加密的 member
	core_util.CopyFieldsWithoutConvert(self, member)
	accountProperties := CMemberProperty.GetByAccount(ctx, util.GetAccountId(ctx))
	memberPropertyMap := GetMemberPropertyMapByName(accountProperties)
	encryptedPhone, ok := EncryptValueByPropertyMap(ctx, DEFAULT_PROPERTY_PHONE, self.Phone, memberPropertyMap)
	if ok {
		member.Phone = cast.ToString(encryptedPhone)
	}
	member.Properties = EncryptMemberProperties(ctx, member.Properties, memberPropertyMap)

	core_component.GO(ctx, func(ctx context.Context) { CMemberInfoGradesHistory.CreateByNewMember(ctx, accountProperties, member) })

	extension.DBRepository.Insert(ctx, C_MEMBER, member)

	if sendEvent {
		self.SendEventAfterInsert(ctx, eventMeta)
	}

	core_component.GO(ctx, func(ctx context.Context) { CTag.AddToDefaultGroup(ctx, self.Tags) })

	return
}

func (self *Member) SendEventAfterInsert(ctx context.Context, eventMeta *EventMeta) {
	// send portal message
	originalSocial := self.GetOriginalSocial()
	core_component.GO(ctx, func(ctx context.Context) {
		self.TriggerMemberActivatedEvent(ctx, originalSocial.Origin, originalSocial.Channel)
	})
	core_component.GO(ctx, func(ctx context.Context) { self.triggerMemberCreatedEvent(ctx) })
	// track customerEvent
	channelInfo := ChannelInfo{
		Id:     originalSocial.Channel,
		Name:   originalSocial.ChannelName,
		Origin: originalSocial.Origin,
	}
	self.TrackMemberActivatedEvent(ctx, channelInfo, eventMeta)
	return
}

// TrackMemberActivatedEvent 发出会员激活事件，如果会员当前等级未设置那么设置为等级一然后发放奖励，成功后通过短信或小程序向会员发送激活通知信息
func (self *Member) TrackMemberActivatedEvent(ctx context.Context, channelInfo ChannelInfo, eventMeta *EventMeta) {
	if !self.IsActivated {
		return
	}
	card := CMembershipCard.GetById(ctx, self.CardId, self.AccountId)
	customerEvent := component.CustomerEventBody{
		AccountId: self.AccountId.Hex(),
		ChannelId: channelInfo.Id,
		OpenId:    self.GetOpenIdByChannelId(channelInfo.Id),
		MemberId:  self.Id.Hex(),
		MsgType:   component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:   component.MAIEVENT_ACTIVATE,
		EventProperties: map[string]interface{}{
			"cardId":           self.CardId.Hex(),
			"cardName":         card.Name,
			"activationSource": self.ActivationSource,
			"ip":               util.GetClientIp(ctx),
		},
		CreateTime: self.ActivatedAt.UnixNano() / 1e6,
	}
	if eventMeta != nil {
		customerEvent.EventProperties["promoterMemberId"] = eventMeta.PromoterMemberId
	}
	if eventMeta != nil && eventMeta.StaffInfo != nil {
		if eventMeta.StaffInfo.StaffId.Valid() {
			customerEvent.EventProperties["staffId"] = eventMeta.StaffInfo.StaffId.Hex()
			customerEvent.EventProperties["staffName"] = eventMeta.StaffInfo.StaffName
		}
		if eventMeta.StaffInfo.StoreId.Valid() {
			customerEvent.EventProperties["storeId"] = eventMeta.StaffInfo.StoreId.Hex()
			customerEvent.EventProperties["storeName"] = eventMeta.StaffInfo.StoreName
		}
	}

	if channelInfo.Id == "" {
		customerEvent.EventProperties["origin"] = constant.PORTAL
	} else {
		customerEvent.EventProperties["origin"] = channelInfo.Origin
		customerEvent.EventProperties["channelId"] = channelInfo.Id
		customerEvent.EventProperties["channelName"] = channelInfo.Name
	}

	if eventMeta != nil && eventMeta.Utm != nil {
		customerEvent.EventProperties["utmSource"] = eventMeta.Utm.UtmSource
		customerEvent.EventProperties["utmContent"] = eventMeta.Utm.UtmContent
		customerEvent.EventProperties["utmMedium"] = eventMeta.Utm.UtmMedium
		customerEvent.EventProperties["utmCampaign"] = eventMeta.Utm.UtmCampaign
		customerEvent.EventProperties["utmTerm"] = eventMeta.Utm.UtmTerm
	}

	customerEvent.SendCustomerEventWithBusinessTag(ctx)
	// 初始化客户等级
	if self.Level == 0 {
		err := CMember.UpdateOne(ctx, bson.M{"_id": self.Id}, bson.M{"$set": bson.M{"level": 1, "levelStartedAt": time.Now()}})
		if err != nil {
			log.Error(ctx, "update member level failed", log.Fields{"error": fmt.Sprintf("%v", err)})
		}
		if self.Growth > 0 {
			self.UpdateLevelAfterGrowthChanged(ctx, nil)
		} else {
			self.SendLevelUpReward(ctx, SendLevelUpRewardOption{ChangeType: LEVEL_HISTORY_CHANGE_TYPE_MEMBER_ACTIVE, From: 0, To: 1, Description: getLevelDescription(0, 1)})
		}
		if self.Level == 0 {
			self.Level = 1
		}
	}
	core_component.GO(ctx, func(ctx context.Context) {
		memberLevel, err := CMemberLevel.GetEnabledLevel(ctx, uint64(self.Level))
		if err != nil {
			log.Warn(ctx, "Failed to get memberLevel", log.Fields{
				"memberId":     self.Id.Hex(),
				"growth":       self.GetGrowth(),
				"errorMessage": err.Error(),
			})
			return
		}

		// 发送会员激活消息
		NotifyMember(ctx, self, nil, constant.MESSAGE_RULE_MEMBER_ACTIVATED, map[string]string{
			PLACEHOLDER_MEMBERLEVEL:       memberLevel.Name,
			PLACEHOLDER_MEMBERACTIVATEDAT: time.Now().Format("2006-01-02 15:04:02"),
		}, "")
	})
}

// UpdateActivationProperties 在激活会员后，为会员增加注册门店、注册导购属性，如果此属性已存在那么不再做处理
func (self *Member) UpdateActivationProperties(ctx context.Context, eventMeta *EventMeta) error {
	if eventMeta == nil || eventMeta.StaffInfo == nil {
		return nil
	}
	propertyNames := map[string]struct{}{
		SYSTEM_PROPERTY_ACTIVATION_STORE: {},
		SYSTEM_PROPERTY_ACTIVATION_STAFF: {},
	}
	if eventMeta.StaffInfo.StaffId.IsZero() {
		delete(propertyNames, SYSTEM_PROPERTY_ACTIVATION_STAFF)
	}
	if eventMeta.StaffInfo.StoreId.IsZero() {
		delete(propertyNames, SYSTEM_PROPERTY_ACTIVATION_STORE)
	}
	for _, property := range self.Properties {
		if _, ok := propertyNames[property.Name]; ok {
			delete(propertyNames, property.Name)
		}
	}
	names := make([]string, 0, len(propertyNames))
	for name := range propertyNames {
		names = append(names, name)
	}
	if len(names) == 0 {
		return nil
	}
	memberProperties, err := CMemberProperty.GetByNames(ctx, names)
	if err != nil {
		return err
	}
	if len(memberProperties) == 0 {
		return nil
	}
	properties := make([]Property, 0, len(memberProperties))
	for _, memberProperty := range memberProperties {
		property := Property{
			Id:    memberProperty.Id,
			Name:  memberProperty.Name,
			Value: eventMeta.StaffInfo.StoreId.Hex(),
		}
		if memberProperty.Name == SYSTEM_PROPERTY_ACTIVATION_STAFF {
			property.Value = eventMeta.StaffInfo.StaffId.Hex()
		}
		properties = append(properties, property)
	}
	selector := Common.GenDefaultConditionById(ctx, self.Id)
	return extension.DBRepository.UpdateOne(ctx, C_MEMBER, selector, bson.M{
		"$push": bson.M{
			"properties": bson.M{
				"$each": properties,
			},
		},
	})
}

// triggerMemberCreatedEvent 触发客户创建事件，相关回调会订阅此事件
func (self *Member) triggerMemberCreatedEvent(ctx context.Context) {
	channel := self.getMessageChannel(ctx, "online")
	memberId := self.Id.Hex()
	originSocial := self.GetOriginalSocial()
	if originSocial == nil {
		originSocial = &Social{}
	}
	data := map[string]interface{}{
		"type":         "member_created",
		"member_id":    memberId,
		"account_id":   self.AccountId.Hex(),
		"phone":        self.Phone,
		"is_activated": self.IsActivated,
		"created_at":   util.TransTime(self.CreatedAt),
		"unionId":      originSocial.UnionId,
		"openId":       originSocial.OpenId,
	}
	component.MessageQueue.SendPortalMessage(ctx, channel, data, "member_"+memberId, "member")
}

// getMessageChannel 获取发送会员创建等事件时的渠道信息
func (self *Member) getMessageChannel(ctx context.Context, channelType string) component.MessageChannel {
	originalSocial := self.GetOriginalSocial()
	if nil == originalSocial {
		return component.MessageChannel{}
	}

	channelName := originalSocial.ChannelName
	if originalSocial.ChannelName == "" && bson.IsObjectIdHex(originalSocial.Channel) {
		channelInfo, _ := component.WeConnect.GetChannel(ctx, originalSocial.Channel)
		if channelInfo != nil {
			channelName = channelInfo.Name
		}
	}

	return component.MessageChannel{
		Id:     originalSocial.Channel,
		Name:   channelName,
		Social: originalSocial.Origin,
		Type:   channelType,
	}
}

func (self *Member) Update(ctx context.Context, properties []MemberProperty, existsIds []bson.ObjectId) error {
	return self.updateMember(ctx, properties, existsIds, true)
}

// UpdateWithoutCountGrades 更新会员信息但不根据客户信息计算评分
func (self *Member) UpdateWithoutCountGrades(ctx context.Context, properties []MemberProperty, existsIds []bson.ObjectId) error {
	return self.updateMember(ctx, properties, existsIds, false)
}

// updateMember 通过覆盖所有字段的形式更新，可能会有并发问题
func (self *Member) updateMember(ctx context.Context, properties []MemberProperty, existsIds []bson.ObjectId, needCountInformationGrades bool) error {
	err := self.validateProperties(ctx, properties, existsIds)
	if err != nil {
		return err
	}

	newProperties := []Property{}
	for i, p := range self.Properties {
		if p.Name == DEFAULT_PROPERTY_BIRTHDAY && cast.ToInt64(p.Value) == 0 {
			continue
		}
		newProperties = append(newProperties, self.Properties[i])
	}
	self.Properties = newProperties

	self.UpdatedAt = time.Now()

	// in case the member haven't set MemberValue before
	// we will give it a default stage
	self.GenDefaultValueStage()

	if needCountInformationGrades {
		self.CountInformationGradesWithProperties(properties)
	}

	memberPropertyMap := GetMemberPropertyMapByName(CMemberProperty.GetByAccount(ctx, util.GetAccountId(ctx)))
	phone, _ := EncryptValueByPropertyMap(ctx, DEFAULT_PROPERTY_PHONE, self.Phone, memberPropertyMap)
	setter := bson.M{
		"tags":             self.Tags,
		"remarks":          self.Remarks,
		"phone":            phone,
		"properties":       EncryptMemberProperties(ctx, self.Properties, memberPropertyMap),
		"location":         self.Location,
		"avatar":           self.Avatar,
		"birth":            self.Birth,
		"updatedAt":        self.UpdatedAt,
		"qrcodeViewed":     self.QrcodeViewed,
		"wxCard":           self.WxCard,
		"paidCards":        self.PaidCards,
		"memberValue":      self.MemberValue,
		"source":           self.Source,
		"activationSource": self.ActivationSource,
	}

	if !self.IgnoreIsActivated {
		setter["isActivated"] = self.IsActivated
	}
	if !self.IgnoreSocials {
		setter["socials"] = self.Socials
	}

	if self.CardId.Valid() {
		setter["cardId"] = self.CardId
	}

	if self.ActivatedAt.Unix() > 0 {
		setter["activatedAt"] = self.ActivatedAt
	}

	if self.CardProvideTime.Unix() > 0 {
		setter["cardProvideTime"] = self.CardProvideTime
	}

	updator := bson.M{
		"$set": setter,
	}
	CMember.UpdateOne(ctx, bson.M{"_id": self.Id}, updator)

	return nil
}

func GetMemberPropertyMapByName(accountProperties []MemberProperty) map[string]MemberProperty {
	result := map[string]MemberProperty{}
	for _, property := range accountProperties {
		result[property.Name] = property
	}
	return result
}

func (self *Member) UpdateCustomerValue(ctx context.Context) error {
	updater := bson.M{
		"$set": bson.M{
			"memberValue": self.MemberValue,
			"updatedAt":   time.Now(),
		},
	}

	return CMember.UpdateOne(ctx, bson.M{"_id": self.Id}, updater)
}

func (self *Member) UpdatePaidCards(ctx context.Context) error {
	updater := bson.M{
		"$set": bson.M{
			"paidCards": self.PaidCards,
			"updatedAt": time.Now(),
		},
	}

	return CMember.UpdateOne(ctx, bson.M{"_id": self.Id}, updater)
}

func (self *Member) UpdateRemarks(ctx context.Context) error {
	updater := bson.M{
		"$set": bson.M{
			"remarks":   self.Remarks,
			"updatedAt": time.Now(),
		},
	}

	return CMember.UpdateOne(ctx, bson.M{"_id": self.Id}, updater)
}

// TriggerMemberUpdatedEvent 触发客户更新事件，详情中包括更新前和更新后的值
func (self *Member) TriggerMemberUpdatedEvent(ctx context.Context, logId bson.ObjectId, infoLog interface{}, changeType string) {
	channel := self.getMessageChannel(ctx, "online")
	memberId := self.Id.Hex()
	originSocial := self.GetOriginalSocial()
	if originSocial == nil {
		originSocial = &Social{}
	}
	data := map[string]interface{}{
		"type":         "member_updated",
		"member_id":    memberId,
		"account_id":   self.AccountId.Hex(),
		"is_activated": self.IsActivated,
		"updated_at":   util.TransTime(self.UpdatedAt),
		"phone":        self.Phone,
		"unionId":      originSocial.UnionId,
		"openId":       originSocial.OpenId,
	}

	if logId.Valid() {
		data["infoLogId"] = logId.Hex()
	}
	if infoLog != nil {
		data["infoLog"] = infoLog
	}
	if changeType != "" {
		data["changeType"] = changeType
	}
	component.MessageQueue.SendPortalMessage(ctx, channel, data, "member_"+memberId, "member")
}

// ValidateInfo 校验客户的来源和信息是否符合要求，例如校验手机号邮箱等
func (self *Member) ValidateInfo(ctx context.Context, memberProperties []MemberProperty, excludedMemberIds []bson.ObjectId) error {
	err := self.ValidateSocials(ctx, excludedMemberIds)
	if err != nil {
		return err
	}

	err = self.validateProperties(ctx, memberProperties, excludedMemberIds)
	if err != nil {
		return err
	}

	return nil
}

// GetMainMember 当发生客户合并时，此方法可以从一批要合并的客户中获取到主客户，合并后主客户被保留，其余子客户信息更新到主客户后被删除
// 主客户取这一些客户中已激活且激活事件较早的客户或者是未激活但创建时间较早的记录
func GetMainMember(members []Member) (Member, []Member) {
	var mainMember Member
	var otherMembers []Member

	if len(members) == 0 {
		return mainMember, otherMembers
	}

	mainMember = members[0]
	for index, member := range members {
		if index == 0 {
			continue
		}
		if mainMember.IsActivated {
			if member.IsActivated && member.CreatedAt.Unix() < mainMember.CreatedAt.Unix() {
				otherMembers = append(otherMembers, mainMember)
				mainMember = member
			} else {
				otherMembers = append(otherMembers, member)
			}
		} else {
			if member.CreatedAt.Unix() < mainMember.CreatedAt.Unix() {
				otherMembers = append(otherMembers, mainMember)
				mainMember = member
			} else {
				otherMembers = append(otherMembers, member)
			}
		}
	}

	return mainMember, otherMembers
}

func (self *Member) GetMembershipCard(ctx context.Context) (card *MembershipCard, cardNumber string) {
	if self.CardId.Valid() {
		card = CMembershipCard.GetById(ctx, self.CardId, self.AccountId)
	}
	cardNumber = self.CardNumber

	return
}

func (*Member) GetByAccountAndUnionId(ctx context.Context, accountId bson.ObjectId, unionId string, excludedMemberIds []bson.ObjectId) *Member {
	member := &Member{}
	condition := bson.M{
		"accountId":       accountId,
		"isDeleted":       false,
		"socials.unionId": unionId,
	}
	condition = generateAdditionalCondition(condition, excludedMemberIds)
	extension.DBRepository.FindOne(ctx, C_MEMBER, condition, member)
	if member.Id.Hex() != "" {
		DecryptMember(ctx, member)
		return member
	}
	return nil
}

func (self *Member) MergeMember(ctx context.Context, members []Member, operatorId, reason string) error {
	return self.MergeMemberWithBlocked(ctx, members, operatorId, true, reason, "")
}

// MergeMemberWithBlocked 将一批具有相同标识的客户合并为一个客户，在选出主客户后，新客户的成长值取所有客户的较大值
// 积分值取所有客户积分值之和，并且将子客户的积分变动历史、成长值变动历史等合并到主客户上，此方法可能存在并发问题
// 合并后触发客户合并事件，系统中其他应用订阅此事件处理客户合并
func (self *Member) MergeMemberWithBlocked(ctx context.Context, members []Member, operatorId string, allowBlocked bool, reason string, remark string) error {
	if self.Isblocked() && !allowBlocked {
		return codes.NewError(codes.MemberBlocked)
	}
	memberCount := len(members)
	if memberCount == 0 {
		return codes.NewError(codes.MergeMemberNotFound)
	}
	members = self.removeDulicateMember(members)
	if len(members) == 0 {
		return codes.NewError(codes.MainMemberExistsInSubMember)
	}
	oldMember := *self
	avatarUpdated := false
	nameUpdated := false
	cardMerged := false
	needUpdateCardNumber := false
	mergedGrowthHistoryMemberId := self.Id
	// 记录当前 score
	originalScore := self.Score

	for _, member := range members {
		if member.Isblocked() {
			if !allowBlocked {
				return codes.NewError(codes.MemberBlocked)
			}
			self.Block()
		} else if self.Isblocked() {
			member.Block()
		}

		// merge growth 时选择的是最大成长值客户，所以成长值历史也需要使用最大值的
		if self.Growth < member.Growth {
			mergedGrowthHistoryMemberId = member.Id
		}
		avatarUpdated, nameUpdated, cardMerged = self.UpdateData(member, !avatarUpdated, !nameUpdated)
		if cardMerged {
			needUpdateCardNumber = true
			if err := member.UpdateCardNumberBeforeMerged(ctx); err != nil {
				return err
			}
		}
	}
	self.updateMixPhoneSocials(ctx)

	// in case all the member have no MemberValue, we
	// will give a defalut stage for the finally merged member.
	self.GenDefaultValueStage()

	memberProperties := CMemberProperty.GetByAccount(ctx, util.GetAccountId(ctx))
	memberPropertyMap := GetMemberPropertyMapByName(memberProperties)

	phone, _ := EncryptValueByPropertyMap(ctx, DEFAULT_PROPERTY_PHONE, self.Phone, memberPropertyMap)
	condition := bson.M{"_id": self.Id}
	setter := bson.M{
		"location":               self.Location,
		"tags":                   self.Tags,
		"phone":                  phone,
		"isActivated":            self.IsActivated,
		"isDisabled":             self.IsDisabled,
		"blockedStatus":          self.BlockedStatus,
		"properties":             EncryptMemberProperties(ctx, self.Properties, memberPropertyMap),
		"socials":                self.Socials,
		"birth":                  self.Birth,
		"totalScore":             self.TotalScore,
		"totalCostScore":         self.TotalCostScore,
		"annualAccumulatedScore": self.AnnualAccumulatedScore,
		"annualCostScore":        self.AnnualCostScore,
		"memberValue":            self.MemberValue,
		"wxCard":                 self.WxCard,
		"createdAt":              self.CreatedAt,
		"updatedAt":              time.Now(),
		"growth":                 self.Growth,
		"level":                  self.Level,
		"levelStartedAt":         self.LevelStartedAt,
		"source":                 self.Source,
		"activationSource":       self.ActivationSource,
	}

	if self.ActivatedAt.Unix() > 0 {
		setter["activatedAt"] = self.ActivatedAt
	}
	if self.BlockedStatusUpdatedAt.Unix() > 0 {
		setter["blockedStatusUpdatedAt"] = self.BlockedStatusUpdatedAt
	}

	if self.Nickname != "" {
		setter["nickname"] = self.Nickname
	}

	if self.CardId.Valid() {
		setter["cardId"] = self.CardId
		setter["cardProvideTime"] = self.CardProvideTime
	}

	if needUpdateCardNumber {
		setter["cardNumber"] = self.CardNumber
	}

	if avatarUpdated {
		setter["avatar"] = self.Avatar
	}

	if len(self.PaidCards) > 0 {
		setter["paidCards"] = self.PaidCards
	}
	updator := bson.M{
		"$set": setter,
	}

	incScore := self.Score - originalScore
	if incScore != 0 {
		updator["$inc"] = bson.M{
			"score": incScore,
		}
	}

	memberIds := []bson.ObjectId{}
	for _, member := range members {
		memberIds = append(memberIds, member.Id)
	}

	var err error
	if share_model.IsUnderArmourAccount(ctx) {
		// UnderArmour 租户 production 有 accountId_1_phone_1 唯一索引；
		// 在其他 members 未删除的情况下，无法将其他 members 的 phone 更新到 main member 上，
		// 故针对该租户特殊处理，先删除其他 members，再 update main member。
		deleteAll(ctx, memberIds)
		err = CMember.UpdateOne(ctx, condition, updator)
		if err != nil {
			revertDeletedByIds(ctx, memberIds)
		}
	} else {
		err = CMember.UpdateOne(ctx, condition, updator)
	}
	if err != nil {
		return err
	}

	// 合并客户等级历史记录
	err = CMemberLevelHistory.BatchUpdateMemberId(ctx, memberIds, self.Id, self.LevelFrom)
	if err != nil {
		log.Error(ctx, "Merge member level history failed.", log.Fields{
			"mergedMemberIds": memberIds,
			"mainMemberId":    self.Id,
			"err":             err.Error(),
		})
	}

	// 合并客户权益卡开通记录
	core_component.GO(ctx, func(ctx context.Context) {
		err := CMemberPaidCardRecord.BatchUpdateMemberId(ctx, memberIds, self.Id)
		if err != nil {
			log.Error(ctx, "Merge member paid card log failed.", log.Fields{
				"mergedMemberIds": memberIds,
				"mainMemberId":    self.Id,
				"err":             err.Error(),
			})
		}
	})

	// 合并会员的权益奖励记录
	core_component.GO(ctx, func(ctx context.Context) {
		err := CMemberPrivilegeReward.BatchUpdateMemberId(ctx, memberIds, self.Id)
		if err != nil {
			log.Error(ctx, "Merge member privilege reward failed.", log.Fields{
				"mergedMemberIds": memberIds,
				"mainMemberId":    self.Id,
				"err":             err.Error(),
			})
		}
	})

	if !share_model.IsUnderArmourAccount(ctx) {
		deleteAll(ctx, memberIds)
	}
	selfInstance := *self
	// 创建客户合并的信息变动记录
	core_component.GO(ctx, func(ctx context.Context) { selfInstance.addInfoLogAfterMerged(ctx, &oldMember) })
	// 合并客户的会员任务参与记录
	core_component.GO(ctx, func(ctx context.Context) {
		_, err := CMemberTaskRecord.MergeMemberTaskRecord(ctx, self.Id, memberIds)
		if err != nil {
			log.Warn(ctx, "merge member record failed", log.Fields{
				"mainMemberId":        self.Id,
				"underMergeMemberIds": memberIds,
			})
		}
		_, _ = CMemberTaskLog.MergeMemberTaskLog(ctx, self.Id, memberIds)
		CMemberScoreSyncStatus.BatchUpdateMemberId(ctx, memberIds, self.Id)
	})
	// 合并客户的会员任务奖励记录
	core_component.GO(ctx, func(ctx context.Context) {
		_, err := CMemberTaskReward.MergeMemberTaskReward(ctx, self.Id, memberIds)
		if err != nil {
			log.Warn(ctx, "merge member reward failed", log.Fields{
				"mainMemberId":        self.Id,
				"underMergeMemberIds": memberIds,
			})
		}
	})
	// 合并客户的溢出积分
	core_component.GO(ctx, func(ctx context.Context) {
		_, err := CMemberOverflowScore.MergeMemberOverflowScore(ctx, self.Id, memberIds)
		if err != nil {
			log.Warn(ctx, "merge member overflow score failed", log.Fields{
				"mainMemberId":        self.Id,
				"underMergeMemberIds": memberIds,
			})
		}
		_, err = CMemberReleasableOverflowScore.MergeMemberReleasableOverflowScore(ctx, self.Id, memberIds)
		if err != nil {
			log.Warn(ctx, "merge member releasable overflow score failed", log.Fields{
				"mainMemberId":        self.Id,
				"underMergeMemberIds": memberIds,
			})
		}
		_, err = CScoreConsumeRecord.MergeScoreConsumeRecord(ctx, self.Id, memberIds)
		if err != nil {
			log.Warn(ctx, "merge member score consume record failed", log.Fields{
				"mainMemberId":        self.Id,
				"underMergeMemberIds": memberIds,
			})
		}
		_, err = CMemberScoreValidInfoLog.MergeMemberScoreValidInfoLog(ctx, self.Id, memberIds)
		if err != nil {
			log.Warn(ctx, "merge member score valid info log failed", log.Fields{
				"mainMemberId":        self.Id,
				"underMergeMemberIds": memberIds,
			})
		}
		_, err = CMemberAnnualAccumulatedScore.MergeMemberPastCAP(ctx, self.Id, memberIds)
		if err != nil {
			log.Warn(ctx, "merge member past cap failed", log.Fields{
				"mainMemberId":        self.Id,
				"underMergeMemberIds": memberIds,
			})
		}
	})
	// 合并客户的成长值变动记录
	core_component.GO(ctx, func(ctx context.Context) {
		if self.Id.Hex() != mergedGrowthHistoryMemberId.Hex() {
			_, _, err := CMemberGrowthHistory.MergeMemberGrowthHistory(ctx, self.Id, mergedGrowthHistoryMemberId)
			if err != nil {
				log.Warn(ctx, "merge member growth history failed", log.Fields{
					"mainMemberId":       self.Id,
					"underMergeMemberId": mergedGrowthHistoryMemberId,
				})
			}
		}
	})
	// 合并客户的保护期积分
	core_component.GO(ctx, func(ctx context.Context) {
		_, err := CMemberProtectionPeriodScore.MergeMemberProtectionPeriodScore(ctx, self.Id, memberIds)
		if err != nil {
			log.Warn(ctx, "merge member protect score failed", log.Fields{
				"mainMemberId":        self.Id,
				"underMergeMemberIds": memberIds,
			})
		}
	})
	// 合并客户的积分历史记录
	core_component.GO(ctx, func(ctx context.Context) {
		_, err := CScoreHistory.MergeScoreHistory(ctx, self.Id, memberIds)
		if err != nil {
			log.Warn(ctx, "Failed to merge score history", log.Fields{
				"mainMemberId":        self.Id,
				"underMergeMemberIds": memberIds,
			})
		}
	})
	// 将客户合并记录保存到数据库中
	core_component.GO(ctx, func(ctx context.Context) {
		recordMergeHistory(ctx, self.AccountId, self.Id, memberIds, operatorId, reason, members)
	})
	// 升级当前客户的会员卡
	core_component.GO(ctx, func(ctx context.Context) { selfInstance.upgradeCard(ctx) })
	// 创建客户合并的 portal-backend 中的处理 job
	core_component.GO(ctx, func(ctx context.Context) {
		args := getJobArgs(*self, members, operatorId, remark)
		// staging: 群脉开发（集团）；production：元祖食品 https://gitlab.maiscrm.com/mai/home/<USER>/issues/35090
		if util.StrInArray(args.AccountId, &[]string{"5e7872a773ee1200fb1bec32", "5f96a11479f7565e230fbe58"}) {
			util.CreateJobToBackendHighPriority("member", "MergeMember", args)
		} else {
			util.CreateJob("member", "MergeMember", args)
		}
	})
	// 发出客户合并事件
	core_component.GO(ctx, func(ctx context.Context) { self.sendMemberMergedEvent(ctx, util.MongoIdsToStrs(memberIds)) })
	// 合并客户的地址信息
	core_component.GO(ctx, func(ctx context.Context) { CMemberAddress.MergeMemberAddress(ctx, self.Id, memberIds) })
	// 处理客户的积分同步状态
	core_component.GO(ctx, func(ctx context.Context) { self.alignScoreAndGrowthAfterMemberMerged(ctx) })
	// 合并客户的等级变动历史
	core_component.GO(ctx, func(ctx context.Context) {
		CMemberInfoGradesHistory.MergeMemberInfoGradesHistory(ctx, memberIds, self.Id)
	})
	core_component.GO(ctx, func(ctx context.Context) {
		CStaticGroupUser.AfterMemberMerged(ctx, self.Id, memberIds)
	})
	return nil
}

// alignScoreAndGrowthAfterMemberMerged 为客户合并后的新渠道创建积分同步状态
func (self *Member) alignScoreAndGrowthAfterMemberMerged(ctx context.Context) {
	// 暂时不处理今世缘租户的合并对齐
	if self.AccountId.Hex() == "63588a0e81274c1efc23163a" {
		return
	}
	channels, err := getEnableSyncScoreChannels(ctx)
	if err != nil {
		log.Warn(ctx, "Failed to get channels for align score and growth after member merged", log.Fields{
			"errMsg": err.Error(),
		})
		return
	}
	channelsMap := map[string]*account.ChannelDetailResponse{}
	for _, channel := range channels.Channels {
		key := fmt.Sprintf("%s:%s", channel.Origin, channel.ChannelId)
		channelsMap[key] = channel
	}
	uniqueChannelIds := []string{}
	for _, social := range self.Socials {
		if util.StrInArray(social.Channel, &uniqueChannelIds) {
			continue
		}
		key := fmt.Sprintf("%s:%s", social.Origin, social.Channel)
		channel, ok := channelsMap[key]
		if !ok {
			continue
		}
		if channel.Origin == constant.TAOBAO_MEMBER && social.Nickname == "" && social.UnionId == "" {
			continue
		}
		uniqueChannelIds = append(uniqueChannelIds, social.Channel)
		syncStatus := MemberScoreSyncStatus{
			AccountId:   self.AccountId,
			MemberId:    self.Id,
			ChannelId:   channel.ChannelId,
			BusinessId:  fmt.Sprintf("%s:%s", ALIGN_SCORE_AND_GROWTH_AFTER_MEMBER_MERGED_BUSINESS, bson.NewObjectId().Hex()),
			From:        constant.PORTAL,
			To:          social.Origin,
			Type:        MEMBER_SCORE_SYNC_TYPE_ALIGN,
			Scene:       MEMBER_SCORE_SYNC_SCENE_ALIGN,
			Description: MEMBER_SCORE_GROWTH_SYNC_DESC_ALIGN,
			Status:      MEMBER_SCORE_SYNC_STATUS_UNSYNCED,
			AlignModes:  []string{"score", "growth"},
			SafeAlign:   true,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
		err := syncStatus.Insert(ctx)
		if err != nil {
			log.Warn(ctx, "Insert member score sync status failed after member merged", log.Fields{
				"syncStatus": syncStatus,
				"errMsg":     err.Error(),
			})
		}
	}
}

// sendMemberMergedEvent 发出客户合并事件，此事件是 business tag，仅 mairpc 内部能够消费
func (self *Member) sendMemberMergedEvent(ctx context.Context, memberIds []string) {
	customerEvent := component.CustomerEventBody{
		AccountId: util.GetAccountId(ctx),
		SubType:   component.MAIEVENT_MEMBER_MERGED,
		MsgType:   component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		EventProperties: map[string]interface{}{
			"mainMemberId":    self.Id.Hex(),
			"mergedMemberIds": memberIds,
		},
	}
	customerEvent.SendBusinessEvent(ctx)
}

// addInfoLogAfterMerged 在客户合并后创建合并的信息变动记录
func (self *Member) addInfoLogAfterMerged(ctx context.Context, oldMember *Member) {
	updatedInfo := self.getUpdatedInfoAfterMerged(ctx, oldMember)
	if len(updatedInfo) != 0 {
		options := []MemberInfoLogOption{
			MemberInfoLogWithLogType(INFO_LOG_TYPE_UPDATE_PERSONAL_INFO),
			MemberInfoLogWithDetail(updatedInfo),
		}
		self.AddInfoLog(ctx, true, false, options...)
	}

	// add suspicious member log if status changed
	if self.BlockedStatus != oldMember.BlockedStatus {
		logType := ""

		// this case may not appear
		if self.BlockedStatus == BLOCKEDSTATUS_NORMAL {
			logType = INFO_LOG_TYPE_REMOVE_FROM_SUSPICIOUSLIST
		} else if self.BlockedStatus == BLOCKEDSTATUS_SUSPICIOUS {
			logType = INFO_LOG_TYPE_ADD_TO_SUSPICIOUSLIST
		} else {
			log.Warn(ctx, "Fail to add info log for blocked member", log.Fields{
				"memberId": self.Id.Hex(),
			})
		}
		options := []MemberInfoLogOption{
			MemberInfoLogWithLogType(logType),
			MemberInfoLogWithOperator("", OPERATOR_TYPE_OF_SYSTEM, ""),
		}
		self.AddInfoLog(ctx, true, false, options...)
	}
}

// getUpdatedInfoAfterMerged 在客户合并后生成客户位置、标签和客户属性的变动信息
func (self *Member) getUpdatedInfoAfterMerged(ctx context.Context, oldMember *Member) map[string]interface{} {
	updatedInfos := map[string]interface{}{}

	locationLog := self.getUpdatedInfoByType("location", self.Location, oldMember.Location)
	tagsLog := self.getUpdatedInfoByType("tags", self.Tags, oldMember.Tags)
	properties := self.getUpdatedInfoByProperties(ctx, oldMember)

	util.ExtendMap(updatedInfos, locationLog, tagsLog, properties)

	return updatedInfos
}

func (*Member) getUpdatedInfoByType(typ string, new interface{}, old interface{}) map[string]interface{} {
	if !reflect.DeepEqual(new, old) {
		return map[string]interface{}{
			typ: map[string]interface{}{
				"from": old,
				"to":   new,
			},
		}
	}

	return nil
}

// getUpdatedInfoByProperties 在客户合并、修改客户属性后，将发生改变的客户属性写入信息变动记录
func (self *Member) getUpdatedInfoByProperties(ctx context.Context, oldMember *Member) map[string]interface{} {
	updatedInfo := map[string]interface{}{}
	oldPropertyIdValueMap := map[string]interface{}{}
	allMemberPropertiesMap := map[string]MemberProperty{}
	allPropertyIds := []bson.ObjectId{}

	for _, property := range self.Properties {
		if property.Id.Valid() {
			allPropertyIds = append(allPropertyIds, property.Id)
		}
	}

	for _, property := range oldMember.Properties {
		if property.Id.Valid() {
			id := property.Id.Hex()
			allPropertyIds = append(allPropertyIds, property.Id)
			oldPropertyIdValueMap[id] = property.Value
		}
	}

	allMemberProperties := CMemberProperty.GetByIds(ctx, self.AccountId, allPropertyIds)

	for _, memberProperty := range allMemberProperties {
		allMemberPropertiesMap[memberProperty.Id.Hex()] = memberProperty
	}

	for _, property := range self.Properties {
		id := property.Id.Hex()
		oldValue, ok := oldPropertyIdValueMap[id]

		if ok {
			if !reflect.DeepEqual(property.Value, oldValue) {
				updatedInfo[property.Name] = map[string]interface{}{
					"from": oldValue,
					"to":   property.Value,
					"type": allMemberPropertiesMap[id].Type,
				}
			}
		} else {
			if property.Value != "" {
				updatedInfo[property.Name] = map[string]interface{}{
					"from": "",
					"to":   property.Value,
					"type": allMemberPropertiesMap[id].Type,
				}
			}
		}
	}

	return updatedInfo
}

// recordMergeHistory 记录当前客户的合并历史
func recordMergeHistory(ctx context.Context, accountId, mainMemberId bson.ObjectId, mergeMemberIds []bson.ObjectId, operatorId, reason string, members []Member) {
	mergeHistory := MemberMergeHistory{
		MainMemberId:          mainMemberId,
		MergedMemberIds:       mergeMemberIds,
		MergedMembersSnapshot: members,
		AccountId:             accountId,
		Reason:                reason,
	}
	if operatorId != "" {
		mergeHistory.OperatorId = bson.ObjectIdHex(operatorId)
	}
	mergeHistory.Record(ctx)
}

// upgradeCard 如果当前会员符合条件那么就给客户发放升级后的会员卡
func (self *Member) upgradeCard(ctx context.Context) {
	currentCard, _ := self.GetMembershipCard(ctx)
	if nil == currentCard || !currentCard.IsAutoUpgrade {
		return
	}
	maxCard := CMembershipCard.GetSuitableCard(ctx, self.AccountId, uint32(self.TotalScore))
	if maxCard != nil && currentCard.Id.Hex() != maxCard.Id.Hex() && maxCard.Condition.MinScore > currentCard.Condition.MinScore {
		updator := bson.M{
			"$set": bson.M{
				"cardId":          maxCard.Id,
				"cardProvideTime": time.Now(),
			},
		}
		err := CMember.UpdateOne(ctx, bson.M{"_id": self.Id}, updator)
		if nil == err {
			self.addInfoLogWhenCardUpdated(ctx, maxCard, currentCard)
			self.TrackMembershipCardReceivedEvent(ctx, maxCard, ChannelInfo{Origin: constant.PORTAL}, true)
		}
	}
}

// mergeLocation 将从客户上的位置信息合并到当前客户上，仅当当前客户位置信息为空时有效
func (self *Member) mergeLocation(member Member) {
	if self.Location.Country == "" && member.Location.Country != "" {
		self.Location = member.Location
	}
}

// mergeTags 将从客户上的标签合并到当前客户上并去重
func (self *Member) mergeTags(member Member) {
	for _, tag := range member.Tags {
		if !util.StrInArray(tag, &self.Tags) {
			self.Tags = append(self.Tags, tag)
		}
	}
}

// mergePhone 将从客户的手机号合并到当前客户上，仅当当前客户手机号为空时有效
func (self *Member) mergePhone(member Member) {
	if self.Phone == "" {
		self.Phone = member.Phone
	}
}

// mergeIsDisabled 根据从客户状态设置当前客户是否禁用
func (self *Member) mergeIsDisabled(member Member) {
	if self.CardId.Valid() {
		return
	}
	self.IsDisabled = member.IsDisabled
}

// mergeCard 将从客户的会员卡合并到当前客户上
func (self *Member) mergeCard(member Member) bool {
	if !member.CardId.Valid() || self.CardId.Valid() {
		return false
	}
	self.CardNumber = member.CardNumber
	self.CardId = member.CardId
	self.CardNumber = member.CardNumber
	self.CardProvideTime = member.CardProvideTime
	if self.ActivatedAt.IsZero() && !member.ActivatedAt.IsZero() {
		self.ActivatedAt = member.ActivatedAt
	}
	return true
}

// mergeStatus 合并客户的黑名单状态
func (self *Member) mergeStatus(member Member) {
	if member.Issuspicious() {
		if !self.Issuspicious() || self.BlockedStatusUpdatedAt.Unix() > member.BlockedStatusUpdatedAt.Unix() {
			self.BlockedStatusUpdatedAt = member.BlockedStatusUpdatedAt
		}
		self.BlockedStatus = BLOCKEDSTATUS_SUSPICIOUS
	}
}

// mergeScore 将从客户的积分信息合并到当前客户上，包括可用积分、总积分、年度获得积分和年度消耗积分
func (self *Member) mergeScore(member Member) {
	self.Score += member.Score
	self.TotalScore += member.TotalScore
	self.AnnualAccumulatedScore += member.AnnualAccumulatedScore
	self.TotalCostScore += member.TotalCostScore
	self.AnnualCostScore += member.AnnualCostScore
}

// mergeNickname 合并客户昵称
func (self *Member) mergeNickname(member Member) {
	if self.Nickname != "" || member.Nickname == "" {
		return
	}

	self.Nickname = member.Nickname
}

// mergeMemberValue 合并客户价值
func (self *Member) mergeMemberValue(member Member) {
	// strategy: take the higest value
	selfIG := self.MemberValue.InformationGrades
	memberIG := member.MemberValue.InformationGrades
	self.MemberValue.InformationGrades = selfIG + memberIG

	selfE := self.MemberValue.Engagement
	memberE := member.MemberValue.Engagement
	self.MemberValue.Engagement = selfE + memberE

	selfS := self.MemberValue.Stage
	memberS := member.MemberValue.Stage
	self.MemberValue.Stage = GetAdvancingStage(selfS, memberS)

	// calculate grade again
	selfIG = self.MemberValue.InformationGrades
	selfE = self.MemberValue.Engagement
	self.MemberValue.Grades = selfIG + selfE
}

// mergeSocials 将从客户的渠道信息合并到主客户中，对于激活的会员的情况，激活渠道取二者中较早激活的
func (self *Member) mergeSocials(member Member) {

	// 查找 member.socials 中是否存在 isActivationChannel 为 true 的
	mainHasActivationChannel := isExistActivationChannel(*self)
	subHasActivationChannel := isExistActivationChannel(member)

	briefSocials := map[string]Social{}
	for _, social := range self.Socials {
		if self.CreatedAt.After(member.CreatedAt) {
			social.IsOriginal = false
		}

		// 子 socials 存在 isActivationChannel=true 且 activatedAt 在前， 主的 isActivationChannel 设置 false
		if subHasActivationChannel && member.ActivatedAt.Before(self.ActivatedAt) {
			social.IsActivationChannel = false
		}

		briefSocials[social.GetSocialBrief()] = social
	}

	for _, social := range member.Socials {
		// 主 socials 存在 isActivationChannel=true 且 activatedAt 在前， 子的 isActivationChannel 设置 false
		if mainHasActivationChannel && self.ActivatedAt.Before(member.ActivatedAt) {
			social.IsActivationChannel = false
		}

		mainRecordSocial, existsSameSocial := briefSocials[social.GetSocialBrief()]
		if existsSameSocial {
			mainRecordSocial.MergeSameSocial(social)
			if self.CreatedAt.After(member.CreatedAt) && social.IsOriginal {
				mainRecordSocial.IsOriginal = social.IsOriginal
			}
			briefSocials[social.GetSocialBrief()] = mainRecordSocial
		} else {
			if self.CreatedAt.Before(member.CreatedAt) {
				social.IsOriginal = false
			}
			briefSocials[social.GetSocialBrief()] = social
		}
	}

	self.Socials = []Social{}
	for _, social := range briefSocials {
		self.Socials = append(self.Socials, social)
	}

	// merge Activated
	if !self.CardId.Valid() {
		return
	}
	if member.IsActivated {
		// 如果更新了 activatedAt，也要更新 activationSource，原则上激活渠道、激活时间、激活来源都取自同一个 member
		if mainHasActivationChannel == subHasActivationChannel {
			if !self.IsActivated || self.ActivatedAt.Unix() > member.ActivatedAt.Unix() {
				self.ActivatedAt = member.ActivatedAt
				self.ActivationSource = member.ActivationSource
			}
		} else if !mainHasActivationChannel && subHasActivationChannel {
			self.ActivatedAt = member.ActivatedAt
			self.ActivationSource = member.ActivationSource
		} else if self.ActivatedAt.IsZero() || (!member.ActivatedAt.IsZero() && self.ActivatedAt.Unix() > member.ActivatedAt.Unix()) {
			self.ActivatedAt = member.ActivatedAt
			self.ActivationSource = member.ActivationSource
		}

		self.IsActivated = true
	}
}

// mergePaidCards 将从客户的权益卡合并到当前客户上，相同权益卡的过期时间取较晚的
func (self *Member) mergePaidCards(member Member) {
	for _, card := range member.PaidCards {
		var exist bool
		for i, oldCard := range self.PaidCards {
			if oldCard.CardId != card.CardId || oldCard.CardType != card.CardType {
				continue
			}
			exist = true
			if card.ExpireAt.After(oldCard.ExpireAt) {
				self.PaidCards[i].ExpireAt = card.ExpireAt
				self.PaidCards[i].StartedAt = card.StartedAt
			}
		}
		if !exist {
			self.PaidCards = append(self.PaidCards, card)
		}
	}
}

func isExistActivationChannel(member Member) bool {
	for _, social := range member.Socials {
		if social.IsActivationChannel {
			return true
		}
	}
	return false
}

// 对 member.socials 进行筛选，剔除系统由旧 phone 预生成的且未被使用过的渠道
// taobao:member 渠道的 openId 是由 phone 加密生成；jd 渠道的 unionId 是由 phone 加密生成；
// jd:member v1 渠道的 openId 是由 phone 加密生成，unionId 是京东的 pin；jd:member v2 渠道的 openId 是京东的 xid。
// 目前仅用在 mergeMember 操作中，所有 member 信息已缓存在 self 上且尚未更新到 db，此时来剔除无用的 social
func (self *Member) updateMixPhoneSocials(ctx context.Context) {
	if self.Phone == "" {
		return
	}

	newSocials := []Social{}
	for _, social := range self.Socials {
		if social.Origin == constant.TAOBAO_MEMBER && social.SubscribeTime.IsZero() { // 只对 social.SubscribeTime 为零值（表明未被使用过）的进行筛选
			channel, err := GetChannelByOrigin(ctx, social.Origin, social.Channel)
			if err != nil {
				log.Warn(ctx, "Failed get taobao:member channel by channelId", log.Fields{
					"channelId": social.Channel,
					"message":   err.Error(),
				})
				continue
			} else if channel.EncryptionKey == "" {
				log.Warn(ctx, "Empty channel encryptionKey", log.Fields{
					"channelId": channel.ChannelId,
				})
				continue
			}
			mixPhoneKey := fmt.Sprintf("tmall%s%s", self.Phone, channel.EncryptionKey)
			mixPhone := GetTmallMixPhone(mixPhoneKey)
			if mixPhone == social.OpenId {
				newSocials = append(newSocials, social)
			}
		} else if social.Origin == constant.JD { // 更新所有 jd social 的 unionId，美德乐临时会员通的逻辑
			channel, err := GetChannelByOrigin(ctx, social.Origin, social.Channel)
			if err != nil {
				log.Warn(ctx, "Failed get jd channel by channelId", log.Fields{
					"channelId": social.Channel,
					"message":   err.Error(),
				})
				continue
			}
			if channel.EncryptionKey == "" || channel.BrandId == "" {
				log.Warn(ctx, "EncryptionKey or brandId can't be emtpy", log.Fields{
					"channelId": channel.ChannelId,
				})
				continue
			}
			mixPhone := GetJdMixPhone(channel.EncryptionKey, channel.BrandId, self.Phone)
			social.UnionId = mixPhone
			newSocials = append(newSocials, social)
		} else if social.Origin == constant.JD_MEMBER && social.SubscribeTime.IsZero() { // 只对 SubscribeTime 为空（表明未被使用过）的进行筛选
			channel, err := GetChannelByOrigin(ctx, social.Origin, social.Channel)
			if err != nil {
				log.Warn(ctx, "Failed get jd:member channel by channelId", log.Fields{
					"channelId": social.Channel,
					"message":   err.Error(),
				})
				continue
			} else if !share_model.CChannel.IsJdMemberV1(channel.IntegrationModes) {
				continue
			} else if channel.EncryptionKey == "" {
				log.Warn(ctx, "Empty channel encryptionKey", log.Fields{
					"channelId": channel.ChannelId,
				})
				continue
			}
			mixPhone := GetJdMixPhone(channel.EncryptionKey, channel.ChannelId, self.Phone)
			if mixPhone == social.OpenId {
				newSocials = append(newSocials, social)
			}
		} else { // 其他渠道均保留
			newSocials = append(newSocials, social)
		}
	}
	self.Socials = newSocials
}

// mergeProperties 合并客户属性
func (self *Member) mergeProperties(member Member) {
	var existsPropertyIds []string
	zeroValueIndexMap := map[string]int{}
	for i, property := range self.Properties {
		if !core_util.IsEmpty(reflect.ValueOf(property.Value)) {
			existsPropertyIds = append(existsPropertyIds, property.Id.Hex())
		} else {
			zeroValueIndexMap[property.Id.Hex()] = i
		}
	}

	for _, property := range member.Properties {
		if !core_util.IsEmpty(reflect.ValueOf(property.Value)) && !util.StrInArray(property.Id.Hex(), &existsPropertyIds) {
			existsPropertyIds = append(existsPropertyIds, property.Id.Hex())
			if i, ok := zeroValueIndexMap[property.Id.Hex()]; ok {
				self.Properties[i] = property
				continue
			}
			self.Properties = append(self.Properties, property)
		}
	}
}

// mergeWxCard 合并微信会员卡
func (self *Member) mergeWxCard(member Member) {
	if self.WxCard != nil && self.WxCard.Id != "" {
		return
	}

	self.WxCard = member.WxCard
}

// mergeCreatedAt 合并会员的创建时间，取较早的创建时间
func (self *Member) mergeCreatedAt(member Member) {
	if self.CreatedAt.Unix() > member.CreatedAt.Unix() {
		self.CreatedAt = member.CreatedAt
	}
}

// mergeGrowth 合并成长值，合并后的值取较大者
func (self *Member) mergeGrowth(member Member) {
	if self.Growth < member.Growth {
		self.Growth = member.Growth
	}
}

// mergeLevel 合并会员等级，合并后的值取等级较高者，等级有效期同步更新
func (self *Member) mergeLevel(member Member) {
	if self.Level < member.Level {
		self.Level = member.Level
		self.LevelStartedAt = member.LevelStartedAt
		self.LevelFrom = member.Id
	}
}

func (social Social) isEmpty() bool {
	return social.OpenId == "" && social.Channel == ""
}

func (social Social) GetSocialBrief() string {
	if social.isEmpty() {
		return fmt.Sprintf("%s_%s_%s", social.Origin, social.OriginScene, social.UnionId)
	}
	return fmt.Sprintf("%s_%s_%s", social.Origin, social.Channel, social.OpenId)
}

// MergeSameSocial 将两个相同的渠道中的信息进行合并，基于当前渠道，如果某个信息为空那么取被合并渠道上的值
func (social *Social) MergeSameSocial(s Social) {
	if social.OriginScene == "" {
		social.OriginScene = s.OriginScene
	}
	if social.UnionId == "" {
		social.UnionId = s.UnionId
	}
	if social.FirstOriginScene == "" {
		social.FirstOriginScene = s.FirstOriginScene
	}
	if social.SubscribeTime.Unix() <= 0 {
		social.SubscribeTime = s.SubscribeTime
	}
	if social.UnsubscribeTime.Unix() <= 0 {
		social.UnsubscribeTime = s.UnsubscribeTime
	}
	if social.FirstSubscribeTime.Unix() <= 0 || (s.FirstSubscribeTime.Unix() > 0 && s.FirstSubscribeTime.Before(social.FirstSubscribeTime)) {
		social.FirstSubscribeTime = s.FirstSubscribeTime
	}
	if !social.SubscribeTime.IsZero() && !social.FirstSubscribeTime.IsZero() && social.FirstSubscribeTime.After(social.SubscribeTime) {
		social.FirstSubscribeTime = social.SubscribeTime
	}
	if !social.Subscribed && social.SubscribeTime.Unix() > 0 && social.SubscribeTime.After(social.UnsubscribeTime) {
		social.Subscribed = true
	}
	if social.AuthorizeTime.Before(s.AuthorizeTime) {
		social.AuthorizeTime = s.AuthorizeTime
	}
	if social.FirstAuthorizeTime.Unix() <= 0 {
		social.FirstAuthorizeTime = s.FirstAuthorizeTime
	}
	if social.ChannelName == "" {
		social.ChannelName = s.ChannelName
	}
	if social.Nickname == "" {
		social.Nickname = s.Nickname
	}
	if social.Gender == "" {
		social.Gender = s.Gender
	}
	if social.City == "" {
		social.City = s.City
	}
	if social.Province == "" {
		social.Province = s.Province
	}
	if social.Country == "" {
		social.Country = s.Country
	}
	if social.Avatar == "" {
		social.Avatar = s.Avatar
	}
	if social.Language == "" {
		social.Language = s.Language
	}
	// once Authorized, it can not be set to false
	if social.Authorized || s.Authorized {
		social.Authorized = true
	}
	if social.Extra == "" {
		social.Extra = s.Extra
	}
	// 如果子 social 中 isActivationChannel=true 则设置主的为 true
	if s.IsActivationChannel {
		social.IsActivationChannel = true
	}
}

// UpdateBySocial 更新当前渠道的信息
func (social Social) UpdateBySocial(newSocial Social) Social {
	if !social.IsSameSocial(newSocial) {
		return social
	}

	newSocial.IsOriginal = social.IsOriginal
	newSocial.MergeSameSocial(social)
	if social.Authorized {
		newSocial.Authorized = social.Authorized
		newSocial.FirstAuthorizeTime = social.FirstAuthorizeTime
	}

	return newSocial
}

// IsSameSocial 判断是否是同一个渠道，当渠道来源与渠道标识相同且客户 openId 或 unionId 相同就视为同一个渠道
func (social Social) IsSameSocial(newSocial Social) bool {
	if social.Origin == newSocial.Origin && social.Channel == newSocial.Channel {
		if (social.OpenId == "" && social.UnionId == newSocial.UnionId) || social.OpenId == newSocial.OpenId {
			return true
		}
	}

	return false
}

// validateProperties 校验传入的客户属性的属性值是否符合属性定义中的限制，例如校验手机号格式等
func (self *Member) validateProperties(ctx context.Context, properties []MemberProperty, excludedMemberIds []bson.ObjectId) error {
	var (
		idPropertyMap = self.getPropertyMap()
	)
	for _, property := range properties {
		// 只对客户身上具有的客户属性的值做校验
		p, exists := idPropertyMap[property.Id]

		// 如果一个客户属性是必填项，那么客户身上必须具有这个属性且值不能为空
		// 这一条只对可见的客户属性有效
		if property.IsRequired && property.IsVisible {
			if !exists || core_util.IsEmpty(reflect.ValueOf(p.Value)) {
				data := map[string]interface{}{
					"id":   property.Id.Hex(),
					"name": property.Name,
				}
				return codes.NewErrorWithExtra(codes.MemberPropertyRequired, data)
			}
		}

		// 如果客户身上不存在某个客户属性且此属性不是必须的，那么视为通过
		if !exists {
			continue
		}

		// 当客户属性定义了正则匹配规则时，那么这个客户属性的属性值一定能够被转为字符串
		if property.Rule != "" {
			matched, _ := regexp.MatchString(property.Rule, cast.ToString(p.Value))

			// 空字符串视为通过匹配
			if cast.ToString(p.Value) == "" {
				matched = true
			}

			if !matched {
				data := map[string]interface{}{
					"id":    property.Id.Hex(),
					"name":  property.Name,
					"value": p.Value,
					"rule":  property.Rule,
				}
				return codes.NewErrorWithExtra(codes.MemberPropertyValueInvalid, data)
			}
		}

		// 如果一个客户属性有可选值，那么客户身上的此属性的值必须在可选项中
		if len(property.Options) > 0 {
			var matched bool
			switch property.Type {
			case PROPERTY_TYPE_RADIO:
				matched = true

				// 不校验空值
				value := cast.ToString(p.Value)
				if value != "" {
					matched = util.StrInArray(cast.ToString(p.Value), &property.Options)
				}
			case PROPERTY_TYPE_CHECKBOX:
				// 对于多选的属性值，如果值为空那么视为通过校验
				matched = true

				values := cast.ToStringSlice(p.Value)
				for _, val := range values {
					matched = util.StrInArray(val, &property.Options)
					if !matched {
						break
					}
				}
			}

			if !matched {
				data := map[string]interface{}{
					"id":    property.Id.Hex(),
					"name":  property.Name,
					"value": p.Value,
				}
				return codes.NewErrorWithExtra(codes.MemberPropertyValueInvalid, data)
			}
		}

		// 当客户属性限制值必须唯一时，可能没有限制属性值必填
		// 所以这里只要在值不为空的情况下校验唯一性即可
		if property.IsUnique && cast.ToString(p.Value) != "" {
			var err error

			// 单独校验手机号属性是否唯一，因为旧数据中手机号保存在 member.phone 中而不是客户属性中
			if property.Name == DEFAULT_PROPERTY_PHONE {
				err = self.validateDefaultPhoneUnique(ctx, excludedMemberIds, property)
			}

			// 单独校验邮箱属性，因为旧数据中邮箱是以区分大小写的保存和搜索的
			if property.Name == DEFAULT_PROPERTY_EMAIL {
				err = self.validateDefaultEmailUnique(ctx, excludedMemberIds, p, property)
			}

			if property.Type == PROPERTY_TYPE_INPUT {
				err = self.validateInputUnique(ctx, excludedMemberIds, p, property)
			}

			if property.Type == PROPERTY_TYPE_PHONE && property.Name != DEFAULT_PROPERTY_PHONE {
				err = self.validatePhoneUnique(ctx, excludedMemberIds, p, property)
			}

			if property.Type == PROPERTY_TYPE_EMAIL && property.Name != DEFAULT_PROPERTY_EMAIL {
				err = self.validateEmailUnique(ctx, excludedMemberIds, p, property)
			}

			if err != nil {
				return err
			}
		}

		switch property.Type {
		// 因为 IP 地址的正则表达式过长，所以这里使用 govalidator 来校验 IP
		case PROPERTY_TYPE_IP:
			ip := cast.ToString(p.Value)

			// 不校验空 IP
			if ip == "" {
				break
			}

			if !govalidator.IsIP(ip) {
				data := map[string]interface{}{
					"id":    property.Id.Hex(),
					"name":  property.Name,
					"value": p.Value,
				}
				return codes.NewErrorWithExtra(codes.MemberPropertyValueInvalid, data)
			}
		}
	}

	return nil
}

func (self *Member) getPropertyMap() map[bson.ObjectId]Property {
	return GetPropertyMap(self.Properties)
}

// getPropertyMapWithAge 生成当前客户客户属性的映射表，键为属性 id，值为客户属性值对象
// 此映射表中会额外包含一个年龄客户属性，值根据生日属性计算得到
func (self *Member) getPropertyMapWithAge(dbProperties []MemberProperty) map[bson.ObjectId]Property {
	var (
		birthdayId bson.ObjectId
		ageId      bson.ObjectId
		ageName    string
	)

	propertyMap := GetPropertyMap(self.Properties)

	for _, dbProperty := range dbProperties {
		if dbProperty.Name == DEFAULT_PROPERTY_BIRTHDAY {
			birthdayId = dbProperty.Id
			continue
		}

		if dbProperty.Name == DEFAULT_PROPERTY_AGE {
			ageId = dbProperty.Id
			ageName = dbProperty.Name
			continue
		}
	}

	birthday, exist := propertyMap[birthdayId]

	// no birthday means no age
	if !exist {
		return propertyMap
	}

	birthTimestamp := cast.ToInt64(birthday.Value)
	birthDate := util.TransIntTimestampToTime(birthTimestamp)
	age := GetAgeFromBirthday(birthDate)
	ageProperty := Property{
		Id:    ageId,
		Name:  ageName,
		Value: age,
	}
	propertyMap[ageId] = ageProperty

	return propertyMap
}

// GetAgeFromBirthday 根据客户的生日计算出年龄
func GetAgeFromBirthday(birthday time.Time) int {
	now := time.Now()
	years := now.Year() - birthday.Year()
	if now.YearDay() < birthday.YearDay() {
		years--
	}
	return years
}

// GetPropertyMap 获得客户属性的一个映射表，键为属性 id，值为客户属性定义
func GetPropertyMap(properties []Property) map[bson.ObjectId]Property {
	idProperties := make(map[bson.ObjectId]Property)
	for _, p := range properties {
		idProperties[p.Id] = p
	}

	return idProperties
}

// validateDefaultPhoneUnique 校验客户的手机号的唯一性，通过使用相同手机号查询的结果判断
func (self *Member) validateDefaultPhoneUnique(ctx context.Context, excludedMemberIds []bson.ObjectId, phoneProperty MemberProperty) error {
	if self.Phone == "" {
		return nil
	}
	condition := bson.M{
		"phone":     GetValueConditionWithEncrypted(ctx, self.Phone, &phoneProperty),
		"accountId": self.AccountId,
	}
	condition = generateAdditionalCondition(condition, excludedMemberIds)
	member := CMember.GetByCondition(ctx, condition)
	if member != nil && member.Id.Hex() != "" {
		data := map[string]interface{}{
			"phone":    self.Phone,
			"memberId": member.Id.Hex(),
		}
		return codes.NewErrorWithExtra(codes.PhoneNotUnique, data)
	}
	return nil
}

// ValidateEmailAndPhoneUnique 校验邮箱和手机号的唯一性
func (self *Member) ValidateEmailAndPhoneUnique(ctx context.Context) error {
	excludedMemberIds := []bson.ObjectId{}
	if self.Id.Hex() != "" {
		excludedMemberIds = append(excludedMemberIds, self.Id)
	}
	email := self.GetProperty(DEFAULT_PROPERTY_EMAIL)
	if email != nil {
		emailProperty := CMemberProperty.GetByName(ctx, DEFAULT_PROPERTY_EMAIL)
		if emailProperty == nil {
			return errors.NewNotExistsErrorWithMessage("email", "Property not exists")
		}
		if err := self.validateDefaultEmailUnique(ctx, excludedMemberIds, *email, *emailProperty); err != nil {
			return err
		}
	}

	phoneProperty := CMemberProperty.GetByName(ctx, DEFAULT_PROPERTY_PHONE)
	if phoneProperty == nil {
		return errors.NewNotExistsErrorWithMessage("phone", "Property not exists")
	}
	if err := self.validateDefaultPhoneUnique(ctx, excludedMemberIds, *phoneProperty); err != nil {
		return err
	}

	return nil
}

// validateDefaultEmailUnique 校验邮箱的唯一性
func (self *Member) validateDefaultEmailUnique(ctx context.Context, excludedMemberIds []bson.ObjectId, propertyEmail Property, emailMemberProperty MemberProperty) error {
	if cast.ToString(propertyEmail.Value) == "" {
		return nil
	}

	condition := bson.M{
		"properties": bson.M{
			"$elemMatch": bson.M{
				"id":    propertyEmail.Id,
				"value": GetValueConditionWithEncrypted(ctx, cast.ToString(propertyEmail.Value), &emailMemberProperty),
			},
		},
		"accountId": self.AccountId,
	}
	condition = generateAdditionalCondition(condition, excludedMemberIds)
	member := CMember.GetByCondition(ctx, condition)
	if member != nil && member.Id.Hex() != "" {
		data := map[string]interface{}{
			"email":      propertyEmail.Value,
			"propertyId": propertyEmail.Id.Hex(),
			"memberId":   member.Id.Hex(),
		}
		return codes.NewErrorWithExtra(codes.EmailNotUnique, data)
	}

	return nil
}

func (self *Member) validateInputUnique(ctx context.Context, excludedMemberIds []bson.ObjectId, propertyInput Property, memberProperty MemberProperty) error {
	return self.validatePropertyUnique(ctx, excludedMemberIds, propertyInput, codes.InputNotUnique, true, memberProperty)
}

func (self *Member) validatePhoneUnique(ctx context.Context, excludedMemberIds []bson.ObjectId, propertyInput Property, memberProperty MemberProperty) error {
	return self.validatePropertyUnique(ctx, excludedMemberIds, propertyInput, codes.PhoneNotUnique, false, memberProperty)
}

func (self *Member) validateEmailUnique(ctx context.Context, excludedMemberIds []bson.ObjectId, propertyInput Property, memberProperty MemberProperty) error {
	return self.validatePropertyUnique(ctx, excludedMemberIds, propertyInput, codes.EmailNotUnique, false, memberProperty)
}

// validatePropertyUnique 校验客户属性的唯一性，通过使用相同属性和属性值搜索，根据结果是否为空判断
func (self *Member) validatePropertyUnique(ctx context.Context, excludedMemberIds []bson.ObjectId, propertyInput Property, errorCode core_codes.Code, containsLowerValue bool, memberProperty MemberProperty) error {
	if cast.ToString(propertyInput.Value) == "" {
		return nil
	}

	elemMatch := bson.M{"id": propertyInput.Id}
	if containsLowerValue {
		elemMatch["lowerValue"] = GetValueConditionWithEncrypted(ctx, propertyInput.LowerValue, &memberProperty)
	} else {
		elemMatch["value"] = GetValueConditionWithEncrypted(ctx, cast.ToString(propertyInput.Value), &memberProperty)
	}

	condition := bson.M{
		"properties": bson.M{
			"$elemMatch": elemMatch,
		},
		"accountId": self.AccountId,
	}
	generateAdditionalCondition(condition, excludedMemberIds)
	member := CMember.GetByCondition(ctx, condition)
	if member != nil && member.Id.Hex() != "" {
		data := map[string]interface{}{
			"propertyId": propertyInput.Id.Hex(),
			"value":      propertyInput.Value,
			"memberId":   member.Id.Hex(),
		}
		return codes.NewErrorWithExtra(errorCode, data)
	}

	return nil
}

// ValidateSocials 校验客户渠道，如果不存在 openId 或者存在已有的客户具有相同的 unionId 或者来源不在已支持的来源列表中会返回错误
func (self *Member) ValidateSocials(ctx context.Context, excludedMemberIds []bson.ObjectId) error {
	// 去重
	socials := []Social{}
OUTER_FLAG:
	for _, social := range self.Socials {
		for i, s := range socials {
			if s.IsSameSocial(social) {
				social.MergeSameSocial(s)
				socials[i] = social
				continue OUTER_FLAG
			}
		}
		socials = append(socials, social)
	}
	self.Socials = socials

	var openIds, unionIds []string

	hasPortalOrigin := false
	for _, social := range self.Socials {
		if social.Origin == constant.PORTAL {
			if hasPortalOrigin {
				return codes.NewError(codes.PortalOriginRepeated)
			}
			hasPortalOrigin = true
			continue
		}

		if social.OpenId != "" {
			openIds = append(openIds, social.OpenId)
		}

		if social.UnionId != "" {
			unionIds = append(unionIds, social.UnionId)
		}

		// origin app:*, store, others, pos could have no openId
		if strings.HasPrefix(social.Origin, "app:") ||
			social.Origin == constant.STORE ||
			social.Origin == constant.OTHERS ||
			social.Origin == constant.POS ||
			strings.HasPrefix(social.Origin, constant.WDT_PREFIX) {
			continue
		}

		// jd 渠道，mixPhone 做为 unionId，此时的 openId 可以为空
		if social.Origin == constant.JD && social.OpenId == "" && social.UnionId != "" {
			continue
		}

		if social.OpenId == "" {
			return codes.NewError(codes.MissingOpenId)
		}

		if !util.ValidateOrigin(social.Origin) {
			return codes.NewError(codes.InvalidOrigin)
		}
	}

	if len(openIds) > 0 {
		dbMember := CMember.GetByOpenIds(ctx, self.AccountId, openIds, excludedMemberIds)
		if dbMember != nil {
			data := map[string]interface{}{
				"openIds":  openIds,
				"memberId": dbMember.Id.Hex(),
			}
			return codes.NewErrorWithExtra(codes.OpenIdExist, data)
		}
	}

	if len(unionIds) > 0 {
		dbMember := CMember.GetByUnionIds(ctx, unionIds, excludedMemberIds)
		if dbMember != nil {
			data := map[string]interface{}{
				"unionIds": unionIds,
				"memberId": dbMember.Id.Hex(),
			}
			return codes.NewErrorWithExtra(codes.UnionIdExist, data)
		}
	}

	return nil
}

// GetByUnionIds 通过 unionId 查询一个客户
func (*Member) GetByUnionIds(ctx context.Context, unionIds []string, excludedMemberIds []bson.ObjectId) *Member {
	condition := bson.M{
		"isDeleted": false,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"socials.unionId": bson.M{
			"$in": unionIds,
		},
	}
	condition = generateAdditionalCondition(condition, excludedMemberIds)

	member := new(Member)
	extension.DBRepository.FindOne(ctx, C_MEMBER, condition, member)
	if member.Id.Hex() != "" {
		DecryptMember(ctx, member)
		return member
	}

	return nil
}

// GetByOpenIds 通过 openId 查询一个客户
func (*Member) GetByOpenIds(ctx context.Context, accountId bson.ObjectId, opendIds []string, excludedMemberIds []bson.ObjectId) *Member {
	condition := bson.M{
		"isDeleted":      false,
		"accountId":      accountId,
		"socials.openId": bson.M{"$in": opendIds},
	}
	condition = generateAdditionalCondition(condition, excludedMemberIds)
	member := new(Member)
	extension.DBRepository.FindOne(ctx, C_MEMBER, condition, member)

	if member.Id.Hex() != "" {
		DecryptMember(ctx, member)
		return member
	}

	return nil
}

// GetByAccountAndSocial 查询具有指定渠道的客户且客户在此渠道内的身份标识与传入的 openId 相同
func (self *Member) GetByAccountAndSocial(ctx context.Context, accountId bson.ObjectId, channelId, openId string) (*Member, error) {
	member := &Member{}
	condition := bson.M{
		"accountId": accountId,
		"isDeleted": false,
		"socials": bson.M{
			"$elemMatch": bson.M{
				"channel": channelId,
				"openId":  openId,
			},
		},
	}

	err := extension.DBRepository.FindOne(ctx, C_MEMBER, condition, member)
	if err != nil {
		return nil, err
	}
	DecryptMember(ctx, member)
	return member, nil
}

func (self *Member) UpdateUnionIdBySocial(ctx context.Context, channelId, openId, unionId string) error {
	if unionId == "" {
		return nil
	}
	socialBrief := fmt.Sprintf("%s_%s_%s", channelId, openId, unionId)

	var updatedSocials []Social
	for _, social := range self.Socials {
		if socialBrief == fmt.Sprintf("%s_%s_%s", social.Channel, social.OpenId, social.UnionId) {
			return nil
		}
		if social.Channel == channelId && social.OpenId == openId {
			social.UnionId = unionId
		}
		updatedSocials = append(updatedSocials, social)
	}
	condition := bson.M{
		"_id": self.Id,
		"socials": bson.M{
			"$elemMatch": bson.M{
				"channel": channelId,
				"openId":  openId,
			},
		},
	}
	updator := bson.M{
		"$set": bson.M{
			"socials.$.unionId": unionId,
			"updatedAt":         time.Now(),
		},
	}

	if err := CMember.UpdateOne(ctx, condition, updator); err != nil {
		return err
	}

	// add updated result to self
	self.Socials = updatedSocials

	return nil
}

func (*Member) GetByOriginAndOpenId(ctx context.Context, accountId bson.ObjectId, origin string, openId string, excludedMemberIds []bson.ObjectId) *Member {
	condition := bson.M{
		"accountId": accountId,
		"isDeleted": false,
		"socials": bson.M{
			"$elemMatch": bson.M{
				"origin": origin,
				"openId": openId,
			},
		},
	}

	condition = generateAdditionalCondition(condition, excludedMemberIds)

	member := new(Member)
	extension.DBRepository.FindOne(ctx, C_MEMBER, condition, member)
	if member.Id.Hex() != "" {
		DecryptMember(ctx, member)
		return member
	}
	return nil
}

// UpdateBlockedStatus 更新当前客户的黑名单状态
func (self *Member) UpdateBlockedStatus(ctx context.Context, blockedStatus int, remark string, operatorId string, operatorType string) error {
	if self.BlockedStatus == blockedStatus {
		// https://gitlab.maiscrm.com/mai/home/<USER>/issues/32693
		self.BlockedStatusUpdatedAt = time.Now()
		return nil
	}

	now := time.Now()
	oldBlockedStatus := self.BlockedStatus

	err := CMember.UpdateOne(ctx,
		bson.M{"_id": self.Id},
		bson.M{"$set": bson.M{
			"blockedStatus":          blockedStatus,
			"blockedStatusUpdatedAt": now,
			"updatedAt":              now,
		}},
	)

	if err != nil {
		return codes.NewError(codes.UpdateMemberFail)
	}

	// Update fields of self model
	originBlockedStatus := self.BlockedStatus
	self.BlockedStatus = blockedStatus
	self.BlockedStatusUpdatedAt = now

	// create member info log and trigger status changed event
	core_component.GO(ctx, func(ctx context.Context) {
		self.createBlockedStatusChangedInfoLog(ctx, oldBlockedStatus, remark, operatorId, operatorType)
		self.triggerUpdateBlockedStatusEvent(ctx)

		CMemberScoreSyncStatus.SyncByBlockStatus(ctx, self, originBlockedStatus, blockedStatus)
	})

	return nil
}

func (self *Member) createDisabledStatusSwitchInfoLog(ctx context.Context) {
	logType := INFO_LOG_TYPE_SET_ENABLED
	if self.IsDisabled {
		logType = INFO_LOG_TYPE_SET_DISABLED
	}
	operatorId, operatorType := util.GetOperatorIdAndType(ctx)
	options := []MemberInfoLogOption{
		MemberInfoLogWithLogType(logType),
		MemberInfoLogWithOperator(operatorId, operatorType, ""),
	}
	self.AddInfoLog(ctx, true, false, options...)
}

func (self *Member) createActivateMemberInfoLog(ctx context.Context) {
	operatorId, operatorType := util.GetOperatorIdAndType(ctx)
	options := []MemberInfoLogOption{
		MemberInfoLogWithLogType(INFO_LOG_TYPE_ACTIVATED),
		MemberInfoLogWithOperator(operatorId, operatorType, ""),
	}
	self.AddInfoLog(ctx, true, false, options...)
}

func (self *Member) createBlockedStatusChangedInfoLog(ctx context.Context, oldBlockedStatus int, remark string, operatorId string, operatorType string) {
	// get member info log type
	logType := INFO_LOG_TYPE_REMOVE_FROM_BLACKLIST
	switch self.BlockedStatus {
	case BLOCKEDSTATUS_SUSPICIOUS:
		logType = INFO_LOG_TYPE_ADD_TO_SUSPICIOUSLIST
	case BLOCKEDSTATUS_BLOCKED:
		logType = INFO_LOG_TYPE_ADD_TO_BLACKLIST
	case BLOCKEDSTATUS_NORMAL:
		if oldBlockedStatus == BLOCKEDSTATUS_SUSPICIOUS {
			logType = INFO_LOG_TYPE_REMOVE_FROM_SUSPICIOUSLIST
		}
	}
	options := []MemberInfoLogOption{
		MemberInfoLogWithLogType(logType),
		MemberInfoLogWithDetail(remark),
		MemberInfoLogWithOperator(operatorId, operatorType, ""),
	}
	self.AddInfoLog(ctx, true, false, options...)
}

func (self *Member) triggerUpdateBlockedStatusEvent(ctx context.Context) {
	originalSocial := self.GetOriginalSocial()
	if originalSocial == nil {
		originalSocial = &Social{}
	}
	var (
		channelType = MESSAGE_CHANNEL_TYPE_OFFLINE
	)

	channelName := originalSocial.ChannelName
	if channelName == "" && bson.IsObjectIdHex(originalSocial.Channel) {
		channelInfo, _ := component.WeConnect.GetChannel(ctx, originalSocial.Channel)
		if channelInfo != nil {
			channelName = channelInfo.Name
		}
	}
	if channelName != "" {
		channelType = MESSAGE_CHANNEL_TYPE_ONLINE
	}

	channel := component.MessageChannel{
		Id:     originalSocial.Channel,
		Name:   channelName,
		Social: originalSocial.Origin,
		Type:   channelType,
	}

	blockedStatusEventType := EVENT_BLOCKEDSTATUS_UNBLOCKED
	switch self.BlockedStatus {
	case BLOCKEDSTATUS_SUSPICIOUS:
		blockedStatusEventType = EVENT_BLOCKEDSTATUS_SUSPICIOUS
	case BLOCKEDSTATUS_BLOCKED:
		blockedStatusEventType = EVENT_BLOCKEDSTATUS_BLOCKED
	}

	memberId := self.Id.Hex()
	data := map[string]interface{}{
		"type":                      blockedStatusEventType,
		"member_id":                 memberId,
		"account_id":                self.AccountId.Hex(),
		"blocked_status":            self.BlockedStatus,
		"blocked_status_updated_at": util.TransTime(self.BlockedStatusUpdatedAt),
	}
	component.MessageQueue.SendPortalMessage(ctx, channel, data, "member_"+memberId, "member")
}

// GetScoreExpiredBeforeThisMonthByYear 计算当前客户在积分按照 365 天过期设置的情况下即将过期的积分数量
// 此方法适用于 v1 版本积分规则，计算当前客户过去一年所有增加积分的历史记录并求和，如果客户当前可用积分大于这个值那么差值的绝对值就是过期积分
func (self *Member) GetScoreExpiredBeforeThisMonthByYear(ctx context.Context) int {
	now := time.Now()
	currScore := self.Score
	startMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	elevenMonthsAgo := startMonth.AddDate(0, -11, 0)
	score := getTotalScoreAfter(ctx, self.AccountId, self.Id, elevenMonthsAgo)
	if int64(currScore) > score {
		return int(int64(currScore) - score)
	}
	return 0
}

// GetScoreExpiredBeforeThisMonthByEndOfMonth 计算当前客户在按指定月数过期的设置下即将过期的积分
// 此方法适用于 v1 版本积分规则，计算在当前周期内所有获得的积分，当前客户可用积分超过部分即为过期积分
func (self *Member) GetScoreExpiredBeforeThisMonthByEndOfMonth(ctx context.Context, month int) int {
	now := time.Now()
	currScore := self.Score
	startMonth := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	elevenMonthsAgo := startMonth.AddDate(0, -(month - 1), 0)
	score := getTotalScoreAfter(ctx, self.AccountId, self.Id, elevenMonthsAgo)
	if int64(currScore) > score {
		return int(int64(currScore) - score)
	}
	return 0
}

// GetScoreExpiredBeforeThisYearByEndOfYear 计算当前客户在按自然年过期规则的情况下，下次清零积分时将会过期的积分
// 此方法适用于 v1 版本积分规则，向前计算一个自然年的时间中所有获取的积分，客户当前可用积分多出的部分即为过期积分
func (self *Member) GetScoreExpiredBeforeThisYearByEndOfYear(ctx context.Context, interval int) int {
	now := time.Now()
	currScore := self.Score
	startMonth := time.Date(now.Year(), 12, 31, 0, 0, 0, 0, now.Location())
	yearsAgo := startMonth.AddDate(-interval, 0, 1)
	score := getTotalScoreAfter(ctx, self.AccountId, self.Id, yearsAgo)
	if int64(currScore) > score {
		return int(int64(currScore) - score)
	}
	return 0
}

// GetStatsByTags 计算传入的每个标签各自关联的客户数量
func (self *Member) GetStatsByTags(ctx context.Context, accountId string, tags []string) (map[string]int, error) {
	tagsStats := make(map[string]int)
	for _, tag := range tags {
		defaultCondition := generateDefaultCondition(accountId)
		condition := appendQueryCondition(defaultCondition, "tags", tag)
		count, err := extension.DBRepository.Count(ctx, C_MEMBER, condition)
		if err != nil {
			return nil, err
		}
		tagsStats[tag] = count
	}
	return tagsStats, nil
}

func (*Member) GetByTagsAndOrigin(ctx context.Context, accountId string, tags []string, socialOrigin string, listCondition *request.ListCondition) ([]Member, int) {
	condition := generateDefaultCondition(accountId)
	tagCondition := bson.M{"$in": tags}
	condition = appendQueryCondition(condition, "tags", tagCondition)
	if socialOrigin != "" {
		condition = appendQueryCondition(condition, "socials.origin", socialOrigin)
	}
	var (
		members   []Member
		pageIndex = 1
		pageSize  = 500
		orderBy   []string
	)
	if listCondition != nil {
		pageIndex = int(listCondition.Page)
		pageSize = int(listCondition.PerPage)
		orderBy = listCondition.OrderBy
	}
	page := extension.PagingCondition{
		Selector:  condition,
		PageIndex: pageIndex,
		PageSize:  pageSize,
		Sortor:    orderBy,
	}
	total, _ := extension.DBRepository.FindByPagination(ctx, C_MEMBER, page, &members)
	DecryptMembers(ctx, members)
	return members, total
}

// IssueCardToMembers 向客户发放会员卡，对于初次激活的会员还会发放激活奖励
func (*Member) IssueCardToMembers(ctx context.Context, needToActivate bool, ids []string, cardId string, cardNumber string) (reErr error) {
	accountId := util.GetAccountId(ctx)
	// 入参校验
	if cardId == "" || !bson.IsObjectIdHex(cardId) {
		return codes.NewError(codes.InvalidMongoId)
	}

	// 从数据库中查询会员卡详情以及客户详情
	card := CMembershipCard.GetById(ctx, bson.ObjectIdHex(cardId), bson.ObjectIdHex(accountId))
	members := CMember.GetByIds(ctx, bson.ObjectIdHex(accountId), util.ToMongoIds(ids))
	if len(members) == 0 {
		return codes.NewError(codes.MemberNotFound)
	}

	// 校验能否向这些客户方法会员卡
	err := CMember.ValidateIssuingCardToMembers(card, members)

	if err != nil {
		return err
	}

	// 在发放会员卡之前要先将未激活的客户激活为会员，这里过滤出未激活的客户
	memberIds := make([]bson.ObjectId, len(members))
	inactivatedMemberIds := []bson.ObjectId{}

	for index, member := range members {
		memberIds[index] = member.Id

		if !member.IsActivated {
			inactivatedMemberIds = append(inactivatedMemberIds, member.Id)
		}
	}

	selector := bson.M{
		"_id": bson.M{
			"$in": memberIds,
		},
	}

	setter := bson.M{
		"cardId":          bson.ObjectIdHex(cardId),
		"cardProvideTime": time.Now(),
		"updatedAt":       time.Now(),
	}

	if len(ids) == 1 && !util.IsEmptyValues(cardNumber) {
		setter["cardNumber"] = cardNumber
	}

	updator := bson.M{
		"$set": setter,
	}

	activatedAt := time.Now()

	if needToActivate {
		// 激活还没激活的客户
		if len(inactivatedMemberIds) > 0 {
			activateMemberSelector := bson.M{
				"_id": bson.M{
					"$in": inactivatedMemberIds,
				},
			}

			activateMemberUpdator := bson.M{
				"$set": bson.M{
					"isActivated": true,
					"activatedAt": activatedAt,
					"updatedAt":   time.Now(),
				},
			}

			CMember.UpdateAll(ctx, activateMemberSelector, activateMemberUpdator)
		}
	}

	defer func() {
		// 如果 cardNumber 重复会导致 panic，这里处理一下只返回错误
		if err := recover(); err != nil {
			mgoErr := err.(error)
			if !qmgo.IsDup(mgoErr) {
				panic(mgoErr)
			}
			reErr = codes.NewError(codes.CardNumberExist)
			return
		}
	}()
	updatedCount, _ := CMember.UpdateAll(ctx, selector, updator)

	if updatedCount > 0 {
		core_component.GO(ctx, func(ctx context.Context) {
			for _, member := range members {
				oldCard := new(MembershipCard)

				if member.CardId.Hex() != "" {
					extension.DBRepository.FindByPK(ctx, C_MEMBERSHIP_CARD, member.CardId, oldCard)
				}

				if oldCard.Id.Hex() != card.Id.Hex() {
					member.addInfoLogWhenCardUpdated(ctx, card, oldCard)
				}

				if !member.IsActivated && needToActivate {
					member.IsActivated = true
					member.ActivatedAt = activatedAt
					member.CardId = card.Id

					err := member.TriggerMemberActivatedEvent(ctx, "portal", "")

					member.TrackMemberActivatedEvent(ctx, ChannelInfo{}, nil)
					member.createActivateMemberInfoLog(ctx)

					if err != nil {
						log.Error(ctx, "Fail to trigger activated event", log.Fields{
							"error": err.Error(),
						})
					}
				}
			}
		})
		for _, member := range members {
			member.TrackMembershipCardReceivedEvent(ctx, card, ChannelInfo{Origin: constant.PORTAL}, true)
			if needToActivate && util.ObjectIdInArray(member.Id, &inactivatedMemberIds) {
				RewardByRuleName(ctx, &member, RULE_FIRST_CARD, &ChannelInfo{})
			}
		}
	}

	return nil
}

// ValidateIssuingCardToMembers 验证是否可以向传入的客户方法会员卡，所有的客户必须都不在黑名单中，所有的客户不能被禁用
func (*Member) ValidateIssuingCardToMembers(card *MembershipCard, members []Member) error {
	if card == nil {
		return codes.NewError(codes.CardNotFound)
	}

	err := ensureMembersIsNotBlocked(members)
	if err != nil {
		return err
	}

	err = ensureMembersIsNotDisabled(members)
	if err != nil {
		return err
	}

	return nil
}

func (self *Member) AddNewWeconnectSocail(ctx context.Context, social Social) error {
	for _, s := range self.Socials {
		if s.Channel == social.Channel && s.OpenId == social.OpenId {
			return nil
		}
	}
	condition := bson.M{"_id": self.Id}
	updator := bson.M{
		"$addToSet": bson.M{"socials": social},
		"$set":      bson.M{"updatedAt": time.Now()},
	}
	if err := CMember.UpdateOne(ctx, condition, updator); err != nil {
		return err
	}
	// Fix bug: https://gitlab.maiscrm.com/mai/home/<USER>/4931
	// when updated in db, member itself also need update socials
	self.Socials = append(self.Socials, social)

	return nil
}

// getJobArgs 此方法获取启动 portal-backend MergeMember job 的参数
func getJobArgs(mainMember Member, otherMembers []Member, operatorId string, remark string) JobArgs {
	mainMemberId := mainMember.Id.Hex()
	accountId := mainMember.AccountId.Hex()
	var otherMemberIds []string

	for _, member := range otherMembers {
		otherMemberIds = append(otherMemberIds, member.Id.Hex())
	}

	return JobArgs{
		AccountId:     accountId,
		OtherMemberId: otherMemberIds,
		MainMemberId:  mainMemberId,
		OperatorId:    operatorId,
		Remark:        remark,
	}
}

// GetCardCount 计算当前会员卡关联的激活会员数量
func GetCardCount(ctx context.Context, cardId bson.ObjectId) int {
	selector := bson.M{
		"cardId":      cardId,
		"isActivated": true,
		"isDeleted":   false,
	}
	count, _ := extension.DBRepository.Count(ctx, C_MEMBER, selector)
	return count
}

// GetOriginScene 根据渠道身份信息获取来源场景
func (*Member) GetOriginScene(ctx context.Context, openId, channelId string) string {
	if "" == openId || "" == channelId {
		return ""
	}

	follower, _ := component.WeConnect.GetFollower(ctx, channelId, openId)
	if nil == follower {
		return ""
	}

	return follower.SubscribeSource
}

func (self *Member) UpdateSocials(ctx context.Context) error {
	return CMember.UpdateOne(ctx,
		bson.M{"_id": self.Id, "accountId": util.GetAccountIdAsObjectId(ctx)},
		bson.M{"$set": bson.M{
			"socials":   self.Socials,
			"updatedAt": self.UpdatedAt,
		}},
	)
}

// UpdateSpecifiedWholeSocial 更新一个客户的指定的渠道，覆盖式更新，存在并发问题
func (self *Member) UpdateSpecifiedWholeSocial(ctx context.Context, social Social) error {
	selector := bson.M{
		"_id":       self.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"socials": bson.M{
			"$elemMatch": bson.M{
				"channel": social.Channel,
				"openId":  social.OpenId,
			},
		},
	}
	setter := bson.M{
		"socials.$": social,
		"updatedAt": time.Now(),
	}

	return CMember.UpdateOne(ctx, selector, bson.M{"$set": setter})
}

func ToDbGenderValue(gender string) string {
	result := strings.ToLower(gender)
	if result != FEMALE && result != MALE {
		return UNKNOWN
	}
	return result
}

// UpdateSpecifiedSocialFields 用非覆盖的方式更新客户渠道
func (*Member) UpdateSpecifiedSocialFields(ctx context.Context, memberId, channelId string, fields map[string]interface{}) error {
	setter := bson.M{
		"updatedAt": time.Now(),
	}

	for key, val := range fields {
		if util.StrInArray(key, &EditableSocialFields) {
			if key == "gender" {
				val = ToDbGenderValue(val.(string))
			}

			if util.StrInArray(key, &[]string{"authorizeTime", "firstAuthorizeTime", "subscribeTime", "unsubscribeTime"}) {
				if t, err := time.Parse(core_util.RFC3339Mili, val.(string)); err == nil {
					setter["socials.$."+key] = t
				}
				continue
			}

			setter["socials.$."+key] = val
		}
	}

	selector := bson.M{
		"_id":       bson.ObjectIdHex(memberId),
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"socials": bson.M{
			"$elemMatch": bson.M{
				"channel": channelId,
			},
		},
	}

	if openId, exists := fields["openId"]; exists && openId != "" {
		selector["socials"] = bson.M{
			"$elemMatch": bson.M{
				"channel": channelId,
				"openId":  openId.(string),
			},
		}
	}

	return CMember.UpdateOne(ctx,
		selector,
		bson.M{"$set": setter},
	)
}

// ActivateSocial 将客户的一个渠道置为激活状态并记录激活时间
func (self *Member) ActivateSocial(channel ChannelInfo, subscribeTime time.Time) {
	for i, social := range self.Socials {
		if social.Channel != channel.Id || social.Origin != channel.Origin {
			continue
		}
		// 微信公众号 subscribed 表示关注状态，不能更改
		if social.Origin != constant.WECHAT {
			social.Subscribed = true
		}
		if social.Origin != constant.WECHAT && social.SubscribeTime.IsZero() {
			social.SubscribeTime = subscribeTime
		}
		if social.Origin != constant.WECHAT && social.FirstSubscribeTime.IsZero() {
			social.FirstSubscribeTime = social.SubscribeTime
		}
		if social.Origin == constant.WECHAT && social.Extra == "" {
			social.Extra = subscribeTime.Format(time.RFC3339)
		}
		self.Socials[i] = social
		break
	}
}

// SplitSocials 分离 socials，返回来源渠道和其他渠道
func SplitSocials(socials []Social) (Social, []Social) {
	var (
		originSocial Social
		otherSocials []Social
	)

	for _, social := range socials {
		if social.IsOriginal {
			originSocial = social
		} else {
			otherSocials = append(otherSocials, social)
		}
	}

	return originSocial, otherSocials
}

func (self *Member) addInfoLogWhenCardUpdated(ctx context.Context, newCard *MembershipCard, oldCard *MembershipCard) {
	updatedInfoLog := map[string]interface{}{
		"card": map[string]interface{}{
			"from": map[string]interface{}{
				"id":   oldCard.Id.Hex(),
				"name": oldCard.Name,
			},
			"to": map[string]interface{}{
				"id":   newCard.Id.Hex(),
				"name": newCard.Name,
			},
		},
	}
	options := []MemberInfoLogOption{
		MemberInfoLogWithLogType(INFO_LOG_TYPE_UPDATE_CARD_INFO),
		MemberInfoLogWithDetail(updatedInfoLog),
	}
	self.AddInfoLog(ctx, true, false, options...)
}

// TriggerMemberActivatedEvent 触发客户激活事件，此事件是 portal 事件，在运营后台可以通过 webhook 订阅
func (self *Member) TriggerMemberActivatedEvent(ctx context.Context, origin, channelId string) error {
	if !self.IsActivated {
		return nil
	}
	memberId := self.Id.Hex()
	accountId := self.AccountId.Hex()

	data := map[string]interface{}{
		"type":         component.EVENT_MEMBER_ACTIVATED,
		"account_id":   accountId,
		"member_id":    memberId,
		"is_activated": true,
		"activated_at": self.ActivatedAt.Format(time.RFC3339),
		"ip":           util.GetClientIp(ctx),
	}

	messageChannel := component.GetWebhookMessageChannel(ctx, origin, channelId)

	requiredFields := []string{"account_id", "type"}
	err := util.ValidateRequiredFields(data, requiredFields)
	component.MessageQueue.SendPortalMessage(ctx, messageChannel, data, "member_"+memberId, "member")

	return err
}

// checkShouldMarkMemberSuspicious 判断当前客户是否应该被标记为黑名单或者可疑名单并返回描述信息
func (self *Member) checkShouldMarkMemberSuspicious(ctx context.Context, interval uint64, threshold, operation int, updatedAt int64) (string, bool) {
	opt := "获取"
	if operation == 0 {
		opt = "消耗"
	}
	description := fmt.Sprintf("%v 秒内，%v 积分 %v 次及以上", interval, opt, threshold)
	startTime := time.Now().Add(time.Duration(-interval) * time.Second)
	if self.BlockedStatusUpdatedAt.Unix() > startTime.Unix() && self.BlockedStatus == BLOCKEDSTATUS_NORMAL {
		// Recount when member is removed from suspicious list.
		startTime = self.BlockedStatusUpdatedAt
	}

	// Generally, count ScoreHistory between the scope (-interval, now),
	// When the rule.updatedAt is in this scope, then count ScoreHistory between (rule.updatedAt, now)
	if updatedAt > startTime.Unix() {
		startTime = util.TransUnixToTime(updatedAt)
	}
	count := CScoreHistory.CountByMemberId(ctx, self.Id, operation, startTime)
	if count >= threshold {
		return description, true
	}
	return "", false
}

// MarkMemberSuspicious 在客户消耗积分后根据后台设置的黑名单和可疑名单规则将客户拉黑
func (self *Member) MarkMemberSuspicious(ctx context.Context, suspiciousRule map[string]uint64) {
	memberWhiteList, _ := CMemberWhiteList.FindOneByMemberId(ctx, self.Id, false)
	if memberWhiteList != nil {
		return
	}
	isEnabled := suspiciousRule["blackList.isEnabled"] == 1
	interval := suspiciousRule["blackList.interval"]
	threshold := int(suspiciousRule["blackList.threshold"])
	operation := int(suspiciousRule["blackList.operation"])
	updatedAt := int64(suspiciousRule["blackList.updatedAt"])
	if isEnabled {
		blackListDescription, shouldMarkBlack := self.checkShouldMarkMemberSuspicious(ctx, interval, threshold, operation, updatedAt)
		if shouldMarkBlack {
			self.UpdateBlockedStatus(ctx, BLOCKEDSTATUS_BLOCKED, blackListDescription, "", OPERATOR_TYPE_OF_SYSTEM)
			return
		}
	}
	interval = suspiciousRule["interval"]
	threshold = int(suspiciousRule["threshold"])
	operation = int(suspiciousRule["operation"])
	updatedAt = int64(suspiciousRule["updatedAt"])
	isDisabled := suspiciousRule["isDisabled"] == 1
	if !isDisabled {
		suspiciousDescription, shouldMarkSuspicious := self.checkShouldMarkMemberSuspicious(ctx, interval, threshold, operation, updatedAt)
		if shouldMarkSuspicious {
			self.UpdateBlockedStatus(ctx, BLOCKEDSTATUS_SUSPICIOUS, suspiciousDescription, "", OPERATOR_TYPE_OF_SYSTEM)
		}
	}
}

// GetInactivatedMembers 根据渠道信息查询非激活状态的会员
func (*Member) GetInactivatedMembers(ctx context.Context, accountId bson.ObjectId, openIds []string, unionIds []string) []Member {
	var condition []bson.M
	if len(openIds) > 0 {
		openIdCondition := generateInactivatedCondition(accountId)
		openIdCondition["socials.openId"] = bson.M{
			"$in": openIds,
		}
		condition = append(condition, openIdCondition)
	}

	if len(unionIds) > 0 {
		unionCondition := generateInactivatedCondition(accountId)
		unionCondition["socials.unionId"] = bson.M{
			"$in": unionIds,
		}
		condition = append(condition, unionCondition)
	}

	if len(condition) > 0 {
		var members []Member
		extension.DBRepository.FindAll(ctx, C_MEMBER, bson.M{"$or": condition}, nil, 0, &members)
		DecryptMembers(ctx, members)
		return members
	}

	return nil
}

func generateInactivatedCondition(accountId bson.ObjectId) bson.M {
	condition := bson.M{
		"accountId":   accountId,
		"isActivated": false,
		"isDeleted":   false,
	}

	return condition
}

// needMergeAvatar 判断是否需要合并头像，取非默认头像
func (self *Member) needMergeAvatar(otherMember Member) bool {
	if !self.isDefaultAvatar() || otherMember.isDefaultAvatar() {
		return false
	}

	return true
}

// needMergeName 判断是否需要合并姓名
func (self *Member) needMergeName(otherMember Member) bool {
	return !self.hasName() && otherMember.hasName()
}

// 判断当前客户信息中是否包含姓名属性
func (self *Member) hasName() bool {
	for _, p := range self.Properties {
		if p.Name == DEFAULT_PROPERTY_NAME {
			if !core_util.IsEmpty(reflect.ValueOf(p.Value)) {
				return true
			}
		}
	}
	return false
}

// isDefaultAvatar 判断当前客户的头像是否是默认头像
func (self *Member) isDefaultAvatar() bool {
	return isDefaultAvatar(self.Avatar)
}

// isDefaultAvatar 判断传入的头像是否是默认头像，头像为空或者包含默认头像路径或名称的为默认头像
func isDefaultAvatar(avatar string) bool {
	if avatar == "" {
		return true
	}

	// 尽早更新默认头像
	if strings.HasSuffix(avatar, DEFAULT_AVATAR) {
		return true
	}

	// 尽早更新微信地址保存的头像，因为链接会过期
	if strings.Contains(avatar, WX_IMAGE_PATTERN) {
		return true
	}

	return false
}

// mergeName 将传入客户的姓名合并到当前客户上
func (self *Member) mergeName(member Member) {
	var name interface{}

	for _, property := range member.Properties {
		if property.Name == DEFAULT_PROPERTY_NAME {
			name = property.Value
		}
	}

	for index, property := range self.Properties {
		if property.Name == DEFAULT_PROPERTY_NAME {
			property.Value = name
			self.Properties[index] = property
		}
	}
}

// mergeGender 将传入客户的性别合并到当前客户上
func (self *Member) mergeGender(member Member) {
	var gender interface{}

	for _, property := range member.Properties {
		if property.Name == DEFAULT_PROPERTY_GENDER {
			gender = property.Value
		}
	}
	if gender == nil {
		return
	}

	for index, property := range self.Properties {
		if property.Name == DEFAULT_PROPERTY_GENDER {
			if property.Value != UNKNOWN {
				return
			}
			property.Value = gender
			self.Properties[index] = property
		}
	}
}

// UpdateActivationSource 更新客户的激活来源，只有当当前客户激活来源为空时有效
// 此方法会根据渠道信息和激活场景拼接激活来源字段并更新，同时创建对应的 MemberSource 记录
func (self *Member) UpdateActivationSource(ctx context.Context, channelInfo ChannelInfo) {
	if self.ActivationSource == "" {
		if self.FormatActivationSource(channelInfo.Origin, channelInfo.OriginScene) {
			condition := Common.GenDefaultConditionById(ctx, self.Id)
			extension.DBRepository.UpdateOne(ctx, C_MEMBER, condition, bson.M{
				"$set": bson.M{
					"activationSource": self.ActivationSource,
					"updatedAt":        time.Now(),
				},
			})
			CMemberSource.Create(ctx, "", self.ActivationSource)
		}
	}
}

// AfterActivated 在当前客户被激活后执行操作
func (self *Member) AfterActivated(ctx context.Context, channelInfo ChannelInfo, eventMeta *EventMeta) {
	// 激活后更新客户激活来源字段
	self.UpdateActivationSource(ctx, channelInfo)
	// 触发客户激活事件
	self.TriggerMemberActivatedEvent(ctx, channelInfo.Origin, channelInfo.Id)
	self.TrackMemberActivatedEvent(ctx, channelInfo, eventMeta)
	// 修改客户的注册门店、注册导购
	self.UpdateActivationProperties(ctx, eventMeta)
	// 创建激活的信息记录
	self.createActivateMemberInfoLog(ctx)

	channelMatched := false
	activationChannelExists := false
	social := Social{}
	for _, s := range self.Socials {
		if s.IsActivationChannel {
			activationChannelExists = true
			break
		}
		if s.Channel == channelInfo.Id && s.Origin == channelInfo.Origin && !channelMatched {
			channelMatched = true
			social = s
		}
	}

	// 如果之前不存在激活渠道，且传入渠道匹配到 member 的某个渠道，把该渠道设置为激活渠道
	if !activationChannelExists && channelMatched {
		social.IsActivationChannel = true
		// 微信公众号 subscribed 表示关注状态，不能更改
		if social.Origin != constant.WECHAT {
			social.Subscribed = true
		}
		if social.Origin != constant.WECHAT && social.SubscribeTime.IsZero() {
			social.SubscribeTime = time.Now()
		}
		if channelInfo.OriginScene != "" {
			social.OriginScene = channelInfo.OriginScene
		}
		if social.FirstOriginScene == "" {
			social.FirstOriginScene = social.OriginScene
		}
		if social.Origin != constant.WECHAT && social.FirstSubscribeTime.IsZero() {
			social.FirstSubscribeTime = social.SubscribeTime
		}
		if social.Origin == constant.WECHAT && social.Extra == "" {
			social.Extra = time.Now().Format(time.RFC3339)
		}
		self.UpdateSpecifiedWholeSocial(ctx, social)
		// db 更新，self 也对应更新，防止后面有用到 self 本身的旧数据，把更新后的数据又更为旧数据
		for i, s := range self.Socials {
			if s.Channel == channelInfo.Id && s.Origin == channelInfo.Origin {
				self.Socials[i].IsActivationChannel = true
				break
			}
		}
	}
}

// FormatActivationSource 将格式化后的激活来源更新到当前客户上，如果当前渠道不支持那么返回 false
func (self *Member) FormatActivationSource(origin, scene string) bool {
	if origin == "" {
		return false
	}
	self.ActivationSource = constant.GenActivationSource(origin, scene)
	if self.ActivationSource == "" {
		return false
	}
	return true
}

// TrackMembershipCardReceivedEvent 触发会员卡接收的客户事件
func (self *Member) TrackMembershipCardReceivedEvent(ctx context.Context, memberCard *MembershipCard, channel ChannelInfo, isSystem bool) {
	subType := component.MAIEVENT_RECEIVE_MEMBERSHIP_CARD
	channelName := constant.PORTAL
	if channel.Origin != constant.PORTAL {
		channelName = channel.Name
	}

	if isSystem {
		subType = component.MAIEVENT_SEND_MEMBERSHIP_CARD
	}

	customerEvent := component.CustomerEventBody{
		AccountId:  self.AccountId.Hex(),
		MemberId:   self.Id.Hex(),
		ChannelId:  channel.Id,
		OpenId:     self.GetOpenIdByChannelId(channel.Id),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    subType,
		CreateTime: time.Now().UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"cardId":      memberCard.Id.Hex(),
			"cardName":    memberCard.Name,
			"channelId":   channel.Id,
			"channelName": channelName,
		},
	}
	customerEvent.Send(ctx)
}

// GenDefaultValueStage 生成默认的成长值阶段
func (self *Member) GenDefaultValueStage() {
	// 只更新阶段字段，因为价值中的其他字段都是整型类型，在不赋值的情况下他们将被初始化为默认值 0
	self.MemberValue.Stage = GetAdvancingStage(MEMBER_STAGE_UNKNOWN, self.MemberValue.Stage)
}

// max 获取传入整数中的最大值
func max(numbers ...int) int {
	var max = math.MinInt64
	for _, n := range numbers {
		max = int(math.Max(float64(max), float64(n)))
	}

	return max
}

func (self *Member) CountInformationGrades(ctx context.Context) int64 {
	dbProperties := CMemberProperty.GetByAccount(ctx, util.GetAccountId(ctx))

	return self.CountInformationGradesWithProperties(dbProperties)
}

func (self *Member) CountInformationGradesWithProperties(dbProperties []MemberProperty) int64 {
	var (
		grades        int64
		idPropertyMap map[bson.ObjectId]Property
	)

	idPropertyMap = self.getPropertyMapWithAge(dbProperties)

	for _, dbProperty := range dbProperties {
		property, exists := idPropertyMap[dbProperty.Id]
		if !exists {
			continue
		}

		grades += dbProperty.CountInformationGrades(property)
	}

	self.MemberValue.InformationGrades = grades
	self.CountTotalGrades()

	return grades
}

func (self *Member) CountTotalGrades() int64 {
	self.MemberValue.Grades = self.MemberValue.Engagement + self.MemberValue.InformationGrades

	return self.MemberValue.Grades
}

// GetAndCacheAddress 此方法首先校验地理坐标的客户属性，缓存没有被缓存的信息，最终计算客户价值
func (self *Member) GetAndCacheAddress(ctx context.Context, dbProperties []MemberProperty) {
	if !needCacheAddress(self.Properties, dbProperties) {
		return
	}

	core_component.GO(ctx, func(ctx context.Context) {
		var updatedLocations []MemberProperty
		self.Properties, updatedLocations = getAddressForLocation(ctx, dbProperties, self.Properties)

		// 因为有的信息可能已经被缓存过了，所以重新计算客户价值
		self.CountInformationGradesWithProperties(dbProperties)

		// 保存地理信息属性并更新客户价值
		self.updateForLocation(ctx, updatedLocations)
	})
}

// needCacheAddress 判断是否需要缓存地址
func needCacheAddress(properties []Property, dbProperties []MemberProperty) bool {
	ids := []string{}
	for _, dbProperty := range dbProperties {
		if dbProperty.Type == PROPERTY_TYPE_LOCATION {
			ids = append(ids, dbProperty.Id.Hex())
		}
	}

	needCacheAddress := false
	for _, property := range properties {
		if !util.StrInArray(property.Id.Hex(), &ids) {
			continue
		}

		if core_util.IsZero(property.AddressCache) {
			needCacheAddress = true
		}
	}

	return needCacheAddress
}

// getAddressForLocation 此方法更新所有的地理位置类型的属性，基于腾讯定位服务将省市区等缓存到 AddressCache 中
func getAddressForLocation(ctx context.Context, dbProperties []MemberProperty, properties []Property) (allProperties []Property, updatedLocations []MemberProperty) {
	propertyMap := GetPropertyMap(properties)

	allProperties = []Property{}
	updatedLocations = []MemberProperty{}

	for _, dbProperty := range dbProperties {
		if dbProperty.Type != PROPERTY_TYPE_LOCATION {
			continue
		}

		property, exists := propertyMap[dbProperty.Id]
		if !exists {
			continue
		}

		if !core_util.IsZero(property.AddressCache) {
			continue
		}

		geoLocation, ok := util.GetGeoLocation(property.Value)
		if !ok {
			continue
		}

		lbsResp, err := core_component.TencentLBSClient.GetReverseGeocoding(ctx, geoLocation)
		if err != nil {
			log.Error(ctx, "Failed to get reverse geocoding", log.Fields{
				"location":     geoLocation,
				"errorMessage": err.Error(),
			})
			continue
		}

		address := lbsResp.AddressComponent
		property.AddressCache = bson.M{
			"country":  address.Nation,
			"province": util.FormatWechatProvince(address.Province),
			"city":     util.FormatWechatCity(address.City),
			"district": util.FormatWechatCity(address.District),
			"detail":   address.Street,
		}

		propertyMap[dbProperty.Id] = property
		updatedLocations = append(updatedLocations, dbProperty)
	}

	for _, property := range propertyMap {
		allProperties = append(allProperties, property)
	}

	return
}

func (self *Member) updateForLocation(ctx context.Context, updatedLocations []MemberProperty) {

	propertyMap := self.getPropertyMap()
	now := time.Now()

	for _, locationProperty := range updatedLocations {
		if locationProperty.Type != PROPERTY_TYPE_LOCATION {
			continue
		}

		property, exists := propertyMap[locationProperty.Id]
		if !exists {
			continue
		}

		selector := bson.M{
			"_id":              self.Id,
			"properties.id":    property.Id,
			"properties.value": GetValueConditionWithEncrypted(ctx, property.Value, &locationProperty),
			"isDeleted":        false,
		}

		encryptedProperty, _ := EncryptMemberProperty(ctx, property, locationProperty)
		updater := bson.M{
			"$set": bson.M{
				"properties.$": encryptedProperty,
				"updatedAt":    now,
				"memberValue":  self.MemberValue,
			},
		}

		CMember.UpdateOne(ctx, selector, updater)
	}
}

func (*Member) GetByProperty(ctx context.Context, propertyName string, propertyValue interface{}) *Member {
	property := CMemberProperty.GetByName(ctx, propertyName)
	if property != nil {
		propertyValue = GetValueConditionWithEncrypted(ctx, propertyValue, property)
	}
	memberCond := bson.M{
		"properties": bson.M{
			"$elemMatch": bson.M{
				"name":  propertyName,
				"value": propertyValue,
			},
		},
		"isDeleted": false,
	}

	member := &Member{}
	Common.GetOneByCondition(ctx, memberCond, C_MEMBER, member)

	if member.Id.Hex() != "" {
		DecryptMember(ctx, member)
		return member
	}
	return nil
}

func (*Member) GetAllByProperty(ctx context.Context, propertyName string, propertyValue interface{}) ([]Member, error) {
	property := CMemberProperty.GetByName(ctx, propertyName)
	if property != nil {
		propertyValue = GetValueConditionWithEncrypted(ctx, propertyValue, property)
	}
	selector := Common.GenDefaultCondition(ctx)
	selector["properties"] = bson.M{
		"$elemMatch": bson.M{
			"name":  propertyName,
			"value": propertyValue,
		},
	}
	result := []Member{}
	_, err := Common.GetAllByCondition(ctx, selector, []string{}, 0, C_MEMBER, &result)
	DecryptMembers(ctx, result)
	return result, err
}

// UpdateDisabledStatus 更新客户的禁用状态
func (self *Member) UpdateDisabledStatus(ctx context.Context, disableStatus bool) error {
	if disableStatus == self.IsDisabled {
		return nil
	}

	self.IsDisabled = disableStatus
	self.UpdatedAt = time.Now()

	err := CMember.UpdateOne(ctx,
		bson.M{"_id": self.Id},
		bson.M{"$set": bson.M{
			"isDisabled": self.IsDisabled,
			"updatedAt":  self.UpdatedAt,
		}},
	)

	if err != nil {
		return codes.NewError(codes.UpdateMemberDisabledStatusFail)
	}

	self.createDisabledStatusSwitchInfoLog(ctx)
	return nil
}

// RewardByRuleName 根据不同的积分规则向客户发放积分奖励
func RewardByRuleName(ctx context.Context, member *Member, ruleName string, channelInfo *ChannelInfo) {
	if channelInfo == nil {
		channelInfo = &ChannelInfo{}
	}
	scoreRule := CScoreRule.GetByCondition(ctx, bson.M{
		"name": ruleName,
	}, member.AccountId.Hex())

	if scoreRule == nil {
		log.Warn(ctx, "Reward by ruleName failed", log.Fields{
			"name":     ruleName,
			"memberId": member.Id.Hex(),
		})
		return
	}

	if channelInfo.Origin == "" {
		channelInfo.Origin = "portal"
	}

	scoreRule.Reward(ctx, *member, *channelInfo, "")
}

func (*Member) CountByTag(ctx context.Context, tag string) uint64 {
	condition := bson.M{
		"accountId": bson.ObjectIdHex(util.GetAccountId(ctx)),
		"isDeleted": false,
		"tags":      tag,
	}

	count, _ := extension.DBRepository.Count(ctx, C_MEMBER, condition)

	return uint64(count)
}

func (*Member) CountByTags(ctx context.Context, tags []string) uint64 {
	condition := bson.M{
		"accountId": bson.ObjectIdHex(util.GetAccountId(ctx)),
		"isDeleted": false,
		"tags":      bson.M{"$in": tags},
	}
	count, _ := extension.DBRepository.Count(ctx, C_MEMBER, condition)
	return uint64(count)
}

func (*Member) GetByWxCard(ctx context.Context, cardId, cardCode string) *Member {
	memberCond := bson.M{
		"wxCard.id":   cardId,
		"wxCard.code": cardCode,
		"isDeleted":   false,
	}

	member := &Member{}
	Common.GetOneByCondition(ctx, memberCond, C_MEMBER, member)

	if member.Id.Hex() != "" {
		DecryptMember(ctx, member)
		return member
	}

	return nil
}

func (self *Member) havePhoneProperty() bool {
	for _, prop := range self.Properties {
		if prop.Name == DEFAULT_PROPERTY_PHONE {
			return true
		}
	}
	return false
}

// Logoff 注销客户信息，清空所有信息
func (self *Member) Logoff(ctx context.Context, isDeleteCardId bool, cardNumber string) error {
	condition := bson.M{
		"_id": self.Id,
	}

	self.IsActivated = false
	self.Phone = ""
	self.ActivatedAt = time.Unix(0, 0)

	setter := bson.M{
		"isActivated": false,
		"phone":       "",
		"updatedAt":   time.Now(),
	}

	if self.havePhoneProperty() {
		condition["properties.name"] = DEFAULT_PROPERTY_PHONE
		setter["properties.$.value"] = ""
	}

	if isDeleteCardId {
		setter["cardId"] = ""
	}

	if cardNumber != "" {
		setter["cardNumber"] = cardNumber
	}

	updater := bson.M{
		"$set": setter,
		"$unset": bson.M{
			"activatedAt": "",
		},
	}

	return CMember.UpdateOne(ctx, condition, updater)
}

// 根据 phone 的值，更新相关渠道下的身份信息
// 仅用于 origin 为 jd、jd:member 和 taobao:member 的一些渠道
// taobao:member 渠道的 openId 是由 phone 加密生成；jd 渠道的 unionId 是由 phone 加密生成
// jd:member v1 渠道的 openId 是由 phone 加密生成，unionId 是京东的 pin；jd:member v2 渠道的 openId 是京东的 xid。
// jd:member 营销云方式二会员匹配版渠道的 unionId 是群脉生成的 md5(ObjectId)，将其上报给京东换取的 cdpId 存储在 `channelMixPhone.mixPhone`。
// douyin:member v2 渠道的 openId 是抖音的 openId，抖音的 hashMobile 存储在 `channelMixPhone.mixPhone`。
// 此函数会检查 openId, unionId 的唯一性，若存在于其他客户身上则做合并操作，否则仅添加渠道信息
func (self *Member) UpdateSocialWithPhone(
	ctx context.Context,
	phone string,
	channels []*account.ChannelDetailResponse,
	reqChannelOrigin string,
) bool {
	var socialUpdated bool
	accountId := util.GetAccountIdAsObjectId(ctx)
	existedMixPhones := []string{}
	for _, channel := range channels {
		if channel.EncryptionKey == "" && util.StrInArray(channel.Origin, &[]string{constant.TAOBAO_MEMBER, constant.JD}) {
			continue
		}
		if channel.AppId == "" && channel.Origin == constant.DOUYIN_MEMBER {
			continue
		}

		switch channel.Origin {
		case constant.TAOBAO_MEMBER:
			mixPhoneKey := fmt.Sprintf("tmall%s%s", phone, channel.EncryptionKey)
			mixPhone := GetTmallMixPhone(mixPhoneKey)
			member := CMember.GetByOriginAndOpenId(ctx, accountId, constant.TAOBAO_MEMBER, mixPhone, []bson.ObjectId{self.Id})
			if member != nil {
				if member.Id.Hex() != self.Id.Hex() {
					err := self.MergeMember(ctx, []Member{*member}, "", REASON_DUPLICATE_OPENID)
					if err == nil {
						CMemberMergeHistory.PrintMergeLog(ctx, *self, []Member{*member}, fmt.Sprintf("UpdateSocialWithPhone %s", constant.TAOBAO_MEMBER), REASON_DUPLICATE_OPENID)
					}
				}
			}
			var (
				newSocials                []Social
				foundInactiveTaobaoSocial = false
				foundActiveTaobaoSocial   = false
				socialExists              = false
			)
			for _, social := range self.Socials {
				if social.Channel == channel.ChannelId && social.Origin == constant.TAOBAO_MEMBER {
					if social.OpenId == mixPhone {
						socialExists = true
					} else if social.Nickname == "" {
						foundInactiveTaobaoSocial = true
						social.OpenId = mixPhone
					} else if social.UnionId != "" {
						foundActiveTaobaoSocial = true
					}
				}
				newSocials = append(newSocials, social)
			}
			if !foundInactiveTaobaoSocial && !foundActiveTaobaoSocial && !socialExists {
				social := Social{
					Channel:     channel.ChannelId,
					OpenId:      mixPhone,
					Origin:      constant.TAOBAO_MEMBER,
					ChannelName: channel.Name,
				}

				newSocials = append(newSocials, social)
			}
			self.Socials = newSocials
			socialUpdated = true
		case constant.JD_MEMBER:
			if !share_model.CChannel.IsJdMemberV1(channel.IntegrationModes) &&
				!share_model.CChannel.IsJdCdpWay2(channel.IntegrationModes) {
				continue
			}
			if share_model.CChannel.IsJdMemberV1(channel.IntegrationModes) {
				if channel.EncryptionKey == "" {
					continue
				}
				self.updateJdMemberV1SocialWithPhone(ctx, phone, &socialUpdated, channel)
			} else if share_model.CChannel.IsJdCdpWay2(channel.IntegrationModes) {
				// 手机号非空且渠道来源为 jd:member 时，只可能是营销云请求 SPI 过来的，拿到手机号说明先前已上翻，无需重复上翻
				if reqChannelOrigin == constant.JD_MEMBER {
					continue
				}
				if share_model.CChannel.ChannelCanDecryptPhone(channel.IntegrationModes) {
					continue
				}
				if channel.AppId == "" || channel.AppSecret == "" {
					continue
				}
				self.MergeJdCdpWay2MemberAfterPhoneChanged(ctx, channel)
			}
		case constant.JD:
			if channel.BrandId == "" {
				log.Warn(ctx, "BrandId can't be emtpy", log.Fields{
					"channelId": channel.ChannelId,
				})
				continue
			}
			mixPhone := GetJdMixPhone(channel.EncryptionKey, channel.BrandId, phone)
			member := CMember.GetByAccountAndUnionId(ctx, accountId, mixPhone, []bson.ObjectId{self.Id})
			if member == nil {
				newSocials := []Social{}
				foundJdSocial := false
				for _, social := range self.Socials {
					if social.Channel == channel.ChannelId && social.Origin == constant.JD {
						social.UnionId = mixPhone // 更新所有 jd unionId
						foundJdSocial = true
					}
					newSocials = append(newSocials, social)
				}

				if !foundJdSocial {
					newSocials = append(newSocials, Social{
						Channel:     channel.ChannelId,
						UnionId:     mixPhone,
						Origin:      constant.JD,
						ChannelName: channel.Name,
					})
				}
				self.Socials = newSocials
				socialUpdated = true
			} else if member.Id.Hex() != self.Id.Hex() {
				err := self.MergeMember(ctx, []Member{*member}, "", REASON_DUPLICATE_UNIONID)
				if err == nil {
					CMemberMergeHistory.PrintMergeLog(ctx, *self, []Member{*member}, fmt.Sprintf("UpdateSocialWithPhone %s", constant.JD), REASON_DUPLICATE_UNIONID)
				}
			}
		case constant.DOUYIN_MEMBER:
			if share_model.CChannel.IsSyncingStockMember(channel.IntegrationModes) {
				continue
			}

			var (
				mixPhone            = GetDouyinMaskMixPhone(phone)
				compatibleChannelId = GetCompatibleChannelId(channel.AppId, channel.ChannelId, channel.IntegrationModes)
				compatibleOpenId    string
			)
			if util.StrInArray(mixPhone, &existedMixPhones) {
				continue
			}
			existedMixPhones = append(existedMixPhones, mixPhone)
			// 查出所有记录（有 openId 但是没有 phone）去匹配
			allChannelMixPhones, err := CChannelMixPhone.GetAllByCompatibleChannelIdAndMixPhone(ctx, compatibleChannelId, mixPhone)
			if err != nil {
				continue
			}
			chunkedChannelMixPhones := core_util.ChunkSlice(allChannelMixPhones, 10)
			for _, channelMixPhones := range chunkedChannelMixPhones {
				openIds := core_util.ExtractArrayStringField("OpenId", channelMixPhones)
				if share_model.CChannel.IsBrandMember(channel.IntegrationModes) {
					resp, err := member_share.GetDouyinMemberOpenIdsByUnionIds(ctx, channel, openIds, 2)
					if err != nil || resp == nil {
						continue
					}
					openIds = []string{}
					for _, item := range resp.Data.Items {
						for _, pair := range item.ChannelPairs {
							if strconv.FormatInt(pair.ChannelId, 10) == channel.ChannelId {
								openIds = append(openIds, pair.OpenId)
								break
							}
						}
					}
				}
				var extendInfoList []core_component.DouyinMemberExtendInfoItem
				maskMobile := ReverseDouyinMaskMixPhone(phone)
				for _, openId := range openIds {
					extendInfoList = append(extendInfoList, core_component.DouyinMemberExtendInfoItem{
						OpenId:     openId,
						MaskMobile: maskMobile,
					})
				}
				resp, err := member_share.GetDouyinMemberInfoByOpenIdList(ctx, channel, openIds, extendInfoList, 2)
				if err != nil {
					continue
				}
				if resp != nil {
					douyinPhone := FormatDouyinPhone(phone)
					for _, memberInfoDetail := range resp.Data.MemberInfoList {
						if !memberInfoDetail.Success {
							continue
						}
						if memberInfoDetail.Phone == douyinPhone {
							compatibleOpenId = memberInfoDetail.OpenId
							if share_model.CChannel.IsBrandMember(channel.IntegrationModes) {
								compatibleOpenId = memberInfoDetail.UnionId
							}
							break
						}
					}
				}
				if compatibleOpenId != "" {
					break
				}
			}
			// 未匹配成功，插入新纪录
			// 匹配成功，更新对应记录的 phone，下一次就不需要匹配这条记录
			newChannelMixPhone := &ChannelMixPhone{
				ChannelId: compatibleChannelId,
				MixPhone:  mixPhone,
				Phone:     phone,
				OpenId:    compatibleOpenId,
			}
			newChannelMixPhone.Upsert(ctx)
			if compatibleOpenId == "" {
				continue
			}

			isDifferentWithSelf := true
			for _, social := range self.Socials {
				if social.Channel == channel.ChannelId &&
					social.Origin == constant.DOUYIN_MEMBER &&
					(social.OpenId == compatibleOpenId || social.UnionId == compatibleOpenId) {
					isDifferentWithSelf = false
					break
				}
			}
			if !isDifferentWithSelf {
				continue
			}

			var (
				mergeReason string
				member      *Member
			)
			if share_model.CChannel.IsBrandMember(channel.IntegrationModes) {
				mergeReason = REASON_DUPLICATE_UNIONID
				member = CMember.GetByAccountAndUnionId(ctx, accountId, compatibleOpenId, []bson.ObjectId{self.Id})
			}
			if member == nil {
				mergeReason = REASON_DUPLICATE_OPENID
				member = CMember.GetByOriginAndOpenId(ctx, accountId, constant.DOUYIN_MEMBER, compatibleOpenId, []bson.ObjectId{self.Id})
			}
			if member != nil {
				subMembers := []Member{*member}
				// UnderArmour 租户以 ActivatedAt 早的为主 member
				if share_model.IsUnderArmourAccount(ctx) {
					subMembers = self.ArrangeMainAndSubMembersByActivatedAt(ctx, []Member{*member})
				}
				err := self.MergeMember(ctx, subMembers, "", mergeReason)
				if err == nil {
					CMemberMergeHistory.PrintMergeLog(ctx, *self, []Member{*member}, fmt.Sprintf("UpdateSocialWithPhone %s", constant.DOUYIN_MEMBER), mergeReason)
				}
			}
		}
	}
	return socialUpdated
}

func (self *Member) updateJdMemberV1SocialWithPhone(ctx context.Context, phone string, socialUpdated *bool, channel *account.ChannelDetailResponse) {
	accountId := util.GetAccountIdAsObjectId(ctx)
	mixPhone := GetJdMixPhone(channel.EncryptionKey, channel.ChannelId, phone)
	member := CMember.GetByOriginAndOpenId(ctx, accountId, constant.JD_MEMBER, mixPhone, []bson.ObjectId{})
	if member == nil {
		foundInactiveJdMembberSocial := false
		newSocials := []Social{}
		for _, social := range self.Socials {
			if social.Channel == channel.ChannelId && social.Origin == constant.JD_MEMBER && social.SubscribeTime.IsZero() {
				foundInactiveJdMembberSocial = true
				social.OpenId = mixPhone
			}
			newSocials = append(newSocials, social)
		}
		if !foundInactiveJdMembberSocial {
			social := Social{
				Channel:     channel.ChannelId,
				OpenId:      mixPhone,
				Origin:      constant.JD_MEMBER,
				ChannelName: channel.Name,
			}

			newSocials = append(newSocials, social)
		}
		self.Socials = newSocials
		*socialUpdated = true
	} else {
		if member.Id.Hex() != self.Id.Hex() {
			err := self.MergeMember(ctx, []Member{*member}, "", REASON_DUPLICATE_OPENID)
			if err == nil {
				CMemberMergeHistory.PrintMergeLog(ctx, *self, []Member{*member}, fmt.Sprintf("UpdateSocialWithPhone %s", constant.JD_MEMBER), REASON_DUPLICATE_OPENID)
			}
		}
	}
}

func (self *Member) MergeJdCdpWay2MemberAfterPhoneChanged(ctx context.Context, channel *account.ChannelDetailResponse) error {
	channelMixPhone, err := CMember.RegisterToJdCdp(ctx, self.Phone, channel)
	if err != nil {
		return err
	}
	if channelMixPhone.OpenId == "" {
		return nil
	}
	memberFoundByUnionId := CMember.GetByAccountAndUnionId(ctx, util.GetAccountIdAsObjectId(ctx), channelMixPhone.OpenId, []bson.ObjectId{self.Id})
	if memberFoundByUnionId != nil {
		otherMembers := []Member{*memberFoundByUnionId}
		log.Warn(ctx, "MergeMember_by_RegisterMemberWithPhoneToJd", log.Fields{
			"mergedMemberIds": []string{memberFoundByUnionId.Id.Hex()},
			"reason":          fmt.Sprintf("Found jd cdp way2 member with unionId: %s", channelMixPhone.OpenId),
		})
		err := self.MergeMember(ctx, otherMembers, "", REASON_DUPLICATE_UNIONID)
		if err != nil {
			return err
		}
		CMemberMergeHistory.PrintMergeLog(ctx, *self, otherMembers, "RegisterMemberWithPhoneToJd otherMembers", REASON_DUPLICATE_UNIONID)
	}
	return nil
}

func (self *Member) RegisterToJdCdp(
	ctx context.Context,
	phone string,
	channel *account.ChannelDetailResponse,
) (newChannelMixPhone *ChannelMixPhone, err error) {
	defer func() {
		if err != nil {
			log.Warn(ctx, "Failed to RegisterToJdCdp", log.Fields{
				"channelId": channel.ChannelId,
				"phone":     phone,
				"err":       err.Error(),
			})
		}
	}()
	client, err := jingdong.NewJingdong(
		channel.AppId,
		channel.AppSecret,
		channel.Token,
		channel.ChannelId,
		channel.IntegrationModes,
	)
	if err != nil {
		return nil, err
	}
	req := &jingdong.RegisterJdCdpWay2MemberRequest{
		CrmId:    jingdong.GenerateCrmId(phone, channel.ChannelId),
		MixPhone: jingdong.GenerateMixPhone(phone, channel.EncryptionKey, channel.ChannelId),
	}
	respRegister, err := client.Member.RegisterJdCdpWay2Member(ctx, req)
	if err != nil {
		return nil, err
	}
	if respRegister.Data.CdpId == "" || respRegister.Data.CrmId == "" {
		return nil, fmt.Errorf("CdpId or crmId is empty after registering to jd cdp")
	}
	newChannelMixPhone = &ChannelMixPhone{
		ChannelId: channel.ChannelId,
		MixPhone:  respRegister.Data.CdpId,
		Phone:     phone,
		OpenId:    respRegister.Data.CrmId,
	}
	oldChannelMixPhone, err := CChannelMixPhone.GetByCompatibleChannelIdAndMixPhone(ctx, channel.ChannelId, respRegister.Data.CdpId)
	if oldChannelMixPhone != nil && oldChannelMixPhone.OpenId != "" {
		oldCrmId := oldChannelMixPhone.OpenId
		newCrmId := respRegister.Data.CrmId
		newChannelMixPhone.OpenId = newCrmId
		selector := bson.M{
			"accountId":       util.GetAccountIdAsObjectId(ctx),
			"socials.unionId": oldCrmId,
			"isDeleted":       false,
		}
		updator := bson.M{
			"$set": bson.M{
				"idUpdatedAt":       bson.NewObjectId(),
				"updatedAt":         time.Now(),
				"socials.$.unionId": newCrmId,
			},
		}
		err = CMember.UpdateOne(ctx, selector, updator)
		if err != nil {
			log.Warn(ctx, "Failed to UpdateOne", log.Fields{
				"selector": core_util.MarshalInterfaceToString(selector),
				"updator":  core_util.MarshalInterfaceToString(updator),
				"err":      err.Error(),
			})
		}
		for idx, social := range self.Socials {
			if social.Origin == constant.JD_MEMBER && social.Channel == channel.ChannelId && social.UnionId == oldCrmId {
				self.Socials[idx].UnionId = newCrmId
			}
		}
	}
	err = newChannelMixPhone.Upsert(ctx)
	if err != nil {
		return nil, err
	}
	return newChannelMixPhone, nil
}

// ArrangeMainAndSubMembersByActivatedAt 此方法通过激活时间判断主客户，激活时间早的是主客户
func (self *Member) ArrangeMainAndSubMembersByActivatedAt(ctx context.Context, otherMembers []Member) (subMembers []Member) {
	allMembers := append([]Member{*self}, otherMembers...)
	allUniqueMembers := []Member{}
	found := map[string]bool{}
	for _, m := range allMembers {
		if _, ok := found[m.Id.Hex()]; !ok {
			allUniqueMembers = append(allUniqueMembers, m)
			found[m.Id.Hex()] = true
		}
	}
	var mainMemberIdx int
	for i, member := range allUniqueMembers {
		if !member.IsActivated {
			continue
		}
		member := member
		if !self.IsActivated || member.ActivatedAt.Before(self.ActivatedAt) {
			*self = member
			mainMemberIdx = i
		}
	}
	subMembers = append(allUniqueMembers[:mainMemberIdx], allUniqueMembers[mainMemberIdx+1:]...)
	log.Warn(ctx, "Merge members arranged by activatedAt", log.Fields{
		"subMembers": core_util.ToStringArray(core_util.ExtractArrayField("Id", subMembers)),
		"mainMember": self.Id.Hex(),
	})
	return subMembers
}

// AddDefaultCard 为当前客户发放默认会员卡
func (self *Member) AddDefaultCard(ctx context.Context) (*MembershipCard, bool) {
	// 如果当前客户已经有会员卡了那么什么也不做
	if self.CardId.Valid() {
		return nil, false
	}

	card := CMembershipCard.GetDefaultCard(ctx)
	if card == nil {
		return nil, false
	}
	self.CardId = card.Id
	self.CardProvideTime = time.Now()

	return card, true
}

func (self *Member) FindAll(ctx context.Context, selector bson.M, sortor []string, limit int) ([]Member, error) {
	members := []Member{}
	if _, ok := selector["$or"]; !ok {
		selector["isDeleted"] = false
	}

	err := extension.DBRepository.FindAll(ctx, C_MEMBER, selector, sortor, limit, &members)
	DecryptMembers(ctx, members)
	return members, err
}

func (self *Member) FindAllWithFields(ctx context.Context, selector, fields bson.M, sortor []string, limit int) ([]Member, error) {
	members := []Member{}
	if _, ok := selector["$or"]; !ok {
		selector["isDeleted"] = false
	}

	err := extension.DBRepository.FindAllWithFields(ctx, C_MEMBER, selector, fields, sortor, limit, &members)
	DecryptMembers(ctx, members)

	return members, err
}

// DeleteById 删除客户
func (*Member) DeleteById(ctx context.Context, memberId string) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       bson.ObjectIdHex(memberId),
		"isDeleted": false,
	}

	updator := bson.M{
		"$set": bson.M{
			"isDeleted": true,
			"updatedAt": time.Now(),
		},
	}

	return CMember.UpdateOne(ctx, selector, updator)
}

// RecoverById 恢复一个已经被删除的客户
func (self *Member) RecoverById(ctx context.Context, memberId string) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       bson.ObjectIdHex(memberId),
		"isDeleted": true,
	}

	updator := bson.M{
		"$set": bson.M{
			"isDeleted": false,
			"updatedAt": time.Now(),
		},
	}

	return CMember.UpdateOne(ctx, selector, updator)
}

// UpdateSocialByFollower 根据关注者信息更新对应渠道的信息
func (self *Member) UpdateSocialByFollower(ctx context.Context, follower *member.FollowerDetail) bool {
	newSocial := GetSocialByFollower(follower, false)
	for i, social := range self.Socials {
		if !social.IsSameSocial(newSocial) {
			continue
		}
		updatedSocial := social.UpdateBySocial(newSocial)
		// 当 originScene 为 wecontact 时，不应该修改已关注渠道的关注状态
		// https://gitlab.maiscrm.com/mai/home/<USER>/issues/58211#note_5273797
		if newSocial.OriginScene == constant.WECONTACT && social.Subscribed {
			updatedSocial.Subscribed = true
		}
		// 当 originScene 为 wecontact 时是企微客户群成员更新进群时间，不需要更新 extra
		// 公众号渠道不能覆盖 extra
		if newSocial.OriginScene != constant.WECONTACT && newSocial.Origin != constant.WECHAT {
			// 零值问题，直接更新的时候，空也要更。
			updatedSocial.Extra = follower.Extra
		}
		if updatedSocial != social {
			self.Socials[i] = updatedSocial
			err := self.UpdateSpecifiedWholeSocial(ctx, updatedSocial)
			if err != nil {
				return false
			}
			return true
		}
	}

	return false
}

// GetSocialByFollower 根据关注者信息生成一个渠道
func GetSocialByFollower(follower *member.FollowerDetail, isOriginal bool) Social {
	social := Social{
		Channel:          follower.ChannelId,
		OpenId:           follower.OpenId,
		UnionId:          follower.UnionId,
		Origin:           follower.Origin,
		OriginScene:      follower.SubscribeSource,
		FirstOriginScene: follower.FirstSubscribeSource,
		IsOriginal:       isOriginal,
		ChannelName:      follower.ChannelName,
		Nickname:         follower.Nickname,
		Gender:           follower.Gender,
		City:             follower.City,
		Province:         follower.Province,
		Country:          follower.Country,
		Avatar:           follower.Avatar,
		Authorized: util.StrInArray(follower.SubscribeSource, &[]string{
			SUBSCRIBE_SOURCE_OAUTH,
			constant.WECONTACT,
		}),
		Language: follower.Language,
		Extra:    follower.Extra,
	}
	if social.Origin == constant.WEAPP && follower.SubscribeSource == SUBSCRIBE_SOURCE_OAUTH {
		social.OriginScene = ""
		social.FirstOriginScene = ""
	}
	if follower.SubscribeTime > 0 {
		social.SubscribeTime = util.TransIntTimestampToTime(follower.SubscribeTime)
	}
	if follower.UnsubscribeTime > 0 {
		social.UnsubscribeTime = util.TransIntTimestampToTime(follower.UnsubscribeTime)
	}
	if follower.FirstSubscribeTime > 0 {
		social.FirstSubscribeTime = util.TransIntTimestampToTime(follower.FirstSubscribeTime)
	}
	if social.Authorized {
		social.FirstAuthorizeTime = time.Now()
		social.AuthorizeTime = time.Now()
	}

	social.Subscribed = follower.IsSubscribed || social.SubscribeTime.After(social.UnsubscribeTime)

	return social
}

// DiffMemberProperties 获取当前客户和传入的关注者的不同的客户属性
func DiffMemberProperties(ctx context.Context, follower *member.FollowerDetail, memberProperties []Property, oldName, oldAvatar string) map[string]interface{} {
	idPropertyMap := GetPropertyMap(memberProperties)
	change := make(map[string]interface{})
	accountProperties := CMemberProperty.GetByAccount(ctx, util.GetAccountId(ctx))
	for _, memberProperty := range accountProperties {
		property, exists := idPropertyMap[memberProperty.Id]
		switch memberProperty.Name {
		case DEFAULT_PROPERTY_NAME:
			if follower.Nickname == "" {
				continue
			}

			if !exists {
				change[DEFAULT_PROPERTY_NAME] = map[string]interface{}{
					"from": "",
					"to":   follower.Nickname,
				}
				continue
			}

			if cast.ToString(property.Value) == "" {
				change[DEFAULT_PROPERTY_NAME] = map[string]interface{}{
					"from": "",
					"to":   follower.Nickname,
				}
			}
		case DEFAULT_PROPERTY_GENDER:
			if follower.Gender == "" {
				continue
			}

			var gender string
			switch strings.ToLower(follower.Gender) {
			case MALE, FEMALE:
				gender = strings.ToLower(follower.Gender)
			default:
				gender = UNKNOWN
			}

			if !exists {
				change[DEFAULT_PROPERTY_GENDER] = map[string]interface{}{
					"from": "",
					"to":   gender,
				}
				continue
			}

			if cast.ToString(property.Value) == UNKNOWN {
				change[DEFAULT_PROPERTY_GENDER] = map[string]interface{}{
					"from": UNKNOWN,
					"to":   gender,
				}
			}
		case DEFAULT_PROPERTY_ADDRESS:
			if follower.Country == "" && follower.Province == "" && follower.City == "" {
				continue
			}

			if !exists {
				change[DEFAULT_PROPERTY_ADDRESS] = map[string]interface{}{
					"from": "",
					"to": map[string]interface{}{
						"country":  follower.Country,
						"province": follower.Province,
						"city":     follower.City,
					},
				}
				continue
			}

			address, _ := property.Value.(bson.M)
			if cast.ToString(address["country"]) == "" && cast.ToString(address["province"]) == "" && cast.ToString(address["city"]) == "" {
				province := util.FormatWechatProvince(follower.Province)
				city := util.FormatWechatCity(follower.City)
				change[DEFAULT_PROPERTY_ADDRESS] = map[string]interface{}{
					"from": "",
					"to": map[string]interface{}{
						"country":  follower.Country,
						"province": province,
						"city":     city,
					},
				}
			}
		case DEFAULT_PROPERTY_AVATAR:
			if follower.Avatar == "" {
				continue
			}

			if !exists {
				change[DEFAULT_PROPERTY_AVATAR] = map[string]interface{}{
					"from": "",
					"to":   follower.Avatar,
				}
				continue
			}

			if isDefaultAvatar(oldAvatar) {
				change[DEFAULT_PROPERTY_AVATAR] = map[string]interface{}{
					"from": oldAvatar,
					"to":   follower.Avatar,
				}
			}
		}
	}

	return change
}

// UpdateEmptyPropertiesByFollower 根据关注者信息更新当前客户信息
func (self *Member) UpdateEmptyPropertiesByFollower(ctx context.Context, follower *member.FollowerDetail) bool {
	// 如果没有更新那么就直接返回
	if !self.isNeedUpdate(follower) {
		return false
	}

	memberProperties := CMemberProperty.GetByAccount(ctx, util.GetAccountId(ctx))
	updatedProperties, edited := self.UpdateSocialProperties(follower.Nickname, follower.Gender, follower.Country, follower.Province, follower.City, follower.Avatar, memberProperties)

	if edited {
		err := self.Update(ctx, updatedProperties, []bson.ObjectId{})
		if err != nil {
			log.Warn(ctx, "Failed to update member property when upsert by follower", log.Fields{
				"error": err.Error(),
			})
			edited = false
		}
	}

	return edited
}

// UpdateSocialProperties 根据渠道信息更新客户属性，例如省市区、性别、昵称等
func (self *Member) UpdateSocialProperties(name, gender, country, province, city, avatar string, accountProperties []MemberProperty) ([]MemberProperty, bool) {
	idPropertyMap := self.getPropertyMap()
	edited := false
	// 为了防止客户有为空的必填属性，这里只校验被更新的客户属性
	updatedProperties := []MemberProperty{}
	for _, memberProperty := range accountProperties {
		property, exists := idPropertyMap[memberProperty.Id]
		switch memberProperty.Name {
		case DEFAULT_PROPERTY_NAME:
			if name == "" {
				continue
			}

			if !exists {
				self.Properties = append(self.Properties, Property{
					Id:    memberProperty.Id,
					Name:  memberProperty.Name,
					Value: name,
				})
				edited = true
				updatedProperties = append(updatedProperties, memberProperty)
				continue
			}

			if cast.ToString(property.Value) == "" {
				self.updatePropertyValueById(memberProperty.Id, name)
				edited = true
				updatedProperties = append(updatedProperties, memberProperty)
			}
		case DEFAULT_PROPERTY_GENDER:
			if gender == "" {
				continue
			}

			switch strings.ToLower(gender) {
			case MALE, FEMALE:
				gender = strings.ToLower(gender)
			default:
				gender = UNKNOWN
			}

			if !exists {
				self.Properties = append(self.Properties, Property{
					Id:    memberProperty.Id,
					Name:  memberProperty.Name,
					Value: gender,
				})
				edited = true
				updatedProperties = append(updatedProperties, memberProperty)
				continue
			}

			if cast.ToString(property.Value) == UNKNOWN {
				self.updatePropertyValueById(memberProperty.Id, gender)
				edited = true
				updatedProperties = append(updatedProperties, memberProperty)
			}
		case DEFAULT_PROPERTY_ADDRESS:
			if country == "" && province == "" && city == "" {
				continue
			}

			if !exists {
				province := util.FormatWechatProvince(province)
				city := util.FormatWechatCity(city)
				self.Properties = append(self.Properties, Property{
					Id:   memberProperty.Id,
					Name: memberProperty.Name,
					Value: bson.M{
						"country":  country,
						"province": province,
						"city":     city,
					},
				})
				self.Location = Location{
					Country:  country,
					Province: province,
					City:     city,
				}
				edited = true
				updatedProperties = append(updatedProperties, memberProperty)
				continue
			}

			address, _ := property.Value.(bson.M)
			if cast.ToString(address["country"]) == "" && cast.ToString(address["province"]) == "" && cast.ToString(address["city"]) == "" {
				province := util.FormatWechatProvince(province)
				city := util.FormatWechatCity(city)
				self.updatePropertyValueById(memberProperty.Id, bson.M{
					"country":  country,
					"province": province,
					"city":     city,
					"district": "",
					"detail":   "",
				})
				self.Location = Location{
					Country:  country,
					Province: province,
					City:     city,
				}
				edited = true
				updatedProperties = append(updatedProperties, memberProperty)
			}
		case DEFAULT_PROPERTY_AVATAR:
			if avatar == "" {
				continue
			}

			if !exists {
				self.Properties = append(self.Properties, Property{
					Id:    memberProperty.Id,
					Name:  memberProperty.Name,
					Value: avatar,
				})
				self.Avatar = avatar
				edited = true
				updatedProperties = append(updatedProperties, memberProperty)
				continue
			}

			if self.isDefaultAvatar() {
				self.updatePropertyValueById(memberProperty.Id, avatar)
				self.Avatar = avatar
				edited = true
				updatedProperties = append(updatedProperties, memberProperty)
			}
		}
	}

	if self.Nickname == "" && name != "" {
		self.Nickname = name
		edited = true
	}

	return updatedProperties, edited
}

// UpdateEmptyPropertiesByYouzanUser 根据有赞传来的客户信息更新当前客户的客户属性
func (self *Member) UpdateEmptyPropertiesByYouzanUser(ctx context.Context, youzanUser *member.YouzanUser) {
	var (
		properties           []Property
		existedPropertiesMap = map[string]Property{}
		isYouzanMemberProp   *MemberProperty
	)

	if youzanUser.IsMember != nil {
		isYouzanMemberProp = CMemberProperty.GetByPropertyId(ctx, IS_YOUZAN_MEMBER_PROPERTY_ID)
		if isYouzanMemberProp != nil {
			properties = append(properties, Property{
				Id:    isYouzanMemberProp.Id,
				Name:  isYouzanMemberProp.Name,
				Value: youzanUser.IsMember.Value,
			})
		}
	}

	for _, prop := range self.Properties {
		existedPropertiesMap[prop.Name] = prop
		if isYouzanMemberProp == nil || (isYouzanMemberProp != nil && prop.Name != isYouzanMemberProp.Name) {
			properties = append(properties, prop)
		}
	}

	defaultProperties := CMemberProperty.GetByDefault(ctx, util.GetAccountId(ctx), true)
	for _, defaultProperty := range defaultProperties {
		if _, existed := existedPropertiesMap[defaultProperty.Name]; existed {
			continue
		}

		switch defaultProperty.Name {
		case DEFAULT_PROPERTY_NAME:
			if youzanUser.Name == "" {
				continue
			}
			properties = append(properties, Property{
				Id:    defaultProperty.Id,
				Name:  defaultProperty.Name,
				Value: youzanUser.Name,
			})
		case DEFAULT_PROPERTY_GENDER:
			gender := youzanUser.Gender
			if gender == "" {
				gender = UNKNOWN
			}
			properties = append(properties, Property{
				Id:    defaultProperty.Id,
				Name:  defaultProperty.Name,
				Value: gender,
			})
		case DEFAULT_PROPERTY_ADDRESS:
			if youzanUser.Province == "" && youzanUser.City == "" && youzanUser.District == "" && youzanUser.Detail == "" {
				continue
			}
			properties = append(properties, Property{
				Id:   defaultProperty.Id,
				Name: defaultProperty.Name,
				Value: bson.M{
					"country":  "中国",
					"province": youzanUser.Province,
					"city":     youzanUser.City,
					"district": youzanUser.District,
					"detail":   youzanUser.Detail,
				},
			})
		case DEFAULT_PROPERTY_PHONE:
			if youzanUser.Mobile == "" {
				continue
			}
			properties = append(properties, Property{
				Id:    defaultProperty.Id,
				Name:  defaultProperty.Name,
				Value: youzanUser.Mobile,
			})
		case DEFAULT_PROPERTY_BIRTHDAY:
			birthTime := util.GetTimeFromDate(youzanUser.Birth)
			if birthTime == nil {
				continue
			}
			properties = append(properties, Property{
				Id:    defaultProperty.Id,
				Name:  defaultProperty.Name,
				Value: birthTime.Unix() * 1000,
			})
		case DEFAULT_PROPERTY_ZODIAC_SIGN:
			birthTime := util.GetTimeFromDate(youzanUser.Birth)
			if birthTime == nil {
				continue
			}
			zodiacSign := util.GetZodiacSign(*birthTime)
			properties = append(properties, Property{
				Id:    defaultProperty.Id,
				Name:  defaultProperty.Name,
				Value: zodiacSign,
			})
		}
	}

	self.Properties = properties
}

// isNeedUpdate 在更新数据库之前判断是否需要更新，减少数据库请求
func (self *Member) isNeedUpdate(follower *member.FollowerDetail) bool {
	propertyNeedUpdate := map[string]bool{
		DEFAULT_PROPERTY_NAME:    true,
		DEFAULT_PROPERTY_GENDER:  true,
		DEFAULT_PROPERTY_ADDRESS: true,
		DEFAULT_PROPERTY_AVATAR:  true,
		"nickname":               true,
	}

	// need not update if follower have no certain property
	if follower.Nickname == "" {
		propertyNeedUpdate[DEFAULT_PROPERTY_NAME] = false
		propertyNeedUpdate["nickname"] = false
	}
	if follower.Gender == "" {
		propertyNeedUpdate[DEFAULT_PROPERTY_GENDER] = false
	}
	if follower.Avatar == "" {
		propertyNeedUpdate[DEFAULT_PROPERTY_AVATAR] = false
	}
	if follower.Country == "" && follower.Province == "" && follower.City == "" {
		propertyNeedUpdate[DEFAULT_PROPERTY_ADDRESS] = false
	}

	// validate member.properties
	for _, property := range self.Properties {
		switch property.Name {
		case DEFAULT_PROPERTY_NAME:
			if property.Value != "" {
				propertyNeedUpdate[property.Name] = false
				continue
			}
		case DEFAULT_PROPERTY_GENDER:
			if property.Value != UNKNOWN {
				propertyNeedUpdate[property.Name] = false
				continue
			}
		case DEFAULT_PROPERTY_ADDRESS:
			address, _ := property.Value.(bson.M)
			if cast.ToString(address["country"]) != "" || cast.ToString(address["province"]) != "" || cast.ToString(address["city"]) != "" {
				propertyNeedUpdate[property.Name] = false
				continue
			}
		case DEFAULT_PROPERTY_AVATAR:
			if !self.isDefaultAvatar() {
				propertyNeedUpdate[property.Name] = false
				continue
			}
		}
	}

	if self.Nickname == follower.Nickname {
		propertyNeedUpdate["nickname"] = false
	}

	for _, need := range propertyNeedUpdate {
		if need {
			return true
		}
	}
	return false
}

func (self *Member) updatePropertyValueById(propertyId bson.ObjectId, value interface{}) bool {
	for i, property := range self.Properties {
		if property.Id == propertyId {
			self.Properties[i].Value = value
		}
	}

	return false
}

func (self *Member) GetSocialByOpenId(openId string) *Social {
	for i, social := range self.Socials {
		if social.OpenId == openId {
			return &self.Socials[i]
		}
	}

	return nil
}

func (self *Member) GetSocialByChannelId(channelId string) *Social {
	for i, social := range self.Socials {
		if social.Channel == channelId {
			return &self.Socials[i]
		}
	}

	return nil
}

func (self *Member) GetOpenIdByChannelId(channelId string) string {
	if social := self.GetSocialByChannelId(channelId); social != nil {
		return social.OpenId
	}
	return ""
}

// UpdateData 使用传入的客户更新当前客户的信息
func (self *Member) UpdateData(member Member, needMergeAvatar, needMergeName bool) (avatarMerged bool, nameMerged bool, cardMerged bool) {

	if needMergeAvatar && self.needMergeAvatar(member) {
		avatarMerged = true
		self.Avatar = member.Avatar
	}
	if needMergeName && self.needMergeName(member) {
		nameMerged = true
		self.mergeName(member)
	}
	if self.Source == "" {
		self.Source = member.Source
	}
	if self.ActivationSource == "" {
		self.ActivationSource = member.ActivationSource
	}

	self.mergeLocation(member)
	self.mergeTags(member)
	self.mergePhone(member)
	self.mergeIsDisabled(member)
	cardMerged = self.mergeCard(member)
	self.mergeStatus(member)
	self.mergeProperties(member)
	self.mergeSocials(member)
	self.mergeScore(member)
	self.mergeNickname(member)
	self.mergeMemberValue(member)
	self.mergeWxCard(member)
	self.mergeCreatedAt(member)
	self.mergeGrowth(member)
	self.mergeLevel(member)
	self.mergeGender(member)
	self.mergePaidCards(member)

	for _, property := range self.Properties {
		if property.Name != DEFAULT_PROPERTY_BIRTHDAY {
			continue
		}
		birthdayTimestamp := int64(cast.ToInt(property.Value)) / util.MILLIS_OF_SECOND
		birthday := time.Unix(birthdayTimestamp, 0)
		month := birthday.Format("1")
		day := birthday.Format("2")
		self.Birth = uint64(core_util.StringToInt(month)*100 + core_util.StringToInt(day))
	}

	return
}

// 为了避免并发增减 growth 时出问题，我们将 growth 的类型定成了 int64，
// 并且允许它在数据库中储存为负数。但为了方便使用，我们会把负数的 growth 当做 0
func (self *Member) GetGrowth() uint64 {
	if self.Growth < 0 {
		return 0
	}

	return uint64(self.Growth)
}

// GetLevel 会根据当前 member.growth 的值来查询数据库获取对应 level
func (self Member) GetLevel(ctx context.Context) (uint64, string, *MemberLevel) {
	level, err := CMemberLevel.GetByLevel(ctx, uint64(self.Level))
	if err != nil {
		return uint64(self.Level), "", nil
	}
	return level.Level, level.Name, &level
}

// GetLevelByLevels 会根据 member.growth 与所给的 levels 来计算并返回对应 level，不需要查询数据库
func (self Member) GetLevelByLevels(levels []MemberLevel) (uint64, string, *MemberLevel) {
	level, ok := GetLevelByGrowth(levels, self.GetGrowth())
	if !ok {
		return 0, "", nil
	}

	return level.Level, level.Name, &level
}

// GetJdMixPhone 生成京东规则的哈心运算后的手机号
func GetJdMixPhone(secretKey, brandId, phone string) string {
	mixPhoneKey := fmt.Sprintf("%s%s%s%s", secretKey, brandId, phone, secretKey)
	firstEncrypted := strings.ToUpper(util.EncryptWithMd5(mixPhoneKey))
	mixPhone := strings.ToUpper(util.EncryptWithMd5(firstEncrypted))
	return mixPhone
}

// GetTmallMixPhone 生成天猫规则的哈希运算后的手机号
func GetTmallMixPhone(key string) string {
	return util.EncryptWithMd5(util.EncryptWithMd5(key))
}

// GetDouyinHashMixPhone 生成抖音规则哈希运算后的手机号，参照：https://bytedance.feishu.cn/docx/RWn0dlsSHoAMUvxZ3k1ceLdLnNe
func GetDouyinHashMixPhone(appId, phone string) string {
	h := sha256.New()
	raw := []byte(phone + constant.DOUYIN_HASH_MOBILE_SALT + appId)
	h.Write(raw)
	return base64.URLEncoding.EncodeToString(h.Sum(nil))
}

// GetDouyinMaskMixPhone 生成抖音规则掩码运算后的手机号，参照：https://op.jinritemai.com/docs/notice-docs/21/7053
func GetDouyinMaskMixPhone(phone string) string {
	phone = FormatDouyinPhone(phone)
	if strings.HasPrefix(phone, "+") && len(phone) > 5 {
		return phone[:len(phone)-4] + "****"
	}
	if !strings.HasPrefix(phone, "+") && len(phone) == 11 {
		return phone[:3] + "****" + phone[7:]
	}
	return phone
}

func ReverseDouyinMaskMixPhone(phone string) string {
	phone = FormatDouyinPhone(phone)
	if strings.HasPrefix(phone, "+") && len(phone) > 5 {
		return "+" + strings.Repeat("*", len(phone)-5) + phone[len(phone)-4:]
	}
	if !strings.HasPrefix(phone, "+") && len(phone) == 11 {
		return "***" + phone[3:7] + "****"
	}
	return phone
}

func FormatDouyinPhone(phone string) string {
	if strings.HasPrefix(phone, "+") {
		phone = strings.ReplaceAll(phone, "-", "")
	}
	if strings.HasPrefix(phone, "+86") {
		phone = strings.TrimPrefix(phone, "+86")
	}
	return phone
}

// getChannelById 调用 AccountService 获取渠道详情
func getChannelById(ctx context.Context, channelId string) *account.ChannelDetailResponse {
	if channelId == "" {
		return nil
	}

	resp, err := mairpc.Run(
		"AccountService.GetChannel",
		ctx,
		&account.ChannelDetailRequest{
			ChannelId: channelId,
		},
	)
	if err != nil {
		return nil
	}
	return resp.(*account.ChannelDetailResponse)
}

// DeletePropertyFromMember 删除客户的某个客户属性
func (*Member) DeletePropertyFromMember(ctx context.Context, memberId, propertyName string) error {

	selector := bson.M{
		"_id":       bson.ObjectIdHex(memberId),
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}

	updator := bson.M{
		"$pull": bson.M{"properties": bson.M{"name": propertyName}},
	}

	return extension.DBRepository.UpdateOne(ctx, C_MEMBER, selector, updator)
}

// DeletePropertiesFromMember 删除客户的某些客户属性
func (*Member) DeletePropertiesFromMember(ctx context.Context, memberId string, propertyIds []bson.ObjectId) error {
	selector := bson.M{
		"_id":       bson.ObjectIdHex(memberId),
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updator := bson.M{
		"$pull": bson.M{
			"properties": bson.M{
				"id": bson.M{
					"$in": propertyIds,
				},
			},
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_MEMBER, selector, updator)
}

// SendOauthEventByChannel 触发 oauth 事件
func (self *Member) SendOauthEventByChannel(ctx context.Context, memberId, channelId, channelName, channelType string) {
	customerEvent := component.CustomerEventBody{
		AccountId: util.GetAccountId(ctx),
		MemberId:  memberId,
		MsgType:   component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:   oauthEventSubTypeMap[channelType],
		EventProperties: map[string]interface{}{
			"channelId":   channelId,
			"channelName": channelName,
			"channelType": channelType,
		},
		CreateTime: time.Now().UnixNano() / 1e6,
	}

	customerEvent.SendCustomerEvent(ctx)
}

func (s *Social) getSocialValueByName(name string) interface{} {
	if name == "" {
		return nil
	}
	b, _ := json.Marshal(s)
	socialMap := map[string]interface{}{}
	json.Unmarshal(b, &socialMap)
	return socialMap[name]
}

// DeleteSocialInfoByIds 注销客户的某个渠道信息
func (*Member) DeleteSocialInfoByIds(ctx context.Context, id, channelId, openId string) error {
	m := CMember.GetById(ctx, util.GetAccountId(ctx), id)
	social := Social{}
	for _, temp := range m.Socials {
		if temp.Channel == channelId && temp.OpenId == openId {
			social = temp
			break
		}
	}
	properties := []Property{}
	needClearPropertyNames := []string{"name", "phone", "img"}
	propertySocialNameMap := map[string]string{
		"name":  "Nickname",
		"phone": "",
		"img":   "Avatar",
	}
	for _, property := range m.Properties {
		if util.StrInArray(property.Name, &needClearPropertyNames) && util.IsEqual(social.getSocialValueByName(propertySocialNameMap[property.Name]), property.Value) {
			continue
		}
		properties = append(properties, property)
	}
	selector := Common.GenDefaultConditionById(ctx, bson.ObjectIdHex(id))
	selector["socials.openId"] = openId
	selector["socials.channel"] = channelId
	updater := bson.M{
		"$set": bson.M{
			"socials.$.avatar":     "",
			"socials.$.nickname":   "",
			"socials.$.authorized": false,
			"updatedAt":            time.Now(),
			"properties":           properties,
			// 删除外层头像
			"avatar": "",
		},
		"$unset": bson.M{
			"socials.$.firstAuthorizeTime": "",
			"socials.$.authorizeTime":      "",
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_MEMBER, selector, updater)
}

// DeletePhoneById 注销客户的手机号
func (*Member) DeletePhoneById(ctx context.Context, id string) error {
	selector := Common.GenDefaultConditionById(ctx, bson.ObjectIdHex(id))
	updater := bson.M{
		"phone":     "",
		"updatedAt": time.Now(),
	}
	m := CMember.GetById(ctx, util.GetAccountId(ctx), id)
	if m.havePhoneProperty() {
		selector["properties.name"] = DEFAULT_PROPERTY_PHONE
		updater["properties.$.value"] = ""
	}
	return extension.DBRepository.UpdateOne(ctx, C_MEMBER, selector, bson.M{"$set": updater})
}

// SyncScoreToWxCard 将积分同步到微信会员卡
func (self *Member) SyncScoreToWxCard(ctx context.Context) error {
	wxCard := self.WxCard
	if wxCard == nil || wxCard.Id == "" || wxCard.Channel == "" || wxCard.Code == "" {
		return errors.NewNotExistsError("wxCard")
	}
	updateWxCardReq := component.UpdateWxCardRequest{
		Code:   self.WxCard.Code,
		CardId: self.WxCard.Id,
		Bonus:  int(self.Score),
	}
	return component.WeConnect.UpdateWxCard(ctx, self.WxCard.Channel, updateWxCardReq)
}

// UpsertPaidCards 更新付费会员卡，在开通付费会员或退出付费会员时调用
func (self *Member) UpsertPaidCards(ctx context.Context, record MemberPaidCardRecord) error {
	paidCard := genPaidCard(record)
	for _, c := range self.PaidCards {
		if c.CardId != record.CardId {
			continue
		}
		if c.CardNumber != "" && c.CardNumber != record.CardNumber {
			continue
		}
		if c.ExpireAt.After(time.Now()) {
			// 还未到期
			paidCard.StartedAt = c.StartedAt
		}
		condition := bson.M{
			"_id":              self.Id,
			"accountId":        self.AccountId,
			"paidCards.cardId": paidCard.CardId,
		}
		if paidCard.CardNumber != "" {
			condition["paidCards.cardNumber"] = paidCard.CardNumber
		}
		updater := bson.M{
			"$set": bson.M{
				"paidCards.$": paidCard,
			},
		}
		return extension.DBRepository.UpdateOne(ctx, C_MEMBER, condition, updater)
	}
	condition := bson.M{
		"_id":       self.Id,
		"accountId": self.AccountId,
	}
	updater := bson.M{
		"$push": bson.M{
			"paidCards": paidCard,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_MEMBER, condition, updater)
}

// genPaidCard 根据开卡记录生成一个付费会员卡
func genPaidCard(record MemberPaidCardRecord) PaidCard {
	paidCard := PaidCard{
		CardId:     record.CardId,
		CardType:   record.CardType,
		StartedAt:  record.StartAt,
		ExpireAt:   record.ExpireAt,
		CardNumber: record.CardNumber,
	}
	if paidCard.ExpireAt.IsZero() {
		paidCard.ExpireAt = time.Date(2099, 1, 1, 0, 0, 0, 0, time.Local)
	}
	return paidCard
}

// GetMemberPaidCardsByRecordIds 通过开卡记录获取付费会员卡
func (m *Member) GetMemberPaidCardsByRecordIds(ctx context.Context, recordIds []bson.ObjectId) (result []IMemberCard) {
	memberPaidCardRecords, err := CMemberPaidCardRecord.GetAllByIds(ctx, recordIds)
	if err != nil {
		log.Warn(ctx, "Failed to get member paid card record", log.Fields{
			"id":     recordIds,
			"errMsg": err.Error(),
		})
		return
	}

	cardIds := core_util.ExtractArrayStringField("CardId", memberPaidCardRecords)

	memberPaidCards, err := CMemberPaidCard.GetAllByIds(ctx, util.ToMongoIds(cardIds))
	if err != nil {
		log.Warn(ctx, "Failed to get member paid card", log.Fields{
			"ids":    cardIds,
			"errMsg": err.Error(),
		})
		return
	}
	for _, c := range memberPaidCards {
		result = append(result, c)
	}
	return
}

// GetEnableMemberCards 获取当前客户所有可用的付费会员卡
func (m *Member) GetEnablePaidMemberCards(ctx context.Context) (result []IMemberCard) {
	enablePaidCardIds := []bson.ObjectId{}
	for _, paidCard := range m.PaidCards {
		if paidCard.ExpireAt.After(time.Now()) {
			enablePaidCardIds = append(enablePaidCardIds, paidCard.CardId)
		}
	}
	if len(enablePaidCardIds) > 0 {
		condition := bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"isDeleted": false,
			"type":      TYPE_PAID_MEMBER,
			"_id": bson.M{
				"$in": enablePaidCardIds,
			},
		}
		paidCards, err := CMemberPaidCard.GetAllByCondition(ctx, condition)
		if err != nil {
			log.Warn(ctx, "Failed to get member paid card", log.Fields{
				"ids":    enablePaidCardIds,
				"errMsg": err.Error(),
			})
		}
		for _, paidCard := range paidCards {
			result = append(result, paidCard)
		}
	}
	return result

}

// GetEnableMemberCards 获取当前客户的所有可用付费会员卡和等级会员卡
func (m *Member) GetEnableMemberCards(ctx context.Context, memberLevel uint64) (result []IMemberCard) {
	enablePaidCardIds := []bson.ObjectId{}
	for _, paidCard := range m.PaidCards {
		if paidCard.ExpireAt.After(time.Now()) {
			enablePaidCardIds = append(enablePaidCardIds, paidCard.CardId)
		}
	}
	if len(enablePaidCardIds) > 0 {
		condition := bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"isDeleted": false,
			"type":      TYPE_PAID_MEMBER,
			"_id": bson.M{
				"$in": enablePaidCardIds,
			},
		}
		paidCards, err := CMemberPaidCard.GetAllByCondition(ctx, condition)
		if err != nil {
			log.Warn(ctx, "Failed to get member paid card", log.Fields{
				"ids":    enablePaidCardIds,
				"errMsg": err.Error(),
			})
		}
		for _, paidCard := range paidCards {
			result = append(result, paidCard)
		}
	}

	if !m.CardId.IsZero() {
		setting, err := member_model.CMemberSetting.GetByType(ctx, member_model.MEMBER_SETTING_TYPE_MEMBER)
		// 如果存在配置则需要开启，如果不存在设置，可以认为无零售模块应当获取等级
		if setting.Enabled || err != nil {
			memberLevel, err := CMemberLevel.GetByLevel(ctx, memberLevel)
			if err != nil {
				log.Warn(ctx, "Failed to get member level", log.Fields{
					"memberLevel": memberLevel,
					"errMsg":      err.Error(),
				})
			} else {
				result = append(result, memberLevel)
			}
		}
	}
	return result

}

// GetDeletedMemberById 查询客户，包含满足条件但是被删除的客户
func (*Member) GetDeletedMemberById(ctx context.Context, accountId, id string) *Member {
	condition := GenerateIdQueryConditionWithDeleted(ctx, id)

	member := new(Member)
	extension.DBRepository.FindOne(ctx, C_MEMBER, condition, member)

	if member.Id.Hex() != "" {
		DecryptMember(ctx, member)
		return member
	}
	return nil
}

// GenerateIdQueryConditionWithDeleted 根据 id 生成查询条件，同时搜索客户 id、渠道 openId、渠道 unionId 和手机号
func GenerateIdQueryConditionWithDeleted(ctx context.Context, id string) bson.M {
	defaultCondition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": true,
	}
	condition := bson.M{}
	if bson.IsObjectIdHex(id) {
		condition = appendQueryCondition(defaultCondition, "_id", bson.ObjectIdHex(id))
	} else {
		findByOpenId := appendQueryCondition(defaultCondition, "socials.openId", id)
		findByUnionId := appendQueryCondition(defaultCondition, "socials.unionId", id)
		findByPhone := appendQueryCondition(defaultCondition, "phone", GetValueConditionWithEncrypted(ctx, id, nil))

		condition["$or"] = []bson.M{
			findByPhone,
			findByOpenId,
			findByUnionId,
		}
	}
	return condition
}

// removeDulicateMember 对传入的客户列表去重
func (self *Member) removeDulicateMember(members []Member) []Member {
	newMembers := []Member{}
	memberIds := []string{}
	for _, member := range members {
		if member.Id == self.Id {
			continue
		}
		if !core_util.StrInArray(member.Id.Hex(), &memberIds) {
			newMembers = append(newMembers, member)
			memberIds = append(memberIds, member.Id.Hex())
		}
	}

	return newMembers
}

// 手机号为空时，删除预生成的天猫或京东渠道，防止会员被合并
func (self *Member) UpdateSocialWithoutPhone(ctx context.Context) {
	newSocials := []Social{}
	isIgnoreSocials := true
	for _, social := range self.Socials {
		if social.Origin == constant.TAOBAO_MEMBER && social.Nickname == "" && social.UnionId == "" && !social.Subscribed {
			isIgnoreSocials = false
			continue
		}

		if social.Origin == constant.JD_MEMBER && social.Nickname == "" && social.UnionId == "" && !social.Subscribed {
			isIgnoreSocials = false
			continue
		}

		newSocials = append(newSocials, social)
	}

	self.Socials = newSocials
	self.IgnoreSocials = isIgnoreSocials && self.IgnoreSocials
}

// IsPaidMember 判断当前客户是不是付费会员，通过校验付费会员卡有效期判断
func (self *Member) IsPaidMember(ctx context.Context) (bool, time.Time, bson.ObjectId) {
	var oldExpireAt time.Time
	var isPaidMember bool
	var cardId bson.ObjectId
	for _, paidCard := range self.PaidCards {
		if paidCard.CardType == MEMBER_TYPE_PAID_MEMBER {
			isPaidMember = true
			oldExpireAt = paidCard.ExpireAt
			cardId = paidCard.CardId
			break
		}
	}

	return isPaidMember, oldExpireAt, cardId
}

// SendMemberCreatedEvent 触发客户创建事件
func (self *Member) SendMemberCreatedEvent(ctx context.Context) {
	eventBody := component.CustomerEventBody{
		AccountId:  self.AccountId.Hex(),
		MemberId:   self.Id.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_DISTRIBUTOR_MEMBER_CREATED,
		CreateTime: time.Now().UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"memberId": self.Id.Hex(),
		},
	}
	eventBody.SendCustomerEventWithBusinessTag(ctx)
}

// SendMemberUpdatedEvent 触发客户更新事件
func (self *Member) SendMemberUpdatedEvent(ctx context.Context) {
	eventBody := component.CustomerEventBody{
		AccountId:  self.AccountId.Hex(),
		MemberId:   self.Id.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_DISTRIBUTOR_MEMBER_UPDATED,
		CreateTime: time.Now().UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"memberId": self.Id.Hex(),
		},
	}
	eventBody.SendCustomerEventWithBusinessTag(ctx)
}

func (self *Member) UpdateCardNumberBeforeMerged(ctx context.Context) error {
	condition := Common.GenDefaultConditionById(ctx, self.Id)
	updater := bson.M{
		"$set": bson.M{
			"cardNumber":       self.Id.Hex(),
			"originCardNumber": self.CardNumber,
		},
	}
	return self.UpdateOne(ctx, condition, updater)
}
