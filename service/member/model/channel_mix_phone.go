package model

import (
	"context"
	"strings"
	"time"

	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"
)

const (
	C_CHANNEL_MIX_PHONE = "channelMixPhone"
)

var (
	CChannelMixPhone = ChannelMixPhone{}
)

type ChannelMixPhone struct {
	Id        bson.ObjectId `bson:"_id,omitempty"`
	AccountId bson.ObjectId `bson:"accountId"`
	CreatedAt time.Time     `bson:"createdAt,omitempty"`
	UpdatedAt time.Time     `bson:"updatedAt,omitempty"`
	IsDeleted bool          `bson:"isDeleted"`
	// 单店铺对应 channel.channelId，品牌店铺对应 channel.appId
	ChannelId string `bson:"channelId"`
	MixPhone  string `bson:"mixPhone"`
	Phone     string `bson:"phone,omitempty"`
	// 单店铺对应 member.socials.openId，品牌店铺对应 member.socials.unionId
	OpenId string `bson:"openId,omitempty"`
}

func GetCompatibleChannelId(appId, channelId string, integrationModes []string) string {
	if share_model.CChannel.IsBrandMember(integrationModes) && appId != "" {
		return appId
	}
	return channelId
}

// compatibleChannelId：单店铺传 channel.channelId，品牌店铺传 channel.appId
func (m *ChannelMixPhone) GetByCondition(ctx context.Context, condition bson.M) (*ChannelMixPhone, error) {
	condition["accountId"] = util.GetAccountIdAsObjectId(ctx)
	condition["isDeleted"] = false
	result := &ChannelMixPhone{}
	err := Common.GetByCondition(ctx, condition, C_CHANNEL_MIX_PHONE, result)
	if err != nil {
		return nil, err
	}
	result.decryptPhone(ctx)
	return result, err
}

// compatibleChannelId：单店铺传 channel.channelId，品牌店铺传 channel.appId
func (m *ChannelMixPhone) GetByCompatibleChannelIdAndMixPhone(ctx context.Context, compatibleChannelId, mixPhone string) (*ChannelMixPhone, error) {
	condition := Common.GenDefaultCondition(ctx)
	condition["channelId"] = compatibleChannelId
	condition["mixPhone"] = mixPhone
	result := &ChannelMixPhone{}
	err := Common.GetByCondition(ctx, condition, C_CHANNEL_MIX_PHONE, result)
	if err != nil {
		return nil, err
	}
	result.decryptPhone(ctx)
	return result, err
}

// GetAllByCompatibleChannelIdAndMixPhone 查询所有包含 openId，并且不含 phone 的记录
func (m *ChannelMixPhone) GetAllByCompatibleChannelIdAndMixPhone(ctx context.Context, compatibleChannelId, mixPhone string) ([]ChannelMixPhone, error) {
	condition := Common.GenDefaultCondition(ctx)
	condition["channelId"] = compatibleChannelId
	condition["mixPhone"] = mixPhone
	condition["openId"] = bson.M{"$exists": true}
	condition["phone"] = bson.M{"$exists": false}
	result := []ChannelMixPhone{}
	_, err := Common.GetAllByCondition(ctx, condition, nil, 0, C_CHANNEL_MIX_PHONE, &result)
	if err != nil {
		return nil, err
	}
	return result, err
}

func (*ChannelMixPhone) ListByPagination(ctx context.Context, condition extension.PagingCondition) (int, []ChannelMixPhone, error) {
	result := []ChannelMixPhone{}
	total, err := extension.DBRepository.FindByPagination(ctx, C_CHANNEL_MIX_PHONE, condition, &result)
	for i := range result {
		result[i].decryptPhone(ctx)
	}
	return total, result, err
}

func (m *ChannelMixPhone) ResetOpenIds(ctx context.Context, openIdsToReset []string) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["openId"] = bson.M{
		"$in": openIdsToReset,
	}
	updater := bson.M{
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
		"$unset": bson.M{
			"openId": "",
		},
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_CHANNEL_MIX_PHONE, selector, updater)
	return err
}

func (m *ChannelMixPhone) Upsert(ctx context.Context) error {
	selector, updater := m.getUpsertSelectorAndUpdater(ctx)
	_, err := extension.DBRepository.Upsert(ctx, C_CHANNEL_MIX_PHONE, selector, updater)
	if err != nil {
		return err
	}
	return nil
}

func (m *ChannelMixPhone) BatchUpsert(ctx context.Context, channelMixPhones []*ChannelMixPhone) error {
	docs := []interface{}{}
	for _, channelMixPhone := range channelMixPhones {
		selector, updater := channelMixPhone.getUpsertSelectorAndUpdater(ctx)
		docs = append(docs, selector, updater)
	}
	_, err := extension.DBRepository.BatchUpsert(ctx, C_CHANNEL_MIX_PHONE, docs...)
	if err != nil && len(err.WriteErrors) > 0 {
		return err
	}
	return nil
}

func (m *ChannelMixPhone) getUpsertSelectorAndUpdater(ctx context.Context) (bson.M, bson.M) {
	m.encryptPhone(ctx)

	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"channelId": m.ChannelId,
		"mixPhone":  m.MixPhone,
	}
	if strings.Contains(m.MixPhone, "****") {
		if m.Phone != "" && m.OpenId == "" {
			selector["phone"] = m.Phone
		} else if m.Phone == "" && m.OpenId != "" {
			selector["openId"] = m.OpenId
		} else if m.Phone != "" && m.OpenId != "" {
			selector["$or"] = []bson.M{
				{"phone": m.Phone},
				{"openId": m.OpenId},
			}
		}
	}
	selector = util.FormatConditionContainedOr(selector)

	setter := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"channelId": m.ChannelId,
		"mixPhone":  m.MixPhone,
		"updatedAt": time.Now(),
	}
	if m.Phone != "" {
		setter["phone"] = m.Phone
	}
	if m.OpenId != "" {
		setter["openId"] = m.OpenId
	}
	updater := bson.M{
		"$set": setter,
		"$setOnInsert": bson.M{
			"createdAt": time.Now(),
		},
	}

	return selector, updater
}

func (m *ChannelMixPhone) decryptPhone(ctx context.Context) {
	if m.Phone == "" {
		return
	}
	decryptedPhone, err := extension.Encryption.Decrypt(ctx, m.Phone)
	if err != nil {
		return
	}
	m.Phone = decryptedPhone
	return
}

func (m *ChannelMixPhone) encryptPhone(ctx context.Context) {
	if m.Phone == "" {
		return
	}
	m.Phone, _ = EncryptStringValue(ctx, m.Phone)
	return
}

func (*ChannelMixPhone) Iterate(ctx context.Context, condition bson.M, sorter []string) (extension.IterWrapper, error) {
	return extension.DBRepository.Iterate(ctx, C_CHANNEL_MIX_PHONE, condition, sorter)
}

func (*ChannelMixPhone) RemoveByOpenId(ctx context.Context, channelId, openId string) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"channelId": channelId,
		"openId":    openId,
	}
	return extension.DBRepository.RemoveOne(ctx, C_CHANNEL_MIX_PHONE, selector)
}
