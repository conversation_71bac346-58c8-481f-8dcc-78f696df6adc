package model

import (
	"fmt"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/service/share/util"
	"reflect"
	"regexp"
	"strings"
	"time"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

const (
	OPERATE_THIS_YEAR = "operate_this_year"
	OPERATE_LAST_YEAR = "operate_last_year"
)

type ScoreInfo struct {
	Id          bson.ObjectId
	Brief       string
	SubBrief    string
	BusinessId  string
	Identifier  string
	MemberId    string
	memberLevel *uint64 // 触发积分增减时 member 的等级，如果是 maievent-level-up 触发，则使用 eventProperties 中的 level
	Assigner    string
	Reason      string
	Description string
	Remark      string
	Origin      string
	RawMeta     string
	ChannelInfo ChannelInfo
	Score       int64
	CreatedAt   int64
	Meta        bson.M
	User        interface{}

	ProtectScoreDueDate time.Time
	OrderCreateTime     int64
	OccurredAt          int64

	ExpireAt      time.Time
	FixedExpireAt *bool  // 仅 UA 支持延期，因此固定有效期仅在 scoreV2 的逻辑判断
	OutTradeNo    string // 外部流水号

	// NEW -----------------
	ProtectScoreId                bson.ObjectId
	IssueProtectScore             bool // 是否是发放保护期积分
	IssueOverflowScore            bool // 是否是发溢出积分
	ProtectScoreNeedAddToScore    int64
	ProtectScoreNeedAddToOverflow int64

	resetSetting      *ScoreResetRule
	limitScoreSetting *MemberScoreSetting

	incScoreOption *incScoreOption
	scoreTrigger   RewardTrigger

	descriptionMapper map[string]interface{}
	eventProperties   map[string]interface{}

	taskLog *MemberTaskLog

	ConsumeRecord ScoreConsumeRecord

	DisableEvent                 bool
	NotDecAnnualAccumulatedScore bool
}

type incScoreOption struct {
	capOption               capOption
	disableCheckScoreEnough bool // 减扣积分时是否关闭积分足够检查
	needMinusTotalScore     bool // 是否减除总积分
}

type capOption struct {
	needOptCAP     *bool  // 指定是否操作 cap
	onlyOperateCAP bool   // 是否只操作 CAP
	operateYear    string // 操作年份
}

type ScoreInfoGenerator interface {
	GenerateScoreInfo(ctx context.Context, needInit bool) *ScoreInfo
}

func (s *ScoreInfo) Copy(ctx context.Context, parameters map[string]interface{}) *ScoreInfo {
	info := ScoreInfo{}
	_ = copier.Instance(copier.NewOption().SetCopyUnexported(true)).From(s).CopyTo(&info)
	info.taskLog = s.taskLog
	info.memberLevel = s.memberLevel
	info.FixedExpireAt = s.FixedExpireAt
	infoValue := reflect.ValueOf(&info).Elem()
	for k, v := range parameters {
		fieldValue := infoValue.FieldByName(k)
		setterValue := reflect.ValueOf(v)
		if fieldValue.CanSet() && setterValue.Type().ConvertibleTo(fieldValue.Type()) {
			fieldValue.Set(setterValue.Convert(fieldValue.Type()))
		}
	}

	if info.Score > 500000 {
		log.Error(ctx, "score is suspected to be too big", log.Fields{"scoreInfo": info})
	}

	return &info
}

func (s *ScoreInfo) SetByScoreReward(reward TaskReward) *ScoreInfo {
	if reward.Score.Value > 0 {
		return s.setByScore(reward.Score)
	}

	if reward.AmountScore.Value > 0 {
		return s.setByScore(reward.AmountScore)
	}
	return s
}

func (s *ScoreInfo) setByScore(score ScoreReward) *ScoreInfo {
	if score.FixExpireAt {
		s.FixedExpireAt = new(bool)
		*s.FixedExpireAt = true
		s.ExpireAt = score.ExpireAt
	}
	if score.ExpireAfterDays > 0 {
		s.ExpireAt = core_util.SetHourMinAndSecond(time.Now().AddDate(0, 0, int(score.ExpireAfterDays)), 23, 59, 59)
	}

	if score.SubBrief != "" {
		s.SubBrief = score.SubBrief
	}

	if s.incScoreOption == nil {
		s.incScoreOption = &incScoreOption{}
	}

	var capType = new(bool)
	switch score.CAPType {
	case CAP_TYPE_IN_CAP:
		*capType = true
		s.incScoreOption.capOption.needOptCAP = capType
	case CAP_TYPE_OUT_CAP:
		s.incScoreOption.capOption.needOptCAP = capType
	}

	return s
}

func (s *ScoreInfo) InitOperatorYear(ctx context.Context) {
	var isLastYear bool
	if s.NeedDelayScore(ctx) {
		scoreValidInfoLog, _, _ := CMemberScoreValidInfoLog.GetEarliestLogByIdentifier(ctx, bson.ObjectIdHex(s.MemberId), s.BusinessId)
		isLastYear = s.BusinessId != "" && scoreValidInfoLog != nil && scoreValidInfoLog.CreatedAt.Year()+1 == time.Now().Year()
	}

	if isLastYear {
		s.SetCAPOperateYear(OPERATE_LAST_YEAR)
	} else {
		s.SetCAPOperateYear(OPERATE_THIS_YEAR)
	}
}

func (s *ScoreInfo) NeedDelayScore(ctx context.Context) bool {
	// 目前只有 UA 需要延期
	specialAccountIds := []string{
		"636b604e4017cb6e6a5ee953", // UA feature test
		"6166d86f9526b7705a449df3", // UA staging
		"6180ea32e0df4b1472704228", // UA production
		"658e25912300df434014de93", // staging koston v2 积分测试
	}

	if util.StrInArray(util.GetAccountId(ctx), &specialAccountIds) {
		return true
	}

	return false
}

func (s *ScoreInfo) TryCreateValidLog(ctx context.Context, history *ScoreHistory, createdAt time.Time) error {
	if s.NeedDelayScore(ctx) || s.GetCAPSetting(ctx).IsYearLimitationEnabled {
		// 创建有效行为日志
		infoLog := CMemberScoreValidInfoLog.genLog(ctx, history.BusinessId, history.SubBrief, history.MemberId, createdAt)
		err := infoLog.Create(ctx)
		if err != nil {
			log.Warn(ctx, "Create score valid info log failed.", log.Fields{"err": err.Error(), "history": history})
		}
		return err
	}
	return nil
}

func (s *ScoreInfo) InitIncScoreOption(ctx context.Context) *ScoreInfo {
	if s.resetSetting == nil {
		s.resetSetting = CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	}
	s.GetCAPSetting(ctx)
	if s.incScoreOption == nil {
		s.incScoreOption = &incScoreOption{}
		needOptCAP := s.NeedOptCAP()
		s.incScoreOption.capOption.needOptCAP = &needOptCAP
	}
	return s
}

// NeedOptCAP 是否需要计算年度累计积分
func (s *ScoreInfo) NeedOptCAP() bool {
	if s.Brief == SYNC_FROM_DOUYIN_HISTORY_MEMBER {
		return false
	}
	// 指定 更新/不更新 时
	if s.incScoreOption.capOption.needOptCAP != nil {
		if !(*s.incScoreOption.capOption.needOptCAP) {
			return false
		} else {
			return true
		}
	}
	// 非退款的负积分不参与计算年度累计
	if s.Score < 0 && !core_util.ContainsString(&RollbackScoreSubBrief, s.SubBrief) {
		return false
	}

	// 正积分包含积分消耗流水认为是兑换积分退款，不计算年度累计
	if s.Score > 0 && s.ConsumeRecord.Id.Valid() {
		return false
	}
	// TODO 添加标记标识正积分为回滚积分

	// 保护积分的 CAP 计算在创建时就需要计算，发放时无需重新算
	if s.ProtectScoreId.Valid() && s.IssueProtectScore {
		return false
	}
	// 没有 subBrief 默认需要操作年度累计积分
	if s.SubBrief == "" {
		return true
	}

	// 未开启年度累计上限需要减扣年度累计积分
	if s.limitScoreSetting == nil || !s.limitScoreSetting.GetCAPSetting().IsCAPEnabled() {
		return true
	}

	// 释放溢出积分不减扣年度累计积分，在进入溢出积分时已经加到年度累计积分中了
	if s.IssueOverflowScore {
		return false
	}

	// 无效 CAP 行为不参与计算年度 CAP
	if core_util.IsContains(s.SubBrief, NinCAPScoreSubBrief) {
		return false
	}

	// 更新非本年的 CAP，不更新 member.annualAccumulatedScore
	if !s.IsUpdateThisYearCAP() {
		return false
	}

	return true
}

// NeedCheckScoreEnough 是否需要判断积分足够扣减
func (s *ScoreInfo) NeedCheckScoreEnough() bool {
	// 非减扣积分不验证积分是否足够
	if s.Score >= 0 {
		return false
	}

	// 禁用检查时不检查积分是否足够
	if s.incScoreOption.disableCheckScoreEnough {
		return false
	}

	// 如果开启年度积分上限，且指定仅减扣积分上限，不检查积分是否足够
	if s.limitScoreSetting != nil && s.limitScoreSetting.GetCAPSetting().IsCAPEnabled() && s.incScoreOption.capOption.onlyOperateCAP {
		return false
	}

	// 退款扣积分可负，无需判断积分足够
	if core_util.IsContains(s.SubBrief, RollbackScoreSubBrief) {
		return false
	}

	return true
}

// DisableCheckScoreEnough 设置不需要验证积分足够
func (s *ScoreInfo) DisableCheckScoreEnough() {
	s.incScoreOption.disableCheckScoreEnough = true
}

// SetCAPOperateYear 该积分变动影响的年份
func (s *ScoreInfo) SetCAPOperateYear(operateYear string) {
	s.incScoreOption.capOption.operateYear = operateYear
}

// SetOnlyOperateCAP 设置仅操作 CAP，如进入保护期后需要先将 CAP 加上
func (s *ScoreInfo) SetOnlyOperateCAP() {
	s.incScoreOption.capOption.onlyOperateCAP = true
	needOptCAP := true
	s.incScoreOption.capOption.needOptCAP = &needOptCAP
}

// SetShouldMinusTotalScore 设置需要减累计总积分
func (s *ScoreInfo) SetShouldMinusTotalScore() {
	s.incScoreOption.needMinusTotalScore = true
}

// NeedMinusTotalScore 是否需要减扣总积分，即是否算回滚
func (s *ScoreInfo) NeedMinusTotalScore() bool {
	if s.Score > 0 {
		return false
	}

	if s.incScoreOption.needMinusTotalScore {
		return true
	}

	if s.incScoreOption.capOption.onlyOperateCAP {
		return false
	}

	switch s.Brief {
	case REFUND_REDUCE_SCORE:
		return true
	}

	switch s.SubBrief {
	case SCORE_SUB_BRIEF_ALL_REFUND, SCORE_SUB_BRIEF_PARTIAL_REFUND:
		return true
	case SCORE_SUB_BRIEF_OVERFLOW:
		return false
	}

	return false
}

// 是否算消费积分
func (s *ScoreInfo) IsCostScore() bool {
	if s.Score > 0 {
		return false
	}

	switch s.SubBrief {
	case ASSIGNER_EXPIRED_SCORE:
		return false
	}

	return true
}

func (s *ScoreInfo) GetScoreResetRule() *ScoreResetRule {
	return s.resetSetting
}

func (s *ScoreInfo) GetCAPSetting(ctx context.Context) *CAPSetting {
	if s.limitScoreSetting == nil {
		setting, err := CMemberScoreSetting.GetByCode(ctx, C_SCORE_SETTING_CODE_LIMIT)
		if err != nil {
			return nil
		}
		s.limitScoreSetting = &setting
	}

	return s.limitScoreSetting.GetCAPSetting()
}

func (s *ScoreInfo) IsUpdateThisYearCAP() bool {
	switch s.incScoreOption.capOption.operateYear {
	case OPERATE_LAST_YEAR:
		return false
	case OPERATE_THIS_YEAR:
		return true
	}
	return true
}

func (s *ScoreInfo) NeedUpdateScore() bool {
	// 仅更新积分上限时不用修改 member.score
	if s.NeedOptCAP() && s.incScoreOption.capOption.onlyOperateCAP {
		return false
	}

	if s.limitScoreSetting == nil || !s.limitScoreSetting.GetCAPSetting().IsCAPEnabled() {
		return true
	}
	return true
}

func (s *ScoreInfo) IsOnlyOperateCAP() bool {
	return s.incScoreOption.capOption.onlyOperateCAP
}

func (s *ScoreInfo) SetTrigger(trigger RewardTrigger) {
	s.scoreTrigger = trigger
}

func (s *ScoreInfo) SetDescriptionMapper(mapper map[string]interface{}) {
	if mapper != nil {
		s.descriptionMapper = mapper
	}
}

func (s *ScoreInfo) GetDescription() (description string) {
	var (
		descriptions []string
	)
	if len(strings.Split(s.Description, "_")) > 0 {
		descriptions = strings.Split(s.Description, "_")
		description = descriptions[0]
	}

	if s.descriptionMapper == nil || len(s.descriptionMapper) == 0 {
		description = s.Description
		return
	}
	if ok, err := regexp.MatchString(`\{.*\}`, s.Description); err != nil || !ok {
		description = s.Description
		return
	}

	keyReg, _ := regexp.Compile(`{.*}`)
	for i := range descriptions {
		if i == 0 {
			continue
		}
		if keyReg.MatchString(descriptions[i]) {
			key := strings.Trim(keyReg.FindString(descriptions[i]), "{}")
			if val, exists := s.descriptionMapper[key]; exists {
				description = fmt.Sprintf("%s_%s", description, keyReg.ReplaceAllString(descriptions[i], cast.ToString(val)))
			}
		} else {
			description = fmt.Sprintf("%s_%s", description, descriptions[i])
		}
	}

	return
}

func (s *ScoreInfo) GetRemark() string {
	return s.Remark
}

func (s *ScoreInfo) GetTrigger() RewardTrigger {
	return s.scoreTrigger
}

func (s *ScoreInfo) LimitScoreByTask(t *TaskInfo) {
	if s.Score <= 0 {
		return
	}
	if t.NeedLimitScoreReward() {
		if s.Score >= int64(t.RemainRewardScore) {
			s.Score = int64(t.RemainRewardScore)
		}
	}
}

func (s *ScoreInfo) GetMemberLevel(ctx context.Context, m Member) uint64 {
	if s.memberLevel == nil {
		return cast.ToUint64(m.Level)
	}
	return *s.memberLevel
}

func (s *ScoreInfo) SetMemberLevel(level uint64) {
	lv := level
	s.memberLevel = &lv
}

func (s *ScoreInfo) GenerateScoreHistory(ctx context.Context, member *Member) *ScoreHistory {
	trigger := s.GetTrigger()

	if member == nil {
		m := (&ScoreHistory{MemberId: bson.ObjectIdHex(s.MemberId)}).GetMember(ctx)
		member = &m
	}

	scoreHistory := ScoreHistory{
		Id:           s.Id,
		Assigner:     s.Assigner,
		Increment:    s.Score,
		MemberId:     member.Id,
		Brief:        s.Brief,
		SubBrief:     s.SubBrief,
		Description:  s.GetDescription(),
		Channel:      s.ChannelInfo,
		AccountId:    member.AccountId,
		Meta:         s.Meta,
		Balance:      member.Score + s.Score,
		BusinessId:   s.BusinessId,
		Identifier:   s.Identifier,
		Origin:       s.Origin,
		CurrentLevel: uint64(member.Level),
		OutTradeNo:   s.OutTradeNo,
		Remark:       s.GetRemark(),

		TriggerType:  trigger.TriggerType,
		TriggerScene: trigger.TriggerScene,
		TriggerId:    trigger.TriggerId,
		Trigger:      trigger.Trigger,

		DisablePointChangedEvent: s.DisableEvent,
	}

	if s.ExpireAt.IsZero() {
		scoreHistory.ExpireAt = CScoreHistory.GetExpireAt(ctx, s, time.Now())
	} else {
		scoreHistory.ExpireAt = s.ExpireAt
	}

	if s.FixedExpireAt != nil {
		scoreHistory.FixedExpireAt = *s.FixedExpireAt
	}

	if s.Brief == SYNC_FROM_DOUYIN_HISTORY_MEMBER {
		scoreHistory.FixedExpireAt = true
		scoreHistory.ExpireAt = time.Date(2024, 9, 15, 23, 59, 59, 0, time.Local)
	}

	if s.User != nil {
		scoreHistory.User = s.User.(User)
	}
	if s.CreatedAt != 0 {
		scoreHistory.CreatedAt = util.TransUnixToTime(s.CreatedAt)
	}
	scoreHistory.GetMember(ctx)
	return &scoreHistory
}

func (s *ScoreInfo) SetResetSetting(resetSetting *ScoreResetRule) *ScoreInfo {
	s.resetSetting = resetSetting
	return s
}

func (s *ScoreInfo) SetLimitSetting(limitSetting *MemberScoreSetting) *ScoreInfo {
	s.limitScoreSetting = limitSetting
	return s
}
