package model

import (
	"context"
	"encoding/json"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/core/util/algorithm"
	"mairpc/proto/common/types"
	"mairpc/service/share/component"
	"mairpc/service/share/util"

	"github.com/spf13/cast"
)

type TaskInfo struct {
	ChannelId            string
	EventId              string
	Task                 *MemberTask
	EventProperties      map[string]interface{}
	TriggerIdentifier    string // 本次触发任务的唯一标识
	RewardId             string
	Amount               uint64
	WithoutPreventAmount uint64 // 不包含屏蔽商品的 amount
	AmountScoreRewardId  string
	Brief                string
	Identifier           string
	scoreInfo            *ScoreInfo
	SingleScoreEnabled   bool // 每笔积分上限是否开启
	MaxRewardScore       uint64
	RemainRewardScore    uint64

	noRewardSetting *MemberScoreSetting

	// TODO 后续和 eventTaskBuilder，现在暂时从 builder 传递
	scoreResetRule      *ScoreResetRule
	scoreSettingsMapper map[string]*MemberScoreSetting
	levelMapper         map[uint64]*MemberLevel
	memberId            string
	originEventRequest  *types.Event

	taskLog *MemberTaskLog
	reward  *MemberTaskReward
}

func GenerateTaskInfoByEvent(ctx context.Context, event *types.Event, trigger TaskEventTrigger, task *MemberTask, eventProperties map[string]interface{}, member *Member) *TaskInfo {
	task.EnsureScoreDescriptionAndRemark()
	taskInfo := TaskInfo{
		ChannelId:            event.ChannelId,
		EventId:              event.EventId,
		Task:                 task,
		EventProperties:      eventProperties,
		TriggerIdentifier:    getTaskIdentifierByEvent(event, trigger),
		Amount:               cast.ToUint64(eventProperties[trigger.AmountField]),
		WithoutPreventAmount: cast.ToUint64(eventProperties[trigger.AmountField]),
		memberId:             member.Id.Hex(),
		originEventRequest:   event,
	}

	return &taskInfo
}

func (t *TaskInfo) InitSetting(ctx context.Context) *TaskInfo {
	setting := t.GetScoreLimitSetting(ctx)
	if setting != nil && setting.ReceiveLimit.SingleLimit > 0 {
		t.SingleScoreEnabled = true
		rewards, err := CMemberTaskReward.FindAllRewardByIdentifier(ctx, t.TriggerIdentifier, bson.ObjectIdHex(t.memberId))
		if err != nil {
			log.Error(ctx, "Get rewards failed.", log.Fields{"err": err.Error(), "identifier": t.TriggerIdentifier})
		} else {
			t.MaxRewardScore = setting.ReceiveLimit.SingleLimit
			for _, reward := range rewards {
				if reward.TaskInfo.IsDefaultScoreRule() {
					// 额外积分不受单笔上限限制，需要排除
					actualScore := reward.RewardInfo.ActualReward.Score
					for _, bonus := range reward.RewardInfo.BonusInfo.ScoreBonuses {
						actualScore -= bonus.GetBonusScore(reward.RewardInfo.Amount)
					}
					t.MaxRewardScore -= actualScore
					if t.MaxRewardScore <= 0 {
						t.MaxRewardScore = 0
						break
					}
				}
			}
			t.RemainRewardScore = t.MaxRewardScore
		}
	}
	_, err := t.RemovePreventAmount(ctx)
	if err != nil {
		log.Error(ctx, "remove prevent amount failed.", log.Fields{"err": err, "taskInfo": t})
	}
	return t
}

func getTaskIdentifierByEvent(event *types.Event, trigger TaskEventTrigger) string {
	if event == nil {
		return ""
	}

	if trigger.IdentifierField != "" {
		eventProperties := map[string]interface{}{}
		json.Unmarshal([]byte(event.Properties), &eventProperties)
		i, ok := eventProperties[trigger.IdentifierField]
		if ok {
			return cast.ToString(i)
		}
	}

	return event.MsgId
}

func (t *TaskInfo) GetSubBrief() string {
	switch t.EventId {
	case component.MAIEVENT_PURCHASE_PRODUCT_SERVICE, component.MAIEVENT_PURCHASE_PRODUCT, component.MAIEVENT_ORDER_COMPLETED:
		return SCORE_SUB_BRIEF_RETAIL
	case component.MAIEVENT_ACTIVATE:
		return SCORE_SUB_BRIEF_WELCOME
	case component.MAIEVENT_REFUND_ORDER:
		if isAllRefund, ok := t.EventProperties["isAllRefund"].(bool); ok && isAllRefund {
			return SCORE_SUB_BRIEF_ALL_REFUND
		}
		return SCORE_SUB_BRIEF_PARTIAL_REFUND
	default:
		return ""
	}
}

// TODO 有一种情况还需要处理，如果限制订单最多积分为 10，每满 1 元 1 积分，下单 100 元获得 10 积分。此时 taskReward.rewardInfo 的 amount 应该如何处理
// 获取获得奖励时用于计算金额的 amount
func (t *TaskInfo) GetAmount(ctx context.Context) uint64 {
	var (
		noRewardSetting, _ = CMemberScoreSetting.GetByCode(ctx, C_SCORE_SETTING_CODE_NO_SCORE_REWARD)
		amount             uint64
	)
	switch t.Task.GetSettingType() {
	case TASK_SETTING_TYPE_MULTI_AMOUNT:
		if t.Task.Settings.MultiAmount > 0 {
			var (
				rewardedValues = algorithm.CSet.Instance()
				multiSettings  = (MultiAmountSettingsOperator)(t.Task.MultiAmountSettings).SortOtherRuleToEnd()
			)
			for i, rule := range multiSettings {
				multiSettings[i].NoRewardSetting = &noRewardSetting
				itemAmount, err := multiSettings[i].GetAmountByFilterArray(ctx, t, rewardedValues)
				if err != nil {
					log.Warn(ctx, "Invalid amount array.", log.Fields{"err": err})
				}
				if itemAmount < rule.Amount {
					continue
				}
				amount += itemAmount
			}
		} else {
			log.Error(ctx, "Invalid task config", log.Fields{"task": *(t.Task)})
		}
		return amount
	default:
		return t.Amount
	}
}

func (t *TaskInfo) GenerateScoreInfo(ctx context.Context, needInit bool) *ScoreInfo {
	scoreInfo := (&ScoreInfo{
		SubBrief:        t.GetSubBrief(),
		Remark:          t.Task.ScoreRemark,
		taskLog:         t.taskLog,
		eventProperties: t.EventProperties,
	}).
		SetResetSetting(t.scoreResetRule).
		SetLimitSetting(t.scoreSettingsMapper[C_SCORE_SETTING_CODE_LIMIT])

	if needInit {
		scoreInfo.InitIncScoreOption(ctx)
	}

	scoreInfo.BusinessId = t.TriggerIdentifier

	if t.Identifier != "" {
		scoreInfo.Identifier = t.Identifier
	}

	// 获取渠道信息
	channelInfo := &ChannelInfo{Origin: "portal"}
	channelInfo.SetByChannelId(ctx, t.ChannelId)
	scoreInfo.ChannelInfo = *channelInfo

	switch t.EventId {
	case component.MAIEVENT_PURCHASE_PRODUCT_SERVICE, component.MAIEVENT_PURCHASE_PRODUCT, component.MAIEVENT_ORDER_COMPLETED:
		if createTime, ok := t.EventProperties["orderCreateTime"]; ok {
			scoreInfo.OrderCreateTime = cast.ToInt64(createTime)
		}
		if occurredAt, ok := t.EventProperties["occurredAt"]; ok {
			scoreInfo.OccurredAt = cast.ToInt64(occurredAt)
		}
	case component.MAIEVENT_REFUND_ORDER:
		if refundId, ok := t.EventProperties["refundId"]; ok {
			if scoreInfo.Meta == nil {
				scoreInfo.Meta = bson.M{
					"refundId": refundId,
				}
			}
		}
	case component.MAIEVENT_MEMBER_LEVEL_UP:
		scoreInfo.SetMemberLevel(cast.ToUint64(t.EventProperties["level"]))
	default:
		// empty
	}
	needSetLevel := !(t.Task.GetSettingType() == TASK_SETTING_TYPE_TIMES && t.Task.Settings.Times > 1)
	if level, ok := t.EventProperties["memberLevel"]; ok && needSetLevel {
		v, err := cast.ToUint16E(level)
		if err == nil {
			scoreInfo.SetMemberLevel(cast.ToUint64(v))
		}
	}

	if t.Brief != "" {
		scoreInfo.Brief = t.Brief
	}
	scoreInfo.Origin = t.Task.Id.Hex()
	scoreInfo.SetTrigger(t.GenerateTrigger(ctx))
	scoreInfo.SetDescriptionMapper(t.EventProperties)
	t.scoreInfo = scoreInfo

	return scoreInfo
}

func (t *TaskInfo) GetIdentifier(ctx context.Context) string {
	if t.scoreInfo == nil {
		t.GenerateScoreInfo(ctx, true)
	}
	return t.scoreInfo.BusinessId
}

func (t *TaskInfo) GenerateTrigger(ctx context.Context) RewardTrigger {
	var triggerType string
	switch {
	case t.Task.Type == MEMBER_TASK_TYPE_SCORE_RULE:
		triggerType = TRIGGER_TYPE_SCORE_RULE
	case t.Task.Type == "task" && t.Task.IsDefault:
		triggerType = TRIGGER_TYPE_TASK
	default:
		triggerType = TRIGGER_TYPE_INVITATION
	}
	trigger := RewardTrigger{
		TriggerType:  triggerType,
		TriggerScene: t.Task.Name,
		TriggerId:    t.Task.Id.Hex(),
		Trigger:      t.EventId,
		From:         REWARD_FROM_TASK,
	}
	return trigger
}

func (t *TaskInfo) NeedLimitScoreReward() bool {
	// 单笔订单获取积分上限仅作用于默认积分规则
	if !t.Task.IsDefaultScoreRule() {
		return false
	}
	if t.MaxRewardScore <= 0 && !t.SingleScoreEnabled {
		return false
	}
	return true
}

func (t *TaskInfo) RefreshRestScoreRewardLimit(ctx context.Context, s *ScoreInfo) {
	if t.NeedLimitScoreReward() && s.Score > 0 {
		if int64(t.RemainRewardScore)-s.Score < 0 {
			log.Error(ctx, "Over limit score reward", log.Fields{"taskInfo": *t, "scoreInfo": *s})
		} else {
			t.RemainRewardScore = t.RemainRewardScore - uint64(s.Score)
		}
	}
}

func (t *TaskInfo) SetNoRewardSetting(noRewardSetting *MemberScoreSetting) {
	t.noRewardSetting = noRewardSetting
}

func (t *TaskInfo) SetScoreResetRule(s *ScoreResetRule) *TaskInfo {
	t.scoreResetRule = s
	return t
}

func (t *TaskInfo) SetLevelMapper(l map[uint64]*MemberLevel) *TaskInfo {
	t.levelMapper = l
	return t
}

func (t *TaskInfo) SetTaskLog(taskLog *MemberTaskLog) *TaskInfo {
	t.taskLog = taskLog
	return t
}

func (t *TaskInfo) SetMemberScoreSettingsMapper(s map[string]*MemberScoreSetting) *TaskInfo {
	t.scoreSettingsMapper = s
	return t
}

func (t *TaskInfo) GetNoRewardSetting(ctx context.Context) *MemberScoreSetting {
	if t.noRewardSetting == nil {
		noRewardSetting, _ := CMemberScoreSetting.GetByCode(ctx, C_SCORE_SETTING_CODE_NO_SCORE_REWARD)
		t.noRewardSetting = &noRewardSetting
	}
	return t.noRewardSetting
}

func (t *TaskInfo) GetScoreLimitSetting(ctx context.Context) *MemberScoreSetting {
	var setting MemberScoreSetting
	if t.scoreSettingsMapper == nil {
		t.scoreSettingsMapper = map[string]*MemberScoreSetting{}
	}

	if val, ok := t.scoreSettingsMapper[C_SCORE_SETTING_CODE_LIMIT]; ok {
		return val
	} else {
		var err error
		setting, err = CMemberScoreSetting.GetByCode(ctx, C_SCORE_SETTING_CODE_LIMIT)
		if err != nil {
			return nil
		}
		t.scoreSettingsMapper[C_SCORE_SETTING_CODE_LIMIT] = &setting
	}

	return t.scoreSettingsMapper[C_SCORE_SETTING_CODE_LIMIT]
}

func (t *TaskInfo) GetScoreSettingMapper() map[string]*MemberScoreSetting {
	if t.scoreSettingsMapper == nil {
		t.scoreSettingsMapper = make(map[string]*MemberScoreSetting)
	}
	return t.scoreSettingsMapper
}

func (t *TaskInfo) RemovePreventAmount(ctx context.Context) (bool, error) {
	if t.noRewardSetting == nil {
		t.GetNoRewardSetting(ctx)
	}
	return t.noRewardSetting.NoScoreReward.RemovePreventAmount(ctx, t)
}

func (t *TaskInfo) GetOriginRequest() *types.Event {
	return t.originEventRequest
}

func (t *TaskInfo) GetMemberTaskReward(ctx context.Context) *MemberTaskReward {
	if t.reward != nil {
		return t.reward
	}
	reward, err := CMemberTaskReward.FindByIdentifier(ctx, t.Task.Code, t.TriggerIdentifier, bson.ObjectIdHex(t.memberId))
	if err != nil {
		return nil
	}
	return &reward
}

func (t *TaskInfo) ResetRollbackAmount(ctx context.Context, reward MemberTaskReward) {
	// 暂时只对订单退款做处理
	if !util.StrInArray(t.EventId, &[]string{
		component.MAIEVENT_REFUND_ORDER,
		component.MAIEVENT_REFUND_PRODUCT,
	}) {
		return
	}
	rollbackTrigger, _ := t.Task.GetEventRollbacker(t.EventId)
	rewardTrigger, _ := t.Task.GetEventTrigger(reward.RewardInfo.TriggerEventId)
	rollbackAmount := cast.ToUint64(t.EventProperties[rollbackTrigger.AmountField])
	shouldRollbackAmount := GetShouldRollbackAmount(rewardTrigger, reward.RewardInfo.Amount, reward.CountTotalRollbackAmount(), rollbackAmount)
	if shouldRollbackAmount == 0 {
		return
	}
	// 没有满足金额限制，全退
	t.EventProperties[rollbackTrigger.AmountField] = shouldRollbackAmount
	if t.WithoutPreventAmount == rollbackAmount {
		t.WithoutPreventAmount = shouldRollbackAmount
	}
}

func GetShouldRollbackAmount(rewardTrigger TaskEventTrigger, rewardAmount, rollbackedAmount, rollbackAmount uint64) uint64 {
	if rollbackAmount == 0 || rewardAmount <= rollbackAmount+rollbackedAmount {
		return 0
	}
	remainAmount := rewardAmount - rollbackedAmount - rollbackAmount
	hasAmountLimit := false
	for _, filter := range rewardTrigger.PropertyFilters {
		// 如果相等说明事件属性中对金额字段做了限制，下面做判断，如果扣除本次即将减扣的金额后不满足限制，那么奖励全退
		if filter.Field == rewardTrigger.AmountField {
			hasAmountLimit = true
			for _, rule := range filter.Rules {
				// 满足任一条件就不处理了
				if rule.Compare(remainAmount) {
					return 0
				}
			}
		}
	}
	// 没有金额限制就不处理
	if !hasAmountLimit {
		return 0
	}
	// 没有满足金额限制，全退
	return remainAmount + rollbackAmount
}
