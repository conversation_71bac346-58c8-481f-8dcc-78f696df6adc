package member

import (
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	pb_member "mairpc/proto/member"
	"mairpc/service/member/jobs"
	"mairpc/service/member/model"
	"mairpc/service/share/util"
	"time"

	"github.com/spf13/cast"
	"github.com/stretchr/testify/assert"
	"golang.org/x/net/context"
)

// 验证提前 30 天通知的情况下，积分计算正确
func (m *MemberFeatureSuite) TestV1SenderForYearDailyBasic() {
	ctx := m.GetV1SenderAccountCtx()
	rule := model.CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	assert.NotNil(m.T(), rule)
	rule.ResetType = model.SCORE_RESET_TYPE_YEAR
	rule.NotificationSetting = model.ScoreResetNotificationSetting{
		Type: model.SCORE_RESET_NOTIFICATION_TYPE_DAILY,
	}
	var (
		sender      = m.GetV1SenderByRule(rule)
		member      = m.PrepareMemberForSender(ctx)
		nextResetAt = util.GetEndTimeOfMonth(time.Now())
		resetTo     = util.GetEndTimeOfMonth(time.Now())
		resetFrom   = resetTo.AddDate(-1, 0, 0)
		execFrom    = nextResetAt.AddDate(0, 0, -35)
	)
	m.RewardScore(ctx, member, resetFrom.AddDate(0, 0, -1), 100)          // 本月底应该过期
	m.RewardScore(ctx, member, util.GetStartTimeOfYear(nextResetAt), 100) // 不应该过期
	member = m.RefreshMember(ctx, member)
	assert.Equal(m.T(), int64(200), member.Score, member.Id.Hex())
	execFrom = time.Date(execFrom.Year(), execFrom.Month(), execFrom.Day(), 12, 0, 0, 0, time.Local)
	m.iterateSendDate(ctx, member, execFrom, nextResetAt.AddDate(0, 0, -1), sender, true, 100, 1)
}

func (m *MemberFeatureSuite) TestV1SenderForEndOfMonthDailyBasic() {
	ctx := m.GetV1SenderAccountCtx()
	rule := model.CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	assert.NotNil(m.T(), rule)
	rule.ResetType = model.SCORE_RESET_TYPE_END_OF_MONTH
	rule.ResetDate.Month = 3 // 三个月过期
	rule.NotificationSetting = model.ScoreResetNotificationSetting{
		Type: model.SCORE_RESET_NOTIFICATION_TYPE_DAILY,
	}
	var (
		sender      = m.GetV1SenderByRule(rule)
		member      = m.PrepareMemberForSender(ctx)
		nextResetAt = util.GetEndTimeOfMonth(time.Now())
		resetTo     = util.GetEndTimeOfMonth(time.Now())
		resetFrom   = resetTo.AddDate(0, -rule.ResetDate.Month, 0)
		execFrom    = nextResetAt.AddDate(0, 0, -35)
	)
	m.RewardScore(ctx, member, resetFrom.AddDate(0, 0, -1), 100) // 本月底应该过期
	m.RewardScore(ctx, member, execFrom.AddDate(0, 0, -1), 100)  // 不应该过期
	member = m.RefreshMember(ctx, member)
	assert.Equal(m.T(), int64(200), member.Score, member.Id.Hex())
	execFrom = time.Date(execFrom.Year(), execFrom.Month(), execFrom.Day(), 12, 0, 0, 0, time.Local)
	m.iterateSendDate(ctx, member, execFrom, nextResetAt.AddDate(0, 0, -1), sender, true, 100, 1)
}

func (m *MemberFeatureSuite) TestV1SenderForEndOfYearDailyBasic() {
	ctx := m.GetV1SenderAccountCtx()
	rule := model.CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	assert.NotNil(m.T(), rule)
	rule.ResetType = model.SCORE_RESET_TYPE_END_OF_YEAR
	rule.ResetInterval = 2 // 两年过期
	rule.NotificationSetting = model.ScoreResetNotificationSetting{
		Type: model.SCORE_RESET_NOTIFICATION_TYPE_DAILY,
	}
	var (
		sender      = m.GetV1SenderByRule(rule)
		member      = m.PrepareMemberForSender(ctx)
		nextResetAt = util.GetEndTimeOfYear(time.Now())
		resetTo     = util.GetEndTimeOfYear(time.Now())
		resetFrom   = resetTo.AddDate(-int(rule.ResetInterval), 0, 0)
		execFrom    = time.Date(time.Now().Year(), time.November, 30, 12, 0, 0, 0, time.Local)
	)
	m.RewardScore(ctx, member, resetFrom.AddDate(0, 0, -1), 100) // 本年底应该过期
	m.RewardScore(ctx, member, execFrom.AddDate(0, 0, -1), 100)  // 不应该过期
	member = m.RefreshMember(ctx, member)
	assert.Equal(m.T(), int64(200), member.Score, member.Id.Hex())
	m.iterateSendDate(ctx, member, execFrom, nextResetAt, sender, true, 100, 1)
}

func (m *MemberFeatureSuite) TestV1SenderForFixedDayDailyBasic() {
	ctx := m.GetV1SenderAccountCtx()
	rule := model.CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	assert.NotNil(m.T(), rule)
	rule.ResetType = model.SCORE_RESET_TYPE_FIXED_DAY
	rule.ResetDate.Month = int(time.Now().Month())
	rule.ResetDate.Day = time.Now().Day() // 设为当前时间晚上过期
	rule.NotificationSetting = model.ScoreResetNotificationSetting{
		Type: model.SCORE_RESET_NOTIFICATION_TYPE_DAILY,
	}
	var (
		sender      = m.GetV1SenderByRule(rule)
		member      = m.PrepareMemberForSender(ctx)
		nextResetAt = time.Date(time.Now().Year(), time.Month(rule.ResetDate.Month), rule.ResetDate.Day, 23, 59, 59, 0, time.Local)
		execFrom    = nextResetAt.AddDate(0, 0, -35)
	)
	m.RewardScore(ctx, member, nextResetAt.AddDate(-1, 0, 0), 100) // 应该过期
	m.RewardScore(ctx, member, nextResetAt.AddDate(0, 0, -1), 100) // 应该过期
	member = m.RefreshMember(ctx, member)
	assert.Equal(m.T(), int64(200), member.Score, member.Id.Hex())
	execFrom = time.Date(execFrom.Year(), execFrom.Month(), execFrom.Day(), 12, 0, 0, 0, time.Local)
	m.iterateSendDate(ctx, member, execFrom, nextResetAt.AddDate(0, 0, -1), sender, true, 200, 1)
}

// 验证提前 30 天通知的情况下，在通知下下次积分过期时，积分统计正确
func (m *MemberFeatureSuite) TestV1SenderForYearDaily2() {
	ctx := m.GetV1SenderAccountCtx()
	rule := model.CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	assert.NotNil(m.T(), rule)
	rule.ResetType = model.SCORE_RESET_TYPE_YEAR
	rule.NotificationSetting = model.ScoreResetNotificationSetting{
		Type: model.SCORE_RESET_NOTIFICATION_TYPE_DAILY,
	}
	var (
		sender      = m.GetV1SenderByRule(rule)
		member      = m.PrepareMemberForSender(ctx)
		nextResetAt = util.GetEndTimeOfMonth(time.Now())
		resetTo     = util.GetEndTimeOfMonth(time.Now())
		resetFrom   = resetTo.AddDate(-1, 0, 0)
		execFrom    = nextResetAt.AddDate(0, 0, -1)
	)
	m.RewardScore(ctx, member, resetFrom.AddDate(0, 0, -1), 100)          // 本月底应该过期
	m.RewardScore(ctx, member, util.GetStartTimeOfYear(nextResetAt), 100) // 不应该过期
	member = m.RefreshMember(ctx, member)
	assert.Equal(m.T(), int64(200), member.Score, member.Id.Hex())
	execFrom = time.Date(execFrom.Year(), execFrom.Month(), execFrom.Day(), 12, 0, 0, 0, time.Local)
	m.iterateSendDate(ctx, member, execFrom, nextResetAt.AddDate(0, 0, 1), sender, false, 0, 1)
}

func (m *MemberFeatureSuite) TestV1SenderForEndOfMonthDaily2() {
	ctx := m.GetV1SenderAccountCtx()
	rule := model.CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	assert.NotNil(m.T(), rule)
	rule.ResetType = model.SCORE_RESET_TYPE_END_OF_MONTH
	rule.ResetDate.Month = 3 // 三个月过期
	rule.NotificationSetting = model.ScoreResetNotificationSetting{
		Type: model.SCORE_RESET_NOTIFICATION_TYPE_DAILY,
	}
	var (
		sender      = m.GetV1SenderByRule(rule)
		member      = m.PrepareMemberForSender(ctx)
		nextResetAt = util.GetEndTimeOfMonth(time.Now())
		resetTo     = util.GetEndTimeOfMonth(time.Now())
		resetFrom   = resetTo.AddDate(0, -rule.ResetDate.Month, 0)
		execFrom    = nextResetAt.AddDate(0, 0, -5)
	)
	m.RewardScore(ctx, member, resetFrom.AddDate(0, 0, -1), 100) // 本月底应该过期
	m.RewardScore(ctx, member, execFrom.AddDate(0, 0, -1), 100)  // 不应该过期
	member = m.RefreshMember(ctx, member)
	assert.Equal(m.T(), int64(200), member.Score, member.Id.Hex())
	execFrom = time.Date(execFrom.Year(), execFrom.Month(), execFrom.Day(), 12, 0, 0, 0, time.Local)
	m.iterateSendDate(ctx, member, execFrom, nextResetAt.AddDate(0, 0, 1), sender, false, 0, 1)
}

func (m *MemberFeatureSuite) TestV1SenderForYearWeekly() {
	ctx := m.GetV1SenderAccountCtx()
	rule := model.CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	assert.NotNil(m.T(), rule)
	// 设置每周三提醒
	rule.ResetType = model.SCORE_RESET_TYPE_YEAR
	rule.NotificationSetting = model.ScoreResetNotificationSetting{
		Type: model.SCORE_RESET_NOTIFICATION_TYPE_WEEKLY,
		Day:  3,
	}
	var (
		sender      = m.GetV1SenderByRule(rule)
		member      = m.PrepareMemberForSender(ctx)
		nextResetAt = util.GetEndTimeOfMonth(time.Now())
		resetTo     = util.GetEndTimeOfMonth(time.Now())
		resetFrom   = resetTo.AddDate(-1, 0, 0)
		execFrom    = nextResetAt.AddDate(0, 0, -7)
	)
	m.RewardScore(ctx, member, resetFrom.AddDate(0, 0, -1), 100)
	m.RewardScore(ctx, member, time.Now(), 100)
	member = m.RefreshMember(ctx, member)
	assert.Equal(m.T(), int64(200), member.Score, member.Id.Hex())
	execFrom = time.Date(execFrom.Year(), execFrom.Month(), execFrom.Day(), 12, 0, 0, 0, time.Local)
	m.iterateSendDate(ctx, member, execFrom, nextResetAt, sender, true, 100, 1)
}

func (m *MemberFeatureSuite) TestV1SenderForYearMonthly() {
	ctx := m.GetV1SenderAccountCtx()
	rule := model.CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	assert.NotNil(m.T(), rule)
	// 设置每月 10 日提醒
	rule.ResetType = model.SCORE_RESET_TYPE_YEAR
	rule.NotificationSetting = model.ScoreResetNotificationSetting{
		Type: model.SCORE_RESET_NOTIFICATION_TYPE_MONTHLY,
		Day:  10,
	}
	var (
		sender      = m.GetV1SenderByRule(rule)
		member      = m.PrepareMemberForSender(ctx)
		nextResetAt = util.GetEndTimeOfMonth(time.Now())
		resetTo     = util.GetEndTimeOfMonth(time.Now())
		resetFrom   = resetTo.AddDate(-1, 0, 0)
		execFrom    = util.GetStartTimeOfMonth(time.Now())
	)
	m.RewardScore(ctx, member, resetFrom.AddDate(0, 0, -1), 100)
	m.RewardScore(ctx, member, time.Now(), 100)
	member = m.RefreshMember(ctx, member)
	assert.Equal(m.T(), int64(200), member.Score, member.Id.Hex())
	execFrom = time.Date(execFrom.Year(), execFrom.Month(), execFrom.Day(), 12, 0, 0, 0, time.Local)
	m.iterateSendDate(ctx, member, execFrom, nextResetAt, sender, true, 100, 1)
}

func (m *MemberFeatureSuite) GetV1SenderByRule(rule *model.ScoreResetRule) *jobs.V1Sender {
	sender := &jobs.V1Sender{}
	sender.SetRule(rule)
	return sender
}

func (m *MemberFeatureSuite) PrepareMemberForSender(ctx context.Context) model.Member {
	member := m.GetRandomMember(ctx)
	assert.True(m.T(), member.Id.Valid())
	m.RemoveMemberScoreHistories(ctx, member)
	member = m.RefreshMember(ctx, member)
	assert.True(m.T(), member.Id.Valid())
	assert.Equal(m.T(), int64(0), member.Score)
	assert.Equal(m.T(), 0, m.CountMemberScoreHistories(ctx, member))
	return member
}

func (m *MemberFeatureSuite) RemoveMemberScoreHistories(ctx context.Context, member model.Member) {
	extension.DBRepository.UpdateOne(
		ctx,
		model.C_MEMBER,
		model.Common.GenDefaultConditionById(ctx, member.Id),
		bson.M{
			"$set": bson.M{
				"score": 0,
			},
		},
	)
	extension.DBRepository.RemoveAll(
		ctx,
		model.C_SCORE_HISTORY,
		bson.M{
			"accountId": member.AccountId,
			"memberId":  member.Id,
		},
	)
}

func (m *MemberFeatureSuite) CountMemberScoreHistories(ctx context.Context, member model.Member) int {
	count, err := extension.DBRepository.Count(
		ctx,
		model.C_SCORE_HISTORY,
		bson.M{
			"accountId": member.AccountId,
			"memberId":  member.Id,
		},
	)
	assert.NoError(m.T(), err)
	return count
}

func (m *MemberFeatureSuite) RefreshMember(ctx context.Context, member model.Member) model.Member {
	return m.GetMemberById(ctx, member.Id.Hex())
}

func (m *MemberFeatureSuite) RewardScore(ctx context.Context, member model.Member, createdAt time.Time, score int64) {
	memberService.UpdateScore(ctx, &pb_member.UpdateScoreRequest{
		Ids:         []string{member.Id.Hex()},
		Score:       score,
		CreatedAt:   createdAt.Unix(),
		Description: "feature test",
	})
}

func (m *MemberFeatureSuite) iterateSendDate(ctx context.Context, member model.Member, from, to time.Time, sender jobs.ScoreExpireNotificationSender, mapExists bool, expiredScore, expectedSendCount int) {
	var (
		sendCount int
	)
	for from.Before(to) {
		jobs.SetJobExecTime(from)
		if sender.ShouldSend(ctx) {
			sendCount++
			replaceMap, err := sender.GenerateReplaceMap(ctx, member)
			assert.NoError(m.T(), err, member.Id.Hex(), from.Format(core_util.COMMON_TIME_LAYOUT))
			assert.Equal(m.T(), mapExists, len(replaceMap) > 0, member.Id.Hex(), from.Format(core_util.COMMON_TIME_LAYOUT))
			if mapExists {
				assert.Equal(m.T(), expiredScore, cast.ToInt(replaceMap[model.PLACEHOLDER_EXPIRINGSCORE]), member.Id.Hex(), from.Format(core_util.COMMON_TIME_LAYOUT))
			}
		}
		from = from.AddDate(0, 0, 1)
	}
	assert.Equal(m.T(), expectedSendCount, sendCount, member.Id.Hex())
}
