package service

import (
	"encoding/json"
	"fmt"
	"mairpc/core/component"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	"mairpc/proto/member"
	"mairpc/service/ec/service"
	"mairpc/service/member/model"
	"mairpc/service/share/util"
	"time"

	"mairpc/core/extension/bson"

	"golang.org/x/net/context"
)

type WechatworkBusinessExtra struct {
	CreateFromWechatwork  bool   `json:"createFromWechatwork"`
	IsVisibleInWechatWork *bool  `json:"isVisibleInWechatWork"`
	WechatWorkTagGroupId  string `json:"wechatWorkTagGroupId"`
	NeedSyncToWechatwork  *bool  `json:"needSyncToWechatwork"`
}

func (MemberService) BatchUpdateTagGroupBusiness(ctx context.Context, req *member.BatchUpdateTagGroupBusinessRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	groupIds := []bson.ObjectId{}
	for _, businessUpdater := range req.BusinessUpdaters {
		gId := bson.ObjectIdHex(businessUpdater.Id)
		if !util.ObjectIdInArray(gId, &groupIds) {
			groupIds = append(groupIds, gId)
		}
	}

	tagGroups := model.CTagGroup.GetAllByCondition(ctx, bson.M{
		"_id": bson.M{
			"$in": groupIds,
		},
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}, []string{})

	groupMap := map[string]model.TagGroup{}
	for _, tagGroup := range tagGroups {
		groupMap[tagGroup.Id.Hex()] = tagGroup
	}

	for _, businessUpdater := range req.BusinessUpdaters {
		group := groupMap[businessUpdater.Id]
		condition := bson.M{
			"_id":       group.Id,
			"accountId": util.GetAccountIdAsObjectId,
		}
		setter := bson.M{}
		updater := bson.M{}
		isActionRemoved := false
		extra := util.UnmarshalJsonString(businessUpdater.Extra)
		if business := group.GetBusiness(businessUpdater.Business); business != nil {
			switch businessUpdater.Action {
			case member.UpdateTagGroupBusinessRequest_UPSERT:
				for key, value := range business.Extra {
					if _, ok := extra[key]; !ok {
						extra[key] = value
					}
				}
				condition["businesses.business"] = businessUpdater.Business
				setter = bson.M{
					"businesses.$.extra": extra,
					"updatedAt":          time.Now(),
				}
			case member.UpdateTagGroupBusinessRequest_REMOVE:
				updater["$pull"] = bson.M{
					"businesses": bson.M{
						"business": businessUpdater.Business,
					},
				}
				setter = bson.M{
					"updatedAt": time.Now(),
				}
				isActionRemoved = true
				if business.Business == "wechatwork" {
					UpdateSyncTagGroupIds(ctx, &group, isActionRemoved)
				}
			}
		} else {
			if businessUpdater.Action == member.UpdateTagGroupBusinessRequest_UPSERT {
				updater["$push"] = bson.M{
					"businesses": model.TagGroupBusiness{
						Business: businessUpdater.Business,
						Extra:    extra,
					},
				}
				setter = bson.M{
					"updatedAt": time.Now(),
				}
			}
		}
		if businessUpdater.Business == "wechatwork" {
			newOrder := GetNewOrderByBusinessExtra(ctx, group, isActionRemoved)
			if newOrder != -1 {
				setter["order"] = newOrder
			}
		}
		if req.StaffOperateDisabled != nil {
			setter["staffOperateDisabled"] = req.StaffOperateDisabled.Value
		}
		updater["$set"] = setter
		err := model.CTagGroup.UpdateByCondition(ctx, condition, updater)
		if err != nil {
			return nil, err
		}
	}

	component.GO(ctx, func(ctx context.Context) {
		createTagGroupAuditLog(ctx, req.BusinessUpdaters, groupMap)
	})

	return &response.EmptyResponse{}, nil
}

func GetNewOrderByBusinessExtra(ctx context.Context, tagGroup model.TagGroup, isActionRemoved bool) int {
	newOrder := -1
	// 移除企微的 business，将序号更新为 0 并将其后的标签组序号前移 1
	if isActionRemoved {
		newOrder = 0
		tagGroup.UdpateOrderAfterTagGroup(ctx)
	} else if tagGroup.Order == 0 {
		nextOrder, err := model.CTagGroup.GetNextOrder(ctx)
		if err == nil {
			newOrder = int(nextOrder)
		}
	}
	return newOrder
}

func createTagGroupAuditLog(ctx context.Context, businessUpdaters []*member.UpdateTagGroupBusinessRequest, groupMap map[string]model.TagGroup) {
	for _, updater := range businessUpdaters {
		businessExtra := &WechatworkBusinessExtra{}
		json.Unmarshal([]byte(updater.Extra), businessExtra)
		if businessExtra.NeedSyncToWechatwork != nil {
			if *businessExtra.NeedSyncToWechatwork {
				service.CreateAuditLog(ctx, "标签同步", fmt.Sprintf("新增同步标签【%s】", groupMap[updater.Id].Name), "wechatwork")
			} else {
				service.CreateAuditLog(ctx, "标签同步", fmt.Sprintf("移除同步标签【%s】", groupMap[updater.Id].Name), "wechatwork")
			}
		}
		if businessExtra.IsVisibleInWechatWork != nil {
			service.CreateAuditLog(ctx, "导购权限", "编辑导购可见客户标签", "wechatwork")
		}
	}
}
