package service

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"mairpc/core/errors"
	"mairpc/core/extension"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/account"
	"mairpc/proto/client"
	"mairpc/proto/common/origin"
	pb_origin "mairpc/proto/common/origin"
	"mairpc/proto/common/request"
	"mairpc/proto/common/types"
	pb_ec_store "mairpc/proto/ec/store"
	pb "mairpc/proto/member"
	"mairpc/service/member/codes"
	"mairpc/service/member/elasticmodel"
	"mairpc/service/member/model"
	"mairpc/service/share/constant"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"
	"reflect"
	"regexp"
	"strings"
	"time"

	"mairpc/core/extension/bson"

	"github.com/asaskevich/govalidator"
	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

var (
	// This is all we support the mobile phone number of the regular expression pattern
	rxPhone = regexp.MustCompile(validators.PHONE_REGREX)

	socialsOrderByPrefix  = "socials."
	propertyOrderByPrefix = "properties."
)

const (
	SEARCH_TYPE_MEMBER_ID            = "memberId"
	SEARCH_TYPE_CARD_NUMBER          = "cardNumber"
	SEARCH_TYPE_OPEN_ID_UNION_ID     = "openIdUnionId"
	SEARCH_TYPE_NAME_PHONE_EMAIL     = "namePhoneEmail"
	SEARCH_TYPE_CONTACT_OR_PHONE     = "contactOrPhone"
	SEARCH_TYPE_MEMBER_ID_NAME       = "memberIdName"
	SEARCH_TYPE_NAME_CONTACT         = "nameContact"
	SEARCH_TYPE_NAME_CONTACT_ADDRESS = "nameContactAddress"
	SEARCH_TYPE_ACTIVATION_STAFF     = "activationStaff"
)

func (MemberService) SearchMember(ctx context.Context, req *pb.SearchMemberRequest) (*pb.MemberListResponse, error) {
	var (
		accountIds []bson.ObjectId
		orderBys   = []string{"-createdAt"}

		ids      []string
		size     int
		total    int64
		scrollId string
		err      error

		pageCondition extension.PagingCondition
		resp          *pb.MemberListResponse
		searchFromDB  bool
		mockReq       = &pb.SearchMemberRequest{}
	)

	// sometimes if request is invalid, we'd better return error
	// rather than return nothing. So we validate request here
	err = validateSearchRequest(req)
	if err != nil {
		return nil, err
	}

	if len(req.Ids) > 0 && len(req.ExcludeIds) > 0 {
		req.Ids = util.StrArrayDiff(req.Ids, req.ExcludeIds)
		// 指定 ids 后排除部分 ids 后不存在指定的 ids 了，说明结果一定为空，直接返回
		if len(req.Ids) == 0 {
			dbMembers := []model.Member{}
			resp := formatSearchMemberResponse(ctx, dbMembers, 0, req.RequiredCardName, getPropertyFilter(req.PropertyFilter, req.ShowSystemProperties))
			return resp, nil
		}
		// 指定 ids 后，从 ids 中移除 excludeIds 后就可以丢弃 excludeIds 了
		req.ExcludeIds = []string{}
	}

	// https://gitlab.maiscrm.com/mai/home/<USER>/issues/37960
	if req.SearchKeyWord != "" && req.SearchType == SEARCH_TYPE_OPEN_ID_UNION_ID {
		searchFromDB = true
		pageCondition = extension.PagingCondition{
			Selector: bson.M{
				"isDeleted": false,
				"accountId": util.GetAccountIdAsObjectId(ctx),
				"$or": []bson.M{
					{"socials.openId": req.SearchKeyWord},
					{"socials.unionId": req.SearchKeyWord},
				},
			},
			PageIndex: 1,
			PageSize:  20,
			Sortor:    []string{},
		}
		goto DbSearch
	}

	mockReq.Ids = req.Ids
	mockReq.ListCondition = req.ListCondition
	mockReq.ShowSystemProperties = req.ShowSystemProperties
	if len(req.Ids) > 0 && reflect.DeepEqual(req, mockReq) {
		searchFromDB = true
		pageCondition = util.FormatPagingCondition(bson.M{
			"isDeleted": false,
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"_id": bson.M{
				"$in": util.ToMongoIds(req.Ids),
			},
		}, req.ListCondition)
		goto DbSearch
	}

	if len(req.AccountIds) > 0 {
		accountIds = util.ToMongoIds(req.AccountIds)
	} else {
		accountIds = share_model.GenGroupAccountId(ctx, req.IsGroup)
	}

	ids, size, total, scrollId, err = searchByElasticSearch(ctx, req, accountIds, orderBys)
	if err != nil {
		return nil, err
	}
	if total == 0 {
		dbMembers := []model.Member{}
		resp := formatSearchMemberResponse(ctx, dbMembers, 0, req.RequiredCardName, getPropertyFilter(req.PropertyFilter, req.ShowSystemProperties))

		return resp, nil
	}

	// we shall still get data with sort info. Otherwise the member array that we
	// return will contains inproper data
	pageCondition = extension.PagingCondition{
		Selector: bson.M{
			"isDeleted": false,
			"_id":       bson.M{"$in": util.ToMongoIds(ids)},
			"accountId": bson.M{"$in": accountIds},
		},
		PageIndex: 1, // should always be the first page
		PageSize:  size,
		Sortor:    []string{},
	}

	// if we use scroll, the size should be fixed
	if req.UseScroll {
		pageCondition.PageSize = elasticmodel.MEMBER_SCROLL_SIZE
		if req.ScrollSize > 0 {
			pageCondition.PageSize = cast.ToInt(req.ScrollSize)
		}
	}

DbSearch:
	dbMembers := model.CMember.GetAllByPagination(ctx, pageCondition)
	if !searchFromDB {
		resp = formatSearchMemberResponse(ctx, dbMembers, uint64(total), req.RequiredCardName, getPropertyFilter(req.PropertyFilter, req.ShowSystemProperties))
		resp = reorderSearchMemberResponse(resp, ids)
		resp.ScrollId = scrollId
	} else {
		total := len(dbMembers)
		resp = formatSearchMemberResponse(ctx, dbMembers, uint64(total), req.RequiredCardName, getPropertyFilter(req.PropertyFilter, req.ShowSystemProperties))
	}

	return resp, nil
}

func searchByElasticSearch(ctx context.Context, req *pb.SearchMemberRequest, accountIds []bson.ObjectId, orderBys []string) (ids []string, size int, total int64, scrollId string, err error) {
	memberPropertiesMap := getMemberPropertiesMapByName(ctx, accountIds)

	var query core_util.ElasticQuery
	boolClause := &core_util.BoolClause{}
	mustClause := &core_util.MustClause{}
	mustNotClause := &core_util.MustNotClause{}
	boolClause.Set(mustNotClause)
	boolClause.Set(mustClause)
	query.SetClause(boolClause)

	// 是否精确搜索
	explicitSearch := true
	if needSwitchScore(req) {
		// since we use SearchKeyWord, the result should be scored
		// and sorted by score
		query.SwitchScore(true)
		// we use createdAt as secondary sort field to fix the result order
		// when _score is same
		orderBys = []string{"-_score", "createdAt"}
		if req.ListCondition != nil {
			req.ListCondition.OrderBy = []string{"-_score", "createdAt"}
		}
		if req.SearchKeyWord != "" {
			bClause := &core_util.BoolClause{}
			bClause, explicitSearch = genSearchKeywordClause(ctx, req.SearchType, req.SearchKeyWord, bClause, memberPropertiesMap)
			mustClause.Add(bClause)
		}
	}

	// tags
	if len(req.Tags) > 0 {
		tagsClause := &core_util.TermsClause{}
		tagsClause.Set("tags", req.Tags)
		mustClause.Add(tagsClause)
	}

	if len(req.ExcludeTags) > 0 {
		tagsClause := &core_util.TermsClause{}
		tagsClause.Set("tags", req.ExcludeTags)
		mustNotClause.Add(tagsClause)
	}

	var (
		hasStaticTags = req.TagsSelector != nil && len(req.TagsSelector.TagConditions) != 0
		hasSmartTag   = len(req.ModelTags) > 0 || len(req.RuleTags) > 0
	)
	if hasStaticTags && !hasSmartTag {
		tagsClause := formatTagsSelector(req.TagsSelector)
		mustClause.Add(tagsClause)
	} else if !hasStaticTags && hasSmartTag {
		bClause := &core_util.BoolClause{}
		mClause := &core_util.MustClause{}
		if len(req.ModelTags) > 0 {
			modelClause := &core_util.HasChildClause{}
			modelClause.NeedSpilt()
			modelClause.Set("memberLabelDetail", req.ModelTags)
			mClause.Add(modelClause)
		}
		if len(req.RuleTags) > 0 {
			mClause.Add(buildRuleTagsClause(req.RuleTags))
		}
		bClause.Set(mClause)
		mustClause.Add(bClause)
	} else if hasStaticTags && hasSmartTag {
		bClause := &core_util.BoolClause{}
		mClause := &core_util.MustClause{}
		staticClause := formatTagsSelector(req.TagsSelector)
		mClause.Add(staticClause)
		if len(req.ModelTags) > 0 {
			modelClause := &core_util.HasChildClause{}
			modelClause.NeedSpilt()
			modelClause.Set("memberLabelDetail", req.ModelTags)
			mClause.Add(modelClause)
		}
		if len(req.RuleTags) > 0 {
			mClause.Add(buildRuleTagsClause(req.RuleTags))
		}
		bClause.Set(mClause)
		mustClause.Add(bClause)
	}

	// score
	if req.Score != nil {
		scoreClause := util.ParseElasticIntegerRange("score", req.Score)
		mustClause.Add(scoreClause)
	}

	// annualCostScore
	if req.AnnualCostScore != nil {
		annualCostClause := util.ParseElasticIntegerRange("annualCostScore", req.AnnualCostScore)
		mustClause.Add(annualCostClause)
	}

	// cardId
	if len(req.Cards) > 0 {
		cardsClause := &core_util.TermsClause{}
		cardsClause.Set("cardId", req.Cards)
		mustClause.Add(cardsClause)
	}

	// blockedStatus
	if len(req.BlockStatuses) > 0 {
		blockedClause := &core_util.TermsClause{}
		blockedClause.Set("blockedStatus", req.BlockStatuses)
		mustClause.Add(blockedClause)
	}

	if len(req.Ids) > 0 {
		idClause := &core_util.TermsClause{}
		idClause.Set("_id", req.Ids)
		mustClause.Add(idClause)
	}

	if len(req.ExcludeIds) > 0 {
		idClause := &core_util.TermsClause{}
		idClause.Set("_id", req.ExcludeIds)
		mustNotClause.Add(idClause)
	}

	if req.CreatedAt != nil {
		createdAtClause := util.ParseElasticDateRange("createdAt", req.CreatedAt)
		mustClause.Add(createdAtClause)
	}

	// address property
	if req.Location != nil {
		addressClause := elasticmodel.GenPropertyClause(ctx, model.DEFAULT_PROPERTY_ADDRESS, req.Location, memberPropertiesMap[model.DEFAULT_PROPERTY_ADDRESS])
		mustClause.Add(addressClause)
	}

	// birthday property
	if req.Birthday != nil {
		dateClause := elasticmodel.GenPropertyClause(ctx, model.DEFAULT_PROPERTY_BIRTHDAY, req.Birthday, memberPropertiesMap[model.DEFAULT_PROPERTY_BIRTHDAY])
		mustClause.Add(dateClause)
	}

	if req.FilterZeroBirth {
		tClause := &core_util.TermClause{}
		tClause.Set("birth", 0)
		mustClause.Add(tClause)
	}

	// month and date for birthday
	if req.BirthFrom > 0 && req.BirthTo > 0 {
		if req.BirthFrom > req.BirthTo {
			bClause := &core_util.BoolClause{}
			sClause := &core_util.ShouldClause{}

			fromRange := &core_util.RangeClause{}
			fromRange.SetKey("birth")
			fromRange.SetGTE(req.BirthFrom)
			fromRange.SetLTE(1231)
			sClause.Add(fromRange)

			toRange := &core_util.RangeClause{}
			toRange.SetKey("birth")
			toRange.SetGTE(101)
			toRange.SetLTE(req.BirthTo)
			sClause.Add(toRange)

			bClause.Set(sClause)

			mustClause.Add(bClause)
		} else {
			dateRange := &core_util.RangeClause{}
			dateRange.SetKey("birth")
			dateRange.SetGTE(req.BirthFrom)
			dateRange.SetLTE(req.BirthTo)

			mustClause.Add(dateRange)
		}
	}

	if req.CardTime != nil {
		dateRange := util.ParseElasticDateRange("activatedAt", req.CardTime)
		if dateRange != nil {
			mustClause.Add(dateRange)
		}
	}

	if req.CardProvideTime != nil {
		dateRange := util.ParseElasticDateRange("cardProvideTime", req.CardProvideTime)
		if dateRange != nil {
			mustClause.Add(dateRange)
		}
	}

	// if member card is valid
	if req.IsValidCard != nil {
		var (
			bClause      = &core_util.BoolClause{}
			termClause   = &core_util.TermClause{}
			existsClause = &core_util.ExistsClause{}
			isValid      = req.IsValidCard.Value
		)

		if isValid {
			mClause := &core_util.MustClause{}

			bClause.Set(mClause)
			mClause.Add(termClause)
			mClause.Add(existsClause)
		} else {
			sClause := &core_util.ShouldClause{}

			bClause.Set(sClause)
			sClause.Add(termClause)
			sClause.Add(existsClause)
		}

		termClause.Set("isActivated", isValid)
		existsClause.Set("cardId", isValid)

		mustClause.Add(bClause)
	}

	// parse query for channel and source
	if len(req.Channels) > 0 || len(req.Origins) > 0 {
		var (
			bClause = &core_util.BoolClause{}
			sClause = &core_util.ShouldClause{}
		)
		bClause.Set(sClause)

		if len(req.Channels) > 0 {
			channelClauses := formatSocialClausesByChannelIds(ctx, req.Channels, "channel", req.Channels)
			sClause.AddMulti(channelClauses...)
		}

		if len(req.Origins) > 0 {
			originClauses := formatSocialClausesByOrigins(ctx, req.Origins, "origin", req.Origins)
			sClause.AddMulti(originClauses...)
		}

		mustClause.Add(bClause)
	}

	if len(req.Genders) > 0 {
		var genders []string
		for _, g := range req.Genders {
			genders = append(genders, strings.ToLower(g))
		}

		if util.StrInArray(model.UNKNOWN, &genders) {
			genders = append(genders, "")
		}

		genderClause := elasticmodel.GenPropertyClause(ctx, model.DEFAULT_PROPERTY_GENDER, genders, memberPropertiesMap[model.DEFAULT_PROPERTY_GENDER])
		mustClause.Add(genderClause)
	}

	if req.ActivatedState != nil {
		if req.ActivatedState.ActivatedState == pb.ActivatedStateFilter_DISABLED {
			disableClause := &core_util.TermClause{}
			disableClause.Set("isDisabled", true)
			mustClause.Add(disableClause)
		} else if req.ActivatedState.ActivatedState != pb.ActivatedStateFilter_ALL {
			isActivated := req.ActivatedState.ActivatedState == pb.ActivatedStateFilter_ACTIVATED
			activateClause := &core_util.TermClause{}
			activateClause.Set("isActivated", isActivated)
			mustClause.Add(activateClause)
		}
	}

	if len(req.Properties) > 0 {
		for _, property := range req.Properties {
			// 因为老代码的关系，我们对搜索 “属性值等于 string” 的情况做单独处理
			if property.PropertyValue != "" {
				propertyClause := elasticmodel.GenPropertyClause(ctx, property.PropertyName, property.PropertyValue, memberPropertiesMap[property.PropertyName])
				if propertyClause != nil {
					mustClause.Add(propertyClause)
				}
				continue
			}

			// 处理其它 oneOf 中的搜索条件
			propertyClause := elasticmodel.GenPropertyOneOfClause(ctx, property, memberPropertiesMap[property.PropertyName])
			if propertyClause != nil {
				mustClause.Add(propertyClause)
			}
		}
	}

	if len(req.Properties) == 0 && len(req.PropertiesIn) > 0 {
		shouldClause := &core_util.ShouldClause{}
		boolClause.Set(shouldClause)
		for _, property := range req.PropertiesIn {
			propertyClause := elasticmodel.GenPropertyOneOfClause(ctx, property, memberPropertiesMap[property.PropertyName])
			if propertyClause != nil {
				shouldClause.Add(propertyClause)
			}
		}
	}

	// validateSearchRequest 中会校验 req.Socials 中 Channel 和 Origin 必传，因此无需再处理 socials 查询条件中的通配符
	if len(req.Socials) > 0 {
		originalSocials := []*origin.SocialSearchInfo{}
		nonOriginalSocials := []*origin.SocialSearchInfo{}
		for _, socialInfo := range req.Socials {
			if core_util.IsZero(socialInfo) {
				continue
			}
			if socialInfo.IsOriginal != nil && socialInfo.IsOriginal.Value {
				originalSocials = append(originalSocials, socialInfo)
				continue
			}
			nonOriginalSocials = append(nonOriginalSocials, socialInfo)
		}

		if len(originalSocials) > 0 {
			originClause := &core_util.BoolClause{}
			originSClause := &core_util.ShouldClause{}
			originClause.Set(originSClause)
			mustClause.Add(originClause)
			for _, socialInfo := range originalSocials {
				formatSocialQuery(socialInfo, originSClause)
			}
		}

		if len(nonOriginalSocials) > 0 {
			nonOriginClause := &core_util.BoolClause{}
			nonOriginSClause := &core_util.ShouldClause{}
			nonOriginClause.Set(nonOriginSClause)
			mustClause.Add(nonOriginClause)
			for _, socialInfo := range nonOriginalSocials {
				formatSocialQuery(socialInfo, nonOriginSClause)
			}
		}
	}

	// 与 req.Socials 参数一样，无需处理 socials 查询条件中的通配符
	if len(req.ActivationSocials) > 0 {
		originalSocials := []*origin.SocialSearchInfo{}
		nonOriginalSocials := []*origin.SocialSearchInfo{}
		for _, socialInfo := range req.ActivationSocials {
			if core_util.IsZero(socialInfo) {
				continue
			}
			if socialInfo.IsOriginal != nil && socialInfo.IsOriginal.Value {
				originalSocials = append(originalSocials, socialInfo)
				continue
			}
			nonOriginalSocials = append(nonOriginalSocials, socialInfo)
		}

		if len(originalSocials) > 0 {
			originClause := &core_util.BoolClause{}
			originSClause := &core_util.ShouldClause{}
			originClause.Set(originSClause)
			mustClause.Add(originClause)
			for _, socialInfo := range originalSocials {
				formatSocialQuery(socialInfo, originSClause)
			}
		}

		if len(nonOriginalSocials) > 0 {
			nonOriginClause := &core_util.BoolClause{}
			nonOriginSClause := &core_util.ShouldClause{}
			nonOriginClause.Set(nonOriginSClause)
			mustClause.Add(nonOriginClause)
			for _, socialInfo := range nonOriginalSocials {
				formatSocialQuery(socialInfo, nonOriginSClause)
			}
		}
	}

	if req.ExcludeSocial != nil && req.ExcludeSocial.Origin != nil && req.ExcludeSocial.Channel != nil {
		excludeSocialClause := &core_util.ExistsClause{}
		excludeSocialClause.Set(fmt.Sprintf("socials.%s:%s", req.ExcludeSocial.Origin.Value, req.ExcludeSocial.Channel.Value), false)
		mustClause.Add(excludeSocialClause)
	}

	if len(req.Source) > 0 {
		sourceClause := &core_util.TermsClause{}
		sourceClause.Set("source", req.Source)
		mustClause.Add(sourceClause)
	}

	if len(req.ActivationSource) > 0 {
		activationSourceClause := &core_util.TermsClause{}
		activationSourceClause.Set("activationSource", req.ActivationSource)
		mustClause.Add(activationSourceClause)
	}

	// add common clauses for query
	if req.IsMember != nil {
		memberClause := &core_util.ExistsClause{}
		memberClause.Set("cardId", req.IsMember.Value)
		mustClause.Add(memberClause)
	}

	if req.IsPaidMember != nil {
		memberPaidCards, _ := model.CMemberPaidCard.GetByType(ctx, model.TYPE_PAID_MEMBER)
		if len(memberPaidCards) != 0 {
			if req.IsPaidMember.Value {
				paidCardClause := util.ParseElasticRange(fmt.Sprintf("paidCards.%s.expireAt", memberPaidCards[0].Id.Hex()), types.RangeType_CLOSE_INFINITE, time.Now(), nil)
				mustClause.Add(paidCardClause)
			} else {
				paidCardClause := &core_util.BoolClause{}
				paidCardSClause := &core_util.ShouldClause{}
				existsClause := &core_util.ExistsClause{}
				existsClause.Set(fmt.Sprintf("paidCards.%s", memberPaidCards[0].Id.Hex()), false)
				paidCardSClause.Add(existsClause)
				rangeClause := util.ParseElasticRange(fmt.Sprintf("paidCards.%s.expireAt", memberPaidCards[0].Id.Hex()), types.RangeType_INFINITE_CLOSE, nil, time.Now())
				paidCardSClause.Add(rangeClause)
				paidCardClause.Set(paidCardSClause)
				mustClause.Add(paidCardClause)
			}
		}
	}

	if len(req.Levels) > 0 {
		memberLevels, err := model.CMemberLevel.GetAllEnalbedLevel(ctx)
		if err != nil {
			return nil, 0, 0, "", err
		}
		lvs, _ := core_util.GetFieldsInAndNotInElement(req.Levels, core_util.ExtractArrayField("Level", memberLevels))
		levelClause := &core_util.TermsClause{}
		levelClause.Set("level", core_util.ToUInt64Array(lvs))
		mustClause.Add(levelClause)
	}

	if req.Growth != nil {
		growthClause := util.ParseElasticIntegerRange("growth", req.Growth)
		mustClause.Add(growthClause)
	}

	if req.UpdatedAt != nil {
		updatedAt := util.ParseElasticDateRange("updatedAt", req.UpdatedAt)
		if updatedAt != nil {
			mustClause.Add(updatedAt)
		}
	}

	if req.LevelStartedAt != nil {
		levelStartedAt := util.ParseElasticStringDateRange("levelStartedAt", req.LevelStartedAt)
		if levelStartedAt != nil {
			mustClause.Add(levelStartedAt)
		}
	}

	if req.ActivatedAt != nil {
		activatedAt := util.ParseElasticStringDateRange("activatedAt", req.ActivatedAt)
		if activatedAt != nil {
			mustClause.Add(activatedAt)
		}
	}

	if req.Grades != nil {
		gradesClause := util.ParseElasticIntegerRange("memberValue.grades", req.Grades)
		mustClause.Add(gradesClause)
	}

	if len(req.ExcludeOrigins) > 0 {
		originClauses := formatSocialClausesByOrigins(ctx, req.ExcludeOrigins, "origin", req.ExcludeOrigins)
		mustNotClause.AddMulti(originClauses...)
	}

	if req.IsFilterNullPhone {
		phoneClause := &core_util.TermClause{}
		phoneClause.Set("phone", "")
		mustNotClause.Add(phoneClause)
	}

	// accountIds
	accountIdsClause := &core_util.TermsClause{}
	accountIdsClause.Set("accountId", accountIds)
	mustClause.Add(accountIdsClause)

	// isDeleted
	deleteClause := &core_util.TermClause{}
	deleteClause.Set("isDeleted", false)
	mustClause.Add(deleteClause)

	// if we use SearchKeyWord, then the result shall be filtered
	filterScore := float64(0)
	if req.SearchKeyWord != "" && !explicitSearch {
		filterScore = elasticmodel.GetMinScore()
	}

	if req.ListCondition != nil && len(req.ListCondition.OrderBy) != 0 {
		orderBys = req.ListCondition.OrderBy
	}
	if filterScore > 0 && req.UseScroll {
		req.UseScroll = false
		var subErr error
		req.ListCondition, subErr = parseListConditionByScrollId(req.ScrollId, uint32(req.ScrollSize), orderBys)
		if subErr != nil {
			return nil, 0, 0, "", subErr
		}
		defer func() {
			if err != nil || len(ids) == 0 {
				return
			}
			req.ListCondition.Page += 1
			scrollId = base64.StdEncoding.EncodeToString([]byte(core_util.MarshalInterfaceToString(req.ListCondition)))
		}()
	}

	var from int
	from, size = util.ParseElasticPagingCondition(req.ListCondition)
	// we'll only use first social to sort members
	var firstSocial *origin.SocialSearchInfo
	if len(req.Socials) > 0 {
		firstSocial = req.Socials[0]
	}
	elasticSortFields := formatElasticsearchOrderBys(orderBys, firstSocial)

	if !req.UseScroll {
		ids, total, err = elasticmodel.EMember.SearchByPagination(ctx, query, elasticSortFields, from, size, filterScore)
	} else {
		scrollSize := elasticmodel.MEMBER_SCROLL_SIZE
		if req.ScrollSize > 0 {
			scrollSize = cast.ToInt(req.ScrollSize)
		}
		ids, scrollId, total, err = elasticmodel.EMember.ScrollBySearchAfter(ctx, query, elasticSortFields, req.ScrollId, scrollSize)
	}
	if err != nil {
		return nil, 0, 0, "", err
	}

	return ids, size, total, scrollId, nil
}

func genSearchKeywordClause(ctx context.Context, searchType string, keyword string, boolClause *core_util.BoolClause, memberPropertiesMap map[string][]model.MemberProperty) (*core_util.BoolClause, bool) {
	shouldClause := &core_util.ShouldClause{}
	boolClause.Set(shouldClause)

	if strings.Contains(keyword, ":") {
		properyInfo := strings.Split(keyword, ":")

		propertyClause := elasticmodel.GenPropertyClause(ctx, properyInfo[0], properyInfo[1], memberPropertiesMap[properyInfo[0]])
		if propertyClause != nil {
			shouldClause.Add(propertyClause)
		}
	}

	if searchType == "" {
		switch {
		case bson.IsObjectIdHex(keyword):
			searchType = SEARCH_TYPE_MEMBER_ID
		case rxPhone.FindString(keyword) != "", govalidator.IsEmail(keyword):
			searchType = SEARCH_TYPE_NAME_PHONE_EMAIL
		case len([]rune(keyword)) == 12:
			searchType = SEARCH_TYPE_CARD_NUMBER
		case len([]rune(keyword)) > 15:
			searchType = SEARCH_TYPE_OPEN_ID_UNION_ID
		}
	}

	// 是否明文查询（即非模糊查询）
	explicitSearch := false
	// 是否需要添加对 name 属性的查询
	needNameTerm := false
	switch searchType {
	case SEARCH_TYPE_MEMBER_ID:
		termClause := &core_util.TermClause{}
		termClause.Set("_id", keyword)
		termClause.SetBoost(100)
		shouldClause.Add(termClause)
		explicitSearch = true
	case SEARCH_TYPE_CARD_NUMBER:
		termClause := &core_util.TermClause{}
		termClause.Set("cardNumber", keyword)
		termClause.SetBoost(100)
		shouldClause.Add(termClause)
		explicitSearch = true
	case SEARCH_TYPE_OPEN_ID_UNION_ID:
		// empty 通过 db 查询 https://gitlab.maiscrm.com/mai/home/<USER>/issues/37960
	case SEARCH_TYPE_NAME_PHONE_EMAIL:
		// 此 case 只添加 phone 和 email 的查询，标记 name 查询条件由后续逻辑添加
		needNameTerm = true
		// 对于匹配手机号和邮箱格式的搜索值，就不添加 name 的模糊查询了
		switch {
		case rxPhone.FindString(keyword) != "":
			phoneTerm := elasticmodel.GenPropertyClause(ctx, model.DEFAULT_PROPERTY_PHONE, keyword, memberPropertiesMap[model.DEFAULT_PROPERTY_PHONE])
			shouldClause.Add(phoneTerm)
			explicitSearch = true
		case govalidator.IsEmail(keyword):
			phoneTerm := elasticmodel.GenPropertyClause(ctx, model.DEFAULT_PROPERTY_EMAIL, keyword, memberPropertiesMap[model.DEFAULT_PROPERTY_EMAIL])
			shouldClause.Add(phoneTerm)
			explicitSearch = true
		}
	case SEARCH_TYPE_CONTACT_OR_PHONE:
		if rxPhone.FindString(keyword) != "" {
			phoneTerm := elasticmodel.GenPropertyClause(ctx, model.DEFAULT_PROPERTY_PHONE, keyword, memberPropertiesMap[model.DEFAULT_PROPERTY_PHONE])
			shouldClause.Add(phoneTerm)
			explicitSearch = true
		}
		// 存在联系人名称也是是手机号
		contactTerm := elasticmodel.GenPropertyClause(ctx, model.DEFAULT_PROPERTY_CONTACT, keyword, memberPropertiesMap[model.DEFAULT_PROPERTY_CONTACT])
		shouldClause.Add(contactTerm)
		// 如果查询内容未匹配手机号，联系人名称还需要做模糊查询
		if !explicitSearch {
			contactClause := elasticmodel.GenFuzzyPropertyClause(model.DEFAULT_PROPERTY_CONTACT, keyword)
			shouldClause.Add(contactClause)
		}
	case SEARCH_TYPE_MEMBER_ID_NAME:
		needNameTerm = true
		if bson.IsObjectIdHex(keyword) {
			termClause := &core_util.TermClause{}
			termClause.Set("_id", keyword)
			termClause.SetBoost(100)
			shouldClause.Add(termClause)
			// 对于 objectId 格式的搜索值，查询 name 时不进行模糊搜索
			explicitSearch = true
		}
	case SEARCH_TYPE_NAME_CONTACT:
		explicitSearch = false
		// 如果是手机号格式则只查手机号
		if rxPhone.FindString(keyword) != "" {
			// 存在联系人名称也是是手机号
			contactTerm := elasticmodel.GenPropertyClause(ctx, model.DEFAULT_PROPERTY_CONTACT, keyword, memberPropertiesMap[model.DEFAULT_PROPERTY_CONTACT])
			shouldClause.Add(contactTerm)
			break
		}
		// 匹配名称
		needNameTerm = true
		// 联系人名称是普通字符串的情况
		contactClause := elasticmodel.GenFuzzyPropertyClause(model.DEFAULT_PROPERTY_CONTACT, keyword)
		shouldClause.Add(contactClause)
	case SEARCH_TYPE_NAME_CONTACT_ADDRESS:
		// 省市区匹配中如果将此值设置为 false，则会有 minScore 的限制
		// 由于省市区匹配时条件很多会导致分数降低，因此将此值设置为 true
		explicitSearch = true
		// 如果是手机号格式则只查手机号
		if rxPhone.FindString(keyword) != "" {
			// 存在联系人名称也是是手机号
			contactTerm := elasticmodel.GenPropertyClause(ctx, model.DEFAULT_PROPERTY_CONTACT, keyword, memberPropertiesMap[model.DEFAULT_PROPERTY_CONTACT])
			shouldClause.Add(contactTerm)
			break
		}
		// 匹配名称
		needNameTerm = true
		// 联系人名称是普通字符串的情况
		contactClause := elasticmodel.GenFuzzyPropertyClause(model.DEFAULT_PROPERTY_CONTACT, keyword)
		shouldClause.Add(contactClause)
		location := &types.Location{
			Country:  keyword,
			Province: keyword,
			City:     keyword,
			District: keyword,
			Detail:   keyword,
		}
		shouldClause.Add(elasticmodel.GenAddressPropertyClause("address", location, true))
	case SEARCH_TYPE_ACTIVATION_STAFF:
		needNameTerm = false
		explicitSearch = true
		if bson.IsObjectIdHex(keyword) {
			term := elasticmodel.GenPropertyClause(ctx, "注册导购", keyword, memberPropertiesMap["注册导购"])
			shouldClause.Add(term)
		} else {
			resp, _ := client.GetEcStoreServiceClient().ListBriefStaffs(ctx, &pb_ec_store.ListStaffsRequest{
				SearchKey: keyword,
				ListCondition: &request.ListCondition{
					Page:    1,
					PerPage: 10,
				},
			})
			staffIds := []string{bson.NewObjectId().Hex()}
			if resp != nil && len(resp.Items) > 0 {
				for _, item := range resp.Items {
					staffIds = append(staffIds, item.Id)
				}
			}
			term := elasticmodel.GenPropertyClause(ctx, "注册导购", staffIds, memberPropertiesMap["注册导购"])
			shouldClause.Add(term)
		}
	default:
		// 没有 searchType 时默认查询 name
		needNameTerm = true
		// name 属性应该是模糊查询
		explicitSearch = false
	}

	// if the searchkey is objectId or a phone number, then we'll avoid analyze
	// the searchkey and match it in elasticsearch
	if needNameTerm {
		if !explicitSearch {
			// 加密属性不支持模糊查询
			nameClause := elasticmodel.GenFuzzyPropertyClause(model.DEFAULT_PROPERTY_NAME, keyword)
			shouldClause.Add(nameClause)
		}

		// add a term for name, so the explicit matched data will have highest _score
		nameTerm := elasticmodel.GenPropertyClause(ctx, model.DEFAULT_PROPERTY_NAME, keyword, memberPropertiesMap[model.DEFAULT_PROPERTY_NAME])
		shouldClause.Add(nameTerm)
	}

	return boolClause, explicitSearch
}

func formatSearchMemberResponse(ctx context.Context, dbMembers []model.Member, total uint64, requiredCardName bool, propertyFilter *pb.MemberPropertyFilter) *pb.MemberListResponse {
	if total == 0 {
		return &pb.MemberListResponse{
			Members:    []*pb.MemberDetailResponse{},
			TotalCount: 0,
		}
	}

	var members []*pb.MemberDetailResponse

	stages, _ := model.CMemberStage.GetStages(ctx)

	idCardMap := map[bson.ObjectId]model.MembershipCard{}
	if requiredCardName {
		cards := getCardsForMembers(ctx, dbMembers)
		for _, card := range cards {
			idCardMap[card.Id] = card
		}
	}

	levels, _ := model.CMemberLevel.GetAllEnalbedLevel(ctx)
	levelMapper := core_util.MakeMapper("Level", levels)

	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	if propertyFilter != nil {
		if propertyFilter.IsVisible != nil {
			condition["isVisible"] = propertyFilter.IsVisible.Value
		}
		if propertyFilter.IsVisibleInFilter != nil {
			condition["isVisibleInFilter"] = propertyFilter.IsVisibleInFilter.Value
		}
		if propertyFilter.IsSystem != nil {
			condition["isSystem"] = propertyFilter.IsSystem.Value
		}
		if propertyFilter.ShowSystemProperties {
			delete(condition, "isSystem")
		}
	}
	properties := model.CMemberProperty.GetAllByCondition(ctx, condition)
	for _, item := range dbMembers {
		var level uint64
		var levelName string
		var memberLevel *model.MemberLevel
		if lv, ok := levelMapper[uint64(item.Level)].(model.MemberLevel); ok {
			level = uint64(item.Level)
			levelName = lv.Name
			memberLevel = &lv
		} else if item.IsActivated {
			level, levelName, memberLevel = item.GetLevelByLevels(levels)
		}

		member := formatBasicMemberResponse(&item, properties, stages, uint32(level), levelName, memberLevel)

		member.SocialMember = getSocialMember(ctx, &item)
		if requiredCardName {
			if card, ok := idCardMap[item.CardId]; ok {
				member.Card = formatMembershipCardDetail(&card)
			}
		}

		members = append(members, member)
	}

	return &pb.MemberListResponse{
		Members:    members,
		TotalCount: total,
	}
}

func parseListConditionByScrollId(scrollId string, scrollSize uint32, orderBy []string) (*request.ListCondition, error) {
	listCondition := &request.ListCondition{}
	if scrollId != "" {
		scrollIdBytes, subErr := base64.StdEncoding.DecodeString(scrollId)
		if subErr != nil {
			return nil, errors.NewInvalidArgumentErrorWithMessage("scrollId", subErr.Error())
		}
		subErr = json.Unmarshal(scrollIdBytes, listCondition)
		if subErr != nil {
			return nil, errors.NewInvalidArgumentErrorWithMessage("scrollId", subErr.Error())
		}
	} else {
		listCondition = &request.ListCondition{
			Page:    1,
			PerPage: elasticmodel.MEMBER_SCROLL_SIZE,
			OrderBy: orderBy,
		}
		if scrollSize > 0 {
			listCondition.PerPage = scrollSize
		}
	}
	return listCondition, nil
}

func getCardsForMembers(ctx context.Context, dbMembers []model.Member) []model.MembershipCard {
	if len(dbMembers) == 0 {
		return []model.MembershipCard{}
	}

	cardIds := []string{}
	accountIds := []string{
		util.GetAccountId(ctx),
	}
	for _, dbMember := range dbMembers {
		if !dbMember.CardId.Valid() {
			continue
		}
		if !util.StrInArray(dbMember.CardId.Hex(), &cardIds) {
			cardIds = append(cardIds, dbMember.CardId.Hex())
		}
		if !util.StrInArray(dbMember.AccountId.Hex(), &accountIds) {
			accountIds = append(accountIds, dbMember.AccountId.Hex())
		}
	}

	cards, _ := model.CMembershipCard.GetByIds(ctx, util.ToMongoIds(cardIds), util.ToMongoIds(accountIds))
	return cards
}

func reorderSearchMemberResponse(pbMembers *pb.MemberListResponse, ids []string) *pb.MemberListResponse {
	result := &pb.MemberListResponse{
		TotalCount: pbMembers.TotalCount,
		Members:    []*pb.MemberDetailResponse{},
	}

	for _, id := range ids {
		for _, pbMember := range pbMembers.Members {
			if pbMember.Id == id {
				result.Members = append(result.Members, pbMember)
			}
		}
	}

	return result
}

// formatElasticsearchOrderBys will format social order field.
func formatElasticsearchOrderBys(orderBys []string, social *origin.SocialSearchInfo) []string {
	result := []string{}
	orderBys = util.NormalizeOrderBy(orderBys)

	for _, orderBy := range orderBys {
		if strings.Contains(orderBy, socialsOrderByPrefix) {
			if social == nil {
				result = append(result, orderBy)
				continue
			}

			if social.Channel == nil {
				result = append(result, orderBy)
				continue
			}

			if social.Origin == nil {
				result = append(result, orderBy)
				continue
			}

			newOrderBy := strings.Replace(orderBy, socialsOrderByPrefix, fmt.Sprintf(
				elasticmodel.SOCIAL_ORDERBY,
				social.Origin.Value,
				social.Channel.Value,
			), -1)
			result = append(result, newOrderBy)
			continue
		}

		if strings.Contains(orderBy, propertyOrderByPrefix) {
			orderByParts := strings.Split(orderBy, ".")
			propertyName := orderByParts[len(orderByParts)-1]
			newOrderBy := orderBy + "." + elasticmodel.GetPropertyValueType(propertyName)

			result = append(result, newOrderBy)
			continue
		}

		result = append(result, orderBy)
	}

	return result
}

func validateSearchRequest(req *pb.SearchMemberRequest) error {
	// check if socials and orderBys are valid
	err := validateSearchRequestOrderBys(req)
	if err != nil {
		return err
	}

	// check if all socials contains origin and channelId
	err = validateSearchRequestOriginAndChannelId(req)
	if err != nil {
		return err
	}

	return nil
}

func validateSearchRequestOrderBys(req *pb.SearchMemberRequest) error {
	if req.ListCondition == nil || len(req.ListCondition.OrderBy) == 0 {
		return nil
	}

	containsSocialField := false
	for _, orderBy := range req.ListCondition.OrderBy {
		if strings.Contains(orderBy, socialsOrderByPrefix) {
			containsSocialField = true
		}
	}
	if !containsSocialField {
		return nil
	}

	if len(req.Socials) == 0 {
		return codes.NewError(codes.MustContainsSocialInfo)
	}

	if req.Socials[0].Channel == nil || req.Socials[0].Origin == nil {
		return codes.NewError(codes.MustContainsSocialInfo)
	}

	return nil
}

func validateSearchRequestOriginAndChannelId(req *pb.SearchMemberRequest) error {
	if len(req.Socials) == 0 {
		return nil
	}

	socials := []*pb_origin.SocialSearchInfo{}
	socials = append(socials, req.Socials...)
	socials = append(socials, req.ActivationSocials...)

	for _, social := range socials {
		if social.Channel == nil || social.Origin == nil {
			return codes.NewError(codes.MustContainsSocialInfo)
		}
	}

	return nil
}

func formatSocialQuery(socialInfo *origin.SocialSearchInfo, shouldClause *core_util.ShouldClause) {
	innerBClause := &core_util.BoolClause{}
	mClause := &core_util.MustClause{}
	innerBClause.Set(mClause)
	shouldClause.Add(innerBClause)

	// we'll try to get social's origin and channel, since
	// they can speed up elastic query
	channel := ""
	if socialInfo.Channel != nil {
		channel = socialInfo.Channel.Value
	}
	origin := ""
	if socialInfo.Origin != nil {
		origin = socialInfo.Origin.Value
	}

	if socialInfo.Channel != nil {
		clause := &core_util.TermClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "channel")
		clause.Set(key, socialInfo.Channel.Value)
		mClause.Add(clause)
	}

	if socialInfo.Origin != nil {
		clause := &core_util.TermClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "origin")
		clause.Set(key, socialInfo.Origin.Value)
		mClause.Add(clause)
	}

	if socialInfo.Subscribed != nil {
		clause := &core_util.TermClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "subscribed")
		clause.Set(key, socialInfo.Subscribed.Value)
		mClause.Add(clause)
	}

	if socialInfo.SubscribeTime != nil {
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "subscribeTime")
		clause := util.ParseElasticStringDateRange(key, socialInfo.SubscribeTime)
		mClause.Add(clause)
	}

	if socialInfo.UnsubscribeTime != nil {
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "unsubscribeTime")
		clause := util.ParseElasticStringDateRange(key, socialInfo.UnsubscribeTime)
		mClause.Add(clause)
	}

	if socialInfo.FirstSubscribeTime != nil {
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "firstSubscribeTime")
		clause := util.ParseElasticStringDateRange(key, socialInfo.FirstSubscribeTime)
		mClause.Add(clause)
	}

	if socialInfo.AuthorizeTime != nil {
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "authorizeTime")
		clause := util.ParseElasticStringDateRange(key, socialInfo.AuthorizeTime)
		mClause.Add(clause)
	}

	if socialInfo.Nickname != nil {
		clause := &core_util.MatchClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "nickname")
		clause.Set(key, socialInfo.Nickname.Value)
		clause.SwitchFuzzy(true)
		mClause.Add(clause)
	}

	if socialInfo.Gender != nil {
		clause := &core_util.TermClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "gender")
		clause.Set(key, socialInfo.Gender.Value)
		mClause.Add(clause)
	}

	if socialInfo.City != nil {
		clause := &core_util.TermClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "city")
		clause.Set(key, socialInfo.City.Value)
		mClause.Add(clause)
	}

	if socialInfo.Province != nil {
		clause := &core_util.TermClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "province")
		clause.Set(key, socialInfo.Province.Value)
		mClause.Add(clause)
	}

	if socialInfo.Country != nil {
		clause := &core_util.TermClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "country")
		clause.Set(key, socialInfo.Country.Value)
		mClause.Add(clause)
	}

	if socialInfo.UnionId != nil {
		clause := &core_util.TermClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "unionId")
		clause.Set(key, socialInfo.UnionId.Value)
		mClause.Add(clause)
	}

	if socialInfo.OpenIds != nil {
		clause := &core_util.TermsClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "openId")
		clause.Set(key, socialInfo.OpenIds.Value)
		mClause.Add(clause)
	}

	if socialInfo.Authorized != nil {
		clause := &core_util.TermClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "authorized")
		clause.Set(key, socialInfo.Authorized.Value)
		mClause.Add(clause)
	}

	if socialInfo.Extra != nil {
		switch socialInfo.Extra.Type {
		case pb_origin.SocialExtraSearchInfo_EXISTS:
			existsClause := &core_util.ExistsClause{}
			key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "extra")
			existsClause.Set(key, socialInfo.Extra.Exists)
			mClause.Add(existsClause)
		case pb_origin.SocialExtraSearchInfo_EQUAL:
			clause := &core_util.TermClause{}
			key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "extra")
			clause.Set(key, socialInfo.Extra.Value)
			mClause.Add(clause)
		}
	}

	// https://gitlab.maiscrm.com/mai/home/<USER>/13930
	// special case for "taobao:member" origin
	if origin == constant.TAOBAO_MEMBER || origin == constant.JD_MEMBER {
		clause := &core_util.RangeClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "subscribeTime")
		clause.SetKey(key)
		clause.SetGTE(0) // date gte 1970-01-01T00:00:00.000Z
		mClause.Add(clause)
	}

	// if origin is JD, the openId can not be empty
	if origin == constant.JD {
		originClause := &core_util.BoolClause{}
		mustNotClause := &core_util.MustNotClause{}
		openIdClause := &core_util.TermClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "openId")
		openIdClause.Set(key, "")
		mustNotClause.Add(openIdClause)
		originClause.Set(mustNotClause)
		mClause.Add(originClause)
	}

	if socialInfo.IsOriginal != nil {
		clause := &core_util.TermClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "isOriginal")
		clause.Set(key, socialInfo.IsOriginal.Value)
		mClause.Add(clause)
	}

	if socialInfo.IsActivationChannel != nil {
		clause := &core_util.TermClause{}
		key := fmt.Sprintf(elasticmodel.SOCIAL_QUERY_FORMAT, origin, channel, "isActivationChannel")
		clause.Set(key, socialInfo.IsActivationChannel.Value)
		mClause.Add(clause)
	}
}

func formatTagsSelector(tagsSelector *pb.TagsSelector) *core_util.BoolClause {
	tagsClause := &core_util.BoolClause{}
	tagsSClause := &core_util.ShouldClause{}
	tagsMClause := &core_util.MustClause{}
	if tagsSelector.IsOrCondition {
		tagsClause.Set(tagsSClause)
	} else {
		tagsClause.Set(tagsMClause)
	}

	for _, cond := range tagsSelector.TagConditions {
		if cond.IsOrCondition {
			termsClause := &core_util.TermsClause{}
			termsClause.Set("tags", cond.Tags)
			if tagsSelector.IsOrCondition {
				tagsSClause.Add(termsClause)
			} else {
				tagsMClause.Add(termsClause)
			}
		} else {
			bClause := &core_util.BoolClause{}
			mClause := &core_util.MustClause{}
			bClause.Set(mClause)
			for _, tag := range cond.Tags {
				termClause := &core_util.TermClause{}
				termClause.Set("tags", tag)
				mClause.Add(termClause)
			}
			if tagsSelector.IsOrCondition {
				tagsSClause.Add(bClause)
			} else {
				tagsMClause.Add(bClause)
			}
		}
	}

	return tagsClause
}

// 将 origins 中的元素转化成 channelPair，使用 channelPair 穷举 socials 条件并生成 ElasticClause
func formatSocialClausesByOrigins(ctx context.Context, origins []string, field string, values []string) []core_util.ElasticClause {
	channelPairs := getChannelIdAndOriginPairsByRequest(ctx, &account.ChannelListRequest{
		Origins: origins,
		Status:  []string{"enable", "disable"},
	})
	var result []core_util.ElasticClause
	if len(channelPairs) == 0 {
		clause := elasticmodel.GenSocialsClause(0, field, "", "", values...)
		result = append(result, clause)
	} else {
		for _, pair := range channelPairs {
			clause := elasticmodel.GenSocialsClause(0, field, pair.ChannelId, pair.Origin, values...)
			result = append(result, clause)
		}
	}
	return result
}

// 将 channelIds 中的元素转化成 channelPair，使用 channelPair 穷举 socials 条件并生成 ElasticClause
func formatSocialClausesByChannelIds(ctx context.Context, channelIds []string, field string, values []string) []core_util.ElasticClause {
	channelPairs := getChannelIdAndOriginPairsByRequest(ctx, &account.ChannelListRequest{
		ChannelIds: channelIds,
		Status:     []string{"enable", "disable"},
	})
	var result []core_util.ElasticClause
	if len(channelPairs) == 0 {
		clause := elasticmodel.GenSocialsClause(0, field, "", "", values...)
		result = append(result, clause)
	} else {
		for _, pair := range channelPairs {
			clause := elasticmodel.GenSocialsClause(0, field, pair.ChannelId, pair.Origin, values...)
			result = append(result, clause)
		}
	}
	return result
}

func getMemberPropertiesMapByName(ctx context.Context, accountIds []bson.ObjectId) map[string][]model.MemberProperty {
	properties := model.CMemberProperty.GetAllByCondition(ctx, bson.M{
		"accountId": bson.M{
			"$in": accountIds,
		},
	})
	result := map[string][]model.MemberProperty{}
	for _, property := range properties {
		result[property.Name] = append(result[property.Name], property)
	}
	return result
}

func needSwitchScore(req *pb.SearchMemberRequest) bool {
	return req.SearchKeyWord != "" || isSearchSocialNickname(req.Socials)
}

func isSearchSocialNickname(socials []*origin.SocialSearchInfo) bool {
	for _, s := range socials {
		if s.Nickname != nil {
			return true
		}
	}
	return false
}

func buildRuleTagsClause(ruleTags []string) core_util.BoolClause {
	ruleTagGroups := make(map[string][]string)
	for _, ruleTag := range ruleTags {
		parts := strings.Split(ruleTag, "-")
		if len(parts) < 2 {
			ruleTagGroups[ruleTag] = []string{ruleTag}
			continue
		}
		ruleTagGroups[parts[0]] = append(ruleTagGroups[parts[0]], ruleTag)
	}
	bClause := core_util.BoolClause{}
	mClause := core_util.MustClause{}
	for _, tags := range ruleTagGroups {
		ruleClause := &core_util.HasChildClause{}
		ruleClause.Set("ruleLabelDetail", tags)
		mClause.Add(ruleClause)
	}
	bClause.Set(mClause)
	return bClause
}
