package service

import (
	"mairpc/core/errors"
	"mairpc/core/log"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	pb_member "mairpc/proto/member"
	"mairpc/service/member/model"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

// 目前仅用于更新 jd:member 渠道 unionId
func (MemberService) UpdateMemberSocialUniqueId(ctx context.Context, req *pb_member.UpdateMemberSocialUniqueIdRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	member := model.CMember.GetById(ctx, util.GetAccountId(ctx), req.MemberId)
	if member == nil {
		return nil, errors.NewNotExistsError("memberId")
	}
	for _, social := range member.Socials {
		if social.Channel == req.ChannelId {
			social.UnionId = req.NewUniqueId
			if err := member.UpdateSpecifiedWholeSocial(ctx, social); err != nil {
				return nil, err
			}
			log.Warn(ctx, "The member's unique ID has been updated", log.Fields{
				"req":         req,
				"oldUniqueId": social.UnionId,
			})
			return &response.EmptyResponse{}, nil
		}
	}
	return nil, errors.NewInvalidArgumentError("channelId")
}
