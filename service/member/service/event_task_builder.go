package service

import (
	"encoding/json"
	"fmt"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/proto/common/types"
	proto_member "mairpc/proto/member"
	"mairpc/service/member/codes"
	member_model "mairpc/service/member/model"
	"mairpc/service/share/component"
	"mairpc/service/share/model"
	"mairpc/service/share/util"
	"time"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

const (
	EVENT_TASK_VERSION_KEY = "EVENT_TASK_VERSION"

	FROM_RECORD   = "record"
	FROM_ROLLBACK = "rollback"
)

var (
	EVENT_TASK_V2_RELEASE_TIME = time.Date(2023, 11, 2, 0, 0, 0, 0, time.Local)
)

type EventTaskBuilder struct {
	Tasks           []*member_model.MemberTask
	TasksMapper     map[string]*member_model.MemberTask
	TasksInfoMapper map[string]*member_model.TaskInfoV2
	Member          *member_model.Member
	ScoreSetting    map[string]*member_model.MemberScoreSetting
	eventProperties map[string]any
	event           *types.Event
	LevelSetting    *member_model.MemberLevelSetting
	ScoreResetRule  *member_model.ScoreResetRule
	LevelMapper     map[uint64]*member_model.MemberLevel
	ChannelId       string

	LogsMapper map[string]*member_model.MemberTaskLog

	taskRecords map[string]*member_model.MemberTaskRecord
	taskRewards map[string]*member_model.MemberTaskReward
}

func GenerateEventTaskBuilder(ctx context.Context, req *proto_member.EventTriggerRequest) *EventTaskBuilder {
	eventProperties := map[string]interface{}{}
	json.Unmarshal([]byte(req.Event.Properties), &eventProperties)

	return &EventTaskBuilder{
		ScoreSetting:    make(map[string]*member_model.MemberScoreSetting),
		eventProperties: eventProperties,
		event:           req.Event,
		ChannelId:       req.Event.ChannelId,
		LogsMapper:      map[string]*member_model.MemberTaskLog{},
		TasksMapper:     map[string]*member_model.MemberTask{},
		TasksInfoMapper: map[string]*member_model.TaskInfoV2{},
		LevelMapper:     map[uint64]*member_model.MemberLevel{},
	}
}

func (e *EventTaskBuilder) GetLogIdentifier(code string) string {
	return fmt.Sprintf("%s_%s", e.event.MsgId, code)
}

func (e *EventTaskBuilder) GetEventOccurredAt() time.Time {
	occurredAt, _ := util.TransStrToTime(e.event.OccourredAt)
	if occurredAt.IsZero() {
		occurredAt = time.Now()
	}
	return occurredAt
}

func (e *EventTaskBuilder) GetTaskLog(ctx context.Context, code string) *member_model.MemberTaskLog {
	if e.LogsMapper == nil {
		e.LogsMapper = make(map[string]*member_model.MemberTaskLog)
	}
	if e.LogsMapper[e.GetLogIdentifier(code)] == nil {
		e.LogsMapper[e.GetLogIdentifier(code)] = &member_model.MemberTaskLog{
			Member: member_model.CMemberTaskLog.GenMember(e.GetRelatedMember(ctx).Id, e.GetRelatedMember(ctx).Level),
			Task:   member_model.CMemberTaskLog.GenTask(ctx, e.GetTask(ctx, code)),
			Event:  member_model.CMemberTaskLog.GenEvent(e.event.MsgId, e.event.EventId, e.GetEventProperties()),
			ScoreSetting: member_model.MemberTaskLogScoreSetting{
				NoScoreRewardSetting: member_model.CMemberTaskLog.GenNoScoreRewardSetting(e.GetScoreSetting(ctx, member_model.C_SCORE_SETTING_CODE_NO_SCORE_REWARD)),
			},
		}
	}
	return e.LogsMapper[e.GetLogIdentifier(code)]
}

func (e *EventTaskBuilder) GetEventProperties() map[string]any {
	return e.eventProperties
}

func (e *EventTaskBuilder) GetEventId() string {
	return e.event.EventId
}

func (e *EventTaskBuilder) getMemberId() string {
	return e.event.MemberId
}

func (e *EventTaskBuilder) GetRelatedTasks(ctx context.Context) []*member_model.MemberTask {
	if e.Tasks != nil {
		return e.Tasks
	} else {
		e.Tasks, _ = member_model.CMemberTask.GetByEventId(ctx, e.GetEventId())
		for i := range e.Tasks {
			e.TasksMapper[e.Tasks[i].Code] = e.Tasks[i]
		}
	}
	return e.Tasks
}

func (e *EventTaskBuilder) SetRelatedTasks(tasks []*member_model.MemberTask) {
	e.Tasks = tasks
}

func (e *EventTaskBuilder) getRelatedTasksDirectly(ctx context.Context, isRollback bool) []*member_model.MemberTask {
	if isRollback {
		tasks, _ := member_model.CMemberTask.GetRollbackByEventId(ctx, e.GetEventId())
		return tasks
	}
	tasks, _ := member_model.CMemberTask.GetByEventId(ctx, e.GetEventId())
	return tasks
}

func (e *EventTaskBuilder) GetRelatedMember(ctx context.Context) *member_model.Member {
	if e.Member != nil {
		return e.Member
	} else {
		e.Member = member_model.CMember.GetById(ctx, util.GetAccountId(ctx), e.getMemberId())
	}
	return e.Member
}

func (e *EventTaskBuilder) RefreshAMember(ctx context.Context) *member_model.Member {
	e.Member = nil
	return e.GetRelatedMember(ctx)
}

func (e *EventTaskBuilder) GetScoreSetting(ctx context.Context, code string) *member_model.MemberScoreSetting {
	if e.ScoreSetting == nil {
		e.ScoreSetting = make(map[string]*member_model.MemberScoreSetting)
	}

	if setting, exists := e.ScoreSetting[code]; exists {
		return setting
	} else {
		value, err := member_model.CMemberScoreSetting.GetByCode(ctx, code)
		if err != nil {
			e.ScoreSetting[code] = nil
		} else {
			e.ScoreSetting[code] = &value
		}
	}
	return e.ScoreSetting[code]
}

func (e *EventTaskBuilder) GetTask(ctx context.Context, code string) *member_model.MemberTask {
	if e.TasksMapper == nil {
		e.TasksMapper = make(map[string]*member_model.MemberTask)
	}

	if task, ok := e.TasksMapper[code]; ok {
		return task
	}
	tasks := e.GetRelatedTasks(ctx)
	for i := range tasks {
		if tasks[i].Code == code {
			return tasks[i]
		}
	}

	return nil
}

func (e *EventTaskBuilder) GetMemberLevel(ctx context.Context, level uint64) *member_model.MemberLevel {
	if e.LevelMapper == nil {
		e.LevelMapper = make(map[uint64]*member_model.MemberLevel)
	}

	if l, ok := e.LevelMapper[level]; ok {
		return l
	}

	memberLevel, err := member_model.CMemberLevel.GetByLevel(ctx, level)
	if err != nil {
		return nil
	}
	e.LevelMapper[level] = &memberLevel

	return e.LevelMapper[level]
}

func (e *EventTaskBuilder) GetScoreResetRule(ctx context.Context) *member_model.ScoreResetRule {
	if e.ScoreResetRule == nil {
		e.ScoreResetRule = member_model.CScoreResetRule.GetByAccount(ctx, util.GetAccountIdAsObjectId(ctx))
	}

	return e.ScoreResetRule
}

func (e *EventTaskBuilder) GetLevelSetting(ctx context.Context) *member_model.MemberLevelSetting {
	if e.LevelSetting == nil {
		levelSetting, err := member_model.CMemberLevelSetting.GetMemberLevelSetting(ctx)
		if err != nil {
			log.Error(ctx, "can't find level setting", log.Fields{})
		}
		e.LevelSetting = levelSetting
	}

	return e.LevelSetting
}

func (e *EventTaskBuilder) GetTaskItemTriggerIdentifier(ctx context.Context, code string) string {
	if e.event == nil {
		return ""
	}

	task := e.GetTask(ctx, code)
	if task == nil {
		return e.event.MsgId
	}

	trigger := e.GetTaskItemTrigger(ctx, code)
	if trigger == nil || trigger.IdentifierField == "" {
		return e.event.MsgId
	}

	i, ok := e.eventProperties[trigger.IdentifierField]
	if ok {
		return cast.ToString(i)
	}

	return ""
}

func (e *EventTaskBuilder) IsTrigger(ctx context.Context, code string) bool {
	task := e.GetTask(ctx, code)
	if task == nil {
		return false
	}
	_, isTrigger := task.GetTrigger(task.EventTrigger, e.GetEventId())
	return isTrigger
}

func (e *EventTaskBuilder) IsRollback(ctx context.Context, code string) bool {
	task := e.GetTask(ctx, code)
	if task == nil {
		return false
	}
	_, isRollback := task.GetTrigger(task.EventRollbacker, e.GetEventId())
	return isRollback
}

func (e *EventTaskBuilder) GetTaskItemTrigger(ctx context.Context, code string) *member_model.TaskEventTrigger {
	task := e.GetTask(ctx, code)
	if task == nil {
		return nil
	}
	trigger, isTrigger := task.GetTrigger(task.EventTrigger, e.GetEventId())
	if isTrigger {
		return &trigger
	}
	trigger, isRollback := task.GetTrigger(task.EventRollbacker, e.GetEventId())
	if isRollback {
		return &trigger
	}
	return nil
}

func (e *EventTaskBuilder) GetTaskItemInfo(ctx context.Context, code string) *member_model.TaskInfoV2 {
	task := e.GetTask(ctx, code)

	if task == nil {
		return nil
	}

	info, exists := e.TasksInfoMapper[code]
	if exists {
		return info
	}

	trigger := e.GetTaskItemTrigger(ctx, code)

	info = &member_model.TaskInfoV2{
		Amount:               cast.ToUint64(e.eventProperties[trigger.AmountField]),
		WithoutPreventAmount: cast.ToUint64(e.eventProperties[trigger.AmountField]),
	}

	// 单笔限制处理
	if limitSetting := e.GetScoreSetting(ctx, member_model.C_SCORE_SETTING_CODE_LIMIT); limitSetting != nil && limitSetting.ReceiveLimit.SingleLimit > 0 {
		existsScore, _ := member_model.CMemberTaskReward.GetDefaultRewardBasicScoreByIdentifier(ctx, e.GetTaskItemTriggerIdentifier(ctx, code), bson.ObjectIdHex(e.getMemberId()))
		info.MaxRewardScore = limitSetting.ReceiveLimit.SingleLimit - existsScore

		// todo MaxRewardScore RemainRewardScore 有啥区别
		if info.MaxRewardScore <= 0 {
			info.MaxRewardScore = 0
		}
		info.RemainRewardScore = info.MaxRewardScore
	}

	// 移除屏蔽订单和商品
	if noRewardSetting := e.GetScoreSetting(ctx, member_model.C_SCORE_SETTING_CODE_NO_SCORE_REWARD); noRewardSetting != nil && info.WithoutPreventAmount > 0 {
		if !noRewardSetting.NoScoreReward.NeedCheckProductNoReward(code, task.Business) {
			goto GenInfoV2Finish
		}

		amountArray, ok := e.GetEventProperties()[noRewardSetting.NoScoreReward.ProductSetting.AmountArrayField].([]interface{})
		if !ok {
			log.Warn(ctx, "Invalid amount array.", log.Fields{"properties": e.GetEventProperties()})
			goto GenInfoV2Finish
		}
		for _, item := range amountArray {
			arrayItem := new(map[string]interface{})
			err := core_util.CopyByJson(item, &arrayItem)
			if err != nil {
				log.Error(ctx, "format amount array failed.", log.Fields{"arrayItem": arrayItem})
				break
			}

			var (
				valueAmount              = cast.ToUint64((*arrayItem)[noRewardSetting.NoScoreReward.ProductSetting.AmountField])
				noRewardValue            = cast.ToString((*arrayItem)[noRewardSetting.NoScoreReward.ProductSetting.AmountValueField])
				noRewardPlatform         = cast.ToString((*arrayItem)["platform"])
				price                    = cast.ToInt64((*arrayItem)["price"])
				payAmount                = cast.ToInt64((*arrayItem)["payAmount"])
				payRatio         float64 = -1
			)

			// 回滚的元素跳过统计
			if e.GetEventId() == component.MAIEVENT_ORDER_COMPLETED && noRewardSetting.NoScoreReward.IsProductRefund(cast.ToString((*arrayItem)["refundStatus"])) {
				continue
			}

			if noRewardPlatform == "" {
				noRewardPlatform = cast.ToString(e.GetEventProperties()["platform"])
			}
			if price > 0 {
				payRatio = util.DivideFloat(float64(payAmount), float64(price))
			}
			// 开启积分屏蔽、设置了屏蔽商品、默认积分规则需要剔除屏蔽商品
			if noRewardSetting.NoScoreReward.ShouldNoRewardByProduct(noRewardValue, noRewardPlatform, code, task.Business, payRatio) {
				info.WithoutPreventAmount -= valueAmount
				continue
			}
		}
	}

GenInfoV2Finish:
	e.TasksInfoMapper[code] = info
	return info
}

func (e *EventTaskBuilder) PreValidBeforeTrigger(ctx context.Context, isRollback bool) (valid bool, err error) {
	var falseReason string
	defer func() {
		if !valid || err != nil {
			key := "ValidBeforeTrigger"
			if isRollback {
				key = "ValidBeforeTriggerInRollback"
			}
			writeHeader(ctx, key, falseReason)
		}
	}()
	// 没搜到事件对应任务就什么都不做，减少资源消耗
	if tasks := e.getRelatedTasksDirectly(ctx, isRollback); len(tasks) == 0 {
		falseReason = "no tasks"
		return false, nil
	} else {
		e.SetRelatedTasks(tasks)
	}

	if e.GetRelatedMember(ctx) == nil {
		falseReason = "can't find member"
		return false, codes.NewError(codes.MemberNotFound)
	}

	// 非会员无法完成会员任务 home#25733
	if !e.GetRelatedMember(ctx).CardId.Valid() || !e.GetRelatedMember(ctx).IsActivated || e.GetRelatedMember(ctx).IsDisabled || e.GetRelatedMember(ctx).BlockedStatus == 2 {
		falseReason = "not activated or disabled or blocked"
		return false, nil
	}

	// 被禁用的客户不发任务奖励
	if e.GetRelatedMember(ctx).IsDisabled {
		falseReason = "member disabled"
		return false, nil
	}

	return true, nil
}

func (e *EventTaskBuilder) ValidBeforeRecord(ctx context.Context, code string) (bool, error) {
	task := e.GetTask(ctx, code)

	if !task.IsEnabled || !task.MatchTaskCycle() {
		switch {
		case !task.IsEnabled:
			e.GetTaskLog(ctx, code).AppendResult(member_model.ResultCodeTaskNotEnabled)
		case !task.MatchTaskCycle():
			e.GetTaskLog(ctx, code).AppendResult(member_model.ResultCodeTaskNotMatchTaskCycle)
		}

		return false, errors.NewNotEnabledError("task")
	}

	// 会员任务未开始
	if !task.InValidPerid() {
		e.GetTaskLog(ctx, task.Code).AppendResult(member_model.ResultCodeTaskNotStart)
		return false, nil
	}

	// TODO defer writeHeader() valid failed reason
	return true, nil
}

func (e *EventTaskBuilder) ValidWhenRecording(ctx context.Context, code string) (bool, error) {
	task := e.GetTask(ctx, code)
	// 验证任务是否还有奖励
	if !task.CheckTaskLimitCountEnough(ctx) {
		e.GetTaskLog(ctx, task.Code).AppendResult(member_model.ResultCodeOutOfTaskLimit)
		log.Warn(ctx, "task is out of reward", log.Fields{
			"businessId": e.GetTaskItemTriggerIdentifier(ctx, code),
			"memberId":   e.getMemberId(),
		})
		return false, nil
	}

	// 检查客户的任务完成次数，如果已经达到个人限流上限，那么不再记录进度
	// 判断是否到限流上限了。
	if e.personalLimited(ctx, code) {
		return false, errors.NewInvalidArgumentErrorWithMessage("memberId", fmt.Sprintf("task [%v] exceed personal limit", code))
	}

	// 如果没找到，说明这个会员第一次完成当前任务，这属于正常业务所以继续执行后面的逻辑
	if e.getTaskRecord(ctx, code).Valid() && e.getTaskRecord(ctx, code).ContainsIdentifier(ctx, e.GetTaskItemTriggerIdentifier(ctx, code)) {
		e.GetTaskLog(ctx, task.Code).AppendResult(member_model.ResultCodeDuplicateIdentifier)
		return false, errors.NewAlreadyExistsError("identifier")
	}
	// TODO defer writeHeader() valid failed reason
	return true, nil
}

func (e *EventTaskBuilder) personalLimited(ctx context.Context, code string) bool {
	enabled, remainCount := e.GetTask(ctx, code).GetPersonalLimitRemain(*e.getTaskRecord(ctx, code))
	if enabled && remainCount == 0 {
		e.GetTaskLog(ctx, code).AppendResult(member_model.ResultCodeOutOfPersonalLimit)
		return true
	}
	return false
}

func (e *EventTaskBuilder) getRecordSpanKey(ctx context.Context, code string) string {
	return fmt.Sprintf(member_model.MEMBER_TASK_RECORD_LOCK, e.getMemberId(), e.GetTask(ctx, code).Id.Hex())
}

func (e *EventTaskBuilder) UpsertRecord(ctx context.Context, code string) error {
	var (
		record     = e.getTaskRecord(ctx, code)
		businessId = e.GetTaskItemTriggerIdentifier(ctx, code)
		err        error
	)
	if record.Valid() {
		err = record.Record(ctx, businessId)
	} else {
		record.TaskCode = code
		record.MemberId = e.GetRelatedMember(ctx).Id
		record.Identifiers = []string{businessId}
		err = record.Create(ctx)
	}

	if err != nil {
		return err
	}

	e.GetTaskLog(ctx, code).SetRecord(record)
	return nil
}

func (e *EventTaskBuilder) SpanLockBeforeRecord(ctx context.Context, code string) bool {
	deadline := time.Now().Add(time.Second * member_model.MEMBER_TASK_RECORD_WAIT_DURATION)
	for {
		if time.Now().After(deadline) {
			return false
		}
		ok, _ := extension.RedisClient.SetNX(e.getRecordSpanKey(ctx, code), "", member_model.MEMBER_TASK_RECORD_LOCK_DURATION)
		if !ok {
			time.Sleep(time.Millisecond * 500)
		} else {
			break
		}
	}
	return true
}

func (e *EventTaskBuilder) CanReward(ctx context.Context, code string) (string, bool) {
	canRewardHandler := member_model.InitCanRewardHandler(*e.GetTask(ctx, code), member_model.InitCanRewardOption{
		Member:      *e.GetRelatedMember(ctx),
		Amount:      e.GetTaskItemInfo(ctx, code).Amount,
		Identifiers: e.getTaskRecord(ctx, code).Identifiers,
	})
	canReward := canRewardHandler.CanReward(ctx)
	failedReason := canRewardHandler.CanNotRewardReason(ctx)
	return failedReason, canReward
}

func (e *EventTaskBuilder) RewardMember(ctx context.Context, code string) (member_model.ScoreRewardInfo, member_model.GrowthRewardInfo, []member_model.RewardCouponInfo, error) {
	var (
		task = e.GetTask(ctx, code)
	)

	rewards := core_util.ListWithResult[member_model.TaskReward, member_model.TaskRewardResult]{Items: member_model.GenTaskRewardGetter(task).GetTaskReward(e.GetRelatedMember(ctx))}
	result := rewards.ForEach(genTaskRewardCouponAndGrowthHandler(ctx, e, code))

	// TODO change to v2
	scoreReward, err := task.SendScoreReward(ctx, e.GetRelatedMember(ctx), genTaskInfoV1(ctx, e, code))

	return scoreReward,
		member_model.GrowthRewardInfo{Growth: core_util.Sum[uint64](core_util.ExtractArrayFieldV2("Growth", uint64(0), core_util.ExtractArrayFieldV2("GrowthReward", member_model.GrowthRewardInfo{}, result))...)},
		// TODO check 工具方法是否有问题
		core_util.CombineArraysV2(core_util.ExtractArrayFieldV2("Coupons", []member_model.RewardCouponInfo{}, result)...), err
}

func (e *EventTaskBuilder) PostRecordMember(ctx context.Context, code string) error {
	task := e.GetTask(ctx, code)
	// 更新任务触发次数
	err := task.IncTaskDailyCount(ctx, task.Id)
	if err != nil {
		log.Error(ctx, "increase task daily count failed", log.Fields{"err": err.Error()})
	}
	err = task.IncTaskMonthlyCount(ctx, task.Id)
	if err != nil {
		log.Error(ctx, "increase task monthly count failed", log.Fields{"err": err.Error()})
	}
	err = task.IncTaskTotalCount(ctx)
	if err != nil {
		log.Error(ctx, "increase task total count failed", log.Fields{"err": err.Error()})
	}

	return nil
}

func (e *EventTaskBuilder) Record(ctx context.Context, code string) (uint64, uint64, error) {
	if valid, err := e.ValidBeforeRecord(ctx, code); err != nil || !valid {
		return 0, 0, err
	}

	if !e.SpanLockBeforeRecord(ctx, code) {
		e.GetTaskLog(ctx, code).AppendResult(member_model.ResultCodeTerminateByTooManyRequest)
		return 0, 0, errors.NewTooManyRequestsError("proto_member")
	}
	defer extension.RedisClient.Del(e.getRecordSpanKey(ctx, code))

	if valid, err := e.ValidWhenRecording(ctx, code); err != nil || !valid {
		return 0, 0, err
	}

	err := e.UpsertRecord(ctx, code)
	if err != nil {
		return 0, 0, err
	}

	if _, canReward := e.CanReward(ctx, code); !canReward {
		e.GetTaskLog(ctx, code).AppendResult(member_model.ResultCodeCanNotReward)
		return 0, 0, nil
	}

	task := e.GetTask(ctx, code)

	taskReward := e.getTaskReward(ctx, code)

	// 先申明一个 MemberTaskReward 对象，并赋值 ID。奖励记录尽可能关联 MemberTaskReward 的 ID 方便以后回滚
	// 发奖
	scoreInfo, growthInfo, couponRewards, err := e.RewardMember(ctx, code)
	if err != nil {
		return 0, 0, err
	}

	err = e.PostRecordMember(ctx, code)
	if err != nil {
		log.Error(ctx, "record failed", log.Fields{"memberId": e.getMemberId(), "code": code})
	}

	// 发完奖以后需要更新任务进度，以及记录奖励日志
	rewardIdentifiers, recordIdentifiers := task.GetIdentifiers(*e.getTaskRecord(ctx, code))

	taskReward.RewardInfo = member_model.MemberTaskRewardInfo{
		Identifiers:    rewardIdentifiers,
		Amount:         e.GetWithoutPreventAmount(ctx, code),
		TriggerEventId: e.GetEventId(),
		ActualReward: member_model.ActualTaskReward{
			Score:                 scoreInfo.GetTotalScore(),
			Growth:                growthInfo.Growth,
			CouponIds:             core_util.ToObjectIdArray(core_util.ExtractArrayField("Id", couponRewards)),
			MemberShipDiscountIds: core_util.CombineArraysV2(core_util.ExtractArrayFieldV2("MemberShipDiscountIds", []string{}, couponRewards)...),
		},
		BonusInfo: member_model.TaskRewardBonusInfo{
			LevelScoreMultiplier:          scoreInfo.LevelScoreMultiplier,
			MemberPaidCardScoreMultiplier: scoreInfo.MemberPaidCardScoreMultiplier,
			MarketingScoreMultiplier:      scoreInfo.MarketingScoreMultiplier,
			// TODO 和 MarketingScoreMultiplier 整合
			MarketingScoreMultipliers: scoreInfo.MarketingScoreMultipliers,
			ScoreBonuses:              member_model.ScoreRewardInfo{}.GetUniqueByScoreBonuses(scoreInfo.ScoreBonuses), // 多规则每满中需要去重
		},
	}

	taskReward.Create(ctx)

	e.getTaskRecord(ctx, code).AfterReward(ctx, recordIdentifiers, rewardIdentifiers, e.GetEventOccurredAt(), task)

	if task.NeedSendTaskCompletedEvent(e.GetRelatedMember(ctx).Level) {
		e.GetTaskLog(ctx, code).AppendResult(member_model.ResultCodeCompleteTask)
		e.getTaskRecord(ctx, code).SendTaskCompletedEvent(ctx, *task, *taskReward, *e.GetRelatedMember(ctx), e.ChannelId, e.event) // 发送会员任务完成事件
	}
	return scoreInfo.GetTotalScore(), growthInfo.Growth, nil
}

func (e *EventTaskBuilder) Rollback(ctx context.Context, code string) (uint64, uint64, error) {
	identifier := e.GetTaskItemTriggerIdentifier(ctx, code)

	// 先判断当前 task 是否有支持 rollback，不支持就返回错误
	if !e.GetTask(ctx, code).CanRollback() {
		return 0, 0, errors.NewInvalidArgumentErrorWithMessage("task", "can not rollback current task")
	}

	// 先检查 memberTaskReward 里有没有对应 identifier
	taskReward, err := member_model.CMemberTaskReward.FindByIdentifier(ctx, code, identifier, bson.ObjectIdHex(e.getMemberId()))
	// memberTaskReward 存在，我们对其做必要的处理
	if err == nil {
		taskHandler := InitTaskHandlerV2(taskReward)
		if !taskHandler.CanRollbackV2(identifier) {
			return 0, 0, errors.NewInvalidArgumentErrorWithMessage("identifier", "already rollbacked")
		}
		// 处理部分退款导致的不满足触发条件的情况
		e.ResetRollbackAmount(ctx, taskReward, code)
		// 按成长值的会员成长模式才发放成长值
		rollbackInfo, err := taskHandler.CountRollbackRewardV2(ctx, identifier, code, e.GetTaskItemInfo(ctx, code).WithoutPreventAmount, e)
		if err != nil {
			return 0, 0, err
		}
		rollbackScore := rollbackInfo.Score
		rollbackGrowth := rollbackInfo.Growth
		rollbackScoreContainsBonusScore := rollbackInfo.BonusScore
		rollbackMembershipDiscountIds := rollbackInfo.MembershipDiscountIds

		// 收回成长值奖励
		if rollbackGrowth > 0 {
			err := e.rollbackGrowth(ctx, rollbackGrowth, code, &taskReward)
			if err != nil {
				log.Warn(ctx, "rollback growth failed.", log.Fields{"err": err, "memberId": e.getMemberId()})
			}
		}

		// 收回积分奖励
		if rollbackScore > 0 {
			e.rollbackScore(ctx, rollbackScore, rollbackScoreContainsBonusScore, code, &taskReward)
		}

		if len(rollbackMembershipDiscountIds) > 0 {
			rollbackMembershipDiscountIds = member_model.RollbackMembershipDiscounts(ctx, rollbackMembershipDiscountIds)
		}

		_ = taskReward.PushRollbackInfo(ctx, identifier, rollbackScore, rollbackGrowth, e.GetTaskItemInfo(ctx, code).WithoutPreventAmount, rollbackMembershipDiscountIds)

		if rollbackGrowth == 0 && rollbackScore == 0 && len(rollbackMembershipDiscountIds) == 0 {
			return 0, 0, nil
		}

		// 当奖励全部退完时，可以认为任务全部回滚了，需要将 record 回退，因为 record 可能占用了次数 https://gitlab.maiscrm.com/mai/home/<USER>/issues/46013#note_4508421
		taskReward, _ = member_model.CMemberTaskReward.FindByIdentifier(ctx, code, identifier, bson.ObjectIdHex(e.getMemberId()))
		if taskReward.IsAllRefund(ctx) {
			taskRecord, _ := member_model.CMemberTaskRecord.GetRecord(ctx, bson.ObjectIdHex(e.getMemberId()), code)
			for _, identifier := range taskReward.RewardInfo.Identifiers {
				taskRecord.PersonalLimit.RollbackByIdentifier(identifier)
			}
			err = taskRecord.SavePersonalLimit(ctx)
			if err != nil {
				log.Error(ctx, "Rollback task personal limit failed.", log.Fields{"err": err, "identifier": identifier})
			}
		}

		if errs := e.GetTask(ctx, code).RollbackRewardPrivilege(ctx, *e.GetRelatedMember(ctx), identifier, e.GetEventProperties()); len(errs) > 0 {
			return 0, 0, errors.NewUnknowError(errs)
		}
		return rollbackScore, rollbackGrowth, nil
	}

	// 可能当前 identifier 还没用于发奖，而是记录在了 memberTaskRecord，所以我们继续执行
	// 检查 memberTaskRecord 里有没有，有就删掉这个 identifier
	taskRecord, err := member_model.CMemberTaskRecord.FindByIdentifier(ctx, code, identifier, bson.ObjectIdHex(e.getMemberId()))
	if err == nil {
		err := taskRecord.RemoveIdentifier(ctx, identifier)
		if err != nil {
			return 0, 0, err
		}
		return 0, 0, nil
	}

	// 如果都没找到，就说明当前 identifier 不对
	return 0, 0, codes.NewError(codes.MemberTaskIdentifierNotFound)
}

func (e *EventTaskBuilder) rollbackGrowth(ctx context.Context, rollbackGrowth uint64, code string, taskReward *member_model.MemberTaskReward) error {
	option := member_model.IncGrowthOption{
		Growth:            -int64(rollbackGrowth),
		Reason:            taskReward.GetGrowthRollbackReason(),
		GrowthType:        member_model.GetGrowthType(e.GetEventId()),
		BusinessId:        e.GetTaskItemTriggerIdentifier(ctx, code),
		Identifier:        "",
		ChannelId:         e.ChannelId,
		IgnoredChannelIds: nil,
		CreatedAt:         nil,
		NeedCtxOperator:   false,
		GrowthTrigger:     e.GenerateTrigger(ctx, code),
	}
	_, err := e.GetRelatedMember(ctx).IncGrowth(ctx, option)
	return err
}

func (e *EventTaskBuilder) rollbackScore(ctx context.Context, rollbackScore, rollbackScoreContainsBonusScore uint64, code string, taskReward *member_model.MemberTaskReward) {
	taskInfo := genTaskInfoV1(ctx, e, code)
	scoreInfo := taskInfo.GenerateScoreInfo(ctx, true).Copy(ctx, map[string]interface{}{
		"Score":       rollbackScore,
		"MemberId":    e.getMemberId(),
		"Description": taskReward.GetScoreRollbackReason(),
	})
	// 退款不检查积分是否足够，扣成负积分也需要扣
	scoreInfo.DisableCheckScoreEnough()
	memberScoreLimit, _ := member_model.GetScoreLimit(ctx, bson.ObjectIdHex(e.getMemberId()))
	defer func() {
		_, ok := member_model.GetMemberScoreSettingByCode(taskReward.GetRewardScoreRule(ctx), member_model.C_SCORE_SETTING_CODE_LIMIT)
		if ok {
			// rollbackScore 中包含的 bonusScore 是不参与限制的，需要排除
			needRollbackLimitScore := int64(rollbackScore - rollbackScoreContainsBonusScore)
			if needRollbackLimitScore > 0 {
				memberScoreLimit.RollbackScoreByIdentifier(ctx, uint64(needRollbackLimitScore), scoreInfo.BusinessId)
			}
		} else {
		}
	}()

	// 收回积分奖励
	if rollbackScore > 0 {
		identifier := taskInfo.GetIdentifier(ctx)
		if taskInfo.Task.Settings.Times > 1 {
			// 多次触发类的任务，需要使用获取奖励的那次触发。例如 A+B 触发奖励，积分和保护期对应的 businessId 都是 B，退款 A 时也应拿着 B 去查
			// TODO add test
			identifier = taskReward.RewardInfo.Identifiers[len(taskReward.RewardInfo.Identifiers)-1]
		}

		protectionScores, _ := member_model.CMemberProtectionPeriodScore.GetAllByIdentifierAndTaskId(ctx, bson.ObjectIdHex(e.getMemberId()), identifier, taskInfo.Task.Id.Hex())

		remainScore := member_model.RollbackProtectionScore(ctx, protectionScores, scoreInfo)
		if remainScore > 0 {
			rollbackInfo := scoreInfo.Copy(ctx, map[string]interface{}{
				"Brief":       member_model.REFUND_REDUCE_SCORE,
				"Assigner":    member_model.SYSTEM_ASSIGNER,
				"Score":       -remainScore,
				"Description": taskReward.GetScoreRollbackReason(),
				"Remark":      "", // 回退积分时，不应该使用 task 中的 scoreRemark，这里置空，在创建 scoreHistory 时会自动使用 description 填充
			})
			// 开启整年过期积分设置时，如果有部分是固定有效期积分，可能已经过期，此时不应该被扣（目前 UA 会生成有效期很短的活动积分）
			if scoreInfo.GetScoreResetRule().IsScoreWholeYearExpireEnabled() {
				histories, err := member_model.CScoreHistory.FindAllByMemberIdBusinessIdAndTaskId(ctx, bson.ObjectIdHex(e.getMemberId()), scoreInfo.BusinessId, taskInfo.Task.Id.Hex(), time.Time{})
				if err != nil {
					log.Error(ctx, "get score history failed", log.Fields{"err": err.Error()})
				}
				if len(histories) > 0 {
					_, _, expired := member_model.CScoreHistory.GetFixedExpireScoreHistoriesExpirePartAndUsePart(ctx, histories)
					// 最多仅可退 remain+used
					maxRefundScore := cast.ToInt64(taskReward.RewardInfo.ActualReward.Score) - expired
					for i := range taskReward.RollbackInfo {
						maxRefundScore -= cast.ToInt64(taskReward.RollbackInfo[i].Score)
					}
					if maxRefundScore < remainScore {
						rollbackInfo.Score = -maxRefundScore
					}
				}
			}
			// 如果当前退还的积分对应的奖励是在之前的年度累积周期里，那么不应该减去年度累积积分
			if !isRollbackInSameRange(taskReward, scoreInfo.GetScoreResetRule()) {
				rollbackInfo.NotDecAnnualAccumulatedScore = true
			}
			resp, err := member_model.RewardScoreToMembers(ctx, rollbackInfo, []member_model.Member{*e.GetRelatedMember(ctx)})
			if err != nil || len(resp.FailedMemberIds) > 0 {
				log.Warn(ctx, "rollback score to member failed.", log.Fields{"err": err, "resp": resp, "memberId": e.getMemberId()})
			}
		}
	}
}

func (e *EventTaskBuilder) GenerateTrigger(ctx context.Context, code string) member_model.RewardTrigger {
	var triggerType string
	task := e.GetTask(ctx, code)
	switch {
	case task.Type == member_model.MEMBER_TASK_TYPE_SCORE_RULE:
		triggerType = member_model.TRIGGER_TYPE_SCORE_RULE
	case task.Type == "task" && task.IsDefault:
		triggerType = member_model.TRIGGER_TYPE_TASK
	default:
		triggerType = member_model.TRIGGER_TYPE_INVITATION
	}
	trigger := member_model.RewardTrigger{
		TriggerType:  triggerType,
		TriggerScene: task.Name,
		TriggerId:    task.Id.Hex(),
		Trigger:      e.GetEventId(),
		From:         member_model.REWARD_FROM_TASK,
	}
	return trigger
}

func (e *EventTaskBuilder) GetWithoutPreventAmount(ctx context.Context, code string) uint64 {
	var (
		task   = e.GetTask(ctx, code)
		amount = cast.ToUint64(e.GetEventProperties()[e.GetTaskItemTrigger(ctx, code).AmountField])
	)
	if !e.GetScoreSetting(ctx, member_model.C_SCORE_SETTING_CODE_NO_SCORE_REWARD).NoScoreReward.NeedCheckProductNoReward(task.Code, task.Business) {
		return amount
	}

	// TODO 计算过之后记录下来，避免重复计算
	// TODO 退款的话直接返回 t.GetAmount

	preventAmount, err := member_model.GetAmountByMultiSetting(ctx, member_model.WithEvent(e.GetEventId(), e.GetEventProperties()), member_model.WithTaskOption(task), member_model.WithCalPreventAmount(&e.GetScoreSetting(ctx, member_model.C_SCORE_SETTING_CODE_NO_SCORE_REWARD).NoScoreReward))
	if err != nil {
		log.Error(ctx, "calculate without prevent amount failed", log.Fields{"err": err})
		return amount
	}
	if preventAmount >= amount {
		return 0
	} else {
		return amount - preventAmount
	}
}

func (e *EventTaskBuilder) getTaskRecord(ctx context.Context, code string) *member_model.MemberTaskRecord {
	record, exists := e.getTaskRecordMapper(ctx)[code]
	if !exists {
		taskRecord, err := member_model.CMemberTaskRecord.GetRecord(ctx, e.GetRelatedMember(ctx).Id, code)
		if err != nil {
			record = &member_model.MemberTaskRecord{}
			record.PersonalLimit = model.InitLimitRemains()
		} else {
			record = taskRecord
		}
		e.getTaskRecordMapper(ctx)[code] = record
	}

	return record
}

func (e *EventTaskBuilder) getTaskReward(ctx context.Context, code string) *member_model.MemberTaskReward {
	reward, exists := e.getTaskRewardMapper(ctx)[code]
	if !exists {
		switch {
		case e.IsRollback(ctx, code):
			taskReward, err := member_model.CMemberTaskReward.FindByIdentifier(ctx, code, e.GetTaskItemTriggerIdentifier(ctx, code), e.GetRelatedMember(ctx).Id)
			if err != nil {
				reward = &member_model.MemberTaskReward{}
			} else {
				reward = &taskReward
			}

		default:
			task := e.GetTask(ctx, code)
			reward = &member_model.MemberTaskReward{
				Id:       bson.NewObjectId(),
				MemberId: e.GetRelatedMember(ctx).Id,
				TaskInfo: member_model.MemberTaskInfo{
					Business:            task.Business,
					Code:                task.Code,
					Rewards:             e.getSuitableTaskRewards(ctx, code),
					Settings:            task.Settings,
					MultiAmountSettings: task.MultiAmountSettings,
				},
				MemberLevel: uint64(e.GetRelatedMember(ctx).Level),
			}
			_, expireAt, _ := e.GetRelatedMember(ctx).IsPaidMember(ctx)
			if expireAt.After(time.Now()) {
				reward.IsPaidMember = true
			}
		}
		e.getTaskRewardMapper(ctx)[code] = reward
	}
	return reward
}

func (e *EventTaskBuilder) getSuitableTaskRewards(ctx context.Context, code string, options ...string) []member_model.TaskReward {
	var (
		task   = e.GetTask(ctx, code)
		member = e.GetRelatedMember(ctx)
	)

	switch task.GetSettingType() {
	case member_model.TASK_SETTING_TYPE_PERFECT_INFORMATION:
		return []member_model.TaskReward{task.Reward}
	case member_model.TASK_SETTING_TYPE_AMOUNT, member_model.TASK_SETTING_TYPE_TIMES:
		return getDefaultSuitableRewards(task, member)
	case member_model.TASK_SETTING_TYPE_MULTI_AMOUNT:
		var result []member_model.TaskReward
		// 后续可能也需要考虑适用等级，太复杂，暂时不处理
		// multiAmount 的任务奖励在 task.multiAmountSettings 中
		rewards := core_util.ExtractArrayFieldV2("Reward", member_model.TaskReward{}, task.MultiAmountSettings)
		for i := range rewards {
			reward := rewards[i]
			if len(options) == 1 {
				amountScoreRewardId := options[0]
				if amountScoreRewardId == "" || (amountScoreRewardId != "" && amountScoreRewardId == reward.TaskRewardId) {
					result = append(result, reward)
				}
			}
		}
		return result
	}
	return nil
}

func (e *EventTaskBuilder) getTaskRecordMapper(ctx context.Context) map[string]*member_model.MemberTaskRecord {
	if e.taskRecords == nil {
		e.taskRecords = make(map[string]*member_model.MemberTaskRecord)
	}
	return e.taskRecords
}

func (e *EventTaskBuilder) getTaskRewardMapper(ctx context.Context) map[string]*member_model.MemberTaskReward {
	if e.taskRewards == nil {
		e.taskRewards = make(map[string]*member_model.MemberTaskReward)
	}
	return e.taskRewards
}

func (e *EventTaskBuilder) SetEventProperties(properties map[string]any) *EventTaskBuilder {
	e.eventProperties = properties
	return e
}

func (e *EventTaskBuilder) SetLevelSetting(s *member_model.MemberLevelSetting) *EventTaskBuilder {
	e.LevelSetting = s
	return e
}

func (e *EventTaskBuilder) SetEvent(event *types.Event) *EventTaskBuilder {
	e.event = event
	return e
}

func (e *EventTaskBuilder) SetScoreResetRule(s *member_model.ScoreResetRule) *EventTaskBuilder {
	e.ScoreResetRule = s
	return e
}

func (e *EventTaskBuilder) SetTasksMapper(t map[string]*member_model.MemberTask) *EventTaskBuilder {
	e.TasksMapper = t
	return e
}

func (e *EventTaskBuilder) SetScoreSetting(s map[string]*member_model.MemberScoreSetting) *EventTaskBuilder {
	e.ScoreSetting = s
	return e
}

func (e *EventTaskBuilder) SetLevelMapper(lvm map[uint64]*member_model.MemberLevel) *EventTaskBuilder {
	e.LevelMapper = lvm
	return e
}

func (e *EventTaskBuilder) ResetRollbackAmount(ctx context.Context, reward member_model.MemberTaskReward, code string) {
	// 暂时只对订单退款做处理
	if !util.StrInArray(e.GetEventId(), &[]string{
		component.MAIEVENT_REFUND_ORDER,
		component.MAIEVENT_REFUND_PRODUCT,
	}) {
		return
	}
	task := e.GetTask(ctx, code)
	if task == nil {
		return
	}
	// 获取 trigger，用于下面的属性限制判断
	rollbackTrigger, _ := task.GetEventRollbacker(e.GetEventId())
	rewardTrigger, _ := task.GetEventTrigger(reward.RewardInfo.TriggerEventId)
	rewardAmount := reward.RewardInfo.Amount
	rollbackAmount := cast.ToUint64(e.eventProperties[rollbackTrigger.AmountField])
	shouldRollbackAmount := member_model.GetShouldRollbackAmount(rewardTrigger, rewardAmount, reward.CountTotalRollbackAmount(), rollbackAmount)
	if shouldRollbackAmount == 0 {
		return
	}
	// 没有满足金额限制，全退
	properties := e.GetEventProperties()
	properties[rollbackTrigger.AmountField] = shouldRollbackAmount
	e.SetEventProperties(properties)
}

func genTaskRewardCouponAndGrowthHandler(ctx context.Context, builder *EventTaskBuilder, code string) func(_ int, reward member_model.TaskReward) (result member_model.TaskRewardResult, needBreak bool) {
	return func(_ int, reward member_model.TaskReward) (result member_model.TaskRewardResult, needBreak bool) {
		// 验证适用等级

		// TODO Validate 和 CheckMemberLevelSuitable
		member := builder.GetRelatedMember(ctx)
		if !reward.Validate(member) {
			defer writeHeader(ctx, fmt.Sprintf("task-%v-reward", code), "validate failed, can't access reward.")
			return
		}
		if !reward.CheckMemberLevelSuitable(ctx, member) {
			return
		}

		result.Coupons = genSendTaskRewardCouponHandler(ctx, builder, code)(reward)

		// 发成长值奖励
		// 按成长值的会员成长模式才发放成长值
		if builder.GetLevelSetting(ctx) != nil && builder.GetLevelSetting(ctx).GetLevelRule() == member_model.LEVEL_RULE_GROWTH {
			result.GrowthReward = genSendTaskRewardGrowthHandler(ctx, builder, code)(reward)
		}

		return
	}
}

func writeHeader(ctx context.Context, key string, value any) {
	core_util.WriteAccessLogExtra(ctx, key, value)
}

func genSendTaskRewardCouponHandler(ctx context.Context, builder *EventTaskBuilder, code string) func(reward member_model.TaskReward) []member_model.RewardCouponInfo {
	return func(reward member_model.TaskReward) []member_model.RewardCouponInfo {
		couponReward := member_model.RewardContentDirect{}
		_ = copier.Instance(nil).RegisterIgnoreTargetFields([]copier.FieldKey{"Score"}).From(reward).CopyTo(&couponReward)
		return couponReward.Reward(ctx, *builder.GetRelatedMember(ctx), "", fmt.Sprintf("memberTask:%s", code), "").Coupons
	}
}

func genSendTaskRewardGrowthHandler(ctx context.Context, builder *EventTaskBuilder, code string) func(reward member_model.TaskReward) member_model.GrowthRewardInfo {
	return func(reward member_model.TaskReward) member_model.GrowthRewardInfo {
		task := builder.GetTask(ctx, code)
		taskInfo := genTaskInfoV1(ctx, builder, code)

		var totalGrowth uint64

		// 在某些任务条件中，成长值奖励是可以成倍发放的，因此获取倍率
		multiplier := task.GetRewardMultiplier(taskInfo.Amount, reward)
		growth := multiplier * reward.Growth.Value

		// 发放正常的成长值
		if growth > 0 {
			reason := reward.Growth.Reason
			if reward.Growth.Reason == "" {
				reason = fmt.Sprintf("%s-%s", member_model.MEMBER_GROWTH_REASON_PREFIX_TASK, task.Name)
			}

			option := member_model.IncGrowthOption{
				Growth:            int64(growth),
				Reason:            reason,
				GrowthType:        task.GetGrowthType(),
				ChannelId:         taskInfo.ChannelId,
				BusinessId:        builder.GetTaskItemTriggerIdentifier(ctx, code),
				LevelSetting:      builder.GetLevelSetting(ctx),
				IgnoredChannelIds: nil,
				CreatedAt:         nil,
				NeedCtxOperator:   false,
				GrowthTrigger:     taskInfo.GenerateTrigger(ctx),
				SceneId:           fmt.Sprintf("memberTask:%s", taskInfo.Task.Id.Hex()),
			}

			_, err := builder.GetRelatedMember(ctx).IncGrowth(ctx, option)
			if err != nil {
				log.Error(ctx, "reward growth failed.", log.Fields{
					"code":     code,
					"memberId": builder.getMemberId(),
				})
				return member_model.GrowthRewardInfo{}
			}
			totalGrowth += growth
		}

		// 发放会员营销的每满成长值
		if reward.GrowthAmount > 0 && taskInfo.Amount/cast.ToUint64(reward.GrowthAmount) >= 1 {
			times := taskInfo.Amount / cast.ToUint64(reward.GrowthAmount)
			amountGrowth := times * reward.AmountGrowth.Value
			reason := reward.AmountGrowth.Reason
			if reward.AmountGrowth.Reason == "" {
				reason = fmt.Sprintf("%s-%s", member_model.MEMBER_GROWTH_REASON_PREFIX_INVITATION, task.Name)
			}
			option := member_model.IncGrowthOption{
				Growth:            int64(amountGrowth),
				Reason:            reason,
				GrowthType:        task.GetGrowthType(),
				ChannelId:         taskInfo.ChannelId,
				BusinessId:        builder.GetTaskItemTriggerIdentifier(ctx, code),
				LevelSetting:      builder.GetLevelSetting(ctx),
				IgnoredChannelIds: nil,
				CreatedAt:         nil,
				NeedCtxOperator:   false,
				GrowthTrigger:     taskInfo.GenerateTrigger(ctx),
			}

			_, err := builder.GetRelatedMember(ctx).IncGrowth(ctx, option)
			if err != nil {
				log.Error(ctx, "reward growth failed.", log.Fields{
					"code":     code,
					"memberId": builder.getMemberId(),
				})
				return member_model.GrowthRewardInfo{}
			}
			totalGrowth += amountGrowth
		}
		return member_model.GrowthRewardInfo{Growth: totalGrowth}
	}
}

func getDefaultSuitableRewards(task *member_model.MemberTask, member *member_model.Member) []member_model.TaskReward {
	var (
		needToReward, memberRewards, paidCardRewards []member_model.TaskReward
	)
	for i, reward := range task.Rewards {
		if core_util.StrInArray("paidMember", &reward.SuitableMemberTypes) {
			paidCardRewards = append(paidCardRewards, task.Rewards[i])
		} else {
			memberRewards = append(memberRewards, task.Rewards[i])
		}
	}

	newRewards := append(paidCardRewards, memberRewards...)
	if len(newRewards) == 0 {
		newRewards = []member_model.TaskReward{task.Reward}
	}
	for i, reward := range newRewards {
		if isMemberPaidCardSuitable(reward.SuitableMemberTypes, member.PaidCards) {
			needToReward = append(needToReward, newRewards[i])
			break
		}
		if (len(reward.SuitableMemberTypes) == 0 ||
			core_util.StrInArray("member", &reward.SuitableMemberTypes)) &&
			isMemberLevelSuitable(member.Level, reward.SuitableMemberLevels) {
			needToReward = append(needToReward, newRewards[i])
		}
	}

	return needToReward
}

func isMemberPaidCardSuitable(suitableMemberTypes []string, paidCards []member_model.PaidCard) bool {
	for _, memberType := range suitableMemberTypes {
		for _, paidCard := range paidCards {
			if memberType == paidCard.CardType && paidCard.ExpireAt.After(time.Now()) {
				return true
			}
		}
	}
	return false
}

func isMemberLevelSuitable(memberLevel int64, suitableMemberLevels []uint64) bool {
	if len(suitableMemberLevels) == 0 {
		return true
	}
	return core_util.IndexOfArray(cast.ToUint64(memberLevel), suitableMemberLevels) != -1
}

// TODO need change to v2
func genTaskInfoV1(ctx context.Context, builder *EventTaskBuilder, code string) *member_model.TaskInfo {
	task := builder.GetTask(ctx, code)
	// TODO check if ok is need
	trigger, _ := task.GetEventTrigger(builder.GetEventId())
	taskInfo := member_model.GenerateTaskInfoByEvent(ctx, builder.event, trigger, task, builder.GetEventProperties(), builder.GetRelatedMember(ctx))
	taskInfo.SetNoRewardSetting(builder.GetScoreSetting(ctx, member_model.C_SCORE_SETTING_CODE_NO_SCORE_REWARD))

	taskInfo.SetTaskLog(builder.GetTaskLog(ctx, code))

	taskInfo.SetScoreResetRule(builder.GetScoreResetRule(ctx))
	taskInfo.TriggerIdentifier = builder.GetTaskItemTriggerIdentifier(ctx, code)
	setting := func() map[string]*member_model.MemberScoreSetting {
		var result = make(map[string]*member_model.MemberScoreSetting)
		for _, rule := range []string{"scoreSettingProtection", "scoreSettingLimit", "scoreSettingNoScoreReward"} {
			result[rule] = builder.GetScoreSetting(ctx, rule)
		}
		return result
	}()

	taskInfo.SetMemberScoreSettingsMapper(setting)
	// TODO 获取客户等级
	taskInfo.SetLevelMapper(builder.LevelMapper)

	taskInfo.InitSetting(ctx)

	return taskInfo
}

func isRollbackInSameRange(taskReward *member_model.MemberTaskReward, rule *member_model.ScoreResetRule) bool {
	if taskReward == nil || rule == nil {
		return true
	}
	switch rule.ResetType {
	case member_model.SCORE_RESET_TYPE_NEVER,
		member_model.SCORE_RESET_TYPE_WHOLE_YEAR,
		member_model.SCORE_RESET_TYPE_BY_MONTH:
		// 自然年清零的情况，判断是否是同一年
		return time.Now().Year() == taskReward.CreatedAt.Year()
	}
	return true
}
