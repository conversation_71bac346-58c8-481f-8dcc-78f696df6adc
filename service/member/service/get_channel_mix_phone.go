package service

import (
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	pb_member "mairpc/proto/member"
	"mairpc/service/member/model"

	"golang.org/x/net/context"
)

func (MemberService) GetChannelMixPhone(ctx context.Context, req *pb_member.GetChannelMixPhoneRequest) (*pb_member.ChannelMixPhoneDetailResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if req.MixPhone == "" && req.OpenId == "" {
		return nil, errors.NewInvalidParamsError(map[string]interface{}{
			"mixPhone": "MixPhone or openId is required",
		})
	}
	condition := bson.M{
		"channelId": req.ChannelId,
	}
	if req.MixPhone != "" {
		condition["mixPhone"] = req.MixPhone
	}
	if req.OpenId != "" {
		condition["openId"] = req.OpenId
	}
	channelMixPhone, err := model.CChannelMixPhone.GetByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	return formatChannelMixPhone(channelMixPhone), nil
}

func formatChannelMixPhone(channelMixPhone *model.ChannelMixPhone) *pb_member.ChannelMixPhoneDetailResponse {
	result := &pb_member.ChannelMixPhoneDetailResponse{}
	core_util.CopyRFC3339(channelMixPhone, result)
	return result
}
