package service

import (
	"fmt"
	"mairpc/core/errors"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/algorithm"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	"mairpc/proto/member"
	"mairpc/service/member/model"
	"mairpc/service/share/component"
	share_model "mairpc/service/share/model"
	"strings"

	"golang.org/x/net/context"
)

const (
	SCORE_RULE_TYPE_FINISH_ORDER_SCORE = "finishOrderScore"
	SCORE_RULE_TYPE_PURCHASE_SCORE     = "purchaseScore"
)

var (
	businessMap = map[string]string{
		SCORE_RULE_TYPE_PURCHASE_SCORE:     model.MEMBER_TASK_BUSINESS_LOYALTY_PURCHASE_PLATFORM,
		SCORE_RULE_TYPE_FINISH_ORDER_SCORE: model.MEMBER_TASK_BUSINESS_LOYALTY_FINISH_ORDER_PLATFORM,
	}
	defaultScoreRules = []string{
		model.C_SCORE_SETTING_CODE_PROTECTION, model.C_SCORE_SETTING_CODE_LIMIT,
	}
	defaultScoreRuleRewardPrivileges = []string{
		model.MEMBER_PRIVILEGE_MULTIPLY_SCORE,
		model.DEFAULT_PRIVILEGE_LOYALTY,
		model.DEFAULT_PRIVILEGE_BIRTHDAY,
	}

	defaultPlatformTaskNameMap = map[string]string{
		model.MEMBER_TASK_BUSINESS_LOYALTY_PURCHASE_PLATFORM:     "购买商品奖励（积分）",
		model.MEMBER_TASK_BUSINESS_LOYALTY_FINISH_ORDER_PLATFORM: "完成下单奖励（积分）",
	}
	defaultPlatformEventRollbackMap = map[string][]model.TaskEventTrigger{
		model.MEMBER_TASK_BUSINESS_LOYALTY_PURCHASE_PLATFORM: {
			{
				EventId:         component.MAIEVENT_REFUND_ORDER,
				AmountField:     ORDER_TASK_REFUND_AMOUNT_FIELD_REFUND_GOODS_AMOUNT,
				IdentifierField: "orderId",
			},
		},
		model.MEMBER_TASK_BUSINESS_LOYALTY_FINISH_ORDER_PLATFORM: {
			{
				EventId:         component.MAIEVENT_REFUND_ORDER,
				AmountField:     "",
				IdentifierField: "orderId",
				PropertyFilters: []model.TaskEventPropertyFilter{
					model.TaskEventPropertyFilter{
						Field: "isAllRefund",
						Rules: []share_model.CompareRule{
							share_model.CompareRule{
								Type:     share_model.TYPE_BOOL,
								Operator: share_model.OPERATOR_EQ,
								Value:    true,
							},
						},
					},
				},
			},
		},
	}

	defaultScoreRuleRewardReasonMap = map[string]string{
		model.MEMBER_TASK_BUSINESS_LOYALTY_PURCHASE_PLATFORM:     "购买商品奖励",
		model.MEMBER_TASK_BUSINESS_LOYALTY_FINISH_ORDER_PLATFORM: "完成下单奖励",
	}
	defaultScoreRuleRollbackReasonMap = map[string]string{
		model.MEMBER_TASK_BUSINESS_LOYALTY_PURCHASE_PLATFORM:     "退货退款",
		model.MEMBER_TASK_BUSINESS_LOYALTY_FINISH_ORDER_PLATFORM: "退货退款",
	}
)

func (m MemberService) UpsertPlatformDefaultScoreTask(ctx context.Context, req *member.UpsertPlatformDefaultScoreTaskRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	// 验证请求数据
	err := validateUpsertPlatformDefaultScoreTaskRequest(ctx, req)
	if err != nil {
		return nil, err
	}

	var (
		insertDefault = false
		insertCustom  = false
	)

	setUpsertPlatformScoreTaskRequest(ctx, req)

	if req.IsEnabled {
		insertDefault, err = upsertDefaultScoreRule(ctx, businessMap[req.ScoreRuleType], req)
		if err != nil {
			return nil, err
		}
	}

	if req.IsPlatformEnabled {
		insertCustom, err = upsertPlatformScoreRule(ctx, businessMap[req.ScoreRuleType], req)
		if err != nil {
			return nil, err
		}
	}

	// 统一处理 enable
	err = setIsEnabledByRequest(ctx, businessMap[req.ScoreRuleType], req)
	if err != nil {
		return nil, err
	}

	if insertDefault || insertCustom {
		// 同步积分屏蔽规则
		syncNoScoreRewardSetting(ctx, m)
	}

	// 同步会员营销权益
	syncMemberMarketingPrivilege(ctx)

	return &response.EmptyResponse{}, nil
}

func syncMemberMarketingPrivilege(ctx context.Context) {
	privileges, err := model.CMemberPrivilege.FindAllByCondition(ctx, share_model.Base.GenDefaultCondition(ctx))
	if err != nil {
		log.Error(ctx, "get privileges failed.", log.Fields{"err": err.Error()})
		return
	}

	privilegeNames := append(core_util.ToStringArray(core_util.ExtractArrayField("Name", privileges)), defaultScoreRuleRewardPrivileges...)
	tasks, err := model.CMemberTask.GetDefaultScoreRule(ctx)
	if err != nil {
		log.Error(ctx, "get default score rule failed.", log.Fields{"err": err.Error()})
	}
	for i := range tasks {
		// 保留非营销权益，可能是客户定制的一些权益
		err = tasks[i].SetApplyPrivileges(ctx, getTaskNewScoreRewardPrivileges(ctx, tasks[i], privilegeNames))
		if err != nil {
			log.Error(ctx, "update privileges failed.", log.Fields{"err": err.Error()})
		}
	}
}

func getTaskNewScoreRewardPrivileges(ctx context.Context, task model.MemberTask, newPrivileges []string) []string {
	exists := getTaskExistsScoreRewardPrivileges(ctx, task)
	set := algorithm.CSet.InstanceFromStringSlice(&newPrivileges)
	for i := range exists {
		set.Insert(exists[i])
	}
	return core_util.ToStringArray(set.ToArray())
}

func getTaskExistsScoreRewardPrivileges(ctx context.Context, task model.MemberTask) []string {
	switch task.GetSettingType() {
	case model.TASK_SETTING_TYPE_MULTI_AMOUNT:
		return task.MultiAmountSettings[0].Reward.Score.ApplyPrivileges
	case model.TASK_SETTING_TYPE_AMOUNT, model.TASK_SETTING_TYPE_TIMES, model.TASK_SETTING_TYPE_PERFECT_INFORMATION:
		if len(task.Rewards) > 0 {
			return task.Rewards[0].Score.ApplyPrivileges
		} else {
			return task.Reward.Score.ApplyPrivileges
		}
	}
	return nil
}

func setUpsertPlatformScoreTaskRequest(ctx context.Context, req *member.UpsertPlatformDefaultScoreTaskRequest) {
	for i, rule := range req.PlatformScoreRules {
		if rule.Reward != nil && rule.Reward.Score != nil {
			setScoreRewardInfo(ctx, businessMap[req.ScoreRuleType], req.PlatformScoreRules[i].Reward.Score)
		}
		if rule.MultiAmountSettings != nil {
			for j, setting := range rule.MultiAmountSettings {
				if setting.Reward != nil && setting.Reward.Score != nil {
					setScoreRewardInfo(ctx, businessMap[req.ScoreRuleType], req.PlatformScoreRules[i].MultiAmountSettings[j].Reward.Score)
				}
				req.PlatformScoreRules[i].MultiAmountSettings[j].AmountField = "payAmount"
				req.PlatformScoreRules[i].MultiAmountSettings[j].AmountValueField = "productSku"
				req.PlatformScoreRules[i].MultiAmountSettings[j].AmountArrayField = "goodsList"
			}
		}
	}
}

func setScoreRewardInfo(ctx context.Context, business string, scoreReward *member.TaskScoreReward) {
	scoreReward.ScoreRules = defaultScoreRules
	scoreReward.Reason = defaultScoreRuleRewardReasonMap[business]
	scoreReward.RollbackReason = defaultScoreRuleRollbackReasonMap[business]
}

func syncNoScoreRewardSetting(ctx context.Context, m MemberService) {
	setting, err := model.CMemberScoreSetting.GetByCode(ctx, model.C_SCORE_SETTING_CODE_NO_SCORE_REWARD)
	if err != nil {
		log.Warn(ctx, "no score reward settings not exists", log.Fields{})
		return
	}
	err = afterUpdateNoRewardSetting(ctx, m, &setting)
	if err != nil {
		log.Warn(ctx, "sync no score reward setting failed.", log.Fields{
			"err": err.Error(),
		})
	}
}

func setIsEnabledByRequest(ctx context.Context, business string, req *member.UpsertPlatformDefaultScoreTaskRequest) error {
	var err error
	if req.IsEnabled {
		err = model.CMemberTask.EnableDefaultPlatformTaskByBusiness(ctx, business)
	} else {
		err = model.CMemberTask.DisableDefaultPlatformTaskByBusiness(ctx, business)
	}
	if err != nil {
		return err
	}
	if req.IsPlatformEnabled {
		// 开启请求中包含的平台，关闭请求中不包含的平台
		err = updateCustomPlatformScoreRuleIsEnabled(ctx, business, req.PlatformScoreRules)
	} else {
		err = model.CMemberTask.DisableCustomPlatformTaskByBusiness(ctx, business)
	}
	if err != nil {
		return err
	}
	return nil
}

func updateCustomPlatformScoreRuleIsEnabled(ctx context.Context, business string, rules []*member.PlatformScoreRuleSetting) error {
	tasks, err := model.CMemberTask.FindByBusinessAndTypeByDefault(ctx, business, model.MEMBER_TASK_TYPE_SCORE_RULE, false)
	if err != nil {
		return err
	}

	var (
		enabledPlatforms                          = core_util.ToStringArray(core_util.ExtractArrayField("Platform", rules))
		enabledPlatformsSet                       = algorithm.CSet.InstanceFromStringSlice(&enabledPlatforms)
		needEnableTaskCodes, needDisableTaskCodes = algorithm.CSet.Instance(), algorithm.CSet.Instance()
	)

	for _, task := range tasks {
		platform := getScoreRuleTaskPlatform(ctx, task)
		// 统一规则已更新过
		if platform == model.REWARD_PLATFORM_ALL {
			continue
		}
		if enabledPlatformsSet.Has(platform) {
			needEnableTaskCodes.Insert(task.Code)

		} else {
			needDisableTaskCodes.Insert(task.Code)
		}
	}

	if needEnableTaskCodes.Size() > 0 {
		err = model.CMemberTask.EnableTaskByCodes(ctx, core_util.ToStringArray(needEnableTaskCodes.ToArray()))
	}
	if needDisableTaskCodes.Size() > 0 {
		err = model.CMemberTask.DisableTaskByCodes(ctx, core_util.ToStringArray(needDisableTaskCodes.ToArray()))
	}

	if err != nil {
		return err
	}
	return nil
}

func validateUpsertPlatformDefaultScoreTaskRequest(ctx context.Context, req *member.UpsertPlatformDefaultScoreTaskRequest) error {
	if (req.IsEnabled || req.IsPlatformEnabled) && len(req.PlatformScoreRules) < 1 {
		return errors.NewInvalidArgumentErrorWithMessage("platformScoreRules", "need one rule at least")
	}

	hasAll := false
	for _, rule := range req.PlatformScoreRules {
		if rule.Platform == model.REWARD_PLATFORM_ALL {
			hasAll = true
			break
		}
	}
	if req.IsEnabled && !hasAll {
		return errors.NewInvalidArgumentErrorWithMessage("platformScoreRules", "must has 'ALL' platform rule")
	}
	return nil
}

// 更新购买商品和完成下单统一规则
func upsertDefaultScoreRule(ctx context.Context, business string, req *member.UpsertPlatformDefaultScoreTaskRequest) (bool, error) {
	hasInsert := false
	// 获取需要统一规则中需要忽略的平台
	ignorePlatforms := getCustomEnabledPlatforms(ctx, req)
	tasks, err := model.CMemberTask.FindByBusinessAndTypeByDefault(ctx, business, model.MEMBER_TASK_TYPE_SCORE_RULE, true)
	if err != nil {
		return false, err
	}
	switch len(tasks) {
	case 0:
		task := &model.MemberTask{
			IsDefault: true,
			Business:  business,
			Type:      model.MEMBER_TASK_TYPE_SCORE_RULE,
			EventTrigger: []model.TaskEventTrigger{
				model.TaskEventTrigger{
					EventId:         component.MAIEVENT_ORDER_COMPLETED,
					AmountField:     "goodsAmount",
					IdentifierField: "orderId",
				},
			},
		}
		setDefaultPlatformScoreRule(ctx, task, ignorePlatforms, req)
		err = task.Create(ctx)
		hasInsert = true
	default:
		// 理论上只有一条默认的统一规则
		if len(tasks) > 1 {
			log.Error(ctx, "too many default tasks", log.Fields{"taskIds": core_util.ExtractArrayField("Id", tasks)})
		}
		setDefaultPlatformScoreRule(ctx, &tasks[0], ignorePlatforms, req)
		err = (&tasks[0]).UpdateByTaskSettingType(ctx)
	}

	if err != nil {
		return hasInsert, err
	}
	return hasInsert, nil
}

// 更新购买商品和完成下单自定义平台规则
func upsertPlatformScoreRule(ctx context.Context, business string, req *member.UpsertPlatformDefaultScoreTaskRequest) (bool, error) {
	tasks, err := model.CMemberTask.FindByBusinessAndTypeByDefault(ctx, business, model.MEMBER_TASK_TYPE_SCORE_RULE, false)
	if err != nil {
		return false, err
	}

	hasInsert := false
	// 获取需要统一规则中需要忽略的平台
	for i := range req.PlatformScoreRules {
		if req.PlatformScoreRules[i].Platform == model.REWARD_PLATFORM_ALL {
			continue
		}
		var task *model.MemberTask
		for j := range tasks {
			platform := getScoreRuleTaskPlatform(ctx, tasks[j])
			if platform == req.PlatformScoreRules[i].Platform {
				task = &tasks[j]
				break
			}
		}
		if task == nil {
			task = &model.MemberTask{
				IsDefault: false,
				Business:  business,
				Type:      model.MEMBER_TASK_TYPE_SCORE_RULE,
				EventTrigger: []model.TaskEventTrigger{
					{
						EventId:         component.MAIEVENT_ORDER_COMPLETED,
						AmountField:     "goodsAmount",
						IdentifierField: "orderId",
					},
				},
			}
			setCustomPlatformScoreRule(ctx, task, req.PlatformScoreRules[i])
			err = task.Create(ctx)
			hasInsert = true
		} else {
			setCustomPlatformScoreRule(ctx, task, req.PlatformScoreRules[i])
			err = task.UpdateByTaskSettingType(ctx)
		}
	}
	if err != nil {
		return hasInsert, err
	}
	return hasInsert, nil
}

func setCustomPlatformScoreRule(ctx context.Context, task *model.MemberTask, rule *member.PlatformScoreRuleSetting) {
	setScoreRule(ctx, task, rule)
	filters := append(getNotPlatformFilter(task.EventTrigger[0].PropertyFilters), model.TaskEventPropertyFilter{
		Field: "platform",
		Rules: []share_model.CompareRule{
			{
				Type:     share_model.TYPE_STRING,
				Operator: share_model.OPERATOR_EQ,
				Value:    rule.Platform,
			},
		},
	})
	task.EventTrigger[0].PropertyFilters = filters

	filters = append(getNotPlatformFilter(task.EventRollbacker[0].PropertyFilters), model.TaskEventPropertyFilter{
		Field: "platform",
		Rules: []share_model.CompareRule{
			{
				Type:     share_model.TYPE_STRING,
				Operator: share_model.OPERATOR_EQ,
				Value:    rule.Platform,
			},
		},
	})
	task.EventRollbacker[0].PropertyFilters = filters
}

func setDefaultPlatformScoreRule(ctx context.Context, task *model.MemberTask, ignorePlatforms []string, req *member.UpsertPlatformDefaultScoreTaskRequest) {
	task.Name = defaultPlatformTaskNameMap[task.Business]

	for i := range req.PlatformScoreRules {
		if req.PlatformScoreRules[i].Platform != model.REWARD_PLATFORM_ALL {
			continue
		}
		rule := req.PlatformScoreRules[i]

		setScoreRule(ctx, task, rule)
		// 保留非 platform 的 filter、添加排除其他平台的 filter 微商城、有赞、微盟、微信小店订单需要额外排除
		ignorePlatforms = append(ignorePlatforms, model.REWARD_PLATFORM_MICROMALL, model.REWARD_PLATFORM_YOUZAN, model.REWARD_PLATFORM_WEIMOB, model.REWARD_PLATFORM_WESHOP)
		filters := append(getNotPlatformFilter(task.EventTrigger[0].PropertyFilters), model.TaskEventPropertyFilter{
			Field: "platform",
			Rules: []share_model.CompareRule{
				share_model.CompareRule{
					Type:     share_model.TYPE_STRING,
					Operator: share_model.OPERATOR_NIN,
					Value:    strings.Join(ignorePlatforms, " "),
				},
			},
		})
		task.EventTrigger[0].PropertyFilters = filters

		filters = append(getNotPlatformFilter(task.EventRollbacker[0].PropertyFilters), model.TaskEventPropertyFilter{
			Field: "platform",
			Rules: []share_model.CompareRule{
				share_model.CompareRule{
					Type:     share_model.TYPE_STRING,
					Operator: share_model.OPERATOR_NIN,
					Value:    strings.Join(ignorePlatforms, " "),
				},
			},
		})

		task.EventRollbacker[0].PropertyFilters = filters
		break
	}
}

func setScoreRule(ctx context.Context, task *model.MemberTask, rule *member.PlatformScoreRuleSetting) {
	var (
		needOverwriteCp = copier.Instance(copier.NewOption().SetOverwrite(true))
		cp              = copier.Instance(copier.NewOption().SetOverwrite(false))
	)

	task.Rewards = nil
	task.Reward = model.TaskReward{}
	task.MultiAmountSettings = nil

	task.Name = fmt.Sprintf("%s-%s", defaultPlatformTaskNameMap[task.Business], getScoreRuleTaskPlatform(ctx, *task))
	task.EventRollbacker = defaultPlatformEventRollbackMap[task.Business]

	_ = needOverwriteCp.From(rule.Settings).CopyTo(&task.Settings)
	switch task.Business {
	case model.MEMBER_TASK_BUSINESS_LOYALTY_PURCHASE_PLATFORM:
		if rule.Settings.MultiAmount > 0 {
			_ = needOverwriteCp.From(rule.MultiAmountSettings).CopyTo(&task.MultiAmountSettings)
		} else {
			_ = cp.From(rule.Reward).CopyTo(&task.Reward)
			_ = needOverwriteCp.From(rule.Reward.Score).CopyTo(&(task.Reward.Score))
		}
	case model.MEMBER_TASK_BUSINESS_LOYALTY_FINISH_ORDER_PLATFORM:
		_ = cp.From(rule.Reward).CopyTo(&task.Reward)
		_ = needOverwriteCp.From(rule.Reward.Score).CopyTo(&(task.Reward.Score))
	}
}

func getNotPlatformFilter(from []model.TaskEventPropertyFilter) []model.TaskEventPropertyFilter {
	var filters []model.TaskEventPropertyFilter
	for j := range from {
		if from[j].Field != "platform" {
			filters = append(filters, from[j])
		}
	}
	return filters
}

// 获取积分规则任务所属平台
func getScoreRuleTaskPlatform(ctx context.Context, task model.MemberTask) string {
	if len(task.EventTrigger) < 1 {
		return ""
	}

	if task.Type != model.MEMBER_TASK_TYPE_SCORE_RULE {
		log.Warn(ctx, "invalid task type", log.Fields{
			"err":  fmt.Sprintf("[%v] task should not get platform", task.Type),
			"task": task,
		})
	}

	for _, filter := range task.EventTrigger[0].PropertyFilters {
		if filter.Field == "platform" {
			for _, rule := range filter.Rules {
				switch rule.Value.(type) {
				case string:
					platform := rule.Value.(string)
					if rule.Operator == share_model.OPERATOR_EQ {
						if platform == model.REWARD_PLATFORM_DOUYIN || platform == model.REWARD_PLATFORM_TAOBAO {
							return platform
						}
						// 兼容自定义过的积分规则其它平台的也会返回
						if platform != "" {
							return platform
						}
					}
					if rule.Operator == share_model.OPERATOR_NIN {
						return model.REWARD_PLATFORM_ALL
					}
				}
			}
		}
	}

	return model.REWARD_PLATFORM_ALL
}

// 获取自定义设置的
func getCustomEnabledPlatforms(ctx context.Context, req *member.UpsertPlatformDefaultScoreTaskRequest) []string {
	if !req.IsPlatformEnabled {
		return nil
	}
	platforms := core_util.ToStringArray(core_util.ExtractArrayField("Platform", req.PlatformScoreRules))
	core_util.Remove(&platforms, model.REWARD_PLATFORM_ALL)
	return platforms
}
