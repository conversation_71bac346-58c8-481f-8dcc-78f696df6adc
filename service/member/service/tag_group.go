package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"time"

	"mairpc/core/component"
	core_util "mairpc/core/util"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/proto/common/types"
	pb_ec_setting "mairpc/proto/ec/setting"
	"mairpc/proto/member"
	"mairpc/service/ec/service"
	"mairpc/service/member/codes"
	"mairpc/service/member/model"
	share_component "mairpc/service/share/component"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

func (MemberService) CreateTagGroup(ctx context.Context, req *member.CreateTagGroupRequest) (*member.TagGroup, error) {
	accountId := bson.ObjectIdHex(util.GetAccountId(ctx))
	var (
		tagGroup *model.TagGroup
		name     string
	)
	if req.Is<PERSON><PERSON><PERSON> {
		tagGroup = model.CTagGroup.GetDefault(ctx, accountId)
		name = model.DEFAULT_TAG_GROUP_NAME
	} else {
		tagGroup = model.CTagGroup.GetByCondition(ctx, bson.M{
			"name":      req.Name,
			"accountId": accountId,
			"isDeleted": false,
		})
		name = req.Name
	}

	if tagGroup != nil {
		return nil, codes.NewError(codes.TagGroupHasExisted)
	}

	tagSet := model.CTagSet.GetDefault(ctx)
	newTagGroup := &model.TagGroup{
		Id:        bson.NewObjectId(),
		Name:      name,
		TagSetId:  tagSet.Id,
		AccountId: accountId,
		IsDefault: req.IsDefault,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	if req.TagSetId != "" {
		newTagGroup.TagSetId = bson.ObjectIdHex(req.TagSetId)
	}
	if len(req.Businesses) > 0 {
		tagGroupBusinesses := []model.TagGroupBusiness{}
		for _, b := range req.Businesses {
			tmpBusiness := model.TagGroupBusiness{
				Extra: map[string]interface{}{},
			}
			tmpBusiness.Business = b.Business
			json.Unmarshal([]byte(b.Extra), &tmpBusiness.Extra)
			tagGroupBusinesses = append(tagGroupBusinesses, tmpBusiness)
			if b.Business == "wechatwork" && cast.ToBool(tmpBusiness.Extra["isVisibleInWechatWork"]) {
				newTagGroup.Order, _ = model.CTagGroup.GetNextOrder(ctx)
			}
		}
		newTagGroup.Businesses = tagGroupBusinesses
	}
	err := newTagGroup.Create(ctx)
	if err != nil {
		return nil, codes.NewError(codes.CreateTagGroupFail)
	}

	return formatTagGroupResponse(ctx, newTagGroup, nil), nil
}

func (MemberService) GetTagGroupList(ctx context.Context, req *member.GetTagGroupListRequest) (*member.TagGroupList, error) {
	accountId := util.GetAccountId(ctx)
	var dbTagGroups []model.TagGroup
	var totalCount int
	var orderBy []string
	// 请求中只传了 orderBy 则获取到的是所有标签组
	isAllTagGroup := reflect.DeepEqual(req, &member.GetTagGroupListRequest{OrderBy: req.OrderBy})
	condition := bson.M{
		"accountId": bson.ObjectIdHex(accountId),
	}
	if !req.IncludeDeleted {
		condition["isDeleted"] = false
	}

	if req.UpdatedAt != nil {
		condition["updatedAt"] = util.ParseStringDateRange(req.UpdatedAt)
	}

	if len(req.GroupNames) > 0 {
		condition["name"] = bson.M{
			"$in": req.GroupNames,
		}
	}

	if len(req.GroupIds) > 0 {
		condition["_id"] = bson.M{
			"$in": core_util.ToObjectIdArray(req.GroupIds),
		}
	}

	if req.SearchKeyword != "" {
		condition["name"] = util.GetFuzzySearchStrRegex(req.SearchKeyword)
	}

	if req.StaffOperateDisabled != nil {
		condition["staffOperateDisabled"] = req.StaffOperateDisabled.Value
	}

	if req.StrategyId != "" {
		condition["strategies.id"] = util.ToMongoId(req.StrategyId)
	}

	if req.IsStrategyTagGroup != nil {
		condition["strategies.0"] = bson.M{
			"$exists": req.IsStrategyTagGroup.Value,
		}
	}

	if req.BusinessSelector != nil {
		businessSelector := bson.M{}
		if req.BusinessSelector.Business != "" {
			if req.BusinessSelector.ByExcluded {
				condition["$or"] = []bson.M{
					{
						"businesses.business": bson.M{
							"$ne": req.BusinessSelector.Business,
						},
					},
					{
						"businesses": bson.M{
							"$exists": false,
						},
					},
					{
						"businesses.0": bson.M{
							"$exists": false,
						},
					},
				}
			} else {
				businessSelector["business"] = req.BusinessSelector.Business
			}
		}

		if req.BusinessSelector.Extra != "" {
			extra := util.UnmarshalJsonString(req.BusinessSelector.Extra)
			for key, value := range extra {
				businessSelector[fmt.Sprintf("extra.%s", key)] = value
			}
		}

		if len(businessSelector) != 0 {
			condition["businesses"] = bson.M{
				"$elemMatch": businessSelector,
			}
		}
		if _, ok := condition["$or"]; ok {
			condition = util.FormatConditionContainedOr(condition)
		}
	}

	// 查询特定规则组标签时按规则组内序号排序
	if req.StrategyId != "" {
		pipeline := []bson.M{
			{"$match": condition},
			{"$addFields": bson.M{
				"filteredStrategies": bson.M{
					"$filter": bson.M{
						"input": "$strategies",
						"as":    "strategy",
						"cond": bson.M{
							"$eq": bson.A{"$$strategy.id", util.ToMongoId(req.StrategyId)},
						},
					},
				},
			}},
			{"$sort": bson.M{"filteredStrategies.order": 1}},
			{"$skip": (req.ListCondition.Page - 1) * req.ListCondition.PerPage},
			{"$limit": req.ListCondition.PerPage},
		}
		dbTagGroups, _ = model.CTagGroup.Aggregate(ctx, pipeline)
		totalCount = len(dbTagGroups)
	} else {
		if nil == req.ListCondition {
			orderBy = req.OrderBy
			dbTagGroups = model.CTagGroup.GetAllByCondition(ctx, condition, orderBy)
			totalCount = len(dbTagGroups)
		} else {
			pageIndex, pageSize := util.ParsePagingCondition(req.ListCondition)
			orderBy = req.ListCondition.OrderBy
			dbTagGroups, totalCount = model.CTagGroup.GetAllByPagination(ctx, condition, pageIndex, pageSize, orderBy)
		}
	}

	resp := &member.TagGroupList{
		Total: uint64(totalCount),
	}
	if req.WithoutTags {
		resp.Items = formatTagGroupsWithoutTags(dbTagGroups)
	} else {
		resp.Items = formatTagGroupsWithAllTags(ctx, dbTagGroups, req.Tag, isAllTagGroup)
	}

	return resp, nil
}

func (MemberService) UpdateTagGroup(ctx context.Context, req *member.UpdateTagGroupRequest) (*member.TagGroup, error) {
	tagGroup := model.CTagGroup.GetById(ctx, bson.ObjectIdHex(req.Id))
	if nil == tagGroup {
		return nil, codes.NewError(codes.TagGroupNotFound)
	}

	if req.Name != "" {
		if tagGroup.Name == req.Name {
			return nil, errors.New("新名称不能与原名称相同")
		}
		condition := model.Common.GenDefaultCondition(ctx)
		condition["name"] = req.Name
		if tagGroup := model.CTagGroup.GetByCondition(ctx, condition); tagGroup != nil {
			return nil, errors.New("标签分组名称已存在")
		}
		// 同步修改规则组中的标签组名称
		channel, _ := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
		if channel != nil {
			for _, strategy := range tagGroup.Strategies {
				resp := &share_component.WxCommonResponse{}
				share_component.WeconnectProxy(ctx, channel.ChannelId, "POST", model.EDIT_STRATEGY_TAG_PATH, nil, share_component.EditStrategyTagRequest{
					Id:   strategy.GroupId,
					Name: req.Name,
				}, resp)
			}
		}
		tagGroup.Name = req.Name
	}

	if req.Order > 0 && tagGroup.Order != req.Order {
		condition, number := genUpdateTagGroupOrderCondition(ctx, tagGroup.Order, req)
		model.CTagGroup.IncOrder(ctx, condition, number)
		tagGroup.Order = req.Order
	}

	if req.StaffOperateDisabled != nil {
		tagGroup.StaffOperateDisabled = req.StaffOperateDisabled.Value
	}

	updateTagGroupBusiness(ctx, tagGroup, req.BusinessUpdater)
	err := tagGroup.Update(ctx)
	if err != nil {
		return nil, codes.NewError(codes.UpdateTagGroupFail)
	}

	component.GO(ctx, func(ctx context.Context) {
		service.CreateAuditLog(ctx, "导购权限", "编辑导购可见客户标签", "wechatwork")
	})

	return formatTagGroupResponse(ctx, tagGroup, nil), nil
}

func genUpdateTagGroupOrderCondition(ctx context.Context, oldOrder uint64, req *member.UpdateTagGroupRequest) (bson.M, int) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       bson.M{"$ne": util.ToMongoId(req.Id)},
		"isDeleted": false,
	}
	newOrder := req.Order
	number := 1
	if newOrder > oldOrder {
		number = -1
		selector["order"] = bson.M{
			"$gt":  oldOrder,
			"$lte": newOrder,
		}
	} else {
		selector["order"] = bson.M{
			"$gte": newOrder,
			"$lt":  oldOrder,
		}
	}
	return selector, number
}

func (MemberService) DeleteTagGroup(ctx context.Context, req *request.DetailRequest) (*response.BoolResponse, error) {
	accountId := bson.ObjectIdHex(util.GetAccountId(ctx))
	tagGroup := model.CTagGroup.GetById(ctx, util.ToMongoId(req.Id))
	if tagGroup == nil {
		return nil, codes.NewError(codes.TagGroupNotFound)
	}
	err := model.CTagGroup.DeleteById(ctx, accountId, bson.ObjectIdHex(req.Id))
	if err != nil {
		return &response.BoolResponse{Value: false}, err
	}
	// 删除标签组后修改后续其他标签组的排序
	if tagGroup.Order != 0 {
		tagGroup.UdpateOrderAfterTagGroup(ctx)
	}

	return &response.BoolResponse{Value: true}, nil
}

func (MemberService) GetTagGroup(ctx context.Context, req *member.GetTagGroupRequest) (*member.TagGroup, error) {
	accountId := bson.ObjectIdHex(util.GetAccountId(ctx))
	condition := formatGetTagGroupCondition(req, accountId)
	tagGroup := model.CTagGroup.GetByCondition(ctx, condition)
	if nil == tagGroup {
		return &member.TagGroup{}, nil
	}

	return formatTagGroupResponse(ctx, tagGroup, nil), nil
}

func updateTagGroupBusiness(ctx context.Context, tagGroup *model.TagGroup, updater *member.UpdateTagGroupBusinessRequest) {
	if updater == nil || tagGroup.Id.Hex() != updater.Id {
		return
	}

	businessExists := false
	isActionRemoved := false
	for index, business := range tagGroup.Businesses {
		if business.Business != updater.Business {
			continue
		}

		businessExists = true
		// 更新
		if updater.Action == member.UpdateTagGroupBusinessRequest_UPSERT {
			business.Extra = core_util.UnmarshalJsonString(updater.Extra)
			tagGroup.Businesses[index] = business
			break
		}
		// 删除
		tagGroup.Businesses = append(tagGroup.Businesses[:index], tagGroup.Businesses[index+1:]...)
		isActionRemoved = true
		break
	}
	if !businessExists && updater.Action == member.UpdateTagGroupBusinessRequest_UPSERT {
		tagGroup.Businesses = append(tagGroup.Businesses, model.TagGroupBusiness{
			Business: updater.Business,
			Extra:    core_util.UnmarshalJsonString(updater.Extra),
		})
	}
	if updater.Business == "wechatwork" {
		newOrder := GetNewOrderByBusinessExtra(ctx, *tagGroup, isActionRemoved)
		if newOrder != -1 {
			tagGroup.Order = uint64(newOrder)
		}
		UpdateSyncTagGroupIds(ctx, tagGroup, isActionRemoved)
	}
}

func formatTagGroupResponse(ctx context.Context, tagGroup *model.TagGroup, sortor []string) *member.TagGroup {
	result := &member.TagGroup{}
	core_util.FormatIntDate(tagGroup, result, map[string]interface{}{
		"Businesses": formatTagGroupBusinesses,
	}, map[string]string{})
	result.Tags = *model.CTag.GetGroupTags(ctx, tagGroup.AccountId, tagGroup.Id, sortor)

	return result
}

func formatGetTagGroupCondition(req *member.GetTagGroupRequest, accountId bson.ObjectId) bson.M {
	condition := bson.M{
		"accountId": accountId,
		"isDeleted": false,
	}

	if util.IsStringFieldSet(req.Id) {
		condition["_id"] = bson.ObjectIdHex(req.Id)
	}

	if util.IsStringFieldSet(req.Name) {
		condition["name"] = req.Name
	}

	if req.IsDefault != nil {
		condition["isDefault"] = req.IsDefault.Value
	}

	return condition
}

func formatTagGroupBusinesses(businesses []model.TagGroupBusiness) []*member.TagGroupBusiness {
	result := []*member.TagGroupBusiness{}

	for _, b := range businesses {
		item := &member.TagGroupBusiness{}
		item.Business = b.Business

		bytes, _ := json.Marshal(b.Extra)
		item.Extra = string(bytes)

		result = append(result, item)
	}

	return result
}

func formatTagGroupByTagKey(ctx context.Context, tagGroup *model.TagGroup, sortor []string, tag string) *member.TagGroup {
	result := &member.TagGroup{}
	core_util.FormatIntDate(tagGroup, result, map[string]interface{}{
		"Businesses": formatTagGroupBusinesses,
	}, map[string]string{})

	var tagNames []string
	var tags []model.Tag
	condition := bson.M{
		"accountId": tagGroup.AccountId,
		"groupId":   tagGroup.Id,
	}
	if tag != "" {
		condition["tag"] = util.GetFuzzySearchStrRegex(tag)
	}
	tags = model.CTag.GetAllByConditon(ctx, condition, sortor)
	for _, tag := range tags {
		tagNames = append(tagNames, tag.Tag)
	}
	result.Tags = tagNames
	result.TagCount = uint64(len(tagNames))

	return result
}

func formatTagGroupsWithoutTags(groups []model.TagGroup) []*member.TagGroup {
	result := make([]*member.TagGroup, 0, len(groups))
	for _, group := range groups {
		temp := &member.TagGroup{}
		core_util.FormatIntDate(group, temp, map[string]interface{}{
			"Businesses": formatTagGroupBusinesses,
		}, map[string]string{})
		result = append(result, temp)
	}
	return result
}

func formatTagGroupsWithAllTags(ctx context.Context, groups []model.TagGroup, tag string, isAllTagGroup bool) []*member.TagGroup {
	result := formatTagGroupsWithoutTags(groups)
	groupIds := make([]bson.ObjectId, 0, len(groups))
	groupTagsMap := make(map[string]*member.TagGroup, len(groups))
	for i := range result {
		group := result[i]
		groupIds = append(groupIds, bson.ObjectIdHex(group.Id))
		groupTagsMap[group.Id] = group
	}
	var tags []model.Tag
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}
	// 不是所有标签组才过滤 groupId，所有标签组直接获取所有标签即可
	if !isAllTagGroup {
		condition["groupId"] = bson.M{"$in": groupIds}
	}
	if tag != "" {
		condition["tag"] = util.GetFuzzySearchStrRegex(tag)
	}
	tags = model.CTag.GetAllWithFields(ctx, condition, bson.M{
		"tag":     1,
		"groupId": 1,
	})
	for _, tag := range tags {
		group, ok := groupTagsMap[tag.GroupId.Hex()]
		if !ok {
			continue
		}
		// group 为指针类型，tag 会直接添加到 result 中对应的 group
		group.Tags = append(group.Tags, tag.Tag)
		group.TagCount++
	}
	return result
}

func UpdateSyncTagGroupIds(ctx context.Context, tagGroup *model.TagGroup, isActionRemoved bool) {
	setting, _ := client.GetEcSettingServiceClient().GetWechatworkSetting(ctx, &pb_ec_setting.GetSettingsRequest{})
	if setting == nil || !setting.IsSyncPartialTagGroup {
		return
	}
	for _, business := range tagGroup.Businesses {
		if business.Business != "wechatwork" {
			continue
		}
		tagId, ok := business.Extra["wechatWorkTagGroupId"].(string)
		if !ok {
			break
		}
		needSync, ok := business.Extra["needSyncToWechatwork"].(bool)
		if !ok {
			break
		}
		exists := util.StrInArray(tagId, &setting.SyncTagGroupIds)
		// 需要同步但未添加到 syncTagGroupIds 中
		if needSync && !exists {
			client.GetEcSettingServiceClient().UpdateWechatworkSetting(ctx, &pb_ec_setting.UpdateWechatworkSettingRequest{
				IsSyncPartialTagGroup: &types.BoolValue{Value: true},
				SyncTagGroupIds:       append(setting.SyncTagGroupIds, tagId),
			})
			break
		}
		if !needSync && exists || isActionRemoved {
			syncTagGroupIds := util.RemoveStrArrayElem(setting.SyncTagGroupIds, tagId)
			client.GetEcSettingServiceClient().UpdateWechatworkSetting(ctx, &pb_ec_setting.UpdateWechatworkSettingRequest{
				IsSyncPartialTagGroup: &types.BoolValue{Value: true},
				SyncTagGroupIds:       syncTagGroupIds,
			})
		}
	}
}
