package service

import (
	"fmt"
	"mairpc/service/member/model"
	async_cache "mairpc/service/share/async_cache"
	"mairpc/service/share/util"

	"mairpc/core/component"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/proto/account"
	"mairpc/proto/common/response"
	"mairpc/proto/member"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

const (
	SCORE_HISTORY_COUNT_HASH_KEY    = "member:scoreHistoryCount:%s"
	SCORE_HISTORY_COUNT_RESULT_KEY  = "scoreHistoryCountResult"
	SCORE_HISTORY_COUNT_LAST_ID_KEY = "scoreHistoryCountLastId"
)

func (m MemberService) CountScoreHistories(ctx context.Context, req *member.ScoreHistoryListRequest) (*response.AsyncCacheResponse, error) {
	code := bson.NewObjectId().Hex()
	rpcErr := async_cache.CreateAsyncCacheData(ctx, &account.CreateAsyncCacheDataRequest{
		Code: code,
	})
	if rpcErr != nil {
		return nil, errors.NewMaiRPCError(rpcErr.Code, rpcErr.Desc)
	}

	component.GO(ctx, func(oldCtx context.Context) {
		var (
			ctx       = core_util.CtxWithReadSecondaryPreferred(oldCtx)
			updateReq = &account.UpdateAsyncCacheDataRequest{
				Code:   code,
				Status: "completed",
			}
			cachedCount, lastId = getScoreHistoryCountCache(ctx)
			nextLastId          = getNextLastId(ctx, lastId)
			idCondition         = bson.M{}
			useCache            = false
		)
		selector, err := GenListScoreHistoriesSelector(ctx, m, req)
		if err != nil {
			updateReq.Status = "failed"
			updateReq.FailedReason = err.Error()
		} else {
			if len(selector) == 1 {
				if lastId.Valid() {
					idCondition["$gt"] = lastId
				}
				if nextLastId.Valid() {
					idCondition["$lte"] = nextLastId
				}
				if len(idCondition) > 0 {
					selector["_id"] = idCondition
					useCache = true
				}
			}
			count, err := extension.DBRepository.Count(ctx, model.C_SCORE_HISTORY, selector)
			if useCache {
				count += cachedCount
				cacheScoreHistoryCount(ctx, count, nextLastId)
			}
			updateReq.Data = fmt.Sprintf("{\"total\": %d}", count)
			if err != nil {
				updateReq.Status = "failed"
				updateReq.FailedReason = err.Error()
			}
		}

		async_cache.UpdateAsyncCacheData(ctx, updateReq)
	})

	return &response.AsyncCacheResponse{
		Code: code,
	}, nil
}

func getNextLastId(ctx context.Context, lastId bson.ObjectId) bson.ObjectId {
	var history model.ScoreHistory
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}
	if lastId.Valid() {
		condition["_id"] = bson.M{"$gte": lastId}
	}
	extension.DBRepository.FindOneWithSortor(ctx, model.C_SCORE_HISTORY, condition, []string{"-_id"}, &history)
	return history.Id
}

func getScoreHistoryCountCache(ctx context.Context) (int, bson.ObjectId) {
	key := fmt.Sprintf(SCORE_HISTORY_COUNT_HASH_KEY, util.GetAccountId(ctx))
	data, _ := extension.RedisClient.Hgetall(key)
	if len(data) != 0 {
		return cast.ToInt(data[SCORE_HISTORY_COUNT_RESULT_KEY]), bson.ObjectIdHex(data[SCORE_HISTORY_COUNT_LAST_ID_KEY])
	}
	return 0, bson.NilObjectId
}

func cacheScoreHistoryCount(ctx context.Context, count int, lastId bson.ObjectId) error {
	key := fmt.Sprintf(SCORE_HISTORY_COUNT_HASH_KEY, util.GetAccountId(ctx))
	data := map[string]string{
		SCORE_HISTORY_COUNT_RESULT_KEY:  cast.ToString(count),
		SCORE_HISTORY_COUNT_LAST_ID_KEY: lastId.Hex(),
	}
	return extension.RedisClient.Hmset(key, data)
}
