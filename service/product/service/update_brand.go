package service

import (
	"mairpc/core/errors"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	"mairpc/proto/product"
	"mairpc/service/product/model"
	"mairpc/service/share/component"
	"mairpc/service/share/util"
	"time"

	"mairpc/core/extension/bson"

	"golang.org/x/net/context"
)

func (ProductService) UpdateBrand(ctx context.Context, req *product.UpdateBrandRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if req.Name != "" {
		brandDetail := model.CProductBrand.GetByName(ctx, req.Name)
		if brandDetail != nil && brandDetail.Id != bson.ObjectIdHex(req.Id) {
			return nil, errors.NewAlreadyExistsError("name")
		}
	}
	productBrand := &model.ProductBrand{
		Id:          bson.ObjectIdHex(req.Id),
		Name:        req.Name,
		Description: req.Description,
		Logo:        req.Logo,
		DivideType:  req.DivideType,
	}
	err := productBrand.Update(ctx)
	if err != nil {
		return nil, errors.NewNotExistsError("product")
	}

	TriggerBrandUpdatedEvent(ctx, productBrand.Id.Hex(), productBrand.IsDeleted)
	return &response.EmptyResponse{}, nil
}

func TriggerBrandUpdatedEvent(ctx context.Context, productBrandId string, isDeleted bool) {
	data := map[string]interface{}{
		"type":       component.EVENT_PRODUCT_BRAND_UPDATED,
		"account_id": util.GetAccountId(ctx),
		"brand_id":   productBrandId,
		"is_deleted": isDeleted,
		"updated_at": time.Now().Format("2006-01-02 15:04:05"),
	}
	component.MessageQueue.SendPortalMessage(ctx, component.MessageChannel{}, data, "brand_"+productBrandId, "product")
}
