package service

import (
	"mairpc/core/errors"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/product"
	"mairpc/service/product/model"

	"golang.org/x/net/context"
)

func (ProductService) CreateBrand(ctx context.Context, req *product.CreateBrandRequest) (*product.BrandDetail, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	brandDetail := model.CProductBrand.GetByName(ctx, req.Name)
	if brandDetail != nil {
		return nil, errors.NewAlreadyExistsError("name")
	}
	brand := &model.ProductBrand{
		Name:        req.Name,
		Description: req.Description,
		Logo:        req.Logo,
		DivideType:  req.DivideType,
	}

	brand.Create(ctx)
	result := formatBrand(brand)
	return result, nil
}

func formatBrand(source *model.ProductBrand) *product.BrandDetail {
	result := &product.BrandDetail{}
	core_util.CopyRFC3339(source, result)
	return result
}
