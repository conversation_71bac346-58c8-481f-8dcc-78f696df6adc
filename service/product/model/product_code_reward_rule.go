package model

import (
	"context"
	"fmt"
	"mairpc/core/errors"
	"mairpc/core/util/copier"
	"mairpc/proto/client"
	share_component "mairpc/service/share/component"
	"mairpc/service/share/constant"
	"math/rand"
	"net/http"
	"time"

	"github.com/qiniu/qmgo"

	mairpc "mairpc/core/client"
	"mairpc/core/component"
	"mairpc/core/extension"
	pb_coupon "mairpc/proto/coupon"
	pb_member "mairpc/proto/member"
	"mairpc/service/product/codes"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"
)

const (
	C_PRODUCT_CODE_REWARD_RULE = "productCodeRewardRule"

	REWARD_TYPE_SCORE       = "score"
	REWARD_TYPE_COUPON      = "coupon"
	REWARD_TYPE_CUSTOM      = "custom"
	REWARD_TYPE_REDPACK     = "redpack"
	REWARD_TYPE_GROWTH      = "growth"
	REWARD_TYPE_LUCKYDRAW   = "luckydraw"
	REWARD_RULE_NAME_LENGTH = 60
	PRODUCT_REWARD_BRIEF    = "product_reward"
)

var CProductCodeRewardRule = &ProductCodeRewardRule{}

type ProductCodeRewardRule struct {
	Id                        bson.ObjectId                 `bson:"_id,omitempty"`
	Name                      string                        `bson:"name"`
	ValidDate                 ProductCodeRewardRuleDate     `bson:"validDate"`
	Reward                    ProductCodeRewardRuleReward   `bson:"reward"`
	Rewards                   []ProductCodeRewardRuleReward `bson:"rewards"`
	Skus                      []string                      `bson:"skus"`
	BatchIds                  []bson.ObjectId               `bson:"batchIds"`
	LimitMemberCount          uint64                        `bson:"limitMemberCount"`
	ExchangedMemberCount      uint64                        `bson:"exchangedMemberCount"`
	ExchangedProductCodeCount uint64                        `bson:"exchangedProductCodeCount"`
	LimitTimesPerMember       uint64                        `bson:"limitTimesPerMember"`
	LimitFirstExchange        bool                          `bson:"limitFirstExchange"`
	LimitMemberTags           []string                      `bson:"limitMemberTags"`
	FirstTimeTags             []string                      `bson:"firstTimeTags"`
	SecondTimeTags            []string                      `bson:"secondTimeTags"`
	IsEnabled                 bool                          `bson:"isEnabled"`
	IsDeleted                 bool                          `bson:"isDeleted"`
	CreatedAt                 time.Time                     `bson:"createdAt"`
	UpdatedAt                 time.Time                     `bson:"updatedAt"`
	AccountId                 bson.ObjectId                 `bson:"accountId"`
	NeedAuthorizePosition     bool                          `bson:"needAuthorizePosition"`
}

type ProductCodeRewardRuleDate struct {
	Permanent bool      `bson:"permanent"`
	BeginTime time.Time `bson:"beginTime"`
	EndTime   time.Time `bson:"endTime"`
}

type ProductCodeRewardRuleReward struct {
	Type      string                         `bson:"type"`
	Score     uint64                         `bson:"score"`
	Coupons   []ProductCodeRewardRuleCoupon  `bson:"coupons"`
	Redpack   ProductCodeRewardRuleRedpack   `bson:"redpack"`
	Growth    ProductCodeRewardRuleGrowth    `bson:"growth"`
	Luckydraw ProductCodeRewardRuleLuckydraw `bson:"Luckydraw"`
}

type ProductCodeRewardRuleLuckydraw struct {
	Id    string `bson:"id"`
	Name  string `bson:"name"`
	Count uint64 `bson:"count"`
}

type ProductCodeRewardRuleGrowth struct {
	Value int64 `bson:"value"`
}

type ProductCodeRewardRuleCoupon struct {
	Id    bson.ObjectId `bson:"id"`
	Title string        `bson:"title"`
}

type ProductCodeRewardRuleRedpack struct {
	MaxAmount  uint64 `bson:"maxAmount"`
	MinAmount  uint64 `bson:"minAmount"`
	Wishing    string `bson:"wishing"`
	SenderName string `bson:"senderName"`
}

func (r *ProductCodeRewardRule) Create(ctx context.Context) error {
	_, err := extension.DBRepository.Insert(ctx, C_PRODUCT_CODE_REWARD_RULE, r)
	return err
}

func (*ProductCodeRewardRule) GetById(ctx context.Context, id bson.ObjectId) (*ProductCodeRewardRule, error) {
	accountId := util.GetAccountIdAsObjectId(ctx)

	rewardRule := &ProductCodeRewardRule{}
	err := Common.GetById(ctx, id, accountId, "false", C_PRODUCT_CODE_REWARD_RULE, rewardRule)
	return rewardRule, err
}

func (*ProductCodeRewardRule) GetAllByPagination(ctx context.Context, condition bson.M, page, pageSize uint32, orderbys []string) ([]ProductCodeRewardRule, int) {
	var rewardRules []ProductCodeRewardRule
	totalCount := Common.GetAllByPagination(ctx, condition, page, pageSize, orderbys, C_PRODUCT_CODE_REWARD_RULE, &rewardRules)
	return rewardRules, totalCount
}

func (*ProductCodeRewardRule) UpdateOne(ctx context.Context, selector, updater bson.M) error {
	selector["accountId"] = util.GetAccountIdAsObjectId(ctx)
	updater["$set"].(bson.M)["updatedAt"] = time.Now()

	return extension.DBRepository.UpdateOne(ctx, C_PRODUCT_CODE_REWARD_RULE, selector, updater)
}

func (*ProductCodeRewardRule) UpdateAll(ctx context.Context, selector, updater bson.M) (int, error) {
	return Common.UpdateAll(ctx, C_PRODUCT_CODE_REWARD_RULE, "false", selector, updater)
}

func (r *ProductCodeRewardRule) SkuExists(sku string) bool {
	for _, s := range r.Skus {
		if s == sku {
			return true
		}
	}
	return false
}

type RewardCouponInfo struct {
	MembershipDiscountId string
	Title                string
	StartTime            string
	EndTime              string
	CouponId             string
}

type RewardRedpackInfo struct {
	Amount     uint64
	Wishing    string
	SenderName string
}

type LuckydrawInfo struct {
	Id    string
	Name  string
	Count uint64
}

type GrowthInfo struct {
	Growth uint64
}

type ScoreInfo struct {
	Score uint64
}

type SentResult struct {
	Score      uint64
	ScoreInfo  ScoreInfo
	Coupons    []RewardCouponInfo
	Redpack    RewardRedpackInfo
	Luckydraw  LuckydrawInfo
	Growth     uint64
	GrowthInfo GrowthInfo
	HasCustom  bool
	// 奖励类型，奖励逐次发放，所以只会有一个奖励类型
	Type string
	// 产品码活动ID
	ProductCodeRewardId string
}

type RewarderParams struct {
	ProductName    string
	ProductNumber  string
	ProductCode    string
	SpecProperties []string
	SpecSku        string
	ChannelId      string
	Origin         string
	Longitude      float64
	Latitude       float64
}

type scoreReward struct {
	MemberId    string
	Score       int64
	ProductName string
	ChannelId   string
	Origin      string
	ProductCode string
}

type couponReward struct {
	CouponIds  []string
	MemberId   string
	MemberName string
	Nickname   string
	Phone      string
	ChannelId  string
}

type redpackReward struct {
	Id         bson.ObjectId
	MemberId   string
	Amount     uint64
	Wishing    string
	ChannelId  string
	SenderName string
	Origin     string
}

type growthReward struct {
	Value     int64
	MemberId  string
	Reason    string
	ChannelId string
}

type luckyReward struct {
	Id       string
	Name     string
	Count    uint64
	MemberId string
}

// TODO
type customReward struct {
}

type rewarder interface {
	send(ctx context.Context) (*SentResult, error)
}

// GetAllByProductCode can get all the product code matching productCodeRewardRules
func (*ProductCodeRewardRule) GetAllByProductCode(ctx context.Context, productCode *ProductCode) []ProductCodeRewardRule {
	condition := bson.M{
		"$or": []bson.M{
			{
				"skus": productCode.SpecPropSku,
			},
			{
				"batchIds": productCode.BatchId,
			},
		},
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	var productCodeRewardRules []ProductCodeRewardRule
	extension.DBRepository.FindAll(ctx, C_PRODUCT_CODE_REWARD_RULE, condition, nil, 0, &productCodeRewardRules)

	return productCodeRewardRules
}

func (rewarder *scoreReward) send(ctx context.Context) (*SentResult, error) {
	request := &pb_member.UpdateScoreRequest{
		Ids:         []string{rewarder.MemberId},
		Score:       rewarder.Score,
		Assigner:    PRODUCT_REWARD_BRIEF,
		Brief:       PRODUCT_REWARD_BRIEF,
		Description: fmt.Sprintf("%s %s", rewarder.ProductName, rewarder.ProductCode),
		ChannelId:   rewarder.ChannelId,
		Origin:      rewarder.Origin,
	}

	_, err := mairpc.RPCProxy.Call(
		getMemberServiceClient,
		"MemberService.UpdateScore",
		ctx,
		request,
	)
	sentResult := &SentResult{}
	if err != nil {
		return sentResult, err
	}

	sentResult.Score = uint64(rewarder.Score)
	sentResult.ScoreInfo = ScoreInfo{
		Score: uint64(rewarder.Score),
	}
	sentResult.Type = REWARD_TYPE_SCORE
	return sentResult, nil
}

func (rewarder *couponReward) send(ctx context.Context) (*SentResult, error) {
	req := &pb_coupon.IssueCouponRequest{
		MemberId:   rewarder.MemberId,
		CheckLimit: true,
		ChannelId:  rewarder.ChannelId,
		IsSystem:   true,
	}
	var (
		err        error
		sentResult = &SentResult{}
	)
	for _, couponId := range rewarder.CouponIds {
		req.CouponId = couponId
		resp, mairpcErr := mairpc.RPCProxy.Call(
			getCouponServiceClient,
			"CouponService.IssueCoupon",
			ctx,
			req,
		)

		if mairpcErr != nil {
			err = mairpcErr
		} else {
			resp := resp.(*pb_coupon.IssueCouponResponse)
			couponInfo := RewardCouponInfo{
				MembershipDiscountId: resp.Id,
				Title:                resp.Coupon.Title,
				StartTime:            resp.ValidFrom,
				EndTime:              resp.ExpiredAt,
				CouponId:             couponId,
			}
			sentResult.Coupons = append(sentResult.Coupons, couponInfo)
		}
	}

	// As long as one coupon sent successfully, the result is a success
	if len(sentResult.Coupons) > 0 {
		sentResult.Type = REWARD_TYPE_COUPON
		return sentResult, nil
	}
	return nil, err
}

func (rewarder *redpackReward) send(ctx context.Context) (*SentResult, error) {
	req := &pb_member.SendRedpackRequest{
		MemberId:   rewarder.MemberId,
		ChannelId:  rewarder.ChannelId,
		CampaignId: rewarder.Id.Hex(),
		Wishing:    rewarder.Wishing,
		SenderName: rewarder.SenderName,
		ActionName: rewarder.SenderName,
		Amount:     rewarder.Amount,
		Remark:     "产品码活动",
		Origin: &pb_member.RedpackOrigin{
			Id:   rewarder.ChannelId,
			Name: rewarder.Origin,
		},
		ChannelOrigin: rewarder.Origin,
	}

	_, maiErr := client.GetMemberServiceClient().SendRedpack(ctx, req)
	if maiErr != nil {
		return nil, maiErr
	}

	return &SentResult{
		Type: REWARD_TYPE_REDPACK,
		Redpack: RewardRedpackInfo{
			Wishing:    rewarder.Wishing,
			Amount:     rewarder.Amount,
			SenderName: rewarder.SenderName,
		},
	}, nil
}

func (rewarder *growthReward) send(ctx context.Context) (*SentResult, error) {
	_, err := client.GetMemberServiceClient().UpdateMemberGrowth(ctx, &pb_member.UpdateMemberGrowthRequest{
		MemberId:  rewarder.MemberId,
		Growth:    rewarder.Value,
		Reason:    rewarder.Reason,
		ChannelId: rewarder.ChannelId,
		Type:      "system",
	})
	if err != nil {
		return nil, err
	}
	return &SentResult{
		Growth: uint64(rewarder.Value),
		Type:   REWARD_TYPE_GROWTH,
		GrowthInfo: GrowthInfo{
			Growth: uint64(rewarder.Value),
		},
	}, nil
}

func (rewarder *luckyReward) send(ctx context.Context) (*SentResult, error) {
	req := share_component.PortalRequest{
		Method: http.MethodPost,
		Path:   "/campaigncenter/admin/luckydraw/issue-extra-chance",
		Body: map[string]interface{}{
			"campaignId": rewarder.Id,
			"memberId":   rewarder.MemberId,
			"total":      rewarder.Count,
			"reason":     "产品码活动奖励",
		},
	}
	_, err := req.Call(ctx)
	if err != nil {
		return nil, err
	}
	return &SentResult{
		Type: REWARD_TYPE_LUCKYDRAW,
		Luckydraw: LuckydrawInfo{
			Id:    rewarder.Id,
			Name:  rewarder.Name,
			Count: rewarder.Count,
		},
	}, nil
}

func (rewarder *customReward) send(ctx context.Context) (*SentResult, error) {
	// TODO: There are no custom awards to send designs currently
	return &SentResult{
		HasCustom: true,
		Type:      REWARD_TYPE_CUSTOM,
	}, nil
}

func (self *ProductCodeRewardRule) getRewarder(ctx context.Context, member *pb_member.MemberDetailResponse, params *RewarderParams) (rewarder, error) {
	switch self.Reward.Type {
	case REWARD_TYPE_SCORE:
		return &scoreReward{
			MemberId:    member.Id,
			Score:       int64(self.Reward.Score),
			ProductName: params.ProductName,
			ChannelId:   params.ChannelId,
			Origin:      params.Origin,
			ProductCode: params.ProductCode,
		}, nil
	case REWARD_TYPE_COUPON:
		couponIds := []string{}
		for _, coupon := range self.Reward.Coupons {
			couponIds = append(couponIds, coupon.Id.Hex())
		}

		return &couponReward{
			CouponIds:  couponIds,
			MemberId:   member.Id,
			MemberName: member.Name,
			Nickname:   member.SocialMember,
			Phone:      member.Phone,
			ChannelId:  params.ChannelId,
		}, nil
	case REWARD_TYPE_REDPACK:
		minAmount := float64(self.Reward.Redpack.MinAmount)
		maxAmount := float64(self.Reward.Redpack.MaxAmount)
		var (
			wechatChannelIds []string
			weappChannelIds  []string
		)
		for _, social := range append(member.Socials, member.OriginFrom) {
			if social.Origin == constant.WECHAT && social.Subscribed {
				wechatChannelIds = append(wechatChannelIds, social.Channel)
			} else if social.Origin == constant.WEAPP {
				weappChannelIds = append(weappChannelIds, social.Channel)
			}
		}

		var (
			channelId string
			err       error
		)

		getChannelId := func(tradeOrigin string, channelIds []string) (string, error) {
			for _, channelId := range channelIds {
				resp, err := share_component.WeConnect.GetPaymentConfigurationByChannel(ctx, tradeOrigin, channelId)
				if err != nil {
					return "", err
				}
				if resp.ChannelId != "" && resp.PaymentStatus == "ENABLE" {
					return resp.ChannelId, nil
				}
			}
			return "", nil
		}
		if params.Origin == constant.WEAPP {
			channelId, err = getChannelId(share_component.CHANNEL_TRADE_ORIGIN_WEAPP, weappChannelIds)
			if err != nil {
				return nil, err
			}
			if channelId == "" {
				channelId, err = getChannelId(share_component.CHANNEL_TRADE_ORIGIN_WECHAT, wechatChannelIds)
				if err != nil {
					return nil, err
				}
			}
		} else {
			channelId, err = getChannelId(share_component.CHANNEL_TRADE_ORIGIN_WECHAT, wechatChannelIds)
			if err != nil {
				return nil, err
			}
			if channelId == "" {
				channelId, err = getChannelId(share_component.CHANNEL_TRADE_ORIGIN_WEAPP, weappChannelIds)
				if err != nil {
					return nil, err
				}
			}
		}
		if channelId == "" {
			return nil, errors.NewNotExistsErrorWithMessage("channel", "no payment available wechat channel")
		}
		return &redpackReward{
			Id:         self.Id,
			MemberId:   member.Id,
			ChannelId:  channelId,
			Origin:     params.Origin,
			Wishing:    self.Reward.Redpack.Wishing,
			SenderName: self.Reward.Redpack.SenderName,
			Amount:     uint64(minAmount + rand.Float64()*(maxAmount-minAmount)),
		}, nil
	case REWARD_TYPE_GROWTH:
		return &growthReward{
			Value:     self.Reward.Growth.Value,
			MemberId:  member.Id,
			Reason:    "",
			ChannelId: params.ChannelId,
		}, nil
	case REWARD_TYPE_LUCKYDRAW:
		return &luckyReward{
			Id:       self.Reward.Luckydraw.Id,
			Name:     self.Reward.Luckydraw.Name,
			Count:    self.Reward.Luckydraw.Count,
			MemberId: member.Id,
		}, nil
	case REWARD_TYPE_CUSTOM:
		return &customReward{}, nil
	}

	extra := map[string]interface{}{
		"ruleId":         self.Id.Hex(),
		"ruleRewardType": self.Reward.Type,
	}

	return nil, codes.NewErrorWithExtra(codes.RewardTypeCustomNotSupport, extra)
}

func (self *ProductCodeRewardRule) SendRewards(ctx context.Context, member *pb_member.MemberDetailResponse, params *RewarderParams) ([]*SentResult, error) {
	var (
		err           error
		resultErr     error
		receivedCount uint64
		results       []*SentResult
		result        *SentResult
		rewarderImpl  rewarder
	)
	err = self.BasicCheck(ctx, member, params)
	if err != nil {
		return nil, err
	}
	defer func() {
		if err != nil {
			// CheckLimitMemberCount pre-set data, If the failure to sent need to rollback
			component.GO(ctx, func(ctx context.Context) { self.RollbackLimitMemberCount(ctx, receivedCount) })
		} else {
			// If sent successfully, according to the rules for members to set tags asynchronously
			component.GO(ctx, func(ctx context.Context) { self.SetTagsToMember(ctx, receivedCount, member) })
		}
	}()
	receivedCount = CProductCodeHistory.CountRewardMemberTimes(ctx, member.Id, self.Id.Hex())
	if !self.CheckLimitMemberCount(ctx, receivedCount) {
		err = codes.NewError(codes.ExceededLimitCount)
		return nil, err
	}

	if self.LimitTimesPerMember > 0 {
		if self.LimitTimesPerMember > 0 && receivedCount >= self.LimitTimesPerMember {
			err = codes.NewError(codes.MemberExceededLimit)
			return nil, err
		}
	}

	if self.LimitFirstExchange {
		count := CProductCodeHistory.CountRewardMemberTimes(ctx, member.Id, "")
		if count > 0 {
			err = codes.NewError(codes.LimitFirstExchange)
			return nil, err
		}
	}

	temp := ProductCodeRewardRule{}
	err = copier.Instance(nil).From(self).CopyTo(&temp)
	if err != nil {
		return nil, err
	}
	temp.Rewards = []ProductCodeRewardRuleReward{}
	rewards := self.Rewards
	if len(rewards) == 0 {
		rewards = append(rewards, self.Reward)
	}
	for _, reward := range rewards {
		temp.Reward = reward
		rewarderImpl, err = temp.getRewarder(ctx, member, params)
		if err != nil {
			resultErr = err
			continue
		}
		result, err = rewarderImpl.send(ctx)
		if err != nil {
			resultErr = err
			continue
		}
		result.ProductCodeRewardId = self.Id.Hex()
		results = append(results, result)
	}
	return results, resultErr
}

func (self *ProductCodeRewardRule) BasicCheck(ctx context.Context, member *pb_member.MemberDetailResponse, params *RewarderParams) error {
	if !self.IsEnabled {
		return codes.NewError(codes.RuleNotEnable)
	}

	if !self.ValidDate.Permanent && time.Now().Before(self.ValidDate.BeginTime) {
		extra := map[string]interface{}{
			"startTime": self.ValidDate.BeginTime.Unix(),
		}
		return codes.NewErrorWithExtra(codes.ActivityNotStarted, extra)
	}

	if !self.CheckTags(member.Tags) {
		return codes.NewError(codes.TagsNotMatch)
	}

	if !self.ValidDate.Permanent && time.Now().After(self.ValidDate.EndTime) {
		extra := map[string]interface{}{
			"endTime": self.ValidDate.EndTime.Unix(),
		}
		return codes.NewErrorWithExtra(codes.ActivityHasEnded, extra)
	}

	if self.NeedAuthorizePosition {
		if util.IsZeroFloat(params.Longitude) && util.IsZeroFloat(params.Latitude) {
			return codes.NewError(codes.NeedAuthorizePosition)
		}
	}

	return nil
}

func (self *ProductCodeRewardRule) CheckTags(tags []string) bool {
	if 0 == len(self.LimitMemberTags) {
		return true
	}

	for _, tag := range self.LimitMemberTags {
		if util.StrInArray(tag, &tags) {
			return true
		}
	}

	return false
}

func (self *ProductCodeRewardRule) CheckLimitMemberCount(ctx context.Context, receivedCount uint64) bool {
	selector := bson.M{"_id": self.Id}

	// In order to prevent the high concurrency error
	// Inc 1 to exchangedMemberCount and exchangedProductCodeCount field to mark the current user is receiving rewards
	// Notes: if this user doesn't receive this rule reward, you need to roll back the data
	// You can call RollbackLimitMemberCount to rollback
	incCondition := bson.M{
		"exchangedProductCodeCount": 1,
	}
	isFirstExchange := 0 == receivedCount
	if isFirstExchange {
		incCondition["exchangedMemberCount"] = 1
	}

	change := qmgo.Change{
		Update: bson.M{
			"$inc": incCondition,
		},
		ReturnNew: true,
	}
	extension.DBRepository.FindAndApply(ctx, C_PRODUCT_CODE_REWARD_RULE, selector, []string{}, change, self)
	if 0 == self.LimitMemberCount || !isFirstExchange {
		return true
	}

	return self.ExchangedMemberCount <= self.LimitMemberCount
}

func (self *ProductCodeRewardRule) RollbackLimitMemberCount(ctx context.Context, receivedCount uint64) {
	selector := bson.M{"_id": self.Id}
	incCondition := bson.M{
		"exchangedProductCodeCount": -1,
	}
	if 0 == receivedCount {
		incCondition["exchangedMemberCount"] = -1
	}

	updator := bson.M{
		"$inc": incCondition,
	}

	extension.DBRepository.UpdateOne(ctx, C_PRODUCT_CODE_REWARD_RULE, selector, updator)
}

func (self *ProductCodeRewardRule) SetTagsToMember(ctx context.Context, receivedCount uint64, member *pb_member.MemberDetailResponse) {
	tags := []string{}
	if len(self.FirstTimeTags) > 0 && 0 == receivedCount {
		tags = self.FirstTimeTags
	}

	if len(self.SecondTimeTags) > 0 && receivedCount >= 1 {
		tags = self.SecondTimeTags
	}

	needAddTags := util.StrArrayDiff(tags, member.Tags)
	if len(needAddTags) > 0 {
		mairpc.RPCProxy.Call(
			getMemberServiceClient,
			"MemberService.AddTags",
			ctx,
			&pb_member.MemberTags{
				Ids:  []string{member.Id},
				Tags: tags,
			},
		)
	}
}

func (*ProductCodeRewardRule) GetOneByCondition(ctx context.Context, condition bson.M) (*ProductCodeRewardRule, error) {
	rule := &ProductCodeRewardRule{}
	err := Common.GetOneByCondition(ctx, condition, C_PRODUCT_CODE_REWARD_RULE, rule)
	if err != nil {
		return nil, err
	}

	return rule, nil
}
