package model

import (
	"context"
	"time"

	"mairpc/service/account/model"

	"mairpc/core/extension"
	core_util "mairpc/core/util"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"
)

const (
	C_PRODUCT_BRAND = "productBrand"
)

var CProductBrand = &ProductBrand{}

type ProductBrand struct {
	Id           bson.ObjectId `bson:"_id,omitempty"`
	AccountId    bson.ObjectId `bson:"accountId"`
	Name         string        `bson:"name"`
	Description  string        `bson:"description"`
	Logo         string        `bson:"logo"`
	ProductCount uint64        `bson:"productCount"`
	CreatedBy    bson.ObjectId `bson:"createdBy,omitempty"`
	CreatedAt    time.Time     `bson:"createdAt"`
	UpdatedBy    bson.ObjectId `bson:"updatedBy,omitempty"`
	UpdatedAt    time.Time     `bson:"updatedAt"`
	IsDeleted    bool          `bson:"isDeleted"`
	DivideType   string        `bson:"divideType,omitempty"` // 当前品牌分账类型，afterPaid（支付后立即分账），默认为空，表示使用全局设置
}

func (*ProductBrand) GetByName(ctx context.Context, name string) *ProductBrand {
	accountId := util.GetAccountIdAsObjectId(ctx)
	condition := bson.M{
		"accountId": accountId,
		"name":      name,
		"isDeleted": false,
	}
	var result *ProductBrand
	model.Common.GetOneByCondition(ctx, condition, C_PRODUCT_BRAND, &result)

	return result
}

func (p *ProductBrand) Create(ctx context.Context) error {
	now := time.Now()
	p.CreatedAt = now
	p.UpdatedAt = now
	p.Id = bson.NewObjectId()
	p.AccountId = util.GetAccountIdAsObjectId(ctx)
	operatorId := core_util.GetUserId(ctx)
	if bson.IsObjectIdHex(operatorId) {
		p.CreatedBy = bson.ObjectIdHex(operatorId)
		p.UpdatedBy = bson.ObjectIdHex(operatorId)
	}

	_, err := extension.DBRepository.Insert(ctx, C_PRODUCT_BRAND, p)
	return err
}

func (p *ProductBrand) Update(ctx context.Context) error {
	accountId := util.GetAccountIdAsObjectId(ctx)
	operatorId := core_util.GetUserId(ctx)

	selector := bson.M{
		"_id":       p.Id,
		"accountId": accountId,
		"isDeleted": false,
	}
	setCondition := bson.M{
		"name":        p.Name,
		"description": p.Description,
		"logo":        p.Logo,
		"updatedAt":   time.Now(),
		"divideType":  p.DivideType,
	}
	if bson.IsObjectIdHex(operatorId) {
		setCondition["updatedBy"] = bson.ObjectIdHex(operatorId)
	}
	updater := bson.M{
		"$set": setCondition,
	}

	return extension.DBRepository.UpdateOne(ctx, C_PRODUCT_BRAND, selector, updater)
}

func (*ProductBrand) Delete(ctx context.Context, id string) error {
	accountId := util.GetAccountIdAsObjectId(ctx)
	operatorId := core_util.GetUserId(ctx)

	selector := bson.M{
		"accountId": accountId,
		"_id":       bson.ObjectIdHex(id),
	}
	setCondition := bson.M{
		"isDeleted": true,
		"updatedAt": time.Now(),
	}
	if bson.IsObjectIdHex(operatorId) {
		setCondition["updatedBy"] = bson.ObjectIdHex(operatorId)
	}
	updater := bson.M{
		"$set": setCondition,
	}
	return extension.DBRepository.UpdateOne(ctx, C_PRODUCT_BRAND, selector, updater)
}

func (*ProductBrand) GetByIds(ctx context.Context, ids ...bson.ObjectId) ([]ProductBrand, error) {
	results := []ProductBrand{}
	if len(ids) == 0 {
		return results, nil
	}

	selector := bson.M{
		"_id":       bson.M{"$in": ids},
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}

	err := extension.DBRepository.FindAll(ctx, C_PRODUCT_BRAND, selector, []string{}, 0, &results)

	return results, err
}
