package constant

import "fmt"

const (
	// channel origins
	WECHAT         = "wechat"
	WEAPP          = "weapp"
	WESHOP         = "weshop"
	WEWEBSITE      = "wewebsite"
	WEIBO          = "weibo"
	ALIPAY         = "alipay"
	PORTAL         = "portal"
	QQ             = "qq"
	APP_ANDROID    = "app:android"
	APP_IOS        = "app:ios"
	APP_WEB        = "app:web"
	APP_WEBVIEW    = "app:webview"
	APP_GOOGLE     = "app:google"
	APP_APPLE      = "app:apple"
	APP_FACEBOOK   = "app:facebook"
	OTHERS         = "others"
	FACEBOOK       = "facebook"
	TMALL          = "tmall"
	WDT            = "wdt"
	YOUZAN         = "youzan"
	WEIMOB         = "weimob"
	SHUYUN         = "shuyun"
	TAOBAO         = "taobao"
	TAOBAO_MEMBER  = "taobao:member" // 淘宝会员通
	TAOBAO_COUPON  = "taobao:coupon" // 淘宝三方码
	ALIPAY_ACCOUNT = "alipay:account"
	HELPDESK       = "helpdesk"
	STORE          = "store"
	WDT_PREFIX     = "wdt:"
	JD             = "jd"
	JD_POP         = "jd:pop"
	WECONTACT      = "wecontact" // 企业微信外部联系人
	WEWORKAPP      = "weworkapp" // 企业小程序应用
	BAIDU          = "baidu"
	DOUYIN         = "douyin"
	JD_MEMBER      = "jd:member" // 京东会员通
	JD_COUPON      = "jd:coupon" // 京东三方码
	ALIPAYAPP      = "alipayapp"
	BYTEDANCEAPP   = "bytedanceapp"
	DOUYIN_MEMBER  = "douyin:member" // 抖店会员通
	DOUYINLIFE     = "douyinlife"    // 抖音本地生活
	DOUYINACCOUNT  = "douyinaccount" // 抖音移动或网页应用授权渠道
	DOUYINJS       = "douyinjs"      // 抖音 js 授权
	POS            = "pos"
	MEITUAN        = "meituan"
	MEITUAN_COUPON = "meituan:coupon"
	MEITUAN_MEMBER = "meituan:member" // 美团闪购会员通
	MEITUANLIFE    = "meituanlife"    // 美团生活服务
	JUZIBOT        = "juzibot"
	ELEME          = "eleme" // 饿了么
	WHATS_APP      = "whatsapp"
	APP_LINE       = "app:line"   // line App
	LINE_LOGIN     = "line:login" // line Login

	// channel businesses
	YOUZAN_STORE = "youzan:store"
)

const (
	// Reference to https://bytedance.feishu.cn/docx/RWn0dlsSHoAMUvxZ3k1ceLdLnNe
	DOUYIN_HASH_MOBILE_SALT = "9b29tB_g6jAhrK43n7FUdg=="
	DOUYIN_MASK_MOBILE_SALT = "dljZ-AzQoynb0075wOsQXQ=="
)

var Origins = []string{
	WECHAT,
	WESHOP,
	WEIBO,
	ALIPAY,
	PORTAL,
	QQ,
	APP_ANDROID,
	APP_IOS,
	APP_WEB,
	APP_WEBVIEW,
	OTHERS,
	FACEBOOK,
	TMALL,
	WDT,
	YOUZAN,
	SHUYUN,
	WEIMOB,
	TAOBAO,
	TAOBAO_MEMBER,
	TAOBAO_COUPON,
	JD_MEMBER,
	JD_COUPON,
	ALIPAY_ACCOUNT,
	WEAPP,
	HELPDESK,
	STORE,
	JD,
	JD_POP,
	WECONTACT,
	WEWEBSITE,
	WEWORKAPP,
	BAIDU,
	DOUYIN,
	ALIPAYAPP,
	BYTEDANCEAPP,
	DOUYIN_MEMBER,
	DOUYINLIFE,
	DOUYINACCOUNT,
	DOUYINJS,
	POS,
	MEITUAN,
	MEITUAN_COUPON,
	MEITUAN_MEMBER,
	MEITUANLIFE,
	JUZIBOT,
	APP_LINE,
	APP_GOOGLE,
	APP_APPLE,
	APP_FACEBOOK,
	ELEME,
	WHATS_APP,
	APP_LINE,
}

const (
	BUSINESS_WECHATWORK = "wechatwork"
	BUSINESS_RETAIL     = "retail"
	BUSINESS_PORTAL     = "portal"

	WX_CARD = "wxCard"
)

var OriginNameMap = map[string]string{
	WECHAT:         "服务号",
	WEIBO:          "微博",
	ALIPAY:         "支付宝",
	PORTAL:         "SCRM后台",
	QQ:             "QQ",
	APP_ANDROID:    "Android App",
	APP_IOS:        "iOS App",
	APP_WEB:        "网站",
	APP_WEBVIEW:    "手机版网站",
	OTHERS:         "其他",
	TAOBAO:         "淘宝",
	TAOBAO_MEMBER:  "淘宝会员通",
	TAOBAO_COUPON:  "淘宝三方码",
	JD_MEMBER:      "京东会员通",
	JD_COUPON:      "京东三方码",
	MEITUAN_MEMBER: "美团会员通",
	MEITUANLIFE:    "美团生活服务会员通",
	MEITUAN_COUPON: "美团团购券",
	DOUYIN:         "抖音",
	DOUYIN_MEMBER:  "抖店会员通",
	DOUYINLIFE:     "抖音本地生活",
	DOUYINACCOUNT:  "抖音网站应用",
	DOUYINJS:       "抖音H5",
	WESHOP:         "微信小店",
	WEAPP:          "小程序",
	JD:             "京东",
	APP_LINE:       "Line",
	APP_APPLE:      "Apple",
	APP_GOOGLE:     "Google",
	APP_FACEBOOK:   "Facebook",
}

var OriginNamePrefixMap = map[string]string{
	WEAPP:  "小程序",
	WECHAT: "公众号",
	WESHOP: "微信小店",
}

var ActivationSourcePrefixMap = map[string]string{
	TAOBAO_MEMBER:  "tmall_",
	JD_MEMBER:      "jd_",
	YOUZAN:         "youzan_",
	DOUYIN_MEMBER:  "douyin_",
	DOUYINLIFE:     "douyinlife_",
	DOUYINACCOUNT:  "douyinaccount_",
	DOUYINJS:       "douyinjs_",
	MEITUAN_MEMBER: "meituan_",
	WEAPP:          "mp_",
	WECHAT:         "oa_",
	WESHOP:         "weshop_",
	MEITUANLIFE:    "meituanlife_",
}

func GenActivationSource(origin, scene string) string {
	if scene == "" {
		if origin == WEAPP {
			return ""
		}
		scene = "others"
	}
	prefix := ActivationSourcePrefixMap[origin]
	if prefix == "" {
		return ""
	}
	return fmt.Sprintf("%s%s", prefix, scene)
}

var SystemPropertyNameMap = map[string]string{
	"name":           "姓名",
	"birthday":       "生日",
	"mobile":         "手机",
	"address":        "地址",
	"email":          "邮箱",
	"industry":       "行业",
	"education":      "学历",
	"sign":           "星座",
	"position":       "职位",
	"idcard":         "身份证",
	"img":            "头像",
	"gender":         "性别",
	"nation":         "民族",
	"income":         "收入",
	"notDisturb":     "是否免打扰",
	"belongingStore": "归属门店",
}
