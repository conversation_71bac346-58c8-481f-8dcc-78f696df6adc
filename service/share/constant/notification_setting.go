package constant

const (
	SMS_TOPIC       = "群脉电商"
	OPERATOR_SYSTEM = "system"

	// 买家通知消息类型
	MESSAGE_TYPE_ORDER        = "order"
	MESSAGE_TYPE_AFTER_SALES  = "afterSales"
	MESSAGE_TYPE_COUPON       = "coupon"
	MESSAGE_TYPE_GROUPON      = "groupon"
	MESSAGE_TYPE_DISTRIBUTION = "distribution"

	// 商家通知消息类型
	MESSAGE_TYPE_STORE       = "store"
	MESSAGE_TYPE_CONSIGNMENT = "consignment"

	// 买家通知推送规则
	MESSAGE_RULE_ORDER_AUTO_CLOSED       = "orderAutoClosed"
	MESSAGE_RULE_ORDER_PAID              = "orderPaid"
	MESSAGE_RULE_ORDER_PICK_UP           = "orderPickup"
	MESSAGE_RULE_ORDER_HAD_PICK_UP       = "orderHadPickup"
	MESSAGE_RULE_ORDER_PICKUP_INCOMPLETE = "orderPickupIncomplete"
	MESSAGE_RULE_ORDER_SHIPPED           = "orderShipped"

	MESSAGE_RULE_REFUND_APPLIED                 = "refundApplied"
	MESSAGE_RULE_ORDER_CANCELLED_BY_STAFF       = "orderCancelledByStaff"
	MESSAGE_RULE_REFUND_BY_STAFF                = "refundByStaff"
	MESSAGE_RULE_REFUND_AGREED                  = "refundAgreed"
	MESSAGE_RULE_REFUND_REFUSED                 = "refundRefused"
	MESSAGE_RULE_REFUND_AND_RETURN_GOODS_AGREED = "refundAndReturnGoodsAgreed"
	MESSAGE_RULE_GOODS_RECEIVED                 = "goodsReceived"

	MESSAGE_RULE_COUPONS_RECEIVED            = "couponsReceived"
	MESSAGE_RULE_COUPONS_EXPIRED             = "couponsExpired"
	MESSAGE_RULE_FORWARDING_COUPONS_RECEIVED = "forwardingCouponsReceived"

	MESSAGE_RULE_CAMPAIGN_WILL_SRART = "campaignWillStart"

	MESSAGE_RULE_GROUPON_SUCCESS = "grouponSuccess"
	MESSAGE_RULE_GROUPON_FAILURE = "grouponFailure"

	MESSAGE_RULE_BALANCE_WILL_START = "balanceWillStart"
	MESSAGE_RULE_BALANCE_WILL_END   = "balanceWillEnd"

	MESSAGE_RULE_MEMBER_REGISTERED         = "memberRegistered"
	MESSAGE_RULE_MEMBER_UPGRADED           = "memberUpgraded"
	MESSAGE_RULE_COMMISSION_RECEIVED       = "commissionReceived"
	MESSAGE_RULE_GUIDE_MEMBER_ACTIVATED    = "guideMemberActivated"
	MESSAGE_RULE_GUIDE_COMMISSION_RECEIVED = "guideCommissionReceived"
	MESSAGE_RULE_COMMISSION_WAIT_CONFIRM   = "commissionWaitConfirm"

	MESSAGE_RULE_INVITER_COMPLETED_TASK = "inviterCompletedTask"
	MESSAGE_RULE_INVITEE_COMPLETED_TASK = "inviteeCompletedTask"

	STORED_VALUE_CARD_RECEIVED    = "storedValueCardReceived"
	STORED_VALUE_CARD_UNCOLLECTED = "storedValueCardUncollected"
	STORED_VALUE_CARD_EXPIRED     = "storedValueCardExpired"

	REDEEM_CARD_RECEIVED    = "redeemCardReceived"
	REDEEM_CARD_UNCOLLECTED = "redeemCardUncollected"
	REDEEM_CARD_EXPIRED     = "redeemCardExpired"

	// 商家通知推送规则
	MESSAGE_RULE_NEW_ORDERS_ASSIGNED           = "newOrdersAssigned"
	MESSAGE_RULE_PART_REFUNDED                 = "partRefunded"
	MESSAGE_RULE_ALL_REFUNDED                  = "allRefunded"
	MESSAGE_RULE_UNSHIPPED_PARTIAL_REFUNDED    = "unshippedPartialRefunded"
	MESSAGE_RULE_UNSHIPPED_ALL_REFUNDED        = "unshippedAllRefunded"
	MESSAGE_RULE_SHIPPED_APPLY_REFUND          = "shippedApplyRefund"
	MESSAGE_RULE_SHIPPED_APPLY_REFUND_CANCEL   = "shippedApplyRefundCancel"
	MESSAGE_RULE_UNSHIPPED_APPLY_REFUND_CANCEL = "unshippedAllRefundedCancel"

	MESSAGE_RULE_CONSIGNMENT_APPLICATION_SUCCEEDED = "consignmentApplicationSuccess"
	MESSAGE_RULE_CONSIGNMENT_COMMISSION_RECEIVED   = "consignmentCommissionReceived"
	MESSAGE_RULE_CONSIGNMENT_APPLICATION_FAILED    = "consignmentApplicationFailed"

	// 会员通知规则
	MESSAGE_RULE_MEMBER_ACTIVATED         = "memberActivated"
	MESSAGE_RULE_MEMBER_LEVEL_UPGRADED    = "memberLevelUpgraded"
	MESSAGE_RULE_SCORE_RECEIVED           = "scoreReceived"
	MESSAGE_RULE_SCORE_WILL_EXPIRE        = "scoreWillExpire"
	MESSAGE_RULE_PAID_MEMBER_ACTIVATED    = "paidMemberActivated"
	MESSAGE_RULE_BENEFIT_CARD_WILL_EXPIRE = "benefitCardWillExpire"
)

var (
	RuleSlice = []string{
		"orderAutoClosed",
		"orderPaid",
		"orderPickup",
		"orderHadPickup",
		"orderPickupIncomplete",
		"orderShipped",
		"refundApplied",
		"orderCancelledByStaff",
		"refundByStaff",
		"refundAgreed",
		"refundRefused",
		"refundAndReturnGoodsAgreed",
		"goodsReceived",
		"couponsReceived",
		"couponsExpired",
		"forwardingCouponsReceived",
		"campaignWillStart",
		"grouponSuccess",
		"grouponFailure",
		"bargainWillBegin",
		"bargainStockNotEnough",
		"bargainWillOvertime",
		"bargainFloorPrice",
		"balanceWillStart",
		"balanceWillEnd",
		"memberRegistered",
		"memberUpgraded",
		"commissionReceived",
		"guideMemberActivated",
		"guideCommissionReceived",
		"newOrdersAssigned",
		"partRefunded",
		"allRefunded",
		"unshippedPartialRefunded",
		"unshippedAllRefunded",
		"unshippedAllRefundedCancel",
		"shippedApplyRefund",
		"shippedApplyRefundCancel",
		"consignmentApplicationSuccess",
		"consignmentApplicationFailed",
		"consignmentCommissionReceived",
		"commissionWaitConfirm",
		"inviterCompletedTask",
		"inviteeCompletedTask",
		"lastPeriodShipped",
		"memberActivated",
		"memberLevelUpgraded",
		"scoreReceived",
		"scoreWillExpire",
		"paidMemberActivated",
		"benefitCardWillExpire",
		"storedValueCardReceived",
		"storedValueCardUncollected",
		"storedValueCardExpired",
		"redeemCardReceived",
		"redeemCardUncollected",
		"redeemCardExpired",
		"staffTaskUrge",
		"staffTaskStart",
	}

	// 订阅消息参数限制
	PARAMETERS_LIMIT_MAP = map[string]int{
		"thing":            20,
		"number":           32,
		"letter":           32,
		"symbol":           5,
		"character_string": 32,
		"phone_number":     17,
		"car_number":       8,
		"name":             10,
		"phrase":           5,
	}
)
