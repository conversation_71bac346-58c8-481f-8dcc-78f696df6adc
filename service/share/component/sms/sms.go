package sms

import (
	"context"
	"mairpc/core/extension/bson"
	"mairpc/proto/account"
	"time"
)

const (
	SEND_SMS_SUCCESS_CODE = "OK"

	SMS_SEND_STATUS_PENDING = "PENDING"
	SMS_SEND_STATUS_SUCCEED = "SUCCEED"
	SMS_SEND_STATUS_FAILED  = "FAILED"

	SMS_PROVIDER_ALIYUN   = "aliyun"
	SMS_PROVIDER_PICC     = "picc"
	SMS_PROVIDER_TENCENT  = "tencent"
	SMS_PROVIDER_CN_NICE  = "cnnice"
	SMS_PROVIDER_ZRWINFO  = "zrwinfo"
	SMS_PROVIDER_HISENSE  = "hisense"
	SMS_PROVIDER_BIOSTIME = "biostime"
)

type SmsResponse struct {
	RequestId   string    `bson:"requestId"`
	Code        string    `bson:"code"`
	Message     string    `bson:"message"`
	BizId       string    `bson:"bizId"`
	SendStatus  string    `bson:"sendStatus"`
	ErrCode     string    `bson:"errCode,omitempty"`
	SendDate    time.Time `bson:"sendDate,omitempty"`
	ReceiveDate time.Time `bson:"receiveDate,omitempty"`
	BillCount   int       `bson:"billCount,omitempty"`
	// 此字段仅用作某些短信发送参数需要和 SmsSendLog.Id 关联，用作传递，不存入 db
	SendLogId bson.ObjectId `bson:"-"`
}

type SmsSender interface {
	Send(ctx context.Context, req *account.SendSmsRequest) (*SmsResponse, error)
}

func (self *SmsResponse) SetSmsSendStatus() {
	self.SendStatus = SMS_SEND_STATUS_PENDING
	if self.Code != SEND_SMS_SUCCESS_CODE {
		self.SendStatus = SMS_SEND_STATUS_FAILED
	}
}

func (self *SmsResponse) SetSmsSendFinalStatus() {
	self.SendStatus = SMS_SEND_STATUS_SUCCEED
	if self.Code != SEND_SMS_SUCCESS_CODE {
		self.SendStatus = SMS_SEND_STATUS_FAILED
	}
}
