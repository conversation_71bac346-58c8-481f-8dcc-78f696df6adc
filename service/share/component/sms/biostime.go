package sms

import (
	"context"
	"encoding/json"
	"fmt"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/account"
	"net/url"
)

const (
	BIOSTIME_SMS_SERVICE_NAME = "biostimesms"
	BIOSTIME_SMS_SEND         = "/sms/Api/ReturnJson/Send.do"
)

type BiostimeSmsSender struct {
	SpCode    string
	LoginName string
	Password  string
}

type sendBiostimeSmsRequest struct {
	SpCode         string `json:"SpCode"`
	LoginName      string `json:"LoginName"`
	Password       string `json:"Password"`
	MessageContent string `json:"MessageContent"`
	UserNumber     string `json:"UserNumber"`
	SerialNumber   string `json:"SerialNumber"`
}

type sendBiostimeSmsResponse struct {
	Result      string `json:"result"`
	Description string `json:"description"`
	TaskId      string `json:"taskid"`
}

func (req *sendBiostimeSmsRequest) genEncodedValues() string {
	values := url.Values{}
	reqMap := make(map[string]string)
	core_util.CopyByJson(req, &reqMap)
	for k, v := range reqMap {
		values.Set(k, v)
	}
	return values.Encode()
}

func (b *BiostimeSmsSender) Send(ctx context.Context, req *account.SendSmsRequest) (*SmsResponse, error) {
	sendReq := sendBiostimeSmsRequest{
		SpCode:         b.SpCode,
		LoginName:      b.LoginName,
		Password:       b.Password,
		MessageContent: fmt.Sprintf("【%s】%s", req.SmsTopic, req.Text),
		UserNumber:     req.Phone,
		SerialNumber:   req.BusinessId,
	}
	body, _, err := extension.RequestClient.PostFormData(log.SwitchOnResponseBodyLog(ctx), BIOSTIME_SMS_SERVICE_NAME, BIOSTIME_SMS_SEND, sendReq.genEncodedValues(), nil)
	if err != nil {
		return nil, err
	}
	resp := new(sendBiostimeSmsResponse)
	err = json.Unmarshal(body, resp)
	if err != nil {
		return nil, err
	}
	result := &SmsResponse{
		RequestId: resp.TaskId,
		Message:   resp.Description,
		SendLogId: bson.NewObjectId(),
	}
	if resp.Result == "0" {
		result.Code = SEND_SMS_SUCCESS_CODE
	}
	result.SetSmsSendFinalStatus()
	return result, nil
}
