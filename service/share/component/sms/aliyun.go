package sms

import (
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"mairpc/core/extension"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/proto/account"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"

	_ "github.com/alibabacloud-go/dysmsapi-********/v2/client"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk"
	"github.com/aliyun/alibaba-cloud-sdk-go/sdk/requests"
	"github.com/spf13/cast"
)

var (
	ALIYUN_SMS_SERVICE_NAME = "aliyunsms"

	NOTIFICATION_TEMPLATE_CODE = "SMS_489875643"
	CAPTCHA_TEMPLATE_CODE      = "SMS_489880566"
	MARKETING_TEMPLATE_CODE    = "SMS_489635588"
	GAT_TEMPLATE_CODE          = "SMS_157447300" // 港澳台

	NORTH_AMERICAN_PHONE = `^\+1\-?[0-9]{5,14}$`

	SMS_TEMPLATE_CODE_MAP = map[account.SendSmsRequest_TemplateType]string{
		account.SendSmsRequest_NOTICE:            NOTIFICATION_TEMPLATE_CODE,
		account.SendSmsRequest_VERIFICATION_CODE: CAPTCHA_TEMPLATE_CODE,
		account.SendSmsRequest_MARKETING:         MARKETING_TEMPLATE_CODE,
		account.SendSmsRequest_HK_MAC_TW_INT:     GAT_TEMPLATE_CODE,
	}
	GLOBE_SMS_TYPE_MAP = map[account.SendSmsRequest_TemplateType]string{
		account.SendSmsRequest_NOTICE:            "NOTIFY",
		account.SendSmsRequest_VERIFICATION_CODE: "OTP",
		account.SendSmsRequest_MARKETING:         "MKT",
	}
)

type AliyunSmsSender struct {
	AccessKey     string
	AccessSecret  string
	TemplateCodes map[string]string
}

type AliyunSmsRequest map[string]string

type AliyunSmsToGlobeResponse struct {
	RequestId string `json:"requestId"`
	Code      string `json:"code"`
	BizId     string `json:"messageId"`
	BillCount string `json:"segments,omitempty"`
}

func (self *AliyunSmsSender) Send(ctx context.Context, smsReq *account.SendSmsRequest) (*SmsResponse, error) {
	var err error
	smsResp := &SmsResponse{}
	if regexp.MustCompile(NORTH_AMERICAN_PHONE).MatchString(smsReq.Phone) {
		smsResp, err = self.SmsToNorthAmerica(ctx, smsReq)
	} else {
		req := self.newSmsRequest(smsReq.Phone, smsReq.SmsTopic, smsReq.Text, self.GetTemplateCode(smsReq.MessageType))
		queryStr := fmt.Sprintf("?Signature=%s&%s", req.sign(self.GetAccessSecret()), req.sort())
		var resp []byte
		resp, _, err = extension.RequestClient.Get(ctx, "", "http://dysmsapi.aliyuncs.com"+queryStr, nil, nil)
		if err != nil {
			return nil, err
		}
		err = xml.Unmarshal(resp, smsResp)
		smsResp.SetSmsSendStatus()
	}
	if err != nil {
		return nil, err
	}
	return smsResp, nil
}

func (self *AliyunSmsSender) newSmsRequest(phone, signName, text, templateCode string) AliyunSmsRequest {
	if strings.ContainsAny(phone, "-+") {
		templateCode = GAT_TEMPLATE_CODE
		phone = strings.Replace(phone, "-", "", -1)
		phone = strings.Replace(phone, "+", "", -1)
	}

	request := AliyunSmsRequest{
		"SignatureMethod":  "HMAC-SHA1",
		"SignatureNonce":   core_util.GenUniqueId(),
		"AccessKeyId":      self.GetAccessKey(),
		"SignatureVersion": "1.0",
		"Timestamp":        time.Now().UTC().Format(time.RFC3339),
		"Action":           "SendSms",
		"Version":          "2017-05-25",
		"RegionId":         "cn-hangzhou",
		"PhoneNumbers":     phone,
		"SignName":         signName,
		"TemplateCode":     templateCode,
	}

	textMap := map[string]string{
		"msg": text,
	}

	textMapString, _ := json.Marshal(textMap)
	request["TemplateParam"] = string(textMapString)

	return request
}

func (self *AliyunSmsSender) SmsToNorthAmerica(ctx context.Context, smsReq *account.SendSmsRequest) (*SmsResponse, error) {
	client, err := sdk.NewClientWithAccessKey("cn-hangzhou", self.GetAccessKey(), self.GetAccessSecret())
	if err != nil {
		return nil, err
	}
	phone := smsReq.Phone
	if strings.ContainsAny(phone, "-+") {
		phone = strings.Replace(phone, "-", "", -1)
		phone = strings.Replace(phone, "+", "", -1)
	}
	domain := extension.RequestClient.GetFullUrl(ALIYUN_SMS_SERVICE_NAME, "")
	if strings.HasPrefix(domain, "https") {
		domain = strings.Replace(domain, "https://", "", 1)
	} else {
		domain = strings.Replace(domain, "http://", "", 1)
	}

	req := requests.NewCommonRequest()
	req.Method = "POST"
	req.Scheme = "https"
	req.Version = "2017-05-25"
	req.Domain = domain
	req.ApiName = "SendMessageToGlobe"
	req.QueryParams["To"] = phone
	req.QueryParams["From"] = "***********"
	req.QueryParams["Message"] = fmt.Sprintf("[%s]%s", smsReq.SmsTopic, smsReq.Text)
	req.QueryParams["Type"] = func() string {
		if globeSmsType, ok := GLOBE_SMS_TYPE_MAP[smsReq.MessageType]; ok {
			return globeSmsType
		}
		return GLOBE_SMS_TYPE_MAP[account.SendSmsRequest_NOTICE]
	}()

	resp, err := client.ProcessCommonRequest(req)
	if err != nil {
		return nil, err
	}
	result := &AliyunSmsToGlobeResponse{}
	err = json.Unmarshal(resp.GetHttpContentBytes(), &result)
	if err != nil {
		return nil, err
	}
	return formatAliyunSmsToGlobeResponse(result), nil
}

func formatAliyunSmsToGlobeResponse(resp *AliyunSmsToGlobeResponse) *SmsResponse {
	result := &SmsResponse{}
	copier.Instance(nil).From(resp).CopyTo(result)
	result.BillCount = cast.ToInt(resp.BillCount)
	if result.Code == "OK" {
		result.SendStatus = SEND_SMS_SUCCESS_CODE
	} else {
		result.SendStatus = SMS_SEND_STATUS_FAILED
	}
	return result
}

func (s AliyunSmsRequest) sort() string {
	keys := make([]string, 0, len(s))
	for key := range s {
		keys = append(keys, key)
	}

	sort.Strings(keys)
	var queryString string
	for _, key := range keys {
		queryString = fmt.Sprintf("%s&%s=%s", queryString, key, specialUrlEncode(s[key]))
	}

	return queryString[1:]
}

func (s AliyunSmsRequest) sign(secret string) string {
	mac := hmac.New(sha1.New, []byte(secret+"&"))
	stringToSign := fmt.Sprintf("GET&%s&%s", specialUrlEncode("/"), specialUrlEncode(s.sort()))
	mac.Write([]byte(stringToSign))
	sign := base64.StdEncoding.EncodeToString(mac.Sum(nil))
	return specialUrlEncode(sign)
}

func specialUrlEncode(s string) string {
	s = url.QueryEscape(s)
	specialReplaceMap := map[string]string{
		"+":   "%20",
		"*":   "%2A",
		"%7E": "~",
	}

	for key, value := range specialReplaceMap {
		s = strings.Replace(s, key, value, -1)
	}

	return s
}

func (s AliyunSmsSender) GetAccessKey() string {
	if s.AccessKey != "" {
		return s.AccessKey
	}
	return core_util.GetAliyunSmsAKId()
}

func (s AliyunSmsSender) GetAccessSecret() string {
	if s.AccessSecret != "" {
		return s.AccessSecret
	}
	return core_util.GetAliyunSmsAKSecret()
}

func (s AliyunSmsSender) GetTemplateCode(templateType account.SendSmsRequest_TemplateType) (code string) {
	if len(s.TemplateCodes) > 0 {
		code = s.TemplateCodes[cast.ToString(int32(templateType))]
	}
	if code == "" {
		code = SMS_TEMPLATE_CODE_MAP[templateType]
	}
	return
}
