package component

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mairpc/core/extension"
	"mairpc/core/log"
	"mairpc/service/share/util"
	"net/url"
	"os"
	"runtime"
	"strconv"
	"strings"
	"time"

	"github.com/parnurzeal/gorequest"
	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

const (
	CHANNEL_SERVICE_NAME                    = "weconnect"
	FOLLOWER_ID_URL                         = "/accounts/%s/users/%s"
	FOLLOWER_OPENID_URL                     = "/accounts/%s/users/originId/%s"
	FOLLOWER_UNOIN_URL                      = "/users/unionId/%s"
	OK_STATUS                               = 200
	NO_CONTENT_STATUS                       = 204
	PARAM_ERROR_STATUS                      = 412
	INTERNAL_ERROR_STATUS                   = 500
	USER_ID_LENGTH                          = 24
	CHANNELS_URL                            = "/accounts/accountIds/%s"
	CHANNEL_URL                             = "/accounts/%s"
	GET_COR_PCHAIN_SHARED_WEAPP_CHANNEL     = "/wechatcp/chainCrops/%s/sharedWeApp"
	QRCODES_URL                             = "/accounts/%s/qrcodes"
	QRCODE_URL                              = "/accounts/%s/qrcodes/%s"
	TPL_MESSAGE_URL                         = "/accounts/%s/templateMessages"
	SINGLE_TAGET_TPL_MSG_URL                = "/accounts/%s/templateMessage"
	MESSAGE_URL                             = "/accounts/%s/customerServiceMessages/user/%s"
	MASS_MESSAGE_URL                        = "/accounts/%s/massMessages"
	SUBSCRIBE_MESSAGE_URL                   = "/accounts/%s/subscribeMessage"
	MENUS_URL                               = "/accounts/%s/menus"
	UNIFIED_ORDER_URL                       = "/%s/orders"
	V2_UNIFIED_ORDER_URL                    = "/%s/v2/orders"
	PAY_SIGN_URL                            = "/%s/pay/sign"
	PAYMENT_CONFIGURATION_URL               = "/weixin/pay/configuration/quncrmAccountId/%s"
	JSSDK_TICKET_URL                        = "/accounts/%s/jsTicket"
	WECHATCP_JSSDK_JSTICKET                 = "/wechatcp/apps/%s/jsTicket"
	WECHATCP_JSSDK_TICKET_URL               = "/wechatcp/suites/%s/corps/%s/jsTicket"
	TEST_WECHATCP_JSSDK_TICKET_URL          = "/wechatcp/corps/%s/apps/%s/jsTicket"
	PICC_WECHATCP_JS_SIGNATURE_URL          = "/picc/wechatcp/apps/%s/jsSignature"
	SEND_REDPACK_URL                        = "/weixin/redpacks"
	GET_REDPACK_URL                         = "/weixin/redpacks/tradeNo/%s"
	GET_WECHAT_TOKEN_URL                    = "/accounts/%s/refreshToken"
	PRODUCE_MQ_MESSAGE                      = "/mq/produce"
	PRODUCE_CUSTOMER_EVENT                  = "/mq/customerEvent/produce"
	BENCHMARK_URL                           = "/benchmark"
	DECRYPT_MINI_PROGRAM_URL                = "/accounts/%s/miniprogram/dataDecrypt"
	ORDER_REFUND_URL                        = "/%s/refunds"
	V2_ORDER_REFUND_URL                     = "/%s/v2/refunds"
	ORDER_REFUND_STATUS_URL                 = "/%s/refunds/outRefundNo/%s"
	REPORT_WEAPP_FORMID_URL                 = "/accounts/%s/miniprogram/templates/formId"
	GET_TEMPLATE_MESSAGE                    = "/accounts/%s/templateMessage/%s"
	LIST_TEMPLATES                          = "/accounts/%s/templateMessages/templates"
	DELETE_PROFIT_SHARING_RECEIVER          = "/%s/profitSharingReceivers"
	CREATE_PROFIT_SHARING_RECEIVER          = "/%s/profitSharingReceivers"
	MULTI_PROFIT_SHARING                    = "/%s/profitSharings"
	GET_PROFIT_SHARING                      = "/%s/profitSharings"
	FINISH_PROFIT_SHARING                   = "/%s/profitSharings"
	GET_REFRESHED_PROFIT_SHARING            = "/%s/profitSharings/outOrderNo/%s"
	GET_WEAPP_VERSION                       = "/accounts/%s/miniprogram/currentVersion"
	GET_WEAPP_CHANGE_LOGS                   = "/accounts/%s/miniprogram/changeLogs"
	GET_WEAPP_QR_CODE                       = "/accounts/%s/miniprogram/wxaQrcodes"
	EXPRESS_URL_PREFIX                      = "/accounts/%s/express/"
	SEND_TRANSFER                           = "/%s/transfers"
	SEND_V2_TRANSFER                        = "/%s/v2/transfers"
	GET_TRANSFER                            = "/%s/transfers/tradeNo/%s"
	GET_V2_TRANSFER                         = "/%s/v2/transfers/tradeBatchNo/%s/tradeDetailNo/%s"
	LIST_DEPARTMENTS                        = "/wechatcp/apps/%s/departments"
	LIST_DEPARTMENTS_BY_ID                  = "/wechatcp/apps/%s/departments/%s"
	LIST_DEPARTMENT_USERS                   = "/wechatcp/apps/%s/users?departmentId=%s&fetchChild=%s"
	GET_USER                                = "/wechatcp/apps/%s/users/%s"
	CONTACT_WAYS                            = "/wechatcp/apps/%s/externalContact/contactWays"
	POST_CONTACT_WAYS                       = "/wechatcp/apps/%s/externalContact/group/contactWays?&pageNum=%s&pageSize=%s"
	GET_CONTACT_WAY                         = "/wechatcp/apps/%s/externalContact/contactWays/%s"
	GET_CONTACT_WAY_BY_STATE                = "/wechatcp/apps/%s/externalContact/contactWays/state/%s"
	SEARCH_CONTACT_WAY                      = "/wechatcp/contactWays/search"
	WELCOME_MESSAGES                        = "/wechatcp/apps/%s/externalContact/welcomeMessages"
	GROUP_WELCOME_TEMPLATE                  = "/wechatcp/apps/%s/externalContact/groupWelcomeTemplates"
	EDIT_GROUP_WELCOME_TEMPLATE             = "/wechatcp/apps/%s/externalContact/groupWelcomeTemplates/%s"
	LIST_CHATS                              = "/wechatcp/apps/%s/externalContact/groupChats"
	LIST_CHAT_MEMBERS                       = "/wechatcp/apps/%s//externalContact/groupChats/%s"
	LIST_STATS_CHATS                        = "/wechatcp/apps/%s/externalContact/groupChats/statistic"
	LIST_STATS_STAFF_CONTACT                = "/wechatcp/apps/%s/externalContact/userBehavior/statistic"
	GROUP_CHAT_JOIN_WAYS                    = "/wechatcp/apps/%s/externalContact/groupChat/joinWays"
	GET_GROUP_CHAT_JOIN_WAYS                = "/wechatcp/apps/%s/externalContact/groupChat/joinWays/%s"
	GET_CORP                                = "/wechatcp/corps/%s"
	LIST_TAGS                               = "/wechatcp/corps/%s/externalContact/tags"
	LIST_TAGS_BY_APPID                      = "/wechatcp/apps/%s/externalContact/tags"
	ADD_TAGS                                = "/wechatcp/corps/%s/externalContact/tags"
	EDIT_TAG                                = "/wechatcp/corps/%s/externalContact/tags"
	DELETE_TAGS                             = "/wechatcp/corps/%s/externalContact/tags"
	DELETE_TAGS_BY_APPID                    = "/wechatcp/apps/%s/externalContact/tags"
	EDIT_MEMBER_TAGS_BY_CORPID              = "/wechatcp/corps/%s/externalContact/tags/mark"
	GET_SUITE_APP_INFO                      = "/wechatcp/corps/%s/suites/%s"
	EDIT_MEMBER_TAGS_BY_APPID               = "/wechatcp/apps/%s/externalContact/tags/mark"
	ADD_WECHAT_TAG_TO_USERS                 = "/accounts/%s/users/bulkAddWxTag/v2"
	REMOVE_WECHAT_TAG_FROM_USERS            = "/accounts/%s/users/bulkRemoveWxTag"
	List_SYSTEM_EXTERNAL_USER               = "/wechatcp/apps/%s/externalUsers"
	GET_SYSTEM_EXTERNAL_USER                = "/wechatcp/apps/%s/externalUsers/%s"
	GET_EXTERNAL_USER                       = "/wechatcp/apps/%s/externalContact/externalUsers/%s"
	LIST_EXTERNAL_USER                      = "/wechatcp/apps/%s/externalContact/externalUsers"
	SYNC_EXTERNAL_USERS                     = "/wechatcp/apps/%s/externalUsers/sync"
	LIST_FOLLOW_EXTERNAL_USERS              = "/wechatcp/apps/%s/externalContact/followUsers/%s/externalUsers"
	EXTERNAL_MASS_MESSAGE                   = "/wechatcp/apps/%s/externalContact/massMessages"
	EXTERNAL_MASS_MESSAGE_RESULT            = "/wechatcp/apps/%s/externalContact/groupMessageResultList"
	EXTERNAL_MASS_MESSAGE_TASK_LIST         = "/wechatcp/apps/%s/externalContact/groupMessageTaskList"
	EXTERNAL_MASS_MESSAGE_TASK              = "/wechatcp/apps/%s/externalContact/massMessage/tasks"
	EXTERNAL_MASS_MESSAGE_TASK_RESULT       = "/wechatcp/apps/%s/externalContact/massMessage/tasks/%s"
	EXTERNAL_MASS_MESSAGE_TASK_CANCEL       = "/wechatcp/apps/%s/externalContact/massMessage/tasks/%s/cancel"
	CONVERT_TO_OPEN_ID                      = "/wechatcp/apps/%s/users/convertToOpenId"
	GET_APP_DETAIL                          = "/wechatcp/apps/%s/details"
	SEND_ALIYUNQA_SMS                       = "/aliyunqa/openapi/oem/simpleSms/sendSms"
	SEND_ALIYUNQA_DIGITAL_SMS               = "/aliyunqa/openapi/oem/simpleSms/sendDigitalSms"
	GET_ALIYUNQA_SMS                        = "/aliyunqa/openapi/oem/smscontent/smsDetail/%s"
	ALIYUNQA_PROXY_URL                      = "/aliyunqa/openapi/oem/proxy?serverEnvironment=%s&tenantId=%s&tenantRegion=%s&workspaceId=%s"
	BYTE_DANCE_PRE_PAY                      = "/byte_dance_app/orders"
	SEND_WELCOME_MESSAGE                    = "/wechatcp/apps/%s/externalContact/sendWelcomeMessage"
	DOWNLOAD_MEDIA_METERIAL                 = "/accounts/%s/materials/mediaType/%s/%s"
	DOWNLOAD_TEMPORARY_MEDIA_METERIAL       = "/accounts/%s/materials/temporary/mediaType/%s/%s"
	CREATE_TEMPORARY_MEDIA_METERIAL         = "/accounts/%s/materials/temporary/mediaType/%s"
	CREATE_GROUP_MESSAGES                   = "/wechatcp/apps/%s/externalContact/groupMessages"
	GET_PAYMENT_CONFIGURATION               = "/%s/pay/configuration/quncrmAccountId/%s"
	GET_PAYMENT_CONFIGURATION_BY_CHANNEL    = "/%s/pay/configuration/quncrmAccountId/%s/weconnectAccountId/%s"
	LIST_WECHATCP_ADMINS                    = "/wechatcp/apps/%s/admins"
	LIST_WECHATCP_AUTH_USERS                = "/wechatcp/apps/%s/users/authList"
	CONVERT_OPEN_GROUPID_TOCHATID           = "/accounts/%s/miniprogram/convertOpenGroupIdToChatId"
	UPDATE_WX_CARD                          = "/accounts/%s/memberCards/%s"
	WECONNECT_PROXY                         = "/accounts/%s/proxy"
	DELETE_USER                             = "/users"
	ACTIVE_CODE_COUNT                       = "/wechatcp/apps/%s/activeCode/count"
	CREATE_RENEW_USER_ORDER                 = "/wechatcp/apps/%s/renew/user/tasks"
	GET_RENEW_USER_RESULT                   = "/wechatcp/apps/%s/renew/user/tasks/%s"
	CREATE_ACTIVE_USER_TASK                 = "/wechatcp/apps/%s/active/user/tasks"
	Get_ACTIVE_USER_TASK_RESULT             = "/wechatcp/apps/%s/active/user/tasks/%s"
	TRANSFER_USER                           = "/wechatcp/apps/%s/transfer/users"
	GET_ACTIVE_USER_DETAIL                  = "/wechatcp/apps/%s/active/users/%s"
	SHARE_ACTIVE_CODE                       = "/wechatcp/apps/%s/share/activeCode/tasks"
	GET_SHARE_ACTIVE_CODE_RESULT            = "/wechatcp/apps/%s/share/activeCode/tasks/%s"
	CONVERT_CHAT_EXTERNAL_USERID_TO_UNIONID = "/wechatcp/apps/%s/externalContact/convertChatExternalUserIdToUnionId"
	WECONNECT_V2_PROXY                      = "/%s/v2/proxy"
	CORRECT_EXTERNAL_USER                   = "/wechatcp/apps/%s/externalUser/%s/correct"
	CONVERT_TO_EXTERNAL_USER_ID             = "/wechatcp/apps/%s/externalContact/convertUnionIdAndOpenIdToExternalUserId"
	DOWNLOAD_MINI_PROGRAM_QR_CODE           = "/accounts/%s/miniprogram/qrcodes?unlimit=%s"
	ADD_OR_REMOVE_CALLBACK                  = "/webhookCallbacks/%s/%s/%s/%s/callback/%s"
	LIST_CHAT_MESSAGE_HISTORIES             = "/wechatcp/corps/%s/messageAudit/wechatCPChatMessageHistories/search"
	LIST_CHAT_MESSAGE_HISTORY_PREVIEWS      = "/wechatcp/corps/%s/messageAudit/wechatCPChatMessageHistoryPreviews/search"
	MANUAL_SYNC_CHAT_MESSAGE_HISTORIES      = "/wechatcp/corps/%s/messageAudit/wechatCPChatMessageHistories/manualSync"
	GET_CHAT_MESSAGE_HISTORIES_SYNC_INFO    = "/wechatcp/corps/%s/messageAudit/wechatCPChatMessageHistories/syncInfo"
	REDO_RECOGNIZE_SPEECH                   = "/wechatcp/corps/%s/messageAudit/wechatCPChatMessageHistories/%s/reRecognizeSpeech"
	GET_MESSAGE_AUDIT_PERMIT_USERS          = "/wechatcp/corps/%s/messageAudit/permitUsers"
	GET_MESSAGE_AUDIT_STATUS                = "/wechatcp/corps/%s/messageAuditStatus"
	LIST_TEMPLATE_MESSAGE_RESULTS           = "/accounts/%s/templateMessage"
	AUTH_WESHOP                             = "/wechatServiceMarketingProvider/%s/login"
	GET_SERVICE_INFO                        = "/wechatServiceMarketingProvider/%s/serviceInfo"
	SYNC_CHANNEL                            = "/wechatProviders/%s/apps/sync"
)

func GetChannels(ctx context.Context, accountIds []string, orderBy, ordering string) ([]Channel, error) {
	path := buildUri(CHANNELS_URL, strings.Join(accountIds, ","))
	urlValues := &url.Values{}
	if orderBy != "" {
		urlValues.Set("orderBy", orderBy)
	}
	if ordering != "" {
		urlValues.Set("ordering", ordering)
	}
	var channels []Channel

	err := getWeconnectJson(ctx, urlValues, path, &channels)

	if nil != err {
		return nil, err
	}

	return channels, nil
}

func GetChannel(ctx context.Context, accountId string) (*Channel, error) {
	path := buildUri(CHANNEL_URL, accountId)

	ch := &Channel{}

	err := getWeconnectJson(ctx, &url.Values{}, path, ch)

	if nil != err {
		return nil, err
	}

	return ch, nil
}

func GetCorpChainSharedWeAppChannel(ctx context.Context, corpId string) (*Channel, error) {
	path := buildUri(GET_COR_PCHAIN_SHARED_WEAPP_CHANNEL, corpId)
	params := &url.Values{}
	params.Add("quncrmAccountId", util.GetAccountId(ctx))
	channel := &Channel{}
	err := getWeconnectJson(ctx, params, path, channel)
	if nil != err {
		return nil, err
	}
	return channel, nil
}

func GetFollowersWithUnion(ctx context.Context, unionId string, pFilter *map[string]string) ([]Follower, uint64, error) {
	var followers []Follower
	channelIdStr, exits := (*pFilter)["channel_ids"]
	if !exits {
		channelIdStr = ""
	}
	path := buildUri(FOLLOWER_UNOIN_URL, unionId)
	pNewFilter := &url.Values{}
	channelIds := strings.Split(channelIdStr, ",")
	if channelIdStr != "" {
		for _, channelId := range channelIds {
			pNewFilter.Add("accountIds", channelId)
		}
	}
	err := getWeconnectJson(ctx, pNewFilter, path, &followers)
	if nil != err {
		return nil, 0, err
	}

	return followers, uint64(len(followers)), nil
}

func GetQrcodes(ctx context.Context, channelId string, pFilter *map[string]string, page uint32, pageSize uint32) ([]Qrcode, uint64, error) {
	newFilter := transferPagination(int(page), int(pageSize))
	sourceType := (*pFilter)["type"]
	if sourceType != "" {
		newFilter.Set("type", strings.ToUpper(sourceType))
	}
	expired := (*pFilter)["expired"]
	if expired != "" {
		newFilter.Set("expired", expired)
	}
	temporary := (*pFilter)["temporary"]
	if temporary != "" {
		newFilter.Set("temporary", temporary)
	}

	path := buildUri(QRCODES_URL, channelId)

	pPagination := &Pagination{}

	err := getWeconnectJson(ctx, newFilter, path, pPagination)
	if nil != err {
		return nil, 0, err
	}

	if nil == pPagination {
		//empty result
		return nil, 0, nil
	}

	var qrcodes []Qrcode
	err = unmarshal(ctx, pPagination.Results, &qrcodes)

	return qrcodes, pPagination.TotalAmount, err
}

func GetQrcode(ctx context.Context, channelId string, qrcodeId string) (*Qrcode, error) {
	path := buildUri(QRCODE_URL, channelId, qrcodeId)
	qrcode := &Qrcode{}
	err := getWeconnectJson(ctx, &url.Values{}, path, qrcode)
	if err != nil {
		return nil, err
	}
	return qrcode, nil
}

func CreateQrcode(ctx context.Context, channelId string, data *NewQrcode) (*Qrcode, error) {
	path := buildUri(QRCODES_URL, channelId)
	var qrcode Qrcode
	err := postJson(ctx, data, path, &qrcode)
	return &qrcode, err
}

func UpdateQrcode(ctx context.Context, channelId string, qrcodeId string, qrcodeContent map[string]interface{}) (*Qrcode, error) {
	path := buildUri(QRCODE_URL, channelId, qrcodeId)
	var qrcode Qrcode
	err := putJson(ctx, qrcodeContent, path, &qrcode)
	return &qrcode, err
}

// Only when the weconnect are wrong, err != nil
// If follower do not exist, it will be return nil, nil
func GetFollower(ctx context.Context, channelId string, userId string) (*Follower, error) {
	urlPath := FOLLOWER_OPENID_URL
	if len(userId) == USER_ID_LENGTH {
		urlPath = FOLLOWER_ID_URL
	}
	path := buildUri(urlPath, channelId, userId)

	follower := &Follower{}
	err := getWeconnectJson(ctx, &url.Values{}, path, follower)
	if nil != err {
		return nil, err
	}

	return follower, nil //if follower is nil, then means 404, no such Follower resource
}

func GetMenus(ctx context.Context, channelId string) (*Menus, error) {
	path := buildUri(MENUS_URL, channelId)

	menus := &Menus{}
	err := getWeconnectJson(ctx, &url.Values{}, path, menus)
	if nil != err {
		return nil, err
	}
	return menus, nil
}

func SendTplMessage(ctx context.Context, channelId string, data *TplMessage) error {
	path := buildUri(TPL_MESSAGE_URL, channelId)
	var result interface{}
	err := postJson(ctx, data, path, &result)
	return err
}

func SendTplMessageToSingle(ctx context.Context, channelId string, data *SingleTargetTplMessage) (*SingleTargetTplMessage, error) {
	path := buildUri(SINGLE_TAGET_TPL_MSG_URL, channelId)
	message := &SingleTargetTplMessage{}
	err := postJson(ctx, data, path, message)
	if message.Status == "ERROR" {
		return message, errors.New(message.ErrorDescription)
	}
	return message, err
}

func SendWechatMessage(ctx context.Context, channelId string, userId string, data *Message) error {
	path := buildUri(MESSAGE_URL, channelId, userId)
	var result interface{}
	err := postJson(ctx, data, path, &result)
	return err
}

func SendMassMessage(ctx context.Context, channelId string, data *MassMessage) error {
	path := buildUri(MASS_MESSAGE_URL, channelId)
	var result interface{}
	err := postJson(ctx, data, path, &result)
	return err
}

func SendSubscribeMessage(ctx context.Context, channelId string, data *SubscribeMsg) (*SubscribeMsgResult, error) {
	path := buildUri(SUBSCRIBE_MESSAGE_URL, channelId)
	result := &SubscribeMsgResult{}
	err := postJson(ctx, data, path, &result)
	if result.Status == "ERROR" {
		return result, errors.New(result.ErrorDescription)
	}
	return result, err
}

func buildUri(url string, args ...interface{}) string {
	return fmt.Sprintf(url, args...)
}

func transferPagination(page int, pageSize int) *url.Values {
	newFilter := url.Values{}
	if page > 0 {
		newFilter.Set("pageNum", cast.ToString(page))
	}
	if pageSize > 0 {
		newFilter.Set("pageSize", cast.ToString(pageSize))
	}
	return &newFilter
}

func parseWeconnectResponse(ctx context.Context, resp *WeconnectResponse, result interface{}) error {
	var err error
	if resp.Code == OK_STATUS {
		//resp.Data is raw data of the successful result
		if resp.Data != nil {
			err = unmarshal(ctx, resp.Data, result)
		}
	} else if resp.Code == NO_CONTENT_STATUS {
		result = nil //mark result is EMPTY
	} else if resp.Code == INTERNAL_ERROR_STATUS {
		err = util.NewApiError(util.ServiceError, "WeConnect Service Internal error", nil)
	} else if resp.Code == PARAM_ERROR_STATUS {
		var errData map[string]interface{}
		err = unmarshal(ctx, resp.Data, &errData)
		if err == nil {
			err = util.NewApiError(
				cast.ToInt(errData["errorCode"]),
				cast.ToString(errData["errorMessage"]),
				nil,
			)
		}
	}

	return err
}

// Send GET request to weconnect service
func getWeconnectJson(ctx context.Context, params *url.Values, url string, result interface{}) error {
	respBody, _, err := extension.RequestClient.Get(ctx, CHANNEL_SERVICE_NAME, url, params, nil)

	if err != nil {
		return err
	}

	resp := new(WeconnectResponse)
	err = unmarshal(ctx, respBody, resp)
	if err != nil {
		return err
	}

	return parseWeconnectResponse(ctx, resp, result)
}

// Send POST request to weconnect service
func postJson(ctx context.Context, data interface{}, url string, result interface{}) error {
	respBody, _, err := extension.RequestClient.PostJson(ctx, CHANNEL_SERVICE_NAME, url, data, nil)
	if err != nil {
		return err
	}

	resp := &WeconnectResponse{}
	err = unmarshal(ctx, respBody, resp)
	if err != nil {
		return err
	}

	return parseWeconnectResponse(ctx, resp, result)
}

// Send POST request to weconnect service
func postJsonV2(ctx context.Context, data interface{}, url string, result interface{}) (gorequest.Response, error) {
	respBody, resp, err := extension.RequestClient.PostJson(ctx, CHANNEL_SERVICE_NAME, url, data, nil)
	if err != nil {
		return resp, err
	}

	tempResult := &WeconnectResponse{}
	err = unmarshal(ctx, respBody, tempResult)
	if err != nil {
		return resp, err
	}

	return resp, parseWeconnectResponse(ctx, tempResult, result)
}

// Send PUT request to weconnect service
func putJson(ctx context.Context, data interface{}, url string, result interface{}) error {
	respBody, _, err := extension.RequestClient.PutJson(ctx, CHANNEL_SERVICE_NAME, url, data, nil)
	if err != nil {
		return err
	}

	resp := &WeconnectResponse{}
	err = unmarshal(ctx, respBody, resp)
	if err != nil {
		return err
	}

	return parseWeconnectResponse(ctx, resp, result)
}

// Send PATCH request to weconnect service
func patchJson(ctx context.Context, data interface{}, url string, result interface{}) error {
	respBody, _, err := extension.RequestClient.PatchJson(ctx, CHANNEL_SERVICE_NAME, url, data, nil)
	if err != nil {
		return err
	}

	resp := &WeconnectResponse{}
	err = unmarshal(ctx, respBody, resp)
	if err != nil {
		return err
	}

	return parseWeconnectResponse(ctx, resp, result)
}

// Send DELETE request to weconnect service
func deleteJson(ctx context.Context, data interface{}, url string, result interface{}) error {
	respBody, _, err := extension.RequestClient.DeleteJson(ctx, CHANNEL_SERVICE_NAME, url, data, nil)
	if err != nil {
		return err
	}

	resp := &WeconnectResponse{}
	err = unmarshal(ctx, respBody, resp)
	if err != nil {
		return err
	}

	return parseWeconnectResponse(ctx, resp, result)
}

func getWeconnectTradeOrigin(channelOrigin string) string {
	tradeOrigin := CHANNEL_TRADE_ORIGIN_WECHAT

	switch channelOrigin {
	case CHANNEL_ORIGIN_WEAPP:
		tradeOrigin = CHANNEL_TRADE_ORIGIN_WEAPP
	case CHANNEL_ORIGIN_WECP:
		tradeOrigin = CHANNEL_TRADE_ORIGIN_WECP
	case CHANNEL_ORIGIN_BYTE_DANCE:
		tradeOrigin = CHANNEL_TRADE_ORIGIN_BYTE_DANCE
	case CHANNEL_ORIGIN_ALIPAY_APP:
		tradeOrigin = CHANNEL_TRADE_ORIGIN_ALIPAY_APP
	}

	return tradeOrigin
}

func (preOrder *PreOrder) UnifiedOrder(ctx context.Context, origin string) (*PreOrderResponse, error) {
	result := &PreOrderResponse{}

	tradeOrigin := getWeconnectTradeOrigin(origin)
	url := fmt.Sprintf(UNIFIED_ORDER_URL, tradeOrigin)

	err := postJson(ctx, preOrder, url, result)
	return result, err
}

func (preOrder *PreOrder) V2UnifiedOrder(ctx context.Context, origin string) (*V2PreOrderResponse, error) {
	result := &V2PreOrderResponse{}

	tradeOrigin := getWeconnectTradeOrigin(origin)
	url := fmt.Sprintf(V2_UNIFIED_ORDER_URL, tradeOrigin)

	err := postJson(ctx, preOrder, url, result)
	return result, err
}

func PaySign(ctx context.Context, req PaySignRequest, origin string) (PaySignResponse, error) {
	result := &PaySignResponse{}

	tradeOrigin := getWeconnectTradeOrigin(origin)
	url := fmt.Sprintf(PAY_SIGN_URL, tradeOrigin)

	err := postJson(ctx, req, url, result)
	return *result, err
}

func GetPaymentConfiguration(ctx context.Context, quncrmAccountId string) (*PaymentConfiguration, error) {
	path := buildUri(PAYMENT_CONFIGURATION_URL, quncrmAccountId)
	paymentConfiguration := &PaymentConfiguration{}
	err := getWeconnectJson(ctx, &url.Values{}, path, paymentConfiguration)
	if err != nil {
		return nil, err
	}
	return paymentConfiguration, nil
}

func GetJssdkTicket(ctx context.Context, channelId string, ticketType string, isDelegated bool) (*ResponseTicket, error) {
	query := &url.Values{}
	query.Add("appSecret", util.ToString(!isDelegated))
	if ticketType != "" {
		query.Add("ticketType", ticketType)
	}

	path := buildUri(JSSDK_TICKET_URL, channelId)
	respBody, _, err := extension.RequestClient.Get(ctx, CHANNEL_SERVICE_NAME, path, query, nil)
	if err != nil {
		return nil, err
	}

	return buildTicketResponse(ctx, respBody)
}

func GetTestWechatcpJssdkTicket(ctx context.Context, corpId, appId, ticketType string) (*ResponseTicket, error) {
	query := &url.Values{}
	query.Add("ticketType", ticketType)

	path := buildUri(TEST_WECHATCP_JSSDK_TICKET_URL, corpId, appId)
	respBody, _, err := extension.RequestClient.Get(ctx, CHANNEL_SERVICE_NAME, path, query, nil)
	if err != nil {
		return nil, err
	}

	return buildTicketResponse(ctx, respBody)
}

func GetPiccWechatcpJsSignature(ctx context.Context, channelId, urlStr, ticketType string) (*PiccResponseTicket, error) {
	query := &url.Values{}
	query.Add("url", urlStr)
	query.Add("ticketType", ticketType)

	path := buildUri(PICC_WECHATCP_JS_SIGNATURE_URL, channelId)
	respBody, _, err := extension.RequestClient.Get(ctx, CHANNEL_SERVICE_NAME, path, query, nil)
	if err != nil {
		return nil, err
	}

	resp := &WeconnectResponse{}
	err = unmarshal(ctx, respBody, resp)
	if err != nil {
		return nil, err
	}
	response := &PiccResponseTicket{}
	err = unmarshal(ctx, resp.Data, response)
	if err != nil {
		return nil, err
	}
	return response, nil
}

func GetWechatcpJssdkTicketAppId(ctx context.Context, ticketType, channelId string) (*ResponseTicket, error) {
	query := &url.Values{}
	query.Add("ticketType", ticketType)
	path := buildUri(WECHATCP_JSSDK_JSTICKET, channelId)
	respBody, _, err := extension.RequestClient.Get(ctx, CHANNEL_SERVICE_NAME, path, query, nil)
	if err != nil {
		return nil, err
	}
	return buildTicketResponse(ctx, respBody)
}

func GetWechatcpJssdkTicket(ctx context.Context, suiteId, corpId, ticketType, suiteAppId string) (*ResponseTicket, error) {
	query := &url.Values{}
	query.Add("ticketType", ticketType)
	if suiteAppId != "" {
		query.Add("suiteAppId", suiteAppId)
	}

	path := buildUri(WECHATCP_JSSDK_TICKET_URL, suiteId, corpId)
	respBody, _, err := extension.RequestClient.Get(ctx, CHANNEL_SERVICE_NAME, path, query, nil)
	if err != nil {
		return nil, err
	}

	return buildTicketResponse(ctx, respBody)
}

func buildTicketResponse(ctx context.Context, respBody []byte) (*ResponseTicket, error) {
	ticket := &ResponseTicket{}

	resp := &WeconnectResponse{}
	err := unmarshal(ctx, respBody, resp)
	if err != nil {
		return nil, err
	}

	if resp.Code == OK_STATUS {
		jsTicket := &JsTicket{}
		err = unmarshal(ctx, resp.Data, jsTicket)
		ticket.Ticket = jsTicket.Ticket
		ticket.ExpiresIn = getTicketExpiredIn(jsTicket.ExpireDateTime)
		ticket.AppId = jsTicket.AppId
		ticket.CorpId = jsTicket.CorpId
	} else if resp.Code != NO_CONTENT_STATUS {
		weconnectError := &WeconnectError{}
		err = unmarshal(ctx, resp.Data, weconnectError)
		ticket.ErrorCode = weconnectError.ErrorCode
		ticket.ErrorMsg = weconnectError.ErrorMessage
	}
	return ticket, err
}

func getTicketExpiredIn(expireDateTime int64) int64 {
	now := time.Now().Unix()
	expiredIn := expireDateTime/util.MILLIS_OF_SECOND - now
	return expiredIn
}

func unmarshal(ctx context.Context, data []byte, v interface{}) error {
	err := json.Unmarshal(data, v)
	if nil != err {
		stack := make([]byte, log.MaxStackSize)
		stack = stack[:runtime.Stack(stack, false)]
		log.ErrorTrace(ctx, "Invalid JSON data from WeConnect", log.Fields{
			"data":  string(data),
			"error": err.Error(),
		}, stack)
	}

	return err
}

func (self *Redpack) Send(ctx context.Context) error {
	return postJson(ctx, self, SEND_REDPACK_URL, self)
}

func GetRedpack(ctx context.Context, tradeno string) (*Redpack, error) {
	path := buildUri(GET_REDPACK_URL, tradeno)
	redpack := &Redpack{}
	err := getWeconnectJson(ctx, &url.Values{}, path, redpack)
	if nil != err {
		return nil, err
	}
	return redpack, nil
}

func GetWechatToken(ctx context.Context, channelId string, data WechatTokenRequest) (WechatTokenResponse, error) {
	path := buildUri(GET_WECHAT_TOKEN_URL, channelId)
	var result WechatTokenResponse

	err := postJson(ctx, data, path, &result)
	return result, err
}

func ProduceMqMessage(ctx context.Context, m *MessageBody) (map[string]interface{}, error) {
	query := url.Values{}
	query.Add("topic", m.Topic)
	query.Add("tag", m.Tag)
	query.Add("key", m.Key)

	url := PRODUCE_MQ_MESSAGE + "?" + query.Encode()
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)

	var result map[string]interface{}
	err := postJson(ctx, m.Data, url, &result)

	return result, err
}

func ProduceCustomerEvent(ctx context.Context, reqBody *CustomerEventBody) (map[string]interface{}, error) {
	var result map[string]interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)

	err := postJson(ctx, reqBody, PRODUCE_CUSTOMER_EVENT, &result)

	return result, err
}

func Benchmark(ctx context.Context, data *BenchmarkRequest) error {
	var result map[string]interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)

	return postJson(ctx, data, BENCHMARK_URL, &result)
}

func DecryptMiniProgram(ctx context.Context, channelId string, data *DecryptRequest) (map[string]string, error) {
	var result map[string]string
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)

	url := fmt.Sprintf(DECRYPT_MINI_PROGRAM_URL, channelId)

	err := postJson(ctx, data, url, &result)
	return result, err
}

func GetMiniProgramPhoneByCode(ctx context.Context, data *ProxyRequest) (*GetMiniProgramPhoneByCodeResponse, error) {
	var response GetMiniProgramPhoneByCodeResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func AddMemberLevelSetting(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	var response WxCardCommonResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func GetMemberLevelSetting(ctx context.Context, data *ProxyRequest) (*GetMemberLevelSettingResponse, error) {
	var response GetMemberLevelSettingResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func ListMembers(ctx context.Context, data *ProxyRequest) (*ListMembersResponse, error) {
	var response ListMembersResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func UpdateMemberLevel(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	var response WxCardCommonResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func GetMemberInfo(ctx context.Context, data *ProxyRequest) (*GetMemberInfoResponse, error) {
	var response GetMemberInfoResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func GetMemberScore(ctx context.Context, data *ProxyRequest) (*GetMemberScoreResponse, error) {
	var response GetMemberScoreResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func GetMemberScoreHistory(ctx context.Context, data *ProxyRequest) (*GetMemberScoreHistoryResponse, error) {
	var response GetMemberScoreHistoryResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func UpdateMemberScore(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	var response WxCardCommonResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func SetWeshopRelatedWxa(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	var response WxCardCommonResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func GetWeshopRelatedWxa(ctx context.Context, data *ProxyRequest) (*GetWeshopRelatedWxaResponse, error) {
	var response GetWeshopRelatedWxaResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func DeleteWeshopRelatedWxa(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	var response WxCardCommonResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func GetWxaRelatedWeshops(ctx context.Context, data *ProxyRequest) (*GetWxaRelatedWeshopsResponse, error) {
	var response GetWxaRelatedWeshopsResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func CreateWeshopMember(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	var response WxCardCommonResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func UpdateWeshopMember(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	var response WxCardCommonResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func GetWeshopMember(ctx context.Context, data *ProxyRequest) (*GetWeshopMemberResponse, error) {
	var response GetWeshopMemberResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func GetWeshopMembers(ctx context.Context, data *ProxyRequest) (*GetWeshopMembersResponse, error) {
	var response GetWeshopMembersResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func DeleteWeshopMember(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	var response WxCardCommonResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func ListOrders(ctx context.Context, data *ProxyRequest) (*ListWeshopOrdersResponse, error) {
	var response ListWeshopOrdersResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func GetOrderDetails(ctx context.Context, data *ProxyRequest) (*GetWeshopOrderDetailsResponse, error) {
	var response GetWeshopOrderDetailsResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func GetAfterSaleOrder(ctx context.Context, data *ProxyRequest) (*GetAfterSaleOrderResponse, error) {
	var response GetAfterSaleOrderResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func OrderRefund(ctx context.Context, origin string, data *OrderRefundRequest) (*OrderRefundInfo, error) {
	var result OrderRefundInfo
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)

	tradeOrigin := getWeconnectTradeOrigin(origin)
	var url string
	if origin == "alipayapp" {
		url = fmt.Sprintf(V2_ORDER_REFUND_URL, tradeOrigin)
	} else {
		url = fmt.Sprintf(ORDER_REFUND_URL, tradeOrigin)
	}
	err := postJson(ctx, data, url, &result)
	return &result, err
}

func OrderRefundStatus(ctx context.Context, origin, outRefundNo string) (*OrderRefundInfo, error) {
	var result OrderRefundInfo
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tradeOrigin := getWeconnectTradeOrigin(origin)
	refundUrl := fmt.Sprintf(ORDER_REFUND_STATUS_URL, tradeOrigin, outRefundNo)
	data := &url.Values{}
	data.Set("fetchOrigin", "true")
	err := getWeconnectJson(ctx, data, refundUrl, &result)
	return &result, err
}

func ReportWeappFormId(ctx context.Context, channelId string, data *ReportWeappFormIdRequest) (map[string]interface{}, error) {
	var result map[string]interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(REPORT_WEAPP_FORMID_URL, channelId)
	err := postJson(ctx, data, url, &result)
	return result, err
}

func GetSingleTemplateMessage(ctx context.Context, channelId, messageId string) (*SingleTargetTplMessage, error) {
	var result SingleTargetTplMessage
	templateUrl := fmt.Sprintf(GET_TEMPLATE_MESSAGE, channelId, messageId)
	err := getWeconnectJson(ctx, &url.Values{}, templateUrl, &result)

	return &result, err
}

func ListTemplates(ctx context.Context, channelId string) ([]BriefTemplate, error) {
	var result []BriefTemplate
	err := getWeconnectJson(ctx, &url.Values{}, fmt.Sprintf(LIST_TEMPLATES, channelId), &result)
	return result, err
}

func DeleteProfitSharingReceiver(ctx context.Context, channelId string, data *DeleteProfitSharingReceiverRequest) (map[string]interface{}, error) {
	var result map[string]interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(DELETE_PROFIT_SHARING_RECEIVER, channelId)
	err := deleteJson(ctx, data, url, &result)

	return result, err
}

func CreateProfitSharingReceiver(ctx context.Context, channelId string, data *CreateProfitSharingReceiverRequest) (map[string]interface{}, error) {
	var result map[string]interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(CREATE_PROFIT_SHARING_RECEIVER, channelId)
	err := postJson(ctx, data, url, &result)

	return result, err
}

func MultiProfitSharing(ctx context.Context, channelId string, data *MultiProfitSharingRequest) (map[string]interface{}, error) {
	var result map[string]interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(MULTI_PROFIT_SHARING, channelId)
	err := postJson(ctx, data, url, &result)

	return result, err
}

func GetProfitSharing(ctx context.Context, channelId, transactionId string) (OrderMultiProfitSharingResponse, error) {
	query := &url.Values{}
	query.Add("transactionId", transactionId)

	var resp OrderMultiProfitSharingResponse
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	refundUrl := fmt.Sprintf(GET_PROFIT_SHARING, channelId)
	err := getWeconnectJson(ctx, query, refundUrl, &resp)

	return resp, err
}

func FinishProfitSharing(ctx context.Context, channelId string, data *FinishProfitSharingRequest) (map[string]interface{}, error) {
	var result map[string]interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(FINISH_PROFIT_SHARING, channelId)
	err := postJson(ctx, data, url, &result)

	return result, err
}

func GetRefreshedProfitSharing(ctx context.Context, channelId, outOrderNo string) (OrderMultiProfitSharingInfo, error) {
	var resp OrderMultiProfitSharingInfo
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	templateUrl := fmt.Sprintf(GET_REFRESHED_PROFIT_SHARING, channelId, outOrderNo)
	err := getWeconnectJson(ctx, &url.Values{}, templateUrl, &resp)

	return resp, err
}

func GetWeappVersion(ctx context.Context, accountId string) (*GetWeappVersionResponse, error) {
	var result GetWeappVersionResponse
	templateUrl := fmt.Sprintf(GET_WEAPP_VERSION, accountId)
	err := getWeconnectJson(ctx, &url.Values{}, templateUrl, &result)

	return &result, err
}

func GetWeappChangeLogs(ctx context.Context, accountId string) (*GetWeappChangeLogsResponse, error) {
	var result GetWeappChangeLogsResponse
	templateUrl := fmt.Sprintf(GET_WEAPP_CHANGE_LOGS, accountId)
	err := getWeconnectJson(ctx, &url.Values{}, templateUrl, &result)

	return &result, err
}

func GetWeappQrCode(ctx context.Context, accountId string, data *GetQrCodeRequest) ([]byte, error) {
	tmpUrl := fmt.Sprintf(GET_WEAPP_QR_CODE, accountId)
	respBody, _, err := extension.RequestClient.PostJson(ctx, CHANNEL_SERVICE_NAME, tmpUrl, data, nil)

	return respBody, err
}

func GetDeliveries(ctx context.Context, accountId string) (*GetDeliveriesResponse, error) {
	result := GetDeliveriesResponse{}
	tmpUrl := fmt.Sprintf(EXPRESS_URL_PREFIX, accountId) + "deliveries"
	err := getWeconnectJson(ctx, &url.Values{}, tmpUrl, &result)

	return &result, err
}

func AddExpressOrder(ctx context.Context, accountId string, data *AddExpressOrderRequest) (*ExpressOrderResponse, error) {
	result := ExpressOrderResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(EXPRESS_URL_PREFIX, accountId) + "orders/add"
	err := postJson(ctx, data, url, &result)
	return &result, err
}

func GetExpressOrderPath(ctx context.Context, accountId string, data *GetExpressOrderPathRequest) (*GetExpressOrderPathResponse, error) {
	result := GetExpressOrderPathResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(EXPRESS_URL_PREFIX, accountId) + "paths/get"
	err := postJson(ctx, data, url, &result)
	return &result, err
}

func GetAllExpressAccounts(ctx context.Context, accountId string) (*GetAllExpressAccountsResponse, error) {
	result := GetAllExpressAccountsResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(EXPRESS_URL_PREFIX, accountId) + "account/getAll"
	err := getWeconnectJson(ctx, &url.Values{}, tmpUrl, &result)
	return &result, err
}

func SendTransfer(ctx context.Context, origin string, data *SendTransferRequest) (*TransferDetailResponse, error) {
	result := TransferDetailResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(SEND_TRANSFER, origin)
	err := postJson(ctx, data, tmpUrl, &result)

	// https://work.weixin.qq.com/api/doc/90000/90135/90278
	// 当状态为FAIL时，存在业务结果未明确的情况，所以如果状态为FAIL，请务必再请求一次查询接口，通过查询接口确认此次付款结果。
	if result.ResultCode == SEND_TRANSFER_FAIL {
		resp, err := GetTransfer(ctx, origin, result.TradeNo)
		return resp, err
	}

	return &result, err
}

func SendV2Transfer(ctx context.Context, origin string, data *SendV2TransferRequest) (*V2TransferDetailResponse, error) {
	result := []*V2TransferDetailResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(SEND_V2_TRANSFER, origin)
	err := postJson(ctx, data, tmpUrl, &result)
	if err != nil {
		return nil, err
	}
	if len(result) == 0 {
		return nil, errors.New("Invalid transfer")
	}
	return result[0], nil
}

func GetTransfer(ctx context.Context, origin, tradeNo string) (*TransferDetailResponse, error) {
	result := TransferDetailResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(GET_TRANSFER, origin, tradeNo)
	err := getWeconnectJson(ctx, &url.Values{}, tmpUrl, &result)
	return &result, err
}

func GetV2Transfer(ctx context.Context, origin, tradeBatchNo, tradeNo string) (*V2TransferDetailResponse, error) {
	result := V2TransferDetailResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(GET_V2_TRANSFER, origin, tradeBatchNo, tradeNo)
	err := getWeconnectJson(ctx, &url.Values{}, tmpUrl, &result)
	return &result, err
}

func ListDepartments(ctx context.Context, channelId string) ([]Department, error) {
	result := []Department{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(LIST_DEPARTMENTS, channelId)
	err := getWeconnectJson(ctx, &url.Values{}, tmpUrl, &result)
	return result, err
}

func ListDepartmentsById(ctx context.Context, channelId, departmentId string) ([]Department, error) {
	result := []Department{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(LIST_DEPARTMENTS_BY_ID, channelId, departmentId)
	err := getWeconnectJson(ctx, &url.Values{}, tmpUrl, &result)
	return result, err
}

func ListDepartmentUsers(ctx context.Context, channelId, departmentId, fetchChild string) ([]User, error) {
	result := []User{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(LIST_DEPARTMENT_USERS, channelId, departmentId, fetchChild)
	err := getWeconnectJson(ctx, &url.Values{}, tmpUrl, &result)
	return result, err
}

func GetUser(ctx context.Context, channelId, userId string) (*User, error) {
	result := User{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(GET_USER, channelId, userId)
	err := getWeconnectJson(ctx, &url.Values{}, tmpUrl, &result)
	return &result, err
}

func GetContactWay(ctx context.Context, channelId, contactWayId string) (*ContactWay, error) {
	result := ContactWay{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(GET_CONTACT_WAY, channelId, contactWayId)
	err := getWeconnectJson(ctx, &url.Values{}, tmpUrl, &result)
	return &result, err
}

func SearchContactWay(ctx context.Context, channelId string, req SearchContactWayRequest) (*SearchContactWayResponse, error) {
	result := &SearchContactWayResponse{}
	tmpUrl := fmt.Sprintf(CONTACT_WAYS, channelId)
	data := &url.Values{}
	if req.Name != "" {
		data.Set("name", req.Name)
	}
	if req.Type != "" {
		data.Set("type", req.Type)
	}
	if req.Group != "" {
		data.Set("group", req.Group)
	}
	if req.UserId != "" {
		data.Set("userId", req.UserId)
	}
	if req.PageSize != "" {
		data.Set("pageSize", req.PageSize)
	}
	if req.PageNum != "" {
		data.Set("pageNum", req.PageNum)
	}
	if len(req.Ids) > 0 {
		for _, id := range req.Ids {
			data.Add("ids", id)
		}
	}
	if len(req.Groups) > 0 {
		for _, group := range req.Groups {
			data.Add("groups", group)
		}
	}
	err := getWeconnectJson(ctx, data, tmpUrl, result)
	return result, err
}

func SearchContactWayWithoutChannelId(ctx context.Context, req SearchContactWayRequest) (*SearchContactWayResponse, error) {
	result := &SearchContactWayResponse{}
	baseUrl := SEARCH_CONTACT_WAY
	query := url.Values{}
	if req.PageSize != "" {
		query.Set("pageSize", req.PageSize)
	}
	if req.PageNum != "" {
		query.Set("pageNum", req.PageNum)
	}
	if req.OrderBy != "" {
		query.Set("orderBy", req.OrderBy)
	}
	if req.Ordering != "" {
		query.Set("ordering", req.Ordering)
	}
	finalUrl := baseUrl
	if len(query) > 0 {
		finalUrl = baseUrl + "?" + query.Encode()
	}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	err := postJson(ctx, req, finalUrl, result)
	return result, err
}

func ListContactWays(ctx context.Context, channelId string, req SearchContactWayRequest) (*SearchContactWayResponse, error) {
	result := &SearchContactWayResponse{}
	tmpUrl := fmt.Sprintf(POST_CONTACT_WAYS, channelId, req.PageNum, req.PageSize)
	err := postJson(ctx, req, tmpUrl, result)
	return result, err
}

func GetContactWayByState(ctx context.Context, channelId, state string) (*ContactWay, error) {
	result := ContactWay{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(GET_CONTACT_WAY_BY_STATE, channelId, state)
	err := getWeconnectJson(ctx, &url.Values{}, tmpUrl, &result)
	return &result, err
}

func CreateContactWay(ctx context.Context, channelId string, contactWay ContactWayRequest) (*ContactWay, error) {
	result := ContactWay{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(CONTACT_WAYS, channelId)
	err := postJson(ctx, contactWay, tmpUrl, &result)
	return &result, err
}

func UpdateContactWay(ctx context.Context, channelId, contactWayId string, contactWay ContactWayRequest) (*ContactWay, error) {
	result := ContactWay{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(GET_CONTACT_WAY, channelId, contactWayId)
	err := putJson(ctx, contactWay, tmpUrl, &result)
	return &result, err
}

func DeleteContactWay(ctx context.Context, channelId, contactWayId string) error {
	var result interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(GET_CONTACT_WAY, channelId, contactWayId)
	err := deleteJson(ctx, &url.Values{}, tmpUrl, &result)
	return err
}

func ListContactWay(ctx context.Context, channelId string, groupIds []string) ([]ContactWay, error) {
	return []ContactWay{}, nil
}

func ListWelcomeMessages(ctx context.Context, channelId string, req *ListContactWelcomeMsgsRequest) (*ListContactWelcomeMsgsResponse, error) {
	result := &ListContactWelcomeMsgsResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	reqUrl := fmt.Sprintf(WELCOME_MESSAGES, channelId)
	params := url.Values{}
	params.Set("pageSize", cast.ToString(req.PageSize))
	params.Set("pageNum", cast.ToString(req.PageNum))
	if req.DepartmentId != 0 {
		params.Set("departmentId", cast.ToString(req.DepartmentId))
	}
	if req.SearchKey != "" {
		params.Set("searchKey", req.SearchKey)
	}
	for _, originId := range req.OriginIds {
		params.Add("originIds", originId)
	}
	err := getWeconnectJson(ctx, &params, reqUrl, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func DeleteWelcomeMessage(ctx context.Context, channelId, userId string) error {
	var result interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	params := url.Values{}
	params.Add("originIds", userId)
	tmpUrl := fmt.Sprintf(WELCOME_MESSAGES, channelId)
	err := deleteJson(ctx, &params, tmpUrl, &result)
	return err
}

func CreateGroupWelcomeTemplate(ctx context.Context, channelId string, groupWelcomeTemplate *GroupWelcomeTemplate) (*GroupWelcomeTemplate, error) {
	result := GroupWelcomeTemplate{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	reqUrl := fmt.Sprintf(GROUP_WELCOME_TEMPLATE, channelId)
	err := postJson(ctx, groupWelcomeTemplate, reqUrl, &result)
	return &result, err
}

func UpdateGroupWelcomeTemplate(ctx context.Context, channelId string, groupWelcomeTemplate *GroupWelcomeTemplate) (*WeconnectResponse, error) {
	result := WeconnectResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	reqUrl := fmt.Sprintf(EDIT_GROUP_WELCOME_TEMPLATE, channelId, groupWelcomeTemplate.TemplateId)
	err := putJson(ctx, groupWelcomeTemplate, reqUrl, &result)
	return &result, err
}

func GetGroupWelcomeTemplate(ctx context.Context, channelId, templateId string) (*GroupWelcomeTemplate, error) {
	result := GroupWelcomeTemplate{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	reqUrl := fmt.Sprintf(EDIT_GROUP_WELCOME_TEMPLATE, channelId, templateId)
	err := getWeconnectJson(ctx, &url.Values{}, reqUrl, &result)
	return &result, err
}

func DeleteGroupWelcomeTemplate(ctx context.Context, channelId, templateId string) error {
	result := WeconnectResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	params := &url.Values{}
	params.Set("templateId", templateId)
	reqUrl := fmt.Sprintf(EDIT_GROUP_WELCOME_TEMPLATE, channelId, templateId)
	err := deleteJson(ctx, &params, reqUrl, &result)
	return err
}

func ListChats(ctx context.Context, channelId, cursor string, status int, userIds []string, partyIds []int) (string, []GroupchatBrief, error) {
	resp := Pagination{}

	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(LIST_CHATS, channelId)
	params := url.Values{}
	params.Set("cursor", cursor)
	if status != 0 {
		params.Set("status", cast.ToString(status))
	}
	if len(userIds) > 0 {
		for _, userId := range userIds {
			params.Add("userIds", userId)
		}
	}
	if len(partyIds) > 0 {
		for _, partyId := range partyIds {
			params.Add("partyIds", cast.ToString(partyId))
		}
	}
	err := getWeconnectJson(ctx, &params, tmpUrl, &resp)
	if err != nil {
		// 当 err 存在时， resp.Results 为空，因此没必要进行后面的操作，否则会导致解析 json 报错
		return "", nil, err
	}

	result := []GroupchatBrief{}
	err = unmarshal(ctx, resp.Results, &result)

	return resp.Next, result, err
}

func ListChatMembers(ctx context.Context, channelId, chatId string) (*Groupchat, error) {
	result := Groupchat{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(LIST_CHAT_MEMBERS, channelId, chatId)
	err := getWeconnectJson(ctx, &url.Values{}, tmpUrl, &result)
	return &result, err
}

func ListStatsStaffContact(ctx context.Context, channelId string, req ListStatsStaffContactRequest) (ListStatsStaffContactResponse, error) {
	var results []StatsStaffContactData
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(LIST_STATS_STAFF_CONTACT, channelId)
	params := url.Values{}
	if len(req.UserIds) > 0 {
		for _, id := range req.UserIds {
			params.Add("userIds", id)
		}
	}
	params.Set("startTime", cast.ToString(req.StartTime))
	params.Set("endTime", cast.ToString(req.EndTime))
	err := getWeconnectJson(ctx, &params, tmpUrl, &results)
	res := ListStatsStaffContactResponse{}
	res.Results = results
	return res, err
}

func ListStatsChats(ctx context.Context, channelId string, req ListStatsChatsRequest) (ListStatsChatsResponse, error) {
	result := ListStatsChatsResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(LIST_STATS_CHATS, channelId)
	params := url.Values{}
	params.Set("pageSize", cast.ToString(req.PageSize))
	params.Set("pageNum", cast.ToString(req.PageNum))
	params.Set("startTime", cast.ToString(req.StartTime))
	if len(req.UserIds) > 0 {
		for _, id := range req.UserIds {
			params.Add("userIds", id)
		}
	}
	if len(req.PartyIds) > 0 {
		for _, id := range req.PartyIds {
			params.Add("partyIds", cast.ToString(id))
		}
	}
	if req.Ordering != "" {
		params.Set("ordering", req.Ordering)
	}
	if req.OrderBy != "" {
		params.Set("orderBy", req.OrderBy)
	}
	err := getWeconnectJson(ctx, &params, tmpUrl, &result)
	return result, err
}

func CreateGroupchatJoinWays(ctx context.Context, channelId string, req GroupchatJoinWaysRequest) (*GroupchatJoinWays, error) {
	result := GroupchatJoinWays{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(GROUP_CHAT_JOIN_WAYS, channelId)
	err := postJson(ctx, req, tmpUrl, &result)
	return &result, err
}

func UpdateGroupchatJoinWays(ctx context.Context, channelId, configId string, req GroupchatJoinWaysRequest) (*GroupchatJoinWays, error) {
	result := GroupchatJoinWays{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(GET_GROUP_CHAT_JOIN_WAYS, channelId, configId)
	err := putJson(ctx, req, tmpUrl, &result)
	return &result, err
}

func GetGroupchatJoinWay(ctx context.Context, channelId, configId string) (*GroupchatJoinWays, error) {
	path := buildUri(GET_GROUP_CHAT_JOIN_WAYS, channelId, configId)
	result := &GroupchatJoinWays{}
	err := getWeconnectJson(ctx, nil, path, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func GetCorp(ctx context.Context, corpId string) (*CorpDetail, error) {
	result := &CorpDetail{}
	path := buildUri(GET_CORP, corpId)
	err := getWeconnectJson(ctx, &url.Values{}, path, result)
	if nil != err {
		return nil, err
	}

	return result, nil
}

func ListTags(ctx context.Context, corpId, channelId, appId string, tagIds []string) ([]*GroupTagsResponse, error) {
	result := []*GroupTagsResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(LIST_TAGS, corpId)
	params := url.Values{}
	if appId != "" {
		tmpUrl = fmt.Sprintf(LIST_TAGS_BY_APPID, appId)
	} else {
		params.Add("channelId", channelId)
	}
	if len(tagIds) > 0 {
		for _, tagId := range tagIds {
			params.Add("tagIds", tagId)
		}
	}
	err := getWeconnectJson(ctx, &params, tmpUrl, &result)
	return result, err
}

func AddTags(ctx context.Context, corpId, channelId string, data AddTagsRequest) (*GroupTagsResponse, error) {
	result := GroupTagsResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(ADD_TAGS, corpId)
	url = fmt.Sprintf("%s?channelId=%s", url, channelId)
	err := postJson(ctx, data, url, &result)
	return &result, err
}

func EditTag(ctx context.Context, corpId, channelId string, data EditTagRequest) error {
	var result interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(EDIT_TAG, corpId)
	url = fmt.Sprintf("%s?channelId=%s", url, channelId)
	err := putJson(ctx, data, url, &result)
	return err
}

func DeleteTags(ctx context.Context, corpId, channelId, appId string, data DeleteTagsRequest) error {
	var result interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(DELETE_TAGS, corpId)
	if appId != "" {
		url = fmt.Sprintf(DELETE_TAGS, appId)
	} else {
		url = fmt.Sprintf("%s?channelId=%s", url, channelId)
	}
	err := deleteJson(ctx, data, url, &result)
	return err
}

func EditMemberTags(ctx context.Context, corpId, channelId, appId string, data EditMemberTagsRequest) error {
	var result interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(EDIT_MEMBER_TAGS_BY_CORPID, corpId)
	if corpId == "" {
		url = fmt.Sprintf(EDIT_MEMBER_TAGS_BY_APPID, appId)
	} else {
		url = fmt.Sprintf("%s?channelId=%s&useAppTokenForTagSync=%v&clearCache=%v", url, channelId, data.UseAppSecret, data.ClearCache)
	}
	err := postJson(ctx, data, url, &result)
	return err
}

func AddWechatTagToUsers(ctx context.Context, channelId string, wxTagId int, openIds []string) (map[string][]string, int, error) {
	result := map[string][]string{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(ADD_WECHAT_TAG_TO_USERS, channelId)
	data := map[string]interface{}{
		"wxTagId":   wxTagId,
		"originIds": openIds,
	}
	resp, err := postJsonV2(ctx, data, url, &result)
	if err != nil {
		return result, resp.StatusCode, err
	}
	return result, resp.StatusCode, nil
}

func RemoveWechatTagFromUsers(ctx context.Context, channelId string, wxTagId int, openIds []string) (interface{}, int, error) {
	var result interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(REMOVE_WECHAT_TAG_FROM_USERS, channelId)
	data := map[string]interface{}{
		"wxTagId":   wxTagId,
		"originIds": openIds,
	}
	resp, err := postJsonV2(ctx, data, url, &result)
	if err != nil {
		return nil, resp.StatusCode, err
	}
	return result, resp.StatusCode, nil
}

func GetExternalUser(ctx context.Context, channelId, externalUserId string) (GetExternalUserResponse, error) {
	var result GetExternalUserResponse
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(GET_EXTERNAL_USER, channelId, externalUserId)
	err := getWeconnectJson(ctx, &url.Values{}, path, &result)
	return result, err
}

func GetSystemExternalUser(ctx context.Context, channelId, externalUserId string) (GetSystemExternalUserResponse, error) {
	var result GetSystemExternalUserResponse
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(GET_SYSTEM_EXTERNAL_USER, channelId, externalUserId)
	err := getWeconnectJson(ctx, &url.Values{}, path, &result)
	return result, err
}

func ListSystemExternalUser(ctx context.Context, channelId string, externalUserIds []string) (*ListSystemExternalUserResponse, error) {
	result := &ListSystemExternalUserResponse{}
	externalUserData := []GetSystemExternalUserResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(List_SYSTEM_EXTERNAL_USER, channelId)
	data := map[string]interface{}{
		"externalUserIds": externalUserIds,
	}
	err := postJson(ctx, data, path, &externalUserData)
	if err != nil {
		return &ListSystemExternalUserResponse{}, err
	}
	result.ExternalUserData = externalUserData
	return result, err
}

func ListFollowExternalUsers(ctx context.Context, channelId, userId string) (ListFollowExternalUserResponse, error) {
	result := ListFollowExternalUserResponse{}
	externalUserIds := []string{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(LIST_FOLLOW_EXTERNAL_USERS, channelId, userId)
	err := getWeconnectJson(ctx, &url.Values{}, path, &externalUserIds)
	result.ExternalUserIds = externalUserIds
	return result, err
}

func ListExternalUser(ctx context.Context, channelId, userId, cursor string, limit string) (ListExternalUserResponse, error) {
	var result ListExternalUserResponse
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(LIST_EXTERNAL_USER, channelId)
	data := &url.Values{}
	data.Add("userId", userId)
	data.Add("limit", limit)
	data.Add("cursor", cursor)
	err := getWeconnectJson(ctx, data, path, &result)
	return result, err
}

func SyncExternalUsers(ctx context.Context, channelId string, userIds []string, force, forceOld bool) error {
	var result interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(SYNC_EXTERNAL_USERS, channelId)
	data := map[string]interface{}{
		"force":    force,
		"forceOld": forceOld,
	}
	if len(userIds) > 0 {
		data["userIds"] = userIds
	}
	err := postJson(ctx, data, url, &result)
	if err != nil {
		return err
	}
	return nil
}

func ConvertToOpenId(ctx context.Context, channelId, userId string) (string, error) {
	var result map[string]interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(CONVERT_TO_OPEN_ID, channelId)
	err := postJson(ctx, map[string]interface{}{
		"userId":     userId,
		"withoutApp": true,
	}, url, &result)
	return cast.ToString(result["openid"]), err
}

func GetAppDetail(ctx context.Context, channelId string) (AppDetail, error) {
	var result AppDetail
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	tmpUrl := fmt.Sprintf(GET_APP_DETAIL, channelId)
	err := getWeconnectJson(ctx, &url.Values{}, tmpUrl, &result)
	return result, err
}

func SendAliyunqaSms(ctx context.Context, req SendAliyunqaSmsRequest, isDigital bool, oem AliyunqaOem) (*SendAliyunqaSmsResponse, error) {
	result := &SendAliyunqaSmsResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := SEND_ALIYUNQA_SMS
	if isDigital {
		path = SEND_ALIYUNQA_DIGITAL_SMS
	}
	query := buildOemQuery(oem)
	path = fmt.Sprintf("%s?%s", path, query.Encode())
	err := postJson(ctx, req, path, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func GetAliyunqaSms(ctx context.Context, id string, oem AliyunqaOem) (*AliyunqaSmsDetailResponse, error) {
	result := &AliyunqaSmsDetailResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(GET_ALIYUNQA_SMS, id)
	err := getWeconnectJson(ctx, buildOemQuery(oem), path, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func GetAliyunqaOrderRemain(ctx context.Context, oem AliyunqaOem, req GetAliyunqaResourceProxyRequest) (*GetAliyunqaResourceProxyResponse, error) {
	result := &GetAliyunqaResourceProxyResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(ALIYUNQA_PROXY_URL, oem.ServerEnvironment, oem.TenantId, oem.TenantRegion, oem.WorkspaceId)
	err := postJson(ctx, req, path, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func SendAliyunqaOrderConsumed(ctx context.Context, oem AliyunqaOem, req SendAliyunqaResourceProxyRequest) (*SendAliyunqaResourceProxyResponse, error) {
	result := &SendAliyunqaResourceProxyResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(ALIYUNQA_PROXY_URL, oem.ServerEnvironment, oem.TenantId, oem.TenantRegion, oem.WorkspaceId)
	err := postJson(ctx, req, path, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func SendWelcomeMessage(ctx context.Context, channelId string, req SendWelcomeMessageRequest) error {
	var result interface{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	url := fmt.Sprintf(SEND_WELCOME_MESSAGE, channelId)
	return postJson(ctx, req, url, &result)
}

func DownloadMediaMeterial(ctx context.Context, channelId, mediaType, mediaId string, isTemporary bool, savePath string) error {
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(DOWNLOAD_MEDIA_METERIAL, channelId, mediaType, mediaId)
	if isTemporary {
		path = fmt.Sprintf(DOWNLOAD_TEMPORARY_MEDIA_METERIAL, channelId, mediaType, mediaId)
	}
	body, resp, err := extension.RequestClient.Get(ctx, CHANNEL_SERVICE_NAME, path, &url.Values{}, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	weResp := new(WeconnectResponse)
	err = unmarshal(ctx, body, weResp)
	// 如果下载文件成功，body 一定无法通过 json 解析，只要解析成功则说明下载文件失败，body 中是报错信息
	if err == nil {
		var result interface{}
		// 解析报错信息后返回报错
		err = parseWeconnectResponse(ctx, weResp, &result)
		if err != nil {
			return err
		}
	}

	// 创建一个文件用于保存
	out, err := os.Create(savePath)
	if err != nil {
		return err
	}
	defer out.Close()

	// 将响应流和文件流对接起来
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return err
	}
	return nil
}

func CreateTemporaryMediaMaterial(ctx context.Context, channelId, mediaType, mediaUrl string) (*CreateTemporaryMediaMaterialResp, error) {
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)

	// 下载文件
	body, resp, err := extension.RequestClient.Get(ctx, "", mediaUrl, nil, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	path := fmt.Sprintf(CREATE_TEMPORARY_MEDIA_METERIAL, channelId, mediaType)

	body, resp, err = extension.RequestClient.PostFile(ctx, CHANNEL_SERVICE_NAME, path, "media", body, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	weResp := new(WeconnectResponse)
	err = unmarshal(ctx, body, weResp)
	if err != nil {
		return nil, err
	}

	var result CreateTemporaryMediaMaterialResp
	// 解析报错信息后返回报错
	err = parseWeconnectResponse(ctx, weResp, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func CreateGroupMessages(ctx context.Context, channelId string, req CreateGroupMessageRequest) (*CreateGroupMessageResponse, error) {
	path := fmt.Sprintf(CREATE_GROUP_MESSAGES, channelId)
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	result := &CreateGroupMessageResponse{}
	err := postJson(ctx, req, path, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func GetPaymonetCredentialConfiguration(ctx context.Context, channel string, quncrmAccountId string) (*PaymentCredentialConfigurationResponse, error) {
	result := &PaymentCredentialConfigurationResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(GET_PAYMENT_CONFIGURATION, channel, quncrmAccountId)
	err := getWeconnectJson(ctx, &url.Values{}, path, result)
	return result, err
}

func ListWechatcpAdmins(ctx context.Context, channelId string) (*ListWechatcpAdminsResponse, error) {
	result := &ListWechatcpAdminsResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(LIST_WECHATCP_ADMINS, channelId)
	err := getWeconnectJson(ctx, &url.Values{}, path, result)
	return result, err
}

func ListWechatcpAuthUsers(ctx context.Context, channelId string) (*ListWechatcpAuthUsersResponse, error) {
	result := &ListWechatcpAuthUsersResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(LIST_WECHATCP_AUTH_USERS, channelId)
	err := getWeconnectJson(ctx, &url.Values{}, path, result)
	return result, err
}

func buildOemQuery(oem AliyunqaOem) *url.Values {
	query := &url.Values{}
	query.Set("tenantId", oem.TenantId)
	query.Set("workspaceId", oem.WorkspaceId)
	query.Set("tenantRegion", oem.TenantRegion)
	query.Set("serverEnvironment", oem.ServerEnvironment)
	return query
}

func ByteDancePrePay(ctx context.Context, req ByteDancePrePayRequest) (ByteDancePrePayResponse, error) {
	result := &ByteDancePrePayResponse{}

	err := postJson(ctx, req, BYTE_DANCE_PRE_PAY, result)
	return *result, err
}

func ConvertOpenGroupIdToChatId(ctx context.Context, channelId string, req ConvertOpenGroupIdToChatIdRequest) (*ConvertOpenGroupIdToChatIdResp, error) {
	path := buildUri(CONVERT_OPEN_GROUPID_TOCHATID, channelId)
	resp := &ConvertOpenGroupIdToChatIdResp{}

	err := postJson(ctx, req, path, resp)
	return resp, err
}

func UpdateWxCard(ctx context.Context, channelId string, req UpdateWxCardRequest) error {
	path := buildUri(UPDATE_WX_CARD, channelId, req.Code)
	var resp interface{}

	err := putJson(ctx, req, path, &resp)
	return err
}

func WeconnectProxy(ctx context.Context, channelId, method, path string, params, body, result interface{}) error {
	url := buildUri(WECONNECT_PROXY, channelId)
	data := map[string]interface{}{
		"method": method,
		"path":   path,
		"params": params,
		"body":   body,
	}
	switch body.(type) {
	case GetAutoActiveStatusRequest, SetAutoActiveStatusRequest, GetAppLicenseInfoRequest, ListActivatedAccountRequest:
		data["wechatCPConfig"] = WechatCPConfig{UseProviderAccessToken: true}
	}
	respBody, _, err := extension.RequestClient.PostJson(ctx, CHANNEL_SERVICE_NAME, url, data, nil)
	if err != nil {
		return err
	}

	resp := WeconnectProxyResponse{
		Data: result,
	}
	err = json.Unmarshal(respBody, &resp)
	if err != nil {
		return err
	}

	if resp.Code != 200 {
		return errors.New(resp.Message)
	}

	return nil
}

func WeconnectProxyV2(ctx context.Context, origin, channelId, method, path string, body, result interface{}) error {
	data := map[string]interface{}{
		"method":             method,
		"path":               path,
		"weconnectAccountId": channelId,
		"quncrmAccountId":    util.GetAccountId(ctx),
		"body":               body,
	}
	return postJson(ctx, data, buildUri(WECONNECT_V2_PROXY, origin), result)
}

func SendExternalContactMassMessage(ctx context.Context, channelId string, data *ExternalContactMassMessage) (*ExternalContactMassMessageResponse, error) {
	path := buildUri(EXTERNAL_MASS_MESSAGE, channelId)
	result := &ExternalContactMassMessageResponse{}
	err := postJson(ctx, data, path, result)
	return result, err
}

func GetExternalContactMassMessageResult(ctx context.Context, channelId string, req *GetExternalContactMassMessage) (*ExternalContactMassMessageResult, error) {
	result := &ExternalContactMassMessageResult{}
	urlValues := &url.Values{}
	if req.MsgId != "" {
		urlValues.Set("msgId", req.MsgId)
	}
	if req.UserId != "" {
		urlValues.Set("userId", req.UserId)
	}
	if req.Limit > 0 {
		urlValues.Set("limit", cast.ToString(req.Limit))
	}
	if req.Cursor != "" {
		urlValues.Set("cursor", req.Cursor)
	}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(EXTERNAL_MASS_MESSAGE_RESULT, channelId)
	err := getWeconnectJson(ctx, urlValues, path, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func GetMassMessageResultTaskList(ctx context.Context, channelId string, req *GetExternalContactMassMessage) (*MassMessageTaskList, error) {
	result := &MassMessageTaskList{}
	urlValues := &url.Values{}
	if req.MsgId != "" {
		urlValues.Set("msgId", req.MsgId)
	}
	if req.Limit > 0 {
		urlValues.Set("limit", cast.ToString(req.Limit))
	}
	if req.Cursor != "" {
		urlValues.Set("cursor", req.Cursor)
	}
	if req.RetryMode != "" {
		urlValues.Set("retryMode", req.RetryMode)
	}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(EXTERNAL_MASS_MESSAGE_TASK_LIST, channelId)
	err := getWeconnectJson(ctx, urlValues, path, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func CreateMassMessageTask(ctx context.Context, channelId string, data *ExternalContactMassMessage) (string, error) {
	path := buildUri(EXTERNAL_MASS_MESSAGE_TASK, channelId)
	var result string
	err := postJson(ctx, data, path, &result)
	return result, err
}

func GetMassMessageTaskResult(ctx context.Context, channelId, taskId string) (*MassMessageTaskResult, error) {
	path := buildUri(EXTERNAL_MASS_MESSAGE_TASK_RESULT, channelId, taskId)
	result := &MassMessageTaskResult{}
	err := getWeconnectJson(ctx, nil, path, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func CancelMassMessageTask(ctx context.Context, channelId, taskId string) error {
	path := buildUri(EXTERNAL_MASS_MESSAGE_TASK_CANCEL, channelId, taskId)
	var resp, req interface{}
	err := putJson(ctx, req, path, &resp)
	return err
}

func DeleteFollower(ctx context.Context, channelId, openId string) error {
	params := &DeleteFollowerRequest{
		AccountId: channelId,
		OriginId:  openId,
	}
	query := url.Values{}
	query.Set("accountId", channelId)
	query.Set("originId", openId)
	err := deleteJson(ctx, params, fmt.Sprintf("%s?%s", DELETE_USER, query.Encode()), nil)
	return err
}

func GetActiveCodeCount(ctx context.Context, channelId string) (uint64, error) {
	var result uint64 = 0
	path := buildUri(ACTIVE_CODE_COUNT, channelId)
	err := getWeconnectJson(ctx, nil, path, &result)
	if err != nil {
		return 0, err
	}
	return result, nil
}

func CreateRenewUserOrder(ctx context.Context, channelId string, req *CreateRenewUserOrderRequest) (string, error) {
	// 下单人，服务商企业内成员 userid
	req.BuyUserId = "vincent"
	path := buildUri(CREATE_RENEW_USER_ORDER, channelId)
	taskId := ""
	err := postJson(ctx, req, path, &taskId)
	return taskId, err
}

func GetRenewUserResult(ctx context.Context, channelId, orderId string) (*GetRenewUserResultResponse, error) {
	path := buildUri(GET_RENEW_USER_RESULT, channelId, orderId)
	result := &GetRenewUserResultResponse{}
	err := getWeconnectJson(ctx, nil, path, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func CreateActiveUserTask(ctx context.Context, channelId string, userIds []string) (string, error) {
	path := buildUri(CREATE_ACTIVE_USER_TASK, channelId)
	var result string
	err := postJson(ctx, userIds, path, &result)
	return result, err
}

func GetActiveUserTaskResult(ctx context.Context, channelId, taskId string) (*ActiveUserTaskResult, error) {
	path := buildUri(Get_ACTIVE_USER_TASK_RESULT, channelId, taskId)
	result := &ActiveUserTaskResult{}
	err := getWeconnectJson(ctx, nil, path, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func TransferUsers(ctx context.Context, channelId string, requests []TransferUsersRequest) ([]TransferUsersResponse, error) {
	path := buildUri(TRANSFER_USER, channelId)
	results := []TransferUsersResponse{}
	err := postJson(ctx, requests, path, &results)
	return results, err
}

func GetActiveUserDetail(ctx context.Context, channelId, userId string) (*ActiveUser, error) {
	path := buildUri(GET_ACTIVE_USER_DETAIL, channelId, userId)
	result := &ActiveUser{}
	err := getWeconnectJson(ctx, nil, path, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func ShareActiveCode(ctx context.Context, channelId string, req ShareActiveCodeRequest) (string, error) {
	path := buildUri(SHARE_ACTIVE_CODE, channelId)
	var result string
	err := postJson(ctx, req, path, &result)
	return result, err
}

func GetShareActiveCodeResult(ctx context.Context, channelId, taskId string) (ShareActiveCodeResult, error) {
	path := buildUri(GET_SHARE_ACTIVE_CODE_RESULT, channelId, taskId)
	result := ShareActiveCodeResult{}
	err := getWeconnectJson(ctx, nil, path, &result)
	if err != nil {
		return result, err
	}
	return result, nil
}

func ConvertChatExternalUserIdToUnionId(ctx context.Context, channelId string, req *ConvertChatExternalUserIdToUnionIdRequest) ([]ConvertChatExternalUserIdToUnionIdResult, error) {
	path := buildUri(CONVERT_CHAT_EXTERNAL_USERID_TO_UNIONID, channelId)
	result := []ConvertChatExternalUserIdToUnionIdResult{}
	err := postJson(ctx, req, path, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func CreateWechatCoupon(ctx context.Context, data *CreateWechatCouponRequest) (*CreateWechatCouponResponse, error) {
	var response CreateWechatCouponResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_V2_PROXY, CHANNEL_TRADE_ORIGIN_WEAPP), &response)
	return &response, err
}

func RedeemedWechatCoupon(ctx context.Context, data *RedeemedWechatCouponRequest) (*RedeemedWechatCouponResponse, error) {
	var response RedeemedWechatCouponResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_V2_PROXY, CHANNEL_TRADE_ORIGIN_WEAPP), &response)
	return &response, err
}

func UpdateWechatCoupon(ctx context.Context, data *UpdateWechatCouponRequest) (*UpdateWechatCouponResponse, error) {
	var response UpdateWechatCouponResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_V2_PROXY, CHANNEL_TRADE_ORIGIN_WEAPP), &response)
	return &response, err
}

func UpdateWechatCouponStock(ctx context.Context, data *UpdateWechatCouponStockRequest) (*UpdateWechatCouponStockResponse, error) {
	var response UpdateWechatCouponStockResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_V2_PROXY, CHANNEL_TRADE_ORIGIN_WEAPP), &response)
	return &response, err
}

func GetWechatCouponSign(ctx context.Context, data map[string]interface{}) (*GetWechatCouponSignResponse, error) {
	var response GetWechatCouponSignResponse
	err := postJson(ctx, data, fmt.Sprintf(PAY_SIGN_URL, CHANNEL_TRADE_ORIGIN_WEAPP)+"?signType=HMAC_SHA256", &response)
	return &response, err
}

func ReturnWechatCoupon(ctx context.Context, data *ReturnWechatCouponRequest) (*ReturnWechatCouponResponse, error) {
	var response ReturnWechatCouponResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_V2_PROXY, CHANNEL_TRADE_ORIGIN_WEAPP), &response)
	return &response, err
}

func GetWechatCoupon(ctx context.Context, data *GetWechatCouponRequest) (*GetWechatCouponResponse, error) {
	var response GetWechatCouponResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_V2_PROXY, CHANNEL_TRADE_ORIGIN_WEAPP), &response)
	return &response, err
}

func UploadWechatMarketingImage(ctx context.Context, data *UploadWechatMarketingImageRequest) (*UploadWechatMarketingImageResponse, error) {
	var response UploadWechatMarketingImageResponse
	urlValues := &url.Values{}
	urlValues.Set("url", data.ImageUrl)
	urlValues.Set("quncrmAccountId", data.QuncrmAccountId)
	err := postJson(ctx, map[string]string{}, "/wechat_app/v2/uploadImage?"+urlValues.Encode(), &response)
	return &response, err
}

func CorrectExternalUser(ctx context.Context, req *CorrectExternalUserRequest) error {
	err := postJson(ctx, req, fmt.Sprintf(CORRECT_EXTERNAL_USER, req.ChannelId, req.OpenId), nil)
	return err
}

func ConvertOpenIdAndUnionIdToExternalUserId(ctx context.Context, openId, unionId, channelId string) error {
	req := map[string]string{
		"openId":  openId,
		"unionId": unionId,
	}
	return postJson(ctx, req, fmt.Sprintf(CONVERT_TO_EXTERNAL_USER_ID, channelId), nil)
}

func DownloadMiniProgramQrcode(ctx context.Context, channelId, savePath string, req *DownloadMiniProgramQrcodeRequest) error {
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(DOWNLOAD_MINI_PROGRAM_QR_CODE, channelId, cast.ToString(req.Unlimit))
	body, resp, err := extension.RequestClient.PostJson(ctx, CHANNEL_SERVICE_NAME, path, req, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	weResp := new(WeconnectResponse)
	err = unmarshal(ctx, body, weResp)
	// 如果下载文件成功，body 一定无法通过 json 解析，只要解析成功则说明下载文件失败，body 中是报错信息
	if err == nil {
		var result interface{}
		// 解析报错信息后返回报错
		err = parseWeconnectResponse(ctx, weResp, &result)
		if err != nil {
			return err
		}
	}

	// 创建一个文件用于保存
	out, err := os.Create(savePath)
	if err != nil {
		return err
	}
	defer out.Close()

	// 将响应流和文件流对接起来
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return err
	}
	return nil
}

func CreateCustomerAcquisitionLink(ctx context.Context, data *ProxyRequest) (*CreateCustomerAcquisitionLinkResponse, error) {
	var response CreateCustomerAcquisitionLinkResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func GetCustomerAcquisitionLink(ctx context.Context, data *ProxyRequest) (*GetCustomerAcquisitionLinkResponse, error) {
	var response GetCustomerAcquisitionLinkResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func UpdateCustomerAcquisitionLink(ctx context.Context, data *ProxyRequest) (*UpdateCustomerAcquisitionLinkResponse, error) {
	var response UpdateCustomerAcquisitionLinkResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func DeleteCustomerAcquisitionLink(ctx context.Context, data *ProxyRequest) (*DeleteCustomerAcquisitionLinkResponse, error) {
	var response DeleteCustomerAcquisitionLinkResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func GetCustomerAcquisitionQuota(ctx context.Context, data *ProxyRequest) (*GetCustomerAcquisitionQuotaResponse, error) {
	var response GetCustomerAcquisitionQuotaResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_PROXY, data.ChannelId), &response)
	return &response, err
}

func GetSuiteAppInfo(ctx context.Context, corpId, suiteId string) (*GetSuiteAppInfoResponse, error) {
	path := buildUri(GET_SUITE_APP_INFO, corpId, suiteId)
	result := &GetSuiteAppInfoResponse{}
	err := getWeconnectJson(ctx, nil, path, result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func ListMerchantCashCoupons(ctx context.Context, data *ProxyRequest) (*ListMerchantCashCouponsResponse, error) {
	var response ListMerchantCashCouponsResponse
	err := postJson(ctx, data, fmt.Sprintf(WECONNECT_V2_PROXY, CHANNEL_TRADE_ORIGIN_WEAPP), &response)
	return &response, err
}

func GetPaymentConfigurationByChannel(ctx context.Context, origin, channelId string) (*PaymentCredentialConfigurationResponse, error) {
	response := &PaymentCredentialConfigurationResponse{}
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	path := fmt.Sprintf(GET_PAYMENT_CONFIGURATION_BY_CHANNEL, origin, util.GetAccountId(ctx), channelId)
	err := getWeconnectJson(ctx, &url.Values{}, path, response)
	return response, err
}

func AddOrRemoveCallback(ctx context.Context, req AddOrRemoveCallbackRequest) error {
	path := fmt.Sprintf(ADD_OR_REMOVE_CALLBACK, req.Platform, req.AccountId, req.MsgType, req.SubType, req.Action)
	result := map[string]interface{}{}
	data := map[string]interface{}{
		"secretKey": WEBHOOK_SIGNATURE_KEY,
		"isInner":   true,
		"url":       req.CallbackUrl,
	}
	err := postJson(ctx, data, path, &result)
	return err
}

func ListChatMessageHistories(ctx context.Context, corpId string, req ListChatMessageHistoriesRequest) (*ListChatMessageHistoriesResponse, error) {
	response := &ListChatMessageHistoriesResponse{}
	path := fmt.Sprintf(LIST_CHAT_MESSAGE_HISTORIES, corpId)
	err := postJson(ctx, &req, path, response)
	return response, err
}

func ListChatMessageHistoryPreviews(ctx context.Context, corpId string, req ListChatMessageHistoryPreviewsRequest) (*ListChatMessageHistoryPreviewsResponse, error) {
	response := &ListChatMessageHistoryPreviewsResponse{}
	path := fmt.Sprintf(LIST_CHAT_MESSAGE_HISTORY_PREVIEWS, corpId)
	err := postJson(ctx, &req, path, response)
	return response, err
}

func ManualSyncChatMessageHistories(ctx context.Context, corpId string) error {
	path := fmt.Sprintf(MANUAL_SYNC_CHAT_MESSAGE_HISTORIES, corpId)
	err := postJson(ctx, map[string]string{}, path, map[string]string{})
	return err
}

func GetChatMessageHistoriesSyncInfo(ctx context.Context, corpId string) (*ChatMessageHistoriesSyncInfo, error) {
	response := &ChatMessageHistoriesSyncInfo{}
	path := fmt.Sprintf(GET_CHAT_MESSAGE_HISTORIES_SYNC_INFO, corpId)
	err := getWeconnectJson(ctx, &url.Values{}, path, response)
	return response, err
}

func RedoChatMessageHistoryRecognizeSpeech(ctx context.Context, corpId, messageId string) (*ChatMessageHistory, error) {
	response := &ChatMessageHistory{}
	path := fmt.Sprintf(REDO_RECOGNIZE_SPEECH, corpId, messageId)
	err := postJson(ctx, &url.Values{}, path, response)
	return response, err
}

func GetMessageAuditStatus(ctx context.Context, corpId string) (*MessageAuditStatus, error) {
	response := &MessageAuditStatus{}
	path := fmt.Sprintf(GET_MESSAGE_AUDIT_STATUS, corpId)
	err := getWeconnectJson(ctx, &url.Values{}, path, response)
	return response, err
}

func GetMessageAuditPermitUsers(ctx context.Context, corpId string) ([]string, error) {
	response := []string{}
	path := fmt.Sprintf(GET_MESSAGE_AUDIT_PERMIT_USERS, corpId)
	err := getWeconnectJson(ctx, &url.Values{}, path, &response)
	return response, err
}

func ListTemplateMessageResults(ctx context.Context, channelId string, resultIds []string) ([]TemplateMessageResult, error) {
	var resp []TemplateMessageResult
	params := &url.Values{}
	for _, resultId := range resultIds {
		params.Add("resultIds", resultId)
	}
	err := getWeconnectJson(ctx, params, fmt.Sprintf(LIST_TEMPLATE_MESSAGE_RESULTS, channelId), &resp)
	return resp, err
}

func LoginAuthWeshop(ctx context.Context, providerId, code string) (*LoginAuthResponse, error) {
	response := &LoginAuthResponse{}
	query := url.Values{}
	query.Set("code", code)
	path := fmt.Sprintf("%s?%s", fmt.Sprintf(AUTH_WESHOP, providerId), query.Encode())
	err := postJson(ctx, &url.Values{}, path, response)
	return response, err
}

func GetBoughtServiceInfo(ctx context.Context, providerId, appId string, serviceId uint64) (*ServiceInfoResponse, error) {
	response := &ServiceInfoResponse{}
	query := url.Values{}
	query.Set("appId", appId)
	query.Set("serviceId", strconv.FormatUint(serviceId, 10))
	path := fmt.Sprintf("%s?%s", fmt.Sprintf(GET_SERVICE_INFO, providerId), query.Encode())
	err := getWeconnectJson(ctx, &url.Values{}, path, response)
	return response, err
}

func SyncChannel(ctx context.Context, providerId, appId string) (*Channel, error) {
	response := &Channel{}
	body := map[string]interface{}{
		"appId": appId,
	}
	err := postJson(ctx, body, fmt.Sprintf(SYNC_CHANNEL, providerId), response)
	return response, err
}
