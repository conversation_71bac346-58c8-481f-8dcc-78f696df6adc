package component

import (
	"encoding/json"
	"time"
)

type WechatTokenRequest struct {
	Validation bool `json:"validation"`
	AppSecret  bool `json:"appSecret"`
}

type WechatTokenResponse struct {
	Token          string `json:"token"`
	RefreshToken   string `json:"refreshToken"`
	ExpireDateTime int64  `json:"expireDateTime"`
	Expired        bool   `json:"expired"`
}

type WeconnectResponse struct {
	Code    uint32
	Message string
	Data    json.RawMessage
}

type WeconnectError struct {
	ErrorCode    int64  `json:"errorCode"`
	ErrorMessage string `json:"errorMessage"`
}

type Pagination struct {
	PageSize    uint32
	PageNum     uint32
	TotalAmount uint64
	Next        string
	Results     json.RawMessage
}

// Raw follower structure got from weconnect
type Follower struct {
	Id              string   `json:"id"`
	ChannelId       string   `json:"accountId"`
	OpenId          string   `json:"originId"`
	Nickname        string   `json:"nickname"`
	Language        string   `json:"language"`
	Country         string   `json:"country"`
	Province        string   `json:"province"`
	City            string   `json:"city"`
	Gender          string   `json:"gender"`
	Subscribed      bool     `json:"subscribed"`
	SubscribedTime  int64    `json:"subscribeTime"`
	UnsubscribeTime int64    `json:"unsubscribeTime"`
	UnionId         string   `json:"unionId"`
	CreatedAt       int64    `json:"createTime"`
	Avatar          string   `json:"headerImgUrl"`
	Origin          string   `json:"firstSubscribeSource"` //where comes this follower
	Tags            []string `json:"tags"`
	SubscribeSource string   `json:"subscribeSource"`
}

// Raw Channel structure got from weconnect
type Channel struct {
	Id                    string `json:"id"`
	SocialId              string `json:"appId"`
	CorpId                string `json:"corpId"`
	SocialAccount         string `json:"channelAccount"`
	ProviderAppId         string `json:"providerAppId"`
	Name                  string `json:"name"`
	Social                string `json:"channel"` //WEIXIN or WEIBO
	HeadImageUrl          string `json:"headImageUrl"`
	CreatedAt             int64  `json:"createTime"`
	QuncrmAccountId       string `json:"quncrmAccountId"`
	QuncrmEnvironment     string `json:"quncrmEnvironment"`
	Description           string `json:"description"`
	Status                string `json:"status"`
	ServiceUrl            string `json:"serviceUrl"`
	NeedOpenComment       bool   `json:"needOpenComment"`
	AccessStatus          string `json:"accessStatus"`
	PublicKey             string `json:"publicKey"`
	AppPrivateKey         string `json:"appPrivateKey"`
	AppPublicKey          string `json:"appPublicKey"`
	Token                 string `json:"token"`
	EncodingAESKey        string `json:"encodingAESKey"`
	SignType              string `json:"signType"`
	MenuStatus            string `json:"menuStatus"`
	AccountType           string `json:"accountType"`
	HasDevAuthority       bool   `json:"hasDevAuthority"`
	IsTest                bool   `json:"isTest"`
	WeiboAccessStatus     string `json:"weiboAccessStatus,omitempty"`
	IsCustomizedApp       bool   `json:"isCustomizedApp"`
	WeiboAppkey           string `json:"weiboAppkey"`
	WeiboTokenExpireTime  int64  `json:"weiboTokenExpireTime"`
	SuiteId               string `json:"suiteId"`
	FillUnionId           bool   `json:"fillUnionId"`
	UseAppTokenForTagSync bool   `json:"useAppTokenForTagSync"`
	AuthorizePermission   []int  `json:"authorizePermission"`
}

// Qrcode related structures
type Qrcode struct {
	Id             string `json:"id"`
	Name           string `json:"name"`
	Ticket         string `json:"ticket"`
	ImageUrl       string `json:"imageUrl"`
	CreatedAt      int64  `json:"createTime"`
	ScanCount      uint64 `json:"scanCount"`
	SubscribeCount uint64 `json:"subscribeCount"`
	SceneId        uint64 `json:"sceneId"`
	Temporary      bool   `json:"temporary"`
	ExpireSeconds  int64  `json:"expireSeconds"`
	Expired        bool   `json:"expired"`
}

// Structure for new created qrcode
type NewQrcode struct {
	Name          string `json:"name"`
	Description   string `json:"description,omitempty"`
	Type          string `json:"type"`
	ReplyMessage  string `json:"replyMessage,omitempty"`
	Temporary     bool   `json:"temporary"`
	ExpireSeconds uint32 `json:"expireSeconds"`
}

type UserQuery struct {
	OriginIds []string `json:"originIds"`
	UserIds   []string `json:"userIds"`
	Tags      []string `json:"tags"`
	SendToAll bool     `json:"sendToAll,omitempty"`
}

type TemplateMessage struct {
	TemplateId  string                      `json:"templateId"`
	Url         string                      `json:"url,omitempty"`
	TopColor    string                      `json:"topcolor"`
	Page        string                      `json:"page,omitempty"`
	Data        interface{}                 `json:"data"` // 微信订阅消息：{"key1":{"value": "value1"}}; 抖音订阅消息：{"key1": "value1"}
	MiniProgram *TemplateMessageMiniProgram `json:"miniProgram,omitempty"`
}

type TemplateMessageMiniProgram struct {
	AppId    string `json:"appId"`
	PagePath string `json:"pagePath"`
}

type TplMessage struct {
	ScheduleTime    uint64          `json:"scheduleTime,omitempty"`
	UserQuery       UserQuery       `json:"userQuery"`
	TemplateMessage TemplateMessage `json:"templateMessage"`
}

type SingleTargetTplMessage struct {
	Id               string          `json:"id,omitempty"`
	OriginId         string          `json:"originId"`
	TemplateMessage  TemplateMessage `json:"templateMessage"`
	Status           string          `json:"status,omitempty"`
	ErrorType        string          `json:"errorType,omitempty"`
	ErrorDescription string          `json:"errorDescription,omitempty"`
}

type BriefTemplate struct {
	TemplateId string `json:"template_id"`
	Title      string `json:"title"`
}

type SubscribeMsg struct {
	OriginId         string          `json:"originId,omitempty"`
	AppSecret        bool            `json:"appSecret,omitempty"`
	SubscribeMessage TemplateMessage `json:"subscribeMessage"`
}

type SubscribeMsgResult struct {
	Id               string `json:"id,omitempty"`
	Status           string `json:"status,omitempty"`
	ErrCode          string `json:"errCode,omitempty"`
	ErrorDescription string `json:"errorDescription,omitempty"`
}

type Message struct {
	ToUser                 string        `json:"toUser"`
	MsgType                string        `json:"msgType"`
	Content                string        `json:"content"`
	Url                    string        `json:"url"`
	ThumbImageUrl          string        `json:"thumbImageUrl"`
	Title                  string        `json:"title"`
	Description            string        `json:"description,omitempty"`
	HqMediaUrl             string        `json:"hqMediaUrl"`
	Articles               []Article     `json:"articles"`
	MaterialId             string        `json:"materialId"`
	MediaId                string        `json:"mediaId,omitempty"`
	AppId                  string        `json:"appId"`
	PagePath               string        `json:"pagePath"`
	EmphasisFirstItem      bool          `json:"emphasisFirstItem"`
	EnableIdTrans          bool          `json:"enableIdTrans"`
	EnableDuplicateCheck   bool          `json:"enableDuplicateCheck"`
	DuplicateCheckInterval uint64        `json:"duplicateCheckInterval"`
	ContentItems           []ContentItem `json:"contentItems"`
}

type MassMessage struct {
	MassiveType string    `json:"massiveType"`
	TotalCount  string    `json:"totalCount"`
	UserQuery   UserQuery `json:"userQuery"`
	MassMessage Message   `json:"massMessage"`
	IsMarketing bool      `json:"isMarketing,omitempty"`
}

type ExternalContactMassMessageResponse struct {
	FailList []string `json:"failList"`
	MsgId    string   `json:"msgId"`
}

type ExternalContactMassMessage struct {
	ExternalUserIds []string      `json:"externalUserIds"`
	ChatType        string        `json:"chatType"`
	ChatIdList      []string      `json:"chatIdList"`
	Sender          string        `json:"sender"`
	Text            Text          `json:"text"`
	Attachments     []*Attachment `json:"attachments,omitempty"`
}

type Text struct {
	Content string `json:"content"`
}

type GetExternalContactMassMessage struct {
	MsgId     string `json:"msgId"`
	UserId    string `json:"userId"`
	Limit     int    `json:"limit"`
	Cursor    string `json:"cursor"`
	RetryMode string `json:"retryMode"`
}

type MassMessageTaskResult struct {
	Sender     string   `json:"sender"`
	MsgId      string   `json:"msgId"`
	Status     string   `json:"status"`
	FailList   []string `json:"failList"`
	StatusDesc string   `json:"statusDesc"`
}

type MassMessageTaskList struct {
	NextCursor string            `json:"nextCursor"`
	Tasks      []MassMessageTask `json:"tasks"`
}

type MassMessageTask struct {
	UserId   string `json:"userId"`
	Status   string `json:"status"`
	SendTime int64  `json:"sendTime"`
}

type ExternalContactMassMessageResult struct {
	NextCursor  string        `json:"nextCursor"`
	SendResults []SendResults `json:"sendResults"`
}

type SendResults struct {
	ExternalUserId string `json:"externalUserId"`
	UserId         string `json:"userId"`
	Status         string `json:"status"`
	SendTime       int64  `json:"sendTime"`
}

type Article struct {
	Title        string `json:"title"`
	Description  string `json:"description"`
	Url          string `json:"url"`
	Author       string `json:"author"`
	Content      string `json:"content"`
	ContentUrl   string `json:"contentUrl"`
	SourceUrl    string `json:"sourceUrl"`
	ShowCoverPic bool   `json:"showCoverPic"`
}

type ContentItem struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type ReplyMessage struct {
	MsgType       string    `json:"msgType,omitempty"`
	Content       string    `json:"content,omitempty"`
	Url           string    `json:"url,omitempty"`
	Title         string    `json:"title,omitempty"`
	Description   string    `json:"description,omitempty"`
	ThumbImageUrl string    `json:"thumbImageUrl,omitempty"`
	HqMediaUrl    string    `json:"hqMediaUrl,omitempty"`
	Articles      []Article `json:"articles,omitempty"`
}

type Menu struct {
	Id           string       `json:"id"`
	Name         string       `json:"name"`
	Keycode      string       `json:"keycode,omitempty"`
	Type         string       `json:"type,omitempty"`
	ParentId     string       `json:"parentId,omitempty"`
	Index        int32        `json:"index"`
	SubMenus     []Menu       `json:"subMenus"`
	AccountId    string       `json:"accountId"`
	ReplyMessage ReplyMessage `json:"replyMessage,omitempty"`
	HitCount     int32        `json:"hitCount"`
}

type PreOrder struct {
	QuncrmAccountId string                 `json:"quncrmAccountId"`
	ChannelId       string                 `json:"weconnectAccountId"`
	TradeType       string                 `json:"tradeType"`
	ProductId       string                 `json:"productId"`
	SpbillCreateIp  string                 `json:"spbillCreateIp"`
	Subject         string                 `json:"subject"`
	Detail          string                 `json:"detail"`
	OutTradeNo      string                 `json:"outTradeNo"`
	TotalFee        int64                  `json:"totalFee"`
	TimeExpire      int64                  `json:"timeExpire"`
	Metadata        map[string]interface{} `json:"metadata"`
	BuyerId         string                 `json:"buyerId,omitempty"`
	SubAppId        string                 `json:"subAppId,omitempty"`
	SubBuyerId      string                 `json:"subBuyerId,omitempty"`
	ProfitSharing   bool                   `json:"profitSharing"`
	Callback        PrepayCallback         `json:"callback,omitempty"`
	AppId           string                 `json:"appId,omitempty"`
	Buyer           PreOrderBuyer          `json:"buyer,omitempty"`
}

type PreOrderBuyer struct {
	OpenId string `json:"openId,omitempty"`
	Pid    string `json:"pid,omitempty"`
}

type PrepayCallback struct {
	Url       string `json:"url"`
	SecretKey string `json:"secretKey"`
}

type PreOrderResponse struct {
	Extension map[string]interface{} `json:"extension"`
}

type V2PreOrderResponse struct {
	TradeNo string `json:"tradeNo"`
}

type PaySignRequest struct {
	QuncrmAccountId string        `json:"quncrmAccountId"`
	ChannelId       string        `json:"weconnectAccountId"`
	Params          PaySignParams `json:"params"`
}

type PaySignParams struct {
	AppId     string `json:"appId"`
	TimeStamp string `json:"timeStamp"`
	NonceStr  string `json:"nonceStr"`
	Package   string `json:"package"`
	SignType  string `json:"signType"`
}

type PaySignResponse struct {
	AppId    string `json:"appId"`
	SignType string `json:"signType"`
	PaySign  string `json:"paySign"`
}

type PaymentConfiguration struct {
	AccountId     string `json:"quncrmAccountId"`
	ChannelType   string `json:"channelType"`
	AppId         string `json:"appId"`
	ChannelId     string `json:"weconnectAccountId"`
	PaymentStatus string `json:"paymentStatus"`
	SellerId      string `json:"sellerId"`
}

type Menus struct {
	Menus  []Menu `json:"menus"`
	Status string `json:"status"`
}

type JsTicket struct {
	Ticket         string `json:"ticket"`
	ExpireDateTime int64  `json:"expireDateTime"`
	Expired        bool   `json:"expired"`
	AppId          string `json:"appId"`
	CorpId         string `json:"corpId"`
}

type ResponseTicket struct {
	ErrorCode int64  `json:"errcode"`
	ErrorMsg  string `json:"errmsg"`
	Ticket    string `json:"ticket"`
	ExpiresIn int64  `json:"expiresIn"`
	AppId     string `json:"appId"`
	CorpId    string `json:"corpId"`
}

type PiccResponseTicket struct {
	Signature string `json:"signature"`
	AppId     string `json:"appId"`
	CorpId    string `json:"corpId"`
	NonceStr  string `json:"nonceStr"`
	Timestamp int64  `json:"timestamp"`
	ErrorCode int64  `json:"errcode"`
}

type Redpack struct {
	AccountId   string                 `json:"quncrmAccountId"`
	ChannelType string                 `json:"channelType,omitempty"`
	SellerId    string                 `json:"sellerId,omitempty"`
	AppId       string                 `json:"appId,omitempty"`
	TradeNo     string                 `json:"tradeNo"`
	DetailId    string                 `json:"detailId,omitempty"`
	TotalFee    int64                  `json:"totalFee"`
	TotalNum    int64                  `json:"totalNum"`
	OpenId      string                 `json:"openId"`
	ActionName  string                 `json:"actionName"`
	SenderName  string                 `json:"senderName"`
	Remark      string                 `json:"remark"`
	Wishing     string                 `json:"wishing"`
	HBType      string                 `json:"hbType,omitempty"`
	SendType    string                 `json:"sendType,omitempty"`
	Status      string                 `json:"status,omitempty"`
	ClientIp    string                 `json:"clientIp"`
	CreateTime  int64                  `json:"createTime,omitempty"`
	ReceiveTime int64                  `json:"receiveTime,omitempty"`
	FailureCode string                 `json:"failureCode,omitempty"`
	FailureMsg  string                 `json:"failureMsg,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	ResultCode  string                 `json:"resultCode,omitempty"`
	ChannelId   string                 `json:"weconnectAccountId"`
}

type BenchmarkRequest struct {
	Secret string  `json:"secret"`
	Steps  []*Step `json:"steps,omitempty"`
}

type Step struct {
	Sleep              uint64 `json:"sleep,omitempty"`
	CalculateFibonacci uint64 `json:"calculateFibonacci,omitempty"`
	OpenTcpSocket      string `json:"openTcpSocket,omitempty"`
}

type DecryptRequest struct {
	JsCode        string `json:"jsCode"`
	EncryptedData string `json:"encryptedData"`
	Iv            string `json:"iv"`
}

type OrderRefundRequest struct {
	AccountId     string                  `json:"quncrmAccountId,omitempty"`
	ChannelId     string                  `json:"weconnectAccountId"`
	OutTradeNo    string                  `json:"outTradeNo"`
	OutRefundNo   string                  `json:"outRefundNo"`
	TotalFee      uint64                  `json:"totalFee,omitempty"`
	RefundFee     uint64                  `json:"refundFee"`
	OpUserId      string                  `json:"opUserId,omitempty"`
	Metadata      interface{}             `json:"metadata"`
	RefundDesc    string                  `json:"refundDesc"`
	Callback      ByteDanceRefundCallBack `json:"callback,omitempty"`
	DisableMsg    bool                    `json:"disableMsg,omitempty"`
	MsgPage       string                  `json:"msgPage,omitempty"`
	ProfitSharing bool                    `json:"profitSharing,omitempty"`
	SpuList       string                  `json:"spuList,omitempty"`
}

type ByteDanceRefundCallBack struct {
	Url       string `json:"url"`
	SecretKey string `json:"secretKey"`
	IsInner   bool   `json:"isInner"`
}

type OrderRefundInfo struct {
	ChannelType   string                  `json:"channelType"`
	AccountId     string                  `json:"quncrmAccountId"`
	ChannelId     string                  `json:"weconnectAccountId"`
	SellerId      string                  `json:"sellerId"`
	TradeNo       string                  `json:"tradeNo"`
	OutTradeNo    string                  `json:"outTradeNo"`
	OutRefundNo   string                  `json:"outRefundNo"`
	RefundNo      string                  `json:"refundNo"`
	RefundFee     float64                 `json:"refundFee"`
	OpUserId      string                  `json:"opUserId"`
	CreateAt      int64                   `json:"createTime"`
	Metadata      interface{}             `json:"metadata"`
	RefundStatus  string                  `json:"refundStatus"`
	FailureCode   string                  `json:"failureCode"`
	FailureMsg    string                  `json:"failureMsg"`
	Extension     RefundExtension         `json:"extension"`
	ProviderAppId string                  `json:"providerAppId"`
	DisableMsg    bool                    `json:"disableMsg"`
	MsgPage       string                  `json:"msgPage"`
	ProfitSharing bool                    `json:"profitSharing"`
	Callback      ByteDanceRefundCallBack `json:"callback"`
}

type RefundExtension struct {
	WechatAppId   string `json:"wechatAppId"`
	BuyerNickname string `json:"buyerNickname"`
	PrepayId      string `json:"prepayId"`
}

type ReportWeappFormIdRequest struct {
	OpenId string `json:"openId"`
	FormId string `json:"formId"`
}

type DeleteProfitSharingReceiverRequest struct {
	Id              string       `json:"id"`
	QuncrmAccountId string       `json:"quncrmAccountId"`
	ChannelId       string       `json:"weconnectAccountId"`
	AppId           string       `json:"appId"`
	Receiver        ReceiverInfo `json:"receiver"`
}

type ReceiverInfo struct {
	AppId        string `json:"appId,omitempty"`
	Type         string `json:"type"`
	Account      string `json:"account"`
	Name         string `json:"name,omitempty"`
	RelationType string `json:"relationType"`
}

type CreateProfitSharingReceiverRequest struct {
	QuncrmAccountId string       `json:"quncrmAccountId"`
	ChannelId       string       `json:"weconnectAccountId"`
	AppId           string       `json:"appId"`
	Receiver        ReceiverInfo `json:"receiver"`
}

type SharingReceiverInfo struct {
	Type        string `json:"type"`
	Account     string `json:"account"`
	Amount      uint64 `json:"amount"`
	Description string `json:"description"`
	Result      string `json:"result"`
	FinishTime  string `json:"finishTime"`
	OutOrderNo  string `json:"outOrderNo"`
	AppId       string `json:"appId"`
	FailReason  string `json:"failReason"`
}

type MultiProfitSharingRequest struct {
	AppId           string                `json:"appId"`
	QuncrmAccountId string                `json:"quncrmAccountId"`
	ChannelId       string                `json:"weconnectAccountId"`
	TransactionId   string                `json:"transactionId"`
	OutOrderNo      string                `json:"outOrderNo"`
	Multi           bool                  `json:"multi"`
	Receivers       []SharingReceiverInfo `json:"receivers"`
}

type GetProfitSharingRequest struct {
	TransactionId string `json:"transactionId"`
}

type OrderMultiProfitSharingResponse struct {
	Results     []OrderMultiProfitSharingInfo `json:"results"`
	TotalAmount int64                         `json:"totalAmount"`
}

type OrderMultiProfitSharingInfo struct {
	Id            string                `json:"id"`
	ChannelType   string                `json:"channelType"`
	AccountId     string                `json:"quncrmAccountId"`
	SellerId      string                `json:"sellerId"`
	SubMchId      string                `json:"subMchId"`
	TransactionId string                `json:"transactionId"`
	OutOrderNo    string                `json:"outOrderNo"`
	Receivers     []SharingReceiverInfo `json:"receivers"`
	OrderId       string                `json:"orderId"`
	Status        string                `json:"status"`
	Amount        uint64                `json:"amount"`
	Description   string                `json:"description"`
	Finished      bool                  `json:"finished"`
	Multi         bool                  `json:"multi"`
	FailureMsg    string                `json:"failureMsg"`
	CreateAt      int64                 `json:"createTime"`
}

func (o *OrderMultiProfitSharingInfo) GetMerchantId() string {
	if o.SubMchId != "" {
		return o.SubMchId
	}

	return o.SellerId
}

type FinishProfitSharingRequest struct {
	QuncrmAccountId string `json:"quncrmAccountId"`
	ChannelId       string `json:"weconnectAccountId"`
	TransactionId   string `json:"transactionId"`
	OutOrderNo      string `json:"outOrderNo"`
	Finished        bool   `json:"finished"`
	Description     string `json:"description"`
	AppId           string `json:"appId"`
}

type GetWeappVersionResponse struct {
	LastVersion    string `json:"lastVersion"`
	ReleaseVersion string `json:"releaseVersion"`
}

type GetWeappChangeLogsResponse struct {
	Results []ChangeLogs `json:"results"`
}

type ChangeLogs struct {
	Id          string `json:"id"`
	TemplateId  string `json:"templateId"`
	Version     string `json:"version"`
	Description string `json:"description"`
	SuiteId     string `json:"suiteId"`
	Status      string `json:"status"`
	Deleted     bool   `json:"deleted"`
	UpdatedAt   int64  `json:"updateTime"`
	CreatedAt   int64  `json:"createTime"`
}

type GetQrCodeRequest struct {
	Path  string `json:"path"`
	Width string `json:"width"`
}

type GetDeliveriesResponse struct {
	Items []DeliveryDetail `json:"deliveries"`
	Count int64            `json:"count"`
}

type DeliveryDetail struct {
	DeliveryId   string            `json:"deliveryId"`
	DeliveryName string            `json:"deliveryName"`
	CanUseCash   int64             `json:"canUseCash"`
	CanGetQuota  int64             `json:"canGetQuota"`
	CashBizId    string            `json:"cashBizId"`
	Services     []DeliveryService `json:"services"`
}

type DeliveryService struct {
	ServiceType int64  `json:"serviceType"`
	ServiceName string `json:"serviceName"`
}

type AddExpressOrderRequest struct {
	AddSource    int64           `json:"addSource"`
	WxAppId      string          `json:"wxAppId"`
	OrderId      string          `json:"orderId"`
	OpenId       string          `json:"openId"`
	DeliveryId   string          `json:"deliveryId"`
	BizId        string          `json:"bizId"`
	CustomRemark string          `json:"customRemark"`
	TagId        int64           `json:"tagId"`
	Sender       OrderContact    `json:"sender"`
	Receiver     OrderContact    `json:"receiver"`
	Cargo        OrderCargo      `json:"cargo"`
	Shop         OrderShop       `json:"shop"`
	Insured      OrderInsured    `json:"insured"`
	Service      DeliveryService `json:"service"`
	ExpectTime   int64           `json:"expectTime"`
	WaybillId    string          `json:"waybillId"`
}

type OrderContact struct {
	Name     string `json:"name"`
	Tel      string `json:"telephone"`
	Mobile   string `json:"mobile"`
	Company  string `json:"company"`
	PostCode string `json:"postCode"`
	Country  string `json:"country"`
	Province string `json:"province"`
	City     string `json:"city"`
	Area     string `json:"area"`
	Address  string `json:"address"`
}

type OrderCargo struct {
	Count      int64              `json:"count"`
	Weight     float64            `json:"weight"`
	SpaceX     float64            `json:"spaceX"`
	SpaceY     float64            `json:"spaceY"`
	SpaceZ     float64            `json:"spaceZ"`
	DetailList []OrderCargoDetail `json:"detailList"`
}

type OrderCargoDetail struct {
	Name  string `json:"name"`
	Count int64  `json:"count"`
}

type OrderShop struct {
	WxaPath    string `json:"wxaPath"`
	ImgUrl     string `json:"imgUrl"`
	GoodsName  string `json:"goodsName"`
	GoodsCount int64  `json:"goodsCount"`
}

type OrderInsured struct {
	UseInsured   int64 `json:"useInsured"`
	InsuredValue int64 `json:"insuredValue"`
}

type ExpressOrderResponse struct {
	OrderId            string        `json:"orderId"`
	WaybillId          string        `json:"waybillId"`
	Waybills           []WaybillData `json:"waybills"`
	DeliveryResultCode int64         `json:"deliveryResultCode"`
	DeliveryResultMsg  string        `json:"deliveryResultMsg"`
	PrintHtml          string        `json:"printHtml"`
	DeliveryId         string        `json:"deliveryId"`
	OrderStatus        string        `json:"orderStatus"`
}

type WaybillData struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type GetExpressOrderPathRequest struct {
	OrderId    string `json:"orderId"`
	OpenId     string `json:"openId"`
	DeliveryId string `json:"deliveryId"`
	WaybillId  string `json:"waybillId"`
}

type GetExpressOrderPathResponse struct {
	OpenId     string        `json:"openId"`
	DeliveryId string        `json:"deliveryId"`
	WaybillId  string        `json:"waybillId"`
	PathNum    int64         `json:"pathNum"`
	Paths      []ExpressPath `json:"paths"`
}

type ExpressPath struct {
	ActionTime int64  `json:"actionTime"`
	ActionType int64  `json:"actionType"`
	ActionMsg  string `json:"actionMsg"`
}

type GetAllExpressAccountsResponse struct {
	Count    int64            `json:"count"`
	Accounts []ExpressAccount `json:"accounts"`
}

type ExpressAccount struct {
	BizId           string            `json:"bizId"`
	DeliveryId      string            `json:"deliveryId"`
	CreateTime      int64             `json:"createTime"`
	UpdateTime      int64             `json:"updateTime"`
	StatusCode      string            `json:"statusCode"`
	Alias           string            `json:"alias"`
	RemarkWrongMsg  string            `json:"remarkWrongMsg"`
	RemarkContent   string            `json:"remarkContent"`
	QuotaNum        int64             `json:"quotaNum"`
	QuotaUpdateTime int64             `json:"quotaUpdateTime"`
	Services        []DeliveryService `json:"services"`
}

type SendTransferRequest struct {
	QuncrmAccountId string `json:"quncrmAccountId"`
	ChannelId       string `json:"weconnectAccountId"`
	TradeNo         string `json:"tradeNo"`
	OpenId          string `json:"openId"`
	CheckName       string `json:"checkName,omitempty"` // in(FORCE_CHECK|NO_CHECK)
	Amount          uint64 `json:"amount"`
	Desc            string `json:"desc"`
	AppId           string `json:"appId"`
	ReceiveUserName string `json:"receiveUserName"`
	// 付款到银行卡专有
	EncBankNo   string `json:"encBankNo"`
	EncTrueName string `json:"encTrueName"`
	BankCode    string `json:"bankCode"`
	// 企业微信支付专有，参考 https://work.weixin.qq.com/api/doc/90000/90135/90278
	MsgType  string `json:"msgType"`  // 付款消息类型，NORMAL_MSG：普通付款消息 APPROVAL_MSG：审批付款消息
	ActName  string `json:"actName"`  // 项目名称，最长50个utf8字符
	ClientIp string `json:"clientIp"` // 调用接口的机器IP地址
}

type TransferDetailResponse struct {
	Id              string `json:"id"`
	QuncrmAccountId string `json:"quncrmAccountId"`
	ChannelType     string `json:"channelType"`
	SellerId        string `json:"sellerId"`
	Amount          uint64 `json:"amount"`
	Desc            string `json:"desc"`
	TradeNo         string `json:"tradeNo"`
	ResultCode      string `json:"resultCode"`
	FailureCode     string `json:"failureCode"`
	FailureMsg      string `json:"failureMsg"`
	Status          string `json:"status"`
	CreatedTime     uint64 `json:"createdTime"`
	DetailId        string `json:"detailId"` // 微信流水号，与 weconnect 保持一致
	// 付款到零钱
	AppId           string `json:"appId"`
	OpenId          string `json:"openId"`
	CheckName       string `json:"checkName"`
	ClientIp        string `json:"clientIp"`
	ReceiveUserName string `json:"receiveUserName"`
	// 付款到银行卡
	EncBankNo    string `json:"encBankNo"`
	EncTrueName  string `json:"encTrueName"`
	BankCode     string `json:"bankCode"`
	CmmsAmount   uint64 `json:"cmmsAmount"`   // 手续费
	PaymentTime  uint64 `json:"paymentTime"`  // 付款成功时间
	TransferTime uint64 `json:"transferTime"` // 转账时间
}

type SendV2TransferRequest struct {
	QuncrmAccountId    string           `json:"quncrmAccountId"`
	ChannelId          string           `json:"weconnectAccountId"`
	AppId              string           `json:"appId"`
	TradeBatchNo       string           `json:"tradeBatchNo"`
	BatchName          string           `json:"batchName"`
	BatchDesc          string           `json:"batchDesc"`
	TotalAmount        uint64           `json:"totalAmount"`
	TotalNum           uint64           `json:"totalNum"`
	TransferDetailList []TransferDetail `json:"transferDetailList"`
}

type SendV3TransferRequest struct {
	AppId           string                `json:"appid"`
	OutTradeNo      string                `json:"out_bill_no"`
	Scene           string                `json:"transfer_scene_id"`
	OpenId          string                `json:"openid"`
	ReceiveUserName string                `json:"user_name"`
	TransferAmount  uint64                `json:"transfer_amount"`
	TransferRemark  string                `json:"transfer_remark"`
	SceneInfoList   []V3TransferSceneInfo `json:"transfer_scene_report_infos"`
}

type V3TransferSceneInfo struct {
	Type    string `json:"info_type"`
	Content string `json:"info_content"`
}

type SendV3TransferResponse struct {
	OutTradeNo   string `json:"out_bill_no"`
	Status       string `json:"state"`
	FailedReason string `json:"fail_reason"`
	ConfirmCode  string `json:"package_info"`
}

type TransferDetail struct {
	TradeDetailNo   string `json:"tradeDetailNo"`
	Amount          uint64 `json:"amount"`
	Desc            string `json:"desc"`
	OpenId          string `json:"openId"`
	ReceiveUserName string `json:"receiveUserName"`
}

type V2TransferDetailResponse struct {
	Id                     string `json:"id"`
	QuncrmAccountId        string `json:"quncrmAccountId"`
	ChannelType            string `json:"channelType"`
	SellerId               string `json:"sellerId"`
	AppId                  string `json:"appId"`
	ReceiveUserName        string `json:"receiveUserName"`
	TradeBatchNo           string `json:"tradeBatchNo"`
	BatchId                string `json:"batchId"`
	TradeDetailNo          string `json:"tradeDetailNo"`
	DetailId               string `json:"detailId"`
	Amount                 int64  `json:"amount"`
	Desc                   string `json:"desc"`
	Status                 string `json:"status"`
	DetailStatus           string `json:"detailStatus"`
	FailCode               string `json:"failCode"`
	FailureCode            string `json:"failureCode"`
	FailureMsg             string `json:"failureMsg"`
	CreateTime             int64  `json:"createTime"`
	DetailStatusUpdateTime int64  `json:"detailStatusUpdateTime"`
}

type V3TransferDetailResponse struct {
	Status       string    `json:"state"`
	OutTradeNo   string    `json:"out_bill_no"`
	DetailId     string    `json:"transfer_bill_no"`
	FailedReason string    `json:"fail_reason"`
	UpdatedAt    time.Time `json:"update_time"`
	CreatedAt    time.Time `json:"create_time"`
}

type Department struct {
	Id       int    `json:"id"`
	Name     string `json:"name"`
	ParentId int    `json:"parentid"`
	Order    int    `json:"order"`
	NameEn   string `json:"name_en"`
}

type User struct {
	Name             string `json:"name"`
	UserId           string `json:"userid"`
	Mobile           string `json:"mobile"`
	Gender           int    `json:"gender"` // 0表示未定义，1表示男性，2表示女性
	Email            string `json:"email"`
	Avatar           string `json:"avatar"`
	Status           int    `json:"status"` // 激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业
	Position         string `json:"position"`
	ExternalPosition string `json:"externalPosition"`
	Order            []int  `json:"order"`
	Department       []int  `json:"department"` // 成员所属部门id列表
	IsLeaderInDept   []int  `json:"isLeaderInDept"`
	Telephone        string `json:"telephone"`
	Alias            string `json:"alias"`
	Qrcode           string `json:"qrcode"`
}

type ContactWay struct {
	Id         string   `json:"id"`
	ReqId      string   `json:"reqId"`
	CreateTime int64    `json:"createTime"`
	Type       string   `json:"type"`
	Scene      string   `json:"scene"`
	Remark     string   `json:"remark"`
	SkipVerify bool     `json:"skipVerify"`
	State      string   `json:"state"`
	UserIds    []string `json:"userIds"`
	PartyIds   []int64  `json:"partyIds"`
	// 渠道 ID
	AccountId  string            `json:"accountId"`
	Name       string            `json:"name"`
	ImageUrl   string            `json:"imageUrl"`
	Tags       []string          `json:"tags"`
	Extra      string            `json:"extra"`
	WelcomeMsg ContactWelcomeMsg `json:"welcomeMsg,omitempty"`
	ConfigId   string            `json:"configId"`
	CreatedAt  int64             `json:"createdAt"`
	Group      string            `json:"group"`
}

type ContactWayRequest struct {
	Type       string             `json:"type"`
	Scene      string             `json:"scene"`
	Remark     string             `json:"remark"`
	SkipVerify bool               `json:"skipVerify"`
	State      string             `json:"state"`
	UserIds    []string           `json:"userIds"`
	PartyIds   []int64            `json:"partyIds"`
	Name       string             `json:"name"`
	Tags       []string           `json:"tags"`
	Extra      string             `json:"extra"`
	WelcomeMsg *ContactWelcomeMsg `json:"welcomeMsg,omitempty"`
	Group      string             `json:"group"`
}

type SearchContactWayRequest struct {
	Name       string   `json:"name"`
	Type       string   `json:"type,omitempty"`
	Group      string   `json:"group,omitempty"`
	UserId     string   `json:"userId,omitempty"`
	PageSize   string   `json:"pageSize"`
	PageNum    string   `json:"pageNum"`
	Ids        []string `json:"ids"`
	Groups     []string `json:"groups"`
	AccountIds []string `json:"accountIds"`
	OrderBy    string   `json:"orderBy"`
	Ordering   string   `json:"ordering"`
}

type SearchContactWayResponse struct {
	PageSize    int          `json:"pageSize"`
	PageNum     int          `json:"pageNum"`
	TotalAmount int          `json:"totalAmount"`
	OrderBy     string       `json:"orderBy"`
	Ordering    string       `json:"ordering"`
	Results     []ContactWay `json:"results"`
}

type ListContactWelcomeMsgsRequest struct {
	PageSize     int      `json:"pageSize,omitempty"`
	PageNum      int      `json:"pageNum,omitempty"`
	DepartmentId int      `json:"departmentId,omitempty"`
	OriginIds    []string `json:"originIds,omitempty"`
	SearchKey    string   `json:"searchKey,omitempty"`
}

type ListContactWelcomeMsgsResponse struct {
	PageSize    int                `json:"pageSize"`
	PageNum     int                `json:"pageNum"`
	TotalAmount int                `json:"totalAmount"`
	OrderBy     string             `json:"orderBy"`
	Ordering    string             `json:"ordering"`
	Results     []WelcomeMsgDetail `json:"results"`
}

type WelcomeMsgDetail struct {
	Subscribed bool              `json:"subscribed"`
	OriginId   string            `json:"originId"`
	Nickname   string            `json:"nickname"`
	Department []int             `json:"department"`
	WelcomeMsg ContactWelcomeMsg `json:"welcomeMsg"`
}

type ContactWelcomeMsg struct {
	Text             *ContactWelcomeMsgText             `json:"text,omitempty"`
	Image            *ContactWelcomeMsgImage            `json:"image,omitempty"`
	Link             *ContactWelcomeMsgLink             `json:"link,omitempty"`
	MiniProgram      *ContactWelcomeMsgMiniprogram      `json:"miniprogram,omitempty"`
	Attachments      []*Attachment                      `json:"attachments,omitempty"`
	PlaceholderRules []ContactWelcomeMsgPlaceholderRule `json:"placeholderRules,omitempty"`
}

type Attachment struct {
	MsgType     string                        `json:"msgType"`
	Image       *ContactWelcomeMsgImage       `json:"image,omitempty"`
	Link        *ContactWelcomeMsgLink        `json:"link,omitempty"`
	MiniProgram *ContactWelcomeMsgMiniprogram `json:"miniprogram,omitempty"`
	Video       *ContactWelcomeMsgVideo       `json:"video,omitempty"`
}

type ContactWelcomeMsgText struct {
	Content string `json:"content,omitempty"`
}

type ContactWelcomeMsgImage struct {
	Url string `json:"url,omitempty"`
}

type ContactWelcomeMsgLink struct {
	Title  string `json:"title,omitempty"`
	PicUrl string `json:"picUrl,omitempty"`
	Desc   string `json:"desc,omitempty"`
	Url    string `json:"url,omitempty"`
}

type ContactWelcomeMsgMiniprogram struct {
	Title         string `json:"title,omitempty"`
	AppId         string `json:"appId,omitempty"`
	ThumbImageUrl string `json:"thumbImageUrl,omitempty"`
	PagePath      string `json:"pagePath,omitempty"`
}

type ContactWelcomeMsgVideo struct {
	Url string `json:"url,omitempty"`
}

type ContactWelcomeMsgPlaceholderRule struct {
	Placeholder string                `json:"placeholder"`
	Filler      PlaceholderRuleFilter `json:"filler"`
}

type PlaceholderRuleFilter struct {
	Property string `json:"property"`
}

type GroupWelcomeTemplate struct {
	TemplateId  string                           `json:"templateId,omitempty"`
	Text        *GroupWelcomeTemplateText        `json:"text,omitempty"`
	Image       *GroupWelcomeTemplateImage       `json:"image,omitempty"`
	Link        *GroupWelcomeTemplateLink        `json:"link,omitempty"`
	MiniProgram *GroupWelcomeTemplateMiniProgram `json:"miniprogram,omitempty"`
	Video       *GroupWelcomeTemplateVideo       `json:"video,omitempty"`
	File        *GroupWelcomeTemplateFile        `json:"file,omitempty"`
	Notify      bool                             `json:"notify"`
}

type GroupWelcomeTemplateText struct {
	Content string `json:"content,omitempty"`
}

type GroupWelcomeTemplateImage struct {
	Url string `json:"url,omitempty"`
}

type GroupWelcomeTemplateLink struct {
	Title  string `json:"title,omitempty"`
	PicUrl string `json:"picUrl,omitempty"`
	Desc   string `json:"desc,omitempty"`
	Url    string `json:"url,omitempty"`
}

type GroupWelcomeTemplateMiniProgram struct {
	Title         string `json:"title,omitempty"`
	ThumbImageUrl string `json:"thumbImageUrl,omitempty"`
	AppId         string `json:"appId,omitempty"`
	PagePath      string `json:"pagePath,omitempty"`
}

type GroupWelcomeTemplateVideo struct {
	Url string `json:"url,,omitempty"`
}

type GroupWelcomeTemplateFile struct {
	Url string `json:"url,omitempty"`
}

type GroupchatBrief struct {
	ChatId string `json:"chatId"`
	Status int    `json:"status"`
}

type Groupchat struct {
	ChatId     string            `json:"chatId"`
	Name       string            `json:"name"`
	Owner      string            `json:"owner"`
	CreateTime int64             `json:"createTime"`
	Notice     string            `json:"notice"`
	MemberList []GroupchatMember `json:"members"`
}

type GroupchatMember struct {
	UserId        string  `json:"userId"`
	Type          string  `json:"type"`
	JoinTime      int64   `json:"joinTime"`
	JoinScene     string  `json:"joinScene"`
	UnionId       string  `json:"unionId"`
	Name          string  `json:"name"`
	GroupNickName string  `json:"groupNickNIme"`
	Invitor       Invitor `json:"invitor"`
	State         string  `json:"state"`
}

type Invitor struct {
	UserId string `json:"userId"`
}

type ListStatsChatsRequest struct {
	PageSize  int64
	PageNum   int64
	StartTime int64
	UserIds   []string
	PartyIds  []int
	Ordering  string // 顺序，ASC、DESC
	OrderBy   string // 排序字段 newChat, totalChat
}

type ListStatsStaffContactRequest struct {
	StartTime int64
	EndTime   int64
	UserIds   []string
	PartyIds  []int
}

type ListStatsStaffContactResponse struct {
	Results []StatsStaffContactData `json:"results"`
}

type StatsStaffContactData struct {
	StatTime              int64   `json:"statTime"`
	NewApplyCount         uint64  `json:"newApplyCount"`
	NewContactCount       uint64  `json:"newContactCount"`
	ChatCount             uint64  `json:"chatCount"`
	MessageCount          uint64  `json:"messageCount"`
	ReplyPercentage       float64 `json:"replyPercentage"`
	AvgReplyTime          float64 `json:"avgReplyTime"`
	NegativeFeedbackCount uint64  `json:"negativeFeedbackCount"`
}

type ListStatsChatsResponse struct {
	PageSize    int64            `json:"pageSize"`
	PageNum     int64            `json:"pageNum"`
	TotalAmount int64            `json:"totalAmount"`
	OrderBy     string           `json:"orderBy"`
	Ordering    string           `json:"DESC"`
	Results     []StatsGroupchat `json:"results"`
}

type StatsGroupchat struct {
	Owner string             `json:"owner"`
	Data  StatsGroupchatData `json:"data"`
}

type StatsGroupchatData struct {
	NewChatCnt   uint64 `json:"newChat"`
	ChatTotal    uint64 `json:"totalChat"`
	ChatHasMsg   uint64 `json:"chatHasMsg"`
	NewMemberCnt uint64 `json:"newMember"`
	MemberTotal  uint64 `json:"totalMember"`
	MemberHasMsg uint64 `json:"memberHasMsg"`
	MsgTotal     uint64 `json:"totalMsg"`
}

type GroupTagsResponse struct {
	GroupId    string `json:"groupId"`
	GroupName  string `json:"groupName"`
	CreateTime int64  `json:"createTime"`
	Order      uint64 `json:"order"`
	// 仅在通过 tagIds 获取标签库标签时使用此字段
	IsDeleted bool          `json:"isDeleted"`
	Tags      []TagResponse `json:"tags"`
}

type GroupchatJoinWaysRequest struct {
	Scene          string   `json:"scene"`
	Remark         string   `json:"remark"`
	AutoCreateRoom int      `json:"autoCreateRoom"`
	RoomBaseName   string   `json:"roomBaseName"`
	RoomBaseId     uint64   `json:"roomBaseId"`
	ChatIds        []string `json:"chatIds"`
	State          string   `json:"state"`
}

type GroupchatJoinWays struct {
	ConfigId       string   `json:"configId"`
	Scene          string   `json:"scene"`
	Remark         string   `json:"remark"`
	AutoCreateRoom int      `json:"autoCreateRoom"`
	RoomBaseName   string   `json:"roomBaseName"`
	RoomBaseId     uint64   `json:"roomBaseId"`
	ChatIds        []string `json:"chatIds"`
	State          string   `json:"state"`
	ImageUrl       string   `json:"imageUrl"`
}

type CorpDetail struct {
	ID                  string                  `json:"id"`
	ReqID               string                  `json:"reqId"`
	Name                string                  `json:"name"`
	Type                string                  `json:"type"`
	UserMax             int                     `json:"userMax"`
	AgentMax            int                     `json:"agentMax"`
	AuthInfos           []interface{}           `json:"authInfos"`
	ContactsSync        CorpContactSync         `json:"contactsSync"`
	ExternalContactSync CorpExternalContactSync `json:"externalContactSync"`
	MessageAudit        CorpMessageAudit        `json:"messageAudit"`
}

type CorpContactSync struct {
	CorpID         string `json:"corpId"`
	ContactsSecret string `json:"contactsSecret"`
	Token          string `json:"token"`
	EncodingAeskey string `json:"encodingAeskey"`
	ServiceURL     string `json:"serviceUrl"`
	EncryptAppID   string `json:"encryptAppId"`
	EncodingAESKey string `json:"encodingAESKey"`
}

type CorpExternalContactSync struct {
	CorpID         string `json:"corpId"`
	ContactsSecret string `json:"contactsSecret"`
	ServiceURL     string `json:"serviceUrl"`
	EncryptAppID   string `json:"encryptAppId"`
}

type TagResponse struct {
	Id         string `json:"id"`
	Name       string `json:"name"`
	CreateTime int64  `json:"createTime"`
	Order      uint64 `json:"order"`
	// 仅在通过 tagIds 获取标签库标签时使用此字段
	IsDeleted bool `json:"isDeleted"`
}

type CorpMessageAudit struct {
	CorpSecret string                   `json:"corpSecret"`
	RsaKeys    []CorpMessageAuditRsaKey `json:"rsaKeys"`
}

type CorpMessageAuditRsaKey struct {
	KeyVersion int    `json:"keyVersion"`
	PrivateKey string `json:"privateKey"`
}

type AddTagsRequest struct {
	GroupId   string          `json:"groupId,omitempty"`
	GroupName string          `json:"groupName"`
	Order     uint64          `json:"order"`
	Tags      []AddRequestTag `json:"tags"`
}

type AddRequestTag struct {
	Name  string `json:"name"`
	Order string `json:"order"`
}

type EditTagRequest struct {
	Id    string `json:"id"`
	Name  string `json:"name"`
	Order uint64 `json:"order"`
}

type DeleteTagsRequest struct {
	TagIds   []string `json:"tagIds"`
	GroupIds []string `json:"groupIds"`
}

type EditMemberTagsRequest struct {
	UserId         string   `json:"userId"`
	ExternalUserId string   `json:"externalUserId"`
	AddTag         []string `json:"addTag"`
	RemoveTag      []string `json:"removeTag"`
	UseAppSecret   bool     `json:"useAppTokenForTagSync"`
	ClearCache     bool     `json:"clearCache"`
}

type UserWechatTagOperator struct {
	WxTagId   string   `json:"wxTagId"`
	OriginIds []string `json:"originIds"`
}

type ListSystemExternalUserResponse struct {
	ExternalUserData []GetSystemExternalUserResponse `json:"externalUserData"`
}

type GetSystemExternalUserResponse struct {
	MemberId        string `json:"memberId"`
	OriginId        string `json:"originId"`
	UnionId         string `json:"unionId"`
	SubscribeSource string `json:"subscribeSource"`
	Extra           string `json:"extra"`
	// 毫秒级时间戳
	SubscribeTime int64        `json:"subscribeTime"`
	CreateTime    int64        `json:"createTime"`
	Enable        bool         `json:"enable"`
	Authorized    bool         `json:"authorized"`
	Subscribed    bool         `json:"subscribed"`
	FollowUser    []FollowUser `json:"followUser"`
	Profiles      Profiles     `json:"profiles"`
	Gender        string       `json:"gender"`
	NickName      string       `json:"nickName"`
	Errmsg        string       `json:"errmsg"`
	Errcode       int          `json:"errcode"` // 正常返回：0
}

type Profiles struct {
	MassSendUsageCount int   `json:"massSendUsageCount"`
	FirstSubscribeTime int64 `json:"firstSubscribeTime"`
}

type GetExternalUserResponse struct {
	Errmsg       string       `json:"errmsg"`
	FollowUser   []FollowUser `json:"followUser"`
	ExternalUser ExternalUser `json:"externalUser"`
	Errcode      int          `json:"errcode"` // 正常返回：0
}

type ListExternalUserResponse struct {
	Errmsg     string                   `json:"errmsg"`
	Errcode    int                      `json:"errcode"` // 正常返回：0
	NextCursor string                   `json:"nextCursor"`
	Results    []ListExternalUserResult `json:"results"`
}

type ListExternalUserResult struct {
	FollowUser   FollowUser   `json:"follower"`
	ExternalUser ExternalUser `json:"externalUser"`
}

type ExternalUser struct {
	Gender         string `json:"gender"`
	ExternalUserID string `json:"externalUserId"`
	Avatar         string `json:"avatar"`
	Type           int    `json:"type"`
	UnionID        string `json:"unionId"`
	Name           string `json:"name"`
}

// 此结构在多处响应结构中使用，使用其中的字段时需注意 weconnect 的实际响应值，某些字段可能是不返回的
type FollowUser struct {
	UserID        string          `json:"userId"`
	Tags          []FollowUserTag `json:"tags"`
	TagIds        []string        `json:"tagIds"`
	Description   string          `json:"description"`
	RemarkMobiles []string        `json:"remarkMobiles"`
	State         string          `json:"state"`
	Operator      string          `json:"operator"`
	Remark        string          `json:"remark"`
	AddWay        int             `json:"addWay"`
	// 秒级时间戳
	CreateTime int64 `json:"createTime"`
	// 以下字段仅 GetSystemExternalUserResponse 中有值，是 weconnect 内部处理的字段
	Subscribed bool `json:"subscribed"`
	// 毫秒级时间戳
	SubscribeTime int64 `json:"subscribeTime"`
}

type FollowUserTag struct {
	TagName   string `json:"tagName"`
	GroupName string `json:"groupName"`
	Type      int    `json:"type"`
	TagId     string `json:"tagId"`
}

type AppDetail struct {
	Id               int              `json:"id"`
	Name             string           `json:"name"`
	AllowEmployees   AllowEmployees   `json:"allowEmployees"`
	AllowDepartments AllowDepartments `json:"allowDepartments"`
}

type AllowEmployees struct {
	Users []User `json:"user"`
}

type AllowDepartments struct {
	Partyid []int64 `json:"partyid"`
}

type SendAliyunqaSmsRequest struct {
	OutId          string `json:"outId"`
	TaskName       string `json:"taskName"`
	SignName       string `json:"signName"`
	SmsTemplateId  string `json:"smsTemplateId"`
	PhoneNumbers   string `json:"phoneNumbers"`
	PlatformId     string `json:"platformId"`
	ChannelType    int    `json:"channelType"`
	IsVariable     int    `json:"isVariable"`
	TemplateParams string `json:"templateParam"`
	Creator        string `json:"creator,omitempty"`
}

type SendAliyunqaSmsResponse struct {
	Data      interface{}   `json:"data"`
	ErrorCode interface{}   `json:"errorCode"`
	ErrorDesc string        `json:"errorDesc"`
	ExStack   interface{}   `json:"exStack"`
	Opers     []interface{} `json:"opers"`
	Solution  interface{}   `json:"solution"`
	Success   bool          `json:"success"`
	TraceId   string        `json:"traceId"`
}

type AliyunqaSmsDetailResponse struct {
	Data      AliyunqaSmsDetailData `json:"data"`
	ErrorCode interface{}           `json:"errorCode"`
	ErrorDesc interface{}           `json:"errorDesc"`
	ExStack   interface{}           `json:"exStack"`
	Opers     []interface{}         `json:"opers"`
	Solution  interface{}           `json:"solution"`
	Success   bool                  `json:"success"`
	TraceId   string                `json:"traceId"`
}

type GetAliyunqaResourceProxyRequest struct {
	RequestName string         `json:"requestName"`
	Params      AliGetResPacks `json:"params"`
}

type AliGetResPacks struct {
	Template  string `json:"template"`
	ProductId string `json:"productId"`
}

type GetAliyunqaResourceProxyResponse struct {
	Success   bool                      `json:"success"`
	TraceId   string                    `json:"traceId"`
	ErrorCode string                    `json:"errorCode"`
	ErrorDesc string                    `json:"errorDesc"`
	Data      AliyunqaResourceProxyData `json:"data"`
}

type AliyunqaResourceProxyData struct {
	CurrentPage           int    `json:"currentPage"`
	PageSize              int    `json:"pageSize"`
	PageCount             int    `json:"pageCount"`
	TotalCount            int    `json:"totalCount"`
	BagsInfo              string `json:"bagsInfo"`
	EffectiveInstanceFlag bool   `json:"effectiveInstanceFlag"`
	SlrGrantedFlag        bool   `json:"slrGrantedFlag"`
}

type AliyunqaBagInfo struct {
	CurrCapacity int `json:"currCapacity"`
}

type SendAliyunqaResourceProxyRequest struct {
	RequestName string             `json:"requestName"`
	Params      AliConsumeResPacks `json:"params"`
}

type AliConsumeResPacks struct {
	ConsumeTime  int    `json:"consumeTime"`
	ConsumeType  string `json:"consumeType"`
	ProductId    string `json:"productId"`
	ConsumeInfos string `json:"consumeInfos"`
	Template     string `json:"template"`
	TenantId     string `json:"tenantId"`
}

type SendAliyunqaResourceProxyResponse struct {
	Success   bool                `json:"success"`
	TraceId   string              `json:"traceId"`
	ErrorCode string              `json:"errorCode"`
	ErrorDesc string              `json:"errorDesc"`
	Data      AliyunqaConsumeData `json:"data"`
}

type AliyunqaConsumeData struct {
	Count int `json:"count"`
}

type AliyunqaSmsDetailData struct {
	TemplateStatus   int         `json:"templateStatus"`
	TemplateType     int         `json:"templateType"`
	Reason           interface{} `json:"reason"`
	SmsTemplateCode  interface{} `json:"smsTemplateCode"`
	TemplateName     string      `json:"templateName"`
	TemplateContent  string      `json:"templateContent"`
	SmsContentSuffix string      `json:"smsContentSuffix"`
	Remark           string      `json:"remark"`
	IsVariable       int         `json:"isVariable"`
	Id               string      `json:"id"`
	PlatformId       string      `json:"platformId"`
}

type AliyunqaOem struct {
	TenantId          string `json:"tenantId"`
	WorkspaceId       string `json:"workspaceId"`
	TenantRegion      string `json:"tenantRegion"`
	ServerEnvironment string `json:"serverEnvironment"`
}

type ByteDancePrePayRequest struct {
	ChannelId  string         `json:"weconnectAccountId"`
	OutTradeNo string         `json:"outTradeNo"`
	TotalFee   int64          `json:"totalFee"`
	Subject    string         `json:"subject"`
	Detail     string         `json:"detail"`
	ValidTime  int64          `json:"validTime"`
	DisableMsg bool           `json:"disableMsg,omitempty"`
	MsgPage    string         `json:"msgPage,omitempty"`
	SubMchId   string         `json:"subMchId,omitempty"`
	SpuList    string         `json:"spuList,omitempty"`
	Callback   PrepayCallback `json:"callback,omitempty"`
}

type ByteDancePrePayResponse struct {
	ChannelType string    `json:"channelType"`
	ChannelId   string    `json:"weconnectAccountId"`
	AppId       string    `json:"appId"`
	Subject     string    `json:"subject"`
	Detail      string    `json:"detail"`
	OutTradeNo  string    `json:"outTradeNo"`
	TotalFee    int64     `json:"totalFee"`
	CreateTime  int64     `json:"createTime"`
	ValidTime   int64     `json:"validTime"`
	ProviderId  string    `json:"providerId"`
	DisableMsg  bool      `json:"disableMsg"`
	MsgPage     string    `json:"msgPage"`
	Extension   Extension `json:"extension"`
	TradeStatus string    `json:"tradeStatus"`
	FailureCode string    `json:"failureCode"`
	FailureMsg  string    `json:"failureMsg"`
}

type Extension struct {
	OrderId    string `json:"order_id"`
	OrderToken string `json:"order_token"`
}

type SendWelcomeMessageRequest struct {
	WelcomeCode      string                             `json:"welcomeCode"`
	Text             ContactWelcomeMsgText              `json:"text"`
	Images           []ContactWelcomeMsgImage           `json:"images"`
	Links            []ContactWelcomeMsgLink            `json:"links"`
	Miniprograms     []ContactWelcomeMsgMiniprogram     `json:"miniprograms"`
	Attachments      []*Attachment                      `json:"attachments,omitempty"`
	PlaceholderRules []ContactWelcomeMsgPlaceholderRule `json:"placeholderRules"`
}

type CreateGroupMessageRequest struct {
	UserQuery    CreateGroupMessageUserQuery `json:"userQuery"`
	SenderId     string                      `json:"senderId"`
	CreatedBy    string                      `json:"createdBy"`
	ScheduleTime int64                       `json:"scheduleTime,omitempty"`
	GroupMessage GroupMessage                `json:"groupMessage"`
}

type CreateGroupMessageResponse struct {
	Id           string `json:"id"`
	ReqId        string `json:"reqId"`
	CreatedBy    string `json:"createdBy"`
	Status       string `json:"status"`
	TotalCount   int64  `json:"totalCount"`
	SentCount    int64  `json:"sentCount"`
	ErrorCount   int64  `json:"errorCount"`
	CreateTime   int64  `json:"createTime"`
	ScheduleTime int64  `json:"scheduleTime"`
}

type CreateGroupMessageUserQuery struct {
	MemberIds       []string `json:"memberIds,omitempty"`
	ExternalUserIds []string `json:"externalUserIds,omitempty"`
}

type GroupMessage struct {
	Text        *GroupMessageText        `json:"text,omitempty"`
	Image       *GroupMessageImage       `json:"image,omitempty"`
	Link        *GroupMessageLink        `json:"link,omitempty"`
	Miniprogram *GroupMessageMiniprogram `json:"miniprogram,omitempty"`
}

type GroupMessageText struct {
	Content string `json:"content"`
}

type GroupMessageImage struct {
	Url string `json:"url"`
}

type GroupMessageLink struct {
	Url    string `json:"url"`
	Title  string `json:"title"`
	PicUrl string `json:"picUrl"`
	Desc   string `json:"desc"`
}

type GroupMessageMiniprogram struct {
	ThumbImageUrl string `json:"thumbImageUrl"`
	Title         string `json:"title"`
	AppId         string `json:"appId"`
	PagePath      string `json:"pagePath"`
}

type PaymentCredentialConfigurationResponse struct {
	PaymentConfiguration
	P12Credential    PaymentCredential `json:"p12Credential"`
	PemCredential    PaymentCredential `json:"pemCredential"`
	PemCredentialKey PaymentCredential `json:"pemCredentialKey"`
}

type PaymentCredential struct {
	Id   string `json:"id"`
	Data string `json:"data"`
	Name string `json:"name"`
	Type string `json:"type"`
}

type WechatcpAdmin struct {
	UserId   string `json:"userId"`
	OpenId   string `json:"openId"`
	AuthType string `json:"authType"`
}

type ListWechatcpAdminsResponse struct {
	Admins []WechatcpAdmin `json:"admins"`
}

type ListWechatcpAuthUsersResponse struct {
	UserAuthList []UserAuthList `json:"userAuthList"`
	NextCursor   string         `json:"nextCursor"`
}

type UserAuthList struct {
	OpenId string `json:"openId"`
}

type ConvertOpenGroupIdToChatIdRequest struct {
	JsCode        string `json:"jsCode"`
	EncryptedData string `json:"encryptedData"`
	Iv            string `json:"iv"`
}

type ConvertOpenGroupIdToChatIdResp struct {
	ChatId string `json:"chatId"`
}

type CreateTemporaryMediaMaterialResp struct {
	Id         string `json:"id"`
	MediaType  string `json:"mediaType"`
	MediaId    string `json:"mediaId"`
	Temporary  bool   `json:"temporary"`
	CreateTime int64  `json:"createTime"`
	ExpireTime uint64 `json:"expireTime"`
}

type SetWxCardFieldRequest struct {
	CardId           string          `json:"card_id"`
	ServiceStatement WeappCardInfo   `json:"service_statement,omitempty"`
	BindOldCard      WeappCardInfo   `json:"bind_old_card,omitempty"`
	RequiredForm     WxCardFormField `json:"required_form,omitempty"`
	OptionalForm     WxCardFormField `json:"optional_form,omitempty"`
}

type GetWxCardActivationUrlRequest struct {
	CardId   string `json:"card_id"`
	OuterStr string `json:"outer_str,omitempty"`
}

type GetWxCardMemberActivationInfoRequest struct {
	CardId string `json:"card_id"`
	Code   string `json:"code,omitempty"`
}

type WeappCardInfo struct {
	Name string `json:"name"`
	Url  string `json:"url"`
}

type WxCardFormField struct {
	CanModify         bool        `json:"can_modify,omitempty"`
	RichFieldList     []RichField `json:"rich_field_list,omitempty"`
	CommonFieldIdList []string    `json:"common_field_id_list,omitempty"`
	CustomFieldList   []string    `json:"custom_field_list,omitempty"`
}

type RichField struct {
	Type   string   `json:"type"`
	Name   string   `json:"name"`
	Values []string `json:"values"`
}

type WeconnectProxyResponse struct {
	Code    int32       `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type GetWxCardActivationUrlResponse struct {
	WxCardCommonResponse
	Url string `json:"url,omitempty"`
}

type WxCardActivationMemberInfoResponse struct {
	WxCardCommonResponse
	OpenId         string   `json:"openid,omitempty"`
	Nickname       string   `json:"nickname,omitempty"`
	Bonus          uint64   `json:"bonus,omitempty"`
	Balance        uint64   `json:"balance,omitempty"`
	Sex            string   `json:"sex,omitempty"`
	UserInfo       UserInfo `json:"user_info,omitempty"`
	UserCardStatus string   `json:"user_card_status,omitempty"`
	HasActive      bool     `json:"has_active,omitempty"`
}

type WxCardCommonResponse struct {
	ErrCode int32  `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

type UpdateWxCardRequest struct {
	Code              string                      `json:"code"`
	CardId            string                      `json:"cardId"`
	Bonus             int                         `json:"bonus,omitempty"`
	AddBonus          int                         `json:"addBonus,omitempty"`
	Balance           int                         `json:"balance,omitempty"`
	AddBalance        int                         `json:"addBalance,omitempty"`
	RecordBonus       string                      `json:"recordBonus,omitempty"`
	RecordBalance     string                      `json:"recordBalance,omitempty"`
	BackgroundPicUrl  string                      `json:"backgroundPicUrl,omitempty"`
	CustomFieldValue1 string                      `json:"customFieldValue1,omitempty"`
	CustomFieldValue2 string                      `json:"customFieldValue2,omitempty"`
	NotifyOptional    *UpdateWxCardNotifyOptional `json:"notifyOptional,omitempty"`
}

type UpdateWxCardNotifyOptional struct {
	IsNotifyBonus        bool `json:"isNotifyBonus,omitempty"`
	IsNotifyBalance      bool `json:"isNotifyBalance,omitempty"`
	IsNotifyCustomField1 bool `json:"isNotifyCustomField1,omitempty"`
	IsNotifyCustomField2 bool `json:"isNotifyCustomField2,omitempty"`
}

type UserInfo struct {
	CommonFieldList []CommonField `json:"common_field_list,omitempty"`
	CustomFieldList []CustomField `json:"custom_field_list,omitempty"`
}

type CommonField struct {
	Name  string `json:"name,omitempty"`
	Value string `json:"value,omitempty"`
}

type CustomField struct {
	Name      string   `json:"name,omitempty"`
	Value     string   `json:"value,omitempty"`
	ValueList []string `json:"value_list,omitempty"`
}

type DecryptWxCardCode struct {
	EncryptCode string `json:"encrypt_code"`
}

type DecryptWxCardCodeResponse struct {
	WxCardCommonResponse
	Code string `json:"code,omitempty"`
}

type ListFollowExternalUserResponse struct {
	ExternalUserIds []string `json:"externalUserIds,omitempty"`
}

type DeleteFollowerRequest struct {
	AccountId string `json:"accountId"`
	OriginId  string `json:"originId"`
}

type CreateRenewUserOrderRequest struct {
	BuyUserId string   `json:"buyUserId"`
	UserIds   []string `json:"userIds"`
	Months    uint64   `json:"months"`
}

type GetRenewUserResultResponse struct {
	Status       string             `json:"status"`
	Order        RenewOrder         `json:"order"`
	Users        []RenewUser        `json:"users"`
	InvalidUsers []InvalidRenewUser `json:"invalidUsers"`
}

type RenewOrder struct {
	Id     string `json:"id"`
	Status string `json:"status"`
}

type RenewUser struct {
	UserId     string `json:"userId"`
	Status     string `json:"status"`
	CreateTime int64  `json:"createTime"`
	ActiveTime int64  `json:"activeTime"`
	ExpireTime int64  `json:"expireTime"`
}

type InvalidRenewUser struct {
	ErrorMsg  string `json:"errorMsg"`
	ErrorCode int64  `json:"errorCode"`
	UserId    string `json:"userId"`
}

type ActiveUserTaskResult struct {
	Id     string       `json:"id"`
	Status string       `json:"status"`
	Users  []ActiveUser `json:"users"`
}

type ActiveUser struct {
	UserId     string `json:"userId"`
	Status     string `json:"status"`
	CreateTime int64  `json:"createTime"`
	ActiveTime int64  `json:"activeTime"`
	ExpireTime int64  `json:"expireTime"`
	ErrorCode  int64  `json:"errorCode"`
}

type TransferUsersRequest struct {
	UserId         string `json:"handoverUserId"`
	TransferUserId string `json:"takeoverUserId"`
}

type TransferUsersResponse struct {
	UserId         string `json:"handoverUserId"`
	TransferUserId string `json:"takeoverUserId"`
	ErrorCode      int64  `json:"errorCode"`
}

type ShareActiveCodeRequest struct {
	DownstreamChannelId string `json:"downstreamChannelId"`
	Count               int64  `json:"count"`
}

type ShareActiveCodeResult struct {
	Id          string       `json:"id"`
	Status      string       `json:"status"`
	LicenseLogs []LicenseLog `json:"licenseLogs"`
}

type LicenseLog struct {
	ErrorCode    int64  `json:"errorCode"`
	ErrorMessage string `json:"errorMessage"`
	Status       string `json:"status"`
}

type ConvertChatExternalUserIdToUnionIdRequest struct {
	ExternalUserIds []string `json:"externalUserIds"`
	ChatId          string   `json:"chatId"`
}

type ConvertChatExternalUserIdToUnionIdResult struct {
	ExternalUserId string `json:"externalUserId"`
	UnionId        string `json:"unionId"`
}

type CreateWechatCouponRequest struct {
	QuncrmAccountId string                 `json:"quncrmAccountId"`
	ChannelId       string                 `json:"weconnectAccountId"`
	Method          string                 `json:"method"`
	Path            string                 `json:"path"`
	Body            CreateWechatCouponData `json:"body"`
}

type CreateWechatCouponData struct {
	StockName          string             `json:"stock_name,omitempty"`
	BelongMerchant     string             `json:"belong_merchant,omitempty"`
	Comment            string             `json:"comment,omitempty"`
	GoodsName          string             `json:"goods_name,omitempty"`
	StockType          string             `json:"stock_type,omitempty"`
	CouponUseRule      CouponUseRule      `json:"coupon_use_rule,omitempty"`
	StockSendRule      StockSendRule      `json:"stock_send_rule,omitempty"`
	OutRequestNo       string             `json:"out_request_no,omitempty"`
	CustomEntrance     *CustomEntrance    `json:"custom_entrance,omitempty"`
	DisplayPatternInfo DisplayPatternInfo `json:"display_pattern_info,omitempty"`
	CouponCodeMode     string             `json:"coupon_code_mode,omitempty"`
	NotifyConfig       NotifyConfig       `json:"notify_config,omitempty"`
}

type NotifyConfig struct {
	NotifyAppId string `json:"notify_appid"`
}

type CouponUseRule struct {
	CouponAvailableTime *CouponAvailableTime `json:"coupon_available_time,omitempty"`
	FixedNormalCoupon   *FixedNormalCoupon   `json:"fixed_normal_coupon,omitempty"`
	DiscountCoupon      *DiscountCoupon      `json:"discount_coupon,omitempty"`
	UseMethod           string               `json:"use_method,omitempty"`
	MiniProgramsAppid   string               `json:"mini_programs_appid,omitempty"`
	MiniProgramsPath    string               `json:"mini_programs_path,omitempty"`
}

type CouponAvailableTime struct {
	AvailableBeginTime       string                    `json:"available_begin_time,omitempty"`
	AvailableEndTime         string                    `json:"available_end_time,omitempty"`
	AvailableDayAfterReceive int64                     `json:"available_day_after_receive,omitempty"`
	WaitDaysAfterReceive     int64                     `json:"wait_days_after_receive,omitempty"`
	AvailableWeek            AvailableWeek             `json:"available_week,omitempty"`
	IrregularyAvaliableTime  []IrregularyAvaliableTime `json:"irregulary_avaliable_time,omitempty"`
}

type AvailableWeek struct {
	WeekDay          []int64            `json:"week_day,omitempty"`
	AvailableDayTime []AvailableDayTime `json:"available_day_time,omitempty"`
}

type AvailableDayTime struct {
	BeginTime int64 `json:"begin_time"`
	EndTime   int64 `json:"end_time"`
}

type IrregularyAvaliableTime struct {
	BeginTime string `json:"begin_time,omitempty"`
	EndTime   string `json:"end_time,omitempty"`
}

type FixedNormalCoupon struct {
	DiscountAmount     int64 `json:"discount_amount,omitempty"`
	TransactionMinimum int64 `json:"transaction_minimum,omitempty"`
}

type DiscountCoupon struct {
	DiscountPercent    int64 `json:"discount_percent,omitempty"`
	TransactionMinimum int64 `json:"transaction_minimum,omitempty"`
}

type CustomEntrance struct {
	MiniProgramsInfo *MiniProgramsInfo `json:"mini_programs_info,omitempty"`
	Appid            string            `json:"appid,omitempty"`
	HallID           string            `json:"hall_id,omitempty"`
	StoreID          string            `json:"store_id,omitempty"`
}

type MiniProgramsInfo struct {
	MiniProgramsAppid string `json:"mini_programs_appid,omitempty"`
	MiniProgramsPath  string `json:"mini_programs_path,omitempty"`
	EntranceWords     string `json:"entrance_words,omitempty"`
	GuidingWords      string `json:"guiding_words,omitempty"`
}

type DisplayPatternInfo struct {
	Description     string      `json:"description,omitempty"`
	MerchantLogoURL string      `json:"merchant_logo_url,omitempty"`
	MerchantName    string      `json:"merchant_name,omitempty"`
	BackgroundColor string      `json:"background_color,omitempty"`
	CouponImageURL  string      `json:"coupon_image_url,omitempty"`
	FinderInfo      *FinderInfo `json:"finder_info,omitempty"`
}

type FinderInfo struct {
	FinderID                 string `json:"finder_id,omitempty"`
	FinderVideoCoverImageURL string `json:"finder_video_cover_image_url,omitempty"`
	FinderVideoID            string `json:"finder_video_id,omitempty"`
}

type StockSendRule struct {
	MaxCoupons         int64 `json:"max_coupons,omitempty"`
	MaxCouponsPerUser  int64 `json:"max_coupons_per_user,omitempty"`
	MaxCouponsByDay    int64 `json:"max_coupons_by_day,omitempty"`
	NaturalPersonLimit bool  `json:"natural_person_limit,omitempty"`
	PreventAPIAbuse    bool  `json:"prevent_api_abuse,omitempty"`
	Transferable       bool  `json:"transferable,omitempty"`
	Shareable          bool  `json:"shareable,omitempty"`
}

type CreateWechatCouponResponse struct {
	StockID    string `json:"stock_id,omitempty"`
	CreateTime string `json:"create_time,omitempty"`
}

type RedeemedWechatCouponRequest struct {
	QuncrmAccountId string                   `json:"quncrmAccountId"`
	ChannelId       string                   `json:"weconnectAccountId"`
	Method          string                   `json:"method"`
	Path            string                   `json:"path"`
	Body            RedeemedWechatCouponData `json:"body"`
}

type RedeemedWechatCouponData struct {
	CouponCode   string `json:"coupon_code,omitempty"`
	StockID      string `json:"stock_id,omitempty"`
	Appid        string `json:"appid,omitempty"`
	UseTime      string `json:"use_time,omitempty"`
	UseRequestNo string `json:"use_request_no,omitempty"`
	Openid       string `json:"openid,omitempty"`
}

type RedeemedWechatCouponResponse struct {
	StockID          string `json:"stock_id"`
	Openid           string `json:"openid"`
	WechatpayUseTime string `json:"wechatpay_use_time"`
}

type UpdateWechatCouponRequest struct {
	QuncrmAccountId string                 `json:"quncrmAccountId"`
	ChannelId       string                 `json:"weconnectAccountId"`
	Method          string                 `json:"method"`
	Path            string                 `json:"path"`
	Body            UpdateWechatCouponData `json:"body"`
}

type UpdateWechatCouponData struct {
	CustomEntrance     CustomEntrance     `json:"custom_entrance,omitempty"`
	Comment            string             `json:"comment,omitempty"`
	GoodsName          string             `json:"goods_name,omitempty"`
	OutRequestNo       string             `json:"out_request_no,omitempty"`
	DisplayPatternInfo DisplayPatternInfo `json:"display_pattern_info,omitempty"`
	CouponUseRule      CouponUseRule      `json:"coupon_use_rule,omitempty"`
	StockSendRule      StockSendRule      `json:"stock_send_rule,omitempty"`
	NotifyConfig       NotifyConfig       `json:"notify_config,omitempty"`
}

type UpdateWechatCouponResponse struct{}

type UpdateWechatCouponStockRequest struct {
	QuncrmAccountId string                      `json:"quncrmAccountId"`
	ChannelId       string                      `json:"weconnectAccountId"`
	Method          string                      `json:"method"`
	Path            string                      `json:"path"`
	Body            UpdateWechatCouponStockData `json:"body"`
}

type UpdateWechatCouponStockData struct {
	StockID               string `json:"stock_id,omitempty"`
	TargetMaxCoupons      int64  `json:"target_max_coupons,omitempty"`
	CurrentMaxCoupons     int64  `json:"current_max_coupons,omitempty"`
	ModifyBudgetRequestNo string `json:"modify_budget_request_no,omitempty"`
}

type UpdateWechatCouponStockResponse struct {
	MaxCoupons      int64 `json:"max_coupons"`
	MaxCouponsByDay int64 `json:"max_coupons_by_day"`
}

type GetWechatCouponSignResponse struct {
	PaySign  string `json:"paySign"`
	AppId    string `json:"appId"`
	SignType string `json:"signType"`
}

type ReturnWechatCouponRequest struct {
	QuncrmAccountId string                 `json:"quncrmAccountId"`
	ChannelId       string                 `json:"weconnectAccountId"`
	Method          string                 `json:"method"`
	Path            string                 `json:"path"`
	Body            ReturnWechatCouponData `json:"body"`
}

type ReturnWechatCouponData struct {
	CouponCode      string `json:"coupon_code,omitempty"`
	StockID         string `json:"stock_id,omitempty"`
	ReturnRequestNo string `json:"return_request_no,omitempty"`
}

type ReturnWechatCouponResponse struct {
	WechatpayReturnTime string `json:"wechatpay_return_time"`
}

type GetWechatCouponRequest struct {
	QuncrmAccountId string `json:"quncrmAccountId"`
	Method          string `json:"method"`
	Path            string `json:"path"`
	ChannelId       string `json:"weconnectAccountId"`
}

type GetWechatCouponResponse struct {
	StockName            string               `json:"stock_name"`
	BelongMerchant       string               `json:"belong_merchant"`
	Comment              string               `json:"comment"`
	GoodsName            string               `json:"goods_name"`
	StockType            string               `json:"stock_type"`
	CouponUseRule        CouponUseRule        `json:"coupon_use_rule"`
	StockSendRule        StockSendRule        `json:"stock_send_rule"`
	CustomEntrance       CustomEntrance       `json:"custom_entrance"`
	DisplayPatternInfo   DisplayPatternInfo   `json:"display_pattern_info"`
	StockState           string               `json:"stock_state"`
	CouponCodeMode       string               `json:"coupon_code_mode"`
	StockID              string               `json:"stock_id"`
	CouponCodeCount      CouponCodeCount      `json:"coupon_code_count"`
	SendCountInformation SendCountInformation `json:"send_count_information"`
}

type CouponCodeCount struct {
	TotalCount     int64 `json:"total_count"`
	AvailableCount int64 `json:"available_count"`
}

type SendCountInformation struct {
	TotalSendNum    int64 `json:"total_send_num"`
	TotalSendAmount int64 `json:"total_send_amount"`
	TodaySendNum    int64 `json:"today_send_num"`
	TodaySendAmount int64 `json:"today_send_amount"`
}

type UploadWechatMarketingImageRequest struct {
	ImageUrl        string `json:"imageUrl"`
	QuncrmAccountId string `json:"quncrmAccountId"`
	ChannelId       string `json:"weconnectAccountId"`
}

type UploadWechatMarketingImageResponse struct {
	ImageUrl string `json:"url"`
}

type TransferCustomerRequest struct {
	HandoverUserId     string   `json:"handover_userid"`
	TakeoverUserId     string   `json:"takeover_userid"`
	ExternalUserId     []string `json:"external_userid"`
	TransferSuccessMsg string   `json:"transfer_success_msg,omitempty"`
}

type TransferCustomerResponse struct {
	Errcode  int                `json:"errcode"`
	Errmsg   string             `json:"errmsg"`
	Customer []TransferCustomer `json:"customer"`
	Details  []ProxyDetail      `json:"details"`
}

type ProxyDetail struct {
	Uri             string `json:"uri"`
	Method          string `json:"method"`
	Direction       string `json:"direction"`
	RequestBodyData string `json:"requestBodyData"`
	StatusCode      string `json:"statusCode"`
	Response        string `json:"response"`
}

type TransferCustomer struct {
	ExternalUserId string `json:"external_userid"`
	Errcode        int    `json:"errcode"`
}

type TransferGroupchatsRequest struct {
	ChatIdList []string `json:"chat_id_list"`
	NewOwner   string   `json:"new_owner"`
}

type TransferGroupchatsResponse struct {
	Errcode        int                      `json:"errcode"`
	Errmsg         string                   `json:"errmsg"`
	FailedChatList []transferFailedChatList `json:"failed_chat_list"`
	Details        []ProxyDetail            `json:"details"`
}

type transferFailedChatList struct {
	ChatId  string `json:"chat_id"`
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
}

type GetTransferResultRequest struct {
	HandoverUserId string `json:"handover_userid"`
	TakeoverUserId string `json:"takeover_userid"`
	Cursor         string `json:"cursor"`
}

type GetTransferResultResponse struct {
	NextCursor string           `json:"next_cursor"`
	Customer   []TransferResult `json:"customer"`
}

type TransferResult struct {
	ExternalUserId string `json:"external_userid"`
	Status         int    `json:"status"`
}

type GetUnassignedListRequest struct {
	Cursor string `json:"cursor"`
}

type GetUnassignedListResponse struct {
	NextCursor string           `json:"next_cursor"`
	IsLast     bool             `json:"is_last"`
	Errcode    string           `json:"errcode"`
	Info       []UnassignedInfo `json:"info"`
}

type UnassignedInfo struct {
	HandoverUserId string `json:"handover_userid"`
	ExternalUserId string `json:"external_userid"`
	DimissionTime  int64  `json:"dimission_time"`
}

type DeleteCustomerStrategyRequest struct {
	StrategyId int `json:"strategy_id"`
}

type CreateCustomerStrategyRequest struct {
	StrategyName string   `json:"strategy_name"`
	AdminList    []string `json:"admin_list"`
	Range        []Range  `json:"range"`
}

type EditCustomerStrategyRequest struct {
	StrategyId   int      `json:"strategy_id"`
	StrategyName string   `json:"strategy_name"`
	AdminList    []string `json:"admin_list,omitempty"`
	RangeAdd     []Range  `json:"range_add,omitempty"`
	RangeDel     []Range  `json:"range_del,omitempty"`
}

type CreateCustomerStrategyResponse struct {
	Errcode    int    `json:"errcode"`
	Errmsg     string `json:"errmsg"`
	StrategyId int    `json:"strategy_id"`
}

type Range struct {
	Type    int    `json:"type"`
	UserId  string `json:"userid,omitempty"`
	PartyId int    `json:"partyid,omitempty"`
}

type AddStrategyTagRequest struct {
	StrategyId int           `json:"strategy_id"`
	GroupName  string        `json:"group_name"`
	Tags       []StrategyTag `json:"tag"`
}

type StrategyTag struct {
	Id   string `json:"id,omitempty"`
	Name string `json:"name"`
}

type AddStrategyTagResponse struct {
	Errcode  int              `json:"errcode"`
	Errmsg   string           `json:"errmsg"`
	TagGroup StrategyTagGroup `json:"tag_group"`
}

type StrategyTagGroup struct {
	GroupId   string        `json:"group_id"`
	GroupName string        `json:"group_name"`
	Tags      []StrategyTag `json:"tag"`
}

type EditStrategyTagRequest struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

type DelStrategyTagRequest struct {
	TagIds   []string `json:"tag_id"`
	GroupIds []string `json:"group_id"`
}

func (self *V2TransferDetailResponse) GetFailureMsg() string {
	failureMsgMap := map[string]string{
		"ACCOUNT_FROZEN":                         "账户冻结",
		"REAL_NAME_CHECK_FAIL":                   "用户未实名",
		"NAME_NOT_CORRECT":                       "用户姓名校验失败",
		"OPENID_INVALID":                         "openid 校验失败",
		"TRANSFER_QUOTA_EXCEED":                  "超过用户单笔收款额度",
		"DAY_RECEIVED_QUOTA_EXCEED":              "超过用户单日收款额度",
		"MONTH_RECEIVED_QUOTA_EXCEED":            "超过用户单月收款额度",
		"DAY_RECEIVED_COUNT_EXCEED":              "超过用户单日收款次数",
		"PRODUCT_AUTH_CHECK_FAIL":                "产品权限校验失败",
		"OVERDUE_CLOSE":                          "转账关闭",
		"ID_CARD_NOT_CORRECT":                    "用户身份证校验失败",
		"ACCOUNT_NOT_EXIST":                      "用户账户不存在",
		"TRANSFER_RISK":                          "转账存在风险",
		"REALNAME_ACCOUNT_RECEIVED_QUOTA_EXCEED": "用户账户收款受限，请引导用户在微信支付查看详情",
		"RECEIVE_ACCOUNT_NOT_PERMMIT":            "未配置该用户为转账收款人",
		"PAYER_ACCOUNT_ABNORMAL":                 "商户账户付款受限，可前往商户平台-违约记录获取解除功能限制指引",
		"PAYEE_ACCOUNT_ABNORMAL":                 "用户账户收款异常，请引导用户完善其在微信支付的身份信息以继续收款",
		"NOT_FOUND":                              "转账单不存在",
	}
	msg := failureMsgMap[self.FailureCode]
	if msg != "" {
		return msg
	}
	return self.FailureMsg
}

func (self *V3TransferDetailResponse) GetFailureMsg() string {
	temp := V2TransferDetailResponse{
		FailureCode: self.FailedReason,
		FailureMsg:  self.FailedReason,
	}
	return temp.GetFailureMsg()
}

type CorrectExternalUserRequest struct {
	UserIds         []string `json:"userIds"`
	DeletedUserIds  []string `json:"deletedUserIds"`
	UnsubscribeTime string   `json:"unsubscribeTime"`
	ChannelId       string   `json:"channelId"`
	OpenId          string   `json:"openId"`
}

type WechatCPConfig struct {
	UseProviderAccessToken bool `json:"useProviderAccessToken"`
}

type GetAutoActiveStatusRequest struct {
	CorpId string `json:"corpid"`
}

type GetAutoActiveStatusResponse struct {
	Errcode          int    `json:"errcode"`
	Errmsg           string `json:"errmsg"`
	AutoActiveStatus int    `json:"auto_active_status"`
}

type SetAutoActiveStatusRequest struct {
	CorpId           string `json:"corpid"`
	AutoActiveStatus int    `json:"auto_active_status"`
}

type WxCommonResponse struct {
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
}

type DownloadMiniProgramQrcodeRequest struct {
	// unlimit 不在 body 中传递，需在 param 中传递
	Unlimit bool `json:"-"`
	// unlimit 为 false 时传 path 参数
	Path string `json:"path,omitempty"`
	// unlimit 为 true 时传 page 和 scene 参数
	Page  string `json:"page,omitempty"`
	Scene string `json:"scene,omitempty"`
	// 以下参数为公共参数
	Width      int                  `json:"width"`
	AutoColor  bool                 `json:"autoColor"`
	LineColor  MiniProgramLineColor `json:"lineColor"`
	IsHyaline  bool                 `json:"isHyaline"`
	CheckPath  bool                 `json:"checkPath"`
	EnvVersion string               `json:"envVersion,omitempty"`
}

type MiniProgramLineColor struct {
	R int `json:"r"`
	G int `json:"g"`
	B int `json:"b"`
}

type ProxyRequest struct {
	QuncrmAccountId string      `json:"quncrmAccountId"`
	ChannelId       string      `json:"weconnectAccountId"`
	Method          string      `json:"method"`
	Path            string      `json:"path"`
	Body            interface{} `json:"body"`
}

type CreateCustomerAcquisitionLinkBody struct {
	LinkName   string                       `json:"link_name"`
	Range      CustomerAcquisitionLinkRange `json:"range"`
	SkipVerify bool                         `json:"skip_verify"`
}

type GetCustomerAcquisitionLinkBody struct {
	LinkId string `json:"link_id"`
}

type CustomerAcquisitionLinkRange struct {
	UserList       []string `json:"user_list"`
	DepartmentList []int    `json:"department_list"`
}

type CreateCustomerAcquisitionLinkResponse struct {
	Errcode int                     `json:"errcode"`
	Errmsg  string                  `json:"errmsg"`
	Link    CustomerAcquisitionLink `json:"link"`
}

type CustomerAcquisitionLink struct {
	LinkId     string `json:"link_id"`
	LinkName   string `json:"link_name"`
	URL        string `json:"url"`
	CreateTime int    `json:"create_time"`
	SkipVerify bool   `json:"skip_verify,omitempty"`
}

type GetCustomerAcquisitionLinkResponse struct {
	Errcode int                          `json:"errcode"`
	Errmsg  string                       `json:"errmsg"`
	Link    CustomerAcquisitionLink      `json:"link"`
	Range   CustomerAcquisitionLinkRange `json:"range"`
}

type GetMiniProgramPhoneByCodeRequest struct {
	Code string `json:"code"`
}

type GetMiniProgramPhoneByCodeResponse struct {
	Errcode   int              `json:"errcode"`
	Errmsg    string           `json:"errmsg"`
	PhoneInfo MiniProgramPhone `json:"phone_info"`
}

type MiniProgramPhone struct {
	PhoneNumber     string `json:"phoneNumber"`
	PurePhoneNumber string `json:"purePhoneNumber"`
	CountryCode     string `json:"countryCode"`
}

type AddMemberLevelSettingRequest struct {
	Level     int64  `json:"grade"`
	LevelName string `json:"name,omitempty"`
	// 经验值门槛，必须大于0
	ExperienceValueThreshold string `json:"experience_value_threshold"`
	// 经验值倍数，按乘以10进行填写，比如倍数为1.2倍，则填12，必须大于0
	ExperienceValueMultiple int64 `json:"experience_value_multiple"`
}

type GetMemberLevelSettingResponse struct {
	WxCardCommonResponse
	Info struct {
		ExperienceValueConf struct {
			PayAmountRule struct {
				PayValue       string `json:"pay_value"`
				GainValue      string `json:"gain_value"`
				PayAmountFloor string `json:"pay_amount_floor"`
			} `json:"pay_amount_rule"`
			PayTimeRule struct {
				GainValue string `json:"gain_value"`
			} `json:"pay_time_rule"`
		} `json:"experience_value_conf"`
		CardConf struct {
			ExperienceValueConf []struct {
				Grade                    int64  `json:"grade"`
				ValidName                string `json:"valid_name"`
				ExperienceValueThreshold string `json:"experience_value_threshold"`
				ExperienceValueMultiple  int64  `json:"experience_value_multiple"`
				AuditInfo                struct {
					LatestName string `json:"latest_name"`
					Status     int64  `json:"status"`
				} `json:"audit_info"`
			} `json:"grade_card_list"`
		} `json:"card_conf"`
	} `json:"info"`
}

type UpdateMemberLevelRequest struct {
	OpenId string `json:"openid"`
	Level  int64  `json:"grade"`
}

type GetMemberInfoRequest struct {
	OpenId          string `json:"openid"`
	NeedPhoneNumber bool   `json:"need_phone_number"`
}

type GetMemberInfoResponse struct {
	WxCardCommonResponse
	Info struct {
		OpenId   string `json:"openid"`
		UnionId  string `json:"unionid"`
		UserInfo struct {
			PhoneNumber string `json:"phone_number"`
		} `json:"user_info"`
		UserGradeInfo struct {
			Grade           int32  `json:"grade"`
			ExperienceValue string `json:"experience_value"`
		} `json:"user_grade_info"`
	} `json:"info"`
}

type GetMemberScoreRequest struct {
	OpenId string `json:"openid"`
}

type GetMemberScoreResponse struct {
	WxCardCommonResponse
	Info struct {
		Score string `json:"score"`
	} `json:"info"`
}

type GetMemberScoreHistoryRequest struct {
	OpenId   string `json:"openid"`
	PageNum  int64  `json:"page_num"`
	PageSize int64  `json:"page_size"`
}

type GetMemberScoreHistoryResponse struct {
	WxCardCommonResponse
	TotalNum int64 `json:"total_num"`
	List     []struct {
		Score  string `json:"score"`
		Remark string `json:"remark"`
		Source int    `json:"source"`
	} `json:"list"`
}

type UpdateMemberScoreRequest struct {
	OpenId    string `json:"openid"`
	Score     string `json:"score"`
	Remark    string `json:"remark,omitempty"`
	RequestId string `json:"request_id"`
}

type ListMembersRequest struct {
	NeedPhoneNumber bool  `json:"need_phone_number"`
	PageNum         int64 `json:"page_num"`
	PageSize        int64 `json:"page_size"`
}

type ListMembersResponse struct {
	WxCardCommonResponse
	TotalNum int64 `json:"total_num"`
	List     []struct {
		OpenId   string `json:"openid"`
		UnionId  string `json:"unionid"`
		UserInfo struct {
			PhoneNumber string `json:"phone_number"`
		} `json:"user_info"`
		UserGradeInfo struct {
			Grade           uint32 `json:"grade"`
			ExperienceValue string `json:"experience_value"`
		} `json:"user_grade_info"`
	} `json:"list"`
}

type ListWeshopOrdersRequest struct {
	UpdateTimeRange TimeRange `json:"update_time_range"`
	Status          int64     `json:"status,omitempty"`
	OpenId          string    `json:"openid,omitempty"`
	NextKey         string    `json:"next_key,omitempty"`
	PageSize        int64     `json:"page_size"`
}

type TimeRange struct {
	StartTime int64 `json:"start_time"`
	EndTime   int64 `json:"end_time"`
}

type ListWeshopOrdersResponse struct {
	WxCardCommonResponse
	OrderIdList []string `json:"order_id_list"`
	NextKey     string   `json:"next_key"`
	HasMore     bool     `json:"has_more"`
}

type GetWeshopOrderDetailsRequest struct {
	OrderId             string `json:"order_id"`
	EncodeSensitiveInfo bool   `json:"encode_sensitive_info,omitempty"`
}

type GetWeshopOrderDetailsResponse struct {
	WxCardCommonResponse
	Order WeshopOrder `json:"order"`
}

type WeshopOrder struct {
	CreateTime      int64           `json:"create_time"`
	UpdateTime      int64           `json:"update_time"`
	OrderId         string          `json:"order_id"`
	Status          int64           `json:"status"` // 订单状态：10=待付款，20=待发货，21=部分发货，30=待收货，100=完成，200=全部商品售后之后订单取消，250=未付款用户主动取消或超时未付款订单自动取消
	OpenId          string          `json:"openid"`
	UnionId         string          `json:"unionid"`
	OrderDetail     OrderDetail     `json:"order_detail"`
	AftersaleDetail AftersaleDetail `json:"aftersale_detail"`
}

type OrderDetail struct {
	ProductInfos    []ProductInfo    `json:"product_infos"`
	PriceInfo       PriceInfo        `json:"price_info"`
	PayInfo         *PayInfo         `json:"pay_info"`
	DeliveryInfo    DeliveryInfo     `json:"delivery_info"`
	CouponInfo      CouponInfo       `json:"conpon_info"`
	ExtInfo         *ExtInfo         `json:"ext_info"`
	CommissionInfos []CommissionInfo `json:"commission_infos"`
	SharerInfo      SharerInfo       `json:"sharer_info"`
	SettleInfo      SettleInfo       `json:"settle_info"`
	SkuSharerInfos  []SkuSharerInfo  `json:"sku_sharer_infos"`
	AgentInfo       AgentInfo        `json:"agent_info"`
	SourceInfos     []SourceInfo     `json:"source_infos"`
}

type ProductInfo struct {
	ProductId               string              `json:"product_id"`
	SkuId                   string              `json:"sku_id"`
	ThumbImg                string              `json:"thumb_img"`
	SkuCnt                  int64               `json:"sku_cnt"`
	SalePrice               int64               `json:"sale_price"`
	Title                   string              `json:"title"`
	OnAftersaleSkuCnt       int64               `json:"on_aftersale_sku_cnt"`
	FinishAftersaleSkuCnt   int64               `json:"finish_aftersale_sku_cnt"`
	SkuCode                 string              `json:"sku_code"`
	MarketPrice             int64               `json:"market_price"`
	SkuAttrs                []AttrInfo          `json:"sku_attrs"`
	RealPrice               int64               `json:"real_price"`
	OutProductId            string              `json:"out_product_id"`
	OutSkuId                string              `json:"out_sku_id"`
	IsDiscounted            bool                `json:"is_discounted"`
	VipDiscountedPrice      int64               `json:"vip_discounted_price,omitempty"`
	EstimatePrice           int64               `json:"estimate_price"`
	IsChangePrice           bool                `json:"is_change_price"`
	ChangePrice             int64               `json:"change_price"`
	OutWarehouseId          string              `json:"out_warehouse_id"`
	SkuDeliverInfo          SkuDeliverInfo      `json:"sku_deliver_info"`
	ExtraService            ProductExtraService `json:"extra_service"`
	UseDeduction            bool                `json:"use_deduction"`
	DeductionPrice          int64               `json:"deduction_price"`
	OrderProductCouponInfos []CouponInfo        `json:"order_product_coupon_info_list"`
}

type SkuDeliverInfo struct {
	StockType           int64 `json:"stock_type"` // 商品发货类型：0=现货，1=全款预售
	PredictDeliveryTime int64 `json:"predict_delivery_time"`
}

type ProductExtraService struct {
	SevenDayReturn   int64 `json:"seven_day_return"`  // 七天无理由：0=不支持，1=支持
	FreightInsurance int64 `json:"freight_insurance"` // 商家运费险：0=不支持，1=支持
}

type AttrInfo struct {
	AttrKey   string `json:"attr_key"`
	AttrValue string `json:"attr_value"`
}

type PayInfo struct {
	PrepayId      string `json:"prepay_id"`
	PrepayTime    int64  `json:"prepay_time"`
	PayTime       int64  `json:"pay_time"`
	TransactionId string `json:"transaction_id"`
	PaymentMethod int64  `json:"payment_method"` // 支付方式：1=微信支付，2=先用后付，3=抽奖商品0元订单，4=会员积分兑换订单
}

type PriceInfo struct {
	ProductPrice         int64 `json:"product_price"`
	OrderPrice           int64 `json:"order_price"`
	Freight              int64 `json:"freight"`
	DiscountedPrice      int64 `json:"discounted_price"`
	VipDiscountedPrice   int64 `json:"vip_discounted_price,omitempty"`
	IsDiscounted         bool  `json:"is_discounted"`
	OriginalOrderPrice   int64 `json:"original_order_price"`
	EstimateProductPrice int64 `json:"estimate_product_price"`
	ChangeDownPrice      int64 `json:"change_down_price"`
	ChangeFreight        int64 `json:"change_freight"`
	IsChangeFreight      bool  `json:"is_change_freight"`
	UseDeduction         bool  `json:"use_deduction"`
	DeductionPrice       int64 `json:"deduction_price"`
}

type DeliveryInfo struct {
	AddressInfo         AddressInfo           `json:"address_info"`
	DeliveryProductInfo []DeliveryProductInfo `json:"delivery_product_info"`
	ShipDoneTime        int64                 `json:"ship_done_time"`
	DeliverMethod       int64                 `json:"deliver_method"` // 订单发货方式：0=普通物流，1=虚拟发货
	AddressUnderReview  AddressInfo           `json:"address_under_review"`
	AddressApplyTime    int64                 `json:"address_apply_time"`
	EwaybillOrderCode   string                `json:"ewaybill_order_code"`
}

type DeliveryProductInfo struct {
	WaybillId       string               `json:"waybill_id"`
	DeliveryId      string               `json:"delivery_id"`
	ProductInfos    []FreightProductInfo `json:"product_infos"`
	DeliveryName    string               `json:"delivery_name"`
	DeliveryTime    int64                `json:"delivery_time"`
	DeliverType     uint32               `json:"deliver_type"` // 配送方式：1=自寄快递，2=在线签约快递单，3=虚拟商品无需物流发货，4=在线快递散单
	DeliveryAddress AddressInfo          `json:"delivery_address"`
}

type FreightProductInfo struct {
	ProductId  string `json:"product_id"`
	SkuId      string `json:"sku_id"`
	ProductCnt int64  `json:"product_cnt"`
}

type AddressInfo struct {
	UserName              string           `json:"user_name"`
	PostalCode            string           `json:"postal_code"`
	ProvinceName          string           `json:"province_name"`
	CityName              string           `json:"city_name"`
	CountyName            string           `json:"county_name"`
	DetailInfo            string           `json:"detail_info"`
	TelNumber             string           `json:"tel_number"`
	HouseNumber           string           `json:"house_number"`
	VirtualOrderTelNumber string           `json:"virtual_order_tel_number"`
	TelNumberExtInfo      TelNumberExtInfo `json:"tel_number_ext_info"`
	UseTelNumber          uint32           `json:"use_tel_number"` // 0=不使用虚拟号码，1=使用虚拟号码
	HashCode              string           `json:"hash_code"`
	NationalCode          string           `json:"national_code,omitempty"`
}

type TelNumberExtInfo struct {
	RealTelNumber        string `json:"real_tel_number"`
	VirtualTelNumber     string `json:"virtual_tel_number"`
	VirtualTelExpireTime int64  `json:"virtual_tel_expire_time"`
	GetVirtualTelCnt     int64  `json:"get_virtual_tel_cnt"`
}

type CouponInfo struct {
	UserCouponId string `json:"user_coupon_id"`
}

type AftersaleDetail struct {
	OnAftersaleOrderCnt int64 `json:"on_aftersale_order_cnt"`
	AftersaleOrderList  []struct {
		AftersaleOrderId string `json:"aftersale_order_id"`
	} `json:"aftersale_order_list"`
}

type ExtInfo struct {
	CustomerNotes     string `json:"customer_notes"`
	MerchantNotes     string `json:"merchant_notes"`
	VipOrderSessionId string `json:"vip_order_session_id,omitempty"`
}

type CommissionInfo struct {
	SkuId        string `json:"sku_id"`
	NickName     string `json:"nick_name"`
	Type         int64  `json:"type"`   // 分账方类型：0=达人，1=团长
	Status       int64  `json:"status"` // 分账状态：1=未结算，2=已结算
	Amount       int64  `json:"amount"`
	FinderId     string `json:"finder_id"`
	OpenFinderId string `json:"openfinderid"`
}

type SharerInfo struct {
	SharerOpenId  string `json:"sharer_openid"`
	SharerUnionId string `json:"sharer_unionid"`
	SharerType    int64  `json:"sharer_type"` // 分享员类型：0=普通分享员，1=店铺分享员
	ShareScene    int64  `json:"share_scene"` // 分享场景：1=直播间，2=橱窗，3=短视频，4=视频号主页，5=商品详情页，6=带商品的公众号文章
}

type SkuSharerInfo struct {
	SharerInfo
	SkuId     string `json:"sku_id"`
	FromWecom bool   `json:"from_wecom"`
}

type SettleInfo struct {
	PredictCommissionFee    int64 `json:"predict_commission_fee"`
	CommissionFee           int64 `json:"commission_fee"`
	PredictWecoinCommission int64 `json:"predict_wecoin_commission"`
	WecoinCommission        int64 `json:"wecoin_commission"`
}

type AgentInfo struct {
	AgentFinderId       string `json:"agent_finder_id"`
	AgentFinderNickname string `json:"agent_finder_nickname"`
}

type SourceInfo struct {
	SkuId                  string `json:"sku_id"`
	SaleChannel            int64  `json:"sale_channel"`
	AccountType            int64  `json:"account_type"`
	AccountId              string `json:"account_id"`
	AccountNickname        string `json:"account_nickname"`
	ContentType            int64  `json:"content_type"`
	ContentId              string `json:"content_id"`
	PromoterHeadSupplierId string `json:"promoter_head_supplier_id"`
}

type UpdateCustomerAcquisitionLinkBody struct {
	LinkId     string                       `json:"link_id"`
	LinkName   string                       `json:"link_name"`
	Range      CustomerAcquisitionLinkRange `json:"range"`
	SkipVerify bool                         `json:"skip_verify"`
}

type UpdateCustomerAcquisitionLinkResponse struct {
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
}

type DeleteCustomerAcquisitionLinkBody struct {
	LinkId string `json:"link_id"`
}

type DeleteCustomerAcquisitionLinkResponse struct {
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
}

type GetCustomerAcquisitionQuotaResponse struct {
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
	Total   int    `json:"total"`
	Balance int    `json:"balance"`
}

type GetSuiteAppInfoResponse struct {
	SuiteID     string        `json:"suiteId"`
	SuiteType   string        `json:"suiteType"`
	CreateTime  int64         `json:"createTime"`
	EditionInfo []EditionInfo `json:"editionInfo"`
}

type EditionInfo struct {
	AgentID     int    `json:"agentId"`
	EditionID   string `json:"editionId"`
	EditionName string `json:"editionName"`
	AppStatus   int    `json:"appStatus"`
	UserLimit   int64  `json:"userLimit"`
	ExpiredTime int64  `json:"expiredTime"`
}

type ListMerchantCashCouponsResponse struct {
	TotalCount int                  `json:"total_count"`
	Data       []MerchantCashCoupon `json:"data"`
	Limit      int                  `json:"limit"`
	Offset     int                  `json:"offset"`
}

type MerchantCashCoupon struct {
	StockID           string `json:"stock_id"`
	StockCreatorMchid string `json:"stock_creator_mchid"`
	StockName         string `json:"stock_name"`
	Status            string `json:"status"`
	CreateTime        string `json:"create_time"`
	Description       string `json:"description"`
	StockUseRule      struct {
		MaxCoupons        int `json:"max_coupons"`
		MaxAmount         int `json:"max_amount"`
		MaxAmountByDay    int `json:"max_amount_by_day"`
		FixedNormalCoupon struct {
			CouponAmount       int `json:"coupon_amount"`
			TransactionMinimum int `json:"transaction_minimum"`
		} `json:"fixed_normal_coupon"`
		MaxCouponsPerUser int      `json:"max_coupons_per_user"`
		TradeType         []string `json:"trade_type"`
		CombineUse        bool     `bson:"combine_use"`
	} `json:"stock_use_rule"`
	AvailableBeginTime string `json:"available_begin_time"`
	AvailableEndTime   string `json:"available_end_time"`
	DistributedCoupons int    `json:"distributed_coupons"`
	NoCash             bool   `json:"no_cash"`
	StartTime          string `bson:"start_Time"`
	Singleitem         bool   `json:"singleitem "`
	StockType          string `json:"stock_type"`
}

type AddOrRemoveCallbackRequest struct {
	Platform    string `json:"-"`
	AccountId   string `json:"-"`
	MsgType     string `json:"-"`
	SubType     string `json:"-"`
	Action      string `json:"-"`
	CallbackUrl string `json:"-"`
}

type GetAppLicenseInfoRequest struct {
	CorpId  string `json:"corpid"`
	SuiteId string `json:"suite_id"`
}

type GetAppLicenseInfoResponse struct {
	LicenseStatus    int64 `json:"license_status,omitempty"`
	LicenseCheckTime int64 `json:"license_check_time,omitempty"`
}

type ListActivatedAccountRequest struct {
	CorpId string `json:"corpid,omitempty"`
	Limit  int    `json:"limit,omitempty"`
	Cursor string `json:"cursor,omitempty"`
}

type ListActivatedAccountResponse struct {
	HasMore     int                    `json:"has_more,omitempty"`
	Cursor      string                 `json:"next_cursor,omitempty"`
	AccountList []ActivatedAccountInfo `json:"account_list,omitempty"`
}

type ActivatedAccountInfo struct {
	UserId     string `json:"userid,omitempty"`
	Type       int    `json:"type,omitempty"`
	ExpireTime int64  `json:"expire_time,omitempty"`
	ActiveTime int64  `json:"active_time,omitempty"`
}

type ConvertUserIdRequest struct {
	UserIdList []string `json:"userid_list,omitempty"`
}

type ConvertUserIdResponse struct {
	SucceedResult []ConvertUserIdResult `json:"open_userid_list,omitempty"`
	FailedResult  []string              `json:"invalid_userid_list,omitempty"`
}

type ConvertUserIdResult struct {
	UserId     string `json:"userid"`
	OpenUserId string `json:"open_userid"`
}

type ListChatMessageHistoriesRequest struct {
	MainId          string   `json:"mainId,omitempty"`
	RelatedId       string   `json:"relatedId,omitempty"`
	RoomId          string   `json:"roomId,omitempty"`
	Types           []string `json:"types,omitempty"`
	SearchKey       string   `json:"searchKey,omitempty"`
	MessageTimeFrom int64    `json:"messageTimeFrom,omitempty"`
	MessageTimeTo   int64    `json:"messageTimeTo,omitempty"`
	PageNum         int      `json:"pageNum,omitempty"`
	PageSize        int      `json:"pageSize,omitempty"`
}

type ListChatMessageHistoriesResponse struct {
	TotalAmount uint64               `json:"totalAmount,omitempty"`
	Results     []ChatMessageHistory `json:"results"`
}

type ChatMessageHistory struct {
	MessageId   string                 `json:"messageId,omitempty"`
	MessageTime int64                  `json:"messageTime,omitempty"`
	MessageType string                 `json:"messageType,omitempty"`
	Data        ChatMessageHistoryData `json:"data"`
	From        string                 `json:"from,omitempty"`
	To          []string               `json:"to,omitempty"`
	RoomId      string                 `json:"roomId,omitempty"`
	IsCompleted bool                   `json:"isCompleted,omitempty"`
}

type ChatMessageHistoryData struct {
	Content      string `json:"content,omitempty"`
	SdkFileKey   string `json:"sdkFileKey,omitempty"`
	FileName     string `json:"fileName,omitempty"`
	FileSize     int64  `json:"fileSize,omitempty"`
	LinkUrl      string `json:"linkUrl,omitempty"`
	PlayLength   int64  `json:"playLength,omitempty"`
	InviteType   int64  `json:"inviteType,omitempty"`
	CallDuration int64  `json:"callDuration,omitempty"`
	Title        string `json:"title,omitempty"`
	Description  string `json:"description,omitempty"`
	DisplayName  string `json:"displayName,omitempty"`
}

type ListChatMessageHistoryPreviewsRequest struct {
	MainId     string   `json:"mainId,omitempty"`
	RelatedIds []string `json:"relatedIds,omitempty"`
	RoomIds    []string `json:"roomIds,omitempty"`
	IsAllRoom  bool     `json:"isAllRoom,omitempty"`
	IsAllUser  bool     `json:"isAllUser,omitempty"`
	PageNum    int      `json:"pageNum,omitempty"`
	PageSize   int      `json:"pageSize,omitempty"`
}

type ListChatMessageHistoryPreviewsResponse struct {
	PageNum     int                         `json:"pageNum,omitempty"`
	PageSize    int                         `json:"pageSize,omitempty"`
	TotalAmount int64                       `json:"totalAmount,omitempty"`
	Results     []ChatMessageHistoryPreview `json:"results,omitempty"`
}

type ChatMessageHistoryPreview struct {
	From        string                 `json:"from,omitempty"`
	To          string                 `json:"to,omitempty"`
	RoomId      string                 `json:"roomId,omitempty"`
	IsAllUser   bool                   `json:"isAllUser,omitempty"`
	MessageType string                 `json:"messageType,omitempty"`
	MessageTime int64                  `json:"messageTime,omitempty"`
	LastData    ChatMessageHistoryData `json:"lastData,omitempty"`
}

type ChatMessageHistoriesSyncInfo struct {
	LastSyncTime     int64  `json:"lastSyncTime"`
	ManualSyncStatus string `json:"manualSyncStatus"`
}

type MessageAuditStatus struct {
	IsOpened bool `json:"isOpened"`
}

type TemplateMessageResult struct {
	Id        string `json:"id"`
	Status    string `json:"status"`
	ErrorType string `json:"errorType"`
}

type LoginAuthResponse struct {
	OrderID   uint64 `json:"orderId"`
	OpenID    string `json:"openId"`
	AppID     string `json:"appId"`
	ServiceID uint64 `json:"serviceId"`
	SkuID     string `json:"skuId"`
	SpecID    string `json:"specId"`
}

type ServiceInfoResponse struct {
	Buyer BuyerInfo `json:"buyer"`
}

type BuyerInfo struct {
	AppID     string     `json:"appId"`
	ServiceID uint64     `json:"serviceId"`
	SpecList  []SpecInfo `json:"specList"`
}

type SpecInfo struct {
	ExpireTime      int64  `json:"expireTime"`
	SpecificationID string `json:"specificationId"`
}
