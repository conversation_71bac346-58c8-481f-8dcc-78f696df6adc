package invoice

import (
	"context"
	"mairpc/proto/common/ec"
	pb_ec_order "mairpc/proto/ec/order"
)

const (
	PROVIDER_NUO_E   = "nuo-e"   // 诺e
	PROVIDER_BAIWANG = "baiwang" // 百望
)

type InvoiceRequestor interface {
	Invoice(ctx context.Context, request *InvoiceRequest) (*InvoiceResponse, error)
	Download(ctx context.Context, request *DownloadRequest) (*DownloadResponse, error)
	SendToEmail(ctx context.Context, request *SendToEmailRequest) (*SendToEmailResponse, error)
	CreateEInvoiceUrl(ctx context.Context, request *DownloadRequest) error
	CreateRedConfirmation(ctx context.Context, request *CreateRedConfirmationRequest) (*CreateRedConfirmationResponse, error)
	IssueRedInvoice(ctx context.Context, request *InvoiceRequest) error
}

type CreateRedConfirmationRequest struct {
	// 申请红字确认单的发票信息：重开时为原蓝票，其余为红票
	InvoiceRequest
	// 原蓝票开票流水号：仅 invoice.isRed 为 true 时使用，发票重开时原蓝票流水号为其本身 invoice.id
	BlueInvoiceSerialNo string
}

type CreateRedConfirmationResponse struct {
	RedConfirmUuid string
}

type InvoiceRequest struct {
	PublicParameters
	TaxSetting
	OrderInfo           OrderInfo
	Invoice             Invoice
	Products            []InvoiceProduct
	DeliveryFeeTemplate *ec.InvoiceTemplate
}

type InvoiceProduct struct {
	OrderProduct    *pb_ec_order.OrderProduct
	InvoiceTemplate *ec.InvoiceTemplate
}

type Invoice struct {
	Id             string `json:"id"`
	Name           string `json:"name"`
	Type           string `json:"type"`
	TaxID          string `json:"taxID"`
	Email          string `json:"email"`
	Address        string `json:"address"`
	Phone          string `json:"phone"`
	BankName       string `json:"bankName"`
	BankAccount    string `json:"bankAccount"`
	SerialNo       string `json:"serialNo"`
	Status         string `json:"status"`
	IsRed          bool   `json:"isRed"`
	RedConfirmUuid string `json:"redConfirmUuid"`
}

type OrderInfo struct {
	Id          string `json:"id"`
	Number      string `json:"number"`
	CreatedAt   string `json:"createdAt"`
	DeliveryFee uint64 `json:"deliveryFee"`
}

type PublicParameters struct {
	Username          string `json:"username"`
	Password          string `json:"password"`
	Salt              string `json:"salt"`
	AppKey            string `json:"appKey"`
	AppSecret         string `json:"appSecret"`
	RefreshToken      string `json:"refreshToken"`
	TaxpayerId        string `json:"taxpayerId"`
	AuthorizationCode string `json:"authorizationCode"`
	TripleDesKey      string `json:"tripleDesKey"`
}

type TaxSetting struct {
	DrawerTaxpayerId       string `json:"drawerTaxpayerId"`
	DrawerTaxpayerFullName string `json:"drawerTaxpayerFullName"`
	SellerTaxpayerId       string `json:"sellerTaxpayerId"`
	SellerTaxpayerFullName string `json:"sellerTaxpayerFullName"`
	SellerAddress          string `json:"sellerAddress"`
	SellerPhone            string `json:"sellerPhone"`
	SellerBankName         string `json:"sellerBankName"`
	SellerBankAccount      string `json:"sellerBankAccount"`
	Drawer                 string `json:"drawer"`
	Payer                  string `json:"payer"`
	Reviewer               string `json:"reviewer"`
}

type InvoiceResponse struct {
	Url        string
	Code       string
	Number     string
	InvoicedAt string
	Amount     uint64
}

type DownloadRequest struct {
	InvoiceRequest
	PdfXzfs string // 0:发票开具状态查询;1:PDF文件(PDF_FILE);2:PDF文件链接地址;3:PDF文件和链接地址都返回
}

type DownloadResponse struct {
	Url        string `json:"url"`
	Code       string
	Number     string
	InvoicedAt string
	Amount     uint64
}

type SendToEmailRequest struct {
	InvoiceRequest
	Email  string `json:"email"`
	Code   string
	Number string
}

type SendToEmailResponse struct {
}

func GetInvoiceClient(provider string, isTest bool) InvoiceRequestor {
	switch provider {
	case PROVIDER_NUO_E:
		return NewNuoeClient(isTest)
	case PROVIDER_BAIWANG:
		return NewBaiwangClient(isTest)
	}
	return nil
}
