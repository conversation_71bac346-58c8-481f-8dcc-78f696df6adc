package invoice

import (
	"context"
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"math"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"

	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/core/util"
	"mairpc/core/util/copier"
	setting_model "mairpc/service/ec/model/setting"

	"github.com/avast/retry-go"
	jsoniter "github.com/json-iterator/go"
	"github.com/modern-go/reflect2"
	"github.com/spf13/cast"
	"gopkg.in/go-playground/validator.v9"
)

const (
	BAIWANG_ROUTER_REST_URL                     = "https://openapi.baiwang.com/router/rest"
	BAIWANG_INVOICE_STATUS_FAILED               = "03"
	BAIWANG_INVOICE_NOT_EXISTS_ERROR            = "Baiwang invoice is not exists"
	BAIWANG_INVOICE_VOIDED_ERROR                = "Baiwang invoice is voided"
	BAIWANG_INVOICE_URL_EMPTY_ERROR             = "Baiwang invoice url is empty"
	BAIWANG_INVOICE_API_ERROR_CAN_REFRESH_TOKEN = `(缺少必需参数 token|参数 token 的格式或取值范围错误|100002|100007)`
	BAIWANG_INVOICE_API_ERROR_REQUEST_REPEATED  = `90003`

	INVOICE_FAVORABLE_POLICY_NONUSE   = "nonuse"
	INVOICE_FAVORABLE_POLICY_ZERO_TAX = "zeroTax"
	INVOICE_FAVORABLE_POLICY_FREE_TAX = "freeTax"
	INVOICE_FAVORABLE_POLICY_NO_TAX   = "noTax"

	INVOICE_LINE_NATURE_NORMAL     = "0" // 正常行
	INVOICE_LINE_NATURE_DISCOUNT   = "1" // 折扣行
	INVOICE_LINE_NATURE_DISCOUNTED = "2" // 被折扣行
)

var vatSpecialManagementTypeLabel = map[string]string{
	INVOICE_FAVORABLE_POLICY_ZERO_TAX: "出口零税",
	INVOICE_FAVORABLE_POLICY_FREE_TAX: "免税",
	INVOICE_FAVORABLE_POLICY_NO_TAX:   "不征税",
}

var jsoniterUsingNum = jsoniter.Config{
	EscapeHTML:             true,
	SortMapKeys:            true,
	ValidateJsonRawMessage: true,
	UseNumber:              true,
}.Froze()

var validate *validator.Validate = validator.New()

func init() {
	jsoniter.RegisterTypeDecoderFunc(reflect2.TypeOf("").String(), util.StringDecoderFunc)
	jsoniter.RegisterTypeDecoderFunc(reflect2.TypeOf(int64(1)).String(), util.Int64DecoderFunc)
}

type baiwangClient struct {
	IsTest bool
}

func NewBaiwangClient(isTest bool) InvoiceRequestor {
	n := &baiwangClient{
		IsTest: isTest,
	}
	return n
}

type BaiwangCommonResponse struct {
	Success       bool   `json:"success,omitempty"`
	Method        string `json:"method,omitempty"`
	RequestId     string `json:"requestId,omitempty"`
	ErrorResponse struct {
		Code       string `json:"code,omitempty"`
		Message    string `json:"message,omitempty"`
		SubCode    string `json:"subCode,omitempty"`
		SubMessage string `json:"subMessage,omitempty"`
	} `json:"ErrorResponse,omitempty"`
}

type GetBaiwangInvoiceRequest struct {
	TaxNo string             `json:"taxNo"`
	Data  BaiwangInvoiceData `json:"data,omitempty"`
}

type BaiwangInvoiceData struct {
	SerialNo           string `json:"serialNo,omitempty"`
	InvoiceStartDate   string `json:"invoiceStartDate,omitempty"`
	InvoiceEndDate     string `json:"invoiceEndDate,omitempty"`
	InvoiceNo          string `json:"invoiceNo,omitempty"`
	InvoiceTypeCode    string `json:"invoiceTypeCode,omitempty"`
	BuyerName          string `json:"buyerName,omitempty"`
	PageNo             int64  `json:"pageNo,omitempty"`
	PageSize           int64  `json:"pageSize,omitempty"`
	InvoiceSpecialMark string `json:"invoiceSpecialMArk,omitempty"`
	OriginalInvoiceNo  string `json:"originalInvoiceNo,omitempty"`
	BuyerTaxNo         string `json:"buyerTaxNo,omitempty"`
}

type GetBaiwangInvoiceResponse struct {
	BaiwangCommonResponse
	Response []GetBaiwangInvoiceResponseItem `json:"response,omitempty"`
}

type GetBaiwangInvoiceResponseItem struct {
	InvoiceStatus     string                 `json:"invoiceStatus"`
	EInvoiceUrl       string                 `json:"eInvoiceUrl"`
	InvoiceDate       string                 `json:"invoiceDate"`
	InvoiceNo         string                 `json:"invoiceNo"`
	InvoiceTotalPrice float64                `json:"invoiceTotalPrice"`
	InvoiceTotalTax   float64                `json:"invoiceTotalTax"`
	InvoiceTypeCode   string                 `json:"invoiceTypeCode"`
	Drawer            string                 `json:"drawer"`
	BuyerName         string                 `json:"buyerName"`
	BuyerTaxNo        string                 `json:"buyerTaxNo"`
	InvoiceDetails    []BaiwangInvoiceDetail `json:"electricInvoiceDetails"`
}

type BaiwangInvoiceDetail struct {
	GoodsLineNo        string  `json:"goodsLineNo"`
	GoodsCode          string  `json:"goodsCode"`
	GoodsPersonalCode  string  `json:"goodsPersonalCode"`
	GoodsName          string  `json:"goodsName"`
	GoodsSpecification string  `json:"goodsSpecification"`
	GoodsUnit          string  `json:"goodsUnit"`
	GoodsQuantity      float64 `json:"goodsQuantity"`
	GoodsPrice         float64 `json:"goodsPrice"`
	GoodsTotalPrice    float64 `json:"goodsTotalPrice"`
	GoodsTotalPriceTax float64 `json:"goodsTotalPriceTax"`
	GoodsTotalTax      float64 `json:"goodsTotalTax"`
	GoodsTaxRate       float64 `json:"goodsTaxRate"`
	InvoiceLineNature  string  `json:"invoiceLineNature"`
	DeductibleAmount   float64 `json:"deductibleAmount"`
}

type CreateBaiwangInvoiceRequest struct {
	TaxNo               string                   `json:"taxNo"`
	TaxUserName         string                   `json:"taxUserName,omitempty"`
	InvoiceTerminalCode string                   `json:"invoiceTerminalCode,omitempty"`
	IsSplit             bool                     `json:"isSplit,omitempty"`
	OrgCode             string                   `json:"orgCode,omitempty"`
	TaxDiskNo           string                   `json:"taxDiskNo,omitempty"`
	FormatGenerate      bool                     `json:"formatGenerate,omitempty"`
	FormatPushType      bool                     `json:"formatPushType,omitempty"`
	CompletionCustom    string                   `json:"completionCustom,omitempty"`
	IsAsync             string                   `json:"isAsync,omitempty"`
	IsReturnRedInfo     string                   `json:"isReturnRedInfo,omitempty"`
	Data                CreateBaiwangInvoiceData `json:"data"`
}

type CreateBaiwangInvoiceData struct {
	InvoiceTypeCode      string                     `json:"invoiceTypeCode"`
	PaperInvoiceFlag     string                     `json:"paperInvoiceFlag,omitempty"`
	PaperInvoiceTypeCode string                     `json:"paperInvoiceTypeCode,omitempty"`
	InvoiceType          string                     `json:"invoiceType,omitempty"`
	InvoiceSpecialMark   string                     `json:"invoiceSpecialMark,omitempty"`
	IsConfirmIssue       string                     `json:"isConfirmIssue,omitempty"`
	TaxationMethod       string                     `json:"taxationMethod,omitempty"`
	TaxationLabel        string                     `json:"taxationLabel,omitempty"`
	InvoiceListMark      string                     `json:"invoiceListMark,omitempty"`
	PriceTaxMark         string                     `json:"priceTaxMark,omitempty"`
	SerialNo             string                     `json:"serialNo"`
	OrderNo              string                     `json:"orderNo,omitempty"`
	BuyerTaxNo           string                     `json:"buyerTaxNo,omitempty"`
	BuyerName            string                     `json:"buyerName"`
	BuyerAddressPhone    string                     `json:"buyerAddressPhone,omitempty"`
	BuyerBankAccount     string                     `json:"buyerBankAccount,omitempty"`
	Drawer               string                     `json:"drawer,omitempty"`
	Checker              string                     `json:"checker,omitempty"`
	Payee                string                     `json:"payee,omitempty"`
	InvoiceTotalPrice    float64                    `json:"invoiceTotalPrice,omitempty"`
	InvoiceTotalTax      float64                    `json:"invoiceTotalTax,omitempty"`
	InvoiceTotalPriceTax float64                    `json:"invoiceTotalPriceTax,omitempty"`
	Remarks              string                     `json:"remarks,omitempty"`
	RedInfoNo            string                     `json:"redInfoNo,omitempty"`
	OriginalInvoiceCode  string                     `json:"originalInvoiceCode,omitempty"`
	OriginalInvoiceNo    string                     `json:"originalInvoiceNo,omitempty"`
	DeductibleAmount     float64                    `json:"deductibleAmount,omitempty"`
	SellerAddressPhone   string                     `json:"sellerAddressPhone,omitempty"`
	SellerBankAccount    string                     `json:"sellerBankAccount,omitempty"`
	Ext                  map[string]interface{}     `json:"ext,omitempty"`
	SystemName           string                     `json:"systemName,omitempty"`
	SystemId             string                     `json:"systemId,omitempty"`
	BuyerEmail           string                     `json:"buyerEmail,omitempty"`
	EmailCarbonCopy      string                     `json:"emailCarbonCopy,omitempty"`
	BuyerPhone           string                     `json:"buyerPhone,omitempty"`
	UserAccount          string                     `json:"userAccount,omitempty"`
	RedIssueReason       string                     `json:"redIssueReason,omitempty"`
	DiscountType         string                     `json:"discountType,omitempty"`
	DiscountAmount       float64                    `json:"discountAmount,omitempty"`
	DiscountRate         int64                      `json:"discountRate,omitempty"`
	MainGoodsName        string                     `json:"mainGoodsName,omitempty"`
	BuyerBankName        string                     `json:"buyerBankName,omitempty"`
	BuyerBankNumber      string                     `json:"buyerBankNumber,omitempty"`
	BuyerAddress         string                     `json:"buyerAddress,omitempty"`
	BuyerTelPhone        string                     `json:"buyerTelphone,omitempty"`
	SellerBankName       string                     `json:"sellerBankName,omitempty"`
	SellerBankNumber     string                     `json:"sellerBankNumber,omitempty"`
	SellerAddress        string                     `json:"sellerAddress,omitempty"`
	SellerTelPhone       string                     `json:"sellerTelphone,omitempty"`
	RedConfirmUuid       string                     `json:"redConfirmUuid,omitempty"`
	ContractNumber       string                     `json:"contractNumber,omitempty"`
	VoucherNo            string                     `json:"voucherNo,omitempty"`
	BuyerNaturalPerson   string                     `json:"buyerNaturalPerson,omitempty"`
	DisplayBuyer         bool                       `json:"displayBuyer,omitempty"`
	DisplaySeller        bool                       `json:"displaySeller,omitempty"`
	InvoiceDetailsList   []BaiwangInvoiceDetailItem `json:"invoiceDetailsList"`
}

type BaiwangInvoiceDetailItem struct {
	GoodsLineNo             int64                  `json:"goodsLineNo"`
	OriginalInvoiceDetailNo string                 `json:"originalInvoiceDetailNo,omitempty"`
	InvoiceLineNature       string                 `json:"invoiceLineNature,omitempty"`
	GoodsCode               string                 `json:"goodsCode,omitempty"`
	GoodsPersonalCode       string                 `json:"goodsPersonalCode,omitempty"`
	GoodsName               string                 `json:"goodsName,omitempty"`
	GoodsSpecification      string                 `json:"goodsSpecification,omitempty"`
	GoodsUnit               string                 `json:"goodsUnit,omitempty"`
	GoodsQuantity           float64                `json:"goodsQuantity,omitempty"`
	GoodsPrice              float64                `json:"goodsPrice,omitempty"`
	GoodsTotalPrice         float64                `json:"goodsTotalPrice"`
	GoodsTotalTax           float64                `json:"goodsTotalTax,omitempty"`
	GoodsTaxRate            float64                `json:"goodsTaxRate"`
	VatSpecialManagement    string                 `json:"vatSpecialManagement,omitempty"`
	FreeTaxMark             string                 `json:"freeTaxMark,omitempty"`
	PreferentialMarkFlag    string                 `json:"preferentialMarkFlag,omitempty"`
	GoodsDiscountAmount     float64                `json:"goodsDiscountAmount,omitempty"`
	PriceTaxMark            string                 `json:"priceTaxMark,omitempty"`
	Ext                     map[string]interface{} `json:"ext,omitempty"`
}

type CreateBaiwangInvoiceResponse struct {
	BaiwangCommonResponse
	Response struct {
		Fail    []CreateBaiwangInvoiceResponseData `json:"fail,omitempty"`
		Success []CreateBaiwangInvoiceResponseData `json:"success,omitempty"`
	} `json:"response,omitempty"`
}

type CreateBaiwangInvoiceResponseData struct {
	InvoiceCode          string                     `json:"invoiceCode,omitempty"`
	InvoiceNo            string                     `json:"invoiceNo,omitempty"`
	InvoiceCheckCode     string                     `json:"invoiceCheckCode,omitempty"`
	InvoiceDate          string                     `json:"invoiceDate,omitempty"`
	InvoiceQrCode        string                     `json:"invoiceQrCode,omitempty"`
	TaxControlCode       string                     `json:"taxControlCode,omitempty"`
	InvoiceTypeCode      string                     `json:"invoiceTypeCode,omitempty"`
	SerialNo             string                     `json:"serialNo,omitempty"`
	EInvoiceUrl          string                     `json:"eInvoiceUrl,omitempty"`
	InvoiceTotalPrice    float64                    `json:"invoiceTotalPrice,omitempty"`
	InvoiceTotalPriceTax float64                    `json:"invoiceTotalPriceTax,omitempty"`
	InvoiceTotalTax      float64                    `json:"invoiceTotalTax,omitempty"`
	PaperInvoiceCode     string                     `json:"paperInvoiceCode,omitempty"`
	PaperInvoiceNo       string                     `json:"paperInvoiceNo,omitempty"`
	InvoiceDetailsList   []BaiwangInvoiceDetailItem `json:"invoiceDetailsList,omitempty"`
}

type BaiwangTokenRequest struct {
	AppSecret string `json:"client_secret,omitempty"`
	Username  string `json:"username,omitempty"`
	Password  string `json:"password,omitempty"`
}

type BaiwangTokenResponse struct {
	BaiwangCommonResponse
	Response struct {
		AccessToken  string `json:"access_token,omitempty"`
		TokenType    string `json:"token_type,omitempty"`
		RefreshToken string `json:"refresh_token,omitempty"`
		Scope        string `json:"scope,omitempty"`
		ExpireIn     int64  `json:"expires_in,omitempty"`
	} `json:"response,omitempty"`
}

type CreateBaiwangEInvoiceUrlRequest struct {
	TaxNo string                       `json:"taxNo"`
	Data  CreateBaiwangEInvoiceUrlData `json:"data"`
}

type CreateBaiwangEInvoiceUrlData struct {
	PushType         string `json:"pushType,omitempty"`
	InvoiceIssueMode string `json:"invoiceIssueMode,omitempty"`
	InvoiceCode      string `json:"invoiceCode,omitempty"`
	InvoiceNo        string `json:"invoiceNo,omitempty"`
	EInvoiceNo       string `json:"einvoiceNo,omitempty"`
	SerialNo         string `json:"serialNo,omitempty"`
	Phone            string `json:"phone,omitempty"`
	Email            string `json:"email,omitempty"`
	EmailCarbonCopy  string `json:"emailCarbonCopy,omitempty"`
}

type CreateBaiwangEInvoiceUrlResponse struct {
	BaiwangCommonResponse
	Response struct {
		EInvoiceUrl string `json:"einvoiceUrl"`
		FileType    string `json:"fileType"`
		UrlMap      struct {
			PdfUrl string `json:"pdfUrl,omitempty"`
			OfdUrl string `json:"ofdUrl,omitempty"`
			XmlUrl string `json:"xmlUrl,omitempty"`
		} `json:"urlMap"`
	} `json:"response,omitempty"`
}

type CreateBaiwangRedConfirmationRequest struct {
	InvoiceRequest
	TaxNo                         string                      `json:"taxNo"`
	OrgCode                       string                      `json:"orgCode"`
	TaxUserName                   string                      `json:"taxUserName"`
	Drawer                        string                      `json:"drawer"`
	DrawerCredentialsType         string                      `json:"drawerCredentialsType"`
	RedConfirmSerialNo            string                      `json:"redConfirmSerialNo"`
	EntryIdentity                 string                      `json:"entryIdentity"`
	SellerTaxNo                   string                      `json:"sellerTaxNo"`
	SellerTaxName                 string                      `json:"sellerTaxName"`
	DeliverFlag                   string                      `json:"deliverFlag"`
	BuyerEmail                    string                      `json:"buyerEmail"`
	BuyerPhone                    string                      `json:"buyerPhone"`
	BuyerTaxNo                    string                      `json:"buyerTaxNo"`
	BuyerTaxName                  string                      `json:"buyerTaxName"`
	RedInvoiceIsPaper             string                      `json:"redInvoiceIsPaper"`
	OriginInvoiceIsPaper          string                      `json:"originInvoiceIsPaper"`
	OriginalInvoiceNo             string                      `json:"originalInvoiceNo"`
	OriginalPaperInvoiceCode      string                      `json:"originalPaperInvoiceCode"`
	OriginalPaperInvoiceNo        string                      `json:"originalPaperInvoiceNo"`
	OriginInvoiceDate             string                      `json:"originInvoiceDate"`
	OriginInvoiceTotalPrice       float64                     `json:"originInvoiceTotalPrice"`
	OriginInvoiceTotalTax         float64                     `json:"originInvoiceTotalTax"`
	OriginInvoiceType             string                      `json:"originInvoiceType"`
	OriginInvoiceSetCode          string                      `json:"originInvoiceSetCode"`
	AutoIssueSwitch               string                      `json:"autoIssueSwitch"`
	InvoiceTotalPrice             float64                     `json:"invoiceTotalPrice"`
	InvoiceTotalTax               float64                     `json:"invoiceTotalTax"`
	RedInvoiceLabel               string                      `json:"redInvoiceLabel"`
	InvoiceSource                 string                      `json:"invoiceSource"`
	PriceTaxMark                  string                      `json:"priceTaxMark"`
	RedConfirmDetailReqEntityList []RedConfirmDetailReqEntity `json:"redConfirmDetailReqEntityList"`
}

type RedConfirmDetailReqEntity struct {
	OriginalInvoiceDetailNo int64   `json:"originalInvoiceDetailNo"`
	GoodsLineNo             int64   `json:"goodsLineNo"`
	GoodsCode               string  `json:"goodsCode"`
	GoodsName               string  `json:"goodsName,omitempty"`
	GoodsSimpleName         string  `json:"goodsSimpleName,omitempty"`
	ProjectName             string  `json:"projectName"`
	GoodsSpecification      string  `json:"goodsSpecification,omitempty"`
	GoodsUnit               string  `json:"goodsUnit,omitempty"`
	GoodsPrice              string  `json:"goodsPrice,omitempty"`
	GoodsQuantity           string  `json:"goodsQuantity,omitempty"`
	GoodsTaxRate            float64 `json:"goodsTaxRate"`
	GoodsTotalPrice         float64 `json:"goodsTotalPrice,omitempty"`
	GoodsTotalTax           float64 `json:"goodsTotalTax"`
	GoodsPriceTax           float64 `json:"goodsPriceTax,omitempty"`
	GoodsTotalPriceTax      float64 `json:"goodsTotalPriceTax"`
}

type CreateBaiwangRedConfirmationResponse struct {
	BaiwangCommonResponse
	Response []struct {
		RedConfirmSerialNo string `json:"redConfirmSerialNo"`
		RedConfirmNo       string `json:"redConfirmNo"`
		RedConfirmUuid     string `json:"redConfirmUuid"`
		ConfirmState       string `json:"confirmState"`
		RedInvoiceNo       string `json:"redInvoiceNo"`
		ConfirmBillingMark string `json:"confirmBillingMark"`
	} `json:"response,omitempty"`
}

type IssueBaiwangRedInvoiceRequest struct {
	TaxNo          string `json:"taxNo"`
	SerialNo       string `json:"serialNo"`
	RedConfirmUuid string `json:"redConfirmUuid"`
	Drawer         string `json:"drawer"`
}

type IssueBaiwangRedInvoiceResponse struct {
	BaiwangCommonResponse
	Response struct {
		SerialNo        string `json:"serialNo"`
		InvoiceDate     string `json:"invoiceDate"`
		EInvoiceNo      string `json:"einvoiceNo"`
		InvoiceTypeCode string `json:"invoiceTypeCode"`
	} `json:"response"`
}

func (client *baiwangClient) Invoice(ctx context.Context, req *InvoiceRequest) (*InvoiceResponse, error) {
	createBaiwangInvoiceReq := client.genInvoiceRequestContent(req)
	var resp CreateBaiwangInvoiceResponse
	err := client.executeRetryably(ctx, "baiwang.output.invoice.issue", &createBaiwangInvoiceReq, &req.PublicParameters, &resp)
	if err != nil {
		if strings.Contains(err.Error(), BAIWANG_INVOICE_API_ERROR_REQUEST_REPEATED) {
			return &InvoiceResponse{}, nil
		}
		return nil, err
	}
	if len(resp.Response.Success) == 0 {
		return nil, errors.New("Failed to create baiwang invoice")
	}
	invoiceDetail := resp.Response.Success[0]
	invoicedAt, _ := time.ParseInLocation("20060102150405", invoiceDetail.InvoiceDate, time.Local)
	return &InvoiceResponse{
		Url:        invoiceDetail.EInvoiceUrl,
		Code:       invoiceDetail.InvoiceCode,
		Number:     invoiceDetail.InvoiceNo,
		InvoicedAt: invoicedAt.In(time.UTC).Format("20060102150405"),
		Amount:     uint64(invoiceDetail.InvoiceTotalPriceTax * 100),
	}, nil
}

func (client *baiwangClient) CreateRedConfirmation(ctx context.Context, req *CreateRedConfirmationRequest) (*CreateRedConfirmationResponse, error) {
	listInvoicesResp, err := client.ListInvoices(ctx, &GetBaiwangInvoiceRequest{
		TaxNo: req.DrawerTaxpayerId,
		Data: BaiwangInvoiceData{
			SerialNo: req.BlueInvoiceSerialNo,
		},
	}, &req.PublicParameters)
	if err != nil {
		return nil, err
	}
	blueInvoice := listInvoicesResp.Response[0]
	var createRedConfirmationResp CreateBaiwangRedConfirmationResponse
	createRedConfirmationRequest := &CreateBaiwangRedConfirmationRequest{
		TaxNo:                   req.DrawerTaxpayerId,     // 机构税号
		Drawer:                  blueInvoice.Drawer,       // 乐企开票人
		RedConfirmSerialNo:      bson.NewObjectId().Hex(), // 红字确认单流水号
		EntryIdentity:           "01",                     // 录入方身份 01-销方 02-购方
		SellerTaxNo:             req.DrawerTaxpayerId,     // 销方统一社会信用代码/纳税人识别号/身份证件号码
		SellerTaxName:           client.limitStringLength(req.SellerTaxpayerFullName, 20),
		BuyerTaxNo:              blueInvoice.BuyerTaxNo,
		BuyerTaxName:            blueInvoice.BuyerName,                                              // 购方名称
		OriginInvoiceIsPaper:    "N",                                                                // Y-纸质发票 N-电子发票
		OriginalInvoiceNo:       blueInvoice.InvoiceNo,                                              // 蓝票号码，发票来源为 2 时必填
		OriginInvoiceDate:       blueInvoice.InvoiceDate,                                            // 蓝票开具日期
		OriginInvoiceTotalPrice: cast.ToFloat64(fmt.Sprintf("%.2f", blueInvoice.InvoiceTotalPrice)), // 蓝票合计金额
		OriginInvoiceTotalTax:   blueInvoice.InvoiceTotalTax,                                        // 蓝票合计税额
		OriginInvoiceType:       blueInvoice.InvoiceTypeCode,                                        // 蓝票票种
		AutoIssueSwitch:         "Y",                                                                // Y-自动开票 N-不自动开票
		InvoiceTotalPrice: func() float64 {
			// 不含税价格 = 含税价格 / (1 + 税率)
			var totalPrice float64
			if req.OrderInfo.DeliveryFee > 0 && req.DeliveryFeeTemplate != nil {
				totalPrice += cast.ToFloat64(req.OrderInfo.DeliveryFee) / (1 + client.goodsTaxRate(req.DeliveryFeeTemplate.FavorablePolicy, req.DeliveryFeeTemplate.TaxRate))
			}
			for _, product := range req.Products {
				totalPrice += cast.ToFloat64(product.OrderProduct.PayAmount) / (1 + client.goodsTaxRate(product.InvoiceTemplate.FavorablePolicy, product.InvoiceTemplate.TaxRate))
			}
			return -client.fen2Yuan(math.Round(totalPrice))
		}(), // 红字冲销金额
		InvoiceTotalTax: func() float64 {
			var totalTax float64
			if req.OrderInfo.DeliveryFee > 0 && req.DeliveryFeeTemplate != nil {
				taxRate := client.goodsTaxRate(req.DeliveryFeeTemplate.FavorablePolicy, req.DeliveryFeeTemplate.TaxRate)
				totalTax += float64(req.OrderInfo.DeliveryFee) / (1 + taxRate) * taxRate
			}
			for _, product := range req.Products {
				taxRate := client.goodsTaxRate(product.InvoiceTemplate.FavorablePolicy, product.InvoiceTemplate.TaxRate)
				totalTax += float64(product.OrderProduct.PayAmount) / (1 + taxRate) * taxRate
			}
			return -client.fen2Yuan(math.Round(totalTax))
		}(), // 红字冲销税额
		RedInvoiceLabel:               "02", // 冲红原因 01-开票有误 02-销货退回（01 不能部分红冲，影响部分退款后的发票重开，固定传 02）
		InvoiceSource:                 "2",  // 发票来源 2-电子发票服务平台
		PriceTaxMark:                  "1",  // 含税标识 1-含税
		RedConfirmDetailReqEntityList: client.genBaiwangRedConfirmDetailEntityList(ctx, req, blueInvoice),
	}
	if err := client.executeRetryably(ctx, "baiwang.output.redinvoice.add", createRedConfirmationRequest, &req.PublicParameters, &createRedConfirmationResp); err != nil {
		return nil, err
	}
	if len(createRedConfirmationResp.Response) == 0 {
		return nil, errors.New("Failed to create red confirm of baiwang")
	}
	return &CreateRedConfirmationResponse{
		RedConfirmUuid: createRedConfirmationResp.Response[0].RedConfirmUuid,
	}, nil
}

func (client *baiwangClient) IssueRedInvoice(ctx context.Context, req *InvoiceRequest) error {
	var resp IssueBaiwangRedInvoiceResponse
	IssueRedInvoiceRequest := &IssueBaiwangRedInvoiceRequest{
		TaxNo:          req.DrawerTaxpayerId,                     // 机构税号
		SerialNo:       req.Invoice.SerialNo,                     // 开票流水号
		RedConfirmUuid: req.Invoice.RedConfirmUuid,               // 红字确认单 uuid
		Drawer:         client.limitStringLength(req.Drawer, 20), // 开票人
	}
	if err := client.executeRetryably(ctx, "baiwang.output.redinvoice.issued", IssueRedInvoiceRequest, &req.PublicParameters, &resp); err != nil {
		return err
	}
	return nil
}

func (client *baiwangClient) Download(ctx context.Context, req *DownloadRequest) (*DownloadResponse, error) {
	resp, err := client.ListInvoices(ctx, &GetBaiwangInvoiceRequest{
		TaxNo: req.DrawerTaxpayerId,
		Data: BaiwangInvoiceData{
			SerialNo: req.Invoice.SerialNo,
		},
	}, &req.PublicParameters)
	if err != nil {
		return nil, err
	}
	invoiceDetail := resp.Response[0]
	if invoiceDetail.InvoiceStatus == BAIWANG_INVOICE_STATUS_FAILED {
		return nil, errors.New(BAIWANG_INVOICE_VOIDED_ERROR)
	}
	if invoiceDetail.EInvoiceUrl == "" {
		return nil, errors.New(BAIWANG_INVOICE_URL_EMPTY_ERROR)
	}
	invoicedAt, _ := time.ParseInLocation("2006-01-02 15:04:05", invoiceDetail.InvoiceDate, time.Local)
	return &DownloadResponse{
		Url:        invoiceDetail.EInvoiceUrl,
		InvoicedAt: invoicedAt.In(time.UTC).Format("2006-01-02 15:04:05"),
	}, nil
}

func (client *baiwangClient) ListInvoices(ctx context.Context, req *GetBaiwangInvoiceRequest, commonReq *PublicParameters) (*GetBaiwangInvoiceResponse, error) {
	var resp GetBaiwangInvoiceResponse
	err := client.executeRetryably(ctx, "baiwang.output.einvoice.query", req, commonReq, &resp)
	if err != nil {
		return nil, err
	}
	if len(resp.Response) == 0 {
		return nil, errors.New(BAIWANG_INVOICE_NOT_EXISTS_ERROR)
	}
	return &resp, nil
}

func (client *baiwangClient) SendToEmail(ctx context.Context, req *SendToEmailRequest) (*SendToEmailResponse, error) {
	return nil, nil
}

func (client *baiwangClient) CreateEInvoiceUrl(ctx context.Context, req *DownloadRequest) error {
	var resp CreateBaiwangEInvoiceUrlResponse
	err := client.executeRetryably(ctx, "baiwang.output.format.create", &CreateBaiwangEInvoiceUrlRequest{
		TaxNo: req.DrawerTaxpayerId,
		Data: CreateBaiwangEInvoiceUrlData{
			InvoiceIssueMode: "1", // 数电版式生成，其他代表税控发票生成
			SerialNo:         req.Invoice.SerialNo,
		},
	}, &req.InvoiceRequest.PublicParameters, &resp)
	if err != nil {
		return err
	}
	return nil
}

func (client *baiwangClient) executeRetryably(ctx context.Context, method string, request interface{}, commonRequest *PublicParameters, resp interface{}) error {
	return retry.Do(
		func() error {
			url := client.formatUrlWithQueryString(method, commonRequest, request)
			return client.execute(ctx, url, request, resp)
		},
		retry.RetryIf(func(err error) bool {
			return err != nil && !strings.Contains(err.Error(), BAIWANG_INVOICE_API_ERROR_REQUEST_REPEATED)
		}),
		retry.Attempts(3),
		retry.LastErrorOnly(true),
		retry.DelayType(retry.BackOffDelay),
		retry.OnRetry(func(n uint, err error) {
			regex := regexp.MustCompile(BAIWANG_INVOICE_API_ERROR_CAN_REFRESH_TOKEN)
			if regex.MatchString(err.Error()) {
				newToken := client.refreshToken(ctx, commonRequest)
				commonRequest.AuthorizationCode = newToken
			}
		}),
	)
}

func (client *baiwangClient) execute(ctx context.Context, url string, request interface{}, resp interface{}) error {
	ctx = log.SwitchOnResponseBodyLog(ctx)
	body, _, innerError := extension.RequestClient.PostJson(ctx, "", url, request, nil)
	if innerError != nil {
		return innerError
	}
	if innerError := jsoniterUsingNum.Unmarshal(body, resp); innerError != nil {
		return innerError
	}
	commonResp := &BaiwangCommonResponse{}
	copier.Instance(nil).From(resp).CopyTo(commonResp)
	if commonResp.Success == true {
		return nil
	}
	return commonResp.error()
}

func (client *baiwangClient) formatUrlWithQueryString(method string, commonRequest *PublicParameters, request interface{}) string {
	queryStringArgs := &url.Values{}
	queryStringArgs.Add("method", method)
	queryStringArgs.Add("appKey", commonRequest.AppKey)
	queryStringArgs.Add("token", commonRequest.AuthorizationCode)
	queryStringArgs.Add("timestamp", cast.ToString(time.Now().Unix()))
	queryStringArgs.Add("format", "json")
	queryStringArgs.Add("version", "6.0")
	queryStringArgs.Add("type", "sync")
	queryStringArgs.Add("sign", client.getSign(*queryStringArgs, commonRequest.AppSecret, request))
	return fmt.Sprintf("%s?%s", BAIWANG_ROUTER_REST_URL, queryStringArgs.Encode())
}

func (client *baiwangClient) formatAuthUrlWithQueryString(grantType string, commonRequest *PublicParameters) string {
	queryStringArgs := &url.Values{}
	queryStringArgs.Add("method", "baiwang.oauth.token")
	queryStringArgs.Add("timestamp", cast.ToString(time.Now().UnixMilli()))
	queryStringArgs.Add("grant_type", grantType)
	if grantType == "refresh_token" {
		queryStringArgs.Add("refresh_token", commonRequest.RefreshToken)
	}
	queryStringArgs.Add("version", "6.0")
	queryStringArgs.Add("client_id", commonRequest.AppKey)
	return fmt.Sprintf("%s?%s", BAIWANG_ROUTER_REST_URL, queryStringArgs.Encode())
}

func (client *baiwangClient) getSign(args url.Values, appSecret string, request interface{}) string {
	keys := []string{}
	for k := range args {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	query := appSecret
	for _, k := range keys {
		query += k + args.Get(k)
	}
	resp, _ := jsoniterUsingNum.Marshal(request)
	m := &map[string]interface{}{}
	jsoniterUsingNum.Unmarshal(resp, m)
	reqBody, _ := jsoniterUsingNum.Marshal(m)
	query += string(reqBody)
	query += appSecret
	signBytes := md5.Sum([]byte(query))
	return strings.ToUpper(hex.EncodeToString(signBytes[:]))
}

func (resp *BaiwangCommonResponse) error() error {
	return fmt.Errorf(
		"baiwang:method:%s,code:%s,message:%s,subCode:%s,subMessage:%s,requestId:%s",
		resp.Method,
		resp.ErrorResponse.Code,
		resp.ErrorResponse.Message,
		resp.ErrorResponse.SubCode,
		resp.ErrorResponse.SubMessage,
		resp.RequestId,
	)
}

func (client *baiwangClient) genInvoiceRequestContent(req *InvoiceRequest) CreateBaiwangInvoiceRequest {
	return CreateBaiwangInvoiceRequest{
		TaxNo:          req.DrawerTaxpayerId, // 税号（销方机构税号）
		FormatGenerate: true,                 // 是否生成并返回版式链接
		FormatPushType: true,                 // 版式生成是否推送
		Data: CreateBaiwangInvoiceData{
			InvoiceTypeCode: "02",                                            // 发票种类编码：02-全电发票（普通发票）
			InvoiceType:     "0",                                             // 开票类型：0-蓝票，1-红票
			TaxationMethod:  "0",                                             // 征税方式：0-普通征税，2-差额征税
			PriceTaxMark:    "1",                                             // 含税标志：0-不含税，1-含税
			SerialNo:        req.Invoice.SerialNo,                            // 开票流水号
			BuyerTaxNo:      req.Invoice.TaxID,                               // 购方单位税号
			BuyerName:       client.limitStringLength(req.Invoice.Name, 100), // 购方单位名称
			Drawer:          client.limitStringLength(req.Drawer, 20),        // 开票人
			Checker:         client.limitStringLength(req.Reviewer, 16),      // 复核人
			Payee:           client.limitStringLength(req.Payer, 16),         // 收款人
			Remarks: func() string {
				remarks := []string{}
				for _, item := range req.Products {
					if item.InvoiceTemplate == nil || item.InvoiceTemplate.Remarks == "" {
						continue
					}
					remarks = append(remarks, item.InvoiceTemplate.Remarks)
				}
				return client.limitStringLength(strings.Join(remarks, "；"), 200)
			}(), // 备注
			BuyerEmail:       req.Invoice.Email,                                  // 客户邮箱
			BuyerPhone:       client.buyerPhone(req.Invoice.Phone),               // 客户电话
			BuyerBankName:    client.limitStringLength(req.Invoice.BankName, 80), // 购买方银行名称
			BuyerBankNumber:  req.Invoice.BankAccount,                            // 购买方银行账号
			BuyerAddress:     client.limitStringLength(req.Invoice.Address, 80),  // 购买方地址
			BuyerTelPhone:    client.buyerPhone(req.Invoice.Phone),               // 购买方电话
			SellerBankName:   client.limitStringLength(req.SellerBankName, 80),   // 销方银行名称
			SellerBankNumber: req.SellerBankAccount,                              // 销方银行账号
			SellerAddress:    client.limitStringLength(req.SellerAddress, 200),   // 销方地址
			SellerTelPhone:   req.SellerPhone,                                    // 销方电话
			BuyerNaturalPerson: func() string {
				if req.Invoice.Type == "person" {
					return "Y"
				}
				return "N"
			}(), // 购方自然人标记：Y-自然人，N-非自然人，默认为 N
			DisplayBuyer:       true, // 购方开户行银行账号是否显示在备注
			DisplaySeller:      true, // 销方开户行银行账号是否显示在备注
			InvoiceDetailsList: client.genBaiwangInvoiceDetailItems(req),
		},
	}
}

func (client *baiwangClient) genBaiwangRedConfirmDetailEntityList(ctx context.Context, req *CreateRedConfirmationRequest, blueInvoice GetBaiwangInvoiceResponseItem) []RedConfirmDetailReqEntity {
	var redConfirmDetailEntityList []RedConfirmDetailReqEntity
	productNumberToInvoiceDetailMap := make(map[string][]BaiwangInvoiceDetail)
	for _, blueInvoiceDetail := range blueInvoice.InvoiceDetails {
		productNumberToInvoiceDetailMap[blueInvoiceDetail.GoodsPersonalCode] = append(productNumberToInvoiceDetailMap[blueInvoiceDetail.GoodsPersonalCode], blueInvoiceDetail)
	}
	lineNo := 1
	for _, product := range req.Products {
		if product.OrderProduct.PayAmount == 0 {
			continue
		}
		redConfirmDetailEntityList = append(redConfirmDetailEntityList, RedConfirmDetailReqEntity{
			OriginalInvoiceDetailNo: func() int64 {
				invoiceDetails := productNumberToInvoiceDetailMap[fmt.Sprintf("%s:%s", product.OrderProduct.Number, product.OrderProduct.Spec.Sku)]
				if len(invoiceDetails) == 0 {
					invoiceDetails = productNumberToInvoiceDetailMap[product.OrderProduct.Number]
				}
				for _, invoiceDetail := range invoiceDetails {
					if invoiceDetail.InvoiceLineNature == INVOICE_LINE_NATURE_DISCOUNT {
						continue
					}
					return cast.ToInt64(invoiceDetail.GoodsLineNo)
				}
				return 0
			}(),
			GoodsLineNo: int64(lineNo),
			GoodsCode: func() string {
				invoiceDetails := productNumberToInvoiceDetailMap[fmt.Sprintf("%s:%s", product.OrderProduct.Number, product.OrderProduct.Spec.Sku)]
				if len(invoiceDetails) > 0 {
					return productNumberToInvoiceDetailMap[fmt.Sprintf("%s:%s", product.OrderProduct.Number, product.OrderProduct.Spec.Sku)][0].GoodsCode
				}
				invoiceDetails = productNumberToInvoiceDetailMap[product.OrderProduct.Number]
				if len(invoiceDetails) == 0 {
					log.Warn(ctx, "Failed to get goods code from original invoice", log.Fields{
						"blueInvoiceNo":      blueInvoice.InvoiceNo,
						"blueInvoiceDetails": blueInvoice.InvoiceDetails,
					})
					return ""
				}
				return productNumberToInvoiceDetailMap[product.OrderProduct.Number][0].GoodsCode
			}(),
			ProjectName:  product.OrderProduct.Name,
			GoodsTaxRate: client.goodsTaxRate(product.InvoiceTemplate.FavorablePolicy, product.InvoiceTemplate.TaxRate),
			GoodsTotalTax: func() float64 {
				taxRate := client.goodsTaxRate(product.InvoiceTemplate.FavorablePolicy, product.InvoiceTemplate.TaxRate)
				totalPrice := client.fen2Yuan(float64(product.OrderProduct.PayAmount) / (1 + taxRate))
				totalPriceWithTax := cast.ToFloat64(fmt.Sprintf("%.2f", totalPrice*taxRate))
				return -totalPriceWithTax
			}(),
			GoodsPrice:         cast.ToString(client.fen2Yuan(product.OrderProduct.Price)),
			GoodsQuantity:      cast.ToString(-int64(product.OrderProduct.Total)),
			GoodsTotalPriceTax: -client.fen2Yuan(product.OrderProduct.PayAmount),
		})
		lineNo++
	}
	if req.OrderInfo.DeliveryFee > 0 && req.DeliveryFeeTemplate != nil {
		redConfirmDetailEntityList = append(redConfirmDetailEntityList, RedConfirmDetailReqEntity{
			OriginalInvoiceDetailNo: func() int64 {
				if len(productNumberToInvoiceDetailMap[req.DeliveryFeeTemplate.TaxClassificationCode]) == 0 {
					log.Warn(ctx, "Failed to get lineNo of delivery fee from original invoice", log.Fields{
						"blueInvoiceNo":      blueInvoice.InvoiceNo,
						"blueInvoiceDetails": blueInvoice.InvoiceDetails,
					})
					return 0
				}
				return cast.ToInt64(productNumberToInvoiceDetailMap[req.DeliveryFeeTemplate.TaxClassificationCode][0].GoodsLineNo)
			}(),
			GoodsLineNo:  int64(lineNo),
			GoodsCode:    req.DeliveryFeeTemplate.TaxClassificationCode,
			ProjectName:  client.limitStringLength(req.DeliveryFeeTemplate.Name, 100),
			GoodsTaxRate: client.goodsTaxRate(req.DeliveryFeeTemplate.FavorablePolicy, req.DeliveryFeeTemplate.TaxRate),
			GoodsTotalTax: func() float64 {
				taxRate := client.goodsTaxRate(req.DeliveryFeeTemplate.FavorablePolicy, req.DeliveryFeeTemplate.TaxRate)
				totalPrice := client.fen2Yuan(float64(req.OrderInfo.DeliveryFee) / (1 + taxRate))
				totalPriceWithTax := cast.ToFloat64(fmt.Sprintf("%.2f", totalPrice*taxRate))
				return -totalPriceWithTax
			}(),
			GoodsPrice:         cast.ToString(client.fen2Yuan(req.OrderInfo.DeliveryFee)),
			GoodsQuantity:      "-1",
			GoodsTotalPriceTax: -client.fen2Yuan(req.OrderInfo.DeliveryFee),
		})
	}
	return redConfirmDetailEntityList
}

func (client *baiwangClient) genBaiwangInvoiceDetailItems(req *InvoiceRequest) []BaiwangInvoiceDetailItem {
	baiwangInvoiceDetailItems := []BaiwangInvoiceDetailItem{}
	lineNo := 1
	for _, p := range req.Products {
		if p.OrderProduct.PayAmount == 0 {
			continue
		}
		item := BaiwangInvoiceDetailItem{
			GoodsLineNo: int64(lineNo), // 明细行号
			InvoiceLineNature: func() string {
				if p.OrderProduct.PayAmount < p.OrderProduct.Price*p.OrderProduct.Total {
					return INVOICE_LINE_NATURE_DISCOUNTED
				}
				return INVOICE_LINE_NATURE_NORMAL
			}(), // 发票行性质
			GoodsCode: p.InvoiceTemplate.TaxClassificationCode, // 税收分类编码（必须是末级节点，可在 https://znfm.baiwang.com 查询）
			GoodsPersonalCode: func() string {
				if p.OrderProduct.Spec.Sku == "" {
					return p.OrderProduct.Number
				}
				return fmt.Sprintf("%s:%s", p.OrderProduct.Number, p.OrderProduct.Spec.Sku)
			}(), // 商品编码
			GoodsName: client.limitStringLength(p.OrderProduct.Name, 100), // 商品名称
			GoodsSpecification: func() string {
				if p.OrderProduct.Spec == nil || len(p.OrderProduct.Spec.Properties) == 0 {
					return ""
				}
				return client.limitStringLength(strings.Join(p.OrderProduct.Spec.Properties, "|"), 36)
			}(), // 规格型号
			GoodsQuantity:        float64(p.OrderProduct.Total),                                                     // 商品数量
			GoodsPrice:           client.fen2Yuan(p.OrderProduct.Price),                                             // 商品单价
			GoodsTotalPrice:      client.fen2Yuan(p.OrderProduct.Total * p.OrderProduct.Price),                      // 金额
			GoodsTaxRate:         client.goodsTaxRate(p.InvoiceTemplate.FavorablePolicy, p.InvoiceTemplate.TaxRate), // 税率
			VatSpecialManagement: client.vatSpecialManagement(p.InvoiceTemplate.FavorablePolicy),                    // 优惠政策类型
			FreeTaxMark:          client.getFreeTaxMark(p.InvoiceTemplate.FavorablePolicy),                          // 零税率标识
			PreferentialMarkFlag: client.preferentialMarkFlag(p.InvoiceTemplate.FavorablePolicy),                    // 是否使用优惠政策
		}
		baiwangInvoiceDetailItems = append(baiwangInvoiceDetailItems, item)
		lineNo++
		if p.OrderProduct.Price*p.OrderProduct.Total > p.OrderProduct.PayAmount {
			item = BaiwangInvoiceDetailItem{
				GoodsLineNo:          int64(lineNo),                                                        // 明细行号
				InvoiceLineNature:    INVOICE_LINE_NATURE_DISCOUNT,                                         // 发票行性质
				GoodsCode:            p.InvoiceTemplate.TaxClassificationCode,                              // 税收分类编码（必须是末级节点，可在 https://znfm.baiwang.com 查询）
				GoodsPersonalCode:    fmt.Sprintf("%s:%s", p.OrderProduct.Number, p.OrderProduct.Spec.Sku), // 商品编码
				GoodsName:            client.limitStringLength(p.OrderProduct.Name, 100),                   // 商品名称
				GoodsTotalPrice:      -client.fen2Yuan(p.OrderProduct.Price*p.OrderProduct.Total - p.OrderProduct.PayAmount),
				GoodsTaxRate:         client.goodsTaxRate(p.InvoiceTemplate.FavorablePolicy, p.InvoiceTemplate.TaxRate), // 税率
				VatSpecialManagement: client.vatSpecialManagement(p.InvoiceTemplate.FavorablePolicy),                    // 优惠政策类型
				FreeTaxMark:          client.getFreeTaxMark(p.InvoiceTemplate.FavorablePolicy),                          // 零税率标识
				PreferentialMarkFlag: client.preferentialMarkFlag(p.InvoiceTemplate.FavorablePolicy),                    // 是否使用优惠政策
			}
			baiwangInvoiceDetailItems = append(baiwangInvoiceDetailItems, item)
			lineNo++
		}
	}
	if req.OrderInfo.DeliveryFee > 0 && req.DeliveryFeeTemplate != nil {
		item := BaiwangInvoiceDetailItem{
			GoodsLineNo:          int64(lineNo),                                                                                 // 明细行号
			InvoiceLineNature:    INVOICE_LINE_NATURE_NORMAL,                                                                    // 发票行性质
			GoodsCode:            req.DeliveryFeeTemplate.TaxClassificationCode,                                                 // 税收分类编码（必须是末级节点，可在 https://znfm.baiwang.com 查询）
			GoodsPersonalCode:    req.DeliveryFeeTemplate.TaxClassificationCode,                                                 // 商品编码
			GoodsName:            client.limitStringLength(req.DeliveryFeeTemplate.Name, 100),                                   // 商品名称
			GoodsSpecification:   "",                                                                                            // 规格型号
			GoodsTotalPrice:      client.fen2Yuan(req.OrderInfo.DeliveryFee),                                                    // 金额
			GoodsTaxRate:         client.goodsTaxRate(req.DeliveryFeeTemplate.FavorablePolicy, req.DeliveryFeeTemplate.TaxRate), // 税率
			FreeTaxMark:          client.getFreeTaxMark(req.DeliveryFeeTemplate.FavorablePolicy),                                // 零税率标识
			PreferentialMarkFlag: client.preferentialMarkFlag(req.DeliveryFeeTemplate.FavorablePolicy),                          // 是否使用优惠政策
		}
		baiwangInvoiceDetailItems = append(baiwangInvoiceDetailItems, item)
	}
	return baiwangInvoiceDetailItems
}

func (client *baiwangClient) refreshToken(ctx context.Context, req *PublicParameters) string {
	var (
		url  string
		resp BaiwangTokenResponse
	)
	defer func() {
		if resp.Response.AccessToken == "" {
			log.Error(ctx, "Failed to refresh baiwang token", log.Fields{
				"url":  fmt.Sprintf("%s", url),
				"req":  fmt.Sprintf("%+v", *req),
				"resp": fmt.Sprintf("%+v", resp),
			})
		}
	}()
	invoiceSetting, err := setting_model.CInvoiceSetting.Get(ctx)
	if err != nil {
		log.Error(ctx, "Failed to get invoice setting", log.Fields{
			"errMsg": err.Error(),
		})
		return ""
	}
	if req.RefreshToken != "" {
		url = client.formatAuthUrlWithQueryString("refresh_token", req)
		client.execute(ctx, url, &BaiwangTokenRequest{
			AppSecret: req.AppSecret,
		}, &resp)
		if resp.Response.AccessToken != "" {
			invoiceSetting.PublicParameters.AuthorizationCode = resp.Response.AccessToken
			if resp.Response.RefreshToken != "" {
				invoiceSetting.PublicParameters.RefreshToken = resp.Response.RefreshToken
			}
			if err := invoiceSetting.Update(ctx); err != nil {
				log.Error(ctx, "Failed to update invoice setting", log.Fields{
					"errMsg": err.Error(),
				})
			}
			return resp.Response.AccessToken
		}
	}
	url = client.formatAuthUrlWithQueryString("password", req)
	client.execute(ctx, url, &BaiwangTokenRequest{
		AppSecret: req.AppSecret,
		Username:  req.Username,
		Password:  encryptWithSha1(fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%s%s", req.Password, req.Salt))))),
	}, &resp)
	if resp.Response.AccessToken != "" {
		invoiceSetting.PublicParameters.AuthorizationCode = resp.Response.AccessToken
		if resp.Response.RefreshToken != "" {
			invoiceSetting.PublicParameters.RefreshToken = resp.Response.RefreshToken
		}
		if err := invoiceSetting.Update(ctx); err != nil {
			log.Error(ctx, "Failed to update invoice setting", log.Fields{
				"errMsg": err.Error(),
			})
		}
		return resp.Response.AccessToken
	}
	return ""
}

func encryptWithSha1(str string) string {
	h := sha1.New()
	io.WriteString(h, str)
	return fmt.Sprintf("%x", h.Sum(nil))
}

func (client *baiwangClient) fen2Yuan(fen interface{}) float64 {
	return cast.ToFloat64(fen) / 100.0
}

func (client *baiwangClient) getFreeTaxMark(favorablePolicy string) string {
	switch favorablePolicy {
	case INVOICE_FAVORABLE_POLICY_FREE_TAX:
		return "1"
	case INVOICE_FAVORABLE_POLICY_NO_TAX:
		return "2"
	case INVOICE_FAVORABLE_POLICY_ZERO_TAX:
		return "3"
	default:
		return ""
	}
}

func (client *baiwangClient) limitStringLength(value string, limit int) string {
	runeValue := []rune(value)
	if len(runeValue) > limit {
		return string(runeValue[:limit])
	}
	return value
}

func (client *baiwangClient) vatSpecialManagement(favorablePolicy string) string {
	if vatSpecialManagement, ok := vatSpecialManagementTypeLabel[favorablePolicy]; ok {
		return vatSpecialManagement
	}
	return ""
}

func (client *baiwangClient) preferentialMarkFlag(favorablePolicy string) string {
	if favorablePolicy == INVOICE_FAVORABLE_POLICY_NONUSE {
		return "0"
	}
	return "1"
}

func (client *baiwangClient) goodsTaxRate(favorablePolicy string, taxRate int64) float64 {
	if favorablePolicy == INVOICE_FAVORABLE_POLICY_NONUSE {
		return client.fen2Yuan(taxRate)
	}
	return 0
}

// 百望期望购买方 buyerPhone、buyerTelphone 参数，只支持手机号，不支持座机号
func (client *baiwangClient) buyerPhone(phone string) string {
	if regexp.MustCompile(`^1[0-9]{10}$`).MatchString(phone) {
		return phone
	}
	return ""
}
