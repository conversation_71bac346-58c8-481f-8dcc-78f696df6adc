package invoice

import (
	"context"
	"encoding/base64"
	"encoding/xml"
	"fmt"
	"mairpc/core/extension"
	"mairpc/core/log"
	"mairpc/service/share/util"
	"net/url"
	"strings"
	"time"

	"github.com/spf13/cast"

	"github.com/forgoer/openssl"
)

// API 文档：http://fw1.shdzfp.com:28888/doc

var (
	nuoeUrl           = "http://fw2test.shdzfp.com:15002/sajt-shdzfp-sl-http/SvrServlet"
	nuoeProductionUrl = "http://fw11.shdzfp.com:9012/sajt-shdzfp-sl-http/SvrServlet"
)

func NewNuoeClient(isTest bool) InvoiceRequestor {
	n := &nuoeClient{
		IsTest: isTest,
	}
	return n
}

type nuoeClient struct {
	IsTest bool
}

type GlobalInfo struct {
	TerminalCode      string `xml:"terminalCode"`
	AppId             string `xml:"appId"`
	Version           string `xml:"version"`
	InterfaceCode     string `xml:"interfaceCode"`
	RequestCode       string `xml:"requestCode"`
	RequestTime       string `xml:"requestTime"`
	ResponseCode      string `xml:"responseCode"`
	DataExchangeId    string `xml:"dataExchangeId"`
	UserName          string `xml:"userName"`
	PassWord          string `xml:"passWord"`
	TaxpayerId        string `xml:"taxpayerId"`
	AuthorizationCode string `xml:"authorizationCode"`
}

type ReturnStateInfo struct {
	ReturnCode    string `xml:"returnCode"`
	ReturnMessage string `xml:"returnMessage"`
}

type InvoiceData struct {
	DataDescription DataDescription `xml:"dataDescription"`
	Content         string          `xml:"content"`
}

type DataDescription struct {
	ZipCode     string `xml:"zipCode"`
	EncryptCode string `xml:"encryptCode"`
	CodeType    string `xml:"codeType"`
}

type RequestFpkjxx struct {
	XMLName     xml.Name    `xml:"REQUEST_FPKJXX"`
	Class       string      `xml:"class,attr"`
	FpkjxxFptxx FpkjxxFptxx `xml:"FPKJXX_FPTXX"`
	FpkjxxXmxxs FpkjxxXmxxs `xml:"FPKJXX_XMXXS"`
	FpkjxxDdxx  FpkjxxDdxx  `xml:"FPKJXX_DDXX"`
}

type FpkjxxXmxxs struct {
	Class      string       `xml:"class,attr"`
	Size       string       `xml:"size,attr"`
	FpkjxxXmxx []FpkjxxXmxx `xml:"FPKJXX_XMXX"`
}

type FpkjxxFptxx struct {
	Class     string `xml:"class,attr"`
	Fpqqlsh   string `xml:"FPQQLSH"`
	Dsptbm    string `xml:"DSPTBM"`
	Nsrsbh    string `xml:"NSRSBH"`
	Nsrmc     string `xml:"NSRMC"`
	Fjh       string `xml:"FJH"`
	Nsrdzdah  string `xml:"NSRDZDAH"`
	SwjgDm    string `xml:"SWJG_DM"`
	Dkbz      string `xml:"DKBZ"`
	Pydm      string `xml:"PYDM"`
	Kpxm      string `xml:"KPXM"`
	BmbBbh    string `xml:"BMB_BBH"`
	XhfNsrsbh string `xml:"XHF_NSRSBH"`
	Xhfmc     string `xml:"XHFMC"`
	XhfDz     string `xml:"XHF_DZ"`
	XhfDh     string `xml:"XHF_DH"`
	XhfYhzh   string `xml:"XHF_YHZH"`
	Ghfmc     string `xml:"GHFMC"`
	GhfNsrsbh string `xml:"GHF_NSRSBH"`
	GhfSf     string `xml:"GHF_SF"`
	GhfDz     string `xml:"GHF_DZ"`
	GhfGddh   string `xml:"GHF_GDDH"`
	GhfSj     string `xml:"GHF_SJ"`
	GhfEmail  string `xml:"GHF_EMAIL"`
	Ghfqlx    string `xml:"GHFQYLX"`
	GhfYhzh   string `xml:"GHF_YHZH"`
	HyDm      string `xml:"HY_DM"`
	HyMc      string `xml:"HY_MC"`
	Kpy       string `xml:"KPY"`
	Sky       string `xml:"SKY"`
	Fhr       string `xml:"FHR"`
	Kprq      string `xml:"KPRQ"`
	Kplx      string `xml:"KPLX"`
	YfpDm     string `xml:"YFP_DM"`
	YfpHm     string `xml:"YFP_HM"`
	Czdm      string `xml:"CZDM"`
	QdBz      string `xml:"QD_BZ"`
	Qdxmmc    string `xml:"QDXMMC"`
	YfpzlDm   string `xml:"YFPZL_DM"`
	Ykprq     string `xml:"YKPRQ"`
	Chyy      string `xml:"CHYY"`
	Tschbz    string `xml:"TSCHBZ"`
	Kphjje    string `xml:"KPHJJE"`
	Hjbhsje   string `xml:"HJBHSJE"`
	Hjse      string `xml:"HJSE"`
	Bz        string `xml:"BZ"`
	Fplx      string `xml:"FPLX"`
	Hztzd     string `xml:"HZTZD"`
	Byzd1     string `xml:"BYZD1"`
	Byzd2     string `xml:"BYZD2"`
	Byzd3     string `xml:"BYZD3"`
	Byzd4     string `xml:"BYZD4"`
	Byzd5     string `xml:"BYZD5"`
}

type FpkjxxXmxx struct {
	Xmmc    string `xml:"XMMC"`
	Xmdw    string `xml:"XMDW"`
	Ggxh    string `xml:"GGXH"`
	Xmsl    string `xml:"XMSL"`
	Hsbz    string `xml:"HSBZ"`
	Fphxz   string `xml:"FPHXZ"`
	Xmdj    string `xml:"XMDJ"`
	Spbm    string `xml:"SPBM"`
	Zxbm    string `xml:"ZXBM"`
	Yhzcbs  string `xml:"YHZCBS"`
	Lslbs   string `xml:"LSLBS"`
	Zzstsgl string `xml:"ZZSTSGL"`
	Kce     string `xml:"KCE"`
	Xmje    string `xml:"XMJE"`
	Sl      string `xml:"SL"`
	Se      string `xml:"SE"`
	Byzd1   string `xml:"BYZD1"`
	Byzd2   string `xml:"BYZD2"`
	Byzd3   string `xml:"BYZD3"`
	Byzd4   string `xml:"BYZD4"`
	Byzd5   string `xml:"BYZD5"`
}

type FpkjxxDdxx struct {
	Class  string `xml:"class,attr"`
	Ddh    string `xml:"DDH"`
	Thdh   string `xml:"THDH"`
	Dddate string `xml:"DDDATE"`
}

type NuoeEmptyResponse struct {
}

type RequestFpxxxzNew struct {
	XMLName xml.Name `xml:"REQUEST_FPXXXZ_NEW"`
	Class   string   `xml:"class,attr"`
	Fpqqlsh string   `xml:"FPQQLSH"`
	Dsptbm  string   `xml:"DSPTBM"`
	Nsrsbh  string   `xml:"NSRSBH"`
	Ddh     string   `xml:"DDH"`
	PdfXzfs string   `xml:"PDF_XZFS"`
}

type RequestFpkjxxFpjGxxNew struct {
	Fpqqlsh      string `xml:"FPQQLSH"`
	Ddh          string `xml:"DDH"`
	Kplsh        string `xml:"KPLSH"`
	Fwm          string `xml:"FWM"`
	Ewm          string `xml:"EWM"`
	FpzlDm       string `xml:"FPZL_DM"`
	FpDm         string `xml:"FP_DM"`
	FpHm         string `xml:"FP_HM"`
	Kprq         string `xml:"KPRQ"`
	Kplx         string `xml:"KPLX"`
	Hjbhsje      string `xml:"HJBHSJE"`
	Kphjse       string `xml:"KPHJSE"`
	PdfFile      string `xml:"PDF_FILE"`
	PdfUrl       string `xml:"PDF_URL"`
	Czdm         string `xml:"CZDM"`
	Returncode   string `xml:"RETURNCODE"`
	RrturnMesage string `xml:"RETURNMESSAGE"`
}

type RequestEmailphonefpts struct {
	XMLName xml.Name `xml:"REQUEST_EMAILPHONEFPTS"`
	Class   string   `xml:"class,attr"`
	Tsfsxx  Tsfsxx   `xml:"TSFSXX"`
	Fpxxs   Fpxxs    `xml:"FPXXS"`
}

type Tsfsxx struct {
	Class      string     `xml:"class,attr"`
	ComonNodes ComonNodes `xml:"COMMON_NODES"`
}

type ComonNodes struct {
	Class      string       `xml:"class,attr"`
	Size       string       `xml:"size,attr"`
	CommonNode []CommonNode `xml:"COMMON_NODE"`
}

type CommonNode struct {
	Name  string `xml:"NAME"`
	Value string `xml:"VALUE"`
}

type Fpxxs struct {
	Class string `xml:"class,attr"`
	Size  string `xml:"size,attr"`
	Fpxx  []Fpxx `xml:"FPXX"`
}

type Fpxx struct {
	ComonNodes ComonNodes `xml:"COMMON_NODES"`
}

type SendToEmailResponseContent struct {
	ResponseEmailphonefpts ResponseEmailphonefpts `xml:"RESPONSE_EMAILPHONEFPTS"`
}

type ResponseEmailphonefpts struct {
	ComonNodes ComonNodes `xml:"COMMON_NODES"`
}

type NuoeRequest struct {
	XMLName         xml.Name        `xml:"interface"`
	SchemaLocation  string          `xml:"xmlns:schemaLocation,attr"`
	Xsi             string          `xml:"xmlns:xsi,attr"`
	Version         string          `xml:"version,attr"`
	GlobalInfo      GlobalInfo      `xml:"globalInfo"`
	ReturnStateInfo ReturnStateInfo `xml:"returnStateInfo"`
	Data            NuoeRequestData `xml:"Data"`
}

type NuoeRequestData struct {
	DataDescription DataDescription `xml:"dataDescription"`
	Content         string          `xml:"content"`
}

type NuoeResponse struct {
	GlobalInfo      GlobalInfo      `xml:"globalInfo"`
	ReturnStateInfo ReturnStateInfo `xml:"returnStateInfo"`
	Data            InvoiceData     `xml:"Data"`
}

type NuoeErrorResponse struct {
	Code    string `json:"code,omitempty"`
	Message string `json:"message,omitempty"`
}

func (r NuoeErrorResponse) Error() string {
	return fmt.Sprintf("nuoe:%s:%s", r.Code, r.Message)
}

func (client *nuoeClient) Invoice(ctx context.Context, req *InvoiceRequest) (*InvoiceResponse, error) {
	globalInfo := genGlobalInfo("ECXML.FPKJ.BC.E_INV", req)
	tripleDesKey := req.PublicParameters.TripleDesKey

	xmlData, err := xml.Marshal(genInvoiceRequestContent(req))
	if err != nil {
		return nil, err
	}

	result, err := openssl.Des3ECBEncrypt(xmlData, []byte(tripleDesKey), openssl.PKCS5_PADDING)
	if err != nil {
		return nil, err
	}

	request := &NuoeRequest{
		GlobalInfo: globalInfo,
		Data: NuoeRequestData{
			DataDescription: genDescription(),
			Content:         base64.StdEncoding.EncodeToString(result),
		},
	}
	resp := &NuoeEmptyResponse{}
	err = client.execute(ctx, "POST", tripleDesKey, request, resp)
	if err != nil {
		return nil, err
	}
	return &InvoiceResponse{}, nil
}

func (client *nuoeClient) Download(ctx context.Context, req *DownloadRequest) (*DownloadResponse, error) {
	globalInfo := genGlobalInfo("ECXML.FPXZ.CX.E_INV", &req.InvoiceRequest)
	tripleDesKey := req.PublicParameters.TripleDesKey
	xmlData, err := xml.Marshal(genDownloadRequestContent(req))
	if err != nil {
		return nil, err
	}
	result, err := openssl.Des3ECBEncrypt(xmlData, []byte(tripleDesKey), openssl.PKCS5_PADDING)
	if err != nil {
		return nil, err
	}

	request := &NuoeRequest{
		GlobalInfo: globalInfo,
		Data: NuoeRequestData{
			DataDescription: genDescription(),
			Content:         base64.StdEncoding.EncodeToString(result),
		},
	}
	resp := &RequestFpkjxxFpjGxxNew{}
	err = client.execute(ctx, "POST", tripleDesKey, request, resp)
	if err != nil {
		return nil, err
	}
	return &DownloadResponse{
		Url:    resp.PdfUrl,
		Code:   resp.FpDm,
		Number: resp.FpHm,
	}, nil
}

func (client *nuoeClient) SendToEmail(ctx context.Context, req *SendToEmailRequest) (*SendToEmailResponse, error) {
	globalInfo := genGlobalInfo("ECXML.EMAILPHONEFPTS.TS.E.INV", &req.InvoiceRequest)
	tripleDesKey := req.PublicParameters.TripleDesKey
	xmlData, err := xml.Marshal(genSendToEmailRequestContent(req))
	if err != nil {
		return nil, err
	}

	result, err := openssl.Des3ECBEncrypt(xmlData, []byte(tripleDesKey), openssl.PKCS5_PADDING)
	if err != nil {
		return nil, err
	}

	request := &NuoeRequest{
		GlobalInfo: globalInfo,
		Data: NuoeRequestData{
			DataDescription: genDescription(),
			Content:         base64.StdEncoding.EncodeToString(result),
		},
	}
	resp := &SendToEmailResponseContent{}
	err = client.execute(ctx, "POST", tripleDesKey, request, resp)
	if err != nil {
		return nil, err
	}
	return &SendToEmailResponse{}, nil
}

func genGlobalInfo(interfaceCode string, invoiceRequest *InvoiceRequest) GlobalInfo {
	return GlobalInfo{
		TerminalCode:      "1",
		AppId:             "DZFP", // 普通发票
		Version:           "1.1",
		InterfaceCode:     interfaceCode,
		RequestCode:       invoiceRequest.Username,
		RequestTime:       time.Now().Format("2006-01-02 15:04:05"),
		ResponseCode:      "121",
		DataExchangeId:    fmt.Sprintf("%s.%s%s%s", invoiceRequest.Username, invoiceRequest.OrderInfo.Number[1:9], "0", invoiceRequest.OrderInfo.Number[9:]),
		UserName:          invoiceRequest.Username,
		PassWord:          "",
		TaxpayerId:        invoiceRequest.TaxpayerId,
		AuthorizationCode: invoiceRequest.AuthorizationCode,
	}
}

func genDescription() DataDescription {
	return DataDescription{
		ZipCode:     "0",
		EncryptCode: "1",
		CodeType:    "3DES",
	}
}

func genInvoiceRequestContent(req *InvoiceRequest) RequestFpkjxx {
	content := RequestFpkjxx{
		FpkjxxFptxx: FpkjxxFptxx{
			Class:    "FPKJXX_FPTXX",
			Fpqqlsh:  req.Invoice.SerialNo,       // 发票请求唯一流水号
			Dsptbm:   req.Username,               // 平台编码
			Nsrsbh:   req.DrawerTaxpayerId,       // 开票方识别号
			Nsrmc:    req.DrawerTaxpayerFullName, // 开票方名称
			Fjh:      "",                         // 分机号
			Nsrdzdah: "",                         // 开票方电子档案号
			SwjgDm:   "",                         // 税务机构代码
			Dkbz:     "0",                        // 代开标志，0:自开,1:代开.默认为自开
			Pydm:     "",                         // 票样代码
			Kpxm: func() string {
				names := []string{}
				for _, p := range req.Products {
					names = append(names, p.OrderProduct.Name)
				}
				return util.Substr(strings.Join(names, ","), 199)
			}(), // 主要开票项目
			BmbBbh:    "20.0",                     // 编码表版本号
			XhfNsrsbh: req.SellerTaxpayerId,       // 销货方识别号
			Xhfmc:     req.SellerTaxpayerFullName, // 销货方名称
			XhfDz:     req.SellerAddress,          // 销货方地址
			XhfDh:     req.SellerPhone,            // 销货方电话
			XhfYhzh: func() string {
				return fmt.Sprintf("%s %s", req.SellerBankName, req.SellerBankAccount)
			}(), // 销货方银行账号
			Ghfmc:     req.Invoice.Name,    // 购货方名称
			GhfNsrsbh: req.Invoice.TaxID,   // 购货方识别号
			GhfSf:     "",                  // 购货方省份
			GhfDz:     req.Invoice.Address, // 购货方地址
			GhfGddh:   req.Invoice.Phone,   // 购货方固定电话
			GhfSj:     "",                  // 购货方手机
			GhfEmail:  req.Invoice.Email,   // 购货方邮箱
			Ghfqlx: func() string {
				if req.Invoice.Type == "company" {
					return "01"
				}
				return "03"
			}(), // 购货方企业类型，01：企业 ，02：机关事业单位， 03：个人 ，04：其它
			GhfYhzh: fmt.Sprintf("%s %s", req.Invoice.BankName, req.Invoice.BankAccount), // 购货方银行、账号
			HyDm:    "",                                                                  // 行业代码
			HyMc:    "",                                                                  // 行业名称
			Kpy:     req.TaxSetting.Drawer,                                               // 开票员
			Sky:     req.TaxSetting.Payer,                                                // 收款员
			Fhr:     req.TaxSetting.Reviewer,                                             // 复核人
			Kprq:    "",                                                                  // 开票日期
			Kplx:    "1",                                                                 // 开票类型，1：正票，2：红票
			YfpDm:   "",                                                                  // 原发票代码
			YfpHm:   "",                                                                  // 原发票号码
			Czdm:    "10",                                                                // 操作代码，10：正票正常开具，20：退货折让红票
			QdBz:    "0",                                                                 // 清单标志，默认为0(商品明细大于8行，平台自动生成清单)。
			Qdxmmc:  "",                                                                  // 清单发票项目名称
			YfpzlDm: "",                                                                  // 原发票种类代码
			Ykprq:   "",                                                                  // 原开票日期
			Chyy:    "",                                                                  // 冲红原因
			Tschbz:  "",                                                                  // 特殊冲红标志
			Kphjje: func() string {
				amount := 0
				for _, p := range req.Products {
					amount += int(p.OrderProduct.PayAmount)
				}
				return toYuan(amount)
			}(), // 价税合计金额，小数点后2位，以元为单位精确到分
			Hjbhsje: "0", // 合计不含税金额，平台处理价税分离，此值传0
			Hjse:    "0", // 合计税额，平台处理价税分离，此值传0
			Bz: func() string {
				remarks := []string{}
				for _, item := range req.Products {
					if item.InvoiceTemplate == nil || item.InvoiceTemplate.Remarks == "" {
						continue
					}
					remarks = append(remarks, item.InvoiceTemplate.Remarks)
				}
				remarksStr := strings.Join(remarks, "；")
				if len([]rune(remarksStr)) > 100 {
					return string([]rune(remarksStr)[:100])
				}
				return remarksStr
			}(), // 备注
			Fplx:  "", // 发票类型
			Hztzd: "", // 红票通知单编号
			Byzd1: "", // 备用字段
			Byzd2: "", // 备用字段
			Byzd3: "", // 备用字段
			Byzd4: "", // 备用字段
			Byzd5: "", // 备用字段
		},
		FpkjxxXmxxs: getFpkjxxXmxxs(req),
		FpkjxxDdxx: FpkjxxDdxx{
			Class:  "FPKJXX_DDXX",
			Ddh:    req.OrderInfo.Number, // 订单号
			Thdh:   "",                   // 退款单号
			Dddate: "",                   // 订单时间
		},
	}
	content.Class = "REQUEST_FPKJXX"
	return content
}

func getFpkjxxXmxxs(req *InvoiceRequest) FpkjxxXmxxs {
	fpkjxxXmxxs := []FpkjxxXmxx{}
	for _, p := range req.Products {
		if p.OrderProduct.PayAmount == 0 {
			continue
		}
		xmxx := FpkjxxXmxx{
			Xmmc: p.OrderProduct.Name, // 项目名称
			Xmdw: "",                  // 项目单位
			Ggxh: func() string {
				if len(p.OrderProduct.Spec.Properties) == 0 {
					return ""
				}
				properties := strings.Join(p.OrderProduct.Spec.Properties, "|")
				r := []rune(properties)
				if len(r) > 20 {
					r = r[:20]
				}
				return string(r)
			}(), // 规格型号
			Xmsl: cast.ToString(p.OrderProduct.Total), // 项目数量 TODO: 需要删掉退款商品
			Hsbz: "1",                                 // 含税标志，表示项目单价和项目金额是否含税。0表示都不含税，1表示都含税。
			Fphxz: func() string {
				if p.OrderProduct.PayAmount < p.OrderProduct.Price*p.OrderProduct.Total {
					return "2"
				}
				return "0"
			}(), // 发票行性质，0：正常行，1：折扣行，2：被折扣行
			Xmdj: toYuan(p.OrderProduct.Price),            // 项目单价 TODO: 需要考虑 0 元商品等
			Spbm: p.InvoiceTemplate.TaxClassificationCode, // 商品编码
			Zxbm: "",                                      // 自行编码
			Yhzcbs: func(p InvoiceProduct) string {
				if "nonuse" == p.InvoiceTemplate.FavorablePolicy {
					return "0"
				}
				return "1"
			}(p), // 优惠政策标识，0：不使用，1：使用
			Lslbs: func(p InvoiceProduct) string {
				if "zeroTax" == p.InvoiceTemplate.FavorablePolicy {
					return "3"
				}
				if "freeTax" == p.InvoiceTemplate.FavorablePolicy {
					return "1"
				}
				if "noTax" == p.InvoiceTemplate.FavorablePolicy {
					return "2"
				}
				return ""
			}(p), // 零税率标识 空：非零税率， 1：免税，2：不征税，3普通零税率
			Zzstsgl: func(p InvoiceProduct) string {
				if "freeTax" == p.InvoiceTemplate.FavorablePolicy {
					return "免税"
				}
				if "noTax" == p.InvoiceTemplate.FavorablePolicy {
					return "不征税"
				}
				return ""
			}(p), // 增值税特殊管理  当YHZCBS为1时必填，LSLBS为0填写出口零税，LSLBS为1填写免税，LSLBS为2填写不征税
			Kce:  "0",                                                 // 扣除额
			Xmje: toYuan(p.OrderProduct.Price * p.OrderProduct.Total), // 项目金额，小数点后2位，以元为单位精确到分。 等于=单价*数量，根据含税标志，确定此金额是否为含税金额。
			Sl:   toYuan(p.InvoiceTemplate.TaxRate),                   // 税率
			//Se:      cast.ToString(float64(p.OrderProduct.PayAmount*uint64(p.InvoiceTemplate.TaxRate)) / 10000.0), // 税额
			Byzd1: "", // 备用字段
			Byzd2: "", // 备用字段
			Byzd3: "", // 备用字段
			Byzd4: "", // 备用字段
			Byzd5: "", // 备用字段
		}
		fpkjxxXmxxs = append(fpkjxxXmxxs, xmxx)
		if p.OrderProduct.Price*p.OrderProduct.Total > p.OrderProduct.PayAmount {
			fxmxx := FpkjxxXmxx{
				Xmmc:  p.OrderProduct.Name,                     // 项目名称
				Xmdw:  "",                                      // 项目单位
				Ggxh:  "",                                      // 规格型号
				Hsbz:  "0",                                     // 含税标志，表示项目单价和项目金额是否含税。0表示都不含税，1表示都含税。
				Fphxz: "1",                                     // 发票行性质，0：正常行，1：折扣行，2：被折扣行
				Spbm:  p.InvoiceTemplate.TaxClassificationCode, // 商品编码
				Zxbm:  "",                                      // 自行编码
				Yhzcbs: func(p InvoiceProduct) string {
					if "nonuse" == p.InvoiceTemplate.FavorablePolicy {
						return "0"
					}
					return "1"
				}(p), // 优惠政策标识，0：不使用，1：使用
				Lslbs: func(p InvoiceProduct) string {
					if "zeroTax" == p.InvoiceTemplate.FavorablePolicy {
						return "3"
					}
					if "freeTax" == p.InvoiceTemplate.FavorablePolicy {
						return "1"
					}
					if "noTax" == p.InvoiceTemplate.FavorablePolicy {
						return "2"
					}
					return ""
				}(p), // 零税率标识 空：非零税率， 1：免税，2：不征税，3普通零税率
				Zzstsgl: func(p InvoiceProduct) string {
					if "freeTax" == p.InvoiceTemplate.FavorablePolicy {
						return "免税"
					}
					if "noTax" == p.InvoiceTemplate.FavorablePolicy {
						return "不征税"
					}
					return ""
				}(p), // 增值税特殊管理  当YHZCBS为1时必填，LSLBS为0填写出口零税，LSLBS为1填写免税，LSLBS为2填写不征税
				Kce:  "0",                                                                                        // 扣除额
				Xmje: toYuan(int64(p.OrderProduct.PayAmount) - int64(p.OrderProduct.Price*p.OrderProduct.Total)), // 项目金额，小数点后2位，以元为单位精确到分。 等于=单价*数量，根据含税标志，确定此金额是否为含税金额。
				Sl:   toYuan(p.InvoiceTemplate.TaxRate),                                                          // 税率
			}
			fpkjxxXmxxs = append(fpkjxxXmxxs, fxmxx)
		}
	}
	if req.OrderInfo.DeliveryFee > 0 && req.DeliveryFeeTemplate != nil {
		xmxx := FpkjxxXmxx{
			Xmmc:  req.DeliveryFeeTemplate.Name,                  // 项目名称
			Xmdw:  "",                                            // 项目单位
			Ggxh:  "无",                                           // 规格型号
			Xmsl:  cast.ToString(1),                              // 项目数量 TODO: 需要删掉退款商品
			Hsbz:  "1",                                           // 含税标志，表示项目单价和项目金额是否含税。0表示都不含税，1表示都含税。
			Fphxz: "0",                                           // 发票行性质，0：正常行，1：折扣行，2：被折扣行
			Xmdj:  toYuan(req.OrderInfo.DeliveryFee),             // 项目单价 TODO: 需要考虑 0 元商品等
			Spbm:  req.DeliveryFeeTemplate.TaxClassificationCode, // 商品编码
			Zxbm:  "",                                            // 自行编码
			Yhzcbs: func(favorablePolicy string) string {
				if "nonuse" == favorablePolicy {
					return "0"
				}
				return "1"
			}(req.DeliveryFeeTemplate.FavorablePolicy), // 优惠政策标识，0：不使用，1：使用
			Lslbs: func(favorablePolicy string) string {
				if "zeroTax" == favorablePolicy {
					return "3"
				}
				if "freeTax" == favorablePolicy {
					return "1"
				}
				if "noTax" == favorablePolicy {
					return "2"
				}
				return ""
			}(req.DeliveryFeeTemplate.FavorablePolicy), // 零税率标识 空：非零税率， 1：免税，2：不征税，3普通零税率
			Zzstsgl: func(favorablePolicy string) string {
				if "freeTax" == favorablePolicy {
					return "免税"
				}
				if "noTax" == favorablePolicy {
					return "不征税"
				}
				return ""
			}(req.DeliveryFeeTemplate.FavorablePolicy), // 增值税特殊管理  当YHZCBS为1时必填，LSLBS为0填写出口零税，LSLBS为1填写免税，LSLBS为2填写不征税
			Kce:  "0",                                     // 扣除额
			Xmje: toYuan(req.OrderInfo.DeliveryFee),       // 项目金额，小数点后2位，以元为单位精确到分。 等于=单价*数量，根据含税标志，确定此金额是否为含税金额。
			Sl:   toYuan(req.DeliveryFeeTemplate.TaxRate), // 税率
			//Se:      cast.ToString(float64(p.OrderProduct.PayAmount*uint64(p.InvoiceTemplate.TaxRate)) / 10000.0), // 税额
			Byzd1: "", // 备用字段
			Byzd2: "", // 备用字段
			Byzd3: "", // 备用字段
			Byzd4: "", // 备用字段
			Byzd5: "", // 备用字段
		}

		fpkjxxXmxxs = append(fpkjxxXmxxs, xmxx)
	}
	return FpkjxxXmxxs{
		Class:      "FPKJXX_XMXX;",
		Size:       cast.ToString(len(fpkjxxXmxxs)),
		FpkjxxXmxx: fpkjxxXmxxs,
	}
}

func genDownloadRequestContent(req *DownloadRequest) RequestFpxxxzNew {
	content := RequestFpxxxzNew{
		Fpqqlsh: req.Invoice.SerialNo, // 发票请求唯一流水号
		Dsptbm:  req.Username,         // 平台编码
		Nsrsbh:  req.DrawerTaxpayerId, // 开票方识别号
		Ddh:     req.OrderInfo.Number, // 订单号
		PdfXzfs: req.PdfXzfs,          // PDF下载方式
	}
	content.Class = "REQUEST_FPXXXZ_NEW"
	return content
}

func genSendToEmailRequestContent(req *SendToEmailRequest) RequestEmailphonefpts {
	content := RequestEmailphonefpts{
		Class: "REQUEST_EMAILPHONEFPTS",
		Tsfsxx: Tsfsxx{
			Class: "TSFSXX",
			ComonNodes: ComonNodes{
				Class: "COMMON_NODE;",
				Size:  "2", // 注意和下方个数一致
				CommonNode: []CommonNode{
					{
						Name:  "TSFS", // TSFS
						Value: "0",    // 推送方式
					},
					{
						Name:  "EMAIL",   // EMAIL
						Value: req.Email, // 邮箱
					},
				},
			},
		},
		Fpxxs: Fpxxs{
			Class: "FPXX;",
			Size:  "1", // 注意和下方个数一致
			Fpxx: []Fpxx{
				{
					ComonNodes: ComonNodes{
						Class: "COMMON_NODE;",
						Size:  "4", // 注意和下方个数一致
						CommonNode: []CommonNode{
							{
								Name:  "FPQQLSH",            // FPQQLSH
								Value: req.Invoice.SerialNo, // 发票请求唯一流水号
							},
							{
								Name:  "NSRSBH",             // NSRSBH
								Value: req.DrawerTaxpayerId, // 开票方识别号
							},
							{
								Name:  "FP_DM",  // FP_DM
								Value: req.Code, // 发票代码
							},
							{
								Name:  "FP_HM",    // FP_HM
								Value: req.Number, // 发票号码
							},
						},
					},
				},
			},
		},
	}

	return content
}

func (client *nuoeClient) execute(ctx context.Context, method string, tripleDesKey string, request *NuoeRequest, response interface{}) error {
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	ctx = log.SwitchOnResponseBodyLog(ctx)

	request.SchemaLocation = "http://www.chinatax.gov.cn/tirip/dataspec/interfaces.xsd"
	request.Xsi = "http://www.w3.org/2001/XMLSchema-instance"
	request.Version = "WLFP1.0"

	host := nuoeProductionUrl
	if client.IsTest {
		host = nuoeUrl
	}

	headers := map[string]string{}
	headers["Connection"] = "close"

	body, _, err := extension.RequestClient.RequestXml(ctx, method, host, request, &headers, &url.Values{})
	if err != nil {
		return err
	}
	nuoeResp := &NuoeResponse{}
	if err := xml.Unmarshal(body, &nuoeResp); err != nil {
		return err
	}
	if nuoeResp.ReturnStateInfo.ReturnCode != "0000" {
		message := ""
		code := nuoeResp.ReturnStateInfo.ReturnCode
		if nuoeResp.ReturnStateInfo.ReturnMessage != "" {
			b, _ := base64.StdEncoding.DecodeString(nuoeResp.ReturnStateInfo.ReturnMessage)
			message = string(b)
		}
		return &NuoeErrorResponse{
			Code:    code,
			Message: message,
		}
	}
	// 有些请求是没有返回报文的。
	if nuoeResp.Data.Content == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(nuoeResp.Data.Content)
	if err != nil {
		return err
	}

	b, err = openssl.Des3ECBDecrypt(b, []byte(tripleDesKey), openssl.PKCS5_PADDING)
	if err != nil {
		return err
	}

	if err := xml.Unmarshal(b, &response); err != nil {
		return err
	}

	return nil
}

func toYuan(fen interface{}) string {
	return cast.ToString(cast.ToFloat64(fen) / 100.0)
}

func (client *nuoeClient) CreateEInvoiceUrl(ctx context.Context, req *DownloadRequest) error {
	return nil
}

func (client *nuoeClient) CreateRedConfirmation(ctx context.Context, req *CreateRedConfirmationRequest) (*CreateRedConfirmationResponse, error) {
	return &CreateRedConfirmationResponse{}, nil
}

func (client *nuoeClient) IssueRedInvoice(ctx context.Context, req *InvoiceRequest) error {
	return nil
}
