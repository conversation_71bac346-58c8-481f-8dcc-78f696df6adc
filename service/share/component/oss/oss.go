package oss

import (
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"

	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/core/util"
	pb_account "mairpc/proto/account"
	pb_client "mairpc/proto/client"

	conf "github.com/spf13/viper"
	"golang.org/x/net/context"
)

var (
	ossClient                    IOSSClient
	ossCDNClient                 IOSSClient
	ossClientInServiceSetting    = sync.Map{}
	ossCDNClientInServiceSetting = sync.Map{}
)

const (
	OSS_PROVIDER_ALIYUN  = "aliyun"
	OSS_PROVIDER_MINIO   = "minio"
	OSS_PROVIDER_TENCENT = "tencent"
	OSS_PROVIDER_AWS     = "aws"
	OSS_PROVIDER_AZURE   = "azure"

	HTTPGet    HTTPMethod = "GET"
	HTTPPut    HTTPMethod = "PUT"
	HTTPHead   HTTPMethod = "HEAD"
	HTTPPost   HTTPMethod = "POST"
	HTTPDelete HTTPMethod = "DELETE"

	EXPIRATION_SECONDS int64 = 604800 // 7 day

	RETENTION_POLICY_7_DAY   = "r7d"
	RETENTION_POLICY_30_DAY  = "r30d"
	RETENTION_POLICY_90_DAY  = "r90d"
	RETENTION_POLICY_180_DAY = "r180d"
	RETENTION_POLICY_365_DAY = "r365d"
)

type IOSSClient interface {
	SetBucket(ctx context.Context, name string) error
	SignUrl(ctx context.Context, key string, method HTTPMethod, contentType string, expires int64, option *SignUrlOption) (string, error)
	DoesObjectExist(ctx context.Context, key string) (bool, error)
	GetObject(ctx context.Context, key, filePath string) error
	PutObject(ctx context.Context, key, filePath string, options *PutObjectOptions) error
	GetObjectMetadata(ctx context.Context, key string, option *GetObjectOptions) (*GetMetadataResult, error)
	DeleteObject(ctx context.Context, key string) error
	RenameObject(ctx context.Context, srcKey, destKey string) error
	CopyObject(ctx context.Context, srcKey, destKey string) error
	GetPostSignature(ctx context.Context, accessKeySecret, bucket, endpoint string, option *PostPolicyOption) (*PostSignatureResult, error)
}

type HTTPMethod string

type GetMetadataResult struct {
	http.Header
}

type PostSignatureResult struct {
	Url       string
	Policy    string
	Signature string
	FormData  map[string]string
}

func GetOSSClientByProvider(provider, endpoint, accessKeyId, accessKeySecret string) IOSSClient {
	var client IOSSClient
	switch provider {
	case OSS_PROVIDER_ALIYUN:
		if !strings.HasPrefix(endpoint, "http://") && !strings.HasPrefix(endpoint, "https://") {
			endpoint = fmt.Sprintf("https://%s", endpoint)
		}
		client = initAliyunOss(endpoint, accessKeyId, accessKeySecret)
	case OSS_PROVIDER_MINIO, OSS_PROVIDER_TENCENT, OSS_PROVIDER_AWS:
		client = initMinioOss(endpoint, accessKeyId, accessKeySecret)
	case OSS_PROVIDER_AZURE:
		client = initAzureOss(endpoint, accessKeyId, accessKeySecret)
	default:
		// empty
	}
	return client
}

func OSSClient(ctx context.Context) IOSSClient {
	defaultClient := func() IOSSClient {
		if ossClient == nil {
			ossClient = GetOSSClientByProvider(util.GetOSSProvider(), conf.GetString("oss-endpoint"), util.GetOSSAccessKeyId(), util.GetOSSAccessKeySecret())
			ossClient.SetBucket(ctx, conf.GetString("oss-private-bucket"))
		}
		return ossClient
	}
	accountId := util.GetAccountId(ctx)
	if !bson.IsObjectIdHex(accountId) {
		return defaultClient()
	}
	if value, ok := ossClientInServiceSetting.Load(accountId); ok {
		return value.(IOSSClient)
	}
	defer time.AfterFunc(time.Minute, func() {
		ossClientInServiceSetting.Delete(accountId)
	})
	resp, err := pb_client.GetAccountServiceClient().GetServiceSetting(ctx, &pb_account.GetServiceSettingRequest{
		Service: "file",
	})
	if err != nil || resp.File == nil {
		client := defaultClient()
		ossClientInServiceSetting.Store(accountId, client)
		return client
	}
	client := GetOSSClientByProvider(resp.Provider, resp.File.Endpoint, resp.File.AccessKeyId, resp.File.AccessKeySecret)
	client.SetBucket(ctx, resp.File.Bucket)
	ossClientInServiceSetting.Store(accountId, client)
	return client
}

func OSSCDNClient(ctx context.Context) IOSSClient {
	defaultClient := func() IOSSClient {
		if ossCDNClient == nil {
			ossCDNClient = GetOSSClientByProvider(util.GetOSSProvider(), conf.GetString("oss-endpoint"), util.GetOSSAccessKeyId(), util.GetOSSAccessKeySecret())
			ossCDNClient.SetBucket(ctx, conf.GetString("oss-cdn-bucket"))
		}
		return ossCDNClient
	}
	accountId := util.GetAccountId(ctx)
	if !bson.IsObjectIdHex(accountId) {
		return defaultClient()
	}
	if value, ok := ossCDNClientInServiceSetting.Load(accountId); ok {
		return value.(IOSSClient)
	}
	defer time.AfterFunc(time.Minute, func() {
		ossCDNClientInServiceSetting.Delete(accountId)
	})
	resp, err := pb_client.GetAccountServiceClient().GetServiceSetting(ctx, &pb_account.GetServiceSettingRequest{
		Service: "cdn",
	})
	if err != nil || resp.Cdn == nil {
		client := defaultClient()
		ossCDNClientInServiceSetting.Store(accountId, client)
		return client
	}
	client := GetOSSClientByProvider(resp.Provider, resp.Cdn.Endpoint, resp.Cdn.AccessKeyId, resp.Cdn.AccessKeySecret)
	client.SetBucket(ctx, resp.Cdn.Bucket)
	ossCDNClientInServiceSetting.Store(accountId, client)
	return client
}

func GetObjectKey(module, fileName string, options ObjectKeyOption) (string, error) {
	retentionPolicies := []string{
		RETENTION_POLICY_7_DAY,
		RETENTION_POLICY_30_DAY,
		RETENTION_POLICY_90_DAY,
		RETENTION_POLICY_180_DAY,
		RETENTION_POLICY_365_DAY,
	}
	fileNameParts := make([]string, 0, 6)
	if options.RetentionPolicy != "" {
		if util.StrInArray(options.RetentionPolicy, &retentionPolicies) {
			fileNameParts = append(fileNameParts, options.RetentionPolicy)
		} else {
			return "", errors.NewInvalidParamsError(map[string]interface{}{
				"error": "The value of retentionPolicy is not defined.",
			})
		}
	}
	if options.AccountId != "" {
		fileNameParts = append(fileNameParts, options.AccountId)
	}
	fileNameParts = append(fileNameParts, "modules", module)
	if options.BusinessName != "" {
		fileNameParts = append(fileNameParts, options.BusinessName)
	}
	fileNameParts = append(fileNameParts, fileName)
	return strings.Join(fileNameParts, "/"), nil
}

func IsPathStyle(provider string) bool {
	switch provider {
	case OSS_PROVIDER_ALIYUN, OSS_PROVIDER_TENCENT, OSS_PROVIDER_AWS:
		return false
	default:
		return true
	}
}

func ReplaceUrlByEndpointExternal(ctx context.Context, originalUrl string, isPrivate bool) string {
	var err error
	defer func() {
		if err != nil {
			log.Warn(ctx, "Failed to replace url by external endpoint", log.Fields{"err": err.Error()})
		}
	}()
	endpointExternal := conf.GetString("oss-endpoint-external")
	if endpointExternal == "" {
		return originalUrl
	}
	targetUrlParsed, err := url.Parse(originalUrl)
	if err != nil {
		return originalUrl
	}
	endpointExternalParsed, err := url.Parse(endpointExternal)
	if err != nil {
		return originalUrl
	}
	var bucket string
	if isPrivate {
		bucket = conf.GetString("oss-private-bucket")
	} else {
		bucket = conf.GetString("oss-cdn-bucket")
	}
	targetUrlParsed.Host = endpointExternalParsed.Host
	targetUrlParsed.Path = strings.TrimPrefix(targetUrlParsed.Path, fmt.Sprintf("/%s", bucket))
	if endpointExternalParsed.Path == "/" {
		targetUrlParsed.Path = fmt.Sprintf("%s%s%s", endpointExternalParsed.Path, bucket, targetUrlParsed.Path)
	} else {
		targetUrlParsed.Path = fmt.Sprintf("%s/%s%s", endpointExternalParsed.Path, bucket, targetUrlParsed.Path)
	}
	return targetUrlParsed.String()
}
