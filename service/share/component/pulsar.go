package component

import (
	"context"
	"encoding/json"
	"errors"
	"mairpc/core/extension"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/service/share/util"
	"sync"
	"time"

	"github.com/apache/pulsar-client-go/pulsar"
)

type pulsarProducer struct {
	client    pulsar.Client
	producers sync.Map
	mutex     sync.Mutex
}

var (
	PulsarProducer = &pulsarProducer{
		producers: sync.Map{},
	}
)

func init() {
	extension.RegisterExtension(PulsarProducer)
}

func (*pulsarProducer) Name() string {
	return "pulsarProducer"
}

func (p *pulsarProducer) InitWithConf(conf map[string]interface{}, debug bool) error {
	if core_util.GetMqProvider() != MESSAGE_QUEUE_PROVIDER_PULSAR || core_util.IsTencentMq() {
		return nil
	}
	options := pulsar.ClientOptions{
		URL:               core_util.GetMqUrl(),
		Authentication:    pulsar.NewAuthenticationToken(core_util.GetMqAKSecret()),
		OperationTimeout:  30 * time.Second,
		KeepAliveInterval: 10 * time.Second,
		ConnectionTimeout: 10 * time.Second,
	}
	p.client, err = pulsar.NewClient(options)
	return err
}

func (p *pulsarProducer) Close() {
	p.producers.Range(func(_, value any) bool {
		value.(pulsar.Producer).Close()
		return true
	})
}

func (p *pulsarProducer) Produce(ctx context.Context, topic, msgTag string, data interface{}) error {
	if InFeatureTest {
		return nil
	}
	bytes, err := json.Marshal(data)
	if err != nil {
		log.Warn(ctx, "[MQTrace] Failed to marshal message body", log.Fields{"data": data, "errMsg": err.Error()})
		return errors.New("Failed to marshal message body before send to pulsar")
	}
	producer, err := p.getProducer(ctx, topic)
	if err != nil {
		log.Error(ctx, "[MQTrace] Failed to create producer", log.Fields{"topic": topic, "errMsg": err.Error()})
		return err
	}
	messageId, err := producer.Send(ctx, &pulsar.ProducerMessage{
		Payload: bytes,
		Properties: map[string]string{
			"_TAG_FILTER_TAG": msgTag,
			"REQUEST_ID":      core_util.ExtractRequestIDFromCtx(ctx),
			"ACCOUNT_ID":      core_util.GetAccountId(ctx),
		},
	})
	if err != nil {
		log.Error(ctx, "[MQTrace] Failed to produce message", log.Fields{
			"topic":       util.FormatMqTopicForPulsar(ctx, topic),
			"tag":         msgTag,
			"messageBody": data,
			"errMsg":      err.Error(),
		})
		return err
	}
	log.Warn(ctx, "[MQTrace] Produced message", log.Fields{
		"topic":       util.FormatMqTopicForPulsar(ctx, topic),
		"id":          messageId.String(),
		"tag":         msgTag,
		"messageBody": data,
	})
	return nil
}

func (p *pulsarProducer) getProducer(ctx context.Context, topic string) (pulsar.Producer, error) {
	// Pulsar Broker 不允许同名（host 名）producer 连同一个 topic，所以用 double-checked locking 保护
	if producer, ok := p.producers.Load(topic); ok {
		return producer.(pulsar.Producer), nil
	}
	p.mutex.Lock()
	defer p.mutex.Unlock()
	if producer, ok := p.producers.Load(topic); ok {
		return producer.(pulsar.Producer), nil
	}

	producer, err := p.client.CreateProducer(pulsar.ProducerOptions{
		Topic:       util.FormatMqTopicForPulsar(ctx, topic),
		Name:        core_util.GetK8sHostFullName(),
		SendTimeout: 60 * time.Second,
		// Tag filter 不支持 batching
		DisableBatching: true,
	})
	if err != nil {
		return nil, err
	}
	p.producers.Store(topic, producer)
	return producer, nil
}
