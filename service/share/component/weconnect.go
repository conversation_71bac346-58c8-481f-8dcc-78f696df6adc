package component

import (
	"golang.org/x/net/context"
)

const (
	// origin in models/channel
	CHANNEL_ORIGIN_WECHAT     = "wechat"
	CHANNEL_ORIGIN_WEAPP      = "weapp"
	CHANNEL_ORIGIN_WECP       = "WECHAT_CP"
	CHANNEL_ORIGIN_BYTE_DANCE = "bytedanceapp"
	CHANNEL_ORIGIN_ALIPAY_APP = "alipayapp"
	// origin used in trade uri
	// see: http://mairpc/we-connect/blob/develop/docs/trade_api.md#initupdate-channel-payment-configuration
	CHANNEL_TRADE_ORIGIN_WECHAT     = "weixin"
	CHANNEL_TRADE_ORIGIN_WEAPP      = "wechat_app"
	CHANNEL_TRADE_ORIGIN_WECP       = "wechat_cp"
	CHANNEL_TRADE_ORIGIN_BYTE_DANCE = "byte_dance_app"
	CHANNEL_TRADE_ORIGIN_ALIPAY_APP = "alipay_app"

	TEMPLATE_MESSAGE_STATUS_FINISHED = "FINISHED"
	TEMPLATE_MESSAGE_STATUS_FAILED   = "FAILED"
	TEMPLATE_MESSAGE_STATUS_ERROR    = "ERROR"

	// 回调配置执行动作
	CALLBACK_CONFIG_ACTION_ADD    = "ADD"
	CALLBACK_CONFIG_ACTION_REMOVE = "REMOVE"

	CONTACT_WAY_TYPE_SINGLE = "SINGLE"
	CONTACT_WAY_TYPE_MULTI  = "MULTI"

	SEND_TRANSFER_SUCCESS           = "SUCCESS"
	SEND_TRANSFER_FAIL              = "FAIL"
	SEND_TRANSFER_MSG_TYPE_NORMAL   = "NORMAL_MSG"   // 普通付款消息
	SEND_TRANSFER_MSG_TYPE_APPROVAL = "APPROVAL_MSG" // 审批付款消息

	JSSDK_TICKET_TYPE_WX_CARD = "wx_card"
	JSSDK_TICKET_TYPE_JSAPI   = "jsapi"

	LICENSE_RENEW_TASK_STATUS_FAILED             = "FAILED"
	LICENSE_RENEW_TASK_STATUS_SUCCESSFUL         = "SUCCESSFUL"
	LICENSE_RENEW_ORDER_STATUS_PAY_SUCCESS       = "PAY_SUCCESS"
	LICENSE_RENEW_ORDER_STATUS_WAIT_PAY          = "WAIT_PAY"
	LICENSE_RENEW_ORDER_STATUS_WAIT_PAY_CLOSED   = "WAIT_PAY_CLOSED"
	LICENSE_RENEW_ORDER_STATUS_WAIT_PAY_TIME_OUT = "WAIT_PAY_TIME_OUT"
	LICENSE_RENEW_ORDER_STATUS_INVALID           = "INVALID"
	LICENSE_RENEW_ORDER_STATUS_REFUNDING         = "REFUNDING"
	LICENSE_RENEW_ORDER_STATUS_REFUNDED          = "REFUNDED"
	LICENSE_RENEW_ORDER_STATUS_REFUND_DENY       = "REFUND_DENY"
)

// WeConnector is a container for all operations about weconnect. Every function will have a tracing version, and it will be
// traced during the request lifetime. For those non-tracing version functions, use TODO context to aviod being traced.
type WeConnector interface {
	GetChannels(ctx context.Context, accountIds []string, orderBy, ordering string) ([]Channel, error)
	GetChannel(ctx context.Context, accountId string) (*Channel, error)
	GetCorpChainSharedWeAppChannel(ctx context.Context, corpId string) (*Channel, error)
	GetFollowersWithUnion(ctx context.Context, unionId string, pFilter *map[string]string) ([]Follower, uint64, error)
	GetQrcodes(ctx context.Context, channelId string, pFilter *map[string]string, page uint32, pageSize uint32) ([]Qrcode, uint64, error)
	GetQrcode(ctx context.Context, channelId string, qrcodeId string) (*Qrcode, error)
	CreateQrcode(ctx context.Context, channelId string, data *NewQrcode) (*Qrcode, error)
	UpdateQrcode(ctx context.Context, channelId string, qrcodeId string, qrcodeContent map[string]interface{}) (*Qrcode, error)
	GetFollower(ctx context.Context, channelId string, userId string) (*Follower, error)
	GetMenus(ctx context.Context, channelId string) (*Menus, error)
	SendTplMessage(ctx context.Context, channelId string, data *TplMessage) error
	SendWechatMessage(ctx context.Context, channelId string, userId string, data *Message) error
	SendMassMessage(ctx context.Context, channelId string, data *MassMessage) error
	ProduceMqMessage(ctx context.Context, messageBody *MessageBody) (map[string]interface{}, error)
	ProduceCustomerEvent(ctx context.Context, body *CustomerEventBody) (map[string]interface{}, error)
	Benchmark(ctx context.Context, body *BenchmarkRequest) error
	DecryptMiniProgram(ctx context.Context, channelId string, data *DecryptRequest) (map[string]string, error)
	GetMiniProgramPhoneByCode(ctx context.Context, data *ProxyRequest) (*GetMiniProgramPhoneByCodeResponse, error)
	// https://developers.weixin.qq.com/doc/channels/API/vip/grade/add.html
	AddMemberLevelSetting(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error)
	// https://developers.weixin.qq.com/doc/channels/API/vip/grade/get.html
	GetMemberLevelSetting(ctx context.Context, data *ProxyRequest) (*GetMemberLevelSettingResponse, error)
	// https://developers.weixin.qq.com/doc/channels/API/vip/member/get_list.html
	ListMembers(ctx context.Context, data *ProxyRequest) (*ListMembersResponse, error)
	// https://developers.weixin.qq.com/doc/channels/API/vip/grade/update_user.html
	UpdateMemberLevel(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error)
	// https://developers.weixin.qq.com/doc/channels/API/vip/member/get.html
	GetMemberInfo(ctx context.Context, data *ProxyRequest) (*GetMemberInfoResponse, error)
	// https://developers.weixin.qq.com/doc/channels/API/vip/score/get_user_score.html
	GetMemberScore(ctx context.Context, data *ProxyRequest) (*GetMemberScoreResponse, error)
	// https://developers.weixin.qq.com/doc/channels/API/vip/score/get_user_flow_record.html#source
	GetMemberScoreHistory(ctx context.Context, data *ProxyRequest) (*GetMemberScoreHistoryResponse, error)
	// https://developers.weixin.qq.com/doc/channels/API/vip/score/incr_user_score.html
	// https://developers.weixin.qq.com/doc/channels/API/vip/score/decr_user_score.html
	UpdateMemberScore(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error)
	// https://developers.weixin.qq.com/doc/channels/API/order/list_get.html
	ListOrders(ctx context.Context, data *ProxyRequest) (*ListWeshopOrdersResponse, error)
	// https://developers.weixin.qq.com/doc/channels/API/order/get.html
	GetOrderDetails(ctx context.Context, data *ProxyRequest) (*GetWeshopOrderDetailsResponse, error)
	// https://developers.weixin.qq.com/doc/store/shop/API/aftersale/getaftersaleorder.html
	GetAfterSaleOrder(ctx context.Context, data *ProxyRequest) (*GetAfterSaleOrderResponse, error)
	OrderRefund(ctx context.Context, origin string, data *OrderRefundRequest) (*OrderRefundInfo, error)
	OrderRefundStatus(ctx context.Context, origin, outRefundNo string) (*OrderRefundInfo, error)
	SetWeshopRelatedWxa(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error)
	GetWeshopRelatedWxa(ctx context.Context, data *ProxyRequest) (*GetWeshopRelatedWxaResponse, error)
	DeleteWeshopRelatedWxa(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error)
	GetWxaRelatedWeshops(ctx context.Context, data *ProxyRequest) (*GetWxaRelatedWeshopsResponse, error)
	CreateWeshopMember(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error)
	UpdateWeshopMember(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error)
	DeleteWeshopMember(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error)
	GetWeshopMember(ctx context.Context, data *ProxyRequest) (*GetWeshopMemberResponse, error)
	GetWeshopMembers(ctx context.Context, data *ProxyRequest) (*GetWeshopMembersResponse, error)
	ReportWeappFormId(ctx context.Context, channelId string, data *ReportWeappFormIdRequest) (map[string]interface{}, error)
	SendTplMessageToSingle(ctx context.Context, channelId string, data *SingleTargetTplMessage) (*SingleTargetTplMessage, error)
	SendSubscribeMessage(ctx context.Context, channelId string, data *SubscribeMsg) (*SubscribeMsgResult, error)
	GetSingleTemplateMessage(ctx context.Context, channelId, messageId string) (*SingleTargetTplMessage, error)
	ListTemplates(ctx context.Context, channelId string) ([]BriefTemplate, error)
	CreateProfitSharingReceiver(ctx context.Context, channelId string, data *CreateProfitSharingReceiverRequest) (map[string]interface{}, error)
	DeleteProfitSharingReceiver(ctx context.Context, channelId string, data *DeleteProfitSharingReceiverRequest) (map[string]interface{}, error)
	MultiProfitSharing(ctx context.Context, channelId string, data *MultiProfitSharingRequest) (map[string]interface{}, error)
	GetProfitSharing(ctx context.Context, channelId, transactionId string) (OrderMultiProfitSharingResponse, error)
	GetRefreshedProfitSharing(ctx context.Context, channelId, outOrderNo string) (OrderMultiProfitSharingResponse, error)
	FinishProfitSharing(ctx context.Context, channelId string, data *FinishProfitSharingRequest) (map[string]interface{}, error)
	GetWeappVersion(ctx context.Context, accountId string) (*GetWeappVersionResponse, error)
	GetWeappChangeLogs(ctx context.Context, accountId string) (*GetWeappChangeLogsResponse, error)
	GetWeappQrCode(ctx context.Context, accountId string, data *GetQrCodeRequest) ([]byte, error)
	GetDeliveries(ctx context.Context, accountId string) (*GetDeliveriesResponse, error)
	AddExpressOrder(ctx context.Context, accountId string, data *AddExpressOrderRequest) (*ExpressOrderResponse, error)
	GetExpressOrderPath(ctx context.Context, accountId string, data *GetExpressOrderPathRequest) (*GetExpressOrderPathResponse, error)
	GetAllExpressAccounts(ctx context.Context, accountId string) (*GetAllExpressAccountsResponse, error)
	SendTransfer(ctx context.Context, origin string, data *SendTransferRequest) (*TransferDetailResponse, error)
	SendV2Transfer(ctx context.Context, origin string, data *SendV2TransferRequest) (*V2TransferDetailResponse, error)
	GetTransfer(ctx context.Context, origin, tradeNo string) (*TransferDetailResponse, error)
	GetV2Transfer(ctx context.Context, origin, tradeBatchNo, tradeNo string) (*V2TransferDetailResponse, error)
	ListDepartments(ctx context.Context, channelId string) ([]Department, error)
	ListDepartmentsById(ctx context.Context, channelId, departmentId string) ([]Department, error)
	ListDepartmentUsers(ctx context.Context, channelId, departmentId, fetchChild string) ([]User, error)
	GetUser(ctx context.Context, channelId, userId string) (*User, error)
	GetContactWay(ctx context.Context, channelId, contactWayId string) (*ContactWay, error)
	GetContactWayByState(ctx context.Context, channelId, state string) (*ContactWay, error)
	SearchContactWay(ctx context.Context, channelId string, req SearchContactWayRequest) (*SearchContactWayResponse, error)
	ListContactWays(ctx context.Context, channelId string, req SearchContactWayRequest) (*SearchContactWayResponse, error)
	CreateContactWay(ctx context.Context, channelId string, contactWay ContactWayRequest) (*ContactWay, error)
	UpdateContactWay(ctx context.Context, channelId, contactWayId string, contactWay ContactWayRequest) (*ContactWay, error)
	DeleteContactWay(ctx context.Context, channelId, contactWayId string) error
	ListContactWay(ctx context.Context, channelId string, groupIds []string) ([]ContactWay, error)
	ListWelcomeMessages(ctx context.Context, channelId string, req *ListContactWelcomeMsgsRequest) (*ListContactWelcomeMsgsResponse, error)
	DeleteWelcomeMessage(ctx context.Context, channelId, userId string) error
	CreateGroupWelcomeTemplate(ctx context.Context, channelId string, req *GroupWelcomeTemplate) (*GroupWelcomeTemplate, error)
	UpdateGroupWelcomeTemplate(ctx context.Context, channelId string, req *GroupWelcomeTemplate) (*WeconnectResponse, error)
	DeleteGroupWelcomeTemplate(ctx context.Context, channelId, templateId string) error
	ListChats(ctx context.Context, channelId, cursor string, status int, userIds []string, partyIds []int) (string, []GroupchatBrief, error)
	ListChatMembers(ctx context.Context, channelId, chatId string) (*Groupchat, error)
	ListStatsChats(ctx context.Context, channelId string, req ListStatsChatsRequest) (ListStatsChatsResponse, error)
	CreateGroupchatJoinWays(ctx context.Context, channelId string, req GroupchatJoinWaysRequest) (*GroupchatJoinWays, error)
	UpdateGroupchatJoinWays(ctx context.Context, channelId, config string, req GroupchatJoinWaysRequest) (*GroupchatJoinWays, error)
	GetGroupchatJoinWay(ctx context.Context, channelId, config string) (*GroupchatJoinWays, error)
	GetCorp(ctx context.Context, corpId string) (*CorpDetail, error)
	ListTags(ctx context.Context, corpId, channelId, appId string, tagIds []string) ([]*GroupTagsResponse, error)
	AddTags(ctx context.Context, corpId, channelId string, data AddTagsRequest) (*GroupTagsResponse, error)
	EditTag(ctx context.Context, corpId, channelId string, data EditTagRequest) error
	DeleteTags(ctx context.Context, corpId, channelId, appId string, data DeleteTagsRequest) error
	EditMemberTags(ctx context.Context, corpId, channelId, appId string, data EditMemberTagsRequest) error
	AddWechatTagToUsers(ctx context.Context, channelId string, wxTagId int, openIds []string) (map[string][]string, int, error)
	RemoveWechatTagFromUsers(ctx context.Context, channelId string, wxTagId int, openIds []string) (interface{}, int, error)
	GetExternalUser(ctx context.Context, channelId, externalUserId string) (GetExternalUserResponse, error)
	// 此接口是从 weconnect 记录在 db 中的数据取值，会处理一些微信接口不会返回的字段，比如外部联系人是否删除。
	GetSystemExternalUser(ctx context.Context, channelId, externalUserId string) (GetSystemExternalUserResponse, error)
	ListSystemExternalUser(ctx context.Context, channelId string, externalUserIds []string) (*ListSystemExternalUserResponse, error)
	ListFollowExternalUsers(ctx context.Context, channelId, userId string) (ListFollowExternalUserResponse, error)
	ListExternalUser(ctx context.Context, channelId, userId, cursor, limit string) (ListExternalUserResponse, error)
	// userIds 为空则会全量同步外部联系人
	SyncExternalUsers(ctx context.Context, channelId string, userIds []string, force, forceOld bool) error
	ConvertToOpenId(ctx context.Context, channelId, userId string) (string, error)
	// 响应中 AllowEmployees.Users 只有 userId，其他字段均为空，需要注意不要直接使用其他字段
	GetAppDetail(ctx context.Context, channelId string) (AppDetail, error)
	SendAliyunqaSms(ctx context.Context, req SendAliyunqaSmsRequest, isDigital bool, oem AliyunqaOem) (*SendAliyunqaSmsResponse, error)
	GetAliyunqaSms(ctx context.Context, id string, oem AliyunqaOem) (*AliyunqaSmsDetailResponse, error)
	SendWelcomeMessage(ctx context.Context, channelId string, req SendWelcomeMessageRequest) error
	DownloadMediaMeterial(ctx context.Context, channelId, mediaType, mediaId string, isTemporary bool, savePath string) error
	CreateTemporaryMediaMaterial(ctx context.Context, channelId, mediaType, mediaUrl string) (*CreateTemporaryMediaMaterialResp, error)
	CreateGroupMessages(ctx context.Context, channelId string, req CreateGroupMessageRequest) (*CreateGroupMessageResponse, error)
	GetPaymonetCredentialConfiguration(ctx context.Context, channel string, quncrmAccountId string) (*PaymentCredentialConfigurationResponse, error)
	ListWechatcpAdmins(ctx context.Context, channelId string) (*ListWechatcpAdminsResponse, error)
	ListWechatcpAuthUsers(ctx context.Context, channelId string) (*ListWechatcpAuthUsersResponse, error)
	UpdateWxCard(ctx context.Context, channelId string, req UpdateWxCardRequest) error
	WeconnectProxy(ctx context.Context, channelId, method, path string, params, body, result interface{}) error
	WeconnectProxyV2(ctx context.Context, origin, channelId, method, path string, body, result interface{}) error
	// https://gitlab.maiscrm.com/mai/weconnect/-/blob/develop/docs/wechatcp_api.md#添加企业群发消息任务
	SendExternalContactMassMessage(ctx context.Context, channelId string, data *ExternalContactMassMessage) (*ExternalContactMassMessageResponse, error)
	// https://gitlab.maiscrm.com/mai/weconnect/-/blob/develop/docs/wechatcp_api.md#获取企业群发成员执行结果
	GetExternalContactMassMessageResult(ctx context.Context, channelId string, req *GetExternalContactMassMessage) (*ExternalContactMassMessageResult, error)
	// https://gitlab.maiscrm.com/mai/weconnect/-/blob/develop/docs/wechatcp_api.md#获取群发成员发送任务列表
	GetMassMessageResultTaskList(ctx context.Context, channelId string, req *GetExternalContactMassMessage) (*MassMessageTaskList, error)
	// https://gitlab.maiscrm.com/mai/weconnect/-/blob/develop/docs/wechatcp_api.md#添加企业群发消息异步任务
	CreateMassMessageTask(ctx context.Context, channelId string, data *ExternalContactMassMessage) (string, error)
	// https://gitlab.maiscrm.com/mai/weconnect/-/blob/develop/docs/wechatcp_api.md#获取企业群发消息异步任务执行结果
	GetMassMessageTaskResult(ctx context.Context, channelId, taskId string) (*MassMessageTaskResult, error)
	CancelMassMessageTask(ctx context.Context, channelId, taskId string) error
	DeleteFollower(ctx context.Context, channelId, openId string) error
	GetActiveCodeCount(ctx context.Context, channelId string) (uint64, error)
	CreateRenewUserOrder(ctx context.Context, channelId string, req *CreateRenewUserOrderRequest) (string, error)
	GetRenewUserResult(ctx context.Context, channelId, orderId string) (*GetRenewUserResultResponse, error)
	CreateActiveUserTask(ctx context.Context, channelId string, userIds []string) (string, error)
	GetActiveUserTaskResult(ctx context.Context, channelId, taskId string) (*ActiveUserTaskResult, error)
	TransferUsers(ctx context.Context, channelId string, requests []TransferUsersRequest) ([]TransferUsersResponse, error)
	GetActiveUserDetail(ctx context.Context, channelId, userId string) (*ActiveUser, error)
	ShareActiveCode(ctx context.Context, channelId string, req ShareActiveCodeRequest) (string, error)
	GetShareActiveCodeResult(ctx context.Context, channelId, taskId string) (ShareActiveCodeResult, error)
	// https://gitlab.maiscrm.com/mai/weconnect/-/blob/story-1027/docs/wechatcp_api.md#根据客户群-externaluserids-和-chatid-转换-unionid
	ConvertChatExternalUserIdToUnionId(ctx context.Context, channelId string, req *ConvertChatExternalUserIdToUnionIdRequest) ([]ConvertChatExternalUserIdToUnionIdResult, error)
	// https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter9_2_1.shtml
	CreateWechatCoupon(ctx context.Context, data *CreateWechatCouponRequest) (*CreateWechatCouponResponse, error)
	// https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter9_2_3.shtml
	RedeemedWechatCoupon(ctx context.Context, data *RedeemedWechatCouponRequest) (*RedeemedWechatCouponResponse, error)
	// https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter9_2_12.shtml
	UpdateWechatCoupon(ctx context.Context, data *UpdateWechatCouponRequest) (*UpdateWechatCouponResponse, error)
	// https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter9_2_11.shtml
	UpdateWechatCouponStock(ctx context.Context, data *UpdateWechatCouponStockRequest) (*UpdateWechatCouponStockResponse, error)
	GetWechatCouponSign(ctx context.Context, data map[string]interface{}) (*GetWechatCouponSignResponse, error)
	// https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter9_2_13.shtml
	ReturnWechatCoupon(ctx context.Context, data *ReturnWechatCouponRequest) (*ReturnWechatCouponResponse, error)
	// https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter9_2_2.shtml
	GetWechatCoupon(ctx context.Context, data *GetWechatCouponRequest) (*GetWechatCouponResponse, error)
	// https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter9_0_1.shtml
	UploadWechatMarketingImage(ctx context.Context, data *UploadWechatMarketingImageRequest) (*UploadWechatMarketingImageResponse, error)
	// 纠正外部联系人数据
	CorrectExternalUser(ctx context.Context, req *CorrectExternalUserRequest) error
	ConvertOpenIdAndUnionIdToExternalUserId(ctx context.Context, openId, unionId, channelId string) error
	// 创建小程序二维码
	DownloadMiniProgramQrcode(ctx context.Context, channelId, savePath string, req *DownloadMiniProgramQrcodeRequest) error
	// 创建获客链接:https://developer.work.weixin.qq.com/document/path/97297#%E5%88%9B%E5%BB%BA%E8%8E%B7%E5%AE%A2%E9%93%BE%E6%8E%A5
	CreateCustomerAcquisitionLink(ctx context.Context, data *ProxyRequest) (*CreateCustomerAcquisitionLinkResponse, error)
	// 获取获客链接:https://developer.work.weixin.qq.com/document/path/97398#%E8%8E%B7%E5%8F%96%E8%8E%B7%E5%AE%A2%E9%93%BE%E6%8E%A5%E8%AF%A6%E6%83%85
	GetCustomerAcquisitionLink(ctx context.Context, data *ProxyRequest) (*GetCustomerAcquisitionLinkResponse, error)
	// 编辑获客链接:https://developer.work.weixin.qq.com/document/path/97297#%E7%BC%96%E8%BE%91%E8%8E%B7%E5%AE%A2%E9%93%BE%E6%8E%A5
	UpdateCustomerAcquisitionLink(ctx context.Context, data *ProxyRequest) (*UpdateCustomerAcquisitionLinkResponse, error)
	// 删除获客链接:https://developer.work.weixin.qq.com/document/path/97297#%E5%88%A0%E9%99%A4%E8%8E%B7%E5%AE%A2%E9%93%BE%E6%8E%A5
	DeleteCustomerAcquisitionLink(ctx context.Context, data *ProxyRequest) (*DeleteCustomerAcquisitionLinkResponse, error)
	// 查询剩余使用量:https://developer.work.weixin.qq.com/document/path/97375
	GetCustomerAcquisitionQuota(ctx context.Context, data *ProxyRequest) (*GetCustomerAcquisitionQuotaResponse, error)
	// 获取企业授权信息
	GetSuiteAppInfo(ctx context.Context, corpId, suiteId string) (*GetSuiteAppInfoResponse, error)
	// 查询微信代金券批次列表:https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter9_1_4.shtml
	ListMerchantCashCoupons(ctx context.Context, data *ProxyRequest) (*ListMerchantCashCouponsResponse, error)
	// 查询某个 channel 的支付配置
	GetPaymentConfigurationByChannel(ctx context.Context, origin, channelId string) (*PaymentCredentialConfigurationResponse, error)
	// 获取联系客户统计数据：https://kb.maiscrm.com/api/weconnect/wechatcCpAPI.html#%E8%8E%B7%E5%8F%96%E8%81%94%E7%B3%BB%E5%AE%A2%E6%88%B7%E7%BB%9F%E8%AE%A1%E6%95%B0%E6%8D%AE
	ListStatsStaffContact(ctx context.Context, channelId string, req ListStatsStaffContactRequest) (ListStatsStaffContactResponse, error)
	// 添加/移除回调配置：https://kb.maiscrm.com/api/weconnect/webhookAPI.html#%E6%B7%BB%E5%8A%A0-%E7%A7%BB%E9%99%A4%E5%9B%9E%E8%B0%83%E9%85%8D%E7%BD%AE
	AddOrRemoveCallback(ctx context.Context, req AddOrRemoveCallbackRequest) error
	// 搜索会话存档
	ListChatMessageHistories(ctx context.Context, corpId string, req ListChatMessageHistoriesRequest) (*ListChatMessageHistoriesResponse, error)
	// 搜索会话存档预览
	ListChatMessageHistoryPreviews(ctx context.Context, corpId string, req ListChatMessageHistoryPreviewsRequest) (*ListChatMessageHistoryPreviewsResponse, error)
	// 手动同步会话存档
	ManualSyncChatMessageHistories(ctx context.Context, corpId string) error
	// 获取会话存档同步信息
	GetChatMessageHistoriesSyncInfo(ctx context.Context, corpId string) (*ChatMessageHistoriesSyncInfo, error)
	// 重新进行会话存档语音识别
	RedoChatMessageHistoryRecognizeSpeech(ctx context.Context, corpId, messageId string) (*ChatMessageHistory, error)
	// 获取模板消息发送结果
	ListTemplateMessageResults(ctx context.Context, channelId string, resultIds []string) ([]TemplateMessageResult, error)
	// 获取企业会话存档状态
	GetMessageAuditStatus(ctx context.Context, corpId string) (*MessageAuditStatus, error)
	// 获取企业会话存档开启用户列表
	GetMessageAuditPermitUsers(ctx context.Context, corpId string) ([]string, error)
	// 微信小商店登录授权
	LoginAuthWeshop(ctx context.Context, providerId, code string) (*LoginAuthResponse, error)
	// 获取微信商店已购买服务信息
	GetBoughtServiceInfo(ctx context.Context, providerId, appId string, serviceId uint64) (*ServiceInfoResponse, error)
	// 同步微信渠道
	SyncChannel(ctx context.Context, providerId, appId string) (*Channel, error)
}

// WeConnect is a global singleton WeConnector instance.
var WeConnect WeConnector = &maiWeConnect{}

type maiWeConnect struct {
}

func (wc *maiWeConnect) GetChannels(ctx context.Context, accountIds []string, orderBy, ordering string) ([]Channel, error) {
	return GetChannels(ctx, accountIds, orderBy, ordering)
}

func (wc *maiWeConnect) GetChannel(ctx context.Context, accountId string) (*Channel, error) {
	return GetChannel(ctx, accountId)
}
func (wc *maiWeConnect) GetCorpChainSharedWeAppChannel(ctx context.Context, corpId string) (*Channel, error) {
	return GetCorpChainSharedWeAppChannel(ctx, corpId)
}
func (wc *maiWeConnect) GetFollowersWithUnion(ctx context.Context, unionId string, pFilter *map[string]string) ([]Follower, uint64, error) {
	return GetFollowersWithUnion(ctx, unionId, pFilter)
}
func (wc *maiWeConnect) GetFollower(ctx context.Context, channelId string, userId string) (*Follower, error) {
	return GetFollower(ctx, channelId, userId)
}
func (wc *maiWeConnect) GetQrcodes(ctx context.Context, channelId string, pFilter *map[string]string, page uint32, pageSize uint32) ([]Qrcode, uint64, error) {
	return GetQrcodes(ctx, channelId, pFilter, page, pageSize)
}
func (wc *maiWeConnect) GetQrcode(ctx context.Context, channelId string, qrcodeId string) (*Qrcode, error) {
	return GetQrcode(ctx, channelId, qrcodeId)
}
func (wc *maiWeConnect) CreateQrcode(ctx context.Context, channelId string, data *NewQrcode) (*Qrcode, error) {
	return CreateQrcode(ctx, channelId, data)
}
func (wc *maiWeConnect) UpdateQrcode(ctx context.Context, channelId string, qrcodeId string, qrcodeContent map[string]interface{}) (*Qrcode, error) {
	return UpdateQrcode(ctx, channelId, qrcodeId, qrcodeContent)
}
func (wc *maiWeConnect) GetMenus(ctx context.Context, channelId string) (*Menus, error) {
	return GetMenus(ctx, channelId)
}
func (wc *maiWeConnect) SendTplMessage(ctx context.Context, channelId string, data *TplMessage) error {
	return SendTplMessage(ctx, channelId, data)
}
func (wc *maiWeConnect) SendTplMessageToSingle(ctx context.Context, channelId string, data *SingleTargetTplMessage) (*SingleTargetTplMessage, error) {
	return SendTplMessageToSingle(ctx, channelId, data)
}
func (wc *maiWeConnect) SendSubscribeMessage(ctx context.Context, channelId string, data *SubscribeMsg) (*SubscribeMsgResult, error) {
	return SendSubscribeMessage(ctx, channelId, data)
}
func (wc *maiWeConnect) SendWechatMessage(ctx context.Context, channelId string, userId string, data *Message) error {
	return SendWechatMessage(ctx, channelId, userId, data)
}
func (wc *maiWeConnect) SendMassMessage(ctx context.Context, channelId string, data *MassMessage) error {
	return SendMassMessage(ctx, channelId, data)
}
func (wc *maiWeConnect) ProduceMqMessage(ctx context.Context, messageBody *MessageBody) (map[string]interface{}, error) {
	return ProduceMqMessage(ctx, messageBody)
}
func (wc *maiWeConnect) ProduceCustomerEvent(ctx context.Context, reqBody *CustomerEventBody) (map[string]interface{}, error) {
	return ProduceCustomerEvent(ctx, reqBody)
}
func (wc *maiWeConnect) Benchmark(ctx context.Context, reqBody *BenchmarkRequest) error {
	return Benchmark(ctx, reqBody)
}
func (wc *maiWeConnect) DecryptMiniProgram(ctx context.Context, channelId string, data *DecryptRequest) (map[string]string, error) {
	return DecryptMiniProgram(ctx, channelId, data)
}
func (wc *maiWeConnect) GetMiniProgramPhoneByCode(ctx context.Context, data *ProxyRequest) (*GetMiniProgramPhoneByCodeResponse, error) {
	return GetMiniProgramPhoneByCode(ctx, data)
}

func (wc *maiWeConnect) AddMemberLevelSetting(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	return AddMemberLevelSetting(ctx, data)
}

func (wc *maiWeConnect) GetMemberLevelSetting(ctx context.Context, data *ProxyRequest) (*GetMemberLevelSettingResponse, error) {
	return GetMemberLevelSetting(ctx, data)
}

func (wc *maiWeConnect) ListMembers(ctx context.Context, data *ProxyRequest) (*ListMembersResponse, error) {
	return ListMembers(ctx, data)
}

func (wc *maiWeConnect) UpdateMemberLevel(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	return UpdateMemberLevel(ctx, data)
}

func (wc *maiWeConnect) GetMemberInfo(ctx context.Context, data *ProxyRequest) (*GetMemberInfoResponse, error) {
	return GetMemberInfo(ctx, data)
}

func (wc *maiWeConnect) GetMemberScore(ctx context.Context, data *ProxyRequest) (*GetMemberScoreResponse, error) {
	return GetMemberScore(ctx, data)
}

func (wc *maiWeConnect) GetMemberScoreHistory(ctx context.Context, data *ProxyRequest) (*GetMemberScoreHistoryResponse, error) {
	return GetMemberScoreHistory(ctx, data)
}

func (wc *maiWeConnect) UpdateMemberScore(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	return UpdateMemberScore(ctx, data)
}

func (wc *maiWeConnect) SetWeshopRelatedWxa(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	return SetWeshopRelatedWxa(ctx, data)
}

func (wc *maiWeConnect) GetWeshopRelatedWxa(ctx context.Context, data *ProxyRequest) (*GetWeshopRelatedWxaResponse, error) {
	return GetWeshopRelatedWxa(ctx, data)
}

func (wc *maiWeConnect) GetWxaRelatedWeshops(ctx context.Context, data *ProxyRequest) (*GetWxaRelatedWeshopsResponse, error) {
	return GetWxaRelatedWeshops(ctx, data)
}

func (wc *maiWeConnect) DeleteWeshopRelatedWxa(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	return DeleteWeshopRelatedWxa(ctx, data)
}

func (wc *maiWeConnect) CreateWeshopMember(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	return CreateWeshopMember(ctx, data)
}

func (wc *maiWeConnect) UpdateWeshopMember(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	return UpdateWeshopMember(ctx, data)
}

func (wc *maiWeConnect) GetWeshopMember(ctx context.Context, data *ProxyRequest) (*GetWeshopMemberResponse, error) {
	return GetWeshopMember(ctx, data)
}

func (wc *maiWeConnect) GetWeshopMembers(ctx context.Context, data *ProxyRequest) (*GetWeshopMembersResponse, error) {
	return GetWeshopMembers(ctx, data)
}

func (wc *maiWeConnect) DeleteWeshopMember(ctx context.Context, data *ProxyRequest) (*WxCardCommonResponse, error) {
	return DeleteWeshopMember(ctx, data)
}

func (wc *maiWeConnect) ListOrders(ctx context.Context, data *ProxyRequest) (*ListWeshopOrdersResponse, error) {
	return ListOrders(ctx, data)
}

func (wc *maiWeConnect) GetOrderDetails(ctx context.Context, data *ProxyRequest) (*GetWeshopOrderDetailsResponse, error) {
	return GetOrderDetails(ctx, data)
}

func (wc *maiWeConnect) GetAfterSaleOrder(ctx context.Context, data *ProxyRequest) (*GetAfterSaleOrderResponse, error) {
	return GetAfterSaleOrder(ctx, data)
}

func (wc *maiWeConnect) OrderRefund(ctx context.Context, origin string, data *OrderRefundRequest) (*OrderRefundInfo, error) {
	return OrderRefund(ctx, origin, data)
}

func (wc *maiWeConnect) OrderRefundStatus(ctx context.Context, origin, outRefundNo string) (*OrderRefundInfo, error) {
	return OrderRefundStatus(ctx, origin, outRefundNo)
}

func (wc *maiWeConnect) ReportWeappFormId(ctx context.Context, channelId string, data *ReportWeappFormIdRequest) (map[string]interface{}, error) {
	return ReportWeappFormId(ctx, channelId, data)
}

func (wc *maiWeConnect) GetSingleTemplateMessage(ctx context.Context, channelId, messageId string) (*SingleTargetTplMessage, error) {
	return GetSingleTemplateMessage(ctx, channelId, messageId)
}

func (wc *maiWeConnect) ListTemplates(ctx context.Context, channelId string) ([]BriefTemplate, error) {
	return ListTemplates(ctx, channelId)
}

func (wc *maiWeConnect) DeleteProfitSharingReceiver(ctx context.Context, channelId string, data *DeleteProfitSharingReceiverRequest) (map[string]interface{}, error) {
	return DeleteProfitSharingReceiver(ctx, channelId, data)
}

func (wc *maiWeConnect) CreateProfitSharingReceiver(ctx context.Context, channelId string, data *CreateProfitSharingReceiverRequest) (map[string]interface{}, error) {
	return CreateProfitSharingReceiver(ctx, channelId, data)
}

func (wc *maiWeConnect) MultiProfitSharing(ctx context.Context, channelId string, data *MultiProfitSharingRequest) (map[string]interface{}, error) {
	return MultiProfitSharing(ctx, channelId, data)
}

func (wc *maiWeConnect) GetProfitSharing(ctx context.Context, channelId, transactionId string) (OrderMultiProfitSharingResponse, error) {
	return GetProfitSharing(ctx, channelId, transactionId)
}

func (wc *maiWeConnect) GetRefreshedProfitSharing(ctx context.Context, channelId, outOrderNo string) (OrderMultiProfitSharingResponse, error) {
	resp := OrderMultiProfitSharingResponse{}
	results, err := GetRefreshedProfitSharing(ctx, channelId, outOrderNo)
	if err != nil {
		return resp, nil
	}
	resp.Results = append(resp.Results, results)
	return resp, nil
}

func (wc *maiWeConnect) FinishProfitSharing(ctx context.Context, channelId string, data *FinishProfitSharingRequest) (map[string]interface{}, error) {
	return FinishProfitSharing(ctx, channelId, data)
}

func (wc *maiWeConnect) GetWeappVersion(ctx context.Context, accountId string) (*GetWeappVersionResponse, error) {
	return GetWeappVersion(ctx, accountId)
}

func (wc *maiWeConnect) GetWeappChangeLogs(ctx context.Context, accountId string) (*GetWeappChangeLogsResponse, error) {
	return GetWeappChangeLogs(ctx, accountId)
}

func (wc *maiWeConnect) GetWeappQrCode(ctx context.Context, accountId string, data *GetQrCodeRequest) ([]byte, error) {
	return GetWeappQrCode(ctx, accountId, data)
}

func (wc *maiWeConnect) GetDeliveries(ctx context.Context, accountId string) (*GetDeliveriesResponse, error) {
	return GetDeliveries(ctx, accountId)
}

func (wc *maiWeConnect) AddExpressOrder(ctx context.Context, accountId string, data *AddExpressOrderRequest) (*ExpressOrderResponse, error) {
	return AddExpressOrder(ctx, accountId, data)
}

func (wc *maiWeConnect) GetExpressOrderPath(ctx context.Context, accountId string, data *GetExpressOrderPathRequest) (*GetExpressOrderPathResponse, error) {
	return GetExpressOrderPath(ctx, accountId, data)
}

func (wc *maiWeConnect) GetAllExpressAccounts(ctx context.Context, accountId string) (*GetAllExpressAccountsResponse, error) {
	return GetAllExpressAccounts(ctx, accountId)
}

func (wc *maiWeConnect) SendTransfer(ctx context.Context, origin string, data *SendTransferRequest) (*TransferDetailResponse, error) {
	return SendTransfer(ctx, origin, data)
}

func (wc *maiWeConnect) SendV2Transfer(ctx context.Context, origin string, data *SendV2TransferRequest) (*V2TransferDetailResponse, error) {
	return SendV2Transfer(ctx, origin, data)
}

func (wc *maiWeConnect) GetTransfer(ctx context.Context, origin, tradeNo string) (*TransferDetailResponse, error) {
	return GetTransfer(ctx, origin, tradeNo)
}

func (wc *maiWeConnect) GetV2Transfer(ctx context.Context, origin, tradeBatchNo, tradeNo string) (*V2TransferDetailResponse, error) {
	return GetV2Transfer(ctx, origin, tradeBatchNo, tradeNo)
}

func (wc *maiWeConnect) ListDepartments(ctx context.Context, channelId string) ([]Department, error) {
	return ListDepartments(ctx, channelId)
}

func (wc *maiWeConnect) ListDepartmentsById(ctx context.Context, channelId, departmentId string) ([]Department, error) {
	return ListDepartmentsById(ctx, channelId, departmentId)
}

func (wc *maiWeConnect) ListDepartmentUsers(ctx context.Context, channelId, departmentId, fetchChild string) ([]User, error) {
	return ListDepartmentUsers(ctx, channelId, departmentId, fetchChild)
}

func (wc *maiWeConnect) GetUser(ctx context.Context, channelId, userId string) (*User, error) {
	return GetUser(ctx, channelId, userId)
}

func (wc *maiWeConnect) GetContactWay(ctx context.Context, channelId, contactWayId string) (*ContactWay, error) {
	return GetContactWay(ctx, channelId, contactWayId)
}

func (wc *maiWeConnect) SearchContactWay(ctx context.Context, channelId string, req SearchContactWayRequest) (*SearchContactWayResponse, error) {
	return SearchContactWay(ctx, channelId, req)
}

func (wc *maiWeConnect) ListContactWays(ctx context.Context, channelId string, req SearchContactWayRequest) (*SearchContactWayResponse, error) {
	return SearchContactWay(ctx, channelId, req)
}

func (wc *maiWeConnect) GetContactWayByState(ctx context.Context, channelId, state string) (*ContactWay, error) {
	return GetContactWayByState(ctx, channelId, state)
}

func (wc *maiWeConnect) CreateContactWay(ctx context.Context, channelId string, contactWay ContactWayRequest) (*ContactWay, error) {
	return CreateContactWay(ctx, channelId, contactWay)
}

func (wc *maiWeConnect) UpdateContactWay(ctx context.Context, channelId, contactWayId string, contactWay ContactWayRequest) (*ContactWay, error) {
	return UpdateContactWay(ctx, channelId, contactWayId, contactWay)
}

func (wc *maiWeConnect) DeleteContactWay(ctx context.Context, channelId, contactWayId string) error {
	return DeleteContactWay(ctx, channelId, contactWayId)
}

func (wc *maiWeConnect) ListContactWay(ctx context.Context, channelId string, groupIds []string) ([]ContactWay, error) {
	return ListContactWay(ctx, channelId, groupIds)
}

func (wc *maiWeConnect) ListWelcomeMessages(ctx context.Context, channelId string, req *ListContactWelcomeMsgsRequest) (*ListContactWelcomeMsgsResponse, error) {
	return ListWelcomeMessages(ctx, channelId, req)
}

func (wc *maiWeConnect) DeleteWelcomeMessage(ctx context.Context, channelId, userId string) error {
	return DeleteWelcomeMessage(ctx, channelId, userId)
}

func (wc *maiWeConnect) CreateGroupWelcomeTemplate(ctx context.Context, channelId string, req *GroupWelcomeTemplate) (*GroupWelcomeTemplate, error) {
	return CreateGroupWelcomeTemplate(ctx, channelId, req)
}

func (wc *maiWeConnect) UpdateGroupWelcomeTemplate(ctx context.Context, channelId string, req *GroupWelcomeTemplate) (*WeconnectResponse, error) {
	return UpdateGroupWelcomeTemplate(ctx, channelId, req)
}

func (wc *maiWeConnect) DeleteGroupWelcomeTemplate(ctx context.Context, channelId, templateId string) error {
	return DeleteGroupWelcomeTemplate(ctx, channelId, templateId)
}

func (wc *maiWeConnect) ListChats(ctx context.Context, channelId, cursor string, status int, userIds []string, partyIds []int) (string, []GroupchatBrief, error) {
	return ListChats(ctx, channelId, cursor, status, userIds, partyIds)
}

func (wc *maiWeConnect) ListChatMembers(ctx context.Context, channelId, chatId string) (*Groupchat, error) {
	return ListChatMembers(ctx, channelId, chatId)
}

func (wc *maiWeConnect) ListStatsChats(ctx context.Context, channelId string, req ListStatsChatsRequest) (ListStatsChatsResponse, error) {
	return ListStatsChats(ctx, channelId, req)
}

func (wc *maiWeConnect) ListStatsStaffContact(ctx context.Context, channelId string, req ListStatsStaffContactRequest) (ListStatsStaffContactResponse, error) {
	return ListStatsStaffContact(ctx, channelId, req)
}

func (wc *maiWeConnect) CreateGroupchatJoinWays(ctx context.Context, channelId string, req GroupchatJoinWaysRequest) (*GroupchatJoinWays, error) {
	return CreateGroupchatJoinWays(ctx, channelId, req)
}

func (wc *maiWeConnect) UpdateGroupchatJoinWays(ctx context.Context, channelId, configId string, req GroupchatJoinWaysRequest) (*GroupchatJoinWays, error) {
	return UpdateGroupchatJoinWays(ctx, channelId, configId, req)
}

func (wc *maiWeConnect) GetGroupchatJoinWay(ctx context.Context, channelId, configId string) (*GroupchatJoinWays, error) {
	return GetGroupchatJoinWay(ctx, channelId, configId)
}

func (wc *maiWeConnect) GetCorp(ctx context.Context, corpId string) (*CorpDetail, error) {
	return GetCorp(ctx, corpId)
}

func (wc *maiWeConnect) ListTags(ctx context.Context, corpId, channelId, appId string, tagIds []string) ([]*GroupTagsResponse, error) {
	return ListTags(ctx, corpId, channelId, appId, tagIds)
}

func (wc *maiWeConnect) AddTags(ctx context.Context, corpId, channelId string, data AddTagsRequest) (*GroupTagsResponse, error) {
	return AddTags(ctx, corpId, channelId, data)
}

func (wc *maiWeConnect) EditTag(ctx context.Context, corpId, channelId string, data EditTagRequest) error {
	return EditTag(ctx, corpId, channelId, data)
}

func (wc *maiWeConnect) DeleteTags(ctx context.Context, corpId, channelId, appId string, data DeleteTagsRequest) error {
	return DeleteTags(ctx, corpId, channelId, appId, data)
}

func (wc *maiWeConnect) EditMemberTags(ctx context.Context, corpId, channelId, appId string, data EditMemberTagsRequest) error {
	return EditMemberTags(ctx, corpId, channelId, appId, data)
}

func (wc *maiWeConnect) AddWechatTagToUsers(ctx context.Context, channelId string, wxTagId int, openIds []string) (map[string][]string, int, error) {
	return AddWechatTagToUsers(ctx, channelId, wxTagId, openIds)
}

func (wc *maiWeConnect) RemoveWechatTagFromUsers(ctx context.Context, channelId string, wxTagId int, openIds []string) (interface{}, int, error) {
	return RemoveWechatTagFromUsers(ctx, channelId, wxTagId, openIds)
}

func (wc *maiWeConnect) GetExternalUser(ctx context.Context, channelId, externalUserId string) (GetExternalUserResponse, error) {
	return GetExternalUser(ctx, channelId, externalUserId)
}

func (wc *maiWeConnect) GetSystemExternalUser(ctx context.Context, channelId, externalUserId string) (GetSystemExternalUserResponse, error) {
	return GetSystemExternalUser(ctx, channelId, externalUserId)
}

func (wc *maiWeConnect) ListSystemExternalUser(ctx context.Context, channelId string, externalUserIds []string) (*ListSystemExternalUserResponse, error) {
	return ListSystemExternalUser(ctx, channelId, externalUserIds)
}
func (wc *maiWeConnect) ListFollowExternalUsers(ctx context.Context, channelId, userIds string) (ListFollowExternalUserResponse, error) {
	return ListFollowExternalUsers(ctx, channelId, userIds)
}
func (wc *maiWeConnect) ListExternalUser(ctx context.Context, channelId, userId, cursor, limit string) (ListExternalUserResponse, error) {
	return ListExternalUser(ctx, channelId, userId, cursor, limit)
}

func (wc *maiWeConnect) SyncExternalUsers(ctx context.Context, channelId string, userIds []string, force, forceOld bool) error {
	return SyncExternalUsers(ctx, channelId, userIds, force, forceOld)
}

func (wc *maiWeConnect) ConvertToOpenId(ctx context.Context, channelId, userId string) (string, error) {
	return ConvertToOpenId(ctx, channelId, userId)
}

func (wc *maiWeConnect) GetAppDetail(ctx context.Context, channelId string) (AppDetail, error) {
	return GetAppDetail(ctx, channelId)
}

func (wc *maiWeConnect) SendAliyunqaSms(ctx context.Context, req SendAliyunqaSmsRequest, isDigital bool, oem AliyunqaOem) (*SendAliyunqaSmsResponse, error) {
	return SendAliyunqaSms(ctx, req, isDigital, oem)
}

func (wc *maiWeConnect) GetAliyunqaSms(ctx context.Context, id string, oem AliyunqaOem) (*AliyunqaSmsDetailResponse, error) {
	return GetAliyunqaSms(ctx, id, oem)
}

func (wc *maiWeConnect) SendWelcomeMessage(ctx context.Context, channelId string, req SendWelcomeMessageRequest) error {
	return SendWelcomeMessage(ctx, channelId, req)
}

func (wc *maiWeConnect) DownloadMediaMeterial(ctx context.Context, channelId, mediaType, mediaId string, isTemporary bool, savePath string) error {
	return DownloadMediaMeterial(ctx, channelId, mediaType, mediaId, isTemporary, savePath)
}

func (wc *maiWeConnect) CreateTemporaryMediaMaterial(ctx context.Context, channelId, mediaType, mediaUrl string) (*CreateTemporaryMediaMaterialResp, error) {
	return CreateTemporaryMediaMaterial(ctx, channelId, mediaType, mediaUrl)
}

func (wc *maiWeConnect) CreateGroupMessages(ctx context.Context, channelId string, req CreateGroupMessageRequest) (*CreateGroupMessageResponse, error) {
	return CreateGroupMessages(ctx, channelId, req)
}

func (wc *maiWeConnect) GetPaymonetCredentialConfiguration(ctx context.Context, channel string, quncrmAccountId string) (*PaymentCredentialConfigurationResponse, error) {
	return GetPaymonetCredentialConfiguration(ctx, channel, quncrmAccountId)
}

func (wc *maiWeConnect) ListWechatcpAdmins(ctx context.Context, channelId string) (*ListWechatcpAdminsResponse, error) {
	return ListWechatcpAdmins(ctx, channelId)
}

func (wc *maiWeConnect) ListWechatcpAuthUsers(ctx context.Context, channelId string) (*ListWechatcpAuthUsersResponse, error) {
	return ListWechatcpAuthUsers(ctx, channelId)
}

func (wc *maiWeConnect) UpdateWxCard(ctx context.Context, channelId string, req UpdateWxCardRequest) error {
	return UpdateWxCard(ctx, channelId, req)
}

func (wc *maiWeConnect) WeconnectProxy(ctx context.Context, channelId, method, path string, params, body, result interface{}) error {
	return WeconnectProxy(ctx, channelId, method, path, params, body, result)
}

func (wc *maiWeConnect) WeconnectProxyV2(ctx context.Context, origin, channelId, method, path string, body, result interface{}) error {
	return WeconnectProxyV2(ctx, origin, channelId, method, path, body, result)
}

func (wc *maiWeConnect) SendExternalContactMassMessage(ctx context.Context, channelId string, data *ExternalContactMassMessage) (*ExternalContactMassMessageResponse, error) {
	return SendExternalContactMassMessage(ctx, channelId, data)
}

func (wc *maiWeConnect) GetExternalContactMassMessageResult(ctx context.Context, channelId string, req *GetExternalContactMassMessage) (*ExternalContactMassMessageResult, error) {
	return GetExternalContactMassMessageResult(ctx, channelId, req)
}

func (wc *maiWeConnect) GetMassMessageResultTaskList(ctx context.Context, channelId string, req *GetExternalContactMassMessage) (*MassMessageTaskList, error) {
	return GetMassMessageResultTaskList(ctx, channelId, req)
}

func (wc *maiWeConnect) CreateMassMessageTask(ctx context.Context, channelId string, data *ExternalContactMassMessage) (string, error) {
	return CreateMassMessageTask(ctx, channelId, data)
}

func (wc *maiWeConnect) GetMassMessageTaskResult(ctx context.Context, channelId, taskId string) (*MassMessageTaskResult, error) {
	return GetMassMessageTaskResult(ctx, channelId, taskId)
}

func (wc *maiWeConnect) CancelMassMessageTask(ctx context.Context, channelId, taskId string) error {
	return CancelMassMessageTask(ctx, channelId, taskId)
}

func (wc *maiWeConnect) DeleteFollower(ctx context.Context, channelId, openId string) error {
	return DeleteFollower(ctx, channelId, openId)
}

func (wc *maiWeConnect) GetActiveCodeCount(ctx context.Context, channelId string) (uint64, error) {
	return GetActiveCodeCount(ctx, channelId)
}

func (wc *maiWeConnect) CreateRenewUserOrder(ctx context.Context, channelId string, req *CreateRenewUserOrderRequest) (string, error) {
	return CreateRenewUserOrder(ctx, channelId, req)
}

func (wc *maiWeConnect) GetRenewUserResult(ctx context.Context, channelId, orderId string) (*GetRenewUserResultResponse, error) {
	return GetRenewUserResult(ctx, channelId, orderId)
}

func (wc *maiWeConnect) CreateActiveUserTask(ctx context.Context, channelId string, userIds []string) (string, error) {
	return CreateActiveUserTask(ctx, channelId, userIds)
}

func (wc *maiWeConnect) GetActiveUserTaskResult(ctx context.Context, channelId, taskId string) (*ActiveUserTaskResult, error) {
	return GetActiveUserTaskResult(ctx, channelId, taskId)
}

func (wc *maiWeConnect) TransferUsers(ctx context.Context, channelId string, requests []TransferUsersRequest) ([]TransferUsersResponse, error) {
	return TransferUsers(ctx, channelId, requests)
}

func (wc *maiWeConnect) GetActiveUserDetail(ctx context.Context, channelId, userId string) (*ActiveUser, error) {
	return GetActiveUserDetail(ctx, channelId, userId)
}

func (wc *maiWeConnect) ShareActiveCode(ctx context.Context, channelId string, req ShareActiveCodeRequest) (string, error) {
	return ShareActiveCode(ctx, channelId, req)
}

func (wc *maiWeConnect) GetShareActiveCodeResult(ctx context.Context, channelId, taskId string) (ShareActiveCodeResult, error) {
	return GetShareActiveCodeResult(ctx, channelId, taskId)
}
func (wc *maiWeConnect) ConvertChatExternalUserIdToUnionId(ctx context.Context, channelId string, req *ConvertChatExternalUserIdToUnionIdRequest) ([]ConvertChatExternalUserIdToUnionIdResult, error) {
	return ConvertChatExternalUserIdToUnionId(ctx, channelId, req)
}

func (wc *maiWeConnect) CreateWechatCoupon(ctx context.Context, data *CreateWechatCouponRequest) (*CreateWechatCouponResponse, error) {
	return CreateWechatCoupon(ctx, data)
}

func (wc *maiWeConnect) RedeemedWechatCoupon(ctx context.Context, data *RedeemedWechatCouponRequest) (*RedeemedWechatCouponResponse, error) {
	return RedeemedWechatCoupon(ctx, data)
}

func (wc *maiWeConnect) UpdateWechatCoupon(ctx context.Context, data *UpdateWechatCouponRequest) (*UpdateWechatCouponResponse, error) {
	return UpdateWechatCoupon(ctx, data)
}

func (wc *maiWeConnect) UpdateWechatCouponStock(ctx context.Context, data *UpdateWechatCouponStockRequest) (*UpdateWechatCouponStockResponse, error) {
	return UpdateWechatCouponStock(ctx, data)
}

func (wc *maiWeConnect) GetWechatCouponSign(ctx context.Context, data map[string]interface{}) (*GetWechatCouponSignResponse, error) {
	return GetWechatCouponSign(ctx, data)
}

func (wc *maiWeConnect) ReturnWechatCoupon(ctx context.Context, data *ReturnWechatCouponRequest) (*ReturnWechatCouponResponse, error) {
	return ReturnWechatCoupon(ctx, data)
}

func (wc *maiWeConnect) GetWechatCoupon(ctx context.Context, data *GetWechatCouponRequest) (*GetWechatCouponResponse, error) {
	return GetWechatCoupon(ctx, data)
}

func (wc *maiWeConnect) UploadWechatMarketingImage(ctx context.Context, data *UploadWechatMarketingImageRequest) (*UploadWechatMarketingImageResponse, error) {
	return UploadWechatMarketingImage(ctx, data)
}

func (wc *maiWeConnect) CorrectExternalUser(ctx context.Context, req *CorrectExternalUserRequest) error {
	return CorrectExternalUser(ctx, req)
}

func (wc *maiWeConnect) ConvertOpenIdAndUnionIdToExternalUserId(ctx context.Context, openId, unionId, channelId string) error {
	return ConvertOpenIdAndUnionIdToExternalUserId(ctx, openId, unionId, channelId)
}

func (wc *maiWeConnect) DownloadMiniProgramQrcode(ctx context.Context, channelId, savePath string, req *DownloadMiniProgramQrcodeRequest) error {
	return DownloadMiniProgramQrcode(ctx, channelId, savePath, req)
}

func (wc *maiWeConnect) CreateCustomerAcquisitionLink(ctx context.Context, data *ProxyRequest) (*CreateCustomerAcquisitionLinkResponse, error) {
	return CreateCustomerAcquisitionLink(ctx, data)
}

func (wc *maiWeConnect) GetCustomerAcquisitionLink(ctx context.Context, data *ProxyRequest) (*GetCustomerAcquisitionLinkResponse, error) {
	return GetCustomerAcquisitionLink(ctx, data)
}

func (wc *maiWeConnect) UpdateCustomerAcquisitionLink(ctx context.Context, data *ProxyRequest) (*UpdateCustomerAcquisitionLinkResponse, error) {
	return UpdateCustomerAcquisitionLink(ctx, data)
}

func (wc *maiWeConnect) DeleteCustomerAcquisitionLink(ctx context.Context, data *ProxyRequest) (*DeleteCustomerAcquisitionLinkResponse, error) {
	return DeleteCustomerAcquisitionLink(ctx, data)
}

func (wc *maiWeConnect) GetCustomerAcquisitionQuota(ctx context.Context, data *ProxyRequest) (*GetCustomerAcquisitionQuotaResponse, error) {
	return GetCustomerAcquisitionQuota(ctx, data)
}

func (wc *maiWeConnect) GetSuiteAppInfo(ctx context.Context, corpId, suiteId string) (*GetSuiteAppInfoResponse, error) {
	return GetSuiteAppInfo(ctx, corpId, suiteId)
}

func (wc *maiWeConnect) ListMerchantCashCoupons(ctx context.Context, data *ProxyRequest) (*ListMerchantCashCouponsResponse, error) {
	return ListMerchantCashCoupons(ctx, data)
}

func (wc *maiWeConnect) GetPaymentConfigurationByChannel(ctx context.Context, origin, channelId string) (*PaymentCredentialConfigurationResponse, error) {
	return GetPaymentConfigurationByChannel(ctx, origin, channelId)
}

func (wc *maiWeConnect) AddOrRemoveCallback(ctx context.Context, req AddOrRemoveCallbackRequest) error {
	return AddOrRemoveCallback(ctx, req)
}

func (wc *maiWeConnect) ListChatMessageHistories(ctx context.Context, corpId string, req ListChatMessageHistoriesRequest) (*ListChatMessageHistoriesResponse, error) {
	return ListChatMessageHistories(ctx, corpId, req)
}

func (wc *maiWeConnect) ListChatMessageHistoryPreviews(ctx context.Context, corpId string, req ListChatMessageHistoryPreviewsRequest) (*ListChatMessageHistoryPreviewsResponse, error) {
	return ListChatMessageHistoryPreviews(ctx, corpId, req)
}

func (wc *maiWeConnect) ManualSyncChatMessageHistories(ctx context.Context, corpId string) error {
	return ManualSyncChatMessageHistories(ctx, corpId)
}

func (wc *maiWeConnect) GetChatMessageHistoriesSyncInfo(ctx context.Context, corpId string) (*ChatMessageHistoriesSyncInfo, error) {
	return GetChatMessageHistoriesSyncInfo(ctx, corpId)
}

func (wc *maiWeConnect) RedoChatMessageHistoryRecognizeSpeech(ctx context.Context, corpId, messageId string) (*ChatMessageHistory, error) {
	return RedoChatMessageHistoryRecognizeSpeech(ctx, corpId, messageId)
}

func (wc *maiWeConnect) GetMessageAuditStatus(ctx context.Context, corpId string) (*MessageAuditStatus, error) {
	return GetMessageAuditStatus(ctx, corpId)
}

func (wc *maiWeConnect) GetMessageAuditPermitUsers(ctx context.Context, corpId string) ([]string, error) {
	return GetMessageAuditPermitUsers(ctx, corpId)
}

func (wc *maiWeConnect) ListTemplateMessageResults(ctx context.Context, channelId string, resultIds []string) ([]TemplateMessageResult, error) {
	return ListTemplateMessageResults(ctx, channelId, resultIds)
}

func (wc *maiWeConnect) LoginAuthWeshop(ctx context.Context, providerId, code string) (*LoginAuthResponse, error) {
	return LoginAuthWeshop(ctx, providerId, code)
}

func (wc *maiWeConnect) GetBoughtServiceInfo(ctx context.Context, providerId, appId string, serviceId uint64) (*ServiceInfoResponse, error) {
	return GetBoughtServiceInfo(ctx, providerId, appId, serviceId)
}

func (wc *maiWeConnect) SyncChannel(ctx context.Context, providerId, appId string) (*Channel, error) {
	return SyncChannel(ctx, providerId, appId)
}
