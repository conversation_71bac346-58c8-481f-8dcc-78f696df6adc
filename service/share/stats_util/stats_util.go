package util

import (
	"context"
	"fmt"
	core_util "mairpc/core/util"
	"mairpc/service/share/util"
	"reflect"
	"strings"
	"sync"

	"github.com/panjf2000/ants/v2"
)

type StatsUtil struct {
	structInstance any
	req            any
	resp           any
	options        *Options
}

func New(structInstance, req, resp any, options ...Option) *StatsUtil {
	opts := loadOptions(options...)
	return &StatsUtil{
		structInstance: structInstance,
		req:            req,
		resp:           resp,
		options:        opts,
	}
}

func (stats *StatsUtil) Exec(ctx context.Context) error {
	pool, err := stats.getPool(ctx)
	if err != nil {
		return err
	}

	defer pool.Release()
	var wg sync.WaitGroup
	structInstanceValue := reflect.ValueOf(stats.structInstance).Elem()
	respValue := reflect.ValueOf(stats.resp).Elem()

	for funcName, fields := range stats.getFuncFieldsMap() {
		wg.Add(1)
		tempFields := fields
		tempFuncName := strings.ToUpper(funcName[:1]) + funcName[1:]
		pool.Submit(func() {
			defer wg.Done()
			methodValue := structInstanceValue.MethodByName(tempFuncName)
			if !methodValue.IsValid() {
				return
			}
			result := methodValue.Call([]reflect.Value{
				reflect.ValueOf(ctx),
				reflect.ValueOf(stats.req),
			})
			for key, field := range tempFields {
				if field == "" {
					continue
				}
				field = strings.ToUpper(field[:1]) + field[1:]
				respValue.FieldByName(field).Set(reflect.ValueOf(result[key].Interface()))
			}
		})
	}
	wg.Wait()
	return nil
}

func (stats *StatsUtil) getPool(ctx context.Context) (*ants.Pool, error) {
	size := 10
	if stats.options.Size > 0 {
		size = int(stats.options.Size)
	}
	return util.NewGoroutinePoolWithPanicHandler(size, util.WithContext(core_util.DuplicateContext(ctx)))
}

func (stats *StatsUtil) getFuncFieldsMap() map[string][]string {
	funcFieldsMap := stats.options.FuncFieldsMap
	if funcFieldsMap == nil {
		funcFieldsMap = map[string][]string{}
	}

	fieldsWithFunc := stats.getStatsFuncFields()
	for _, field := range stats.getStatsFields() {
		if util.StrInArray(strings.ToLower(field), &fieldsWithFunc) {
			continue
		}
		field = strings.ToUpper(field[:1]) + field[1:]
		funcName := fmt.Sprintf("Get%s", field)
		funcFieldsMap[funcName] = []string{field}
	}
	return funcFieldsMap
}

// 获取指定了统计方法的字段
func (stats *StatsUtil) getStatsFuncFields() []string {
	fieldsWithFunc := []string{}
	for _, fields := range stats.options.FuncFieldsMap {
		for _, field := range fields {
			fieldsWithFunc = append(fieldsWithFunc, strings.ToLower(field))
		}
	}
	return fieldsWithFunc
}

// 如果参数中有直接返回，参数中没有，从 resp 中获取
func (stats *StatsUtil) getStatsFields() []string {
	if len(stats.options.StatsFields) > 0 {
		return stats.options.StatsFields
	}
	fields := []string{}
	respType := reflect.TypeOf(stats.resp).Elem()
	for i := 0; i < respType.NumField(); i++ {
		name := respType.Field(i).Name
		if util.StrInArray(name, &stats.options.IgnoreFields) {
			continue
		}
		fields = append(fields, name)
	}
	return fields
}
