package model

import (
	"fmt"
	"mairpc/core/errors"
	"mairpc/proto/common/types"
	"mairpc/service/share/util"
	"reflect"
	"regexp"
	"strings"
	"time"

	"github.com/asaskevich/govalidator"
	"github.com/spf13/cast"
)

const (
	// http://git.augmentum.com.cn/scrm/bigdata/blob/develop/docs/api.md#%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B
	OPERATOR_IN           = "IN"
	OPERATOR_NIN          = "NOT_IN"
	OPERATOR_EQ           = "EQUALS"
	OPERATOR_NE           = "NOT_EQUALS"
	OPERATOR_GT           = "GT"
	OPERATOR_LT           = "LT"
	OPERATOR_GTE          = "GTE"
	OPERATOR_LTE          = "LTE"
	OPERATOR_NULL         = "IS_NULL"
	OPERATOR_NNULL        = "IS_NOT_NULL"
	OPERATOR_BETWEEN      = "BETWEEN"
	OPERATOR_NOT_BETWEEN  = "NOT_BETWEEN"
	OPERATOR_CONTAINS     = "CONTAINS"
	OPERATOR_NOT_CONTAINS = "NOT_CONTAINS"
	OPERATOR_INANY        = "CONTAINS_ANY"
	OPERATOR_NINANY       = "NOT_CONTAINS_ANY"
	OPERATOR_RLIKE        = "RLIKE"
	OPERATOR_NOT_RLIKE    = "NOT_RLIKE"

	TYPE_STRING           = "string"
	TYPE_INPUT            = "input"
	TYPE_DATE             = "date"
	TYPE_NUMBER           = "number"
	TYPE_STRING_ARRAY     = "stringArray"
	TYPE_BOOL             = "bool"
	TYPE_DOMESTIC_ADDRESS = "domesticAddress"
	TYPE_ARRAYS           = "arrays"
)

var (
	StringOperators = []string{
		OPERATOR_IN,
		OPERATOR_NIN,
	}
	IntOperators = []string{
		OPERATOR_EQ,
		OPERATOR_NE,
		OPERATOR_GT,
		OPERATOR_LT,
		OPERATOR_GTE,
		OPERATOR_LTE,
		OPERATOR_BETWEEN,
	}

	nullOperator = []string{
		OPERATOR_NULL,
		OPERATOR_NNULL,
	}
)

type Compare interface {
	GetOperator() string
	GetValue() interface{}
}

type CompareRule struct {
	// 比较值
	Value interface{} `bson:"value"`
	// 比较符
	Operator string `bson:"operator"`
	// 值的类型
	// 有需要的时候可以在这里储存值类型以便做类型转化
	Type string `bson:"type,omitempty"`
}

func (c CompareRule) GetOperator() string {
	return c.Operator
}

func (c CompareRule) GetValue() interface{} {
	return c.Value
}

func (c CompareRule) String() string {
	var (
		format string
	)

	switch c.Operator {
	case OPERATOR_IN:
		format = "满足任一值：[%v]"
	case OPERATOR_NIN:
		format = "不等于任一值：[%v]"
	case OPERATOR_EQ:
		format = "等于：[%v]"
	case OPERATOR_NE:
		format = "不等于：[%v]"
	case OPERATOR_GT:
		format = "大于：[%v]"
	case OPERATOR_LT:
		format = "小于：[%v]"
	case OPERATOR_GTE:
		format = "大于等于：[%v]"
	case OPERATOR_LTE:
		format = "小于等于：[%v]"
	case OPERATOR_NULL:
		format = "为空"
	case OPERATOR_NNULL:
		format = "非空"
	case OPERATOR_BETWEEN:
		format = "在 [%v] 之间"
	case OPERATOR_NOT_BETWEEN:
		format = "不可在 [%v] 之间"
	case OPERATOR_CONTAINS:
		format = "包含： [%v]"
	case OPERATOR_NOT_CONTAINS:
		format = "不可包含： [%v]"
	case OPERATOR_INANY:
		format = "包含任一值：[%v]"
	case OPERATOR_NINANY:
		format = "不包含任一值：[%v]"
	}

	return fmt.Sprintf(format, c.Value)
}

func (c CompareRule) Compare(value interface{}) bool {
	switch c.Type {
	case TYPE_STRING:
		return MatchString(cast.ToString(value), c)
	case TYPE_INPUT:
		return MatchInput(cast.ToString(value), c)
	case TYPE_DATE:
		return MatchDate(cast.ToInt64(value), c)
	case TYPE_NUMBER:
		return MatchNumber(cast.ToFloat64(value), c)
	case TYPE_STRING_ARRAY:
		return MatchStringArray(cast.ToStringSlice(value), c)
	case TYPE_BOOL:
		return MatchBool(cast.ToBool(value), c)
	case TYPE_DOMESTIC_ADDRESS:
		return MatchDomesticAddress(cast.ToString(value), c)
	case TYPE_ARRAYS:
		return MatchArrays(cast.ToStringSlice(value), c)
	}

	return false
}

// MatchString 会对字符串比较
func MatchString(value string, rule Compare) bool {
	ruleValue := rule.GetValue()
	switch rule.GetOperator() {
	case OPERATOR_CONTAINS:
		if rv, rok := ruleValue.(string); rok {
			if strings.Contains(value, rv) {
				return true
			}
		}
	case OPERATOR_INANY:
		// 这里常量命名存在一些容易混淆的地方，OPERATOR_INANY 的值是 CONTAINS_ANY
		// 但很多地方是当做 IN_ANY 来使用的，这里则是用作包含任一（CONTAINS_ANY）的含义
		rvs := cast.ToStringSlice(ruleValue)
		for _, rv := range rvs {
			if strings.Contains(value, rv) {
				return true
			}
		}
	case OPERATOR_IN:
		rvs := cast.ToStringSlice(ruleValue)
		if len(rvs) == 0 || util.StrInArray(value, &rvs) {
			return true
		}
	case OPERATOR_NOT_CONTAINS:
		if rv, rok := ruleValue.(string); rok {
			if !strings.Contains(value, rv) {
				return true
			}
		}
	case OPERATOR_NIN:
		rvs := cast.ToStringSlice(ruleValue)
		if !util.StrInArray(value, &rvs) {
			return true
		}
	case OPERATOR_EQ:
		rv := cast.ToString(ruleValue)
		if rv == value {
			return true
		}
	case OPERATOR_NE:
		rv := cast.ToString(ruleValue)
		if rv != value {
			return true
		}
	case OPERATOR_NULL:
		if value == "" {
			return true
		}
	case OPERATOR_NNULL:
		if value != "" {
			return true
		}
	case OPERATOR_RLIKE, OPERATOR_NOT_RLIKE:
		if re, err := regexp.Compile(cast.ToString(ruleValue)); err == nil {
			if rule.GetOperator() == OPERATOR_RLIKE && re.MatchString(value) {
				return true
			}

			if rule.GetOperator() == OPERATOR_NOT_RLIKE && !re.MatchString(value) {
				return true
			}
		}
	case OPERATOR_NOT_BETWEEN:
		rvs := cast.ToStringSlice(ruleValue)
		if len(rvs) != 2 {
			return false
		}
		rv1 := rvs[0]
		rv2 := rvs[1]
		if value < rv1 || value > rv2 {
			return true
		}
	case OPERATOR_BETWEEN:
		rvs := cast.ToStringSlice(ruleValue)
		if len(rvs) != 2 {
			return false
		}
		rv1 := rvs[0]
		rv2 := rvs[1]
		if value >= rv1 && value <= rv2 {
			return true
		}
	}

	return false
}

// MatchInput 类似 MatchString，但用于比较用户输入，比较时无视大小写
func MatchInput(value string, rule Compare) bool {
	rv := strings.ToLower(cast.ToString(rule.GetValue()))
	switch rule.GetOperator() {
	case OPERATOR_CONTAINS:
		if strings.Contains(value, rv) {
			return true
		}
	case OPERATOR_NOT_CONTAINS:
		if !strings.Contains(value, rv) {
			return true
		}
	case OPERATOR_IN:
		rvs := cast.ToStringSlice(rule.GetValue())
		if len(rvs) == 0 || util.StrInArray(value, &rvs) {
			return true
		}
	case OPERATOR_NIN:
		rvs := cast.ToStringSlice(rule.GetValue())
		if !util.StrInArray(value, &rvs) {
			return true
		}
	case OPERATOR_EQ:
		if rv == value {
			return true
		}
	case OPERATOR_NE:
		if rv != value {
			return true
		}
	case OPERATOR_NULL:
		if value == "" {
			return true
		}
	case OPERATOR_NNULL:
		if value != "" {
			return true
		}
	}

	return false
}

func MatchDate(timestamp int64, rule Compare) bool {
	ruleValue := rule.GetValue()
	switch rule.GetOperator() {
	case OPERATOR_EQ:
		rv := cast.ToInt64(ruleValue)
		if rv == timestamp {
			return true
		}
	case OPERATOR_NE:
		rv := cast.ToInt64(ruleValue)
		if rv != timestamp {
			return true
		}
	case OPERATOR_BETWEEN, OPERATOR_NOT_BETWEEN:
		rv := cast.ToIntSlice(ruleValue)
		if len(rv) != 2 {
			break
		}

		rv0 := cast.ToInt64(rv[0])
		rv1 := cast.ToInt64(rv[1])
		if rule.GetOperator() == OPERATOR_BETWEEN && timestamp >= rv0 && timestamp <= rv1 {
			return true
		}
		if rule.GetOperator() == OPERATOR_NOT_BETWEEN && (timestamp < rv0 || timestamp > rv1) {
			return true
		}
	case OPERATOR_LT:
		rv := cast.ToInt64(ruleValue)
		if timestamp < rv {
			return true
		}
	case OPERATOR_GTE:
		rv := cast.ToInt64(ruleValue)
		if timestamp >= rv {
			return true
		}
	case OPERATOR_GT:
		rv := cast.ToInt64(ruleValue)
		if timestamp > rv {
			return true
		}
	case OPERATOR_LTE:
		rv := cast.ToInt64(ruleValue)
		if timestamp <= rv {
			return true
		}
	case OPERATOR_NULL:
		if timestamp == 0 {
			return true
		}
	case OPERATOR_NNULL:
		if timestamp != 0 {
			return true
		}
	}

	return false
}

func MatchNumber(value float64, rule Compare) bool {
	ruleValue := rule.GetValue()
	switch rule.GetOperator() {
	case OPERATOR_EQ:
		rv := cast.ToFloat64(ruleValue)
		if rv == value {
			return true
		}
	case OPERATOR_NE:
		rv := cast.ToFloat64(ruleValue)
		if rv != value {
			return true
		}
	case OPERATOR_INANY, OPERATOR_IN:
		rvs := cast.ToStringSlice(ruleValue)
		for _, rv := range rvs {
			if cast.ToFloat64(rv) == value {
				return true
			}
		}
	case OPERATOR_BETWEEN, OPERATOR_NOT_BETWEEN:
		rvs := cast.ToStringSlice(ruleValue)
		if len(rvs) != 2 {
			break
		}

		rv0 := cast.ToFloat64(rvs[0])
		rv1 := cast.ToFloat64(rvs[1])
		if rule.GetOperator() == OPERATOR_BETWEEN && value >= rv0 && value <= rv1 {
			return true
		}

		if rule.GetOperator() == OPERATOR_NOT_BETWEEN && (value < rv0 || value > rv1) {
			return true
		}
	case OPERATOR_LT:
		rv := cast.ToFloat64(ruleValue)
		if value < rv {
			return true
		}
	case OPERATOR_GTE:
		rv := cast.ToFloat64(ruleValue)
		if value >= rv {
			return true
		}
	case OPERATOR_GT:
		rv := cast.ToFloat64(ruleValue)
		if value > rv {
			return true
		}
	case OPERATOR_LTE:
		rv := cast.ToFloat64(ruleValue)
		if value <= rv {
			return true
		}
	case OPERATOR_NULL:
		if value == 0 {
			return true
		}
	case OPERATOR_NNULL:
		if value != 0 {
			return true
		}
	case OPERATOR_NIN:
		rvs := cast.ToStringSlice(ruleValue)
		for _, rv := range rvs {
			if cast.ToFloat64(rv) == value {
				return false
			}
		}
		return true
	}

	return false
}

func MatchStringArray(value []string, rule Compare) bool {
	rvs := cast.ToStringSlice(rule.GetValue())
	switch rule.GetOperator() {
	case OPERATOR_EQ:
		if util.StrArrayDeepEqual(rvs, value) {
			return true
		}
	case OPERATOR_NE:
		if !util.StrArrayDeepEqual(rvs, value) {
			return true
		}
	case OPERATOR_INANY:
		for _, rv := range rvs {
			if util.StrInArray(rv, &value) {
				return true
			}
		}
	case OPERATOR_NINANY:
		includeAny := false
		for _, rv := range rvs {
			if util.StrInArray(rv, &value) {
				includeAny = true
				break
			}
		}

		if !includeAny {
			return true
		}
	case OPERATOR_NULL:
		if len(value) == 0 {
			return true
		}
	case OPERATOR_NNULL:
		if len(value) != 0 {
			return true
		}
	}

	return false
}

func MatchBool(value bool, rule Compare) bool {
	rv := cast.ToBool(rule.GetValue())
	switch rule.GetOperator() {
	case OPERATOR_EQ:
		if rv == value {
			return true
		}
	case OPERATOR_NE:
		if rv != value {
			return true
		}
	}

	return false
}

// 比较国内地址，地址格式需要是 "Province,City,District,Detail" 格式
func MatchDomesticAddress(value string, rule Compare) bool {
	rvs, ok := rule.GetValue().([]interface{})
	if !util.StrInArray(rule.GetOperator(), &nullOperator) && !ok {
		return false
	}

	var addresses []string
	for _, rv := range rvs {
		stringRvs := cast.ToStringSlice(rv)
		if len(stringRvs) != 0 {
			for i, stringRv := range stringRvs {
				if stringRv == "" {
					stringRvs[i] = "*"
				}
			}
			addresses = append(addresses, strings.Join(stringRvs, ","))
		} else {
			addresses = append(addresses, strings.Join(stringRvs, ","))
		}
	}

	switch rule.GetOperator() {
	case OPERATOR_IN:
		for _, candidate := range addresses {
			if ok, _ := regexp.MatchString(candidate, value); ok {
				return true
			}
			if strings.Contains(value, candidate) {
				return true
			}
		}
	case OPERATOR_NIN:
		nin := true
		for _, candidate := range addresses {
			if strings.Contains(value, candidate) {
				nin = false
			}
		}

		if nin {
			return true
		}
	case OPERATOR_NULL:
		if value == "" {
			return true
		}
	case OPERATOR_NNULL:
		if value != "" {
			return true
		}
	}

	return false
}

func MatchArrays(value []string, rule Compare) bool {
	rvs := cast.ToStringSlice(rule.GetValue())
	switch rule.GetOperator() {
	case OPERATOR_INANY:
		for _, rv := range rvs {
			if !util.StrInArray(rv, &value) {
				return true
			}
		}
	case OPERATOR_NINANY:
		allSubscribed := true
		for _, rv := range rvs {
			if !util.StrInArray(rv, &value) {
				allSubscribed = false
				break
			}
		}

		if allSubscribed {
			return true
		}
	case OPERATOR_NIN:
		for _, rv := range rvs {
			if util.StrInArray(rv, &value) {
				return false
			}
		}
		return true
	case OPERATOR_IN:
		for _, rv := range rvs {
			if util.StrInArray(rv, &value) {
				return true
			}
		}
	case OPERATOR_NNULL:
		if len(value) != 0 {
			return true
		}
	case OPERATOR_NULL:
		if len(value) == 0 {
			return true
		}
	}

	return false

}

func FormatProtobufRules(rules []*types.CompareRule) []CompareRule {
	result := []CompareRule{}
	for _, rule := range rules {
		r := CompareRule{
			Type:     rule.Type,
			Operator: rule.Operator,
		}

		switch rule.Type {
		case TYPE_STRING:
			if rule.Operator == OPERATOR_IN || rule.Operator == OPERATOR_NIN {
				r.Value = strings.Split(cast.ToString(rule.Value), ",")
			} else {
				r.Value = rule.Value
			}
		case TYPE_INPUT:
			r.Value = rule.Value
		case TYPE_DOMESTIC_ADDRESS:
			r.Value = [][]string{
				strings.Split(cast.ToString(rule.Value), ","),
			}
		case TYPE_DATE:
			r.Value = cast.ToInt64(rule.Value)
		case TYPE_NUMBER:
			if govalidator.IsNumeric(rule.Value) {
				r.Value = cast.ToFloat64(rule.Value)
			} else {
				// 如果 number 的比较符是 between，我们就要把值存成字符串数组
				// 请求里是 "123.45,567.89"
				r.Value = strings.Split(rule.Value, ",")
			}
		case TYPE_STRING_ARRAY:
			strArray := rule.Value
			r.Value = strings.Split(strArray, ",")
		case TYPE_BOOL:
			r.Value = cast.ToBool(rule.Value)
		case TYPE_ARRAYS:
			// TODO: 这个类型一般用不上，而且解释成本过高，暂时不支持使用。
		}

		result = append(result, r)
	}

	return result
}

func ProtoRuleToModelRuleConvertor(from reflect.Value, _ reflect.Type) (reflect.Value, error) {
	if rule, ok := from.Interface().(*types.CompareRule); ok {
		r := CompareRule{
			Type:     rule.Type,
			Operator: rule.Operator,
		}
		switch rule.Type {
		case TYPE_STRING:
			if rule.Operator == OPERATOR_IN || rule.Operator == OPERATOR_NIN {
				r.Value = strings.Split(cast.ToString(rule.Value), ",")
			} else {
				r.Value = rule.Value
			}
		case TYPE_INPUT:
			r.Value = rule.Value
		case TYPE_DOMESTIC_ADDRESS:
			r.Value = [][]string{
				strings.Split(cast.ToString(rule.Value), ","),
			}
		case TYPE_DATE:
			r.Value = cast.ToInt64(rule.Value)
		case TYPE_NUMBER:
			if govalidator.IsNumeric(rule.Value) {
				r.Value = cast.ToFloat64(rule.Value)
			} else {
				// 如果 number 的比较符是 between，我们就要把值存成字符串数组
				// 请求里是 "123.45,567.89"
				r.Value = strings.Split(rule.Value, ",")
			}
		case TYPE_STRING_ARRAY:
			strArray := rule.Value
			r.Value = strings.Split(strArray, ",")
		case TYPE_BOOL:
			r.Value = cast.ToBool(rule.Value)
		case TYPE_ARRAYS:
			// TODO: 这个类型一般用不上，而且解释成本过高，暂时不支持使用。
		}
		return reflect.ValueOf(r), nil
	}

	return from, errors.NewInvalidArgumentError("rules")
}

func ModelRuleToProtoRuleConvertor(from reflect.Value, _ reflect.Type) (reflect.Value, error) {
	if rule, ok := from.Interface().(CompareRule); ok {
		r := &types.CompareRule{
			Type:     rule.Type,
			Operator: rule.Operator,
		}
		switch rule.Type {
		case TYPE_INPUT, TYPE_DATE, TYPE_BOOL:
			r.Value = cast.ToString(rule.Value)
		case TYPE_STRING:
			if rule.Operator == OPERATOR_IN || rule.Operator == OPERATOR_NIN {
				v := cast.ToStringSlice(rule.Value)
				r.Value = strings.Join(v, ",")
			} else {
				r.Value = cast.ToString(rule.Value)
			}
		case TYPE_DOMESTIC_ADDRESS:
			values, ok := rule.Value.([]interface{})
			if !ok || len(values) == 0 {
				return from, errors.NewInvalidArgumentError("rules")
			}
			r.Value = strings.Join(cast.ToStringSlice(values[0]), ",")
		case TYPE_STRING_ARRAY:
			arr := cast.ToStringSlice(rule.Value)
			r.Value = strings.Join(arr, ",")
		case TYPE_NUMBER:
			switch rule.Operator {
			case OPERATOR_BETWEEN, OPERATOR_NOT_BETWEEN:
				arr := cast.ToStringSlice(rule.Value)
				r.Value = strings.Join(arr, ",")
			default:
				r.Value = cast.ToString(rule.Value)
			}
		case TYPE_ARRAYS:
			// TODO: 这个类型一般用不上，而且解释成本过高，暂时不支持使用。
		}
		return reflect.ValueOf(r), nil
	}
	return from, errors.NewInvalidArgumentError("rules")
}

func FormatModelRules(rules []CompareRule) []*types.CompareRule {
	result := []*types.CompareRule{}
	for _, rule := range rules {
		r := &types.CompareRule{
			Type:     rule.Type,
			Operator: rule.Operator,
		}

		switch rule.Type {
		case TYPE_INPUT, TYPE_DATE, TYPE_BOOL:
			r.Value = cast.ToString(rule.Value)
		case TYPE_STRING:
			if rule.Operator == OPERATOR_IN || rule.Operator == OPERATOR_NIN {
				v := cast.ToStringSlice(rule.Value)
				r.Value = strings.Join(v, ",")
			} else {
				r.Value = cast.ToString(rule.Value)
			}
		case TYPE_DOMESTIC_ADDRESS:
			values, ok := rule.Value.([]interface{})
			if !ok || len(values) == 0 {
				continue
			}
			r.Value = strings.Join(cast.ToStringSlice(values[0]), ",")
		case TYPE_STRING_ARRAY:
			arr := cast.ToStringSlice(rule.Value)
			r.Value = strings.Join(arr, ",")
		case TYPE_NUMBER:
			switch rule.Operator {
			case OPERATOR_BETWEEN, OPERATOR_NOT_BETWEEN:
				arr := cast.ToStringSlice(rule.Value)
				r.Value = strings.Join(arr, ",")
			default:
				r.Value = cast.ToString(rule.Value)
			}
		case TYPE_ARRAYS:
			// TODO: 这个类型一般用不上，而且解释成本过高，暂时不支持使用。
		}

		result = append(result, r)
	}
	return result
}

// MatchRFC3339 时间字符串，支持 YYYY-MM-DD 和 RFC3339 格式
func MatchRFC3339(value string, rule Compare) bool {
	switch rule.GetOperator() {
	case OPERATOR_EQ:
		rv := cast.ToString(rule.GetValue())
		rvt, vt := formatTimeWithSamePrecision(rv, value)
		return vt.Equal(rvt)
	case OPERATOR_NE:
		rv := cast.ToString(rule.GetValue())
		rvt, vt := formatTimeWithSamePrecision(rv, value)
		return !vt.Equal(rvt)
	case OPERATOR_BETWEEN:
		rv := cast.ToStringSlice(rule.GetValue())
		if len(rv) != 2 {
			return false
		}
		start, vts := formatTimeWithSamePrecision(rv[0], value)
		end, vte := formatTimeWithSamePrecision(rv[1], value)
		return (vts.After(start) && vte.Before(end)) || vts.Equal(start) || vte.Equal(end)
	case OPERATOR_NOT_BETWEEN:
		rv := cast.ToStringSlice(rule.GetValue())
		if len(rv) != 2 {
			return false
		}
		start, vts := formatTimeWithSamePrecision(rv[0], value)
		end, vte := formatTimeWithSamePrecision(rv[1], value)
		return vts.Before(start) || vte.After(end)
	case OPERATOR_GTE:
		rv := cast.ToString(rule.GetValue())
		rvt, vt := formatTimeWithSamePrecision(rv, value)
		return vt.After(rvt) || vt.Equal(rvt)
	case OPERATOR_GT:
		rv := cast.ToString(rule.GetValue())
		rvt, vt := formatTimeWithSamePrecision(rv, value)
		return vt.After(rvt)
	case OPERATOR_LTE:
		rv := cast.ToString(rule.GetValue())
		rvt, vt := formatTimeWithSamePrecision(rv, value)
		return vt.Before(rvt) || vt.Equal(rvt)
	case OPERATOR_LT:
		rv := cast.ToString(rule.GetValue())
		rvt, vt := formatTimeWithSamePrecision(rv, value)
		return vt.Before(rvt)
	}
	return false
}

// formatTimeWithSamePrecision 统一时间精度，以 t1 为准
func formatTimeWithSamePrecision(s1, s2 string) (time.Time, time.Time) {
	t1, t2 := util.MustTransStrToTime(s1), util.MustTransStrToTime(s2)
	if len(s2) == 10 {
		t2, _ = time.Parse("2006-01-02", s2)
	}
	if len(s1) == 10 {
		t1, _ = time.Parse("2006-01-02", s1)
		return time.Date(t1.Year(), t1.Month(), t1.Day(), 0, 0, 0, 0, time.Local),
			time.Date(t2.Year(), t2.Month(), t2.Day(), 0, 0, 0, 0, time.Local)
	}

	return t1, t2
}
