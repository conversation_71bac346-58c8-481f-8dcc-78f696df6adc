package model

import (
	"context"
	"reflect"
	"strconv"
	"time"

	"github.com/qiniu/qmgo"

	"mairpc/core/extension"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"
)

type BaseModel struct{}

var (
	Base = BaseModel{}
)

func (BaseModel) GetAllByPagination(ctx context.Context, condition bson.M, page uint32, pageSize uint32, orderbys []string, collection string, documents interface{}) int {
	sortFields := util.NormalizeOrderBy(orderbys)
	pageCond := extension.PagingCondition{
		Selector:  condition,
		PageIndex: int(page),
		PageSize:  int(pageSize),
		Sortor:    sortFields,
	}

	totalCount, _ := extension.DBRepository.FindByPagination(ctx, collection, pageCond, documents)

	return totalCount
}
func (BaseModel) GetAllByPaginationWithoutCount(ctx context.Context, condition bson.M, page uint32, pageSize uint32, orderbys []string, collection string, documents interface{}) {
	sortFields := util.NormalizeOrderBy(orderbys)
	pageCond := extension.PagingCondition{
		Selector:  condition,
		PageIndex: int(page),
		PageSize:  int(pageSize),
		Sortor:    sortFields,
	}

	extension.DBRepository.FindByPaginationWithoutCount(ctx, collection, pageCond, documents)
}

func (BaseModel) GetAllByPaginationWithoutCountWithHint(ctx context.Context, condition bson.M, page uint32, pageSize uint32, orderbys []string, collection string, hint string, documents interface{}) {
	sortFields := util.NormalizeOrderBy(orderbys)
	pageCond := extension.PagingCondition{
		Selector:  condition,
		PageIndex: int(page),
		PageSize:  int(pageSize),
		Sortor:    sortFields,
	}
	extension.DBRepository.FindByPaginationWithoutCountWithHint(ctx, collection, pageCond, hint, documents)
}

func (BaseModel) GetByPaginationWithFields(ctx context.Context, condition bson.M, page uint32, pageSize uint32, collection string, documents interface{}, fields bson.M) int {
	pageCond := extension.PagingCondition{
		Selector:  condition,
		PageIndex: int(page),
		PageSize:  int(pageSize),
	}

	totalCount, _ := extension.DBRepository.FindByPaginationWithFields(ctx, collection, pageCond, documents, fields)

	return totalCount
}

func (BaseModel) GetAllByCondition(ctx context.Context, condition bson.M, orderbys []string, limit int, collection string, documents interface{}) (int, error) {
	var (
		sortFields []string
	)

	if orderbys != nil {
		sortFields = util.NormalizeOrderBy(orderbys)
	}

	err := extension.DBRepository.FindAll(ctx, collection, condition, sortFields, limit, documents)
	if err != nil {
		return 0, err
	}

	return reflect.ValueOf(documents).Elem().Len(), nil
}

func (BaseModel) GetAllByConditionWithFields(ctx context.Context, condition, fields bson.M, orderbys []string, limit int, collection string, documents interface{}) (int, error) {
	var (
		sortFields []string
	)

	if orderbys != nil {
		sortFields = util.NormalizeOrderBy(orderbys)
	}

	err := extension.DBRepository.FindAllWithFields(ctx, collection, condition, fields, sortFields, limit, documents)
	if err != nil {
		return 0, err
	}

	return reflect.ValueOf(documents).Elem().Len(), nil
}

func (BaseModel) GetOneByCondition(ctx context.Context, condition bson.M, collection string, document interface{}) error {
	condition["accountId"] = util.GetAccountIdAsObjectId(ctx)

	return extension.DBRepository.FindOne(ctx, collection, condition, document)
}

func (BaseModel) GetById(ctx context.Context, id, accountId bson.ObjectId, isDeleted, collection string, document interface{}) error {
	condition := bson.M{
		"_id":       id,
		"accountId": accountId,
	}

	if isDeleted != "" {
		deleted, _ := strconv.ParseBool(isDeleted)
		condition["isDeleted"] = deleted
	}

	return extension.DBRepository.FindOne(ctx, collection, condition, document)
}

// GetByIdWithDefault will get a model object by id with the default condition
// The default condition is accountId(from ctx) and isDeleted(false)
func (BaseModel) GetByIdWithDefault(ctx context.Context, id bson.ObjectId, collection string, document interface{}) error {
	condition := bson.M{
		"_id":       id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}

	return extension.DBRepository.FindOne(ctx, collection, condition, document)
}

func (BaseModel) GetByIds(ctx context.Context, ids []bson.ObjectId, accountId bson.ObjectId, isDeleted, collection string, documents interface{}) (int, error) {
	condition := bson.M{
		"_id":       bson.M{"$in": ids},
		"accountId": accountId,
	}

	if isDeleted != "" {
		deleted, _ := strconv.ParseBool(isDeleted)
		condition["isDeleted"] = deleted
	}

	return Base.GetAllByCondition(ctx, condition, nil, 0, collection, documents)
}

func (BaseModel) GetByCondition(ctx context.Context, condition bson.M, collection string, document interface{}) error {
	return extension.DBRepository.FindOne(ctx, collection, condition, document)
}

func (BaseModel) FindAndApply(ctx context.Context, condition bson.M, sort []string, change qmgo.Change, collection string, document interface{}) error {
	return extension.DBRepository.FindAndApply(ctx, collection, condition, sort, change, document)
}

func (BaseModel) FindOneWithSortor(ctx context.Context, condition bson.M, sort []string, collection string, document interface{}) error {
	return extension.DBRepository.FindOneWithSortor(ctx, collection, condition, sort, document)
}

func (BaseModel) UpdateAll(ctx context.Context, collection, isDeleted string, selector, updater bson.M) (int, error) {
	accountId := util.GetAccountId(ctx)
	selector["accountId"] = bson.ObjectIdHex(accountId)

	if isDeleted != "" {
		deleted, _ := strconv.ParseBool(isDeleted)
		selector["isDeleted"] = deleted
	}

	if _, ok := updater["$set"]; ok {
		updater["$set"].(bson.M)["updatedAt"] = time.Now()
	} else {
		updater["$set"] = bson.M{"updatedAt": time.Now()}
	}

	return extension.DBRepository.UpdateAll(ctx, collection, selector, updater)
}

func (BaseModel) GenDefaultCondition(ctx context.Context) bson.M {
	return bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
}

func (BaseModel) GenDefaultConditionById(ctx context.Context, id bson.ObjectId) bson.M {
	condition := Base.GenDefaultCondition(ctx)
	condition["_id"] = id

	return condition
}

func (BaseModel) UpdateOne(ctx context.Context, collection string, isDeleted string, selector, updater bson.M) error {
	selector["accountId"] = util.GetAccountIdAsObjectId(ctx)
	if isDeleted != "" {
		deleted, _ := strconv.ParseBool(isDeleted)
		selector["isDeleted"] = deleted
	}

	if _, ok := updater["$set"]; ok {
		updater["$set"].(bson.M)["updatedAt"] = time.Now()
	} else {
		updater["$set"] = bson.M{"updatedAt": time.Now()}
	}

	return extension.DBRepository.UpdateOne(ctx, collection, selector, updater)
}

func (BaseModel) RemoveById(ctx context.Context, collection, id string) error {
	return extension.DBRepository.RemoveOne(ctx, collection, bson.M{"_id": bson.ObjectIdHex(id)})
}

func (BaseModel) CountByCondition(ctx context.Context, collection string, condition bson.M) uint64 {
	count, _ := extension.DBRepository.Count(ctx, collection, condition)
	return uint64(count)
}

func (BaseModel) Exist(ctx context.Context, collection string, condition bson.M) bool {
	err := extension.DBRepository.FindOne(ctx, collection, condition, bson.M{})
	return err != bson.ErrNotFound
}
