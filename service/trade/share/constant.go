package share

// model.order
const (
	// 接入渠道
	//
	// 对应 tradeStore type 字段，不同渠道的订单可能会有多个平台，以下仅为后台已支持的渠道
	ORDER_FROM_WDT        = "wdt"
	ORDER_FROM_TMALL      = "tmall"
	ORDER_FROM_YOUZAN     = "youzan"
	ORDER_FROM_JD         = "jd"
	ORDER_FROM_DMSSTORE   = "dmsstore"
	ORDER_FROM_DOUDIAN    = "doudian"
	ORDER_FROM_DOUYINLIFE = "douyinlife"
	ORDER_FROM_RETAIL     = "mai-retail"
	ORDER_FROM_MICROMALL  = "micromall"
	ORDER_FROM_MEITUAN    = "meituan"
	ORDER_FROM_WESHOP     = "weshop"

	// 订单提供商
	//
	// 对应 tradeStore provider 字段，订单数据可能同步自不同的提供商
	ORDER_PROVIDER_MEITUAN_SHANGOU = "meituan:shangou" // 美团闪购

	// 订单状态
	ORDER_STATUS_WAIT_BUYER_PAY           = "waitBuyerPay"          // 待付款
	ORDER_STATUS_PARTIAL_PAID             = "partialPaid"           // 部分打款
	ORDER_STATUS_WAIT_SELLER_SEND_GOODS   = "waitSellerSendGoods"   // 待发货
	ORDER_STATUS_ABNORMAL_SHIPMENT        = "abnormalShipment"      // 异常发货
	ORDER_STATUS_WAIT_BUYER_CONFIRM_GOODS = "waitBuyerConfirmGoods" // 已发货
	ORDER_STATUS_TRADE_BUYER_SIGNED       = "tradeBuyerSigned"      // 已完成
	ORDER_STATUS_TRADE_CLOSED             = "tradeClosed"           // 已关闭

	// 支付方式
	ORDER_PAYMENT_TYPE_WECHATPAY      = "wechatpay"      // 微信支付
	ORDER_PAYMENT_TYPE_ALIPAY         = "alipay"         // 支付宝支付
	ORDER_PAYMENT_TYPE_ONLINEPAY      = "onlinepay"      // 在线支付
	ORDER_PAYMENT_TYPE_PEERPAY        = "peerpay"        // 找人代付
	ORDER_PAYMENT_TYPE_CODPAY         = "codpay"         // 货到付款
	ORDER_PAYMENT_TYPE_PREPAIDCARDPAY = "prepaidcardpay" // 礼品卡支付
	ORDER_PAYMENT_TYPE_OFFLINE        = "offline"        // 线下支付
	ORDER_PAYMENT_TYPE_BANK_TRANSFER  = "bankTransfer"   // 银行转账
	ORDER_PAYMENT_TYPE_OTHERS         = "others"         // 其它

	// 商品售后状态
	ORDER_AFTER_SALE_STATUS_REFUNDING                        = "refunding"                        // 退款中
	ORDER_AFTER_SALE_STATUS_REFUNDED                         = "refunded"                         // 已退款
	ORDER_AFTER_SALE_STATUS_CANCELED                         = "canceled"                         // 退款已取消
	ORDER_AFTER_SALE_STATUS_FAILED                           = "failed"                           // 退款失败
	ORDER_AFTER_SALE_STATUS_REJECTED                         = "rejected"                         // 拒绝退款
	ORDER_AFTER_SALE_STATUS_CLOSED                           = "closed"                           // 退款关闭
	ORDER_AFTER_SALE_STATUS_SUCCESS                          = "success"                          // 退款成功
	ORDER_AFTER_SALE_STATUS_WAIT_SELLER_AGREE                = "wait_seller_agree"                // 买家已申请退款，等待卖家同意
	ORDER_AFTER_SALE_STATUS_WAIT_BUYER_RETURN_GOODS          = "wait_buyer_return_goods"          // 卖家已经同意退款，等待买家退货
	ORDER_AFTER_SALE_STATUS_WAIT_SELLER_CONFIRM_GOODS        = "wait_seller_confirm_goods"        // 买家已经退货，等待卖家确认收货
	ORDER_AFTER_SALE_STATUS_SELLER_REFUSE_BUYER              = "seller_refuse_buyer"              // 卖家拒绝退款
	ORDER_AFTER_SALE_STATUS_SELLER_REFUSE_BUYER_RETURN_GOODS = "seller_refuse_buyer_return_goods" // 卖家未收到货，拒绝退款

	// 退款状态
	//
	// 目前前端只支持未退款、退款中和已退款两种情况
	REFUND_STATUS_REFUNDING        = "refunding"       // 退款中
	REFUND_STATUS_PARTIAL_REFUNDED = "partialRefunded" // 部分退款
	REFUND_STATUS_REFUNDED         = "refunded"        // 已退款
	REFUND_STATUS_EXCHANGING       = "exchanging"      // 换货中
	REFUND_STATUS_EXCHANGED        = "exchanged"       // 已换货
	REFUND_STATUS_CLOSED           = "closed"          // 未付款关闭或手工关闭
	REFUND_STATUS_SUCCESS          = "SUCCESS"         // 退款成功

	// 购买平台
	//
	// 对应 tradeStore platform 字段，不同渠道进来的订单所属的平台
	ORDER_PLATFORM_OTHERS           = "others"            // 其它
	ORDER_PLATFORM_OFFLINE          = "offline"           // 线下
	ORDER_PLATFORM_YOUZAN           = "youzan"            // 有赞
	ORDER_PLATFORM_TMALL            = "tmall"             // 天猫
	ORDER_PLATFORM_ORIGIN_JD        = "jd"                // 京东
	ORDER_PLATFORM_MEITUAN          = "meituan"           // 美团
	ORDER_PLATFORM_WESHOP           = "weshop"            // 微信小店
	ORDER_PLATFORM_TAOBAO           = "wdt:taobao"        // 淘宝
	ORDER_PLATFORM_FENXIAO          = "wdt:fenxiao"       // 淘宝分销
	ORDER_PLATFORM_JD               = "wdt:jd"            // 京东
	ORDER_PLATFORM_PAIPAI           = "wdt:paipai"        // 拍拍
	ORDER_PLATFORM_AMAZON           = "wdt:amazon"        // 亚马逊
	ORDER_PLATFORM_YHD              = "wdt:yhd"           // 1号店
	ORDER_PLATFORM_DANGDANG         = "wdt:dangdang"      // 当当
	ORDER_PLATFORM_COO8             = "wdt:coo8"          // 库巴
	ORDER_PLATFORM_ALIBABA          = "wdt:alibaba"       // 阿里巴巴
	ORDER_PLATFORM_ECSHOP           = "wdt:ecshop"        // ECShop
	ORDER_PLATFORM_MECOXLANE        = "wdt:mecoxlane"     // 麦考林
	ORDER_PLATFORM_VPLUS            = "wdt:vplus"         // V+
	ORDER_PLATFROM_SUNING           = "wdt:suning"        // 苏宁
	ORDER_PLATFORM_VIP              = "wdt:vip"           // 唯品会
	ORDER_PLATFORM_YIXUN            = "wdt:yixun"         // 易迅
	ORDER_PLATFORM_JMEI             = "wdt:jmei"          // 聚美优品
	ORDER_PLATFORM_KOUDAITONG       = "wdt:koudaitong"    // 口袋通
	ORDER_PLATFORM_HISHOP           = "wdt:hishop"        // Hishop
	ORDER_PLATFORM_VPUBAO           = "wdt:vpubao"        // 微铺宝
	ORDER_PLATFORM_MEILISHUO        = "wdt:meilishuo"     // 美丽说
	ORDER_PLATFORM_MOGUJIE          = "wdt:mogujie"       // 蘑菇街
	ORDER_PLATFORM_BEIBEI           = "wdt:beibei"        // 贝贝网
	ORDER_PLATFORM_ECSTORE          = "wdt:ecstore"       // ECstore
	ORDER_PLATFORM_ZHE800           = "wdt:zhe800"        // 折800
	ORDER_PLATFORM_ICBCMALL         = "wdt:icbcmall"      // 融易购
	ORDER_PLATFORM_ICHUANYI         = "wdt:ichuanyi"      // 穿衣助手
	ORDER_PLATFORM_CHUCHUJIE        = "wdt:chuchujie"     // 楚楚街
	ORDER_PLATFORM_WMWP             = "wdt:wmwp"          // 微盟旺店
	ORDER_PLATFORM_JUANPI           = "wdt:juanpi"        // 卷皮网
	ORDER_PLATFORM_SFHEY            = "wdt:sfhei"         // 顺丰嘿客
	ORDER_PLATFORM_FEINIU           = "wdt:feiniu"        // 飞牛网
	ORDER_PLATFORM_WEIdIAN          = "wdt:weidian"       // 微店
	ORDER_PLATFORM_BAIdUMALL        = "wdt:baidumall"     // 百度mall
	ORDER_PLATFORM_MIA              = "wdt:mia"           // 蜜芽宝贝
	ORDER_PLATFORM_VIPLADY          = "wdt:viplady"       // 明星衣橱
	ORDER_PLATFORM_BUYCCB           = "wdt:buyccb"        // 善融商城
	ORDER_PLATFORM_ALIEXPRESS       = "wdt:aliexpress"    // 速卖通
	ORDER_PLATFORM_MENGDIAN         = "wdt:mengdian"      // 萌店
	ORDER_PLATFORM_PINDUODUO        = "wdt:pinduoduo"     // 拼多多
	ORDER_PLATFORM_JDDJ             = "wdt:jddj"          // 京东到家
	ORDER_PLATFORM_BAIdUWAIMAI      = "wdt:baiduwaimai"   // 百度外卖
	ORDER_PLATFORM_MEITUANWAIMAI    = "wdt:meituanwaimai" // 美团外卖
	ORDER_PLATFORM_DIANPING         = "wdt:dianping"      // 大众点评
	ORDER_PLATFORM_KOUBEIWAIMAI     = "wdt:koubeiwaimai"  // 口碑外卖
	ORDER_PLATFORM_ELE              = "wdt:ele"           // 饿了么
	ORDER_PLATFORM_WOMAI            = "wdt:womai"         // 我买
	ORDER_PLATFORM_RRD              = "wdt:rrd"           // 人人店
	ORDER_PLATFORM_MEITUN           = "wdt:meitun"        // 美囤妈妈
	ORDER_PLATFORM_91PINTUAN        = "wdt:91pintuan"     // 91拼团
	ORDER_PLATFORM_KAOLA            = "wdt:kaola"         // 网易考拉海购
	ORDER_PLATFORM_QIANMI           = "wdt:qianmi"        // 千米网
	ORDER_PLATFORM_TESHEHUI         = "wdt:teshehui"      // 特奢汇
	ORDER_PLATFORM_CHUCHUJIEPT      = "wdt:chuchujiept"   // 楚楚街拼团
	ORDER_PLATFORM_TMALLQYG         = "wdt:tmallqyg"      // 天猫企业购
	ORDER_PLATFORM_HAIZIWANG        = "wdt:haiziwang"     // 孩子王
	ORDER_PLATFORM_XIAOHONGSHU      = "wdt:xiaohongshu"   // 小红书
	ORDER_PLATFORM_GEGEJIA          = "wdt:gegejia"       // 格格家
	ORDER_PLATFORM_YUNJINET         = "wdt:yunjinet"      // 云集
	ORDER_PLATFORM_CHUCHUTONG       = "wdt:chuchutong"    // 楚楚通
	ORDER_PLATFORM_FANLI            = "wdt:fanli"         // 返利网
	ORDER_PLATFORM_JIUXIAN          = "wdt:jiuxian"       // 酒仙网
	ORDER_PLATFORM_JK               = "wdt:jk"            // 平安好医生
	ORDER_PLATFORM_XIACHUFANG       = "wdt:xiachufang"    // 下厨房
	ORDER_PLATFORM_HAOSHIQI         = "wdt:haoshiqi"      // 好食期
	ORDER_PLATFORM_DAVDAIN          = "wdt:davdian"       // 大V店
	ORDER_PLATFORM_HAOYIKU          = "wdt:haoyiku"       // 好衣库
	ORDER_PLATFORM_DALINGJIA        = "wdt:dalingjia"     // 达令家
	ORDER_PLATFORM_AIKUCUN          = "wdt:aikucun"       // 爱库存
	ORDER_PLATFORM_FANGXINGOU       = "wdt:fangxingou"    // 放心购
	ORDER_PLATFORM_MEIRIYITAO       = "wdt:meiriyitao"    // 每日一淘
	ORDER_PLATFORM_BEIKEYOUPIN      = "wdt:beikeyoupin"   // 贝壳优品
	ORDER_PLATFORM_XIAOMIYOUPIN     = "wdt:xiaomiyoupin"  // 小米有品
	ORDER_PLATFORM_WEILAIJISHI      = "wdt:weilaijishi"   // 未来集市
	ORDER_PLATFORM_AIQIYISC         = "wdt:aiqiyisc"      // 爱奇艺商城
	ORDER_PLATFORM_KUAISHOUXD       = "wdt:kuaishouxd"    // 快手小店
	ORDER_PLATFORM_MOKUAIXX         = "wdt:mokuaixx"      // 魔筷星选
	ORDER_PLATFORM_HAIPAIKE         = "wdt:haipaike"      // 海拍客
	ORDER_PLATFORM_YIQIANBAO        = "wdt:yiqianbao"     // 壹钱包
	ORDER_PLATFORM_MEIRIYOUXIAN     = "wdt:meiriyouxian"  // 每日优鲜
	ORDER_PLATFORM_WANGYIYX         = "wdt:wangyiyx"      // 网易严选
	ORDER_PLATFORM_DUODIAN          = "wdt:duodian"       // 多点
	ORDER_PLATFORM_JIAOLANJR        = "wdt:jiaolanjr"     // 娇兰佳人
	ORDER_PLATFORM_JINRIBAOTUAN     = "wdt:jinribaotuan"  // 今日爆团
	ORDER_PLATFORM_ALIJKDYF         = "wdt:alijkdyf"      // 阿里健康大药房
	ORDER_PLATFORM_CUSTOMIZED       = "wdt:customized"    // 定制
	ORDER_PLATFORM_DOUDIAN          = "doudian"           // 抖店
	ORDER_PLATFORM_DOUYINLIFE       = "douyinlife"        // 抖音本地生活
	ORDER_PLATFORM_ORIGIN_JD_DAOJIA = "jd:daojia"         // 京东到家
	ORDER_PLATFORM_HUALALA          = "hualala"           // 哗啦啦
)

// model.setting
const (
	SYNC_MODE_AUTO   = "auto"   // 店铺同步方式: 自动
	SYNC_MODE_MANUAL = "manual" // 店铺同步方式: 手动
)

const (
	LAYOUT_BY_YEAR   = "2006"
	LAYOUT_BY_DAY    = "2006-1-2"
	LAYOUT_BY_MINUTE = "2006-01-02 15:04:05"
)

// model.order_sync_history
const (
	TYPE_AUTO    = "AUTO"
	TYPE_MANUAL  = "MANUAL"
	TYPE_CMD     = "CMD" // 通过 sre 后台执行命令的同步，仅做一些逻辑处理，不存入数据库，执行 MairpcCommand 时传给 type 字段
	TYPE_PARTNER = "PARTNER"

	STATUS_SYNCHRONIZING      = "SYNCHRONIZING"      // 同步中
	STATUS_FINISH_WITH_FAILED = "FINISH_WITH_FAILED" // 同步完成但其中含有同步失败的数据
	STATUS_FINISH             = "FINISH"             // 同步成功
	STATUS_ERROR              = "ERROR"              // 状态为`同步中`持续时间为半个小时以上时为此状态
)

// model.memberLevelHistory
const (
	LEVEL_TYPE_REMAIN     = "remain"    // 保级
	LEVEL_TYPE_LEVEL_UP   = "levelUp"   // 升级
	LEVEL_TYPE_LEVEL_DOWN = "levelDown" // 降级

	LEVEL_RULE_GROWTH = "growth" // 会员成长模式按互动行为
	LEVEL_RULE_ORDER  = "order"  // 会员成长模式按消费行为

	ORDER_RULE_RANGE_TYPE_REENT          = "recent"
	ORDER_RULE_RANGE_TYPE_LEVEL_START_AT = "levelStartAt"
)

// upsert 订单接口 v1 版本为 PHP
const (
	UPSERT_ORDER_EDITION_V2 = "v2"
	UPSERT_ORDER_EDITION_V3 = "v3"
)
