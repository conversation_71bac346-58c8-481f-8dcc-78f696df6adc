package member

import (
	"encoding/json"
	"fmt"
	"mairpc/core/errors"
	ext "mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/algorithm"
	pb_member "mairpc/proto/member"
	pb_store "mairpc/proto/trade/store"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"
	trade_client "mairpc/service/trade/client"
	model "mairpc/service/trade/model/member"
	"mairpc/service/trade/model/order"
	"mairpc/service/trade/share"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/spf13/cast"
	"github.com/spf13/cobra"
	"golang.org/x/net/context"
)

const (
	ORDER_RULE_RANGE_TYPE_REENT          = "recent"
	ORDER_RULE_RANGE_TYPE_LEVEL_START_AT = "levelStartAt"

	ORDER_RUE_ORIGIN_TYPE_ALL       = "all"
	ORDER_RUE_ORIGIN_TYPE_PLANTFORM = "platform"
	ORDER_RUE_ORIGIN_TYPE_STORE     = "store"
)

func init() {
	MemberCmd.AddCommand(calculateMemberLevel)
}

var calculateMemberLevel = &cobra.Command{
	Use: "calculateMemberLevel",
	RunE: func(cmd *cobra.Command, args []string) error {
		ctx := core_util.CtxWithRequestID(context.Background(), util.GetReqIdFromArgs(args))
		var wg sync.WaitGroup
		pool, err := util.NewGoroutinePoolWithPanicHandler(5)
		if err != nil {
			log.Error(ctx, "make pool failed", log.Fields{"err": fmt.Sprintf("%v", err)})
			return err
		}
		defer pool.Release()

		jobOptions := util.GetArgs(args)
		argsMap, err := getReqArgs(ctx, jobOptions["request"])
		if err != nil {
			return err
		}

		var accountIds []string
		if aids, ok := argsMap["accountIds"]; ok {
			accountIds = core_util.ToStringArray(aids)
		}

		util.ExecActivatedAccountsIterative(ctx, nil, func(ctx context.Context) error {
			wg.Add(1)
			pool.Submit(func() {
				defer wg.Done()
				if len(accountIds) > 0 && !core_util.ContainsString(&accountIds, util.GetAccountId(ctx)) {
					return
				}

				// https://gitlab.maiscrm.com/mai/impl/dhc/dhc-module/-/issues/167#note_4088239
				if util.GetAccountId(ctx) == "62ff2c05265ee254775f2212" && !(time.Now().Hour() >= 0 && time.Now().Hour() < 6) {
					return
				}

				errs := CalculateMemberLevelByAccount(ctx, GenAccountCalculator(ctx, nil, nil, nil, nil))
				log.Warn(ctx, "Calculate account finished.", log.Fields{
					"errors": errs,
				})
			})
			return nil
		})
		wg.Wait()
		return nil
	},
}

func getReqArgs(ctx context.Context, request interface{}) (map[string]interface{}, error) {
	argsMap := map[string]interface{}{}
	byteData, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(byteData, &argsMap)
	if err != nil {
		return nil, err
	}
	return argsMap, nil
}

/*
此 job 仅对按交易的会员成长模式的租户执行

客户每次下单会 upsert 一条 tradeMemberLevelStatus 记录（状态为 pending），job 每指定小时跑一次，遍历所有 pending 的 tradeMemberLevelStatus。

对于每个 tradeMemberLevelStatus，会查询上次 tradeMemberLevelStatusLog，以该 log 的 createdAt 作为起始时间（如果不存在，则以等级开始时间为起始时间），以 job 运行时间为结束时间

- 查询出起始时间到等级开始时间的所有退款单，找出这些退款单对应的订单，找到其中最早的一笔订单：
  - 如果最早的订单在等级开始时间之前，说明当期退款了一笔升级前的订单，有可能客户的当前等级已无法维持，需要找到该订单参与了哪次升级，从这次升级的原始等级开始重放后续订单的升级情况
  - 如果最早的订单在等级开始之后，则正常处理订单数值的计算，是否能升级

- 如果无退款单，则正常计算
*/

type TradeStats struct {
	trade  order.Trade
	amount int64
}

func (*TradeStats) Less(i, j interface{}) bool {
	return i.(*TradeStats).trade.OrderCompleteTime.Before(j.(*TradeStats).trade.OrderCompleteTime)
}

func (*TradeStats) Equal(i, j interface{}) bool {
	return i.(*TradeStats).trade.OrderId == j.(*TradeStats).trade.OrderId
}

func (t *TradeStats) Empty() bool {
	return t.trade.OrderId == ""
}

func CalculateMemberLevelByAccount(ctx context.Context, calculator *Calculator) (errs []error) {
	if !calculator.ValidateLevelSetting(ctx) {
		return
	}

	var (
		memberLevelStatus = &model.TradeMemberLevelStatus{}
	)

	it := calculator.GetIterator(ctx)
	defer it.Close()
	for it.Next(memberLevelStatus) {
		_ = memberLevelStatus.Process(ctx)

		statusLog, err := calculator.Calculate(ctx, memberLevelStatus)
		if err != nil {
			log.Error(ctx, "Iterate trade member level status failed.", log.Fields{
				"memberId": memberLevelStatus.MemberId.Hex(),
				"err":      err.Error(),
			})
			errs = append(errs, err)
		}

		_ = statusLog.Create(ctx)
		_ = memberLevelStatus.Done(ctx)
	}
	return
}

type Calculator struct {
	levelSetting      *pb_member.MemberLevelSetting
	platformStores    map[string]*pb_store.SimpleStoreList
	iterator          *ext.IterWrapper
	memberLevelMapper map[uint64]*pb_member.MemberLevel
}

func GenAccountCalculator(ctx context.Context, levelSetting *pb_member.MemberLevelSetting, platformStores map[string]*pb_store.SimpleStoreList, iterator *ext.IterWrapper, memberLevelMapper map[uint64]*pb_member.MemberLevel) *Calculator {
	calculator := &Calculator{}
	if levelSetting != nil {
		calculator.levelSetting = levelSetting
	}
	if platformStores != nil {
		calculator.platformStores = platformStores
	}
	if iterator != nil {
		calculator.iterator = iterator
	}
	if memberLevelMapper != nil {
		calculator.memberLevelMapper = memberLevelMapper
	}
	return calculator
}

func (c *Calculator) GetMemberLevelMapper(ctx context.Context) map[uint64]*pb_member.MemberLevel {
	if c.memberLevelMapper == nil {
		resp, err := share.ListMemberLevels(ctx)
		if err != nil {
			return nil
		}
		c.memberLevelMapper = core_util.MakeMapperV2("Level", uint64(0), resp.Items)
	}
	return c.memberLevelMapper
}

func (c *Calculator) GetLevelSetting(ctx context.Context) *pb_member.MemberLevelSetting {
	if c.levelSetting == nil {
		levelSetting, err := share.GetMemberLevelSetting(ctx)
		if err != nil {
			// 等级设置不存在
			return nil
		}
		c.levelSetting = levelSetting
	}

	return c.levelSetting
}

func (c *Calculator) GetPlatformStores(ctx context.Context) map[string]*pb_store.SimpleStoreList {
	if c.platformStores == nil {
		resp, err := trade_client.StoreService.ListPlatforms(ctx, &pb_store.ListPlatformsRequest{})
		if err != nil {
			return nil
		}
		c.platformStores = resp.Platforms
	}

	return c.platformStores
}

func (c *Calculator) GetIterator(ctx context.Context) ext.IterWrapper {
	if c.iterator == nil {
		prePareSuccess := c.prePareIterator(ctx)
		if !prePareSuccess {
			return emptyIter{}
		}
	}

	return *c.iterator
}

func (c *Calculator) prePareIterator(ctx context.Context) bool {
	if c.iterator == nil {
		it, err := ext.DBRepository.Iterate(ctx, model.C_TRADE_MEMBER_LEVEL_STATUS, bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"status":    model.TRADE_MEMBER_LEVEL_STATUS_PENDING,
			"createdAt": bson.M{"$lte": time.Now()},
		}, []string{"_id"})
		if err != nil {
			return false
		} else {
			c.iterator = &it
		}
	}
	return true
}

func (c *Calculator) ValidateLevelSetting(ctx context.Context) bool {
	if c.GetLevelSetting(ctx) == nil {
		return false
	}

	if c.GetLevelSetting(ctx).LevelRule == share.LEVEL_RULE_ORDER {
		return true
	}

	return false
}

func (c *Calculator) Calculate(ctx context.Context, memberLevelStatus *model.TradeMemberLevelStatus) (statusLog model.TradeMemberLevelStatusLog, err error) {
	defer func() {
		if r := recover(); r != nil {
			stack := make([]byte, log.MaxStackSize)
			stack = stack[:runtime.Stack(stack, false)]
			log.ErrorTrace(ctx, "Panic when calculate member level", log.Fields{
				"memberId": memberLevelStatus.MemberId.Hex(),
			}, stack)
			err = errors.ConvertRecoveryError(r)
		}
		if err != nil {
			statusLog.ErrMsg = err.Error()
		}
	}()

	statusLog = model.TradeMemberLevelStatusLog{
		MemberId: memberLevelStatus.MemberId,
	}

	member := memberLevelStatus.GetMember(ctx)
	if member == nil || !member.IsActivated {
		// 客户不存在或者未激活，不计算等级
		statusLog.ErrMsg = "member do not exist or are not activated."
		return statusLog, nil
	}

	statusLog.OriginLevel = int64(member.Level)
	statusLog.Level = int64(member.Level)

	// 获取计算等级的起点，非空时说明有非当前等级起点
	// TODO 记录有效订单到 statusLog 中
	levelFrom := c.CalculateLevelFrom(ctx, memberLevelStatus)
	if levelFrom != nil {
		log.Warn(ctx, "need recalculate level", log.Fields{
			"levelFrom":      levelFrom.retryLevel,
			"levelCreatedAt": levelFrom.retryTime,
			"memberId":       member.Id,
		})
		err = c.levelUpSlow(ctx, memberLevelStatus, &statusLog, levelFrom)
	} else {
		// 等级累加直升
		err = c.levelUp(ctx, memberLevelStatus, &statusLog)
	}

	if err != nil {
		statusLog.ErrMsg = err.Error()
	}

	return statusLog, err
}

type levelHistory struct {
	retryTime  time.Time
	retryLevel uint64
}

func (c *Calculator) CalculateLevelFrom(ctx context.Context, memberLevelStatus *model.TradeMemberLevelStatus) *levelHistory {
	var (
		levelStartedAt time.Time
		levelFrom      *levelHistory
	)

	levelStartedAt, _ = util.TransStrToTime(memberLevelStatus.GetMember(ctx).LevelStartedAt)
	// 获取当前等级开始后的退款单中对应的最早的一笔订单
	levelFrom, err := c.getEarliestValidRefundTradeLevel(ctx, memberLevelStatus.MemberId.Hex(), levelStartedAt)
	if err != nil {
		return nil
	}
	return levelFrom
}

// 从指定等级开始按订单重放重新计算等级
func (c *Calculator) levelUpSlow(ctx context.Context, memberLevelStatus *model.TradeMemberLevelStatus, statusLog *model.TradeMemberLevelStatusLog, levelFrom *levelHistory) error {
	var (
		realLevelStartedAt time.Time
		member             = memberLevelStatus.GetMember(ctx)
		err                error
	)

	// 重设 member 等级，如果客户仅退款，可能会降级，此处设置为 levelFrom 的等级
	statusLog.Level = int64(levelFrom.retryLevel)
	if levelFrom.retryLevel < 1 {
		statusLog.Level = 1
	}
	{
		switch c.GetLevelSetting(ctx).OrderRule.Range.Type {
		case ORDER_RULE_RANGE_TYPE_REENT:
			realLevelStartedAt, err = c.reCalculateRecent(ctx, levelFrom.retryTime, member, statusLog)
		case ORDER_RULE_RANGE_TYPE_LEVEL_START_AT:
			realLevelStartedAt, err = c.reCalculateLevelStart(ctx, levelFrom.retryTime, member, statusLog)
		}
		if err != nil {
			return err
		}
	}

	if uint32(statusLog.Level) != member.Level {
		err = share.LevelUpMember(ctx, member.Id, uint32(statusLog.Level), realLevelStartedAt.Local().Format(core_util.RFC3339))
		if err != nil {
			return err
		}
	}

	// 重算等级保级了或者降级了等级起始时间一定不是当前时间，需要单独重设；如果升级，一定超过当年等级的开始时间
	if uint32(statusLog.Level) <= member.Level {
		err = ext.DBRepository.UpdateOne(ctx, "member", bson.M{"_id": bson.ObjectIdHex(member.Id)}, bson.M{"$set": bson.M{"levelStartedAt": realLevelStartedAt}})
		if err != nil {
			return err
		}
	}
	return nil
}

func (c *Calculator) levelUp(ctx context.Context, memberLevelStatus *model.TradeMemberLevelStatus, statusLog *model.TradeMemberLevelStatusLog) error {
	// 做简单升级操作
	// 如果可以维持，计算能否升级
	var (
		member = memberLevelStatus.GetMember(ctx)
	)
	err := c.validateLevelUp(ctx, c.genLevelUpTradeSelector(ctx, member), member, statusLog)
	if err != nil {
		return err
	}

	c.calculateCurrentLevelTradeStats(ctx, statusLog)

	return nil
}

func preOrderTradeStats(queue *algorithm.BSTree[*TradeStats]) (int64, int64, int64) {
	var totalOrder, totalAmount, singleAmount int64
	if queue == nil {
		return totalOrder, totalAmount, singleAmount
	}
	if queue.Left != nil {
		tOrder, tAmount, sAmount := preOrderTradeStats(queue.Left)
		totalOrder += tOrder
		totalAmount += tAmount
		if sAmount > singleAmount {
			singleAmount = sAmount
		}
	}
	{
		trade := queue.Data()
		totalOrder++
		totalAmount += trade.amount
		if trade.amount > singleAmount {
			singleAmount = trade.amount
		}
	}

	if queue.Right != nil {
		tOrder, tAmount, sAmount := preOrderTradeStats(queue.Right)
		totalOrder += tOrder
		totalAmount += tAmount
		if sAmount > singleAmount {
			singleAmount = sAmount
		}
	}

	return totalOrder, totalAmount, singleAmount
}

func canLevelUp(tradeStatusLog *model.TradeMemberLevelStatusLog, levelTradeRule *pb_member.MemberLevelOrderRule) bool {
	return (tradeStatusLog.TotalAmount >= cast.ToInt64(levelTradeRule.TotalAmount) && levelTradeRule.TotalAmount > 0) ||
		(tradeStatusLog.TotalOrder >= cast.ToInt64(levelTradeRule.TotalOrder) && levelTradeRule.TotalOrder > 0) ||
		(tradeStatusLog.SingleAmount >= cast.ToInt64(levelTradeRule.SingleAmount) && levelTradeRule.SingleAmount > 0)
}

// 根据订单创建时间查询等级历史
func getLatestLevelUpLevelByOrderCompleteTime(ctx context.Context, memberId string, orderCompleteTime time.Time) (*levelHistory, error) {
	var (
		page         = uint32(1)
		levelFrom    *levelHistory
		preLevelFrom *pb_member.MemberLevelHistory
	)

	for {
		resp, err := share.ListMemberLevelHistory(ctx, memberId, nil, []string{share.LEVEL_TYPE_LEVEL_UP}, page)
		if err != nil {
			return nil, err
		}
		if len(resp.Items) == 0 {
			break
		}
		preLevelFrom = resp.Items[0]
		for i := range resp.Items {
			levelCreatedAt, _ := time.ParseInLocation(core_util.RFC3339, resp.Items[i].StartedAt, time.Local)
			if levelCreatedAt.Unix()-orderCompleteTime.Unix() < 0 {
				levelFrom = &levelHistory{
					retryTime:  levelCreatedAt,
					retryLevel: preLevelFrom.PreviousLevel,
				}
				goto OUT
			}
			preLevelFrom = resp.Items[i]
		}
		page++
	}
OUT:
	if levelFrom == nil {
		// 客户完成所退订单时无升级历史，此时取一等级重算
		if preLevelFrom == nil {
			return nil, nil
		} else {
			levelCreatedAt, _ := time.ParseInLocation(core_util.RFC3339, preLevelFrom.StartedAt, time.Local)
			return &levelHistory{
				retryTime:  levelCreatedAt,
				retryLevel: preLevelFrom.Level,
			}, nil
		}
	}
	return levelFrom, nil
}

// getEarliestValidRefundTradeLevel 获取等级起始时间后的退款中有效的最早创建订单触发升级时间
func (c *Calculator) getEarliestValidRefundTradeLevel(ctx context.Context, memberId string, levelStartedAt time.Time) (*levelHistory, error) {
	var (
		tradePriorityQueue *algorithm.BSTree[*TradeStats]
		levelFrom          *levelHistory // 最早退款原单触发了哪次升级
		err                error
	)

	// 有退款单时先根据退款单查询
	tradePriorityQueueByRefund, err := c.getEarliestValidRefundByTradeRefund(ctx, memberId, levelStartedAt)
	if err != nil {
		return nil, err
	}

	// 无退款单时根据订单的退款时间查询
	tradePriorityQueueByTrade, err := c.getEarliestValidRefundByTrade(ctx, memberId, levelStartedAt)
	if err != nil {
		return nil, err
	}

	tradePriorityQueue = tradePriorityQueueByRefund.Merge(tradePriorityQueueByTrade)

	// 如果等级后退款了等级开始前的订单，则需要验证是否能维持当前等级
	for tradePriorityQueue != nil {
		miniTrade := tradePriorityQueue.Minimum()
		if tradePriorityQueue.Left == nil {
			miniTrade = tradePriorityQueue
		}
		// 最早退单原单在等级开始之后，则无需判断是否能维持
		if miniTrade == nil || miniTrade.Data().trade.OrderCompleteTime.IsZero() || miniTrade.Data().trade.OrderCompleteTime.Unix() > levelStartedAt.Unix() {
			return nil, nil
		}

		levelFrom, err = getLatestLevelUpLevelByOrderCompleteTime(ctx, memberId, miniTrade.Data().trade.OrderCompleteTime)
		if err != nil {
			return nil, err
		}
		if levelFrom == nil {
			// 这笔在等级起始时间前的退款单原单没有触发升级，可以认为是会员激活前的订单退款，查找下一个有效的退款原单
			tradePriorityQueue = tradePriorityQueue.Remove(miniTrade)
		} else {
			return levelFrom, nil
		}
	}
	return levelFrom, nil
}

// 查询 levelStartedAt 之后产生的退款单，如果最新执行的 log 晚于 levelStartedAt，则查询该 log 之后的退款单，减小计算量
// 计算插入的退款单是否处于起始时间之前，如果在之前说明可能产生降级，查询所有的退款单出发了哪次升级，从该次升级往后重新计算等级
func (c *Calculator) getEarliestValidRefundByTradeRefund(ctx context.Context, memberId string, levelStartedAt time.Time) (*algorithm.BSTree[*TradeStats], error) {
	var (
		tradePriorityQueue *algorithm.BSTree[*TradeStats]
	)
	orderSelector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"memberId":  bson.ObjectIdHex(memberId),
	}
	c.setSelectorOriginBySetting(ctx, orderSelector)

	// 默认查询等级开始后的退款完成订单
	pageCondition := ext.PagingCondition{
		Selector: bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"memberId":  bson.ObjectIdHex(memberId),
			"status": bson.M{
				"$in": order.REFUNDED_STATUS_LIST,
			},
			"createdAt": bson.M{
				"$gte": levelStartedAt,
			},
		},
		PageIndex: 1,
		PageSize:  100,
	}

	resp, err := share.ListMemberLevelHistory(ctx, memberId, nil, []string{share.LEVEL_TYPE_LEVEL_UP}, 1)
	if err != nil {
		return nil, err
	}
	if len(resp.Items) == 0 {
		// 客户无升级记录，认为是历史客户，不考虑降级，提前 return
		return nil, nil
	}

	// 查询最近一次成功的 log 时间，dhc 23.07.01 有过等级变动 暂不做此处理，
	if !core_util.ContainsString(&[]string{"621f00a2278bae5a38124b22", "62ff2c05265ee254775f2212"}, util.GetAccountId(ctx)) {
		logs := []model.TradeMemberLevelStatusLog{}
		err = ext.DBRepository.FindAll(ctx, model.C_TRADE_MEMBER_LEVEL_STATUS_LOG, bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"memberId":  bson.ObjectIdHex(memberId),
			"errMsg":    "",
		}, []string{"-createdAt"}, 1, &logs)
		if err != nil {
			return nil, err
		}

		if len(logs) > 0 {
			if levelStartedAt.Before(logs[0].CreatedAt) {
				pageCondition.Selector["createdAt"] = bson.M{
					"$gte": logs[0].CreatedAt,
				}
			}
		}
	}
	// 查找从起始时间至今的退款单
	for {
		refunds, _, err := order.CTradeRefund.GetAllByCondition(ctx, pageCondition)
		if err != nil {
			return nil, err
		}
		if len(refunds) == 0 {
			break
		}
		for i := range refunds {
			refundTrade := refunds[i].GetRelatedTradeWithSelector(ctx, orderSelector)
			if refundTrade == nil || refundTrade.OrderCompleteTime.IsZero() || refundTrade.IsTmallVirtual() {
				continue
			}
			// 将原单插入 bst
			stats := TradeStats{
				trade: *refundTrade,
			}
			tradePriorityQueue = tradePriorityQueue.Insert(algorithm.NewBSTNode(&stats))
		}
		pageCondition.PageIndex++
	}

	return tradePriorityQueue, nil
}

func (c *Calculator) getEarliestValidRefundByTrade(ctx context.Context, memberId string, levelStartedAt time.Time) (*algorithm.BSTree[*TradeStats], error) {
	var (
		tradePriorityQueue *algorithm.BSTree[*TradeStats]
	)

	// 默认查询等级开始后的退款完成订单
	pageCondition := ext.PagingCondition{
		Selector: bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"memberId":  bson.ObjectIdHex(memberId),
			"status": bson.M{
				"$in": order.REFUNDED_STATUS_LIST,
			},
			"refundTime": bson.M{
				"$gte": levelStartedAt,
			},
		},
		PageIndex: 1,
		PageSize:  100,
	}

	c.setSelectorOriginBySetting(ctx, pageCondition.Selector)

	resp, err := share.ListMemberLevelHistory(ctx, memberId, nil, []string{share.LEVEL_TYPE_LEVEL_UP}, 1)
	if err != nil {
		return nil, err
	}
	if len(resp.Items) == 0 {
		// 客户无升级记录，认为是历史客户，不考虑降级，提前 return
		return nil, nil
	}

	// 查询最近一次成功的 log 时间
	if !core_util.ContainsString(&[]string{"621f00a2278bae5a38124b22", "62ff2c05265ee254775f2212"}, util.GetAccountId(ctx)) {
		logs := []model.TradeMemberLevelStatusLog{}
		err = ext.DBRepository.FindAll(ctx, model.C_TRADE_MEMBER_LEVEL_STATUS_LOG, bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"memberId":  bson.ObjectIdHex(memberId),
			"errMsg":    "",
		}, []string{"-createdAt"}, 1, &logs)
		if err != nil {
			return nil, err
		}

		if len(logs) > 0 {
			pageCondition.Selector["refundTime"] = bson.M{
				"$gte": logs[0].CreatedAt,
			}
		}
	}

	// 查找从起始时间至今的退款单
	for {
		trade, _, err := order.CTrade.GetByPagingCondition(ctx, pageCondition)
		if err != nil {
			return nil, err
		}
		if len(trade) == 0 {
			break
		}
		for i := range trade {
			if trade[i].IsTmallVirtual() {
				continue
			}
			// 将原单插入 bst
			stats := TradeStats{
				trade: trade[i],
			}
			tradePriorityQueue = tradePriorityQueue.Insert(algorithm.NewBSTNode(&stats))
		}
		pageCondition.PageIndex++
	}

	return tradePriorityQueue, nil
}

// 核查是否能升级
func (c *Calculator) validateLevelUp(ctx context.Context, pageCondition ext.PagingCondition, member *pb_member.MemberDetailResponse, statusLog *model.TradeMemberLevelStatusLog) error {
	var (
		mapper                    = c.GetMemberLevelMapper(ctx)
		nextLv                    = cast.ToUint64(member.Level + 1)
		levelUpTradeCompletedTime time.Time
	)

	for {
		trades, _, err := order.CTrade.GetByPagingCondition(ctx, pageCondition)
		if err != nil {
			return err
		}
		if len(trades) == 0 {
			break
		}
		for i := range trades {
			if trades[i].IsTmallVirtual() {
				continue
			}
			refund, err := appendTradeStatsToLog(ctx, trades[i], statusLog)
			if err != nil {
				return err
			}

			if !refund {
				appendValidOrder(ctx, trades[i], statusLog)
			}
			// 每获得一笔订单就开始判断是否能升级，且需要判断所有等级，客户有可能跳级
			var nowTradeLevelUp bool
			for !refund {
				level, exists := mapper[nextLv]
				if !exists {
					break
				}
				if canLevelUp(statusLog, level.OrderRule) {
					nowTradeLevelUp = true
					statusLog.Level = int64(nextLv)
					levelUpTradeCompletedTime = trades[i].OrderCompleteTime
					// 重置
					statusLog.LevelUp(int64(member.Level), statusLog.Level)
				}
				nextLv++
			}
			// 保证下次等级是当前等级的下一等级
			nextLv = uint64(statusLog.Level + 1)
			if nowTradeLevelUp {
				statusLog.ResetByLevelUp(c.GetLevelSetting(ctx).OrderRule.Range.Type)
			}
		}
		pageCondition.PageIndex++
	}

	// 升级
	if statusLog.IsLevelUp() {
		err := share.LevelUpMember(ctx, member.Id, cast.ToUint32(statusLog.Level), levelUpTradeCompletedTime.Local().Format(core_util.RFC3339))
		if err != nil {
			return err
		}
		return nil
	}

	return nil
}

// 生成能否升级的订单查询参数
func (c *Calculator) genLevelUpTradeSelector(ctx context.Context, member *pb_member.MemberDetailResponse) ext.PagingCondition {
	var (
		setting = c.GetLevelSetting(ctx)
	)

	selector := bson.M{
		"accountId":   util.GetAccountIdAsObjectId(ctx),
		"memberId":    bson.ObjectIdHex(member.Id),
		"isDeleted":   false,
		"orderStatus": share.ORDER_STATUS_TRADE_BUYER_SIGNED,
	}
	switch setting.OrderRule.Range.Type {
	case ORDER_RULE_RANGE_TYPE_REENT: // 订单范围按时间范围，则计算起点为当前事件往前推有效月数
		start := time.Now().AddDate(0, -1*cast.ToInt(setting.OrderRule.Range.DateRange.Month), 0)
		selector["orderCompleteTime"] = bson.M{
			"$gte": start,
		}
	case ORDER_RULE_RANGE_TYPE_LEVEL_START_AT: // 订单范围按等级起始开始计算，则计算起点为当前等级开始时间
		start, _ := util.TransStrToTime(member.LevelStartedAt)
		selector["orderCompleteTime"] = bson.M{
			"$gt": start,
		}
	}
	c.setSelectorOriginBySetting(ctx, selector)
	pageCondition := ext.PagingCondition{
		Selector:  selector,
		PageIndex: 1,
		PageSize:  100,
		Sortor:    []string{"orderCompleteTime"},
	}
	return pageCondition
}

func getTradePayAmount(ctx context.Context, trade order.Trade) (int64, bool, error) {
	payAmount := cast.ToInt64(share.Yuan2Fen(trade.ActualAmount))
	isAllRefund := false
	// 有退款
	if trade.RefundStatus != "" {
		// 621f00a2278bae5a38124b22、62ff2c05265ee254775f2212 DHC 租户退款超过七天后忽略 mai/home#45883
		if core_util.ContainsString(&[]string{"621f00a2278bae5a38124b22", "62ff2c05265ee254775f2212"}, util.GetAccountId(ctx)) {
			return getTradePayAmountForDHC(ctx, trade)
		}

		switch trade.OrderPlatform {
		case share.ORDER_FROM_TMALL:
			if trade.ExtraFields != nil {
				if v, ok := trade.ExtraFields.(map[string]any); ok {
					payAmount = cast.ToInt64(v["goodsPayAmount"])
					if payAmount == 0 {
						isAllRefund = true
					}
				}
			}
		default:
			// 退款完成的退款单计算退款金额
			tradeRefunds, err := order.CTradeRefund.GetAll(ctx, bson.M{
				"memberId": trade.MemberId,
				"orderId":  trade.OrderId,
				"status": bson.M{
					"$in": order.REFUNDED_STATUS_LIST,
				},
			}, []string{})
			if err != nil {
				return payAmount, false, err
			}
			var refundAmount = 0.0
			for _, tradeRefund := range tradeRefunds {
				refundAmount += tradeRefund.RefundAmount
			}
			if refundAmount >= trade.ActualAmount {
				refundAmount = trade.ActualAmount
				isAllRefund = true
			}
			payAmount = cast.ToInt64(share.Yuan2Fen(trade.ActualAmount - refundAmount))
		}
	}
	return payAmount, isAllRefund, nil
}

func getTradePayAmountForDHC(ctx context.Context, trade order.Trade) (int64, bool, error) {
	payAmount := cast.ToInt64(share.Yuan2Fen(trade.ActualAmount))
	isAllRefund := false
	switch trade.OrderPlatform {
	case share.ORDER_FROM_TMALL:
		if trade.RefundTime.After(trade.OrderCompleteTime.Add(time.Hour * 7 * 24)) {
			return payAmount, false, nil
		}
		if trade.ExtraFields != nil {
			if v, ok := trade.ExtraFields.(map[string]any); ok {
				payAmount = cast.ToInt64(v["goodsPayAmount"])
				if payAmount == 0 {
					isAllRefund = true
				}
			}
		}
	default:
		tradeRefunds, err := order.CTradeRefund.GetAll(ctx, bson.M{
			"memberId": trade.MemberId,
			"orderId":  trade.OrderId,
		}, []string{"createTime"})
		if err != nil {
			return payAmount, false, err
		}
		if len(tradeRefunds) > 0 {
			var refundAmount = 0.0
			for i := range tradeRefunds {
				if tradeRefunds[i].CreateTime.Before(trade.OrderCompleteTime.Add(time.Hour * 7 * 24)) {
					refundAmount += tradeRefunds[i].RefundAmount
				}
			}
			if refundAmount >= trade.ActualAmount {
				refundAmount = trade.ActualAmount
				isAllRefund = true
			}

			payAmount = cast.ToInt64(share.Yuan2Fen(trade.ActualAmount - refundAmount))
		} else if trade.RefundTime.Before(trade.OrderCompleteTime.Add(time.Hour * 7 * 24)) {
			// DHC 退款无退单的只有 tmall 和官方店，官方店退款只会全退且不修改原单的 payAmount，所以此处超过七天后的
			payAmount = 0
			isAllRefund = true
		}
	}
	return payAmount, isAllRefund, nil
}

func appendTradeStatsToLog(ctx context.Context, trade order.Trade, log *model.TradeMemberLevelStatusLog) (bool, error) {
	tAmount, refund, err := getTradePayAmount(ctx, trade)
	if err != nil {
		return false, err
	}
	if !refund {
		log.AppendSingleTradeAmount(tAmount)
	}

	return refund, nil
}

func appendValidOrder(ctx context.Context, trade order.Trade, log *model.TradeMemberLevelStatusLog) (bool, error) {
	tAmount, refund, err := getTradePayAmount(ctx, trade)
	if err != nil {
		return false, err
	}
	if !refund {
		log.ValidOrders = append(log.ValidOrders, model.ValidOrder{
			Id:                trade.Id,
			OrderId:           trade.OrderId,
			OrderCompleteTime: trade.OrderCreateTime,
			Amount:            tAmount,
		})
	}

	return refund, nil
}

func getTradesTotalAmountAndMaxSingleAmount(ctx context.Context, memberId bson.ObjectId, trades []order.Trade) (int64, int64, int64, error) {
	var totalAmount, totalOrder, singleAmount int64
	for i := range trades {
		payAmount, isAllRefund, err := getTradePayAmount(ctx, trades[i])
		if err != nil {
			return 0, 0, 0, err
		}

		if isAllRefund {
			continue
		}

		if payAmount > 0 && payAmount > singleAmount {
			singleAmount = payAmount
		}
		totalOrder++
		totalAmount += payAmount
	}
	return totalAmount, totalOrder, singleAmount, nil
}

func (c *Calculator) getBasicPaginationByOriginSetting(ctx context.Context, memberId string) ext.PagingCondition {
	pageCondition := ext.PagingCondition{
		Selector: bson.M{
			"accountId":   util.GetAccountIdAsObjectId(ctx),
			"memberId":    bson.ObjectIdHex(memberId),
			"isDeleted":   false,
			"orderStatus": share.ORDER_STATUS_TRADE_BUYER_SIGNED,
		},
		Sortor:    []string{"orderCompleteTime"},
		PageIndex: 1,
		PageSize:  100,
	}
	c.setSelectorOriginBySetting(ctx, pageCondition.Selector)
	return pageCondition
}

func (c *Calculator) setSelectorOriginBySetting(ctx context.Context, selector bson.M) {
	var (
		setting        = c.GetLevelSetting(ctx)
		platformStores = c.GetPlatformStores(ctx)
	)

	switch setting.OrderRule.Origin.Type {
	case ORDER_RUE_ORIGIN_TYPE_ALL:
		// 排除积分商城订单
		if len(platformStores) > 0 {
			selector["orderPlatform"] = bson.M{"$in": getNeedCalPlatformsByNoInPlatforms(platformStores, []string{"micromall"})}
		} else {
			selector["orderPlatform"] = bson.M{"$ne": "micromall"}
		}
	case ORDER_RUE_ORIGIN_TYPE_PLANTFORM:
		platforms := strings.Split(setting.OrderRule.Origin.Rule.Value, ",")
		switch setting.OrderRule.Origin.Rule.Operator {
		case share_model.OPERATOR_IN:
			selector["orderPlatform"] = bson.M{"$in": platforms}
		case share_model.OPERATOR_NIN:
			if len(platformStores) > 0 {
				selector["orderPlatform"] = bson.M{"$in": getNeedCalPlatformsByNoInPlatforms(platformStores, platforms)}
			} else {
				selector["orderPlatform"] = bson.M{"$nin": platforms}
			}
		}
	case ORDER_RUE_ORIGIN_TYPE_STORE:
		stores := strings.Split(setting.OrderRule.Origin.Rule.Value, ",")
		switch setting.OrderRule.Origin.Rule.Operator {
		case share_model.OPERATOR_IN:
			selector["storeId"] = bson.M{"$in": core_util.ToObjectIdArray(stores)}
		case share_model.OPERATOR_NIN:
			if len(platformStores) > 0 {
				selector["storeId"] = bson.M{"$in": core_util.ToObjectIdArray(getNeedCalStoreIdsByNoInStoreIds(platformStores, stores))}
			} else {
				selector["storeId"] = bson.M{"$nin": stores}
			}
		}
	}
}

func getNeedCalPlatformsByNoInPlatforms(platformStores map[string]*pb_store.SimpleStoreList, platforms []string) []string {
	needCalPlatforms := []string{}
	for k := range platformStores {
		platform := k
		if !core_util.ContainsString(&platforms, platform) {
			needCalPlatforms = append(needCalPlatforms, platform)
		}
	}
	return needCalPlatforms
}

func getNeedCalStoreIdsByNoInStoreIds(platformStores map[string]*pb_store.SimpleStoreList, noInStoreIds []string) []string {
	allStoreIds := []string{}
	for k := range platformStores {
		if stores, ok := platformStores[k]; ok {
			allStoreIds = append(allStoreIds, core_util.ToStringArray(core_util.ExtractArrayField("Id", stores.Stores))...)
		}
	}
	_, needCalStoreIds := core_util.GetFieldsInAndNotInElement(allStoreIds, noInStoreIds)
	return core_util.ToStringArray(needCalStoreIds)
}

func (c *Calculator) calculatePastRecent(ctx context.Context, levelStartedAt time.Time, statusLog *model.TradeMemberLevelStatusLog, member *pb_member.MemberDetailResponse) (*algorithm.BSTree[*TradeStats], map[string]int64, error) {
	var (
		setting       = c.GetLevelSetting(ctx)
		pageCondition = c.getBasicPaginationByOriginSetting(ctx, member.Id)
		mapper        = c.GetMemberLevelMapper(ctx)
		nextLv        = uint64(2)
	)

	start := levelStartedAt.AddDate(0, -1*cast.ToInt(setting.OrderRule.Range.DateRange.Month), 0)
	pageCondition.Selector["orderCompleteTime"] = bson.M{
		"$gte": start,
		"$lte": levelStartedAt,
	}

	var (
		tradePriorityQueue *algorithm.BSTree[*TradeStats]
		tradeMapper        = make(map[string]int64)
		levelUp            = false
	)

	// 首次将等级开始前 recent 时间段内的订单累计起来
	for {
		trades, _, err := order.CTrade.GetByPagingCondition(ctx, pageCondition)
		if err != nil {
			return nil, nil, err
		}
		if len(trades) == 0 {
			break
		}
		for i := range trades {
			if trades[i].IsTmallVirtual() {
				continue
			}
			payAmount, isAllRefund, err := getTradePayAmount(ctx, trades[i])
			if err != nil {
				return nil, nil, err
			}

			if isAllRefund {
				continue
			}
			stats := TradeStats{
				trade:  trades[i],
				amount: payAmount,
			}

			tradePriorityQueue = tradePriorityQueue.Insert(algorithm.NewBSTNode(&stats))
			tradeMapper[trades[i].OrderId] = stats.amount

			totalOrder, totalAmount, singleAmount := preOrderTradeStats(tradePriorityQueue)
			statusLog.SetTotalStatsByTrades(totalAmount, totalOrder, singleAmount)

			if levelUp {
				statusLog.AppendLevelSingleTradeAmount(payAmount)
			} else {
				statusLog.SetLevelTotalStatsByTrades(totalAmount, totalOrder, singleAmount)
			}

			for {
				nextLevel, ok := mapper[nextLv]
				if !ok {
					break
				}
				if canLevelUp(statusLog, nextLevel.OrderRule) {
					statusLog.LevelUp(int64(member.Level), int64(nextLevel.Level))
					statusLog.ResetByLevelUp(share.ORDER_RULE_RANGE_TYPE_REENT)
					levelUp = true
				}
				nextLv++
			}
			// 保证下次等级是当前等级的下一等级
			nextLv = uint64(statusLog.Level + 1)
		}
		pageCondition.PageIndex++
	}

	return tradePriorityQueue, tradeMapper, nil
}

func (c *Calculator) reCalculateRecent(ctx context.Context, levelStartedAt time.Time, member *pb_member.MemberDetailResponse, statusLog *model.TradeMemberLevelStatusLog) (time.Time, error) {
	// 第一步：历史订单（重放历史订单时如果未调整过门槛，不可能升级，所以此处不考虑升级）
	// 第二步：追加后续订单，重放
	tradePriorityQueue, tradeMapper, err := c.calculatePastRecent(ctx, levelStartedAt, statusLog, member)
	if err != nil {
		return time.Time{}, err
	}

	var (
		mapper = c.GetMemberLevelMapper(ctx)
		nextLv = uint64(statusLog.Level + 1)
	)

	pageCondition := c.getBasicPaginationByOriginSetting(ctx, member.Id)
	// 从该等级开始按订单遍历
	pageCondition.Selector["orderCompleteTime"] = bson.M{
		"$gt": levelStartedAt,
	}

	for {
		trades, _, err := order.CTrade.GetByPagingCondition(ctx, pageCondition)
		if err != nil {
			return time.Time{}, err
		}
		if len(trades) == 0 {
			break
		}
		for i := range trades {
			if trades[i].IsTmallVirtual() {
				continue
			}
			payAmount, isAllRefund, err := getTradePayAmount(ctx, trades[i])
			if err != nil {
				return time.Time{}, err
			}

			if isAllRefund {
				continue
			}

			stats := TradeStats{
				trade:  trades[i],
				amount: payAmount,
			}

			if tradePriorityQueue.Has(algorithm.NewBSTNode[*TradeStats](&stats)) {
				continue
			}

			statusLog.AppendLevelSingleTradeAmount(payAmount)

			if tradePriorityQueue != nil {
				miniTrade := tradePriorityQueue.Minimum()
				// 最早订单至当前订单时间超过设定范围，需要移除，循环，直到无订单或订单都在设定时间范围内
				for miniTrade != nil && stats.trade.OrderCompleteTime.AddDate(0, -1*cast.ToInt(c.GetLevelSetting(ctx).OrderRule.Range.DateRange.Month), 0).After(miniTrade.Data().trade.OrderCompleteTime) {
					// 移除最早订单相关数据
					tradePriorityQueue = tradePriorityQueue.Remove(miniTrade)
					if tradePriorityQueue == nil {
						break
					}
					miniTrade = tradePriorityQueue.Minimum()
				}
			}

			tradePriorityQueue = tradePriorityQueue.Insert(algorithm.NewBSTNode(&stats))
			tradeMapper[trades[i].OrderId] = stats.amount

			// 判断是否升级
			totalOrder, totalAmount, singleAmount := preOrderTradeStats(tradePriorityQueue)
			statusLog.SetTotalStatsByTrades(totalAmount, totalOrder, singleAmount)

			for {
				nextLevel, ok := mapper[nextLv]
				if !ok {
					break
				}
				if canLevelUp(statusLog, nextLevel.OrderRule) {
					statusLog.LevelUp(int64(member.Level), int64(nextLevel.Level))
					levelStartedAt = trades[i].OrderCompleteTime
					statusLog.ResetByLevelUp(share.ORDER_RULE_RANGE_TYPE_REENT)
				}
				nextLv++
			}
			// 保证下次等级是当前等级的下一等级
			nextLv = uint64(statusLog.Level + 1)
		}
		pageCondition.PageIndex++
	}

	setOrderLists(statusLog, tradePriorityQueue.ToArray())

	return levelStartedAt, nil
}

func setOrderLists(t *model.TradeMemberLevelStatusLog, orders []*TradeStats) *model.TradeMemberLevelStatusLog {
	for i := range orders {
		item := model.ValidOrder{
			Id:                orders[i].trade.Id,
			OrderId:           orders[i].trade.OrderId,
			OrderCompleteTime: orders[i].trade.OrderCompleteTime,
			Amount:            orders[i].amount,
		}
		t.ValidOrders = append(t.ValidOrders, item)
	}
	return t
}

func (c *Calculator) reCalculateLevelStart(ctx context.Context, levelStartedAt time.Time, member *pb_member.MemberDetailResponse, statusLog *model.TradeMemberLevelStatusLog) (time.Time, error) {
	var (
		mapper = c.GetMemberLevelMapper(ctx)
		nextLv = uint64(statusLog.Level + 1)
	)

	pageCondition := c.getBasicPaginationByOriginSetting(ctx, member.Id)
	pageCondition.Selector["orderCompleteTime"] = bson.M{
		"$gte": levelStartedAt,
	}

	for {
		trades, _, err := order.CTrade.GetByPagingCondition(ctx, pageCondition)
		if err != nil {
			return time.Time{}, err
		}
		if len(trades) == 0 {
			break
		}
		for i := range trades {
			if trades[i].IsTmallVirtual() {
				continue
			}
			refund, err := appendTradeStatsToLog(ctx, trades[i], statusLog)
			if err != nil {
				return time.Time{}, err
			}
			if refund {
				continue
			}

			appendValidOrder(ctx, trades[i], statusLog)

			var nowTradeLevelUp bool
			for {
				nextLevel, ok := mapper[nextLv]
				if !ok {
					break
				}
				if canLevelUp(statusLog, nextLevel.OrderRule) {
					// 升级后 init
					statusLog.LevelUp(int64(member.Level), int64(nextLevel.Level))
					nowTradeLevelUp = true
					levelStartedAt = trades[i].OrderCompleteTime
				}
				nextLv++
			}
			// 保证下次等级是当前等级的下一等级
			nextLv = uint64(statusLog.Level + 1)
			if nowTradeLevelUp {
				statusLog.ResetByLevelUp(share.ORDER_RULE_RANGE_TYPE_LEVEL_START_AT)
			}
		}
		pageCondition.PageIndex++
	}

	return levelStartedAt, nil
}

// 计算当前等级开始时间内的订单数据用于计算保级
func (c *Calculator) calculateCurrentLevelTradeStats(ctx context.Context, statusLog *model.TradeMemberLevelStatusLog) {
	var (
		totalAmount, totalOrder, singleAmount = int64(0), int64(0), int64(0)
		selector                              bson.M
		err                                   error
		trades                                []order.Trade
		member                                *pb_member.MemberDetailResponse
	)

	if statusLog.Level <= statusLog.OriginLevel {
		member, err = share.GetMemberById(ctx, statusLog.MemberId, []string{}, "")
		if err != nil {
			log.Warn(ctx, "get member failed.", log.Fields{"memberId": statusLog.MemberId.Hex(), "err": err})
			return
		}
		start, _ := util.TransStrToTime(member.LevelStartedAt)
		selector = bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"memberId":  statusLog.MemberId,
			"isDeleted": false,
			"orderCompleteTime": bson.M{
				"$gt": start,
			},
		}
		c.setSelectorOriginBySetting(ctx, selector)
		pageCondition := ext.PagingCondition{Selector: selector, PageIndex: 1, PageSize: 100}
		for {
			trades, _, err = order.CTrade.GetByPagingCondition(ctx, pageCondition)
			if err != nil {
				break
			}
			temp := make([]order.Trade, 0, len(trades))
			for _, trade := range trades {
				if !trade.IsTmallVirtual() {
					temp = append(temp, trade)
				}
			}
			trades = temp
			if len(trades) == 0 {
				break
			}
			var tAmount, tOrder, sAmount int64
			tAmount, tOrder, sAmount, err = getTradesTotalAmountAndMaxSingleAmount(ctx, statusLog.MemberId, trades)
			if err != nil {
				break
			}
			totalAmount += tAmount
			totalOrder += tOrder
			if sAmount > 0 && sAmount > singleAmount {
				singleAmount = sAmount
			}
			pageCondition.PageIndex++
		}
		statusLog.LevelSingleAmount = singleAmount
		statusLog.LevelTotalAmount = totalAmount
		statusLog.LevelTotalOrder = totalOrder
	}
	if err != nil {
		log.Warn(ctx, "calculate level trade status failed.", log.Fields{"err": err, "memberId": statusLog.MemberId.Hex()})
	}
}

type emptyIter struct{}

func (e emptyIter) Next(result interface{}) bool {
	return false
}

func (e emptyIter) Close() error {
	return nil
}

func (e emptyIter) Err() error {
	return nil
}

func (e emptyIter) All(results interface{}) error {
	return nil
}
