package order

import (
	"context"
	"mairpc/core/client"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/account"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/service/share/util"
	"mairpc/service/trade/model/order"
	"mairpc/service/trade/model/store"
	"mairpc/service/trade/share"
	"sync"
	"time"

	"github.com/panjf2000/ants/v2"
)

// 目前支持的订单接入渠道：有赞，天猫，美团，抖音本地生活，微信小店
var SupportedTypes = []string{share.ORDER_FROM_YOUZAN, share.ORDER_FROM_TMALL, share.ORDER_FROM_MEITUAN, share.ORDER_FROM_DOUYINLIFE, share.ORDER_FROM_WESHOP}

func (OrderService) SyncMonitorJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	nowTime := time.Now()
	pool, err := ants.NewPool(5)
	if err != nil {
		return nil, err
	}
	var wg sync.WaitGroup
	defer pool.Release()
	util.ExecActivatedAccountsIterative(ctx, []string{"trade"}, func(ctx context.Context) error {
		if !CanSyncOrder(ctx, nowTime) {
			return nil
		}
		wg.Add(1)
		pool.Submit(func() {
			defer wg.Done()
			condition := bson.M{
				"accountId": util.GetAccountIdAsObjectId(ctx),
				"isDeleted": false,
				"type":      bson.M{"$in": SupportedTypes},
			}
			it, err := store.CTradeStore.Iterate(ctx, condition, []string{})
			if err != nil {
				log.Warn(ctx, "Failed to iterate tradeStores to sync order", log.Fields{
					"accountId": core_util.GetAccountId(ctx),
				})
				return
			}

			defer it.Close()
			tradeStore := &store.TradeStore{}
			for it.Next(tradeStore) {
				tradeStore.CheckAndModifyStatus(ctx, nowTime)
				if !tradeStore.CanSync(ctx) {
					continue
				}

				if err := tradeStore.SetSyncStatusToSynchronizing(ctx, share.TYPE_AUTO, nowTime); err != nil {
					log.Warn(ctx, "Failed to set tradeStore sync status as synchronizing", log.Fields{
						"accountId":    core_util.GetAccountId(ctx),
						"storeId":      tradeStore.Id.Hex(),
						"storeType":    tradeStore.Type,
						"err":          err.Error(),
						"syncType":     share.TYPE_AUTO,
						"syncTime":     nowTime,
						"syncTimeUnix": nowTime.Unix(),
					})
					continue
				}

				log.Warn(ctx, "Start automatically sync trade for store", log.Fields{
					"accountId":    core_util.GetAccountId(ctx),
					"storeId":      tradeStore.Id.Hex(),
					"storeType":    tradeStore.Type,
					"syncType":     share.TYPE_AUTO,
					"syncTime":     nowTime,
					"syncTimeUnix": nowTime.Unix(),
				})
			}
		})
		wg.Wait()
		return nil
	})

	err = createSyncJob(ctx, nowTime)
	if err != nil {
		return nil, err
	}

	return &response.EmptyResponse{}, nil
}

func CanSyncOrder(ctx context.Context, nowTime time.Time) bool {
	tradeSetting := order.CTradeSetting.GetByCondition(ctx, bson.M{"syncMode": share.SYNC_MODE_AUTO})
	if tradeSetting == nil {
		return false
	}

	if !tradeSetting.CheckExecuteTime(ctx, nowTime) {
		return false
	}
	return true
}

func createSyncJob(ctx context.Context, syncTime time.Time) error {
	args := core_util.MarshalInterfaceToString(map[string]interface{}{
		"type":        share.TYPE_AUTO,
		"endTimeUnix": syncTime.Unix(),
	})

	_, maiErr := client.Run(
		"AccountService.CreateJob",
		ctx,
		&account.CreateJobRequest{
			JobName:      "syncorder",
			Module:       "trade",
			SubModule:    "order",
			FunctionName: "syncOrder",
			Description:  "每次触发同步订单退款单任务创建一个",
			Type:         "sync",
			Args:         args,
			Requests: &account.ResourcesRequests{
				CpuNumber: 0.3,
				MemoryMB:  1024,
			},
		},
	)
	if maiErr != nil {
		log.Warn(ctx, "Error found when create sync trade job", log.Fields{
			"syncType":     share.TYPE_AUTO,
			"err":          maiErr.Error(),
			"syncTime":     syncTime,
			"syncTimeUnix": syncTime.Unix(),
		})
		return maiErr
	}

	return nil
}
