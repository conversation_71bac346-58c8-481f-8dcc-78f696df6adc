package order

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"time"

	"mairpc/core/component/douyinlife"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/core/validators"
	pb_origin "mairpc/proto/common/origin"
	"mairpc/proto/common/types"
	pb_member "mairpc/proto/member"
	member "mairpc/service/member/model"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"
	"mairpc/service/trade/model/store"
	"mairpc/service/trade/share"

	"github.com/spf13/cast"
)

type DouyinlifeOrder struct {
	Context              context.Context
	OrderId              string
	ReceiverPhone        string
	EncryptReceiverPhone string
	ReceiverName         string
	AccountId            bson.ObjectId
	Store                *store.TradeStore
	SyncHistoryId        bson.ObjectId
	Order                *douyinlife.Order
	Member               *pb_member.MemberDetailResponse
	Trade                *Trade
	TempProvider         TempProviderData
}

func (o *DouyinlifeOrder) Init(ctx context.Context, order *douyinlife.Order, tempProvider TempProviderData) error {
	o.Context = ctx
	o.Order = order
	o.OrderId = order.OrderId
	o.AccountId = util.GetAccountIdAsObjectId(ctx)
	o.TempProvider = tempProvider
	o.SyncHistoryId = tempProvider.SyncHistories[1].Id
	o.Store = &tempProvider.Store
	o.Trade = CTrade.GetByCondition(o.Context, bson.M{"orderId": order.OrderId})
	o.setReceiverInfo()
	return nil
}

func (o *DouyinlifeOrder) GetOrderId() string {
	return o.Order.OrderId
}

func (o *DouyinlifeOrder) GetOrderFrom() string {
	return share.ORDER_PLATFORM_DOUYINLIFE
}

func (o *DouyinlifeOrder) GetOrderPlatform() string {
	return share.ORDER_PLATFORM_DOUYINLIFE
}

func (o *DouyinlifeOrder) GetOrderCreateTime() string {
	return util.TransUnixToTime(o.Order.CreateOrderTime).Format(o.GetTimeLayout("orderCreateTime"))
}

func (o *DouyinlifeOrder) GetOrderUpdateTime() string {
	return util.TransUnixToTime(o.Order.UpdateOrderTime).Format(o.GetTimeLayout("orderUpdateTime"))
}

func (o *DouyinlifeOrder) GetOrderCompleteTime() string {
	if o.Trade != nil && !o.Trade.OrderCompleteTime.IsZero() {
		return o.Trade.OrderCompleteTime.Format(o.GetTimeLayout("orderCompleteTime"))
	}
	if o.GetOrderStatus() == share.ORDER_STATUS_TRADE_BUYER_SIGNED {
		return util.TransUnixToTime(o.Order.UpdateOrderTime).Format(o.GetTimeLayout("orderCompleteTime"))
	}
	return ""
}

func (o *DouyinlifeOrder) GetOrderPayTime() string {
	if o.Order.PayTime > 0 {
		return util.TransUnixToTime(o.Order.PayTime).Format(o.GetTimeLayout("orderPayTime"))
	}
	return ""
}

func (o *DouyinlifeOrder) GetOrderStatus() string {
	switch o.Order.OrderStatus {
	case douyinlife.ORDER_STATUS_INIT, douyinlife.ORDER_STATUS_WAIT_PAY:
		return share.ORDER_STATUS_WAIT_BUYER_PAY
	case douyinlife.ORDER_STATUS_PAY_SUCCESS:
		return share.ORDER_STATUS_WAIT_SELLER_SEND_GOODS
	case douyinlife.ORDER_STATUS_AVAILABLE:
		return share.ORDER_STATUS_WAIT_BUYER_CONFIRM_GOODS
	case douyinlife.ORDER_STATUS_FINISH:
		// douyin 退款也是已完成状态，需要处理成已关闭
		if o.getAfterSaleStatus() == share.ORDER_AFTER_SALE_STATUS_REFUNDED {
			return share.ORDER_STATUS_TRADE_CLOSED
		}
		return share.ORDER_STATUS_TRADE_BUYER_SIGNED
	case douyinlife.ORDER_STATUS_CLOSE:
		return share.ORDER_STATUS_TRADE_CLOSED
	}
	return ""
}

func (o *DouyinlifeOrder) GetGoodsAmount() float64 {
	var amountCent int64
	for _, subOrder := range o.Order.SubOrderAmountInfos {
		if subOrder == nil || subOrder.OriginAmount == nil {
			continue
		}
		if subOrder.SubOrderType == douyinlife.SUB_ORDER_FEE_TYPE_PRODUCT {
			amountCent += *subOrder.OriginAmount
		}
	}
	return util.ConvertAmountUnitFromCentToYuan(amountCent)
}

func (o *DouyinlifeOrder) GetPostalAmount() float64 {
	var amountCent int64
	for _, subOrder := range o.Order.SubOrderAmountInfos {
		if subOrder == nil || subOrder.OriginAmount == nil {
			continue
		}
		if subOrder.SubOrderType != douyinlife.SUB_ORDER_FEE_TYPE_PRODUCT {
			amountCent += *subOrder.OriginAmount
		}
	}
	return util.ConvertAmountUnitFromCentToYuan(amountCent)
}

func (o *DouyinlifeOrder) getSubOrderId(subOrderType int32) string {
	subOrderIds := []string{}
	for _, subOrder := range o.Order.SubOrderAmountInfos {
		if subOrder == nil {
			continue
		}
		if subOrder.SubOrderType == subOrderType {
			subOrderIds = append(subOrderIds, subOrder.SubOrderId)
		}
	}
	if len(subOrderIds) == 0 {
		return ""
	}
	sort.Strings(subOrderIds)
	return subOrderIds[0]
}

func (o *DouyinlifeOrder) GetActualAmount() float64 {
	return util.ConvertAmountUnitFromCentToYuan(o.getActualAmount())
}

func (o *DouyinlifeOrder) getActualAmount() int64 {
	discountAmountPointer := o.getGoodsDiscountCentPointer()
	originAmount := o.getOriginAmount()
	if discountAmountPointer != nil && originAmount != nil {
		return *originAmount - *discountAmountPointer
	}
	return *originAmount
}

func (o *DouyinlifeOrder) getOriginAmount() *int64 {
	if o.Order.OriginAmount != nil {
		return o.Order.OriginAmount
	}
	if o.Order.OriginalAmount != nil {
		return o.Order.OriginalAmount
	}
	return nil
}

func (o *DouyinlifeOrder) getGoodsDiscountCentPointer() *int64 {
	var amountCent int64
	for _, subOrder := range o.Order.SubOrderAmountInfos {
		if subOrder == nil || subOrder.DiscountAmount == nil {
			continue
		}
		amountCent += *subOrder.DiscountAmount
	}
	if amountCent == 0 {
		return nil
	}
	return &amountCent
}

func (o *DouyinlifeOrder) GetDiscountAmount() float64 {
	originAmount := o.getOriginAmount()
	if originAmount != nil {
		return util.ConvertAmountUnitFromCentToYuan(*originAmount - o.getActualAmount())
	}
	return float64(0)
}

func (o *DouyinlifeOrder) GetPaymentType() string {
	return share.ORDER_PAYMENT_TYPE_ONLINEPAY
}

func (o *DouyinlifeOrder) GetBuyerId() string {
	if o.Order.OpenId != "" {
		return o.Order.OpenId
	}
	return ""
}

func (o *DouyinlifeOrder) GetBuyerNickname() string {
	return ""
}

func (o *DouyinlifeOrder) GetGoodsDetail() *[]GoodsDetail {
	allGoodsDetail := []GoodsDetail{}
	productCount := o.getProductCount()
	subTotalAmount, subTotalPayAmount, subTotalDiscount := o.sumSubOrderAmounts(douyinlife.SUB_ORDER_FEE_TYPE_PRODUCT)
	detail := GoodsDetail{
		GoodsNumber:     o.Order.SkuId,
		Name:            o.Order.SkuName,
		Price:           util.DivideFloatWithRound(subTotalAmount, float64(productCount), 2),
		DiscountPrice:   util.DivideFloatWithRound(subTotalPayAmount, float64(productCount), 2),
		Discount:        util.DivideFloatWithRound(subTotalDiscount, float64(productCount), 2),
		Oid:             o.getSubOrderId(douyinlife.SUB_ORDER_FEE_TYPE_PRODUCT),
		Count:           productCount,
		Sku:             o.Order.SkuId,
		OutSkuId:        o.Order.ThirdSkuId,
		SubtotalAmount:  subTotalPayAmount,
		RefundAmount:    o.getRefundAmount(),
		AfterSaleStatus: o.getAfterSaleStatus(),
	}
	allGoodsDetail = append(allGoodsDetail, detail)
	noProductGoodsDetails := o.noProductGoodsDetails()
	if len(noProductGoodsDetails) > 0 {
		allGoodsDetail = append(allGoodsDetail, noProductGoodsDetails...)
	}
	return &allGoodsDetail
}

func (o *DouyinlifeOrder) getProductCount() uint64 {
	for _, product := range o.Order.Products {
		if product == nil {
			continue
		}
		if product.ProductId == o.Order.SkuId {
			return uint64(product.Number)
		}
	}
	return uint64(1)
}

func (o *DouyinlifeOrder) sumSubOrderAmounts(subOrderType int32) (float64, float64, float64) {
	var (
		totalAmountCent, totalPayAmountCent, totalDiscountCent int64
		totalAmount, totalPayAmount, totalDiscount             float64
	)
	for _, subOrder := range o.Order.SubOrderAmountInfos {
		if subOrder == nil {
			continue
		}
		if subOrder.SubOrderType == subOrderType {
			if subOrder.OriginAmount != nil {
				totalAmountCent += *subOrder.OriginAmount
			}
			if subOrder.ReceiptAmount != nil {
				totalPayAmountCent += *subOrder.ReceiptAmount
			}
			if subOrder.DiscountAmount != nil {
				totalDiscountCent += *subOrder.DiscountAmount
			}
		}
	}
	totalAmount = util.ConvertAmountUnitFromCentToYuan(totalAmountCent)
	totalPayAmount = util.ConvertAmountUnitFromCentToYuan(totalPayAmountCent)
	totalDiscountTmp := util.ConvertAmountUnitFromCentToYuan(totalDiscountCent)
	if totalDiscountCent > 0 {
		totalPayAmount = util.SubtractFloat(totalAmount, totalDiscountTmp)
	}
	totalDiscount = util.SubtractFloat(totalAmount, totalPayAmount)
	return totalAmount, totalPayAmount, totalDiscount
}

func (o *DouyinlifeOrder) getRefundAmount() float64 {
	var refundAmountCent int64
	for _, certificate := range o.Order.Certificates {
		if certificate != nil && certificate.RefundAmount != nil {
			refundAmountCent += *certificate.RefundAmount
		}
	}
	return util.ConvertAmountUnitFromCentToYuan(refundAmountCent)
}

func (o *DouyinlifeOrder) getAfterSaleStatus() string {
	var sameStatusOfAll int32
	for _, certificate := range o.Order.Certificates {
		if sameStatusOfAll == 0 {
			sameStatusOfAll = certificate.ItemStatus
		}
		if sameStatusOfAll != certificate.ItemStatus {
			return ""
		}
	}
	switch sameStatusOfAll {
	case douyinlife.ORDER_CERTIFICATE_STATUS_REFUNDING:
		return share.ORDER_AFTER_SALE_STATUS_REFUNDING
	case douyinlife.ORDER_CERTIFICATE_STATUS_REFUND_SUCCESS:
		return share.ORDER_AFTER_SALE_STATUS_REFUNDED
	}
	return ""
}

func (o *DouyinlifeOrder) noProductGoodsDetails() (details []GoodsDetail) {
	prefix := "douyinlife_sub_order_fee_"
	for subOrderType, feeName := range douyinlife.SubOrderFeeTypeName {
		if subOrderType == douyinlife.SUB_ORDER_FEE_TYPE_PRODUCT {
			continue
		}
		sku := fmt.Sprintf("%s%d", prefix, subOrderType)
		subTotalAmount, subTotalPayAmount, subTotalDiscount := o.sumSubOrderAmounts(subOrderType)
		if subTotalAmount > 0 {
			details = append(details, GoodsDetail{
				GoodsNumber:    sku,
				Name:           feeName,
				Price:          subTotalAmount,
				DiscountPrice:  subTotalPayAmount,
				Discount:       subTotalDiscount,
				Count:          1,
				Sku:            sku,
				SubtotalAmount: subTotalAmount,
				Oid:            o.getSubOrderId(subOrderType),
			})
		}
	}
	return details
}

func (o *DouyinlifeOrder) UpsertMember() (*pb_member.MemberDetailResponse, error) {
	if o.Store.IsNotCreateMember {
		return nil, nil
	}
	var (
		member *pb_member.MemberDetailResponse
		params *pb_member.UpsertMemberRequest
		err    error
	)
	if o.Store.IsBelongToReceiver {
		if o.ReceiverPhone == "" {
			return nil, nil
		}
		params, err = o.getUpsertReceiverMemberParams()
	} else {
		if o.Order.OpenId == "" {
			return nil, nil
		}
		params, err = o.getUpsertMemberParams()
	}
	if err != nil {
		return nil, err
	}
	member, err = o.upsertMember(params)
	if err != nil {
		return nil, err
	}
	o.Member = member
	return member, nil
}

func (o *DouyinlifeOrder) setReceiverInfo() {
	if o.Order.Receiver == nil {
		return
	}
	o.ReceiverName = o.Order.Receiver.Name
	phone := o.Order.Receiver.RealPhone
	if !validators.CValidator.IsPhone(phone, nil) {
		log.Warn(o.Context, "Receiver member phone is invalid when synchronizing douyinlife orders", log.Fields{
			"accountId":     o.AccountId.Hex(),
			"storeId":       o.Store.Id.Hex(),
			"syncHistoryId": o.SyncHistoryId.Hex(),
			"orderId":       o.GetOrderId(),
			"receiver":      *o.Order.Receiver,
		})
		return
	}
	o.ReceiverPhone = phone
	o.EncryptReceiverPhone, _ = member.EncryptStringValue(o.Context, o.ReceiverPhone)
}

func (o *DouyinlifeOrder) getUpsertReceiverMemberParams() (*pb_member.UpsertMemberRequest, error) {
	if o.ReceiverPhone == "" {
		return nil, errors.New("Failed to get receiver phone")
	}
	propertiesMap := o.getReceiverPropertiesMap()
	extraBoolCond := map[string]*types.BoolValue{}
	extraBoolCond["isDefault"] = &types.BoolValue{Value: true}
	propertyInfos, getPropertyInfosErr := share.GetPropertyInfos(o.Context, propertiesMap, "name", extraBoolCond)
	if getPropertyInfosErr != nil {
		return nil, getPropertyInfosErr
	}
	request := &pb_member.UpsertMemberRequest{
		OriginFrom: &pb_origin.OriginInfo{
			Channel:     o.Store.Sid,
			ChannelName: o.Store.Name,
			OpenId:      o.GetOrigin() + ":" + o.EncryptReceiverPhone,
			Origin:      o.GetOrigin(),
			Nickname:    o.ReceiverName,
		},
		Properties: propertyInfos,
	}
	return request, nil
}

func (o *DouyinlifeOrder) getReceiverPropertiesMap() map[string]interface{} {
	propertiesMap := map[string]interface{}{}
	if o.ReceiverPhone != "" {
		propertiesMap["phone"] = &pb_member.PropertyInfo_ValueString{
			ValueString: &pb_member.PropertyStringValue{
				Value: o.ReceiverPhone,
			},
		}
	}
	if o.ReceiverName != "" {
		propertiesMap["name"] = &pb_member.PropertyInfo_ValueString{
			ValueString: &pb_member.PropertyStringValue{
				Value: o.ReceiverName,
			},
		}
	}
	return propertiesMap
}

func (o *DouyinlifeOrder) getUpsertMemberParams() (*pb_member.UpsertMemberRequest, error) {
	propertiesMap := map[string]interface{}{}
	if o.Store.IsMergeReceiver {
		propertiesMap = o.getReceiverPropertiesMap()
	}
	extraBoolCond := map[string]*types.BoolValue{}
	extraBoolCond["isDefault"] = &types.BoolValue{Value: true}
	propertyInfos, getPropertyInfosErr := share.GetPropertyInfos(o.Context, propertiesMap, "name", extraBoolCond)
	if getPropertyInfosErr != nil {
		return nil, getPropertyInfosErr
	}
	request := &pb_member.UpsertMemberRequest{
		OriginFrom: &pb_origin.OriginInfo{
			Channel:     o.Store.Sid,
			ChannelName: o.Store.Name,
			OpenId:      o.GetBuyerId(),
			Origin:      o.GetOrigin(),
		},
		Properties: propertyInfos,
	}
	if o.Store.IsMergeReceiver && o.ReceiverPhone != "" {
		request.OtherSocials = []*pb_origin.OriginInfo{
			{
				Channel:     o.Store.Sid,
				ChannelName: o.Store.Name,
				OpenId:      o.GetOrigin() + ":" + o.EncryptReceiverPhone,
				Origin:      o.GetOrigin(),
				Nickname:    o.ReceiverName,
			},
		}
	}
	return request, nil
}

func (o *DouyinlifeOrder) GetOrigin() string {
	return constant.DOUYINLIFE
}

func (o *DouyinlifeOrder) upsertMember(params *pb_member.UpsertMemberRequest) (*pb_member.MemberDetailResponse, error) {
	member, _ := share.GetMemberByOpenId(o.Context, params.OriginFrom.OpenId, []string{"Properties"}, "")
	if member != nil && member.Id != "" {
		return member, nil
	}
	resultMember, upsertMemberErr := share.UpsertMember(o.Context, params)
	if upsertMemberErr != nil {
		return nil, upsertMemberErr
	}
	return resultMember, nil
}

func (o *DouyinlifeOrder) UpsertReceiverMember() (*pb_member.MemberDetailResponse, error) {
	if o.Store.IsNotCreateMember {
		return nil, nil
	}
	if o.ReceiverPhone == "" {
		return nil, nil
	}
	params, err := o.getUpsertReceiverMemberParams()
	if err != nil {
		return nil, err
	}
	return o.upsertMember(params)
}

func (o *DouyinlifeOrder) GetReceiver() (*Receiver, error) {
	receiver := o.getReceiverInfo()
	if receiver.Mobile == "" {
		return nil, nil
	}
	if o.Store.IsMergeReceiver || o.Store.IsBelongToReceiver {
		if o.Member != nil && o.Member.Id != "" {
			receiver.MemberId = bson.ObjectIdHex(o.Member.Id)
		}
		return receiver, nil
	}
	receiverMember, err := o.UpsertReceiverMember()
	if err != nil {
		return nil, err
	}
	if receiverMember != nil && receiverMember.Id != "" {
		receiver.MemberId = bson.ObjectIdHex(receiverMember.Id)
	}
	return receiver, nil
}

func (o *DouyinlifeOrder) getReceiverInfo() *Receiver {
	return &Receiver{
		Country: "中国",
		Name:    o.ReceiverName,
		Mobile:  o.ReceiverPhone,
	}
}

func (o *DouyinlifeOrder) GetDelivery() (*Delivery, error) {
	if o.Order.Delivery == nil {
		return nil, nil
	}
	delivery := &Delivery{}
	delivery.ExpressNo = o.Order.Delivery.ShopNumber
	delivery.Type = cast.ToString(o.Order.Delivery.DeliverModel)
	if deliveryType, ok := douyinlife.DeliverModel[o.Order.Delivery.DeliverModel]; ok {
		delivery.Type = deliveryType
	}
	return delivery, nil
}

func (o *DouyinlifeOrder) GetCloseReason() string {
	if o.Order.OrderStatus == douyinlife.ORDER_STATUS_CLOSE {
		return "用户取消支付或超时未支付"
	}
	return ""
}

func (o *DouyinlifeOrder) GetDealCode() string {
	return ""
}

func (o *DouyinlifeOrder) GetOutTradeNo() string {
	return ""
}

func (o *DouyinlifeOrder) GetCoupons() (*[]Coupon, error) {
	return nil, nil
}

func (o *DouyinlifeOrder) GetUtm() (*Utm, error) {
	return nil, nil
}

func (o *DouyinlifeOrder) GetRefundStatus() string {
	if o.GetOrderStatus() == share.ORDER_STATUS_TRADE_CLOSED &&
		o.getAfterSaleStatus() == share.ORDER_AFTER_SALE_STATUS_REFUNDED {
		return share.REFUND_STATUS_REFUNDED
	}
	return ""
}

func (o *DouyinlifeOrder) GetBuyerMessage() string {
	if o.Order.Delivery != nil {
		return o.Order.Delivery.Remark
	}
	return ""
}

func (o *DouyinlifeOrder) GetSellerMemo() string {
	return ""
}

func (o *DouyinlifeOrder) GetOrderConsignTime() string {
	return ""
}

func (o *DouyinlifeOrder) GetExtraFields() (interface{}, error) {
	return nil, nil
}

func (o *DouyinlifeOrder) GetCondition() bson.M {
	condition := bson.M{
		"accountId": o.AccountId,
		"orderId":   o.GetOrderId(),
		"isDeleted": false,
	}
	return condition
}

func (o *DouyinlifeOrder) IsFiltered() bool {
	return false
}

func (o *DouyinlifeOrder) UpsertExtraTrade(trade *Trade) (*Trade, error) {
	trade.ErpOrderId = o.GetOrderId()
	if o.GetRefundStatus() == share.REFUND_STATUS_REFUNDED && len(o.Order.Certificates) != 0 {
		var (
			latestRefundTimestamp int64
			refundId              string
			extraFields           = map[string]any{
				"refundAmount": o.getRefundAmount(),
			}
		)
		for _, certificates := range o.Order.Certificates {
			if certificates.RefundTime > latestRefundTimestamp {
				latestRefundTimestamp = certificates.RefundTime
				refundId = certificates.OrderItemId
			}
		}
		if latestRefundTimestamp > 0 {
			trade.RefundTime = util.TransUnixToTime(latestRefundTimestamp)
			extraFields["refundId"] = refundId
		}
		trade.ExtraFields = extraFields
	}
	return trade, nil
}

func (o *DouyinlifeOrder) GetTimeLayout(timeType string) string {
	switch timeType {
	case "orderCreateTime", "orderUpdateTime", "orderCompleteTime", "orderPayTime":
		return share.LAYOUT_BY_MINUTE
	}
	return time.RFC3339
}

func (o *DouyinlifeOrder) GetOrderSourceJsonStr() (string, error) {
	encodedOrder, err := json.Marshal(o.Order)
	return string(encodedOrder), err
}

func (o *DouyinlifeOrder) AfterUpsert(trade *Trade) error {
	if trade.DisableEvent || !trade.MemberId.Valid() {
		return nil
	}
	if trade.IsNeedSendMemberEvent(o.Trade, "OrderCompleteTime") {
		if o.GetRefundStatus() == share.REFUND_STATUS_REFUNDED {
			if trade.sentPurchaseEvent {
				// 该 trade 在本次请求发过购买事件，需等待防止退款事件早于购买事件
				time.Sleep(time.Second * 10)
			}
			o.sendRefundOrderEvent(trade)
			o.sendRefundProductEvent(trade)
			o.afterSendRefundEvent(trade)
		}
		o.sendOrderCompleteEvent(trade)
	}
	return nil
}

func (o *DouyinlifeOrder) sendOrderCompleteEvent(trade *Trade) {
	if trade.OrderCompleteTime.IsZero() {
		return
	}
	_, goodsList := trade.GetGoodMsgAndList()
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_ORDER_COMPLETED + ":" + trade.OrderId,
		AccountId:  trade.AccountId.Hex(),
		MemberId:   trade.MemberId.Hex(),
		ChannelId:  trade.GetChannelId(o.Context),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_ORDER_COMPLETED,
		CreateTime: trade.OrderCompleteTime.UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"memberId":  trade.MemberId.Hex(),
			"id":        trade.Id.Hex(),
			"number":    trade.OrderId,
			"orderId":   trade.OrderId,
			"platform":  trade.OrderPlatform,
			"storeId":   trade.StoreId.Hex(),
			"storeName": trade.StoreName,
			"goodsAmount": func() uint64 {
				// 订单完成事件属性 goodsAmount 不包括邮费，也不包括子订单中退款的金额
				goodsPayAmount := util.SubtractFloat(trade.GoodsAmount, o.getRefundAmount())
				if goodsPayAmount < 0 {
					goodsPayAmount = 0
				}
				return share.Yuan2Fen(goodsPayAmount)
			}(),
			"logisticsFee":    share.Yuan2Fen(trade.PostalAmount),
			"completedAt":     trade.OrderCompleteTime.UnixNano() / 1e6,
			"orderCreateTime": trade.OrderCreateTime.UnixNano() / 1e6,
			"goodsList":       goodsList,
		},
	}
	if trade.CurrentLevel != 0 {
		eventBody.EventProperties["memberLevel"] = trade.CurrentLevel
	}
	eventBody.SendCustomerEvent(o.Context)
}

func (o *DouyinlifeOrder) sendRefundOrderEvent(trade *Trade) {
	if trade.OrderPayTime.IsZero() {
		return
	}
	refundGoodsList := o.getRefundGoodsList(trade)
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_REFUND_ORDER + ":" + trade.OrderId,
		AccountId:  o.AccountId.Hex(),
		MemberId:   trade.MemberId.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_REFUND_ORDER,
		CreateTime: trade.RefundTime.UnixNano() / 1e6,
		ChannelId:  trade.GetChannelId(o.Context),
		EventProperties: map[string]interface{}{
			"tradeId":           trade.Id.Hex(),
			"refundId":          o.getRefundId(trade),
			"id":                trade.Id.Hex(),
			"number":            trade.OrderId,
			"orderId":           trade.OrderId,
			"platform":          trade.OrderPlatform,
			"storeId":           trade.StoreId.Hex(),
			"storeName":         trade.StoreName,
			"productName":       o.Order.SkuName,
			"productCounts":     o.getProductCount(),
			"refundAmount":      share.Yuan2Fen(o.getRefundAmount()),
			"refundGoodsAmount": share.Yuan2Fen(o.getRefundAmount()),
			"goodsList":         refundGoodsList,
			"isAllRefund":       true,
		},
	}
	if trade.CurrentLevel != 0 {
		eventBody.EventProperties["memberLevel"] = trade.CurrentLevel
	}
	eventBody.SendCustomerEvent(o.Context)
}

func (o *DouyinlifeOrder) sendRefundProductEvent(trade *Trade) {
	if trade.OrderPayTime.IsZero() {
		return
	}
	refundGoodsDetail := o.getRefundGoods(trade)
	if refundGoodsDetail == nil {
		return
	}
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_REFUND_PRODUCT + ":" + trade.OrderId + ":" + refundGoodsDetail.Sku,
		AccountId:  o.AccountId.Hex(),
		MemberId:   trade.MemberId.Hex(),
		ChannelId:  trade.GetChannelId(o.Context),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_REFUND_PRODUCT,
		CreateTime: trade.RefundTime.UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"platform":           trade.OrderPlatform,
			"tradeId":            trade.Id.Hex(),
			"storeId":            trade.StoreId.Hex(),
			"storeName":          trade.StoreName,
			"orderId":            trade.OrderId,
			"productName":        refundGoodsDetail.Name,
			"productNumber":      refundGoodsDetail.GoodsNumber,
			"productCounts":      refundGoodsDetail.Count,
			"productSku":         refundGoodsDetail.Sku,
			"productExternalSku": refundGoodsDetail.ExternalSku,
			"refundId":           o.getRefundId(trade),
			"refundAmount":       share.Yuan2Fen(refundGoodsDetail.RefundAmount),
			"id":                 trade.Id.Hex(),
			"number":             trade.OrderId,
			"outTradeId":         fmt.Sprintf("%s_%s_%s", trade.OrderId, refundGoodsDetail.OutSkuId, refundGoodsDetail.Sku), // 商品唯一标识
			"isAllRefund":        true,
		},
	}
	if trade.CurrentLevel != 0 {
		eventBody.EventProperties["memberLevel"] = trade.CurrentLevel
	}
	eventBody.SendCustomerEvent(o.Context)
}

func (o *DouyinlifeOrder) getRefundId(trade *Trade) (refundId string) {
	extraFields, ok := trade.ExtraFields.(map[string]interface{})
	if !ok {
		return
	}
	if refundIdInterface, ok := extraFields["refundId"]; ok {
		refundId = cast.ToString(refundIdInterface)
		return
	}
	return
}

func (o *DouyinlifeOrder) getRefundGoods(trade *Trade) *GoodsDetail {
	var refundGoodsDetail GoodsDetail
	for _, detail := range trade.GoodsDetail {
		if detail.RefundAmount > 0.0 {
			refundGoodsDetail = detail
			return &refundGoodsDetail
		}
	}
	return nil
}

func (o *DouyinlifeOrder) getRefundGoodsList(trade *Trade) (goodList []map[string]interface{}) {
	refundGoodsDetail := o.getRefundGoods(trade)
	if refundGoodsDetail == nil {
		return nil
	}
	goodList = append(goodList, map[string]interface{}{
		"productName":   refundGoodsDetail.Name,
		"productNumber": refundGoodsDetail.GoodsNumber,
		"productSku":    refundGoodsDetail.Sku,
		"price":         share.Yuan2Fen(refundGoodsDetail.Price),
		"count":         refundGoodsDetail.Count,
		"specs":         refundGoodsDetail.PropertiesName,
		"category":      refundGoodsDetail.Category,
		"payAmount":     share.Yuan2Fen(refundGoodsDetail.SubtotalAmount),
		"properties":    refundGoodsDetail.Properties,
	})
	return goodList
}

func (o *DouyinlifeOrder) afterSendRefundEvent(trade *Trade) {
	for idx, goodsDetail := range trade.GoodsDetail {
		if goodsDetail.Oid == o.getSubOrderId(douyinlife.SUB_ORDER_FEE_TYPE_PRODUCT) {
			trade.GoodsDetail[idx].HasSendEvent = true
		}
	}
	trade.Update(o.Context)
}

func (o *DouyinlifeOrder) IsVirtual() bool {
	return false
}

func (o *DouyinlifeOrder) GetChannelId() string {
	return o.TempProvider.Channel.ChannelId
}

func (o *DouyinlifeOrder) GetStaffId() bson.ObjectId {
	return bson.NilObjectId
}

func (o *DouyinlifeOrder) GetStaffCode() string {
	return ""
}
