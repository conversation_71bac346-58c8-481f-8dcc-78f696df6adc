package order

import (
	"mairpc/core/extension/bson"
	"mairpc/proto/account"
	pb_member "mairpc/proto/member"
	"mairpc/service/trade/model/store"
	"time"
)

type BaseOrder interface {
	GetOrderId() string                                             // 获取订单编号
	GetOrderFrom() string                                           // 获取订单实际提供方
	GetOrderPlatform() string                                       // 获取订单实际产生平台
	GetOrderCreateTime() string                                     // 获取订单创建时间
	GetOrderUpdateTime() string                                     // 获取订单更新时间
	GetOrderCompleteTime() string                                   // 获取订单完成时间
	GetOrderPayTime() string                                        // 获取订单支付时间
	GetOrderStatus() string                                         // 获取订单状态
	GetGoodsAmount() float64                                        // 获取订单总金额，不包含邮费
	GetPostalAmount() float64                                       // 获取订单邮费
	GetActualAmount() float64                                       // 获取订单实际支付金额，包含邮费
	GetDiscountAmount() float64                                     // 获取订单优惠金额
	GetPaymentType() string                                         // 获取支付类型
	GetBuyerId() string                                             // 获取购买用户 id
	GetBuyerNickname() string                                       // 获取购买用户 nickname
	GetGoodsDetail() *[]GoodsDetail                                 // 获取订单商品详情
	UpsertMember() (*pb_member.MemberDetailResponse, error)         // 创建或更新下单人信息
	UpsertReceiverMember() (*pb_member.MemberDetailResponse, error) // 创建或更新收货人信息
	GetReceiver() (*Receiver, error)                                // 获取收货信息
	GetDelivery() (*Delivery, error)                                // 获取配送信息
	GetCloseReason() string                                         // 获取订单关闭原因
	GetDealCode() string                                            // 获取订单交易码
	GetOutTradeNo() string                                          // 获取订单外部订单编号
	GetCoupons() (*[]Coupon, error)                                 // 获取订单优惠劵信息
	GetUtm() (*Utm, error)                                          // 获取订单来源信息，目前只有零售订单才有
	GetRefundStatus() string                                        // 获取退款状态
	GetBuyerMessage() string                                        // 获取买家留言
	GetSellerMemo() string                                          // 获取商家留言
	GetOrderConsignTime() string                                    // 获取发货时间
	GetExtraFields() (interface{}, error)                           // 获取自定义字段
	GetCondition() bson.M                                           // 获取订单查询条件
	IsFiltered() bool                                               // 判断是否需要过滤该笔订单
	UpsertExtraTrade(*Trade) (*Trade, error)                        // upsert 订单除基本字段以外字段
	GetTimeLayout(string) string                                    // 获取时间转换格式
	GetOrderSourceJsonStr() (string, error)                         // 获取原始订单 json 字符串
	AfterUpsert(*Trade) error                                       // 用于处理 upsertTrade 成功之后的逻辑
	IsVirtual() bool                                                // 订单中是否是虚拟商品
	GetChannelId() string                                           // 获取店铺渠道标识
	GetStaffId() bson.ObjectId                                      // 获取导购 id
	GetStaffCode() string                                           // 获取导购编码
}

type TempProviderData struct {
	SyncDataType           string                    // 同步数据类型
	Store                  store.TradeStore          // 对应门店
	SyncHistories          []*TradeSyncHistory       // 上一次同步订单历史记录即最新的同步历史记录、当前同步新创建的同步历史记录
	SyncHistoriesForRefund []*TradeRefundSyncHistory // 上一次同步退款单历史记录即最新的同步历史记录、当前同步新创建的同步历史记录
	StartTime              string                    // 开始时间
	EndTime                string                    // 结束时间
	Start                  time.Time                 // 开始时间
	End                    time.Time                 // 结束时间
	Total                  int64                     // 订单总量
	PageNo                 int64                     // 页码
	PageSize               int64                     // 每页订单数量
	Client                 interface{}               // 对应组件
	Channel                *account.ChannelDetailResponse
}
