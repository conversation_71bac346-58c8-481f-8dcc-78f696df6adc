package order

import (
	"context"
	"encoding/json"
	"errors"
	"mairpc/core/component/youzan"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	pb_origin "mairpc/proto/common/origin"
	"mairpc/proto/common/types"
	pb_member "mairpc/proto/member"
	"mairpc/service/share/util"
	"mairpc/service/trade/model/store"
	"mairpc/service/trade/share"
	"reflect"
	"strings"
	"time"

	"github.com/spf13/cast"
)

type YouzanOrder struct {
	Context       context.Context
	FullOrderInfo *youzan.GetTradesSoldFullOrderInfoListFullOrderInfo
	AccountId     bson.ObjectId
	StoreId       bson.ObjectId
	Store         *store.TradeStore
	SyncHistoryId bson.ObjectId
	AccessToken   string
	Client        *youzan.Youzan
	DecryptedData map[string]interface{}
	OrderDetail   *youzan.GetTradeResponse
}

func (yzOrder *YouzanOrder) Init(ctx context.Context, fullOrderInfo *youzan.GetTradesSoldFullOrderInfoListFullOrderInfo, tempProvider TempProviderData) error {
	yzOrder.Context = ctx
	yzOrder.FullOrderInfo = fullOrderInfo
	yzOrder.AccountId = util.GetAccountIdAsObjectId(ctx)
	yzOrder.StoreId = tempProvider.Store.Id
	yzOrder.SyncHistoryId = tempProvider.SyncHistories[1].Id
	yzOrder.Store = &tempProvider.Store
	yzOrder.AccessToken = yzOrder.Store.GetAccessToken(ctx)
	yzOrder.Client = youzan.NewYouzan(yzOrder.AccessToken)

	tradeDetail, getTradeDetailErr := yzOrder.GetTradeDetail(0)
	if getTradeDetailErr != nil {
		return getTradeDetailErr
	}
	yzOrder.OrderDetail = tradeDetail

	return nil
}

func (yzOrder *YouzanOrder) DecryptData() (*youzan.BatchCloudSecretDecryptResponse, error) {
	addressInfo := yzOrder.FullOrderInfo.AddressInfo
	buyerInfo := yzOrder.FullOrderInfo.BuyerInfo
	buerName := yzOrder.FullOrderInfo.OrderInfo.OrderExtra.BuyerName
	var sources []string
	if addressInfo.SelfFetchInfo != "" {
		sources = append(sources, addressInfo.SelfFetchInfo)
	}
	if addressInfo.DeliveryAddress != "" {
		sources = append(sources, addressInfo.DeliveryAddress)
	}
	if addressInfo.ReceiverName != "" {
		sources = append(sources, addressInfo.ReceiverName)
	}
	if addressInfo.ReceiverTel != "" {
		sources = append(sources, addressInfo.ReceiverTel)
	}
	if buyerInfo.FansNickname != "" {
		sources = append(sources, buyerInfo.FansNickname)
	}
	if buyerInfo.BuyerPhone != "" {
		sources = append(sources, buyerInfo.BuyerPhone)
	}
	if buerName != "" {
		sources = append(sources, buerName)
	}
	request := &youzan.BatchCloudSecretDecryptRequest{
		Sources: sources,
	}

	result, err := yzOrder.Client.Common.BatchCloudSecretDecrypt(yzOrder.Context, request)
	if err != nil {
		return nil, err
	}
	if result != nil {
		decryptData := (*result).(map[string]interface{})
		if addressInfo.SelfFetchInfo != "" {
			yzOrder.FullOrderInfo.AddressInfo.SelfFetchInfo = cast.ToString(decryptData[addressInfo.SelfFetchInfo])
		}
		if addressInfo.DeliveryAddress != "" {
			yzOrder.FullOrderInfo.AddressInfo.DeliveryAddress = cast.ToString(decryptData[addressInfo.DeliveryAddress])
		}
		if addressInfo.ReceiverName != "" {
			yzOrder.FullOrderInfo.AddressInfo.ReceiverName = cast.ToString(decryptData[addressInfo.ReceiverName])
		}
		if addressInfo.ReceiverTel != "" {
			receiverTel := cast.ToString(decryptData[addressInfo.ReceiverTel])
			receiverTel = strings.Replace(receiverTel, " ", "", -1)
			yzOrder.FullOrderInfo.AddressInfo.ReceiverTel = receiverTel
		}
		if buyerInfo.FansNickname != "" {
			yzOrder.FullOrderInfo.BuyerInfo.FansNickname = cast.ToString(decryptData[buyerInfo.FansNickname])
		}
		if buyerInfo.BuyerPhone != "" {
			buyerPhone := cast.ToString(decryptData[buyerInfo.BuyerPhone])
			buyerPhone = strings.Replace(buyerPhone, " ", "", -1)
			yzOrder.FullOrderInfo.BuyerInfo.BuyerPhone = buyerPhone
		}
		if buerName != "" {
			yzOrder.FullOrderInfo.OrderInfo.OrderExtra.BuyerName = cast.ToString(decryptData[buerName])
		}
	}

	return result, nil
}

func (yzOrder *YouzanOrder) GetOrderId() string {
	return yzOrder.FullOrderInfo.OrderInfo.Tid
}

func (yzOrder *YouzanOrder) GetOrderFrom() string {
	return share.ORDER_FROM_YOUZAN
}

func (yzOrder *YouzanOrder) GetOrderPlatform() string {
	return share.ORDER_PLATFORM_YOUZAN
}

func (yzOrder *YouzanOrder) GetOrderCreateTime() string {
	return yzOrder.FullOrderInfo.OrderInfo.Created
}

func (yzOrder *YouzanOrder) GetOrderUpdateTime() string {
	return yzOrder.FullOrderInfo.OrderInfo.UpdateTime
}

func (yzOrder *YouzanOrder) GetOrderCompleteTime() string {
	return yzOrder.FullOrderInfo.OrderInfo.SuccessTime
}

func (yzOrder *YouzanOrder) GetOrderPayTime() string {
	return yzOrder.FullOrderInfo.OrderInfo.PayTime
}

func (yzOrder *YouzanOrder) GetOrderStatus() string {
	orderStatus := yzOrder.FullOrderInfo.OrderInfo.Status
	switch orderStatus {
	case "WAIT_BUYER_PAY":
		return share.ORDER_STATUS_WAIT_BUYER_PAY
	case "WAIT_SELLER_SEND_GOODS":
		return share.ORDER_STATUS_WAIT_SELLER_SEND_GOODS
	case "WAIT_BUYER_CONFIRM_GOODS":
		return share.ORDER_STATUS_WAIT_BUYER_CONFIRM_GOODS
	case "TRADE_SUCCESS":
		return share.ORDER_STATUS_TRADE_BUYER_SIGNED
	case "TRADE_CLOSED":
		return share.ORDER_STATUS_TRADE_CLOSED
	}
	return ""
}

func (yzOrder *YouzanOrder) GetGoodsAmount() float64 {
	goodsAmount := yzOrder.FullOrderInfo.PayInfo.TotalFee
	return cast.ToFloat64(goodsAmount)
}

func (yzOrder *YouzanOrder) GetPostalAmount() float64 {
	postalAmount := yzOrder.FullOrderInfo.PayInfo.PostFee
	return cast.ToFloat64(postalAmount)
}

func (yzOrder *YouzanOrder) GetActualAmount() float64 {
	actualAmount := yzOrder.FullOrderInfo.PayInfo.Payment
	return cast.ToFloat64(actualAmount)
}

func (yzOrder *YouzanOrder) GetDiscountAmount() float64 {
	discountAmount := yzOrder.GetGoodsAmount() - yzOrder.GetActualAmount() + yzOrder.GetPostalAmount()
	if discountAmount < 0 {
		return 0
	}
	return discountAmount
}

// 支付类型
// WEIXIN (微信自有支付) WEIXIN_DAIXIAO (微信代销支付) ALIPAY (支付宝支付) BANKCARDPAY (银行卡支付)  BAIDUPAY (百度钱包支付)  ECARD(有赞E卡支付) PREPAIDCARD (储值卡余额支付)
// PEERPAY (代付)
// CODPAY (货到付款)
// PRESENTTAKE (直接领取赠品) COUPONPAY(优惠券/码全额抵扣) BULKPURCHASE(来自分销商的采购) MERGEDPAY(合并付货款) PURCHASE_PAY (采购单支付) MARKPAY (标记收款) OFCASH (现金支付) ENCHASHMENT_GIFT_CARD(礼品卡支付)
func (yzOrder *YouzanOrder) GetPaymentType() string {
	isPayed := yzOrder.FullOrderInfo.OrderInfo.OrderTags.IsPayed
	if !isPayed {
		return ""
	}
	payType := yzOrder.FullOrderInfo.OrderInfo.PayTypeStr
	switch payType {
	case "WEIXIN", "WEIXIN_DAIXIAO", "ALIPAY", "BANKCARDPAY", "BAIDUPAY", "ECARD", "PREPAIDCARD":
		return share.ORDER_PAYMENT_TYPE_ONLINEPAY
	case "PEERPAY":
		return share.ORDER_PAYMENT_TYPE_PEERPAY
	case "CODPAY":
		return share.ORDER_PAYMENT_TYPE_CODPAY
	default:
		return share.ORDER_PAYMENT_TYPE_OTHERS
	}
}

func (yzOrder *YouzanOrder) GetBuyerId() string {
	return yzOrder.FullOrderInfo.BuyerInfo.YzOpenID
}

func (yzOrder *YouzanOrder) GetBuyerNickname() string {
	if yzOrder.FullOrderInfo.BuyerInfo.FansNickname != "" {
		return yzOrder.FullOrderInfo.BuyerInfo.FansNickname
	}
	return yzOrder.FullOrderInfo.BuyerInfo.BuyerPhone
}

func (yzOrder *YouzanOrder) GetGoodsDetail() *[]GoodsDetail {
	trade := CTrade.GetByOrderId(yzOrder.Context, yzOrder.GetOrderId())
	afterSaleStatusMap := make(map[string]string)
	if trade != nil {
		for _, detail := range trade.GoodsDetail {
			afterSaleStatusMap[detail.Sku] = detail.AfterSaleStatus
		}
	}
	var allGoodsDetail []GoodsDetail
	orders := yzOrder.FullOrderInfo.Orders
	for _, order := range orders {
		var (
			sku         = cast.ToString(order.ItemID) + cast.ToString(order.SkuID)
			goodsNumber = order.OuterItemID
		)
		if goodsNumber == "" {
			goodsNumber = cast.ToString(order.ItemID)
		}
		if order.SkuID == 0 {
			sku = cast.ToString(order.ItemID)
		}
		detail := GoodsDetail{
			GoodsNumber:     goodsNumber,
			Oid:             order.Oid,
			Name:            order.Title,
			Price:           cast.ToFloat64(order.Price),
			Count:           cast.ToUint64(order.Num),
			Sku:             sku,
			DiscountPrice:   cast.ToFloat64(order.DiscountPrice),
			OutSkuId:        order.OuterSkuID,
			PropertiesName:  GetPropertiesName(order.SkuPropertiesName),
			SubtotalAmount:  cast.ToFloat64(order.Payment),
			ThumbnailUrl:    order.PicPath,
			Weight:          cast.ToFloat64(order.Weight),
			AfterSaleStatus: afterSaleStatusMap[sku],
		}
		allGoodsDetail = append(allGoodsDetail, detail)
	}
	return &allGoodsDetail
}

func GetPropertiesName(skuPropertiesName string) string {
	if skuPropertiesName == "" || skuPropertiesName == "[]" {
		return ""
	}

	type Property struct {
		K   string `json:"k"`
		V   string `json:"v"`
		KId int64  `json:"k_id"`
		VId int64  `json:"v_id"`
	}
	properties := &[]Property{}
	json.Unmarshal([]byte(skuPropertiesName), properties)
	result := ""
	for _, property := range *properties {
		result += property.K + ":" + property.V + " "
	}
	result = strings.TrimSpace(result)

	return result
}

func (yzOrder *YouzanOrder) UpsertMember() (*pb_member.MemberDetailResponse, error) {
	ctx := yzOrder.Context
	buyerInfo := yzOrder.FullOrderInfo.BuyerInfo
	yzOpenId := buyerInfo.YzOpenID
	member, _ := share.GetMemberByOpenId(ctx, yzOpenId, []string{"Properties"}, "")
	if member != nil && member.Id != "" {
		return member, nil
	}

	// 获取有赞客户详情，用于创建客户
	customerDetail, err := yzOrder.GetCustomerDetail(0)
	if err != nil {
		log.Warn(ctx, "Get youzan user customer detail failed when sync order", log.Fields{
			"accountId":     yzOrder.AccountId.Hex(),
			"storeId":       yzOrder.StoreId.Hex(),
			"syncHistoryId": yzOrder.SyncHistoryId.Hex(),
			"tid":           yzOrder.GetOrderId(),
			"errMsg":        err.Error(),
			"yzOpenId":      yzOpenId,
		})
		return nil, err
	}
	youzanUser := &pb_member.YouzanUser{
		ChannelId:   yzOrder.Store.Sid,
		ChannelName: yzOrder.Store.Name,
		OpenId:      yzOpenId,
		Mobile:      customerDetail.Mobile,
		Name:        customerDetail.Name,
		Gender: func(g int64) string {
			switch g {
			case 1:
				return "male"
			case 2:
				return "female"
			}
			return "unknown"
		}(customerDetail.Gender),
		IsMember: &types.BoolValue{
			Value: customerDetail.MemberCreatedAt > 0,
		},
		Score: &types.UIntValue{
			Value: uint64(customerDetail.Points),
		},
		Province: customerDetail.ProvinceName,
		City:     customerDetail.CityName,
		District: customerDetail.CountyName,
		Remarks:  customerDetail.Remark,
	}
	if customerDetail.Birthday != "" {
		youzanUser.Birth = strings.Split(customerDetail.Birthday, " ")[0]
	}
	memberDetail, upsertErr := share.UpsertFromYouzan(ctx, youzanUser)
	if upsertErr != nil {
		log.Warn(yzOrder.Context, "Failed to upsert youzanUser", log.Fields{
			"accountId":     yzOrder.AccountId.Hex(),
			"storeId":       yzOrder.StoreId.Hex(),
			"syncHistoryId": yzOrder.SyncHistoryId.Hex(),
			"tid":           yzOrder.GetOrderId(),
			"err":           upsertErr.Error(),
			"youzanUser":    core_util.MarshalInterfaceToString(youzanUser),
		})
		return nil, upsertErr
	}

	return memberDetail, nil
}

func (yzOrder *YouzanOrder) GetCustomerDetail(retryTimes int) (*youzan.GetScrmCustomerDetailResponse, error) {
	request := &youzan.GetScrmCustomerDetailRequest{
		YzOpenID: yzOrder.FullOrderInfo.BuyerInfo.YzOpenID,
		Fields:   "user_base,credit",
	}
	resp, err := yzOrder.Client.Customer.GetScrmCustomerDetail(yzOrder.Context, request)
	if err != nil {
		yzErr, ok := err.(*youzan.YouzanErrorResponse)
		if !ok {
			if retryTimes > 3 {
				return nil, err
			}
			time.Sleep(time.Second * 3)
			retryTimes++
			return yzOrder.GetCustomerDetail(retryTimes)
		}
		if yzErr.Code == ********* {
			return resp, nil
		}
		return nil, err
	}
	return resp, nil
}

// 有赞粉丝性别的可选值：m(男),f(女),未知则为空
// 有赞客户性别的可选值：1(男),2(女),0(未知)
func (yzOrder *YouzanOrder) GetGender(gender interface{}) string {
	genderResult := "unknown"
	switch v := gender.(type) {
	case int64:
		if v == 1 {
			genderResult = "male"
		} else if v == 2 {
			genderResult = "female"
		} else {
			genderResult = "unknown"
		}
	case string:
		if v == "m" {
			genderResult = "male"
		} else if v == "f" {
			genderResult = "female"
		} else {
			genderResult = "unknown"
		}
	}

	return genderResult
}

func (yzOrder *YouzanOrder) GetUsersInfo() (*youzan.QueryUsersInfoResponse, error) {
	request := &youzan.QueryUsersInfoRequest{
		YzOpenID: yzOrder.FullOrderInfo.BuyerInfo.YzOpenID,
	}
	return yzOrder.Client.User.QueryUsersInfo(yzOrder.Context, request)
}

func (yzOrder *YouzanOrder) GetTradeDetail(retryTimes int) (*youzan.GetTradeResponse, error) {
	request := &youzan.GetTradeRequest{
		Tid: yzOrder.GetOrderId(),
	}
	resp, err := yzOrder.Client.Trade.GetTrade(yzOrder.Context, request)
	if err != nil {
		if retryTimes > 3 {
			return nil, err
		}
		time.Sleep(time.Second * 3)
		retryTimes++
		return yzOrder.GetTradeDetail(retryTimes)
	}
	return resp, nil
}

func (yzOrder *YouzanOrder) IsEmptyReceiverAddress() bool {
	addressInfo := yzOrder.FullOrderInfo.AddressInfo
	return addressInfo.DeliveryProvince == "" && addressInfo.DeliveryCity == "" && addressInfo.DeliveryDistrict == ""
}

func (yzOrder *YouzanOrder) GetReceiverAddressString() string {
	addressInfo := yzOrder.FullOrderInfo.AddressInfo
	receiverAddress := "中国 " + addressInfo.DeliveryProvince +
		" " + addressInfo.DeliveryCity +
		" " + addressInfo.DeliveryDistrict +
		" " + addressInfo.DeliveryAddress +
		"，" + addressInfo.ReceiverName +
		"，" + addressInfo.ReceiverTel
	return receiverAddress
}

func (yzOrder *YouzanOrder) UpsertReceiverMember() (*pb_member.MemberDetailResponse, error) {
	logMsg := "Failed to upsert youzan receiver member"
	logFields := log.Fields{
		"accountId":     yzOrder.AccountId.Hex(),
		"storeId":       yzOrder.StoreId.Hex(),
		"syncHistoryId": yzOrder.SyncHistoryId.Hex(),
		"tid":           yzOrder.GetOrderId(),
	}

	addressInfo := yzOrder.FullOrderInfo.AddressInfo
	receiverTel := addressInfo.ReceiverTel
	if receiverTel == "" {
		logFields["err"] = errors.New("ReceiverTel not found")
		log.Warn(yzOrder.Context, logMsg, logFields)
		return nil, nil
	}

	if !validators.CValidator.IsPhone(receiverTel, nil) {
		logFields["err"] = errors.New("ReceiverTel format error")
		log.Warn(yzOrder.Context, logMsg, logFields)
		return nil, nil
	}

	var member *pb_member.MemberDetailResponse
	member, _ = share.GetMemberByPhone(yzOrder.Context, receiverTel, []string{"Properties"}, "")
	if member != nil && member.Id != "" {
		return member, nil
	}
	openId := store.TRADE_STORE_TYPE_YOUZAN + ":" + receiverTel
	member, _ = share.GetMemberByOpenId(yzOrder.Context, openId, []string{"Properties"}, "")
	if member != nil && member.Id != "" {
		return member, nil
	}

	propertiesMap := map[string]interface{}{}
	var addressPropertyValue pb_member.PropertyInfo_ValueArray
	if addressInfo.DeliveryProvince != "" {
		addressPropertyValue = pb_member.PropertyInfo_ValueArray{
			ValueArray: &pb_member.PropertyArrayValue{
				Value: []string{
					"中国",
					addressInfo.DeliveryProvince,
					addressInfo.DeliveryCity,
					addressInfo.DeliveryDistrict,
					addressInfo.DeliveryAddress,
				},
			},
		}
		propertiesMap["address"] = &addressPropertyValue
	}
	propertiesMap["phone"] = &pb_member.PropertyInfo_ValueString{
		ValueString: &pb_member.PropertyStringValue{
			Value: receiverTel,
		},
	}
	if addressInfo.ReceiverName != "" {
		propertiesMap["name"] = &pb_member.PropertyInfo_ValueString{
			ValueString: &pb_member.PropertyStringValue{
				Value: addressInfo.ReceiverName,
			},
		}
	}

	extraBoolCond := map[string]*types.BoolValue{}
	extraBoolCond["isDefault"] = &types.BoolValue{Value: true}
	propertyInfos, getPropertyInfosErr := share.GetPropertyInfos(yzOrder.Context, propertiesMap, "name", extraBoolCond)
	if getPropertyInfosErr != nil {
		logFields["err"] = getPropertyInfosErr
		log.Warn(yzOrder.Context, logMsg, logFields)
		return nil, getPropertyInfosErr
	}

	request := &pb_member.UpsertMemberRequest{
		OriginFrom: &pb_origin.OriginInfo{
			Channel:     string(yzOrder.Store.Sid),
			ChannelName: yzOrder.Store.Name,
			OpenId:      share.ORDER_FROM_YOUZAN + ":" + receiverTel,
			Origin:      share.ORDER_FROM_YOUZAN,
		},
		Properties: propertyInfos,
	}

	receiverMember, upsertMemberErr := share.UpsertMember(yzOrder.Context, request)
	if upsertMemberErr != nil {
		logFields["err"] = upsertMemberErr
		log.Warn(yzOrder.Context, logMsg, logFields)
		return nil, upsertMemberErr
	}
	return receiverMember, nil
}

func getYouzanEncryptedStringValue(str string) string {
	if strings.HasPrefix(str, "$") && strings.HasSuffix(str, "$") {
		return ""
	}
	return str
}

func (yzOrder *YouzanOrder) GetReceiver() (*Receiver, error) {
	addressInfo := yzOrder.FullOrderInfo.AddressInfo
	receiver := &Receiver{
		Mobile:    getYouzanEncryptedStringValue(addressInfo.ReceiverTel),
		Country:   "中国",
		Name:      getYouzanEncryptedStringValue(addressInfo.ReceiverName),
		Province:  addressInfo.DeliveryProvince,
		City:      addressInfo.DeliveryCity,
		District:  addressInfo.DeliveryDistrict,
		Address:   getYouzanEncryptedStringValue(addressInfo.DeliveryAddress),
		Telephone: getYouzanEncryptedStringValue(addressInfo.ReceiverTel),
	}

	receiverMember, err := yzOrder.UpsertReceiverMember()
	if err != nil {
		return nil, err
	}
	if receiverMember != nil && receiverMember.Id != "" {
		receiver.MemberId = bson.ObjectIdHex(receiverMember.Id)
	}

	return receiver, nil
}

func (yzOrder *YouzanOrder) GetDelivery() (*Delivery, error) {
	delivery := &Delivery{}
	expressType := getExpressType(yzOrder.FullOrderInfo.OrderInfo.ExpressType)
	delivery.Type = expressType

	tradeDetail := yzOrder.OrderDetail
	if tradeDetail == nil {
		return delivery, nil
	}
	if tradeDetail == nil || len(tradeDetail.DeliveryOrder) == 0 {
		return delivery, nil
	}

	express := tradeDetail.DeliveryOrder[0].Dists
	if len(express) == 0 || core_util.IsEmpty(reflect.ValueOf(express[0].ExpressInfo)) {
		return delivery, nil
	}
	expressInfo := express[0].ExpressInfo
	traceInfo, getTraceInfoErr := yzOrder.GetTraceInfo(&expressInfo)
	if getTraceInfoErr != nil {
		return nil, getTraceInfoErr
	}
	delivery.ExpressCompany = traceInfo.Name
	delivery.ExpressNo = traceInfo.Nu

	return delivery, nil
}

func getExpressType(expressType int64) string {
	switch expressType {
	case 0:
		return "express"
	case 1:
		return "self_fetch"
	case 2:
		return "local_delivery"
	}
	return ""
}

func (yzOrder *YouzanOrder) GetTraceInfo(expressInfo *youzan.GetTradeDeliveryOrderDistsExpressInfo) (*youzan.GetLogisticsGoodsexpressResponse, error) {
	request := &youzan.GetLogisticsGoodsexpressRequest{
		ExpressID: expressInfo.ExpressID,
		ExpressNo: expressInfo.ExpressNo,
	}
	resp, err := yzOrder.Client.Logistics.GetLogisticsGoodsexpress(yzOrder.Context, request)
	if err != nil {
		if yzErr, ok := err.(*youzan.YouzanErrorResponse); ok {
			// 物流信息不存在，仍可同步订单
			if yzErr.Code == ********* {
				return resp, nil
			}
		}
		return nil, err
	}
	return resp, nil
}

func (yzOrder *YouzanOrder) GetCloseReason() string {
	closeType := yzOrder.FullOrderInfo.OrderInfo.CloseType
	switch closeType {
	case 1:
		return "过期关闭"
	case 2:
		return "标记退款"
	case 3:
		return "订单取消"
	case 4:
		return "买家取消"
	case 5:
		return "卖家取消"
	case 6:
		return "部分退款"
	case 10:
		return "无法联系上买家"
	case 11:
		return "买家误拍或重拍了"
	case 12:
		return "买家无诚意完成交易"
	case 13:
		return "已通过银行线下汇款"
	case 14:
		return "已通过同城见面交易"
	case 15:
		return "已通过货到付款交易"
	case 16:
		return "已通过网上银行直接汇款"
	case 17:
		return "已经缺货无法交易"
	default:
		return ""
	}
}

func (yzOrder *YouzanOrder) GetDealCode() string {
	return ""
}

func (yzOrder *YouzanOrder) GetOutTradeNo() string {
	if len(yzOrder.FullOrderInfo.PayInfo.OuterTransactions) > 0 {
		return yzOrder.FullOrderInfo.PayInfo.OuterTransactions[0]
	}
	return ""
}

func (yzOrder *YouzanOrder) GetCoupons() (*[]Coupon, error) {
	tradeDetail := yzOrder.OrderDetail
	if tradeDetail == nil {
		return &[]Coupon{}, nil
	}
	if tradeDetail == nil || len(tradeDetail.OrderPromotion.Order) == 0 {
		return &[]Coupon{}, nil
	}

	coupons := []Coupon{}
	for _, order := range tradeDetail.OrderPromotion.Order {
		coupons = append(coupons, Coupon{
			Id:     order.CouponID,
			Title:  order.PromotionTitle,
			Amount: share.Yuan2Fen(cast.ToFloat64(order.DiscountFee)),
		})
	}

	return &coupons, nil
}

func (yzOrder *YouzanOrder) GetUtm() (*Utm, error) {
	return nil, nil
}

func (yzOrder *YouzanOrder) GetRefundStatus() string {
	switch yzOrder.FullOrderInfo.OrderInfo.RefundState {
	case 1, 11:
		return share.REFUND_STATUS_REFUNDING
	case 2:
		return share.REFUND_STATUS_PARTIAL_REFUNDED
	case 12:
		return share.REFUND_STATUS_REFUNDED
	}

	return ""
}

func (yzOrder *YouzanOrder) GetBuyerMessage() string {
	return yzOrder.FullOrderInfo.RemarkInfo.BuyerMessage
}

func (yzOrder *YouzanOrder) GetSellerMemo() string {
	return yzOrder.FullOrderInfo.RemarkInfo.TradeMemo
}

func (yzOrder *YouzanOrder) GetOrderConsignTime() string {
	return yzOrder.FullOrderInfo.OrderInfo.ConsignTime
}

func (yzOrder *YouzanOrder) GetExtraFields() (interface{}, error) {
	return nil, nil
}

func (yzOrder *YouzanOrder) GetCondition() bson.M {
	condition := bson.M{
		"accountId": yzOrder.AccountId,
		"orderId":   yzOrder.GetOrderId(),
		"isDeleted": false,
	}

	return condition
}

func (yzOrder *YouzanOrder) IsFiltered() bool {
	return false
}

func (yzOrder *YouzanOrder) UpsertExtraTrade(trade *Trade) (*Trade, error) {
	trade.ErpOrderId = yzOrder.GetOrderId()

	if trade.MemberId.Hex() != "" && !yzOrder.IsEmptyReceiverAddress() {
		share.UpdateMemberProperties(yzOrder.Context, trade.MemberId, yzOrder.GetReceiverAddressString(), nil)
	}

	return trade, nil
}

func (yzOrder *YouzanOrder) GetTimeLayout(timeType string) string {
	switch timeType {
	case "orderCreateTime", "orderUpdateTime", "orderCompleteTime", "orderPayTime", "orderConsignTime":
		return share.LAYOUT_BY_MINUTE
	}

	return time.RFC3339
}

func (yzOrder *YouzanOrder) GetOrderSourceJsonStr() (string, error) {
	encodedOrder, err := json.Marshal(yzOrder.FullOrderInfo)
	return string(encodedOrder), err
}

func (yzOrder *YouzanOrder) AfterUpsert(trade *Trade) error {
	return nil
}

func (yzOrder *YouzanOrder) IsVirtual() bool {
	return false
}

func (yzOrder *YouzanOrder) GetChannelId() string {
	if yzOrder.Store != nil {
		return yzOrder.Store.Sid
	}
	return ""
}

func (yzOrder *YouzanOrder) GetStaffId() bson.ObjectId {
	return bson.NilObjectId
}

func (yzOrder *YouzanOrder) GetStaffCode() string {
	return ""
}
