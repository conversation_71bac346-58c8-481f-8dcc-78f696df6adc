package order

import (
	"context"
	"encoding/json"
	"mairpc/core/extension/bson"
	"mairpc/proto/client"
	"mairpc/proto/member"
	"mairpc/service/share/component"
	"mairpc/service/trade/model/store"
	"mairpc/service/trade/share"
	"strings"
	"time"
)

const (
	ORDER_FROM_DISTRIBUTION_MODULE    = "distributionModule"
	ORDER_PLATFORM_DISTRIBUTION_ORDER = "distributionOrder"
)

var (
	distributionOrderStatusMap = map[string]string{
		"UNPAID":    share.ORDER_STATUS_WAIT_BUYER_PAY,
		"PAID":      share.ORDER_STATUS_WAIT_SELLER_SEND_GOODS,
		"SHIPPED":   share.ORDER_STATUS_WAIT_BUYER_CONFIRM_GOODS,
		"COMPLETED": share.ORDER_STATUS_TRADE_BUYER_SIGNED,
		"CANCELED":  share.ORDER_STATUS_TRADE_CLOSED,
		"ACCEPTED":  share.ORDER_STATUS_WAIT_SELLER_SEND_GOODS,
	}

	distributionPaymentMap = map[string]string{
		"ONLINE":   share.ORDER_PAYMENT_TYPE_ONLINEPAY,
		"OFFLINE":  share.ORDER_PAYMENT_TYPE_OFFLINE,
		"TRANSFER": share.ORDER_PAYMENT_TYPE_BANK_TRANSFER,
		"WECHAT":   share.ORDER_PAYMENT_TYPE_WECHATPAY,
	}

	distributionRefundStatusMap = map[string]string{
		"REFUNDING": share.REFUND_STATUS_REFUNDING,
		"REFUNDED":  share.REFUND_STATUS_REFUNDED,
		"FAILED":    share.REFUND_STATUS_CLOSED,
	}
)

type distributionOrder struct {
	order component.DistributionOrderModel
	ctx   context.Context
	Store *store.TradeStore
}

func Init(ctx context.Context, order component.DistributionOrderModel) *distributionOrder {
	result := &distributionOrder{
		ctx:   ctx,
		order: order,
	}
	result.ensureTradeStore()
	return result
}

func (d *distributionOrder) ensureTradeStore() {
	tradeStore := store.CTradeStore.FindBySid(d.ctx, d.order.Channel.Id)
	if tradeStore != nil {
		d.Store = tradeStore
		return
	}
	tradeStore = &store.TradeStore{
		Type:            "others",
		Platform:        "others",
		Sid:             d.order.Channel.Id,
		Name:            d.order.Channel.Name,
		IsNew:           true,
		IsDisabled:      true,
		IsCreateChannel: false,
	}
	tradeStore.Create(d.ctx)
	d.Store = tradeStore
}

func (d distributionOrder) GetOrderId() string {
	return d.order.Number
}

func (d distributionOrder) GetOrderFrom() string {
	return ORDER_FROM_DISTRIBUTION_MODULE
}

func (d distributionOrder) GetOrderPlatform() string {
	return ORDER_PLATFORM_DISTRIBUTION_ORDER
}

func (d distributionOrder) GetOrderCreateTime() string {
	if d.order.CreatedAt.IsZero() {
		return ""
	}
	return d.order.CreatedAt.Format(time.RFC3339)
}

func (d distributionOrder) GetOrderUpdateTime() string {
	if d.order.UpdatedAt.IsZero() {
		return ""
	}
	return d.order.UpdatedAt.Format(time.RFC3339)
}

func (d distributionOrder) GetOrderCompleteTime() string {
	if d.order.CompletedAt.IsZero() {
		return ""
	}
	return d.order.CompletedAt.Format(time.RFC3339)
}

func (d distributionOrder) GetOrderPayTime() string {
	if !d.order.PaidAt.IsZero() {
		return d.order.PaidAt.Format(time.RFC3339)
	}
	for _, history := range d.order.Histories {
		if history.Status == "PAID" {
			return history.CreatedAt.Format(time.RFC3339)
		}
	}
	return ""
}

func (d distributionOrder) GetOrderStatus() string {
	return distributionOrderStatusMap[d.order.Status]
}

func (d distributionOrder) GetGoodsAmount() float64 {
	amount := int64(0)
	for _, product := range d.order.Products {
		amount += product.TotalAmount
	}
	return float64(amount) / 100
}

func (d distributionOrder) GetPostalAmount() float64 {
	return 0
}

func (d distributionOrder) GetActualAmount() float64 {
	return float64(d.order.PayAmount) / 100
}

func (d distributionOrder) GetDiscountAmount() float64 {
	amount := int64(0)
	for _, discount := range d.order.Discounts {
		amount += discount.Amount
	}
	return float64(-amount) / 100
}

func (d distributionOrder) GetPaymentType() string {
	return distributionPaymentMap[d.order.Payment]
}

func (d distributionOrder) GetBuyerId() string {
	return d.order.MemberId
}

func (d distributionOrder) GetBuyerNickname() string {
	return d.order.Buyer.Name
}

func convertDistributionProductToGoodDetail(product component.DistributionProduct) GoodsDetail {
	return GoodsDetail{
		GoodsNumber:   product.Number,
		Name:          product.Name,
		Price:         float64(product.Price) / 100,
		DiscountPrice: float64(product.PayAmount) / float64(product.Total) / 100,
		Discount: func() float64 {
			amount := int64(0)
			for _, discount := range product.Discounts {
				amount += discount.Amount
			}
			return float64(-amount) / 100
		}(),
		Count:          uint64(product.Total),
		Sku:            product.Sku.Id,
		SubtotalAmount: float64(product.PayAmount),
		ThumbnailUrl: func() string {
			if len(product.Pictures) > 0 {
				return product.Pictures[0]
			}
			return ""
		}(),
		PropertiesName: func() string {
			return strings.Join(product.Sku.Properties, " ")
		}(),
	}
}

func (d distributionOrder) GetGoodsDetail() *[]GoodsDetail {
	goods := make([]GoodsDetail, 0, len(d.order.Products))
	for _, product := range d.order.Products {
		goods = append(goods, convertDistributionProductToGoodDetail(product))
	}
	return &goods
}

func (d distributionOrder) UpsertMember() (*member.MemberDetailResponse, error) {
	resp, err := client.GetMemberServiceClient().GetMember(d.ctx, &member.MemberDetailRequest{
		Id: d.order.Buyer.Phone,
	})
	// 为了防止客户合并、注销等找不到客户详情这种订单过多导致分页获取订单时始终有这种订单，这里不返回错误。
	if err == nil {
		return resp, nil
	}
	return &member.MemberDetailResponse{
		Id: d.order.MemberId,
	}, nil
}

func (d distributionOrder) UpsertReceiverMember() (*member.MemberDetailResponse, error) {
	return nil, nil
}

func (d distributionOrder) GetReceiver() (*Receiver, error) {
	return &Receiver{
		Mobile:   d.order.Contact.Phone,
		Name:     d.order.Contact.Name,
		Province: d.order.Contact.Address.Province,
		City:     d.order.Contact.Address.City,
		District: d.order.Contact.Address.District,
		Address:  d.order.Contact.Address.Detail,
	}, nil
}

func (d distributionOrder) GetDelivery() (*Delivery, error) {
	// TODO: 调 express 接口
	return nil, nil
}

func (d distributionOrder) GetCloseReason() string {
	return ""
}

func (d distributionOrder) GetDealCode() string {
	return d.order.YeepayTradeNo
}

func (d distributionOrder) GetOutTradeNo() string {
	return d.order.TradeNo
}

func (d distributionOrder) GetCoupons() (*[]Coupon, error) {
	return nil, nil
}

func (d distributionOrder) GetUtm() (*Utm, error) {
	return nil, nil
}

func (d distributionOrder) GetRefundStatus() string {
	return distributionRefundStatusMap[d.order.RefundStatus]
}

func (d distributionOrder) GetBuyerMessage() string {
	return d.order.Message
}

func (d distributionOrder) GetSellerMemo() string {
	return d.order.Remarks
}

func (d distributionOrder) GetOrderConsignTime() string {
	for _, history := range d.order.Histories {
		if history.Status == "SHIPPED" {
			return history.CreatedAt.Format(time.RFC3339)
		}
	}
	return ""
}

func (d distributionOrder) GetExtraFields() (interface{}, error) {
	return map[string]string{
		"distributionOrderId": d.order.Id,
	}, nil
}

func (d distributionOrder) GetCondition() bson.M {
	condition := Common.GenDefaultCondition(d.ctx)
	condition["orderId"] = d.GetOrderId()
	return condition
}

func (d distributionOrder) IsFiltered() bool {
	return false
}

func (d distributionOrder) UpsertExtraTrade(trade *Trade) (*Trade, error) {
	trade.DisableEvent = true
	trade.ErpOrderId = d.GetOrderId()
	return trade, nil
}

func (d distributionOrder) GetTimeLayout(s string) string {
	return time.RFC3339
}

func (d distributionOrder) GetOrderSourceJsonStr() (string, error) {
	bytes, err := json.Marshal(d.order)
	return string(bytes), err
}

func (d distributionOrder) AfterUpsert(trade *Trade) error {
	return nil
}

func (d distributionOrder) IsVirtual() bool {
	return false
}

func (d distributionOrder) GetChannelId() string {
	return d.order.Channel.Id
}

func (d distributionOrder) GetStaffId() bson.ObjectId {
	return bson.NilObjectId
}

func (d distributionOrder) GetStaffCode() string {
	return ""
}
