package order

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/client"
	"mairpc/proto/common/origin"
	"mairpc/proto/common/types"
	pb_ec_store "mairpc/proto/ec/store"
	pb_member "mairpc/proto/member"
	member "mairpc/service/member/model"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	share_util "mairpc/service/share/util"
	"mairpc/service/trade/model/store"
	"mairpc/service/trade/share"

	"github.com/spf13/cast"
)

const (
	WESHOP_ORDER_STATUS_UNPAID                     int64 = 10  // 待付款
	WESHOP_ORDER_STATUS_PAID                       int64 = 20  // 待发货
	WESHOP_ORDER_STATUS_PARTIAL_SHIPPED            int64 = 21  // 部分发货
	WESHOP_ORDER_STATUS_SHIPPED                    int64 = 30  // 待收货
	WESHOP_ORDER_STATUS_COMPLETED                  int64 = 100 // 完成
	WESHOP_ORDER_STATUS_REFUND_COMPLETE_AND_CANCEL int64 = 200 // 全部售后完成，订单取消
	WESHOP_ORDER_STATUS_MEMBER_CANCEL_OR_EXPIRE    int64 = 250 // 用户主动取消或超时未付款自动取消

	WESHOP_DELIVERY_TYPE_SELF_EXPRESS     uint32 = 1 // 自寄快递
	WESHOP_DELIVERY_TYPE_SIGNED_EXPRESS   uint32 = 2 // 在线签约快递单
	WESHOP_DELIVERY_TYPE_NO_EXPRESS       uint32 = 3 // 虚拟商品无需物流发货
	WESHOP_DELIVERY_TYPE_UNSIGNED_EXPRESS uint32 = 4 // 在线快递散单

	WESHOP_ORDER_PAYMENT_TYPE_WECHATPAY int64 = 1 // 微信支付
	WESHOP_ORDER_PAYMENT_TYPE_PRIOR_USE int64 = 2 // 先用后付

	WESHOP_RECEIVER_INFO_USE_TEL_NUMBER     uint32 = 0 // 不使用虚拟号码
	WESHOP_RECEIVER_INFO_USE_VIRTUAL_NUMBER uint32 = 1 // 使用虚拟号码

	WESHOP_ORDER_DELIVER_METHOD_VIRTUAL int64 = 1 // 虚拟发货

	WESHOP_ORDER_SOURCE_CONTENT_TYPE_WECONNECT = 1 // 企微成员转发
)

var weshopDeliveryType = map[uint32]string{
	WESHOP_DELIVERY_TYPE_SELF_EXPRESS:     "自寄快递",
	WESHOP_DELIVERY_TYPE_SIGNED_EXPRESS:   "在线签约快递单",
	WESHOP_DELIVERY_TYPE_NO_EXPRESS:       "虚拟商品无需物流发货",
	WESHOP_DELIVERY_TYPE_UNSIGNED_EXPRESS: "在线快递散单",
}

type WeshopOrder struct {
	Order                *component.WeshopOrder
	AccountId            bson.ObjectId
	TempProvider         TempProviderData
	Store                *store.TradeStore
	Context              context.Context
	ReceiverPhone        string
	EncryptReceiverPhone string
	Member               *pb_member.MemberDetailResponse
	Trade                *Trade
}

func (o *WeshopOrder) Init(ctx context.Context, orderId string, tempProvider TempProviderData) error {
	orderDetail, err := component.WeConnect.GetOrderDetails(ctx, &component.ProxyRequest{
		QuncrmAccountId: util.GetAccountId(ctx),
		ChannelId:       tempProvider.Channel.ChannelId,
		Method:          "POST",
		Path:            "channels/ec/order/get",
		Body: &component.GetWeshopOrderDetailsRequest{
			OrderId:             orderId,
			EncodeSensitiveInfo: false,
		},
	})
	if err != nil {
		return err
	}
	o.Context = ctx
	o.Order = &orderDetail.Order
	o.AccountId = share_util.GetAccountIdAsObjectId(ctx)
	o.TempProvider = tempProvider
	o.Store = &tempProvider.Store
	o.ReceiverPhone = o.getReceiverPhoneNumber()
	o.EncryptReceiverPhone, _ = member.EncryptStringValue(o.Context, o.ReceiverPhone)
	o.Trade = CTrade.GetByCondition(ctx, bson.M{"orderId": o.Order.OrderId})
	return nil
}

func (o *WeshopOrder) AfterUpsert(trade *Trade) error {
	if trade.DisableEvent || !trade.MemberId.Valid() {
		return nil
	}
	if trade.IsNeedSendMemberEvent(o.Trade, "OrderCompleteTime") {
		trade.SendOrderCompleteEvent(o.Context)
	}
	if trade.IsNeedSendMemberEvent(o.Trade, "RefundStatus") ||
		trade.IsNeedSendMemberEvent(o.Trade, "OrderCompleteTime") {
		refundGoodsCnt, refundGoodsName, refundAmount, afterSaleGoods, refundId, isAllRefund := o.preProcessForRefundEvent()
		if refundGoodsCnt > 0 {
			if trade.sentPurchaseEvent {
				// 该 trade 在本次请求发过购买事件，需等待防止退款事件早于购买事件
				time.Sleep(time.Second * 10)
			}
			o.sendRefundOrderEvent(trade, refundGoodsCnt, refundGoodsName, refundAmount, afterSaleGoods, refundId, isAllRefund)
			o.sendRefundProductEvent(trade, afterSaleGoods, refundId, isAllRefund)
			o.afterSendRefundEvent(trade, afterSaleGoods)
		}
	}
	return nil
}

func (o *WeshopOrder) preProcessForRefundEvent() (
	refundGoodsCnt int64,
	refundGoodsName string,
	refundAmount float64,
	afterSaleGoods []map[string]any,
	refundId string,
	isAllRefund bool,
) {
	isAllRefund = true
	for _, product := range o.Order.OrderDetail.ProductInfos {
		if product.FinishAftersaleSkuCnt == 0 {
			isAllRefund = false
			continue
		}
		currentGoodsAllRefund := product.FinishAftersaleSkuCnt == product.SkuCnt
		isAllRefund = isAllRefund && currentGoodsAllRefund
		refundGoodsCnt += product.FinishAftersaleSkuCnt
		subtotalAmount := share_util.ConvertAmountUnitFromCentToYuan(product.RealPrice)
		discountPrice := share_util.DivideFloatWithRound(subtotalAmount, float64(product.SkuCnt), 2)
		afterSaleGoodsAmount := func() float64 {
			if currentGoodsAllRefund {
				return subtotalAmount
			} else {
				return share_util.MultiplyFloatWithRound(float64(product.FinishAftersaleSkuCnt), discountPrice, 2)
			}
		}()
		refundAmount = share_util.AddFloatWithRound(refundAmount, afterSaleGoodsAmount, 2)
		afterSaleGoods = append(afterSaleGoods, map[string]any{
			"productName":    product.Title,
			"productSku":     product.SkuId,
			"productSkuCode": product.SkuCode,
			"price":          share.Yuan2Fen(discountPrice),
			"count":          uint64(product.FinishAftersaleSkuCnt),
			"payAmount":      share.Yuan2Fen(afterSaleGoodsAmount),
		})
		refundGoodsName = fmt.Sprintf(",%s", product.Title)
	}
	refundGoodsName = strings.TrimPrefix(refundGoodsName, ",")
	isAllRefund = isAllRefund && refundGoodsCnt > 0
	afterSaleOrderIds := []string{}
	for _, refund := range o.Order.AftersaleDetail.AftersaleOrderList {
		afterSaleOrderIds = append(afterSaleOrderIds, refund.AftersaleOrderId)
	}
	sort.Strings(afterSaleOrderIds)
	if len(afterSaleOrderIds) > 0 {
		refundId = strings.Join(afterSaleOrderIds, ",")
	} else {
		refundId = fmt.Sprintf("refund_%s", o.GetOrderId())
	}
	return
}

func (o *WeshopOrder) sendRefundOrderEvent(
	trade *Trade,
	refundGoodsCnt int64,
	refundGoodsName string,
	refundAmount float64,
	afterSaleGoods []map[string]any,
	refundId string,
	isAllRefund bool,
) {
	if trade.OrderPayTime.IsZero() {
		return
	}
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_REFUND_ORDER + ":" + trade.OrderId,
		AccountId:  o.AccountId.Hex(),
		MemberId:   trade.MemberId.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_REFUND_ORDER,
		CreateTime: o.Order.UpdateTime,
		ChannelId:  trade.GetChannelId(o.Context),
		EventProperties: map[string]interface{}{
			"tradeId":           trade.Id.Hex(),
			"refundId":          refundId,
			"id":                trade.Id.Hex(),
			"number":            trade.OrderId,
			"orderId":           trade.OrderId,
			"platform":          trade.OrderPlatform,
			"storeId":           trade.StoreId.Hex(),
			"storeName":         trade.StoreName,
			"productName":       refundGoodsName,
			"productCounts":     refundGoodsCnt,
			"refundAmount":      share.Yuan2Fen(refundAmount),
			"refundGoodsAmount": share.Yuan2Fen(refundAmount),
			"goodsList":         afterSaleGoods,
			"goodsAmount":       share.Yuan2Fen(o.GetGoodsAmount()),
			"isAllRefund":       isAllRefund,
		},
	}
	if trade.CurrentLevel != 0 {
		eventBody.EventProperties["memberLevel"] = trade.CurrentLevel
	}
	eventBody.SendCustomerEvent(o.Context)
}

func (o *WeshopOrder) sendRefundProductEvent(
	trade *Trade,
	afterSaleGoods []map[string]any,
	refundId string,
	isAllRefund bool,
) {
	if trade.OrderPayTime.IsZero() {
		return
	}
	for _, goods := range afterSaleGoods {
		eventBody := component.CustomerEventBody{
			Id:         component.MAIEVENT_REFUND_PRODUCT + ":" + refundId + ":" + cast.ToString(goods["skuId"]),
			AccountId:  o.AccountId.Hex(),
			MemberId:   trade.MemberId.Hex(),
			ChannelId:  trade.GetChannelId(o.Context),
			MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
			SubType:    component.MAIEVENT_REFUND_PRODUCT,
			CreateTime: o.Order.UpdateTime,
			EventProperties: map[string]interface{}{
				"platform":           trade.OrderPlatform,
				"tradeId":            trade.Id.Hex(),
				"storeId":            trade.StoreId.Hex(),
				"storeName":          trade.StoreName,
				"orderId":            trade.OrderId,
				"productName":        goods["productName"],
				"productNumber":      goods["productSkuCode"],
				"productCounts":      goods["count"],
				"productSku":         goods["productSku"],
				"productExternalSku": goods["productSku"],
				"refundId":           refundId,
				"refundAmount":       goods["payAmount"],
				"id":                 trade.Id.Hex(),
				"number":             trade.OrderId,
				"outTradeId":         fmt.Sprintf("%s_%s_%s", trade.OrderId, goods["productSku"], goods["productSkuCode"]), // 商品唯一标识
				"isAllRefund":        isAllRefund,
			},
		}
		if trade.CurrentLevel != 0 {
			eventBody.EventProperties["memberLevel"] = trade.CurrentLevel
		}
		eventBody.SendCustomerEvent(o.Context)
	}
}

func (o *WeshopOrder) afterSendRefundEvent(trade *Trade, afterSaleGoods []map[string]any) {
	for _, goods := range afterSaleGoods {
		for idx, goodsDetail := range trade.GoodsDetail {
			if goodsDetail.Sku == cast.ToString(goods["productSku"]) {
				trade.GoodsDetail[idx].HasSendEvent = true
			}
		}
	}
	trade.Update(o.Context)
}

func (o *WeshopOrder) GetBuyerMessage() string {
	if o.Order.OrderDetail.ExtInfo != nil {
		return o.Order.OrderDetail.ExtInfo.CustomerNotes
	}
	return ""
}

func (o *WeshopOrder) GetSellerMemo() string {
	if o.Order.OrderDetail.ExtInfo != nil {
		return o.Order.OrderDetail.ExtInfo.MerchantNotes
	}
	return ""
}

func (o *WeshopOrder) GetChannelId() string {
	return o.TempProvider.Channel.ChannelId
}

func (o *WeshopOrder) GetCloseReason() string {
	switch o.Order.Status {
	case WESHOP_ORDER_STATUS_REFUND_COMPLETE_AND_CANCEL:
		return "全部商品售后之后，订单取消"
	case WESHOP_ORDER_STATUS_MEMBER_CANCEL_OR_EXPIRE:
		return "未付款用户主动取消或超时未付款订单自动取消"
	}
	return ""
}

func (o *WeshopOrder) GetCondition() bson.M {
	condition := bson.M{
		"accountId": o.AccountId,
		"orderId":   o.GetOrderId(),
		"isDeleted": false,
	}
	return condition
}

func (o *WeshopOrder) GetDelivery() (*Delivery, error) {
	deliveryInfo := o.Order.OrderDetail.DeliveryInfo.DeliveryProductInfo
	delivery := &Delivery{}
	if len(deliveryInfo) > 0 {
		delivery = &Delivery{
			ExpressNo:      deliveryInfo[0].WaybillId,
			ExpressCompany: deliveryInfo[0].DeliveryName,
		}
		if deliveryType, ok := weshopDeliveryType[deliveryInfo[0].DeliverType]; ok {
			delivery.Type = deliveryType
		}
	}
	return delivery, nil
}

func (o *WeshopOrder) GetOrderCreateTime() string {
	return share_util.TransUnixToTime(o.Order.CreateTime).Format(o.GetTimeLayout("orderCreateTime"))
}

func (o *WeshopOrder) GetOrderUpdateTime() string {
	return share_util.TransUnixToTime(o.Order.UpdateTime).Format(o.GetTimeLayout("orderUpdateTime"))
}

func (o *WeshopOrder) GetOrderConsignTime() string {
	deliveryInfo := o.Order.OrderDetail.DeliveryInfo.DeliveryProductInfo
	if len(deliveryInfo) > 0 && deliveryInfo[0].DeliveryTime > 0 {
		return share_util.TransUnixToTime(deliveryInfo[0].DeliveryTime).Format(o.GetTimeLayout("orderConsignTime"))
	}
	return ""
}

func (o *WeshopOrder) GetOrderCompleteTime() string {
	if o.Trade != nil && !o.Trade.OrderCompleteTime.IsZero() {
		return o.Trade.OrderCompleteTime.Format(o.GetTimeLayout("orderCompleteTime"))
	}
	if o.GetOrderStatus() == share.ORDER_STATUS_TRADE_BUYER_SIGNED {
		return share_util.TransUnixToTime(o.Order.UpdateTime).Format(o.GetTimeLayout("orderCompleteTime"))
	}
	return ""
}

func (o *WeshopOrder) GetOrderPayTime() string {
	if o.Order.OrderDetail.PayInfo == nil || o.Order.OrderDetail.PayInfo.PayTime == 0 {
		return ""
	}
	return share_util.TransUnixToTime(o.Order.OrderDetail.PayInfo.PayTime).Format(o.GetTimeLayout("orderPayTime"))
}

func (o *WeshopOrder) GetTimeLayout(timeType string) string {
	switch timeType {
	case "orderCreateTime", "orderUpdateTime", "orderConsignTime", "orderPayTime", "orderCompleteTime":
		return share.LAYOUT_BY_MINUTE
	}
	return time.RFC3339
}

func (o *WeshopOrder) GetOrderStatus() string {
	switch o.Order.Status {
	case WESHOP_ORDER_STATUS_UNPAID:
		return share.ORDER_STATUS_WAIT_BUYER_PAY
	case WESHOP_ORDER_STATUS_PAID:
		return share.ORDER_STATUS_WAIT_SELLER_SEND_GOODS
	case WESHOP_ORDER_STATUS_PARTIAL_SHIPPED, WESHOP_ORDER_STATUS_SHIPPED:
		return share.ORDER_STATUS_WAIT_BUYER_CONFIRM_GOODS
	case WESHOP_ORDER_STATUS_COMPLETED:
		return share.ORDER_STATUS_TRADE_BUYER_SIGNED
	case WESHOP_ORDER_STATUS_REFUND_COMPLETE_AND_CANCEL, WESHOP_ORDER_STATUS_MEMBER_CANCEL_OR_EXPIRE:
		return share.ORDER_STATUS_TRADE_CLOSED
	}
	return ""
}

func (o *WeshopOrder) GetGoodsAmount() float64 {
	return share_util.ConvertAmountUnitFromCentToYuan(o.Order.OrderDetail.PriceInfo.ProductPrice)
}

func (o *WeshopOrder) GetPostalAmount() float64 {
	if o.Order.OrderDetail.PriceInfo.IsChangeFreight && o.Order.OrderDetail.PriceInfo.ChangeDownPrice > 0 {
		return share_util.ConvertAmountUnitFromCentToYuan(o.Order.OrderDetail.PriceInfo.ChangeFreight)
	}
	return share_util.ConvertAmountUnitFromCentToYuan(o.Order.OrderDetail.PriceInfo.Freight)
}

func (o *WeshopOrder) GetActualAmount() float64 {
	return share_util.ConvertAmountUnitFromCentToYuan(o.Order.OrderDetail.PriceInfo.OrderPrice)
}

func (o *WeshopOrder) GetDiscountAmount() float64 {
	originalOrderPrice := share_util.AddFloatWithRound(o.GetGoodsAmount(), o.GetPostalAmount(), 3)
	return share_util.SubtractFloatWithRound(originalOrderPrice, o.GetActualAmount(), 2)
}

func (o *WeshopOrder) GetPaymentType() string {
	paymentMethod := o.Order.OrderDetail.PayInfo.PaymentMethod
	if paymentMethod == WESHOP_ORDER_PAYMENT_TYPE_WECHATPAY || paymentMethod == WESHOP_ORDER_PAYMENT_TYPE_PRIOR_USE {
		return share.ORDER_PAYMENT_TYPE_WECHATPAY
	}
	return share.ORDER_PAYMENT_TYPE_OTHERS
}

func (o *WeshopOrder) GetBuyerNickname() string {
	return ""
}

func (o *WeshopOrder) GetGoodsDetail() *[]GoodsDetail {
	allGoodsDetail := []GoodsDetail{}
	for _, product := range o.Order.OrderDetail.ProductInfos {
		price := share_util.ConvertAmountUnitFromCentToYuan(product.SalePrice)
		subtotalAmount := share_util.ConvertAmountUnitFromCentToYuan(product.RealPrice)
		discountPrice := share_util.DivideFloatWithRound(subtotalAmount, float64(product.SkuCnt), 2)
		goodDetail := GoodsDetail{
			GoodsNumber:    product.ProductId,
			Name:           product.Title,
			Price:          price,
			DiscountPrice:  discountPrice,
			Discount:       share_util.SubtractFloatWithRound(price, discountPrice, 2),
			Oid:            o.Order.OrderId,
			Count:          uint64(product.SkuCnt),
			Sku:            product.SkuId,
			OutSkuId:       product.OutSkuId,
			SubtotalAmount: subtotalAmount,
			RefundAmount: func() float64 {
				refundCount := product.OnAftersaleSkuCnt + product.FinishAftersaleSkuCnt
				return share_util.MultiplyFloatWithRound(float64(refundCount), discountPrice, 2)
			}(),
			ThumbnailUrl: product.ThumbImg,
			AfterSaleStatus: func() string {
				if product.OnAftersaleSkuCnt > 0 {
					return share.ORDER_AFTER_SALE_STATUS_REFUNDING
				}
				if product.FinishAftersaleSkuCnt > 0 && product.FinishAftersaleSkuCnt == product.SkuCnt {
					return share.ORDER_AFTER_SALE_STATUS_REFUNDED
				}
				return ""
			}(),
		}
		allGoodsDetail = append(allGoodsDetail, goodDetail)
	}
	return &allGoodsDetail
}

func (o *WeshopOrder) UpsertMember() (*pb_member.MemberDetailResponse, error) {
	if o.Store.IsNotCreateMember {
		return nil, nil
	}
	var (
		request *pb_member.UpsertMemberRequest
		err     error
	)
	if o.Store.IsBelongToReceiver {
		if o.ReceiverPhone == "" {
			return nil, nil
		}
		request, err = o.getUpsertReceiverMemberRequest()
	} else {
		if o.Order.OpenId == "" {
			return nil, nil
		}
		request, err = o.getUpsertMemberRequest()
	}
	if err != nil {
		return nil, err
	}
	member, err := o.upsertMember(request)
	if err != nil {
		return nil, err
	}
	o.Member = member
	return member, nil
}

func (o *WeshopOrder) getUpsertMemberRequest() (*pb_member.UpsertMemberRequest, error) {
	propertiesMap := map[string]interface{}{}
	if o.Store.IsMergeReceiver {
		propertiesMap = o.getReceiverPropertiesMap()
	}
	extraBoolCond := map[string]*types.BoolValue{}
	extraBoolCond["isDefault"] = &types.BoolValue{Value: true}
	propertiesInfos, err := share.GetPropertyInfos(o.Context, propertiesMap, "name", extraBoolCond)
	if err != nil {
		return nil, err
	}
	request := &pb_member.UpsertMemberRequest{
		OriginFrom: &origin.OriginInfo{
			Channel:     o.Store.Sid,
			ChannelName: o.Store.Name,
			OpenId:      o.GetBuyerId(),
			UnionId:     o.Order.UnionId,
			Origin:      o.GetOrigin(),
		},
		Properties: propertiesInfos,
	}
	if o.Store.IsMergeReceiver && o.ReceiverPhone != "" {
		request.OtherSocials = []*origin.OriginInfo{
			{
				Channel:     o.Store.Sid,
				ChannelName: o.Store.Name,
				OpenId:      o.GetOrigin() + ":" + o.EncryptReceiverPhone,
				Origin:      o.GetOrigin(),
				Nickname:    o.Order.OrderDetail.DeliveryInfo.AddressInfo.UserName,
			},
		}
	}
	return request, nil
}

func (o *WeshopOrder) getUpsertReceiverMemberRequest() (*pb_member.UpsertMemberRequest, error) {
	propertiesMap := o.getReceiverPropertiesMap()
	extraBoolCond := map[string]*types.BoolValue{}
	extraBoolCond["isDefault"] = &types.BoolValue{Value: true}
	propertiesInfos, err := share.GetPropertyInfos(o.Context, propertiesMap, "name", extraBoolCond)
	if err != nil {
		return nil, err
	}
	return &pb_member.UpsertMemberRequest{
		OriginFrom: &origin.OriginInfo{
			Channel:     o.Store.Sid,
			ChannelName: o.Store.Name,
			OpenId:      o.GetOrigin() + ":" + o.EncryptReceiverPhone,
			Origin:      o.GetOrigin(),
			Nickname:    o.Order.OrderDetail.DeliveryInfo.AddressInfo.UserName,
		},
		Properties: propertiesInfos,
	}, nil
}

func (o *WeshopOrder) getReceiverPropertiesMap() map[string]interface{} {
	propertiesMap := map[string]interface{}{}
	receiverInfo := o.Order.OrderDetail.DeliveryInfo.AddressInfo
	if receiverInfo.UserName != "" {
		propertiesMap["name"] = &pb_member.PropertyInfo_ValueString{
			ValueString: &pb_member.PropertyStringValue{
				Value: receiverInfo.UserName,
			},
		}
	}
	if o.ReceiverPhone != "" {
		propertiesMap["phone"] = &pb_member.PropertyInfo_ValueString{
			ValueString: &pb_member.PropertyStringValue{
				Value: o.ReceiverPhone,
			},
		}
	}
	if receiverInfo.ProvinceName != "" {
		propertiesMap["address"] = &pb_member.PropertyInfo_ValueArray{
			ValueArray: &pb_member.PropertyArrayValue{
				Value: []string{
					receiverInfo.ProvinceName,
					receiverInfo.CityName,
					receiverInfo.CountyName,
					receiverInfo.DetailInfo,
					receiverInfo.HouseNumber,
				},
			},
		}
	}
	return propertiesMap
}

func (o *WeshopOrder) getReceiverPhoneNumber() string {
	receiverInfo := o.Order.OrderDetail.DeliveryInfo.AddressInfo
	if receiverInfo.UseTelNumber == WESHOP_RECEIVER_INFO_USE_VIRTUAL_NUMBER {
		return ""
	}
	if o.Order.OrderDetail.DeliveryInfo.DeliverMethod == WESHOP_ORDER_DELIVER_METHOD_VIRTUAL {
		if validators.CValidator.IsPhone(receiverInfo.VirtualOrderTelNumber, nil) {
			return receiverInfo.VirtualOrderTelNumber
		}
		return ""
	}
	if validators.CValidator.IsPhone(receiverInfo.TelNumber, nil) {
		return receiverInfo.TelNumber
	}
	return ""
}

func (o *WeshopOrder) upsertMember(req *pb_member.UpsertMemberRequest) (*pb_member.MemberDetailResponse, error) {
	member, _ := share.GetMemberByOpenId(o.Context, req.OriginFrom.OpenId, []string{"Properties"}, "")
	if member != nil && member.Id != "" {
		return member, nil
	}
	return share.UpsertMember(o.Context, req)
}

func (o *WeshopOrder) GetReceiver() (*Receiver, error) {
	receiverInfos := o.Order.OrderDetail.DeliveryInfo.AddressInfo
	if receiverInfos.UserName == "" {
		return nil, nil
	}
	receiver := &Receiver{
		Name:     receiverInfos.UserName,
		Country:  "中国",
		Province: receiverInfos.ProvinceName,
		City:     receiverInfos.CityName,
		District: receiverInfos.CountyName,
		Address:  receiverInfos.DetailInfo,
	}
	if o.ReceiverPhone != "" {
		receiver.Mobile = o.ReceiverPhone
	}
	if o.Store.IsBelongToReceiver || o.Store.IsMergeReceiver {
		if o.Member != nil && o.Member.Id != "" {
			receiver.MemberId = bson.ObjectIdHex(o.Member.Id)
		}
		return receiver, nil
	}
	receiverMember, err := o.UpsertReceiverMember()
	if err != nil {
		return nil, err
	}
	if receiverMember != nil && receiverMember.Id != "" {
		receiver.MemberId = bson.ObjectIdHex(receiverMember.Id)
	}
	return receiver, nil
}

func (o *WeshopOrder) UpsertExtraTrade(trade *Trade) (*Trade, error) {
	trade.ErpOrderId = o.GetOrderId()
	return trade, nil
}

func (o *WeshopOrder) UpsertReceiverMember() (*pb_member.MemberDetailResponse, error) {
	if o.Store.IsNotCreateMember || o.ReceiverPhone == "" {
		return nil, nil
	}
	request, err := o.getUpsertReceiverMemberRequest()
	if err != nil {
		return nil, err
	}
	return o.upsertMember(request)
}

func (o *WeshopOrder) GetRefundStatus() string {
	if o.GetOrderStatus() == share.ORDER_STATUS_TRADE_CLOSED {
		return share.REFUND_STATUS_REFUNDED
	}
	return ""
}

func (o *WeshopOrder) IsVirtual() bool {
	if o.Order.OrderDetail.DeliveryInfo.DeliverMethod == WESHOP_ORDER_DELIVER_METHOD_VIRTUAL {
		return true
	}
	return false
}

func (o *WeshopOrder) GetOrderId() string {
	return o.Order.OrderId
}

func (o *WeshopOrder) GetBuyerId() string {
	return o.Order.OpenId
}

func (o *WeshopOrder) GetOrigin() string {
	return constant.WESHOP
}

func (o *WeshopOrder) GetOrderFrom() string {
	return share.ORDER_PLATFORM_WESHOP
}

func (o *WeshopOrder) GetOrderPlatform() string {
	return share.ORDER_PLATFORM_WESHOP
}

func (o *WeshopOrder) GetOrderSourceJsonStr() (string, error) {
	encodedOrder, err := json.Marshal(o.Order)
	return string(encodedOrder), err
}

func (o *WeshopOrder) IsFiltered() bool {
	return false
}

func (o *WeshopOrder) GetDealCode() string {
	return ""
}

func (o *WeshopOrder) GetOutTradeNo() string {
	return ""
}

func (o *WeshopOrder) GetCoupons() (*[]Coupon, error) {
	return nil, nil
}

func (o *WeshopOrder) GetUtm() (*Utm, error) {
	return nil, nil
}

func (o *WeshopOrder) GetExtraFields() (interface{}, error) {
	return nil, nil
}

func (o *WeshopOrder) GetStaffId() bson.ObjectId {
	staffCode := o.GetStaffCode()
	if staffCode == "" {
		return bson.NilObjectId
	}
	staff, err := client.GetEcStoreServiceClient().GetStaff(o.Context, &pb_ec_store.StaffDetailRequest{
		StaffId: staffCode,
	})
	if err != nil {
		log.Warn(o.Context, "Failed to get staff by staffNo", log.Fields{
			"errMsg":  err.Error(),
			"staffNo": staffCode,
		})
		return bson.NilObjectId
	}
	return bson.ObjectIdHex(staff.Id)
}

func (o *WeshopOrder) GetStaffCode() string {
	if len(o.Order.OrderDetail.SourceInfos) == 0 {
		return ""
	}
	for _, sourceInfo := range o.Order.OrderDetail.SourceInfos {
		if sourceInfo.ContentType != WESHOP_ORDER_SOURCE_CONTENT_TYPE_WECONNECT {
			continue
		}
		return sourceInfo.ContentId
	}
	return ""
}
