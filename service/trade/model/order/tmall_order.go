package order

import (
	"context"
	"encoding/json"
	"fmt"
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/core/validators"
	pb_origin "mairpc/proto/common/origin"
	"mairpc/proto/common/types"
	pb_member "mairpc/proto/member"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"
	"mairpc/service/trade/model/store"
	"mairpc/service/trade/share"
	"math"
	"sort"
	"strings"
	"time"

	"github.com/spf13/cast"
)

const (
	TMALL_ORDER_UPSERT_MEMBER_LOCK = "%s:trade:sync-tmall-order:%s"
)

type TmallTrade struct {
	Tid            string `json:"Tid"`
	TaobaoUserId   string `json:"TaobaoUserId"`
	Data           string `json:"Data"`
	CreatedAt      string `json:"CreatedAt"`
	OrderUpdatedAt string `json:"OrderUpdatedAt"`
	UpdatedAt      string `json:"UpdatedAt"`
	IsDeleted      bool   `json:"IsDeleted"`
}
type TmallTradeData struct {
	PicPath                   string           `json:"pic_path"`
	Payment                   string           `json:"payment"`
	SnapshotURL               string           `json:"snapshot_url"`
	SellerRate                bool             `json:"seller_rate"`
	PostFee                   string           `json:"post_fee"`
	ReceiverState             string           `json:"receiver_state"`
	ConsignTime               string           `json:"consign_time"`
	AvailableConfirmFee       string           `json:"available_confirm_fee"`
	ReceivedPayment           string           `json:"received_payment"`
	TimeoutActionTime         string           `json:"timeout_action_time"`
	PromotionDetails          PromotionDetails `json:"promotion_details"`
	Tid                       string           `json:"tid"`
	Num                       int64            `json:"num"`
	NumIid                    int64            `json:"num_iid"`
	Status                    string           `json:"status"`
	Title                     string           `json:"title"`
	Type                      string           `json:"type"`
	Price                     string           `json:"price"`
	DiscountFee               string           `json:"discount_fee"`
	HasPostFee                bool             `json:"has_post_fee"`
	TotalFee                  string           `json:"total_fee"`
	Created                   string           `json:"created"`
	PayTime                   string           `json:"pay_time"`
	Modified                  string           `json:"modified"`
	EndTime                   string           `json:"end_time"`
	BuyerMessage              string           `json:"buyer_message"`
	BuyerMemo                 string           `json:"buyer_memo"`
	SellerMemo                string           `json:"seller_memo"`
	StepTradeStatus           string           `json:"step_trade_status"`
	StepPaidFee               string           `json:"step_paid_fee"`
	BuyerOpenUid              string           `json:"buyer_open_uid"`
	ShippingType              string           `json:"shipping_type"`
	AdjustFee                 string           `json:"adjust_fee"`
	TradeFrom                 string           `json:"trade_from"`
	CanRate                   bool             `json:"can_rate"`
	ServiceOrders             ServiceOrders    `json:"service_orders"`
	BuyerRate                 bool             `json:"buyer_rate"`
	SellerCanRate             bool             `json:"seller_can_rate"`
	ReceiverName              string           `json:"receiver_name"`
	ReceiverCountry           string           `json:"receiver_country"`
	ReceiverDistrict          string           `json:"receiver_district"`
	ReceiverCity              string           `json:"receiver_city"`
	ReceiverTown              string           `json:"receiver_town"`
	ReceiverAddress           string           `json:"receiver_address"`
	ReceiverMobile            string           `json:"receiver_mobile"`
	ReceiverPhone             string           `json:"receiver_phone"`
	Orders                    Orders           `json:"orders"`
	SellerNick                string           `json:"seller_nick"`
	BuyerNick                 string           `json:"buyer_nick"`
	ExpandcardInfo            ExpandcardInfo   `json:"expandcard_info"`
	ExpandCardBasicPrice      string           `json:"expand_card_basic_price"`
	ExpandCardExpandPrice     string           `json:"expand_card_expand_price"`
	ExpandCardBasicPriceUsed  string           `json:"expand_card_basic_price_used"`
	ExpandCardExpandPriceUsed string           `json:"expand_card_expand_price_used"`
	IsPartConsign             bool             `json:"is_part_consign"`
	RxAuditStatus             string           `json:"rx_audit_status"`
	CouponFee                 int64            `json:"coupon_fee"`
	Ouid                      string           `json:"ouid"`
	SellerFlag                int64            `json:"seller_flag"`
}
type ExpandcardInfo struct {
	BasicPrice      string `json:"basic_price"`
	ExpandPrice     string `json:"expand_price"`
	BasicPriceUsed  string `json:"basic_price_used"`
	ExpandPriceUsed string `json:"expand_price_used"`
}
type Orders struct {
	Order []Order `json:"order"`
}
type Order struct {
	PicPath                           string `json:"pic_path"`
	RefundStatus                      string `json:"refund_status"`
	OuterIid                          string `json:"outer_iid"`
	SnapshotURL                       string `json:"snapshot_url"`
	TimeoutActionTime                 string `json:"timeout_action_time"`
	BuyerRate                         bool   `json:"buyer_rate"`
	SellerRate                        bool   `json:"seller_rate"`
	Cid                               int64  `json:"cid"`
	OID                               uint64 `json:"oid"`
	Status                            string `json:"status"`
	Title                             string `json:"title"`
	Price                             string `json:"price"`
	NumIid                            int64  `json:"num_iid"`
	SkuID                             string `json:"sku_id"`
	Num                               int64  `json:"num"`
	OuterSkuID                        string `json:"outer_sku_id"`
	OrderFrom                         string `json:"order_from"`
	TotalFee                          string `json:"total_fee"`
	Payment                           string `json:"payment"`
	DiscountFee                       string `json:"discount_fee"` // 子订单级订单优惠金额
	AdjustFee                         string `json:"adjust_fee"`
	SkuPropertiesName                 string `json:"sku_properties_name"`
	RefundID                          string `json:"refund_id"`
	IsOversold                        bool   `json:"is_oversold"`
	EndTime                           string `json:"end_time"`
	ConsignTime                       string `json:"consign_time"`
	ShippingType                      string `json:"shipping_type"`
	LogisticsCompany                  string `json:"logistics_company"`
	InvoiceNo                         string `json:"invoice_no"`
	DivideOrderFee                    string `json:"divide_order_fee"`
	PartMjzDiscount                   string `json:"part_mjz_discount"` // 优惠分摊
	BindOids                          string `json:"bind_oids"`
	ExpandCardExpandPriceUsedSuborder string `json:"expand_card_expand_price_used_suborder"`
	ExpandCardBasicPriceUsedSuborder  string `json:"expand_card_basic_price_used_suborder"`
	ItemMealID                        string `json:"item_meal_id"`
	Customization                     string `json:"customization"`
}
type PromotionDetails struct {
	PromotionDetails []PromotionDetail `json:"promotion_details"`
}
type PromotionDetail struct {
	ID            float64 `json:"id"`
	PromotionName string  `json:"promotion_name"`
	DiscountFee   string  `json:"discount_fee"`
	GiftItemName  string  `json:"gift_item_name"`
	GiftItemID    string  `json:"gift_item_id"`
	GiftItemNum   string  `json:"gift_item_num"`
	PromotionDesc string  `json:"promotion_desc"`
	PromotionID   string  `json:"promotion_id"`
}
type ServiceOrders struct {
	ServiceOrders interface{} `json:"service_orders"`
}
type TmallOrder struct {
	Context       context.Context
	OrderInfo     *TmallTrade
	AccountId     bson.ObjectId
	StoreId       bson.ObjectId
	Store         *store.TradeStore
	SyncHistoryId bson.ObjectId
	AccessToken   string
	OrderData     *TmallTradeData
	Member        *pb_member.MemberDetailResponse
	Trade         *Trade // 数据库查找的已存在的订单
}

func (t *TmallOrder) Init(ctx context.Context, orderInfo *TmallTrade, tempProvider TempProviderData) error {
	t.Context = ctx
	t.OrderInfo = orderInfo
	t.AccountId = util.GetAccountIdAsObjectId(ctx)
	t.StoreId = tempProvider.Store.Id
	t.SyncHistoryId = tempProvider.SyncHistories[1].Id
	t.Store = &tempProvider.Store

	decoder := json.NewDecoder(strings.NewReader(orderInfo.Data))
	decoder.UseNumber()
	t.OrderData = &TmallTradeData{}
	err := decoder.Decode(t.OrderData)
	if err != nil {
		return err
	}

	mobile := t.OrderData.ReceiverMobile
	if mobile != "" {
		if pos := strings.Index(mobile, "86-"); pos != -1 {
			t.OrderData.ReceiverMobile = mobile[3:]
		}
	}

	t.Trade = CTrade.GetByCondition(t.Context, bson.M{"orderId": t.OrderData.Tid})
	return nil
}

func (t *TmallOrder) GetOrderId() string {
	return t.OrderInfo.Tid
}

func (t *TmallOrder) GetOrderFrom() string {
	return share.ORDER_FROM_TMALL
}

func (t *TmallOrder) GetOrderPlatform() string {
	return share.ORDER_PLATFORM_TMALL
}

func (t *TmallOrder) GetOrderCreateTime() string {
	return t.OrderData.Created
}

func (t *TmallOrder) GetOrderUpdateTime() string {
	return t.OrderData.Modified
}

func (t *TmallOrder) GetOrderCompleteTime() string {
	if t.GetOrderStatus() == share.ORDER_STATUS_TRADE_BUYER_SIGNED {
		return t.OrderData.EndTime
	}
	return ""
}

func (t *TmallOrder) GetOrderPayTime() string {
	return t.OrderData.PayTime
}

// TRADE_NO_CREATE_PAY(没有创建支付宝交易)、WAIT_BUYER_PAY(等待买家付款)、PAY_PENDING(国际信用卡支付付款确认中)
// SELLER_CONSIGNED_PART(卖家部分发货)、WAIT_SELLER_SEND_GOODS(等待卖家发货,即:买家已付款)、PAID_FORBID_CONSIGN(拼团中订单或者发货强管控的订单，已付款但禁止发货)
// WAIT_BUYER_CONFIRM_GOODS(等待买家确认收货,即:卖家已发货)
// TRADE_BUYER_SIGNED(买家已签收,货到付款专用)、TRADE_FINISHED(交易成功)
// TRADE_CLOSED(付款以后用户退款成功，交易自动关闭)、TRADE_CLOSED_BY_TAOBAO(付款以前，卖家或买家主动关闭交易)
// WAIT_PRE_AUTH_CONFIRM(0元购合约中)
func (t *TmallOrder) GetOrderStatus() string {
	switch t.OrderData.Status {
	case "WAIT_BUYER_PAY", "TRADE_NO_CREATE_PAY", "PAY_PENDING":
		return share.ORDER_STATUS_WAIT_BUYER_PAY
	case "SELLER_CONSIGNED_PART", "WAIT_SELLER_SEND_GOODS", "PAID_FORBID_CONSIGN":
		return share.ORDER_STATUS_WAIT_SELLER_SEND_GOODS
	case "WAIT_BUYER_CONFIRM_GOODS":
		return share.ORDER_STATUS_WAIT_BUYER_CONFIRM_GOODS
	case "TRADE_BUYER_SIGNED", "TRADE_FINISHED":
		return share.ORDER_STATUS_TRADE_BUYER_SIGNED
	case "TRADE_CLOSED", "TRADE_CLOSED_BY_TAOBAO":
		return share.ORDER_STATUS_TRADE_CLOSED
	}
	return ""
}

func (t *TmallOrder) GetGoodsAmount() float64 {
	return cast.ToFloat64(t.OrderData.TotalFee)
}

func (t *TmallOrder) GetPostalAmount() float64 {
	return cast.ToFloat64(t.OrderData.PostFee)
}

func (t *TmallOrder) GetActualAmount() float64 {
	return cast.ToFloat64(t.OrderData.Payment)
}

func (t *TmallOrder) GetDiscountAmount() float64 {
	if t.GetOrderStatus() == share.ORDER_STATUS_TRADE_CLOSED && share.Yuan2Fen(t.GetActualAmount()) == 0 {
		return 0
	}
	discountAmount := 0.0
	for _, order := range t.OrderData.Orders.Order {
		discountFee := cast.ToFloat64(order.DiscountFee)
		discountAmount += discountFee
		if order.PartMjzDiscount != "" {
			discountAmount += cast.ToFloat64(order.PartMjzDiscount)
		}
		if order.AdjustFee != "" && order.AdjustFee != "0.00" {
			adjustFee := cast.ToFloat64(order.AdjustFee)
			if discountFee < 0 && adjustFee < 0 && share.Yuan2Fen(math.Abs(adjustFee)) == share.Yuan2Fen(math.Abs(discountFee)) {
				discountAmount -= adjustFee
			}
		}
	}
	discountAmountFen := share.Yuan2Fen(discountAmount)
	return float64(discountAmountFen) / 100
}

func (t *TmallOrder) GetPaymentType() string {
	if t.OrderData.PayTime != "" {
		if t.OrderData.Status == "TRADE_BUYER_SIGNED" {
			return share.ORDER_PAYMENT_TYPE_CODPAY
		}
		return share.ORDER_PAYMENT_TYPE_ONLINEPAY
	}
	return ""
}

func (t *TmallOrder) GetBuyerId() string {
	if t.Trade == nil || t.Trade.BuyerId == "" || strings.Index(t.Trade.BuyerId, "*") != -1 {
		return t.OrderData.Ouid
	}

	return t.Trade.BuyerId
}

func (t *TmallOrder) GetBuyerNickname() string {
	if t.Trade == nil || t.Trade.BuyerNickname == "" || strings.Index(t.Trade.BuyerNickname, "*") != -1 {
		return t.OrderData.BuyerNick
	}
	return t.Trade.BuyerNickname
}

func (t *TmallOrder) GetGoodsDetail() *[]GoodsDetail {
	// 更新 GoodsDetail 时保证 HasSendEvent 字段不会丢失
	hasSendEventGoodOids := []string{}
	if t.Trade != nil {
		for _, goodDetail := range t.Trade.GoodsDetail {
			if goodDetail.HasSendEvent {
				hasSendEventGoodOids = append(hasSendEventGoodOids, goodDetail.Oid)
			}
		}
	}
	allGoodsDetail := []GoodsDetail{}
	for _, order := range t.OrderData.Orders.Order {
		discountFee := cast.ToFloat64(order.DiscountFee)
		discount := discountFee
		actualAmount := cast.ToFloat64(order.TotalFee)
		if order.PartMjzDiscount != "" {
			partMjzDiscount := cast.ToFloat64(order.PartMjzDiscount)
			discount = util.AddFloat(discount, partMjzDiscount)
			actualAmount = util.SubtractFloat(actualAmount, partMjzDiscount)
		}
		if order.AdjustFee != "" && order.AdjustFee != "0.00" {
			adjustFee := cast.ToFloat64(order.AdjustFee)
			if discountFee < 0 && adjustFee < 0 && share.Yuan2Fen(math.Abs(adjustFee)) == share.Yuan2Fen(math.Abs(discountFee)) {
				discount = util.SubtractFloat(discount, adjustFee)
			}
		}

		// https://gitlab.maiscrm.com/mai/home/<USER>/issues/55360
		payment := cast.ToFloat64(order.Payment)
		if payment < actualAmount && payment > 0 {
			actualAmount = payment
		}

		detail := GoodsDetail{
			GoodsNumber:     order.OuterIid,
			Name:            order.Title,
			Price:           cast.ToFloat64(order.Price),
			DiscountPrice:   cast.ToFloat64(actualAmount) / cast.ToFloat64(order.Num),
			Discount:        discount,
			Oid:             cast.ToString(order.OID),
			Count:           cast.ToUint64(order.Num),
			Sku:             order.SkuID,
			OutSkuId:        order.OuterSkuID,
			SubtotalAmount:  cast.ToFloat64(actualAmount),
			PropertiesName:  strings.Replace(order.SkuPropertiesName, ";", " ", -1),
			AfterSaleStatus: getAfterSaleStatus(order.RefundStatus),
			ThumbnailUrl:    order.PicPath,
		}
		if util.StrInArray(cast.ToString(order.OID), &hasSendEventGoodOids) {
			detail.HasSendEvent = true
		}

		allGoodsDetail = append(allGoodsDetail, detail)
	}
	return &allGoodsDetail
}

// WAIT_SELLER_AGREE(买家已经申请退款，等待卖家同意)
// WAIT_BUYER_RETURN_GOODS(卖家已经同意退款，等待买家退货)
// WAIT_SELLER_CONFIRM_GOODS(买家已经退货，等待卖家确认收货)
// SELLER_REFUSE_BUYER(卖家拒绝退款)
// CLOSED(退款关闭)
// SUCCESS(退款成功)
func getAfterSaleStatus(status string) string {
	switch status {
	case "WAIT_SELLER_AGREE", "WAIT_BUYER_RETURN_GOODS", "WAIT_SELLER_CONFIRM_GOODS":
		return share.ORDER_AFTER_SALE_STATUS_REFUNDING
	case "SELLER_REFUSE_BUYER":
		return share.ORDER_AFTER_SALE_STATUS_REJECTED
	case "CLOSED":
		return share.ORDER_AFTER_SALE_STATUS_CANCELED
	case "SUCCESS":
		return share.ORDER_AFTER_SALE_STATUS_REFUNDED
	}
	return ""
}

func (t *TmallOrder) GetOrigin() string {
	return constant.TAOBAO
}

func (t *TmallOrder) UpsertMember() (*pb_member.MemberDetailResponse, error) {
	if t.Store.IsNotCreateMember {
		return nil, nil
	}
	if t.Trade != nil && t.Trade.MemberId.Hex() != "" {
		member, err := share.GetMemberById(t.Context, t.Trade.MemberId, []string{}, "")
		if err != nil && !strings.Contains(err.Error(), "2000007") {
			return nil, err
		}
		if member != nil && member.Id != "" {
			t.Member = member
			return member, nil
		}
	}

	params := &pb_member.UpsertMemberRequest{}
	var err error
	if t.Store.IsBelongToReceiver {
		if !t.isReceiverMobileExists() {
			return nil, nil
		}
		if t.isDesensitized("Receiver name", t.GetReceiverInfo().Name) {
			return nil, nil
		}
		params, err = t.GetUpsertReceiverMemberParams()
	} else {
		if t.isDesensitized("User name", t.GetBuyerNickname()) {
			return nil, nil
		}
		if t.GetBuyerId() == "" {
			log.Warn(t.Context, "Buyer id not found when upsert tmall member", log.Fields{
				"accountId":     t.AccountId.Hex(),
				"orderId":       t.GetOrderId(),
				"storeId":       t.StoreId.Hex(),
				"syncHistoryId": t.SyncHistoryId.Hex(),
			})
			return nil, nil
		}
		params, err = t.getUpsertMemberParams()
	}
	if err != nil {
		return nil, err
	}

	member, err := t.UpsertMemberDetail(params)
	if err != nil {
		return nil, err
	}
	t.Member = member
	return member, nil
}

func (t *TmallOrder) isDesensitized(fieldName, fieldValue string) bool {
	if strings.Index(fieldValue, "*") != -1 {
		log.Warn(t.Context, fieldName+" is desensitized when upsert tmall member", log.Fields{
			"accountId":     t.AccountId.Hex(),
			fieldName:       fieldValue,
			"orderId":       t.GetOrderId(),
			"storeId":       t.StoreId.Hex(),
			"syncHistoryId": t.SyncHistoryId.Hex(),
		})
		return true
	}
	return false
}

func (t *TmallOrder) getUpsertMemberParams() (*pb_member.UpsertMemberRequest, error) {
	propertiesMap := map[string]interface{}{}
	// 将收货人信息合并到下单人身上
	if t.Store.IsMergeReceiver {
		propertiesMap = t.getReceiverPropertiesMap()
	}
	username := t.GetBuyerNickname()
	if username != "" {
		propertiesMap["name"] = &pb_member.PropertyInfo_ValueString{
			ValueString: &pb_member.PropertyStringValue{
				Value: username,
			},
		}
	}

	extraBoolCond := map[string]*types.BoolValue{}
	extraBoolCond["isDefault"] = &types.BoolValue{Value: true}
	propertyInfos, getPropertyInfosErr := share.GetPropertyInfos(t.Context, propertiesMap, "name", extraBoolCond)
	if getPropertyInfosErr != nil {
		return nil, getPropertyInfosErr
	}

	request := &pb_member.UpsertMemberRequest{
		OriginFrom: &pb_origin.OriginInfo{
			Channel:     t.Store.Sid,
			ChannelName: t.Store.Name,
			OpenId:      t.GetBuyerId(),
			Origin:      t.GetOrigin(),
			Nickname:    username,
		},
		Properties: propertyInfos,
	}
	return request, nil
}

func (t *TmallOrder) UpsertMemberDetail(params *pb_member.UpsertMemberRequest) (*pb_member.MemberDetailResponse, error) {
	ok, del := util.TryToGetSpinRedisLock(fmt.Sprintf(TMALL_ORDER_UPSERT_MEMBER_LOCK, t.AccountId.Hex(), params.OriginFrom.OpenId), 10, time.Second*3)
	if !ok {
		return nil, errors.NewTooManyRequestsError("openId")
	}
	defer del()
	member, _ := share.GetMemberByOpenId(t.Context, params.OriginFrom.OpenId, []string{"Properties"}, "")
	if member != nil && member.Id != "" {
		return member, nil
	}

	resultMember, upsertMemberErr := share.UpsertMember(t.Context, params)
	if upsertMemberErr != nil {
		return nil, upsertMemberErr
	}
	if t.Store.IsMergeReceiver {
		if err := t.BindReceiverSocial(resultMember.Id); err != nil {
			return nil, err
		}
	}
	return resultMember, nil
}

func (t *TmallOrder) BindReceiverSocial(memberId string) error {
	request := &pb_member.BindChannelRequest{
		MemberId:    memberId,
		ChannelId:   t.Store.Sid,
		ChannelName: t.Store.Name,
		OpenId:      t.GetOrigin() + ":" + t.OrderData.ReceiverMobile,
		Nickname:    t.OrderData.ReceiverName,
		Origin:      t.GetOrigin(),
	}

	_, err := share.BindSocial(t.Context, request)
	if err != nil {
		return err
	}
	return nil
}

func (t *TmallOrder) GetReceiver() (*Receiver, error) {
	if t.Trade != nil {
		tradeReceiver := t.Trade.Receiver
		if tradeReceiver.Mobile != "" || tradeReceiver.Province != "" || tradeReceiver.MemberId.Hex() != "" {
			return &t.Trade.Receiver, nil
		}
	}

	receiver := t.GetReceiverInfo()
	if t.Store.IsMergeReceiver || t.Store.IsBelongToReceiver {
		if t.Member != nil && t.Member.Id != "" {
			receiver.MemberId = bson.ObjectIdHex(t.Member.Id)
		}
		return receiver, nil
	}

	receiverMember, err := t.UpsertReceiverMember()
	if err != nil {
		return nil, err
	}
	if receiverMember != nil && receiverMember.Id != "" {
		receiver.MemberId = bson.ObjectIdHex(receiverMember.Id)
	}
	return receiver, nil
}

func (t *TmallOrder) GetReceiverInfo() *Receiver {
	city := t.OrderData.ReceiverCity
	district := t.OrderData.ReceiverDistrict
	if city == "" {
		city = t.OrderData.ReceiverDistrict
		district = t.OrderData.ReceiverTown
	}

	receiver := &Receiver{
		Mobile:    t.OrderData.ReceiverMobile,
		Country:   t.OrderData.ReceiverCountry,
		Name:      t.OrderData.ReceiverName,
		Province:  t.OrderData.ReceiverState,
		City:      city,
		District:  district,
		Address:   t.OrderData.ReceiverAddress,
		Telephone: t.OrderData.ReceiverPhone,
	}

	return receiver
}

func (t *TmallOrder) UpsertReceiverMember() (*pb_member.MemberDetailResponse, error) {
	if t.Store.IsNotCreateMember {
		return nil, nil
	}

	if !t.isReceiverMobileExists() {
		return nil, nil
	}
	if t.isDesensitized("Receiver name", t.OrderData.ReceiverName) {
		return nil, nil
	}

	params, err := t.GetUpsertReceiverMemberParams()
	if err != nil {
		return nil, err
	}
	return t.UpsertMemberDetail(params)
}

func (t *TmallOrder) isReceiverMobileExists() bool {
	mobile := t.OrderData.ReceiverMobile
	if mobile == "" {
		log.Warn(t.Context, "Receiver mobile not found when upsert tmall member", log.Fields{
			"accountId":     t.AccountId.Hex(),
			"storeId":       t.StoreId.Hex(),
			"syncHistoryId": t.SyncHistoryId.Hex(),
			"orderId":       t.GetOrderId(),
			"mobile":        mobile,
		})
		return false
	}
	if !validators.CValidator.IsPhone(mobile, nil) {
		log.Warn(t.Context, "Receiver member mobile is invalid  when upsert tmall member", log.Fields{
			"accountId":     t.AccountId.Hex(),
			"storeId":       t.StoreId.Hex(),
			"syncHistoryId": t.SyncHistoryId.Hex(),
			"orderId":       t.GetOrderId(),
			"mobile":        mobile,
		})
		return false
	}
	return true
}

func (t *TmallOrder) GetUpsertReceiverMemberParams() (*pb_member.UpsertMemberRequest, error) {
	receiver := t.GetReceiverInfo()
	propertiesMap := t.getReceiverPropertiesMap()
	extraBoolCond := map[string]*types.BoolValue{}
	extraBoolCond["isDefault"] = &types.BoolValue{Value: true}
	propertyInfos, getPropertyInfosErr := share.GetPropertyInfos(t.Context, propertiesMap, "name", extraBoolCond)
	if getPropertyInfosErr != nil {
		return nil, getPropertyInfosErr
	}

	request := &pb_member.UpsertMemberRequest{
		OriginFrom: &pb_origin.OriginInfo{
			Channel:     t.Store.Sid,
			ChannelName: t.Store.Name,
			OpenId:      t.GetOrigin() + ":" + t.OrderData.ReceiverMobile,
			Origin:      t.GetOrigin(),
			Nickname:    receiver.Name,
		},
		Properties: propertyInfos,
	}
	return request, nil
}

func (t *TmallOrder) getReceiverPropertiesMap() map[string]interface{} {
	receiver := t.GetReceiverInfo()
	propertiesMap := map[string]interface{}{}
	var addressPropertyValue pb_member.PropertyInfo_ValueArray
	if receiver.Province != "" {
		addressPropertyValue = pb_member.PropertyInfo_ValueArray{
			ValueArray: &pb_member.PropertyArrayValue{
				Value: []string{
					receiver.Country,
					receiver.Province,
					receiver.City,
					receiver.District,
					receiver.Address,
				},
			},
		}
		propertiesMap["address"] = &addressPropertyValue
	}

	if receiver.Mobile != "" {
		if validators.CValidator.IsPhone(receiver.Mobile, nil) {
			propertiesMap["phone"] = &pb_member.PropertyInfo_ValueString{
				ValueString: &pb_member.PropertyStringValue{
					Value: receiver.Mobile,
				},
			}
		}
	}

	return propertiesMap
}

func (t *TmallOrder) GetDelivery() (*Delivery, error) {
	delivery := &Delivery{}
	if len(t.OrderData.Orders.Order) > 0 {
		delivery.Type = "express"
		delivery.ExpressNo = t.OrderData.Orders.Order[0].InvoiceNo
		delivery.ExpressCompany = t.OrderData.Orders.Order[0].LogisticsCompany
	}

	return delivery, nil
}

func (t *TmallOrder) GetCloseReason() string {
	return ""
}

func (t *TmallOrder) GetDealCode() string {
	return ""
}

func (t *TmallOrder) GetOutTradeNo() string {
	return ""
}

func (t *TmallOrder) GetCoupons() (*[]Coupon, error) {
	return nil, nil
}

func (t *TmallOrder) GetUtm() (*Utm, error) {
	return nil, nil
}

func (t *TmallOrder) GetRefundStatus() string {
	hasRefundStatus := []string{
		share.ORDER_AFTER_SALE_STATUS_REFUNDING,
		share.ORDER_AFTER_SALE_STATUS_REFUNDED,
	}
	isRefunding := false
	hasRefund := false
	for _, goodDetail := range *t.GetGoodsDetail() {
		if util.StrInArray(goodDetail.AfterSaleStatus, &hasRefundStatus) {
			hasRefund = true
			if goodDetail.AfterSaleStatus == share.ORDER_AFTER_SALE_STATUS_REFUNDING {
				isRefunding = true
			}
		}
	}

	if !hasRefund {
		return ""
	}
	if isRefunding {
		return share.REFUND_STATUS_REFUNDING
	}
	return share.REFUND_STATUS_REFUNDED
}

func (t *TmallOrder) GetBuyerMessage() string {
	return t.OrderData.BuyerMessage
}

func (t *TmallOrder) GetSellerMemo() string {
	return t.OrderData.SellerMemo
}

func (t *TmallOrder) GetOrderConsignTime() string {
	return t.OrderData.ConsignTime
}

func (t *TmallOrder) GetExtraFields() (interface{}, error) {
	return nil, nil
}

func (t *TmallOrder) GetCondition() bson.M {
	condition := bson.M{
		"accountId": t.AccountId,
		"orderId":   t.GetOrderId(),
		"isDeleted": false,
	}

	return condition
}

func (t *TmallOrder) IsFiltered() bool {
	return false
}

func (t *TmallOrder) UpsertExtraTrade(trade *Trade) (*Trade, error) {
	// trade 表已存在的唯一索引 "accountId_1_erpOrderId_1"，所以需要保证 erpOrderId 字段唯一才能存到数据库中
	trade.ErpOrderId = t.GetOrderId()
	if refundGood := t.GetLatestRefundedGood(); refundGood != nil {
		endTime, _ := time.ParseInLocation(share.LAYOUT_BY_MINUTE, refundGood.EndTime, time.Local)
		if endTime.After(trade.RefundTime) {
			trade.RefundTime = endTime
		}
	}
	return trade, nil
}

func (t *TmallOrder) GetTimeLayout(timeType string) string {
	switch timeType {
	case "orderCreateTime", "orderUpdateTime", "orderCompleteTime", "orderPayTime", "orderConsignTime":
		return share.LAYOUT_BY_MINUTE
	}

	return time.RFC3339
}

func (t *TmallOrder) GetOrderSourceJsonStr() (string, error) {
	return t.OrderInfo.Data, nil
}

func (t *TmallOrder) AfterUpsert(trade *Trade) error {
	// 天猫订单没有退款单，记录退款后的订单金额到 extraField 里
	if t.GetRefundStatus() != "" {
		goodsPayAmount := cast.ToFloat64(t.OrderData.ReceivedPayment) - cast.ToFloat64(t.OrderData.PostFee)
		if goodsPayAmount < 0 {
			goodsPayAmount = 0
		}
		trade.ExtraFields = map[string]any{
			"goodsPayAmount": goodsPayAmount * 100.0,
		}
		trade.Update(t.Context)
	}

	// 触发积分同步以调用淘宝会员通接口获取下单人手机号填充 member 属性
	if t.GetOrderStatus() == share.ORDER_STATUS_TRADE_BUYER_SIGNED && t.Member != nil {
		if IsTaobaoMember(t.Member) && t.Member.Phone == "" {
			request := &pb_member.RecordMemberScoreSyncRequest{
				MemberId:    t.Member.Id,
				ChannelId:   t.Store.Sid,
				Type:        "add",
				BusinessId:  bson.NewObjectId().Hex(),
				Score:       0,
				From:        "portal",
				To:          "taobao:member",
				Description: "淘宝订单完成，填充手机号",
			}
			_, err := share.RecordMemberScoreSync(t.Context, request)
			if err != nil {
				return err
			}
		}
	}

	if trade.DisableEvent {
		return nil
	}

	// t.Trade 是在 upsert 更新之前查找的，所以这里是算 old，传参的 trade 是更新之后
	if trade.IsNeedSendMemberEvent(t.Trade, "OrderCompleteTime") {
		t.SendOrderCompleteEvent(trade)
	}

	if trade.MemberId.Valid() {
		// 获取未发退款事件的退款商品列表以及判断是否全部退款
		refundGoodsMap, refundIds, isAllRefund := t.preprocessingSendRefundEvent(t.Trade)
		if len(refundGoodsMap) > 0 {
			// 该 trade 在本次请求发过购买事件，需等待防止退款事件早于购买事件
			if trade.sentPurchaseEvent {
				time.Sleep(time.Second * 10)
			}
			t.SendRefundOrderEvent(trade, refundGoodsMap, refundIds, isAllRefund)
			t.SendRefundProductEvent(trade, refundGoodsMap, refundIds, isAllRefund)
			// 更新商品发退款事件的状态
			t.afterSendRefundEvent(trade, refundGoodsMap)
		}
	}
	return nil
}

func (t *TmallOrder) SendOrderCompleteEvent(trade *Trade) {
	if trade.OrderCompleteTime.IsZero() {
		return
	}
	_, goodsList := trade.GetGoodMsgAndList()

	// 淘宝订单发订单完成事件时，事件属性 goodsAmount 是商品的实付总金额，不包括邮费，也不包括子订单中退款的金额
	// 由于 trade.ActualAmount 记录的是下单那一刻客户支付金额，该处逻辑不好变动，所以单独给淘宝订单处理发订单完成事件的逻辑
	// ReceivedPayment 是卖家实际收到的支付宝打款金额，这个金额会随着子订单的确认收货而不断增加，交易成功后等于买家实付款减去退款金额
	goodsPayAmount := cast.ToFloat64(t.OrderData.ReceivedPayment) - cast.ToFloat64(t.OrderData.PostFee)
	if goodsPayAmount < 0 {
		goodsPayAmount = 0
	}
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_ORDER_COMPLETED + ":" + trade.OrderId,
		AccountId:  trade.AccountId.Hex(),
		MemberId:   trade.MemberId.Hex(),
		ChannelId:  trade.GetChannelId(t.Context),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_ORDER_COMPLETED,
		CreateTime: trade.OrderCompleteTime.UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"memberId":        trade.MemberId.Hex(),
			"id":              trade.Id.Hex(),
			"number":          trade.OrderId,
			"orderId":         trade.OrderId,
			"platform":        trade.OrderPlatform,
			"storeId":         trade.StoreId.Hex(),
			"storeName":       trade.StoreName,
			"goodsAmount":     goodsPayAmount * 100.0,
			"logisticsFee":    trade.PostalAmount * 100.0,
			"completedAt":     trade.OrderCompleteTime.UnixNano() / 1e6,
			"orderCreateTime": trade.OrderCreateTime.UnixNano() / 1e6,
			"goodsList":       goodsList,
		},
	}
	if trade.CurrentLevel != 0 {
		eventBody.EventProperties["memberLevel"] = trade.CurrentLevel
	}

	eventBody.SendCustomerEvent(t.Context)
}

func (t *TmallOrder) preprocessingSendRefundEvent(old *Trade) (refundGoodsMap map[string][]Order, refundIds []string, isAllRefund bool) {
	refundGoods := t.getRefundGoods()
	if len(refundGoods) == 0 {
		return refundGoodsMap, refundIds, isAllRefund
	}

	sort.SliceStable(refundGoods, func(i, j int) bool {
		firstEndTime, _ := time.ParseInLocation(share.LAYOUT_BY_MINUTE, refundGoods[i].EndTime, time.Local)
		secondEndTime, _ := time.ParseInLocation(share.LAYOUT_BY_MINUTE, refundGoods[j].EndTime, time.Local)
		return firstEndTime.Before(secondEndTime)
	})

	if len(refundGoods) == len(t.OrderData.Orders.Order) {
		isAllRefund = true
	}

	refundGoodsMap = make(map[string][]Order, len(refundGoods))
	if old == nil {
		for _, refundGood := range refundGoods {
			refundGoodsMap[refundGood.RefundID] = append(refundGoodsMap[refundGood.RefundID], refundGood)
			if !util.StrInArray(refundGood.RefundID, &refundIds) {
				refundIds = append(refundIds, refundGood.RefundID)
			}
		}
		return refundGoodsMap, refundIds, isAllRefund
	}
	for _, refundGood := range refundGoods {
		for _, goodDetail := range old.GoodsDetail {
			if cast.ToString(refundGood.OID) == goodDetail.Oid && !goodDetail.HasSendEvent {
				refundGoodsMap[refundGood.RefundID] = append(refundGoodsMap[refundGood.RefundID], refundGood)
				if !util.StrInArray(refundGood.RefundID, &refundIds) {
					refundIds = append(refundIds, refundGood.RefundID)
				}
			}
		}
	}
	return refundGoodsMap, refundIds, isAllRefund
}

func (t *TmallOrder) getRefundGoods() []Order {
	refundOrders := []Order{}
	for _, orderInfo := range t.OrderData.Orders.Order {
		if orderInfo.RefundID != "" && orderInfo.RefundStatus == "SUCCESS" && orderInfo.Status == "TRADE_CLOSED" {
			refundOrders = append(refundOrders, orderInfo)
		}
	}
	return refundOrders
}

func (t *TmallOrder) GetLatestRefundedGood() *Order {
	refundOrders := t.getRefundGoods()
	if len(refundOrders) == 0 {
		return nil
	}
	sort.SliceStable(refundOrders, func(i, j int) bool {
		firstEndTime, _ := time.ParseInLocation(share.LAYOUT_BY_MINUTE, refundOrders[i].EndTime, time.Local)
		secondEndTime, _ := time.ParseInLocation(share.LAYOUT_BY_MINUTE, refundOrders[j].EndTime, time.Local)
		return firstEndTime.After(secondEndTime)
	})
	return &refundOrders[0]
}

func (t *TmallOrder) SendRefundOrderEvent(trade *Trade, refundGoodsMap map[string][]Order, refundIds []string, isAllRefund bool) {
	if trade.OrderPayTime.IsZero() {
		return
	}

	channelId := trade.GetChannelId(t.Context)
	for idx, refundId := range refundIds {
		refundGoods := refundGoodsMap[refundId]
		// 子订单交易结束时间作为退款时间
		refundTime, _ := time.ParseInLocation(share.LAYOUT_BY_MINUTE, refundGoods[0].EndTime, time.Local)
		// 取商品的实付金额作为退款金额
		goodsName, goodsCount, refundAmount, refundGoodsAmount, goodsList := getRefundProductNameAndCountAndPayAmount(t.Context, refundGoods, t.GetActualAmount()-t.GetPostalAmount(), t.getRefundGoods(), isAllRefund)
		eventBody := component.CustomerEventBody{
			Id:         component.MAIEVENT_REFUND_ORDER + ":" + refundId,
			AccountId:  t.AccountId.Hex(),
			MemberId:   trade.MemberId.Hex(),
			MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
			SubType:    component.MAIEVENT_REFUND_ORDER,
			CreateTime: refundTime.UnixNano() / 1e6,
			ChannelId:  channelId,
			EventProperties: map[string]interface{}{
				"tradeId":           trade.Id.Hex(),
				"refundId":          refundId,
				"id":                trade.Id.Hex(),
				"number":            trade.OrderId,
				"orderId":           trade.OrderId,
				"platform":          trade.OrderPlatform,
				"storeId":           trade.StoreId.Hex(),
				"storeName":         trade.StoreName,
				"productName":       goodsName,
				"productCounts":     goodsCount,
				"refundAmount":      share.Yuan2Fen(refundAmount),
				"refundGoodsAmount": share.Yuan2Fen(refundGoodsAmount),
				"goodsList":         goodsList,
			},
		}
		if trade.CurrentLevel != 0 {
			eventBody.EventProperties["memberLevel"] = trade.CurrentLevel
		}
		if isAllRefund && idx == len(refundIds)-1 {
			eventBody.EventProperties["isAllRefund"] = isAllRefund
		}
		eventBody.SendCustomerEvent(t.Context)
	}
}

func getRefundProductNameAndCountAndPayAmount(ctx context.Context, orderGoods []Order, withoutPostFeeMaxPayAmount float64, allRefundGoods []Order, isAllRefund bool) (string, uint64, float64, float64, []map[string]any) {
	var (
		goodName                               string
		goodCount                              uint64
		refundGoodPayAmount, refundGoodsAmount float64
		goodsList                              []map[string]any

		nowAllRefundGoodsAmount float64
	)

	for _, good := range allRefundGoods {
		nowAllRefundGoodsAmount += cast.ToFloat64(good.TotalFee)
		if good.PartMjzDiscount != "" {
			partMjzDiscount := cast.ToFloat64(good.PartMjzDiscount)
			nowAllRefundGoodsAmount -= partMjzDiscount
		}
	}

	for index, good := range orderGoods {
		if index == 0 {
			goodName = good.Title
		} else {
			goodName = goodName + "," + good.Title
		}
		goodCount += uint64(good.Num)
		actualAmount := cast.ToFloat64(good.TotalFee)
		if good.PartMjzDiscount != "" {
			partMjzDiscount := cast.ToFloat64(good.PartMjzDiscount)
			actualAmount -= partMjzDiscount
		}
		refundGoodPayAmount += actualAmount
		goodsList = append(goodsList, map[string]any{
			"productName": goodName,
			"productSku":  good.SkuID,
			"price":       share.Yuan2Fen(cast.ToFloat64(good.Price)),
			"count":       uint64(good.Num),
			"payAmount":   share.Yuan2Fen(actualAmount),
		})
	}
	refundGoodsAmount = refundGoodPayAmount
	if nowAllRefundGoodsAmount > withoutPostFeeMaxPayAmount {
		if isAllRefund {
			refundGoodsAmount = refundGoodPayAmount - (nowAllRefundGoodsAmount - withoutPostFeeMaxPayAmount)
		} else {
			log.Error(ctx, "refund amount more than payAmount", log.Fields{})
		}
	}

	return goodName, goodCount, refundGoodPayAmount, refundGoodsAmount, goodsList
}

func (t *TmallOrder) SendRefundProductEvent(trade *Trade, refundGoodsMap map[string][]Order, refundIds []string, isAllRefund bool) {
	if trade.OrderPayTime.IsZero() {
		return
	}

	channelId := trade.GetChannelId(t.Context)
	for refundIdIdx, refundId := range refundIds {
		refundGoods := refundGoodsMap[refundId]
		for idx, refundGood := range refundGoods {
			refundTime, _ := time.ParseInLocation(share.LAYOUT_BY_MINUTE, refundGood.EndTime, time.Local)

			refundAmount := cast.ToFloat64(refundGood.TotalFee)
			if refundGood.PartMjzDiscount != "" {
				partMjzDiscount := cast.ToFloat64(refundGood.PartMjzDiscount)
				refundAmount -= partMjzDiscount
			}
			eventBody := component.CustomerEventBody{
				Id:         component.MAIEVENT_REFUND_PRODUCT + ":" + refundId + ":" + refundGood.OuterIid + ":" + cast.ToString(refundGood.OID),
				AccountId:  t.AccountId.Hex(),
				MemberId:   trade.MemberId.Hex(),
				ChannelId:  channelId,
				MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
				SubType:    component.MAIEVENT_REFUND_PRODUCT,
				CreateTime: refundTime.UnixNano() / 1e6,
				EventProperties: map[string]interface{}{
					"platform":           trade.OrderPlatform,
					"tradeId":            trade.Id.Hex(),
					"storeId":            trade.StoreId.Hex(),
					"storeName":          trade.StoreName,
					"orderId":            trade.OrderId,
					"productName":        refundGood.Title,
					"productNumber":      refundGood.OuterIid,
					"productCounts":      refundGood.Num,
					"productSku":         refundGood.SkuID,
					"productExternalSku": refundGood.OuterSkuID,
					"refundId":           refundGood.RefundID,
					"refundAmount":       share.Yuan2Fen(refundAmount),
					"id":                 trade.Id.Hex(),
					"number":             trade.OrderId,
					"outTradeId":         fmt.Sprintf("%s_%s_%s", trade.OrderId, refundGood.OuterIid, refundGood.SkuID), // 商品唯一标识
				},
			}
			if trade.CurrentLevel != 0 {
				eventBody.EventProperties["memberLevel"] = trade.CurrentLevel
			}
			if isAllRefund && refundIdIdx == len(refundIds)-1 && idx == len(refundGoods)-1 {
				eventBody.EventProperties["isAllRefund"] = isAllRefund
			}
			eventBody.SendCustomerEvent(t.Context)
		}
	}
}

func (t *TmallOrder) afterSendRefundEvent(trade *Trade, refundGoodsMap map[string][]Order) {
	refundGoodList := []Order{}
	for _, refundGoods := range refundGoodsMap {
		refundGoodList = append(refundGoodList, refundGoods...)
	}
	for _, refundGood := range refundGoodList {
		for idx, goodDetail := range trade.GoodsDetail {
			if cast.ToString(refundGood.OID) == goodDetail.Oid {
				trade.GoodsDetail[idx].HasSendEvent = true
			}
		}
	}
	trade.Update(t.Context)
}

func (t *TmallOrder) IsVirtual() bool {
	if t.OrderData.ShippingType == "virtual" {
		return true
	}
	return false
}

func (t *TmallOrder) GetChannelId() string {
	if t.Store != nil {
		return t.Store.Sid
	}
	return ""
}

func (t *TmallOrder) GetStaffId() bson.ObjectId {
	return bson.NilObjectId
}

func (t *TmallOrder) GetStaffCode() string {
	return ""
}
