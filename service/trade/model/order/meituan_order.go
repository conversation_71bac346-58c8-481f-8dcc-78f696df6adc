package order

import (
	"context"
	"encoding/json"
	"mairpc/core/component/meituan"
	"mairpc/core/extension/bson"
	pb_member "mairpc/proto/member"
	"mairpc/service/share/component"
	"mairpc/service/share/util"
	"mairpc/service/trade/model/store"
	"mairpc/service/trade/share"
	"strings"
	"time"

	"github.com/spf13/cast"
)

var (
	MEITUAN_PACKAGE_BAG_BOX_SKU    = "meituan_package_bag_box_sku"
	MEITUAN_PACKAGE_BAG_BOX_NUMBER = "meituan_package_bag_box_number"
	MEITUAN_PACKAGE_BAG_BOX        = "打包袋/包装盒"
)

type MeituanOrder struct {
	Context          context.Context
	OrderIdStr       string
	OrderId          int64
	AccountId        bson.ObjectId
	StoreId          bson.ObjectId
	Store            *store.TradeStore
	SyncHistoryId    bson.ObjectId
	Client           *meituan.Meituan
	OrderData        *meituan.GetOrderDetailResponse
	OrderDataUser    *meituan.UserMemberInfo
	OrderDataGoods   meituan.OrderProducts
	SkuBenefitDetail []meituan.SkuBenefitDetail
	Member           *pb_member.MemberDetailResponse
	Trade            *Trade // 数据库查找的已存在的订单
	TempProvider     TempProviderData
}

func (m *MeituanOrder) Init(ctx context.Context, orderIdStr string, tempProvider TempProviderData) error {
	m.Context = ctx
	m.OrderIdStr = orderIdStr
	actualOrderId := orderIdStr
	if strings.HasPrefix(orderIdStr, "his_") {
		actualOrderId = strings.TrimPrefix(orderIdStr, "his_")
	}
	m.OrderId = cast.ToInt64(actualOrderId)
	m.AccountId = util.GetAccountIdAsObjectId(ctx)
	m.TempProvider = tempProvider
	m.StoreId = tempProvider.Store.Id
	m.SyncHistoryId = tempProvider.SyncHistories[1].Id
	m.Store = &tempProvider.Store
	m.Client = tempProvider.Client.(*meituan.Meituan)
	orderDetail, err := m.Client.Trade.GetOrderDetail(ctx, &meituan.GetOrderDetailRequest{
		OrderId:       m.OrderId,
		IsMtLogistics: 1,
	})
	if err != nil {
		return err
	}
	m.OrderData = orderDetail
	if orderDetail.Data.UserMemberInfo != "" {
		userInfo := meituan.UserMemberInfo{}
		err := json.Unmarshal([]byte(m.OrderData.Data.UserMemberInfo), &userInfo)
		if err != nil {
			return err
		}
		if userInfo.CardCode != "" {
			m.OrderDataUser = &userInfo
		}
	}
	if orderDetail.Data.Detail != "" {
		orderProducts := []meituan.OrderProductDetail{}
		decoder := json.NewDecoder(strings.NewReader(m.OrderData.Data.Detail))
		decoder.UseNumber()
		err := decoder.Decode(&orderProducts)
		if err != nil {
			return err
		}
		m.OrderDataGoods = orderProducts
	}
	if m.OrderData.Data.SkuBenefitDetail != "" {
		skuBenefitDetail := []meituan.SkuBenefitDetail{}
		decoder := json.NewDecoder(strings.NewReader(m.OrderData.Data.SkuBenefitDetail))
		decoder.UseNumber()
		err := decoder.Decode(&skuBenefitDetail)
		if err != nil {
			return err
		}
		m.SkuBenefitDetail = skuBenefitDetail
	}
	m.Trade = CTrade.GetByCondition(m.Context, bson.M{"orderId": actualOrderId})
	return nil
}

func (m *MeituanOrder) GetOrderId() string {
	return cast.ToString(m.OrderData.Data.OrderId)
}

func (m *MeituanOrder) GetOrderFrom() string {
	return share.ORDER_PLATFORM_MEITUAN
}

func (m *MeituanOrder) GetOrderPlatform() string {
	return share.ORDER_PLATFORM_MEITUAN
}

func (m *MeituanOrder) GetOrderCreateTime() string {
	return time.Unix(m.OrderData.Data.Ctime, 0).Format(m.GetTimeLayout("orderCreateTime"))
}

func (m *MeituanOrder) GetOrderUpdateTime() string {
	updatedTime := m.OrderData.Data.Utime
	if updatedTime < m.OrderData.Data.OrderSendTime {
		updatedTime = m.OrderData.Data.OrderSendTime
	}
	if m.OrderData.Data.LogisticsFetchTime > 0 && m.OrderData.Data.LogisticsCancelTime == 0 && updatedTime < m.OrderData.Data.LogisticsFetchTime {
		updatedTime = m.OrderData.Data.LogisticsFetchTime
	}
	if updatedTime < m.OrderData.Data.OrderCompletedTime {
		updatedTime = m.OrderData.Data.OrderCompletedTime
	}
	return time.Unix(updatedTime, 0).Format(m.GetTimeLayout("orderUpdateTime"))
}

func (m *MeituanOrder) GetOrderCompleteTime() string {
	if m.OrderData.Data.OrderCompletedTime > 0 {
		return time.Unix(m.OrderData.Data.OrderCompletedTime, 0).Format(m.GetTimeLayout("orderCompleteTime"))
	}
	return ""
}

func (m *MeituanOrder) GetOrderPayTime() string {
	if m.OrderData.Data.OrderSendTime > 0 {
		return time.Unix(m.OrderData.Data.OrderSendTime, 0).Format(m.GetTimeLayout("orderPayTime"))
	}
	return ""
}

func (m *MeituanOrder) GetOrderStatus() string {
	if m.OrderData.Data.Status != 8 && m.OrderData.Data.Status != 9 {
		if m.OrderData.Data.LogisticsFetchTime > 0 && m.OrderData.Data.LogisticsCancelTime == 0 {
			return share.ORDER_STATUS_WAIT_BUYER_CONFIRM_GOODS
		}
	}
	switch m.OrderData.Data.Status {
	case 1:
		if m.OrderData.Data.OrderSendTime > 0 { // 美团全程送订单需要有骑手接单才会给商家推送订单
			return share.ORDER_STATUS_WAIT_SELLER_SEND_GOODS
		}
		return share.ORDER_STATUS_WAIT_BUYER_PAY
	case 2, 4:
		return share.ORDER_STATUS_WAIT_SELLER_SEND_GOODS
	case 8:
		return share.ORDER_STATUS_TRADE_BUYER_SIGNED
	case 9:
		return share.ORDER_STATUS_TRADE_CLOSED
	}

	return ""
}

func (m *MeituanOrder) GetGoodsAmount() float64 {
	return m.OrderData.Data.OriginalPrice - m.OrderData.Data.ShippingFee
}

func (m *MeituanOrder) GetPostalAmount() float64 {
	return m.OrderData.Data.ShippingFee
}

func (m *MeituanOrder) GetActualAmount() float64 {
	return m.OrderData.Data.Total
}

func (m *MeituanOrder) GetDiscountAmount() float64 {
	discountAmount := m.OrderData.Data.OriginalPrice - m.OrderData.Data.Total
	if discountAmount < 0 {
		return 0
	}
	return discountAmount
}

func (m *MeituanOrder) GetPaymentType() string {
	switch m.OrderData.Data.PayType {
	case 1:
		return share.ORDER_PAYMENT_TYPE_CODPAY
	case 2:
		return share.ORDER_PAYMENT_TYPE_ONLINEPAY
	}
	return ""
}

func (m *MeituanOrder) GetBuyerId() string {
	if m.OrderDataUser != nil {
		return m.OrderDataUser.CardCode
	}

	return ""
}

func (m *MeituanOrder) GetBuyerNickname() string {
	return ""
}

func (m *MeituanOrder) GetGoodsDetail() *[]GoodsDetail {
	// 更新 GoodsDetail 时保证 afterSaleStatus 字段不变
	oidAfterSaleStatusMap := map[string]string{}
	if m.Trade != nil {
		for _, goodDetail := range m.Trade.GoodsDetail {
			oidAfterSaleStatusMap[goodDetail.Oid] = goodDetail.AfterSaleStatus
		}
	}

	skuBenefitDetailMap := make(map[string]*meituan.SkuBenefitDetail)
	if len(m.SkuBenefitDetail) > 0 {
		for idx, skuBenefitDetail := range m.SkuBenefitDetail {
			key := skuBenefitDetail.AppSpuCode + "_" + skuBenefitDetail.SkuId
			skuBenefitDetailMap[key] = &m.SkuBenefitDetail[idx]
		}
	}

	allGoodsDetail := []GoodsDetail{}
	allBoxAmount := 0.0
	for _, good := range m.OrderDataGoods {
		discount := 0.0
		discountPrice := good.OriginalPrice
		subtotalAmount := util.MultiplyFloat(good.OriginalPrice, cast.ToFloat64(good.Quantity))

		key := good.AppSpuCode + "_" + good.SkuId
		if skuBenefitDetail, ok := skuBenefitDetailMap[key]; ok {
			discount = skuBenefitDetail.TotalReducePrice
			discountPrice = skuBenefitDetail.ActivityPrice
			subtotalAmount = skuBenefitDetail.TotalActivityPrice
		}
		detail := GoodsDetail{
			GoodsNumber:    good.AppSpuCode,
			Name:           good.FoodName,
			Price:          good.OriginalPrice,
			DiscountPrice:  discountPrice,
			Discount:       discount,
			Oid:            cast.ToString(good.ItemId),
			Count:          cast.ToUint64(good.Quantity),
			Sku:            cast.ToString(good.MtSkuId),
			OutSkuId:       good.SkuId,
			SubtotalAmount: subtotalAmount,
			PropertiesName: strings.Replace(good.FoodProperty, ",", " ", -1),
			ThumbnailUrl:   good.Picture,
			Barcode:        good.Upc,
			Weight:         cast.ToFloat64(good.Weight),
		}
		if afterSaleStatus, ok := oidAfterSaleStatusMap[detail.Oid]; ok {
			detail.AfterSaleStatus = afterSaleStatus
		}
		allGoodsDetail = append(allGoodsDetail, detail)
		allBoxAmount += util.MultiplyFloat(good.BoxPrice, good.BoxNum)
	}
	packageBagBox := cast.ToFloat64(m.OrderData.Data.PackageBagMoney)/100 + allBoxAmount
	if packageBagBox > 0 {
		allGoodsDetail = append(allGoodsDetail, GoodsDetail{
			GoodsNumber:    MEITUAN_PACKAGE_BAG_BOX_NUMBER,
			Name:           MEITUAN_PACKAGE_BAG_BOX,
			Price:          packageBagBox,
			Discount:       0.0,
			Count:          1,
			Sku:            MEITUAN_PACKAGE_BAG_BOX_SKU,
			SubtotalAmount: packageBagBox,
		})
	}

	return &allGoodsDetail
}

func (m *MeituanOrder) UpsertMember() (*pb_member.MemberDetailResponse, error) {
	if m.Store.IsNotCreateMember {
		return nil, nil
	}
	if m.OrderDataUser == nil {
		return nil, nil
	}
	member, err := share.GetMemberByCardNumber(m.Context, m.OrderDataUser.CardCode, []string{}, "")
	if err != nil && !strings.Contains(err.Error(), "2000007") {
		return nil, err
	}
	if member != nil && member.Id != "" {
		m.Member = member
		return member, nil
	}

	return nil, nil
}

func (m *MeituanOrder) UpsertReceiverMember() (*pb_member.MemberDetailResponse, error) {
	return nil, nil
}

func (m *MeituanOrder) GetReceiver() (*Receiver, error) {
	receiver := &Receiver{
		Country: "中国",
		Name:    m.OrderData.Data.RecipientName,
		Address: getMeituanActualAddress(m.OrderData.Data.RecipientAddress, m.OrderData.Data.OrderShippingAddress),
	}
	return receiver, nil
}

func getMeituanActualAddress(addressStr string, briefAddress string) string {
	aArr := strings.Split(addressStr, "@#")
	if len(aArr) == 1 {
		aArr = append(aArr, briefAddress)
	} else if aArr[1] == "" {
		aArr[1] = briefAddress
	}
	if aArr[0] == "" || aArr[1] == "" {
		return aArr[1] + aArr[0]
	}
	detail := []rune(aArr[0])
	address := []rune(aArr[1])
	idx := len(address)
	for j := range address {
		i := 0
		if string(detail[i]) != string(address[j]) {
			continue
		}
		idx = j
		temp := j
		for i < len(detail) && temp < len(address) {
			if string(detail[i]) != string(address[temp]) {
				break
			}
			i++
			temp++
		}
		if temp == len(address) && idx != len(address)-1 {
			break
		} else {
			idx = len(address)
		}
	}
	actualAddress := []rune{}
	actualAddress = append(actualAddress, address[:idx]...)
	actualAddress = append(actualAddress, detail...)
	return string(actualAddress)
}

func (m *MeituanOrder) GetDelivery() (*Delivery, error) {
	typeStr := ""
	switch m.OrderData.Data.PickType {
	case 0:
		if m.OrderData.Data.IsThirdShipping == 1 {
			typeStr = "第三方平台配送"
		}
		if m.OrderData.Data.IsThirdShipping == 0 {
			switch m.OrderData.Data.LogisticsCode {
			case "0000":
				typeStr = "商家自配"
			case "0002":
				typeStr = "趣活"
			case "0016":
				typeStr = "达达"
			case "0033":
				typeStr = "E代送"
			case "1001":
				typeStr = "美团专送-加盟"
			case "1002":
				typeStr = "美团专送-自建"
			case "1003":
				typeStr = "美团跑腿（原众包）"
			case "1004":
				typeStr = "美团专送-城市代理"
			case "2001":
				typeStr = "角马"
			case "2002":
				typeStr = "快送"
			case "3001":
				typeStr = "混合送（即美团专送+快送）"
			case "2010":
				typeStr = "全城送"
			case "30011001":
				typeStr = "混合加盟"
			case "30011002":
				typeStr = "混合自建"
			case "30012002":
				typeStr = "混合快送"
			case "10039001":
				typeStr = "邻趣"
			case "00009002":
				typeStr = "商家自配-海葵"
			case "00009003":
				typeStr = "商家自配-开放平台"
			case "5001":
				typeStr = "聚合配送"
			case "999":
				typeStr = "三方配送服务"
			case "4001":
				typeStr = "光速达"
			case "40011001":
				typeStr = "光速达加盟"
			case "40012002":
				typeStr = "光速达众包"
			case "4011":
				typeStr = "快速达"
			case "40111001":
				typeStr = "快速达加盟"
			case "40112002":
				typeStr = "快速达众包"
			case "4012":
				typeStr = "及时达"
			case "40121001":
				typeStr = "及时达加盟"
			case "4015":
				typeStr = "企客全城送"
			case "4033":
				typeStr = "专人直送"
			}
		}
	case 1:
		typeStr = ORDER_DELIVERY_METHOD_SELF_FETCH
	}
	return &Delivery{
		Type: typeStr,
	}, nil
}

func (m *MeituanOrder) GetCloseReason() string {
	if m.Trade != nil {
		return m.Trade.CloseReason
	}
	return ""
}

func (m *MeituanOrder) GetDealCode() string {
	return ""
}

func (m *MeituanOrder) GetOutTradeNo() string {
	return ""
}

func (m *MeituanOrder) GetCoupons() (*[]Coupon, error) {
	return nil, nil
}

func (m *MeituanOrder) GetUtm() (*Utm, error) {
	return nil, nil
}

func (m *MeituanOrder) GetRefundStatus() string {
	if m.Trade != nil {
		return m.Trade.RefundStatus
	}
	return ""
}

func (m *MeituanOrder) GetBuyerMessage() string {
	return m.OrderData.Data.Caution
}

func (m *MeituanOrder) GetSellerMemo() string {
	return ""
}

func (m *MeituanOrder) GetOrderConsignTime() string {
	if m.OrderData.Data.LogisticsFetchTime > 0 && m.OrderData.Data.LogisticsCancelTime == 0 {
		return time.Unix(m.OrderData.Data.LogisticsFetchTime, 0).Format(m.GetTimeLayout("orderConsignTime"))
	}
	if m.OrderData.Data.OrderCompletedTime > 0 {
		return time.Unix(m.OrderData.Data.OrderCompletedTime, 0).Format(m.GetTimeLayout("orderConsignTime"))
	}

	return ""
}

func (m *MeituanOrder) GetExtraFields() (interface{}, error) {
	return nil, nil
}

func (m *MeituanOrder) GetCondition() bson.M {
	condition := bson.M{
		"accountId": m.AccountId,
		"orderId":   m.GetOrderId(),
		"isDeleted": false,
	}

	return condition
}

func (m *MeituanOrder) IsFiltered() bool {
	return false
}

func (m *MeituanOrder) UpsertExtraTrade(trade *Trade) (*Trade, error) {
	trade.ErpOrderId = m.GetOrderId()
	return trade, nil
}

func (m *MeituanOrder) GetTimeLayout(timeType string) string {
	switch timeType {
	case "orderCreateTime", "orderUpdateTime", "orderCompleteTime", "orderPayTime", "orderConsignTime":
		return share.LAYOUT_BY_MINUTE
	}

	return time.RFC3339
}

func (m *MeituanOrder) GetOrderSourceJsonStr() (string, error) {
	encodedOrder, err := json.Marshal(m.OrderData)
	return string(encodedOrder), err
}

func (m *MeituanOrder) AfterUpsert(trade *Trade) error {
	if trade.DisableEvent {
		return nil
	}

	if trade.IsNeedSendMemberEvent(m.Trade, "OrderCompleteTime") {
		m.SendOrderCompleteEvent(trade)
	}
	return nil
}

func (m *MeituanOrder) SendOrderCompleteEvent(trade *Trade) {
	if trade.OrderCompleteTime.IsZero() {
		return
	}
	goodMsg, goodsList := trade.GetGoodMsgAndList()
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_ORDER_COMPLETED + ":" + trade.OrderId,
		AccountId:  trade.AccountId.Hex(),
		MemberId:   trade.MemberId.Hex(),
		ChannelId:  trade.GetChannelId(m.Context),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_ORDER_COMPLETED,
		CreateTime: trade.OrderCompleteTime.UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"memberId":        trade.MemberId.Hex(),
			"id":              trade.Id.Hex(),
			"number":          trade.OrderId,
			"orderId":         trade.OrderId,
			"platform":        trade.OrderPlatform,
			"storeId":         trade.StoreId.Hex(),
			"storeName":       trade.StoreName,
			"goodsAmount":     goodMsg["goodsPayAmount"],
			"logisticsFee":    share.Yuan2Fen(trade.PostalAmount),
			"completedAt":     trade.OrderCompleteTime.UnixNano() / 1e6,
			"orderCreateTime": trade.OrderCreateTime.UnixNano() / 1e6,
			"goodsList":       goodsList,
		},
	}
	if trade.CurrentLevel != 0 {
		eventBody.EventProperties["memberLevel"] = trade.CurrentLevel
	}

	eventBody.SendCustomerEvent(m.Context)
}

func (m *MeituanOrder) IsVirtual() bool {
	return false
}

func (m *MeituanOrder) GetChannelId() string {
	return m.TempProvider.Channel.ChannelId
}

func (m *MeituanOrder) GetStaffId() bson.ObjectId {
	return bson.NilObjectId
}

func (m *MeituanOrder) GetStaffCode() string {
	return ""
}
