package order

import (
	"context"
	"encoding/json"
	"fmt"
	"mairpc/core/component"
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	pb_account "mairpc/proto/account"
	"mairpc/proto/client"
	pb_origin "mairpc/proto/common/origin"
	"mairpc/proto/common/types"
	pb_member "mairpc/proto/member"
	pb_order "mairpc/proto/trade/order"
	model_coupon "mairpc/service/coupon/model"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"
	"mairpc/service/trade/model/store"
	"mairpc/service/trade/share"
	"strings"
	"time"
)

// 接口创建的订单
// 标识订单是调接口插入的，以区别同步进来的订单的判断：是否存在 SyncHistoryId
// 只有同步订单和导入订单会生成 tradeSyncHistory，所以通过这个字段判断
type ApiOrder struct {
	Context     context.Context
	AccountId   bson.ObjectId
	ApiReq      *pb_order.UpsertOrderRequest
	Store       *store.TradeStore // upsert trade 创建新订单之前先 upsert tradeStore
	GoodsDetail *[]GoodsDetail
	Member      *pb_member.MemberDetailResponse
	OldTrade    *Trade // 数据库查找的已存在的订单
	Edition     string // 对应于 upsertOrder 接口的版本
}

func (a *ApiOrder) Init(ctx context.Context, apiReq *pb_order.UpsertOrderRequest, edition string) error {
	a.Context = ctx
	a.AccountId = util.GetAccountIdAsObjectId(ctx)
	a.ApiReq = apiReq
	a.Edition = edition

	var err error
	a.GoodsDetail, err = genGoodsDetail(apiReq, edition)
	if err != nil {
		return err
	}

	a.Store, err = upsertTradeStore(ctx, apiReq)
	if err != nil {
		return err
	}

	condition := a.GetCondition()
	a.OldTrade = CTrade.GetByCondition(ctx, condition)
	if a.OldTrade != nil {
		a.GoodsDetail = a.OldTrade.MergeGoodsDetail(a.GoodsDetail)
	}

	return nil
}

func extraFieldsTransformer(extraFields string) interface{} {
	if extraFields == "" {
		return nil
	}
	var extra interface{}
	decoder := json.NewDecoder(strings.NewReader(extraFields))
	decoder.UseNumber()
	if err := decoder.Decode(&extra); err != nil {
		return nil
	}
	return extra
}

func propertiesTransformer(pbProperties []*pb_order.Property) map[string]interface{} {
	if len(pbProperties) == 0 {
		return nil
	}
	properties := make(map[string]interface{})
	for _, property := range pbProperties {
		if value := getPropertyValue(property); value != nil {
			properties[property.Name] = value
		}
	}
	return properties
}

func getPropertyValue(property *pb_order.Property) interface{} {
	switch value := property.GetValue().(type) {
	case *pb_order.Property_ValueInt:
		return value.ValueInt
	case *pb_order.Property_ValueDouble:
		return value.ValueDouble
	case *pb_order.Property_ValueBool:
		return value.ValueBool
	case *pb_order.Property_ValueString:
		return value.ValueString
	}
	return nil
}

func genGoodsDetail(req *pb_order.UpsertOrderRequest, edition string) (*[]GoodsDetail, error) {
	if len(req.GoodsDetail) == 0 {
		return nil, errors.NewInvalidArgumentErrorWithMessage("goodsDetail", "Can not be empty")
	}

	if edition == share.UPSERT_ORDER_EDITION_V3 {
		goodsDetail := []GoodsDetail{}
		for _, good := range req.GoodsDetail {
			goodDetail := GoodsDetail{}
			copier.Instance(nil).RegisterResetDiffField(
				[]copier.DiffFieldPair{
					{Origin: "ExtraFields", Targets: []string{"ExtraFields"}},
				},
			).RegisterTransformer(copier.Transformer{
				"ExtraFields": extraFieldsTransformer,
				"Properties":  propertiesTransformer,
			}).From(good).CopyTo(&goodDetail)
			goodsDetail = append(goodsDetail, goodDetail)
		}
		return &goodsDetail, nil
	}

	goodsDetail := []GoodsDetail{}
	for _, good := range req.GoodsDetail {
		discountPrice := 0.0
		subtotalAmount := 0.0
		goodDiscountPriceFen := share.Yuan2Fen(good.DiscountPrice)
		goodSubtotalAmountFen := share.Yuan2Fen(good.SubtotalAmount)
		if goodDiscountPriceFen > 0 {
			discountPrice = good.DiscountPrice
			if goodSubtotalAmountFen == 0 {
				subtotalAmount = float64(goodDiscountPriceFen*good.Count) / 100
			}
		}
		if goodSubtotalAmountFen > 0 {
			subtotalAmount = good.SubtotalAmount
			if goodDiscountPriceFen == 0 {
				discountPriceFen := util.Round(float64(goodSubtotalAmountFen) / float64(good.Count))
				discountPrice = float64(discountPriceFen) / 100
			}
		}
		if goodDiscountPriceFen == 0 && goodSubtotalAmountFen == 0 {
			discountPrice = good.Price
			discountPriceFen := share.Yuan2Fen(discountPrice)
			subtotalAmount = float64(discountPriceFen*good.Count) / 100
		}

		discount := 0.0
		if !core_util.IsZero(good.Discount) {
			discount = good.Discount
		} else {
			goodPriceFen := share.Yuan2Fen(good.Price)
			discountPriceFen := share.Yuan2Fen(discountPrice)
			discount = float64(share.Uint64AmountSub(goodPriceFen, discountPriceFen)*good.Count) / 100
		}

		goodDetail := GoodsDetail{}
		copier.Instance(nil).RegisterResetDiffField(
			[]copier.DiffFieldPair{
				{Origin: "ExtraFields", Targets: []string{"ExtraFields"}},
			},
		).RegisterTransformer(copier.Transformer{
			"ExtraFields": extraFieldsTransformer,
			"Properties":  propertiesTransformer,
		}).From(good).CopyTo(&goodDetail)
		goodDetail.DiscountPrice = discountPrice
		goodDetail.Discount = discount
		goodDetail.SubtotalAmount = subtotalAmount
		goodsDetail = append(goodsDetail, goodDetail)
	}
	return &goodsDetail, nil
}

func upsertTradeStore(ctx context.Context, req *pb_order.UpsertOrderRequest) (*store.TradeStore, error) {
	tradeStore := store.CTradeStore.FindBySid(ctx, req.Sid)
	if tradeStore == nil {
		if req.OrderPlatform == "" {
			return nil, errors.NewInvalidArgumentErrorWithMessage("orderPlatform", "Missing parameter")
		}
		storeName := req.StoreName
		if req.StoreName == "" {
			storeName = req.Sid
		}

		tradeStore = &store.TradeStore{
			Type:            req.OrderPlatform,
			Platform:        req.OrderPlatform,
			Sid:             req.Sid,
			Name:            storeName,
			IsNew:           true,
			IsDisabled:      true,
			IsCreateChannel: req.IsCreateChannel,
		}
		var err error
		tradeStore, err = tradeStore.Create(ctx)
		if err != nil {
			return nil, err
		} else {
			return tradeStore, nil
		}
	}
	// 之前删除的店铺，需要重置一些字段，已存在的店铺不需要
	if tradeStore.IsDeleted {
		tradeStore.SyncType = ""
		tradeStore.SyncStatus = ""
		tradeStore.SyncTime = time.Time{}
		tradeStore.SyncRefundStatus = ""
		tradeStore.SyncRefundTime = time.Time{}
		tradeStore.IsDeleted = false
		tradeStore.IsDisabled = true
	}
	if req.StoreName != "" {
		tradeStore.Name = req.StoreName
	}
	if req.OrderPlatform != "" {
		tradeStore.Type = req.OrderPlatform
		tradeStore.Platform = req.OrderPlatform
	}
	if err := tradeStore.Update(ctx); err != nil {
		return nil, err
	} else {
		return tradeStore, nil
	}
}

func (a *ApiOrder) GetOrderId() string {
	return a.ApiReq.OrderId
}

func (a *ApiOrder) GetOrderFrom() string {
	if a.ApiReq.OrderFrom != "" {
		return a.ApiReq.OrderFrom
	}
	return a.GetOrderPlatform()
}

func (a *ApiOrder) GetOrderPlatform() string {
	return a.Store.Platform
}

func (a *ApiOrder) GetOrderCreateTime() string {
	return a.ApiReq.OrderCreateTime
}

func (a *ApiOrder) GetOrderUpdateTime() string {
	return a.ApiReq.OrderUpdateTime
}

func (a *ApiOrder) GetOrderCompleteTime() string {
	return a.ApiReq.OrderCompleteTime
}

func (a *ApiOrder) GetOrderPayTime() string {
	return a.ApiReq.OrderPayTime
}

func (a *ApiOrder) GetOrderStatus() string {
	return a.ApiReq.OrderStatus
}

func (a *ApiOrder) GetGoodsAmount() float64 {
	if a.ApiReq.GoodsAmount > 0 {
		return a.ApiReq.GoodsAmount
	}
	return getAmount(a.GoodsDetail, "price")
}

func getAmount(goodsDetail *[]GoodsDetail, field string) float64 {
	amount := uint64(0)
	for _, goodDetail := range *goodsDetail {
		baseAmount := uint64(0)
		if field == "price" {
			baseAmount = share.Yuan2Fen(goodDetail.Price)
		}
		if field == "discountPrice" {
			baseAmount = share.Yuan2Fen(goodDetail.DiscountPrice)
		}
		amount += baseAmount * goodDetail.Count
	}
	return float64(amount) / 100
}

func (a *ApiOrder) GetPostalAmount() float64 {
	return a.ApiReq.PostalAmount
}

func (a *ApiOrder) GetActualAmount() float64 {
	if a.Edition == share.UPSERT_ORDER_EDITION_V3 {
		return a.ApiReq.ActualAmount
	}

	if !core_util.IsZero(a.ApiReq.ActualAmount) {
		return a.ApiReq.ActualAmount
	}

	return getAmount(a.GoodsDetail, "discountPrice") + a.GetPostalAmount()
}

func (a *ApiOrder) GetDiscountAmount() float64 {
	if a.Edition == share.UPSERT_ORDER_EDITION_V3 {
		return a.ApiReq.DiscountAmount
	}

	if !core_util.IsZero(a.ApiReq.DiscountAmount) {
		return a.ApiReq.DiscountAmount
	}
	totalAmount := share.Yuan2Fen(a.GetGoodsAmount()) + share.Yuan2Fen(a.GetPostalAmount())
	actualAmount := share.Yuan2Fen(a.GetActualAmount())
	discountAmountFen := share.Uint64AmountSub(totalAmount, actualAmount)
	return float64(discountAmountFen) / 100
}

func (a *ApiOrder) GetPaymentType() string {
	return a.ApiReq.PaymentType
}

func (a *ApiOrder) GetBuyerId() string {
	return a.ApiReq.BuyerId
}

func (a *ApiOrder) GetBuyerNickname() string {
	if a.ApiReq.BuyerNickname == "" {
		return a.ApiReq.BuyerId
	}
	return a.ApiReq.BuyerNickname
}

func (a *ApiOrder) GetGoodsDetail() *[]GoodsDetail {
	return a.GoodsDetail
}

func (a *ApiOrder) UpsertMember() (*pb_member.MemberDetailResponse, error) {
	ctx := a.Context
	req := a.ApiReq
	tradeStore := a.Store
	// 无主订单
	if req.BuyerId == "" {
		return nil, nil
	}

	if bson.IsObjectIdHex(req.BuyerId) {
		member, _ := share.GetMemberById(ctx, bson.ObjectIdHex(req.BuyerId), []string{}, "")
		if member != nil && member.Id != "" {
			if err := populateMemberPhone(ctx, req, tradeStore, member); err != nil {
				return nil, err
			}
			a.Member = member
			return member, nil
		}
	}

	member, _ := share.GetMemberByOpenId(ctx, req.BuyerId, []string{"Properties"}, "")
	if member != nil && member.Id != "" {
		if err := populateMemberPhone(ctx, req, tradeStore, member); err != nil {
			return nil, err
		}
		a.Member = member
		return member, nil
	}

	if req.UnionId != "" {
		member, _ := share.GetMemberByUnionId(ctx, req.UnionId, []string{"Properties"}, "")
		if member != nil && member.Id != "" {
			if err := populateMemberPhone(ctx, req, tradeStore, member); err != nil {
				return nil, err
			}
			a.Member = member
			return member, nil
		}
	}

	if req.BuyerPhone != "" {
		member, _ := share.GetMemberByPhone(ctx, req.BuyerPhone, []string{"Properties"}, "")
		if member != nil && member.Id != "" {
			if err := updateMemberSocial(ctx, req, member); err != nil {
				return nil, err
			}
			a.Member = member
			return member, nil
		}
	}

	// 设置 upsertMember 参数
	channelName := tradeStore.Name
	channelId := tradeStore.Sid
	channelOrigin := getOrigin(a.Store.Platform)
	if req.ChannelId != "" {
		channel, err := share.GetEnableChannel(ctx, req.ChannelId)
		if err != nil {
			return nil, err
		}
		channelId = req.ChannelId
		if channel.Name != "" {
			channelName = channel.Name
		}
		if channel.Origin != "" {
			channelOrigin = channel.Origin
		}
	}
	upsertReq := &pb_member.UpsertMemberRequest{
		OriginFrom: &pb_origin.OriginInfo{
			Channel:     channelId,
			ChannelName: channelName,
			OpenId:      req.BuyerId,
			Origin:      channelOrigin,
		},
	}
	if req.UnionId != "" {
		upsertReq.OriginFrom.UnionId = req.UnionId
	}

	propertiesMap := map[string]interface{}{}
	propertyNameValue := ""
	if a.Member != nil && a.Member.Name != "" {
		propertyNameValue = a.Member.Name
	} else if req.BuyerNickname != "" {
		propertyNameValue = req.BuyerNickname
	}
	if propertyNameValue != "" {
		propertiesMap["name"] = &pb_member.PropertyInfo_ValueString{
			ValueString: &pb_member.PropertyStringValue{
				Value: propertyNameValue,
			},
		}
	}
	if req.BuyerPhone != "" {
		propertiesMap["phone"] = &pb_member.PropertyInfo_ValueString{
			ValueString: &pb_member.PropertyStringValue{
				Value: req.BuyerPhone,
			},
		}
	} else if tradeStore.IsMergeReceiver {
		mobile := req.Receiver.Mobile
		if mobile != "" && validators.CValidator.IsPhone(mobile, nil) {
			propertiesMap["phone"] = &pb_member.PropertyInfo_ValueString{
				ValueString: &pb_member.PropertyStringValue{
					Value: mobile,
				},
			}
		}
		country := "中国"
		if req.Receiver.Country != "" {
			country = req.Receiver.Country
		}
		if req.Receiver.Province != "" {
			propertiesMap["address"] = &pb_member.PropertyInfo_ValueArray{
				ValueArray: &pb_member.PropertyArrayValue{
					Value: []string{
						country,
						req.Receiver.Province,
						req.Receiver.City,
						req.Receiver.District,
						req.Receiver.Address,
					},
				},
			}
		}
	}
	extraBoolCond := map[string]*types.BoolValue{}
	extraBoolCond["isDefault"] = &types.BoolValue{Value: true}
	propertyInfos, getPropertyInfosErr := share.GetPropertyInfos(ctx, propertiesMap, "name", extraBoolCond)
	if getPropertyInfosErr != nil {
		return nil, getPropertyInfosErr
	}
	upsertReq.Properties = propertyInfos

	insertedMember, upsertMemberErr := share.UpsertMember(ctx, upsertReq)
	if upsertMemberErr != nil {
		return nil, upsertMemberErr
	}
	a.Member = insertedMember
	return insertedMember, nil
}

func populateMemberPhone(ctx context.Context, req *pb_order.UpsertOrderRequest, tradeStore *store.TradeStore, member *pb_member.MemberDetailResponse) error {
	if member.Phone != "" {
		return nil
	}
	if !tradeStore.IsMergeReceiver {
		return nil
	}
	if req.Receiver.Mobile == "" {
		return nil
	}
	propertiesMap := map[string]interface{}{}
	propertiesMap["phone"] = &pb_member.PropertyInfo_ValueString{
		ValueString: &pb_member.PropertyStringValue{
			Value: req.Receiver.Mobile,
		},
	}
	extraBoolCond := map[string]*types.BoolValue{}
	extraBoolCond["isDefault"] = &types.BoolValue{Value: true}
	propertyInfos, getPropertyInfosErr := share.GetPropertyInfos(ctx, propertiesMap, "name", extraBoolCond)
	if getPropertyInfosErr != nil {
		return getPropertyInfosErr
	}

	updateMemberReq := &pb_member.BatchUpdateMemberPropertyRequest{
		MemberIds:  []string{member.Id},
		Properties: propertyInfos,
	}
	err := share.BatchUpdateMemberProperty(ctx, updateMemberReq)
	if err != nil {
		return err
	}
	return nil
}

func getOrigin(origin string) string {
	if origin == "tmall" {
		return "taobao"
	}
	if !util.StrInArray(origin, &constant.Origins) {
		return constant.OTHERS
	}
	return origin
}

func updateMemberSocial(ctx context.Context, req *pb_order.UpsertOrderRequest, member *pb_member.MemberDetailResponse) error {
	if bson.IsObjectIdHex(req.BuyerId) || req.ChannelId == "" {
		return nil
	}
	for _, social := range member.Socials {
		if social.Channel == req.ChannelId {
			return nil
		}
	}

	channel, _ := share.GetEnableChannel(ctx, req.ChannelId)
	if channel == nil {
		return nil
	}
	_, err := share.UpdateMember(ctx, &pb_member.UpdateMemberRequest{
		MemberId: member.Id,
		AddedSocials: []*pb_origin.OriginInfo{
			{
				Channel:     req.ChannelId,
				ChannelName: channel.Name,
				OpenId:      req.BuyerId,
				Origin:      channel.Origin,
			},
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (a *ApiOrder) UpsertReceiverMember() (*pb_member.MemberDetailResponse, error) {
	ctx := a.Context
	req := a.ApiReq
	tradeStore := a.Store
	mobile := req.Receiver.Mobile
	if req.IsNotCreateReceiver {
		return nil, nil
	}
	if mobile == "" || !validators.CValidator.IsPhone(mobile, nil) {
		return nil, nil
	}

	member, _ := share.GetMemberByPhone(ctx, mobile, []string{"Properties"}, "")
	if member != nil && member.Id != "" {
		return member, nil
	}

	propertiesMap := map[string]interface{}{}
	if req.Receiver.Province != "" {
		country := "中国"
		if req.Receiver.Country != "" {
			country = req.Receiver.Country
		}
		propertiesMap["address"] = &pb_member.PropertyInfo_ValueArray{
			ValueArray: &pb_member.PropertyArrayValue{
				Value: []string{
					country,
					req.Receiver.Province,
					req.Receiver.City,
					req.Receiver.District,
					req.Receiver.Address,
				},
			},
		}
	}
	if req.Receiver.Name != "" {
		propertiesMap["name"] = &pb_member.PropertyInfo_ValueString{
			ValueString: &pb_member.PropertyStringValue{
				Value: req.Receiver.Name,
			},
		}
	}
	if mobile != "" {
		propertiesMap["phone"] = &pb_member.PropertyInfo_ValueString{
			ValueString: &pb_member.PropertyStringValue{
				Value: mobile,
			},
		}
	}
	extraBoolCond := map[string]*types.BoolValue{}
	extraBoolCond["isDefault"] = &types.BoolValue{Value: true}
	propertyInfos, getPropertyInfosErr := share.GetPropertyInfos(ctx, propertiesMap, "name", extraBoolCond)
	if getPropertyInfosErr != nil {
		return nil, getPropertyInfosErr
	}
	origin := getOrigin(a.Store.Platform)
	upsertReq := &pb_member.UpsertMemberRequest{
		OriginFrom: &pb_origin.OriginInfo{
			Channel:     tradeStore.Sid,
			ChannelName: tradeStore.Name,
			OpenId:      fmt.Sprintf("%s:%s", origin, mobile),
			Nickname:    req.Receiver.Name,
			Origin:      origin,
		},
		Properties: propertyInfos,
	}
	insertedMember, upsertMemberErr := share.UpsertMember(ctx, upsertReq)
	if upsertMemberErr != nil {
		return nil, upsertMemberErr
	}
	return insertedMember, nil
}

func (a *ApiOrder) GetReceiver() (*Receiver, error) {
	if a.ApiReq.Receiver == nil {
		return nil, nil
	}
	receiver := &Receiver{}
	copier.Instance(nil).From(a.ApiReq.Receiver).CopyTo(receiver)
	receiverMember, err := a.UpsertReceiverMember()
	if err != nil {
		return nil, err
	}
	if receiverMember != nil && receiverMember.Id != "" {
		receiver.MemberId = bson.ObjectIdHex(receiverMember.Id)
	}
	return receiver, nil
}

func (a *ApiOrder) GetDelivery() (*Delivery, error) {
	if a.ApiReq.Delivery == nil {
		return nil, nil
	}
	delivery := &Delivery{}
	copier.Instance(nil).From(a.ApiReq.Delivery).CopyTo(delivery)
	return delivery, nil
}

func (a *ApiOrder) GetCloseReason() string {
	if a.GetOrderStatus() == share.ORDER_STATUS_TRADE_CLOSED {
		return a.ApiReq.CloseReason
	}
	return ""
}

func (a *ApiOrder) GetDealCode() string {
	return ""
}

func (a *ApiOrder) GetOutTradeNo() string {
	return a.ApiReq.ExternalTradeNo
}

func (a *ApiOrder) GetCoupons() (*[]Coupon, error) {
	coupons := mergeCoupons(a.Context, a.OldTrade, a.Member, a.ApiReq)
	if coupons != nil && len(*coupons) > 0 {
		return coupons, nil
	}
	return nil, nil
}

func mergeCoupons(ctx context.Context, trade *Trade, member *pb_member.MemberDetailResponse, req *pb_order.UpsertOrderRequest) *[]Coupon {
	if member == nil || len(req.CouponCodes) == 0 {
		if trade == nil {
			return nil
		} else {
			return &trade.Coupons
		}
	}
	memberId := bson.ObjectIdHex(member.Id)
	coupons := []Coupon{}
	if trade != nil && len(trade.Coupons) > 0 {
		coupons = trade.Coupons
	}

	existedCouponCodes := []string{}
	for _, coupon := range coupons {
		existedCouponCodes = append(existedCouponCodes, coupon.Code)
	}

	for _, couponCode := range req.CouponCodes {
		if util.StrInArray(couponCode, &existedCouponCodes) {
			continue
		}
		result := model_coupon.CMembershipDiscount.GetAllByCodeAndMemberId(ctx, []string{couponCode}, memberId)
		if result == nil {
			continue
		}
		coupon := Coupon{
			Id:     result[0].Coupon.Id.Hex(),
			Title:  result[0].Coupon.Title,
			Amount: 0,
			Code:   result[0].Code,
		}
		coupons = append(coupons, coupon)
	}

	return &coupons
}

func (a *ApiOrder) GetUtm() (*Utm, error) {
	if a.ApiReq.Utm == nil {
		return nil, nil
	}
	utm := &Utm{}
	copier.Instance(nil).From(a.ApiReq.Utm).CopyTo(utm)
	return utm, nil
}

func (a *ApiOrder) GetRefundStatus() string {
	return a.ApiReq.RefundStatus
}

func (a *ApiOrder) GetBuyerMessage() string {
	return a.ApiReq.BuyerMessage
}

func (a *ApiOrder) GetSellerMemo() string {
	return a.ApiReq.SellerMemo
}

func (a *ApiOrder) GetOrderConsignTime() string {
	return a.ApiReq.OrderConsignTime
}

func (a *ApiOrder) GetExtraFields() (interface{}, error) {
	if a.ApiReq.ExtraFields != "" {
		var extra interface{}
		decoder := json.NewDecoder(strings.NewReader(a.ApiReq.ExtraFields))
		decoder.UseNumber()
		if err := decoder.Decode(&extra); err != nil {
			return nil, err
		}
		return extra, nil
	}
	return nil, nil
}

func (a *ApiOrder) GetCondition() bson.M {
	return bson.M{
		"accountId": a.AccountId,
		"orderId":   a.GetOrderId(),
		"isDeleted": false,
	}
}

func (a *ApiOrder) IsFiltered() bool {
	return false
}

func (a *ApiOrder) UpsertExtraTrade(trade *Trade) (*Trade, error) {
	// 默认取下单人 member.Level，如果传入参数存在则取传入的参数值
	if a.ApiReq.CurrentLevel > 0 {
		trade.CurrentLevel = a.ApiReq.CurrentLevel
	}
	trade.ErpOrderId = a.GetOrderId()
	if a.ApiReq.ErpOrderId != "" {
		trade.ErpOrderId = a.ApiReq.ErpOrderId
	}
	if a.ApiReq.OccurredAt != "" {
		timeResult, err := GetTime(a.ApiReq.OccurredAt, a.GetTimeLayout("occurredAt"))
		if err != nil {
			return nil, err
		}
		trade.OccurredAt = *timeResult
	}
	trade.ChannelId = a.ApiReq.ChannelId
	trade.StoreCode = a.ApiReq.StoreCode
	if a.ApiReq.StaffId != "" {
		trade.StaffId = bson.ObjectIdHex(a.ApiReq.StaffId)
	}
	trade.StaffCode = a.ApiReq.StaffCode
	trade.DisableEvent = a.ApiReq.DisableEvent

	// 处理币种和汇率
	trade.LocalCurrency = a.ApiReq.LocalCurrency
	trade.ExchangeRatio = a.ApiReq.ExchangeRatio
	var handleErr error
	trade, handleErr = handleCurrencyAndExchangeRadio(a.Context, trade, a.ApiReq)
	if handleErr != nil {
		return nil, handleErr
	}
	trade.AddedScore = a.ApiReq.AddedScore
	if a.ApiReq.RefundTime != "" {
		timeResult, err := GetTime(a.ApiReq.RefundTime, a.GetTimeLayout("refundTime"))
		if err != nil {
			return nil, err
		}
		trade.RefundTime = *timeResult
	}
	if len(a.ApiReq.Properties) > 0 {
		trade.Properties = propertiesTransformer(a.ApiReq.Properties)
	}

	return trade, nil
}

func handleCurrencyAndExchangeRadio(ctx context.Context, trade *Trade, req *pb_order.UpsertOrderRequest) (*Trade, error) {
	if req.LocalCurrency != "" {
		exchangeRatio, err := getCurrencyExchangeRatio(ctx, req.LocalCurrency, req.ExchangeRatio)
		if err != nil {
			return nil, err
		}
		trade.GoodsAmount = util.RoundTo(trade.GoodsAmount*exchangeRatio, 2)
		trade.ActualAmount = util.RoundTo(trade.ActualAmount*exchangeRatio, 2)
		trade.PostalAmount = util.RoundTo(trade.PostalAmount*exchangeRatio, 2)
		discountAmount := trade.GoodsAmount + trade.PostalAmount - trade.ActualAmount
		if discountAmount < 0 {
			discountAmount = 0
		}
		trade.DiscountAmount = util.RoundTo(discountAmount, 2)
	}
	return trade, nil
}

func getCurrencyExchangeRatio(ctx context.Context, localCurrency string, exchangeRatio float64) (float64, error) {
	setting := CTradeSetting.GetByCondition(ctx, bson.M{})
	if setting.SystemCurrency == "" {
		return 0, errors.NewInternal("请联系管理员，配置系统货币配置")
	}
	exchangeCurrency := CTradeExchangeCurrency.GetByCode(ctx, localCurrency)
	if exchangeCurrency == nil && core_util.IsZero(exchangeRatio) {
		message := fmt.Sprintf("请联系管理员，去群脉后台配置%s: 兑换汇率", localCurrency)
		return 0, errors.NewInternal(message)
	}

	if exchangeCurrency == nil {
		exchangeCurrency = &TradeExchangeCurrency{
			Code:  localCurrency,
			Name:  "",
			Ratio: exchangeRatio,
		}
		if err := exchangeCurrency.Create(ctx); err != nil {
			return 0, err
		}
	}
	return exchangeCurrency.Ratio, nil
}

func (a *ApiOrder) GetTimeLayout(string) string {
	// 接口里的时间参数格式保持一致
	return share.LAYOUT_BY_MINUTE
}

func (a *ApiOrder) GetOrderSourceJsonStr() (string, error) {
	encodedOrder, err := json.Marshal(a.ApiReq)
	return string(encodedOrder), err
}

func (a *ApiOrder) AfterUpsert(trade *Trade) error {
	if !trade.DisableEvent {
		component.GO(a.Context, func(ctx context.Context) {
			// 发事件的逻辑：根据时间字段是否发生变更来判断
			// 例如 OrderConsignTime 在创建订单时传入也认为是一次变更，从无到有的变更；此外还有订单更新时该字段从旧到新的变更，都会触发订单发货事件
			a.sendMemberEvent(ctx, a.OldTrade, trade)
		})
	}
	return nil
}

func (a *ApiOrder) sendMemberEvent(ctx context.Context, old, new *Trade) {
	notSendEventLog := []string{
		share.ORDER_FROM_RETAIL,
		share.ORDER_FROM_MICROMALL,
	}
	hasPlatformSentSameEvent := util.StrInArray(new.OrderFrom, &notSendEventLog)
	if !hasPlatformSentSameEvent && new.IsNeedSendMemberEvent(old, "OrderPayTime") {
		log.Warn(ctx, "get SendPurchaseEvent", log.Fields{
			"OrderPayTime": new.OrderPayTime.Format(share.LAYOUT_BY_MINUTE),
		})
		new.SendPurchaseEvent(ctx)
		new.SendPurchaseProductEvent(ctx)
	}

	// 私域商城订单已发送订单发货事件，无需在这里再次发送
	if new.OrderFrom != share.ORDER_FROM_RETAIL && new.IsNeedSendMemberEvent(old, "OrderConsignTime") {
		log.Warn(ctx, "get SendOrderShipEvent", log.Fields{
			"OrderConsignTime": new.OrderConsignTime.Format(share.LAYOUT_BY_MINUTE),
		})
		new.SendOrderShipEvent(ctx)
	}

	if !hasPlatformSentSameEvent && new.IsNeedSendMemberEvent(old, "OrderCompleteTime") {
		log.Warn(ctx, "get SendOrderCompleteEvent", log.Fields{
			"OrderCompleteTime": new.OrderCompleteTime.Format(share.LAYOUT_BY_MINUTE),
		})
		new.SendOrderCompleteEvent(ctx)
	}
}

func (a *ApiOrder) IsVirtual() bool {
	return false
}

func (a *ApiOrder) GetChannelId() string {
	if a.ApiReq != nil && a.ApiReq.ChannelId != "" {
		return a.ApiReq.ChannelId
	}
	if strings.ToLower(a.GetOrderPlatform()) == PLATFORM_POS {
		response, _ := client.GetAccountServiceClient().GetChannel(a.Context, &pb_account.ChannelDetailRequest{Origin: "pos"})
		if response != nil {
			return response.ChannelId
		}
	}
	if a.Store != nil {
		return a.Store.Sid
	}
	return ""
}

func (a *ApiOrder) GetStaffId() bson.ObjectId {
	return bson.NilObjectId
}

func (a *ApiOrder) GetStaffCode() string {
	return ""
}
