package order

import (
	"context"
	"fmt"
	core_component "mairpc/core/component"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	pb_account "mairpc/proto/account"
	proto_client "mairpc/proto/client"
	"mairpc/proto/member"
	pb_store "mairpc/proto/store"
	trade_member "mairpc/proto/trade/member"
	trade_store "mairpc/proto/trade/store"
	"mairpc/service/member/model"
	"mairpc/service/share/component"
	"mairpc/service/share/util"
	"mairpc/service/trade/client"
	"mairpc/service/trade/model/store"
	"mairpc/service/trade/share"
	"math"
	"reflect"
	"strings"
	"sync"
	"time"

	"github.com/spf13/viper"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	C_TRADE = "trade"

	LAST_PAY_TIME              = "tradeLatestPayTime"
	FIRST_PAID_STORE           = "firstPaidStore"
	LAST_PAID_STORE            = "lastPaidStore"
	TRADE_BUYS                 = "tradeBuys"
	TRADE_TOTAL_CONSUME_AMOUNT = "tradeTotalConsumeAmount"
	TRADE_AVERAGE_AMOUNT       = "tradeAverageAmount"

	PLATFORM_RETAIL    = "mai-retail"
	PLATFORM_MICROMALL = "micromall"
	PLATFORM_POS       = "pos"

	REFUND_STATUS_REFUNDED = "refunded"
	// 折扣类型
	ORDER_DISCOUNT_TYPE_COUPON   = "coupon"   // 优惠券
	ORDER_DISCOUNT_TYPE_PRESENT  = "present"  // 买赠券
	ORDER_DISCOUNT_TYPE_DELIVERY = "delivery" // 运费券

	ORDER_DELIVERY_METHOD_EXPRESS        = "express"        // 快递
	ORDER_DELIVERY_METHOD_LOCAL_DELIVERY = "local_delivery" // 同城配送
	ORDER_DELIVERY_METHOD_SELF_FETCH     = "self_fetch"     // 自提订单
)

var (
	CTrade = &Trade{}

	UPDATE_TRADE_MEMBER_PROPERTY_LOCK_TEMPLATE = "%s:trade.order:update-trade-member-property:%s" // {accountId}:trade.order:update-trade-member-property:{memberId}
	UPDATE_TRADE_MEMBER_PROPERTY_LOCK_DURATION = 10                                               // 10s
)

type Trade struct {
	Id                       bson.ObjectId          `bson:"_id,omitempty"`
	AccountId                bson.ObjectId          `bson:"accountId"`
	CreatedAt                time.Time              `bson:"createdAt"`
	UpdatedAt                time.Time              `bson:"updatedAt"`
	IsDeleted                bool                   `bson:"isDeleted"`
	MemberId                 bson.ObjectId          `bson:"memberId,omitempty"`
	ChannelId                string                 `bson:"channelId,omitempty"`
	StoreId                  bson.ObjectId          `bson:"storeId"`
	StoreName                string                 `bson:"storeName"`
	OrderId                  string                 `bson:"orderId"`
	OrderFrom                string                 `bson:"orderFrom"`
	OrderPlatform            string                 `bson:"orderPlatform"`
	OrderStatus              string                 `bson:"orderStatus"`
	CloseReason              string                 `bson:"closeReason,omitempty"`
	GoodsAmount              float64                `bson:"goodsAmount"`
	DiscountAmount           float64                `bson:"discountAmount"`
	PostalAmount             float64                `bson:"postalAmount"`
	ActualAmount             float64                `bson:"actualAmount"`
	PaymentType              string                 `bson:"paymentType,omitempty"`
	BuyerId                  string                 `bson:"buyerId"`
	BuyerNickname            string                 `bson:"buyerNickname"`
	GoodsDetail              []GoodsDetail          `bson:"goodsDetail"`
	Receiver                 Receiver               `bson:"receiver"`
	Delivery                 Delivery               `bson:"delivery,omitempty"`
	SyncHistoryId            bson.ObjectId          `bson:"syncHistoryId,omitempty"`
	SceneId                  string                 `bson:"sceneId,omitempty"`
	ExtraFields              interface{}            `bson:"extraFields,omitempty"`
	StoreCode                string                 `bson:"storeCode,omitempty"`
	StaffId                  bson.ObjectId          `bson:"staffId,omitempty"`
	StaffCode                string                 `bson:"staffCode,omitempty"`
	DealCode                 string                 `bson:"dealCode,omitempty"`
	ErpOrderId               string                 `bson:"erpOrderId,omitempty"`
	RefundStatus             string                 `bson:"refundStatus"`
	RefundTime               time.Time              `bson:"refundTime,omitempty"`
	OutTradeNo               string                 `bson:"outTradeNo,omitempty"`
	AddedScore               float64                `bson:"addedScore,omitempty"`
	LocalCurrency            string                 `bson:"localCurrency,omitempty"`
	ExchangeRatio            float64                `bson:"exchangeRatio,omitempty"`
	Coupons                  []Coupon               `bson:"coupons,omitempty"`
	BuyerMessage             string                 `bson:"buyerMessage,omitempty"`
	SellerMemo               string                 `bson:"sellerMemo,omitempty"`
	HasSendMemberEventLog    interface{}            `bson:"hasSendMemberEventLog,omitempty"`
	Utm                      Utm                    `bson:"utm,omitempty"`
	CurrentLevel             uint64                 `bson:"currentLevel,omitempty"`
	OrderCreateTime          time.Time              `bson:"orderCreateTime"`
	OrderPayTime             time.Time              `bson:"orderPayTime,omitempty"`
	OrderCompleteTime        time.Time              `bson:"orderCompleteTime,omitempty"`
	OrderUpdateTime          time.Time              `bson:"orderUpdateTime,omitempty"`
	OrderConsignTime         time.Time              `bson:"orderConsignTime,omitempty"`
	OccurredAt               time.Time              `bson:"occurredAt,omitempty"`
	DisableEvent             bool                   `bson:"disableEvent,omitempty"`
	HasLatestPayTimeMigrated bool                   `bson:"hasLatestPayTimeMigrated,omitempty"`
	DeductAmountByScore      uint64                 `bson:"deductAmountByScore,omitempty"`
	Properties               map[string]interface{} `bson:"properties,omitempty"`

	sentPurchaseEvent bool `bson:"-"` // update 或者 create 之后发送了订单购买事件后为 true
}

type GoodsDetail struct {
	GoodsNumber     string                 `bson:"goodsNumber,omitempty"`
	Name            string                 `bson:"name,omitempty"`
	Price           float64                `bson:"price"`
	DiscountPrice   float64                `bson:"discountPrice"`
	Discount        float64                `bson:"discount"`
	Oid             string                 `bson:"oid,omitempty"`
	Count           uint64                 `bson:"count,omitempty"`
	Sku             string                 `bson:"sku,omitempty"`
	OutSkuId        string                 `bson:"outSkuId,omitempty"`
	SubtotalAmount  float64                `bson:"subtotalAmount"`
	PropertiesName  string                 `bson:"propertiesName,omitempty"`
	AfterSaleStatus string                 `bson:"afterSaleStatus,omitempty"`
	ThumbnailUrl    string                 `bson:"thumbnailUrl,omitempty"`
	Barcode         string                 `bson:"barcode,omitempty"`
	Category        string                 `bson:"category,omitempty"`
	Remark          string                 `bson:"remark,omitempty"`
	GiftType        string                 `bson:"giftType,omitempty"`
	Weight          float64                `bson:"weight,omitempty"`
	RefundAmount    float64                `bson:"refundAmount,omitempty"`
	HasSendEvent    bool                   `bson:"hasSendEvent,omitempty"` // 天猫订单发商品退款事件需要该字段避免重发
	ExtraFields     interface{}            `bson:"extraFields,omitempty"`
	ExternalSku     string                 `bson:"externalSku,omitempty"` // 商品规格外部编码 用于 mai-retail，历史数据存在，新数据已用 outSkuId 代替
	Properties      map[string]interface{} `bson:"properties,omitempty"`
}

type Receiver struct {
	Mobile    string        `bson:"mobile"`
	Name      string        `bson:"name"`
	Country   string        `bson:"country"`
	Province  string        `bson:"province"`
	City      string        `bson:"city"`
	District  string        `bson:"district"`
	Address   string        `bson:"address"`
	MemberId  bson.ObjectId `bson:"memberId,omitempty"`
	Telephone string        `bson:"telephone"`
}

type Delivery struct {
	ExpressNo      string `bson:"expressNo"`
	Type           string `bson:"type"`
	ExpressCompany string `bson:"expressCompany"`
}

type Coupon struct {
	Id     string `bson:"id"`
	Title  string `bson:"title"`
	Amount uint64 `bson:"amount"`
	Code   string `bson:"code"`
	Type   string `bson:"type"`
}

type Utm struct {
	UtmSource   string `bson:"utmSource,omitempty"`
	UtmMedium   string `bson:"utmMedium,omitempty"`
	UtmContent  string `bson:"utmContent,omitempty"`
	UtmCampaign string `bson:"utmCampaign,omitempty"`
	UtmTerm     string `bson:"utmTerm,omitempty"`
}

func (self *Trade) Create(ctx context.Context) error {
	self.Id = bson.NewObjectId()
	self.AccountId = util.GetAccountIdAsObjectId(ctx)
	self.CreatedAt = time.Now()
	self.UpdatedAt = time.Now()
	self.IsDeleted = false
	_, err := extension.DBRepository.Insert(ctx, C_TRADE, self)
	self.sentPurchaseEvent = self.AfterSave(ctx, nil)
	return err
}

func (self *Trade) GetByCondition(ctx context.Context, condition bson.M) *Trade {
	condition["accountId"] = util.GetAccountIdAsObjectId(ctx)

	trade := Trade{}
	err := extension.DBRepository.FindOne(ctx, C_TRADE, condition, &trade)
	if err != nil || trade.Id.Hex() == "" {
		return nil
	}

	return &trade
}

func (self *Trade) GetByOrderId(ctx context.Context, orderId string) *Trade {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"orderId":   orderId,
	}

	trade := Trade{}
	err := extension.DBRepository.FindOne(ctx, C_TRADE, selector, &trade)
	if err != nil || trade.Id.Hex() == "" {
		return nil
	}

	return &trade
}

func (self *Trade) GetByPagingCondition(ctx context.Context, page extension.PagingCondition) ([]Trade, int, error) {
	trades := []Trade{}
	total, err := extension.DBRepository.FindByPagination(ctx, C_TRADE, page, &trades)
	if err != nil {
		return nil, 0, err
	}
	return trades, total, err
}

func (self *Trade) Update(ctx context.Context) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       self.Id,
	}
	settor := bson.M{
		"updatedAt":      time.Now(),
		"storeId":        self.StoreId,
		"storeName":      self.StoreName,
		"orderId":        self.OrderId,
		"orderFrom":      self.OrderFrom,
		"orderPlatform":  self.OrderPlatform,
		"orderStatus":    self.OrderStatus,
		"goodsAmount":    self.GoodsAmount,
		"postalAmount":   self.PostalAmount,
		"actualAmount":   self.ActualAmount,
		"discountAmount": self.DiscountAmount,
		"paymentType":    self.PaymentType,
		"buyerId":        self.BuyerId,
		"buyerNickname":  self.BuyerNickname,
		"goodsDetail":    self.GoodsDetail,
		"receiver":       self.Receiver,
		"delivery":       self.Delivery,
		"disableEvent":   self.DisableEvent,
		"properties":     self.Properties,
	}
	if self.SyncHistoryId.Hex() != "" {
		settor["syncHistoryId"] = self.SyncHistoryId
	}
	if self.MemberId.Hex() != "" {
		settor["memberId"] = self.MemberId
	}
	if self.ChannelId != "" {
		settor["channelId"] = self.ChannelId
	}
	if self.CloseReason != "" {
		settor["closeReason"] = self.CloseReason
	}
	if self.SceneId != "" {
		settor["sceneId"] = self.SceneId
	}
	if self.StoreCode != "" {
		settor["storeCode"] = self.StoreCode
	}
	if self.StaffId.Hex() != "" {
		settor["staffId"] = self.StaffId
	}
	if self.StaffCode != "" {
		settor["staffCode"] = self.StaffCode
	}
	if self.DealCode != "" {
		settor["dealCode"] = self.DealCode
	}
	if self.ErpOrderId != "" {
		settor["erpOrderId"] = self.ErpOrderId
	}
	if self.RefundStatus != "" {
		settor["refundStatus"] = self.RefundStatus
	}
	if !self.RefundTime.IsZero() {
		settor["refundTime"] = self.RefundTime
	}
	if self.OutTradeNo != "" {
		settor["outTradeNo"] = self.OutTradeNo
	}
	if self.LocalCurrency != "" {
		settor["localCurrency"] = self.LocalCurrency
	}
	if len(self.Coupons) > 0 {
		settor["coupons"] = self.Coupons
	}
	if self.BuyerMessage != "" {
		settor["buyerMessage"] = self.BuyerMessage
	}
	if self.SellerMemo != "" {
		settor["sellerMemo"] = self.SellerMemo
	}
	if self.HasSendMemberEventLog != nil {
		settor["hasSendMemberEventLog"] = self.HasSendMemberEventLog
	}
	if self.ExtraFields != nil {
		settor["extraFields"] = self.ExtraFields
	}
	if !self.OrderCreateTime.IsZero() {
		settor["orderCreateTime"] = self.OrderCreateTime
	}
	if !self.OrderUpdateTime.IsZero() {
		settor["orderUpdateTime"] = self.OrderUpdateTime
	}
	if !self.OrderCompleteTime.IsZero() {
		settor["orderCompleteTime"] = self.OrderCompleteTime
	}
	if !self.OrderPayTime.IsZero() {
		settor["orderPayTime"] = self.OrderPayTime
	}
	if !self.OrderConsignTime.IsZero() {
		settor["orderConsignTime"] = self.OrderConsignTime
	}
	if !core_util.IsZero(self.AddedScore) {
		settor["addedScore"] = self.AddedScore
	}
	if !core_util.IsZero(self.ExchangeRatio) {
		settor["exchangeRatio"] = self.ExchangeRatio
	}
	if self.CurrentLevel != 0 {
		settor["currentLevel"] = self.CurrentLevel
	}

	updator := bson.M{
		"$set": settor,
	}

	old := CTrade.GetByCondition(ctx, selector)
	err := extension.DBRepository.UpdateOne(ctx, C_TRADE, selector, updator)
	self.sentPurchaseEvent = CTrade.AfterUpdate(ctx, old)
	return err
}

func (self *Trade) UpdateExtraFields(ctx context.Context) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       self.Id,
	}
	updator := bson.M{
		"$set": bson.M{
			"extraFields": self.ExtraFields,
			"updatedAt":   time.Now(),
		},
	}

	old := CTrade.GetByCondition(ctx, selector)
	err := extension.DBRepository.UpdateOne(ctx, C_TRADE, selector, updator)
	CTrade.AfterUpdate(ctx, old)
	return err
}

func (self *Trade) AfterUpdate(ctx context.Context, old *Trade) (sentPurchaseEvent bool) {
	if old != nil && old.Id.Hex() != "" {
		new := CTrade.GetByCondition(ctx, bson.M{"_id": old.Id})
		sentPurchaseEvent = new.AfterSave(ctx, old)
	}
	return
}

func (self *Trade) AfterSave(ctx context.Context, old *Trade) (sentPurchaseEvent bool) {
	if (old == nil && !self.OrderPayTime.IsZero()) || (!self.OrderPayTime.IsZero() && old.OrderPayTime.IsZero()) {
		core_component.GO(ctx, func(ctx context.Context) {
			err := UpdateMemberLatestPayTime(ctx, self.Id.Hex())
			if err != nil {
				log.Warn(ctx, "Failed to update member latest pay time after save trade", log.Fields{
					"trade":  self,
					"errMsg": err.Error(),
				})
			}
		})
	}
	if self.OrderFrom == share.ORDER_FROM_TMALL &&
		(old == nil || old.OrderStatus != self.OrderStatus) &&
		self.OrderStatus == share.ORDER_STATUS_TRADE_BUYER_SIGNED &&
		share.Yuan2Fen(self.ActualAmount) > 0 &&
		self.MemberId.Hex() != "" {
		uniqueId := self.OrderId
		if self.ErpOrderId != "" {
			uniqueId = self.ErpOrderId
		}
		UpdateMemberScore(ctx, self.MemberId, self.StoreId, self.ActualAmount, uniqueId)
	}

	if self.MemberId.Valid() && self.CurrentLevel > 0 {
		var (
			isOrderCompleted, isCompletedOrderOccurRefund bool
		)
		if self.OrderStatus == share.ORDER_STATUS_TRADE_BUYER_SIGNED && (old == nil || old.OrderStatus != self.OrderStatus) {
			isOrderCompleted = true
		}
		// 退款完成在创建 status
		if self.RefundStatus == share.REFUND_STATUS_REFUNDED && (old == nil || old.RefundStatus != self.RefundStatus) && !self.OrderCompleteTime.IsZero() {
			isCompletedOrderOccurRefund = true
		}
		if isOrderCompleted || isCompletedOrderOccurRefund {
			_, _ = client.MemberService.CreateTradeMemberLevelStatus(ctx, &trade_member.CreateTradeMemberLevelStatusRequest{
				MemberId:       self.MemberId.Hex(),
				OrderNo:        self.OrderId,
				OrderStatus:    self.OrderStatus,
				OrderTouchedAt: self.UpdatedAt.Format(time.RFC3339),
			})
		}
	}

	if old == nil {
		req := &component.UpdateRfmAnalysesStatusRequest{
			OrderSource: self.StoreId.Hex(),
		}
		component.Bigdata.UpdateRfmAnalysesStatus(ctx, req)
	}

	// 对外接口发事件逻辑在 api_order.go
	if self.SyncHistoryId.Hex() == "" {
		return
	}

	if self.DisableEvent {
		return
	}

	// 考虑到不同平台的订单发事件逻辑可能会不同，这里允许其单独处理发事件逻辑，单独处理逻辑在各平台订单实例的 AfterUpsert() 里
	// AfterUpsert 函数的执行是在当前的 AfterSave 之后，所以无论是哪个事件要单独处理都要考虑到原本发事件的顺序
	needHandleSeparatelyEvents := []string{}
	tradeStore := self.GetRelatedStore(ctx)
	if tradeStore.Provider == "jushita" && self.OrderFrom == share.ORDER_FROM_TMALL {
		needHandleSeparatelyEvents = append(needHandleSeparatelyEvents, "OrderCompleteTime")
	}
	if tradeStore.Provider == share.ORDER_PROVIDER_MEITUAN_SHANGOU && self.OrderPlatform == share.ORDER_FROM_MEITUAN {
		needHandleSeparatelyEvents = append(needHandleSeparatelyEvents, "OrderCompleteTime")
	}
	if self.OrderPlatform == share.ORDER_FROM_DOUYINLIFE {
		needHandleSeparatelyEvents = append(needHandleSeparatelyEvents, "OrderCompleteTime")
	}
	if self.OrderPlatform == share.ORDER_FROM_WESHOP {
		needHandleSeparatelyEvents = append(needHandleSeparatelyEvents, "OrderCompleteTime")
	}

	// 零售,积分商城订单不发购买商品和服务及订单完成事件
	notSendEventLog := []string{
		share.ORDER_FROM_RETAIL,
		share.ORDER_FROM_MICROMALL,
	}
	hasPlatformSentSameEvent := util.StrInArray(self.OrderFrom, &notSendEventLog)
	if !hasPlatformSentSameEvent && !util.StrInArray("OrderPayTime", &needHandleSeparatelyEvents) && self.IsNeedSendMemberEvent(old, "OrderPayTime") {
		sentPurchaseEvent = true
		self.SendPurchaseEvent(ctx)
		self.SendPurchaseProductEvent(ctx)
	}

	// 私域商城订单已发送订单发货事件，无需在这里再次发送
	if self.OrderFrom != share.ORDER_FROM_RETAIL && !util.StrInArray("OrderConsignTime", &needHandleSeparatelyEvents) && self.IsNeedSendMemberEvent(old, "OrderConsignTime") {
		self.SendOrderShipEvent(ctx)
	}

	if !hasPlatformSentSameEvent && !util.StrInArray("OrderCompleteTime", &needHandleSeparatelyEvents) && self.IsNeedSendMemberEvent(old, "OrderCompleteTime") {
		self.SendOrderCompleteEvent(ctx)
	}
	return
}

// 根据支付金额或者退款金额更新 member 积分
// 如果是退款操作，realAmount 需要传负数，对应给用户减积分
func UpdateMemberScore(ctx context.Context, memberId, storeId bson.ObjectId, realAmount float64, uniqueId string) {
	tradeStore := store.CTradeStore.GetByCondition(ctx, bson.M{"_id": storeId, "isDeleted": false})
	channel, err := share.GetChannel(ctx, tradeStore.Sid)
	if err != nil || !channel.EnableSyncScore {
		return
	}
	var member *member.MemberDetailResponse
	member, _ = share.GetMemberById(ctx, memberId, []string{}, "")
	if member == nil {
		// 开通了会员通,创建下单人时查到了会员通创建的客户,创建收货人时,由于手机号加密后和会员通用户的 openId 相同,将会员通创建的用户 merge 了,导致订单上的 memberId 对应的
		// 客户被删除,没法加积分,从历史记录中查出 mainMemberId 加积分, 订单上的 memberId 会在 mergeMember 中更新
		condition := bson.M{
			"accountId":      util.GetAccountIdAsObjectId(ctx),
			"mergeMemberIds": []bson.ObjectId{memberId},
		}
		mergeHistory := model.MemberMergeHistory{}
		err := extension.DBRepository.FindOne(ctx, model.C_MEMBERMERGEHISTORY, condition, &mergeHistory)
		if err != nil || mergeHistory.Id.Hex() == "" {
			log.Warn(ctx, "Member and mergeHistory not found", log.Fields{
				"accountId": util.GetAccountId(ctx),
				"storeId":   storeId.Hex(),
				"memberId":  memberId.Hex(),
			})
			return
		}
		log.Warn(ctx, "Member not found, member mergeHistory exists", log.Fields{
			"accountId": util.GetAccountId(ctx),
			"storeId":   storeId.Hex(),
			"memberId":  memberId.Hex(),
		})
		member, _ = share.GetMemberById(ctx, mergeHistory.MainMemberId, []string{}, "")
	}

	if !IsTaobaoMember(member) {
		return
	}

	// TODO updateMemberScoreByRule upgradeMemberLevelByBuy 加积分
}

func IsTaobaoMember(member *member.MemberDetailResponse) bool {
	if member.OriginFrom != nil {
		if member.OriginFrom.Origin == "taobao:member" && member.OriginFrom.Subscribed {
			return true
		}
	}

	for _, social := range member.Socials {
		if social.Origin == "taobao:member" && social.Subscribed {
			return true
		}
	}

	return false
}

// 根据某一字段是否首次出现或发生变更从而判断是否需要发事件，仅限可以使用 == 比较的数据类型、bool 类型使用该方法没有意义
func (self *Trade) IsNeedSendMemberEvent(old *Trade, field string) bool {
	if self.MemberId.Hex() == "" {
		return false
	}

	newValue := reflect.ValueOf(*self).FieldByName(field)
	if newValue.IsZero() {
		return false
	}

	if old == nil {
		return true
	} else {
		oldValue := reflect.ValueOf(*old).FieldByName(field)
		if oldValue.Interface() != newValue.Interface() {
			return true
		}
	}

	return false
}

func (self *Trade) GetRelatedStore(ctx context.Context) *store.TradeStore {
	return store.CTradeStore.GetByCondition(ctx, bson.M{"_id": self.StoreId, "isDeleted": false})
}

func (self *Trade) SendPurchaseEvent(ctx context.Context) {
	if self.OrderPayTime.IsZero() {
		return
	}

	goodMsg, goodsList := self.GetGoodMsgAndList()
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_PURCHASE_PRODUCT_SERVICE + ":" + self.OrderId,
		AccountId:  self.AccountId.Hex(),
		MemberId:   self.MemberId.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_PURCHASE_PRODUCT_SERVICE,
		CreateTime: self.OrderPayTime.UnixNano() / 1e6,
		ChannelId:  self.GetChannelId(ctx),
		EventProperties: map[string]interface{}{
			"tradeId":         self.Id.Hex(),
			"orderId":         self.OrderId,
			"platform":        self.OrderPlatform,
			"storeId":         self.StoreId.Hex(),
			"storeCode":       self.StoreCode,
			"storeName":       self.StoreName,
			"productKinds":    len(self.GoodsDetail),
			"productCounts":   goodMsg["counts"],
			"actualAmount":    share.Yuan2Fen(self.ActualAmount - self.PostalAmount),
			"goodsAmount":     share.Yuan2Fen(self.GoodsAmount),
			"discountAmount":  share.Yuan2Fen(self.DiscountAmount),
			"postalAmount":    share.Yuan2Fen(self.PostalAmount),
			"orderAmount":     share.Yuan2Fen(self.ActualAmount),
			"unitPrice":       goodMsg["unitPrice"],
			"orderCreateTime": self.OrderCreateTime.UnixNano() / 1e6,
			"goodsList":       goodsList,
			"id":              self.Id.Hex(),
			"number":          self.OrderId,
			"properties":      self.Properties,
		},
	}
	if self.CurrentLevel != 0 {
		eventBody.EventProperties["memberLevel"] = self.CurrentLevel
	}

	extraFields := self.ExtraFields
	if extraFields != nil {
		if extraFieldsMap, ok := extraFields.(primitive.M); ok {
			if extraFieldsMap["eventProperties"] != nil {
				eventProperties := extraFieldsMap["eventProperties"]
				if eventPropertiesMap, ok := eventProperties.(primitive.M); ok {
					for key, value := range eventPropertiesMap {
						eventBody.EventProperties[key] = value
					}
				}
			}
		}
	}

	eventBody.SendCustomerEvent(ctx)
}

func (self *Trade) GetGoodMsgAndList() (map[string]interface{}, []map[string]interface{}) {
	counts := uint64(0)
	goodsPayAmount := uint64(0)
	goodList := []map[string]interface{}{}
	for _, detail := range self.GoodsDetail {
		counts += detail.Count
		goodsPayAmount += share.Yuan2Fen(detail.SubtotalAmount)
		goodList = append(goodList, map[string]interface{}{
			"productName":   detail.Name,
			"productNumber": detail.GoodsNumber,
			"productSku":    detail.Sku,
			"price":         share.Yuan2Fen(detail.Price),
			"count":         detail.Count,
			"specs":         detail.PropertiesName,
			"category":      detail.Category,
			"payAmount":     share.Yuan2Fen(detail.SubtotalAmount),
			"properties":    detail.Properties,
		})
	}

	var unitPrice uint64
	unitPrice = 0
	if counts != 0 {
		unitPrice = share.Yuan2Fen(self.ActualAmount / float64(counts))
	}

	return map[string]interface{}{
		"counts":         counts,
		"unitPrice":      unitPrice,
		"goodsPayAmount": goodsPayAmount,
	}, goodList
}

func (self *Trade) SendPurchaseProductEvent(ctx context.Context) {
	if self.OrderPayTime.IsZero() {
		return
	}
	if len(self.GoodsDetail) == 0 {
		return
	}

	tradeStore := self.GetRelatedStore(ctx)
	channelId := self.GetChannelId(ctx)
	if channelId == "" {
		channelId = tradeStore.Sid
	}

	for _, detail := range self.GoodsDetail {
		existingSku := detail.OutSkuId
		if detail.Sku != "" {
			existingSku = detail.Sku
		}

		eventBody := component.CustomerEventBody{
			Id:         component.MAIEVENT_PURCHASE_PRODUCT + ":" + self.OrderId + ":" + detail.GoodsNumber + ":" + existingSku,
			AccountId:  self.AccountId.Hex(),
			MemberId:   self.MemberId.Hex(),
			ChannelId:  channelId,
			MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
			SubType:    component.MAIEVENT_PURCHASE_PRODUCT,
			CreateTime: self.OrderPayTime.UnixNano() / 1e6,
			EventProperties: map[string]interface{}{
				"id":                 self.Id.Hex(),
				"number":             self.OrderId,
				"orderId":            self.OrderId,
				"orderCreateTime":    self.OrderCreateTime.UnixNano() / 1e6,
				"platform":           self.OrderPlatform,
				"memberId":           self.MemberId.Hex(),
				"storeId":            self.StoreId.Hex(),
				"storeCode":          self.StoreCode,
				"storeName":          self.StoreName,
				"productName":        detail.Name,
				"productNumber":      detail.GoodsNumber,
				"productSku":         detail.Sku,
				"productExternalSku": detail.OutSkuId,
				"price":              share.Yuan2Fen(detail.Price),
				"payAmount":          share.Yuan2Fen(detail.SubtotalAmount),
				"count":              detail.Count,
				"categoryId":         detail.Category,
				"afterSaleStatus":    detail.AfterSaleStatus,
				"outTradeId":         fmt.Sprintf("%s_%s_%s", self.OrderId, detail.GoodsNumber, detail.Sku), // 商品唯一标识
				"properties":         detail.Properties,
			},
		}
		if tradeStore.Provider == "jushita" {
			eventBody.Id = eventBody.Id + ":" + detail.Oid
		}
		if self.CurrentLevel != 0 {
			eventBody.EventProperties["memberLevel"] = self.CurrentLevel
		}

		eventBody.SendCustomerEvent(ctx)
	}
}

func (self *Trade) SendOrderShipEvent(ctx context.Context) {
	if self.OrderConsignTime.IsZero() {
		return
	}

	eventBody := component.CustomerEventBody{
		Id:         fmt.Sprintf("%s:%s:%s", component.MAIEVENT_ORDER_SHIP, self.OrderId, self.Delivery.ExpressNo),
		AccountId:  self.AccountId.Hex(),
		MemberId:   self.MemberId.Hex(),
		ChannelId:  self.GetChannelId(ctx),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_ORDER_SHIP,
		CreateTime: self.OrderConsignTime.UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"memberId":     self.MemberId.Hex(),
			"id":           self.Id.Hex(),
			"number":       self.OrderId,
			"platform":     self.OrderPlatform,
			"storeId":      self.StoreId.Hex(),
			"storeCode":    self.StoreCode,
			"storeName":    self.StoreName,
			"deliveryName": self.Delivery.ExpressCompany,
			"waybillId":    self.Delivery.ExpressNo,
			"shippedAt":    self.OrderConsignTime.Unix(),
			"properties":   self.Properties,
		},
	}
	if self.CurrentLevel != 0 {
		eventBody.EventProperties["memberLevel"] = self.CurrentLevel
	}

	eventBody.SendCustomerEvent(ctx)
}

func (self *Trade) SendOrderCompleteEvent(ctx context.Context) {
	if self.OrderCompleteTime.IsZero() {
		return
	}
	_, goodsList := self.GetGoodMsgAndList()
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_ORDER_COMPLETED + ":" + self.OrderId,
		AccountId:  self.AccountId.Hex(),
		MemberId:   self.MemberId.Hex(),
		ChannelId:  self.GetChannelId(ctx),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_ORDER_COMPLETED,
		CreateTime: self.OrderCompleteTime.UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"memberId":        self.MemberId.Hex(),
			"id":              self.Id.Hex(),
			"number":          self.OrderId,
			"orderId":         self.OrderId,
			"platform":        self.OrderPlatform,
			"storeId":         self.StoreId.Hex(),
			"storeCode":       self.StoreCode,
			"storeName":       self.StoreName,
			"goodsAmount":     share.Yuan2Fen(self.GoodsAmount),
			"logisticsFee":    share.Yuan2Fen(self.PostalAmount),
			"completedAt":     self.OrderCompleteTime.UnixNano() / 1e6,
			"orderCreateTime": self.OrderCreateTime.UnixNano() / 1e6,
			"goodsList":       goodsList,
			"properties":      self.Properties,
		},
	}
	if self.CurrentLevel != 0 {
		eventBody.EventProperties["memberLevel"] = self.CurrentLevel
	}
	eventBody.SendCustomerEvent(ctx)
}

func (self *Trade) SendOrderBindEvent(ctx context.Context) {
	if self.MemberId.IsZero() {
		return
	}

	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_ORDER_BOUND + ":" + self.OrderId,
		AccountId:  self.AccountId.Hex(),
		MemberId:   self.MemberId.Hex(),
		ChannelId:  self.GetChannelId(ctx),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_ORDER_BOUND,
		CreateTime: time.Now().UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"id":      self.Id.Hex(),
			"number":  self.OrderId,
			"orderId": self.OrderId,
		},
	}

	eventBody.SendCustomerEvent(ctx)
}

func (self *Trade) UpsertTrade(ctx context.Context, sourceOrder BaseOrder, store *store.TradeStore, syncHistoryId *bson.ObjectId) (*Trade, error) {
	if err := CTradeSource.Upsert(ctx, sourceOrder); err != nil {
		return nil, err
	}

	condition := sourceOrder.GetCondition()
	trade := self.GetByCondition(ctx, condition)
	isInsert := false
	if trade == nil {
		isInsert = true
		trade = &Trade{}
	}
	trade.StoreId = store.Id
	trade.StoreCode = store.StoreCode
	if trade.StoreCode == "" {
		trade.StoreCode = store.Sid
	}
	trade.StoreName = store.Name
	if syncHistoryId != nil && syncHistoryId.Hex() != "" {
		trade.SyncHistoryId = *syncHistoryId
	}
	trade.OrderId = sourceOrder.GetOrderId()
	trade.OrderFrom = sourceOrder.GetOrderFrom()
	trade.OrderPlatform = sourceOrder.GetOrderPlatform()
	if sourceOrder.GetChannelId() != "" {
		trade.ChannelId = sourceOrder.GetChannelId()
	}

	var timeResult *time.Time
	var err error
	if sourceOrder.GetOrderCreateTime() != "" {
		timeResult, err = GetTime(sourceOrder.GetOrderCreateTime(), sourceOrder.GetTimeLayout("orderCreateTime"))
		if err != nil {
			return nil, err
		}
		trade.OrderCreateTime = *timeResult
	}

	if sourceOrder.GetOrderUpdateTime() != "" {
		timeResult, err = GetTime(sourceOrder.GetOrderUpdateTime(), sourceOrder.GetTimeLayout("orderUpdateTime"))
		if err != nil {
			return nil, err
		}
		trade.OrderUpdateTime = *timeResult
	}

	if sourceOrder.GetOrderConsignTime() != "" {
		timeResult, err = GetTime(sourceOrder.GetOrderConsignTime(), sourceOrder.GetTimeLayout("orderConsignTime"))
		if err != nil {
			return nil, err
		}
		trade.OrderConsignTime = *timeResult
	}

	if sourceOrder.GetOrderCompleteTime() != "" {
		timeResult, err = GetTime(sourceOrder.GetOrderCompleteTime(), sourceOrder.GetTimeLayout("orderCompleteTime"))
		if err != nil {
			return nil, err
		}
		trade.OrderCompleteTime = *timeResult
	}

	if sourceOrder.GetOrderPayTime() != "" {
		timeResult, err = GetTime(sourceOrder.GetOrderPayTime(), sourceOrder.GetTimeLayout("orderPayTime"))
		if err != nil {
			return nil, err
		}
		trade.OrderPayTime = *timeResult
	}

	trade.OrderStatus = sourceOrder.GetOrderStatus()
	trade.GoodsAmount = sourceOrder.GetGoodsAmount()   // 商品总金额,不包含邮费
	trade.PostalAmount = sourceOrder.GetPostalAmount() // 邮费
	trade.ActualAmount = sourceOrder.GetActualAmount() // 实付金额,包含邮费

	discountAmount := sourceOrder.GetDiscountAmount()
	if share.Yuan2Fen(discountAmount) > 0 {
		trade.DiscountAmount = discountAmount
	}

	trade.PaymentType = sourceOrder.GetPaymentType()
	trade.BuyerId = sourceOrder.GetBuyerId()
	trade.BuyerNickname = sourceOrder.GetBuyerNickname()
	trade.GoodsDetail = *sourceOrder.GetGoodsDetail()

	member, upsertMemberErr := sourceOrder.UpsertMember()
	if upsertMemberErr != nil {
		return nil, upsertMemberErr
	}
	if member != nil && member.Id != "" {
		trade.MemberId = bson.ObjectIdHex(member.Id)
		if isInsert && member.Level != 0 {
			trade.CurrentLevel = uint64(member.Level)
		}
	}

	receiver, getReceiverErr := sourceOrder.GetReceiver()
	if getReceiverErr != nil {
		return nil, getReceiverErr
	}
	if receiver != nil {
		trade.Receiver = *receiver
	}

	delivery, getDeliveryErr := sourceOrder.GetDelivery()
	if getDeliveryErr != nil {
		return nil, getDeliveryErr
	}
	if delivery != nil {
		trade.Delivery = *delivery
	}

	trade.CloseReason = sourceOrder.GetCloseReason()
	trade.DealCode = sourceOrder.GetDealCode()
	trade.OutTradeNo = sourceOrder.GetOutTradeNo()

	coupons, getCouponsErr := sourceOrder.GetCoupons()
	if getCouponsErr != nil {
		return nil, getCouponsErr
	}
	if coupons != nil {
		trade.Coupons = *coupons
	}

	utm, getUtmErr := sourceOrder.GetUtm()
	if getUtmErr != nil {
		return nil, getUtmErr
	}
	if utm != nil {
		trade.Utm = *utm
	}

	if sourceOrder.GetRefundStatus() != "" {
		trade.RefundStatus = sourceOrder.GetRefundStatus()
	}

	trade.BuyerMessage = sourceOrder.GetBuyerMessage()
	trade.SellerMemo = sourceOrder.GetSellerMemo()
	trade.DisableEvent = sourceOrder.IsVirtual() // 虚拟订单不需要发事件

	extraFields, getExtraFieldsErr := sourceOrder.GetExtraFields()
	if getExtraFieldsErr != nil {
		return nil, getExtraFieldsErr
	}
	if extraFields != nil {
		trade.ExtraFields = extraFields
	}
	staffId := sourceOrder.GetStaffId()
	if staffId != bson.NilObjectId {
		trade.StaffId = staffId
	}
	if sourceOrder.GetStaffCode() != "" {
		trade.StaffCode = sourceOrder.GetStaffCode()
	}

	trade, err = sourceOrder.UpsertExtraTrade(trade)
	if err != nil {
		return nil, err
	}

	if isInsert {
		if err = trade.Create(ctx); err != nil {
			return nil, err
		}
	} else {
		if err = trade.Update(ctx); err != nil {
			return nil, err
		}
	}

	result := CTrade.GetByCondition(ctx, bson.M{"orderId": trade.OrderId})
	result.sentPurchaseEvent = trade.sentPurchaseEvent
	return result, nil
}

func GetTime(timeStr string, layout string) (*time.Time, error) {
	var t time.Time
	var err error
	t, err = time.ParseInLocation(layout, timeStr, time.Local)
	if err == nil && !t.IsZero() {
		return &t, nil
	}

	return nil, err
}

func (self *Trade) DeleteByOrderId(ctx context.Context, orderId string) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["orderId"] = orderId
	updater := bson.M{
		"$set": bson.M{
			"isDeleted": true,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_TRADE, selector, updater)
}

// update 时会合并数据库已存在的 goodsDetail，需要保证业务无关但逻辑相关的字段保持一致
func (self *Trade) MergeGoodsDetail(newGoodsDetail *[]GoodsDetail) *[]GoodsDetail {
	oldMapping := generateGoodsDetailMapping(&self.GoodsDetail)
	newMapping := generateGoodsDetailMapping(newGoodsDetail)
	mergedMapping := oldMapping
	for key, value := range newMapping {
		if oldGoodDetail, ok := oldMapping[key]; ok {
			if oldGoodDetail.HasSendEvent {
				value.HasSendEvent = true
			}
		}
		mergedMapping[key] = value
	}

	mergedGoodsDetail := []GoodsDetail{}
	for _, value := range mergedMapping {
		mergedGoodsDetail = append(mergedGoodsDetail, value)
	}
	return &mergedGoodsDetail
}

func generateGoodsDetailMapping(goodsDetail *[]GoodsDetail) map[string]GoodsDetail {
	goodsDatailMap := map[string]GoodsDetail{}
	for _, goodDetail := range *goodsDetail {
		uniqueKey := fmt.Sprintf("%s_%s_%s", goodDetail.GoodsNumber, goodDetail.Sku, goodDetail.Oid)
		goodsDatailMap[uniqueKey] = goodDetail
	}

	return goodsDatailMap
}

func (self *Trade) GetAllByPaginationWithoutCount(ctx context.Context, condition bson.M, page uint32, pageSize uint32, orderbys []string) *[]Trade {
	var trades []Trade
	sortFields := util.NormalizeOrderBy(orderbys)
	pageCond := extension.PagingCondition{
		Selector:  condition,
		PageIndex: int(page),
		PageSize:  int(pageSize),
		Sortor:    sortFields,
	}

	extension.DBRepository.FindByPaginationWithoutCount(ctx, C_TRADE, pageCond, &trades)

	return &trades
}

func UpdateMemberLatestPayTime(ctx context.Context, tradeId string) error {
	if tradeId == "" {
		return nil
	}
	trade := &Trade{}
	selector := Common.GenDefaultCondition(ctx)
	if bson.IsObjectIdHex(tradeId) {
		selector["_id"] = bson.ObjectIdHex(tradeId)
	}
	err := extension.DBRepository.FindOneWithSortor(ctx, C_TRADE, selector, []string{"-orderPayTime"}, trade)
	if err != nil {
		return err
	}
	if trade.OrderPayTime.IsZero() || trade.MemberId.IsZero() {
		return nil
	}
	waitDeadline := time.Now().Add(time.Second * 10)
	key := fmt.Sprintf(UPDATE_TRADE_MEMBER_PROPERTY_LOCK_TEMPLATE, util.GetAccountId(ctx), trade.MemberId.Hex())
	// 自旋等待，每 50 毫秒获取一次锁，最多等待 10s。
	for {
		ok, _ := extension.RedisClient.SetNX(key, "1", UPDATE_TRADE_MEMBER_PROPERTY_LOCK_DURATION)
		if ok {
			break
		}
		if time.Now().After(waitDeadline) {
			return errors.NewTooManyRequestsError("memberId")
		}
		time.Sleep(time.Millisecond * 50)
	}
	defer extension.RedisClient.Del(key)
	memberDetail, err := share.GetMemberById(ctx, trade.MemberId, []string{"Properties"}, "")
	if err != nil {
		return errors.NewNotExistsErrorWithMessage("memberId", err.Error())
	}

	var (
		basicProperties        []*member.PropertyInfo
		lastPaidStoreProperty  *member.PropertyInfo
		firstPaidStoreProperty *member.PropertyInfo
		updatedProperties      []*member.PropertyInfo
	)

	wg := sync.WaitGroup{}
	wg.Add(3)
	core_component.GO(ctx, func(ctx context.Context) {
		defer wg.Done()
		basicProperties = trade.IncTradePropertiesValue(ctx, memberDetail)
	})
	core_component.GO(ctx, func(ctx context.Context) {
		defer wg.Done()
		lastPaidStoreProperty = getLastPaidStoreProperty(ctx, memberDetail)
	})
	core_component.GO(ctx, func(ctx context.Context) {
		defer wg.Done()
		firstPaidStoreProperty = getFirstPaidStoreProperty(ctx, memberDetail)
	})
	wg.Wait()
	if firstPaidStoreProperty != nil {
		updatedProperties = append(updatedProperties, firstPaidStoreProperty)
	}
	if lastPaidStoreProperty != nil {
		updatedProperties = append(updatedProperties, lastPaidStoreProperty)
	}
	if len(basicProperties) > 0 {
		updatedProperties = append(updatedProperties, basicProperties...)
	}
	if len(updatedProperties) == 0 {
		return nil
	}

	return share.UpdateMemberProperty(ctx, &member.BatchUpdateMemberPropertyRequest{
		MemberIds:  []string{trade.MemberId.Hex()},
		Properties: updatedProperties,
	})
}

func getLastPaidStoreProperty(ctx context.Context, m *member.MemberDetailResponse) *member.PropertyInfo {
	condition := Common.GenDefaultCondition(ctx)
	condition["memberId"] = bson.ObjectIdHex(m.Id)
	condition["orderPayTime"] = bson.M{
		"$gt": time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local),
	}
	trade := Trade{}
	err := extension.DBRepository.FindOneWithSortor(ctx, C_TRADE, condition, []string{"-orderPayTime"}, &trade)
	if err != nil {
		return nil
	}
	return trade.formatProperty(ctx, LAST_PAID_STORE, m)
}

func getFirstPaidStoreProperty(ctx context.Context, m *member.MemberDetailResponse) *member.PropertyInfo {
	condition := Common.GenDefaultCondition(ctx)
	condition["memberId"] = bson.ObjectIdHex(m.Id)
	condition["orderPayTime"] = bson.M{
		"$gt": time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local),
	}
	trade := Trade{}
	err := extension.DBRepository.FindOneWithSortor(ctx, C_TRADE, condition, []string{"orderPayTime"}, &trade)
	if err != nil {
		return nil
	}
	return trade.formatProperty(ctx, FIRST_PAID_STORE, m)
}

func (t *Trade) IncTradePropertiesValue(ctx context.Context, m *member.MemberDetailResponse) []*member.PropertyInfo {
	var (
		properties                 []*member.PropertyInfo
		newTradeBuys               float64 = 0
		newTradeTotalConsumeAmount float64 = 0
	)
	needIncPropertyIds := []string{
		LAST_PAY_TIME,
		TRADE_BUYS,
		TRADE_TOTAL_CONSUME_AMOUNT,
	}
	for _, propertyId := range needIncPropertyIds {
		if property := t.formatProperty(ctx, propertyId, m); property != nil {
			if propertyId == TRADE_BUYS {
				newTradeBuys = property.GetValueNumber().GetValue()
			} else if propertyId == TRADE_TOTAL_CONSUME_AMOUNT {
				newTradeTotalConsumeAmount = property.GetValueNumber().GetValue()
			}
			properties = append(properties, property)
		}
	}
	if newTradeBuys > 0 {
		properties = append(properties, &member.PropertyInfo{
			PropertyId: TRADE_AVERAGE_AMOUNT,
			Value: &member.PropertyInfo_ValueNumber{
				ValueNumber: &member.PropertyNumberValue{
					Value: math.Round(newTradeTotalConsumeAmount / newTradeBuys),
				},
			},
		})
	}
	return properties
}

func (t *Trade) formatProperty(ctx context.Context, propertyId string, m *member.MemberDetailResponse) *member.PropertyInfo {
	var (
		memberLastPayTime             int64   = 0
		memberTradeBuys               float64 = 0
		memberTradeTotalConsumeAmount float64 = 0
		memberFirstPaidStore                  = ""
		memberLastPaidStore                   = ""
	)
	for _, property := range m.Properties {
		switch property.Property.PropertyId {
		case LAST_PAY_TIME:
			memberLastPayTime = property.GetValueDate().GetValue() / 1000
		case TRADE_BUYS:
			memberTradeBuys = property.GetValueNumber().GetValue()
		case TRADE_TOTAL_CONSUME_AMOUNT:
			memberTradeTotalConsumeAmount = property.GetValueNumber().GetValue()
		case FIRST_PAID_STORE:
			memberFirstPaidStore = property.GetValueString().GetValue()
		case LAST_PAID_STORE:
			memberLastPaidStore = property.GetValueString().GetValue()
		}
	}
	switch propertyId {
	case LAST_PAY_TIME:
		// 如果客户当前的最近支付时间较晚，不更新
		if !time.Unix(memberLastPayTime, 0).Before(t.OrderPayTime) {
			return nil
		}
		return &member.PropertyInfo{
			PropertyId: propertyId,
			Value: &member.PropertyInfo_ValueDate{
				ValueDate: &member.PropertyDateValue{
					Value: t.OrderPayTime.Unix(),
				},
			},
		}
	case FIRST_PAID_STORE:
		// 微商城不处理
		if t.OrderPlatform == PLATFORM_MICROMALL {
			return nil
		}
		id := t.TransTradeStoreIdToDmsStoreId(ctx)
		if id == "" || !bson.IsObjectIdHex(id) || id == memberFirstPaidStore {
			return nil
		}
		return &member.PropertyInfo{
			PropertyId: propertyId,
			Value: &member.PropertyInfo_ValueString{
				ValueString: &member.PropertyStringValue{
					Value: id,
				},
			},
		}
	case LAST_PAID_STORE:
		// 微商城不处理
		if t.OrderPlatform == PLATFORM_MICROMALL {
			return nil
		}
		id := t.TransTradeStoreIdToDmsStoreId(ctx)
		if id == "" || !bson.IsObjectIdHex(id) || id == memberLastPaidStore {
			return nil
		}
		return &member.PropertyInfo{
			PropertyId: propertyId,
			Value: &member.PropertyInfo_ValueString{
				ValueString: &member.PropertyStringValue{
					Value: id,
				},
			},
		}
	case TRADE_BUYS:
		return &member.PropertyInfo{
			PropertyId: propertyId,
			Value: &member.PropertyInfo_ValueNumber{
				ValueNumber: &member.PropertyNumberValue{
					Value: memberTradeBuys + 1,
				},
			},
		}
	case TRADE_TOTAL_CONSUME_AMOUNT:
		return &member.PropertyInfo{
			PropertyId: propertyId,
			Value: &member.PropertyInfo_ValueNumber{
				ValueNumber: &member.PropertyNumberValue{
					Value: memberTradeTotalConsumeAmount + float64(share.Yuan2Fen(t.ActualAmount)),
				},
			},
		}
	}
	return nil
}

func (t *Trade) TransTradeStoreIdToDmsStoreId(ctx context.Context) string {
	resp, err := proto_client.GetTradeStoreServiceClient().SearchStores(ctx, &trade_store.SearchStoresRequest{
		Ids: []string{t.StoreId.Hex()},
	})
	if err != nil || len(resp.Items) == 0 {
		return ""
	}
	tradeStore := resp.Items[0]
	if tradeStore.Platform == "mai-retail" {
		return tradeStore.Sid
	} else if tradeStore.Platform == "micromall" {
		return ""
	}
	storeDetail, err := proto_client.GetStoreServiceClient().GetStore(ctx, &pb_store.GetStoreRequest{
		Codes: []string{
			tradeStore.Sid,
			tradeStore.StoreCode,
		},
	})
	if err != nil {
		return ""
	}
	return storeDetail.Id
}

func (*Trade) UpdateById(ctx context.Context, id bson.ObjectId, updater bson.M) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = id
	return extension.DBRepository.UpdateOne(ctx, C_TRADE, selector, updater)
}

func (*Trade) Iterate(ctx context.Context, selector bson.M, sorter []string) (extension.IterWrapper, error) {
	it, err := extension.DBRepository.Iterate(ctx, C_TRADE, selector, sorter)
	return it, err
}

func (*Trade) IteratePaidTradesByMemberId(ctx context.Context, memberId bson.ObjectId, sorter []string, op func(docs []Trade)) error {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"memberId":  memberId,
		"orderPayTime": bson.M{
			"$gte": time.Date(2000, 1, 1, 0, 0, 0, 0, time.Local),
		},
	}
	cursor, err := extension.DBRepository.IterateWithOption(ctx, C_TRADE, condition, extension.IterateOption{
		Sortor:    sorter,
		BatchSize: 500,
	})
	if err != nil {
		return err
	}
	var (
		trade  Trade
		trades []Trade
	)
	for cursor.Next(&trade) {
		trades = append(trades, trade)
		if len(trades) >= 500 {
			op(trades)
			trades = []Trade{}
		}
	}
	if len(trades) > 0 {
		op(trades)
	}
	return nil
}

// 当前仅用于发送事件时获取渠道 id，包含特殊处理，其他场景视情况使用或者添加新的方法
func (self *Trade) GetChannelId(ctx context.Context) string {
	channelId := self.ChannelId
	if channelId == "" {
		tradeStore := self.GetRelatedStore(ctx)
		channelId = tradeStore.Sid
	}
	if viper.GetString("env") == "smcp-production" && strings.HasPrefix(self.ChannelId, "offline_") && strings.ToLower(self.OrderPlatform) == "pos" {
		response, _ := proto_client.GetAccountServiceClient().GetChannel(ctx, &pb_account.ChannelDetailRequest{Origin: "pos"})
		if response != nil {
			return response.ChannelId
		}
	}
	return channelId
}

func (*Trade) GetLatestDistributionOrder(ctx context.Context) Trade {
	condition := Common.GenDefaultCondition(ctx)
	condition["orderFrom"] = ORDER_FROM_DISTRIBUTION_MODULE
	result := Trade{}
	extension.DBRepository.FindOneWithSortor(ctx, C_TRADE, condition, []string{"-orderCreateTime"}, &result)
	return result
}

func (*Trade) IterateByCondition(ctx context.Context, selecetor bson.M, sorter []string, op func(ctx context.Context, docs []Trade)) error {
	cursor, err := extension.DBRepository.IterateWithOption(ctx, C_TRADE, selecetor, extension.IterateOption{
		Sortor:    sorter,
		BatchSize: 500,
	})
	if err != nil {
		return err
	}
	var (
		trade  Trade
		trades []Trade
	)
	defer cursor.Close()
	for cursor.Next(&trade) {
		trades = append(trades, trade)
		if len(trades) >= 500 {
			op(ctx, trades)
			trades = []Trade{}
		}
	}
	if len(trades) > 0 {
		op(ctx, trades)
	}
	return nil
}

func (t *Trade) IsTmallVirtual() bool {
	if viper.GetString("env") != "dhc-production" {
		return false
	}
	return t.OrderPlatform == share.ORDER_FROM_TMALL && t.DisableEvent
}
