{"userDataPermissions": [{"parentName": "", "business": "messagepush", "menuName": "智能营销", "name": "marketo.*", "resource": "", "order": 1}, {"parentName": "marketo.*", "business": "messagepush", "menuName": "智能路径", "name": "marketo.list", "resource": "marketo", "types": ["allDistributors", "subDistributors", "directlyDistributors", "self"], "order": 2}], "userMenus": [{"parentKey": "", "business": "messagepush", "name": "marketing.*", "requiredPermissions": [], "frontendv1Name": "marketing.automation", "order": 1}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.calendar.view", "requiredPermissions": [], "order": 2}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.calendar.overview", "requiredPermissions": [], "order": 3}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.calendar.detail", "requiredPermissions": [], "order": 4}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.calendar.overview.event", "requiredPermissions": [], "order": 5}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.calendar.overview.rule", "requiredPermissions": [], "order": 6}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.calendar.overview.once", "requiredPermissions": [], "order": 7}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.automation", "requiredPermissions": [], "frontendv1Name": "marketing.automation", "order": 8}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.automation.create", "requiredPermissions": [], "order": 9}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.automation.edit", "requiredPermissions": [], "order": 10}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.automation.copy", "requiredPermissions": [], "order": 11}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.automation.delete", "requiredPermissions": [], "order": 12}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.automation.start", "requiredPermissions": [], "order": 13}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.automation.view.detail", "requiredPermissions": [], "order": 14}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.automation.view.customer.execute", "requiredPermissions": [], "order": 15}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.automation.view.node", "requiredPermissions": [], "order": 16}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.automation.view.customer.node", "requiredPermissions": [], "order": 17}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.automation.export.customer.node", "requiredPermissions": [], "order": 18}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.automation.export.customer.execute", "requiredPermissions": [], "order": 19}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.audit", "requiredPermissions": [], "order": 20}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.audit.submit", "requiredPermissions": [], "order": 21}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.audit.view.pending.detail", "requiredPermissions": [], "order": 22}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.audit.view.audited.detail", "requiredPermissions": [], "order": 23}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.audit.view.pending.list", "requiredPermissions": [], "order": 24}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.audit.view.audited.list", "requiredPermissions": [], "order": 25}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.audit.export", "requiredPermissions": [], "order": 26}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.auditSetting", "requiredPermissions": [], "order": 27}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.auditSetting.enable", "requiredPermissions": [], "order": 28}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.auditSetting.disable", "requiredPermissions": [], "order": 29}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.auditSetting.add", "requiredPermissions": [], "order": 30}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.auditSetting.remove", "requiredPermissions": [], "order": 31}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.materialManagement", "requiredPermissions": [], "order": 32}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.materialManagement.create", "requiredPermissions": [], "order": 33}, {"parentKey": "marketing.*", "business": "messagepush", "name": "marketing.materialManagement.edit", "requiredPermissions": [], "order": 34}, {"parentKey": "", "business": "messagepush", "name": "settings.*", "requiredPermissions": [], "frontendv1Name": "management.sender", "order": 35}, {"parentKey": "settings.*", "business": "messagepush", "name": "settings.email.view.list", "requiredPermissions": [], "frontendv1Name": "management.sender", "order": 36}, {"parentKey": "settings.*", "business": "messagepush", "name": "settings.email.create", "requiredPermissions": [], "order": 37}, {"parentKey": "settings.*", "business": "messagepush", "name": "settings.email.edit", "requiredPermissions": [], "order": 38}, {"parentKey": "settings.*", "business": "messagepush", "name": "settings.email.delete", "requiredPermissions": [], "order": 39}, {"parentKey": "settings.*", "business": "messagepush", "name": "settings.notificationLimit", "requiredPermissions": [], "order": 40}, {"parentKey": "", "business": "messagepush", "name": "member.group.push.*", "requiredPermissions": [], "order": 41, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.template", "requiredPermissions": [], "order": 42, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.create.template", "requiredPermissions": [], "order": 43, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.template.edit", "requiredPermissions": [], "order": 44, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.template.sendLog", "requiredPermissions": [], "order": 45, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.template.delete", "requiredPermissions": [], "order": 46, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.subscribeMessage", "requiredPermissions": [], "order": 47, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.create.subscribeMessage", "requiredPermissions": [], "order": 48, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.subscribeMessage.edit", "requiredPermissions": [], "order": 49, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.subscribeMessage.sendLog", "requiredPermissions": [], "order": 50, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.subscribeMessage.delete", "requiredPermissions": [], "order": 51, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.sms", "requiredPermissions": [], "order": 52, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.create.sms", "requiredPermissions": [], "order": 53, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.sms.edit", "requiredPermissions": [], "order": 54, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.sms.sendLog", "requiredPermissions": [], "order": 55, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.sms.delete", "requiredPermissions": [], "order": 56, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.dsms", "requiredPermissions": [], "order": 57, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.create.dsms", "requiredPermissions": [], "order": 58, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.dsms.edit", "requiredPermissions": [], "order": 59, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.dsms.sendLog", "requiredPermissions": [], "order": 60, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "member.group.push.*", "business": "messagepush", "name": "member.group.push.tasks.dsms.delete", "requiredPermissions": [], "order": 61, "disabledAt": "2025-04-25 15:00:00"}, {"parentKey": "", "business": "messagepush", "name": "marketingPlan.*", "requiredPermissions": [], "order": 80}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "marketingPlan.plan.list", "requiredPermissions": [], "order": 81}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "marketingPlan.plan.view", "requiredPermissions": [], "order": 82}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "marketingPlan.plan.export", "requiredPermissions": [], "order": 83}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "marketingPlan.plan.create", "requiredPermissions": [], "order": 84}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "marketingPlan.plan.edit", "requiredPermissions": [], "order": 85}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "marketingPlan.plan.publish", "requiredPermissions": [], "order": 86}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "marketingPlan.plan.copy", "requiredPermissions": [], "order": 87}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "marketingPlan.plan.delete", "requiredPermissions": [], "order": 88}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.templateMessage", "requiredPermissions": [], "order": 89}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.templateMessage.create", "requiredPermissions": [], "order": 90}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.templateMessage.preview", "requiredPermissions": [], "order": 91}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.templateMessage.edit", "requiredPermissions": [], "order": 92}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.templateMessage.delete", "requiredPermissions": [], "order": 93}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.subscribeMessage", "requiredPermissions": [], "order": 94}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.subscribeMessage.create", "requiredPermissions": [], "order": 95}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.subscribeMessage.preview", "requiredPermissions": [], "order": 96}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.subscribeMessage.edit", "requiredPermissions": [], "order": 97}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.subscribeMessage.delete", "requiredPermissions": [], "order": 98}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.sms", "requiredPermissions": [], "order": 99}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.sms.create", "requiredPermissions": [], "order": 100}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.sms.preview", "requiredPermissions": [], "order": 101}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.sms.edit", "requiredPermissions": [], "order": 102}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.sms.delete", "requiredPermissions": [], "order": 103}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.sms.marketing", "requiredPermissions": [], "order": 104}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.sms.notice", "requiredPermissions": [], "order": 105}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.dsms", "requiredPermissions": [], "order": 106}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.dsms.create", "requiredPermissions": [], "order": 107}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.dsms.preview", "requiredPermissions": [], "order": 108}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.dsms.edit", "requiredPermissions": [], "order": 109}, {"parentKey": "marketingPlan.*", "business": "messagepush", "name": "templates.dsms.delete", "requiredPermissions": [], "order": 110}]}