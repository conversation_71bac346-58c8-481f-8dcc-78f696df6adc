package model

import (
	"context"
	"errors"
	"fmt"
	mai_err "mairpc/core/errors"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	pb_account "mairpc/proto/account"
	pb_campaign "mairpc/proto/campaign"
	"mairpc/proto/client"
	pb_origin "mairpc/proto/common/origin"
	pb_member "mairpc/proto/member"
	coupon_codes "mairpc/service/coupon/codes"
	"mairpc/service/member/codes"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"
	"strings"
	"time"

	"github.com/spf13/cast"
)

type MarketingPlanWorkflowProcessorArgs struct {
	MemberId          bson.ObjectId
	MarketingPlanId   bson.ObjectId
	MarketingPlanName string
	Member            *pb_member.MemberDetailResponse
	MessageMap        map[string]*pb_campaign.MessagePushCustomerMessage
}

type MarketingPlanWorkflowProcessor interface {
	Process(ctx context.Context, args MarketingPlanWorkflowProcessorArgs) MarketingPlanWorkflowResult
	CheckResult(ctx context.Context, result MarketingPlanWorkflowResult) MarketingPlanWorkflowResult
}

type MarketingPlanWorkflowProcessorGroup struct {
	Processors       []MarketingPlanWorkflowProcessor
	PriorityStrategy string
	Type             string
}

func GetMarketingPlanWorkflowProcessorGroups(actionGroup component.MarketingPlanActionGroup, results []MarketingPlanWorkflowResult) []MarketingPlanWorkflowProcessorGroup {
	var (
		groups    []MarketingPlanWorkflowProcessorGroup
		actionIds = core_util.ExtractArrayStringField("ActionId", results)
	)
	if len(actionGroup.MessageActions) > 0 {
		group := MarketingPlanWorkflowProcessorGroup{
			Processors:       []MarketingPlanWorkflowProcessor{},
			PriorityStrategy: actionGroup.PriorityStrategy,
			Type:             MARKETING_PLAN_ACTION_TYPE_MESSAGE,
		}
		for _, action := range actionGroup.MessageActions {
			if util.StrInArray(action.Id, &actionIds) {
				continue
			}
			group.Processors = append(group.Processors, &MessageActionProcessor{
				Action: action,
			})
		}
		// 如果当前动作组中已有动作被执行且是优先级模式，那么不再执行此组
		if (len(group.Processors) == len(actionGroup.MessageActions) || group.PriorityStrategy == PRIORITY_STRATEGY_PRIORITY) && len(group.Processors) > 0 {
			groups = append(groups, group)
		}
	}
	if len(actionGroup.DiscountActions) > 0 {
		group := MarketingPlanWorkflowProcessorGroup{
			Processors:       []MarketingPlanWorkflowProcessor{},
			PriorityStrategy: PRIORITY_STRATEGY_SAME,
			Type:             MARKETING_PLAN_ACTION_TYPE_DISCOUNT,
		}
		for _, action := range actionGroup.DiscountActions {
			if util.StrInArray(action.Id, &actionIds) {
				continue
			}
			group.Processors = append(group.Processors, &DiscountActionProcessor{
				Action: action,
			})
		}
		if len(group.Processors) > 0 {
			groups = append(groups, group)
		}
	}
	if len(actionGroup.WebhookActions) > 0 {
		group := MarketingPlanWorkflowProcessorGroup{
			Processors:       []MarketingPlanWorkflowProcessor{},
			PriorityStrategy: PRIORITY_STRATEGY_SAME,
			Type:             MARKETING_PLAN_ACTION_TYPE_WEBHOOK,
		}
		for _, action := range actionGroup.WebhookActions {
			if util.StrInArray(action.Id, &actionIds) {
				continue
			}
			group.Processors = append(group.Processors, &WebhookActionProcessor{
				Action: action,
			})
		}
		if len(group.Processors) > 0 {
			groups = append(groups, group)
		}
	}
	return groups
}

func GetMarketingPlanWorkflowProcessorByResult(result *MarketingPlanWorkflowResult) MarketingPlanWorkflowProcessor {
	if result == nil {
		return nil
	}
	switch result.ActionType {
	case MARKETING_PLAN_ACTION_TYPE_WEBHOOK:
		return &WebhookActionProcessor{}
	case MARKETING_PLAN_ACTION_TYPE_DISCOUNT:
		return &DiscountActionProcessor{}
	case MARKETING_PLAN_ACTION_TYPE_MESSAGE:
		return &MessageActionProcessor{}
	}
	return nil
}

type WebhookActionProcessor struct {
	Action component.MarketingPlanWebhookAction
}

func (p *WebhookActionProcessor) Process(ctx context.Context, args MarketingPlanWorkflowProcessorArgs) MarketingPlanWorkflowResult {
	data := map[string]any{
		"memberId":  args.MemberId.Hex(),
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"actionId":  p.Action.Id,
	}
	err := component.CallWebhook(ctx, p.Action.Url, data)
	result := MarketingPlanWorkflowResult{
		ActionId:   bson.ObjectIdHex(p.Action.Id),
		ActionType: MARKETING_PLAN_ACTION_TYPE_WEBHOOK,
		Status:     ACTION_RESULT_STATUS_SENT,
	}
	if err != nil {
		result.Result = formatErrorMessage(err)
		result.Status = ACTION_RESULT_STATUS_FAILED
	}
	return result
}

func (*WebhookActionProcessor) CheckResult(ctx context.Context, result MarketingPlanWorkflowResult) MarketingPlanWorkflowResult {
	return result
}

type DiscountActionProcessor struct {
	Action component.MarketingPlanDiscountAction
}

func (p *DiscountActionProcessor) Process(ctx context.Context, args MarketingPlanWorkflowProcessorArgs) MarketingPlanWorkflowResult {
	var (
		errMsg                []string
		membershipDiscountIds []string
	)
	switch p.Action.Type {
	case DISCOUNT_ACTION_TYPE_COUPON:
		for _, couponId := range p.Action.CouponIds {
			resp, err := issueCoupon(ctx, args.MemberId.Hex(), couponId, args.MarketingPlanId.Hex())
			if err != nil {
				errMsg = append(errMsg, formatErrorMessage(err))
				continue
			}
			membershipDiscountIds = append(membershipDiscountIds, resp.Id)
		}
	case DISCOUNT_ACTION_TYPE_SCORE:
		resp, err := UpdateMemberScore(ctx, args.MemberId.Hex(), p.Action.Score, fmt.Sprintf("营销计划-%s", args.MarketingPlanName), args.MarketingPlanId.Hex())
		if err != nil {
			errMsg = append(errMsg, formatErrorMessage(err))
		} else if len(resp.FailedItems) > 0 {
			errMsg = append(errMsg, fmt.Sprintf("Update member %s score failed", args.MemberId.Hex()))
		}
	}
	result := MarketingPlanWorkflowResult{
		ActionId:              bson.ObjectIdHex(p.Action.Id),
		ActionType:            MARKETING_PLAN_ACTION_TYPE_DISCOUNT,
		Status:                ACTION_RESULT_STATUS_SENT,
		MembershipDiscountIds: membershipDiscountIds,
	}
	if len(errMsg) > 0 {
		result.Result = strings.Join(errMsg, ",")
		result.Status = ACTION_RESULT_STATUS_FAILED
	}
	return result
}

func (*DiscountActionProcessor) CheckResult(ctx context.Context, result MarketingPlanWorkflowResult) MarketingPlanWorkflowResult {
	return result
}

type MessageActionProcessor struct {
	Action     component.MarketingPlanMessageAction
	TemplateId string
}

func (p *MessageActionProcessor) Process(ctx context.Context, args MarketingPlanWorkflowProcessorArgs) MarketingPlanWorkflowResult {
	result := MarketingPlanWorkflowResult{
		ActionId:   bson.ObjectIdHex(p.Action.Id),
		ActionType: MARKETING_PLAN_ACTION_TYPE_MESSAGE,
		Status:     ACTION_RESULT_STATUS_SENT,
	}
	message := args.MessageMap[p.Action.TemplateId]
	if message == nil {
		result.Status = ACTION_RESULT_STATUS_FAILED
		result.Result = WORKFLOW_LOG_ERROR_TEMPLATE_MESSAGE_DELETED
		return result
	}
	placeholderMap := genCustomerMessageMemberMap(args.Member, message)
	messageResultId, err := sendCustomerMessage(
		ctx,
		message,
		args.Member,
		placeholderMap,
		"marketingPlan",
		fmt.Sprintf("marketingPlan:%s:%s", args.MarketingPlanId.Hex(), p.Action.Id),
	)
	if err != nil {
		result.Result = formatErrorMessage(err)
		result.Status = ACTION_RESULT_STATUS_FAILED
	} else if messageResultId != "" {
		result.TemplateMessageResultId = messageResultId
		result.ResultDeadline = time.Now().Add(time.Minute * 5)
		result.Status = ACTION_RESULT_STATUS_CONFIRMING
	}
	return result
}

func (p *MessageActionProcessor) CheckResult(ctx context.Context, result MarketingPlanWorkflowResult) MarketingPlanWorkflowResult {
	if result.Status != ACTION_RESULT_STATUS_CONFIRMING || result.TemplateMessageResultId == "" {
		return result
	}
	resp, err := component.WeConnect.ListTemplateMessageResults(ctx, result.ChannelId, []string{result.TemplateMessageResultId})
	if err != nil {
		result.Status = ACTION_RESULT_STATUS_FAILED
		return result
	}
	switch resp[0].Status {
	case "SCHEDULED":
		result.Status = ACTION_RESULT_STATUS_CONFIRMING
	case "FINISHED":
		result.Status = ACTION_RESULT_STATUS_SENT
	default:
		result.Result = share_model.GenMessageNotificationLogDescription(share_model.MESSAGE_NOTIFICATION_LOG_TYPE_TEMPLATE_MESSAGE, resp[0].ErrorType)
		result.Status = ACTION_RESULT_STATUS_FAILED
	}
	return result
}

func genCustomerMessageMemberMap(member *pb_member.MemberDetailResponse, message *pb_campaign.MessagePushCustomerMessage) map[string]string {
	var (
		temp             = make(map[string]string)
		result           = make(map[string]string)
		memberMap        = make(map[string]interface{})
		validPropertyIds []string
	)
	if member == nil || message == nil {
		return result
	}
	for _, property := range member.Properties {
		temp[fmt.Sprintf("member.%s", property.Property.PropertyId)] = getMemberPropertyValueInString(property)
		temp[strings.ToLower(fmt.Sprintf("member%s", property.Property.PropertyId))] = getMemberPropertyValueInString(property)
	}
	temp[fmt.Sprintf("member.%s", TEMPLATE_MESSAGE_DATA_KEY_NICKNAME)] = getNickname(member)
	temp[strings.ToLower(fmt.Sprintf("member%s", TEMPLATE_MESSAGE_DATA_KEY_NICKNAME))] = getNickname(member)
	core_util.CopyByJson(member, &memberMap)
	for k, v := range memberMap {
		switch v.(type) {
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64, string:
			temp[fmt.Sprintf("member.%s", k)] = cast.ToString(v)
			temp[strings.ToLower(fmt.Sprintf("member%s", k))] = cast.ToString(v)
		}
	}
	for _, property := range message.Properties {
		validPropertyIds = append(validPropertyIds, property.PropertyId)
		val := temp[property.PropertyId]
		if val == "" {
			val = temp[strings.ToLower(property.PropertyId)]
		}
		if val == "" {
			temp[property.PropertyId] = property.DefaultValue
			continue
		}
		if len(property.OptionValueMap) > 0 && property.OptionValueMap[val] != "" {
			temp[property.PropertyId] = property.OptionValueMap[val]
			continue
		}
		temp[property.PropertyId] = formatStringWithDisplayRule(val, property.DisplayRule)
	}
	for k, v := range temp {
		if util.StrInArray(k, &validPropertyIds) {
			result[k] = v
		}
	}
	return result
}

func formatStringWithDisplayRule(s string, pbRule *pb_campaign.StringFillRule) string {
	if pbRule != nil {
		rule := share_model.StringFillRule{}
		core_util.CopyByJson(pbRule, &rule)
		return rule.Fill(s)
	}
	return s
}

func sendCustomerMessage(
	ctx context.Context,
	message *pb_campaign.MessagePushCustomerMessage,
	member *pb_member.MemberDetailResponse,
	placeholderMap map[string]string,
	business string,
	businessId string,
) (string, error) {
	switch message.Type {
	case "sms", "dsms":
		err, _, phone := checkBeforeSendingSms(
			ctx,
			member,
			CMarketingNotificationLimitSetting.GetByType(ctx, message.Type),
			fmt.Sprintf("%s:%s", businessId, member.Id),
			message.Type == "dsms",
		)
		if err != nil {
			return "", err
		}
		content := formatTemplateDataValue(message.Sms.Content, placeholderMap)
		messageType := pb_account.SendSmsRequest_MARKETING
		if message.Sms.Type != "" {
			if targetMessageType, ok := pb_account.SendSmsRequest_TemplateType_value[strings.ToUpper(message.Sms.Type)]; ok {
				messageType = pb_account.SendSmsRequest_TemplateType(targetMessageType)
			}
		}
		if message.Type == "sms" {
			err = SendSms(ctx, &pb_account.SendSmsRequest{
				Phone:        phone,
				Text:         content,
				BusinessType: business,
				BusinessId:   businessId,
				SmsTopic:     message.Sms.Topic,
				MessageType:  messageType,
			})
		} else {
			_, err = client.GetAccountServiceClient().SendDsms(ctx, &pb_account.SendDsmsRequest{
				TemplateCode:  message.Dsms.TemplateCode,
				Phone:         phone,
				TemplateParam: placeholderMap,
				Subject:       message.Dsms.Subject,
				BusinessId:    businessId,
				BusinessType:  business,
			})
		}
		return "", err
	case "templateMessage":
		info := getValidSocialByChannelId(member, message.Template.Channel.Id)
		if info == nil {
			return "", errors.New("Invalid social")
		}
		if !CMarketingMemberNotificationLimit.Use(
			ctx,
			bson.ObjectIdHex(member.Id),
			CMarketingNotificationLimitSetting.GetByType(ctx, NOTIFICATION_TYPE_TEMPLATE),
			NOTIFICATION_TYPE_TEMPLATE,
			fmt.Sprintf("%s:%s", businessId, member.Id),
		) {
			return "", NOTIFICATION_LIMIT_EXCEEDED_ERROR
		}
		req := &component.SingleTargetTplMessage{
			OriginId:        info.OpenId,
			TemplateMessage: generateMessagepushTemplateMessage(message, placeholderMap),
		}
		resp, err := component.WeConnect.SendTplMessageToSingle(ctx, info.Channel, req)
		if err != nil {
			return "", err
		}
		return resp.Id, nil
	case "subscribeMessage":
		info := getValidSocialByChannelId(member, message.Template.Channel.Id)
		if info == nil {
			return "", errors.New("Invalid social")
		}
		req := &component.SubscribeMsg{
			OriginId:         info.OpenId,
			AppSecret:        message.Template.AppSecret,
			SubscribeMessage: generateMessagepushTemplateMessage(message, placeholderMap),
		}
		if message.Template.Page == "" {
			message.Template.Page = message.Template.Url
		}
		req.SubscribeMessage.Page = formatTemplateDataValue(message.Template.Page, placeholderMap)
		resp, err := component.WeConnect.SendSubscribeMessage(ctx, info.Channel, req)
		if err != nil {
			if resp != nil {
				return "", errors.New(share_model.GenMessageNotificationLogDescription(share_model.MESSAGE_NOTIFICATION_LOG_TYPE_SUBSCRIBE_MESSAGE, resp.ErrCode))
			}
			return "", err
		}
	}
	return "", nil
}

func generateMessagepushTemplateMessage(customerMessage *pb_campaign.MessagePushCustomerMessage, replaceMap map[string]string) component.TemplateMessage {
	message := component.TemplateMessage{
		TemplateId: customerMessage.Template.Id,
	}
	data := make(map[string]map[string]string)
	for _, keyword := range customerMessage.Template.Keywords {
		value := formatTemplateDataValue(keyword.Value, replaceMap)
		runes := []rune(value)
		// 订阅消息参数类别为 thing 时，限制20个以内字符（可汉字、数字、字母或符号组合）
		// https://gitlab.maiscrm.com/mai/impl/home/<USER>/4277
		if strings.HasPrefix(keyword.Key, "thing") && len(runes) > 20 {
			value = string(runes[:17]) + "..."
		}

		data[keyword.Key] = map[string]string{
			"value": value,
		}

		if keyword.Color != "" {
			data[keyword.Key]["color"] = keyword.Color
		}
	}
	if customerMessage.Template.MiniProgram.PagePath != "" && customerMessage.Template.MiniProgram.AppId != "" {
		message.MiniProgram = &component.TemplateMessageMiniProgram{
			PagePath: formatTemplateDataValue(customerMessage.Template.MiniProgram.PagePath, replaceMap),
			AppId:    customerMessage.Template.MiniProgram.AppId,
		}
	}
	message.Data = data
	return message
}

func getValidSocialByChannelId(member *pb_member.MemberDetailResponse, channelId string) *pb_origin.OriginInfo {
	for _, info := range getMemberSocials(member) {
		if !util.StrInArray(info.Origin, &[]string{
			constant.WEAPP,
			constant.WECHAT,
		}) || info.Channel != channelId || info.OpenId == "" {
			continue
		}
		return info
	}
	return nil
}

func formatErrorMessage(err error) string {
	if err == nil {
		return ""
	}
	if errors.Is(err, NOTIFICATION_LIMIT_EXCEEDED_ERROR) {
		return WORKFLOW_LOG_ERROR_MEMBER_NOTIFICATION_LIMIT_EXCEEDED
	}
	rpcErr := mai_err.ToMaiRPCError(err)
	switch rpcErr.Code {
	case codes.MemberBlocked, coupon_codes.MemberBlocked:
		return WORKFLOW_LOG_ERROR_MEMBER_IS_BLOCKED
	case codes.MemberDisabled:
		return WORKFLOW_LOG_ERROR_FAILED_MEMBER_INVALID
	case codes.MemberNotFound, coupon_codes.MemberNotFound:
		return WORKFLOW_LOG_ERROR_MEMBER_NOT_FOUND
	case coupon_codes.CouponCodeNotFound:
		return WORKFLOW_LOG_ERROR_COUPON_NOT_FOUND
	case coupon_codes.CouponCodeExpired:
		return WORKFLOW_LOG_ERROR_COUPON_EXPIRED
	case coupon_codes.CouponExceedLimit:
		return WORKFLOW_LOG_ERROR_COUPON_EXCEED_LIMIT
	case coupon_codes.CouponNotEnough, coupon_codes.FailedToDecreaseCoupon:
		return WORKFLOW_LOG_ERROR_COUPON_NOT_ENOUGH
	}
	return err.Error()
}
