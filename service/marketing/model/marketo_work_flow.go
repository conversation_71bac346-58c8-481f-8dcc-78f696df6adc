package model

import (
	"context"
	"errors"
	"fmt"
	pb_account_channel "mairpc/proto/account/channel"
	"mairpc/proto/common/request"
	"math/rand"
	"net/url"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/spf13/viper"

	mairpc "mairpc/core/client"
	core_component "mairpc/core/component"
	core_errors "mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/account"
	proto_client "mairpc/proto/client"
	"mairpc/proto/common/origin"
	pb_ec_groupchat "mairpc/proto/ec/groupchat"
	ec_staff "mairpc/proto/ec/staff"
	pb_ec_staff_task "mairpc/proto/ec/staffTask"
	"mairpc/proto/marketing"
	"mairpc/proto/member"
	coupon_codes "mairpc/service/coupon/codes"
	member_codes "mairpc/service/member/codes"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"

	"github.com/asaskevich/govalidator"
	"github.com/qiniu/qmgo"
	"github.com/spf13/cast"
)

const (
	C_MARKETO_WORK_FLOW = "marketoWorkflow"

	WORKFLOW_STATUS_PENDING      = "pending"
	WORKFLOW_STATUS_PROCESSING   = "processing"
	WORKFLOW_STATUS_EVENT        = "event"
	WORKFLOW_STATUS_WAITING      = "waiting"
	WORKFLOW_STATUS_WAITING_TEST = "waitingTest"
	WORKFLOW_STATUS_END          = "end"
	WORKFLOW_STATUS_ERROR        = "error"
	WORKFLOW_STATUS_STOPPED      = "stopped"
	WORKFLOW_STATUS_SUSPEND      = "suspend" // 挂起/暂停，由专用的逻辑去执行

	WORKFLOW_LOG_SUCCEED   = "succeed"
	WORKFLOW_LOG_FAILED    = "failed"
	WORKFLOW_LOG_SUBMITTED = "submited" // type on purpose, for use same string with batch operation

	ACTION  = "action"
	CONTROL = "control"
	END     = "end"

	ACTION_SMS                  = "sms"
	ACTION_DIGITAL_SMS          = "digital_sms"
	ACTION_EMAIL                = "email"
	ACTION_WECHAT               = "wechat"
	ACTION_WECHAT_MASS          = "wechat_mass"
	ACTION_COUPON               = "coupon"
	ACTION_TAG                  = "tag"
	ACTION_CUSTOMER             = "customer"
	ACTION_MEMBER               = "member"
	ACTION_TEMPLATE             = "template"
	ACTION_WEBHOOK              = "webhook"
	ACTION_MESSAGE              = "message"
	ACTION_WECHATWORK_MESSAGE   = "wechatwork_message"
	ACTION_ALIYUNQA_SMS         = "aliyunqa_sms"
	ACTION_ALIYUNQA_DIGITAL_SMS = "aliyunqa_digital_sms"
	ACTION_STAFF_OPERATION      = "staff_operation" // 智慧导购日常运营
	ACTION_WHATSAPP_MESSAGE     = "whatsapp_message"
	ACTION_MEMBER_MEDAL         = "member_medal"
	ACTION_LINE_MESSAGE         = "line_message"

	ACTION_WECHATWORK_MESSAGE_TEXT         = "text"
	ACTION_WECHATWORK_MESSAGE_IMAGE        = "image"
	ACTION_WECHATWORK_MESSAGE_VIDEO        = "video"
	ACTION_WECHATWORK_MESSAGE_LINK         = "link"
	ACTION_WECHATWORK_MESSAGE_MINI_PROGRAM = "miniProgram"

	ACTION_MEMBER_SCORE  = "score"
	ACTION_MEMBER_GROWTH = "growth"
	ACTION_MEMBER_CARD   = "card"

	TAG_TYPE_ADD   = "+"
	TAG_TYPE_MINUS = "-"

	MSGTYPE_TEXT         = "TEXT"
	MSGTYPE_VOICE        = "VOICE"
	MSGTYPE_VIDEO        = "VIDEO"
	MSGTYPE_IMAGE        = "IMAGE"
	MSGTYPE_MPNEWS       = "MPNEWS"
	MSGTYPE_NEWS         = "NEWS"
	MSGTYPE_MINI_PROGRAM = "MINI_PROGRAM_PAGE"

	SPECIAL_PROPERTY_PROPVINCE        = "province"
	SPECIAL_PROPERTY_CITY             = "city"
	SPECIAL_PROPERTY_DISTRICT         = "district"
	SPECIAL_PROPERTY_CREATEDAT        = "createdAt"
	SPECIAL_PROPERTY_SCORE            = "score"
	SPECIAL_PROPERTY_SOCIAL           = "social"         // 创建自
	SPECIAL_PROPERTY_SOCIAL_IDENTITY  = "socialIdentity" // 平台身份
	SPECIAL_PROPERTY_SOURCE           = "source"
	SPECIAL_PROPERTY_ORIGINAL_CHANNEL = "originalChannel"
	SPECIAL_PROPERTY_BOUND_STAFF      = "boundStaff" // 绑定导购

	// don't change the count unless low efficiency can't be tolerated
	MAX_PROCESSING_COUNT           = 100
	MAX_PROCESSING_MINUTE_DURATION = 15
	MAX_PROCESSING_GOROUTINE_COUNT = 20
	MAX_CHECK_SUBMITTED_COUNT      = 1000

	MAX_AQUIRE_LOCK_TIME = 120

	PROCESSING_LOCK_SUFFIX                   = "_marketo_processing_workflow_acccountId"
	RESTORE_LOCK_SUFFIX                      = "_marketo_restore_workflow_acccountId"
	TEMPLATE_MESSAGE_LOCK_SUFFIX             = "_marketo_update_template_status_acccountId"
	SUSPEND_WORKFLOW_LOCK                    = "%s:marketing:triggerSuspendWorkflow"
	TRIGGER_SUSPEND_EXPIRE_POOL_KEY_TEMPLATE = "%s:marketing:triggerSuspendWorkflowPool"

	TRIGGER_SUSPEND_ONCE_DURATION = 10 // 单位：分钟

	MEMBER_PROPERTY_ADDRESS  = "address"
	MEMBER_PROPERTY_LOCATION = "location"
	MEMBER_PROPERTY_DATE     = "date"
	MEMBER_PROPERTY_INPUT    = "input"

	CHINA = "中国"

	TEMPLATE_MESSAGE_DATA_KEY_NAME      = "name"
	TEMPLATE_MESSAGE_DATA_KEY_NICKNAME  = "nickname"
	TEMPLATE_MESSAGE_DATA_KEY_GENDER    = "gender"
	TEMPLATE_MESSAGE_DATA_KEY_MEMBER_ID = "memberId"

	WORKFLOW_LOG_ERROR_MEMBER_NOT_FOUND                             = "memberNotFound"
	WORKFLOW_LOG_ERROR_MEMBER_SOCIAL_NOT_FOUND                      = "memberSocialNotFound"
	WORKFLOW_LOG_ERROR_FAILED_ADD_SCORE                             = "scoreAddFailed"
	WORKFLOW_LOG_ERROR_FAILED_DEDUCT_SCORE                          = "scoreDeductFailed"
	WORKFLOW_LOG_ERROR_FAILED_MEMBER_INVALID                        = "memberInvalid" // 用户未激活或禁用
	WORKFLOW_LOG_ERROR_CARD_NOT_FOUND                               = "cardNotFound"
	WORKFLOW_LOG_ERROR_FAILED_SEND_CARD                             = "cardSendFailed"
	WORKFLOW_LOG_ERROR_FAILED_ADD_TAG                               = "tagAddFailed"
	WORKFLOW_LOG_ERROR_FAILED_ADD_WECHAT_TAG                        = "wechatTagAddFailed"
	WORKFLOW_LOG_ERROR_FAILED_REMOVE_TAG                            = "tagRemoveFailed"
	WORKFLOW_LOG_ERROR_FAILED_REMOVE_WECHAT_TAG                     = "wechatTagRemoveFailed"
	WORKFLOW_LOG_ERROR_ISSUE_COUPON_FAILED                          = "issueCouponFailed"
	WORKFLOW_LOG_ERROR_MEMBER_IS_BLOCKED                            = "memberBlocked"
	WORKFLOW_LOG_ERROR_COUPON_NOT_ENOUGH                            = "couponStockNotEnough"
	WORKFLOW_LOG_ERROR_COUPON_EXCEED_LIMIT                          = "couponBeenReceived"
	WORKFLOW_LOG_ERROR_COUPON_NOT_FOUND                             = "couponNotFound"
	WORKFLOW_LOG_ERROR_COUPON_EXPIRED                               = "couponExpired"
	WORKFLOW_LOG_ERROR_TEMPLATE_MESSAGE_UNSUBSCRIBED                = "templateMessageUnsubscribed"
	WORKFLOW_LOG_ERROR_TEMPLATE_MESSAGE_USER_BLOCK                  = "templateMessageUserBlock"
	WORKFLOW_LOG_ERROR_TEMPLATE_MESSAGE_DELETED                     = "templateMessageDeleted"
	WORKFLOW_LOG_ERROR_MEMBERSHIP_CARD_DISABLED                     = "membershipCardDisabled"
	WORKFLOW_LOG_ERROR_EMPTY_PHONENUMBER                            = "emptyPhoneNumber"
	WORKFLOW_LOG_ERROR_SMS_FAILED                                   = "sendSMSFailed"
	WORKFLOW_LOG_ERROR_WECHAT_MESSAGE_FAILED                        = "sendWechatMessageFailed"
	WORKFLOW_LOG_ERROR_WECHAT_MASS_MESSAGE_FAILED                   = "sendWechatMassMessageFailed"
	WORKFLOW_LOG_ERROR_CUSTOM_PROPERTY_NOT_FOUND                    = "customPropertyNotFound"
	WORKFLOW_LOG_ERROR_CUSTOM_PROPERTY_FAILED                       = "udpateCustomPropertyFailed"
	WORKFLOW_LOG_ERROR_CALL_WEBHOOK_FAILED                          = "callWebhookFailed"
	WORKFLOW_LOG_ERROR_SEND_MESSAGE_FAILED                          = "sendMessageFailed"
	WORKFLOW_LOG_ERROR_SYSTEM_ERROR                                 = "systemError"
	WORKFLOW_LOG_ERROR_EMPTY_EMAIL_ADDRESS                          = "emptyEmailAddress"
	WORKFLOW_LOG_ERROR_INVALID_EMAIL_ADDRESS                        = "invalidEmailAddress"
	WORKLFOW_LOG_ERROR_FAILED_SEND_EMAIL                            = "failedToSendEmail"
	WORKFLOW_LOG_ERROR_SMS_QUOTA_LIMIT                              = "smsQuotaLimit"
	WORKFLOW_LOG_ERROR_BOUNDED_STAFF_NOT_FOUND                      = "boundedStaffNotFound"
	WORKFLOW_LOG_ERROR_SEND_JUZI_MESSAGE_PARTIAL_FAILED             = "sendJuziMessagePartialFailed"
	WORKFLOW_LOG_ERROR_MEMBER_IS_NOT_DISTURB                        = "memberIsNotDisturb"
	WORKFLOW_LOG_ERROR_MEMBER_UNSUBSCRIBE_SMS                       = "memberUnsubscribeSms"
	WORKFLOW_LOG_ERROR_ADD_MEMBER_TO_OPERATION_TEMPLATE_FAILED      = "addMemberToOperationTemplateFailed"
	WORKFLOW_LOG_ERROR_REMOVE_MEMBER_FROM_OPERATION_TEMPLATE_FAILED = "removeMemberFromOperationTemplateFailed"
	WORKFLOW_LOG_ERROR_INVALID_MEMBER_PROPERTY                      = "invalidMemberProperty"
	WORKFLOW_LOG_ERROR_SEND_WHATSAPP_MESSAGE_PARTIAL_FAILED         = "sendWhatsAppMessagePartialFailed"
	WORKFLOW_LOG_ERROR_SEND_WHATSAPP_MESSAGE_FAILED                 = "sendWhatsAppMessageFailed"
	WORKFLOW_LOG_ERROR_ISSUE_MEMBER_MEDAL_FAILED                    = "issueMemberMedalFailed"
	WORKFLOW_LOG_ERROR_ROLLBACK_MEMBER_MEDAL_FAILED                 = "rollbackMemberMedalFailed"
	WORKFLOW_LOG_ERROR_SEND_LINE_MESSAGE_FAILED                     = "sendlineMessageFailed"
	WORKFLOW_LOG_ERROR_MEMBER_NOTIFICATION_LIMIT_EXCEEDED           = "memberNotificationLimitExceeded"

	WECHAT_LONG_TIME_INACTIVE = "Users no longer access to the public, not actively send messages to the user."

	ACCOUNT_OEM_TYPE_ALIYUNQA = "aliyunqa"
)

var (
	CMarketoWorkflow = &MarketoWorkflow{}

	SMSApiKey string

	memberPropertyTextTypes = []string{
		"radio",
		"phone",
		"telephone",
		"email",
		"ip",
		"url",
	}

	memberPropertyDateTypes = []string{
		"date",
		"datetime",
	}

	memberPropertyNumberTypes = []string{
		"currency",
		"number",
	}

	memberPropertyStringArrayTypes = []string{
		"checkbox",
		"image",
	}

	memberPropertyBoolTypes = []string{
		"bool",
	}

	locationTypeIndex = map[string]int{
		SPECIAL_PROPERTY_PROPVINCE: 0,
		SPECIAL_PROPERTY_CITY:      1,
		SPECIAL_PROPERTY_DISTRICT:  2,
	}

	NeedStopStatusList = []string{
		WORKFLOW_STATUS_PENDING,
		WORKFLOW_STATUS_PROCESSING,
		WORKFLOW_STATUS_EVENT,
		WORKFLOW_STATUS_WAITING,
		WORKFLOW_STATUS_WAITING_TEST,
		WORKFLOW_STATUS_SUSPEND,
	}

	WechatMessageErrorReg = regexp.MustCompile(`^Error sending request to Wechat, code: \[([0-9,]+)\]`)

	numberCompiler, _ = regexp.Compile(`.*?(\d+\.?\d{0,}).*?`)

	aliyunqaSmsMap = sync.Map{}
)

type MarketoWorkflow struct {
	Id                  bson.ObjectId `bson:"_id,omitempty"`
	AccountId           bson.ObjectId `bson:"accountId"`
	MemberId            bson.ObjectId `bson:"memberId"`
	MarketoId           bson.ObjectId `bson:"marketoId"`
	IsMarketoRepeatable bool          `bson:"isMarketoRepeatable"`
	WorkflowId          string        `bson:"workflowId"`
	Status              string        `bson:"status"`
	EventAt             time.Time     `bson:"eventAt"`
	EventId             string        `bson:"eventId"`
	WaitingAt           time.Time     `bson:"waitingAt"`
	ProcessingAt        time.Time     `bson:"processingAt"`
	IsDeleted           bool          `bson:"isDeleted"`
	WorkflowLogs        []WorkflowLog `bson:"workflowLogs"`
	Weight              int64         `bson:"weight"`
	CreatedAt           time.Time     `bson:"createdAt"`
	StartedAt           time.Time     `bson:"startedAt,omitempty"`
	StoppedAt           time.Time     `bson:"stoppedAt,omitempty"`
	FinishedAt          time.Time     `bson:"finishedAt,omitempty"`
	Duration            int64         `bson:"duration,omitempty"`
	IsBatchOperation    bool          `bson:"isBatchOperation,omitempty"`
	Identifier          string        `bson:"identifier,omitempty"`
	// store maievent which creates the workflow
	// this field exists only if marketo's type is event
	Event      Maievent `bson:"event,omitempty"`
	RetryTimes int      `bson:"retryTimes"`

	memberDetail        *member.MemberDetailResponse
	workflows           []Workflow
	matched             bool
	incTestStatsId      bson.ObjectId
	testBranch          string
	isTestGoalCompleted bool
	oem                 *account.AccountOemDetail
	// 用于记录发放优惠券后的 id 存入 workflowLog
	membershipDiscountIds []string
	// 用于记录发送短信时的 businessId
	smsBusinessId string
	// 用于记录某条 workflow 时的 log，使用后清空
	err error
	// 用于记录某条 workflow 执行过程中的 openId，使用后清空
	memberOpenId string
	// 用于记录某条 workflow 执行 action 过程中的 resultType
	resultType string
	// 需要重试，不执行后续节点，也不更新状态，由 RestoreStaleProcess 触发后自动更新为 pending 状态
	needRetryFromProcessing bool
	// 是否是在处理 suspend 的流程中
	isProcessSuspend bool
	// 存放额外信息
	info string
	// marketo.user.id
	userId bson.ObjectId
	// 路径名称
	marketoName string
	// 防骚扰设置
	notificationLimitSettings []MarketingNotificationLimitSetting
}

type WorkflowLog struct {
	WorkflowId            string        `bson:"workflowId"`
	FinishedAt            time.Time     `bson:"finishedAt"`
	IncTestStatsId        bson.ObjectId `bson:"incTestStatsId,omitempty"`
	TestBranch            string        `bson:"testBranch,omitempty"`
	IsTestGoalCompleted   bool          `bson:"isTestGoalCompleted,omitempty"`
	SmsBusinessId         string        `bson:"smsBusinessId,omitempty"`
	MembershipDiscountIds []string      `bson:"membershipDiscountIds,omitempty"`
	Info                  string        `bson:"info,omitempty"`
	Error                 string        `bson:"error,omitempty"`
	ErrorType             string        `bson:"errorType,omitempty"`
	Status                string        `bson:"status,omitempty"`
	Matched               bool          `bson:"matched,omitempty"`
	// if the workflow log is send template message,
	// then we should record the message id and channel id.
	// They will be used to asynchronously check if template
	// message send successfully
	MessageId string `bson:"messageId,omitempty"`
	ChannelId string `bson:"channelId,omitempty"`
	// if workflow node is event control,
	// we'll store event info
	Event Maievent `bson:"event,omitempty"`
}

type Maievent struct {
	EventId         string                 `bson:"eventId" json:"eventId,omitempty"`
	OccurredAt      time.Time              `bson:"occurredAt" json:"occurredAt,omitempty"`
	EventProperties map[string]interface{} `bson:"eventProperties" json:"eventProperties,omitempty"`
}

func (a *WorkflowAction) CountJuziApiUsage() int {
	if a.Type != ACTION_WECHATWORK_MESSAGE {
		return 0
	}
	count := 0
	for _, message := range a.WechatworkMessages {
		if message.Type == ACTION_WECHATWORK_MESSAGE_IMAGE {
			count += len(message.Images)
		} else {
			count += 1
		}
	}
	return count
}

func (*MarketoWorkflow) GetPendingList(ctx context.Context, condition bson.M) []MarketoWorkflow {
	var marketoWorkflows []MarketoWorkflow
	extension.DBRepository.FindAll(ctx, C_MARKETO_WORK_FLOW, condition, []string{"-weight"}, MAX_PROCESSING_COUNT, &marketoWorkflows)
	return marketoWorkflows
}

// GetSuspendList 获取 100 个挂起的工作流并标记为处理中，
func (*MarketoWorkflow) GetSuspendList(ctx context.Context, condition bson.M) ([]MarketoWorkflow, map[string]Marketo) {
	deadline := time.Now().Add(time.Second * 10)
	// 自旋等待获取锁，防止并发获取。
	for {
		if acquireSuspendLock(ctx) {
			break
		}
		if time.Now().After(deadline) {
			return nil, nil
		}
		time.Sleep(time.Millisecond * 50)
	}
	defer func() {
		releaseSuspendLock(ctx)
	}()
	var marketoWorkflows []MarketoWorkflow
	extension.DBRepository.FindAll(ctx, C_MARKETO_WORK_FLOW, condition, []string{"-weight"}, MAX_PROCESSING_COUNT, &marketoWorkflows)
	if len(marketoWorkflows) == 0 {
		return nil, nil
	}
	marketoMap := CMarketoWorkflow.GetMarketoMap(ctx, marketoWorkflows)
	if len(marketoMap) == 0 {
		return nil, nil
	}
	// 将取出的 marketoWorkflow 标记为处理中
	CMarketoWorkflow.BatchBeginToProcessWorkflow(ctx, marketoWorkflows)
	return marketoWorkflows, marketoMap
}

func (*MarketoWorkflow) BatchDelete(ctx context.Context, condition bson.M) error {
	_, err := extension.DBRepository.RemoveAll(ctx, C_MARKETO_WORK_FLOW, condition)
	return err
}

func (*MarketoWorkflow) BatchUpdate(ctx context.Context, condition, updater bson.M) error {
	_, err := extension.DBRepository.UpdateAll(ctx, C_MARKETO_WORK_FLOW, condition, updater)
	return err
}

// 更新工作流状态为 processing，并将 startAt 为零值的工作流赋值为 now
func (*MarketoWorkflow) BatchBeginToProcessWorkflow(ctx context.Context, workflows []MarketoWorkflow) {
	var workflowIds []bson.ObjectId
	for _, workflow := range workflows {
		workflowIds = append(workflowIds, workflow.Id)
	}

	condition := bson.M{
		"_id": bson.M{
			"$in": workflowIds,
		},
	}

	now := time.Now()
	updator := bson.M{
		"$set": bson.M{
			"status":       WORKFLOW_STATUS_PROCESSING,
			"processingAt": time.Now(),
		},
		// use $min to make sure the started at field will only update once
		"$min": bson.M{
			"startedAt": now,
		},
	}

	// in case the workflows haven't define startedAt before, we'll
	// update its startedAt
	for i := range workflows {
		if core_util.IsZero(workflows[i].StartedAt) {
			workflows[i].StartedAt = now
		}
	}

	extension.DBRepository.UpdateAll(ctx, C_MARKETO_WORK_FLOW, condition, updator)
}

// 执行一个工作流
func (m *MarketoWorkflow) Process(ctx context.Context, marketoMap map[string]Marketo, memberDetailMap map[string]*member.MemberDetailResponse, smsTopic string, notificationSettings []MarketingNotificationLimitSetting) error {

	workflow, ok := m.prepare(ctx, marketoMap, memberDetailMap, notificationSettings)
	if !ok {
		return nil
	}

	return m.processInternal(ctx, workflow, smsTopic)
}

func (m *MarketoWorkflow) prepare(ctx context.Context, marketoMap map[string]Marketo, memberDetailMap map[string]*member.MemberDetailResponse, notificationSettings []MarketingNotificationLimitSetting) (Workflow, bool) {
	marketo, ok := marketoMap[m.MarketoId.Hex()]
	m.workflows = marketo.Workflows
	m.userId = marketo.User.Id
	m.marketoName = marketo.Name
	m.notificationLimitSettings = notificationSettings
	if !ok || len(m.workflows) == 0 {
		m.Status = WORKFLOW_STATUS_ERROR
		m.updateAfterProcess(ctx, errors.New("Workflows not found"), "", true, Maievent{})

		return Workflow{}, false
	}
	if marketo.ShouldStopAllWorkflows() {
		m.updateStatus(ctx, MARKETO_STATUS_STOPPED)
		return Workflow{}, false
	}

	// sometimes we finished workflow, add workflowLog, but failed to
	// set m.WorkflowId, so we check if m.WorkflowId have already been done
	if len(m.WorkflowLogs) > 0 {
		log := m.WorkflowLogs[len(m.WorkflowLogs)-1]
		if log.Status == WORKFLOW_LOG_SUCCEED && m.WorkflowId == log.WorkflowId {
			if workflow, ok := m.getCurrentWorkflow(); ok {
				if workflow.Type == ACTION {
					if workflow, ok := m.getNextWorkflowById(m.WorkflowId, ""); ok {
						m.WorkflowId = workflow.Id
					}
				}
			}
		}

		// 对于短信节点，如果处理 pending 状态时存在 workflowLog 并且 workflowLog.status 还是 submited 状态
		// 说明是在处理 suspend 状态过程中被打断了然后通过 RestoreStaleProcess 恢复成 pending 了
		// 需要重新更新成 suspend 状态重新执行打标签
		if log.Status == WORKFLOW_LOG_SUBMITTED && m.WorkflowId == log.WorkflowId && m.Status == WORKFLOW_STATUS_PENDING {
			if workflow, ok := m.getCurrentWorkflow(); ok {
				if workflow.Type == ACTION && workflow.Action.Type == ACTION_TAG {
					m.updateStatus(ctx, WORKFLOW_STATUS_SUSPEND)
					return Workflow{}, false
				}
			}
		}
	}

	member, ok := memberDetailMap[m.MemberId.Hex()]
	m.memberDetail = member
	if !ok {
		m.Status = WORKFLOW_STATUS_ERROR
		m.updateAfterProcess(ctx, errors.New("Member not found"), WORKFLOW_LOG_ERROR_MEMBER_NOT_FOUND, true, Maievent{})
		return Workflow{}, false
	}

	workflow, ok := m.getCurrentWorkflow()
	if !ok {
		m.Status = WORKFLOW_STATUS_ERROR
		m.updateAfterProcess(ctx, errors.New("Current workflow not found"), "", true, Maievent{})
		return Workflow{}, false
	}

	if m.WorkflowId != workflow.Id {
		m.WorkflowId = workflow.Id
		m.setWorkflowId(ctx)
	}

	return workflow, true
}

func (m *MarketoWorkflow) processInternal(ctx context.Context, workflow Workflow, smsTopic string) error {
	var (
		isDelayed  bool
		branch     string
		resultType string
		err        error
	)

	switch workflow.Type {
	case ACTION:
		err, resultType = m.processAction(ctx, workflow, smsTopic)
		if err != nil {
			// 处理 err，判断是否失败后继续执行
			if !m.handleActionErr(ctx, workflow, err, resultType, true, true) {
				return err
			}
		}
		if m.Status == WORKFLOW_STATUS_SUSPEND {
			m.updateAfterProcess(ctx, nil, resultType, true, Maievent{})
			return nil
		}
	case CONTROL:
		isDelayed, branch = m.processControl(ctx, workflow)
		if isDelayed {
			m.updateAfterProcess(ctx, nil, "", true, Maievent{})
			return nil
		}

		m.EventAt = time.Time{}
		m.WaitingAt = time.Time{}
		m.EventId = ""
		resultType = WORKFLOW_LOG_SUCCEED
	case END:
		m.Status = WORKFLOW_STATUS_END
		m.updateAfterProcess(ctx, nil, WORKFLOW_LOG_SUCCEED, true, Maievent{})
		return nil
	}

	if workflow.IsTestGoal() {
		m.isTestGoalCompleted = workflow.Control.IsFirstBranch(branch)
	}

	m.Status = WORKFLOW_STATUS_PROCESSING
	_, hasWorkflowStopped := m.updateAfterProcess(ctx, err, resultType, true, Maievent{})
	if hasWorkflowStopped {
		return nil
	}
	nextWorkflow, ok := m.getNextWorkflowById(workflow.Id, branch)
	if !ok {
		m.Status = WORKFLOW_STATUS_ERROR
		m.updateAfterProcess(ctx, errors.New("Next workflow not found"), "", true, Maievent{})
		return nil
	}

	m.WorkflowId = nextWorkflow.Id
	m.setWorkflowId(ctx)

	return m.processInternal(ctx, nextWorkflow, smsTopic)
}

func (m *MarketoWorkflow) processAction(ctx context.Context, w Workflow, smsTopic string) (error, string) {
	// there are some special error cases for member batch operation
	// we'll filter out them here
	if m.IsBatchOperation {
		// if member is blocked, we can only tag/untag him
		if m.memberDetail.BlockedStatus == 2 && w.Action.Type != ACTION_TAG {
			return errors.New("memebr is blocked"), WORKFLOW_LOG_ERROR_MEMBER_IS_BLOCKED
		}
	}

	extra := make(map[string]string)
	if strings.Contains(
		viper.GetString("env"),
		"hisense",
	) {
		extra["ma_trackId"] = m.MarketoId.Hex()
		extra["ma_tracknodeId"] = w.Id
	}

	action := w.Action
	switch action.Type {
	case ACTION_SMS:
		m.smsBusinessId = fmt.Sprintf("%s:%s", m.Id.Hex(), w.Id)
		err, result, phone := checkBeforeSendingSms(
			ctx,
			m.memberDetail,
			m.GetNotificationLimitSetting(NOTIFICATION_TYPE_SMS),
			m.smsBusinessId,
			false,
		)
		if err != nil {
			return err, result
		}
		// replace content with dynamic params
		replaceMap := generateDataReplaceMap(action.PropertyValueMaps, m.memberDetail)
		content := formatTemplateDataValue(action.Content, replaceMap)
		if action.SmsTopic != "" {
			smsTopic = action.SmsTopic
		}
		err = SendSms(ctx, &account.SendSmsRequest{
			Phone:        phone,
			Text:         content,
			BusinessType: "marketo",
			BusinessId:   m.smsBusinessId,
			BusinessName: fmt.Sprintf("%s::%s", m.MarketoId.Hex(), m.marketoName),
			SmsTopic:     smsTopic,
			MessageType:  account.SendSmsRequest_MARKETING,
		})
		if err != nil {
			result := WORKFLOW_LOG_ERROR_SMS_FAILED
			mairpcErr := core_errors.ToMaiRPCError(err)
			if mairpcErr != nil {
				// code list:
				// https://help.aliyun.com/document_detail/101346.html?spm=a2c4g.********.2.14.436556e0fFajy8
				if code, ok := mairpcErr.Extra["code"]; ok {
					result = result + ":" + cast.ToString(code)
				}
			}

			// 因短信配额不足导致自动化营销发短信失败时，记录失败原因
			if strings.Contains(err.Error(), "sms quota limit") {
				result = WORKFLOW_LOG_ERROR_SMS_QUOTA_LIMIT
			}

			return err, result
		}

		return nil, WORKFLOW_LOG_SUCCEED
	case ACTION_ALIYUNQA_SMS, ACTION_ALIYUNQA_DIGITAL_SMS:
		if m.oem == nil || m.oem.OemType != ACCOUNT_OEM_TYPE_ALIYUNQA {
			return errors.New("aliyunqa oem not found"), WORKFLOW_LOG_ERROR_SMS_FAILED
		}

		err, result, phone := checkBeforeSendingSms(
			ctx,
			m.memberDetail,
			m.GetNotificationLimitSetting(NOTIFICATION_TYPE_SMS),
			fmt.Sprintf("%s:%s", m.Id.Hex(), w.Id),
			false,
		)
		if err != nil {
			return err, result
		}
		aliyunqaSms := action.AliyunqaSms
		isVariable, templateParams, err := m.isAliyunqaSmsVariable(ctx, action)
		if err != nil {
			return err, WORKFLOW_LOG_ERROR_INVALID_MEMBER_PROPERTY
		}
		m.smsBusinessId = fmt.Sprintf("%s:%s", m.MarketoId.Hex(), w.Id)
		req := &account.SendSmsRequest{
			Phone:        phone,
			SmsTopic:     aliyunqaSms.SignName,
			BusinessType: "marketo",
			BusinessId:   m.smsBusinessId,
			MemberId:     m.memberDetail.Id,
			SubType:      action.Type,
			AliyunqaSms: &account.AliyunqaSms{
				TaskName:       aliyunqaSms.TaskName,
				SmsTemplateId:  aliyunqaSms.TemplateId,
				PlatformId:     aliyunqaSms.PlatformId,
				ChannelType:    2,
				IsVariable:     isVariable,
				TemplateParams: templateParams,
			},
		}
		if m.userId.Valid() {
			req.UserId = m.userId.Hex()
		}
		err = SendSms(ctx, req)
		if err != nil {
			result := WORKFLOW_LOG_ERROR_SMS_FAILED
			mairpcErr := core_errors.ToMaiRPCError(err)
			if mairpcErr != nil {
				if code, ok := mairpcErr.Extra["code"]; ok {
					result = result + ":" + cast.ToString(code)
				}
			}

			if strings.Contains(err.Error(), "sms quota limit") {
				result = WORKFLOW_LOG_ERROR_SMS_QUOTA_LIMIT
			}
			return err, result
		}

		return nil, WORKFLOW_LOG_SUCCEED
	case ACTION_EMAIL:
		if core_util.IsZero(action.Email) {
			return errors.New("invalid email data"), WORKFLOW_LOG_ERROR_SYSTEM_ERROR
		}

		if m.memberDetail.Email == "" {
			return errors.New("Invalid email address"), WORKFLOW_LOG_ERROR_EMPTY_EMAIL_ADDRESS
		}

		if !govalidator.IsEmail(m.memberDetail.Email) {
			return errors.New("Invalid email address"), WORKFLOW_LOG_ERROR_INVALID_EMAIL_ADDRESS
		}

		if memberIsNotDisturb(m.memberDetail) {
			return errors.New("Member is not disturbed"), WORKFLOW_LOG_ERROR_MEMBER_IS_NOT_DISTURB
		}

		if !CMarketingMemberNotificationLimit.Use(
			ctx,
			bson.ObjectIdHex(m.memberDetail.Id),
			m.GetNotificationLimitSetting(NOTIFICATION_TYPE_EMAIL),
			NOTIFICATION_TYPE_EMAIL,
			fmt.Sprintf("%s:%s", m.Id.Hex(), w.Id),
		) {
			return NOTIFICATION_LIMIT_EXCEEDED_ERROR, WORKFLOW_LOG_ERROR_MEMBER_NOTIFICATION_LIMIT_EXCEEDED
		}

		// replace content with dynamic params
		replaceMap := generateDataReplaceMap(action.PropertyValueMaps, m.memberDetail)
		content := formatTemplateDataValue(action.Email.Body, replaceMap)

		switch action.Email.Type {
		case EMAIL_TYPE_SYSTEM, EMAIL_TYPE_CUSTOM:
			err := SendSystemEmail(
				ctx,
				action.Email.MailSenderId,
				action.Email.Subject,
				content,
				action.Email.ReplyToAddress,
				[]string{m.memberDetail.Email},
				[]string{},
				[]string{},
				true,
			)
			if err != nil {
				return err, WORKLFOW_LOG_ERROR_FAILED_SEND_EMAIL
			}
		}

		return nil, WORKFLOW_LOG_SUCCEED
	case ACTION_WECHAT:
		if memberIsNotDisturb(m.memberDetail) {
			return errors.New("Member is not disturbed"), WORKFLOW_LOG_ERROR_MEMBER_IS_NOT_DISTURB
		}
		err := sendWechatMessage(ctx, action, m.memberDetail, extra)
		if err != nil {
			result := WORKFLOW_LOG_ERROR_WECHAT_MESSAGE_FAILED

			// code list:
			// https://gitlab.maiscrm.com/mai/home/<USER>/12551#note_1915693
			apiErr, ok := err.(*util.ApiError)
			if ok {
				matchedString := WechatMessageErrorReg.FindStringSubmatch(apiErr.Msg)
				if len(matchedString) > 1 {
					errorCode := strings.Replace(matchedString[1], ",", "", -1)
					result = result + ":" + errorCode
				}
				if WECHAT_LONG_TIME_INACTIVE == apiErr.Msg {
					result = WORKFLOW_LOG_ERROR_WECHAT_MESSAGE_FAILED + ":" + "45015"
				}
			}
			// if the channel is not in member.social, the result should be
			// unsubscribed
			if err.Error() == "Invalid social channels" {
				result = WORKFLOW_LOG_ERROR_TEMPLATE_MESSAGE_UNSUBSCRIBED
			}

			return err, result
		}

		return nil, WORKFLOW_LOG_SUCCEED
	case ACTION_COUPON:
		err, membershipDiscountIds, resultType := issueCoupons(ctx, m.MarketoId.Hex(), m.memberDetail, action.Coupon.Ids)
		m.membershipDiscountIds = membershipDiscountIds
		return err, resultType
	case ACTION_TAG:
		if action.Tag.TagType == TAG_TYPE_WECHAT {
			// 微信标签有频率限制，挂起后单独处理
			m.Status = WORKFLOW_STATUS_SUSPEND
			return nil, WORKFLOW_LOG_SUBMITTED
		}
		return updateMemberTags(ctx, m.memberDetail.Id, action.Tag)
	case ACTION_WECHATWORK_MESSAGE, ACTION_WECHAT_MASS, ACTION_WHATSAPP_MESSAGE, ACTION_WEBHOOK:
		// whatsapp 发消息有频率限制，挂起
		// 句子 api 30 秒 800 次频率限制，此处挂起，另做处理
		// 公众号群发每分钟 60 次。
		m.Status = WORKFLOW_STATUS_SUSPEND
		return nil, WORKFLOW_LOG_SUBMITTED
	case ACTION_MEMBER:
		return updateMemberProperty(ctx, m.memberDetail, action.Member, action.Remark, m.MarketoId.Hex())
	case ACTION_CUSTOMER:
		err := updateCustomerProperty(ctx, m.memberDetail.Id, action.Customer)
		if err != nil {
			mairpcErr := core_errors.ToMaiRPCError(err)
			if mairpcErr != nil {
				if mairpcErr.Code != member_codes.MemberDisabled {
					return err, WORKFLOW_LOG_ERROR_CUSTOM_PROPERTY_NOT_FOUND
				}
			}

			return err, WORKFLOW_LOG_ERROR_CUSTOM_PROPERTY_FAILED
		}
		return nil, WORKFLOW_LOG_SUCCEED
	case ACTION_TEMPLATE:
		if memberIsNotDisturb(m.memberDetail) {
			return errors.New("Member is not disturbed"), WORKFLOW_LOG_ERROR_MEMBER_IS_NOT_DISTURB
		}
		if !CMarketingMemberNotificationLimit.Use(
			ctx,
			bson.ObjectIdHex(m.memberDetail.Id),
			m.GetNotificationLimitSetting(NOTIFICATION_TYPE_TEMPLATE),
			NOTIFICATION_TYPE_TEMPLATE,
			fmt.Sprintf("%s:%s", m.Id.Hex(), w.Id),
		) {
			return NOTIFICATION_LIMIT_EXCEEDED_ERROR, WORKFLOW_LOG_ERROR_MEMBER_NOTIFICATION_LIMIT_EXCEEDED
		}
		// the only errorType we should return here is WORKFLOW_LOG_ERROR_TEMPLATE_MESSAGE_UNSUBSCRIBED
		// all the other template message errorType should be checked in a job and updated asynchronously
		return sendTemplateMessage(ctx, m.memberDetail, w.Action, extra)
	case ACTION_LINE_MESSAGE:
		if memberIsNotDisturb(m.memberDetail) {
			return errors.New("Member is not disturbed"), WORKFLOW_LOG_ERROR_MEMBER_IS_NOT_DISTURB
		}
		if !CMarketingMemberNotificationLimit.Use(
			ctx,
			bson.ObjectIdHex(m.memberDetail.Id),
			m.GetNotificationLimitSetting(NOTIFICATION_TYPE_LINE),
			NOTIFICATION_TYPE_LINE,
			fmt.Sprintf("%s:%s", m.Id.Hex(), w.Id),
		) {
			return NOTIFICATION_LIMIT_EXCEEDED_ERROR, WORKFLOW_LOG_ERROR_MEMBER_NOTIFICATION_LIMIT_EXCEEDED
		}
		return sendLineMessage(ctx, m.memberDetail, w.Action, m.Id.Hex())
	case ACTION_MESSAGE, ACTION_DIGITAL_SMS:
		if memberIsNotDisturb(m.memberDetail) {
			return errors.New("Member is not disturbed"), WORKFLOW_LOG_ERROR_MEMBER_IS_NOT_DISTURB
		}
		if action.Type == ACTION_DIGITAL_SMS {
			// 判断当前客户是否退订营销短信
			if memberUnsubscribeMarketingSms(m.memberDetail) {
				return errors.New("Member unsubscribed marketing sms"), WORKFLOW_LOG_ERROR_MEMBER_UNSUBSCRIBE_SMS
			}
			if !CMarketingMemberNotificationLimit.Use(
				ctx,
				bson.ObjectIdHex(m.memberDetail.Id),
				m.GetNotificationLimitSetting(NOTIFICATION_TYPE_DSMS),
				NOTIFICATION_TYPE_DSMS,
				fmt.Sprintf("%s:%s", m.Id.Hex(), w.Id),
			) {
				return NOTIFICATION_LIMIT_EXCEEDED_ERROR, WORKFLOW_LOG_ERROR_MEMBER_NOTIFICATION_LIMIT_EXCEEDED
			}
		}
		err := SendMessage(ctx, action.MessageId, m.memberDetail.Id, &SendMessageOptions{
			Business:                  "marketo",
			BusinessId:                fmt.Sprintf("%s:%s", m.Id.Hex(), w.Id),
			Code:                      m.MarketoId.Hex(),
			NotificationLimitSettings: m.notificationLimitSettings,
		})
		if err != nil {
			return err, WORKFLOW_LOG_ERROR_SEND_MESSAGE_FAILED
		}

		return nil, WORKFLOW_LOG_SUCCEED
	case ACTION_STAFF_OPERATION:
		if action.RemovedStaffOperationTemplateId.Valid() {
			_, err := proto_client.GetEcStaffTaskServiceClient().RemoveMemberFromTemplate(ctx, &pb_ec_staff_task.AddMemberToTemplateRequest{
				MemberId:   m.memberDetail.Id,
				TemplateId: action.RemovedStaffOperationTemplateId.Hex(),
			})
			if err != nil {
				maiRPCError := core_errors.ToMaiRPCError(err)
				if maiRPCError != nil {
					code := cast.ToString(maiRPCError.Extra["code"])
					if code != "" {
						m.info = code
					}
				}
				return err, WORKFLOW_LOG_ERROR_REMOVE_MEMBER_FROM_OPERATION_TEMPLATE_FAILED
			}
			return nil, WORKFLOW_LOG_SUCCEED
		}
		if memberIsNotDisturb(m.memberDetail) {
			return errors.New("Member is not disturbed"), WORKFLOW_LOG_ERROR_MEMBER_IS_NOT_DISTURB
		}
		if !CMarketingMemberNotificationLimit.Use(
			ctx,
			bson.ObjectIdHex(m.memberDetail.Id),
			m.GetNotificationLimitSetting(NOTIFICATION_TYPE_WECONTACT),
			NOTIFICATION_TYPE_WECONTACT,
			fmt.Sprintf("%s:%s", m.Id.Hex(), w.Id),
		) {
			return NOTIFICATION_LIMIT_EXCEEDED_ERROR, WORKFLOW_LOG_ERROR_MEMBER_NOTIFICATION_LIMIT_EXCEEDED
		}
		_, err := proto_client.GetEcStaffTaskServiceClient().AddMemberToTemplate(ctx, &pb_ec_staff_task.AddMemberToTemplateRequest{
			MemberId:   m.memberDetail.Id,
			TemplateId: action.StaffOperationTemplateId.Hex(),
		})
		if err != nil {
			maiRPCError := core_errors.ToMaiRPCError(err)
			if maiRPCError != nil {
				code := cast.ToString(maiRPCError.Extra["code"])
				relation := cast.ToString(maiRPCError.Extra["relation"])
				if code != "" {
					m.info = code
					if relation != "" {
						m.info = fmt.Sprintf("%s:%s", code, relation)
					}
				}
			}
			return err, WORKFLOW_LOG_ERROR_ADD_MEMBER_TO_OPERATION_TEMPLATE_FAILED
		}
		return nil, WORKFLOW_LOG_SUCCEED
	case ACTION_MEMBER_MEDAL:
		var (
			cardIds        []string
			results        []*member.IssueMemberPaidCardResult
			workflowResult = WORKFLOW_LOG_ERROR_ISSUE_MEMBER_MEDAL_FAILED
		)
		for _, medal := range action.MemberMedal.Medals {
			cardIds = append(cardIds, medal.Id.Hex())
		}
		if action.MemberMedal.Type == "issue" {
			resp, err := proto_client.GetMemberServiceClient().BatchIssueMemberPaidCards(ctx, &member.BatchIssueMemberPaidCardsRequest{
				CardIds:  cardIds,
				MemberId: m.memberDetail.Id,
			})
			if err != nil {
				return err, workflowResult
			}
			results = resp.Results
		} else {
			workflowResult = WORKFLOW_LOG_ERROR_ROLLBACK_MEMBER_MEDAL_FAILED
			resp, err := proto_client.GetMemberServiceClient().RollbackMemberPaidCards(ctx, &member.RollbackMemberPaidCardsRequest{
				MemberId: m.memberDetail.Id,
				CardIds:  cardIds,
			})
			if err != nil {
				return err, workflowResult
			}
			results = resp.Results
		}
		var errs []string
		for _, result := range results {
			if result.Succeed {
				return nil, WORKFLOW_LOG_SUCCEED
			}
			m.info = cast.ToString(result.ErrorCode)
			errs = append(errs, fmt.Sprintf("%d:%s", result.ErrorCode, result.ErrorMessage))
		}
		return errors.New(strings.Join(util.StrArrayUnique(errs), ",")), workflowResult
	}

	return nil, ""
}

func (m *MarketoWorkflow) sendWechatworkMessage(ctx context.Context, message ActionWechatworkMessage, staff *ec_staff.Staff) []error {
	memberId := m.memberDetail.Id
	staffId := staff.Id
	switch message.Type {
	case ACTION_WECHATWORK_MESSAGE_TEXT:
		// 此场景动态参数仅有客户名和导购名
		replaceMap := map[string]string{
			"name":      m.memberDetail.Name,
			"staffname": staff.Name,
			"nickname": func() string {
				if m.memberDetail.OriginFrom.Origin == constant.WECONTACT {
					return m.memberDetail.OriginFrom.Nickname
				}
				for _, social := range m.memberDetail.Socials {
					if social.Origin == constant.WECONTACT {
						return social.Nickname
					}
				}
				return m.memberDetail.SocialMember
			}(),
		}
		content := formatTemplateDataValue(message.Text.Content, replaceMap)
		code, err := component.Juzi.SendText(ctx, staffId, memberId, content)
		if err != nil {
			m.info += fmt.Sprintf("%d,", code)
			return []error{err}
		}
	case ACTION_WECHATWORK_MESSAGE_IMAGE:
		var errs []error
		for _, image := range message.Images {
			code, err := component.Juzi.SendImage(ctx, staffId, memberId, image.Url)
			if err != nil {
				m.info += fmt.Sprintf("%d,", code)
				errs = append(errs, err)
			}
		}
		return errs
	case ACTION_WECHATWORK_MESSAGE_VIDEO:
		code, err := component.Juzi.SendVideo(ctx, staffId, memberId, message.Video.Url)
		if err != nil {
			m.info += fmt.Sprintf("%d,", code)
			return []error{err}
		}
	case ACTION_WECHATWORK_MESSAGE_LINK:
		link := message.Link
		code, err := component.Juzi.SendLink(ctx, staffId, memberId, link.Url, link.Title, link.ShareDescription, link.SharePicture)
		if err != nil {
			m.info += fmt.Sprintf("%d,", code)
			return []error{err}
		}
	case ACTION_WECHATWORK_MESSAGE_MINI_PROGRAM:
		miniProgram := message.MiniProgram
		code, err := component.Juzi.SendMiniProgram(ctx, staffId, memberId,
			miniProgram.AppId,
			miniProgram.OriginId,
			miniProgram.ShareDescription,
			miniProgram.Path,
			miniProgram.SharePicture,
			miniProgram.Name,
			miniProgram.Icon)
		if err != nil {
			m.info += fmt.Sprintf("%d,", code)
			return []error{err}
		}
	}
	return nil
}

func callWebhook(ctx context.Context, url string, workflow *MarketoWorkflow) error {
	data := map[string]interface{}{
		"accountId":  util.GetAccountId(ctx),
		"memberId":   workflow.MemberId.Hex(),
		"marketoId":  workflow.MarketoId.Hex(),
		"events":     workflow.GetEvents(),
		"workflowId": workflow.WorkflowId,
	}

	return component.CallWebhook(ctx, url, data)
}

func sendLineMessage(ctx context.Context, m *member.MemberDetailResponse, action WorkflowAction, businessId string) (error, string) {
	var social *origin.OriginInfo
	for _, info := range append(m.Socials, m.OriginFrom) {
		if info.Origin == constant.APP_LINE {
			social = info
			break
		}
	}
	if social == nil {
		return errors.New("member line social not found"), WORKFLOW_LOG_ERROR_MEMBER_SOCIAL_NOT_FOUND
	}

	req := &pb_account_channel.SendLineMessageRequest{
		To: &pb_account_channel.LineMessageReceiver{
			MemberId: m.Id,
			UserId:   social.OpenId,
		},
		ChannelId:  social.Channel,
		Business:   "marketo",
		BusinessId: businessId,
	}
	pbContent := make([]*pb_account_channel.LineMessageContent, len(action.LineMessage.Content))
	copier.Instance(nil).From(action.LineMessage.Content).CopyTo(&pbContent)
	placeholderValueMap := map[string]string{
		"{{Contact:Nickname}}": m.SocialMember,
	}
	for _, messageContent := range pbContent {
		if messageContent.Type == "text" {
			messageContent.Text = replaceLinePlaceholderValue(placeholderValueMap, messageContent.PlaceholderValueSetting.Rules, messageContent.Text, 5000)
		} else if messageContent.Type == "template-button" {
			limit := 60
			if messageContent.ButtonTemplate.Title == "" && messageContent.ButtonTemplate.Url == "" {
				limit = 160
			}
			messageContent.ButtonTemplate.Text = replaceLinePlaceholderValue(placeholderValueMap, messageContent.PlaceholderValueSetting.Rules, messageContent.ButtonTemplate.Text, limit)
		}
	}
	req.Content = pbContent
	_, err := proto_client.GetAccountChannelServiceClient().SendLineMessage(ctx, req)
	if err != nil {
		return err, WORKFLOW_LOG_ERROR_SEND_LINE_MESSAGE_FAILED
	}

	return nil, WORKFLOW_LOG_SUCCEED
}

func replaceLinePlaceholderValue(placeholderValueMap map[string]string, rules []*pb_account_channel.PlaceholderValueRule, text string, limit int) string {
	for _, rule := range rules {
		value := placeholderValueMap[rule.Key]
		if value == "" {
			value = rule.DefaultValue
		}
		// 如果替换动态参数后超出文本上限，则用默认值替代
		newText := strings.ReplaceAll(text, rule.Key, value)
		if len(newText) > limit {
			newText = strings.ReplaceAll(text, rule.Key, rule.DefaultValue)
		}
		text = newText
	}
	return text
}

func sendTemplateMessage(ctx context.Context, m *member.MemberDetailResponse, action WorkflowAction, extra map[string]string) (error, string) {
	template := action.Template
	socials := getMemberSocials(m)
	socials = validateSocials(socials)
	message := &component.SingleTargetTplMessage{
		TemplateMessage: generateTemplateMessage(action, m, extra),
	}

	for _, social := range socials {
		if social.Channel == template.ChannelId && social.OpenId != "" && social.Subscribed {
			message.OriginId = social.OpenId

			resp, err := component.WeConnect.SendTplMessageToSingle(ctx, social.Channel, message)
			if err != nil {
				return err, ""
			}

			return nil, fmt.Sprintf("%s:%s:%s", WORKFLOW_LOG_SUBMITTED, resp.Id, social.Channel)
		}
	}

	return errors.New("Invalid social channels"), WORKFLOW_LOG_ERROR_TEMPLATE_MESSAGE_UNSUBSCRIBED
}

func generateTemplateMessage(action WorkflowAction, m *member.MemberDetailResponse, extra map[string]string) component.TemplateMessage {
	template := action.Template
	message := component.TemplateMessage{
		TemplateId: template.Id,
		Url:        appendQueryToUrl(template.Url, extra),
	}

	if template.AppId != "" {
		message.MiniProgram = &component.TemplateMessageMiniProgram{
			AppId:    template.AppId,
			PagePath: appendQueryToUrl(template.PagePath, extra),
		}
	}

	replaceMap := generateDataReplaceMap(action.PropertyValueMaps, m)
	data := make(map[string]map[string]string)
	for _, args := range template.Data {
		data[args.Key] = map[string]string{
			"color": args.Color,
			"value": formatTemplateDataValue(args.Value, replaceMap),
		}
	}

	message.Data = data

	return message
}

func generateDataReplaceMap(propertyValueMaps []PropertyValueMap, m *member.MemberDetailResponse) map[string]string {
	result := make(map[string]string)
	for _, propertyValueMap := range propertyValueMaps {
		propertyId := propertyValueMap.PropertyId
		defaultValue := propertyValueMap.DefaultValue

		var (
			propertyValue = ""
		)

		if strings.HasPrefix(propertyId, "member.") {
			propertyValue, _ = getMemberFiledForPropertyValue(m, strings.Split(propertyId, ".")[1])
			if propertyValue == "" {
				propertyValue = defaultValue
			}
			propertyValue = propertyValueMap.DisplayRule.Fill(propertyValue)
			result[propertyId] = propertyValue
			continue
		}
		switch propertyId {
		case TEMPLATE_MESSAGE_DATA_KEY_NAME:
			if m.Name != "" {
				propertyValue = m.Name
			} else {
				propertyValue = defaultValue
			}
		case TEMPLATE_MESSAGE_DATA_KEY_NICKNAME:
			nickname := getNickname(m)
			if nickname != "" {
				propertyValue = nickname
			} else {
				propertyValue = defaultValue
			}
		case TEMPLATE_MESSAGE_DATA_KEY_GENDER:
			if value, ok := propertyValueMap.OptionValueMap[m.Gender]; ok {
				propertyValue = value
			} else {
				propertyValue = defaultValue
			}
		case TEMPLATE_MESSAGE_DATA_KEY_MEMBER_ID:
			propertyValue = m.Id
		default:
			for _, p := range m.Properties {
				if p.Property.PropertyId != propertyId {
					continue
				}
				propertyValue = getMemberPropertyValueInString(p)
				break
			}
			if propertyValue == "" {
				propertyValue = defaultValue
			}
		}
		propertyValue = propertyValueMap.DisplayRule.Fill(propertyValue)
		result[propertyId] = propertyValue
	}

	return result
}

func getMemberFiledForPropertyValue(m *member.MemberDetailResponse, field string) (result string, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = errors.New(WORKFLOW_LOG_ERROR_INVALID_MEMBER_PROPERTY)
			return
		}
	}()
	field = core_util.UppercaseFirst(field)
	switch field {
	case "CardName":
		result = m.Card.Name
	case "ActivatedAt":
		if m.ActivatedAt != 0 {
			result = time.Unix(m.ActivatedAt, 0).Format("2006/01/02 15:04:05")
		}
	case "CardId":
		result = m.Card.Id
	default:
		result = cast.ToString(core_util.GetValueByFiledName(m, field))
	}
	return
}

func getNickname(m *member.MemberDetailResponse) string {
	if m.OriginFrom != nil && m.OriginFrom.Nickname != "" {
		return m.OriginFrom.Nickname
	}
	return m.SocialMember
}

func getMemberPhone(m *member.MemberDetailResponse) (phone string) {
	phone = m.Phone
	for _, p := range m.Properties {
		// 适配味全绑定门店功能定制需求，https://gitlab.maiscrm.com/mai/impl/team-standard/home/<USER>/issues/658#note_4162994
		if p.Property.PropertyId == "contactPhone" {
			temp := p.GetValueString().Value
			if validators.CValidator.IsPhone(temp, nil) {
				phone = temp
			}
			return
		}
	}
	return
}

func formatTemplateDataValue(origin string, replaceMap map[string]string) string {
	matched, err := regexp.MatchString("{.*}", origin)
	if !matched || err != nil {
		return origin
	}

	for key, value := range replaceMap {
		re := regexp.MustCompile(fmt.Sprintf("{%s}", key))
		origin = re.ReplaceAllString(origin, value)
	}

	return origin
}

func updateCustomerProperty(ctx context.Context, memberId string, customer ActionCustomer) error {
	value := customer.Value
	if util.StrInArray(customer.PropertyType, &memberPropertyDateTypes) {
		value = cast.ToInt64(value) / 1000
	} else if customer.PropertyType == MEMBER_PROPERTY_ADDRESS {
		address := cast.ToStringSlice(value)
		value = append([]string{CHINA}, address...)
	} else if util.StrInArray(customer.PropertyType, &memberPropertyNumberTypes) {
		value = cast.ToFloat64(value)
	}

	return UpdateCustomerProperty(ctx, memberId, customer.PropertyId, value)
}

func updateMemberProperty(ctx context.Context, member *member.MemberDetailResponse, action ActionMember, description, marketoId string) (error, string) {
	switch action.Type {
	case ACTION_MEMBER_CARD:
		err := ProvideMemberCard(ctx, member.Id, action.CardId)
		if err != nil {
			mairpcErr := core_errors.ToMaiRPCError(err)
			if mairpcErr != nil {
				if mairpcErr.Code == member_codes.CardNotFound {
					return err, WORKFLOW_LOG_ERROR_CARD_NOT_FOUND
				}
			}

			return err, WORKFLOW_LOG_ERROR_FAILED_SEND_CARD
		}
		return nil, WORKFLOW_LOG_SUCCEED
	case ACTION_MEMBER_SCORE:
		score := action.Score
		if description == "" {
			description = action.Description
		}
		if score < 0 && (-score) > member.Score {
			score = -member.Score
		}

		if score == 0 {
			return nil, WORKFLOW_LOG_SUCCEED
		}

		resp, err := UpdateMemberScore(ctx, member.Id, score, description, marketoId)
		if err != nil {
			return returnErrorByScore(err, score)
		}

		if len(resp.FailedItems) > 0 {
			err := errors.New(fmt.Sprintf("Update member %s score failed", member.Id))
			return returnErrorByScore(err, score)
		}

		member.Score += score
		return nil, WORKFLOW_LOG_SUCCEED
	case ACTION_MEMBER_GROWTH:
		growth := action.Growth
		if growth == 0 {
			return nil, WORKFLOW_LOG_SUCCEED
		}
		resp, err := BatchUpdateMemberGrowth(ctx, []string{member.Id}, growth, description, marketoId)
		if err != nil || len(resp.FailedIds) > 0 {
			err := errors.New(fmt.Sprintf("Update member %s growth failed", member.Id))
			return err, WORKFLOW_LOG_ERROR_FAILED_MEMBER_INVALID
		}

		member.Growth += growth
		return nil, WORKFLOW_LOG_SUCCEED
	}

	return nil, ""
}

func returnErrorByScore(err error, score int64) (error, string) {
	if score > 0 {
		return err, WORKFLOW_LOG_ERROR_FAILED_ADD_SCORE
	} else {
		return err, WORKFLOW_LOG_ERROR_FAILED_DEDUCT_SCORE
	}
}

func updateMemberTags(ctx context.Context, memberId string, tag ActionTag) (error, string) {
	memberIds := []string{memberId}

	var (
		err       error
		errorType = WORKFLOW_LOG_SUCCEED
	)
	if tag.Type == TAG_TYPE_ADD {
		_, err = AddMemberTags(ctx, memberIds, tag.Tags)
		if err != nil {
			errorType = WORKFLOW_LOG_ERROR_FAILED_ADD_TAG
		}
	} else if tag.Type == TAG_TYPE_MINUS {
		_, err = DeleteMemberTags(ctx, memberIds, tag.Tags)
		if err != nil {
			errorType = WORKFLOW_LOG_ERROR_FAILED_REMOVE_TAG
		}
	}
	return err, errorType
}

func issueCoupons(ctx context.Context, marketoId string, member *member.MemberDetailResponse, couponIds []string) (error, []string, string) {
	if len(couponIds) == 0 {
		return nil, nil, WORKFLOW_LOG_SUCCEED
	}

	var (
		failedIds []string
		lastErr   *core_errors.MaiRPCError
	)

	membershipDiscountIds := []string{}
	for _, couponId := range couponIds {
		membershipDiscount, mairpcErr := issueCoupon(ctx, member.Id, couponId, marketoId)
		if mairpcErr != nil {
			failedIds = append(failedIds, couponId)
			lastErr = mairpcErr
		}
		if membershipDiscount != nil {
			membershipDiscountIds = append(membershipDiscountIds, membershipDiscount.Id)
		}
	}

	if len(failedIds) != 0 {
		log.Warn(ctx, "MarketoWorkflow issue coupons failed", log.Fields{
			"memberId":        member.Id,
			"failedCouponIds": failedIds,
		})
	}

	if lastErr == nil {
		return nil, membershipDiscountIds, WORKFLOW_LOG_SUCCEED
	}

	// for member batch operation, we need to return string errorType,
	// and the errorType vary from different issueCoupon error
	errorType := ""
	switch lastErr.Code {
	case coupon_codes.CouponNotEnough:
		errorType = WORKFLOW_LOG_ERROR_COUPON_NOT_ENOUGH
	case coupon_codes.CouponExceedLimit:
		errorType = WORKFLOW_LOG_ERROR_COUPON_EXCEED_LIMIT
	case coupon_codes.InvalidCouponId:
		errorType = WORKFLOW_LOG_ERROR_COUPON_NOT_FOUND
	case coupon_codes.CouponExpired:
		errorType = WORKFLOW_LOG_ERROR_COUPON_EXPIRED
	default:
		errorType = WORKFLOW_LOG_ERROR_ISSUE_COUPON_FAILED
	}

	return lastErr, membershipDiscountIds, errorType
}

func getMemberSocials(member *member.MemberDetailResponse) []*origin.OriginInfo {
	if member.OriginFrom == nil {
		return member.Socials
	}

	return append(member.Socials, member.OriginFrom)
}

func validateSocials(socials []*origin.OriginInfo) []*origin.OriginInfo {
	socialMap := core_util.MakeArrayMapper("Channel", socials)
	validSocials := []*origin.OriginInfo{}
	for _, sameSocials := range socialMap {
		if len(sameSocials) == 0 {
			continue
		}
		social := sameSocials[0].(*origin.OriginInfo)
		// 防止由于 mergeMember 导致 socials 存在多个 channel 相同的 social
		for _, s := range sameSocials {
			tempSocial := s.(*origin.OriginInfo)
			if tempSocial.Subscribed {
				social = tempSocial
				break
			}
		}
		if social.OpenId == "" || !social.Subscribed {
			continue
		}
		validSocials = append(validSocials, social)
	}
	return validSocials
}

func sendWechatMessage(ctx context.Context, action WorkflowAction, m *member.MemberDetailResponse, extra map[string]string) error {
	socials := getMemberSocials(m)
	switch action.MsgType {
	case MSGTYPE_TEXT, MSGTYPE_IMAGE:
		replaceMap := generateDataReplaceMap(action.PropertyValueMaps, m)
		content := formatTemplateDataValue(action.Content, replaceMap)
		message := component.Message{
			MsgType: action.MsgType,
			Content: content,
			Title:   action.Title,
			Url:     action.Url,
		}
		return sendWechatPlainMessage(ctx, socials, action.ChannelIds, message)
	case MSGTYPE_VOICE, MSGTYPE_VIDEO:
		message := component.Message{
			MsgType: action.MsgType,
			Title:   action.Title,
			MediaId: action.MediaId,
		}
		return sendWechatPlainMessage(ctx, socials, action.ChannelIds, message)
	case MSGTYPE_MPNEWS:
		return sendWechatMpnews(ctx, socials, action.Mpnews)
	case MSGTYPE_NEWS:
		return sendWechatNews(ctx, socials, action.News)
	case MSGTYPE_MINI_PROGRAM:
		return sendWechatMiniProgramPage(ctx, socials, action.ChannelIds, action.MiniPrograms, extra)
	}

	return nil
}

func (action *WorkflowAction) getWechatMessages(m *member.MemberDetailResponse, channel string) []component.Message {
	var messages []component.Message
	switch action.MsgType {
	case MSGTYPE_TEXT, MSGTYPE_IMAGE:
		replaceMap := generateDataReplaceMap(action.PropertyValueMaps, m)
		content := formatTemplateDataValue(action.Content, replaceMap)
		messages = append(messages, component.Message{
			MsgType: action.MsgType,
			Content: content,
			Title:   action.Title,
			Url:     action.Url,
		})
	case MSGTYPE_VOICE, MSGTYPE_VIDEO:
		messages = append(messages, component.Message{
			MsgType: action.MsgType,
			Title:   action.Title,
			MediaId: action.MediaId,
		})
	case MSGTYPE_MPNEWS:
		message := component.Message{
			MsgType:  MSGTYPE_MPNEWS,
			Articles: []component.Article{},
		}
		if materialId, found := getMaterialId(action.Mpnews, channel); found {
			message.MaterialId = materialId
			messages = append(messages, message)
		}
	case MSGTYPE_NEWS:
		message := component.Message{
			MsgType: MSGTYPE_NEWS,
		}
		if materialId, articles, found := getArticles(action.News, channel); found {
			message.Articles = articles
			message.MaterialId = materialId
			messages = append(messages, message)
		}
	case MSGTYPE_MINI_PROGRAM:
		message := component.Message{
			MsgType: MSGTYPE_MINI_PROGRAM,
		}
		for _, miniProgram := range action.MiniPrograms {
			message.AppId = miniProgram.AppId
			message.Title = miniProgram.Title
			message.ThumbImageUrl = miniProgram.ThumbImageUrl
			message.PagePath = miniProgram.PagePath
			messages = append(messages, message)
		}
	}
	return messages
}

func sendWechatPlainMessage(ctx context.Context, socials []*origin.OriginInfo, channelIds []string, message component.Message) (err error) {
	valid := false
	hasSucceedSocial := false
	for _, social := range socials {
		if !util.StrInArray(social.Channel, &channelIds) {
			continue
		}
		valid = true
		message.ToUser = social.OpenId
		err = component.WeConnect.SendWechatMessage(ctx, social.Channel, social.OpenId, &message)
		if err == nil {
			hasSucceedSocial = true
		}
	}
	if hasSucceedSocial {
		err = nil
		return
	}
	if !valid {
		err = errors.New("Invalid social channels")
		return
	}
	return
}

func sendWechatMpnews(ctx context.Context, socials []*origin.OriginInfo, mpnews []Mpnews) (err error) {
	valid := false
	hasSucceedSocial := false
	for _, social := range socials {
		message := component.Message{
			MsgType:  MSGTYPE_MPNEWS,
			Articles: []component.Article{},
		}

		if materialId, found := getMaterialId(mpnews, social.Channel); found {
			valid = true
			message.MaterialId = materialId
			err = component.WeConnect.SendWechatMessage(ctx, social.Channel, social.OpenId, &message)
			if err == nil {
				hasSucceedSocial = true
			}
		}
	}
	if hasSucceedSocial {
		err = nil
		return
	}
	if !valid {
		return errors.New("Invalid social channels")
	}
	return
}

func sendWechatNews(ctx context.Context, socials []*origin.OriginInfo, news []News) (err error) {
	valid := false
	hasSucceedSocial := false
	for _, social := range socials {
		message := component.Message{
			MsgType: MSGTYPE_NEWS,
		}

		if materialId, articles, found := getArticles(news, social.Channel); found {
			valid = true
			message.Articles = articles
			message.MaterialId = materialId
			err = component.WeConnect.SendWechatMessage(ctx, social.Channel, social.OpenId, &message)
			if err == nil {
				hasSucceedSocial = true
			}
		}
	}
	if hasSucceedSocial {
		err = nil
		return
	}
	if !valid {
		return errors.New("Invalid social channels")
	}
	return
}

func sendWechatMiniProgramPage(ctx context.Context, socials []*origin.OriginInfo, channelIds []string, miniPrograms []MiniPrograms, extra map[string]string) (err error) {
	valid := false
	hasSucceedSocial := false
	for _, social := range socials {
		message := component.Message{
			MsgType: MSGTYPE_MINI_PROGRAM,
		}
		if util.StrInArray(social.Channel, &channelIds) {
			valid = true
			for _, miniProgram := range miniPrograms {
				message.AppId = miniProgram.AppId
				message.Title = miniProgram.Title
				message.ThumbImageUrl = miniProgram.ThumbImageUrl
				message.PagePath = appendQueryToUrl(miniProgram.PagePath, extra)
				err = component.WeConnect.SendWechatMessage(ctx, social.Channel, social.OpenId, &message)
				if err == nil {
					hasSucceedSocial = true
				}
			}
		}
	}
	if hasSucceedSocial {
		err = nil
		return
	}
	if !valid {
		return errors.New("Invalid social channels")
	}
	return
}

func appendQueryToUrl(u string, params map[string]string) string {
	if len(params) == 0 || u == "" {
		return u
	}
	result, err := url.Parse(u)
	if err != nil {
		return u
	}
	query := result.Query()
	for k, v := range params {
		query.Set(k, v)
	}
	result.RawQuery = query.Encode()
	return result.String()
}

func getArticles(news []News, channelId string) (materialId string, articles []component.Article, found bool) {
	for _, item := range news {
		if item.ChannelId == channelId {
			return item.MaterialId, formatArticles(item.Articles), true
		}
	}

	return
}

func formatArticles(articles []Article) []component.Article {
	wechatArticles := []component.Article{}
	for _, article := range articles {
		wechatArticles = append(wechatArticles, component.Article{
			Title:       article.Title,
			Description: article.Description,
			Url:         article.Url,
			SourceUrl:   article.SourceUrl,
		})
	}

	return wechatArticles
}

func getMaterialId(mpnews []Mpnews, channelId string) (materialId string, found bool) {
	for _, item := range mpnews {
		if item.ChannelId == channelId {
			return item.MaterialId, true
		}
	}

	return "", false
}

func (m *MarketoWorkflow) processControl(ctx context.Context, w Workflow) (isDelayed bool, branch string) {
	control := w.Control
	controlType := control.Type
	if control.SubType != "" {
		controlType = control.SubType
	}
	switch controlType {
	case WORKFLOW_CONTROL_TYPE_EVENT:
		return m.processControlEvent(control.Event)
	case WORKFLOW_CONTROL_TYPE_PROPERTY:
		if matched, branch := m.processControlProperty(ctx, control.Property); matched {
			return false, branch
		} else {
			return false, control.Property.GetDefaultBranch()
		}
	case WORKFLOW_CONTROL_TYPE_WAITING:
		return m.processControlWaiting(control.Waiting), ""
	case WORKFLOW_CONTROL_TYPE_SOCIAL:
		return m.processControlSocial(ctx, control.Social, w)
	case WORKFLOW_CONTROL_TYPE_MEMBER:
		matched, branch := m.processControlMember(control.Member)
		// return default branch if there is no matched branch
		if !matched {
			return false, Rules(control.Member.Rules).GetDefaultBranch()
		}
		return false, branch
	case WORKFLOW_CONTROL_TYPE_TAG:
		matched, branch := m.processControlTag(ctx, control.Tag)
		// return default branch if there is no matched branch
		if !matched {
			return false, Rules(control.Tag.Rules).GetDefaultBranch()
		}
		return false, branch
	case WORKFLOW_CONTROL_TYPE_MEMBER_GROUP:
		matched, branch := m.processControlMemberGroup(ctx, control.MemberGroup)
		if !matched {
			return false, Rules(control.MemberGroup.Rules).GetDefaultBranch()
		}
		return false, branch
	case WORKFLOW_CONTROL_TYPE_TEST:
		return m.processControlTest(ctx, w)
	case WORKFLOW_CONTROL_TYPE_TEST_GOAL:
		return m.processControlTestGoal(ctx, w)
	case WORKFLOW_CONTROL_TYPE_PARENT_RESULT:
		return m.processControlParentResult(ctx, w)
	}

	return false, ""
}

func (m *MarketoWorkflow) processControlParentResult(ctx context.Context, w Workflow) (bool, string) {
	parentResult, err := m.getParentResult(ctx, w)
	if err != nil {
		return false, ""
	}
	if parentResult.Status == WORKFLOW_LOG_SUCCEED {
		_, branch := matchBool(true, w.Control.ParentResult.Rules)
		return false, branch
	}
	_, branch := matchBool(false, w.Control.ParentResult.Rules)
	return false, branch
}

// 从数据库中获取最新的执行结果
func (m *MarketoWorkflow) getParentResult(ctx context.Context, w Workflow) (WorkflowLog, error) {
	result := WorkflowLog{}
	marketoWorkflow := MarketoWorkflow{}
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = m.Id
	err := extension.DBRepository.FindOne(ctx, C_MARKETO_WORK_FLOW, selector, &marketoWorkflow)
	if err != nil {
		return result, err
	}
	for _, log := range marketoWorkflow.WorkflowLogs {
		if w.ParentId == log.WorkflowId {
			result = log
		}
	}
	return result, nil
}

func filterSocials(socials []*origin.OriginInfo, workflowSocial WorkflowSocial) []string {
	channels := []string{}
	filterFunc := getSocialFilterFunc(workflowSocial.ChannelType)
	if filterFunc == nil {
		return channels
	}

	allowedSocials := []string{
		constant.WEAPP,
	}

	if !util.StrInArray(workflowSocial.ChannelType, &[]string{SOCIAL_CHANNEL_TYPE_USED, SOCIAL_CHANNEL_TYPE_UNUSED}) {
		allowedSocials = append(allowedSocials, constant.WECHAT)
	}

	if len(workflowSocial.Origins) > 0 {
		allowedSocials = workflowSocial.Origins
	}
	for _, social := range socials {
		// 如果是对 origin 的筛选则不需要过滤 origin
		if workflowSocial.Field == "origin" && filterFunc(social) {
			channels = append(channels, getSoicalFieldValue(social, workflowSocial.Field))
			continue
		}
		if util.StrInArray(social.Origin, &allowedSocials) && filterFunc(social) {
			channels = append(channels, getSoicalFieldValue(social, workflowSocial.Field))
		}
	}

	return channels
}

func getSoicalFieldValue(social *origin.OriginInfo, field string) string {
	switch field {
	case "origin":
		return social.Origin
	case "originScene":
		return social.OriginScene
	case "firstOriginScene":
		return social.FirstOriginScene
	case "firstSubscribeTime":
		return strconv.FormatInt(social.FirstSubscribeTime, 10)
	}
	return social.Channel
}

func getSocialFilterFunc(channelType string) func(*origin.OriginInfo) bool {
	switch channelType {
	case SOCIAL_CHANNEL_TYPE_AUTHORIZED:
		return func(social *origin.OriginInfo) bool {
			// https://gitlab.maiscrm.com/mai/home/<USER>/11877
			if social.Subscribed || (!social.Subscribed && social.SubscribeTime > 0) {
				return false
			}

			return social.Authorized
		}
	case SOCIAL_CHANNEL_TYPE_UNAUTHORIZED:
		return func(social *origin.OriginInfo) bool {
			return !social.Authorized && social.AuthorizeTime > 0
		}
	case SOCIAL_CHANNEL_TYPE_SUBSCRIBED:
		return func(social *origin.OriginInfo) bool {
			return social.Subscribed
		}
	case SOCIAL_CHANNEL_TYPE_UNSUBSCRIBED:
		return func(social *origin.OriginInfo) bool {
			return !social.Subscribed && social.SubscribeTime > 0
		}
	// 过滤出已关注或者取消关注的渠道
	case SOCIAL_CHANNEL_TYPE_NOT_SUBSCRIBED:
		return func(social *origin.OriginInfo) bool {
			return social.SubscribeTime > 0
		}
	// 获取用户使用过的的所有渠道
	case SOCIAL_CHANNEL_TYPE_USED, SOCIAL_CHANNEL_TYPE_UNUSED:
		return func(social *origin.OriginInfo) bool {
			return true
		}

	// 获取用户授权过的所有渠道
	case SOCIAL_CHANNEL_TYPE_NOT_AUTHORIZED:
		return func(social *origin.OriginInfo) bool {
			return social.AuthorizeTime > 0
		}
	}

	return nil
}

func getStaffMembers(ctx context.Context, memberId string, channelType string) []*ec_staff.StaffMember {
	req := &ec_staff.GetStaffMembersRequest{
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: 1000,
		},
		MemberIds:      []string{memberId},
		RelationStatus: []string{"added", "bound"},
	}
	// 处理用户删除企业微信好友的情况
	if channelType == SOCIAL_CHANNEL_TYPE_UNSUBSCRIBED {
		req.RelationStatus = []string{"deleted"}
	}
	rep, err := proto_client.GetEcStaffServiceClient().GetStaffMembers(ctx, req)
	if err != nil {
		return []*ec_staff.StaffMember{}
	}

	return rep.Items
}

func filterStaffMembers(members []*ec_staff.StaffMember, social WorkflowSocial) []string {
	memberIds := []string{}
	switch social.Field {
	case "storeId":
		for _, member := range members {
			memberIds = append(memberIds, member.StoreId)
		}
	case "addWay":
		for _, member := range members {
			memberIds = append(memberIds, member.AddWay)
		}
	case "staffDrainageQrcodeId":
		for _, member := range members {
			memberIds = append(memberIds, member.State)
		}
	case "staffNo":
		for _, member := range members {
			memberIds = append(memberIds, member.StaffNo)
		}
	}
	return memberIds
}

func getChatGroups(ctx context.Context, memberId string, channelType string) []*pb_ec_groupchat.Groupchat {
	req := &pb_ec_groupchat.ListGroupchatsRequest{
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: 1000,
		},
		MemberId: memberId,
	}
	// 处理用户退出企业微信社群的情况
	if channelType == SOCIAL_CHANNEL_TYPE_UNAUTHORIZED {
		req.IsMemberDeleted = true
	}
	rep, err := proto_client.GetEcGroupchatServiceClient().ListGroupchats(ctx, req)
	if err != nil {
		return []*pb_ec_groupchat.Groupchat{}
	}

	return rep.Items
}

func filterChatGroups(groups []*pb_ec_groupchat.Groupchat, social WorkflowSocial) []string {
	groupIds := []string{}
	switch social.Field {
	case "chatId":
		for _, group := range groups {
			groupIds = append(groupIds, group.ChatId)
		}
	case "chatGroupId":
		for _, group := range groups {
			groupIds = append(groupIds, group.GroupId)
		}
	case "chatTag":
		for _, group := range groups {
			groupIds = append(groupIds, group.Tags...)
		}
	case "chatOwner":
		for _, group := range groups {
			if group.Owner != nil {
				groupIds = append(groupIds, group.Owner.StaffNo)
			}
		}
	}

	return groupIds
}

func getChatMembers(ctx context.Context, memberId string, channelType string) []*pb_ec_groupchat.GroupchatMember {
	req := &pb_ec_groupchat.ListGroupchatMembersRequest{
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: 1000,
		},
		MemberId: memberId,
	}
	// 处理用户退出企业微信社群的情况
	if channelType == SOCIAL_CHANNEL_TYPE_NOT_AUTHORIZED {
		req.IsMemberDeleted = true
	}
	rep, err := proto_client.GetEcGroupchatServiceClient().ListGroupchatMembers(ctx, req)
	if err != nil {
		return []*pb_ec_groupchat.GroupchatMember{}
	}

	return rep.Items
}

func filterChatMembers(members []*pb_ec_groupchat.GroupchatMember, social WorkflowSocial) []string {
	chatMembers := []string{}
	if social.Field == "groupDrainageQrcodeId" {
		for _, member := range members {
			chatMembers = append(chatMembers, member.State)
		}
	}

	return chatMembers
}

func (m *MarketoWorkflow) processControlSocial(ctx context.Context, social WorkflowSocial, w Workflow) (isDelayed bool, branch string) {
	channels := []string{}
	if social.Field == "" || social.Field == "origin" {
		channels = filterSocials(getMemberSocials(m.memberDetail), social)
	} else {
		// 企业微信好友相关
		if util.StrInArray(social.Field, &[]string{"storeId", "addWay", "staffDrainageQrcodeId", "staffNo"}) {
			channels = filterStaffMembers(getStaffMembers(ctx, m.memberDetail.Id, social.ChannelType), social)
		}
		// 企业微信社群相关
		if util.StrInArray(social.Field, &[]string{"chatId", "chatGroupId", "chatTag", "chatOwner"}) {
			channels = filterChatGroups(getChatGroups(ctx, m.memberDetail.Id, social.ChannelType), social)
		}
		if social.Field == "groupDrainageQrcodeId" {
			channels = filterChatMembers(getChatMembers(ctx, m.memberDetail.Id, social.ChannelType), social)
		}
	}

	switch social.ChannelType {
	case SOCIAL_CHANNEL_TYPE_AUTHORIZED, SOCIAL_CHANNEL_TYPE_UNAUTHORIZED, SOCIAL_CHANNEL_TYPE_SUBSCRIBED, SOCIAL_CHANNEL_TYPE_UNSUBSCRIBED, SOCIAL_CHANNEL_TYPE_USED:
		if len(channels) == 0 {
			branch = Rules(social.Rules).GetDefaultBranch()
			return
		}
		if social.Field == "firstSubscribeTime" {
			_, branch = matchNumber(channels, social.Rules)
			return
		}
		_, branch = matchStringArray(channels, social.Rules)
		return
	case SOCIAL_CHANNEL_TYPE_NOT_AUTHORIZED, SOCIAL_CHANNEL_TYPE_NOT_SUBSCRIBED:
		for _, rule := range social.Rules {
			// 不限的情况
			if social.Field == "" {
				ok := share_model.MatchArrays(channels, rule)
				if !ok || len(channels) == 0 {
					branch = rule.Branch
					return
				}
			}
			if len(channels) == 0 {
				branch = Rules(social.Rules).GetDefaultBranch()
				return
			}
			ok := share_model.MatchArrays(channels, rule)
			if !ok {
				branch = rule.Branch
				return
			}
		}

		branch = Rules(social.Rules).GetDefaultBranch()
		return
	case SOCIAL_CHANNEL_TYPE_UNUSED:
		if len(channels) == 0 {
			branch = Rules(social.Rules).GetDefaultBranch()
			return
		}
		for _, rule := range social.Rules {
			ok := share_model.MatchArrays(channels, rule)
			if ok {
				branch = rule.Branch
				return
			}
		}
	}
	return
}

func (m *MarketoWorkflow) processControlProperty(ctx context.Context, property ControlProperty) (bool, string) {
	if property.Type != "" {
		switch property.Type {
		case SPECIAL_PROPERTY_PROPVINCE:
			return matchString(m.memberDetail.Province, property.Rules)
		case SPECIAL_PROPERTY_CITY:
			return matchString(m.memberDetail.City, property.Rules)
		case SPECIAL_PROPERTY_DISTRICT:
			return matchString(m.memberDetail.District, property.Rules)
		case SPECIAL_PROPERTY_CREATEDAT:
			dayTimestamp := util.GetDayStartTimestamp(time.Unix(m.memberDetail.CreatedAt, 0))
			return matchDate(dayTimestamp*1000, property.Rules, "")
		case SPECIAL_PROPERTY_SCORE:
			return matchNumber(m.memberDetail.Score, property.Rules)
		case SPECIAL_PROPERTY_SOCIAL:
			social := getSocialMatchValue(m.memberDetail.OriginFrom)
			return matchString(social, property.Rules)
		case SPECIAL_PROPERTY_SOCIAL_IDENTITY:
			identities := []string{}
			identities = append(identities, getSocialMatchValue(m.memberDetail.OriginFrom))
			for _, social := range m.memberDetail.Socials {
				identities = append(identities, getSocialMatchValue(social))
			}
			rules := property.Rules
			sort.SliceStable(rules, func(i, j int) bool {
				return rules[i].Branch < rules[j].Branch
			})
			return matchStringArray(identities, rules)
		case SPECIAL_PROPERTY_SOURCE:
			if m.memberDetail.Source == "" {
				return true, getZeroPropertyBranch(property.Rules)
			}
			return matchString(m.memberDetail.Source, property.Rules)
		case SPECIAL_PROPERTY_ORIGINAL_CHANNEL:
			channelId := ""
			for _, social := range append(m.memberDetail.Socials, m.memberDetail.OriginFrom) {
				if social.IsOriginal {
					channelId = social.Channel
				}
			}
			return matchString(channelId, property.Rules)
		case SPECIAL_PROPERTY_BOUND_STAFF:
			staffId := ""
			req := &ec_staff.GetStaffMembersRequest{
				ListCondition: &request.ListCondition{
					Page:    1,
					PerPage: 1,
				},
				MemberIds:      []string{m.memberDetail.Id},
				RelationStatus: []string{"bound"},
			}
			resp, err := proto_client.GetEcStaffServiceClient().GetStaffMembers(ctx, req)
			if err == nil && len(resp.Items) > 0 {
				staffId = resp.Items[0].StaffId
			}
			return matchString(staffId, property.Rules)
		}
	}

	if property.Id.Valid() || property.Name != "" {
		for _, mp := range m.memberDetail.Properties {
			if property.Name != "" && mp.Property.Name != property.Name {
				continue
			}
			if property.Id.Valid() && mp.Property.Id != property.Id.Hex() {
				continue
			}
			if isEmpty(property, mp) {
				return true, getZeroPropertyBranch(property.Rules)
			} else {
				return matchRule(property, property.Rules, mp)
			}
		}

		return true, getZeroPropertyBranch(property.Rules)
	}

	return false, ""
}

func (m *MarketoWorkflow) processControlMember(controlMember ControlMember) (bool, string) {
	member := m.memberDetail
	rules := controlMember.Rules

	switch controlMember.Field {
	case WORKFLOW_CONTROL_MEMBER_FIELD_CARD:
		// member has no member card
		if member.Card == nil || member.Card.Id == "" {
			return true, getZeroPropertyBranch(rules)
		}

		// member has member card
		cardId := member.Card.Id
		return matchString(cardId, rules)
	case WORKFLOW_CONTROL_MEMBER_FIELD_SCORE:
		score := member.Score
		return matchNumber(score, rules)
	case WORKFLOW_CONTROL_MEMBER_FIELD_ACTIVATED_AT:
		// member haven't been activated yet
		if !member.IsActivated {
			return true, getZeroPropertyBranch(rules)
		}

		// member is activated
		timestamp := util.GetDayStartTimestamp(time.Unix(member.ActivatedAt, 0)) * 1000
		return matchDate(timestamp, rules, "")
	case WORKFLOW_CONTROL_MEMBER_FIELD_ANNUAL_COST_SCORE:
		score := member.AnnualCostScore
		return matchNumber(score, rules)
	case WORKFLOW_CONTROL_MEMBER_FIELD_ANNUAL_ACCUMULATE_SCORE:
		score := member.AnnualAccumulatedScore
		return matchNumber(score, rules)
	case WORKFLOW_CONTROL_MEMBER_FIELD_TOTAL_SCORE:
		score := member.TotalScore
		return matchNumber(score, rules)
	case WORKFLOW_CONTROL_MEMBER_FIELD_TOTAL_COST_SCORE:
		score := member.TotalCostScore
		return matchNumber(score, rules)
	case WORKFLOW_CONTROL_MEMBER_FIELD_LEVEL:
		level := member.Level
		return matchString(level, rules)
	case WORKFLOW_CONTROL_MEMBER_FIELD_GROWTH:
		growth := member.Growth
		return matchNumber(growth, rules)
	case WORKFLOW_CONTROL_MEMBER_FIELD_ACTIVATION_SOURCE:
		activationSource := member.ActivationSource
		if activationSource == "" {
			return true, getZeroPropertyBranch(rules)
		}
		return matchString(activationSource, rules)
	case WORKFLOW_CONTROL_MEMBER_FIELD_PAID_MEMBER:
		var isPaidMember bool
		for _, paidCard := range member.PaidCards {
			if paidCard.CardType != "paidMember" {
				continue
			}
			expireAt := core_util.ParseRFC3339(paidCard.ExpireAt)
			isPaidMember = time.Now().Before(expireAt)
		}
		return matchBool(isPaidMember, rules)
	case WORKFLOW_CONTROL_MEMBER_FIELD_ACTIVATION_STORE, WORKFLOW_CONTROL_MEMBER_FIELD_ACTIVATION_STAFF:
		value := ""
		for _, property := range member.Properties {
			if property.Property.PropertyId == controlMember.Field {
				value = property.GetValueString().GetValue()
			}
		}
		return matchString(value, rules)
	case WORKFLOW_CONTROL_MEMBER_FIELD_ACTIVATION_CHANNEL:
		channelId := ""
		for _, social := range append(member.Socials, member.OriginFrom) {
			if social.IsActivationChannel {
				channelId = social.Channel
				break
			}
		}
		return matchString(channelId, rules)
	case WORKFLOW_CONTROL_MEMBER_FIELD_SUBSCRIBED_CHANNEL:
		var channelIds []string
		for _, social := range append(member.Socials, member.OriginFrom) {
			if social.Subscribed && social.Origin != constant.WECONTACT {
				channelIds = append(channelIds, social.Channel)
				continue
			}
			if social.Origin == constant.WECHAT && social.Extra != "" {
				channelIds = append(channelIds, social.Channel)
			}
		}
		return matchStringArray(channelIds, rules)
	}

	return false, ""
}

func (m *MarketoWorkflow) processControlTag(ctx context.Context, controlTag ControlTag) (bool, string) {
	var (
		labelNames     []string
		needFetchLabel = false
	)
	for _, rule := range controlTag.MemberLabelRules {
		// 只有存在模型标签时才调接口
		if len(rule.MemberLabels) > 0 {
			needFetchLabel = true
			break
		}
	}
	if needFetchLabel {
		// 由于这种场景需要支持非二元多分支，所以需要获取全部模型标签及规则标签
		// 这里直接用名字比较，因为 fieldValue 会有各种类型，例如字符串、整数、数组等，直接使用拼接好的名字比较更方便
		labels, err := component.Bigdata.GetLabelsInMember(ctx, m.memberDetail.Id)
		if err != nil {
			log.Warn(ctx, "Failed to get member labels", log.Fields{
				"errorMessage":      err.Error(),
				"marketoWorkflowId": m.Id.Hex(),
				"memberId":          m.memberDetail.Id,
			})
			m.Status = WORKFLOW_STATUS_ERROR
			return false, ""
		}
		for _, label := range labels {
			labelNames = append(labelNames, fmt.Sprintf("%s_%s", label.Name, label.ValueCN))
		}
		labels, err = component.Bigdata.GetRuleLabelsInMember(ctx, m.memberDetail.Id)
		if err != nil {
			log.Warn(ctx, "Failed to get member rule labels", log.Fields{
				"errorMessage":      err.Error(),
				"marketoWorkflowId": m.Id.Hex(),
				"memberId":          m.memberDetail.Id,
			})
			m.Status = WORKFLOW_STATUS_ERROR
			return false, ""
		}
		for _, label := range labels {
			labelNames = append(labelNames, fmt.Sprintf("%s_%s", label.Name, label.ValueCN))
		}
	}
	return matchStringArray(append(m.memberDetail.Tags, labelNames...), controlTag.GetAllRules())
}

func (m *MarketoWorkflow) processControlMemberGroup(ctx context.Context, controTag ControlMemberGroup) (bool, string) {
	rules := controTag.Rules
	var (
		groupIds       []string
		memberGroupIds []string
	)
	for _, rule := range controTag.Rules {
		groupIds = append(groupIds, cast.ToStringSlice(rule.Value)...)
	}
	groupIds = util.StrArrayUnique(groupIds)
	if len(groupIds) > 0 && util.StrInArray(viper.GetString("env"), &[]string{
		"production",
		"staging",
	}) {
		resp, err := proto_client.GetMemberServiceClient().CheckMemberIdsByGroupV2(ctx, &member.CheckMemberIdsByGroupRequest{
			MemberIds:       []string{m.memberDetail.Id},
			DynamicGroupIds: groupIds,
			StaticGroupIds:  groupIds,
		})
		if err != nil {
			log.Warn(ctx, "Failed to check member groups", log.Fields{
				"errorMessage":      err.Error(),
				"marketoWorkflowId": m.Id.Hex(),
				"memberId":          m.memberDetail.Id,
			})
			m.Status = WORKFLOW_STATUS_ERROR
			return false, ""
		}
		if result := resp.Result[m.memberDetail.Id]; result != nil {
			memberGroupIds = append(result.DynamicGroupIds, result.StaticGroupIds...)
		}
		return matchStringArray(memberGroupIds, rules)
	}
	memberGroups, err := getMemberGroupByMember(ctx, m.memberDetail.Id)
	if err != nil {
		log.Warn(ctx, "Failed to get member group by member", log.Fields{
			"errorMessage":      err.Error(),
			"marketoWorkflowId": m.Id.Hex(),
			"memberId":          m.memberDetail.Id,
		})
		m.Status = WORKFLOW_STATUS_ERROR
		return false, ""
	}
	return matchStringArray(core_util.ExtractArrayStringField("Id", memberGroups.Items), rules)
}

func (m *MarketoWorkflow) processControlTest(ctx context.Context, w Workflow) (bool, string) {
	var (
		testStats MarketoTestStats
		err       error
		subErr    error
	)
	test := w.Control.Test
	nextBranch := getNextTestBranch(test.Rules)
	testStats, err = CMarketoTestStats.incBranchTestTimes(ctx, test.StatsId, w, nextBranch, 1)
	defer func() {
		if err == nil {
			m.incTestStatsId = test.StatsId
		}
		m.testBranch = nextBranch
	}()
	// 设置一直执行，不需要等待，直接执行下一个分支
	if test.LimitTimes == 0 {
		return false, nextBranch
	}
	if err == nil {
		// 成功更新测试次数后达到结束条件，设置测试状态为即将结束
		if testStats.TestTimes == test.LimitTimes {
			subErr = CMarketoTestStats.SetStatus(ctx, test.StatsId, TEST_STATUS_PRE_END)
			if err != nil {
				log.Warn(ctx, "failed to set stats status to preEnd", log.Fields{
					"errorMessage":      subErr.Error(),
					"marketoWorkflowId": m.Id.Hex(),
					"workflowId":        w.Id,
					"statsId":           test.StatsId.Hex(),
				})
				m.Status = WORKFLOW_STATUS_ERROR
				return true, ""
			}
		}
	} else {
		testStats, subErr = CMarketoTestStats.GetById(ctx, test.StatsId)
		if subErr != nil {
			log.Warn(ctx, "failed to get test stats", log.Fields{
				"errorMessage":      subErr.Error(),
				"marketoWorkflowId": m.Id.Hex(),
				"statsId":           test.StatsId.Hex(),
				"workflowId":        w.Id,
			})
			m.Status = WORKFLOW_STATUS_ERROR
			return true, ""
		}
		// 如果增加测试数量报错，则说明测试数量可能已达到设置的测试数量，需要进一步验证测试节点统计信息
		if testStats.TestTimes < testStats.LimitTimes {
			// 不是因为达到测试上限报错，是预期之外的错误，需要记录并返回
			log.Warn(ctx, "failed to inc testTimes", log.Fields{
				"marketoWorkflowId": m.Id.Hex(),
				"workflow":          w,
				"errorMessage":      err.Error(),
			})
			m.Status = WORKFLOW_STATUS_ERROR
			return true, ""
		}
	}

	// 如果测试已完成且已设置最终使用的分支，不需要等待，直接执行最终使用的分支
	if testStats.Status == TEST_STATUS_END {
		return false, testStats.EndUseBranch
	}
	// 已达到测试人数，但测试未结束，则后续节点需要等测试结束才可执行，设置等待事件
	if testStats.Status == TEST_STATUS_PRE_END {
		m.Status = WORKFLOW_STATUS_WAITING_TEST
		return true, ""
	}
	return false, nextBranch
}

func (m *MarketoWorkflow) processControlTestGoal(ctx context.Context, w Workflow) (isDelay bool, branch string) {
	testGoal := w.Control.TestGoal
	defer func() {
		// 获取进入 AB 测试时增加过测试次数的统计 id 和测试分支
		// 确保完成目标增加的相关计数是进入 AB 测试时的 marketoTestStats
		// 防止达成测试目标前 AB 测试被修改，增加相关计数到新的统计中，影响前后两次统计的准确性
		testStatsId, testBranch := m.getIncTestStatsIdAndBranch(testGoal.TestId)
		if !testStatsId.Valid() {
			return
		}
		isComplete := branch == testGoal.Rules[0].Branch
		if testBranch == "" {
			m.Status = WORKFLOW_STATUS_ERROR
			log.Warn(ctx, "failed to get test branch", log.Fields{
				"testId":     w.Control.TestId,
				"workflowId": w.Id,
			})
			isDelay = true
			branch = ""
			return
		}

		err := CMarketoTestStats.incBranchTouchGoalCount(ctx, testStatsId, testBranch, 1, isComplete)
		if err != nil {
			log.Warn(ctx, "failed to inc test branch complete count", log.Fields{
				"workflowId":   w.Id,
				"testId":       testGoal.TestId,
				"errorMessage": err.Error(),
			})
			isDelay = true
			branch = ""
			return
		}
	}()
	if m.hadTestGoalCompletedLog(w.Id) || m.isTestGoalCompleted {
		m.isTestGoalCompleted = false
		return false, testGoal.Rules[0].Branch
	}
	return false, Rules(testGoal.Rules).GetDefaultBranch()
}

func GetEfficientBranch(branchStats []TestBranchStats) string {
	if len(branchStats) == 0 {
		return ""
	}
	branch := ""
	completeProportion := 0.0
	for _, stats := range branchStats {
		if branch == "" {
			branch = stats.Branch
		}
		p := float64(stats.CompleteCount) / float64(stats.TestTimes)
		if p > completeProportion {
			completeProportion = p
			branch = stats.Branch
		}
	}
	return branch
}

func getNextTestBranch(rules []ControlRule) string {
	n := uint64(rand.New(rand.NewSource(time.Now().UnixNano())).Int63n(10000))
	for _, rule := range rules {
		proportion := cast.ToUint64(rule.Value)
		if n < proportion {
			return rule.Branch
		}
		n -= proportion
	}
	return rules[0].Branch
}

func getZeroPropertyBranch(rules []ControlRule) string {
	if !Rules(rules).HasNullOperator() {
		return Rules(rules).GetDefaultBranch()
	}

	return Rules(rules).GetNullBranch()
}

func isEmpty(controlProperty ControlProperty, property *member.PropertyDetail) bool {
	if util.StrInArray(controlProperty.Type, &memberPropertyTextTypes) || controlProperty.Type == MEMBER_PROPERTY_INPUT {
		return property.GetValueString().Value == ""
	}

	if util.StrInArray(controlProperty.Type, &memberPropertyDateTypes) {
		return property.GetValueDate().Value == 0
	}

	if util.StrInArray(controlProperty.Type, &memberPropertyNumberTypes) {
		return property.GetValueNumber() == nil
	}

	if util.StrInArray(controlProperty.Type, &memberPropertyStringArrayTypes) {
		return len(property.GetValueArray().Value) == 0
	}

	if util.StrInArray(controlProperty.Type, &memberPropertyBoolTypes) {
		return false
	}

	if controlProperty.Type == MEMBER_PROPERTY_ADDRESS {
		addressSlice := trimAddressCountry(property.GetValueArray().Value)
		return getAddressByType(addressSlice, controlProperty.LocationType) == ""
	}

	if controlProperty.Type == MEMBER_PROPERTY_LOCATION {
		addressSlice := trimAddressCountry(property.GetAddressCache().Value)
		return getAddressByType(addressSlice, controlProperty.LocationType) == ""
	}

	return true
}

func getAddressByType(addressSlice []string, locationType string) string {
	var address string
	if idx, ok := locationTypeIndex[locationType]; ok {
		if len(addressSlice) >= (idx + 1) {
			address = addressSlice[idx]
		}
	} else {
		// 兼容没有 locationType 的版本
		address = strings.Join(addressSlice, "")
	}

	return address
}

func getSocialMatchValue(social *origin.OriginInfo) string {
	if social != nil {
		if social.Channel != "" {
			return social.Channel
		} else {
			return social.Origin
		}
	}

	return ""
}

func matchRule(controlProperty ControlProperty, rules []ControlRule, property *member.PropertyDetail) (bool, string) {
	propertyType := controlProperty.Type
	if util.StrInArray(propertyType, &memberPropertyTextTypes) {
		return matchString(property.GetValueString().Value, rules)
	}

	if propertyType == MEMBER_PROPERTY_INPUT {
		return matchInput(property.GetValueString().Value, rules)
	}

	if util.StrInArray(propertyType, &memberPropertyDateTypes) {
		dateTime := property.GetValueDate().Value
		if propertyType == MEMBER_PROPERTY_DATE {
			dateTime = util.GetDayStartTimestamp(time.Unix(dateTime/1000, 0)) * 1000
		}

		return matchDate(dateTime, rules, controlProperty.DateIgnoreType)
	}

	if util.StrInArray(propertyType, &memberPropertyNumberTypes) {
		if property.GetValueNumber() == nil {
			return matchNumber(0, rules)
		}

		return matchNumber(property.GetValueNumber().Value, rules)
	}

	if util.StrInArray(propertyType, &memberPropertyStringArrayTypes) {
		return matchStringArray(property.GetValueArray().Value, rules)
	}

	if util.StrInArray(propertyType, &memberPropertyBoolTypes) {
		return matchBool(property.GetValueBool().Value, rules)
	}

	if propertyType == MEMBER_PROPERTY_ADDRESS {
		return matchAddress(property.GetValueArray().Value, rules)
	}

	if propertyType == MEMBER_PROPERTY_LOCATION {
		return matchAddress(property.GetAddressCache().Value, rules)
	}

	return matchRuleWithoutType(property, rules)
}

func getMemberPropertyValueInString(p *member.PropertyDetail) string {
	switch v := p.Value.(type) {
	case *member.PropertyDetail_ValueArray:
		if p.Property.Type == MEMBER_PROPERTY_ADDRESS {
			return strings.Join(v.ValueArray.Value, "")
		}
		return strings.Join(v.ValueArray.Value, ",")
	case *member.PropertyDetail_ValueString:
		return v.ValueString.Value
	case *member.PropertyDetail_ValueDate:
		if p.Property.Type == MEMBER_PROPERTY_DATE {
			return time.Unix(v.ValueDate.Value/1000, 0).Format("2006/01/02")
		}
		return time.Unix(v.ValueDate.Value/1000, 0).Format("2006/01/02 15:04:05")
	case *member.PropertyDetail_ValueBool:
		return cast.ToString(v.ValueBool.Value)
	case *member.PropertyDetail_ValueNumber:
		return cast.ToString(v.ValueNumber.Value)
	case *member.PropertyDetail_ValueNumberArray:
		return strings.Join(core_util.ToStringArray(v.ValueNumberArray.Value), ",")
	}
	if p.Property.Type == MEMBER_PROPERTY_LOCATION && p.AddressCache != nil {
		return strings.Join(p.AddressCache.Value, "")
	}
	return ""
}

func matchRuleWithoutType(property *member.PropertyDetail, rules []ControlRule) (bool, string) {
	valueString := property.GetValueString()
	if valueString != nil {
		return matchString(valueString.Value, rules)
	}

	valueDate := property.GetValueDate()
	if valueDate != nil {
		return matchDate(valueDate.Value, rules, "")
	}

	valueNumber := property.GetValueNumber()
	if valueNumber != nil {
		return matchNumber(valueNumber.Value, rules)
	}

	valueArray := property.GetValueArray()
	if valueArray != nil {
		return matchStringArray(valueArray.Value, rules)
	}

	return false, ""
}

func matchString(value interface{}, rules []ControlRule) (bool, string) {
	pv := cast.ToString(value)
	for _, rule := range rules {
		ok := share_model.MatchString(pv, rule)
		if ok {
			return true, rule.Branch
		}
	}

	return false, ""
}

func matchMatchers(eventId string, matchers []RuleMatcher, indexToEventPropertyMap map[int]*marketing.EventProperty) bool {
	for i, matcher := range matchers {
		eventProperty, ok := indexToEventPropertyMap[i]
		if !ok {
			return false
		}
		rule := ControlRule{
			Operator: matcher.Operator,
			Value:    matcher.Value,
		}
		// 事件中只会包含 tradeStore.sid 或 tradeStore._id，特殊处理
		if util.StrInArray(eventId, &SpecialStoreIdEvents) && eventProperty.PropertyId == "storeId" {
			storeIds, err := cast.ToStringSliceE(rule.Value)
			validStoreIds := make([]string, 0, len(storeIds)*2)
			if err == nil {
				for _, storeId := range storeIds {
					validStoreIds = append(validStoreIds, strings.Split(storeId, ":")...)
				}
				rule.Value = validStoreIds
			}
		}
		switch v := eventProperty.GetValue().(type) {
		case *marketing.EventProperty_ValueString:
			// 处理计数型字符串对比计数的情况
			// 如评价商品事件中的带图数量为“3张”格式的字符串，但对比规则则只对比数字
			if isNumber(matcher.Value) {
				nums := numberCompiler.FindStringSubmatch(v.ValueString)
				if len(nums) == 0 {
					return false
				}
				ok = share_model.MatchNumber(cast.ToFloat64(nums[len(nums)-1]), rule)
			} else {
				ok = share_model.MatchString(v.ValueString, rule)
			}
		case *marketing.EventProperty_ValueInt:
			ok = share_model.MatchNumber(cast.ToFloat64(v.ValueInt), rule)
		case *marketing.EventProperty_ValueBool:
			ok = share_model.MatchBool(v.ValueBool, rule)
		case *marketing.EventProperty_ValueDouble:
			ok = share_model.MatchNumber(v.ValueDouble, rule)
		case *marketing.EventProperty_ValueDatetime:
			ok = share_model.MatchRFC3339(v.ValueDatetime, rule)
		default:
			return false
		}
		if !ok {
			return false
		}
	}
	return true
}

func matchInput(value interface{}, rules []ControlRule) (bool, string) {
	pv := strings.ToLower(cast.ToString(value))
	for _, rule := range rules {
		var ok bool
		if util.StrInArray(rule.Operator, &[]string{
			share_model.OPERATOR_IN,
			share_model.OPERATOR_NIN,
		}) {
			ok = share_model.MatchString(pv, rule)
		} else {
			ok = share_model.MatchInput(pv, rule)
		}
		if ok {
			return true, rule.Branch
		}
	}

	return false, ""
}

func matchDate(value interface{}, rules []ControlRule, ignoredType string) (bool, string) {
	pv := cast.ToInt64(value)
	for _, rule := range rules {
		ok := share_model.MatchDate(pv, formatDateRuleByIgnoreType(pv, rule, ignoredType))
		if ok {
			return true, rule.Branch
		}
	}

	return false, ""
}

func formatDateRuleByIgnoreType(timeStamp int64, rule ControlRule, ignoreType string) ControlRule {
	result := rule
	targetTime := util.TransIntTimestampToTime(timeStamp)
	switch ignoreType {
	case MARKETO_DATE_IGNORE_TYPE_YEAR:
		switch rule.GetOperator() {
		case share_model.OPERATOR_BETWEEN, share_model.OPERATOR_NOT_BETWEEN:
			rv := cast.ToIntSlice(rule.Value)
			if len(rv) != 2 {
				return result
			}
			rv1 := util.TransTimeToMilliSec(formatTimeByIgnoreType(targetTime, util.TransIntTimestampToTime(cast.ToInt64(rv[0])), ignoreType))
			rv2 := util.TransTimeToMilliSec(formatTimeByIgnoreType(targetTime, util.TransIntTimestampToTime(cast.ToInt64(rv[1])), ignoreType))
			// 出现此情况说明跨年了
			if rv2 < rv1 {
				t := rv1
				rv1 = rv2
				rv2 = t
				if rule.GetOperator() == share_model.OPERATOR_NOT_BETWEEN {
					result.Operator = share_model.OPERATOR_BETWEEN
				} else {
					result.Operator = share_model.OPERATOR_NOT_BETWEEN
				}
			}
			result.Value = []int64{rv1, rv2}
		default:
			ruleTime := util.TransIntTimestampToTime(cast.ToInt64(rule.GetValue()))
			result.Value = util.TransTimeToMilliSec(formatTimeByIgnoreType(targetTime, ruleTime, ignoreType))
		}
	}
	return result
}

func formatTimeByIgnoreType(source, target time.Time, ignoreType string) time.Time {
	result := target
	switch ignoreType {
	case MARKETO_DATE_IGNORE_TYPE_YEAR:
		result = time.Date(source.Year(), target.Month(), target.Day(), target.Hour(), target.Minute(), target.Second(), target.Nanosecond(), time.Local)
	}
	return result
}

func matchNumber(value interface{}, rules []ControlRule) (bool, string) {
	pv := cast.ToFloat64(value)
	for _, rule := range rules {
		ok := share_model.MatchNumber(pv, rule)
		if ok {
			return true, rule.Branch
		}
	}

	return false, ""
}

func matchStringArray(value interface{}, rules []ControlRule) (bool, string) {
	pvs := cast.ToStringSlice(value)
	for _, rule := range rules {
		if rule.Operator == share_model.OPERATOR_IN {
			rule.Operator = share_model.OPERATOR_INANY
		} else if rule.Operator == share_model.OPERATOR_NIN {
			rule.Operator = share_model.OPERATOR_NINANY
		}
		ok := share_model.MatchStringArray(pvs, rule)
		if ok {
			return true, rule.Branch
		}
	}

	return false, ""
}

func matchBool(value interface{}, rules []ControlRule) (bool, string) {
	pv := cast.ToBool(value)
	for _, rule := range rules {
		ok := share_model.MatchBool(pv, rule)
		if ok {
			return true, rule.Branch
		}
	}

	return false, ""
}

func trimAddressCountry(value interface{}) []string {
	address := cast.ToStringSlice(value)
	if len(address) == 0 {
		return []string{}
	}

	// ignore first character china
	return address[1:]
}

func matchAddress(value interface{}, rules []ControlRule) (bool, string) {
	addressString := strings.Join(trimAddressCountry(value), ",")
	for _, rule := range rules {
		ok := share_model.MatchDomesticAddress(addressString, rule)
		if ok {
			return true, rule.Branch
		}
	}

	return false, ""
}

func (m *MarketoWorkflow) processControlEvent(event ControlEvent) (bool, string) {
	if m.EventAt.IsZero() {
		// 首先计算时间增量，reserveTime 可能为 0，此种情况下 eventAt 就是当前时间
		eventAt := time.Now().Add(time.Millisecond * time.Duration(event.ReserveTime))
		// 如果指定了等待至的准确时间
		if !event.SpecifiedAt.IsZero() {
			eventAt = event.SpecifiedAt
		}
		// 如果还指定了小时、分钟
		if event.NextTime != "" {
			// 获取时间点的小时、分钟数
			hours := core_util.StringToInt(event.NextTime[:2])
			minutes := core_util.StringToInt(event.NextTime[3:])
			temp := time.Date(eventAt.Year(), eventAt.Month(), eventAt.Day(), hours, minutes, 0, 0, time.Local)
			// 如果指定了小时、分钟但没有指定天数，且当前时间已超过指定时间，则加一天
			if event.ReserveTime == 0 && eventAt.After(temp) {
				eventAt = time.Date(eventAt.Year(), eventAt.Month(), eventAt.Day()+1, hours, minutes, 0, 0, time.Local)
			} else {
				eventAt = temp
			}
		}
		m.EventAt = eventAt
		m.EventId = event.EventId
		m.Status = WORKFLOW_STATUS_EVENT
		return true, ""
	}

	if event.IsBinary() || event.IsPropertyLimited() {
		return false, event.GetDefaultBranch()
	}

	matchedTimes := m.getEventMatchTimes(m.WorkflowId, true)
	if event.IsTimesLimited() || matchedTimes != 0 {
		_, branch := matchNumber(matchedTimes, event.Rules)
		return false, branch
	}

	if unmatchedTimes := m.getEventMatchTimes(m.WorkflowId, false); unmatchedTimes != 0 {
		_, branch := matchNumber(0, event.Rules)
		return false, branch
	}

	return false, event.GetDefaultBranch()
}

func (m *MarketoWorkflow) processControlWaiting(w WorkflowWaiting) bool {
	now := time.Now()

	if !core_util.IsEmpty(reflect.ValueOf(m.WaitingAt)) && (now.Equal(m.WaitingAt) || now.After(m.WaitingAt)) {
		return false
	}

	m.Status = WORKFLOW_STATUS_WAITING

	if !core_util.IsEmpty(reflect.ValueOf(w.StartTime)) && !core_util.IsEmpty(reflect.ValueOf(w.EndTime)) {
		startHours := core_util.StringToInt(w.StartTime[:2])
		startMinutes := core_util.StringToInt(w.StartTime[3:])
		endHours := core_util.StringToInt(w.EndTime[:2])
		endMinutes := core_util.StringToInt(w.EndTime[3:])
		startTime := time.Date(now.Year(), now.Month(), now.Day(), startHours, startMinutes, 0, 0, time.Local)
		endTime := time.Date(now.Year(), now.Month(), now.Day(), endHours, endMinutes, 0, 0, time.Local)
		if now.Before(startTime) {
			m.WaitingAt = startTime
			return true
		}
		if now.After(endTime) {
			m.WaitingAt = startTime.AddDate(0, 0, 1)
			return true
		}
	}

	if !core_util.IsZero(w.NextTime) && !core_util.IsZero(w.TimeOffset) {
		daysLater := now.Add(time.Millisecond * time.Duration(w.TimeOffset))

		hours := core_util.StringToInt(w.NextTime[:2])
		minutes := core_util.StringToInt(w.NextTime[3:])

		// after the next natural day, so the TimeOffset must be one day's millisecond exactly,
		// refer to https://gitlab.maiscrm.com/mai/home/<USER>/10682#note_1773971
		m.WaitingAt = time.Date(daysLater.Year(), daysLater.Month(), daysLater.Day(), hours, minutes, 0, 0, time.Local)
		return true
	}

	if !core_util.IsEmpty(reflect.ValueOf(w.TimeOffset)) {
		m.WaitingAt = now.Add(time.Millisecond * time.Duration(w.TimeOffset))
		return true
	}

	if !core_util.IsEmpty(reflect.ValueOf(w.NextTime)) {
		hours := core_util.StringToInt(w.NextTime[:2])
		minutes := core_util.StringToInt(w.NextTime[3:])
		todayWaitingAt := time.Date(now.Year(), now.Month(), now.Day(), hours, minutes, 0, 0, time.Local)
		if todayWaitingAt.After(now) {
			m.WaitingAt = todayWaitingAt
			return true
		}

		m.WaitingAt = time.Date(now.Year(), now.Month(), now.Day()+1, hours, minutes, 0, 0, time.Local)
		return true
	}

	if !core_util.IsEmpty(reflect.ValueOf(w.SpecificAt)) {
		m.WaitingAt = w.SpecificAt
		return true
	}
	return true
}

func (m *MarketoWorkflow) updateWorkflowAfterProcessSuspend(ctx context.Context) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       m.Id,
		"status": bson.M{
			"$ne": WORKFLOW_STATUS_STOPPED,
		},
		"workflowLogs": bson.M{"$elemMatch": bson.M{
			"status":     WORKFLOW_LOG_SUBMITTED,
			"workflowId": m.WorkflowId,
		}},
	}
	var setter bson.M
	index := -1
	for i := range m.WorkflowLogs {
		if m.WorkflowLogs[i].WorkflowId == m.WorkflowId {
			index = i
			break
		}
	}
	if index == -1 {
		log.Warn(ctx, "WorkflowLog not found", log.Fields{
			"workflowId":        m.WorkflowId,
			"workflowLogs":      m.WorkflowLogs,
			"marketoWorkflowId": m.Id.Hex(),
		})
		return
	}

	wLog := m.WorkflowLogs[index]
	now := time.Now()
	if m.err != nil {
		setter = bson.M{
			"workflowLogs.$.status":     WORKFLOW_LOG_FAILED,
			"workflowLogs.$.finishedAt": now,
			"workflowLogs.$.error":      m.err.Error(),
			"workflowLogs.$.errorType":  m.resultType,
			"workflowLogs.$.info":       m.info,
		}
		wLog.Status = WORKFLOW_LOG_FAILED
		wLog.FinishedAt = time.Now()
		wLog.Error = m.err.Error()
		wLog.ErrorType = m.resultType
		wLog.Info = m.info
		m.info = ""
	} else {
		setter = bson.M{
			"workflowLogs.$.status":     WORKFLOW_LOG_SUCCEED,
			"workflowLogs.$.finishedAt": now,
		}
		wLog.Status = WORKFLOW_LOG_SUCCEED
		wLog.FinishedAt = now
	}
	m.WorkflowLogs[index] = wLog

	if m.Status == WORKFLOW_STATUS_PENDING {
		// suspend 处理的只会是 action
		nextWorkflow, ok := m.getNextWorkflowById(m.WorkflowId, "")
		if !ok {
			m.Status = WORKFLOW_STATUS_ERROR
		} else {
			m.WorkflowId = nextWorkflow.Id
			// 这里需要在更新原有节点状态的同时，向 workflowLogs 中添加新的节点
			// 由于 MongoDB 不允许更新 workflowLogs 中的字段的同时往 workflowLogs 中 push 新的内容
			// {"$push": {"workflowLogs": {"status": "end", "test": 1}}, "$set": {"workflowLogs.$.status": "succeed"}}
			// Updating the path 'workflowLogs.$.status' would create a conflict at 'workflowLogs'
			// 所以重置 setter，直接全量更新 workflowLogs 字段
			setter["workflowId"] = nextWorkflow.Id
			if nextWorkflow.IsEnd() {
				m.Status = WORKFLOW_STATUS_END
				endLog := WorkflowLog{
					Status:     WORKFLOW_LOG_SUCCEED,
					FinishedAt: time.Now(),
					WorkflowId: m.WorkflowId,
				}
				m.WorkflowLogs = append(m.WorkflowLogs, endLog)
				setter = bson.M{
					"workflowLogs": m.WorkflowLogs,
				}
			}
		}
	}
	setter["status"] = m.Status
	updater := bson.M{
		"$set": setter,
	}
	m.err = nil
	m.resultType = ""
	change := qmgo.Change{
		Update:    updater,
		ReturnNew: true,
	}
	marketoWorkflow := new(MarketoWorkflow)
	updateErr := extension.DBRepository.FindAndApply(ctx, C_MARKETO_WORK_FLOW, selector, []string{}, change, marketoWorkflow)
	if updateErr != nil {
		// 更新失败说明该流程已停止，这种情况不更新状态，防止 stopped 状态被覆盖，下次任务还会继续执行
		delete(setter, "status")
		delete(selector, "status")
		change.Update = bson.M{
			"$set": setter,
		}
		extension.DBRepository.FindAndApply(ctx, C_MARKETO_WORK_FLOW, selector, []string{}, change, marketoWorkflow)
	}
}

func (m *MarketoWorkflow) rollbackToSuspend(ctx context.Context) {
	condition := Common.GenDefaultConditionById(ctx, m.Id)
	updater := bson.M{
		"$set": bson.M{
			"status": WORKFLOW_STATUS_SUSPEND,
		},
	}
	extension.DBRepository.UpdateOne(ctx, C_MARKETO_WORK_FLOW, condition, updater)
}

func (m *MarketoWorkflow) updateAfterProcess(ctx context.Context, err error, resultType string, needLog bool, maievent Maievent) ([]WorkflowLog, bool) {
	var (
		errInfo            string
		hasWorkflowStopped bool
	)
	if err != nil {
		errInfo = err.Error()
	}
	condition := bson.M{
		"_id": m.Id,
		"status": bson.M{
			"$ne": WORKFLOW_STATUS_STOPPED,
		},
	}
	setter := bson.M{
		"status":    m.Status,
		"waitingAt": m.WaitingAt,
		"eventAt":   m.EventAt,
		"eventId":   m.EventId,
	}
	if m.Status == WORKFLOW_STATUS_END || m.Status == WORKFLOW_STATUS_ERROR {
		finishedAt := time.Now()
		setter["finishedAt"] = finishedAt

		duration := finishedAt.Sub(m.StartedAt)
		setter["duration"] = int64(duration.Seconds() * 1000)
		// 除了 end 和 error 状态，其他状态均不可覆盖 stopped 状态
		delete(condition, "status")
	}
	updator := bson.M{}

	if needLog {
		index := -1
		if m.isProcessSuspend {
			for i := range m.WorkflowLogs {
				if m.WorkflowLogs[i].WorkflowId == m.WorkflowId {
					index = i
					break
				}
			}
			if index == -1 {
				log.Warn(ctx, "Workflow log not found after process", log.Fields{
					"workflowId":        m.WorkflowId,
					"workflowLogs":      m.WorkflowLogs,
					"marketoWorkflowId": m.Id.Hex(),
				})
			}
		}
		workflowLog := WorkflowLog{
			WorkflowId: m.WorkflowId,
			FinishedAt: time.Now(),
			Error:      errInfo,
			Matched:    m.matched,
		}
		if index != -1 {
			m.WorkflowLogs[index].FinishedAt = time.Now()
			m.WorkflowLogs[index].Error = errInfo
			m.WorkflowLogs[index].Matched = m.matched
		}

		if m.incTestStatsId.Valid() {
			workflowLog.IncTestStatsId = m.incTestStatsId
			m.incTestStatsId = bson.ObjectId("")
		}
		if m.testBranch != "" {
			workflowLog.TestBranch = m.testBranch
			m.testBranch = ""
		}
		if m.isTestGoalCompleted {
			workflowLog.IsTestGoalCompleted = m.isTestGoalCompleted
			m.isTestGoalCompleted = false
		}
		if m.smsBusinessId != "" {
			workflowLog.SmsBusinessId = m.smsBusinessId
			m.smsBusinessId = ""
		}
		if len(m.membershipDiscountIds) > 0 {
			workflowLog.MembershipDiscountIds = m.membershipDiscountIds
			m.membershipDiscountIds = nil
		}

		if resultType == WORKFLOW_LOG_SUCCEED {
			workflowLog.Status = WORKFLOW_LOG_SUCCEED
		} else if strings.HasPrefix(resultType, WORKFLOW_LOG_SUBMITTED) {
			workflowLog.Status = WORKFLOW_LOG_SUBMITTED
			slices := strings.Split(resultType, ":")
			if len(slices) == 3 {
				workflowLog.MessageId = slices[1]
				workflowLog.ChannelId = slices[2]
			}
		} else {
			workflowLog.Status = WORKFLOW_LOG_FAILED
			workflowLog.ErrorType = resultType
			workflowLog.Info = m.info
			m.info = ""
		}

		if !core_util.IsZero(maievent) {
			workflowLog.Event = maievent
		}

		if index == -1 {
			updator["$push"] = bson.M{
				"workflowLogs": workflowLog,
			}
		} else {
			m.WorkflowLogs[index] = workflowLog
			setter["workflowLogs"] = m.WorkflowLogs
		}
	}

	updator["$set"] = setter
	change := qmgo.Change{
		Update:    updator,
		ReturnNew: true,
	}
	marketoWorkflow := new(MarketoWorkflow)
	updateErr := extension.DBRepository.FindAndApply(ctx, C_MARKETO_WORK_FLOW, condition, []string{}, change, marketoWorkflow)
	if updateErr != nil {
		// 更新失败说明该流程已停止，这种情况不更新状态，防止 stopped 状态被覆盖，下次任务还会继续执行
		delete(setter, "status")
		delete(condition, "status")
		hasWorkflowStopped = true
		updator["$set"] = setter
		change.Update = updator
		extension.DBRepository.FindAndApply(ctx, C_MARKETO_WORK_FLOW, condition, []string{}, change, marketoWorkflow)
	}

	return marketoWorkflow.WorkflowLogs, hasWorkflowStopped
}

func (m *MarketoWorkflow) updateStatus(ctx context.Context, status string) {
	m.Status = status
	setter := bson.M{
		"status": status,
	}
	if status == MARKETO_STATUS_STOPPED {
		m.StoppedAt = time.Now()
		setter["stoppedAt"] = m.StoppedAt
	}
	extension.DBRepository.UpdateOne(
		ctx,
		C_MARKETO_WORK_FLOW,
		bson.M{"_id": m.Id},
		bson.M{"$set": setter},
	)
}

func (m *MarketoWorkflow) setWorkflowId(ctx context.Context) {
	extension.DBRepository.UpdateOne(
		ctx,
		C_MARKETO_WORK_FLOW,
		bson.M{"_id": m.Id},
		bson.M{"$set": bson.M{
			"workflowId": m.WorkflowId,
		}},
	)
}

func (m *MarketoWorkflow) getCurrentWorkflow() (Workflow, bool) {
	for _, workflow := range m.workflows {
		if workflow.Id == m.WorkflowId {
			if workflow.IsDeleted {
				return m.getNextWorkflowById(m.WorkflowId, "")
			}

			return workflow, true
		}

		if m.WorkflowId == "" && workflow.ParentId == "" {
			return workflow, true
		}
	}

	return Workflow{}, false
}

func (m *MarketoWorkflow) getWorkflowById(workflowId string) (Workflow, bool) {
	for _, workflow := range m.workflows {
		if workflow.Id == workflowId {
			return workflow, true
		}
	}
	return Workflow{}, false
}

func (m *MarketoWorkflow) getNextWorkflowById(workflowId string, branch string) (Workflow, bool) {
	return GetNextWorkflowById(m.workflows, workflowId, branch)
}

func GetNextWorkflowById(workflows []Workflow, workflowId string, branch string) (Workflow, bool) {
	for _, workflow := range workflows {
		if workflow.Id == workflowId && workflow.Type == WORKFLOW_TYPE_CONTROL {
			if workflow.Control.TestGoalId != "" {
				w := getWorkflowById(workflows, workflow.Control.TestGoalId)
				if w == nil {
					return Workflow{}, false
				}
				return *w, true
			}
		}
		if workflow.ParentId == workflowId && workflow.ParentBranch == branch {
			if workflow.IsDeleted {
				workflowId = workflow.Id
				branch = ""
				continue
			}
			return workflow, true
		}
	}

	return Workflow{}, false
}

func (self *MarketoWorkflow) Create(ctx context.Context, marketo Marketo, memberId bson.ObjectId, startAt *time.Time, maievent Maievent) (err error) {
	defer util.DeferForDbPanic(&err)
	// there is no new workflow joins after batch operation created, so we
	// set the last params to false
	self.Build(ctx, marketo.Id, memberId, marketo.GetFirstWorkflow().Id, marketo.getWeight(), marketo.isRepeatable(), false)

	// the workflow's createdAt should be the actually occoured time
	// otherwise it can not be awaken if the next event happened to close
	// refer to https://gitlab.maiscrm.com/mai/home/<USER>/12668
	if startAt != nil {
		self.CreatedAt = *startAt
	}

	self.Event = maievent
	self.workflows = marketo.Workflows
	workflow, ok := self.getCurrentWorkflow()
	needUpdate := false
	if ok && workflow.Type == CONTROL && workflow.Control.Type == WORKFLOW_CONTROL_TYPE_EVENT {
		now := time.Now()
		self.ProcessingAt = now
		self.StartedAt = now
		self.WorkflowId = workflow.Id
		self.processControlEvent(workflow.Control.Event)
		needUpdate = true
	}

	_, err = extension.DBRepository.Insert(ctx, C_MARKETO_WORK_FLOW, self)
	if err != nil {
		return
	}

	if needUpdate {
		self.updateAfterProcess(ctx, nil, "", true, Maievent{})
	}

	return
}

// Only used for once Marketo, and the member gets in after the marketo being created
func (self *MarketoWorkflow) CreateWithOneLog(ctx context.Context, marketo Marketo, memberId bson.ObjectId, isMatch bool, maievent Maievent) (err error) {
	defer util.DeferForDbPanic(&err)
	firstWorkflow := marketo.GetFirstWorkflow()
	if firstWorkflow.Type != WORKFLOW_TYPE_CONTROL || firstWorkflow.Control.Type != WORKFLOW_CONTROL_TYPE_EVENT {
		log.Warn(ctx, "Failed to create workflow with one log", log.Fields{
			"memberId":  memberId.Hex(),
			"marketoId": marketo.Id.Hex(),
		})
		return
	}

	self.Build(ctx, marketo.Id, memberId, firstWorkflow.Id, marketo.getWeight(), marketo.isRepeatable(), false)
	self.Status = WORKFLOW_STATUS_EVENT
	self.EventId = firstWorkflow.Control.Event.EventId
	self.EventAt = time.Now().Add(time.Duration(firstWorkflow.Control.Event.ReserveTime) * time.Millisecond)
	self.ProcessingAt = time.Now()
	self.WorkflowLogs = append(self.WorkflowLogs, WorkflowLog{
		WorkflowId: marketo.GetFirstWorkflow().Id,
		FinishedAt: time.Now(),
		Matched:    isMatch,
		Event:      maievent,
	})
	extension.DBRepository.Insert(ctx, C_MARKETO_WORK_FLOW, self)
	return
}

// This func is only used for marketo whose type is once and the first workflow is Control with Event
// The member can get into this marketo if he matches the entrance condition after the marketo has been created
func (self *MarketoWorkflow) CreateBySpecifyBranch(ctx context.Context, marketo Marketo, parentId, parentBranch string, memberId bson.ObjectId, maievent Maievent) (err error) {
	defer util.DeferForDbPanic(&err)
	self.workflows = marketo.Workflows
	thisWorkflow := marketo.GetSpecifiedWorkflow(parentId, parentBranch)
	self.Build(ctx, marketo.Id, memberId, thisWorkflow.Id, marketo.getWeight(), marketo.isRepeatable(), false)
	self.WorkflowLogs = append(self.WorkflowLogs, WorkflowLog{
		WorkflowId: parentId,
		FinishedAt: time.Now(),
		Status:     WORKFLOW_LOG_SUCCEED,
		Matched:    true,
		Event:      maievent,
	})
	extension.DBRepository.Insert(ctx, C_MARKETO_WORK_FLOW, self)
	return
}

func (*MarketoWorkflow) CheckAndUpdateIfWorkflowsStopped(ctx context.Context, marketo Marketo, marketoWorkflowIds []bson.ObjectId) error {
	if len(marketoWorkflowIds) == 0 {
		return nil
	}
	if marketo.ShouldStopAllWorkflows() {
		return CMarketoWorkflow.DeleteByIds(ctx, marketoWorkflowIds)
	}
	return nil
}

func (self *MarketoWorkflow) Build(ctx context.Context, marketoId, memberId bson.ObjectId, workflowId string, weight int64, isRepeatable, isBatchOperation bool) {
	self.Id = bson.NewObjectId()
	self.AccountId = util.GetAccountIdAsObjectId(ctx)
	self.MemberId = memberId
	self.MarketoId = marketoId
	self.IsMarketoRepeatable = isRepeatable
	self.WorkflowId = workflowId
	self.Weight = weight
	self.IsBatchOperation = isBatchOperation
	self.Status = WORKFLOW_STATUS_PENDING
	self.CreatedAt = time.Now()
}

func (*MarketoWorkflow) BatchInsert(ctx context.Context, marketoId bson.ObjectId, memberIds []bson.ObjectId, isBatchOperation bool, identifier string) {
	marketo := CMarketo.GetById(ctx, marketoId.Hex())
	if marketo == nil {
		log.Warn(ctx, "Failed to get marketo", log.Fields{
			"marketoId": marketoId,
		})
		return
	}
	if marketo.ShouldStopAllWorkflows() {
		return
	}
	workflow := marketo.GetFirstWorkflow()
	workflowId := workflow.Id
	isMarketoRepeatable := marketo.isRepeatable()
	weight := marketo.getWeight()
	needSuspend := workflow.NeedSuspend()
	now := time.Now()
	var (
		marketoWorkflows   []interface{}
		marketoWorkflowIds []bson.ObjectId
	)
	// 根据可见标签再次过滤
	if len(marketo.VisibleMemberTags) > 0 || len(marketo.VisibleMemberRuleTags) > 0 || len(marketo.VisibleMemberModelTags) > 0 {
		memberIdsArray := util.MongoIdsToStrs(memberIds)
		resp, err := proto_client.GetMemberServiceClient().SearchMemberIds(ctx, &member.SearchMemberRequest{
			Ids:       memberIdsArray,
			Tags:      marketo.VisibleMemberTags,
			RuleTags:  marketo.VisibleMemberRuleTags,
			ModelTags: marketo.VisibleMemberModelTags,
			ListCondition: &request.ListCondition{
				Page:    1,
				PerPage: uint32(len(memberIdsArray)),
			},
		})
		if err != nil {
			return
		}
		memberIds = util.ToMongoIds(core_util.IntersectStringSlice(resp.MemberIds, memberIdsArray))
	}
	for _, memberId := range memberIds {
		marketoWorkflow := new(MarketoWorkflow)
		marketoWorkflow.Build(ctx, marketoId, memberId, workflowId, weight, isMarketoRepeatable, isBatchOperation)
		if identifier != "" {
			marketoWorkflow.Identifier = identifier
		}
		if needSuspend {
			marketoWorkflow.Status = WORKFLOW_STATUS_SUSPEND
			marketoWorkflow.WorkflowLogs = []WorkflowLog{
				{
					WorkflowId: workflowId,
					FinishedAt: now,
					Status:     WORKFLOW_LOG_SUBMITTED,
				},
			}
		}

		marketoWorkflows = append(marketoWorkflows, marketoWorkflow)
		marketoWorkflowIds = append(marketoWorkflowIds, marketoWorkflow.Id)
	}
	if len(marketoWorkflows) > 0 {
		extension.DBRepository.InsertUnordered(ctx, C_MARKETO_WORK_FLOW, marketoWorkflows...)
	}
	if len(marketoWorkflowIds) > 0 {
		CMarketoWorkflow.CheckAndUpdateIfWorkflowsStopped(ctx, *marketo, marketoWorkflowIds)
	}
}

func (*MarketoWorkflow) GetByCondition(ctx context.Context, condition bson.M) *MarketoWorkflow {
	marketoWorkflow := new(MarketoWorkflow)
	extension.DBRepository.FindOne(ctx, C_MARKETO_WORK_FLOW, condition, marketoWorkflow)
	if !marketoWorkflow.Id.Valid() {
		return nil
	}

	return marketoWorkflow
}

func (*MarketoWorkflow) GetAllByCondition(ctx context.Context, selector bson.M, sorter []string, limit int) []MarketoWorkflow {
	workflows := []MarketoWorkflow{}
	extension.DBRepository.FindAll(ctx, C_MARKETO_WORK_FLOW, selector, sorter, limit, &workflows)
	return workflows
}

func (*MarketoWorkflow) Awaken(ctx context.Context, memberId, eventId string, eventOccurredAt time.Time, eventProperties []*marketing.EventProperty) {
	condition := bson.M{
		"accountId": bson.ObjectIdHex(util.GetAccountId(ctx)),
		"memberId":  bson.ObjectIdHex(memberId),
		"eventId":   eventId,
		"status":    WORKFLOW_STATUS_EVENT,
		"eventAt": bson.M{
			"$gte": time.Now(),
		},
		"createdAt": bson.M{
			"$lte": eventOccurredAt,
		},
		"isDeleted": false,
	}
	marketoWorkflows := CMarketoWorkflow.GetListByCondition(ctx, condition)
	marketoWorkflowMap := CMarketoWorkflow.GetMarketoWorkflowMap(ctx, marketoWorkflows)
	if len(marketoWorkflowMap) == 0 {
		return
	}

	CMarketoWorkflow.BatchBeginToProcessWorkflow(ctx, marketoWorkflows)

	maievent := GenMaievent(eventId, eventOccurredAt, eventProperties)
	for _, marketoWorkflow := range marketoWorkflows {
		// 此时 marketoWorkflow.Status 原本是 event 状态，因为 BatchBeginToProcessWorkflow 只更新了 db 中的记录
		// awakenInternal 中会使用 updateAfterProcess 更新 marketoWorkflow 的 status，如有并发，会再次被查出并执行
		marketoWorkflow.Status = WORKFLOW_STATUS_PROCESSING
		marketoWorkflow.awakenInternal(ctx, marketoWorkflowMap, eventProperties, maievent)
	}
}

func (*MarketoWorkflow) awakenABTest(ctx context.Context, testId string, marketoId bson.ObjectId) error {
	condition := bson.M{
		"accountId":  util.GetAccountIdAsObjectId(ctx),
		"marketoId":  marketoId,
		"workflowId": testId,
		"status":     WORKFLOW_STATUS_WAITING_TEST,
	}
	updater := bson.M{
		"$set": bson.M{
			"status":    WORKFLOW_STATUS_PENDING,
			"updatedAt": time.Now(),
		},
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_MARKETO_WORK_FLOW, condition, updater)
	return err
}

// GetMarketoMap 获取工作流对应的路径策略映射关系，键为 marketo._id
func (*MarketoWorkflow) GetMarketoMap(ctx context.Context, workflows []MarketoWorkflow) map[string]Marketo {
	marketoMap := make(map[string]Marketo)
	if len(workflows) == 0 {
		return marketoMap
	}

	var marketoIds []bson.ObjectId
	for _, workflow := range workflows {
		marketoIds = append(marketoIds, workflow.MarketoId)
	}

	var marketos []Marketo
	condition := bson.M{
		"_id": bson.M{
			"$in": marketoIds,
		},
		"isDeleted": false,
	}
	extension.DBRepository.FindAll(ctx, C_MARKETO, condition, []string{}, 0, &marketos)
	for _, marketo := range marketos {
		marketoMap[marketo.Id.Hex()] = marketo
	}
	return marketoMap
}

func (*MarketoWorkflow) GetMarketoWorkflowMap(ctx context.Context, workflows []MarketoWorkflow) map[string][]Workflow {
	marketoWorkflowMap := make(map[string][]Workflow)
	if len(workflows) == 0 {
		return marketoWorkflowMap
	}

	var marketoIds []bson.ObjectId
	for _, workflow := range workflows {
		marketoIds = append(marketoIds, workflow.MarketoId)
	}

	var marketos []Marketo
	condition := bson.M{
		"_id": bson.M{
			"$in": marketoIds,
		},
	}
	extension.DBRepository.FindAll(ctx, C_MARKETO, condition, []string{}, 0, &marketos)
	for _, marketo := range marketos {
		marketoWorkflowMap[marketo.Id.Hex()] = marketo.Workflows
	}

	return marketoWorkflowMap
}

// GetMemberDetailMap 获取一批工作流包含的 member 映射，键为 memberId，值为调用 MemberService 得到的 MemberDetailResponse
func (*MarketoWorkflow) GetMemberDetailMap(ctx context.Context, workflows []MarketoWorkflow) (map[string]*member.MemberDetailResponse, error) {
	memberDetailMap := make(map[string]*member.MemberDetailResponse)
	length := len(workflows)
	if length == 0 {
		return memberDetailMap, nil
	}

	var memberIds []string
	for _, workflow := range workflows {
		memberIds = append(memberIds, workflow.MemberId.Hex())
	}

	detailList, err := getMembers(ctx, memberIds)
	if err != nil && core_errors.IsRPCInternalError(err) {
		return memberDetailMap, err
	}

	if detailList != nil {
		for _, member := range detailList.Members {
			memberDetailMap[member.Id] = member
		}
	}

	return memberDetailMap, nil
}

func (*MarketoWorkflow) GetListByCondition(ctx context.Context, condition bson.M) []MarketoWorkflow {
	var marketoWorkflows []MarketoWorkflow

	extension.DBRepository.FindAll(ctx, C_MARKETO_WORK_FLOW, condition, []string{}, 0, &marketoWorkflows)
	return marketoWorkflows
}

func (m *MarketoWorkflow) awakenInternal(ctx context.Context, marketoWorkflowMap map[string][]Workflow, eventProperties []*marketing.EventProperty, maievent Maievent) error {
	m.workflows = marketoWorkflowMap[m.MarketoId.Hex()]
	workflow, ok := m.getCurrentWorkflow()
	if !ok {
		m.Status = WORKFLOW_STATUS_ERROR
		m.updateAfterProcess(ctx, errors.New("Current workflow not found"), "", true, maievent)
		return nil
	}
	if workflow.Control.TestId != "" && !workflow.Control.Event.IsBinary() {
		m.Status = WORKFLOW_STATUS_ERROR
		m.updateAfterProcess(ctx, errors.New("invalid branch type for testGoal"), "", true, maievent)
		return nil
	}

	var branch string
	if m.matched, branch = m.MatchEvent(ctx, workflow, eventProperties); !m.matched {
		m.updateAfterProcess(ctx, nil, "", false, Maievent{})
		m.restoreToEventStatus(ctx)
		return nil
	}

	event := workflow.Control.Event
	if workflow.IsTestGoal() && branch == event.GetFirstRule().Branch {
		m.isTestGoalCompleted = true
	}

	m.WorkflowLogs, _ = m.updateAfterProcess(ctx, nil, "", true, maievent)
	if event.IsBinary() {
		eventTimes := event.Times
		if eventTimes > 0 && (m.getEventMatchTimes(workflow.Id, true)) < eventTimes {
			m.restoreToEventStatus(ctx)
			return nil
		}
	}

	if event.IsMulti() && !event.IsPropertyLimited() {
		return nil
	}

	nextWorkflow, found := m.getNextWorkflowById(m.WorkflowId, branch)
	if !found {
		m.Status = WORKFLOW_STATUS_ERROR
		err := errors.New("Next workflow not found")
		m.updateAfterProcess(ctx, err, "", true, maievent)
		return err
	}

	m.WorkflowId = nextWorkflow.Id
	m.setWorkflowId(ctx)
	m.restoreToPending(ctx)
	return nil
}

func (m *MarketoWorkflow) getEventMatchTimes(workflowId string, matched bool) uint32 {
	var times uint32
	for _, log := range m.WorkflowLogs {
		if log.WorkflowId == workflowId && log.Matched == matched {
			times++
		}
	}

	return times
}

// 获取进入 AB 测试时增加过测试次数的统计 id
func (m *MarketoWorkflow) getIncTestStatsIdAndBranch(testWorkflowId string) (bson.ObjectId, string) {
	for _, workflowLog := range m.WorkflowLogs {
		if workflowLog.WorkflowId != testWorkflowId {
			continue
		}
		return workflowLog.IncTestStatsId, workflowLog.TestBranch
	}
	return bson.ObjectId(""), ""
}

func (m *MarketoWorkflow) hadTestGoalCompletedLog(testGoalId string) bool {
	virtualTestGaolIds := []string{}
	for _, w := range m.workflows {
		if w.Type != WORKFLOW_TYPE_CONTROL {
			continue
		}
		control := w.Control
		if control.TestGoalId == testGoalId {
			virtualTestGaolIds = append(virtualTestGaolIds, w.Id)
		}
	}
	for _, wLog := range m.WorkflowLogs {
		if !wLog.IsTestGoalCompleted {
			continue
		}
		if core_util.StrInArray(wLog.WorkflowId, &virtualTestGaolIds) {
			return true
		}
	}
	return false
}

func (m *MarketoWorkflow) restoreToEventStatus(ctx context.Context) {
	extension.DBRepository.UpdateOne(
		ctx,
		C_MARKETO_WORK_FLOW,
		bson.M{"_id": m.Id},
		bson.M{
			"$set": bson.M{
				"status": WORKFLOW_STATUS_EVENT,
			},
		},
	)
}

func (m *MarketoWorkflow) restoreToPending(ctx context.Context) {
	m.EventAt = time.Time{}
	m.EventId = ""
	m.Status = WORKFLOW_STATUS_PENDING
	m.updateAfterProcess(ctx, nil, "", false, Maievent{})
}

func (m *MarketoWorkflow) MatchEvent(ctx context.Context, workflow Workflow, eventProperties []*marketing.EventProperty) (bool, string) {
	event := workflow.Control.Event
	event.preproccessRules(eventProperties)
	if event.IsBinary() {
		if len(event.Rules) == 0 {
			return false, ""
		}
		firstRule := event.GetFirstRule()
		if len(event.Properties) == 0 {
			return true, firstRule.Branch
		}

		rules := []ControlRule{firstRule}
		return matchEventProperties(event.EventId, event.Properties, rules, eventProperties)
	}

	if event.IsMulti() && event.IsTimesLimited() {
		return true, ""
	}

	rules := event.Rules
	if event.IsMulti() && event.IsAllLimited() {
		rules = []ControlRule{{
			Operator: event.Operator,
			Value:    event.Value,
			Matchers: event.Matchers,
		}}
	}

	return matchEventProperties(event.EventId, event.Properties, rules, eventProperties)
}

func matchEventProperties(eventId string, properties []MarketoEventProperty, rules []ControlRule, eventProperties []*marketing.EventProperty) (bool, string) {
	ruleMatcherEventPropertyMap := map[int]*marketing.EventProperty{}
	eventPropertyMap := map[string]*marketing.EventProperty{}
	for _, ep := range eventProperties {
		eventPropertyMap[ep.PropertyId] = ep
	}
	for i, property := range properties {
		if ep, ok := eventPropertyMap[property.PropertyId]; ok {
			ruleMatcherEventPropertyMap[i] = ep
		}
	}
	for _, rule := range rules {
		if matchMatchers(eventId, rule.Matchers, ruleMatcherEventPropertyMap) {
			return true, rule.Branch
		}

	}

	return false, ""
}

func acquireRestoreLock(ctx context.Context) bool {
	key := generateLockKey(ctx, RESTORE_LOCK_SUFFIX)
	ok, _ := extension.RedisClient.SetNX(key, "1", MAX_AQUIRE_LOCK_TIME)

	return ok
}

func releaseRestoreLock(ctx context.Context) {
	key := generateLockKey(ctx, RESTORE_LOCK_SUFFIX)
	extension.RedisClient.Del(key)
}

func (*MarketoWorkflow) RestoreStaleProcess(ctx context.Context) {

	if !acquireRestoreLock(ctx) {
		return
	}

	defer releaseRestoreLock(ctx)

	oneQuarterBefore := time.Now().Add(-(time.Minute * MAX_PROCESSING_MINUTE_DURATION))
	accountId := util.GetAccountId(ctx)
	condition := bson.M{
		"accountId": bson.ObjectIdHex(accountId),
		"status":    WORKFLOW_STATUS_PROCESSING,
		"processingAt": bson.M{
			"$lte": oneQuarterBefore,
		},
	}

	updator := bson.M{
		"$set": bson.M{
			"status": WORKFLOW_STATUS_PENDING,
		},
	}

	updatedCount, _ := extension.DBRepository.UpdateAll(
		ctx,
		C_MARKETO_WORK_FLOW,
		condition,
		updator,
	)

	if updatedCount > 0 {
		log.Warn(ctx, "Stale marketo workflows restored", log.Fields{
			"count":     updatedCount,
			"accountId": accountId,
		})
	}
}

func generateLockKey(ctx context.Context, suffix string) string {
	accountId := util.GetAccountId(ctx)

	return fmt.Sprintf("%s%s", accountId, suffix)
}

func acquireProcessingLock(ctx context.Context) bool {
	key := generateLockKey(ctx, PROCESSING_LOCK_SUFFIX)
	ok, _ := extension.RedisClient.SetNX(key, "1", MAX_AQUIRE_LOCK_TIME)

	return ok
}

func releaseProcessingLock(ctx context.Context) {
	key := generateLockKey(ctx, PROCESSING_LOCK_SUFFIX)
	extension.RedisClient.Del(key)
}

func acquireSuspendLock(ctx context.Context) bool {
	key := fmt.Sprintf(SUSPEND_WORKFLOW_LOCK, util.GetAccountId(ctx))
	ok, _ := extension.RedisClient.SetNX(key, "1", MAX_AQUIRE_LOCK_TIME)

	return ok
}

func releaseSuspendLock(ctx context.Context) {
	key := fmt.Sprintf(SUSPEND_WORKFLOW_LOCK, util.GetAccountId(ctx))
	extension.RedisClient.Del(key)
}

func (*MarketoWorkflow) ProcessPendingWorkflow(ctx context.Context) {
	if !acquireProcessingLock(ctx) {
		return
	}

	var processingLockReleased bool
	defer func() {
		if processingLockReleased {
			return
		}
		releaseProcessingLock(ctx)
	}()

	now := time.Now()
	accountId := bson.ObjectIdHex(util.GetAccountId(ctx))
	condition := bson.M{
		"$or": []bson.M{
			{
				"accountId": accountId,
				"status":    WORKFLOW_STATUS_PENDING,
				"isDeleted": false,
			},
			{
				"accountId": accountId,
				"status":    WORKFLOW_STATUS_WAITING,
				"waitingAt": bson.M{"$lte": now},
				"isDeleted": false,
			},
			{
				"accountId": accountId,
				"status":    WORKFLOW_STATUS_EVENT,
				"eventAt":   bson.M{"$lte": now},
				"isDeleted": false,
			},
		},
	}

	workflows := CMarketoWorkflow.GetPendingList(ctx, condition)
	if len(workflows) == 0 {
		return
	}

	marketoMap := CMarketoWorkflow.GetMarketoMap(ctx, workflows)
	if len(marketoMap) == 0 {
		return
	}

	settings := CMarketingNotificationLimitSetting.GetEnabledSettings(ctx)

	smsTopic := GetSmsTopic(ctx)
	CMarketoWorkflow.BatchBeginToProcessWorkflow(ctx, workflows)
	// 释放锁之前标记锁已释放，防止 defer 中重复释放锁
	processingLockReleased = true
	// 更新状态完毕后即可释放锁，通过调用的地方控制并发量
	releaseProcessingLock(ctx)

	oem := getAccountOem(ctx)

	workflowBatch := []MarketoWorkflow{}
	length := len(workflows)
	var wg sync.WaitGroup
	// 分批执行
	for index, workflow := range workflows {
		workflowBatch = append(workflowBatch, workflow)
		if (index+1)%10 == 0 || (index+1) == length {
			copiedWorkflows := workflowBatch
			wg.Add(1)
			core_component.GO(ctx, func(ctx context.Context) {
				batchProcess(ctx, copiedWorkflows, marketoMap, smsTopic, &wg, oem, settings)
			})

			workflowBatch = []MarketoWorkflow{}
		}
	}
	wg.Wait()
}

func (*MarketoWorkflow) TriggerSuspend(ctx context.Context) {
	key := fmt.Sprintf(TRIGGER_SUSPEND_EXPIRE_POOL_KEY_TEMPLATE, util.GetAccountId(ctx))
	// 每次执行的过期时间比执行持续时间多一分钟，尽量是执行结束后主动释放，如果执行过程中出现异常未释放，则自动过期
	submitResult, _ := extension.RedisClient.SubmitExpirePool(key, 10, (TRIGGER_SUSPEND_ONCE_DURATION+1)*60)
	if !submitResult.Submitted {
		return
	}
	defer extension.RedisClient.ReleaseExpirePool(key, submitResult.Identifier)
	triggerDeadline := time.Now().Add(time.Minute * TRIGGER_SUSPEND_ONCE_DURATION)
	for time.Now().Before(triggerDeadline) {
		condition := bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"status":    WORKFLOW_STATUS_SUSPEND,
			"isDeleted": false,
		}

		workflows, marketoMap := CMarketoWorkflow.GetSuspendList(ctx, condition)
		if len(workflows) == 0 || len(marketoMap) == 0 {
			return
		}
		memberDetailMap, err := CMarketoWorkflow.GetMemberDetailMap(ctx, workflows)
		if err != nil {
			return
		}

		marketoWorkflowProcessor := MarketoWorkflowProcessor{
			marketoWorkflows:     workflows,
			marketoMap:           marketoMap,
			memberDetailMap:      memberDetailMap,
			notificationSettings: CMarketingNotificationLimitSetting.GetEnabledSettings(ctx),
		}
		marketoWorkflowProcessor.batchProcessSuspend(ctx)
	}
}

func batchProcess(ctx context.Context, workflows []MarketoWorkflow, marketoMap map[string]Marketo, smsTopic string, wg *sync.WaitGroup, oem *account.AccountOemDetail, notificationSettings []MarketingNotificationLimitSetting) {
	defer wg.Done()

	memberDetailMap, err := CMarketoWorkflow.GetMemberDetailMap(ctx, workflows)
	if err != nil {
		CMarketoWorkflow.batchRollbackProcessWorkflow(ctx, workflows, false)
		return
	}

	for _, workflow := range workflows {
		workflow.oem = oem
		workflow.Process(ctx, marketoMap, memberDetailMap, smsTopic, notificationSettings)
	}
}

// 将正在进行的工作流回滚到 pending 状态
func (*MarketoWorkflow) batchRollbackProcessWorkflow(ctx context.Context, workflows []MarketoWorkflow, incRetryTimes bool) {
	var workflowIds []bson.ObjectId
	for _, workflow := range workflows {
		workflowIds = append(workflowIds, workflow.Id)
	}

	condition := bson.M{
		"_id": bson.M{
			"$in": workflowIds,
		},
		"status": WORKFLOW_STATUS_PROCESSING,
	}

	updator := bson.M{
		"$set": bson.M{
			"status": WORKFLOW_STATUS_PENDING,
		},
	}

	if incRetryTimes {
		updator["$inc"] = bson.M{
			"retryTimes": 1,
		}
	}

	extension.DBRepository.UpdateAll(ctx, C_MARKETO_WORK_FLOW, condition, updator)
}

func (*MarketoWorkflow) GetMemberCount(ctx context.Context, condition bson.M) uint64 {
	count, _ := extension.DBRepository.Count(ctx, C_MARKETO_WORK_FLOW, condition)

	return uint64(count)
}

func (*MarketoWorkflow) CountDistinctMembers(ctx context.Context, condition bson.M) int {
	pipleline := []bson.M{
		{
			"$match": condition,
		},
		{
			"$group": bson.M{
				"_id":   bson.M{"memberId": "$memberId", "marketoId": "$marketoId"},
				"total": bson.M{"$sum": 1},
			},
		},
		{
			"$group": bson.M{
				"_id":   bson.M{"marketoId": "$_id.marketoId"},
				"total": bson.M{"$sum": 1},
			},
		},
	}

	result := []bson.M{}
	extension.DBRepository.Aggregate(ctx, C_MARKETO_WORK_FLOW, pipleline, false, &result)

	if len(result) < 1 {
		return 0
	}

	return cast.ToInt(result[0]["total"])
}

func (*MarketoWorkflow) GetMemberIds(ctx context.Context, pageCondition extension.PagingCondition) (int, []string) {
	result := []MarketoWorkflow{}
	total, _ := extension.DBRepository.FindByPagination(ctx, C_MARKETO_WORK_FLOW, pageCondition, &result)

	var memberIds []string
	for _, workflow := range result {
		memberIds = append(memberIds, workflow.MemberId.Hex())
	}

	return total, memberIds
}

func (*MarketoWorkflow) ListAllByCondition(ctx context.Context, sorter []string, limit int, condition bson.M) []MarketoWorkflow {
	var result []MarketoWorkflow
	extension.DBRepository.FindAll(ctx, C_MARKETO_WORK_FLOW, condition, sorter, limit, &result)
	return result
}

func (*MarketoWorkflow) GetByPagination(ctx context.Context, page, pageSize uint32, orderBys []string, condition bson.M, needTotal bool) ([]MarketoWorkflow, int) {
	sortFields := util.NormalizeOrderBy(orderBys)
	pageCond := extension.PagingCondition{
		Selector:  condition,
		PageIndex: int(page),
		PageSize:  int(pageSize),
		Sortor:    sortFields,
	}
	var workflows []MarketoWorkflow
	total := 0
	if needTotal {
		total, _ = extension.DBRepository.FindByPagination(ctx, C_MARKETO_WORK_FLOW, pageCond, &workflows)
	} else {
		extension.DBRepository.FindByPaginationWithoutCount(ctx, C_MARKETO_WORK_FLOW, pageCond, &workflows)
	}

	return workflows, total
}

func (*MarketoWorkflow) GetByPaginationWithoutCount(ctx context.Context, page, pageSize uint32, orderBys []string, condition bson.M) []MarketoWorkflow {
	sortFields := util.NormalizeOrderBy(orderBys)
	pageCond := extension.PagingCondition{
		Selector:  condition,
		PageIndex: int(page),
		PageSize:  int(pageSize),
		Sortor:    sortFields,
	}
	var workflows []MarketoWorkflow
	extension.DBRepository.FindByPaginationWithoutCount(ctx, C_MARKETO_WORK_FLOW, pageCond, &workflows)
	return workflows
}

func (m *MarketoWorkflow) GetWorkflowLogById(workflowId string) *WorkflowLog {
	if workflowId == "" {
		return nil
	}

	for _, workflowLog := range m.WorkflowLogs {
		if workflowLog.WorkflowId == workflowId {
			return &workflowLog
		}
	}

	return nil
}

func (MarketoWorkflow) UpdateAll(ctx context.Context, selector, updater bson.M) (int, error) {
	return Common.UpdateAll(ctx, C_MARKETO_WORK_FLOW, "", selector, updater)
}

func (m MarketoWorkflow) StopAllByMarketoId(ctx context.Context, marketoId bson.ObjectId) error {
	return m.StopAllByMarketoIds(ctx, []bson.ObjectId{marketoId})
}

func (MarketoWorkflow) StopAllByMarketoIds(ctx context.Context, marketoIds []bson.ObjectId) error {
	if len(marketoIds) <= 0 {
		return nil
	}
	var targetMarketoIds []string
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = bson.M{
		"$in": marketoIds,
	}
	marketos := CMarketo.GetAllByCondition(ctx, selector)
	for _, marketo := range marketos {
		if marketo.HasWorkflowsStopped || !marketo.NeedStopWorkflows(ctx) {
			continue
		}
		targetMarketoIds = append(targetMarketoIds, marketo.Id.Hex())
	}
	if len(targetMarketoIds) <= 0 {
		return nil
	}
	selector["_id"] = bson.M{
		"$in": core_util.ToObjectIdArray(targetMarketoIds),
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_MARKETO, selector, bson.M{
		"$set": bson.M{
			"hasWorkflowsStopped": true,
		},
	})
	if err != nil {
		return err
	}
	_, err = proto_client.GetAccountServiceClient().CreateJob(ctx, &account.CreateJobRequest{
		JobName:      "batchstopworkflows",
		FunctionName: "batchStopWorkflows",
		Module:       "marketing",
		Description:  "批量停止智能路径工作流",
		Args: core_util.MarshalInterfaceToString(map[string]interface{}{
			"marketoIds": targetMarketoIds,
		}),
	})
	return err
}

func (MarketoWorkflow) DeleteByIds(ctx context.Context, ids []bson.ObjectId) error {
	selector := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	}
	_, err := extension.DBRepository.RemoveAll(ctx, C_MARKETO_WORK_FLOW, selector)
	return err
}

func (self MarketoWorkflow) GetEvents() []map[string]interface{} {
	// collect events
	events := []Maievent{}
	if self.Event.EventId != "" {
		events = append(events, self.Event)
	}
	for _, workflowLog := range self.WorkflowLogs {
		if workflowLog.Event.EventId != "" {
			events = append(events, self.Event)
		}
	}

	result := []map[string]interface{}{}
	for _, event := range events {
		result = append(result, map[string]interface{}{
			"eventId":         event.EventId,
			"occurredAt":      util.TransTimeToMilliSec(event.OccurredAt),
			"eventProperties": event.EventProperties,
		})
	}

	return result
}

func GenMaievent(eventId string, occurredAt time.Time, properties []*marketing.EventProperty) Maievent {
	if eventId == "" {
		return Maievent{}
	}

	return Maievent{
		EventId:         eventId,
		OccurredAt:      occurredAt,
		EventProperties: formatEventProperties(properties),
	}
}

func formatEventProperties(properties []*marketing.EventProperty) map[string]interface{} {
	result := map[string]interface{}{}

	for _, p := range properties {
		value := p.GetValue()
		if value == nil {
			continue
		}

		switch v := value.(type) {
		case *marketing.EventProperty_ValueString:
			result[p.GetPropertyId()] = v.ValueString
		case *marketing.EventProperty_ValueInt:
			result[p.GetPropertyId()] = v.ValueInt
		case *marketing.EventProperty_ValueBool:
			result[p.GetPropertyId()] = v.ValueBool
		case *marketing.EventProperty_ValueDouble:
			result[p.GetPropertyId()] = v.ValueDouble
		case *marketing.EventProperty_ValueDatetime:
			result[p.GetPropertyId()] = v.ValueDatetime
		}
	}

	return result
}

func (m *MarketoWorkflow) isAliyunqaSmsVariable(ctx context.Context, action WorkflowAction) (bool, []*account.AliyunqaSmsTemplateParam, error) {
	if action.Type == ACTION_ALIYUNQA_DIGITAL_SMS {
		return false, nil, nil
	}
	if m.oem == nil || m.oem.OemType != ACCOUNT_OEM_TYPE_ALIYUNQA {
		return false, nil, nil
	}
	oem := m.buildAliyunqaOem()
	resp, err := getAliyunqaSms(ctx, action.AliyunqaSms.TemplateId, oem)
	if err != nil {
		return false, nil, nil
	}
	if resp.Data.IsVariable == 0 {
		return false, nil, nil
	}
	matcher, _ := regexp.Compile(`\$\{.*?\}`)
	params := matcher.FindAllString(resp.Data.TemplateContent, -1)

	templateParams := []*account.AliyunqaSmsTemplateParam{}
	propertiesMap := core_util.MakeMapper("Name", m.memberDetail.Properties)
	for _, p := range params {
		p = strings.TrimPrefix(p, "${")
		p = strings.TrimSuffix(p, "}")
		p = strings.Trim(p, " ")
		var (
			err   error
			value = ""
		)
		// 会员属性的情况
		if strings.HasPrefix(p, "member_") {
			value, err = getMemberFiledForPropertyValue(m.memberDetail, strings.Split(p, "_")[1])
			if err != nil {
				return false, nil, err
			}
		} else if property, ok := propertiesMap[p]; ok {
			value = property.(*member.PropertyDetail).GetValueString().Value
		}
		templateParams = append(templateParams, &account.AliyunqaSmsTemplateParam{
			Name:  p,
			Value: value,
		})
	}
	return true, templateParams, nil
}

func getAliyunqaSms(ctx context.Context, templateId string, oem component.AliyunqaOem) (*component.AliyunqaSmsDetailResponse, error) {
	key := fmt.Sprintf("%s:%s", util.GetAccountId(ctx), templateId)
	if temp, ok := aliyunqaSmsMap.Load(key); ok {
		return temp.(*component.AliyunqaSmsDetailResponse), nil
	}
	resp, err := component.WeConnect.GetAliyunqaSms(ctx, templateId, oem)
	if err == nil {
		aliyunqaSmsMap.Store(key, resp)
		time.AfterFunc(time.Minute, func() {
			aliyunqaSmsMap.Delete(key)
		})
	}
	return resp, err
}

func (m *MarketoWorkflow) buildAliyunqaOem() component.AliyunqaOem {
	result := component.AliyunqaOem{}
	if m.oem == nil || m.oem.OemType != ACCOUNT_OEM_TYPE_ALIYUNQA {
		return result
	}

	copier.Instance(nil).From(m.oem).CopyTo(&result)
	return result
}

func getAccountOem(ctx context.Context) *account.AccountOemDetail {
	resp, err := mairpc.Run(
		"AccountService.ListAccountOem",
		ctx,
		&account.ListAccountOemRequest{
			AccountIds: []string{util.GetAccountId(ctx)},
		},
	)

	if err != nil {
		return nil
	}

	listResp := resp.(*account.ListAccountOemResponse)
	if len(listResp.Items) == 0 {
		return nil
	}
	return listResp.Items[0]
}

func getMemberGroupByMember(ctx context.Context, memberId string) (*member.GetMemberGroupByMemberResponse, error) {
	req := &member.GetMemberGroupByMemberRequest{MemberId: memberId}
	resp, err := mairpc.Run(
		"MemberService.GetMemberGroupByMember",
		ctx,
		req,
	)
	if err != nil {
		return nil, err
	}
	return resp.(*member.GetMemberGroupByMemberResponse), nil
}

func isNumber(v interface{}) bool {
	switch value := v.(type) {
	case int, int32, int64, float32, float64:
		return true
	case []interface{}:
		if len(value) == 0 {
			return false
		}
		return isNumber(value[0])
	default:
		return false
	}
}

func memberIsNotDisturb(m *member.MemberDetailResponse) bool {
	for _, p := range m.Properties {
		if p.Property.PropertyId == "notDisturb" && p.GetValueString().Value == "是" {
			return true
		}
	}
	return false
}

func memberUnsubscribeMarketingSms(m *member.MemberDetailResponse) bool {
	for _, property := range m.Properties {
		if property.Property.PropertyId == "isUnsubscribedSms" && property.GetValueBool().Value {
			return true
		}
	}
	return false
}

func checkBeforeSendingSms(
	ctx context.Context,
	memberDetail *member.MemberDetailResponse,
	limitSetting MarketingNotificationLimitSetting,
	identifier string,
	isDsms bool,
) (error, string, string) {
	phone := getMemberPhone(memberDetail)
	if phone == "" {
		return errors.New("Invalid phone number"), WORKFLOW_LOG_ERROR_EMPTY_PHONENUMBER, ""
	}

	if memberIsNotDisturb(memberDetail) {
		return errors.New("Member is not disturbed"), WORKFLOW_LOG_ERROR_MEMBER_IS_NOT_DISTURB, ""
	}

	// 判断当前客户是否退订营销短信
	if memberUnsubscribeMarketingSms(memberDetail) {
		return errors.New("Member unsubscribed marketing sms"), WORKFLOW_LOG_ERROR_MEMBER_UNSUBSCRIBE_SMS, ""
	}
	notificationType := NOTIFICATION_TYPE_SMS
	if isDsms {
		notificationType = NOTIFICATION_TYPE_DSMS
	}
	if !CMarketingMemberNotificationLimit.Use(ctx, bson.ObjectIdHex(memberDetail.Id), limitSetting, notificationType, identifier) {
		return NOTIFICATION_LIMIT_EXCEEDED_ERROR, WORKFLOW_LOG_ERROR_MEMBER_NOTIFICATION_LIMIT_EXCEEDED, ""
	}
	return nil, "", phone
}

func (m *MarketoWorkflow) GetNotificationLimitSetting(notificationType string) MarketingNotificationLimitSetting {
	for _, setting := range m.notificationLimitSettings {
		if setting.Type == notificationType {
			return setting
		}
	}
	return MarketingNotificationLimitSetting{}
}
