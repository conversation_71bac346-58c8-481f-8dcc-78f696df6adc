package model

import (
	"context"
	"errors"
	"fmt"
	"mairpc/core/client"
	core_codes "mairpc/core/codes"
	"mairpc/core/component"
	core_errors "mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	proto_client "mairpc/proto/client"
	"mairpc/proto/common/origin"
	ec_staff "mairpc/proto/ec/staff"
	pbMember "mairpc/proto/member"
	coupon_codes "mairpc/service/coupon/codes"
	mall_codes "mairpc/service/mall/codes"
	member_codes "mairpc/service/member/codes"
	product_codes "mairpc/service/product/codes"
	share_component "mairpc/service/share/component"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"
	"net/url"
	"strings"
	"time"

	"github.com/spf13/cast"
)

const (
	MAX_RETRY_TIMES = 5
)

type RateLimiter struct {
	ActionType string
	Key        string
	Count      int
	Interval   time.Duration
}

var (
	limiters = []RateLimiter{
		{
			// 句子官方限制 500/30s，这里分散开，且调的更低一些
			ActionType: ACTION_WECHATWORK_MESSAGE,
			Key:        "%s:marketing:juzi",
			Count:      70,
			Interval:   5 * time.Second,
		},
		{
			ActionType: ACTION_WECHAT_MASS,
			Key:        "%s:marketing:wechatMass",
			Count:      60,
			Interval:   5 * time.Minute,
		},
		{
			ActionType: ACTION_WHATSAPP_MESSAGE,
			Key:        "%s:marketing:whatsApp",
			Count:      100,
			Interval:   time.Second,
		},
		{
			ActionType: ACTION_WEBHOOK,
			Key:        "%s:marketing:webhook:%s",
			Count:      30,
			Interval:   time.Second,
		},
	}

	rateLimitedActions = func() []string {
		var actions []string
		for _, limiter := range limiters {
			actions = append(actions, limiter.ActionType)
		}
		return actions
	}()
)

func getRateLimiter(actionType string) *RateLimiter {
	for _, limiter := range limiters {
		if limiter.ActionType == actionType {
			return &limiter
		}
	}
	return nil
}

type MarketoWorkflowProcessor struct {
	marketoWorkflows     []MarketoWorkflow
	rateLimitedActions   []MarketoWorkflow
	marketoMap           map[string]Marketo
	memberDetailMap      map[string]*pbMember.MemberDetailResponse
	wxTagWorkflowMap     map[string][]*MarketoWorkflow
	notificationSettings []MarketingNotificationLimitSetting
}

func (processor *MarketoWorkflowProcessor) batchProcessSuspend(ctx context.Context) {
	processor.prepare(ctx)
	for i := range processor.marketoWorkflows {
		workflow := processor.marketoWorkflows[i]
		w, ok := workflow.getCurrentWorkflow()
		if !ok {
			continue
		}
		workflow.processSuspend(ctx, w)
	}
	if len(processor.rateLimitedActions) > 0 {
		component.GO(ctx, func(ctx context.Context) {
			processor.processRateLimitedActions(ctx)
		})
	}
	processor.processWxTags(ctx)
}

func (processor *MarketoWorkflowProcessor) prepare(ctx context.Context) {
	newWorkflows := []MarketoWorkflow{}
	// 过滤出微信标签和句子消息的节点
	for i := range processor.marketoWorkflows {
		marketoWorkflow := processor.marketoWorkflows[i]
		marketoWorkflow.isProcessSuspend = true
		w, ok := marketoWorkflow.prepare(ctx, processor.marketoMap, processor.memberDetailMap, processor.notificationSettings)
		if !ok {
			continue
		}
		// 如果是句子消息节点和公众号群发那么直接加入 rateLimitedMarketoWorkflows
		if util.StrInArray(w.Action.Type, &rateLimitedActions) {
			processor.rateLimitedActions = append(processor.rateLimitedActions, marketoWorkflow)
			continue
		}
		// 目前只有标签需要挂起后处理
		if w.Type != ACTION || w.Action.Type != ACTION_TAG {
			continue
		}
		if w.Action.Tag.TagType != TAG_TYPE_WECHAT {
			newWorkflows = append(newWorkflows, marketoWorkflow)
			continue
		}
		if len(processor.wxTagWorkflowMap) == 0 {
			processor.wxTagWorkflowMap = map[string][]*MarketoWorkflow{}
		}
		// 把微信标签操作分组
		for _, wxTagId := range w.Action.Tag.WxTagMap {
			key := fmt.Sprintf("%s:%d:%s", w.Action.Tag.ChannelId, wxTagId, w.Action.Tag.Type)
			processor.wxTagWorkflowMap[key] = append(processor.wxTagWorkflowMap[key], &marketoWorkflow)
		}
	}
	processor.marketoWorkflows = newWorkflows
}

func (processor *MarketoWorkflowProcessor) processWxTags(ctx context.Context) {
	for key := range processor.wxTagWorkflowMap {
		marketoWorkflows := processor.wxTagWorkflowMap[key]
		keyItems := strings.Split(key, ":")
		channelId, operateType := keyItems[0], keyItems[2]
		wxTagId := cast.ToInt(keyItems[1])
		processor.addWxTagForWorkflowMembers(ctx, channelId, operateType, wxTagId, marketoWorkflows)
	}
	processor.updateWxTagWorkflowsAfterProcess(ctx)
}

func (processor *MarketoWorkflowProcessor) addWxTagForWorkflowMembers(ctx context.Context, channelId, operateType string, wxTagId int, marketoWorkflows []*MarketoWorkflow) {
	nweMarketoWorkflows := []*MarketoWorkflow{}
	for i := range marketoWorkflows {
		marketoWorkflow := marketoWorkflows[i]
		// 如果已经存在 err 就不用再执行了，在执行也是报错
		// 需要重试的记录也不再执行，等待恢复执行状态后重新执行
		if marketoWorkflow.err != nil || marketoWorkflow.needRetryFromProcessing {
			continue
		}
		social, isUnsubscribed := GetMemberSocialByChannel(marketoWorkflow.memberDetail, channelId, true)
		if social == nil {
			// social 不存在的直接报错
			marketoWorkflow.err = core_errors.NewNotExistsError("channel")
			if isUnsubscribed {
				marketoWorkflow.err = errors.New("Member is unsubscribed")
			}
			marketoWorkflow.resultType = WORKFLOW_LOG_ERROR_FAILED_ADD_WECHAT_TAG
			if operateType == TAG_TYPE_MINUS {
				marketoWorkflow.resultType = WORKFLOW_LOG_ERROR_FAILED_REMOVE_WECHAT_TAG
			}
			continue
		}
		marketoWorkflow.memberOpenId = social.OpenId
		nweMarketoWorkflows = append(nweMarketoWorkflows, marketoWorkflow)
	}
	workflowsCount := len(nweMarketoWorkflows)
	if workflowsCount == 0 {
		return
	}
	batchSize := 50
	startIndex := 0
	endIndex := startIndex + batchSize
	isDelete := operateType == TAG_TYPE_MINUS
	for {
		if endIndex > workflowsCount {
			endIndex = workflowsCount
		}
		if startIndex == endIndex {
			return
		}
		workflows := nweMarketoWorkflows[startIndex:endIndex]
		openIds := []string{}
		for i := range workflows {
			m := workflows[i]
			openIds = append(openIds, m.memberOpenId)
		}
		// 先批量给客户打标签，打标签失败则说明是某个 openId 更新错误，再分别对每个 openId 操作。
		handleResult := handleWorkflowWechatTag(ctx, wxTagId, channelId, openIds, isDelete)
		for i := range workflows {
			m := workflows[i]
			// 请求响应状态码不是 200 说明请求本身没有经过 weconnect 执行，需要重试。
			if handleResult.StatusCode != 200 {
				m.needRetryFromProcessing = true
				continue
			}
			if isDelete {
				// 对于删除行为中某个 openId 失败导致所有 openIds 失败，weconnect 接口不会自动拆分重试，这里做拆分重试处理
				if handleResult.Err != nil {
					subResult := handleWorkflowWechatTag(ctx, wxTagId, channelId, []string{m.memberOpenId}, isDelete)
					if subResult.Err != nil {
						if subResult.StatusCode != 200 {
							m.needRetryFromProcessing = true
						} else {
							m.resultType = subResult.ErrorType
							m.err = subResult.Err
							if apiErr, ok := subResult.Err.(*util.ApiError); ok {
								reResults := WechatMessageErrorReg.FindStringSubmatch(apiErr.Msg)
								if len(reResults) > 0 {
									code := strings.ReplaceAll(reResults[len(reResults)-1], ",", "")
									m.err = FormatWechatTagErrorByCode(code, apiErr.Msg)
								}
							}
						}
					}
				}
				continue
			}
			code := handleResult.CodeMap[m.memberOpenId]
			if code == "" {
				m.resultType = WORKFLOW_LOG_ERROR_FAILED_ADD_WECHAT_TAG
				m.err = handleResult.Err
				if m.err == nil {
					// 批量打标签的正常响应应该所有 openId 都会有对应的 code
					// 当出现标签不存在等非正常响应时，handleResult.Err 会记录报错，也不应该走到这里
					// 如果出现此报错则需要排查具体原因及时修复
					m.err = errors.New("Invalid response")
				}
				continue
			}
			if code != "0" {
				// 响应码为 0 说明成功，非成功都应该生成错误内容
				m.resultType = fmt.Sprintf("%s:%s", WORKFLOW_LOG_ERROR_FAILED_ADD_WECHAT_TAG, code)
				m.err = FormatWechatTagErrorByCode(code, "")
			}
		}
		startIndex = endIndex
		endIndex += batchSize
	}
}

func (processor *MarketoWorkflowProcessor) updateWxTagWorkflowsAfterProcess(ctx context.Context) {
	updatedMarketoWorkflwoIds := []string{}
	for k := range processor.wxTagWorkflowMap {
		marketoWorkflows := processor.wxTagWorkflowMap[k]
		for i := range marketoWorkflows {
			marketoWorkflow := marketoWorkflows[i]
			if util.StrInArray(marketoWorkflow.Id.Hex(), &updatedMarketoWorkflwoIds) {
				continue
			}
			updatedMarketoWorkflwoIds = append(updatedMarketoWorkflwoIds, marketoWorkflow.Id.Hex())
			// 需要重试，保持 marketoWorkflow 的状态为 processing，由 RestoreStaleProcess 定时唤醒重试。
			if marketoWorkflow.needRetryFromProcessing {
				continue
			}
			marketoWorkflow.Status = WORKFLOW_STATUS_PENDING
			if marketoWorkflow.err != nil {
				workflow, _ := marketoWorkflow.getCurrentWorkflow()
				if workflow.Action.MemberProcessRule != MEMBER_PROCESS_ROLE_FAILED_AND_CONTINUE {
					marketoWorkflow.Status = WORKFLOW_STATUS_ERROR
				}
			}
			marketoWorkflow.updateWorkflowAfterProcessSuspend(ctx)
		}
	}
}

func (processor *MarketoWorkflowProcessor) processRateLimitedActions(ctx context.Context) {
	for _, marketoWorkflow := range processor.rateLimitedActions {
		w, ok := marketoWorkflow.getCurrentWorkflow()
		if !ok {
			continue
		}
		switch w.Action.Type {
		case ACTION_WECHATWORK_MESSAGE:
			marketoWorkflow.processWechatworkMessage(ctx, w)
		case ACTION_WECHAT_MASS:
			marketoWorkflow.processWechatMassMessage(ctx, w)
		case ACTION_WHATSAPP_MESSAGE:
			marketoWorkflow.processWhatsAppMessage(ctx, w)
		case ACTION_WEBHOOK:
			marketoWorkflow.processWebhook(ctx, w)
		}
	}
}

func (m *MarketoWorkflow) processWebhook(ctx context.Context, currentWorkflow Workflow) {
	webhookUrl, err := url.Parse(currentWorkflow.Action.Webhook.Url)
	if err != nil {
		m.handleActionErrorForSuspend(ctx, currentWorkflow, err, WORKFLOW_LOG_ERROR_CALL_WEBHOOK_FAILED, false)
		return
	}
	limiter := getRateLimiter(ACTION_WEBHOOK)
	if limiter != nil && !m.checkRateLimit(ctx, fmt.Sprintf(limiter.Key, util.GetAccountId(ctx), webhookUrl.Host), 1, *limiter) {
		return
	}
	err = callWebhook(ctx, currentWorkflow.Action.Webhook.Url, m)
	if err != nil {
		m.handleActionErrorForSuspend(ctx, currentWorkflow, err, WORKFLOW_LOG_ERROR_CALL_WEBHOOK_FAILED, false)
		return
	}
	m.Status = WORKFLOW_STATUS_PENDING
	m.updateWorkflowAfterProcessSuspend(ctx)
}

func (m *MarketoWorkflow) processWhatsAppMessage(ctx context.Context, currentWorkflow Workflow) {
	var social *origin.OriginInfo
	for _, info := range append(m.memberDetail.Socials, m.memberDetail.OriginFrom) {
		if info.Origin == constant.WHATS_APP {
			social = info
			break
		}
	}
	if social == nil {
		m.handleActionErrorForSuspend(ctx, currentWorkflow, errors.New("member whatsapp social not found"), WORKFLOW_LOG_ERROR_MEMBER_SOCIAL_NOT_FOUND, false)
		return
	}
	if !CMarketingMemberNotificationLimit.Use(
		ctx,
		bson.ObjectIdHex(m.memberDetail.Id),
		m.GetNotificationLimitSetting(NOTIFICATION_TYPE_WHATSAPP),
		NOTIFICATION_TYPE_WHATSAPP,
		fmt.Sprintf("%s:%s", m.Id.Hex(), currentWorkflow.Id),
	) {
		m.handleActionErrorForSuspend(ctx, currentWorkflow, NOTIFICATION_LIMIT_EXCEEDED_ERROR, WORKFLOW_LOG_ERROR_MEMBER_NOTIFICATION_LIMIT_EXCEEDED, false)
		return
	}
	limiter := getRateLimiter(ACTION_WHATSAPP_MESSAGE)
	if limiter != nil && !m.checkRateLimit(ctx, fmt.Sprintf(limiter.Key, util.GetAccountId(ctx)), currentWorkflow.Action.WhatsAppMessage.CountApiUsage(), *limiter) {
		return
	}
	errs := currentWorkflow.Action.WhatsAppMessage.SendMessage(ctx, m.memberDetail, social, m)
	if len(errs) > 0 {
		errorType := WORKFLOW_LOG_ERROR_SEND_WHATSAPP_MESSAGE_FAILED
		if len(errs) < currentWorkflow.Action.WhatsAppMessage.CountApiUsage() {
			errorType = WORKFLOW_LOG_ERROR_SEND_WHATSAPP_MESSAGE_PARTIAL_FAILED
		}
		m.handleActionErrorForSuspend(ctx, currentWorkflow, errors.New(strings.Join(util.StrArrayUnique(errs), ",")), errorType, false)
		return
	}
	// 没报错就更新成 pending，处理下个节点
	m.Status = WORKFLOW_STATUS_PENDING
	m.updateWorkflowAfterProcessSuspend(ctx)
}

// processWechatworkMessage 发句子企微消息
func (m *MarketoWorkflow) processWechatworkMessage(ctx context.Context, currentWorkflow Workflow) {
	// 先找绑定导购，找不到直接报错，不消耗次数
	resp, err := proto_client.GetEcStaffServiceClient().GetStaffMember(ctx, &ec_staff.GetStaffMemberRequest{
		MemberId: m.memberDetail.Id,
	})
	if err != nil {
		m.handleActionErrorForSuspend(ctx, currentWorkflow, err, WORKFLOW_LOG_ERROR_BOUNDED_STAFF_NOT_FOUND, false)
		return
	}
	if !CMarketingMemberNotificationLimit.Use(
		ctx,
		bson.ObjectIdHex(m.memberDetail.Id),
		m.GetNotificationLimitSetting(NOTIFICATION_TYPE_WECONTACT),
		NOTIFICATION_TYPE_WECONTACT,
		fmt.Sprintf("%s:%s", m.Id.Hex(), currentWorkflow.Id),
	) {
		m.handleActionErrorForSuspend(ctx, currentWorkflow, NOTIFICATION_LIMIT_EXCEEDED_ERROR, WORKFLOW_LOG_ERROR_MEMBER_NOTIFICATION_LIMIT_EXCEEDED, false)
	}
	limiter := getRateLimiter(ACTION_WECHATWORK_MESSAGE)
	if limiter != nil && !m.checkRateLimit(ctx, fmt.Sprintf(limiter.Key, util.GetAccountId(ctx)), currentWorkflow.Action.CountJuziApiUsage(), *limiter) {
		return
	}
	var errMsgs []string
	for _, message := range currentWorkflow.Action.WechatworkMessages {
		errs := m.sendWechatworkMessage(ctx, message, resp.Staff)
		for _, err := range errs {
			errMsgs = append(errMsgs, err.Error())
		}
	}
	errMsgs = util.StrArrayUnique(errMsgs)
	if len(errMsgs) > 0 {
		m.handleActionErrorForSuspend(ctx, currentWorkflow, errors.New(strings.Join(errMsgs, ",")), WORKFLOW_LOG_ERROR_SEND_JUZI_MESSAGE_PARTIAL_FAILED, false)
		return
	}
	// 没报错就更新成 pending，处理下个节点
	m.Status = WORKFLOW_STATUS_PENDING
	m.updateWorkflowAfterProcessSuspend(ctx)
}

// 群发公众号消息
func (m *MarketoWorkflow) processWechatMassMessage(ctx context.Context, currentWorkflow Workflow) {
	if memberIsNotDisturb(m.memberDetail) {
		m.handleActionErrorForSuspend(ctx, currentWorkflow, errors.New("Member is not disturbed"), WORKFLOW_LOG_ERROR_MEMBER_IS_NOT_DISTURB, false)
		return
	}
	// 判断客户是否有对应渠道，没有不占用限速
	channels := getSubscribedChannels(m.memberDetail, currentWorkflow.Action.ChannelIds)
	if len(channels) == 0 {
		m.handleActionErrorForSuspend(ctx, currentWorkflow, errors.New("Invalid social channels"), WORKFLOW_LOG_ERROR_TEMPLATE_MESSAGE_UNSUBSCRIBED, false)
		return
	}
	messages := currentWorkflow.Action.getWechatMessages(m.memberDetail, channels[0])
	if len(messages) == 0 {
		m.handleActionErrorForSuspend(ctx, currentWorkflow, errors.New("Invalid wechat message"), WORKFLOW_LOG_ERROR_WECHAT_MASS_MESSAGE_FAILED, false)
		return
	}
	// 限速以公众号为单位
	limiter := getRateLimiter(ACTION_WECHAT_MASS)
	if limiter != nil && !m.checkRateLimit(ctx, fmt.Sprintf(limiter.Key, channels[0]), len(messages), *limiter) {
		return
	}
	// 发消息
	err := currentWorkflow.Action.sendWechatMassMessage(ctx, m.memberDetail, channels[0], messages)
	if err != nil {
		m.handleActionErrorForSuspend(ctx, currentWorkflow, err, WORKFLOW_LOG_ERROR_WECHAT_MASS_MESSAGE_FAILED, false)
		return
	}
	// 没报错就更新成 pending，处理下个节点
	m.Status = WORKFLOW_STATUS_PENDING
	m.updateWorkflowAfterProcessSuspend(ctx)
}

func (m *MarketoWorkflow) processSuspend(ctx context.Context, w Workflow) {
	switch w.Type {
	case WORKFLOW_TYPE_ACTION:
		err, resultType := m.processSuspendAction(ctx, w)
		if err != nil {
			m.handleActionErrorForSuspend(ctx, w, err, resultType, false)
			return
		}
		// 打标签未报错说明打成功了，更新状态成 pending
		m.Status = WORKFLOW_STATUS_PENDING
		m.updateWorkflowAfterProcessSuspend(ctx)
	default:
		return
	}
}

func (m *MarketoWorkflow) processSuspendAction(ctx context.Context, w Workflow) (error, string) {
	if m.IsBatchOperation {
		// if member is blocked, we can only tag/untag him
		if m.memberDetail.BlockedStatus == 2 && w.Action.Type != ACTION_TAG {
			return errors.New("memebr is blocked"), WORKFLOW_LOG_ERROR_MEMBER_IS_BLOCKED
		}
	}

	switch w.Action.Type {
	case ACTION_TAG:
		// 非微信标签不会挂起后处理，这里只用作测试
		if w.Action.Tag.TagType != TAG_TYPE_WECHAT {
			return updateMemberTags(ctx, m.memberDetail.Id, w.Action.Tag)
		}
		// 微信标签都在 processWxTags，不可能走到这里
		return errors.New("unsupported tag action type"), WORKFLOW_LOG_ERROR_SYSTEM_ERROR
	default:
		return errors.New("unsupported action type"), WORKFLOW_LOG_ERROR_SYSTEM_ERROR
	}
}

func (m *MarketoWorkflow) handleActionErr(ctx context.Context, workflow Workflow, err error, resultType string, allowedRollback bool, needLog bool) (continueFailed bool) {
	m.Status = WORKFLOW_STATUS_ERROR
	// handle mairpc error and change resultType if needed
	mErr := core_errors.ToMaiRPCError(err)
	switch mErr.Code {
	// in member batch operation, the blocked member should not receive template message,
	// but in marketo workflow, blocked member could receive template message,
	// so we'll handle these difference in function processAction(), rather than here
	case member_codes.MemberDisabled:
		resultType = WORKFLOW_LOG_ERROR_MEMBERSHIP_CARD_DISABLED
	case member_codes.MemberBlocked, coupon_codes.MemberBlocked, mall_codes.MemberBlocked, product_codes.MemberBlocked:
		resultType = WORKFLOW_LOG_ERROR_MEMBER_IS_BLOCKED
	case core_codes.InternalServerError:
		if mErr.Desc != client.TIMEOUT_ERROR && allowedRollback && m.RetryTimes < MAX_RETRY_TIMES {
			CMarketoWorkflow.batchRollbackProcessWorkflow(ctx, []MarketoWorkflow{*m}, true)
			return false
		}
	}
	// 如果执行规则是失败后终止，则此 workflow status 变为 error
	if workflow.Action.MemberProcessRule != MEMBER_PROCESS_ROLE_FAILED_AND_CONTINUE {
		m.updateAfterProcess(ctx, err, resultType, needLog, Maievent{})
		return false
	}
	return true
}

func (m *MarketoWorkflow) handleActionErrorForSuspend(ctx context.Context, workflow Workflow, err error, resultType string, allowedRollback bool) {
	m.err = err
	m.Status = WORKFLOW_STATUS_ERROR
	m.resultType = resultType
	// 传入 needLog = false，防止 handleActionErr 再生成一条 log
	if m.handleActionErr(ctx, workflow, err, resultType, allowedRollback, false) {
		// 对于出错后继续执行的情况，状态置为 pending
		m.Status = WORKFLOW_STATUS_PENDING
	}
	m.updateWorkflowAfterProcessSuspend(ctx)
}

func getSubscribedChannels(memberDetail *pbMember.MemberDetailResponse, channelIds []string) []string {
	var memberChannels []string
	if memberDetail.OriginFrom != nil {
		memberChannels = append(memberChannels, memberDetail.OriginFrom.Channel)
	}
	for _, social := range memberDetail.Socials {
		memberChannels = append(memberChannels, social.Channel)
	}
	return util.StrArrayDuplicate(memberChannels, channelIds)
}

func (w *MarketoWorkflow) checkRateLimit(ctx context.Context, key string, usage int, limiter RateLimiter) bool {
	limitResult, err := extension.RedisClient.LimitN(key, limiter.Count, usage, limiter.Interval)
	if err != nil || !limitResult.Allowed {
		// 超出限制，保持挂起
		w.rollbackToSuspend(ctx)
		return false
	}
	return true
}

// 因为群发消息只要关注就行，所以不需要像服务消息那样为客户的每个对应的 social（客户合并导致多个相同渠道）都发一遍
func (action *WorkflowAction) sendWechatMassMessage(ctx context.Context, memberDetail *pbMember.MemberDetailResponse, channel string, messages []share_component.Message) error {
	social, _ := GetMemberSocialByChannel(memberDetail, channel, false)
	for _, message := range messages {
		massMessage := &share_component.MassMessage{
			MassiveType: "MASSIVE",
			TotalCount:  "1",
			UserQuery: share_component.UserQuery{
				OriginIds: []string{
					social.OpenId,
				},
			},
			MassMessage: message,
			IsMarketing: true,
		}
		err := share_component.WeConnect.SendMassMessage(ctx, channel, massMessage)
		if err != nil {
			return err
		}
	}
	return nil
}
