package model

import (
	"context"
	"fmt"
	"mairpc/core/errors"
	"mairpc/core/util/copier"
	pb_account_whatsApp "mairpc/proto/account/whatsApp"
	proto_client "mairpc/proto/client"
	"mairpc/proto/common/origin"
	"mairpc/proto/common/request"
	pb_ec_staff_task "mairpc/proto/ec/staffTask"
	"strings"
	"time"

	"github.com/qiniu/qmgo"

	"mairpc/core/client"
	"mairpc/core/component"
	"mairpc/core/extension"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/account"
	"mairpc/proto/marketing"
	pb_member "mairpc/proto/member"
	share_component "mairpc/service/share/component"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"

	"github.com/spf13/cast"
	"github.com/spf13/viper"
)

const (
	C_MARKETO = "marketo"

	MARKETO_STATUS_UNFINISHDRAFT = "unfinishdraft"
	MARKETO_STATUS_DRAFT         = "draft"
	MARKETO_STATUS_WAITING       = "waiting"
	MARKETO_STATUS_ONGOING       = "ongoing"
	MARKETO_STATUS_STOPPED       = "stopped"
	MARKETO_STATUS_ENDED         = "ended"
	MARKETO_STATUS_AUDIT_WAITING = "auditWaiting"
	MARKETO_STATUS_REJECTED      = "rejected"

	MARKETO_TYPE_EVENT = "event"
	MARKETO_TYPE_RULE  = "rule"
	MARKETO_TYPE_ONCE  = "once"

	MARKETO_RULE_TYPE_BIRTHDAY = "birthday"
	MARKETO_RULE_TYPE_HOLIDAY  = "holiday"
	MARKETO_RULE_TYPE_CUSTOM   = "custom"

	MARKETO_RULE_FREQUENCY_DAILY   = "daily"
	MARKETO_RULE_FREQUENCY_WEEKLY  = "weekly"
	MARKETO_RULE_FREQUENCY_MONTHLY = "monthly"
	MARKETO_RULE_FREQUENCY_YEARLY  = "yearly"

	MARKETO_FILTER_TYPE_ALL    = "all"
	MARKETO_FILTER_TYPE_TAG    = "tag"
	MARKETO_FILTER_TYPE_FILE   = "file"
	MARKETO_FILTER_TYPE_GROUP  = "group"
	MARKETO_FILTER_TYPE_CUSTOM = "custom"

	MARKETO_GROUP_TYPE_DYNAMIC = "dynamic"
	MARKETO_GROUP_TYPE_STATIC  = "static"

	MARKETO_FILE_TYPE_CSV  = "csv"
	MARKETO_FILE_TYPE_XLS  = "xls"
	MARKETO_FILE_TYPE_XLSX = "xlsx"

	WORKFLOW_TYPE_ACTION  = "action"
	WORKFLOW_TYPE_CONTROL = "control"
	WORKFLOW_TYPE_END     = "end"

	WORKFLOW_CONTROL_TYPE_EVENT         = "event"
	WORKFLOW_CONTROL_TYPE_PROPERTY      = "property"
	WORKFLOW_CONTROL_TYPE_WAITING       = "waiting"
	WORKFLOW_CONTROL_TYPE_SOCIAL        = "social"
	WORKFLOW_CONTROL_TYPE_EC            = "ec"
	WORKFLOW_CONTROL_TYPE_MEMBER        = "member"
	WORKFLOW_CONTROL_TYPE_TAG           = "tag"
	WORKFLOW_CONTROL_TYPE_TEST          = "test"
	WORKFLOW_CONTROL_TYPE_TEST_GOAL     = "testGoal"
	WORKFLOW_CONTROL_TYPE_MEMBER_GROUP  = "memberGroup"
	WORKFLOW_CONTROL_TYPE_PARENT_RESULT = "parentResult"

	WORKFLOW_CONTROL_EVENT_BRANCH_BINARY = "binary"
	WORKFLOW_CONTROL_EVENT_BRANCH_MULTI  = "multi"

	WORKFLOW_CONTROL_EVENT_LIMIT_PROPERTY = "property"
	WORKFLOW_CONTROL_EVENT_LIMIT_TIMES    = "times"
	WORKFLOW_CONTROL_EVENT_LIMIT_ALL      = "all"

	MARKETO_DATE_IGNORE_TYPE_YEAR = "year"

	WORKFLOW_CONTROL_MEMBER_FIELD_CARD                    = "card"
	WORKFLOW_CONTROL_MEMBER_FIELD_SCORE                   = "score"
	WORKFLOW_CONTROL_MEMBER_FIELD_ACTIVATED_AT            = "activatedAt"
	WORKFLOW_CONTROL_MEMBER_FIELD_ANNUAL_COST_SCORE       = "annualCostScore"
	WORKFLOW_CONTROL_MEMBER_FIELD_ANNUAL_ACCUMULATE_SCORE = "annualAccumulatedScore"
	WORKFLOW_CONTROL_MEMBER_FIELD_ACTIVATION_SOURCE       = "activationSource"
	WORKFLOW_CONTROL_MEMBER_FIELD_TOTAL_SCORE             = "totalScore"
	WORKFLOW_CONTROL_MEMBER_FIELD_TOTAL_COST_SCORE        = "totalCostScore"
	WORKFLOW_CONTROL_MEMBER_FIELD_LEVEL                   = "level"
	WORKFLOW_CONTROL_MEMBER_FIELD_GROWTH                  = "growth"
	WORKFLOW_CONTROL_MEMBER_FIELD_PAID_MEMBER             = "paidMember"
	WORKFLOW_CONTROL_MEMBER_FIELD_ACTIVATION_STORE        = "activationStore"
	WORKFLOW_CONTROL_MEMBER_FIELD_ACTIVATION_STAFF        = "activationStaff"
	WORKFLOW_CONTROL_MEMBER_FIELD_ACTIVATION_CHANNEL      = "activationChannel"
	WORKFLOW_CONTROL_MEMBER_FIELD_SUBSCRIBED_CHANNEL      = "subscribedChannel"

	// http://mairpc/bigdata/blob/develop/docs/api.md#%E6%95%B0%E6%8D%AE%E7%B1%BB%E5%9E%8B
	OPERATOR_IN           = "IN"
	OPERATOR_NIN          = "NOT_IN"
	OPERATOR_EQ           = "EQUALS"
	OPERATOR_NE           = "NOT_EQUALS"
	OPERATOR_GT           = "GT"
	OPERATOR_LT           = "LT"
	OPERATOR_GTE          = "GTE"
	OPERATOR_LTE          = "LTE"
	OPERATOR_NULL         = "IS_NULL"
	OPERATOR_NNULL        = "IS_NOT_NULL"
	OPERATOR_BETWEEN      = "BETWEEN"
	OPERATOR_NOT_BETWEEN  = "NOT_BETWEEN"
	OPERATOR_CONTAINS     = "CONTAINS"
	OPERATOR_NOT_CONTAINS = "NOT_CONTAINS"
	OPERATOR_INANY        = "CONTAINS_ANY"
	OPERATOR_NINANY       = "NOT_CONTAINS_ANY"
	OPERATOR_RLIKE        = "RLIKE"
	OPERATOR_NOT_RLIKE    = "NOT_RLIKE"
	OPERATOR_DEFAULT      = "DEFAULT"

	EVENT_PURCHASE_PRODUCT_SERVICE = "maievent-purchase-product-service"
	// proprtyId from maievent-purchase-product-service
	PROPERTY_ID_ORDER_CREATE_TIME = "orderCreateTime"

	SOCIAL_CHANNEL_TYPE_AUTHORIZED     = "isAuthorized"    // 已授权公众号或者小程序、已加入企业群
	SOCIAL_CHANNEL_TYPE_UNAUTHORIZED   = "isUnauthorized"  // 未授权公众号或者小程序、未加入企业群
	SOCIAL_CHANNEL_TYPE_NOT_AUTHORIZED = "isNotAuthorized" // 从未授权公众号或者小程序、从未加入企业群
	SOCIAL_CHANNEL_TYPE_SUBSCRIBED     = "isSubscribed"    // 已关注公众号、已添加企业好友
	SOCIAL_CHANNEL_TYPE_UNSUBSCRIBED   = "isUnsubscribed"  // 未关注公众号、未添加企业好友
	SOCIAL_CHANNEL_TYPE_NOT_SUBSCRIBED = "isNotSubscribed" // 从未关注公众号、从未添加企业好友
	SOCIAL_CHANNEL_TYPE_USED           = "used"            // 使用过小程序
	SOCIAL_CHANNEL_TYPE_UNUSED         = "unused"          // 未使用过小程序

	GROUP_TYPE_DYNAMIC = "dynamic"
	GROUP_TYPE_STATIC  = "static"

	EMAIL_TYPE_SYSTEM = "system"
	EMAIL_TYPE_CUSTOM = "custom"

	TAG_TYPE_CEP    = "cep"
	TAG_TYPE_WECHAT = "wechat"

	PERIOD_TYPE_START_TO_END      = "startToEnd"
	PERIOD_TYPE_START_TO_INFINITY = "startToInfinity"

	AUDIT_OPERATE_AGREE  = "agree"
	AUDIT_OPERATE_REJECT = "reject"

	AUDITOR_ORIGIN_MESSAGEPUSH = "messagepush"
	AUDITOR_ORIGIN_OTHERS      = "others"

	MEMBER_PROCESS_ROLE_FAILED_AND_STOP     = "failedAndStop"
	MEMBER_PROCESS_ROLE_FAILED_AND_CONTINUE = "failedAndContinue"
)

var (
	CMarketo         = &Marketo{}
	MarketoWeightMap = map[string]int64{
		"event": 999,
		"rule":  500,
		"once":  100,
	}

	ActiveMarketoStatus = []string{
		MARKETO_STATUS_ONGOING,
	}

	// 公众号点击事件只会携带真正触发事件的菜单信息，没有父级菜单信息，需要特殊处理
	SpecialEventIds = []string{
		"maievent-click",
	}

	MarketoTypes = []string{
		MARKETO_TYPE_EVENT,
		MARKETO_TYPE_ONCE,
		MARKETO_TYPE_RULE,
	}

	AuditedStatus = []string{
		MARKETO_STATUS_WAITING,
		MARKETO_STATUS_ONGOING,
		MARKETO_STATUS_STOPPED,
		MARKETO_STATUS_ENDED,
		MARKETO_STATUS_REJECTED,
	}

	SpecialStoreIdEvents = []string{
		share_component.MAIEVENT_PURCHASE_PRODUCT_SERVICE,
		share_component.MAIEVENT_PURCHASE_PRODUCT,
		share_component.MAIEVENT_REFUND_ORDER,
		share_component.MAIEVENT_CANCEL_ORDER,
		share_component.MAIEVENT_ORDER_COMPLETED,
		share_component.MAIEVENT_ORDER_SHIP,
		share_component.MAIEVENT_ORDER_CREATED,
		share_component.MAIEVENT_PAID_MEMBERSHIP_OPEN,
	}

	weekdayMap = map[time.Weekday]uint32{
		time.Monday:    1,
		time.Tuesday:   2,
		time.Wednesday: 3,
		time.Thursday:  4,
		time.Friday:    5,
		time.Saturday:  6,
		time.Sunday:    7,
	}
)

type Marketo struct {
	Id           bson.ObjectId             `bson:"_id"`
	AccountId    bson.ObjectId             `bson:"accountId"`
	CreatedAt    time.Time                 `bson:"createdAt,omitempty"`
	StartAt      time.Time                 `bson:"startAt"`
	FirstStartAt time.Time                 `bson:"firstStartAt,omitempty"`
	Status       string                    `bson:"status"`     // in(unfinishedraft|draft|ongoing|stopped|waiting|ended|auditWaiting|rejected)
	Name         string                    `bson:"name"`       //
	Type         string                    `bson:"type"`       // in(event|rule|once)
	FilterType   string                    `bson:"filterType"` // in(all|tag|group|file)
	Tags         []string                  `bson:"tags"`
	MemberLabels []share_model.MemberLabel `bson:"memberLabels,omitempty"`
	// TODO: support []GroupInfo
	Group                  []GroupInfo     `bson:"group,omitempty"`
	File                   FileInfo        `bson:"file,omitempty"`
	Repeatable             bool            `bson:"repeatable"`
	Event                  MarketoEvent    `bson:"event,omitempty"`
	Rule                   MarketoRule     `bson:"rule,omitempty"`
	Workflows              []Workflow      `bson:"workflows"`
	ShouldStopWorkflows    bool            `bson:"shouldStopWorkflows"`
	HasWorkflowsStopped    bool            `bson:"hasWorkflowsStopped"`
	UpdatedAt              time.Time       `bson:"updatedAt"`
	IsDeleted              bool            `bson:"isDeleted"`
	IsProcessing           bool            `bson:"isProcessing"`
	Duration               int64           `bson:"duration"`
	IsBatchOperation       bool            `bson:"isBatchOperation,omitempty"`
	User                   UserInfo        `bson:"user,omitempty"`
	EstimatedMemberCount   int64           `bson:"estimatedMemberCount"`
	Campaign               Campaign        `bson:"campaign,omitempty"`
	DistributorId          bson.ObjectId   `bson:"distributorId,omitempty"`
	DistributorIds         []bson.ObjectId `bson:"distributorIds,omitempty"`
	EnablePeriod           EnablePeriod    `bson:"enablePeriod"`
	Audit                  Audit           `bson:"audit,omitempty"`
	Remark                 string          `bson:"remark"`
	LastStartAt            time.Time       `bson:"lastStartAt,omitempty"`
	VisibleMemberTags      []string        `bson:"visibleMemberTags,omitempty"`
	VisibleMemberRuleTags  []string        `bson:"visibleMemberRuleTags,omitempty"`
	VisibleMemberModelTags []string        `bson:"visibleMemberModelTags,omitempty"`
}

type Audit struct {
	Auditors               []bson.ObjectId `bson:"auditors"`
	Applicant              UserInfo        `bson:"applicant,omitempty"`
	OperateAuditor         bson.ObjectId   `bson:"operateAuditor,omitempty"`
	AppliedAt              time.Time       `bson:"appliedAt"`
	AuditedAt              time.Time       `bson:"auditedAt,omitempty"`
	RejectReason           string          `bson:"rejectReason,omitempty"`
	Extra                  string          `bson:"extra"`
	AuditorOrigin          string          `bson:"auditorOrigin,omitempty"`
	NeedAllAuditorsConfirm bool            `bson:"needAllAuditorsConfirm,omitempty"`
	Results                []AuditResult   `bson:"results,omitempty"`
	LastNotifiedAt         time.Time       `bson:"lastNotifiedAt,omitempty"`
}

type AuditResult struct {
	AuditorId    bson.ObjectId `bson:"auditorId"`
	AuditedAt    time.Time     `bson:"auditedAt,omitempty"`
	RejectReason string        `bson:"rejectReason,omitempty"`
}

type EnablePeriod struct {
	StartAt time.Time `bson:"startAt"`
	EndAt   time.Time `bson:"endAt"`
	Type    string    `bson:"type"`
}

type GroupInfo struct {
	Id           bson.ObjectId `bson:"id"`
	Name         string        `bson:"name"`
	Type         string        `bson:"type"`
	LastPullTime time.Time     `bson:"lastPullTime"`
}

type FileInfo struct {
	Url  string `bson:"url"`
	Type string `bson:"type"`
}

type UserInfo struct {
	Id   bson.ObjectId `bson:"id"`
	Name string        `bson:"name"`
}

type MarketoEvent struct {
	EventId              string                 `bson:"eventId"`
	PropertyId           string                 `bson:"propertyId"`
	Operator             string                 `bson:"operator"`
	Value                interface{}            `bson:"value"`
	DependentPropertyIds []string               `bson:"dependentPropertyIds"`
	Times                uint32                 `bson:"times"`
	ReserveTime          int64                  `bson:"reserveTime,omitempty"`
	Properties           []MarketoEventProperty `bson:"properties,omitempty"`
	Matchers             []RuleMatcher          `bson:"matchers"`
}

type MarketoEventProperty struct {
	PropertyId           string   `bson:"propertyId"`
	DependentPropertyIds []string `bson:"dependentPropertyIds"`
}

type ControlEvent struct {
	BranchType  string                 `bson:"branchType"`
	LimitType   string                 `bson:"limitType"`
	ReserveTime int64                  `bson:"reserveTime,omitempty"`
	SpecifiedAt time.Time              `bson:"specifiedAt,omitempty"`
	NextTime    string                 `bson:"nextTime,omitempty"`
	EventId     string                 `bson:"eventId"`
	PropertyId  string                 `bson:"propertyId"`
	Properties  []MarketoEventProperty `bson:"properties,omitempty"`

	Rules                []ControlRule `bson:"rules"`
	Times                uint32        `bson:"times"`
	DependentPropertyIds []string      `bson:"dependentPropertyIds"`

	// branchType 等于 all 时的特有字段
	Value    interface{}   `bson:"value"`
	Operator string        `bson:"operator"`
	Matchers []RuleMatcher `bson:"matchers"`
}

type ControlRule struct {
	Operator   string        `bson:"operator"`
	Value      interface{}   `bson:"value"`
	Matchers   []RuleMatcher `bson:"matchers,omitempty"`
	Branch     string        `bson:"branch"`
	BranchName string        `bson:"branchName"`
}

type RuleMatcher struct {
	Operator string      `bson:"operator"`
	Value    interface{} `bson:"value"`
}

func (c ControlRule) GetValue() interface{} {
	return c.Value
}

func (c ControlRule) GetOperator() string {
	return c.Operator
}

type MarketoRule struct {
	Type       string `bson:"type"`
	Date       string `bson:"date,omitempty"`
	DateOffset uint32 `bson:"dateOffset"`
	Frequency  string `bson:"frequency,omitempty"`
	Month      uint32 `bson:"month,omitempty"`
	Day        uint32 `bson:"day,omitempty"`
	Time       string `bson:"time,omitempty"`
}

type Workflow struct {
	Id           string          `bson:"id"`
	ParentId     string          `bson:"parentId,omitempty"`
	ParentBranch string          `bson:"parentBranch,omitempty"`
	Type         string          `bson:"type"` // in(action|control|end)
	Action       WorkflowAction  `bson:"action,omitempty"`
	Control      WorkflowControl `bson:"control,omitempty"`
	IsDeleted    bool            `bson:"isDeleted,omitempty"`
	IsNew        bool            `bson:"isNew,omitempty"`
}

type WorkflowAction struct {
	Type                            string                    `bson:"type"`
	Content                         string                    `bson:"content,omitempty"`
	PropertyValueMaps               []PropertyValueMap        `bson:"propertyValueMaps"`
	ChannelIds                      []string                  `bson:"channelIds,omitempty"`
	MsgType                         string                    `bson:"msgType,omitempty"`
	MediaId                         string                    `bson:"mediaId,omitempty"`
	Title                           string                    `bson:"title,omitempty"`
	Url                             string                    `bson:"url,omitempty"`
	Remark                          string                    `bson:"remark,omitempty"`
	Extra                           interface{}               `bson:"extra,omitempty"`
	Mpnews                          []Mpnews                  `bson:"mpnews,omitempty"`
	News                            []News                    `bson:"news,omitempty"`
	MiniPrograms                    []MiniPrograms            `bson:"miniprograms,omitempty"`
	Coupon                          ActionCoupon              `bson:"coupon,omitempty"`
	Tag                             ActionTag                 `bson:"tag,omitempty"`
	Member                          ActionMember              `bson:"member,omitempty"`
	Customer                        ActionCustomer            `bson:"customer,omitempty"`
	Template                        ActionTemplate            `bson:"template,omitempty"`
	Webhook                         ActionWebhook             `bson:"webhook,omitempty"`
	Email                           ActionEmail               `bson:"email,omitempty"`
	AliyunqaSms                     ActionAliyunqaSms         `bson:"aliyunqaSms,omitempty"`
	WechatworkMessages              []ActionWechatworkMessage `bson:"wechatworkMessages,omitempty"`
	MessageId                       string                    `bson:"messageId,omitempty"`
	MemberProcessRule               string                    `bson:"memberProcessRule,omitempty"`
	StaffOperationTemplateId        bson.ObjectId             `bson:"staffOperationTemplateId,omitempty"`
	RemovedStaffOperationTemplateId bson.ObjectId             `bson:"removedStaffOperationTemplateId,omitempty"`
	WhatsAppMessage                 ActionWhatsAppMessage     `bson:"whatsAppMessage,omitempty"`
	MemberMedal                     ActionMemberMedal         `bson:"memberMedal,omitempty"`
	LineMessage                     ActionLineMessage         `bson:"lineMessage,omitempty"`
	SmsTopic                        string                    `bson:"smsTopic,omitempty"`
}

type BriefMemberMedal struct {
	Id   bson.ObjectId `bson:"id"`
	Name string        `bson:"name"`
}

type ActionMemberMedal struct {
	Type   string             `bson:"type"`
	Medals []BriefMemberMedal `bson:"medals"`
}

type ActionLineMessage struct {
	Content []LineMessageContent `bson:"content,omitempty"`
}

type LineMessageContent struct {
	Type                    string                  `bson:"type"`
	Text                    string                  `bson:"text,omitempty"`
	Media                   LineMedia               `bson:"media,omitempty"`
	ButtonTemplate          LineButtonTemplate      `bson:"buttonTemplate,omitempty"`
	PlaceholderValueSetting PlaceholderValueSetting `bson:"placeholderValueSetting,omitempty"`
}

type LineMedia struct {
	OriginalContentUrl string `bson:"originalContentUrl,omitempty"`
	PreviewImageUrl    string `bson:"previewImageUrl,omitempty"`
	TrackingId         string `bson:"trackingId,omitempty"`
	Duration           int64  `bson:"duration,omitempty"`
}

type LineButtonTemplate struct {
	Title   string       `bson:"title,omitempty"`
	Text    string       `bson:"text,omitempty"`
	Url     string       `bson:"url,omitempty"`
	Actions []LineAction `bson:"actions,omitempty"`
}

type LineAction struct {
	Type  string `bson:"type"`
	Label string `bson:"label,omitempty"`
	Text  string `bson:"text,omitempty"`
	Uri   string `bson:"uri,omitempty"`
}

type ActionWhatsAppMessage struct {
	Type     string                  `bson:"type"`
	Template WhatsAppMessageTemplate `bson:"template,omitempty"`
	Content  []WhatsAppCustomMessage `bson:"content,omitempty"`
}

type WhatsAppMessageTemplate struct {
	Id       bson.ObjectId `bson:"id"`
	Name     string        `bson:"name"`
	Language string        `bson:"language"`
}

type WhatsAppCustomMessage struct {
	Type                    string                  `bson:"type"`
	Text                    string                  `bson:"text"`
	Media                   WhatsAppMedia           `bson:"media"`
	UrlButton               WhatsAppUrlButton       `bson:"urlButton"`
	PlaceholderValueSetting PlaceholderValueSetting `bson:"placeholderValueSetting"`
}

type PlaceholderValueSetting struct {
	Rules []PlaceholderValueRule `bson:"rules"`
}

type PlaceholderValueRule struct {
	Key          string `bson:"key"`
	DefaultValue string `bson:"defaultValue"`
}

type WhatsAppUrlButton struct {
	Header WhatsAppHeader `bson:"header"`
	Body   string         `bson:"body"`
	Footer string         `bson:"footer"`
	Text   string         `bson:"text"`
	Url    string         `bson:"url"`
}

type WhatsAppMedia struct {
	Type        string `bson:"type"`
	Url         string `bson:"url"`
	Name        string `bson:"name"`
	Description string `bson:"description"`
}

type WhatsAppHeader struct {
	Type     string `bson:"type,omitempty"`
	Text     string `bson:"text,omitempty"`
	Url      string `bson:"url,omitempty"`
	FileName string `bson:"fileName,omitempty"`
}

type ActionWechatworkMessage struct {
	Type        string              `bson:"type,omitempty"`
	Text        TextMaterial        `bson:"text,omitempty"`
	Images      []ImageMaterial     `bson:"images,omitempty"`
	Video       VideoMaterial       `bson:"video,omitempty"`
	Link        LinkMaterial        `bson:"link,omitempty"`
	MiniProgram MiniProgramMaterial `bson:"miniProgram,omitempty"`
}

type TextMaterial struct {
	Content string `bson:"content"`
	Title   string `bson:"title"`
}

type ImageMaterial struct {
	Url   string `bson:"url"`
	Title string `bson:"title"`
}

type VideoMaterial struct {
	Url   string `bson:"url"`
	Title string `bson:"title"`
}

type LinkMaterial struct {
	Title            string `bson:"title"`
	Url              string `bson:"url"`
	ShareDescription string `bson:"shareDescription"`
	SharePicture     string `bson:"sharePicture"`
}

type MiniProgramMaterial struct {
	Title              string `bson:"title"`
	AppId              string `bson:"appId"`
	OriginId           string `bson:"originId"`
	Path               string `bson:"path"`
	ContentDescription string `bson:"contentDescription"`
	ShareDescription   string `bson:"shareDescription"`
	Name               string `bson:"name"`
	SharePicture       string `bson:"sharePicture"`
	Icon               string `bson:"icon"`
}

type ActionAliyunqaSms struct {
	TaskName   string `bson:"taskName"`
	PlatformId string `bson:"platformId"`
	SignName   string `bson:"signName"`
	TemplateId string `bson:"templateId"`
}

type ActionEmail struct {
	Type           string `bson:"type"`
	Subject        string `bson:"subject"`
	Body           string `bson:"body"`
	ReplyToAddress string `bson:"replyToAddress"`
	MailType       string `bson:"mailType,omitempty"`
	MailSenderId   string `bson:"mailSenderId"`
}

type ActionWebhook struct {
	Url string `bson:"url"`
}

type ActionTemplate struct {
	ChannelId string         `bson:"channelId"`
	Id        string         `bson:"id"`
	Content   string         `bson:"content"`
	Title     string         `bson:"title"`
	Url       string         `bson:"url"`
	AppId     string         `bson:"appId"`
	PagePath  string         `bson:"pagePath"`
	Data      []TemplateData `bson:"data"`
}

type TemplateData struct {
	Key   string `bson:"key"`
	Value string `bson:"value"`
	Color string `bson:"color"`
}

type PropertyValueMap struct {
	PropertyId     string                     `bson:"propertyId"`
	DefaultValue   string                     `bson:"defaultValue"`
	OptionValueMap map[string]string          `bson:"optionValueMap"`
	DisplayRule    share_model.StringFillRule `bson:"displayRule"`
}

type ActionTag struct {
	Type      string           `bson:"type"`
	Tags      []string         `bson:"tags"`
	TagType   string           `bson:"tagType,omitempty"`
	ChannelId string           `bson:"channelId,omitempty"`
	WxTagMap  map[string]int64 `bson:"wxTagMap,omitempty"`
}

type ActionCoupon struct {
	Type string   `bson:"type"`
	Ids  []string `bson:"ids"`
}

type ActionMember struct {
	Type        string `bson:"type"`
	Score       int64  `bson:"score,omitempty"`
	Growth      int64  `bson:"growth,omitempty"`
	CardId      string `bson:"cardId,omitempty"`
	Description string `bson:"description,omitempty"`
}

type ActionCustomer struct {
	PropertyId   string      `bson:"propertyId"`
	Value        interface{} `bson:"value"`
	PropertyName string      `bson:"propertyName,omitempty"`
	PropertyType string      `bson:"propertyType,omitempty"`
}

type News struct {
	ChannelId  string    `bson:"channelId"`
	MaterialId string    `bson:"materialId"`
	Articles   []Article `bson:"articles"`
}

type MiniPrograms struct {
	AppId         string `bson:"appId"`
	Title         string `bson:"title"`
	PagePath      string `bson:"pagePath"`
	ThumbImageUrl string `bson:"ThumbImageUrl"`
}

type Article struct {
	Title       string `bson:"title"`
	Description string `bson:"description"`
	Url         string `bson:"url"`
	SourceUrl   string `bson:"sourceUrl"`
}

type Mpnews struct {
	ChannelId  string `bson:"channelId"`
	MaterialId string `bson:"materialId"`
}

type WorkflowControl struct {
	Type         string             `bson:"type"`
	SubType      string             `bson:"subType"`
	Event        ControlEvent       `bson:"event,omitempty"`
	Property     ControlProperty    `bson:"property,omitempty"`
	Waiting      WorkflowWaiting    `bson:"waiting,omitempty"`
	Social       WorkflowSocial     `bson:"social,omitempty"`
	Member       ControlMember      `bson:"member,omitempty"`
	Tag          ControlTag         `bson:"tag,omitempty"`
	Test         ControlTest        `bson:"test,omitempty"`
	TestGoal     ControlTestGoal    `bson:"testGoal,omitempty"`
	MemberGroup  ControlMemberGroup `bson:"memberGroup,omitempty"`
	ParentResult ControlResult      `bson:"parentResult,omitempty"`
	TestId       string             `bson:"testId,omitempty"`
	TestGoalId   string             `bson:"testGoalId,omitempty"`
	Extra        interface{}        `bson:"extra,omitempty"`
}

type ControlResult struct {
	BranchType string        `bson:"branchType"`
	Rules      []ControlRule `bson:"rules"`
}

type ControlMemberGroup struct {
	BranchType string        `bson:"branchType"`
	Rules      []ControlRule `bson:"rules"`
}

type ControlProperty struct {
	BranchType     string        `bson:"branchType"`
	Id             bson.ObjectId `bson:"id,omitempty"`
	Name           string        `bson:"name"`
	Type           string        `bson:"type"`
	Rules          []ControlRule `bson:"rules"`
	LocationType   string        `bson:"locationType"`
	DateIgnoreType string        `bson:"dateIgnoreType"`
}

type WorkflowWaiting struct {
	TimeOffset int64     `bson:"timeOffset"`
	SpecificAt time.Time `bson:"specificAt"`
	NextTime   string    `bson:"nextTime"`
	StartTime  string    `bson:"startTime"`
	EndTime    string    `bson:"endTime"`
}

type WorkflowSocial struct {
	BranchType  string        `bson:"branchType"`
	ChannelType string        `bson:"channelType"`
	Field       string        `bson:"field,omitempty"`
	Origins     []string      `bson:"origins,omitempty"`
	Rules       []ControlRule `bson:"rules"`
}

type ControlMember struct {
	BranchType string        `bson:"branchType"`
	Field      string        `bson:"field"`
	Rules      []ControlRule `bson:"rules"`
}

type ControlTag struct {
	BranchType       string            `bson:"branchType"`
	Rules            []ControlRule     `bson:"rules"`
	MemberLabelRules []MemberLabelRule `bson:"memberLabelRules"`
}

type MemberLabelRule struct {
	Operator     string                    `bson:"operator"`
	MemberLabels []share_model.MemberLabel `bson:"memberLabels"`
	Branch       string                    `bson:"branch"`
	BranchName   string                    `bson:"branchName"`
}

type ControlTest struct {
	BranchType         string        `bson:"branchType"`
	Description        string        `bson:"description"`
	LimitTimes         int64         `bson:"limitTimes"`
	UseEfficientBranch bool          `bson:"useEfficientBranch"`
	Rules              []ControlRule `bson:"rules"`
	StatsId            bson.ObjectId `bson:"statsId,omitempty"`
}

type ControlTestGoal struct {
	BranchType string        `bson:"branchType"`
	TestId     string        `bson:"testId"`
	Rules      []ControlRule `bson:"rules"`
}

type WorkflowTestDetail struct {
	Description   string                `bson:"description"`
	WorkflowId    string                `bson:"workflowId"`
	TestTimes     int64                 `bson:"testTimes"`
	EndUseBranch  string                `bson:"endUseBranch"`
	TestDetailMap map[string]TestDetail `bson:"testDetailMap"`
}

type TestDetail struct {
	Branch         string `bson:"branch"`
	TestProportion uint64 `bson:"testProportion"`
	TestTimes      int64  `bson:"testTimes"`
	CompleteCount  int64  `bson:"completeCount"`
}

type Campaign struct {
	Id   bson.ObjectId `bson:"id"`
	Name string        `bson:"name"`
}

func (*Marketo) GetById(ctx context.Context, id string) *Marketo {
	condition := bson.M{
		"_id":       bson.ObjectIdHex(id),
		"isDeleted": false,
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}

	return CMarketo.GetByCondition(ctx, condition)
}

func (*Marketo) GetByCondition(ctx context.Context, condition bson.M) *Marketo {
	marketo := new(Marketo)
	extension.DBRepository.FindOne(ctx, C_MARKETO, condition, marketo)
	if !marketo.Id.Valid() {
		return nil
	}

	return marketo
}

func (*Marketo) GetAllByCondition(ctx context.Context, condition bson.M) []Marketo {
	marketoes := []Marketo{}
	extension.DBRepository.FindAll(ctx, C_MARKETO, condition, []string{}, 0, &marketoes)

	return marketoes
}

func (*Marketo) GetByPagination(ctx context.Context, page, pageSize uint32, orderBys []string, condition bson.M) ([]Marketo, int) {
	sortFields := util.NormalizeOrderBy(orderBys)
	pageCond := extension.PagingCondition{
		Selector:  condition,
		PageIndex: int(page),
		PageSize:  int(pageSize),
		Sortor:    sortFields,
	}
	marketoes := []Marketo{}
	total, _ := extension.DBRepository.FindByPagination(ctx, C_MARKETO, pageCond, &marketoes)

	return marketoes, total
}

func (*Marketo) UpdateAll(ctx context.Context, selector, updater bson.M) (int, error) {
	return Common.UpdateAll(ctx, C_MARKETO, "false", selector, updater)
}

func (*Marketo) UpdateById(ctx context.Context, id bson.ObjectId, updater bson.M) error {
	selector := Common.GenDefaultConditionById(ctx, id)
	return extension.DBRepository.UpdateOne(ctx, C_MARKETO, selector, updater)
}

func (self *Marketo) UpdateStatus(ctx context.Context) error {
	condition := bson.M{
		"_id": self.Id,
	}
	// we'll not update it's updatedAt when toggle status
	setter := bson.M{
		"status":              self.Status,
		"shouldStopWorkflows": self.ShouldStopWorkflows,
		"hasWorkflowsStopped": self.HasWorkflowsStopped,
		"startAt":             self.StartAt,
		"isDeleted":           self.IsDeleted,
		"duration":            self.Duration,
		"updatedAt":           time.Now(),
	}
	if !self.FirstStartAt.IsZero() {
		setter["firstStartAt"] = self.FirstStartAt
	}

	updator := bson.M{
		"$set": setter,
	}

	err := extension.DBRepository.UpdateOne(ctx, C_MARKETO, condition, updator)
	if err != nil {
		return err
	}

	if self.IsDeleted {
		self.afterDeleted(ctx)
	}

	return nil
}

func (self *Marketo) afterDeleted(ctx context.Context) {
	if util.StrInArray(self.Status, &[]string{MARKETO_STATUS_STOPPED, MARKETO_STATUS_ENDED}) {
		component.GO(ctx, func(ctx context.Context) {
			self.removeData(ctx)
		})
	}
	component.GO(ctx, func(ctx context.Context) {
		ids := self.GetBoundStaffOperationTemplateIds()
		if len(ids) > 0 {
			proto_client.GetEcStaffTaskServiceClient().UpdateOperationTemplateBoundMarketo(ctx, &pb_ec_staff_task.UpdateOperationTemplateBoundMarketoRequest{
				MarketoId:          self.Id.Hex(),
				UnboundTemplateIds: ids,
			})
		}
	})
}

func (self *Marketo) GetBoundStaffOperationTemplateIds() []string {
	var ids []string
	for _, workflow := range self.Workflows {
		if workflow.Type == WORKFLOW_TYPE_ACTION && workflow.Action.Type == ACTION_STAFF_OPERATION {
			ids = append(ids, workflow.Action.StaffOperationTemplateId.Hex())
		}
	}
	return ids
}

func (self *Marketo) removeData(ctx context.Context) {
	client.Run(
		"AccountService.CreateJob",
		ctx,
		&account.CreateJobRequest{
			JobName:      "batchdeletemarketoworkflowsandeventstats",
			Module:       "marketing",
			FunctionName: "batchDeleteMarketoWorkflowsAndEventStats",
			Description:  "批量删除智能路径的执行记录",
			Args: core_util.MarshalInterfaceToString(map[string]interface{}{
				"accountId": util.GetAccountId(ctx),
				"marketoId": self.Id.Hex(),
			}),
		},
	)
}

func (self *Marketo) Upsert(ctx context.Context) error {
	condition := bson.M{
		"isDeleted": false,
		"_id":       self.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}

	updator := bson.M{
		"$set": self,
	}

	_, err := extension.DBRepository.Upsert(ctx, C_MARKETO, condition, updator)
	return err
}

func (self *Marketo) CheckMember(ctx context.Context, memberId string, member *pb_member.MemberDetailResponse) bool {
	return self.checkFilterType(ctx, memberId, member)
}

func (self *Marketo) checkFilterType(ctx context.Context, memberId string, member *pb_member.MemberDetailResponse) bool {
	// 优先校验可见标签
	if len(self.VisibleMemberTags) > 0 || len(self.VisibleMemberRuleTags) > 0 || len(self.VisibleMemberModelTags) > 0 {
		resp, _ := proto_client.GetMemberServiceClient().SearchMemberIds(ctx, &pb_member.SearchMemberRequest{
			Ids:       []string{memberId},
			Tags:      self.VisibleMemberTags,
			RuleTags:  self.VisibleMemberRuleTags,
			ModelTags: self.VisibleMemberModelTags,
			ListCondition: &request.ListCondition{
				Page:    1,
				PerPage: 1,
			},
		})
		var memberIds []string
		if resp != nil {
			memberIds = resp.MemberIds
		}
		if !util.StrInArray(memberId, &memberIds) {
			return false
		}
	}
	switch self.FilterType {
	case MARKETO_FILTER_TYPE_ALL:
		return true
	case MARKETO_FILTER_TYPE_TAG:
		if member == nil {
			member, _ = GetMember(ctx, memberId)
		}
		if member != nil {
			for _, tag := range member.Tags {
				if util.StrInArray(tag, &self.Tags) {
					return true
				}
			}
		}
		if len(self.MemberLabels) > 0 {
			values := make(map[string][]string)
			for _, label := range self.MemberLabels {
				values[label.Field] = append(values[label.Field], label.FieldValue)
			}
			checkResp, _ := share_component.Bigdata.CheckLabelMemberIds(ctx, &share_component.CheckLabelMemberIdsRequest{
				MemberIds:   []string{memberId},
				FieldValues: values,
				Conjunction: "OR",
			})
			if checkResp != nil && len(checkResp.MemberIds) > 0 {
				return true
			}
		}
	case MARKETO_FILTER_TYPE_GROUP:
		if self.Group == nil || len(self.Group) == 0 {
			return false
		}

		var (
			dynamicGroups []string
			staticGroups  []string
		)

		for _, g := range self.Group {
			if g.Type == GROUP_TYPE_DYNAMIC {
				dynamicGroups = append(dynamicGroups, g.Id.Hex())
			} else {
				staticGroups = append(staticGroups, g.Id.Hex())
			}
		}

		if len(dynamicGroups) > 0 {
			req := &share_component.CheckMembersRequest{
				MemberIds: []string{memberId},
				GroupIds:  dynamicGroups,
			}
			resp, err := share_component.Bigdata.CheckMembersV2(ctx, "", req)
			if err == nil {
				if resp != nil && len(*resp) > 0 {
					return true
				}
			} else {
				log.Error(ctx, "Got error when call bigdata getGroupsByMemberId", log.Fields{
					"memberId": memberId,
					"error":    err.Error(),
				})
			}
		}

		if len(staticGroups) > 0 {
			resp := Common.SearchStaticGroupUser(ctx, &pb_member.SearchStaticGroupUserRequest{
				SearchKey: memberId,
				GroupIds:  staticGroups,
				ListCondition: &request.ListCondition{
					Page:    1,
					PerPage: 1,
				},
				WithoutTotal: true,
			})
			if resp != nil && len(resp.Items) > 0 {
				return true
			}
		}

		return false
	case MARKETO_FILTER_TYPE_FILE:
		// for marketos which member can continuously joined in
		// these two filter types are not supported now
		return false
	}

	return false
}

func (event *MarketoEvent) CheckEventValue(eventProperties []*marketing.EventProperty) bool {
	event.preproccessValue(eventProperties)
	if len(event.Properties) == 0 {
		return true
	}
	rules := []ControlRule{
		{
			Matchers: event.Matchers,
		},
	}
	matched, _ := matchEventProperties(event.EventId, event.Properties, rules, eventProperties)
	return matched
}

func (event *MarketoEvent) preproccessValue(eventProperties []*marketing.EventProperty) {
	if len(event.Properties) == 0 {
		if event.PropertyId == "" {
			return
		}
		event.Properties = []MarketoEventProperty{
			{
				PropertyId:           event.PropertyId,
				DependentPropertyIds: event.DependentPropertyIds,
			},
		}
		event.Matchers = []RuleMatcher{
			{
				Operator: event.Operator,
				Value:    event.Value,
			},
		}
	}
	prefixMap := event.getDependentPropertiesMap(eventProperties)
	for i, matcher := range event.Matchers {
		prefix, ok := prefixMap[i]
		if !ok {
			continue
		}
		if values, ok := matcher.Value.([]interface{}); ok {
			matcher.Value = trimEventRuleValues(event.EventId, values, prefix)
		}
		event.Matchers[i] = matcher
	}
}

func (event *MarketoEvent) getDependentPropertiesMap(eventProperties []*marketing.EventProperty) map[int]string {
	result := map[int]string{}
	for i, property := range event.Properties {
		if len(property.DependentPropertyIds) == 0 {
			continue
		}
		tempEvent := MarketoEvent{
			PropertyId:           property.PropertyId,
			DependentPropertyIds: property.DependentPropertyIds,
		}
		result[i] = tempEvent.getDependentProperties(eventProperties)
	}
	return result
}

func (event *MarketoEvent) getDependentProperties(eventProperties []*marketing.EventProperty) string {
	var results []string
	for _, dpId := range event.DependentPropertyIds {
		for _, ep := range eventProperties {
			if dpId == ep.PropertyId {
				if pv := ep.GetValueString(); pv != "" {
					results = append(results, pv)
					break
				}

				if pv := ep.GetValueInt(); pv != 0 {
					results = append(results, cast.ToString(pv))
					break
				}

				break
			}
		}
	}

	return strings.Join(results, "")
}

func (m *Marketo) CheckStats(ctx context.Context, memberId bson.ObjectId, eventAt time.Time) bool {
	if m.Event.Times <= 1 {
		return true
	}

	stats := new(MarketoEventStats)

	stats.build(ctx, memberId, m.Id)
	stats.Upsert(ctx, eventAt, m.Event.Times)

	if m.Event.ReserveTime == 0 {
		if stats.Times%m.Event.Times == 0 {
			return true
		}

		return false
	}

	length := len(stats.EventAts)
	if length < int(m.Event.Times) {
		return false
	}

	criticalAt := stats.EventAts[length-1].Add(-time.Millisecond * time.Duration(m.Event.ReserveTime))
	if stats.EventAts[0].After(criticalAt) || stats.EventAts[0].Equal(criticalAt) {
		stats.refresh(ctx, stats.EventAts[length-1])
		return true
	}

	return false
}

func (c *ControlEvent) preproccessRules(eventProperties []*marketing.EventProperty) {
	if len(c.Properties) == 0 {
		if c.PropertyId == "" {
			return
		}
		c.Properties = []MarketoEventProperty{
			{
				PropertyId:           c.PropertyId,
				DependentPropertyIds: c.DependentPropertyIds,
			},
		}
		if c.IsMulti() && c.IsAllLimited() {
			c.Matchers = []RuleMatcher{
				{
					Operator: c.Operator,
					Value:    c.Value,
				},
			}
		}
		for i, rule := range c.Rules {
			rule.Matchers = []RuleMatcher{
				{
					Operator: rule.Operator,
					Value:    rule.Value,
				},
			}
			c.Rules[i] = rule
		}
	}
	// 当 properties 存在时用于映射各位置 property 生成的 prefix
	prefixMap := c.getDependentPropertiesMap(eventProperties)

	var results []ControlRule
	for _, rule := range c.Rules {
		isPrefixTrimed := true
		for i, matcher := range rule.Matchers {
			prefix, ok := prefixMap[i]
			if !ok {
				// 前缀不存在说明该条 matcher 不依赖 dependentPropertyIds，直接处理下一条
				continue
			}
			if values, ok := matcher.Value.([]interface{}); ok {
				matcher.Value = trimEventRuleValues(c.EventId, values, prefix)
			} else {
				// 如果任意 matcher 结构不符合规则，则该 rule 作废，不添加到 result 中
				isPrefixTrimed = false
			}
			rule.Matchers[i] = matcher
		}
		if isPrefixTrimed {
			results = append(results, rule)
		}
	}

	c.Rules = results
}

func trimEventRuleValues(eventId string, values []interface{}, prefix string) []string {
	var sliceTrimed []string
	for _, value := range values {
		sliceValue := cast.ToStringSlice(value)
		if length := len(sliceValue); util.StrInArray(eventId, &SpecialEventIds) && length >= 2 {
			sliceValue = []string{sliceValue[0], sliceValue[length-1]}
		}

		joinedValue := strings.Join(sliceValue, "")
		sliceTrimed = append(sliceTrimed, strings.TrimPrefix(joinedValue, prefix))
	}

	return sliceTrimed
}

func (c *ControlEvent) getDependentProperties(eventProperties []*marketing.EventProperty) string {
	var results []string
	for _, dpId := range c.DependentPropertyIds {
		for _, ep := range eventProperties {
			if dpId == ep.PropertyId {
				if pv := ep.GetValueString(); pv != "" {
					results = append(results, pv)
					break
				}

				if pv := ep.GetValueInt(); pv != 0 {
					results = append(results, cast.ToString(pv))
					break
				}

				break
			}
		}
	}

	return strings.Join(results, "")
}

func (c *ControlEvent) getDependentPropertiesMap(eventProperties []*marketing.EventProperty) map[int]string {
	result := map[int]string{}
	for i, property := range c.Properties {
		if len(property.DependentPropertyIds) == 0 {
			continue
		}
		tempControl := ControlEvent{
			PropertyId:           property.PropertyId,
			DependentPropertyIds: property.DependentPropertyIds,
		}
		result[i] = tempControl.getDependentProperties(eventProperties)
	}
	return result
}

func (m *Marketo) GetFirstWorkflow() Workflow {
	for _, workflow := range m.Workflows {
		if workflow.ParentId == "" {
			return workflow
		}
	}

	return Workflow{}
}

func (m *Marketo) GetSpecifiedWorkflow(parentId, parentBranch string) Workflow {
	for _, workflow := range m.Workflows {
		if workflow.ParentId == parentId && workflow.ParentBranch == parentBranch {
			return workflow
		}
	}
	return Workflow{}
}

func GetEventStartAt(eventId string, eventProperties []*marketing.EventProperty) time.Time {
	if eventId != EVENT_PURCHASE_PRODUCT_SERVICE {
		return time.Now()
	}

	for _, eventProperty := range eventProperties {
		if eventProperty.PropertyId == PROPERTY_ID_ORDER_CREATE_TIME {
			return time.Unix(cast.ToInt64(eventProperty.GetValueInt()/1000), 0)
		}
	}

	return time.Now()
}

func (m *Marketo) getWeight() int64 {
	return MarketoWeightMap[m.Type]
}

func (m *Marketo) isRepeatable() bool {
	return m.Repeatable
}

func (e *ControlEvent) GetFirstRule() ControlRule {
	return e.Rules[0]
}

type Rules []ControlRule

func (r Rules) GetDefaultBranch() string {
	return r[len(r)-1].Branch
}

func (r Rules) HasNullOperator() bool {
	for _, rule := range r {
		if rule.Operator == share_model.OPERATOR_NULL {
			return true
		}
	}

	return false
}

func (r Rules) GetControlRuleByBranch(branch string) (ControlRule, bool) {
	for _, controlRule := range r {
		if controlRule.Branch == branch {
			return controlRule, true
		}
	}
	return ControlRule{}, false
}

func (r Rules) GetNullBranch() string {
	for _, rule := range r {
		if rule.Operator == share_model.OPERATOR_NULL {
			return rule.Branch
		}
	}

	return ""
}

func (e *ControlEvent) GetDefaultBranch() string {
	return Rules(e.Rules).GetDefaultBranch()
}

func (e *ControlEvent) IsBinary() bool {
	return e.BranchType == "" || e.BranchType == WORKFLOW_CONTROL_EVENT_BRANCH_BINARY
}

func (e *ControlEvent) IsMulti() bool {
	return e.BranchType == WORKFLOW_CONTROL_EVENT_BRANCH_MULTI
}

func (e *ControlEvent) IsPropertyLimited() bool {
	return e.LimitType == WORKFLOW_CONTROL_EVENT_LIMIT_PROPERTY
}

func (e *ControlEvent) IsTimesLimited() bool {
	return e.LimitType == WORKFLOW_CONTROL_EVENT_LIMIT_TIMES
}

func (e *ControlEvent) IsAllLimited() bool {
	return e.LimitType == WORKFLOW_CONTROL_EVENT_LIMIT_ALL
}

func (c *ControlProperty) GetDefaultBranch() string {
	return Rules(c.Rules).GetDefaultBranch()
}

func (c WorkflowControl) IsFirstBranch(branch string) bool {
	rules := []ControlRule{}
	switch c.Type {
	case WORKFLOW_CONTROL_TYPE_EVENT:
		rules = c.Event.Rules
	case WORKFLOW_CONTROL_TYPE_MEMBER:
		rules = c.Member.Rules
	case WORKFLOW_CONTROL_TYPE_PROPERTY:
		rules = c.Property.Rules
	case WORKFLOW_CONTROL_TYPE_SOCIAL:
		rules = c.Social.Rules
	case WORKFLOW_CONTROL_TYPE_TAG:
		rules = c.Social.Rules
	case WORKFLOW_CONTROL_TYPE_TEST_GOAL:
		rules = c.TestGoal.Rules
	}
	if len(rules) == 0 {
		return false
	}
	return rules[0].Branch == branch
}

func (w *Workflow) IsTestGoal() bool {
	return w.Type == WORKFLOW_TYPE_CONTROL && w.Control.TestId != "" && w.Control.TestGoalId != ""
}

func (w *Workflow) IsEnd() bool {
	return w.Type == WORKFLOW_TYPE_END
}

func (w *Workflow) NeedSuspend() bool {
	if w.Type != WORKFLOW_TYPE_ACTION {
		return false
	}
	isWechatTag := w.Action.Type == ACTION_TAG && w.Action.Tag.TagType == TAG_TYPE_WECHAT
	isWechatworkMessage := w.Action.Type == ACTION_WECHATWORK_MESSAGE
	isWechatMass := w.Action.Type == ACTION_WECHAT_MASS
	return isWechatTag || isWechatworkMessage || isWechatMass
}

func (m *Marketo) GetWorkflowById(id string) *Workflow {
	return getWorkflowById(m.Workflows, id)
}

func getWorkflowById(workflows []Workflow, id string) *Workflow {
	for _, workflow := range workflows {
		if workflow.Id == id {
			return &workflow
		}
	}

	return nil
}

func (m *Marketo) IsWaiting(id string) bool {
	workflow := m.GetWorkflowById(id)
	if workflow == nil || workflow.Type != WORKFLOW_TYPE_CONTROL {
		return false
	}

	if workflow.Control.Type != WORKFLOW_CONTROL_TYPE_WAITING {
		return false
	}

	return true
}

func (m *Marketo) ShouldStopAllWorkflows() bool {
	return m.ShouldStopWorkflows && util.StrInArray(m.Status, &[]string{
		MARKETO_STATUS_STOPPED,
		MARKETO_STATUS_ENDED,
	})
}

func (m *Marketo) UpdateWorkflows(ctx context.Context) error {
	m.UpdatedAt = time.Now()
	return extension.DBRepository.UpdateOne(ctx, C_MARKETO, bson.M{
		"_id":       m.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}, bson.M{
		"$set": bson.M{
			"updatedAt": m.UpdatedAt,
			"workflows": m.Workflows,
		},
	})
}

func (m *Marketo) UpdateGroupLastPullTime(ctx context.Context, groupId bson.ObjectId, lastPullTime time.Time) error {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       m.Id,
		"group.id":  groupId,
	}
	updater := bson.M{
		"$set": bson.M{
			"updatedAt":            time.Now(),
			"group.$.lastPullTime": lastPullTime,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_MARKETO, condition, updater)
}

func GetTestGoalWorkflowByTestId(workflows []Workflow, testId string) (Workflow, bool) {
	for _, workflow := range workflows {
		if workflow.Control.Type != WORKFLOW_CONTROL_TYPE_TEST_GOAL {
			continue
		}
		if workflow.Control.TestGoal.TestId == testId {
			return workflow, true
		}
	}
	return Workflow{}, false
}

func (*Marketo) GetStatusCountMaps(ctx context.Context, selector bson.M) ([]*marketing.StatusCountMap, error) {
	result := []bson.M{}
	err := extension.DBRepository.Aggregate(ctx, C_MARKETO, []bson.M{
		{
			"$match": selector,
		},
		{
			"$group": bson.M{
				"_id": "$status",
				"count": bson.M{
					"$sum": 1,
				},
			},
		},
	}, false, &result)
	if err != nil {
		return nil, err
	}
	statusCountMaps := []*marketing.StatusCountMap{}
	for _, v := range result {
		statusCountMaps = append(statusCountMaps, &marketing.StatusCountMap{
			Status: cast.ToString(v["_id"]),
			Count:  cast.ToInt64(v["count"]),
		})
	}
	return statusCountMaps, nil
}

func (m Marketo) AutoEnd(ctx context.Context) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["status"] = bson.M{
		"$in": []string{
			// 需要加入 waiting，因为可能审核通过的时候就已经过期了
			MARKETO_STATUS_WAITING,
			MARKETO_STATUS_ONGOING,
			MARKETO_STATUS_STOPPED,
		},
	}
	// 对于结束时间不限的应当滤掉
	selector["enablePeriod.endAt"] = bson.M{
		"$lte": time.Now(),
	}
	selector["enablePeriod.type"] = PERIOD_TYPE_START_TO_END
	marketos := []Marketo{}
	err := extension.DBRepository.FindAll(ctx, C_MARKETO, selector, []string{}, 0, &marketos)
	if err != nil {
		return err
	}
	if len(marketos) == 0 {
		return nil
	}
	needEndIds := []bson.ObjectId{}
	for _, marketo := range marketos {
		// 如果在自动关闭的时候还是执行中，那么需要计算 duration
		if marketo.Status == MARKETO_STATUS_ONGOING {
			duration := marketo.Duration + int64(time.Now().Sub(marketo.StartAt).Seconds()*1000)
			err := extension.DBRepository.UpdateOne(ctx, C_MARKETO, bson.M{"_id": marketo.Id}, bson.M{
				"$set": bson.M{
					"status":   MARKETO_STATUS_ENDED,
					"duration": duration,
				},
			})
			if err != nil {
				return err
			}
			continue
		}
		needEndIds = append(needEndIds, marketo.Id)
	}
	if len(needEndIds) > 0 {
		updater := bson.M{
			"$set": bson.M{
				"status": MARKETO_STATUS_ENDED,
			},
		}
		_, err = extension.DBRepository.UpdateAll(ctx, C_MARKETO, bson.M{"_id": bson.M{"$in": needEndIds}}, updater)
		if err != nil {
			return err
		}
	}
	return m.StopShouldStoppedWorkflowsForEndedMarketos(ctx, needEndIds)
}

func (*Marketo) StopShouldStoppedWorkflowsForEndedMarketos(ctx context.Context, marketoIds []bson.ObjectId) error {
	if len(marketoIds) == 0 {
		return nil
	}
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = bson.M{
		"$in": marketoIds,
	}
	selector["shouldStopWorkflows"] = true
	selector["hasWorkflowsStopped"] = false
	selector["status"] = MARKETO_STATUS_ENDED
	needStopMarketoIds := []interface{}{}
	err := extension.DBRepository.Distinct(ctx, C_MARKETO, selector, "_id", &needStopMarketoIds)
	if err != nil {
		return err
	}
	if len(needStopMarketoIds) == 0 {
		return nil
	}
	component.GO(ctx, func(ctx context.Context) {
		CMarketoWorkflow.StopAllByMarketoIds(ctx, core_util.ToObjectIdArray(needStopMarketoIds))
	})
	return nil
}

func (Marketo) ListNeedStartOnes(ctx context.Context) ([]Marketo, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["status"] = MARKETO_STATUS_WAITING
	selector["enablePeriod.startAt"] = bson.M{
		"$lte": time.Now().Add(5 * time.Minute),
	}
	selector["$or"] = []bson.M{
		{
			"enablePeriod.type": PERIOD_TYPE_START_TO_INFINITY,
		},
		{
			"enablePeriod.type": PERIOD_TYPE_START_TO_END,
			"enablePeriod.endAt": bson.M{
				"$gte": time.Now(),
			},
		},
	}
	result := []Marketo{}
	err := extension.DBRepository.FindAll(ctx, C_MARKETO, selector, []string{}, 0, &result)
	return result, err
}

func (*Marketo) AuditById(ctx context.Context, id bson.ObjectId, operate, rejectReason string) error {
	var (
		marketo = CMarketo.GetByCondition(ctx, bson.M{
			"_id":    id,
			"status": MARKETO_STATUS_AUDIT_WAITING,
		})
		userId            = core_util.GetUserId(ctx)
		operateAuditorId  = bson.NilObjectId
		auditedAuditorIds = marketo.GetAuditedAuditorIds()
	)
	if marketo == nil {
		return errors.NewNotExistsError("marketo")
	}
	// 健合环境存在通过定制接口审核的情况，此时没有 userId
	if bson.IsObjectIdHex(userId) {
		marketoAuditor, err := CMarketoAuditor.GetByUserId(ctx, bson.ObjectIdHex(userId))
		if err != nil {
			return err
		}
		operateAuditorId = marketoAuditor.Id
		// 由于存在审核链接，所以需要判断当前审核人能否处理此路径
		if !util.ObjectIdInArray(operateAuditorId, &marketo.Audit.Auditors) || util.ObjectIdInArray(operateAuditorId, &auditedAuditorIds) {
			return errors.NewCanNotEditError("marketo")
		}
	}
	marketo.Audit.AuditedAt = time.Now()
	marketo.Status = MARKETO_STATUS_WAITING
	if operate == AUDIT_OPERATE_REJECT {
		marketo.Status = MARKETO_STATUS_REJECTED
		marketo.Audit.RejectReason = rejectReason
	}
	if operateAuditorId.Valid() {
		marketo.Audit.OperateAuditor = operateAuditorId
		marketo.Audit.Results = append(marketo.Audit.Results, AuditResult{
			AuditorId:    operateAuditorId,
			AuditedAt:    time.Now(),
			RejectReason: marketo.Audit.RejectReason,
		})
		// 如果需要所有审核人确认，那么需要判断当前是否通过
		if marketo.Audit.NeedAllAuditorsConfirm && operate != AUDIT_OPERATE_REJECT && !marketo.IsAllAuditorConfirmed() {
			marketo.Status = MARKETO_STATUS_AUDIT_WAITING
		}
	}
	setter := bson.M{
		"status": marketo.Status,
		"audit":  marketo.Audit,
	}
	return extension.DBRepository.UpdateOne(ctx, C_MARKETO, Common.GenDefaultConditionById(ctx, marketo.Id), bson.M{"$set": setter})
}

func (*Marketo) ListByAudit(ctx context.Context, condition extension.PagingCondition) (int, []Marketo, error) {
	result := []Marketo{}
	total, err := extension.DBRepository.FindByPagination(ctx, C_MARKETO, condition, &result)
	return total, result, err
}

func (m *Marketo) ApplyAudit(ctx context.Context, auditorIds []bson.ObjectId, extra, auditorOrigin string, applicant UserInfo, needAllAuditorsConfirm bool) error {
	selector := Common.GenDefaultConditionById(ctx, m.Id)
	audit := Audit{
		Auditors:               auditorIds,
		Extra:                  extra,
		AppliedAt:              time.Now(),
		Applicant:              applicant,
		AuditorOrigin:          auditorOrigin,
		NeedAllAuditorsConfirm: needAllAuditorsConfirm,
		LastNotifiedAt:         time.Now(),
	}
	m.Audit = audit
	updater := bson.M{
		"$set": bson.M{
			"audit":  audit,
			"status": MARKETO_STATUS_AUDIT_WAITING,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_MARKETO, selector, updater)
}

func (m *Marketo) NotifyAuditors(ctx context.Context) {
	if m.Audit.AuditorOrigin == AUDITOR_ORIGIN_OTHERS {
		return
	}
	notifiedAuditors := core_util.ExtractArrayStringField("AuditorId", m.Audit.Results)
	needNotifyAuditors := util.StrArrayDiff(util.MongoIdsToStrs(m.Audit.Auditors), notifiedAuditors)
	if len(needNotifyAuditors) == 0 {
		return
	}
	auditors, err := CMarketoAuditor.ListByIds(ctx, util.ToMongoIds(needNotifyAuditors), false)
	if err != nil {
		log.Warn(ctx, "Failed to notify auditors", log.Fields{
			"marketoId": m.Id.Hex(),
		})
		return
	}
	content := fmt.Sprintf(NOTIFICATION_TEMPLATE, m.Audit.Applicant.Name, viper.GetString("redirect-portal-url"), m.Id.Hex(), util.GetAccountId(ctx))
	var failedAuditors []MarketoAuditor
	for _, auditor := range auditors {
		err = auditor.sendEmail(ctx, content)
		if err != nil {
			failedAuditors = append(failedAuditors, auditor)
		}
	}
	for _, failedAuditor := range failedAuditors {
		failedAuditor.sendSms(ctx, content)
	}
	m.UpdateById(ctx, m.Id, bson.M{
		"$set": bson.M{
			"audit.lastNotifiedAt": time.Now(),
		},
	})
}

func (*Marketo) CancelAuditById(ctx context.Context, id bson.ObjectId) error {
	selector := Common.GenDefaultConditionById(ctx, id)
	updater := bson.M{
		"$set": bson.M{
			"status": MARKETO_STATUS_DRAFT,
			"audit":  Audit{},
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_MARKETO, selector, updater)
}

func (*Marketo) StartProcessing(ctx context.Context, id bson.ObjectId) (Marketo, error) {
	selector := Common.GenDefaultConditionById(ctx, id)
	selector["isProcessing"] = false
	updater := bson.M{
		"$set": bson.M{
			"isProcessing": true,
			"lastStartAt":  time.Now(),
		},
	}
	result := Marketo{}
	change := qmgo.Change{
		Update:    updater,
		ReturnNew: true,
	}
	err := extension.DBRepository.FindAndApply(ctx, C_MARKETO, selector, []string{}, change, &result)
	return result, err
}

func (*Marketo) StopProcessing(ctx context.Context, id bson.ObjectId) (Marketo, error) {
	selector := Common.GenDefaultConditionById(ctx, id)
	selector["isProcessing"] = true
	updater := bson.M{
		"$set": bson.M{
			"isProcessing": false,
		},
	}
	result := Marketo{}
	change := qmgo.Change{
		Update:    updater,
		ReturnNew: true,
	}
	err := extension.DBRepository.FindAndApply(ctx, C_MARKETO, selector, []string{}, change, &result)
	return result, err
}

func (self *Marketo) GetRuleIdentifierByTime(t time.Time) string {
	// 只有特殊规则路径需要
	if self.Type != MARKETO_TYPE_RULE {
		return ""
	}
	identifierTime := self.GetFrequencyStartTime(t)
	if identifierTime.IsZero() {
		return ""
	}
	return identifierTime.Format("2006-01-02-15-04")
}

// GetFrequencyStartTime 根据传入时间和路径规则计算传入时间对应的执行周期的起始时间，精确到分钟
func (self *Marketo) GetFrequencyStartTime(t time.Time) time.Time {
	// 生日营销、节日营销周期固定一年
	if self.Rule.Type == MARKETO_RULE_TYPE_BIRTHDAY || self.Rule.Type == MARKETO_RULE_TYPE_HOLIDAY {
		return util.GetStartTimeOfYear(t)
	}
	switch self.Rule.Frequency {
	case MARKETO_RULE_FREQUENCY_DAILY:
		return util.GetStartTimeOfDay(t)
	case MARKETO_RULE_FREQUENCY_WEEKLY:
		return util.GetStartTimeOfWeek(t)
	case MARKETO_RULE_FREQUENCY_MONTHLY:
		return util.GetStartTimeOfMonth(t)
	case MARKETO_RULE_FREQUENCY_YEARLY:
		return util.GetStartTimeOfYear(t)
	}
	return time.Time{}
}

func (self *Marketo) ShouldRunByRule(ctx context.Context, executeTime time.Time) bool {
	// 必须是特殊规则路径
	if self.Type != MARKETO_TYPE_RULE {
		return false
	}
	if self.EnablePeriod.Type == PERIOD_TYPE_START_TO_END && self.EnablePeriod.EndAt.Before(executeTime) {
		return false
	}
	executeTimeStr := executeTime.Format("15:04")
	// rule.time 不匹配不执行
	if self.Rule.Time != executeTimeStr {
		return false
	}
	switch self.Rule.Type {
	case MARKETO_RULE_TYPE_BIRTHDAY:
		return self.Rule.shouldRunForBirthday(executeTime)
	case MARKETO_RULE_TYPE_HOLIDAY:
		return self.Rule.shouldRunForHoliday(executeTime)
	case MARKETO_RULE_TYPE_CUSTOM:
		return self.Rule.shouldRunForCustom(executeTime)
	}
	return false
}

func (rule *MarketoRule) shouldRunForBirthday(executeTime time.Time) bool {
	if rule.Type != MARKETO_RULE_TYPE_BIRTHDAY {
		return false
	}
	switch rule.Frequency {
	case MARKETO_RULE_FREQUENCY_WEEKLY:
		return executeTime.Weekday() == time.Monday
	case MARKETO_RULE_FREQUENCY_MONTHLY:
		return executeTime.Day() == 1
	default:
		return true
	}
}

func (rule *MarketoRule) shouldRunForHoliday(executeTime time.Time) bool {
	if rule.Type != MARKETO_RULE_TYPE_HOLIDAY || rule.Date == "" {
		return false
	}
	targetDate := executeTime.AddDate(0, 0, int(rule.DateOffset))
	solarHolidays := util.GetSolarHolidays(targetDate.Year())
	if holiday, ok := solarHolidays[rule.Date]; ok {
		if holiday.Month() == targetDate.Month() && holiday.Day() == targetDate.Day() {
			return true
		}
	} else {
		lunarHolidays := util.GetLunarHolidaysForSolarDate(targetDate, true)
		if len(lunarHolidays) > 0 && util.StrInArray(rule.Date, &lunarHolidays) {
			return true
		}
	}
	return false
}

func (rule *MarketoRule) shouldRunForCustom(executeTime time.Time) bool {
	if rule.Type != MARKETO_RULE_TYPE_CUSTOM {
		return false
	}
	switch rule.Frequency {
	case MARKETO_RULE_FREQUENCY_DAILY:
		return true
	case MARKETO_RULE_FREQUENCY_WEEKLY:
		// 指定每周几执行时判断是不是匹配
		if weekdayMap[executeTime.Weekday()] == rule.Day {
			return true
		}
	case MARKETO_RULE_FREQUENCY_MONTHLY:
		// 指定每月几号执行时，如果执行时的日数是这一天就执行
		if executeTime.Day() == int(rule.Day) {
			return true
		}
		// 如果还没到指定的日数，但是已经是本月的最后一天了，那么执行
		if executeTime.Day() < int(rule.Day) &&
			util.GetEndTimeOfMonth(executeTime).Day() == executeTime.Day() {
			return true
		}
	case MARKETO_RULE_FREQUENCY_YEARLY:
		// 指定每年几月几号执行时，对于大月份日数匹配就执行，对小月份，月底就执行。
		if int(rule.Month) == int(executeTime.Month()) {
			if executeTime.Day() == int(rule.Day) {
				return true
			}
			if executeTime.Day() < int(rule.Day) &&
				util.GetEndTimeOfMonth(executeTime).Day() == executeTime.Day() {
				return true
			}
		}
	}
	return false
}

func (m *Marketo) NeedStopWorkflows(ctx context.Context) bool {
	condition := Common.GenDefaultCondition(ctx)
	condition["marketoId"] = m.Id
	condition["status"] = bson.M{
		"$in": NeedStopStatusList,
	}
	return CMarketoWorkflow.GetByCondition(ctx, condition) != nil
}

func (rule *MemberLabelRule) GetLabelNames() []string {
	var names []string
	for _, label := range rule.MemberLabels {
		names = append(names, label.Name)
	}
	return names
}

func (tag *ControlTag) GetAllRules() []ControlRule {
	type ruleInfo struct {
		operator string
		values   []string
	}
	branchMap := make(map[string]ruleInfo)
	for _, rule := range tag.Rules {
		branchMap[rule.Branch] = ruleInfo{
			operator: rule.Operator,
			values:   cast.ToStringSlice(rule.Value),
		}
	}
	for _, rule := range tag.MemberLabelRules {
		temp := branchMap[rule.Branch]
		if temp.operator == "" {
			branchMap[rule.Branch] = ruleInfo{
				operator: rule.Operator,
				values:   rule.GetLabelNames(),
			}
			continue
		}
		temp.values = append(temp.values, rule.GetLabelNames()...)
		branchMap[rule.Branch] = temp
	}
	rules := make([]ControlRule, 0, len(branchMap))
	for branch, info := range branchMap {
		rules = append(rules, ControlRule{
			Operator: info.operator,
			Value:    info.values,
			Branch:   branch,
		})
	}
	return rules
}

func (m *ActionWhatsAppMessage) CountApiUsage() int {
	if m.Type == "template" {
		return 1
	}
	return len(m.Content)
}

func (m *ActionWhatsAppMessage) SendMessage(ctx context.Context, memberDetail *pb_member.MemberDetailResponse, social *origin.OriginInfo, workflow *MarketoWorkflow) []string {
	basicRequest := &pb_account_whatsApp.SendMessageRequest{
		From:      social.Channel,
		To:        memberDetail.Phone,
		ChannelId: social.Channel,
		Type:      m.Type,
		PlaceValueHolder: map[string]string{
			"contact:Country":  memberDetail.Country,
			"contact:Email":    memberDetail.Email,
			"contact:Phone":    memberDetail.Phone,
			"contact:Nickname": social.Nickname,
		},
		SendAsync:  true,
		Business:   "marketo",
		BusinessId: fmt.Sprintf("%s:%s", workflow.Id.Hex(), memberDetail.Id),
	}
	if m.Type == "template" {
		template := &pb_account_whatsApp.MessageTaskTemplate{}
		copier.Instance(nil).From(m.Template).CopyTo(template)
		basicRequest.Template = template
		resp, err := proto_client.GetAccountWhatsAppServiceClient().SendMessage(ctx, basicRequest)
		if err != nil {
			return []string{err.Error()}
		} else if resp != nil && resp.ErrorMessage != "" {
			return []string{resp.ErrorMessage}
		}
		return nil
	}
	var (
		errs    []string
		req     = &pb_account_whatsApp.SendCustomMessagesRequest{}
		content []*pb_account_whatsApp.CustomMessageContent
	)
	copier.Instance(nil).From(m.Content).CopyTo(&content)
	copier.Instance(nil).From(basicRequest).CopyTo(req)
	req.Content = content
	resp, err := proto_client.GetAccountWhatsAppServiceClient().SendCustomMessages(ctx, req)
	if err != nil {
		for i := 0; i < len(m.Content); i++ {
			errs = append(errs, err.Error())
		}
		return errs
	}
	for _, result := range resp.Results {
		if result.ErrorMessage != "" {
			errs = append(errs, result.ErrorMessage)
		}
	}
	return errs
}

func (m *Marketo) IsTimeAvaileble(t time.Time) bool {
	switch m.EnablePeriod.Type {
	case PERIOD_TYPE_START_TO_END:
		return m.EnablePeriod.StartAt.Before(t) && m.EnablePeriod.EndAt.After(t)
	case PERIOD_TYPE_START_TO_INFINITY:
		return m.EnablePeriod.StartAt.Before(t)
	default:
		return true
	}
}

func (m *Marketo) GetAuditedAuditorIds() (ids []bson.ObjectId) {
	if m.Audit.OperateAuditor.Valid() {
		ids = append(ids, m.Audit.OperateAuditor)
	}
	for _, result := range m.Audit.Results {
		ids = append(ids, result.AuditorId)
	}
	return
}

func (m *Marketo) IsAllAuditorConfirmed() bool {
	var confirmedAuditorIds []string
	for _, result := range m.Audit.Results {
		if result.RejectReason != "" {
			continue
		}
		confirmedAuditorIds = append(confirmedAuditorIds, result.AuditorId.Hex())
	}
	return len(util.StrArrayUnique(confirmedAuditorIds)) == len(util.StrArrayUnique(util.MongoIdsToStrs(m.Audit.Auditors)))
}
