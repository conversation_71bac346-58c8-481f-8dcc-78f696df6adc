package model

import (
	"context"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	pb_account "mairpc/proto/account"
	"mairpc/proto/client"
	"mairpc/service/share/util"
	"time"
)

const (
	C_MARKETO_AUDITOR = "marketoAuditor"

	NOTIFICATION_TEMPLATE = "%s提交了一个智能路径需要你来审核 %s/navigator#/messagepush/marketing/audit/view/%s?accountId=%s。"
	EMAIL_SUBJECT         = "智能路径待审核"
	EMAIL_SENDER          = "上海群之脉信息科技有限公司"
)

var (
	CMarketoAuditor = &MarketoAuditor{}
)

type MarketoAuditor struct {
	Id            bson.ObjectId `bson:"_id"`
	AccountId     bson.ObjectId `bson:"accountId"`
	IsDeleted     bool          `bson:"isDeleted"`
	CreatedAt     time.Time     `bson:"createdAt"`
	UpdatedAt     time.Time     `bson:"updatedAt"`
	UserId        bson.ObjectId `bson:"userId"`
	Name          string        `bson:"name"`
	Email         string        `bson:"email"`
	IsUserDeleted bool          `bson:"-"`
}

func (*MarketoAuditor) CanAuditSettingBeEnabled(ctx context.Context) (bool, error) {
	selector := Common.GenDefaultCondition(ctx)
	ids := []interface{}{}
	err := extension.DBRepository.Distinct(ctx, C_MARKETO_AUDITOR, selector, "_id", &ids)
	return len(ids) > 0, err
}

func (*MarketoAuditor) Generate(ctx context.Context, accountId bson.ObjectId, userId string) (MarketoAuditor, error) {
	resp, err := client.GetAccountServiceClient().GetUser(ctx, &pb_account.GetUserRequest{
		Id: userId,
	})
	if err != nil {
		return MarketoAuditor{}, err
	}
	m := MarketoAuditor{
		Id:        bson.NewObjectId(),
		AccountId: accountId,
		IsDeleted: false,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		UserId:    bson.ObjectIdHex(userId),
		Email:     resp.Email,
		Name:      resp.Name,
	}
	return m, nil
}

func (*MarketoAuditor) ListByUserIds(ctx context.Context, userIds []bson.ObjectId) ([]MarketoAuditor, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["userId"] = bson.M{
		"$in": userIds,
	}
	result := []MarketoAuditor{}
	err := extension.DBRepository.FindAll(ctx, C_MARKETO_AUDITOR, selector, []string{}, 0, &result)
	return result, err
}

func (m *MarketoAuditor) BatchCreate(ctx context.Context, userIds []string) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}
	existsUserIds, err := m.DistinctUserIds(ctx, selector)
	if err != nil {
		return err
	}
	auditors := []interface{}{}
	needUpdateAuditorUserIds := []bson.ObjectId{}
	aid := util.GetAccountIdAsObjectId(ctx)
	for _, userId := range userIds {
		if util.ObjectIdInArray(bson.ObjectIdHex(userId), &existsUserIds) {
			needUpdateAuditorUserIds = append(needUpdateAuditorUserIds, bson.ObjectIdHex(userId))
			continue
		}
		temp, err := m.Generate(ctx, aid, userId)
		if err != nil {
			return err
		}
		auditors = append(auditors, temp)
	}
	if len(auditors) > 0 {
		_, err = extension.DBRepository.Insert(ctx, C_MARKETO_AUDITOR, auditors...)
		if err != nil {
			return err
		}
	}
	if len(needUpdateAuditorUserIds) > 0 {
		_, err = extension.DBRepository.UpdateAll(ctx, C_MARKETO_AUDITOR, bson.M{
			"userId": bson.M{
				"$in": needUpdateAuditorUserIds,
			},
		}, bson.M{
			"$set": bson.M{
				"isDeleted": false,
				"updatedAt": time.Now(),
			},
		})
	}
	return err
}

func (*MarketoAuditor) ListByPagination(ctx context.Context, condition extension.PagingCondition) (int, []MarketoAuditor, error) {
	result := []MarketoAuditor{}
	total, err := extension.DBRepository.FindByPagination(ctx, C_MARKETO_AUDITOR, condition, &result)
	return total, result, err
}

func (*MarketoAuditor) DeleteByIds(ctx context.Context, ids []bson.ObjectId) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = bson.M{
		"$in": ids,
	}
	updater := bson.M{
		"$set": bson.M{
			"isDeleted": true,
			"updatedAt": time.Now(),
		},
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_MARKETO_AUDITOR, selector, updater)
	return err
}

func (*MarketoAuditor) GetById(ctx context.Context, id bson.ObjectId, containDeleted bool) *MarketoAuditor {
	selecor := Common.GenDefaultConditionById(ctx, id)
	result := &MarketoAuditor{}
	if containDeleted {
		delete(selecor, "isDeleted")
	}
	err := extension.DBRepository.FindOne(ctx, C_MARKETO_AUDITOR, selecor, result)
	if err != nil {
		return nil
	}
	return result
}

func (m *MarketoAuditor) ListIdsByIds(ctx context.Context, ids []bson.ObjectId) ([]bson.ObjectId, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = bson.M{"$in": ids}
	return m.distinctIds(ctx, selector)
}

func (m *MarketoAuditor) ListAllIds(ctx context.Context) ([]bson.ObjectId, error) {
	selector := Common.GenDefaultCondition(ctx)
	return m.distinctIds(ctx, selector)
}

func (*MarketoAuditor) distinctIds(ctx context.Context, selector bson.M) ([]bson.ObjectId, error) {
	result := []interface{}{}
	err := extension.DBRepository.Distinct(ctx, C_MARKETO_AUDITOR, selector, "_id", &result)
	return core_util.ToObjectIdArray(result), err
}

func (*MarketoAuditor) DistinctUserIds(ctx context.Context, selector bson.M) ([]bson.ObjectId, error) {
	var result []interface{}
	err := extension.DBRepository.Distinct(ctx, C_MARKETO_AUDITOR, selector, "userId", &result)
	return core_util.ToObjectIdArray(result), err
}

func (*MarketoAuditor) ListAll(ctx context.Context) ([]MarketoAuditor, error) {
	selector := Common.GenDefaultCondition(ctx)
	var result []MarketoAuditor
	err := extension.DBRepository.FindAll(ctx, C_MARKETO_AUDITOR, selector, []string{}, 0, &result)
	return result, err
}

func (*MarketoAuditor) ListByIds(ctx context.Context, ids []bson.ObjectId, includeDeleted bool) ([]MarketoAuditor, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = bson.M{
		"$in": ids,
	}
	if includeDeleted {
		delete(selector, "isDeleted")
	}
	var result []MarketoAuditor
	err := extension.DBRepository.FindAll(ctx, C_MARKETO_AUDITOR, selector, []string{}, 0, &result)
	return result, err
}

func (m *MarketoAuditor) sendEmail(ctx context.Context, content string) error {
	_, err := client.GetAccountServiceClient().SendSystemEmail(ctx, &pb_account.SendSystemEmailRequest{
		To:         []string{m.Email},
		Body:       content,
		Subject:    EMAIL_SUBJECT,
		SenderName: EMAIL_SENDER,
	})
	return err
}

func (m *MarketoAuditor) sendSms(ctx context.Context, content string) error {
	accountClient := client.GetAccountServiceClient()
	resp, err := accountClient.GetUser(ctx, &pb_account.GetUserRequest{
		Id: m.UserId.Hex(),
	})
	if err != nil {
		return err
	}
	_, err = accountClient.SendSms(ctx, &pb_account.SendSmsRequest{
		Phone: resp.Phone,
		Text:  content,
	})
	return err
}

func (*MarketoAuditor) GetByUserId(ctx context.Context, userId bson.ObjectId) (MarketoAuditor, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["userId"] = userId
	result := MarketoAuditor{}
	err := extension.DBRepository.FindOne(ctx, C_MARKETO_AUDITOR, selector, &result)
	return result, err
}

func (*MarketoAuditor) ListUserIdsByIds(ctx context.Context, ids []bson.ObjectId) []bson.ObjectId {
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = bson.M{
		"$in": ids,
	}
	result := []interface{}{}
	extension.DBRepository.Distinct(ctx, C_MARKETO_AUDITOR, selector, "userId", &result)
	return core_util.ToObjectIdArray(result)
}
