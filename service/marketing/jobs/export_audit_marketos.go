package jobs

import (
	"encoding/csv"
	"fmt"
	"os"
	"strings"
	"time"

	core_util "mairpc/core/util"
	"mairpc/proto/common/request"
	"mairpc/proto/marketing"
	"mairpc/service/marketing/model"
	"mairpc/service/marketing/service"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"

	"github.com/spf13/cobra"
)

func init() {
	RootCmd.AddCommand(exportAuditMarketos)
}

var (
	auditMarketosHeaders = []string{"路径名称", "类型", "有效期", "审核人", "审核时间", "状态"}

	statusMap = map[string]string{
		model.MARKETO_STATUS_UNFINISHDRAFT: "草稿（未完成）",
		model.MARKETO_STATUS_DRAFT:         "草稿",
		model.MARKETO_STATUS_WAITING:       "未开始",
		model.MARKETO_STATUS_ONGOING:       "进行中",
		model.MARKETO_STATUS_STOPPED:       "已停止",
		model.MARKETO_STATUS_ENDED:         "已结束",
		model.MARKETO_STATUS_AUDIT_WAITING: "待审核",
		model.MARKETO_STATUS_REJECTED:      "被驳回",
	}
	typeMap = map[string]string{
		model.MARKETO_TYPE_EVENT: "由客户主动触发的路径",
		model.MARKETO_TYPE_RULE:  "由特殊规则触发的路径",
		model.MARKETO_TYPE_ONCE:  "即时触发的路径",
	}
)

var exportAuditMarketos = &cobra.Command{
	Use: "exportAuditMarketos",
	RunE: func(cmd *cobra.Command, args []string) error {
		jobOptions := util.GetArgs(args)
		ctx := util.GetContextInJob(args)
		ctx = core_util.CtxWithUserID(ctx, jobOptions["userId"].(string))
		ctx = core_util.CtxWithReadSecondaryPreferred(ctx)
		jobId := core_util.GetSreadminJobName()
		err := job_util.BeginToExport(ctx, jobId)
		if err != nil {
			return err
		}

		req := &marketing.ListAuditMarketosRequest{
			Status: "audited",
		}
		currentPage := uint32(1)
		perPage := uint32(100)
		req.ListCondition = &request.ListCondition{
			Page:    currentPage,
			PerPage: perPage,
			OrderBy: []string{"-createdAt"},
		}
		job, _ := job_util.GetJobStatus(ctx, jobId)
		marketingService := service.MarketingService{}
		url, err := job_util.ExportFile(
			ctx,
			job.Name,
			func(f *os.File) error {
				writer := csv.NewWriter(f)
				writer.Write(auditMarketosHeaders)
				for {
					resp, err := marketingService.ListAuditMarketos(ctx, req)
					if err != nil {
						return err
					}
					if len(resp.Items) == 0 {
						break
					}
					for _, auditMarketo := range resp.Items {
						writer.Write(formatAuditMarketoDetail(auditMarketo))
					}
					writer.Flush()
					if currentPage*perPage >= uint32(resp.Total) {
						break
					}
					currentPage++
				}
				return nil
			},
		)
		if err != nil {
			return err
		}
		err = job_util.ExportSucceed(ctx, jobId, url, job.Name)
		if err != nil {
			return err
		}
		return nil
	},
}

func formatAuditMarketoDetail(auditMarketo *marketing.AuditMarketoDetail) []string {
	var auditorNames []string
	if auditMarketo.NeedAllAuditorsConfirm {
		for _, result := range auditMarketo.Results {
			auditorNames = append(auditorNames, result.AuditorName)
		}
	} else {
		auditorNames = []string{
			auditMarketo.AuditorName,
		}
	}
	return []string{
		auditMarketo.Name,
		typeMap[auditMarketo.Type],
		fmt.Sprintf("%s - %s", convertTimeStr(auditMarketo.EnablePeriod.Start), convertTimeStr(auditMarketo.EnablePeriod.End)),
		strings.Join(auditorNames, "、"),
		convertTimeStr(auditMarketo.AuditedAt),
		statusMap[auditMarketo.Status],
	}
}

func convertTimeStr(timeString string) string {
	t, err := time.Parse(time.RFC3339, timeString)
	if err != nil {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}
