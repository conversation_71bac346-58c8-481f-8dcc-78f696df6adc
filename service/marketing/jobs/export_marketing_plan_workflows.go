package jobs

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"github.com/spf13/cobra"
	core_component "mairpc/core/component"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	pb_account "mairpc/proto/account"
	"mairpc/proto/client"
	pb_marketing "mairpc/proto/marketing"
	pb_member "mairpc/proto/member"
	"mairpc/service/marketing/model"
	marketing_service "mairpc/service/marketing/service"
	"mairpc/service/share/component"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"
	"os"
	"strings"
	"sync"
)

func init() {
	RootCmd.AddCommand(exportMarketingWorkflows)
}

var exportMarketingWorkflows = &cobra.Command{
	Use: "exportMarketingPlanWorkflows",
	RunE: func(cmd *cobra.Command, args []string) error {
		var (
			ctx           = core_util.CtxWithReadSecondaryPreferred(util.GetContextInJob(args))
			req           = &pb_marketing.ExportMarketingPlanWorkflowsRequest{}
			workflowsChan = make(chan []model.MarketingPlanWorkflow, 5)
			wg            = &sync.WaitGroup{}
		)
		jobStatus, err := job_util.GetJobStatus(ctx, core_util.GetSreadminJobName())
		if err != nil {
			return err
		}
		util.UnmarshalArgs(args, req)
		err = validators.ValidateRequest(req)
		if err != nil {
			return err
		}
		plan, err := component.Bigdata.GetMarketingPlanDetail(ctx, req.SearchCondition.MarketingPlanId)
		if err != nil {
			return err
		}
		exporter := getMarketingPlanWorkflowsExporter(req, plan, jobStatus)
		err = exporter.Init(ctx)
		if err != nil {
			return err
		}
		wg.Add(1)
		core_component.GO(ctx, func(ctx context.Context) {
			defer wg.Done()
			for {
				workflows, ok := <-workflowsChan
				if !ok {
					break
				}
				_ = exporter.Export(ctx, workflows)
			}
		})
		condition := marketing_service.BuildListMarketingPlanWorkflowsCondition(ctx, req.SearchCondition)
		_ = util.OperateDBByIterationWithGeneric[model.MarketingPlanWorkflow](ctx, model.C_MARKETING_PLAN_WORKFLOW, condition, func(workflows []model.MarketingPlanWorkflow) error {
			workflowsChan <- workflows
			return nil
		}, true, 1000, nil, []string{"_id"})
		close(workflowsChan)
		wg.Wait()
		return exporter.Finish(ctx)
	},
}

func getMarketingPlanWorkflowsExporter(
	req *pb_marketing.ExportMarketingPlanWorkflowsRequest,
	plan *component.MarketingPlanDetail,
	jobStatus *pb_account.JobStatus,
) marketingPlanWorkflowsExporter {
	if req.GroupId != "" {
		return &marketingPlanWorkflowsStaticGroupExporter{
			groupId: req.GroupId,
		}
	}
	return &marketingPlanWorkflowsFileExporter{
		plan:          plan,
		jobStatus:     jobStatus,
		actionGroupId: req.SearchCondition.ActionGroupId,
	}
}

type marketingPlanWorkflowsExporter interface {
	Init(ctx context.Context) error
	Export(ctx context.Context, workflows []model.MarketingPlanWorkflow) error
	Finish(ctx context.Context) error
}

type marketingPlanWorkflowsStaticGroupExporter struct {
	groupId string
}

func (m *marketingPlanWorkflowsStaticGroupExporter) Init(context.Context) error {
	return nil
}

func (m *marketingPlanWorkflowsStaticGroupExporter) Export(ctx context.Context, workflows []model.MarketingPlanWorkflow) error {
	req := &pb_member.BatchCreateStaticGroupMembersRequest{
		GroupIds:  []string{m.groupId},
		MemberIds: core_util.ExtractArrayStringField("MemberId", workflows),
	}
	_, err := client.GetMemberServiceClient().BatchCreateStaticGroupMembers(ctx, req)
	return err
}

func (m *marketingPlanWorkflowsStaticGroupExporter) Finish(context.Context) error {
	return nil
}

type marketingPlanWorkflowsFileExporter struct {
	plan             *component.MarketingPlanDetail
	actionGroupId    string
	actionGroups     map[string]component.MarketingPlanActionGroup
	primaryTarget    component.MarketingPlanTarget
	secondaryTargets []component.MarketingPlanTarget
	jobStatus        *pb_account.JobStatus
	file             *os.File
	sheets           map[string]*core_util.XlsxSheet
	writer           *core_util.XlsxWriter
	filePath         string
}

func (m *marketingPlanWorkflowsFileExporter) Init(ctx context.Context) error {
	// 初始化主要、次要目标列表
	for _, target := range m.plan.Targets {
		if target.Type == model.MARKETING_PLAN_TARGET_TYPE_PRIMARY {
			m.primaryTarget = target
			continue
		}
		m.secondaryTargets = append(m.secondaryTargets, target)
	}
	// 初始化 xlsx 文件
	m.writer = core_util.NewXlsxWriter()
	m.sheets = make(map[string]*core_util.XlsxSheet)
	// 初始化动作列表
	m.actionGroups = make(map[string]component.MarketingPlanActionGroup)
	for i, group := range m.plan.ActionGroups {
		// 如果是导出所有的对照组，那么所有的组都要写入到文件里，每个组一个 Sheet
		if m.actionGroupId == "" || group.Id == m.actionGroupId {
			sheetName := group.Name
			if sheetName == "" {
				if i == 0 {
					sheetName = "对照组"
				} else {
					sheetName = fmt.Sprintf("实验组%d", i)
				}
			}
			m.sheets[group.Id] = m.writer.NewSheet(sheetName)
			m.actionGroups[group.Id] = group
			// 如果指定了动作组，那么就只导出指定的动作组
			if m.actionGroupId != "" {
				break
			}
		}
	}
	// 设置表头
	for _, group := range m.actionGroups {
		sheet := m.sheets[group.Id]
		if sheet == nil {
			continue
		}
		err := sheet.WriteLine(strings.Join(m.formatHeaders(group), ","))
		if err != nil {
			return err
		}
	}
	// 初始化导出文件
	err := job_util.BeginToExport(ctx, core_util.GetSreadminJobName())
	if err != nil {
		return err
	}
	file, path, err := job_util.CreateFileForExport(m.jobStatus.Name)
	if err != nil {
		return err
	}
	m.file = file
	m.filePath = path
	return nil
}

func (m *marketingPlanWorkflowsFileExporter) Export(ctx context.Context, workflows []model.MarketingPlanWorkflow) error {
	var (
		memberNameMap map[string]string
		lines         = make(map[string][]string)
		wg            = &sync.WaitGroup{}
	)
	pool, err := util.NewGoroutinePoolWithPanicHandler(5, util.WithContext(ctx))
	if err != nil {
		return err
	}
	defer pool.Release()
	memberNameMap, _ = marketing_service.GetMemberNameMap(ctx, core_util.ExtractArrayStringField("MemberId", workflows))
	if memberNameMap == nil {
		memberNameMap = make(map[string]string)
	}
	for _, workflow := range workflows {
		wg.Add(1)
		pool.Submit(func() {
			defer wg.Done()
			var (
				line = []string{
					workflow.TriggerAt.Format(core_util.COMMON_TIME_LAYOUT),
					workflow.RunningAt.Format(core_util.COMMON_TIME_LAYOUT),
					memberNameMap[workflow.MemberId.Hex()],
					workflow.MemberId.Hex(),
					formatTargetResult(workflow.TargetResults, m.primaryTarget.Id),
				}
			)
			for _, target := range m.secondaryTargets {
				line = append(line, formatTargetResult(workflow.TargetResults, target.Id))
			}
			for _, result := range workflow.ActionResults {
				line = append(line, formatActionResult(result))
			}
			lines[workflow.ActionGroupId.Hex()] = append(lines[workflow.ActionGroupId.Hex()], strings.Join(line, ","))
		})
	}
	wg.Wait()
	for groupId, groupLines := range lines {
		sheet := m.sheets[groupId]
		if sheet == nil {
			continue
		}
		err = sheet.WriteLines(groupLines)
		if err != nil {
			return err
		}
	}
	return nil
}

func (m *marketingPlanWorkflowsFileExporter) Finish(ctx context.Context) error {
	for _, sheet := range m.sheets {
		err := sheet.Flush()
		if err != nil {
			return err
		}
	}
	err := m.writer.WriteToFile(m.file)
	if err != nil {
		return err
	}
	err = m.file.Sync()
	if err != nil {
		return err
	}
	err = m.file.Close()
	if err != nil {
		return err
	}
	url, err := job_util.ExportByFilePath(ctx, m.jobStatus.Name, m.filePath)
	if err != nil {
		return err
	}
	return job_util.ExportSucceed(ctx, core_util.GetSreadminJobName(), url, m.jobStatus.Name)
}

func (m *marketingPlanWorkflowsFileExporter) formatHeaders(actionGroup component.MarketingPlanActionGroup) []string {
	headers := []string{
		"进入时间",
		"实际执行时间",
		"客户名",
		"客户 id",
		formatTargetName(m.primaryTarget, 0),
	}
	for i, target := range m.secondaryTargets {
		headers = append(headers, formatTargetName(target, i+1))
	}
	for _, action := range actionGroup.MessageActions {
		actionType := "发送短信"
		switch action.Type {
		case "DIGITAL_SMS":
			actionType = "发送数字短信"
		case "TEMPLATE_MESSAGE":
			actionType = "发送模板消息"
		case "SUBSCRIBE_MESSAGE":
			actionType = "发送订阅消息"
		}
		headers = append(headers, actionType)
	}
	for _, action := range actionGroup.DiscountActions {
		actionType := "发放卡券"
		if action.Score > 0 {
			actionType = "发放积分"
		}
		headers = append(headers, actionType)
	}
	for i := 0; i < len(actionGroup.WebhookActions); i++ {
		headers = append(headers, "Webhook")
	}
	return headers
}

func formatTargetName(target component.MarketingPlanTarget, index int) string {
	if target.Name != "" {
		return target.Name
	}
	if target.Type == model.MARKETING_PLAN_TARGET_TYPE_PRIMARY {
		return "主要目标"
	}
	return fmt.Sprintf("次要目标%d", index)
}

func formatTargetResult(results bson.M, targetId string) string {
	result, ok := results[targetId]
	if !ok {
		return "-"
	}
	if cast.ToBool(result) {
		return "已达成"
	}
	return "未达成"
}

func formatLine(line []string) []string {
	for i := range line {
		if line[i] != "" {
			continue
		}
		line[i] = "-"
	}
	return line
}

func formatActionResult(result model.MarketingPlanWorkflowResult) string {
	switch result.Status {
	case model.ACTION_RESULT_STATUS_SENT:
		return "成功"
	case model.ACTION_RESULT_STATUS_FAILED:
		return "失败"
	case model.ACTION_RESULT_STATUS_CONFIRMING:
		return "确认中"
	default:
		return "-"
	}
}
