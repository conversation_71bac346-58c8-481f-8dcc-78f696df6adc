package service

import (
	"context"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	pb_account "mairpc/proto/account"
	"mairpc/proto/client"
	pb_marketing "mairpc/proto/marketing"
	"mairpc/service/marketing/model"
	"mairpc/service/share/util"
)

func (MarketingService) ListAuditors(ctx context.Context, req *pb_marketing.ListAuditorsRequest) (*pb_marketing.ListAuditorsResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	selector := model.Common.GenDefaultCondition(ctx)
	var (
		total         int
		err           error
		auditors      []model.MarketoAuditor
		validAuditors []model.MarketoAuditor
	)
	if req.Unlimited {
		auditors, err = model.CMarketoAuditor.ListAll(ctx)
		total = len(auditors)
	} else {
		total, auditors, err = model.CMarketoAuditor.ListByPagination(ctx, util.FormatPagingCondition(selector, req.ListCondition))
	}
	if err != nil {
		return nil, err
	}
	resp, err := client.GetAccountServiceClient().GetUsers(ctx, &pb_account.UserListRequest{
		Ids:       core_util.ExtractArrayStringField("UserId", auditors),
		Unlimited: true,
	})
	if err != nil {
		return nil, err
	}
	validUserIds := core_util.ExtractArrayStringField("Id", resp.Items)
	for _, auditor := range auditors {
		if !util.StrInArray(auditor.UserId.Hex(), &validUserIds) {
			if req.FilterDeleted {
				continue
			}
			auditor.IsUserDeleted = true
		}
		validAuditors = append(validAuditors, auditor)
	}
	return &pb_marketing.ListAuditorsResponse{
		Total: int64(total),
		Items: formatAuditors(validAuditors),
	}, nil
}

func formatAuditors(auditors []model.MarketoAuditor) []*pb_marketing.AuditorDetail {
	result := []*pb_marketing.AuditorDetail{}
	for _, auditor := range auditors {
		result = append(result, formatAuditor(auditor))
	}
	return result
}

func formatAuditor(auditor model.MarketoAuditor) *pb_marketing.AuditorDetail {
	result := &pb_marketing.AuditorDetail{}
	copier.Instance(nil).From(auditor).CopyTo(result)
	return result
}
