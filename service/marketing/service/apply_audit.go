package service

import (
	"mairpc/core/component"
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/account"
	"mairpc/proto/client"
	"mairpc/proto/common/response"
	pb_marketing "mairpc/proto/marketing"
	"mairpc/service/marketing/model"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

func (MarketingService) ApplyAudit(ctx context.Context, req *pb_marketing.ApplyAuditRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	// 后台提交的审核必须指定审核人
	if !req.ChooseAll && len(req.AuditorIds) == 0 && req.AuditorOrigin != model.AUDITOR_ORIGIN_OTHERS {
		return nil, errors.NewInvalidArgumentErrorWithMessage("auditorIds", "Must provide auditorIds")
	}
	// 校验审核设置
	ok, err := model.CMarketoAuditSetting.CheckAvailable(ctx)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.NewInvalidArgumentErrorWithMessage("audit setting", "Audit setting is not available")
	}
	// 校验智能路径状态
	marketo := model.CMarketo.GetById(ctx, req.MarketoId)
	if marketo == nil {
		return nil, errors.NewNotExistsErrorWithMessage("marketoId", "Cannot find marketo with request marketoId")
	}
	if !util.StrInArray(marketo.Status, &[]string{model.MARKETO_STATUS_DRAFT, model.MARKETO_STATUS_REJECTED}) {
		return nil, errors.NewInvalidArgumentErrorWithMessage("marketo status", "Only draft and rejected marketo can apply audit")
	}
	// 获取选中的审核人
	var auditors []model.MarketoAuditor
	// 如果审核人不在群脉后台，则审核人字段置空
	if req.AuditorOrigin == model.AUDITOR_ORIGIN_OTHERS {
		auditors = []model.MarketoAuditor{}
	} else if req.ChooseAll {
		auditors, err = model.CMarketoAuditor.ListAll(ctx)
		if err != nil {
			return nil, err
		}
	} else {
		auditors, err = model.CMarketoAuditor.ListByIds(ctx, util.ToMongoIds(req.AuditorIds), false)
		if err != nil {
			return nil, err
		}
		if len(auditors) != len(req.AuditorIds) {
			return nil, errors.NewInvalidArgumentError("auditorIds")
		}
	}
	userIds := core_util.ExtractArrayStringField("UserId", auditors)
	resp, err := client.GetAccountServiceClient().GetUsers(ctx, &account.UserListRequest{
		Ids:       userIds,
		Unlimited: true,
	})
	if err != nil {
		return nil, err
	}
	validUserIds := core_util.ExtractArrayStringField("Id", resp.Items)
	if len(validUserIds) == 0 {
		return nil, errors.NewInvalidArgumentError("auditorIds")
	}
	auditorIds := core_util.ExtractArrayFieldWithJudge("Id", auditors, func(auditor model.MarketoAuditor) bool {
		return util.StrInArray(auditor.UserId.Hex(), &validUserIds)
	})
	applicant, err := genApplicant(ctx)
	if err != nil {
		return nil, err
	}
	err = marketo.ApplyAudit(ctx, core_util.ToObjectIdArray(auditorIds), req.Extra, req.AuditorOrigin, applicant, req.NeedAllAuditorsConfirm)
	if err != nil {
		return nil, err
	}
	component.GO(ctx, func(ctx context.Context) {
		marketo.NotifyAuditors(ctx)
	})
	return &response.EmptyResponse{}, nil
}

func genApplicant(ctx context.Context) (model.UserInfo, error) {
	result := model.UserInfo{}
	userId := core_util.GetUserId(ctx)
	if !bson.IsObjectIdHex(userId) {
		return result, errors.NewInvalidArgumentError("userId")
	}
	user, err := client.GetAccountServiceClient().GetUser(ctx, &account.GetUserRequest{
		Id: userId,
	})
	if err != nil {
		return result, err
	}
	result.Id = bson.ObjectIdHex(user.Id)
	result.Name = user.Name
	return result, nil
}
