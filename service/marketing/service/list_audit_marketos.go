package service

import (
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/common/types"
	pb_marketing "mairpc/proto/marketing"
	"mairpc/service/marketing/model"
	"mairpc/service/share/util"
	"time"

	"golang.org/x/net/context"
)

func (MarketingService) ListAuditMarketos(ctx context.Context, req *pb_marketing.ListAuditMarketosRequest) (*pb_marketing.ListAuditMarketosResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	userId := core_util.GetUserId(ctx)
	if !bson.IsObjectIdHex(userId) {
		return nil, errors.NewInvalidArgumentError("userId")
	}
	auditor, _ := model.CMarketoAuditor.GetByUserId(ctx, bson.ObjectIdHex(userId))
	if auditor.Id.IsZero() {
		return &pb_marketing.ListAuditMarketosResponse{
			Total: 0,
			Items: []*pb_marketing.AuditMarketoDetail{},
		}, nil
	}
	selector := model.Common.GenDefaultCondition(ctx)
	selector["audit.auditors"] = auditor.Id
	selector["status"] = model.MARKETO_STATUS_AUDIT_WAITING
	// 查询待审核列表时，排除需要多人审核同时当前审核员已审核的路径
	selector["audit.results.auditorId"] = bson.M{"$ne": auditor.Id}
	if req.Status == "audited" {
		// 查询已审核列表时，仅考虑 audit 结构匹配
		delete(selector, "audit.results.auditorId")
		delete(selector, "status")
		selector["$or"] = []bson.M{
			{"audit.operateAuditor": auditor.Id},
			{"audit.results.auditorId": auditor.Id},
		}
	}
	condition := util.FormatPagingCondition(selector, req.ListCondition)
	total, marketos, err := model.CMarketo.ListByAudit(ctx, condition)
	if err != nil {
		return nil, err
	}
	return &pb_marketing.ListAuditMarketosResponse{
		Total: int64(total),
		Items: formatAuditMarketos(ctx, marketos),
	}, nil
}

func formatAuditMarketo(ctx context.Context, marketo model.Marketo) *pb_marketing.AuditMarketoDetail {
	if len(marketo.Audit.Auditors) == 0 {
		return nil
	}
	var (
		result      = pb_marketing.AuditMarketoDetail{}
		auditors, _ = model.CMarketoAuditor.ListByIds(ctx, marketo.Audit.Auditors, true)
		auditorMap  = make(map[bson.ObjectId]model.MarketoAuditor)
	)
	for _, auditor := range auditors {
		auditorMap[auditor.Id] = auditor
	}
	copier.Instance(nil).From(marketo).CopyTo(&result)
	copier.Instance(nil).RegisterResetDiffField([]copier.DiffFieldPair{
		{
			Origin:  "Applicant.Name",
			Targets: []string{"Applicant"},
		},
	}).RegisterTransformer(copier.Transformer{
		"Results": func(results []model.AuditResult) []*pb_marketing.AuditResult {
			var items []*pb_marketing.AuditResult
			for _, auditResult := range results {
				items = append(items, &pb_marketing.AuditResult{
					AuditorId:     auditResult.AuditorId.Hex(),
					AuditorUserId: auditorMap[auditResult.AuditorId].UserId.Hex(),
					AuditorName:   auditorMap[auditResult.AuditorId].Name,
					AuditedAt:     auditResult.AuditedAt.Format(time.RFC3339),
					RejectReason:  auditResult.RejectReason,
				})
			}
			return items
		},
	}).From(marketo.Audit).CopyTo(&result)
	result.EnablePeriod = &types.StringDateRange{
		Start: marketo.EnablePeriod.StartAt.Format(time.RFC3339),
		End:   marketo.EnablePeriod.EndAt.Format(time.RFC3339),
	}
	result.AuditorName = auditorMap[marketo.Audit.OperateAuditor].Name
	result.SelectedAuditorUserIds = core_util.ToStringArray(core_util.ExtractArrayStringField("UserId", auditors))
	return &result
}

func formatAuditMarketos(ctx context.Context, marketos []model.Marketo) []*pb_marketing.AuditMarketoDetail {
	var result []*pb_marketing.AuditMarketoDetail
	for _, marketo := range marketos {
		result = append(result, formatAuditMarketo(ctx, marketo))
	}
	return result
}
