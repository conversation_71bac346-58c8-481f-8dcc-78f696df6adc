package service

import (
	"context"
	"fmt"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	pb_account "mairpc/proto/account"
	"mairpc/proto/client"
	"mairpc/proto/common/response"
	pb_member "mairpc/proto/member"
	"mairpc/service/share/component"
	"mairpc/service/share/util"
	"time"

	pb_marketing "mairpc/proto/marketing"
)

func (MarketingService) ExportMarketingPlanWorkflows(ctx context.Context, req *pb_marketing.ExportMarketingPlanWorkflowsRequest) (*response.JobResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	planDetail, err := component.Bigdata.GetMarketingPlanDetail(ctx, req.SearchCondition.MarketingPlanId)
	if err != nil {
		return nil, err
	}
	if req.GroupId == "" && req.GroupName != "" {
		resp, err := client.GetMemberServiceClient().CreateMemberStaticGroup(ctx, &pb_member.CreateMemberStaticGroupRequest{
			Name:        req.GroupName,
			Description: req.GroupDescription,
		})
		if err != nil {
			return nil, err
		}
		req.GroupId = resp.Value
	}
	createJobReq := &pb_account.CreateJobRequest{
		JobName:      "exportmarketingplanworkflows",
		Module:       "marketing",
		FunctionName: "exportMarketingPlanWorkflows",
		Args:         core_util.MarshalInterfaceToString(req),
		Description:  "每次导出营销计划详情列表创建一个",
	}
	if req.GroupId == "" {
		createJobReq.Type = "export"
		createJobReq.DisplayName = fmt.Sprintf("智能营销_营销计划_%s_%s.xlsx", planDetail.Name, util.GetJobTimestamp(time.Now()))
	}
	resp, err := client.GetAccountServiceClient().CreateJob(ctx, createJobReq)
	if err != nil {
		return nil, err
	}
	return &response.JobResponse{
		JobId: resp.JobId,
		Key:   resp.Key,
	}, nil
}
