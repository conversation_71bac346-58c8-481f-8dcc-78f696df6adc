package service

import (
	"context"
	"mairpc/core/component"
	"mairpc/core/errors"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/service/marketing/model"
)

func (MarketingService) ResendMarketoAuditNotification(ctx context.Context, req *request.DetailRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	marketo := model.CMarketo.GetById(ctx, req.Id)
	if marketo == nil {
		return nil, errors.NewNotExistsError("marketo")
	}
	if marketo.Status != model.MARKETO_STATUS_AUDIT_WAITING {
		return nil, errors.NewInvalidArgumentError("status")
	}
	component.GO(ctx, func(ctx context.Context) {
		marketo.NotifyAuditors(ctx)
	})
	return &response.EmptyResponse{}, nil
}
