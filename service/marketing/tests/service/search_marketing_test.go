package service

import (
	"mairpc/core/extension"
	"mairpc/proto/common/request"
	"mairpc/proto/common/types"
	"mairpc/proto/marketing"
	"mairpc/service/marketing/model"
	"mairpc/service/test"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"mairpc/core/extension/bson"
)

func (self *MarketingSuite) TestSearchMarketoNormally() {
	ctx := self.GetContext()
	mockTags := []string{"一级用户", "二级用户"}
	mockEventId := "aabbccddee"
	mockEventValue := []string{"群硕测试", "群脉服务"}
	req := &marketing.SearchMarketoRequest{
		Type:   []string{self.defaultMarketo.Type},
		Status: []string{self.defaultMarketo.Status},
		ListCondition: &request.ListCondition{
			Page:    2,
			PerPage: 10,
			OrderBy: []string{"-_id"},
		},
		Tags: mockTags,
		Repeatable: &types.BoolValue{
			Value: true,
		},
		Event: &marketing.MarketoEvent{
			EventId:  mockEventId,
			Operator: "in",
			Value: &marketing.MarketoEvent_ValueStringArray{
				ValueStringArray: &marketing.EventArrayValue{
					Value: mockEventValue,
				},
			},
		},
	}
	self.MockDbRepository().On(
		"FindByPagination",
		ctx,
		model.C_MARKETO,
		mock.MatchedBy(func(pageCond extension.PagingCondition) bool {
			condition := pageCond.Selector
			matchMap := map[string]interface{}{
				"accountId":      self.GetAccountId(),
				"isDeleted":      false,
				"type":           bson.M{"$in": []string{self.defaultMarketo.Type}},
				"status":         bson.M{"$in": []string{self.defaultMarketo.Status}},
				"tags":           mockTags,
				"repeatable":     true,
				"event.eventId":  mockEventId,
				"event.operator": "in",
				"event.value":    mockEventValue,
			}
			return test.IsMatched(condition, matchMap)
		}),
		mock.Anything,
	).Run(func(args mock.Arguments) {
		test.DeepCopy([]model.Marketo{self.defaultMarketo}, args.Get(3))
	}).Return(1, nil).Once()
	resp, err := marketingService.SearchMarketo(ctx, req)
	if assert.Nil(self.T(), err) {
		assert.Equal(self.T(), uint64(1), resp.TotalCount)
	}
}
