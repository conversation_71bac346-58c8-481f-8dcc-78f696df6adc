package service

import (
	"mairpc/proto/marketing"
	"mairpc/service/marketing/codes"
	"mairpc/service/marketing/model"
	"mairpc/service/test"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"mairpc/core/extension/bson"
)

func (self *MarketingSuite) TestGetMarketoNormally() {
	ctx := self.GetContext()
	req := &marketing.GetMarketoRequest{
		Id:   self.defaultMarketo.Id.Hex(),
		Name: self.defaultMarketo.Name,
	}
	self.MockDbRepository().On(
		"FindOne",
		ctx,
		model.C_MARKETO,
		mock.MatchedBy(func(condition bson.M) bool {
			return test.ContainsValue(self.defaultMarketo.Id, condition, "_id") &&
				test.ContainsValue(self.defaultMarketo.Name, condition, "name")
		}),
		mock.Anything,
	).Run(func(args mock.Arguments) {
		test.DeepCopy(self.defaultMarketo, args.Get(3))
	}).Return(nil).Once()

	resp, err := marketingService.GetMarketo(ctx, req)

	assert.NotNil(self.T(), resp)
	assert.Nil(self.T(), err)
	assert.Equal(self.T(), resp.CreatedAt, self.defaultMarketo.CreatedAt.Unix())
	assert.Equal(self.T(), resp.FirstStartAt, self.defaultMarketo.FirstStartAt.Unix())
}

func (self *MarketingSuite) TestGetMarketoFail() {
	ctx := self.GetContext()
	// Test invalid params
	req := &marketing.GetMarketoRequest{}
	resp, err := marketingService.GetMarketo(ctx, req)
	assert.Nil(self.T(), resp)
	self.AssertErrorCode(err, codes.InvalidParams)

	// Test invalid id
	req.Id = "invalid id"
	resp, err = marketingService.GetMarketo(ctx, req)
	assert.Nil(self.T(), resp)
	self.AssertErrorCode(err, codes.InvalidMongoId)

	// Test marketo not found
	req.Id = self.defaultMarketo.Id.Hex()
	self.MockDbRepository().On(
		"FindOne",
		ctx,
		model.C_MARKETO,
		mock.MatchedBy(func(condition bson.M) bool {
			return test.ContainsValue(self.defaultMarketo.Id, condition, "_id")
		}),
		mock.Anything,
	).Return(bson.ErrNotFound).Once()
	resp, err = marketingService.GetMarketo(ctx, req)
	assert.Nil(self.T(), resp)
	self.AssertErrorCode(err, codes.MarketoNotFound)
}
