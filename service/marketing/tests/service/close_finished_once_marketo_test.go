package service

import (
	"mairpc/core/extension/bson"
	"mairpc/proto/common/request"
	"mairpc/service/marketing/model"
	"mairpc/service/test"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func (self *MarketingSuite) TestCloseFinishedOnceMarketoNormally() {
	self.MockDbRepository().On(
		"FindAll",
		self.GetContext(),
		model.C_MARKETO,
		mock.Anything,
		mock.Anything,
		mock.Anything,
		mock.Anything,
	).Run(func(args mock.Arguments) {
		test.DeepCopy([]model.Marketo{self.defaultMarketo}, args.Get(5))
	}).Return(nil).Once()

	self.MockDbRepository().On(
		"FindOne",
		self.GetContext(),
		model.C_MARKETO_WORK_FLOW,
		mock.MatchedBy(func(condition bson.M) bool {
			return test.ContainsValue(self.defaultMarketo.Id, condition, "marketoId")
		}),
		mock.Anything,
		mock.Anything,
		mock.Anything,
	).Return(bson.ErrNotFound).Once()

	self.MockDbRepository().On(
		"UpdateAll",
		self.GetContext(),
		model.C_MARKETO,
		mock.Anything,
		mock.MatchedBy(func(updator bson.M) bool {
			return test.ContainsValue(model.MARKETO_STATUS_STOPPED, updator, "$set", "status")
		}),
	).Return(1, nil).Once()

	resp, err := marketingService.CloseFininshedOnceMarketo(self.GetContext(), &request.EmptyRequest{})

	assert.Nil(self.T(), err)
	assert.Len(self.T(), resp.Marketoes, 1)
}
