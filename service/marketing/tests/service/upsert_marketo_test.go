package service

import (
	"context"
	"time"

	"mairpc/core/extension/bson"
	"mairpc/proto/common/types"
	"mairpc/proto/marketing"
	"mairpc/service/marketing/model"
	share_model "mairpc/service/share/model"
	"mairpc/service/test"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func (self *MarketingSuite) TestUpsertMarketoNormally() {
	ctx := self.GetContext()

	// marketo.Type == event
	// marketo.workflow[0].Type == control
	// marketo.workflow[0].control.Type = event
	// marketo.workflow[1].Type == action
	// marketo.workflow[1].action.Type = template
	req := getDefaultUpsertMarketoReqest()

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)
	assert.Equal(self.T(), req.Event, resp.Event)
	assert.Equal(self.T(), req.Workflows[0].Control.Event, resp.Workflows[0].Control.Event)
	assert.Equal(
		self.T(),
		req.Workflows[1].Action.PropertyValueMaps[0].OptionValueMap,
		resp.Workflows[1].Action.PropertyValueMaps[0].OptionValueMap,
	)
	assert.Equal(self.T(), req.Workflows[0].Control.Extra, resp.Workflows[0].Control.Extra)
	assert.Equal(self.T(), req.EnablePeriod.Start, resp.EnablePeriod.Start)
	assert.Equal(self.T(), req.EnablePeriod.End, resp.EnablePeriod.End)
	assert.Equal(self.T(), req.ShouldStopWorkflows, resp.ShouldStopWorkflows)
	assert.Nil(self.T(), resp.Workflows[0].Control.Property)
	assert.Nil(self.T(), resp.Workflows[0].Control.Waiting)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithProperty() {
	ctx := self.GetContext()

	// marketo.Type == event
	// marketo.workflow[0].Type == control
	// marketo.workflow[0].control.Type = property
	req := getDefaultUpsertMarketoReqest()
	req.Workflows[0].Control.Type = model.WORKFLOW_CONTROL_TYPE_PROPERTY

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)
	assert.Equal(self.T(), req.Event, resp.Event)
	assert.Nil(self.T(), resp.Workflows[0].Control.Event)
	assert.Equal(self.T(), req.Workflows[0].Control.Property, resp.Workflows[0].Control.Property)
	assert.Nil(self.T(), resp.Workflows[0].Control.Waiting)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithWaiting() {
	ctx := self.GetContext()

	// marketo.Type == rule
	// marketo.workflow[0].Type == control
	// marketo.workflow[0].control.Type = waiting
	req := getDefaultUpsertMarketoReqest()
	req.Type = model.MARKETO_TYPE_RULE
	req.Workflows[0].Control.Type = model.WORKFLOW_CONTROL_TYPE_WAITING

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)
	assert.Equal(self.T(), resp.Event, &marketing.MarketoEvent{
		Properties: []*marketing.MarketoEventProperty{},
		Matchers:   []*marketing.RuleMatcher{},
	})
	assert.Equal(self.T(), req.Rule, resp.Rule)
	assert.Nil(self.T(), resp.Workflows[0].Control.Event)
	assert.Nil(self.T(), resp.Workflows[0].Control.Property)
	assert.Equal(self.T(), req.Workflows[0].Control.Waiting, resp.Workflows[0].Control.Waiting)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithAction() {
	ctx := self.GetContext()

	// marketo.Type == event
	// marketo.workflow[0].Type == action
	req := getDefaultUpsertMarketoReqest()
	req.Workflows[0].Type = model.WORKFLOW_TYPE_ACTION

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)

	expectedAction := req.Workflows[0].Action
	expectedAction.News = []*marketing.News{}
	expectedAction.Mpnews = []*marketing.Mpnews{}
	expectedAction.MiniPrograms = []*marketing.MiniPrograms{}
	assert.Equal(self.T(), expectedAction.Type, resp.Workflows[0].Action.Type)
	assert.Equal(self.T(), expectedAction.Content, resp.Workflows[0].Action.Content)

	assert.Nil(self.T(), resp.Workflows[0].Control)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithAction_Text() {
	ctx := self.GetContext()

	// marketo.Type == event
	// marketo.workflow[0].Type == action
	req := getDefaultUpsertMarketoReqest()
	req.Workflows[0].Type = model.WORKFLOW_TYPE_ACTION
	req.Workflows[0].Action.Type = model.ACTION_WECHAT
	req.Workflows[0].Action.MsgType = model.MSGTYPE_TEXT

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)

	expectedAction := req.Workflows[0].Action
	expectedAction.Mpnews = []*marketing.Mpnews{}
	expectedAction.MiniPrograms = []*marketing.MiniPrograms{}
	assert.Equal(self.T(), expectedAction.MsgType, resp.Workflows[0].Action.MsgType)
	assert.Equal(self.T(), expectedAction.Content, resp.Workflows[0].Action.Content)
	assert.Equal(self.T(), expectedAction.Title, resp.Workflows[0].Action.Title)
	assert.Equal(self.T(), expectedAction.PropertyValueMaps, resp.Workflows[0].Action.PropertyValueMaps)
	assert.Equal(self.T(), expectedAction.ChannelIds, resp.Workflows[0].Action.ChannelIds)

	assert.Nil(self.T(), resp.Workflows[0].Control)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithAction_Image() {
	ctx := self.GetContext()

	// marketo.Type == event
	// marketo.workflow[0].Type == action
	req := getDefaultUpsertMarketoReqest()
	req.Workflows[0].Type = model.WORKFLOW_TYPE_ACTION
	req.Workflows[0].Action.Type = model.ACTION_WECHAT
	req.Workflows[0].Action.MsgType = model.MSGTYPE_IMAGE

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)

	expectedAction := req.Workflows[0].Action
	expectedAction.Mpnews = []*marketing.Mpnews{}
	expectedAction.MiniPrograms = []*marketing.MiniPrograms{}
	assert.Equal(self.T(), expectedAction.MsgType, resp.Workflows[0].Action.MsgType)
	assert.Equal(self.T(), expectedAction.Content, resp.Workflows[0].Action.Content)
	assert.Equal(self.T(), expectedAction.Title, resp.Workflows[0].Action.Title)
	assert.Equal(self.T(), expectedAction.Url, resp.Workflows[0].Action.Url)
	assert.Equal(self.T(), expectedAction.ChannelIds, resp.Workflows[0].Action.ChannelIds)

	assert.Nil(self.T(), resp.Workflows[0].Control)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithAction_MsgTypeNews() {
	ctx := self.GetContext()

	// marketo.Type == event
	// marketo.workflow[0].Type == action
	req := getDefaultUpsertMarketoReqest()
	req.Workflows[0].Type = model.WORKFLOW_TYPE_ACTION
	req.Workflows[0].Action.Type = model.ACTION_WECHAT
	req.Workflows[0].Action.MsgType = model.MSGTYPE_NEWS

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)

	expectedAction := req.Workflows[0].Action
	expectedAction.Mpnews = []*marketing.Mpnews{}
	expectedAction.MiniPrograms = []*marketing.MiniPrograms{}
	assert.Equal(self.T(), expectedAction.MsgType, resp.Workflows[0].Action.MsgType)
	assert.Equal(self.T(), expectedAction.News, resp.Workflows[0].Action.News)

	assert.Nil(self.T(), resp.Workflows[0].Control)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithAction_MsgTypeMpnews() {
	ctx := self.GetContext()

	// marketo.Type == event
	// marketo.workflow[0].Type == action
	req := getDefaultUpsertMarketoReqest()
	req.Workflows[0].Type = model.WORKFLOW_TYPE_ACTION
	req.Workflows[0].Action.Type = model.ACTION_WECHAT
	req.Workflows[0].Action.MsgType = model.MSGTYPE_MPNEWS

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)

	expectedAction := req.Workflows[0].Action
	expectedAction.News = []*marketing.News{}
	expectedAction.MiniPrograms = []*marketing.MiniPrograms{}
	assert.Equal(self.T(), expectedAction.MsgType, resp.Workflows[0].Action.MsgType)
	assert.Equal(self.T(), expectedAction.Mpnews, resp.Workflows[0].Action.Mpnews)

	assert.Nil(self.T(), resp.Workflows[0].Control)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithAction_MsgTypeMiniPrograms() {
	ctx := self.GetContext()
	req := getDefaultUpsertMarketoReqest()
	req.Workflows[0].Type = model.WORKFLOW_TYPE_ACTION
	req.Workflows[0].Action.Type = model.ACTION_WECHAT
	req.Workflows[0].Action.MsgType = model.MSGTYPE_MINI_PROGRAM

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)

	expectedAction := req.Workflows[0].Action
	expectedAction.News = []*marketing.News{}
	expectedAction.Mpnews = []*marketing.Mpnews{}
	assert.Equal(self.T(), expectedAction.MiniPrograms, resp.Workflows[0].Action.MiniPrograms)

	assert.Nil(self.T(), resp.Workflows[0].Control)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithAction_Tag() {
	ctx := self.GetContext()

	// marketo.Type == event
	// marketo.workflow[0].Type == action
	req := getDefaultUpsertMarketoReqest()
	req.Workflows[0].Type = model.WORKFLOW_TYPE_ACTION
	req.Workflows[0].Action = &marketing.Action{
		Type: "tag",
		Tag: &marketing.ActionTag{
			Type: "all",
			Tags: []string{"any_1", "any_2"},
		},
		Mpnews:       []*marketing.Mpnews{},
		News:         []*marketing.News{},
		MiniPrograms: []*marketing.MiniPrograms{},
	}

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)

	expectedAction := req.Workflows[0].Action
	assert.Equal(self.T(), expectedAction, resp.Workflows[0].Action)

	assert.Nil(self.T(), resp.Workflows[0].Control)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithAction_Coupon() {
	ctx := self.GetContext()

	// marketo.Type == event
	// marketo.workflow[0].Type == action
	req := getDefaultUpsertMarketoReqest()
	req.Workflows[0].Type = model.WORKFLOW_TYPE_ACTION
	req.Workflows[0].Action = &marketing.Action{
		Type: "coupon",
		Coupon: &marketing.ActionCoupon{
			Type: "all",
			Ids:  []string{test.NewMongoId().Hex()},
		},
		Mpnews:       []*marketing.Mpnews{},
		News:         []*marketing.News{},
		MiniPrograms: []*marketing.MiniPrograms{},
	}

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)

	expectedAction := req.Workflows[0].Action
	assert.Equal(self.T(), expectedAction, resp.Workflows[0].Action)

	assert.Nil(self.T(), resp.Workflows[0].Control)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithAction_Memeber() {
	ctx := self.GetContext()

	// marketo.Type == event
	// marketo.workflow[0].Type == action
	req := getDefaultUpsertMarketoReqest()
	req.Workflows[0].Type = model.WORKFLOW_TYPE_ACTION
	req.Workflows[0].Action = &marketing.Action{
		Type: "member",
		Member: &marketing.ActionMember{
			Type:   "card",
			CardId: test.NewMongoId().Hex(),
		},
		Mpnews:       []*marketing.Mpnews{},
		News:         []*marketing.News{},
		MiniPrograms: []*marketing.MiniPrograms{},
	}

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)

	expectedAction := req.Workflows[0].Action
	assert.Equal(self.T(), expectedAction, resp.Workflows[0].Action)

	assert.Nil(self.T(), resp.Workflows[0].Control)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithAction_Customer() {
	ctx := self.GetContext()

	// marketo.Type == event
	// marketo.workflow[0].Type == action
	req := getDefaultUpsertMarketoReqest()
	req.Workflows[0].Type = model.WORKFLOW_TYPE_ACTION
	req.Workflows[0].Action = &marketing.Action{
		Type: "customer",
		Customer: &marketing.ActionCustomer{
			PropertyId: test.NewMongoId().Hex(),
			Value: &marketing.ActionCustomer_ValueString{
				ValueString: &marketing.PropertyStringValue{
					Value: "test",
				},
			},
		},
		Mpnews:       []*marketing.Mpnews{},
		News:         []*marketing.News{},
		MiniPrograms: []*marketing.MiniPrograms{},
	}

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)

	expectedAction := req.Workflows[0].Action
	assert.Equal(self.T(), expectedAction, resp.Workflows[0].Action)

	assert.Nil(self.T(), resp.Workflows[0].Control)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithAction_Webhook() {
	ctx := self.GetContext()

	// marketo.Type == event
	// marketo.workflow[0].Type == action
	req := getDefaultUpsertMarketoReqest()
	req.Workflows[0].Type = model.WORKFLOW_TYPE_ACTION
	req.Workflows[0].Action = &marketing.Action{
		Type: "webhook",
		Webhook: &marketing.ActionWebhook{
			Url: "ajax.quncrm.com",
		},
		Mpnews:       []*marketing.Mpnews{},
		News:         []*marketing.News{},
		MiniPrograms: []*marketing.MiniPrograms{},
	}

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.NotNil(self.T(), resp)

	expectedAction := req.Workflows[0].Action
	assert.Equal(self.T(), expectedAction, resp.Workflows[0].Action)
	assert.Nil(self.T(), resp.Workflows[0].Control)
	assert.Nil(self.T(), err)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithControl_Member() {
	req := getDefaultUpsertMarketoReqest()
	req.Workflows[0].Type = model.WORKFLOW_TYPE_CONTROL
	req.Workflows[0].Control.Type = model.WORKFLOW_CONTROL_TYPE_MEMBER

	self.mockDbUpsertMarketo(self.GetContext())
	resp, err := marketingService.UpsertMarketo(self.GetContext(), req)

	assert := assert.New(self.T())
	assert.Nil(err)
	assert.NotNil(resp)
	assert.Equal(resp.Workflows[0].Control.Member, req.Workflows[0].Control.Member)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithTags() {
	ctx := self.GetContext()
	req := getDefaultUpsertMarketoReqest()
	req.Type = model.MARKETO_TYPE_ONCE
	req.FilterType = model.MARKETO_FILTER_TYPE_TAG
	req.Tags = []string{"mockTag"}

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.Nil(self.T(), err)
	assert.Equal(self.T(), resp.Tags, req.Tags)
	assert.Equal(self.T(), resp.Group, []*marketing.GroupInfo{})
	assert.Empty(self.T(), resp.File)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithGroup() {
	ctx := self.GetContext()
	req := getDefaultUpsertMarketoReqest()
	req.Type = model.MARKETO_TYPE_ONCE
	req.FilterType = model.MARKETO_FILTER_TYPE_GROUP
	req.Group = []*marketing.GroupInfo{
		&marketing.GroupInfo{
			Id:   bson.NewObjectId().Hex(),
			Name: "mockGroupName",
			Type: "dynamic",
		},
	}

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.Nil(self.T(), err)
	assert.Equal(self.T(), resp.Group[0].Id, req.Group[0].Id)
	assert.Equal(self.T(), resp.Group[0].Name, req.Group[0].Name)
	assert.Equal(self.T(), resp.Group[0].Type, req.Group[0].Type)
	assert.Empty(self.T(), resp.Tags)
	assert.Empty(self.T(), resp.File)
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithFile() {
	ctx := self.GetContext()
	req := getDefaultUpsertMarketoReqest()
	req.Type = model.MARKETO_TYPE_ONCE
	req.FilterType = model.MARKETO_FILTER_TYPE_FILE
	req.File = &marketing.FileInfo{
		Url:  "https://www.mockfileurl.com",
		Type: "csv",
	}

	self.mockDbUpsertMarketo(ctx)
	resp, err := marketingService.UpsertMarketo(ctx, req)

	assert.Nil(self.T(), err)
	assert.Equal(self.T(), resp.File.Url, req.File.Url)
	assert.Equal(self.T(), resp.File.Type, req.File.Type)
	assert.Empty(self.T(), resp.Tags)
	assert.Equal(self.T(), resp.Group, []*marketing.GroupInfo{})
}

func (self *MarketingSuite) TestUpsertMarketoNormallyWithDelete() {
	req := getDefaultUpsertMarketoReqest()
	req.Id = bson.NewObjectId().Hex()
	req.Workflows = []*marketing.Workflow{
		&marketing.Workflow{
			Id:   "randStrin1",
			Type: model.WORKFLOW_TYPE_CONTROL,
			Control: &marketing.Control{
				Type: model.WORKFLOW_CONTROL_TYPE_WAITING,
				Waiting: &marketing.Waiting{
					TimeOffset: 123,
					SpecificAt: time.Now().Unix(),
					NextTime:   "11:22",
				},
			},
			IsDeleted: true,
		},
		&marketing.Workflow{
			Id:       "randStrin2",
			ParentId: "randStrin1",
			Type:     model.WORKFLOW_TYPE_CONTROL,
			Control: &marketing.Control{
				Type: model.WORKFLOW_CONTROL_TYPE_WAITING,
				Waiting: &marketing.Waiting{
					TimeOffset: 123,
					SpecificAt: time.Now().Unix(),
					NextTime:   "11:22",
				},
			},
			IsDeleted: true,
		},
		&marketing.Workflow{
			Id:       "randStrin3",
			ParentId: "randStrin2",
			Type:     model.WORKFLOW_TYPE_END,
		},
	}

	self.MockDbRepository().On(
		"FindOne",
		self.GetContext(),
		model.C_MARKETO,
		mock.Anything,
		mock.Anything,
	).Return(nil).Once().Run(func(args mock.Arguments) {
		test.DeepCopy(model.Marketo{
			Id:        bson.NewObjectId(),
			AccountId: bson.NewObjectId(),
			Workflows: []model.Workflow{
				model.Workflow{
					Id:   "randStrin1",
					Type: model.WORKFLOW_TYPE_CONTROL,
					Control: model.WorkflowControl{
						Type: model.WORKFLOW_CONTROL_TYPE_WAITING,
						Waiting: model.WorkflowWaiting{
							TimeOffset: 123,
							SpecificAt: time.Now(),
							NextTime:   "11:22",
						},
					},
					IsDeleted: false,
				},
				model.Workflow{
					Id:       "randStrin2",
					ParentId: "randStrin1",
					Type:     model.WORKFLOW_TYPE_CONTROL,
					Control: model.WorkflowControl{
						Type: model.WORKFLOW_CONTROL_TYPE_WAITING,
						Waiting: model.WorkflowWaiting{
							TimeOffset: 123,
							SpecificAt: time.Now(),
							NextTime:   "11:22",
						},
					},
					IsDeleted: true,
				},
				model.Workflow{
					Id:       "randStrin3",
					ParentId: "randStrin2",
					Type:     model.WORKFLOW_TYPE_END,
				},
			},
		}, args.Get(3))
	})

	self.mockDbUpsertMarketo(self.GetContext())

	stopWaiting := self.MockDbRepository().On(
		"UpdateAll",
		self.GetContext(),
		model.C_MARKETO_WORK_FLOW,
		mock.Anything,
		mock.Anything,
	).Return(1, nil).Once()

	resp, err := marketingService.UpsertMarketo(self.GetContext(), req)

	self.WaitCallsDone(0, stopWaiting)
	assert.Nil(self.T(), err)
	assert.NotNil(self.T(), resp)
	assert.True(self.T(), resp.Workflows[1].IsDeleted)
}

func (self *MarketingSuite) mockDbUpsertMarketo(ctx context.Context) {
	self.MockDbRepository().On(
		"Upsert",
		ctx,
		model.C_MARKETO,
		mock.Anything,
		mock.Anything,
	).Return(nil, nil).Once()
}

func getDefaultUpsertMarketoReqest() *marketing.UpsertMarketoRequest {
	return &marketing.UpsertMarketoRequest{
		StartAt:             time.Now().Unix(),
		Status:              model.MARKETO_STATUS_DRAFT,
		Name:                "NewMarketo",
		Type:                model.MARKETO_TYPE_EVENT,      // event, rule, once
		FilterType:          model.MARKETO_FILTER_TYPE_ALL, // all, tag
		ShouldStopWorkflows: true,
		User: &marketing.UserInfo{
			Id:   bson.NewObjectId().Hex(),
			Name: "mock user",
		},
		Event: &marketing.MarketoEvent{
			EventId:              "anything",
			PropertyId:           "anything",
			Operator:             "IN",
			Times:                3,
			ReserveTime:          10800000,
			DependentPropertyIds: []string{"channelName"},
			Value: &marketing.MarketoEvent_ValueStringDoubleDimensionalArray{
				ValueStringDoubleDimensionalArray: &marketing.DoubleDimensionalArrayValue{
					Value: []*marketing.EventArrayValue{
						&marketing.EventArrayValue{
							Value: []string{"channel test", "menu test"},
						},
					},
				},
			},
			Properties: []*marketing.MarketoEventProperty{},
		},
		Rule: &marketing.MarketoRule{
			Date:       "国庆节",
			DateOffset: 20,
			Type:       "holiday", // birthday, holiday
		},
		Workflows: []*marketing.Workflow{
			&marketing.Workflow{
				Id:   "randTenStr",
				Type: "control", // control, action, end
				Action: &marketing.Action{
					Type:    "sms", // sms, wechat
					Content: "anything",
					PropertyValueMaps: []*marketing.PropertyValueMap{
						&marketing.PropertyValueMap{
							PropertyId:   "name",
							DefaultValue: "xiao东篱",
							OptionValueMap: map[string]string{
								"male":   "男士",
								"female": "女士",
							},
							DisplayRule: &marketing.DisplayRule{},
						},
					},
					ChannelIds: []string{"a", "b"},
					MsgType:    "TEXT", // TEXT, IMAGE, NEWS, MPNEWS
					Title:      "anything",
					Url:        "anything",
					Mpnews: []*marketing.Mpnews{
						&marketing.Mpnews{
							ChannelId:  "anything",
							MaterialId: "anything",
						},
					},
					News: []*marketing.News{
						&marketing.News{
							ChannelId: "anything",
							Articles: []*marketing.Article{
								&marketing.Article{
									Title:       "anything",
									Description: "anything",
									Url:         "anything",
									SourceUrl:   "anything",
								},
							},
						},
					},
					MiniPrograms: []*marketing.MiniPrograms{
						&marketing.MiniPrograms{
							AppId:         "wx9490daa1d8e3dd2a",
							Title:         "anything",
							PagePath:      "anything",
							ThumbImageUrl: "anything",
						},
					},
				},
				Control: &marketing.Control{
					Extra: `{"hello":"world"}`,
					Type:  "event", // event, property, waiting
					Event: &marketing.ControlEvent{
						ReserveTime:          123,
						SpecifiedAt:          "2022-08-09T10:01:56+08:00",
						EventId:              "event_id",
						PropertyId:           "anything",
						Times:                4,
						DependentPropertyIds: []string{"channelName"},
						Rules: []*marketing.ControlRule{
							&marketing.ControlRule{
								Operator: "IN",
								Value: &marketing.ArbitraryValue{
									Value: &marketing.ArbitraryValue_ValueStringDoubleDimensionalArray{
										ValueStringDoubleDimensionalArray: &marketing.DoubleDimensionalArrayValue{
											Value: []*marketing.EventArrayValue{
												&marketing.EventArrayValue{
													Value: []string{"channel test", "menu test"},
												},
											},
										},
									},
								},
								Branch: "branch_name",
							},
						},
						Properties: []*marketing.MarketoEventProperty{},
					},
					Property: &marketing.ControlProperty{
						Id:    bson.NewObjectId().Hex(),
						Type:  "",
						Rules: []*marketing.ControlRule{},
					},
					Waiting: &marketing.Waiting{
						TimeOffset: 123,
						SpecificAt: time.Now().Unix(),
						NextTime:   "11:22",
					},
					Member: &marketing.ControlMember{
						BranchType: model.WORKFLOW_CONTROL_EVENT_BRANCH_MULTI,
						Field:      model.WORKFLOW_CONTROL_MEMBER_FIELD_ACTIVATED_AT,
						Rules: []*marketing.ControlRule{
							&marketing.ControlRule{
								Operator: share_model.OPERATOR_IN,
								Branch:   "1",
								Value: &marketing.ArbitraryValue{
									Value: &marketing.ArbitraryValue_ValueStringArray{
										ValueStringArray: &marketing.EventArrayValue{
											Value: []string{
												"mockValue1", "mockValue2",
											},
										},
									},
								},
							},
							&marketing.ControlRule{
								Operator: share_model.OPERATOR_IN,
								Branch:   "2",
								Value: &marketing.ArbitraryValue{
									Value: &marketing.ArbitraryValue_ValueIntArray{
										ValueIntArray: &marketing.IntArrayValue{
											Value: []uint64{
												55, 66, 77,
											},
										},
									},
								},
							},
							&marketing.ControlRule{
								Operator: share_model.OPERATOR_BETWEEN,
								Branch:   "3",
								Value: &marketing.ArbitraryValue{
									Value: &marketing.ArbitraryValue_ValueStringDoubleDimensionalArray{
										ValueStringDoubleDimensionalArray: &marketing.DoubleDimensionalArrayValue{
											Value: []*marketing.EventArrayValue{
												&marketing.EventArrayValue{
													Value: []string{"mock1", "mock2"},
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			&marketing.Workflow{
				Id:       "randtenStr",
				ParentId: "randTenStr",
				Type:     "action", // control, action, end
				Action: &marketing.Action{
					Type: "template",
					PropertyValueMaps: []*marketing.PropertyValueMap{
						&marketing.PropertyValueMap{
							PropertyId:   "name",
							DefaultValue: "xiao东篱",
							OptionValueMap: map[string]string{
								"male":   "男士",
								"female": "女士",
							},
							DisplayRule: &marketing.DisplayRule{},
						},
					},
					Template: &marketing.ActionTemplate{
						ChannelId: "123",
						Id:        "456",
						Title:     "支付通知",
						Url:       "跳转链接",
						Data: []*marketing.ActionTemplate_Data{
							&marketing.ActionTemplate_Data{
								Key:   "first",
								Value: "{name}先生",
								Color: "红色",
							},
						},
					},
				},
			},
			&marketing.Workflow{
				Id:       "randEleven",
				ParentId: "randtenStr",
				Type:     "action",
				Action: &marketing.Action{
					Type: model.ACTION_EMAIL,
					PropertyValueMaps: []*marketing.PropertyValueMap{
						&marketing.PropertyValueMap{
							PropertyId:   "name",
							DefaultValue: "xiao东篱",
							OptionValueMap: map[string]string{
								"male":   "男士",
								"female": "女士",
							},
							DisplayRule: &marketing.DisplayRule{},
						},
					},
					Email: &marketing.ActionEmail{
						Type:           model.EMAIL_TYPE_SYSTEM,
						Subject:        "mockSubject",
						Body:           `<html>content</html>`,
						ReplyToAddress: "<EMAIL>",
					},
				},
			},
		},
		EnablePeriod: &types.StringDateRange{
			Start: "2022-01-01T12:30:00+08:00",
			End:   "2022-05-01T08:00:00+08:00",
		},
	}
}
