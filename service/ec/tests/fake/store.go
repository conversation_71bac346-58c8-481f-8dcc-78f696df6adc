package fake

import (
	common_ec "mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	ec_store "mairpc/proto/ec/store"
	"mairpc/proto/store"

	"github.com/stretchr/testify/mock"
	"golang.org/x/net/context"
)

type FakeEcStoreService struct {
	mock.Mock
}

func (f *FakeEcStoreService) ListStores(ctx context.Context, req *ec_store.ListStoresRequest) (*ec_store.ListStoresResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListStoresResponse), args.Error(1)
}

func (f *FakeEcStoreService) GetStore(ctx context.Context, req *request.DetailRequest) (*ec_store.StoreDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.StoreDetail), args.Error(1)
}

func (f *FakeEcStoreService) NearStores(ctx context.Context, req *ec_store.NearStoresRequest) (*ec_store.NearStoresResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.NearStoresResponse), args.Error(1)
}

func (f *FakeEcStoreService) UpdateStoresStatus(ctx context.Context, req *ec_store.UpdateStoresStatusRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListStaffs(ctx context.Context, req *ec_store.ListStaffsRequest) (*ec_store.ListStaffsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListStaffsResponse), args.Error(1)
}

func (f *FakeEcStoreService) GetStoreIds(ctx context.Context, req *ec_store.ListStoresRequest) (*ec_store.StoreIdsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.StoreIdsResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListStaffNos(ctx context.Context, req *ec_store.ListStaffsRequest) (*ec_store.ListStaffNosResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListStaffNosResponse), args.Error(1)
}

func (f *FakeEcStoreService) GetStaff(ctx context.Context, req *ec_store.StaffDetailRequest) (*ec_store.StaffDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.StaffDetail), args.Error(1)
}

func (f *FakeEcStoreService) GetLevelOfStaff(ctx context.Context, req *ec_store.StaffDetailRequest) (*response.IntResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.IntResponse), args.Error(1)
}

func (f *FakeEcStoreService) BatchUpdateStaffStatus(ctx context.Context, req *ec_store.BatchUpdateStaffStatusRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) IsLastStaff(ctx context.Context, req *request.DetailRequest) (*ec_store.IsLastStaffResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.IsLastStaffResponse), args.Error(1)
}

func (f *FakeEcStoreService) CreateDeliveryStores(ctx context.Context, req *request.IdListRequest) (*ec_store.CreateDeliveryStoresResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.CreateDeliveryStoresResponse), args.Error(1)
}

func (f *FakeEcStoreService) DeleteDeliveryStore(ctx context.Context, req *request.DetailRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) UpdateDeliveryStore(ctx context.Context, req *ec_store.UpdateDeliveryStoreRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) SwitchStore(ctx context.Context, req *ec_store.SwitchStoreRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) SyncWechatcpDepartmentAndUserJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) CustomSyncWechatcpDepartmentAndUserJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) SyncWechatcpDepartmentAndUser(ctx context.Context, req *request.EmptyRequest) (*response.AsyncCacheResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.AsyncCacheResponse), args.Error(1)
}

func (f *FakeEcStoreService) SyncWechatcpStaffs(ctx context.Context, req *request.EmptyRequest) (*response.AsyncCacheResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.AsyncCacheResponse), args.Error(1)
}

func (f *FakeEcStoreService) CustomSyncWechatcpDepartmentAndUser(ctx context.Context, req *request.EmptyRequest) (*response.AsyncCacheResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.AsyncCacheResponse), args.Error(1)
}

func (f *FakeEcStoreService) SyncMessageAuditConfig(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) SettleStoresJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) HandleWechatcpSubscribe(ctx context.Context, req *request.MaiWebhookRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) HandleWechatcpUnsubscribe(ctx context.Context, req *request.MaiWebhookRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) SyncWechatcpAdmins(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) SyncWechatcpAuthUsers(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) HandleWechatcpAdminChange(ctx context.Context, req *request.MaiWebhookRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) HandleChangeAuth(ctx context.Context, req *request.MaiWebhookRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) SyncStaff(ctx context.Context, req *ec_store.SyncStaffRequest) (*ec_store.StaffDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.StaffDetail), args.Error(1)
}

func (f *FakeEcStoreService) ImportStores(ctx context.Context, req *ec_store.ImportStoresRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) BatchImportStaff(ctx context.Context, req *ec_store.BatchImportStaffRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListBriefStores(ctx context.Context, req *ec_store.ListStoresRequest) (*ec_store.ListBriefStoresResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListBriefStoresResponse), args.Error(1)
}

func (f *FakeEcStoreService) ExportStores(ctx context.Context, req *ec_store.ListStoresRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListBriefStaffs(ctx context.Context, req *ec_store.ListStaffsRequest) (*ec_store.ListBriefStaffsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListBriefStaffsResponse), args.Error(1)
}
func (f *FakeEcStoreService) ExportStaffs(ctx context.Context, req *ec_store.ListStaffsRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

func (f *FakeEcStoreService) ExportWechatworkStaffs(ctx context.Context, req *ec_store.ListStaffsRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListThirdPartyStores(ctx context.Context, req *ec_store.ListThirdPartyStoresRequest) (*ec_store.ListThirdPartyStoresResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListThirdPartyStoresResponse), args.Error(1)
}

func (f *FakeEcStoreService) UpdateStore(ctx context.Context, req *ec_store.UpdateStoreRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) CreateStore(ctx context.Context, req *ec_store.CreateStoreRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) GetParentStores(ctx context.Context, req *ec_store.GetParentStoresRequest) (*ec_store.ListStoresResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListStoresResponse), args.Error(1)
}

func (f *FakeEcStoreService) CreateStaffProperties(ctx context.Context, req *ec_store.CreateStaffPropertiesRequest) (*ec_store.CreateStaffPropertiesResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.CreateStaffPropertiesResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListStaffProperties(ctx context.Context, req *request.EmptyRequest) (*ec_store.ListStaffPropertiesResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListStaffPropertiesResponse), args.Error(1)
}

func (f *FakeEcStoreService) UpdateStaff(ctx context.Context, req *ec_store.UpdateStaffRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) UpdateStaffPhoneWithVerification(ctx context.Context, req *ec_store.UpdateStaffPhoneWithVerificationRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) GetChildStores(ctx context.Context, req *ec_store.GetChildStoresRequest) (*ec_store.ListStoresResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListStoresResponse), args.Error(1)
}

func (f *FakeEcStoreService) GetRootStores(ctx context.Context, req *request.EmptyRequest) (*ec_store.ListStoresResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListStoresResponse), args.Error(1)
}

func (f *FakeEcStoreService) CreateOrganizationSetting(ctx context.Context, req *ec_store.CreateOrganizationSettingRequest) (*ec_store.OrganizationSettingDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.OrganizationSettingDetail), args.Error(1)
}

func (f *FakeEcStoreService) GetOrganizationSetting(ctx context.Context, req *request.EmptyRequest) (*ec_store.OrganizationSettingDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.OrganizationSettingDetail), args.Error(1)
}

func (f *FakeEcStoreService) UpdateOrganizationSetting(ctx context.Context, req *ec_store.UpdateOrganizationSettingRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListOrganizationDepartments(ctx context.Context, req *ec_store.ListOrganizationDepartmentsRequest) (*ec_store.ListOrganizationDepartmentsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListOrganizationDepartmentsResponse), args.Error(1)
}

func (f *FakeEcStoreService) GetOrganizationDepartment(ctx context.Context, req *ec_store.GetOrganizationDepartmentRequest) (*ec_store.OrganizationDepartment, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.OrganizationDepartment), args.Error(1)
}

func (f *FakeEcStoreService) CreateDepartments(ctx context.Context, req *request.IdListRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) DeleteStaff(ctx context.Context, req *request.DetailRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) DeleteDepartment(ctx context.Context, req *request.DetailRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) UpdateStoreAncestorIdsFiled(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) UpdateStaffFromPromoter(ctx context.Context, req *request.CustomerEventRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) HandleSetStaffStores(ctx context.Context, req *request.CustomerEventRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListOriginalStaffs(ctx context.Context, req *ec_store.ListOriginalStaffsRequest) (*ec_store.ListOriginalStaffsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListOriginalStaffsResponse), args.Error(1)
}

func (f *FakeEcStoreService) PullStaffsTag(ctx context.Context, req *ec_store.PullStaffsTagRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) SetStaffsTags(ctx context.Context, req *ec_store.SetStaffsTagsRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) BatchUpdateStaffTags(ctx context.Context, req *ec_store.BatchUpdateStaffTagsRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListCityExpressStores(ctx context.Context, req *ec_store.ListCityExpressStoresRequest) (*ec_store.ListCityExpressStoresResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListCityExpressStoresResponse), args.Error(1)
}

func (f *FakeEcStoreService) CreateCityExpressStores(ctx context.Context, req *ec_store.BatchUpsertCityExpressStoresRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) RemoveCityExpressStores(ctx context.Context, req *ec_store.RemoveCityExpressStoresRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) BatchUpdateCityExpressStore(ctx context.Context, req *ec_store.BatchUpsertCityExpressStoresRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) ExportStoreQrcodes(ctx context.Context, req *ec_store.ExportStoreQrcodesRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

func (f *FakeEcStoreService) StoreDailyStatsJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) GetStoreOverviewStats(ctx context.Context, req *ec_store.GetStoreOverviewStatsRequest) (*ec_store.GetStoreOverviewStatsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.GetStoreOverviewStatsResponse), args.Error(1)
}

func (f *FakeEcStoreService) GetStoreStatsTrend(ctx context.Context, req *ec_store.GetStoreStatsTrendRequest) (*ec_store.GetStoreStatsTrendResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.GetStoreStatsTrendResponse), args.Error(1)
}

func (f *FakeEcStoreService) ExportStoreStatsTrend(ctx context.Context, req *ec_store.GetStoreStatsTrendRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

func (f *FakeEcStoreService) GetStoreStatsContrast(ctx context.Context, req *ec_store.GetStoreStatsContrastRequest) (*ec_store.GetStoreStatsContrastResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.GetStoreStatsContrastResponse), args.Error(1)
}

func (f *FakeEcStoreService) ExportStoreStatsContrast(ctx context.Context, req *ec_store.GetStoreStatsContrastRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

func (f *FakeEcStoreService) DeleteSyncingStores(ctx context.Context, req *request.EmptyRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

func (f *FakeEcStoreService) BatchUpdateStaffLevels(ctx context.Context, req *ec_store.BatchUpdateStaffLevelsRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) BatchUpdateStaffRoles(ctx context.Context, req *ec_store.BatchUpdateStaffRolesRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListRoles(ctx context.Context, req *ec_store.ListRolesRequest) (*ec_store.ListRolesResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListRolesResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListBriefStaffsByRole(ctx context.Context, req *ec_store.ListBriefStaffsByRoleRequest) (*ec_store.ListBriefStaffsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListBriefStaffsResponse), args.Error(1)
}

func (f *FakeEcStoreService) StatsStoreByDate(ctx context.Context, req *ec_store.StatsStoreByDateRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) ImportStaffLevels(ctx context.Context, req *ec_store.ImportStaffLevelsRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListStoreTree(ctx context.Context, req *ec_store.ListStoreTreeRequest) (*ec_store.ListStoreTreeResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListStoreTreeResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListApplicableStoresForCouponEndpoint(ctx context.Context, req *ec_store.ListApplicableStoresForCouponEndpointRequest) (*store.StoreList, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*store.StoreList), args.Error(1)
}

func (f *FakeEcStoreService) SendGuideMessage(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) InitDefaultStaffRoles(ctx context.Context, req *ec_store.InitDefaultStaffRolesRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) HandleExternalContactAccessed(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) CreateChainCorp(ctx context.Context, req *ec_store.CreateChainCorpRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListChainCorps(ctx context.Context, req *ec_store.ListChainCorpsRequest) (*ec_store.ListChainCorpsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListChainCorpsResponse), args.Error(1)
}

func (f *FakeEcStoreService) GetChainCorp(ctx context.Context, req *ec_store.GetChainCorpRequest) (*ec_store.ChainCorp, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ChainCorp), args.Error(1)
}

func (f *FakeEcStoreService) HandleStoreCreate(ctx context.Context, req *request.CustomerEventRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) HandleStaffCreate(ctx context.Context, req *request.CustomerEventRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) GetCityExpressStore(ctx context.Context, req *ec_store.GetCityExpressStoreRequest) (*ec_store.CityExpressStoreDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.CityExpressStoreDetail), args.Error(1)
}

func (f *FakeEcStoreService) GetSensitiveStaff(ctx context.Context, req *ec_store.StaffDetailRequest) (*ec_store.StaffDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.StaffDetail), args.Error(1)
}

func (f *FakeEcStoreService) GetStaffSensitive(ctx context.Context, req *ec_store.GetStaffSensitiveRequest) (*common_ec.GetSensitiveResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*common_ec.GetSensitiveResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListStoresByMemberId(ctx context.Context, req *ec_store.ListStoresByMemberIdRequest) (*ec_store.ListStoresResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListStoresResponse), args.Error(1)
}

func (f *FakeEcStoreService) BindStore(ctx context.Context, req *ec_store.BindStoreRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) GetBoundStore(ctx context.Context, req *ec_store.GetBoundStoreRequest) (*ec_store.BoundStoreDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.BoundStoreDetail), args.Error(1)
}

func (f *FakeEcStoreService) UpsertTagGroup(ctx context.Context, req *ec_store.UpsertTagGroupRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) CreateTag(ctx context.Context, req *ec_store.CreateTagRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) BatchMoveTagsToGroup(ctx context.Context, req *ec_store.BatchMoveTagsToGroupRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) DeleteTagGroup(ctx context.Context, req *request.DetailRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) DeleteTags(ctx context.Context, req *request.IdListRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListTags(ctx context.Context, req *ec_store.ListTagsRequest) (*ec_store.ListTagsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListTagsResponse), args.Error(1)
}

func (f *FakeEcStoreService) ListTagGroups(ctx context.Context, req *ec_store.ListTagGroupsRequest) (*ec_store.ListTagGroupsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec_store.ListTagGroupsResponse), args.Error(1)
}

func (f *FakeEcStoreService) IsTagAndGroupExists(ctx context.Context, req *request.StringRequest) (*response.BoolResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.BoolResponse), args.Error(1)
}

func (f *FakeEcStoreService) UpdateTagDistributorCount(ctx context.Context, req *ec_store.UpdateTagDistributorCountRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) ExportRetailStores(ctx context.Context, req *ec_store.ListStoresRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

func (f *FakeEcStoreService) BatchCreateTags(ctx context.Context, req *ec_store.BatchCreateTagsRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) AutoOpenAndCloseStore(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) CountStoreBoundStaffMembers(ctx context.Context, req *ec_store.ListStoresRequest) (*response.AsyncCacheResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.AsyncCacheResponse), args.Error(1)
}

func (f *FakeEcStoreService) SyncWechatMemberCount(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcStoreService) CalculateStoreRatingJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}
