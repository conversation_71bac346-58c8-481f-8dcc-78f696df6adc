package fake

import (
	"mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/proto/ec/order"

	"github.com/stretchr/testify/mock"
	"golang.org/x/net/context"
)

type FakeEcOrderService struct {
	mock.Mock
}

// 添加购物车商品
func (f *FakeEcOrderService) CreateCartProduct(ctx context.Context, req *order.CreateCartProductRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 批量添加购物车商品
func (f *FakeEcOrderService) BatchCreateCartProducts(ctx context.Context, req *order.BatchCreateCartProductsRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 删除购物车商品
func (f *FakeEcOrderService) DeleteCartProducts(ctx context.Context, req *order.DeleteCartProductsRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 获取购物车商品
func (f *FakeEcOrderService) ListCartProducts(ctx context.Context, req *order.ListCartProductsRequest) (*order.ListCartProductsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListCartProductsResponse), args.Error(1)
}

// 更新购物车商品选中状态
func (f *FakeEcOrderService) ToggleCartProductsCheckedStatus(ctx context.Context, req *order.ToggleCheckedStatusRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 更新购物车商品数量
func (f *FakeEcOrderService) SetCartProductCount(ctx context.Context, req *order.SetCartProductCountRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 更新购物车商品规格
func (f *FakeEcOrderService) UpdateCartProductSku(ctx context.Context, req *order.UpdateCartProductSkuRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 创建订单
func (f *FakeEcOrderService) CreateOrder(ctx context.Context, req *ec.CreateOrderRequest) (*order.OrderDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.OrderDetail), args.Error(1)
}

// 直接购买
func (f *FakeEcOrderService) Purchase(ctx context.Context, req *ec.PurchaseRequest) (*order.OrderDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.OrderDetail), args.Error(1)
}

// 取消订单
func (f *FakeEcOrderService) CancelOrder(ctx context.Context, req *order.CancelOrderRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 退款
func (f *FakeEcOrderService) RefundOrder(ctx context.Context, req *order.RefundOrderRequest) (*order.RefundOrderResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.RefundOrderResponse), args.Error(1)
}

// 分配订单
func (f *FakeEcOrderService) AssignOrder(ctx context.Context, req *order.AssignOrderRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 更新备注
func (f *FakeEcOrderService) UpdateRemarks(ctx context.Context, req *order.UpdateRemarksRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 接单
func (f *FakeEcOrderService) AcceptOrder(ctx context.Context, req *order.AcceptOrderRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 拒单
func (f *FakeEcOrderService) RejectOrder(ctx context.Context, req *order.RejectOrderRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 核销提货码
func (f *FakeEcOrderService) RedeemOrder(ctx context.Context, req *order.RedeemOrderRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 订单完成
//
// 店员标记已提货/客户确认收货
func (f *FakeEcOrderService) CompleteOrder(ctx context.Context, req *order.CompleteOrderRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 标记已开票
func (f *FakeEcOrderService) ConfirmIssuedOrderInvoice(ctx context.Context, req *order.ConfirmIssuedOrderInvoiceRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 订单列表
func (f *FakeEcOrderService) ListOrders(ctx context.Context, req *order.ListOrdersRequest) (*order.ListOrdersResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListOrdersResponse), args.Error(1)
}

// 统计订单数
func (f *FakeEcOrderService) CountOrders(ctx context.Context, req *order.ListOrdersRequest) (*response.AsyncCacheResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.AsyncCacheResponse), args.Error(1)
}

func (f *FakeEcOrderService) ListUnassignedOrders(ctx context.Context, req *order.ListOrdersRequest) (*order.ListOrdersResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListOrdersResponse), args.Error(1)
}

// 订单详情
func (f *FakeEcOrderService) GetOrder(ctx context.Context, req *order.GetOrderRequest) (*order.OrderDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.OrderDetail), args.Error(1)
}

// 更新订单收货地址
func (f *FakeEcOrderService) UpdateOrderContact(ctx context.Context, req *order.UpdateOrderContactRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 售后退款详情
func (f *FakeEcOrderService) GetOrderRefund(ctx context.Context, req *order.GetOrderRefundRequest) (*order.OrderRefundDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.OrderRefundDetail), args.Error(1)
}

// 售后退款列表
func (f *FakeEcOrderService) ListOrderRefunds(ctx context.Context, req *order.ListOrderRefundsRequest) (*order.ListOrderRefundsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListOrderRefundsResponse), args.Error(1)
}

// 批量更新售后订单状态
func (f *FakeEcOrderService) BatchUpdateRefundStatus(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 更新售后订单状态
func (f *FakeEcOrderService) UpdateRefundStatus(ctx context.Context, req *order.UpdateRefundStatusRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 支付订单
func (f *FakeEcOrderService) PrePay(ctx context.Context, req *order.PrePayRequest) (*ec.PrePayResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec.PrePayResponse), args.Error(1)
}

// 删除交易成功和交易关闭订单
func (f *FakeEcOrderService) DeleteOrder(ctx context.Context, req *order.DeleteOrderRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 打印小票
func (f *FakeEcOrderService) PrintTicket(ctx context.Context, req *order.PrintTicketRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 打印退货小票
func (f *FakeEcOrderService) PrintRefundTicket(ctx context.Context, req *order.PrintTicketRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 处理未支付的订单
//
// 提醒即将过期的订单，及取消过期的订单
func (f *FakeEcOrderService) HandleUnpaidOrders(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 获取核销提货码详情
func (f *FakeEcOrderService) GetOrderByCode(ctx context.Context, req *order.GetOrderByCodeRequest) (*order.OrderDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.OrderDetail), args.Error(1)
}

// 订单支付 Webhook
func (f *FakeEcOrderService) OrderPaidWebhook(ctx context.Context, req *request.MaiWebhookRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 更新退款订单状态
func (f *FakeEcOrderService) RetryRefund(ctx context.Context, req *request.DetailRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 导出订单
func (f *FakeEcOrderService) ExportOrder(ctx context.Context, req *order.ListOrdersRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

// 导出订单商品
func (f *FakeEcOrderService) ExportOrderProduct(ctx context.Context, req *order.ListOrdersRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

// 导出售后退款订单
func (f *FakeEcOrderService) ExportOrderRefund(ctx context.Context, req *order.ListOrderRefundsRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

// 订单退款
func (f *FakeEcOrderService) ExecOrderRefundJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 更新订单打印状态
func (f *FakeEcOrderService) UpdatePrintTicketStatus(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 执行数据分析
func (f *FakeEcOrderService) ExecDataAnalyses(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 线下发货
func (f *FakeEcOrderService) DeliverOrderOffline(ctx context.Context, req *order.DeliverOrderOfflineRequest) (*order.OrderDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.OrderDetail), args.Error(1)
}

// 在线发货
func (f *FakeEcOrderService) DeliverOrderOnline(ctx context.Context, req *order.DeliverOrderOnlineRequest) (*order.OrderDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.OrderDetail), args.Error(1)
}

// 更新退款单
func (f *FakeEcOrderService) UpdateOrderRefund(ctx context.Context, req *order.UpdateOrderRefundRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 获取订单物流轨迹
func (f *FakeEcOrderService) GetOrderTrace(ctx context.Context, req *order.GetOrderTraceRequest) (*order.GetOrderTraceResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.GetOrderTraceResponse), args.Error(1)
}

// 自动完成订单
func (f *FakeEcOrderService) AutoCompleteOrderJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 同步订单到第三方 OMS
func (f *FakeEcOrderService) PushOrdersToOmsJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 推送退款订单到第三方
func (f *FakeEcOrderService) PushRefundOrderJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 从 OMS 同步退款单状态
func (f *FakeEcOrderService) SyncOrderRefundFromOmsJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 从 OMS 同步发货状态
func (f *FakeEcOrderService) SyncLogisticsFromOmsJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 自动关闭退款申请
func (f *FakeEcOrderService) AutoCancelOrderRefundJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 线下支付订单确认付款
func (f *FakeEcOrderService) PayOfflineOrder(ctx context.Context, req *order.PayOfflineOrderRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 修改订单支付方式
func (f *FakeEcOrderService) UpdatePayment(ctx context.Context, req *order.UpdatePaymentRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 获取分账明细
func (f *FakeEcOrderService) ListOrderReceiverProfits(ctx context.Context, req *order.ListOrderReceiverProfitsRequest) (*order.ListOrderReceiverProfitsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListOrderReceiverProfitsResponse), args.Error(1)
}

// 导出分账明细
func (f *FakeEcOrderService) ExportOrderReceiverProfits(ctx context.Context, req *order.ListOrderReceiverProfitsRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

// 买赠活动参与记录
func (f *FakeEcOrderService) ListPresentCampaignRecords(ctx context.Context, req *order.ListPresentCampaignRecordsRequest) (*order.ListPresentCampaignRecordsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListPresentCampaignRecordsResponse), args.Error(1)
}

// 更新订单自定义信息
func (f *FakeEcOrderService) UpdateOrderExtra(ctx context.Context, req *order.UpdateOrderExtraRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 导出买赠活动参与记录
func (f *FakeEcOrderService) ExportPresentCampaignRecords(ctx context.Context, req *order.ListPresentCampaignRecordsRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

// 修改订单活动
func (f *FakeEcOrderService) UpdateOrderCampaign(ctx context.Context, req *order.UpdateOrderCampaignRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 添加评价
func (f *FakeEcOrderService) CreateComments(ctx context.Context, req *order.CreateCommentsRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 删除评价
func (f *FakeEcOrderService) DeleteComment(ctx context.Context, req *request.DetailWithMemberIdRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 回复评价
func (f *FakeEcOrderService) ReplyComment(ctx context.Context, req *order.ReplyCommentRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 删除回复
func (f *FakeEcOrderService) DeleteCommentReply(ctx context.Context, req *request.DetailRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 获取评价列表
func (f *FakeEcOrderService) ListComments(ctx context.Context, req *order.ListCommentsRequest) (*order.ListCommentsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListCommentsResponse), args.Error(1)
}

// 获取忽略用户的评价列表（isIgnoreMemberId 固定为 true）
func (f *FakeEcOrderService) ListCommentsExcludingMember(ctx context.Context, req *order.ListCommentsRequest) (*order.ListCommentsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListCommentsResponse), args.Error(1)
}

// 获取评价总数
func (f *FakeEcOrderService) CountComments(ctx context.Context, req *order.ListCommentsRequest) (*response.AsyncCacheResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.AsyncCacheResponse), args.Error(1)
}

// 更新审核状态
func (f *FakeEcOrderService) UpdateCommentStatus(ctx context.Context, req *order.UpdateCommentStatusRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 批量更新审核状态
func (f *FakeEcOrderService) BatchUpdateCommentStatus(ctx context.Context, req *order.BatchUpdateCommentStatusRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 加精评价
func (f *FakeEcOrderService) UpdateCommentHighlight(ctx context.Context, req *order.UpdateCommentHighlightRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 置顶评价
func (f *FakeEcOrderService) UpdateCommentTopping(ctx context.Context, req *order.UpdateCommentToppingRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 设置评价匿名
func (f *FakeEcOrderService) UpdateCommentAnonymous(ctx context.Context, req *order.UpdateCommentAnonymousRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 推荐评价
func (f *FakeEcOrderService) UpsertCommentRecommendation(ctx context.Context, req *order.UpsertCommentRecommendationRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 获取当前用户已点击推荐的评价
func (f *FakeEcOrderService) CheckRecommendedComments(ctx context.Context, req *order.CheckRecommendedCommentsRequest) (*order.ListRecommendedCommentsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListRecommendedCommentsResponse), args.Error(1)
}

// 获取未评价订单
func (f *FakeEcOrderService) ListCommentableOrders(ctx context.Context, req *order.ListCommentableOrdersRequest) (*order.ListCommentableOrdersResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListCommentableOrdersResponse), args.Error(1)
}

// 通过分佣记录id获取分账订单门店列表
func (f *FakeEcOrderService) ListProfitOrderStoresByTransferBillId(ctx context.Context, req *order.ListProfitOrderStoresByTransferBillIdRequest) (*order.ListProfitOrderStoresByTransferBillIdResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListProfitOrderStoresByTransferBillIdResponse), args.Error(1)
}

// 发送拼团消息
func (f *FakeEcOrderService) SendGrouponMessage(ctx context.Context, req *order.SendGrouponMessageRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 查询客户累计退款金额和退款订单数
func (f *FakeEcOrderService) GetRefundOrderStats(ctx context.Context, req *order.GetRefundOrderStatsRequest) (*order.GetRefundOrderStatsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.GetRefundOrderStatsResponse), args.Error(1)
}

// 创建系统默认评价
func (f *FakeEcOrderService) CreateSystemDefaultComments(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 获取导购推广订单金额
func (f *FakeEcOrderService) GetPromoterOrderAmount(ctx context.Context, req *order.GetPromoterOrderAmountRequest) (*order.GetPromoterOrderAmountResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.GetPromoterOrderAmountResponse), args.Error(1)
}

// 是否存在订单
func (f *FakeEcOrderService) HasOrder(ctx context.Context, req *order.HasOrderRequest) (*response.BoolResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.BoolResponse), args.Error(1)
}

// OMS 发货确认通知
func (f *FakeEcOrderService) DeliveryConfirmNotifyFromOms(ctx context.Context, req *order.DeliveryConfirmNotifyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 获取订单统计信息
func (f *FakeEcOrderService) GetOrderStats(ctx context.Context, req *order.GetOrderStatsRequest) (*order.GetOrderStatsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.GetOrderStatsResponse), args.Error(1)
}

// 获取订单信息排名
func (f *FakeEcOrderService) GetOrderRank(ctx context.Context, req *order.GetOrderRankRequest) (*order.GetOrderRankResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.GetOrderRankResponse), args.Error(1)
}

// 获取店铺用户排名
func (f *FakeEcOrderService) GetMemberOrderRank(ctx context.Context, req *order.GetMemberOrderRankRequest) (*order.GetMemberOrderRankResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.GetMemberOrderRankResponse), args.Error(1)
}

// 账单收入记录列表
func (f *FakeEcOrderService) ListIncomeRecords(ctx context.Context, req *order.ListIncomeRecordsRequest) (*order.ListIncomeRecordsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListIncomeRecordsResponse), args.Error(1)
}

// 账单收入记录列表
func (f *FakeEcOrderService) SearchIncomeRecords(ctx context.Context, req *order.ListIncomeRecordsRequest) (*order.SearchIncomeRecordsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.SearchIncomeRecordsResponse), args.Error(1)
}

// 账单收入金额
func (f *FakeEcOrderService) CountIncomeRecordsAmount(ctx context.Context, req *order.ListIncomeRecordsRequest) (*response.AsyncCacheResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.AsyncCacheResponse), args.Error(1)
}

// 账单支出记录列表
func (f *FakeEcOrderService) ListSpentRecords(ctx context.Context, req *order.ListSpentRecordsRequest) (*order.ListSpentRecordsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListSpentRecordsResponse), args.Error(1)
}

// 账单明细列表
func (f *FakeEcOrderService) ListBillDetails(ctx context.Context, req *order.ListBillDetailsRequest) (*order.ListBillDetailsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListBillDetailsResponse), args.Error(1)
}

// 同步支出记录
func (f *FakeEcOrderService) SyncSpentRecords(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 支出记录详情
func (f *FakeEcOrderService) GetSpentRecord(ctx context.Context, req *request.DetailRequest) (*order.GetSpentRecordResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.GetSpentRecordResponse), args.Error(1)
}

// 账单收入记录导出
func (f *FakeEcOrderService) ExportIncomeRecords(ctx context.Context, req *order.ListIncomeRecordsRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

// 账单支出记录导出
func (f *FakeEcOrderService) ExportSpentRecords(ctx context.Context, req *order.ListSpentRecordsRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

// 账单明细导出
func (f *FakeEcOrderService) ExportBillDetails(ctx context.Context, req *order.ListBillDetailsRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

// 通过支出记录 id 获取门店列表
func (f *FakeEcOrderService) ListStoresBySpentRecordId(ctx context.Context, req *order.ListStoresBySpentRecordIdRequest) (*order.ListProfitOrderStoresByTransferBillIdResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListProfitOrderStoresByTransferBillIdResponse), args.Error(1)
}

// 小店数据概览
func (f *FakeEcOrderService) GetMallOverviewStats(ctx context.Context, req *request.EmptyRequest) (*order.GetMallOverviewStatsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.GetMallOverviewStatsResponse), args.Error(1)
}

// OMS 退货确认通知
func (f *FakeEcOrderService) ReturnConfirmNotifyFromOms(ctx context.Context, req *order.ReturnConfirmNotifyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// OMS 退款确认通知
func (f *FakeEcOrderService) RefundConfirmNotifyFromOms(ctx context.Context, req *order.RefundConfirmNotifyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 更新同城快送订单
func (f *FakeEcOrderService) UpdateCityExpressOrder(ctx context.Context, req *order.UpdateCityExpressOrderRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 计算订单金额
func (f *FakeEcOrderService) CalculateOrderAmount(ctx context.Context, req *ec.CalculateOrderAmountRequest) (*ec.CalculateOrderAmountResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec.CalculateOrderAmountResponse), args.Error(1)
}

// 促销活动参与记录
func (f *FakeEcOrderService) ListPackageCampaignRecords(ctx context.Context, req *order.ListPackageCampaignRecordsRequest) (*order.ListPackageCampaignRecordsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListPackageCampaignRecordsResponse), args.Error(1)
}

// 导出促销活动参与记录
func (f *FakeEcOrderService) ExportPackageCampaignRecords(ctx context.Context, req *order.ListPackageCampaignRecordsRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

// 获取订单优惠券
func (f *FakeEcOrderService) GetMembershipDiscounts(ctx context.Context, req *ec.GetMembershipDiscountsRequest) (*ec.GetMembershipDiscountsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec.GetMembershipDiscountsResponse), args.Error(1)
}

// 获取订单权益卡
func (f *FakeEcOrderService) GetMemberPaidCardRecords(ctx context.Context, req *order.GetMemberPaidCardRecordsRequest) (*order.GetMemberPaidCardRecordsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.GetMemberPaidCardRecordsResponse), args.Error(1)
}

// 按商品拆分订单(元祖梦世界特殊拆单逻辑)
func (f *FakeEcOrderService) SplitOrderByProduct(ctx context.Context, req *order.SplitOrderByProductRequest) (*order.SplitOrderByProductResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.SplitOrderByProductResponse), args.Error(1)
}

// 拆分订单
func (f *FakeEcOrderService) SplitOrder(ctx context.Context, req *order.SplitOrderRequest) (*order.SplitOrderResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.SplitOrderResponse), args.Error(1)
}

// 统计购物车商品数
func (f *FakeEcOrderService) CountCartProducts(ctx context.Context, req *order.CountCartProductsRequest) (*response.IntResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.IntResponse), args.Error(1)
}

// 获取购物车分组商品详情
func (f *FakeEcOrderService) ListCartGroups(ctx context.Context, req *order.ListCartGroupsRequest) (*order.ListCartGroupsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListCartGroupsResponse), args.Error(1)
}

// 发送尾款即将开始或结束消息
func (f *FakeEcOrderService) RemindBalancePayment(ctx context.Context, req *order.RemindBalancePaymentRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 取消活动订单
func (f *FakeEcOrderService) HandleCampaignUnpaidOrders(ctx context.Context, req *order.HandleCampaignUnpaidOrdersRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 订单核销记录列表
func (f *FakeEcOrderService) ListOrderRedemptionRecords(ctx context.Context, req *order.ListOrderRedemptionRecordsRequest) (*order.ListOrderRedemptionRecordsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListOrderRedemptionRecordsResponse), args.Error(1)
}

// 支付尾款
func (f *FakeEcOrderService) PrePayBalance(ctx context.Context, req *ec.PrePayBalanceRequest) (*ec.PrePayResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*ec.PrePayResponse), args.Error(1)
}

// 订阅订单活动事件，处理订单活动
func (f *FakeEcOrderService) HandleOrderCampaign(ctx context.Context, req *request.CustomerEventRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 更新月结分销订单分销状态
func (f *FakeEcOrderService) UpdateMonthlyDistribution(ctx context.Context, req *order.UpdateMonthlyDistributionRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 创建发票
func (f *FakeEcOrderService) CreateInvoice(ctx context.Context, req *order.CreateInvoiceRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 创建合并发票
func (f *FakeEcOrderService) CreateMergedInvoice(ctx context.Context, req *order.CreateMergedInvoiceRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 创建红字确认单
func (f *FakeEcOrderService) CreateRedConfirmation(ctx context.Context, req *order.CreateRedConfirmationRequest) (*order.CreateRedConfirmationResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.CreateRedConfirmationResponse), args.Error(1)
}

// 修改发票
func (f *FakeEcOrderService) UpdateInvoice(ctx context.Context, req *order.UpdateInvoiceRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 发票列表
func (f *FakeEcOrderService) ListInvoices(ctx context.Context, req *order.ListInvoicesRequest) (*order.ListInvoicesResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListInvoicesResponse), args.Error(1)
}

// 获取开票记录
func (f *FakeEcOrderService) ListInvoiceRecords(ctx context.Context, req *order.ListInvoiceRecordsRequest) (*order.ListInvoiceRecordsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListInvoiceRecordsResponse), args.Error(1)
}

// 发送发票到邮箱
func (f *FakeEcOrderService) SendInvoiceEmail(ctx context.Context, req *order.SendInvoiceEmailRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 统计各状态订单数
func (f *FakeEcOrderService) CountOrdersByStatus(ctx context.Context, req *order.CountOrdersByStatusRequest) (*order.CountOrdersByStatusResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.CountOrdersByStatusResponse), args.Error(1)
}

// 获取商品购买统计
func (f *FakeEcOrderService) GetOrderProductPurchaseStats(ctx context.Context, req *order.GetOrderProductPurchaseStatsRequest) (*response.IntResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.IntResponse), args.Error(1)
}

// 定时发货虚拟商品预售订单
func (f *FakeEcOrderService) DeliverVirtualPresellOrders(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

// 获取销售金额和销量统计
func (f *FakeEcOrderService) GetOrderCampaignStats(ctx context.Context, req *order.GetOrderCampaignStatsRequest) (*order.GetOrderCampaignStatsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.GetOrderCampaignStatsResponse), args.Error(1)
}

// 订单改价
func (f *FakeEcOrderService) AdjustOrderAmount(ctx context.Context, req *order.AdjustOrderAmountRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) GetProductPurchaseLimitStatus(ctx context.Context, req *order.GetProductPurchaseLimitStatusRequest) (*response.StringResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.StringResponse), args.Error(1)
}

func (f *FakeEcOrderService) GetOrderDeliveryMethods(ctx context.Context, req *order.GetOrderDeliveryMethodsRequest) (*order.GetOrderDeliveryMethodsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.GetOrderDeliveryMethodsResponse), args.Error(1)
}

func (f *FakeEcOrderService) IsFirstPayment(ctx context.Context, req *request.MemberIdRequest) (*response.BoolResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.BoolResponse), args.Error(1)
}

func (f *FakeEcOrderService) DelayVirtualProductRedeemPeriod(ctx context.Context, req *order.DelayVirtualProductRedeemPeriodRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) RemindVirtualProductWillExpire(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) ListCartProductsByPagination(ctx context.Context, req *order.ListCartProductsByPaginationRequest) (*order.ListCartProductsByPaginationResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListCartProductsByPaginationResponse), args.Error(1)
}

func (f *FakeEcOrderService) ReissueMissingOrderReceiverProfit(ctx context.Context, req *order.ReissueMissingOrderReceiverProfitRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) Invoice(ctx context.Context, req *order.InvoiceRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) InvoiceV2(ctx context.Context, req *order.InvoiceRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) CheckInvoiceStatus(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) UpdateOrderTags(ctx context.Context, req *order.UpdateTagsRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) UpdateOrderRefundTags(ctx context.Context, req *order.UpdateTagsRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) SyncIncomeRecords(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) CountCommentTotal(ctx context.Context, req *order.CountCommentTotalRequest) (*order.CountCommentTotalResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.CountCommentTotalResponse), args.Error(1)
}

func (f *FakeEcOrderService) GetMemberOrderStats(ctx context.Context, req *order.GetMemberOrderStatsRequest) (*order.GetMemberOrderStatsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.GetMemberOrderStatsResponse), args.Error(1)
}

func (f *FakeEcOrderService) HandleVirtualOrderProfit(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) GetOrderByTransferBillId(ctx context.Context, req *request.DetailRequest) (*order.OrderDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.OrderDetail), args.Error(1)
}

func (f *FakeEcOrderService) CreateSimpleOrder(ctx context.Context, req *order.CreateSimpleOrderRequest) (*order.OrderDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.OrderDetail), args.Error(1)
}

func (f *FakeEcOrderService) ExportDeliveryTemplate(ctx context.Context, req *order.ExportDeliveryTemplateRequest) (*order.ExportDeliveryTemplateResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ExportDeliveryTemplateResponse), args.Error(1)
}

func (f *FakeEcOrderService) UpdateLogistics(ctx context.Context, req *order.UpdateLogisticsRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) BatchDeliverOrderOffline(ctx context.Context, req *order.BatchDeliverOrderOfflineRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

func (f *FakeEcOrderService) GetCampaignPurchaseLimit(ctx context.Context, req *order.GetCampaignPurchaseLimitRequest) (*order.CampaignPurchaseLimit, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.CampaignPurchaseLimit), args.Error(1)
}

func (f *FakeEcOrderService) GetSensitive(ctx context.Context, req *order.GetSensitiveRequest) (*order.GetSensitiveResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.GetSensitiveResponse), args.Error(1)
}

func (f *FakeEcOrderService) ImportPickupPasswords(ctx context.Context, req *order.ImportPickupPasswordsRequest) (*response.AsyncCacheResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.AsyncCacheResponse), args.Error(1)
}

func (f *FakeEcOrderService) ExportPickupPasswords(ctx context.Context, req *request.EmptyRequest) (*response.JobResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.JobResponse), args.Error(1)
}

func (f *FakeEcOrderService) UpdateOrderOmsProcessor(ctx context.Context, req *order.UpdateOmsProcessorRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) UpdateOrderRefundOmsProcessor(ctx context.Context, req *order.UpdateOmsProcessorRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) CheckRedeemCode(ctx context.Context, req *order.CheckRedeemCodeRequest) (*order.CheckRedeemCodeResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.CheckRedeemCodeResponse), args.Error(1)
}

// 批量添加购物车商品
func (f *FakeEcOrderService) BatchAddCartProducts(ctx context.Context, req *order.BatchAddCartProductsRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) ListPrepaidCards(ctx context.Context, req *ec.ListPrepaidCardsRequest) (*order.ListPrepaidCardsResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListPrepaidCardsResponse), args.Error(1)
}

func (f *FakeEcOrderService) HandleDivideBackRecord(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) HandleUnpaidOrdersWithShouqianba(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) CreateProxyOrder(ctx context.Context, req *order.CreateProxyOrderRequest) (*order.ProxyOrderDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ProxyOrderDetail), args.Error(1)
}

func (f *FakeEcOrderService) ListProxyOrders(ctx context.Context, req *order.ListProxyOrdersRequest) (*order.ListProxyOrdersResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ListProxyOrdersResponse), args.Error(1)
}

func (f *FakeEcOrderService) GetProxyOrder(ctx context.Context, req *request.DetailRequest) (*order.ProxyOrderDetail, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*order.ProxyOrderDetail), args.Error(1)
}

func (f *FakeEcOrderService) UpdateProxyOrder(ctx context.Context, req *order.UpdateProxyOrderRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) AddProxyOrderProductsToCart(ctx context.Context, req *order.AddProxyOrderProductsToCartRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) SyncOrderStatusToProxyOrderJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}

func (f *FakeEcOrderService) SendPickupOrderNotification(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	args := f.Called(ctx, req)
	return args.Get(0).(*response.EmptyResponse), args.Error(1)
}
