# 赠送的兑换卡过期通知
- business: retail
  target: member
  type: redeemCard
  rule: redeemCardExpired
  text:
    enabled: false
    content: '您的兑换卡{CardNumber}即将过期，请及时使用。'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: CardNumber
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 卡号
    - placeholder: CardName
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 卡名称
    - placeholder: ExpireTime
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 过期时间
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
# 赠送的兑换卡超时未被领取通知
- business: retail
  target: member
  type: redeemCard
  rule: redeemCardUncollected
  text:
    enabled: false
    content: '您送出的兑换卡{CardNumber}因超出24小时未被领取，已自动退回您的账户，如有需要请重新赠送。'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: CardNumber
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 卡号
    - placeholder: ValidDays
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 剩余有效天数
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
    - placeholder: UncollectedTime
      isSelectable: true
      name: 退回时间
# 赠送的兑换卡被领取后通知
- business: retail
  target: member
  type: redeemCard
  rule: redeemCardReceived
  text:
    enabled: false
    content: '您送出的兑换卡{CardNumber}已被微信好友{ReceiverName}领取。'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: ReceiverName
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 领取人微信昵称
    - placeholder: CardNumber
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 卡号
    - placeholder: ValidDays
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 剩余有效天数
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
# 赠送的卡片过期通知
- business: retail
  target: member
  type: storedValueCard
  rule: storedValueCardExpired
  text:
    enabled: false
    content: '您的储值卡{CardNumber}即将过期，卡内余额{Balance}，请及时使用。'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: CardNumber
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 卡号
    - placeholder: Balance
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 余额
    - placeholder: ExpireTime
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 过期时间
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
# 赠送的卡片超时未被领取通知
- business: retail
  target: member
  type: storedValueCard
  rule: storedValueCardUncollected
  text:
    enabled: false
    content: '您送出的储值卡{CardNumber}因超出24小时未被领取，已自动退回您的账户，如有需要请重新赠送。'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: CardNumber
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 卡号
    - placeholder: ValidDays
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 剩余有效天数
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
# 赠送的卡片被领取后通知
- business: retail
  target: member
  type: storedValueCard
  rule: storedValueCardReceived
  text:
    enabled: false
    content: '您送出的储值卡{CardNumber}已被微信好友{ReceiverName}领取。'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: ReceiverName
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 领取人微信昵称
    - placeholder: CardNumber
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 卡号
    - placeholder: ValidDays
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 剩余有效天数
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
# 砍价开始通知
- business: retail
  target: member
  type: bargain
  rule: bargainWillBegin
  text:
    enabled: false
    content: '尊敬的用户，您预约的砍价活动{goodsId}，还有{remainTime}就开始了，快来参加吧~'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: remainTime
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 距离活动开始时间
    - placeholder: campaignStartAt
      isSelectable: false
      name: 活动开始时间
    - placeholder: goodsId
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 商品名称
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
    - placeholder: ExpireTime
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 活动结束时间
    - placeholder: GoodAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 商品金额
    - placeholder: BargainAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 已砍价格
    - placeholder: BalanceAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 还差价格
# 砍价库存不足通知
- business: retail
  target: member
  type: bargain
  rule: bargainStockNotEnough
  text:
    enabled: false
    content: '尊敬的用户，您发起的砍价{goodsId}太火爆了，还有{quantity}件就被抢光了，快来邀请好友助力吧~'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: quantity
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 库存剩余件数
    - placeholder: goodsId
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 商品名称
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
    - placeholder: ExpireTime
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 活动结束时间
    - placeholder: GoodAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 商品金额
    - placeholder: BargainAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 已砍价格
    - placeholder: BalanceAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 还差价格
# 砍价超时通知
- business: retail
  target: member
  type: bargain
  rule: bargainWillOvertime
  text:
    enabled: false
    content: '尊敬的用户，您发起的砍价{goodsId}，还有{time}就超时了，快来邀请好友助力吧~'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: time
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 距砍价到期时间
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
    - placeholder: EndTime
      isSelectable: true
      name: 活动结束时间
    - placeholder: CurrentPrice
      isSelectable: true
      name: 当前价格
    - placeholder: GoodAmount
      isSelectable: true
      name: 商品金额
    - placeholder: goodsId
      isSelectable: true
      name: 商品名称
    - placeholder: BargainAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 已砍价格
    - placeholder: BalanceAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 还差价格
# 砍价活动砍至底价时通知
- business: retail
  target: member
  type: bargain
  rule: bargainFloorPrice
  text:
    enabled: false
    content: '尊敬的用户，您的砍价商品{goodsId}已砍至底价，快来下单吧~'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: goodsId
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 商品名称
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
    - placeholder: ExpireTime
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 活动结束时间
    - placeholder: GoodAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 商品金额
    - placeholder: BargainAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 已砍价格
    - placeholder: BalanceAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 还差价格
# 周期购消息
- business: retail
  target: member
  type: periodBuy
  rule: lastPeriodShipped
  text:
    enabled: false
    content: '{MemberName}{Gender}，您好！您的周期购订单{OrderSn}中的商品已全部发货，配送即将结束，感谢您的订购！'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
# 预售订单通知
- business: retail
  target: member
  type: presell
  rule: balanceWillStart
  text:
    enabled: false
    content: '尊敬的用户，您付定金的商品{goodsName}还有{hours}开始付尾款啦，速来速来～'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: hours
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 距尾款支付开始时间
    - placeholder: BalanceStartAt
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 尾款支付开始时间
    - placeholder: OrderSn
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: DepositAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 定金金额
    - placeholder: BalanceAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 尾款金额
    - placeholder: goodsName
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 商品名称
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: presell
  rule: balanceWillEnd
  text:
    enabled: false
    content: '尊敬的用户，您付定金的商品{goodsName}还有{hours}截止付尾款啦，快来支付啊～'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: hours
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 距尾款支付截止时间
    - placeholder: BalanceEndAt
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 尾款支付截止时间
    - placeholder: OrderSn
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: BalanceAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 尾款金额
    - placeholder: goodsName
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 商品名称
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
# 邀请有礼消息
- business: retail
  target: member
  type: fission
  rule: inviterCompletedTask
  text:
    enabled: false
    content: '恭喜您！邀请有礼活动任务已完成，活动奖励已发送至您的账户，请及时查看使用！'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
    - placeholder: InviteResult
      filler:
        default: 邀请成功，点击查看奖励
      isSelectable: true
      name: 邀请结果
- business: retail
  target: member
  type: fission
  rule: inviteeCompletedTask
  text:
    enabled: false
    content: '您已完成好友助力任务，活动奖励已到账！'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
# 秒杀消息
- business: retail
  target: member
  type: falshSale
  rule: campaignWillStart
  text:
    enabled: false
    content: '尊敬的用户，您预约的秒杀{ProductName}活动还有15分钟就要开始啦，商品数量有限，先到先得哦～'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: ProductName
      filler:
        property: reservation.ProductName
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 商品名称
    - placeholder: FlashSalePrice
      isSelectable: false
      name: 秒杀价格
    - placeholder: FlashSaleTitle
      isSelectable: false
      name: 活动内容
    - placeholder: CampaignStartAt
      isSelectable: false
      name: 活动开始时间
    - placeholder: CampaignEndAt
      isSelectable: false
      name: 活动结束时间
# 订单消息
- business: retail
  target: member
  type: order
  rule: orderAutoClosed
  text:
    enabled: false
    content: '尊敬的用户，您有一个订单{OrderSn}未支付，超时未支付将自动取消，快到小程序商城完成支付吧！'
    isEditable: true
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
    - placeholder: ProductName
      name: 商品名称
      isSelectable: true
      filler:
        property: order.Products.Name
        valueTransfer: {}
        default: ''
    - placeholder: BalanceAmount
      name: 下单金额
      isSelectable: true
      filler:
        property: order.PayAmount
        valueTransfer: {}
        default: ''
    - placeholder: OrderTime
      name: 下单时间
      isSelectable: true
      filler:
        property: order.CreatedAt
        valueTransfer: {}
        default: ''
- business: retail
  target: member
  type: order
  rule: orderPaid
  text:
    enabled: false
    content: '尊敬的用户，您的订单{OrderSn}已支付完成。若需了解订单详细信息，快到小程序商城查看吧！'
    isEditable: true
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: order
  rule: orderPickup
  text:
    enabled: false
    content: '尊敬的用户，您购买的订单{OrderSn}，门店已接单，提货凭证号{VerificationCode}，请持凭证至{StoreName}办理提货，谢谢！'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: StoreName
      filler:
        property: store.Name
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 门店名称
    - placeholder: LocationDetail
      filler:
        property: location.Detail
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 门店地址
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: VerificationCode
      filler:
        property: order.PickupCode
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 提货凭证号
    - placeholder: PickupPassword
      filler:
        property: order.PickupPassword
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 自提口令
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          female: 女士
          male: 先生
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: order
  rule: orderHadPickup
  text:
    enabled: false
    content: '尊敬的用户，您在{StoreName}购买的订单{OrderSn}，提货凭证号{VerificationCode}于{VerificationTime}已核销成功，如有疑问请及时联系客服，谢谢！'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: StoreName
      filler:
        property: store.Name
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 门店名称
    - placeholder: LocationDetail
      filler:
        property: location.Detail
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 门店地址
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: VerificationCode
      filler:
        property: order.PickupCode
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 提货凭证号
    - placeholder: VerificationTime
      filler:
        property: order.UpdateAt
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 提货时间
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: order
# 自提订单未提货，提货时间前，指定时间通知
  rule: orderPickupIncomplete
  text:
    enabled: false
    content: '尊敬的客户，您购买的{OrderSn}，提货凭证号{VerificationCode}，请您在{ReservationTime}，持提货凭证前往{StoreName}办理提货，如有疑问，请及时联系我们的客服。感谢您的支持！'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: StoreName
      filler:
        property: store.Name
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 门店名称
    - placeholder: LocationDetail
      filler:
        property: location.Detail
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 门店地址
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: VerificationCode
      filler:
        property: order.PickupCode
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 提货凭证号
    - placeholder: PickupPassword
      filler:
        property: order.PickupPassword
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 自提口令
    - placeholder: ReservationTime
      filler:
        property: order.Reservation.MemberTime
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 预约提货时间
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: order
  rule: orderShipped
  text:
    enabled: false
    content: '尊敬的用户，您购买的订单{OrderSn}已发货，{LogidticCn}单号{TrackingNo}，如有疑问请及时联系客服，谢谢！'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: LogidticCn
      filler:
        property: logistics.delveryName
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 快递公司
    - placeholder: TrackingNo
      filler:
        property: logistics.waybillId
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 快递单号
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
    - placeholder: BalanceAmount
      name: 金额
      isSelectable: true
      filler:
        property: order.PayAmount
        valueTransfer: {}
        default: ''
    - placeholder: ProductName
      name: 商品
      isSelectable: true
      filler:
        property: order.Products.Name
        valueTransfer: {}
        default: ''
    - placeholder: ContactAddress
      name: 收货地址
      isSelectable: true
      filler:
        property: order.Contact.Address.detail
        valueTransfer: {}
        default: ''
    - placeholder: ShippedAt
      name: 发货时间
      isSelectable: true
      filler:
        property: order.Logistics.Address.ShippedAt
        valueTransfer: {}
        default: ''
# 售后消息
- business: retail
  target: member
  type: afterSales
  rule: refundApplied
  text:
    enabled: false
    content: '尊敬的用户，您购买的订单{OrderSn}，申请退款成功，退款金额将在1-5个工作日内原路返回。'
    isEditable: true
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: RefundAmount
      filler:
        property: orderRefund.RefundAmount
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 退款金额
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          female: 女士
          male: 先生
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: afterSales
  rule: refundByStaff
  text:
    enabled: false
    content: '尊敬的用户，您购买的订单{OrderSn}，商家已发起退款，退款金额将在1-5个工作日内原路返回。'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: RefundAmount
      filler:
        property: orderRefund.RefundAmount
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 退款金额
    - placeholder: RefundTime
      filler:
        property: orderRefund.CreatedAt
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 退款时间
    - placeholder: RefundReason
      filler:
        property: orderRefund.Reason
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 退款原因
    - placeholder: RefundType
      filler:
        property: orderRefund.RefundType
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 退款方式
    - placeholder: ProductName
      name: 商品
      isSelectable: true
      filler:
        property: orderRefund.Products.Name
        valueTransfer: {}
        default: ''
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: afterSales
  rule: refundAgreed
  text:
    enabled: false
    content: '尊敬的用户，您购买的订单{OrderSn}，商家已同意退款，退款金额将在1-5个工作日内原路返回。'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: RefundAmount
      filler:
        property: orderRefund.RefundAmount
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 退款金额
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: afterSales
  rule: refundRefused
  text:
    enabled: false
    content: '尊敬的用户，您购买的订单{OrderSn}，商家拒绝退款，若需了解原因请至小程序商城查看。'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: BalanceAmount
      name: 金额
      isSelectable: true
      filler:
        property: order.PayAmount
        valueTransfer: {}
        default: ''
    - placeholder: ProductName
      name: 商品
      isSelectable: true
      filler:
        property: orderRefund.Products.Name
        valueTransfer: {}
        default: ''
    - placeholder: RejectedReason
      filler:
        property: refundRejected.RejectedReason
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 拒绝原因
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: afterSales
  rule: refundAndReturnGoodsAgreed
  text:
    enabled: false
    content: '尊敬的用户，您购买的订单{OrderSn}，商家已同意退货，请尽快退回货物并填写物流单号，退回货物时请备注您的姓名与手机号方便联系，谢谢。'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: RefundAmount
      filler:
        property: orderRefund.RefundAmount
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 退款金额
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: afterSales
  rule: goodsReceived
  text:
    enabled: false
    content: '尊敬的用户，您购买的订单{OrderSn}，商家已确认收货，退款金额将在1-5个工作日内原路返回。'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: RefundAmount
      filler:
        property: orderRefund.RefundAmount
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 退款金额
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
# 优惠券消息
- business: retail
  target: member
  type: coupon
  rule: couponsReceived
  text:
    enabled: false
    content: '尊敬的用户，已有{GetCouponsNo}张优惠券发放至您的账户，尽快到小程序商城查看吧。'
    isEditable: true
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: CouponName
      filler:
        property: coupon.Title
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 发放的优惠券名称
    - placeholder: GetCouponsNo
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 发放的优惠券数量
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
    - placeholder: CouponAmount
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 优惠券面值
    - placeholder: CouponType
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 优惠券类型
    - placeholder: CouponPeriod
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 有效期
- business: retail
  target: member
  type: coupon
  rule: couponsExpired
  text:
    enabled: false
    content: '尊敬的用户，您有{ExpireCouponsNo}张优惠券即将过期，尽快到小程序商城查看吧。'
    isEditable: true
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: ExpireCouponName
      filler:
        property: coupon.Title
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 即将过期的优惠券名称
    - placeholder: ExpireCouponsNo
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 即将过期的优惠券数量
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: coupon
  rule: forwardingCouponsReceived
  text:
    enabled: false
    content: 尊敬的用户，您转赠的优惠券已被领取，尽快到小程序商城查看吧。
    isEditable: true
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: ShiftCouponName
      filler:
        property: coupon.Title
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 转赠的优惠券名称
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
# 拼团消息
- business: retail
  target: member
  type: groupon
  rule: grouponSuccess
  text:
    enabled: false
    content: '尊敬的用户，您的订单{OrderSn}拼团成功，快到小程序商城查看吧。'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
    - placeholder: GrouponTitle
      isSelectable: false
      name: 活动名称
    - placeholder: ProductName
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 商品名称
- business: retail
  target: member
  type: groupon
  rule: grouponFailure
  text:
    enabled: false
    content: '尊敬的用户，您的订单{OrderSn}拼团失败，快到小程序商城查看吧。'
    isEditable: true
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
    - placeholder: GrouponTitle
      isSelectable: false
      name: 活动名称
    - placeholder: ProductName
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 商品名称
# 分销消息
- business: retail
  target: member
  type: distribution
  rule: memberRegistered
  text:
    enabled: false
    content: 尊敬的用户，恭喜您已成功注册大众分销员，快去分销商品赚去佣金吧。
    isEditable: true
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: SalesmanLv
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 分销员等级
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: distribution
  rule: memberUpgraded
  text:
    enabled: false
    content: '尊敬的分销会员，恭喜您已升级至{SalesmanLv}，快去分销商品赚取佣金吧。'
    isEditable: true
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: SalesmanLv
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 分销员等级
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: distribution
  rule: commissionReceived
  text:
    enabled: false
    content: '尊敬的分销会员，已有{CommissionSum}元佣金发放至您的账户，若需要了解更多佣金明细快去分销中心查看吧。'
    isEditable: true
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: CommissionSum
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 佣金金额
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: distribution
  rule: guideMemberActivated
  text:
    enabled: false
    content: 尊敬的用户，恭喜您已成功激活导购分销员，快去分销商品赚取佣金吧。
    isEditable: true
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: distribution
  rule: guideCommissionReceived
  text:
    enabled: false
    content: '尊敬的分销员，已有{CommissionSum}元佣金发放至您的账户，若需了解更多佣金明细快去分销中心查看吧。'
    isEditable: true
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: CommissionSum
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 佣金金额
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: member
  type: distribution
  rule: commissionWaitConfirm
  text:
    enabled: false
    content: '尊敬的分销会员，已有{CommissionSum}元佣金待提现，若需要了解更多佣金明细快去分销中心查看吧'
    isEditable: false
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: CommissionSum
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 佣金金额
    - placeholder: Month
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 结算月份
    - placeholder: Status
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 当前状态
    - placeholder: Remarks
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 备注
# 店长消息
- business: retail
  target: staff
  type: store
  rule: newOrdersAssigned
  text:
    enabled: false
    content: '您有一个新的订单{OrderSn}，请尽快处理。'
    isEditable: false
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: staff
  type: store
  rule: partRefunded
  text:
    enabled: false
    content: '您有一个待提货订单{OrderSn}发生退款，请尽快处理。'
    isEditable: false
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          female: 女士
          male: 先生
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: staff
  type: store
  rule: allRefunded
  text:
    enabled: false
    content: '您有一个待提货订单{OrderSn}已取消，请尽快处理。'
    isEditable: false
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: staff
  type: store
  rule: unshippedPartialRefunded
  text:
    enabled: false
    content: '您有一个待发货订单{OrderSn}发生退款，请尽快处理。'
    isEditable: false
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          female: 女士
          male: 先生
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: staff
  type: store
  rule: unshippedAllRefunded
  text:
    enabled: false
    content: '您有一个待发货订单{OrderSn}已取消，请尽快处理。'
    isEditable: false
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: OrderSn
      filler:
        property: order.Number
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 订单编号
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: staff
  type: store
  rule: unshippedAllRefundedCancel
  text:
    enabled: false
    content: '您有一个退款申请已关闭，请尽快处理。'
    isEditable: false
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
- business: retail
  target: staff
  type: store
  rule: shippedApplyRefund
  text:
    enabled: false
    content: '您有一个待审核订单，请尽快处理。'
    isEditable: false
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
- business: retail
  target: staff
  type: store
  rule: shippedApplyRefundCancel
  text:
    enabled: false
    content: '您有一个退款申请已关闭，请尽快处理。'
    isEditable: false
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
# 代销小店消息
- business: retail
  target: staff
  type: consignment
  rule: consignmentApplicationSuccess
  text:
    enabled: false
    content: 尊敬的用户，恭喜您的代销小店已审核通过。
    isEditable: false
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: staff
  type: consignment
  rule: consignmentCommissionReceived
  text:
    enabled: false
    content: '尊敬的代销店主，已有{CommissionSum}元佣金发放至您的账户，若需了解更多佣金明细快去微店长端查看吧。'
    isEditable: false
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: CommissionSum
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: false
      name: 佣金金额
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
- business: retail
  target: staff
  type: consignment
  rule: consignmentApplicationFailed
  text:
    enabled: false
    content: 尊敬的用户，很遗憾您的代销小店审核未能通过。
    isEditable: false
  subscribeMessage:
    enabled: false
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: MemberName
      filler:
        property: member.Name
        valueTransfer: {}
        default: 顾客
      isSelectable: true
      name: 姓名
    - placeholder: Gender
      filler:
        property: member.Gender
        valueTransfer:
          male: 先生
          female: 女士
        default: 女士/先生
      isSelectable: true
      name: 性别
# D2R 终端任务通知
# 任务开始通知
- business: D2R
  target: staff
  type: staffTask
  rule: staffTaskStart
  text:
    enabled: false
    content: ''
    isEditable: false
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: taskName
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 任务名称
    - placeholder: taskDesc
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 任务描述
    - placeholder: taskTime
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 任务时间
# 催办通知
- business: D2R
  target: staff
  type: staffTask
  rule: staffTaskUrge
  text:
    enabled: false
    content: ''
    isEditable: false
  subscribeMessage:
    enabled: true
    id: ''
    page: ''
    data: []
  placeholderRules:
    - placeholder: taskName
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 任务名称
    - placeholder: taskDesc
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 任务描述
    - placeholder: taskTime
      filler:
        property: ''
        valueTransfer: {}
        default: ''
      isSelectable: true
      name: 任务时间
