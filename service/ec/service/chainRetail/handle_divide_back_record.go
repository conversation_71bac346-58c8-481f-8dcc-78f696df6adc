package chain_retail

import (
	"mairpc/core/component/yeepay"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/service/share/util"

	"mairpc/proto/common/request"
	pb_common "mairpc/proto/common/response"
	ec_model "mairpc/service/ec/model"
	ec_order "mairpc/service/ec/model/order"
	ec_profit "mairpc/service/ec/model/profitsharing"
	ec_retailer "mairpc/service/ec/model/retailer"

	"golang.org/x/net/context"
)

func (ChainRetailService) HandleDivideBackRecord(ctx context.Context, req *request.EmptyRequest) (*pb_common.EmptyResponse, error) {
	// 非连锁零售商不需要处理分账回退记录
	if !ec_model.IsChainRetail(ctx) {
		return &pb_common.EmptyResponse{}, nil
	}

	condition := bson.M{
		"accountId":          util.GetAccountIdAsObjectId(ctx),
		"status":             ec_profit.BILL_STATUS_SUCCESS,
		"backRecords.status": ec_profit.BACK_RECORDS_STATUS_PROCESSING,
	}
	bills, err := ec_profit.CTransferBill.GetAllByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	for _, bill := range bills {
		order := getOrderByBillId(ctx, bill.Id)
		if !order.Id.Valid() {
			continue
		}
		newBackRecords := []ec_profit.BackRecord{}
		for _, backRecord := range bill.BackRecords {
			if backRecord.Status == ec_profit.BACK_RECORDS_STATUS_PROCESSING {
				status := divideBackQuery(ctx, order, backRecord.DivideBackRequestId)
				backRecord.Status = status
			}
			newBackRecords = append(newBackRecords, backRecord)
		}
		bill.BackRecords = newBackRecords
	}
	return &pb_common.EmptyResponse{}, nil
}

func divideBackQuery(ctx context.Context, order ec_order.Order, divideBackRequestId string) string {
	client := yeepay.NewYeepay(yeepay.YEEPAY_APP_KEY, core_util.GetMaimengPrivateKey())
	req := yeepay.DivideBackQueryRequest{
		ParentMerchantNo:    ec_retailer.GetParentMerchantNo(ctx),
		MerchantNo:          ec_retailer.GetYeepayMerchantNo(ctx),
		OrderId:             order.Id.Hex(),
		UniqueOrderNo:       order.TradeNo,
		DivideBackRequestId: divideBackRequestId,
	}
	resp, err := client.Jiaoyi.DivideBackQuery(ctx, &req)
	if err != nil {
		log.Warn(ctx, "DivideBackQuery fail", log.Fields{
			"req": req,
		})
		return ""
	}
	return resp.Status
}

func getOrderByBillId(ctx context.Context, billId bson.ObjectId) ec_order.Order {
	orderReceiverProfit, _ := ec_order.COrderReceiverProfit.GetByBillId(ctx, billId)
	if !orderReceiverProfit.Id.Valid() {
		return ec_order.Order{}
	}
	order, _ := ec_order.COrder.GetById(ctx, orderReceiverProfit.Order.Id)
	return order
}
