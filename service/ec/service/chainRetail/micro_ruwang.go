package chain_retail

import (
	"context"
	"encoding/json"
	"fmt"
	core_component "mairpc/core/component"
	"mairpc/core/component/yeepay"
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/account"
	pb_client "mairpc/proto/client"
	"mairpc/proto/common/response"
	pb_chain_retail "mairpc/proto/ec/chainRetail"
	pb_distribution "mairpc/proto/ec/distribution"
	ec_client "mairpc/service/ec/client"
	model_ec_chain_retail "mairpc/service/ec/model/chainRetail"
	"mairpc/service/share/component"
	"mairpc/service/share/util"
	"strings"
)

const (
	NOTIFY_URL = "%s/v1/yeepay/microRuwangNotify?accountId=%s"
)

func (s ChainRetailService) MicroRuwang(ctx context.Context, req *pb_chain_retail.MicroRuwangDetail) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	if req.IsRegister {
		ds, err := ec_client.DistributionService.GetDistributionSetting(ctx, &pb_distribution.GetDistributionSettingRequest{SettingType: "member"})
		if err != nil {
			return nil, err
		}
		if !ds.Enabled {
			return nil, errors.NewNotEnabledError("distribution")
		}
		record := model_ec_chain_retail.CYeepayMicroRuwangRecord.GetById(ctx, bson.ObjectIdHex(req.Id))
		if record == nil {
			return nil, errors.NewNotExistsError("id")
		}
		// 不能重复提交
		if record.Status == model_ec_chain_retail.STATUS_SUBMITTED {
			return &response.EmptyResponse{}, nil
		}
		record.Status = model_ec_chain_retail.STATUS_SUBMITTED
		record.Update(ctx)

		err = createYeepayMerchant(ctx, record)
		if err != nil {
			return nil, err
		}
	} else {
		_, err := pb_client.GetAccountServiceClient().VerifySmsVerificationCode(ctx, &account.VerifySmsVerificationCodeRequest{
			Phone: req.Phone,
			Code:  req.Code,
		})
		if err != nil {
			return nil, err
		}
		_, err = s.UpsertMicroRuwangRecord(ctx, req)
		if err != nil {
			return nil, err
		}
	}

	return &response.EmptyResponse{}, nil
}

func createYeepayMerchant(ctx context.Context, record *model_ec_chain_retail.YeepayMicroRuwangRecord) error {
	client := yeepay.NewYeepay(yeepay.YEEPAY_APP_KEY, core_util.GetMaimengPrivateKey())
	if record.Type == model_ec_chain_retail.TYPE_MICRO {
		req, err := genMerRegisterSaasMicroRequest(ctx, *record)
		if err != nil {
			return err
		}
		ruwangResp, err := client.Ruwang.MerRegisterSaasMicro(ctx, req)
		if err != nil {
			return err
		}
		if ruwangResp.ReturnCode != "NIG00000" {
			record.Status = model_ec_chain_retail.STATUS_FAILED
			record.AuditOpinion = ruwangResp.ReturnCode + ruwangResp.ReturnMsg
			record.Update(ctx)
		}
		return nil
	}

	req, err := genMerRegisterSaasMerchantRequest(ctx, *record)
	if err != nil {
		return err
	}
	ruwangResp, err := client.Ruwang.MerRegisterSaasMerchant(ctx, req)
	if err != nil {
		return err
	}
	if ruwangResp.ReturnCode != "NIG00000" {
		record.Status = model_ec_chain_retail.STATUS_FAILED
		record.AuditOpinion = ruwangResp.ReturnCode + ruwangResp.ReturnMsg
		record.Update(ctx)
	}
	return nil
}

func genMerRegisterSaasMicroRequest(ctx context.Context, record model_ec_chain_retail.YeepayMicroRuwangRecord) (*yeepay.MerRegisterSaasMicroRequest, error) {
	notifyUrl := fmt.Sprintf(NOTIFY_URL, util.GetBussinessDomain(), util.GetAccountId(ctx))
	request := &yeepay.MerRegisterSaasMicroRequest{
		RequestNo:        record.StaffId.Hex(),
		BusinessRole:     record.SellerInfo.BusinessRole,
		ParentMerchantNo: record.SellerInfo.ParentMerchantNo,
		NotifyUrl:        notifyUrl,
		ProductInfo:      getProductInfo(),
	}

	merchantCorporationInfo, err := genMerchantCorporationInfoReq(ctx, record)
	if err != nil {
		return nil, err
	}
	request.MerchantCorporationInfo = merchantCorporationInfo

	merchantSubjectInfo, err := getMerchantSubjectInfo(record)
	if err != nil {
		return nil, err
	}
	request.MerchantSubjectInfo = merchantSubjectInfo

	businessAddressInfo, err := getBusinessAddressInfo(ctx, record)
	if err != nil {
		return nil, err
	}
	request.BusinessAddressInfo = businessAddressInfo

	accountInfo, err := getAccountInfo(record)
	if err != nil {
		return nil, err
	}
	request.AccountInfo = accountInfo
	return request, nil
}

func genMerRegisterSaasMerchantRequest(ctx context.Context, record model_ec_chain_retail.YeepayMicroRuwangRecord) (*yeepay.MerRegisterSaasMerchantRequest, error) {
	req := &yeepay.MerRegisterSaasMerchantRequest{
		RequestNo:                        record.StaffId.Hex(),
		BusinessRole:                     "SETTLED_MERCHANT", // 入驻商户
		ParentMerchantNo:                 record.SellerInfo.ParentMerchantNo,
		MerchantContactInfo:              genMerchantContactInfo(record),
		IndustryCategoryInfo:             genIndustryCategoryInfo(),
		NotifyUrl:                        fmt.Sprintf(NOTIFY_URL, util.GetBussinessDomain(), util.GetAccountId(ctx)),
		ProductInfo:                      getProductInfo(),
		FunctionService:                  `["SHARE"]`,
		FunctionServiceQualificationInfo: `{"shareScene":"FZ_FJ002"}`,
	}
	merchantCorporationInfo, err := genMerchantCorporationInfoReq(ctx, record)
	if err != nil {
		return nil, err
	}
	req.MerchantCorporationInfo = merchantCorporationInfo

	merchantSubjectInfo, err := getMerchantSubjectInfo(record)
	if err != nil {
		return nil, err
	}
	req.MerchantSubjectInfo = merchantSubjectInfo

	businessAddressInfo, err := getBusinessAddressInfo(ctx, record)
	if err != nil {
		return nil, err
	}
	req.BusinessAddressInfo = businessAddressInfo

	accountInfo, err := getAccountInfo(record)
	if err != nil {
		return nil, err
	}
	req.SettlementAccountInfo = accountInfo
	return req, nil
}

func getProductInfo() string {
	productInfoBytes, _ := json.Marshal([]map[string]string{
		{
			"productCode": "D1",
			"rateType":    "SINGLE_FIXED",
			"fixedRate":   "0",
		},
	})
	return string(productInfoBytes)
}

func genMerchantCorporationInfoReq(ctx context.Context, record model_ec_chain_retail.YeepayMicroRuwangRecord) (string, error) {
	type MerchantCorporationInfoRequest struct {
		Mobile               string `json:"mobile"`
		LegalLicenceType     string `json:"legalLicenceType"`
		LegalLicenceNo       string `json:"legalLicenceNo"`
		LegalLicenceFrontURL string `json:"legalLicenceFrontUrl"`
		LegalLicenceBackURL  string `json:"legalLicenceBackUrl"`
	}

	legalLicenceFrontURL, legalLicenceBackURL, err := genYeepayQualUrl(ctx, record)
	if err != nil {
		return "", err
	}
	infoReq := MerchantCorporationInfoRequest{
		Mobile:               record.Phone,
		LegalLicenceType:     "ID_CARD",
		LegalLicenceNo:       record.SellerInfo.IdCardInfo.IdCardNumber,
		LegalLicenceFrontURL: legalLicenceFrontURL,
		LegalLicenceBackURL:  legalLicenceBackURL,
	}
	bytes, err := json.Marshal(infoReq)
	if err != nil {
		return "", err
	}
	return string(bytes), nil
}

func genYeepayQualUrl(ctx context.Context, record model_ec_chain_retail.YeepayMicroRuwangRecord) (string, string, error) {
	legalLicenceFrontUrl, err := component.Retailer.MerchantQualUpload(ctx, &component.MerchantQualUploadRequest{
		MerQualUrl: record.SellerInfo.IdCardInfo.IdCardCopy,
	})
	if err != nil {
		return "", "", err
	}

	legalLicenceBackUrl, err := component.Retailer.MerchantQualUpload(ctx, &component.MerchantQualUploadRequest{
		MerQualUrl: record.SellerInfo.IdCardInfo.IdCardNational,
	})
	if err != nil {
		return "", "", err
	}

	return legalLicenceFrontUrl.YeepayMerQualUrl, legalLicenceBackUrl.YeepayMerQualUrl, nil
}

func getMerchantSubjectInfo(record model_ec_chain_retail.YeepayMicroRuwangRecord) (string, error) {
	type MerchantSubjectInfo struct {
		SignName  string `json:"signName"`
		ShortName string `json:"shortName"`
	}
	merchantSubjectInfoBytes, err := json.Marshal(MerchantSubjectInfo{
		SignName:  record.SellerInfo.IdCardInfo.IdCardName,
		ShortName: record.SellerInfo.IdCardInfo.IdCardName,
	})
	if err != nil {
		return "", err
	}
	return string(merchantSubjectInfoBytes), nil
}

func getBusinessAddressInfo(ctx context.Context, record model_ec_chain_retail.YeepayMicroRuwangRecord) (string, error) {
	type BusinessAddressInfo struct {
		Province string `json:"province"` // 省编码
		City     string `json:"city"`     // 市编码
		District string `json:"district"` // 区编码
		Address  string `json:"address"`
	}
	addressInfo := BusinessAddressInfo{
		Address: record.SellerInfo.IdCardInfo.IdCardAddress,
	}
	result, err := core_component.TencentLBSClient.GetGeocoding(ctx, record.SellerInfo.IdCardInfo.IdCardAddress)
	errMsg := ""
	if err != nil {
		errMsg = err.Error()
	}
	log.Warn(ctx, "LBSGeocoding", log.Fields{"result": result, "errMsg": errMsg})
	if result.AddressComponent.Province != "" || result.AddressComponent.City != "" {
		addressInfo.Province, addressInfo.City, addressInfo.District = formatAddressCode(result.AdInfo.Adcode)
	}
	businessAddressInfoBytes, err := json.Marshal(addressInfo)
	if err != nil {
		return "", err
	}
	return string(businessAddressInfoBytes), nil
}

func formatAddressCode(code string) (string, string, string) {
	if code == "320281" {
		return "320000", "329281", "320281"
	}
	codes := strings.Split(code, "")
	codes[4] = "0"
	codes[5] = "0"
	cityCode := strings.Join(codes, "")
	codes[2] = "0"
	codes[3] = "0"
	provinceCode := strings.Join(codes, "")
	return provinceCode, cityCode, code
}

func getAccountInfo(record model_ec_chain_retail.YeepayMicroRuwangRecord) (string, error) {
	type AccountInfo struct {
		SettlementDirection string `json:"settlementDirection"`
		BankAccountType     string `json:"bankAccountType"`
		BankCardNo          string `json:"bankCardNo"`
		BankCode            string `json:"bankCode"`
	}
	accountInfoBytes, err := json.Marshal(AccountInfo{
		SettlementDirection: record.SellerInfo.BankAccountInfo.SettlementDirection,
		BankAccountType:     record.SellerInfo.BankAccountInfo.BankAccountType,
		BankCardNo:          record.SellerInfo.BankAccountInfo.AccountNumber,
		BankCode:            record.SellerInfo.BankAccountInfo.BankAddressCode,
	})
	if err != nil {
		return "", err
	}
	return string(accountInfoBytes), nil
}

func genIndustryCategoryInfo() string {
	type IndustryCategoryInfo struct {
		PrimaryIndustryCategory   string `json:"primaryIndustryCategory"`
		SecondaryIndustryCategory string `json:"secondaryIndustryCategory"`
	}
	industryCategoryInfo := IndustryCategoryInfo{
		PrimaryIndustryCategory:   "122",    // 电商
		SecondaryIndustryCategory: "122002", // 综合电商
	}
	bytes, _ := json.Marshal(industryCategoryInfo)
	return string(bytes)
}

func genMerchantContactInfo(record model_ec_chain_retail.YeepayMicroRuwangRecord) string {
	type MerchantContactInfo struct {
		ContactName   string `json:"contactName"`
		ContactMobile string `json:"contactMobile"`
	}
	merchantContactInfo := MerchantContactInfo{
		ContactName:   record.SellerInfo.IdCardInfo.IdCardName,
		ContactMobile: record.Phone,
	}
	bytes, _ := json.Marshal(merchantContactInfo)
	return string(bytes)
}
