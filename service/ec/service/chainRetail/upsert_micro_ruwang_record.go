package chain_retail

import (
	"context"
	"mairpc/core/extension/bson"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	pb_chain_retail "mairpc/proto/ec/chainRetail"
	model_ec_chain_retail "mairpc/service/ec/model/chainRetail"
	ec_retailer "mairpc/service/ec/model/retailer"
)

func (ChainRetailService) UpsertMicroRuwangRecord(ctx context.Context, req *pb_chain_retail.MicroRuwangDetail) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if req.Type == "" {
		req.Type = model_ec_chain_retail.TYPE_MICRO // 默认是 micro
	}

	record := model_ec_chain_retail.CYeepayMicroRuwangRecord.GetByStaffId(ctx, bson.ObjectIdHex(req.StaffId))
	isInsert := false
	if record == nil {
		isInsert = true
		record = &model_ec_chain_retail.YeepayMicroRuwangRecord{
			StaffId:  bson.ObjectIdHex(req.StaffId),
			MemberId: bson.ObjectIdHex(req.MemberId),
			SellerInfo: model_ec_chain_retail.SellerInfo{
				ParentMerchantNo: ec_retailer.GetParentMerchantNo(ctx),
				BusinessRole:     "SHARE_MERCHANT",
				ProductInfo: []model_ec_chain_retail.ProductInfo{
					{
						ProductCode: "D1",
						RateType:    "SINGLE_FIXED",
						PercentRate: "0",
					},
				},
			},
		}
	}
	if req.BankAccountInfo != nil {
		bankAccountInfo := model_ec_chain_retail.BankAccountInfo{}
		copier.Instance(nil).From(req.BankAccountInfo).CopyTo(&bankAccountInfo)
		record.SellerInfo.BankAccountInfo = bankAccountInfo
	}
	if req.IdCardInfo != nil {
		idCardInfo := model_ec_chain_retail.IdCardInfo{}
		copier.Instance(nil).From(req.IdCardInfo).CopyTo(&idCardInfo)
		record.SellerInfo.IdCardInfo = idCardInfo
	}
	if req.Phone != "" {
		record.Phone = req.Phone
	}
	record.Type = req.Type

	if isInsert {
		err := record.Create(ctx)
		if err != nil {
			return nil, err
		}
	} else {
		err := record.Update(ctx)
		if err != nil {
			return nil, err
		}
	}

	return &response.EmptyResponse{}, nil
}
