package distribution

import (
	"encoding/json"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/client"
	common "mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	"mairpc/proto/common/types"
	"mairpc/proto/ec/distribution"
	pb_ec_order "mairpc/proto/ec/order"
	pb_member "mairpc/proto/member"
	pb_store "mairpc/proto/store"
	ec_model "mairpc/service/ec/model"
	distribution_model "mairpc/service/ec/model/distribution"
	order_model "mairpc/service/ec/model/order"
	profitsharing_model "mairpc/service/ec/model/profitsharing"
	store_model "mairpc/service/ec/model/store"
	order_service "mairpc/service/ec/service/order"
	"mairpc/service/ec/share"
	"mairpc/service/share/util"
	"reflect"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/spf13/cast"

	"golang.org/x/net/context"
)

// 获取分销订单列表
func (d DistributionService) ListDistributionOrders(ctx context.Context, req *distribution.ListDistributionOrdersRequest) (*distribution.ListDistributionOrdersResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	pageIndex, pageSize := util.ParsePagingCondition(req.ListCondition)
	orderBys := []string{"-createdAt"}
	if req.ListCondition != nil && len(req.ListCondition.OrderBy) != 0 {
		orderBys = req.ListCondition.OrderBy
	}

	// 构建分销订单查询条件
	condition, err := GetListDistributionOrdersCondition(ctx, req)
	if err != nil {
		return &distribution.ListDistributionOrdersResponse{}, nil
	}

	if req.StaffId != "" || len(req.StaffIds) > 0 {
		req.StaffIds = append(req.StaffIds, req.StaffId)
		pCondtion := bson.M{"staffId": bson.M{"$in": util.ToMongoIds(req.StaffIds)}}
		promoter, err := distribution_model.CPromoter.GetByDefaultCondition(ctx, pCondtion)
		if err != nil {
			return nil, err
		}
		condition["distribution.promoterId"] = promoter.Id
	}

	var (
		dOrders []order_model.Order
		total   int
	)
	if req.WithoutTotal {
		order_model.Common.GetAllByPaginationWithoutCount(ctx, condition, pageIndex, pageSize, orderBys, order_model.C_ORDER, &dOrders)
	} else {
		total = order_model.Common.GetAllByPagination(ctx, condition, pageIndex, pageSize, orderBys, order_model.C_ORDER, &dOrders)
	}

	membersMap, err := getMembersMap(ctx, core_util.ToStringArray(core_util.ExtractArrayField("MemberId", dOrders)))
	if err != nil {
		return nil, err
	}
	storesMap, err := getStoresMap(ctx, core_util.ToStringArray(core_util.ExtractArrayField("StoreId", dOrders)))
	if err != nil {
		return nil, err
	}
	respOrders := formatOrders(ctx, dOrders, membersMap, storesMap, req.Type)

	resp := distribution.ListDistributionOrdersResponse{
		Total: uint64(total),
		Items: respOrders,
	}
	err = CalDistributionOrdersStats(ctx, d, req, &resp)
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

// 通过请求参数构建分销订单的查询条件
func GetListDistributionOrdersCondition(ctx context.Context, req *distribution.ListDistributionOrdersRequest) (bson.M, error) {
	condition := distribution_model.Common.GenDefaultCondition(ctx)
	condition["maskType"] = order_model.MASK_TYPE_UNMASK
	// 确定分销类型（导购分销/大众分销）
	condition["distribution.promoterType"] = req.Type
	// 限制查询订单的范围为分销订单
	condition["distribution.promoterId"] = bson.M{
		"$exists": true,
	}
	// 只查询支付过的订单
	condition["paidAt"] = bson.M{
		"$exists": true,
	}

	if req.SearchKey != "" {
		fuzzySearchKey := util.GetFuzzySearchStrRegex(req.SearchKey)
		switch req.SearchType {
		case "order":
			if isOrderNumber, _ := regexp.MatchString(`M\d{16}$`, req.SearchKey); isOrderNumber {
				// 如果是订单编号，直接精确搜索
				condition["number"] = req.SearchKey
			} else {
				condition["products.name"] = util.GetPrefixSearchStrRegex(req.SearchKey)
			}
		case "member":
			memberIds, err := getMemberIdsBySearchKey(ctx, req.SearchKey)
			if err != nil {
				return nil, err
			}
			if len(memberIds) > 0 {
				condition["memberId"] = bson.M{
					"$in": util.ToMongoIds(memberIds),
				}
			} else {
				condition["memberId"] = bson.NewObjectId()
			}
		case "contact":
			condition["$or"] = []bson.M{
				{"contact.name": util.GetFuzzySearchStrRegex(req.SearchKey)},
				{"contact.phone": req.SearchKey},
			}
		default:
			var (
				promoters []distribution_model.Promoter
				limit     int
			)
			// 默认查询分销员信息，通过名称、手机号、对应导购的编号等信息确定分销员范围
			mCondition := distribution_model.Common.GenDefaultCondition(ctx)
			mCondition["$or"] = []bson.M{
				{"name": fuzzySearchKey},
				{"phone": req.SearchKey},
			}
			switch req.SearchType {
			case "subPromoter":
				mCondition["parentId"] = bson.M{"$exists": true}
			case "parentPromoter":
				mCondition["parentId"] = bson.M{"$exists": false}
			case "promoter":
				limit = 10
				staff, _ := store_model.CStaff.GetOneByCondition(ctx, bson.M{"staffNo": req.SearchKey})
				if staff.Id.Hex() != "" {
					mCondition["$or"] = []bson.M{
						{"name": fuzzySearchKey},
						{"phone": req.SearchKey},
						{"staffId": staff.Id},
					}
				}
			}
			delete(mCondition, "isDeleted")
			extension.DBRepository.FindAll(ctx, distribution_model.C_PROMOTER, mCondition, nil, limit, &promoters)
			var (
				promoterIds    []bson.ObjectId
				subPromoterIds []bson.ObjectId
				orCond         []bson.M
			)
			for _, promoter := range promoters {
				if promoter.ParentId.Valid() {
					subPromoterIds = append(subPromoterIds, promoter.Id)
					promoterIds = append(promoterIds, promoter.ParentId)
					continue
				}
				promoterIds = append(promoterIds, promoter.Id)
			}
			switch req.SearchType {
			case "subPromoter":
				condition["distribution.promoterId"] = bson.M{"$in": append(promoterIds, bson.NewObjectId())}
				condition["distribution.subPromoterId"] = bson.M{"$in": append(subPromoterIds, bson.NewObjectId())}
			case "parentPromoter":
				condition["distribution.promoterId"] = bson.M{"$in": append(promoterIds, bson.NewObjectId())}
			case "promoter":
				for _, promoter := range promoters {
					var (
						temp       = bson.M{}
						promoterId = promoter.Id
					)
					if promoter.ParentId.Valid() {
						// 如果当前是二级分销员，那么需要精确到这个二级分销员
						temp["distribution.promoterId"] = bson.M{"$in": promoter.HistoryParentIds}
						temp["distribution.subPromoterId"] = promoter.Id
					} else {
						// 如果当前是一级分销员，那么只查直接关联这个一级分销员的订单
						temp["distribution.promoterId"] = promoterId
						temp["distribution.subPromoterId"] = bson.M{"$exists": false}
					}
					orCond = append(orCond, temp)
				}
				if len(orCond) == 0 {
					condition["distribution.promoterId"] = bson.NewObjectId()
				} else {
					condition["$or"] = orCond
				}
			}
		}
	}

	if req.IsPaid != nil && req.IsPaid.Value {
		// 查询是否支付过的订单是通过 histories 中是否有支付记录
		condition["histories"] = bson.M{"$elemMatch": bson.M{"status": order_model.ORDER_STATUS_PAID}}
	}
	if req.Locations != "" {
		locations := []*pb_ec_order.OrderLocationFilter{}
		json.Unmarshal([]byte(req.Locations), &locations)
		if len(locations) > 0 {
			condition["storeId"] = bson.M{"$in": order_service.GetStoreIdsByLocation(ctx, locations)}
		}
	}
	if len(req.StoreIds) > 0 {
		condition["storeId"] = bson.M{
			"$in": util.ToMongoIds(req.StoreIds),
		}
	}
	if timeSpan := util.ParseDateRange(req.CreatedTime); len(timeSpan) > 0 {
		condition["createdAt"] = timeSpan
	}
	if amountSpan := util.ParseIntegerRange(req.ProfitAmount); len(amountSpan) > 0 {
		condition["distribution.amount"] = amountSpan
	}

	if req.Status != "" {
		req.DistributionStatus = append(req.DistributionStatus, req.Status)
		req.DistributionStatus = util.StrArrayUnique(req.DistributionStatus)
	}

	if len(req.DistributionStatus) > 0 {
		condition["distribution.profitSharingStatus"] = bson.M{
			"$in": req.DistributionStatus,
		}
		// 对于查询待结算状态，滤掉已取消的订单
		if len(core_util.IntersectStringSlice([]string{
			order_model.ORDER_RECEIVER_PROFIT_STATUS_PENDING,
		}, req.DistributionStatus)) > 0 {
			if len(req.OrderStatus) > 0 {
				req.OrderStatus = util.RemoveStrArrayElems(req.OrderStatus, []string{order_model.ORDER_STATUS_CANCELED})
			}
			if len(req.OrderStatus) == 0 {
				condition["status"] = bson.M{
					"$ne": order_model.ORDER_STATUS_CANCELED,
				}
			}
		}
	}

	if len(req.OrderStatus) > 0 {
		condition["status"] = bson.M{
			"$in": req.OrderStatus,
		}
	}

	if req.Method != "" {
		condition["method"] = req.Method
	}

	if req.MemberId != "" {
		var (
			promoterIds    []bson.ObjectId
			subPromoterIds []bson.ObjectId
		)
		pCondition := bson.M{
			"memberId": bson.ObjectIdHex(req.MemberId),
		}
		p, err := distribution_model.CPromoter.GetByDefaultCondition(ctx, pCondition)
		if err != nil {
			return nil, err
		}
		if p.ParentId.Valid() {
			// 如果当前 memberId 是二级分销员，那么只返回自己的订单
			subPromoterIds = append(subPromoterIds, p.Id)
			promoterIds = append(promoterIds, p.HistoryParentIds...)
		} else if len(req.PromoterIds) > 0 {
			subPromoterIds = util.ToMongoIds(req.PromoterIds)
			promoterIds = util.ToMongoIds(req.PromoterIds)
		} else {
			promoterIds = distribution_model.CPromoter.GetAllSubPromoterIds(ctx, p.Id)
		}
		promoterIds = append(promoterIds, p.Id)
		condition["distribution.promoterId"] = bson.M{"$in": promoterIds}
		if util.StrInArray(p.Id.Hex(), &req.PromoterIds) && p.ParentId.IsZero() {
			condition["distribution.subPromoterId"] = bson.M{"$exists": false}
		}
		if len(subPromoterIds) > 0 {
			subPromoterIdCond := condition["distribution.subPromoterId"]
			if subPromoterIdCond != nil {
				delete(condition, "distribution.subPromoterId")
				condition["$or"] = []bson.M{
					{
						"distribution.subPromoterId": subPromoterIdCond,
					},
					{
						"distribution.subPromoterId": bson.M{"$in": subPromoterIds},
					},
				}
			} else {
				condition["distribution.subPromoterId"] = bson.M{"$in": subPromoterIds}
			}
		}
		// 小程序端不显示没有分销商品的分销订单（仅大众分销不显示）
		condition["products"] = bson.M{
			"$elemMatch": bson.M{"isDistribution": true},
		}
	}

	if req.OrderMemberId != "" {
		condition["memberId"] = util.ToMongoId(req.OrderMemberId)
	}

	if req.Distributor != nil && len(req.Distributor.Ids) > 0 {
		condition["distributorIds"] = bson.M{
			"$in": util.ToMongoIds(req.Distributor.Ids),
		}
	}

	if len(req.Tags) > 0 {
		tagCondition := []bson.M{}
		for _, tag := range req.Tags {
			tagCondition = append(tagCondition, bson.M{
				"tags": tag,
			})
		}
		condition["$and"] = tagCondition
	}

	if req.RefundStatus != "" {
		if req.RefundStatus == "refunded" {
			condition["refundStatus"] = bson.M{
				"$in": []string{
					order_model.ORDER_REFUND_STATUS_REFUNDING,
					order_model.ORDER_REFUND_STATUS_REFUNDED,
				},
			}
		} else {
			condition["refundStatus"] = bson.M{
				"$exists": false,
			}
		}
	}
	if len(req.IgnoreTags) > 0 {
		condition["tags"] = bson.M{
			"$nin": req.IgnoreTags,
		}
	}
	if bson.IsObjectIdHex(req.TransferBillId) {
		transferBill, err := profitsharing_model.CTransferBill.GetById(ctx, bson.ObjectIdHex(req.TransferBillId))
		if err != nil {
			return nil, err
		}
		// 适配注销后又注册的情况
		promoters, _ := distribution_model.CPromoter.ListByOpenId(ctx, transferBill.Receiver.Account, distribution_model.PROMOTER_TYPE_MEMBER, true)
		if len(promoters) == 0 {
			return nil, errors.NewNotExistsError("promoter")
		}
		condition["distribution.promoterId"] = bson.M{
			"$in": util.ToMongoIds(core_util.ExtractArrayStringField("Id", promoters)),
		}
		profit, _ := order_model.COrderReceiverProfit.GetByTransferBillId(ctx, transferBill.Id)
		if profit.Order.Id.Valid() {
			condition["_id"] = profit.Order.Id
		} else {
			condition["distribution.transferBillId"] = transferBill.Id
		}
	}

	return util.FormatConditionContainedOr(condition), nil
}

// 获取属于指定组织节点下的分销员 id 列表
func GetPromoterIdsByDistributorIds(ctx context.Context, DistributorIds []string) ([]bson.ObjectId, error) {
	// 查找指定组织节点下的所有导购 id
	staffIds, err := ec_model.GetStaffIdsByDistributors(ctx, DistributorIds)
	if err != nil {
		return nil, err
	}
	// 通过导购 id 获取对应的分销员列表
	promoters, err := distribution_model.CPromoter.GetByStaffIds(ctx, staffIds, true)
	if err != nil {
		return nil, err
	}
	promoterIds := []bson.ObjectId{}
	for _, promoter := range promoters {
		promoterIds = append(promoterIds, promoter.Id)
	}
	return promoterIds, nil
}

// 在指定组织节点下按照关键词查找分销员
func GetPromoterIdsByDistributionIdsAndSearchKey(ctx context.Context, DistributorIds []string, searchKey string) ([]bson.ObjectId, error) {
	// 通过关键词查找指定组织节点下的所有导购 id
	staffIds, err := ec_model.GetStaffIdsByDistributorsAndSearchkey(ctx, DistributorIds, searchKey)
	if err != nil {
		return nil, err
	}
	// 通过导购 id 获取对应的分销员列表
	promoters, err := distribution_model.CPromoter.GetByStaffIds(ctx, staffIds, true)
	if err != nil {
		return nil, err
	}
	promoterIds := []bson.ObjectId{}
	for _, promoter := range promoters {
		promoterIds = append(promoterIds, promoter.Id)
	}
	return promoterIds, nil
}

// 通过客户 id 列表获取对应的客户详情映射
func getMembersMap(ctx context.Context, memberIds []string) (map[interface{}]interface{}, error) {
	members, err := share.GetMembersByIds(ctx, core_util.ToStringArray(memberIds))
	if err != nil {
		return nil, err
	}
	membersMap := core_util.MakeMapper("Id", members.Members)
	return membersMap, nil
}

// 通过门店 id 列表获取对应的门店详情的映射
func getStoresMap(ctx context.Context, storeIds []string) (map[interface{}]interface{}, error) {
	stores, err := share.GetStoresByIds(ctx, core_util.ToStringArray(storeIds))
	if err != nil {
		return nil, err
	}
	storesMap := core_util.MakeMapper("Id", stores.Stores)
	return storesMap, nil
}

// 格式化分销订单返回值
func formatOrders(ctx context.Context, orders []order_model.Order, membersMap, storesMap map[interface{}]interface{}, distributionType string) []*distribution.DistributionOrderDetail {
	respOrders := []*distribution.DistributionOrderDetail{}

	var (
		promoterIds       []bson.ObjectId
		customerMemberIds []string
	)

	// 根据查询的分销类型获取对应的分销设置
	dSetting, _ := distribution_model.CDistributionSetting.GetByType(ctx, distributionType)

	for _, rawOrder := range orders {
		// 订单分销过程中记录了系统内部错误信息，替换为中文说明
		if strings.Contains(rawOrder.Distribution.ProfitFailedMsg, "Wechat trade error!") {
			rawOrder.Distribution.ProfitFailedMsg = "系统内部错误"
		}
		products := []*distribution.DistributionOrderProductDetail{}
		for productIndex, p := range rawOrder.Products {
			product := &distribution.DistributionOrderProductDetail{}
			core_util.CopyFieldsRFC3339(p, product)
			// 某些历史订单不存在 distributionMode 字段，通过 GetDistributionMode 方法判断分销模式
			product.DistributionMode = p.GetDistributionMode()
			product.DistributionProportion = float32(p.DistributionProportion)
			spec := &distribution.SpecProdSku{}
			core_util.CopyFields(p.Spec, spec)
			product.Spec = spec
			properties := []string{}
			for _, p := range p.Spec.Properties {
				properties = append(properties, p.Value)
			}
			product.Spec.Properties = properties
			if !core_util.IsEmpty(reflect.ValueOf(p.DistributionCoupon)) {
				coupon := &distribution.Coupon{}
				coupon.Id = p.DistributionCoupon.Id.Hex()
				coupon.Name = p.DistributionCoupon.Name
				product.Coupon = coupon
			}
			product.IsAmountAdjusted = p.IsAmountAdjusted()

			product.PrepaidCardPayAmount = rawOrder.GetOrderProductPrepaidCardPayAmount(productIndex)
			// 格式化商品分销信息
			formatProductDistributionInfo(dSetting, product, p, rawOrder)
			products = append(products, product)
		}

		d := &distribution.OrderDistribution{}
		core_util.CopyFieldsRFC3339(rawOrder.Distribution, d)
		// 非错误记录不展示错误信息
		if d.ProfitSharingStatus != order_model.DISTRIBUTION_STATUS_FAILED {
			d.ProfitFailedMsg = ""
		}
		// 创建 transferBill 之前退款导致的无分佣，此时 ProfitSharingStatus 为空
		if d.Amount == 0 && order_model.IsInRefundProcess(rawOrder.RefundStatus) && d.ProfitSharingStatus == "" {
			d.ProfitSharingStatus = order_model.DISTRIBUTION_STATUS_NOCOMMISSION
		}
		d.MemberId = rawOrder.MemberId.Hex()

		respOrder := &distribution.DistributionOrderDetail{}
		core_util.CopyFieldsRFC3339(rawOrder, respOrder)

		for _, c := range rawOrder.Campaigns {
			tempCampaign := &common.CampaignDetail{}
			copier.Instance(nil).From(c).CopyTo(tempCampaign)
			respOrder.Campaigns = append(respOrder.Campaigns, tempCampaign)
		}

		copier.Instance(nil).From(rawOrder.PrepaidCards).CopyTo(&respOrder.PrepaidCards)
		respOrder.OrderStatus = rawOrder.Status
		respOrder.Method = rawOrder.Method
		respOrder.LogisticsFee = rawOrder.Logistics.Fee
		respOrder.Products = products
		respOrder.Distribution = d
		respOrder.MemberId = rawOrder.MemberId.Hex()
		respOrder.Contact = &distribution.OrderContact{
			Name:  rawOrder.Contact.Name,
			Phone: rawOrder.Contact.Phone,
		}
		if d.ProfitSharingStatus == order_model.DISTRIBUTION_STATUS_PROFITED {
			respOrder.Distribution.ProfitFailedMsg = ""
		}
		promoterIds = append(promoterIds, rawOrder.Distribution.PromoterId)
		if rawOrder.Distribution.ParentPromoterId.Valid() {
			promoterIds = append(promoterIds, rawOrder.Distribution.ParentPromoterId)
		}
		if rawOrder.Distribution.SubPromoterId.Valid() {
			promoterIds = append(promoterIds, rawOrder.Distribution.SubPromoterId)
		}
		customerMemberIds = append(customerMemberIds, rawOrder.MemberId.Hex())
		if membersMap[rawOrder.MemberId.Hex()] == nil {
			respOrder.Name = ""
		} else {
			respOrder.Name = membersMap[rawOrder.MemberId.Hex()].(*pb_member.MemberDetailResponse).Name
			respOrder.Phone = membersMap[rawOrder.MemberId.Hex()].(*pb_member.MemberDetailResponse).Phone
		}
		if storesMap[rawOrder.StoreId.Hex()] != nil {
			respOrder.StoreName = storesMap[rawOrder.StoreId.Hex()].(*pb_store.Store).Name
		}
		if rawOrder.Extra != nil {
			e, _ := json.Marshal(rawOrder.Extra)
			respOrder.Extra = string(e)
		}
		respOrders = append(respOrders, respOrder)
	}

	// 获取分销员列表，用于格式化分销员信息
	promoters := getPromoterInfo(ctx, promoterIds)
	if len(promoters) == 0 {
		return respOrders
	}
	var (
		staffIds    []bson.ObjectId
		promoterMap = make(map[string]*distribution_model.Promoter)
	)
	for _, p := range promoters {
		promoterMap[p.Id.Hex()] = p
		if p.Type == distribution_model.PROMOTER_TYPE_STAFF && p.StaffId.Hex() != "" {
			staffIds = append(staffIds, p.StaffId)
		}
	}

	staffs, _ := store_model.CStaff.GetByIds(ctx, staffIds)

	customerMemberIds = core_util.RemoveDuplicateItem(customerMemberIds)
	customersResp, err := SearchMember(ctx, "", customerMemberIds)
	if err != nil {
		return respOrders
	}

	// 获取预分账记录，用于确定日结订单的分销状态
	profitOrders, _ := order_model.COrderReceiverProfit.GetByPromoterIds(ctx, promoterIds)

	for index, order := range respOrders {
		parentPromoter := promoterMap[order.Distribution.ParentPromoterId]
		if parentPromoter != nil {
			respOrders[index].Distribution.ParentPromoterName = parentPromoter.Name
			respOrders[index].Distribution.ParentPromoterPhone = parentPromoter.Phone
		}
		promoterId := order.Distribution.PromoterId
		if order.Distribution.SubPromoterId != "" {
			promoterId = order.Distribution.SubPromoterId
		}
		promoter := promoterMap[promoterId]
		if promoter != nil {
			respOrders[index].Distribution.PromoterId = promoter.Id.Hex()
			respOrders[index].Distribution.Name = promoter.Name
			respOrders[index].Distribution.Phone = promoter.Phone
			respOrders[index].Distribution.StaffFrozen = promoter.Frozen
			respOrders[index].Distribution.StaffFrozenAt = promoter.FrozenAt.Unix()
			respOrders[index].Distribution.StaffFrozenRemark = promoter.FrozenRemark

			if promoter.Type == distribution_model.PROMOTER_TYPE_STAFF && len(staffs) != 0 {
				// 如果是导购分销，需要格式化导购的信息
				for _, staff := range staffs {
					if promoter.StaffId == staff.Id {
						respOrders[index].Distribution.StaffNo = staff.StaffNo
						respOrders[index].Distribution.StaffId = staff.Id.Hex()
						if staff.Phone != "" {
							respOrders[index].Distribution.Phone = staff.Phone
						}
						break
					}
				}
			}
		}

		if len(customersResp.Members) != 0 {
			for _, customer := range customersResp.Members {
				if order.Distribution.MemberId == customer.Id {
					respOrders[index].Distribution.CustomerName = customer.Name
					break
				}
			}
		}

		// 获取预分账订单状态
		if len(profitOrders) != 0 {
			for _, o := range profitOrders {
				if o.Promoter.Id.Hex() == order.Distribution.PromoterId &&
					o.Order.Id.Hex() == order.Id {
					if o.Status == order_model.ORDER_RECEIVER_PROFIT_STATUS_PENDING_PAYMENT {
						o.Status = order_model.ORDER_RECEIVER_PROFIT_STATUS_PROCESSING
					}
					respOrders[index].Distribution.ProfitSharingStatus = o.Status
				}
			}
		}
	}

	return respOrders
}

func formatProductDistributionInfo(dSetting *distribution_model.DistributionSetting, product *distribution.DistributionOrderProductDetail, orderProduct order_model.OrderProduct, order order_model.Order) {
	orderDistribution := order.Distribution
	product.DistributionStatus = orderDistribution.ProfitSharingStatus
	// 格式化分销订单无佣金说明
	product.DistributionMsg = getNoCommissionMsg(orderDistribution.Extra, order, orderProduct)
	if orderDistribution.ProfitFailedMsg != "" {
		// 如果分销过程存在错误，需要返回错误信息
		product.DistributionMsg = orderDistribution.ProfitFailedMsg
	}
	if dSetting == nil {
		return
	}
	isProductDist := false
	// 判断是否是商品分销
	if dSetting.Type == distribution_model.DISTRIBUTION_SETTING_TYPE_STAFF {
		isProductDist = dSetting.PromoterCommission.CommissionType == distribution_model.PROPORTION_TYPE_PRODUCT
	} else {
		isProductDist = dSetting.Commission.ProportionType == distribution_model.PROPORTION_TYPE_PRODUCT
	}
	// 如果是商品分销，则订单商品一定要是分销商品，否则一定是无佣金，且状态说明固定为“非分销商品”
	if isProductDist && !orderProduct.IsDistribution {
		product.DistributionStatus = order_model.DISTRIBUTION_STATUS_NOCOMMISSION
		product.DistributionMsg = "非分销商品"
		return
	}
	if orderDistribution.ProfitSharingStatus == order_model.DISTRIBUTION_STATUS_NOCOMMISSION {
		product.DistributionStatus = orderDistribution.ProfitSharingStatus
		product.DistributionMsg = getNoCommissionMsg(orderDistribution.Extra, order, orderProduct)
		return
	}
}

func getNoCommissionMsg(extra map[string]string, order order_model.Order, orderProduct order_model.OrderProduct) string {
	msg := extra["无佣金原因"]
	if msg == "" {
		msg = extra["无分佣原因"]
	}
	if msg != "" {
		return msg
	}
	if order_model.IsInRefundProcess(orderProduct.RefundStatus) {
		return "该订单中商品已退款"
	}
	if len(order.PrepaidCards) != 0 {
		return "礼品卡订单不参与分销"
	}
	return ""
}

func getPromoterInfo(ctx context.Context, promoterIds []bson.ObjectId) []*distribution_model.Promoter {
	resp := []*distribution_model.Promoter{}
	if len(promoterIds) == 0 {
		return resp
	}

	condition := bson.M{
		"_id": bson.M{
			"$in": promoterIds,
		},
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}
	err := extension.DBRepository.FindAll(ctx, distribution_model.C_PROMOTER, condition, []string{}, 0, &resp)
	if err != nil {
		return resp
	}

	return resp
}

func getMemberIdsBySearchKey(ctx context.Context, searchKey string) ([]string, error) {
	if validators.CValidator.IsPhone(searchKey, nil) {
		resp, err := share.GetMemberByPhone(ctx, searchKey)
		if err != nil {
			return nil, err
		}
		return []string{resp.Id}, nil
	}
	resp, err := client.GetMemberServiceClient().SearchMemberIds(ctx, &pb_member.SearchMemberRequest{
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: 100,
		},
		SearchKeyWord: searchKey,
	})
	if err != nil {
		return nil, err
	}
	return resp.MemberIds, nil
}

func CalDistributionOrdersStats(ctx context.Context, service DistributionService, req *distribution.ListDistributionOrdersRequest, resp *distribution.ListDistributionOrdersResponse) error {
	if req.MemberId == "" {
		return nil
	}
	promoter, err := distribution_model.CPromoter.GetByMemberId(ctx, bson.ObjectIdHex(req.MemberId))
	if err != nil {
		return err
	}

	var (
		memberIds                    []bson.ObjectId
		needCalculateHistoryPromoter = false
	)
	if !promoter.ParentId.Valid() {
		condition := distribution_model.Common.GenDefaultCondition(ctx)
		if len(req.PromoterIds) > 0 {
			condition["_id"] = bson.M{"$in": util.ToMongoIds(req.PromoterIds)}
		} else {
			memberIds = append(memberIds, promoter.MemberId)
			condition["parentId"] = promoter.Id
		}
		var ids []interface{}
		extension.DBRepository.Distinct(ctx, distribution_model.C_PROMOTER, condition, "memberId", &ids)
		if len(ids) > 0 {
			memberIds = append(memberIds, core_util.ToObjectIdArray(ids)...)
		}
	} else {
		needCalculateHistoryPromoter = true
		memberIds = append(memberIds, promoter.MemberId)
	}
	memberIds = core_util.RemoveDuplicateItem(memberIds)

	pool, err := util.NewGoroutinePoolWithPanicHandler(5)
	if err != nil {
		return err
	}
	defer pool.Release()

	var (
		wg          = &sync.WaitGroup{}
		syncMapping = &sync.Map{}
	)

	for _, id := range memberIds {
		memberId := id
		wg.Add(1)
		_ = pool.Submit(func() {
			defer wg.Done()
			transferBillRequest := &distribution.ListPromoterTransferBillsRequest{
				MemberId:                           memberId.Hex(),
				ListCondition:                      &request.ListCondition{Page: 1, PerPage: 1},
				NeedCalculateTotalAmount:           true,
				NeedCalculateHistoryParentPromoter: needCalculateHistoryPromoter,
			}
			if req.CreatedTime != nil {
				transferBillRequest.CreatedTime = &types.StringDateRange{
					Start: time.Unix(req.CreatedTime.Start, 0).Format(core_util.RFC3339Mili),
					End:   time.Unix(req.CreatedTime.End, 0).Format(core_util.RFC3339Mili),
					Type:  req.CreatedTime.Type,
				}
			}
			listTransferBillResp, err := service.ListPromoterTransferBills(ctx, transferBillRequest)
			if err != nil {
				return
			}
			syncMapping.Store(memberId.Hex(), listTransferBillResp)
		})
	}
	wg.Wait()

	syncMapping.Range(func(key, value interface{}) bool {
		if billResp, ok := value.(*distribution.ListPromoterTransferBillsResponse); ok {
			if req.MemberId == cast.ToString(key) {
				resp.TotalProfitAmount += billResp.TotalAmount
				resp.WaitProfitAmount += billResp.WaitAmount
			}
			resp.TotalShareAmount += billResp.TotalShareAmount
			resp.DistProductTotalAmount += billResp.DistProductTotalAmount
		}
		return true
	})

	return nil
}
