package distribution

import (
	core_component "mairpc/core/component"
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/proto/ec/distribution"
	"mairpc/proto/ec/staff"
	ec_client "mairpc/service/ec/client"
	ec_model "mairpc/service/ec/model"
	distribution_model "mairpc/service/ec/model/distribution"

	share_util "mairpc/service/share/util"
	"time"

	"golang.org/x/net/context"
)

func (d DistributionService) UpdateDistributionSetting(ctx context.Context, req *distribution.UpdateDistributionSettingRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if req.PromoterCommission != nil && req.Type == distribution_model.DISTRIBUTION_SETTING_TYPE_STAFF {
		if err := validateStaffPromoterCommissionSetting(ctx, req.PromoterCommission); err != nil {
			return nil, err
		}
	}

	dSetting, err := distribution_model.CDistributionSetting.GetByType(ctx, req.Type)
	if err != nil {
		return nil, err
	}
	autoApprovalBeforeUpdate := dSetting.AutoApproval
	if !dSetting.HasEnabled && dSetting.Enabled {
		dSetting.HasEnabled = true
	}
	needUnbindAllMemberPromoter := req.Rule != nil && !req.Rule.CanBindSelf && dSetting.Rule.CanBindSelf
	needBindAllMemberPromoter := req.Rule != nil && req.Rule.CanBindSelf && !dSetting.Rule.CanBindSelf

	dSetting = getUpdateField(dSetting, req)
	if dSetting.Enabled && dSetting.Type == distribution_model.PROMOTER_TYPE_MEMBER && ec_model.IsChainRetail(ctx) {
		setDefaultSettingForChainRetail(dSetting)
	}

	if dSetting.PromoterCommission.TaxSetting.DeductTax {
		dSetting.RuleDescription = distribution_model.GenerateDefaultRuleDescription(dSetting.PromoterCommission.TaxSetting.AccountType)
	}

	err = dSetting.Update(ctx, req)
	if err != nil {
		return nil, err
	}
	autoApprovalAfterUpdate := dSetting.AutoApproval

	if dSetting.Rule.BoundType == distribution_model.BOUNDTYPE_UNBOUND {
		promoterMemberIds := distribution_model.CPromoterMember.UnbindAllExceptSelf(ctx)
		// 手动设置不绑定也要将绑定在导购分销员上的客户绑定到自己已注册的大众分销员上
		if dSetting.Type == distribution_model.PROMOTER_TYPE_STAFF {
			HandleUnbindStaffPromoter(ctx, promoterMemberIds)
		}
	}

	// 限定等级的暂时不处理自动通过审核，需要遍历，效率比较低，等客户需求。
	if !autoApprovalBeforeUpdate && autoApprovalAfterUpdate && dSetting.AutoApprovalLimit.MemberLevel == 0 {
		err = approvePromoters(ctx)
		if err != nil {
			return nil, err
		}
	}

	// 解绑大众分销员所有历史绑定关系
	if needUnbindAllMemberPromoter {
		core_component.GO(ctx, func(ctx context.Context) {
			_, err := d.UnbindPromoterMemberForPromoterSelf(ctx, &request.EmptyRequest{})
			if err != nil {
				log.Warn(ctx, "failed to unbind all member promoter", log.Fields{"err": err})
			}
		})
	}
	// 恢复绑定所有分销员和自己的绑定
	if needBindAllMemberPromoter {
		core_component.GO(ctx, func(ctx context.Context) {
			_, err := d.BindPromoterMemberForPromoterSelf(ctx, &request.EmptyRequest{})
			if err != nil {
				log.Warn(ctx, "failed to bind all member promoter", log.Fields{"err": err})
			}
		})
	}

	return &response.EmptyResponse{}, nil
}

func validateStaffPromoterCommissionSetting(ctx context.Context, setting *distribution.PromoterCommissionSetting) error {
	if setting.CommissionType == distribution_model.COMMISSION_TYPE_ORDER {
		setting.DistributionMode = distribution_model.PROMOTER_COMMISSION_DISTRIBUTION_MODE_RANGE
		setting.StaffLevels = nil
		setting.ProductDefaultProfitAmount = 0
		return validateStaffPromoterCommissionRanges(ctx, setting.Range)
	}
	setting.Range = nil
	if setting.DistributionMode == "" {
		if setting.ProductDefaultProfitAmount > 0 {
			setting.DistributionMode = distribution_model.PROMOTER_COMMISSION_DISTRIBUTION_MODE_AMOUNT
		} else {
			setting.DistributionMode = distribution_model.PROMOTER_COMMISSION_DISTRIBUTION_MODE_STAFF_LEVELS
		}
	}

	switch setting.DistributionMode {
	case distribution_model.PROMOTER_COMMISSION_DISTRIBUTION_MODE_AMOUNT:
		if setting.ProductDefaultProfitAmount == 0 {
			return errors.NewInvalidArgumentErrorWithMessage("productDefaultProfitAmount", "ProductDefaultProfitAmount is required")
		}
	case distribution_model.PROMOTER_COMMISSION_DISTRIBUTION_MODE_PROPORTION:
		if setting.DefaultProportion == 0 {
			return errors.NewInvalidArgumentErrorWithMessage("defaultProportion", "DefaultProportion is required")
		}
	case distribution_model.PROMOTER_COMMISSION_DISTRIBUTION_MODE_STAFF_LEVELS:
		staffLevelMap, _ := getStaffLevelMap(ctx)
		if len(staffLevelMap) == 0 {
			return errors.NewNotExistsError("staffLevels")
		}
		return validateStaffLevelSettings(staffLevelMap, setting.StaffLevels)
	default:
		return errors.NewInvalidArgumentError("distributionMode")
	}
	return nil
}

func validateStaffPromoterCommissionRanges(ctx context.Context, ranges []*distribution.RangeSetting) error {
	if len(ranges) == 0 {
		return errors.NewInvalidArgumentErrorWithMessage("range", "Range is required")
	}
	ensureDistributionModeForRange(ranges[0])
	distributionMode := ranges[0].DistributionMode
	staffLevelMap := map[uint64]*staff.StaffLevel{}
	if distributionMode == distribution_model.RANGE_DISTRIBUTION_MODE_STAFF_LEVELS {
		staffLevelMap, _ = getStaffLevelMap(ctx)
		if len(staffLevelMap) == 0 {
			return errors.NewNotExistsError("staffLevels")
		}
	}
	for i := range ranges {
		r := ranges[i]
		ensureDistributionModeForRange(r)
		if r.DistributionMode != distributionMode {
			return errors.NewInvalidArgumentErrorWithMessage("range", "All range should be the same distributionMode")
		}
		if r.DistributionMode == distribution_model.RANGE_DISTRIBUTION_MODE_STAFF_LEVELS {
			if err := validateStaffLevelSettings(staffLevelMap, r.StaffLevels); err != nil {
				return err
			}
		}
	}
	return nil
}

func validateStaffLevelSettings(staffLevelMap map[uint64]*staff.StaffLevel, staffLevels []*distribution.StaffLevelSetting) error {
	if len(staffLevels) != len(staffLevelMap) {
		return errors.NewInvalidArgumentErrorWithMessage("staffLevels", "Incomplete staffLevels setting")
	}
	for _, level := range staffLevels {
		if _, ok := staffLevelMap[level.Level]; !ok {
			return errors.NewInvalidArgumentErrorWithMessage("staffLevels", "Level that does not exist")
		}
	}
	return nil
}

func ensureDistributionModeForRange(r *distribution.RangeSetting) {
	if r.DistributionMode != "" {
		return
	}
	// 默认为 amount
	r.DistributionMode = distribution_model.RANGE_DISTRIBUTION_MODE_AMOUNT
	if r.Proportion != 0 {
		r.DistributionMode = distribution_model.RANGE_DISTRIBUTION_MODE_PROPORTION
		r.Amount = 0
		r.StaffLevels = nil
	} else if r.Amount != 0 {
		r.DistributionMode = distribution_model.RANGE_DISTRIBUTION_MODE_AMOUNT
		r.Proportion = 0
		r.StaffLevels = nil
	} else if len(r.StaffLevels) > 0 {
		r.DistributionMode = distribution_model.RANGE_DISTRIBUTION_MODE_STAFF_LEVELS
		r.Amount = 0
		r.Proportion = 0
	}
}

func getStaffLevelMap(ctx context.Context) (map[uint64]*staff.StaffLevel, error) {
	staffLevelMap := map[uint64]*staff.StaffLevel{}
	resp, err := ec_client.StaffService.ListStaffLevels(ctx, &staff.ListStaffLevelsRequest{
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: 1000,
			OrderBy: []string{"level"},
		},
	})
	if err != nil {
		return staffLevelMap, errors.NewNotExistsError("staffLevels")
	}
	for _, level := range resp.Items {
		staffLevelMap[level.Level] = level
	}
	return staffLevelMap, nil
}

func getUpdateField(dSetting *distribution_model.DistributionSetting, req *distribution.UpdateDistributionSettingRequest) *distribution_model.DistributionSetting {
	if req.Enabled != nil {
		dSetting.Enabled = req.Enabled.Value
	}
	if req.RecruitmentEnabled != nil {
		dSetting.RecruitmentEnabled = req.RecruitmentEnabled.Value
	}
	if req.RecruitmentType != "" {
		dSetting.RecruitmentType = req.RecruitmentType
	}
	if req.AutoApproval != nil {
		dSetting.AutoApproval = req.AutoApproval.Value
	}
	if req.NeedApproval != nil {
		dSetting.NeedApproval = req.NeedApproval.Value
	}
	if req.AutoApprovalLimit != nil {
		dSetting.AutoApprovalLimit = distribution_model.AutoApprovalLimit{
			MemberLevel:     req.AutoApprovalLimit.MemberLevel,
			MemberLevelName: req.AutoApprovalLimit.MemberLevelName,
		}
	}

	if req.Rule != nil {
		if req.Rule.ReplaceType != "" {
			dSetting.Rule.ReplaceType = req.Rule.ReplaceType
		}
		if req.Rule.BoundType != "" {
			dSetting.Rule.BoundType = req.Rule.BoundType
			// 设置为不绑定时，自动设置为允许抢客
			if dSetting.Rule.BoundType == distribution_model.BOUNDTYPE_UNBOUND {
				dSetting.Rule.ReplaceType = distribution_model.REPLACETYPE_ALLOW
			}
		}
		if req.Rule.ProtectionPeriod != 0 {
			dSetting.Rule.ProtectionPeriod = uint64(req.Rule.ProtectionPeriod)
		}
		if req.Rule.AvailablePeriod != 0 {
			dSetting.Rule.AvailablePeriod = uint64(req.Rule.AvailablePeriod)
		}
		dSetting.Rule.IsServiceStaffFirst = req.Rule.IsServiceStaffFirst
		dSetting.Rule.BoundStaffProportionType = req.Rule.BoundStaffProportionType
		dSetting.Rule.IsCarryStoreId = req.Rule.IsCarryStoreId
		if req.Type == distribution_model.PROMOTER_TYPE_MEMBER {
			dSetting.Rule.CanBindSelf = req.Rule.CanBindSelf
		}
		if req.Rule.PromoterMemberBindMode != "" {
			dSetting.Rule.PromoterMemberBindMode = req.Rule.PromoterMemberBindMode
		}
	}

	if dSetting.Rule.PromoterMemberBindMode == "" {
		dSetting.Rule.PromoterMemberBindMode = distribution_model.PROMOTER_MEMBER_BIND_MODE_LINK
	}

	if req.Commission != nil {
		if req.Commission.Proportion != 0 {
			dSetting.Commission.Proportion = float64(req.Commission.Proportion)
		}
		if req.Commission.ProportionType != "" {
			dSetting.Commission.ProportionType = req.Commission.ProportionType
		}
		if req.Commission.ProfitSharingType != 0 {
			dSetting.Commission.ProfitSharingType = int(req.Commission.ProfitSharingType)
		}
		dSetting.Commission.IsDisplayCommission = req.Commission.IsDisplayCommission
	}

	if req.Recruitment != nil {
		if req.Recruitment.Title != "" {
			dSetting.Recruitment.Title = req.Recruitment.Title
		}
		if req.Recruitment.Color != "" {
			dSetting.Recruitment.Color = req.Recruitment.Color
		}
		if req.Recruitment.Image != "" {
			dSetting.Recruitment.Image = req.Recruitment.Image
		}
	}

	if req.PromoterCommission != nil {
		dSetting.PromoterCommission.DeductTax = req.PromoterCommission.DeductTax
		dSetting.PromoterCommission.DistributionMode = req.PromoterCommission.DistributionMode
		// 标识是否开启过代扣个税
		if req.PromoterCommission.DeductTax {
			dSetting.PromoterCommission.HasDeductTaxTurnedOn = true
		}

		if req.PromoterCommission.Cycle != "" {
			dSetting.PromoterCommission.Cycle = req.PromoterCommission.Cycle
		}
		if req.PromoterCommission.CalculateType != "" {
			dSetting.PromoterCommission.CalculateType = req.PromoterCommission.CalculateType
		}
		if len(req.PromoterCommission.Range) != 0 {
			tmpRange := []distribution_model.RangeSetting{}
			for _, r := range req.PromoterCommission.Range {
				rangeSetting := distribution_model.RangeSetting{}
				copier.Instance(nil).From(r).CopyTo(&rangeSetting)
				tmpRange = append(tmpRange, rangeSetting)
			}

			// 至少保留一个区间
			if len(tmpRange) > 0 {
				dSetting.PromoterCommission.Range = tmpRange
			}
		}
		if req.PromoterCommission.CommissionType != "" {
			dSetting.PromoterCommission.CommissionType = req.PromoterCommission.CommissionType
		}
		if req.PromoterCommission.ProductDefaultProfitAmount > 0 || req.PromoterCommission.DefaultProportion > 0 || len(req.PromoterCommission.StaffLevels) > 0 {
			dSetting.PromoterCommission.ProductDefaultProfitAmount = req.PromoterCommission.ProductDefaultProfitAmount
			dSetting.PromoterCommission.DefaultProportion = req.PromoterCommission.DefaultProportion
			copier.Instance(nil).From(req.PromoterCommission.StaffLevels).CopyTo(&dSetting.PromoterCommission.StaffLevels)
		}
		if req.PromoterCommission.TaxSetting != nil {
			dSetting.PromoterCommission.TaxSetting.DeductTax = req.PromoterCommission.TaxSetting.DeductTax
			dSetting.PromoterCommission.TaxSetting.AccountType = req.PromoterCommission.TaxSetting.AccountType
		}
	}

	if req.PosterSetting != nil {
		_ = copier.Instance(nil).From(req.PosterSetting).CopyTo(&dSetting.PosterSetting)
	}

	if req.RuleDescription != nil {
		_ = copier.Instance(nil).From(req.RuleDescription).CopyTo(&dSetting.RuleDescription)
	}
	if req.SubPromoterSetting != nil {
		dSetting.SubPromoterSetting = distribution_model.SubPromoterSetting{
			AmountSendTo:        req.SubPromoterSetting.AmountSendTo,
			AllowCreateByParent: req.SubPromoterSetting.AllowCreateByParent,
		}
	}
	return dSetting
}

func approvePromoters(ctx context.Context) error {
	selector := bson.M{
		"accountId": share_util.GetAccountIdAsObjectId(ctx),
		"status":    distribution_model.PROMOTER_STATUS_PENDING,
		"isDeleted": false,
	}

	updater := bson.M{
		"$set": bson.M{
			"updatedAt":  time.Now(),
			"status":     distribution_model.PROMOTER_STATUS_APPROVED,
			"approvedAt": time.Now(),
		},
	}

	return distribution_model.CPromoter.UpdateAllByCondition(ctx, selector, updater)
}

func setDefaultSettingForChainRetail(dSetting *distribution_model.DistributionSetting) {
	dSetting.RecruitmentEnabled = false
	dSetting.Commission = distribution_model.CommissionSetting{
		ProfitSharingType: 1,
		ProportionType:    distribution_model.PROPORTION_TYPE_PRODUCT,
	}
	dSetting.RuleDescription.Content = "<p style=\"white-space: normal;\"><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444;\">Q</span></strong><strong><span style=\"color: #444444;\">：如何确认商品是否参与分销？如何确认分佣金额？</span></strong></span></p><p style=\"margin-top: 5px; white-space: normal;\"><span style=\"color: #444444; font-size: 14px;\">A：您可在“我的”-“分销中心”-“推广商品”页面查看参与分销的商品，推广商品页面展示了商品的推广分佣金额。</span></p><hr style=\"white-space: normal;\"/><p style=\"white-space: normal;\"><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444;\">Q</span></strong><strong><span style=\"color: #444444;\">：推广商品的佣金何时发放？发到哪里？</span></strong></span></p><p style=\"margin-top: 5px; white-space: normal;\"><span style=\"color: #444444; font-size: 14px;\">A：推广商品的佣金将统一在交易售后维权期结束后结算到您的商户；已结算的佣金将在次日转账至您的银行卡账户，请注意查收。</span></p><p style=\"white-space: normal;\"><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444;\"></span></strong></span></p><hr style=\"white-space: normal;\"/><p style=\"white-space: normal;\"><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444;\">Q</span></strong><strong><span style=\"color: #444444;\">：如何查看收益记录？</span></strong><br/></span></p><p style=\"margin-top: 5px; white-space: normal;\"><span style=\"color: #444444; font-size: 14px;\">A：您可在“我的”-“分销中心”-“分销业绩”-“累计收益”页面查看。</span></p><p style=\"white-space: normal;\"><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444;\"></span></strong></span></p><hr style=\"white-space: normal;\"/><p style=\"white-space: normal;\"><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444;\">Q</span></strong><strong><span style=\"color: #444444;\">：为什么推广了但是没有收益？</span></strong><br/></span></p><p style=\"margin-top: 5px; white-space: normal;\"><span style=\"color: #444444; font-size: 14px;\">A：以下几种情况会导致没有收益：1.推广的商品不参与分佣；2.没有从“推广商品”页面进行分享，而是从其他页面进行的分享；3.订单未完成，未到达分佣时间；4.商品发生退款。</span></p><p style=\"margin-top: 5px; white-space: normal;\"><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444;\"></span></strong></span></p><hr style=\"white-space: normal;\"/><p style=\"white-space: normal;\"><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444;\">Q</span></strong><strong><span style=\"color: #444444;\">：怎样发展客户？</span></strong><br/></span></p><p style=\"margin-top: 5px; white-space: normal;\"><span style=\"color: #444444; font-size: 14px;\">A：通过推广店铺或推广商品，可以发展自己的客户。</span></p>"
}
