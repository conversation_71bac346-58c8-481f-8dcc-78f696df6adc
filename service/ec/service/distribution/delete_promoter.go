package distribution

import (
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/validators"
	promoter_model "mairpc/service/ec/model/distribution"
	"mairpc/service/share/util"

	"mairpc/proto/common/request"
	"mairpc/proto/common/response"

	"golang.org/x/net/context"
)

func (DistributionService) DeletePromoter(ctx context.Context, req *request.StringIdRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       bson.ObjectIdHex(req.Id),
		"isDeleted": false,
	}
	var promoter *promoter_model.Promoter
	promoter, err := promoter_model.CPromoter.GetByDefaultCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	if promoter != nil && promoter.Status != promoter_model.PROMOTER_STATUS_UNAPPROVED {
		return nil, errors.NewInvalidArgumentError("id")
	}
	err = promoter_model.CPromoter.Delete(ctx)
	if err != nil {
		return nil, err
	}
	return &response.EmptyResponse{}, nil
}
