package distribution

import (
	"mairpc/core/extension"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	pb_account "mairpc/proto/account"
	"mairpc/proto/common/request"
	"mairpc/proto/ec/distribution"
	"mairpc/proto/ec/profitsharing"
	"mairpc/service/ec/client"
	distribution_model "mairpc/service/ec/model/distribution"
	ec_distribution "mairpc/service/ec/model/distribution"
	order_model "mairpc/service/ec/model/order"
	ec_profit "mairpc/service/ec/model/profitsharing"
	store_model "mairpc/service/ec/model/store"
	"mairpc/service/share/util"
	"reflect"
	"strings"

	"mairpc/core/extension/bson"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

const (
	BILL_SOURCE_STAFF_DISTRIBUTION  = "staff_distribution"  // 分账来源为导购分销
	BIll_SOURCE_MEMBER_DISTRIBUTION = "member_distribution" // 分账来源为大众分销

	DISTRIBUTION_RECORD_STATUS_PENDING      = "pending"
	DISTRIBUTION_RECORD_STATUS_SUCCESS      = "success"
	DISTRIBUTION_RECORD_STATUS_FAILED       = "failed"
	DISTRIBUTION_RECORD_STATUS_NOCOMMISSION = "noCommission"
)

func (DistributionService) ListDistributionRecords(ctx context.Context, req *distribution.ListDistributionRecordsRequest) (*distribution.ListDistributionRecordsResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	// 没有设置所属部门的用户 distributorIds 是随意生产的，没有对应的 organizationDepartment
	if len(req.DistributorIds) == 1 {
		department, err := store_model.COrganizationDepartment.GetByDistributorId(ctx, util.ToMongoId(req.DistributorIds[0]))
		if err != nil && err != bson.ErrNotFound {
			return nil, err
		}
		if !department.Id.Valid() {
			return &distribution.ListDistributionRecordsResponse{}, nil
		}
	}

	page, pageSize := util.ParsePagingCondition(req.ListCondition)
	orderBys := []string{"-createdAt"}
	if req.ListCondition != nil && len(req.ListCondition.OrderBy) != 0 {
		orderBys = req.ListCondition.OrderBy
	}

	dSetting, err := ec_distribution.CDistributionSetting.GetByType(ctx, req.Type)
	if err != nil {
		return nil, err
	}

	// 分销的月结分佣记录是查 transferBill
	if dSetting.PromoterCommission.Cycle == ec_distribution.CYCLE_MONTH {
		for index, orderBy := range orderBys {
			switch orderBy {
			case "-profitAmount":
				orderBys[index] = "-shareAmount"
			case "profitAmount":
				orderBys[index] = "shareAmount"
			}
		}
		condition, err := GetListMonthlyRecordsCondition(ctx, req)
		if err != nil {
			return nil, err
		}
		var transferBills []ec_profit.TransferBill
		total := 0
		if condition != nil {
			if req.WithoutTotal {
				order_model.Common.GetAllByPaginationWithoutCount(ctx, condition, page, pageSize, orderBys, ec_profit.C_TRANSFER_BILL, &transferBills)
			} else {
				total = order_model.Common.GetAllByPagination(ctx, condition, page, pageSize, orderBys, ec_profit.C_TRANSFER_BILL, &transferBills)
			}
		}
		resp, err := formatMonthlyRecordsPromoterName(ctx, transferBills, dSetting)
		if err != nil {
			return nil, err
		}

		return &distribution.ListDistributionRecordsResponse{
			Total: uint64(total),
			Items: resp,
		}, nil
	}

	condition, err := GetListDRecordsCondition(ctx, req)
	if err != nil {
		return nil, err
	}
	var (
		profitOrders []order_model.OrderReceiverProfit
		total        int
	)
	if req.WithoutTotal {
		order_model.Common.GetAllByPaginationWithoutCount(ctx, condition, page, pageSize, orderBys, order_model.C_ORDER_RECEIVER_PROFIT, &profitOrders)
	} else {
		total = order_model.Common.GetAllByPagination(ctx, condition, page, pageSize, orderBys, order_model.C_ORDER_RECEIVER_PROFIT, &profitOrders)
	}

	resp, err := formatPromoterName(ctx, profitOrders)
	if err != nil {
		return nil, err
	}

	return &distribution.ListDistributionRecordsResponse{
		Total: uint64(total),
		Items: resp,
	}, nil
}

func GetListDRecordsCondition(ctx context.Context, req *distribution.ListDistributionRecordsRequest) (bson.M, error) {
	condition := distribution_model.Common.GenDefaultCondition(ctx)
	condition["promoter.type"] = req.Type

	hasSetPromoterId := false
	if len(req.DistributorIds) > 0 {
		searchKey := ""
		if req.SearchType == order_model.DISTRIBUTION_SEARCH_TYPE_PROMOTER {
			searchKey = req.SearchKey
		}
		promoterIds, err := GetPromoterIdsByDistributionIdsAndSearchKey(ctx, req.DistributorIds, searchKey)
		if err != nil {
			return nil, err
		}
		hasSetPromoterId = true
		condition["promoter.id"] = bson.M{"$in": promoterIds}
	}

	if req.SearchKey != "" {
		switch req.SearchType {
		case order_model.DISTRIBUTION_SEARCH_TYPE_ORDER:
			condition["order.number"] = req.SearchKey
		case order_model.DISTRIBUTION_SEARCH_TYPE_TRANSACTION:
			orderId := bson.NewObjectId()
			order, _ := order_model.COrder.GetByTradeNo(ctx, req.SearchKey)
			if order != nil && order.Id.Valid() {
				orderId = order.Id
			}
			condition["order.id"] = orderId
		case order_model.DISTRIBUTION_SEARCH_TYPE_PROMOTER:
			if hasSetPromoterId {
				break
			}
			promoterIds, err := GetPromoterIdsByDistributionIdsAndSearchKey(ctx, req.DistributorIds, req.SearchKey)
			if err != nil {
				return nil, err
			}
			condition["promoter.id"] = bson.M{"$in": promoterIds}
		default:
			// 同时筛选 number 和 tradeNo
			condition["$or"] = []bson.M{
				{
					"order.number": req.SearchKey,
				}, {
					"order.tradeNo": req.SearchKey,
				},
			}
		}
	}

	if timeSpan := util.ParseDateRange(req.SharedAt); len(timeSpan) > 0 {
		condition["finishedAt"] = timeSpan
	}

	if len(req.Status) > 0 {
		condition["status"] = bson.M{
			"$in": req.Status,
		}
	}

	if len(req.Tags) > 0 {
		condition["order.tags"] = bson.M{
			"$in": req.Tags,
		}
	}

	return condition, nil
}

func GetListMonthlyRecordsCondition(ctx context.Context, req *distribution.ListDistributionRecordsRequest) (bson.M, error) {
	condition := distribution_model.Common.GenDefaultCondition(ctx)
	condition["receiver.accountType"] = ec_profit.RECEIVER_TYPE_OPENID
	condition["receiver.promoterId"] = bson.M{"$exists": true}

	switch req.Type {
	case ec_distribution.DISTRIBUTION_SETTING_TYPE_STAFF:
		condition["source"] = BILL_SOURCE_STAFF_DISTRIBUTION
	case ec_distribution.DISTRIBUTION_SETTING_TYPE_MEMBER:
		condition["source"] = BIll_SOURCE_MEMBER_DISTRIBUTION
	}

	if req.SearchType == order_model.DISTRIBUTION_SEARCH_TYPE_TRANSACTION && req.SearchKey != "" {
		condition["detailIds"] = req.SearchKey
	}
	if timeSpan := util.ParseDateRange(req.SharedAt); len(timeSpan) > 0 {
		condition["finishedAt"] = timeSpan
	}
	if timeSpan := util.ParseDateRange(req.CreatedAt); len(timeSpan) > 0 {
		condition["createdAt"] = timeSpan
	}
	if len(req.DistributorIds) > 0 || (req.SearchKey != "" && req.SearchType == order_model.DISTRIBUTION_SEARCH_TYPE_PROMOTER) {
		var (
			searchKey   string
			err         error
			promoterIds []bson.ObjectId
		)
		if req.SearchType == order_model.DISTRIBUTION_SEARCH_TYPE_PROMOTER {
			searchKey = req.SearchKey
		}
		switch req.Type {
		case "staff":
			promoterIds, err = GetPromoterIdsByDistributionIdsAndSearchKey(ctx, req.DistributorIds, searchKey)
			if err != nil {
				return nil, err
			}
		case "member":
			var (
				promoters      []distribution_model.Promoter
				fuzzySearchKey = util.GetFuzzySearchStrRegex(req.SearchKey)
			)
			selector := distribution_model.Common.GenDefaultCondition(ctx)
			if validators.CValidator.IsPhone(searchKey, nil) {
				selector["$or"] = []bson.M{
					{"name": fuzzySearchKey},
					{"phone": req.SearchKey},
				}
			} else {
				selector["name"] = fuzzySearchKey
			}
			extension.DBRepository.FindAllWithFields(ctx, distribution_model.C_PROMOTER, selector, bson.M{"_id": 1}, nil, 10, &promoters)
			promoterIds = core_util.ExtractArrayFieldV2("Id", bson.NewObjectId(), promoters)
		}
		if len(promoterIds) == 0 {
			return nil, nil
		}
		condition["receiver.promoterId"] = bson.M{"$in": promoterIds}
	}
	if req.SearchType == order_model.DISTRIBUTION_SEARCH_TYPE_ORDER && req.SearchKey != "" {
		order, _ := order_model.COrder.GetByNumber(ctx, req.SearchKey)
		if order == nil || !order.Distribution.TransferBillId.Valid() {
			return nil, nil
		}
		condition["_id"] = order.Distribution.TransferBillId
	}
	if len(req.Status) > 0 {
		condition["status"] = bson.M{
			"$in": req.Status,
		}
	}

	return condition, nil
}

func formatPromoterName(ctx context.Context, profitOrders []order_model.OrderReceiverProfit) ([]*distribution.DistributionRecordDetail, error) {
	resp := []*distribution.DistributionRecordDetail{}
	if len(profitOrders) == 0 {
		return resp, nil
	}

	billIds := core_util.ExtractArrayStringField("TransferBillId", profitOrders)
	core_util.Remove(&billIds, "")
	transferBills, _ := client.ProfitsharingService.ListTransferBills(ctx, &profitsharing.ListTransferBillsRequest{
		BillIds: billIds,
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: uint32(len(billIds)),
		},
	})
	failureMsgMap := map[interface{}]interface{}{}
	if transferBills != nil {
		failureMsgMap = core_util.MakeFieldToFieldMapper("Id", "FailedMessage", transferBills.Items)
	}

	for _, profitOrder := range profitOrders {
		detail := distribution.DistributionRecordDetail{
			OrderNumber:   profitOrder.Order.Number,
			TransactionId: profitOrder.Order.TradeNo,
			ProfitAmount:  profitOrder.ProfitAmount,
			ProfitAt: func() string {
				if profitOrder.FinishedAt.IsZero() {
					return ""
				}
				return profitOrder.FinishedAt.Format(core_util.COMMON_TIME_LAYOUT)
			}(),
			Status:   profitOrder.Status,
			UniqueId: profitOrder.Id.Hex(),
			Order: &distribution.OrderInfo{
				Id:       profitOrder.Order.Id.Hex(),
				Number:   profitOrder.Order.Number,
				MemberId: profitOrder.Order.MemberId.Hex(),
				Tags:     profitOrder.Order.Tags,
			},
			CreatedAt: profitOrder.CreatedAt.Format(core_util.COMMON_TIME_LAYOUT),
		}
		if profitOrder.TransferBillId.Valid() {
			if msg, ok := failureMsgMap[profitOrder.TransferBillId.Hex()]; ok {
				detail.FailureMsg = cast.ToString(msg)
				if strings.Contains(detail.FailureMsg, "Wechat trade error!") {
					detail.FailureMsg = "系统内部错误"
				}
			}
		}
		if profitOrder.Status == order_model.ORDER_RECEIVER_PROFIT_STATUS_NOCOMMISSION ||
			profitOrder.Status == order_model.ORDER_RECEIVER_PROFIT_STATUS_FAILED &&
				detail.FailureMsg == "" {
			detail.FailureMsg = profitOrder.Remarks
		} else if util.StrInArray(profitOrder.Status, &[]string{
			order_model.ORDER_RECEIVER_PROFIT_STATUS_SUCCESS,
			order_model.ORDER_RECEIVER_PROFIT_STATUS_PENDING,
			order_model.ORDER_RECEIVER_PROFIT_STATUS_PROCESSING,
		}) {
			detail.FailureMsg = ""
		}

		if detail.Status == order_model.ORDER_RECEIVER_PROFIT_STATUS_PENDING_PAYMENT {
			detail.Status = order_model.ORDER_RECEIVER_PROFIT_STATUS_PROCESSING
		}

		promoter, _ := distribution_model.CPromoter.GetByIdContainDeleted(ctx, profitOrder.Promoter.Id)
		if promoter != nil {
			detail.Name = promoter.Name
			detail.PromoterPhone = promoter.Phone
			detail.PromoterId = promoter.Id.Hex()
		}

		order, err := order_model.COrder.GetByTradeNo(ctx, profitOrder.Order.TradeNo)
		coupons := []*distribution.Coupon{}
		if err == nil {
			// 旧代销订单预分账单中 Tags 空的，这里重新赋值下
			if len(detail.Order.Tags) == 0 {
				detail.Order.Tags = order.Tags
			}
			for _, orderProduct := range order.Products {
				if core_util.IsEmpty(reflect.ValueOf(orderProduct.DistributionCoupon)) {
					continue
				}
				coupon := &distribution.Coupon{}
				coupon.Id = orderProduct.DistributionCoupon.Id.Hex()
				coupon.Name = orderProduct.DistributionCoupon.Name
				coupons = append(coupons, coupon)
			}
		}
		detail.Coupons = coupons

		resp = append(resp, &detail)
	}

	return resp, nil
}

func formatMonthlyRecordsPromoterName(ctx context.Context, transferBills []ec_profit.TransferBill, dSetting *ec_distribution.DistributionSetting) ([]*distribution.DistributionRecordDetail, error) {
	resp := []*distribution.DistributionRecordDetail{}
	if len(transferBills) == 0 {
		return resp, nil
	}
	userIds := core_util.ToStringArray(core_util.ExtractArrayFieldWithJudge("MarkAsCompletedBy", transferBills, func(transferBill ec_profit.TransferBill) bool {
		return transferBill.MarkAsCompletedBy.Valid()
	}))
	userMap := getUserMap(ctx, userIds)

	for _, transferBill := range transferBills {
		detail := distribution.DistributionRecordDetail{
			TransactionId:  transferBill.DetailId,
			TransactionIds: transferBill.DetailIds,
			ProfitAmount:   transferBill.ShareAmount,
			Tax:            transferBill.Tax,
			Income:         transferBill.Income,
			Status:         transferBill.Status,
			UniqueId:       transferBill.Id.Hex(),
			CreatedAt:      transferBill.CreatedAt.Format(core_util.COMMON_TIME_LAYOUT),
			ProfitAt: func() string {
				if transferBill.FinishedAt.IsZero() {
					return ""
				}
				return transferBill.FinishedAt.Format(core_util.COMMON_TIME_LAYOUT)
			}(),
			FailureMsg: func() string {
				if strings.Contains(transferBill.FailedMessage, "Wechat trade error!") {
					return "系统内部错误"
				}
				return transferBill.FailedMessage
			}(),
			MarkAsCompletedBy: func() string {
				userId := transferBill.MarkAsCompletedBy.Hex()
				if userId == "" {
					return ""
				}
				if user, ok := userMap[userId]; ok {
					return user.Name
				}
				return userId
			}(),
		}

		if util.StrInArray(transferBill.Status, &[]string{
			ec_profit.BILL_STATUS_SUCCESS,
			ec_profit.BILL_STATUS_PENDING,
			ec_profit.BILL_STATUS_PROCESSING,
		}) {
			detail.FailureMsg = ""
		}

		promoter, _ := distribution_model.CPromoter.GetByIdContainDeleted(ctx, transferBill.Receiver.PromoterId)
		if promoter != nil {
			detail.Name = promoter.Name
			detail.PromoterId = promoter.Id.Hex()
			detail.PromoterPhone = promoter.Phone
		}

		order, err := order_model.COrder.GetByTradeNo(ctx, transferBill.OutTradeNo)
		coupons := []*distribution.Coupon{}
		if err == nil {
			for _, orderProduct := range order.Products {
				if !core_util.IsEmpty(reflect.ValueOf(orderProduct.DistributionCoupon)) {
					continue
				}
				coupon := &distribution.Coupon{}
				coupon.Id = orderProduct.DistributionCoupon.Id.Hex()
				coupon.Name = orderProduct.DistributionCoupon.Name
				coupons = append(coupons, coupon)
			}
		}
		detail.Coupons = coupons

		if dSetting.IsOffline {
			detail.OrderCount = order_model.COrder.CountByPromoterIdAndTransferBillId(ctx, transferBill.Id, transferBill.Receiver.PromoterId)
		}

		resp = append(resp, &detail)
	}

	return resp, nil
}

func getUserMap(ctx context.Context, userIds []string) map[string]*pb_account.UserDetailResponse {
	userMap := map[string]*pb_account.UserDetailResponse{}
	userIds = util.StrArrayNonEmpty(userIds)
	userIds = util.StrArrayUnique(userIds)
	if len(userIds) == 0 {
		return userMap
	}
	resp, _ := GetUsers(ctx, &pb_account.UserListRequest{
		Ids:       userIds,
		Unlimited: true,
	})
	if resp != nil {
		for i, u := range resp.Items {
			userMap[u.Id] = resp.Items[i]
		}
	}
	return userMap
}
