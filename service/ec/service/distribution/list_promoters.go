package distribution

import (
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/account"
	"mairpc/proto/ec/distribution"
	pb_member "mairpc/proto/member"
	content_model "mairpc/service/ec/model/content"
	distribution_model "mairpc/service/ec/model/distribution"
	store_model "mairpc/service/ec/model/store"
	"mairpc/service/share/util"

	"github.com/spf13/cast"

	"golang.org/x/net/context"
)

func (DistributionService) ListPromoters(ctx context.Context, req *distribution.ListPromotersRequest) (*distribution.ListPromotersResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	pageIndex, pageSize := util.ParsePagingCondition(req.ListCondition)
	orderBys := []string{"-createdAt"}
	if req.ListCondition != nil && len(req.ListCondition.OrderBy) != 0 {
		orderBys = req.ListCondition.OrderBy
	}
	var parentPromoter *distribution_model.Promoter
	if req.MemberId != "" {
		promoter, err := distribution_model.CPromoter.GetByMemberId(ctx, bson.ObjectIdHex(req.MemberId))
		if err != nil {
			return nil, err
		}
		parentPromoter = promoter
	}
	condition := GetListPromotersCondition(ctx, req, parentPromoter)
	var promoters []*distribution_model.Promoter
	total := distribution_model.Common.GetAllByPagination(ctx, condition, pageIndex, pageSize, orderBys, distribution_model.C_PROMOTER, &promoters)
	if req.ReturnSelfPromoter {
		promoters = append(promoters, parentPromoter)
		total++
	}
	promoterDetails := formatPromoters(ctx, promoters)

	if len(promoters) == 0 {
		return &distribution.ListPromotersResponse{
			Total: uint64(total),
			Items: promoterDetails,
		}, nil
	}

	staffIds := []bson.ObjectId{}
	memberIds := []bson.ObjectId{}
	approverIds := []bson.ObjectId{}
	for _, p := range promoters {
		if p.Type == distribution_model.PROMOTER_TYPE_STAFF && p.StaffId.Hex() != "" {
			staffIds = append(staffIds, p.StaffId)
		}
		if p.MemberId.Valid() {
			memberIds = append(memberIds, p.MemberId)
		}
		if p.ApproverId.Valid() {
			approverIds = append(approverIds, p.ApproverId)
		}
	}

	if len(staffIds) != 0 {
		staffs, _ := store_model.CStaff.GetByIds(ctx, staffIds)
		if len(staffs) > 0 {
			for _, staff := range staffs {
				for _, p := range promoterDetails {
					if staff.Id.Hex() == p.StaffId {
						p.StaffNo = staff.StaffNo
					}
				}
			}
		}
	}

	if len(memberIds) > 0 {
		memberListResp, err := SearchMember(ctx, "", util.MongoIdsToStrs(memberIds))
		if err != nil {
			return nil, err
		}
		weappPage, _ := content_model.CWeappPage.FindOneWithPageInfoType(ctx, content_model.PAGE_TYPE_DISTRIBUTOR_REGISTER)
		propertyIds := []string{}
		if weappPage.Id.Valid() {
			for _, component := range weappPage.PageInfo.Components {
				if component.Type == content_model.WEAPP_PAGE_COMPONENT_TYPE_MEMBER_REGISTER_PROPERTY {
					for _, property := range component.MemberRegisterProperty.Properties {
						if core_util.StrInArray(property.Name, &[]string{"name", "phone"}) {
							continue
						}
						propertyIds = append(propertyIds, property.PropertyId)
					}
				}
			}
		}
		for _, memberDetail := range memberListResp.Members {
			for _, p := range promoterDetails {
				if memberDetail.Id != p.MemberId {
					continue
				}
				for _, property := range memberDetail.Properties {
					if property.Property.Type == "idcard" {
						p.Idcard = property.GetValueString().Value
					}
					if core_util.StrInArray(property.Property.PropertyId, &propertyIds) {
						desensitizeMemberProperty(property)
						p.Properties = append(p.Properties, property)
					}
				}
			}
		}
	}

	if len(approverIds) > 0 {
		userListResp, err := GetUsers(ctx, &account.UserListRequest{Ids: util.MongoIdsToStrs(approverIds)})
		if err != nil {
			return nil, err
		}
		userMap := core_util.MakeMapper("Id", userListResp.Items)
		for _, p := range promoterDetails {
			if user, ok := userMap[p.ApproverId]; ok {
				p.ApproverName = user.(*account.UserDetailResponse).Name
			}
		}
	}

	if core_util.StrInArray("subPromoterCount", &req.ExtraFields) {
		parentIds := core_util.ExtractArrayFieldV2("Id", bson.NewObjectId(), promoters)
		err := formatSubPromoterCount(ctx, parentIds, promoterDetails)
		if err != nil {
			return nil, err
		}
	}

	if core_util.StrInArray("selfBound", &req.ExtraFields) {
		var (
			promoterIds         []bson.ObjectId
			memberIds           []bson.ObjectId
			promoterMemberIdMap = make(map[bson.ObjectId]bson.ObjectId)
			promoterMemberMap   = make(map[bson.ObjectId]struct{})
		)
		for _, promoter := range promoters {
			memberIds = append(memberIds, promoter.MemberId)
			promoterIds = append(promoterIds, promoter.Id)
			promoterMemberIdMap[promoter.Id] = promoter.MemberId
		}
		promoterMembers, _ := distribution_model.CPromoterMember.GetAllByDefaultCondition(ctx, bson.M{
			"promoterId": bson.M{"$in": promoterIds},
			"memberId":   bson.M{"$in": memberIds},
			"status":     distribution_model.PROMOTER_MEMBER_STATUS_BOUND,
		})
		for _, promoterMember := range promoterMembers {
			if promoterMemberIdMap[promoterMember.PromoterId] == promoterMember.MemberId {
				promoterMemberMap[promoterMember.PromoterId] = struct{}{}
			}
		}
		for i, detail := range promoterDetails {
			if _, ok := promoterMemberMap[bson.ObjectIdHex(detail.Id)]; ok {
				promoterDetails[i].SelfBound = true
			}
		}
	}

	return &distribution.ListPromotersResponse{
		Total: uint64(total),
		Items: promoterDetails,
	}, nil
}

func GetListPromotersCondition(ctx context.Context, req *distribution.ListPromotersRequest, parentPromoter *distribution_model.Promoter) bson.M {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"type":      req.Type,
		"isDeleted": false,
	}
	if req.ContainDeleted {
		delete(condition, "isDeleted")
	}
	if len(req.StaffIds) > 0 {
		condition["staffId"] = bson.M{
			"$in": util.ToMongoIds(req.StaffIds),
		}
	}
	if len(req.Status) > 0 {
		condition["status"] = bson.M{
			"$in": req.Status,
		}
	}
	if len(req.ExcludeStatus) > 0 {
		condition["status"] = bson.M{
			"$nin": req.ExcludeStatus,
		}
	}
	if len(req.MemberIds) > 0 {
		condition["memberId"] = bson.M{
			"$in": util.ToMongoIds(req.MemberIds),
		}
	}
	if req.ApproverId != "" {
		condition["approverId"] = bson.ObjectIdHex(req.ApproverId)
	}
	if req.SearchKey != "" {
		fuzzySearchKey := util.GetFuzzySearchStrRegex(req.SearchKey)
		condition["$or"] = []bson.M{
			{"phone": req.SearchKey},
			{"name": fuzzySearchKey},
		}

		sCondition := bson.M{
			"staffNo":   req.SearchKey,
			"isDeleted": false,
		}
		staff, _ := store_model.CStaff.GetOneByCondition(ctx, sCondition)
		if staff.Id.Hex() != "" {
			condition["$or"] = []bson.M{
				{"phone": req.SearchKey},
				{"name": fuzzySearchKey},
				{"staffId": staff.Id},
			}
		}
	}
	if req.Frozen != nil {
		condition["frozen"] = req.Frozen.Value
	}

	if req.IsNewUserDisallowed != nil {
		condition["isNewUserDisallowed"] = req.IsNewUserDisallowed.Value
	}

	if req.IsSigned != nil {
		if req.IsSigned.Value {
			condition["signStatus"] = distribution_model.SIGN_STATUS_SUCCESS
		} else {
			condition["signStatus"] = bson.M{
				"$ne": distribution_model.SIGN_STATUS_SUCCESS,
			}
		}
	}

	if amountSpan := util.ParseIntegerRange(req.TotalProfitAmount); len(amountSpan) > 0 {
		condition["totalProfitAmount"] = amountSpan
	}
	if amountSpan := util.ParseIntegerRange(req.TotalAmount); len(amountSpan) > 0 {
		condition["totalAmount"] = amountSpan
	}
	if membersSpan := util.ParseIntegerRange(req.TotalMembers); len(membersSpan) > 0 {
		condition["totalMembers"] = membersSpan
	}
	if timeSpan := util.ParseDateRange(req.CreatedTime); len(timeSpan) > 0 {
		condition["createdAt"] = timeSpan
	}
	if approvedTimeSpan := util.ParseDateRange(req.ApprovedTime); len(approvedTimeSpan) > 0 {
		condition["approvedAt"] = approvedTimeSpan
	}
	if req.MemberBoundStaffId != "" {
		condition["memberBoundStaff.staffId"] = bson.ObjectIdHex(req.MemberBoundStaffId)
	}

	if len(req.StoreIds) > 0 {
		condition["staffId"] = bson.M{
			"$in": getStaffIdsByStoreIds(ctx, util.ToMongoIds(req.StoreIds)),
		}
	}

	if req.IsSubPromoter != nil {
		condition["parentId"] = bson.M{"$exists": req.IsSubPromoter.Value}
	}

	if req.ParentId != "" {
		condition["parentId"] = bson.ObjectIdHex(req.ParentId)
	}

	if parentPromoter != nil {
		condition["parentId"] = parentPromoter.Id
	}

	if req.Area != "" {
		condition["areas"] = util.GetPrefixSearchStrRegex(req.Area)
	}

	if len(req.Ids) > 0 {
		condition["_id"] = bson.M{"$in": util.ToMongoIds(req.Ids)}
	}

	return condition
}

func getStaffIdsByStoreIds(ctx context.Context, storeIds []bson.ObjectId) []bson.ObjectId {
	staffIds := []bson.ObjectId{}

	staffs, err := store_model.CStaff.GetByStoreIds(ctx, storeIds)
	if err != nil || len(staffs) == 0 {
		return staffIds
	}

	for _, s := range staffs {
		if s.Id.Hex() == "" {
			continue
		}
		staffIds = append(staffIds, s.Id)
	}

	return staffIds
}

func desensitizeMemberProperty(p *pb_member.PropertyDetail) {
	if !p.Property.IsDefault {
		return
	}
	switch p.Property.Type {
	case "idcard":
		p.Value = &pb_member.PropertyDetail_ValueString{
			ValueString: &pb_member.PropertyStringValue{
				Value: util.DesensitizeIdcard(p.GetValueString().Value),
			},
		}
	case "phone":
		p.Value = &pb_member.PropertyDetail_ValueString{
			ValueString: &pb_member.PropertyStringValue{
				Value: util.DesensitizeTel(p.GetValueString().Value),
			},
		}
	case "email":
		p.Value = &pb_member.PropertyDetail_ValueString{
			ValueString: &pb_member.PropertyStringValue{
				Value: util.DesensitizeEmailKeepFirstDigit(p.GetValueString().Value),
			},
		}
	}
}

func formatSubPromoterCount(ctx context.Context, parentIds []bson.ObjectId, promoters []*distribution.PromoterDetail) error {
	results, err := distribution_model.CPromoter.CountSubPromoterByParentIds(ctx, parentIds)
	if err != nil {
		return err
	}
	resultMap := core_util.MakeMapper("_id", results)
	for i, promoter := range promoters {
		if result, ok := resultMap[bson.ObjectIdHex(promoter.Id)]; ok {
			promoters[i].SubPromoterCount = cast.ToUint64(result.(bson.M)["count"])
		}
	}
	return nil
}

func formatPromoters(ctx context.Context, promoters []*distribution_model.Promoter) []*distribution.PromoterDetail {
	var (
		promoterDetails   []*distribution.PromoterDetail
		parentPromoterIds = core_util.ToObjectIdArray(core_util.ExtractArrayFieldWithJudge("ParentId", promoters, func(promoter *distribution_model.Promoter) bool {
			return promoter != nil && promoter.ParentId.Valid()
		}))
		promoterMap       = core_util.MakeMapper("Id", promoters)
		parentPromoterMap map[bson.ObjectId]distribution_model.Promoter
	)
	if len(parentPromoterIds) != 0 {
		parentPromoters, _ := distribution_model.CPromoter.GetByIds(ctx, parentPromoterIds)
		parentPromoterMap = core_util.MakeMapperV2("Id", bson.NewObjectId(), parentPromoters)
	}
	statsMap := make(map[bson.ObjectId]*distribution.Statistics)
	// 管理后台调用时要返回含二级分销员的数据
	if core_util.GetUserId(ctx) != "" {
		statsMap = getSubPromoterStatsMap(ctx, promoters)
	}
	dSetting, _ := distribution_model.CDistributionSetting.GetByType(ctx, distribution_model.DISTRIBUTION_SETTING_TYPE_STAFF)
	copier.Instance(nil).RegisterResetDiffField([]copier.DiffFieldPair{
		{
			Origin:  "Id",
			Targets: []string{"Id", "Count", "SubPromoterStatistics", "SubPromoterTotalMembers"},
		},
		{
			Origin:  "ParentId",
			Targets: []string{"ParentId", "ParentName"},
		},
	}).RegisterTransformer(copier.Transformer{
		"Count": func(promoterId bson.ObjectId) *distribution.PromoterCount {
			promoter, ok := promoterMap[promoterId]
			if !ok {
				return &distribution.PromoterCount{
					CountDetail: make(map[string]*distribution.Statistics),
				}
			}
			return formatPromoterCountDetail(ctx, dSetting, promoter.(*distribution_model.Promoter))
		},
		"ParentName": func(promoterId bson.ObjectId) string {
			if parentPromoter, ok := parentPromoterMap[promoterId]; ok {
				return parentPromoter.Name
			}
			return ""
		},
		"SubPromoterStatistics": func(id bson.ObjectId) *distribution.Statistics {
			return statsMap[id]
		},
		"SubPromoterTotalMembers": func(id bson.ObjectId) uint64 {
			if statsMap[id] == nil {
				return 0
			}
			return statsMap[id].Member
		},
	}).From(promoters).CopyTo(&promoterDetails)
	return promoterDetails
}

func getSubPromoterStatsMap(ctx context.Context, promoters []*distribution_model.Promoter) map[bson.ObjectId]*distribution.Statistics {
	var (
		countMap     = make(map[bson.ObjectId]*distribution.Statistics)
		subPromoters []distribution_model.Promoter
	)
	setting, _ := distribution_model.CDistributionSetting.GetByType(ctx, distribution_model.DISTRIBUTION_SETTING_TYPE_MEMBER)
	// 没开通二级分销的租户不查询
	if setting == nil || setting.SubPromoterSetting.AmountSendTo == "" {
		return countMap
	}
	promoterIds := core_util.ToObjectIdArray(core_util.ExtractArrayStringField("Id", promoters))
	condition := distribution_model.Common.GenDefaultCondition(ctx)
	condition["historyParentIds"] = bson.M{"$in": promoterIds}
	extension.DBRepository.FindAllWithFields(ctx, distribution_model.C_PROMOTER, condition, bson.M{
		"_id":            1,
		"parentStats":    1,
		"parentId":       1,
		"totalMembers":   1,
		"bindingMembers": 1,
	}, nil, 0, &subPromoters)
	for _, promoter := range subPromoters {
		if countMap[promoter.ParentId] == nil {
			countMap[promoter.ParentId] = new(distribution.Statistics)
		}
		countMap[promoter.ParentId].Member += uint64(promoter.TotalMembers)
		countMap[promoter.ParentId].BindingMembers += uint64(promoter.BindingMembers)
		for _, stat := range promoter.ParentStats {
			if countMap[stat.PromoterId] == nil {
				countMap[stat.PromoterId] = new(distribution.Statistics)
			}
			countMap[stat.PromoterId].Order += cast.ToUint64(stat.TotalOrders)
			countMap[stat.PromoterId].PromoteOrder += cast.ToUint64(stat.TotalPromoteOrders)
			countMap[stat.PromoterId].TotalAmount += cast.ToUint64(stat.TotalAmount)
			countMap[stat.PromoterId].PromoteAmount += cast.ToUint64(stat.TotalPromoteAmount)
			countMap[stat.PromoterId].DistProductTotalAmount += cast.ToUint64(stat.DistProductTotalAmount)
			countMap[stat.PromoterId].DistProductPromoteAmount += cast.ToUint64(stat.DistProductPromoteAmount)
		}
	}
	return countMap
}
