package wallet

import (
	"fmt"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/validators"
	ec_wallet "mairpc/proto/ec/wallet"
	ec_setting "mairpc/service/ec/model/setting"
	"mairpc/service/ec/model/wallet"
	"mairpc/service/ec/service/wallet/wallets"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

const (
	PREPAID_CARD_DEFRSY_LOCK_KEY        = "prepaid_card_defray:%s:%s:%s"           // prepaid_card_defray:accountId:prepaidCardId:memberId
	REVOKE_PREPAID_CARD_DEFRSY_LOCK_KEY = "revoke_prepaid_card_defray:%s:%s:%s:%s" // revoke_prepaid_card_defray:accountId:prepaidCardId:transactionId:memberId
)

func (WalletService) PrepaidCardDefray(ctx context.Context, req *ec_wallet.PrepaidCardDefrayRequest) (*ec_wallet.PrepaidCardDefrayResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	lockKey := fmt.Sprintf(PREPAID_CARD_DEFRSY_LOCK_KEY, util.GetAccountId(ctx), req.PrepaidCardId, req.MemberId)
	if ok, _ := extension.RedisClient.SetNX(lockKey, "1", 60); !ok {
		return nil, errors.NewTooManyRequestsError("prepaidCardId")
	}
	defer extension.RedisClient.Del(lockKey)
	prepaidCard, err := wallet.CPrepaidCard.FindById(ctx, bson.ObjectIdHex(req.PrepaidCardId))
	if err != nil {
		return nil, err
	}
	if prepaidCard.CardStatus != wallet.STATUS_ACTIVE || prepaidCard.Status != wallet.STATUS_BOUND {
		return nil, errors.NewInvalidArgumentErrorWithMessage("status", "invalid card status")
	}
	setting, err := ec_setting.CSettings.Get(ctx)
	if err != nil {
		return nil, errors.NewNotExistsError("setting")
	}
	if !setting.IsPrepaidCardConfigured() {
		return nil, errors.NewInvalidArgumentError("setting")
	}
	thirdPartyPrepaidCard := wallets.InitThirdPartyPrepaidCard(*setting)
	if thirdPartyPrepaidCard == nil {
		return nil, errors.NewNotExistsError("prepaidCard")
	}
	err = wallet.CPrepaidCard.Deduct(ctx, prepaidCard.Id, req.Amount)
	if err != nil {
		return nil, err
	}
	request := &wallet.ThirdPartyPrepaidCardDefrayRequest{
		CardNo:    prepaidCard.Number,
		CardPwd:   prepaidCard.Password,
		Amount:    uint32(req.Amount),
		TradeNo:   req.OrderNumber,
		StoreCode: req.StoreCode,
	}
	response, err := thirdPartyPrepaidCard.PrepaidCardDefray(ctx, request)
	if err != nil {
		return nil, err
	}
	// 创建 log
	history := wallet.PrepaidCardHistory{
		PrepaidCardId: prepaidCard.Id,
		MemberId:      bson.ObjectIdHex(req.MemberId),
		Number:        prepaidCard.Number,
		Amount:        uint32(req.Amount),
		Type:          wallet.PREPAID_CARD_HISTORY_TYPE_CONSUME,
		Balance:       response.Balance,
		OutTradeNo:    response.OriginTermSeq,
		OutTradeSeq:   response.OriginTermNo,
		TransactionId: response.TransactionId,
		OrderNumber:   req.OrderNumber,
		StoreCode:     req.StoreCode,
		StoreName:     req.StoreName,
	}
	if req.OrderId != "" {
		history.OrderId = bson.ObjectIdHex(req.OrderId)
	}
	err = history.Insert(ctx)
	if err != nil {
		return nil, err
	}
	return &ec_wallet.PrepaidCardDefrayResponse{
		TradeNo:       response.OriginTermSeq,
		OutTradeSeq:   history.OutTradeSeq,
		TransactionId: history.TransactionId,
	}, nil
}
