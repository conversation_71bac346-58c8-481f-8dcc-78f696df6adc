package wallet

import (
	"fmt"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	ec_wallet "mairpc/proto/ec/wallet"
	ec_setting "mairpc/service/ec/model/setting"
	"mairpc/service/ec/model/wallet"
	"mairpc/service/ec/service/wallet/wallets"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

func (WalletService) RevokePrepaidCardDefray(ctx context.Context, req *ec_wallet.RevokePrepaidCardDefrayRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	lockKey := fmt.Sprintf(REVOKE_PREPAID_CARD_DEFRSY_LOCK_KEY, util.GetAccountId(ctx), req.PrepaidCardId, req.TransactionId, req.MemberId)
	if ok, _ := extension.RedisClient.SetNX(lockKey, "1", 60); !ok {
		return nil, errors.NewTooManyRequestsError("prepaidCardId")
	}
	defer extension.RedisClient.Del(lockKey)

	prepaidCard, err := wallet.CPrepaidCard.FindById(ctx, bson.ObjectIdHex(req.PrepaidCardId))
	if err != nil {
		return nil, err
	}
	setting, err := ec_setting.CSettings.Get(ctx)
	if err != nil {
		return nil, errors.NewNotExistsError("setting")
	}
	if !setting.IsPrepaidCardConfigured() {
		return nil, errors.NewInvalidArgumentError("setting")
	}
	err = checkRefundAmount(ctx, req.Amount, req.OrderNumber)
	if err != nil {
		return nil, err
	}
	thirdPartyPrepaidCard := wallets.InitThirdPartyPrepaidCard(*setting)
	if thirdPartyPrepaidCard == nil {
		return nil, errors.NewNotExistsError("prepaidCard")
	}
	request := &wallet.RevokeThirdPartyPrepaidCardDefrayRequest{
		OriginTermSeq:  req.TradeNo,
		Amount:         uint32(req.Amount),
		CardNo:         prepaidCard.Number,
		CardPwd:        prepaidCard.Password,
		TransactionId:  req.TransactionId,
		OrderCreatedAt: req.OrderCreatedAt,
		StoreCode:      req.StoreCode,
	}
	revokeResp, err := thirdPartyPrepaidCard.RevokePrepaidCardDefray(ctx, request)
	if err != nil {
		return nil, err
	}
	err = wallet.CPrepaidCard.Rollback(ctx, prepaidCard.Id, req.Amount)
	if err != nil {
		return nil, err
	}
	// 创建 log
	history := wallet.PrepaidCardHistory{
		PrepaidCardId: prepaidCard.Id,
		MemberId:      bson.ObjectIdHex(req.MemberId),
		Number:        prepaidCard.Number,
		Amount:        uint32(req.Amount),
		Type:          wallet.PREPAID_CARD_HISTORY_TYPE_REFUND,
		Balance:       revokeResp.Balance,
		OrderNumber:   req.OrderNumber,
		Reason:        req.Reason,
		StoreCode:     req.StoreCode,
	}
	if req.OrderId != "" {
		history.OrderId = bson.ObjectIdHex(req.OrderId)
	}
	err = history.Insert(ctx)
	if err != nil {
		return nil, err
	}
	return &response.EmptyResponse{}, nil
}
