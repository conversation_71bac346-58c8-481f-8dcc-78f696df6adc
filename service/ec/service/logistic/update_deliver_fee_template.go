package logistic

import (
	"fmt"
	"golang.org/x/net/context"
	"mairpc/core/component"
	wechat_trade "mairpc/core/component/wechat/trade"
	"mairpc/core/errors"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/client"
	"mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	pb "mairpc/proto/ec/logistic"
	"mairpc/proto/ec/store"
	model "mairpc/service/ec/model/logistic"
	"mairpc/service/ec/service"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"
)

func (LogisticService) UpdateDeliveryFeeTemplate(ctx context.Context, req *pb.UpdateDeliveryFeeTemplateRequest) (*pb.DeliveryFeeTemplateDetail, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	deliveryFeeTemplate := model.CDeliveryFeeTemplate.GetTemplateByMethod(ctx, req.Method)
	if deliveryFeeTemplate == nil {
		return nil, errors.NewNotExistsError("template")
	}

	originDeliveryFeeTemplate := *deliveryFeeTemplate

	core_util.Clone(req, &deliveryFeeTemplate)

	err := validateDeliveryFeeTemplate(deliveryFeeTemplate)
	if err != nil {
		return nil, err
	}

	if deliveryFeeTemplate.IsFreightInsuranceEnabled && !originDeliveryFeeTemplate.IsFreightInsuranceEnabled {
		resp, err := service.GetChannelByBusinessAndOrigin(ctx, constant.BUSINESS_RETAIL, constant.WEAPP)
		if err != nil || len(resp.Channels) == 0 {
			return nil, errors.NewNotExistsError("channel")
		}
		enabled, _ := wechat_trade.NewWechatTradeClient(resp.Channels[0].ChannelId, false).FreightInsurance.IsServiceEnabled(ctx)
		if !enabled {
			return nil, errors.NewNotEnabledError("freightInsurance")
		}
	}

	err = deliveryFeeTemplate.Update(ctx)
	if err != nil {
		return nil, err
	}

	// 区域运费模板切换为门店独立规则模式，默认按照门店所在区域，将原区域运费模板中的设置分配到各门店的运费规则中
	if originDeliveryFeeTemplate.Method == model.TEMPLATE_METHOD_CITY_EXPRESS && originDeliveryFeeTemplate.DeliveryFeeMode != "store" && req.DeliveryFeeMode == "store" {
		component.GO(ctx, func(ctx context.Context) {
			assignDeliveryRule(ctx, originDeliveryFeeTemplate)
		})
	}

	return formatDeliveryFeeTemplateDetail(*deliveryFeeTemplate), nil
}

func assignDeliveryRule(ctx context.Context, originDeliveryFeeTemplate model.DeliveryFeeTemplate) {
	listCondition := &request.ListCondition{Page: 1, PerPage: 100}
	for {
		resp, _ := client.GetEcStoreServiceClient().ListCityExpressStores(ctx, &store.ListCityExpressStoresRequest{ListCondition: listCondition})
		if resp == nil || len(resp.Items) == 0 {
			break
		}
		for _, item := range resp.Items {
			if item.Location == nil {
				continue
			}
			address := fmt.Sprintf("%s:%s:%s", item.Location.Province, item.Location.City, item.Location.District)
			rule := originDeliveryFeeTemplate.DefaultRule
			for _, r := range originDeliveryFeeTemplate.Rules {
				if util.ValidateAddress(address, r.Areas) {
					rule = r
					break
				}
			}
			freeRule := model.DeliveryFeeTemplateFreeRule{}
			for _, r := range originDeliveryFeeTemplate.FreeRules {
				if util.ValidateAddress(address, r.Areas) {
					freeRule = r
					break
				}
			}
			template := &ec.DeliveryFeeTemplate{
				Type:          originDeliveryFeeTemplate.Type,
				ConditionFree: originDeliveryFeeTemplate.ConditionFree,
				AmountType:    originDeliveryFeeTemplate.AmountType,
			}
			if freeRule.Type == "" {
				template.ConditionFree = false
			} else {
				copier.Instance(nil).From(freeRule).CopyTo(&template.FreeRule)
			}
			copier.Instance(nil).From(rule).CopyTo(&template.Rule)
			_, err := client.GetEcStoreServiceClient().BatchUpdateCityExpressStore(ctx, &store.BatchUpsertCityExpressStoresRequest{
				StoreIds:            []string{item.StoreId},
				DeliveryFeeTemplate: template,
			})
			if err != nil {
				log.Warn(ctx, "Failed to assign delivery rule", log.Fields{
					"storeId": item.StoreId,
					"errMag":  err.Error(),
				})
			}
		}
		listCondition.Page++
	}
}
