package weworkLicense

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	core_component "mairpc/core/component"
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/account"
	pb_account "mairpc/proto/account"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/proto/ec/store"
	wework_license_model "mairpc/service/ec/model/weworkLicense"
	"mairpc/service/ec/service"
	async_cache "mairpc/service/share/async_cache"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"
	"os"
	"strconv"
	"time"

	"github.com/spf13/cast"
)

type ActivateWeworkLicenseResponse struct {
	Count       uint64 `json:"count"`
	FailedCount uint64 `json:"failedCount"`
	FailedUrl   string `json:"failedUrl"`
}

var (
	exportActivateFailedName   = "智慧导购_员工管理_激活账号_失败记录_%s.csv"
	exportActivateFailedResult = "%d个员工账号激活失败"
)

func (WeworkLicenseService) ActivateWeworkLicense(ctx context.Context, req *request.IdListRequest) (*response.AsyncCacheResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	channel, err := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err != nil {
		return nil, err
	}
	chainCorpUserMap := map[string][]string{}
	taskIds := []string{}
	staffMap, _ := getStaffMap(ctx, req.Ids, []string{})
	for _, staff := range staffMap {
		if staff.ChainCorpChannelId != "" {
			chainCorpUserMap[staff.ChainCorpChannelId] = append(chainCorpUserMap[staff.ChainCorpChannelId], staff.StaffNo)
			continue
		}
		chainCorpUserMap[channel.ChannelId] = append(chainCorpUserMap[channel.ChannelId], staff.StaffNo)
	}
	// 使用当前时间戳生成唯一 code
	bytes, _ := json.Marshal(fmt.Sprintf("ActivateWeworkLicense_%s", cast.ToString(time.Now().Unix())))
	code := fmt.Sprintf("%x", md5.Sum(bytes))
	rpcErr := async_cache.CreateAsyncCacheData(ctx, &account.CreateAsyncCacheDataRequest{
		Code: code,
	})
	if rpcErr != nil {
		return nil, errors.NewMaiRPCError(rpcErr.Code, rpcErr.Desc)
	}

	for chainCorpChannelId, userIds := range chainCorpUserMap {
		taskId, err := component.WeConnect.CreateActiveUserTask(ctx, chainCorpChannelId, userIds)
		if err != nil {
			return nil, err
		}
		taskIds = append(taskIds, taskId)
	}
	core_component.GO(ctx, func(ctx context.Context) {
		updateReq := &account.UpdateAsyncCacheDataRequest{
			Code:   code,
			Status: "completed",
		}

		responseStr, err := getActivateWeworkLicenseResponseStr(ctx, channel.ChannelId, taskIds, staffMap)
		if err != nil {
			log.Warn(ctx, "Failed to get active user task result", log.Fields{
				"errorMessage": err.Error(),
			})
			updateReq.Status = "failed"
			updateReq.FailedReason = err.Error()
		}
		updateReq.Data = responseStr
		async_cache.UpdateAsyncCacheData(ctx, updateReq)
	})
	return &response.AsyncCacheResponse{
		Code: code,
	}, nil
}

func getActivateWeworkLicenseResponseStr(ctx context.Context, channelId string, taskIds []string,
	staffMap map[string]*store.StaffDetail) (string, error) {
	url := ""
	users := []component.ActiveUser{}
	failedUsers := []component.ActiveUser{}
	for _, taskId := range taskIds {
		result := &component.ActiveUserTaskResult{}
		for {
			var err error
			result, err = component.WeConnect.GetActiveUserTaskResult(ctx, channelId, taskId)
			if err != nil {
				return "", err
			}
			if len(result.Users) > 0 || result.Status == "FAILED" {
				break
			}
			time.Sleep(1 * time.Second)
		}
		failedResults, err := handleActivateResult(ctx, result, taskId, staffMap)
		if err != nil {
			continue
		}
		failedUsers = append(failedUsers, failedResults...)
		users = append(users, result.Users...)
	}

	if len(failedUsers) > 0 {
		url, err := exportActivateFailedStaff(ctx, failedUsers)
		if err != nil {
			return "", err
		}
		createTransmissionTask(ctx, url, uint64(len(failedUsers)), core_util.GetUserId(ctx), exportActivateFailedName, exportActivateFailedResult)
	}

	response := ActivateWeworkLicenseResponse{
		Count:       uint64(len(users) - len(failedUsers)),
		FailedCount: uint64(len(failedUsers)),
		FailedUrl:   url,
	}
	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	jsonEncoder.Encode(response)
	return bf.String(), nil
}

func handleActivateResult(ctx context.Context, result *component.ActiveUserTaskResult, taskId string,
	staffMap map[string]*store.StaffDetail) ([]component.ActiveUser, error) {
	failedResults := []component.ActiveUser{}
	weworkLicenseLogs := []wework_license_model.WeworkLicenseLog{}
	accountId := util.GetAccountIdAsObjectId(ctx)
	docs := []interface{}{}
	for _, result := range result.Users {
		weworkLicenseLog := wework_license_model.WeworkLicenseLog{
			Type:    wework_license_model.TYPE_ACTIVATION,
			StaffId: util.ToMongoId(staffMap[result.UserId].Id),
			TaskId:  taskId,
		}
		if result.ErrorCode != 0 {
			weworkLicenseLog.Status = wework_license_model.STATUS_FAILED
			weworkLicenseLog.FailedReason = strconv.Itoa(int(result.ErrorCode))
			failedResults = append(failedResults, result)
		} else {
			weworkLicenseLog.Status = wework_license_model.STATUS_SUCCESS
			weworkLicenseLog.EndAt = util.TransIntTimestampToTime(result.ExpireTime)
			doc := getActivatedWeworkLicenses(ctx, util.ToMongoId(staffMap[result.UserId].Id), accountId, result.ActiveTime,
				result.ExpireTime, staffMap[result.UserId].ChainCorpChannelId)
			docs = append(docs, doc...)
		}
		weworkLicenseLogs = append(weworkLicenseLogs, weworkLicenseLog)
	}
	err := wework_license_model.CWeworkLicenseLog.BatchInsert(ctx, weworkLicenseLogs)
	if err != nil {
		return nil, err
	}
	if len(docs) > 0 {
		err = wework_license_model.CWeworkLicense.BatchUpsert(ctx, docs)
		if err != nil {
			return nil, err
		}
	}
	return failedResults, nil
}

func exportActivateFailedStaff(ctx context.Context, failedResults []component.ActiveUser) (string, error) {
	url, err := job_util.ExportFile(
		ctx,
		fmt.Sprintf(exportActivateFailedName, cast.ToString(time.Now().Unix())),
		func(f *os.File) error {
			// 写入表格标题
			f.WriteString(fmt.Sprintf("%s,%s\n", "员工账号", "失败原因"))
			writeCounter := 0
			for _, failedResult := range failedResults {
				f.WriteString(fmt.Sprintf("%s,%s\n", failedResult.UserId, formatFailedReason(strconv.Itoa(int(failedResult.ErrorCode)))))
				writeCounter = writeCounter + 1
				if writeCounter%500 == 0 {
					f.Sync()
				}
			}
			f.Sync()
			return nil
		},
	)
	if err != nil {
		return "", err
	}
	return url, err
}

func createTransmissionTask(ctx context.Context, url string, failedCount uint64, creatorId, name, result string) error {
	return CreateDataTransmissionTask(ctx, &pb_account.CreateDataTransmissionTaskRequest{
		Name:       fmt.Sprintf(name, cast.ToString(time.Now().Unix())),
		Result:     fmt.Sprintf(result, failedCount),
		Type:       "export",
		Status:     "succeed",
		ExecutedAt: time.Now().Format(core_util.RFC3339Mili),
		ExpiredAt:  time.Now().AddDate(0, 0, 7).Format(core_util.RFC3339Mili),
		SourceUrl:  url,
		CreatorId:  creatorId,
	})
}

func getActivatedWeworkLicenses(ctx context.Context, staffId, accountId bson.ObjectId, activeTime, expireTime int64, chainCorpChannelId string) []interface{} {
	docs := []interface{}{}
	selector := bson.M{
		"staffId":   staffId,
		"accountId": accountId,
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"status":             wework_license_model.WEWORK_LICENSE_STATUS_ACTIVATED,
			"chainCorpChannelId": chainCorpChannelId,
			"updatedAt":          time.Now(),
			"startAt":            util.TransIntTimestampToTime(activeTime),
			"endAt":              util.TransIntTimestampToTime(expireTime),
		},
	}
	docs = append(docs, selector, updater)
	return docs
}

func formatFailedReason(errorCode string) string {
	reason := ""
	if errorCode == "701082" {
		reason = "该用户已经激活的情况下，使用新码重新激活的场景，旧码剩余的时长不能超过20天"
	}
	if errorCode == "701004" {
		reason = "用户未激活该类型激活码，不允许续期"
	}
	if errorCode == "701018" {
		reason = "迁移帐号重叠，接收帐号已有相同类型的帐号"
	}
	if errorCode == "701017" {
		reason = "帐号30天内迁移过"
	}
	if errorCode == "701028" {
		reason = "测试企业购买月份超限，最多只能购买一个月"
	}
	if errorCode == "701016" {
		reason = "帐号未激活或者已经过期"
	}
	if errorCode == "701012" {
		reason = "激活码超过绑定有效期"
	}

	return reason
}
