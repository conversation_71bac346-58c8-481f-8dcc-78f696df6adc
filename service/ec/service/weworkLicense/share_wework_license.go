package weworkLicense

import (
	"bytes"
	"crypto/md5"
	"encoding/json"
	"fmt"
	core_component "mairpc/core/component"
	"mairpc/core/errors"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/account"
	"mairpc/proto/common/response"
	wework_license "mairpc/proto/ec/weworkLicense"
	store_model "mairpc/service/ec/model/store"
	"mairpc/service/ec/service"
	async_cache "mairpc/service/share/async_cache"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	job_util "mairpc/service/share/jobs"
	"os"
	"strconv"
	"time"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

var (
	exportShareFailedName   = "智慧导购_员工管理_新增下游激活名额_失败记录_%s.csv"
	exportShareFailedResult = "%d个下游激活名额新增失败"
)

func (WeworkLicenseService) ShareWeworkLicense(ctx context.Context, req *wework_license.ShareWeworkLicenseRequest) (*response.AsyncCacheResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	channel, err := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err != nil {
		return nil, err
	}
	bytes, _ := json.Marshal(fmt.Sprintf("ShareWeworkLicense_%s", cast.ToString(time.Now().Unix())))
	code := fmt.Sprintf("%x", md5.Sum(bytes))
	rpcErr := async_cache.CreateAsyncCacheData(ctx, &account.CreateAsyncCacheDataRequest{
		Code: code,
	})
	if rpcErr != nil {
		return nil, errors.NewMaiRPCError(rpcErr.Code, rpcErr.Desc)
	}
	taskId, err := component.WeConnect.ShareActiveCode(ctx, channel.ChannelId, component.ShareActiveCodeRequest{
		DownstreamChannelId: req.ChannelId,
		Count:               req.Count,
	})
	if err != nil {
		return nil, err
	}
	core_component.GO(ctx, func(ctx context.Context) {
		updateReq := &account.UpdateAsyncCacheDataRequest{
			Code:   code,
			Status: "completed",
		}

		responseStr, err := getShareWeworkLicenseResponseStr(ctx, channel.ChannelId, req.ChannelId, taskId)
		if err != nil {
			log.Warn(ctx, "Failed to get share wework license result", log.Fields{
				"errorMessage": err.Error(),
			})
			updateReq.Status = "failed"
			updateReq.FailedReason = err.Error()
		}
		updateReq.Data = responseStr
		async_cache.UpdateAsyncCacheData(ctx, updateReq)
	})
	return &response.AsyncCacheResponse{
		Code: code,
	}, nil
}

func getShareWeworkLicenseResponseStr(ctx context.Context, channelId, chainCorpChannelId, taskId string) (string, error) {
	url := ""
	result := component.ShareActiveCodeResult{}
	for {
		var err error
		result, err = component.WeConnect.GetShareActiveCodeResult(ctx, channelId, taskId)
		if err != nil {
			return "", err
		}
		if result.Status != "SCHEDULED" {
			break
		}
		time.Sleep(1 * time.Second)
	}
	var licenseCount int64
	failedResults := []component.LicenseLog{}
	for _, licenseLog := range result.LicenseLogs {
		if licenseLog.Status == "FAILED" {
			failedResults = append(failedResults, licenseLog)
		}
		if licenseLog.Status == "ASSIGNED" {
			licenseCount++
		}
	}
	if licenseCount > 0 {
		store_model.CChainCorp.UpdateLicenseCount(ctx, chainCorpChannelId, licenseCount)
	}
	if len(failedResults) > 0 {
		url, err := exportShareFailedReason(ctx, failedResults)
		if err != nil {
			return "", err
		}
		createTransmissionTask(ctx, url, uint64(len(failedResults)), core_util.GetUserId(ctx), exportActivateFailedName, exportActivateFailedResult)
	}

	response := ActivateWeworkLicenseResponse{
		Count:       uint64(licenseCount),
		FailedCount: uint64(len(failedResults)),
		FailedUrl:   url,
	}
	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	jsonEncoder.Encode(response)
	return bf.String(), nil
}

func exportShareFailedReason(ctx context.Context, failedResults []component.LicenseLog) (string, error) {
	url, err := job_util.ExportFile(
		ctx,
		fmt.Sprintf(exportShareFailedName, cast.ToString(time.Now().Unix())),
		func(f *os.File) error {
			f.WriteString(fmt.Sprintf("%s\n", "失败原因"))
			writeCounter := 0
			for _, failedResult := range failedResults {
				f.WriteString(fmt.Sprintf("%s\n", strconv.Itoa(int(failedResult.ErrorCode))))
				writeCounter = writeCounter + 1
				if writeCounter%500 == 0 {
					f.Sync()
				}
			}
			f.Sync()
			return nil
		},
	)
	if err != nil {
		return "", err
	}
	return url, err
}
