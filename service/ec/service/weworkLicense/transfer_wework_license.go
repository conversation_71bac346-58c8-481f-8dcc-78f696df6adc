package weworkLicense

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	core_component "mairpc/core/component"
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/account"
	"mairpc/proto/common/response"
	"mairpc/proto/ec/store"
	wework_license "mairpc/proto/ec/weworkLicense"
	wework_license_model "mairpc/service/ec/model/weworkLicense"
	"mairpc/service/ec/service"
	async_cache "mairpc/service/share/async_cache"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"
	"os"
	"strconv"
	"time"

	"github.com/spf13/cast"
)

var (
	exportTransferFailedName   = "智慧导购_员工管理_继承账号_失败记录_%s.csv"
	exportTransferFailedResult = "%d个员工账号继承失败"
)

type TransferWeworkLicenseResponse struct {
	Count       uint64 `json:"count"`
	FailedCount uint64 `json:"failedCount"`
	FailedUrl   string `json:"failedUrl"`
}

func (WeworkLicenseService) TransferWeworkLicense(ctx context.Context,
	req *wework_license.TransferWeworkLicenseRequest) (*response.AsyncCacheResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	staffIds := append(req.StaffIds, req.TransferStaffIds...)
	staffNoMap, err := getStaffMap(ctx, staffIds, []string{})
	if err != nil {
		return nil, err
	}
	staffMap := map[string]string{}
	for _, staff := range staffNoMap {
		staffMap[staff.Id] = staff.StaffNo
	}
	channel, err := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err != nil {
		return nil, err
	}
	if req.ChainCorpChannelId != "" {
		channel.ChannelId = req.ChainCorpChannelId
	}
	// 使用当前时间戳生成唯一 code
	bytes, _ := json.Marshal(fmt.Sprintf("TransferWeworkLicense_%s", cast.ToString(time.Now().Unix())))
	code := fmt.Sprintf("%x", md5.Sum(bytes))
	rpcErr := async_cache.CreateAsyncCacheData(ctx, &account.CreateAsyncCacheDataRequest{
		Code: code,
	})
	if rpcErr != nil {
		return nil, errors.NewMaiRPCError(rpcErr.Code, rpcErr.Desc)
	}
	core_component.GO(ctx, func(ctx context.Context) {
		updateReq := &account.UpdateAsyncCacheDataRequest{
			Code:   code,
			Status: "completed",
		}
		responseStr, err := getTransferWeworkLicenseResponseStr(ctx, channel.ChannelId, staffNoMap, req, staffMap)
		if err != nil {
			log.Warn(ctx, "Failed to transfer user", log.Fields{
				"errorMessage": err.Error(),
			})
			updateReq.Status = "failed"
			updateReq.FailedReason = err.Error()
		}
		updateReq.Data = responseStr
		async_cache.UpdateAsyncCacheData(ctx, updateReq)
	})
	return &response.AsyncCacheResponse{
		Code: code,
	}, nil
}

func getTransferWeworkLicenseResponseStr(ctx context.Context, channelId string, staffNoMap map[string]*store.StaffDetail,
	req *wework_license.TransferWeworkLicenseRequest, staffMap map[string]string) (string, error) {
	requests := []component.TransferUsersRequest{}
	limit := len(req.StaffIds)
	if len(req.TransferStaffIds) < len(req.StaffIds) {
		limit = len(req.TransferStaffIds)
	}
	for i, staffId := range req.StaffIds {
		if i == limit {
			break
		}
		if _, ok := staffMap[staffId]; !ok {
			continue
		}
		request := component.TransferUsersRequest{
			UserId:         staffMap[staffId],
			TransferUserId: staffMap[req.TransferStaffIds[i]],
		}
		requests = append(requests, request)
	}

	results, err := component.WeConnect.TransferUsers(ctx, channelId, requests)
	if err != nil {
		return "", err
	}
	failedResults := handleTransferUser(ctx, channelId, results, staffNoMap)
	url := ""
	if len(failedResults) > 0 {
		url, err = exportTransferFailedStaff(ctx, failedResults)
		if err != nil {
			return "", err
		}
		createTransmissionTask(ctx, url, uint64(len(failedResults)), core_util.GetUserId(ctx), exportTransferFailedName, exportTransferFailedResult)
	}
	response := TransferWeworkLicenseResponse{
		Count:       uint64(len(results) - len(failedResults)),
		FailedCount: uint64(len(failedResults)),
		FailedUrl:   url,
	}
	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	jsonEncoder.Encode(response)
	return bf.String(), nil
}

func handleTransferUser(ctx context.Context, channelId string, results []component.TransferUsersResponse, staffNoMap map[string]*store.StaffDetail) []component.TransferUsersResponse {
	weworkLicenseLogs := []wework_license_model.WeworkLicenseLog{}
	failedResults := []component.TransferUsersResponse{}
	accountId := util.GetAccountIdAsObjectId(ctx)
	for _, result := range results {
		weworkLicenseLog := wework_license_model.WeworkLicenseLog{
			StaffId:         util.ToMongoId(staffNoMap[result.UserId].Id),
			TransferStaffId: util.ToMongoId(staffNoMap[result.TransferUserId].Id),
			Type:            wework_license_model.TYPE_TRANSFER,
		}
		if result.ErrorCode != 0 {
			weworkLicenseLog.Status = wework_license_model.STATUS_FAILED
			weworkLicenseLog.FailedReason = strconv.Itoa(int(result.ErrorCode))
			failedResults = append(failedResults, result)
			weworkLicenseLogs = append(weworkLicenseLogs, weworkLicenseLog)
			continue
		}
		detail, err := component.WeConnect.GetActiveUserDetail(ctx, channelId, result.TransferUserId)
		if err != nil {
			log.Warn(ctx, "Get active user detail failed", log.Fields{
				"err":     err.Error(),
				"staffId": result.TransferUserId,
			})
			continue
		}
		weworkLicenseLog.Status = wework_license_model.STATUS_SUCCESS
		weworkLicenseLog.EndAt = util.TransIntTimestampToTime(detail.ExpireTime)
		_, err = wework_license_model.CWeworkLicense.GetByStaffId(ctx, weworkLicenseLog.TransferStaffId)
		// 转移给未激活的导购
		if err == bson.ErrNotFound {
			doc := getActivatedWeworkLicenses(ctx, weworkLicenseLog.TransferStaffId, accountId, detail.ActiveTime,
				detail.ExpireTime, staffNoMap[result.TransferUserId].ChainCorpChannelId)
			wework_license_model.CWeworkLicense.BatchUpsert(ctx, doc)
		} else {
			// 转移给已激活过的导购，修改有效期
			updateWeworkLicenseStatus(ctx, weworkLicenseLog.TransferStaffId, weworkLicenseLog.EndAt)
		}
		wework_license_model.CWeworkLicense.UpdateStatus(ctx, weworkLicenseLog.StaffId, wework_license_model.WEWORK_LICENSE_STATUS_INACTIVATED)
		weworkLicenseLogs = append(weworkLicenseLogs, weworkLicenseLog)
	}
	wework_license_model.CWeworkLicenseLog.BatchInsert(ctx, weworkLicenseLogs)
	return failedResults
}

func exportTransferFailedStaff(ctx context.Context, failedResults []component.TransferUsersResponse) (string, error) {
	url, err := job_util.ExportFile(
		ctx,
		fmt.Sprintf(exportTransferFailedName, cast.ToString(time.Now().Unix())),
		func(f *os.File) error {
			// 写入表格标题
			f.WriteString(fmt.Sprintf("%s,%s,%s\n", "员工账号", "接收员工账号", "失败原因"))
			writeCounter := 0
			for _, failedResult := range failedResults {
				f.WriteString(fmt.Sprintf("%s,%s,%s\n",
					failedResult.UserId,
					failedResult.TransferUserId,
					formatFailedReason(strconv.Itoa(int(failedResult.ErrorCode)))))
				writeCounter = writeCounter + 1
				if writeCounter%500 == 0 {
					f.Sync()
				}
			}
			f.Sync()
			return nil
		},
	)
	if err != nil {
		return "", err
	}
	return url, err
}
