package profitsharing

import (
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/proto/common/response"
	pb_distribution "mairpc/proto/ec/distribution"
	"mairpc/proto/ec/profitsharing"
	"mairpc/proto/ec/store"
	ec_client "mairpc/service/ec/client"
	ec_distribution "mairpc/service/ec/model/distribution"
	ec_order "mairpc/service/ec/model/order"
	ec_profitsharing "mairpc/service/ec/model/profitsharing"
	"mairpc/service/share/util"
	"time"

	"golang.org/x/net/context"
)

func (ProfitsharingService) CreateShareBillForOfflinePromoterMonthly(ctx context.Context, req *profitsharing.CreateShareBillForOfflinePromoterMonthlyRequest) (*response.EmptyResponse, error) {
	promoter, err := ec_client.DistributionService.GetPromoter(ctx, &pb_distribution.GetPromoterRequest{PromoterId: req.PromoterId})
	if err != nil {
		return nil, err
	}

	var (
		debug = true
	)

	if req.Debug != nil && !req.Debug.Value {
		debug = false
	}

	// 为大众分销创建 transferBill 时，需要判断是否已存在导购分销员
	if promoter.Type == ec_distribution.PROMOTER_TYPE_MEMBER && promoter.Phone != "" {
		staffDistSetting, err := ec_client.DistributionService.GetDistributionSetting(ctx, &pb_distribution.GetDistributionSettingRequest{
			SettingType: "staff",
		})
		if err != nil {
			return nil, err
		}
		if staffDistSetting.Enabled {
			staff, _ := ec_client.StoreService.GetStaff(ctx, &store.StaffDetailRequest{
				Phone: promoter.Phone,
			})
			// 如果导购分销开启且手机号对应的导购已存在，并且仍在职，则不进行大众分销
			if staff != nil && staff.Status == 1 {
				return nil, errors.NewInvalidArgumentErrorWithMessage("phone", "Staff promoter already exists")
			}
		}
	}

	receiver, err := ec_profitsharing.CProfitSharingReceiver.GetByReceiverAccount(ctx, promoter.OpenId)
	if err != nil {
		return nil, err
	}

	dSetting, err := ec_distribution.CDistributionSetting.GetByType(ctx, promoter.Type)
	if err != nil {
		return nil, err
	}

	var (
		transferBillId   bson.ObjectId
		promoterId       bson.ObjectId
		totalAmount, tax uint64
		orderCount       int
	)

	switch receiver.Type {
	case ec_profitsharing.RECEIVER_TYPE_OPENID:
		// 分销月结
		if receiver.ProfitType != "distribution" {
			return &response.EmptyResponse{}, nil
		}

		transferChannel := ec_order.TRANSFER_CHANNEL_WECHAT_BALANCE

		var (
			from, to time.Time
			ok       bool
		)
		from, _ = util.TransStrToTime(req.OfflineAt)
		for {
			if ok, from, to = getNextOrderRange(from); ok {
				transferBillId, totalAmount, tax, orderCount, err = ec_order.COrder.StatisticDistributionAmountByMonth(ctx, receiver.Id.Hex(), promoter.Id, dSetting, from, to, debug)
				if err != nil {
					log.Warn(ctx, "Failed to statistic monthly distribute.", log.Fields{
						"receiverId":   receiver.Id.Hex(),
						"errorMessage": err.Error(),
					})

					return nil, err
				}

				if transferBillId == "" || totalAmount == 0 {
					// 该月无收益
					from = to.Add(time.Second)
					continue
				}

				if !debug {
					err = createBillForMonthShare(ctx, transferBillId, totalAmount, tax, orderCount, transferChannel, receiver, promoterId, ec_order.PROFIT_SHARING_DESCRIPTION_PERSONAL_DISTRIBUTION, nil, nil)
					if err != nil {
						log.Error(ctx, "Failed to create order share bill for monthly share", log.Fields{
							"transferBillId": transferBillId.Hex(),
							"receiverId":     receiver.Id.Hex(),
							"totalAmount":    totalAmount,
							"errorMessage":   err.Error(),
						})
						return nil, err
					}
				}

				from = to.Add(time.Second)
			} else {
				break
			}
		}
	}

	return &response.EmptyResponse{}, nil
}

func getNextOrderRange(startTime time.Time) (bool, time.Time, time.Time) {
	if startTime.Month() == time.Now().Month() || startTime.Add(time.Second).Month() == time.Now().Month() {
		return false, time.Time{}, time.Time{}
	}

	if startTime.Month()+1 == time.Now().Month() {
		if time.Now().Day() <= 16 {
			return false, time.Time{}, time.Time{}
		}
	}

	_, endTime := util.GetLastMonthTimeSpan(startTime.AddDate(0, 1, 0))
	return true, startTime, endTime
}
