package profitsharing

import (
	"fmt"
	ec_distribution "mairpc/service/ec/model/distribution"
	"time"

	"mairpc/core/extension"
	"mairpc/core/log"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/proto/ec/distribution"
	ec_model "mairpc/service/ec/model"
	ec_order "mairpc/service/ec/model/order"
	ec_profitsharing "mairpc/service/ec/model/profitsharing"
	ec_retailer "mairpc/service/ec/model/retailer"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

const (
	LOCK_KEY_PROFITSHARING_DAILY      = "ec:profitsharing_daily:%s"
	LOCK_LIFETIME_PROFITSHARING_DAILY = 300 // 5 min
)

func (ProfitsharingService) CreateShareBillForDailyJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	lockKey := fmt.Sprintf(LOCK_KEY_PROFITSHARING_DAILY, util.GetAccountId(ctx))
	if ok, _ := extension.RedisClient.SetNX(lockKey, "1", LOCK_LIFETIME_PROFITSHARING_DAILY); !ok {
		return &response.EmptyResponse{}, nil
	}
	defer extension.RedisClient.Del(lockKey)
	receiverDailyShare(ctx)

	return &response.EmptyResponse{}, nil
}

func receiverDailyShare(ctx context.Context) error {
	profitIter, err := ec_order.COrderReceiverProfit.IterateForDailyShare(ctx)
	if err != nil {
		return err
	}
	defer profitIter.Close()
	memberDistributionSetting, _ := client.GetEcDistributionServiceClient().GetDistributionSetting(ctx, &distribution.GetDistributionSettingRequest{
		SettingType: "member",
	})

	profit := &ec_order.OrderReceiverProfit{}
	for profitIter.Next(profit) {
		if !profit.ReceiverId.Valid() {
			log.Warn(ctx, "Invalid order receiver profit", log.Fields{
				"profitId": profit.Id,
			})
			return nil
		}

		if (!util.StrInArray("cardOrder", &profit.Order.Tags) || !util.StrInArray("memberPaidCard", &profit.Order.Tags)) && !canCreateTransferBill(ctx, profit) {
			log.Warn(ctx, "Profit can't create transfer bill", log.Fields{
				"profit": profit,
			})
			continue
		}

		receiverSelector := bson.M{
			"_id": profit.ReceiverId,
		}
		receiver, err := ec_profitsharing.CProfitSharingReceiver.GetByDefaultCondition(ctx, receiverSelector)
		if err != nil {
			log.Warn(ctx, "Failed to find profitSharingReceiver", log.Fields{
				"profitId":     profit.Id.Hex(),
				"errorMessage": err.Error(),
			})
			continue
		}
		transferBillId := bson.NewObjectId()
		err = profit.SetProcessing(ctx, transferBillId)
		if err != nil {
			return err
		}
		description := ec_order.PROFIT_SHARING_DESCRIPTION_PERSONAL
		// 日结分销需要将状态同步到 order.distribution.profitSharingStatus
		if (!util.StrInArray("cardOrder", &profit.Order.Tags) || !util.StrInArray("memberPaidCard", &profit.Order.Tags)) && profit.ProfitType == ec_order.PROFIT_TYPE_DISTRIBUTION {
			ec_order.COrder.UpdateDistributionStatus(ctx, profit.Order.Id.Hex(), ec_order.ORDER_RECEIVER_PROFIT_STATUS_PROCESSING, "", nil, time.Time{})
			description = ec_order.PROFIT_SHARING_DESCRIPTION_PERSONAL_DISTRIBUTION
		}

		err = createBillForDailyShare(ctx, transferBillId, *receiver, profit, description, memberDistributionSetting)
		if err != nil {
			log.Warn(ctx, "Failed to create order share bill for daily share", log.Fields{
				"transferBillId": transferBillId.Hex(),
				"receiverId":     receiver.Id.Hex(),
				"totalAmount":    profit.ProfitAmount,
				"errorMessage":   err.Error(),
			})
		}
	}
	return nil
}

func createBillForDailyShare(ctx context.Context, billId bson.ObjectId, receiver ec_profitsharing.ProfitSharingReceiver, profit *ec_order.OrderReceiverProfit, description string, memberDistributionSetting *distribution.GetDistributionSettingResponse) error {
	briefReceiver := ec_profitsharing.BriefReceiver{
		Id:              receiver.Id,
		TransferChannel: profit.TransferChannel,
		AppId:           receiver.AppId,
		ChannelId:       receiver.ChannelId,
		Name:            receiver.Name,
		Account:         receiver.Account,
		AccountType:     receiver.Type,
	}

	// 开启了个税代扣，并且需要分账，将每个大众分销分账接收方的 account 改成对应的商户号
	if isDeductTax(memberDistributionSetting) && memberDistributionSetting.PromoterCommission.TaxSetting.DivideType == 0 {
		briefReceiver.Account = memberDistributionSetting.PromoterCommission.TaxSetting.MerchantNo
		briefReceiver.Name = memberDistributionSetting.PromoterCommission.TaxSetting.MerchantName
		briefReceiver.AccountType = ec_order.RECEIVER_TYPE_MERCHANT_ID
	}

	bill := ec_profitsharing.TransferBill{
		Id:                   billId,
		AccountId:            receiver.AccountId,
		OutTradeNo:           profit.Id.Hex(),
		OrderTradeNo:         profit.Order.TradeNo,
		DetailId:             profit.Order.TradeNo,
		DetailIds:            []string{profit.Order.TradeNo},
		SingleOrderProfitCap: profit.SingleOrderProfitCap,
		Receiver:             briefReceiver,
		Description:          description,
		ShareAmount:          profit.ProfitAmount,
		Income:               profit.ProfitAmount,
		Status:               ec_profitsharing.BILL_STATUS_PENDING,
		OrderCount:           1, // 日结固定关联一个订单
	}

	if isDeductTax(memberDistributionSetting) && memberDistributionSetting.PromoterCommission.TaxSetting.TaxPayer == 0 {
		// 个人承担佣金时，需要扣除服务费，比例默认是 7%。假设税前金额是 100，到手金额是 X，那么 X+0.07X=100，因此 代付金额=佣金金额/1.07，小数点后舍去
		prop := util.DivideFloatWithRound(float64(memberDistributionSetting.PromoterCommission.TaxSetting.ServiceFeeProportion), 100, 2) // 0.07
		amountWithFen := util.DivideFloatWithRoundFloor(float64(bill.ShareAmount), (1 + prop), 0)                                        // amount / (1 + prop) 单位是分
		// ShareAmount 很小时，可能会出现计算结果为 0 的情况，此时需要使用实际的 ShareAmount 作为 Income
		if amountWithFen == 0 {
			amountWithFen = float64(bill.ShareAmount)
		}
		bill.Income = cast.ToUint64(amountWithFen)
		bill.Tax = bill.ShareAmount - bill.Income
	}

	if profit.Promoter.Type == ec_distribution.PROMOTER_TYPE_STAFF {
		bill.Source = ec_profitsharing.BILL_SOURCE_STAFF_DISTRIBUTION
	} else if profit.Promoter.Type == ec_distribution.PROMOTER_TYPE_MEMBER {
		bill.Source = ec_profitsharing.BIll_SOURCE_MEMBER_DISTRIBUTION
	}

	if profit.TransferChannel != ec_order.TRANSFER_CHANNEL_YEEPAY && receiver.Type == ec_order.RECEIVER_TYPE_MERCHANT_ID {
		bill.Description = ec_order.PROFIT_SHARING_DESCRIPTION_MERCHANT
		// 商户类型的接收方只能使用分账，不能使用转账
		bill.Receiver.TransferChannel = ec_order.TRANSFER_CHANNEL_WECHAT_PROFITSHARING
	}
	if receiver.BankCode != "" {
		bill.Receiver.BankCode = receiver.BankCode
	}
	if profit.Promoter.Id.Hex() != "" {
		bill.Receiver.PromoterId = profit.Promoter.Id
	}
	if profit.ProfitType == ec_order.PROFIT_TYPE_PROFITSHARING {
		bill.Source = ec_profitsharing.BILL_SOURCE_PROFITSHARING
	}
	if profit.TransferChannel == ec_order.TRANSFER_CHANNEL_YEEPAY {
		bill.DetailId = ""
		bill.DetailIds = []string{}
	}

	if !needDivide(profit, memberDistributionSetting) {
		bill.Status = ec_profitsharing.BILL_STATUS_SUCCESS
	}

	handleProfitAmount(ctx, profit, bill.ShareAmount)
	return bill.Create(ctx)
}

func handleProfitAmount(ctx context.Context, profit *ec_order.OrderReceiverProfit, amount uint64) {
	if !profit.Promoter.Id.Valid() {
		return
	}
	promoter, err := ec_distribution.CPromoter.GetById(ctx, profit.Promoter.Id)
	if err != nil {
		log.Error(ctx, "Failed to get promoter in update ProfitSharing status", log.Fields{
			"errorMessage": err.Error(),
			"orderId":      profit.Order.Id.Hex(),
			"promoterId":   profit.Promoter.Id,
		})

		return
	}

	err = promoter.IncProfitAmount(ctx, 0, -int64(amount))
	if err != nil {
		log.Error(ctx, "Failed to update promoter wait profit amount", log.Fields{
			"errorMessage":     err.Error(),
			"orderId":          profit.Order.Id.Hex(),
			"promoterId":       profit.Promoter.Id,
			"waitProfitAmount": -amount,
		})
	}

	return
}

func canCreateTransferBill(ctx context.Context, orderReceiverProfit *ec_order.OrderReceiverProfit) bool {
	if !ec_model.IsChainRetail(ctx) && !ec_model.IsMaimengRetail(ctx) {
		return true
	}
	orderId := orderReceiverProfit.Order.Id
	// 经销订单完成后立即分账，只有在订单完成才会创建预分账表，所以无需判断订单状态
	ecOrder, _ := ec_order.COrder.GetById(ctx, orderId)
	if ecOrder.IsDistributionOrder() {
		return true
	}
	retailerOrder, _ := ec_retailer.CRetailerOrder.GetBySubOrderId(ctx, orderId)

	// 是否是品牌货款分账接受方，品牌货款支付后立即分账，无需判断子单状态
	isBrandOrderReceiverProfit := false
	receiver := ec_profitsharing.CProfitSharingReceiver.GetById(ctx, orderReceiverProfit.ReceiverId)
	if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_BRAND {
		isBrandOrderReceiverProfit = true
	}
	if isBrandOrderReceiverProfit {
		for _, subOrder := range retailerOrder.SubOrders {
			if subOrder.BrandDivideType == ec_retailer.BRAND_DIVIDE_TYPE_AFTER_PAID && subOrder.Id.Hex() == orderId.Hex() {
				return true
			}
		}
	}

	// 连锁零售商和脉盟小店代销订单，需要所有关联的代销子单都可以分账后才能分账
	subOrderIds := []bson.ObjectId{}
	for _, subOrder := range retailerOrder.SubOrders {
		// 忽略经销子单
		if subOrder.IsDistributionOrder() {
			continue
		}
		// 子单全部退款了，忽略子单
		if subOrder.IsRefunded(ctx) {
			continue
		}
		// 有子单未完成，所有单都不能分账
		if subOrder.Status != ec_retailer.ORDER_COMPLETED_STATUS {
			return false
		}
		subOrderIds = append(subOrderIds, subOrder.Id)
	}
	profits, _ := ec_order.COrderReceiverProfit.GetByOrderIds(ctx, subOrderIds)
	count := 0
	now := time.Now()
	for _, profit := range profits {
		if profit.ShareAt.Before(now) {
			count++
		}
	}
	if len(profits) == count {
		return true
	}
	return false
}

func needDivide(profit *ec_order.OrderReceiverProfit, memberDistributionSetting *distribution.GetDistributionSettingResponse) bool {
	// 开启了代付，并且不需要分账
	if profit.Promoter.Type == "member" && isDeductTax(memberDistributionSetting) &&
		memberDistributionSetting.PromoterCommission.TaxSetting.DivideType == 1 &&
		util.StrInArray("yeepay", &profit.Order.Tags) {
		return false
	}
	return true
}

// 是否开启了代扣个税
func isDeductTax(memberDistributionSetting *distribution.GetDistributionSettingResponse) bool {
	if memberDistributionSetting != nil &&
		memberDistributionSetting.Enabled &&
		memberDistributionSetting.PromoterCommission.TaxSetting.DeductTax {
		return true
	}
	return false
}
