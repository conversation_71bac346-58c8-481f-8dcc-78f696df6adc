package profitsharing

import (
	"errors"
	"fmt"
	"mairpc/core/component"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	pb_account "mairpc/proto/account"
	pb_client "mairpc/proto/client"
	"strings"
	"time"

	"mairpc/core/log"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/proto/ec/setting"
	"mairpc/service/ec/client"
	"mairpc/service/ec/model/profitsharing"
	ec_profitsharing "mairpc/service/ec/model/profitsharing"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

const (
	LOCK_KEY_TRANSFER            = "ec:transfer:%s"
	LOCK_LIFETIME_TRANSFER       = 3 * 60 * 60 // 3h
	REFRESH_WHEN_CHECKING_RESULT = "refreshWhenCheckingResult"
	COUNT_KEY_RECEIVER_BILLS     = "%s:ec.profitsharing:execReceiverTransferCount:%s:%s"
)

func (ProfitsharingService) ExecTransferJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	setting, err := client.SettingService.GetSettings(ctx, &setting.GetSettingsRequest{})
	if err != nil {
		return &response.EmptyResponse{}, nil
	}
	exector := &TransferExector{
		Rule: ec_profitsharing.TransferRule{
			IsV2Transfer: setting.WechatTransferMode == "v2",
			IsV3Transfer: setting.WechatTransferMode == "v3",
		},
	}

	accountId := util.GetAccountId(ctx)
	// 当前只给 oatly 租户处理单次转账上限和单日转账上限
	// 后续可以统一配置到 ec.settings 表里，所有租户根据需求配置
	switch accountId {
	case "62b979befd74481d306fffc3":
		exector.Rule.SingleGap = 40000
		exector.Rule.DailyGap = 20000 * 100 // 2W
	case "5e7873c4c3307000f272c9e2":
		// 为了方便测试，群脉测试3 单笔转账金额不超过 0.4 元，每日累积金额不超过 1.6 元，否则都会拆分
		exector.Rule.SingleGap = 40
		exector.Rule.DailyGap = 160
	case "62eb89e682bca228635eed12": // 中和动能拆分需求：超过 500 元拆分为每个子单 300 元
		exector.Rule.SingleGap = 30000
		exector.Rule.DailyGap = 300000 // 当前不确定单日发放上限是多少，先按照每日 10 笔来计算。
		exector.Rule.SplitThreshold = 50000
	case "5f96a11479f7565e230fbe58": // 元祖
		exector.Rule.SingleGap = 50000      // 500 元
		exector.Rule.DailyGap = 20000 * 100 // 2W
	}
	component.GO(ctx, func(ctx context.Context) {
		// 此 job 每 30s 启动一个，这里设置最长执行时间一分钟，30s 后启动下一个 job 时即使未执行完也可并发执行，即同一个租户并发量最多为 2
		err = share_model.ExecuteData(ctx, exector, share_model.ExectorConfigWithDeadline(time.Now().Add(time.Minute)))
		if err != nil {
			log.Warn(ctx, "Failed to exec transfer job", log.Fields{
				"err": err.Error(),
			})
		}
	})
	return &response.EmptyResponse{}, nil
}

type TransferExector struct {
	share_model.ExectorTemplate
	Rule ec_profitsharing.TransferRule
}

func (exector *TransferExector) GetCollection() string {
	return ec_profitsharing.C_TRANSFER_BILL
}

func (exector *TransferExector) NewInstanceForExec() interface{} {
	return &ec_profitsharing.TransferBill{}
}

func (exector *TransferExector) Execute(ctx context.Context, data interface{}) error {
	bill := data.(*ec_profitsharing.TransferBill)
	// 设置一个较大的数值，超过这个数值就置为失败
	max := uint64(1500000) // 一万五
	if util.StrInArray(util.GetAccountId(ctx), &[]string{
		"64d49ed42b7d7e029a11ed02", // 今世缘
		"664c3cc2044ce17015753872", // 今世缘D2C福建
		"645b0dbe088a96644b319e55", // 张裕
	}) {
		max = 5000000 // 五万
	}
	if bill.Income > max {
		bill.UpdateToFailed(ctx)
		createAmountOverflowAlert(ctx, bill)
		return errors.New("invalid amount")
	}
	// 用于限制同一笔订单产生多条账单的情况，限制 100 笔内不会对同一笔订单多次分账
	// 否则微信接口会报错：“对同笔订单分账频率过高”
	rate := make(map[string]struct{}, 100)
	if exector.ExecExtra == nil {
		exector.ExecExtra = make(bson.M)
	}
	if temp, ok := exector.ExecExtra["rate"]; ok {
		rate = temp.(map[string]struct{})
	}
	bill.Receiver.IsV2Transfer = exector.Rule.IsV2Transfer && bill.Receiver.AccountType != ec_profitsharing.RECEIVER_TYPE_MERCHANT
	bill.Receiver.IsV3Transfer = exector.Rule.IsV3Transfer && !bill.Receiver.IsV2Transfer && bill.Receiver.AccountType != ec_profitsharing.RECEIVER_TYPE_MERCHANT
	if handleSubBills(ctx, bill, exector.Rule) {
		if !bill.Finished(bill.Status) {
			exector.NextExecStatus = share_model.EXEC_STATUS_PENDING
			exector.ExecTimeInterval = bill.GetNextExecTime().Sub(time.Now())
		}
		return nil
	}

	switch bill.Status {
	case ec_profitsharing.BILL_STATUS_PENDING:
		if _, ok := rate[bill.OrderTradeNo]; ok && bill.Receiver.TransferChannel != profitsharing.TRANSFER_CHANNEL_YEEPAY {
			// 该交易单号已经在近 100 笔账单执行过了，因此重置执行状态放到 5 分钟后执行
			exector.NextExecStatus = share_model.EXEC_STATUS_PENDING
			exector.ExecTimeInterval = time.Minute * 5
			return nil
		}
		// 从 pending 更新为 processing 后可以直接开始下次执行，用于查询结果
		exector.ExecTimeInterval = 0
		err := TriggerProfitSharing(ctx, bill)
		if err != nil {
			if bill.FailedMessage == "" {
				bill.FailedMessage = "系统内部错误"
			}
			bill.UpdateToFailed(ctx)
		} else {
			bill.UpdateToProcessing(ctx)
			// status 更新为 processing 后下一次还是需要继续执行用于查询账单结果，所以 execStatus 需要指定为 pending
			// 如果不指定 execStatus，默认逻辑会根据返回的 error 更新 execStatus  为 finished 或者 error
			exector.NextExecStatus = share_model.EXEC_STATUS_PENDING
		}
		if len(rate) >= 100 {
			rate = make(map[string]struct{})
		}
		if bill.OrderTradeNo != "" {
			rate[bill.OrderTradeNo] = struct{}{}
		}
	case ec_profitsharing.BILL_STATUS_PROCESSING:
		HandleProfitStatus(ctx, bill)
		if (strings.Contains(bill.FailedMessage, "该用户今日付款次数超过限制") || strings.Contains(bill.FailedMessage, "该单据受用户单日收款限额影响")) && bill.Status == ec_profitsharing.BILL_STATUS_FAILED && bill.IsWechatBalanceTransfer() {
			now := time.Now()
			// 为了保证下次执行不受转账笔数的限制，下次执行时间设置在第二天凌晨五点之后
			nextExecTime := time.Date(now.Year(), now.Month(), now.Day(), 5, 0, 0, 0, time.Local).Add(time.Hour * 24)
			exector.NextExecStatus = share_model.EXEC_STATUS_PENDING
			exector.ExecTimeInterval = nextExecTime.Sub(now)
			bill.UpdateToPending(ctx)
			return nil
		}
		if bill.Status == ec_profitsharing.BILL_STATUS_PROCESSING {
			// 如果 bill 仍是 processing 状态，则下次执行时仍然需要触发
			exector.NextExecStatus = share_model.EXEC_STATUS_PENDING
			// bill.status 仍然是 processing 说明账单未处理完成，需要在 5 分钟后再次执行，防止 bill.status 一直是 processing 会无间隔循环被执行
			exector.ExecTimeInterval = time.Minute * 5
			// 如果当前是等待用户确认收款，查询间隔改为一小时
			if bill.Receiver.IsV3Transfer && bill.WechatTransferV3Info.Status == profitsharing.WECHAT_TRANSFER_V3_STATUS_WAIT_USER_CONFIRM {
				exector.ExecTimeInterval = time.Hour * 2
				createdAt := bill.WechatTransferV3Info.CreatedAt
				nextExecTime := time.Now().Add(time.Hour * 12)
				if !createdAt.IsZero() && createdAt.Add(time.Hour*24).Before(nextExecTime) {
					nextExecTime = createdAt.Add(time.Hour*24 + time.Minute)
					if nextExecTime.Before(time.Now()) {
						nextExecTime = time.Now().Add(time.Minute * 5)
					}
				}
				exector.ExecTimeInterval = nextExecTime.Sub(time.Now())
			}
		}
	default:
		// do nothing
	}
	exector.ExecExtra["rate"] = rate

	return nil
}

func createAmountOverflowAlert(ctx context.Context, bill *ec_profitsharing.TransferBill) {
	pb_client.GetAccountServiceClient().CreateAlert(ctx, &pb_account.CreateAlertRequest{
		Module:         "ec",
		Name:           "transferBill amount overflow",
		Priority:       "P1",
		Assignee:       "koston.zhuang",
		ExtraReceivers: []string{"lorne.qi", "alvin.zeng"},
		Context: core_util.MarshalInterfaceToString(map[string]interface{}{
			"accountId": util.GetAccountId(ctx),
			"amount":    bill.Income,
			"billId":    bill.Id,
		}),
	})
}

func handleSubBills(ctx context.Context, bill *ec_profitsharing.TransferBill, rule ec_profitsharing.TransferRule) bool {
	bill.SplitByRule(ctx, rule)
	if len(bill.SubBills) == 0 {
		return false
	}
	bill.UpdateToProcessing(ctx)
	for i, subBill := range bill.SubBills {
		execSubBill(ctx, bill, &subBill)
		bill.SubBills[i] = subBill
	}
	bill.UpdateProfitResultBySubBills(ctx)
	return true
}

func execSubBill(ctx context.Context, bill *ec_profitsharing.TransferBill, subBill *ec_profitsharing.SubBill) {
	if subBill.TransferDate.After(time.Now()) || ec_profitsharing.CTransferBill.Finished(subBill.Status) {
		return
	}
	tempBill := &ec_profitsharing.TransferBill{
		Id:                       subBill.Id,
		DetailId:                 subBill.DetailId,
		DetailIds:                []string{subBill.DetailId},
		Status:                   subBill.Status,
		WechatTradeNos:           subBill.WechatTradeNos,
		Income:                   subBill.Income,
		Receiver:                 bill.Receiver,
		NeedRetryByWechatBalance: bill.NeedRetryByWechatBalance,
		AccountId:                bill.AccountId,
		Description:              bill.Description,
		IsSubBill:                true,
	}

	switch tempBill.Status {
	case ec_profitsharing.BILL_STATUS_PENDING:
		err := TriggerProfitSharing(ctx, tempBill)
		if err != nil {
			if tempBill.FailedMessage == "" {
				tempBill.FailedMessage = "系统内部错误"
			}
			tempBill.Status = ec_profitsharing.BILL_STATUS_FAILED
		} else {
			tempBill.Status = ec_profitsharing.BILL_STATUS_PROCESSING
		}
	case ec_profitsharing.BILL_STATUS_PROCESSING:
		HandleProfitStatus(ctx, tempBill)
	}
	subBill.Status = tempBill.Status
	subBill.FailedMessage = tempBill.FailedMessage
	subBill.DetailId = tempBill.DetailId
	subBill.FinishedAt = tempBill.FinishedAt
	subBill.WechatTransferV3Info = tempBill.WechatTransferV3Info
}

func TriggerProfitSharing(ctx context.Context, bill *ec_profitsharing.TransferBill) error {
	transfer := new(TransferFactory)
	transferProvider := transfer.GetTransferProvider(bill)
	if transferProvider == nil {
		err := errors.New("transfer provider invalid")
		log.Warn(ctx, "Failed to trigger profit sharing", log.Fields{
			"bill":         bill,
			"errorMessage": err.Error(),
		})
		return err
	}
	if bill.Income == 0 {
		bill.FailedMessage = "分账金额不合法"
		return errors.New("分账金额不合法")
	}
	err := transferProvider.Run(ctx)
	if err != nil {
		errMsg := err.Error()
		// API internal error, code: 100009, msg: Unique valid failed for outOrderNo
		// API internal error, code: 100009, msg: Unique valid failed for tradeNo
		// 这种报错是因为之前加锁时长的问题导致重复提交了分账，导致状态被更新成了已退款
		// 只需要视为已成功分账，将状态更新成 processing 下次执行去查询分账结果即可。
		// 我们先是通过 wechatTransfer 处理分佣，失败后会用 wechatBalance 处理，一个用的字段是 outOrderNo，一个用的字段是 tradeNo
		if strings.Contains(errMsg, "Unique valid failed for outOrderNo") ||
			strings.Contains(errMsg, "Unique valid failed for tradeNo") {
			return nil
		}
		// 对于正常的业务报错，报错信息会记录在 bill.FailedMessage 字段，不需要打印日志
		if bill.FailedMessage == "" {
			log.Warn(ctx, "Failed to exec profitshare.", log.Fields{
				"billId":       bill.Id.Hex(),
				"errorMessage": errMsg,
			})
		}

		return err
	}

	return nil
}

func HandleProfitStatus(ctx context.Context, bill *ec_profitsharing.TransferBill) error {
	transfer := new(TransferFactory)
	transferProvider := transfer.GetTransferProvider(bill)
	if transferProvider == nil {
		log.Warn(ctx, "Failed to handle profit status", log.Fields{
			"bill":         bill,
			"errorMessage": "transfer provider invalid",
		})
		return nil
	}
	if bill.NeedRefreshFromWechat {
		ctx = context.WithValue(ctx, REFRESH_WHEN_CHECKING_RESULT, true)
	}
	err := transferProvider.CheckResult(ctx)
	if err != nil {
		log.Error(ctx, "Failed to get profitshare status.", log.Fields{
			"billId":       bill.Id.Hex(),
			"errorMessage": err.Error(),
		})

		return err
	}
	// 每次查询结果都会产生两条 execLogs，所以这里判断的是执行次数超过 25 次即视为查询超时
	if bill.Status == ec_profitsharing.BILL_STATUS_PROCESSING && len(bill.ExecLogs) > 50 {
		// 最后一次执行如果仍然失败，尝试主动刷新一次 weconnect 数据
		if _, ok := transferProvider.(*WechatTransfer); ok {
			ctx = context.WithValue(ctx, REFRESH_WHEN_CHECKING_RESULT, true)
			if err := transferProvider.CheckResult(ctx); err != nil {
				log.Error(ctx, "Failed to get refreshed profitshare status.", log.Fields{
					"billId":       bill.Id.Hex(),
					"errorMessage": err.Error(),
				})
				return err
			}
		}
		if bill.Status == ec_profitsharing.BILL_STATUS_PROCESSING {
			bill.Status = ec_profitsharing.BILL_STATUS_FAILED
			if bill.FailedMessage == "" {
				bill.FailedMessage = "查询结果超时"
			} else {
				bill.FailedMessage = fmt.Sprintf("%s, %s", bill.FailedMessage, "查询结果超时")
			}
		}
	}
	if bill.IsSubBill {
		return nil
	}

	return bill.UpdateProfitResult(ctx)
}
