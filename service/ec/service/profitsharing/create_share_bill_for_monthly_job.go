package profitsharing

import (
	"fmt"
	pb_distribution "mairpc/proto/ec/distribution"
	ec_client "mairpc/service/ec/client"
	ec_distribution "mairpc/service/ec/model/distribution"
	"mairpc/service/ec/service/mall"
	"time"

	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/log"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	ec_order "mairpc/service/ec/model/order"
	ec_profitsharing "mairpc/service/ec/model/profitsharing"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"

	"golang.org/x/net/context"
)

const (
	LOCK_KEY_PROFITSHARING_MONTHLY      = "ec:profitsharing_monthly:%s"
	LOCK_LIFETIME_PROFITSHARING_MONTHLY = 600 // 10 min
)

func (ProfitsharingService) CreateShareBillForMonthlyJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	lockKey := fmt.Sprintf(LOCK_KEY_PROFITSHARING_MONTHLY, util.GetAccountId(ctx))
	if ok, _ := extension.RedisClient.SetNX(lockKey, "1", LOCK_LIFETIME_PROFITSHARING_MONTHLY); !ok {
		return &response.EmptyResponse{}, nil
	}
	defer extension.RedisClient.Del(lockKey)
	err := shareBillMonthly(ctx)
	if err != nil {
		return nil, err
	}

	return &response.EmptyResponse{}, nil
}

func shareBillMonthly(ctx context.Context) error {
	receiverSelector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"type": bson.M{"$in": []string{
			ec_profitsharing.RECEIVER_TYPE_BANK,
			ec_profitsharing.RECEIVER_TYPE_OPENID,
			ec_profitsharing.RECEIVER_TYPE_PERSONAL,
			ec_profitsharing.RECEIVER_TYPE_MERCHANT,
		}},
	}
	receiverIter, err := ec_profitsharing.CProfitSharingReceiver.Iterate(ctx, receiverSelector, []string{"_id"})
	if err != nil {
		return err
	}
	defer receiverIter.Close()

	staffPromoterSetting, _ := ec_distribution.CDistributionSetting.GetByType(ctx, ec_distribution.DISTRIBUTION_SETTING_TYPE_STAFF)
	memberPromoterSetting, _ := ec_distribution.CDistributionSetting.GetByType(ctx, ec_distribution.DISTRIBUTION_SETTING_TYPE_MEMBER)

	receiver := &ec_profitsharing.ProfitSharingReceiver{}
	for receiverIter.Next(receiver) {
		transferChannel := ""

		var (
			transferBillId bson.ObjectId
			promoterId     bson.ObjectId
			totalAmount    uint64
			tax            uint64
			description    string
			orderCount     int
		)

		switch receiver.Type {
		case ec_profitsharing.RECEIVER_TYPE_BANK:
			transferChannel = ec_order.TRANSFER_CHANNEL_WECHAT_BANK
			transferBillId, totalAmount, err = ec_order.COrderReceiverProfit.StatisticAndUpdateForMonthlyShare(ctx, receiver.Id, transferChannel)
			if err != nil {
				return err
			}
			if transferBillId == "" || totalAmount == 0 {
				continue
			}
		case ec_profitsharing.RECEIVER_TYPE_PERSONAL:
			if receiver.ProfitType != "commission" {
				continue
			}
			if len(receiver.StoreIds) == 0 {
				continue
			} else {
				mall, err := mall.GetMall(ctx, receiver.StoreIds[0], false)
				if err != nil || !mall.Id.Valid() {
					log.Warn(ctx, "member mall receiver has no store", log.Fields{"needToDeleteReveiverId": receiver.Id.Hex()})
					continue
				}
			}
			var underUpdateCommissionOrderIds []bson.ObjectId
			transferChannel = ec_order.TRANSFER_CHANNEL_WECHAT_BALANCE
			transferBillId, underUpdateCommissionOrderIds, totalAmount, err = ec_order.COrderReceiverProfit.StatisticMonthlyCommissionAmount(ctx, receiver.Id, receiver.StoreIds[0])
			// 更新分佣记录
			if len(underUpdateCommissionOrderIds) > 0 {
				err = mall.UpdateMallCommissionBillIdByOrderIds(ctx, transferBillId, underUpdateCommissionOrderIds)
				if err != nil {
					return err
				}
			}

			if err != nil {
				log.Warn(ctx, "Failed to statistic monthly commission.", log.Fields{
					"receiverId":   receiver.Id.Hex(),
					"errorMessage": err.Error(),
				})
				continue
			}
			if transferBillId == "" || totalAmount == 0 {
				continue
			}
		case ec_profitsharing.RECEIVER_TYPE_OPENID, ec_profitsharing.RECEIVER_TYPE_MERCHANT:
			// 分销月结
			if receiver.ProfitType != "distribution" {
				continue
			}
			deductTax := false
			transferChannel = ec_order.TRANSFER_CHANNEL_WECHAT_BALANCE
			// TODO: 优化接口参数和 distributionSetting 重复获取
			promoterId, transferBillId, totalAmount, tax, deductTax, orderCount, err = ec_order.COrder.StatisticMonthlyDistributionAmount(ctx, receiver.Id.Hex(), receiver.Account, receiver.Type)
			if err != nil {
				log.Warn(ctx, "Failed to statistic monthly distribute.", log.Fields{
					"receiverId":   receiver.Id.Hex(),
					"errorMessage": err.Error(),
				})
				continue
			}
			description = ec_order.PROFIT_SHARING_DESCRIPTION_PERSONAL_DISTRIBUTION
			// 如果达到扣税标准且开启代扣个税，才展示代缴个税的备注
			if deductTax && tax > 0 {
				description = ec_order.PROFIT_SHARING_DESCRIPTION_PERSONAL_DISTRIBUTION_TAX
			}

			if transferBillId == "" || totalAmount == 0 {
				continue
			}
		}

		err = createBillForMonthShare(ctx, transferBillId, totalAmount, tax, orderCount, transferChannel, receiver, promoterId, description, staffPromoterSetting, memberPromoterSetting)
		if err != nil {
			log.Error(ctx, "Failed to create order share bill for monthly share", log.Fields{
				"transferBillId": transferBillId.Hex(),
				"receiverId":     receiver.Id.Hex(),
				"totalAmount":    totalAmount,
				"errorMessage":   err.Error(),
			})
		}
	}
	return nil
}

func createBillForMonthShare(
	ctx context.Context,
	billId bson.ObjectId,
	totalAmount,
	tax uint64,
	orderCount int,
	transferChannel string,
	receiver *ec_profitsharing.ProfitSharingReceiver,
	promoterId bson.ObjectId,
	description string,
	staffDistributionSetting *ec_distribution.DistributionSetting,
	memberDistributionSetting *ec_distribution.DistributionSetting,
) error {
	var dSetting *ec_distribution.DistributionSetting
	bill := ec_profitsharing.TransferBill{
		Id:        billId,
		AccountId: receiver.AccountId,
		// 一个 receiverAccount 可以对应多个 receiver
		OutTradeNo: receiver.Id.Hex() + "-" + time.Now().Format("2006-01"),
		Receiver: ec_profitsharing.BriefReceiver{
			Id:              receiver.Id,
			TransferChannel: transferChannel,
			AppId:           receiver.AppId,
			ChannelId:       receiver.ChannelId,
			Name:            receiver.Name,
			Account:         receiver.Account,
			AccountType:     receiver.Type,
		},
		Description: description,
		ShareAmount: totalAmount,
		Income:      totalAmount - tax,
		Tax:         tax,
		Status:      ec_profitsharing.BILL_STATUS_PENDING,
		OrderCount:  orderCount,
	}
	if description == "" {
		bill.Description = ec_order.PROFIT_SHARING_DESCRIPTION_PERSONAL
	}
	if receiver.Type == ec_order.RECEIVER_TYPE_MERCHANT_ID {
		// 商户类型的接收方只能使用分账，不能使用转账，所以商户号类型的接收方不允许使用月结
		return errors.NewInvalidArgumentErrorWithMessage("receiver", "Cannot create monthly share bill for merchant receiver")
	}
	if receiver.BankCode != "" {
		bill.Receiver.BankCode = receiver.BankCode
	}
	if promoterId.Hex() != "" {
		bill.Receiver.PromoterId = promoterId
		promoter, err := ec_client.DistributionService.GetPromoter(ctx, &pb_distribution.GetPromoterRequest{
			PromoterId: promoterId.Hex(),
		})
		if err != nil {
			return err
		}
		if promoter.Type == ec_distribution.PROMOTER_TYPE_STAFF {
			bill.Source = ec_profitsharing.BILL_SOURCE_STAFF_DISTRIBUTION
			dSetting = staffDistributionSetting
		} else if promoter.Type == ec_distribution.PROMOTER_TYPE_MEMBER {
			bill.Source = ec_profitsharing.BIll_SOURCE_MEMBER_DISTRIBUTION
			dSetting = memberDistributionSetting
		}
	} else if receiver.ProfitType == ec_profitsharing.PROFIT_TYPE_COMMISSION {
		bill.Source = ec_profitsharing.BILL_SOURCE_COMMISSION
	} else {
		bill.Source = ec_profitsharing.BILL_SOURCE_PROFITSHARING
	}

	// 如果是线下结算，分销月结记录设置为未结算
	if dSetting != nil && dSetting.IsOffline {
		bill.Status = ec_profitsharing.BILL_STATUS_NOT_ISSUED
		bill.ExecStatus = share_model.EXEC_StATUS_ERROR
		bill.FailedMessage = ec_profitsharing.BILL_FAILED_MESSAGE
	}

	return bill.Create(ctx)
}
