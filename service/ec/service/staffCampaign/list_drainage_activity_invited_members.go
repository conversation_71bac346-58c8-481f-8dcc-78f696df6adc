package staff_campaign

import (
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	pb_staff_campaign "mairpc/proto/ec/staffCampaign"
	pb_member "mairpc/proto/member"
	staff_campaign_model "mairpc/service/ec/model/staffCampaign"
	"mairpc/service/share/util"
	"sort"

	ec_service "mairpc/service/ec/service"

	"golang.org/x/net/context"
)

func (StaffCampaignService) ListDrainageActivityInvitedMembers(ctx context.Context, req *pb_staff_campaign.ListDrainageActivityInvitedMembersRequest) (*pb_staff_campaign.ListDrainageActivityInvitedMembersResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	condition := bson.M{
		"accountId":          util.GetAccountIdAsObjectId(ctx),
		"memberId":           util.ToMongoId(req.MemberId),
		"drainageActivityId": util.ToMongoId(req.ActivityId),
	}
	activityMember, err := staff_campaign_model.CDrainageActivityMember.GetByCondition(ctx, condition)
	if err != nil && err != bson.ErrNotFound {
		return &pb_staff_campaign.ListDrainageActivityInvitedMembersResponse{}, err
	}
	if len(activityMember.TotalInvitedMembers) == 0 {
		return &pb_staff_campaign.ListDrainageActivityInvitedMembersResponse{}, nil
	}
	invitedMembers, err := formatInvitedMember(ctx, activityMember.TotalInvitedMembers)
	if err != nil {
		return nil, err
	}
	return &pb_staff_campaign.ListDrainageActivityInvitedMembersResponse{
		Members: invitedMembers,
	}, nil
}

func formatInvitedMember(ctx context.Context, invitedMembers []staff_campaign_model.InvitedMember) ([]*pb_staff_campaign.DrainageActivityInvitedMember, error) {
	sort.Sort(staff_campaign_model.InvitedMemberArray(invitedMembers))

	// 查出所有被邀请客户的信息
	memberIds := core_util.ToStringArray(core_util.ExtractArrayField("MemberId", invitedMembers))
	memberMap := make(map[string]*pb_staff_campaign.DrainageActivityInvitedMember)
	members, err := ec_service.BatchSearchMembers(ctx, &pb_member.SearchMemberRequest{
		Ids: memberIds,
	})
	if err != nil {
		return nil, err
	}
	if len(members) == 0 {
		return []*pb_staff_campaign.DrainageActivityInvitedMember{}, nil
	}
	for _, member := range members {
		memberMap[member.Id] = &pb_staff_campaign.DrainageActivityInvitedMember{
			MemberId: member.Id,
			Avatar:   member.Avatar,
			Name:     member.Name,
		}
	}

	// 组装返回结果
	membersResponse := []*pb_staff_campaign.DrainageActivityInvitedMember{}
	for _, member := range invitedMembers {
		memberItem, ok := memberMap[member.MemberId.Hex()]
		if ok {
			memberItem.InvitedAt = member.InvitedAt.Format("2006/01/02 15:04")
		}
		membersResponse = append(membersResponse, memberItem)
	}
	return membersResponse, nil
}
