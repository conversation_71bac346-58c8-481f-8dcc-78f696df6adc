package staff_campaign

import (
	"errors"
	"mairpc/core/util/copier"
	"mairpc/proto/common/response"
	pb_staff_campaign "mairpc/proto/ec/staffCampaign"
	"mairpc/service/coupon/service"
	staff_campaign "mairpc/service/ec/model/staffCampaign"
	serviceComponent "mairpc/service/share/component"
	"mairpc/service/share/constant"
	"strings"

	"golang.org/x/net/context"
)

func (StaffCampaignService) UpsertGroupWelcomeTemplate(ctx context.Context, req *pb_staff_campaign.GroupWelcomeTemplateDetail) (*response.EmptyResponse, error) {
	accountChannel, err := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err != nil || accountChannel == nil {
		return &response.EmptyResponse{}, err
	}

	var groupWelcomeTemplate = &serviceComponent.GroupWelcomeTemplate{}
	err = copier.Instance(nil).From(req.WelcomeMsg).CopyTo(groupWelcomeTemplate)
	if err != nil {
		return &response.EmptyResponse{}, err
	}
	if req.WelcomeMsg.MiniProgram != nil && req.WelcomeMsg.MiniProgram.Desc != "" {
		groupWelcomeTemplate.MiniProgram.Title = req.WelcomeMsg.MiniProgram.Desc
	}

	groupWelcomeTemplate.Notify = true
	var welcomeMsg = staff_campaign.GroupWelcomeMsg{}
	err = copier.Instance(nil).From(req.WelcomeMsg).CopyTo(&welcomeMsg)
	if err != nil {
		return nil, err
	}
	var template *staff_campaign.GroupWelcomeTemplate
	if len(req.Id) <= 0 {
		template = &staff_campaign.GroupWelcomeTemplate{
			Name:       req.Name,
			WelcomeMsg: welcomeMsg,
		}

		resp, err := serviceComponent.WeConnect.CreateGroupWelcomeTemplate(ctx, accountChannel.ChannelId, groupWelcomeTemplate)
		if err != nil {
			if strings.Contains(err.Error(), "group welcome template exceed limit") {
				return nil, errors.New("群欢迎语已达到上限，请删除旧欢迎语后重试")
			}
			return nil, err
		}
		if resp == nil {
			return nil, errors.New("failed to create welcome template")
		}

		template.TemplateId = resp.TemplateId
	} else {
		template, err = staff_campaign.CGroupWelcomeTemplate.GetById(ctx, req.Id)
		if err != nil || template == nil {
			return &response.EmptyResponse{}, err
		}

		template.Name = req.Name
		template.WelcomeMsg = welcomeMsg
		groupWelcomeTemplate.TemplateId = template.TemplateId
		_, err = serviceComponent.WeConnect.UpdateGroupWelcomeTemplate(ctx, accountChannel.ChannelId, groupWelcomeTemplate)
		if err != nil {
			return nil, err
		}
	}

	err = template.Upsert(ctx)
	return &response.EmptyResponse{}, err
}
