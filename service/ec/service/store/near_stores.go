package store

import (
	"crypto/md5"
	"fmt"
	"sort"
	"strings"

	"mairpc/core/component"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/algorithm"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	"mairpc/proto/common/types"
	"mairpc/proto/ec/setting"
	ec_store "mairpc/proto/ec/store"
	ec_client "mairpc/service/ec/client"
	store_model "mairpc/service/ec/model/store"
	"mairpc/service/share/util"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

type simpleStore struct {
	Id         bson.ObjectId
	Distance   float64
	Coordinate store_model.Coordinate
}

func (StoreService) NearStores(ctx context.Context, req *ec_store.NearStoresRequest) (*ec_store.NearStoresResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if req.Coordinate == nil {
		if req.IsBoundStore != nil && req.IsBoundStore.Value {
			boundStore, _ := ec_client.StoreService.GetBoundStore(ctx, &ec_store.GetBoundStoreRequest{MemberId: req.MemberId})
			if boundStore != nil {
				req.StoreIds = []string{boundStore.StoreId}
			} else {
				return &ec_store.NearStoresResponse{}, nil
			}
		}
		selector := genStoreSelector(ctx, req)
		page := util.FormatPagingCondition(selector, req.ListCondition)
		stores, total, err := store_model.CStore.FindByPagination(ctx, page)
		if err != nil {
			return nil, err
		}

		return &ec_store.NearStoresResponse{
			Total: int64(total),
			Items: formatEcStores(ctx, stores, req.IsCityExpressStore, false, false),
		}, nil
	}

	setting, err := ec_client.SettingService.GetDeliverySetting(ctx, &request.EmptyRequest{})
	if err != nil {
		log.Warn(ctx, "Get ec setting failed.", log.Fields{"err": err.Error()})
	}

	query := generateCondition(ctx, req, setting)
	stores, err := getAllStoresWithCoordinateField(ctx, query)
	if err != nil {
		return nil, err
	}

	var selectedStoreIds, validSelectedStoreIds *algorithm.Set
	if len(req.SelectedStoreIds) > 10 {
		req.SelectedStoreIds = req.SelectedStoreIds[:10]
	}
	if len(req.SelectedStoreIds) > 0 {
		selectedStoreIds = algorithm.CSet.InstanceFromStringSlice(&req.SelectedStoreIds)
		validSelectedStoreIds = algorithm.CSet.Instance()
	}

	simpleStores := genSimpleStores(ctx, req, stores, selectedStoreIds, validSelectedStoreIds)
	formattedSimpleStores := formatSimpleStores(simpleStores, req, validSelectedStoreIds)
	if formattedSimpleStores == nil {
		return &ec_store.NearStoresResponse{}, nil
	}
	if req.DistanceMatrix {
		setActualDistance(ctx, req.Coordinate, simpleStores)
	}
	distanceMap := core_util.MakeFieldToFieldMapper("Id", "Distance", formattedSimpleStores)
	modelStores, err := getStoresWithSimpleStores(ctx, formattedSimpleStores)
	if err != nil {
		return nil, err
	}

	return &ec_store.NearStoresResponse{
		Total: int64(len(simpleStores)),
		Items: formatResponse(ctx, modelStores, validSelectedStoreIds, req.IsCityExpressStore, distanceMap),
	}, nil
}

func genSimpleStores(ctx context.Context, req *ec_store.NearStoresRequest, stores []store_model.Store, selectedStoreIds, validSelectedStoreIds *algorithm.Set) []*simpleStore {
	storeSetting, err := ec_client.SettingService.GetStoreSetting(ctx, &request.EmptyRequest{})
	if err != nil {
		log.Warn(ctx, "Get store setting failed.", log.Fields{"err": err.Error()})
	}
	simpleStores := []*simpleStore{}
	for _, s := range stores {
		if len(s.Coordinate.Coordinates) != 2 {
			if s.Location.Latitude == 0 && s.Location.Longitude == 0 {
				continue
			}
			s.Coordinate = store_model.Coordinate{
				Type:        "Point",
				Coordinates: []float64{s.Location.Longitude, s.Location.Latitude},
			}
		}
		if selectedStoreIds != nil && selectedStoreIds.Has(s.Id.Hex()) {
			validSelectedStoreIds.Insert(s.Id.Hex())
		}
		temp := &simpleStore{
			Id:         s.Id,
			Distance:   util.CalDistanceByCoordinate(req.Coordinate.Latitude, req.Coordinate.Longitude, s.Coordinate.Coordinates[1], s.Coordinate.Coordinates[0]),
			Coordinate: s.Coordinate,
		}
		if storeSetting != nil &&
			storeSetting.RecommendRule.IsEnabled &&
			storeSetting.RecommendRule.Distance > 0 &&
			float64(storeSetting.RecommendRule.Distance)*1000 < temp.Distance {
			continue
		}
		simpleStores = append(simpleStores, temp)
	}
	return simpleStores
}

// 1.按距离排序，距离小的在前，距离大的在后
// 2.根据 validSelectedStoreIds 排序，在 validSelectedStoreIds 中的在前，不在 validSelectedStoreIds 中在后，都在 validSelectedStoreIds 的按距离排序，距离小的在前，距离大的在后
// 3.此时 validSelectedStoreIds 中对应的门店都在最前面了，检查排在第一位的门店的距离和不在 validSelectedStoreIds 中的第一个门店的距离那个最小，如果后一个最小，放到第一位
// 4.按照请求分页要求，分割 simpleStores，只返回需要的数量
func formatSimpleStores(simpleStores []*simpleStore, req *ec_store.NearStoresRequest, validSelectedStoreIds *algorithm.Set) []*simpleStore {
	sort.Slice(simpleStores, func(i, j int) bool {
		return simpleStores[i].Distance < simpleStores[j].Distance
	})
	sort.Slice(simpleStores, func(i, j int) bool {
		if validSelectedStoreIds != nil {
			if validSelectedStoreIds.Has(simpleStores[i].Id.Hex()) && !validSelectedStoreIds.Has(simpleStores[j].Id.Hex()) {
				return true
			}
			if !validSelectedStoreIds.Has(simpleStores[i].Id.Hex()) && validSelectedStoreIds.Has(simpleStores[j].Id.Hex()) {
				return false
			}
		}
		return simpleStores[i].Distance < simpleStores[j].Distance
	})
	if validSelectedStoreIds != nil && len(simpleStores) >= int(validSelectedStoreIds.Size()+1) && simpleStores[0].Distance > simpleStores[validSelectedStoreIds.Size()].Distance {
		near := simpleStores[validSelectedStoreIds.Size()]
		origin := append([]*simpleStore{near}, simpleStores[0:validSelectedStoreIds.Size()]...)
		origin = append(origin, simpleStores[validSelectedStoreIds.Size()+1:]...)
		simpleStores = origin
	}
	if req.ListCondition == nil {
		req.ListCondition = &request.ListCondition{
			Page:    util.DEFAULT_PAGE_INDEX,
			PerPage: util.DEFAULT_PAGE_SIZE,
		}
	}
	startIndex := (req.ListCondition.Page - 1) * req.ListCondition.PerPage
	endIndex := startIndex + req.ListCondition.PerPage
	if int(startIndex) > len(simpleStores) {
		return nil
	}
	if int(endIndex) > len(simpleStores) {
		endIndex = uint32(len(simpleStores))
	}
	results := simpleStores[startIndex:endIndex]
	return results
}

func getStoresWithSimpleStores(ctx context.Context, simpleStores []*simpleStore) ([]store_model.Store, error) {
	var (
		ids         []string
		modelStores []store_model.Store
		useCache    bool
		cacheKey    string
	)
	useCache = core_util.GetServiceLossLevel(ctx) >= 2
	ids = core_util.ExtractArrayStringField("Id", simpleStores)
	if len(ids) <= 0 {
		return modelStores, nil
	}
	// 排序的目的是更好的命中缓存
	sort.Strings(ids)
	if useCache {
		cacheKey = fmt.Sprintf("%s:near-stores:%x", core_util.MustGetAccountId(ctx), md5.Sum([]byte(strings.Join(ids, ","))))
		value, err := extension.RedisClient.GetResponseCache(cacheKey)
		if err == nil && value != "" {
			err = core_util.UnzipFromString(value, &modelStores)
			if err == nil {
				return modelStores, nil
			}
		}
	}
	storeIds := core_util.ToObjectIdArray(ids)
	condition := bson.M{
		"_id": bson.M{
			"$in": storeIds,
		},
	}
	var result []bson.M
	err := extension.DBRepository.FindAll(ctx, store_model.C_STORE, condition, []string{}, 0, &result)
	if err != nil {
		return nil, err
	}
	for _, store := range result {
		b, _ := bson.Marshal(store)
		modelStore := store_model.Store{}
		_ = bson.Unmarshal(b, &modelStore)
		modelStores = append(modelStores, modelStore)
	}
	if useCache {
		value, err := core_util.ZipToString(modelStores)
		if err != nil {
			return modelStores, nil
		}
		extension.RedisClient.SetResponseCache(cacheKey, 60*10, value)
	}
	return modelStores, nil
}

func getAllStoresWithCoordinateField(ctx context.Context, query bson.M) ([]store_model.Store, error) {
	var (
		stores   []store_model.Store
		useCache bool
		cacheKey string
	)
	// 不缓存 $or 的情况（元祖没有传 Locations 不应该出现 $or ）
	_, ok := query["$or"]
	useCache = !ok && core_util.GetServiceLossLevel(ctx) >= 2
	if useCache {
		cacheKey = getCacheKeyFromBsonM(ctx, query)
		value, err := extension.RedisClient.GetResponseCache(cacheKey)
		if err == nil && value != "" {
			err = core_util.UnzipFromString(value, &stores)
			if err == nil {
				return stores, nil
			}
		}
	}
	fields := bson.M{
		"_id":        1,
		"coordinate": 1,
		"location":   1,
	}
	err := extension.DBRepository.FindAllWithFields(ctx, store_model.C_STORE, query, fields, []string{}, 0, &stores)
	if err != nil {
		return nil, err
	}
	if useCache {
		value, err := core_util.ZipToString(stores)
		if err != nil {
			return stores, nil
		}
		extension.RedisClient.SetResponseCache(cacheKey, 60*10, value)
	}
	return stores, nil
}

func getCacheKeyFromBsonM(ctx context.Context, m bson.M) string {
	var (
		cacheKey string
		cacheKVs []string
	)
	for k, v := range m {
		cacheKVs = append(cacheKVs, fmt.Sprintf("%s:%s", k, v))
	}
	sort.Strings(cacheKVs)
	for _, v := range cacheKVs {
		cacheKey = fmt.Sprintf("%s,%s", v, cacheKey)
	}
	cacheKey = fmt.Sprintf("%s:near-stores-coordinate:%x", core_util.MustGetAccountId(ctx), md5.Sum([]byte(cacheKey)))
	return cacheKey
}

func generateCondition(ctx context.Context, req *ec_store.NearStoresRequest, setting *setting.GetDeliverySettingResponse) bson.M {
	query := store_model.GenDefaultStoreCondition(ctx)
	query["status"] = store_model.STORE_STATUS_OPEN
	if req.IsEnabled != nil {
		query["isEnabled"] = req.IsEnabled.Value
	}

	if req.Location != nil && len(req.Locations) == 0 {
		req.Locations = []*types.Location{req.Location}
	}
	if len(req.Locations) > 0 {
		locConds := []bson.M{}
		for _, loc := range req.Locations {
			cond := bson.M{}
			if loc.Province != "" {
				cond["location.province"] = loc.Province
			}
			if loc.City != "" {
				cond["location.city"] = loc.City
			}
			if loc.District != "" {
				cond["location.district"] = loc.District
			}
			if len(cond) > 0 {
				locConds = append(locConds, cond)
			}
		}
		if len(locConds) == 1 {
			for k, v := range locConds[0] {
				query[k] = v
			}
		} else if len(locConds) > 0 {
			query["$or"] = locConds
		}
	}

	if req.StoreName != "" {
		query["name"] = util.GetFuzzySearchStrRegex(req.StoreName)
	}

	if req.Distributor != nil && len(req.Distributor.Ids) > 0 {
		query["distributorIds"] = bson.M{
			"$in": util.ToMongoIds(req.Distributor.Ids),
		}
	}

	if req.IsCityExpressStore {
		storeIds, err := store_model.CCityExpressStore.FindAllStoreIds(ctx, core_util.ToObjectIdArray(req.StoreIds))
		if err != nil {
			return nil
		}
		req.StoreIds = append(req.StoreIds, core_util.ToStringArray(storeIds)...)
	}

	if req.Status != "" {
		query["status"] = req.Status
	}

	if req.IsDeliveryEnabled != nil {
		query["deliverySetting.isEnabled"] = req.IsDeliveryEnabled.Value
		if setting != nil {
			switch {
			case setting.Logistics.ShipSetting.OnlyAddedStore && req.IsDeliveryEnabled.Value:
				// 部分门店开通物流、筛选物流门店
				// empty
			case !setting.Logistics.ShipSetting.OnlyAddedStore && req.IsDeliveryEnabled.Value:
				// 所有门店都开通物流、筛选物流门店
				delete(query, "deliverySetting.isEnabled")
			case setting.Logistics.ShipSetting.OnlyAddedStore && !req.IsDeliveryEnabled.Value:
				// 部分门店开通物流、筛选非物流门店
				// empty
			case !setting.Logistics.ShipSetting.OnlyAddedStore && !req.IsDeliveryEnabled.Value:
				// 所有门店都开通物流、筛选非物流门店
				delete(query, "deliverySetting.isEnabled")
				req.StoreIds = []string{bson.NilObjectId.Hex()}
			}
		}
	}

	if len(req.StoreTypeIds) > 0 {
		query["storeTypeId"] = bson.M{
			"$in": core_util.ToObjectIdArray(req.StoreTypeIds),
		}
	}

	if len(req.StoreIds) > 0 {
		query["_id"] = bson.M{"$in": util.ToMongoIds(req.StoreIds)}
	}

	if len(req.Tags) > 0 {
		query["tags"] = bson.M{
			"$in": req.Tags,
		}
	}

	return util.FormatConditionContainedOr(query)
}

func genStoreSelector(ctx context.Context, req *ec_store.NearStoresRequest) bson.M {
	selector := store_model.GenDefaultStoreCondition(ctx)

	if req.StoreName != "" {
		selector["name"] = req.StoreName
	}
	if len(req.StoreIds) > 0 {
		selector["_id"] = bson.M{"$in": util.ToMongoIds(req.StoreIds)}
	}
	if req.Status != "" {
		selector["status"] = req.Status
	}
	if req.IsDefault != nil {
		selector["deliverySetting.isDefault"] = req.IsDefault.Value
	}
	if req.Distributor != nil && len(req.Distributor.Ids) > 0 {
		selector["distributorIds"] = bson.M{
			"$in": util.ToMongoIds(req.Distributor.Ids),
		}
	}
	if len(req.Tags) > 0 {
		selector["tags"] = bson.M{
			"$in": req.Tags,
		}
	}

	return selector
}

func formatResponse(ctx context.Context, modelStores []store_model.Store, validSelectedStoreIds *algorithm.Set, needCheckCityExpressStore bool, distanceMap map[interface{}]interface{}) []*ec_store.StoreDetail {
	pbStores := formatEcNearStores(ctx, modelStores, needCheckCityExpressStore)
	result := []*ec_store.StoreDetail{}

	for i := range pbStores {
		if distance, ok := distanceMap[bson.ObjectIdHex(pbStores[i].Id)]; ok {
			pbStores[i].Distance = cast.ToUint64(distance)
		}
		if needCheckCityExpressStore && pbStores[i].CityExpressSetting != nil && pbStores[i].Distance > uint64(pbStores[i].CityExpressSetting.Range*1000) {
			continue
		}
		result = append(result, pbStores[i])
	}
	sort.Slice(result, func(i, j int) bool {
		if validSelectedStoreIds != nil {
			if validSelectedStoreIds.Has(result[i].Id) && !validSelectedStoreIds.Has(result[j].Id) {
				return true
			}
			if !validSelectedStoreIds.Has(result[i].Id) && validSelectedStoreIds.Has(result[j].Id) {
				return false
			}
		}
		return result[i].Distance < result[j].Distance
	})

	if validSelectedStoreIds != nil && len(result) >= int(validSelectedStoreIds.Size()+1) && result[0].Distance > result[validSelectedStoreIds.Size()].Distance {
		near := result[validSelectedStoreIds.Size()]
		origin := append([]*ec_store.StoreDetail{near}, result[0:validSelectedStoreIds.Size()]...)
		origin = append(origin, result[validSelectedStoreIds.Size()+1:]...)
		result = origin
	}
	return result
}

func formatEcNearStores(ctx context.Context, stores []store_model.Store, needCheckCityExpressStore bool) []*ec_store.StoreDetail {
	result := []*ec_store.StoreDetail{}
	var cityExpressStoreMapper map[interface{}]interface{}

	if needCheckCityExpressStore {
		cityExpressStores, err := store_model.CCityExpressStore.FindByStoreIds(ctx, core_util.ToObjectIdArray(core_util.ExtractArrayField("Id", stores)))
		if err != nil {
			log.Warn(ctx, "get city express stores failed", log.Fields{})
		}
		cityExpressStoreMapper = core_util.MakeMapper("StoreId", cityExpressStores)
	}

	for _, store := range stores {
		pbStore := formatEcStore(store)
		if val, ok := cityExpressStoreMapper[store.Id]; needCheckCityExpressStore && ok {
			pbStore.IsCityExpressStore = true
			cityExpressStore := val.(store_model.CityExpressStore)
			tmpCityExpressSetting := &ec_store.CityExpressSetting{}
			copier.Instance(nil).From(cityExpressStore.Delivery).CopyTo(tmpCityExpressSetting)
			pbStore.CityExpressSetting = tmpCityExpressSetting
		}
		result = append(result, pbStore)
	}

	return result
}

func setActualDistance(ctx context.Context, coordinate *request.PointRequest, results []*simpleStore) {
	if len(results) == 0 {
		return
	}

	toLocations := []component.GeoLocation{}
	failedIndex := algorithm.CSet.Instance()
	for i := range results {
		if results[i].Coordinate.Coordinates[0] == float64(0) || results[i].Coordinate.Coordinates[1] == float64(0) {
			log.Warn(ctx, "Get matrix invalid parameters.", log.Fields{"item": results[i]})
			failedIndex.Insert(i)
			continue
		}

		toLocations = append(toLocations, component.GeoLocation{
			Lng: results[i].Coordinate.Coordinates[0],
			Lat: results[i].Coordinate.Coordinates[1],
		})
	}
	matrix, err := component.TencentLBSClient.GetMatrix(ctx, component.GET_MATRIX_MODE_BICYCLING, []component.GeoLocation{
		{
			Lng: coordinate.Longitude,
			Lat: coordinate.Latitude,
		},
	}, toLocations)
	if err != nil && err != component.TENCENT_ERROR_OUT_OF_DAILY_CALL {
		log.Warn(ctx, "Get matrix from tencent lbs failed.", log.Fields{
			"err":  err.Error(),
			"from": fmt.Sprintf("%v,%v", coordinate.Longitude, coordinate.Latitude),
		})
		return
	}

	if (len(matrix.Rows) == 0 && len(toLocations) != 0) || len(matrix.Rows[0].Elements) != len(results) {
		log.Warn(ctx, "Get inconsistent result by to location.", log.Fields{})
		return
	}

	for i, j := 0, 0; i < len(results); i, j = i+1, j+1 {
		if failedIndex.Has(i) {
			i++
			continue
		}
		results[i].Distance = cast.ToFloat64(matrix.Rows[0].Elements[j].Distance)
	}
}
