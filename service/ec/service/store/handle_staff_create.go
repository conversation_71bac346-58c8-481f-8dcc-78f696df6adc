package store

import (
	"context"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	pb_ec_setting "mairpc/proto/ec/setting"
	"mairpc/service/coupon/service"
	ec_client "mairpc/service/ec/client"
	job_staff "mairpc/service/ec/jobs/staff"
	store_model "mairpc/service/ec/model/store"
	wework_license_model "mairpc/service/ec/model/weworkLicense"
	ec_service "mairpc/service/ec/service"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"
	"strings"

	"github.com/spf13/cast"
	conf "github.com/spf13/viper"
)

func (StoreService) HandleStaffCreate(ctx context.Context, req *request.CustomerEventRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	properties := core_util.UnmarshalJsonString(req.Properties)
	staffId := cast.ToString(properties["staffId"])
	if staffId == "" {
		return &response.EmptyResponse{}, nil
	}

	// 为新导购创建引流码
	createDrainageCode(ctx, staffId)
	// 为新导购创建默认的 weworkLicense
	createDefaultWeworkLicense(ctx, staffId)
	// 为新导购创建默认的联系我二维码，用于导购名片中显示
	createDefaultContactWay(ctx, staffId)
	// 新导购通知 weconnect 开启指定导购的外部联系人同步
	syncExternalUsers(ctx, staffId)
	return &response.EmptyResponse{}, nil
}

func createDrainageCode(ctx context.Context, staffId string) {
	// 有新门店时，如果符合旧的引流码规则需要创建对应的联系我二维码
	CreateDrainageCode(ctx, []string{staffId}, []string{})
	ec_service.RestoreContactWays(ctx, []string{staffId})
}

func createDefaultWeworkLicense(ctx context.Context, staffId string) {
	setting, err := ec_client.SettingService.GetWechatworkSetting(ctx, &pb_ec_setting.GetSettingsRequest{})
	if err != nil {
		log.Warn(ctx, "Failed to get wechatwork setting", log.Fields{
			"errorMessage": err.Error(),
		})
		return
	}
	if !setting.IsWeworkLicenseEnabled {
		return
	}
	staff, err := store_model.CStaff.GetById(ctx, bson.ObjectIdHex(staffId))
	if err != nil {
		return
	}
	weworkLicense := wework_license_model.WeworkLicense{
		StaffId:            util.ToMongoId(staffId),
		Status:             wework_license_model.WEWORK_LICENSE_STATUS_INACTIVATED,
		ChainCorpChannelId: staff.ChainCorpChannelId,
	}
	weworkLicense.Create(ctx)
}

func createDefaultContactWay(ctx context.Context, staffId string) {
	wechatworkSetting := ec_service.GetWechatworkSetting(ctx)
	if !wechatworkSetting.IsStaffCardEnabled {
		return
	}
	staff, _ := store_model.CStaff.GetById(ctx, util.ToMongoId(staffId))
	if !staff.Id.Valid() {
		return
	}
	miniProgram := job_staff.GenStaffDefaultContactWayMiniProgram(ctx)
	if miniProgram == nil && !strings.Contains(conf.GetString("env"), "biostime") {
		return
	}
	wecontactChannel, err := ec_service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err != nil {
		log.Warn(ctx, "failed to get wecontact channel", log.Fields{
			"accountId":    util.GetAccountId(ctx),
			"errorMessage": err.Error(),
		})
		return
	}
	if staff.ChainCorpChannelId != "" {
		wecontactChannel.ChannelId = staff.ChainCorpChannelId
	}
	staff.CreateDefaultContactWay(ctx, wecontactChannel.ChannelId, miniProgram)
}

func syncExternalUsers(ctx context.Context, staffId string) {
	staff, err := store_model.CStaff.GetById(ctx, bson.ObjectIdHex(staffId))
	if err != nil {
		return
	}
	channel, err := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err != nil {
		return
	}
	if staff.Source != constant.BUSINESS_WECHATWORK {
		return
	}
	err = component.WeConnect.SyncExternalUsers(ctx, channel.ChannelId, []string{staff.StaffNo}, true, true)
	if err != nil {
		log.Warn(ctx, "failed to sync external users", log.Fields{"staffNo": staff.StaffNo, "errMsg": err.Error()})
	}
}
