package store

import (
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	ec_store "mairpc/proto/ec/store"
	task_model "mairpc/service/ec/model/staffTask"
	store_model "mairpc/service/ec/model/store"
	"mairpc/service/share/util"
	"strings"

	conf "github.com/spf13/viper"
	"golang.org/x/net/context"
)

const (
	SEARCH_TYPE_STAFF = "staff"
	SEARCH_TYPE_STORE = "store"
)

func (StoreService) ListStoreTree(ctx context.Context, req *ec_store.ListStoreTreeRequest) (*ec_store.ListStoreTreeResponse, error) {
	excludeDistributorIds := getExcludeDistributorIds(ctx, req.ExcludeDepartmentIds)
	// 根据筛选条件查询门店和导购
	if req.SearchKey != "" || len(req.Levels) > 0 || len(req.Tags) > 0 {
		stores, staffs, err := searchStoresAndStaffIds(ctx, req, excludeDistributorIds)
		if err != nil {
			return nil, err
		}
		return formatListStoreTreeResponse(ctx, stores, staffs, req.ExtraFields), nil
	}

	// 获取根门店，无任何查询条件
	if req.ParentStoreId == "" {
		rootStores, err := getStores(ctx, req, excludeDistributorIds)
		if err != nil {
			return nil, err
		}
		return formatListStoreTreeResponse(ctx, rootStores, []store_model.Staff{}, req.ExtraFields), nil
	}

	// 获取子门店或门店下导购
	stores, staffs, err := getSubStoresAndStaffs(ctx, req.ParentStoreId, excludeDistributorIds)
	if err != nil {
		return nil, err
	}
	return formatListStoreTreeResponse(ctx, stores, staffs, req.ExtraFields), nil
}

func getExcludeDistributorIds(ctx context.Context, excludeDepartmentIds []string) []string {
	condition := store_model.Common.GenDefaultCondition(ctx)
	condition["departmentId"] = bson.M{"$in": excludeDepartmentIds}
	stores, _ := store_model.CStore.GetAllByCondition(ctx, condition)
	return core_util.ExtractArrayStringField("Id", stores)
}

// 根据查询条件查询门店或导购
func searchStoresAndStaffIds(ctx context.Context, req *ec_store.ListStoreTreeRequest, excludeDistributorIds []string) ([]store_model.Store, []store_model.Staff, error) {
	stores := []store_model.Store{}
	// 按照部门名称/编号搜索
	// 如果是手机号，不用搜索门店
	if req.SearchKey != "" && !validators.CValidator.IsPhone(req.SearchKey, nil) {
		var err error
		condition := getSearchCondition(ctx, req, nil, SEARCH_TYPE_STORE)
		if len(excludeDistributorIds) > 0 {
			condition["distributorIds"] = bson.M{"$nin": util.ToMongoIds(excludeDistributorIds)}
		}
		stores, err = store_model.CStore.GetAllByCondition(ctx, condition)
		if err != nil {
			return nil, nil, err
		}
	}

	storeIds := []bson.ObjectId{}
	if len(stores) > 0 {
		storeIds = util.ToMongoIds(core_util.ToStringArray(core_util.ExtractArrayField("Id", stores)))
	}
	staffCondition := getSearchCondition(ctx, req, storeIds, SEARCH_TYPE_STAFF)
	if len(excludeDistributorIds) > 0 {
		staffCondition["distributorIds"] = bson.M{"$nin": util.ToMongoIds(excludeDistributorIds)}
	}
	// 按照门店/部门名称和导购等级/标签筛选导购，只返回导购
	if len(req.Levels) > 0 || len(req.Tags) > 0 {
		stores = []store_model.Store{}
	}
	log.Warn(ctx, "staff condition log", log.Fields{
		"staffCondition": staffCondition,
		"req":            req,
	})

	staffs, err := store_model.CStaff.GetAllByCondition(ctx, staffCondition)
	if err != nil {
		return nil, nil, err
	}
	return stores, staffs, nil
}

func getStores(ctx context.Context, req *ec_store.ListStoreTreeRequest, excludeDistributorIds []string) ([]store_model.Store, error) {
	rootStores, err := store_model.CStore.GetRootStores(ctx, req.Types, excludeDistributorIds, true, req.BriefFields)
	if err != nil {
		return nil, err
	}
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	if len(excludeDistributorIds) > 0 {
		condition["distributorIds"] = bson.M{"$nin": util.ToMongoIds(excludeDistributorIds)}
	}
	setting := getWechatworkSetting(ctx)
	if !setting.SyncStaffsOnly {
		condition["source"] = store_model.STAFF_SOURCE_WECHATWORK
	}
	if len(req.CreatedBy) > 0 {
		condition["createdBy"] = bson.M{
			"$in": util.ToMongoIds(req.CreatedBy),
		}
	}
	// 特殊情况，可见范围中只有门店，没有部门
	if len(rootStores) == 0 && req.Distributor == nil {
		rootStores, err = store_model.CStore.GetAllByCondition(ctx, condition)
		if err != nil {
			return nil, err
		}
	}
	if req.Distributor != nil && len(req.Distributor.Ids) > 0 {
		distributorIds := filterDistributorIds(ctx, req.Distributor.Ids)
		condition["_id"] = bson.M{"$in": util.ToMongoIds(distributorIds)}
		rootStores, err = store_model.CStore.GetAllByCondition(ctx, condition)
		if err != nil {
			return nil, err
		}
	}
	return rootStores, nil
}

// 如果是部门获取下级门店，如果是门店获取门店下导购
func getSubStoresAndStaffs(ctx context.Context, storeId string, excludeDistributorIds []string) ([]store_model.Store, []store_model.Staff, error) {
	store, err := store_model.CStore.GetByIdIgnoreType(ctx, util.ToMongoId(storeId))
	if err != nil {
		return nil, nil, err
	}

	// 获取部门下子门店
	if store.Type == 2 {
		selector := bson.M{
			"accountId":          util.GetAccountIdAsObjectId(ctx),
			"isDeleted":          false,
			"isEnabled":          true,
			"parentDepartmentId": store.DepartmentId,
		}
		if len(excludeDistributorIds) > 0 {
			selector["distributorIds"] = bson.M{"$nin": util.ToMongoIds(excludeDistributorIds)}
		}
		stores, err := store_model.CStore.GetAllByCondition(ctx, selector)
		if err != nil {
			return nil, nil, err
		}
		return stores, []store_model.Staff{}, nil
	}

	// 获取门店下导购
	condition := bson.M{
		"accountId":                util.GetAccountIdAsObjectId(ctx),
		"isDeleted":                false,
		"source":                   store_model.STAFF_SOURCE_WECHATWORK,
		"status":                   bson.M{"$in": task_model.GetListStaffStatusCondition()},
		"accessibleDistributorIds": store.Id,
	}
	if len(excludeDistributorIds) > 0 {
		condition["distributorIds"] = bson.M{"$nin": util.ToMongoIds(excludeDistributorIds)}
	}
	staffs, err := store_model.CStaff.GetAllByCondition(ctx, condition)
	if err != nil {
		return nil, nil, err
	}
	return []store_model.Store{}, staffs, nil
}

func getSearchCondition(ctx context.Context, req *ec_store.ListStoreTreeRequest, storeIds []bson.ObjectId, searchType string) bson.M {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	if req.Distributor != nil && len(req.Distributor.Ids) > 0 {
		condition["distributorIds"] = bson.M{"$in": util.ToMongoIds(req.Distributor.Ids)}
	}
	if len(req.CreatedBy) > 0 {
		condition["createdBy"] = bson.M{
			"$in": util.ToMongoIds(req.CreatedBy),
		}
	}
	switch searchType {
	case SEARCH_TYPE_STAFF:
		condition["source"] = store_model.STAFF_SOURCE_WECHATWORK
		condition["status"] = bson.M{
			"$in": task_model.GetListStaffStatusCondition(),
		}
		if len(req.Levels) > 0 {
			condition["level"] = bson.M{
				"$in": req.Levels,
			}
		}
		if len(req.Tags) > 0 {
			condition["tags"] = bson.M{
				"$in": req.Tags,
			}
		}

		// SearchKey 不为空时存在两种情况
		// 1.SearchKey 可以查出来门店，则应该用 storeIds 构造 storeCondition，storeCondition 中不再加 searchKey 条件查询导购
		// 2.SearchKey 如果查不出来门店，则使用 SearchKey 构造 nameCondition 和 staffNoCondition
		// storeCondition，nameCondition，staffNoCondition 之间是或者关系
		if req.SearchKey != "" {
			storeCondition := bson.M{}
			nameCondition := bson.M{
				"name": util.GetFuzzySearchStrRegex(req.SearchKey),
			}
			staffNoCondition := bson.M{
				"staffNo": util.GetFuzzySearchStrRegex(req.SearchKey),
			}

			for key, value := range condition {
				storeCondition[key] = value
				nameCondition[key] = value
				staffNoCondition[key] = value
			}

			// 如果是 biostime 环境，不用模糊匹配。ANC-DTC 租户导购较少，不限制
			if strings.Contains(conf.GetString("env"), "biostime") && util.GetAccountId(ctx) != "63745a6313aa7a746d47bbd2" {
				staffNoCondition["staffNo"] = req.SearchKey
				nameCondition["name"] = req.SearchKey
			}
			orCondition := []bson.M{
				nameCondition,
				staffNoCondition,
			}
			if len(storeIds) > 0 {
				storeCondition["accessibleDistributorIds"] = bson.M{"$in": storeIds}
				orCondition = append(orCondition, storeCondition)
			}
			condition = bson.M{
				"$or": orCondition,
			}
		}
	case SEARCH_TYPE_STORE:
		nameCondition := bson.M{
			"name": util.GetFuzzySearchStrRegex(req.SearchKey),
		}
		codeCondition := bson.M{
			"code": util.GetFuzzySearchStrRegex(req.SearchKey),
		}
		for key, value := range condition {
			nameCondition[key] = value
			codeCondition[key] = value
		}
		condition = bson.M{
			"$or": []bson.M{
				nameCondition,
				codeCondition,
			},
		}
	}
	return condition
}

func formatListStoreTreeResponse(ctx context.Context, dbStores []store_model.Store, dbStaffs []store_model.Staff, extraFields []string) *ec_store.ListStoreTreeResponse {
	stores := []*ec_store.BriefStore{}
	copier.Instance(nil).From(dbStores).CopyTo(&stores)
	staffs := []*ec_store.BriefStaff{}
	copier.Instance(nil).From(dbStaffs).CopyTo(&staffs)
	if len(extraFields) > 0 {
		for _, staff := range staffs {
			tempStaff := staff
			if core_util.StrInArray("allGroupchatCount", &extraFields) {
				tempStaff.AllGroupchatCount = getAllGroupchatCount(ctx, staff.StaffNo)
			}
			if core_util.StrInArray("allMemberCount", &extraFields) {
				memberIds := getStaffMemberIds(ctx, tempStaff.Id)
				tempStaff.AllMemberCount = getAllMemberCount(ctx, tempStaff.Id, memberIds)
			}
		}
	}
	return &ec_store.ListStoreTreeResponse{
		Stores: stores,
		Staffs: staffs,
	}
}

func filterDistributorIds(ctx context.Context, distributorIds []string) []string {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"_id":       bson.M{"$in": util.ToMongoIds(distributorIds)},
		"isEnabled": true,
	}
	distributors, _ := store_model.CStore.GetAllByCondition(ctx, condition)
	newDistributorIds := []string{}
	for _, distributor := range distributors {
		if len(util.StrArrayDuplicate(util.MongoIdsToStrs(distributor.AncestorIds), distributorIds)) == 0 {
			newDistributorIds = append(newDistributorIds, distributor.Id.Hex())
		}
	}
	return newDistributorIds
}
