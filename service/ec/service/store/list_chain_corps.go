package store

import (
	"mairpc/core/extension/bson"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	ec_store "mairpc/proto/ec/store"
	store_model "mairpc/service/ec/model/store"
	wework_license_model "mairpc/service/ec/model/weworkLicense"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

func (StoreService) ListChainCorps(ctx context.Context, req *ec_store.ListChainCorpsRequest) (*ec_store.ListChainCorpsResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	if req.CorpName != "" {
		selector["corpName"] = util.GetFuzzySearchStrRegex(req.CorpName)
	}
	pageCondition := util.FormatPagingCondition(selector, req.ListCondition)
	chainCorps, total, err := store_model.CChainCorp.FindByPagination(ctx, pageCondition)
	if err != nil {
		return nil, err
	}
	respChainCorps := []*ec_store.ChainCorp{}
	copier.Instance(nil).RegisterResetDiffField([]copier.DiffFieldPair{
		{
			Origin:  "ChainCorpChannelId",
			Targets: []string{"Id"},
		},
	}).From(chainCorps).CopyTo(&respChainCorps)

	for _, respChainCorp := range respChainCorps {
		if respChainCorp.LicenseCount == 0 {
			continue
		}
		selector := bson.M{
			"accountId":          util.GetAccountIdAsObjectId(ctx),
			"isDeleted":          false,
			"chainCorpChannelId": respChainCorp.Id,
			"status":             wework_license_model.WEWORK_LICENSE_STATUS_ACTIVATED,
		}
		count, err := wework_license_model.CWeworkLicense.Count(ctx, selector)
		if err != nil {
			continue
		}
		respChainCorp.ActivatedCount = int64(count)
		if respChainCorp.LicenseCount-int64(count) > 0 {
			respChainCorp.AvailableCount = respChainCorp.LicenseCount - int64(count)
		}
	}

	return &ec_store.ListChainCorpsResponse{
		Items: respChainCorps,
		Total: int64(total),
	}, nil
}
