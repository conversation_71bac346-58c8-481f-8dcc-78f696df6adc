package member

import (
	"fmt"
	"mairpc/core/component"
	"mairpc/core/extension"
	"mairpc/proto/account"
	pb_client "mairpc/proto/client"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

var (
	SYNC_MEMBER_TAGS_CONTINUOUSLY_LOCK_KEY = "%s:ec.member:sync-member-tags-continuously"
	SYNC_MEMBER_TAGS_CONTINUOUSLY_DURATION = 24 * 60 * 60 // 1天

	SYNC_MEMBER_TAGS_CONTINUOUSLY_THRESHOLD = 10000
)

// 每小时执行一次
func (MemberService) SyncMemberTagsContinuously(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	component.GO(ctx, func(ctx context.Context) {
		util.ExecActivatedAccountsIterative(ctx, []string{constant.BUSINESS_WECHATWORK}, func(ctx context.Context) error {
			// 暂时只对佳贝艾特、中和动能，ANC-DTC、味全、家化租户开启
			if !util.StrInArray(util.GetAccountId(ctx), &[]string{
				"62b024978482c448fa0111a2",
				"62eb89e682bca228635eed12",
				"63745a6313aa7a746d47bbd2",
				"609897549104ab069e4589ac",
				"645376d1630c3639597de782",
				"63563d405d12704b7f295692",
			}) {
				return nil
			}
			if !needSyncMemberTags(ctx) {
				return nil
			}
			if isSyncingMemberTagsContinuously(ctx) {
				return nil
			}
			if IsExternalUserApiOutOfLimit(ctx) {
				return nil
			}
			tagsSyncer := InitTagsSyncer(ctx, true)
			if tagsSyncer == nil {
				return nil
			}
			req, err := GetGetMemberInfoLogsRequest(ctx)
			if err != nil {
				return err
			}
			resp, err := pb_client.GetMemberServiceClient().GetMemberInfoLogCount(ctx, req)
			if err != nil {
				return err
			}

			threshold := SYNC_MEMBER_TAGS_CONTINUOUSLY_THRESHOLD
			if resp.Value < int64(threshold) {
				return nil
			}

			_, err = pb_client.GetAccountServiceClient().CreateJob(ctx, &account.CreateJobRequest{
				JobName:               "syncmembertagscontinuously",
				Module:                "ec",
				SubModule:             "member",
				FunctionName:          "syncMemberTagsContinuously",
				Description:           "每小时检查待同步客户标签记录超过 10w 条创建一个",
				ActiveDeadlineSeconds: 2 * 24 * 3600,
			})
			return err
		})
	})
	return &response.EmptyResponse{}, nil
}

func GetSyncMemberTagsContinuouslyLockKey(ctx context.Context) string {
	return fmt.Sprintf(SYNC_MEMBER_TAGS_CONTINUOUSLY_LOCK_KEY, util.GetAccountId(ctx))
}

func isSyncingMemberTagsContinuously(ctx context.Context) bool {
	value, _ := extension.RedisClient.Get(GetSyncMemberTagsContinuouslyLockKey(ctx))
	return value != ""
}
