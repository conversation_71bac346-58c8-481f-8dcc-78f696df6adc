package member

import (
	"fmt"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"sort"
	"unicode/utf8"

	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	pb_setting "mairpc/proto/ec/setting"
	pb_member "mairpc/proto/member"
	task_model "mairpc/service/ec/model/staffTask"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"

	"github.com/spf13/cast"
	"golang.org/x/net/context"

	"encoding/json"
	"mairpc/proto/account"
	pb_client "mairpc/proto/client"
	ec_client "mairpc/service/ec/client"
)

var (
	SyncTagGroupIgnoreGroupNames = []string{
		task_model.TASK_TAG_GROUP_NAME,
		task_model.STAFF_OPERATION_TAG_GROUP_NAME,
	}
)

// cep 标签组和企业微信标签组互相同步(双向同步只同步增加和修改操作，删除操作需两边同时删除)
// - 有对应的企业微信标签组
//   - 如果标签组名称改变，更新企业微信标签组名称
//   - 合并两方标签组，更新外部联系人标签
//
// - 没有对应的企业微信标签组
//   - 创建对应的企业微信标签组
//   - 更新外部联系人标签
//
// - 可见标签组中没有某个企业微信标签组
//   - cep 中存在该标签组
//   - 将该标签组添加到可见标签组，并在 tagGroupBusiness.extra 中标识创建自企业微信
//   - 合并两方标签组，更新外部联系人标签
//   - cep 中不存在该标签组
//   - 创建该标签组并设置为导购可见标签组，标记创建自企业微信
//
// - 更新 tagGroupBusiness 字段
func (MemberService) SyncTagGroups(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	accountId := util.GetAccountId(ctx)
	if accountId == "56fa4377ba1b82a4378b4798" {
		return &response.EmptyResponse{}, nil
	}
	if accountId == "61664f780d26e23c366a0ca3" {
		return &response.EmptyResponse{}, nil
	}
	syncer, err := intiTagGroupSynchronizer(ctx)
	if err != nil {
		return nil, err
	}

	// 添加日志用于排查人保标签未同步原因，排查完成后删除
	if accountId == "61c97273095a9d1bf713b552" {
		log.Warn(ctx, "debug sync tag groups", log.Fields{
			"wechatworkTags": syncer.WechatworkTagGroups,
		})
		log.Warn(ctx, "debug sync tag groups", log.Fields{
			"modelTags": syncer.TagGroups,
		})
		log.Warn(ctx, "debug sync tag groups", log.Fields{
			"needSync":                       syncer.NeedSync,
			"channelId":                      syncer.ChannelId,
			"corpId":                         syncer.CorpId,
			"needSyncFromWechatworkGroupIds": syncer.NeedSyncFromWechatworkGroupIds,
		})
	}
	syncer.Sync(ctx)

	return &response.EmptyResponse{}, nil
}

func (syncer *TagGroupSynchronizer) Sync(ctx context.Context) {
	if !syncer.NeedSync {
		return
	}
	syncer.syncToWechatwork(ctx)
	syncer.syncFromWechatwork(ctx)
	syncer.upsertTags(ctx)
	syncer.batchUpdateTagGroupBusiness(ctx)
	syncer.updateExternalTags(ctx)
}

func (syncer *TagGroupSynchronizer) syncToWechatwork(ctx context.Context) error {
	for _, tagGroup := range syncer.TagGroups {
		if NeedSyncToWechatwork(GetBusinessExtra(tagGroup, constant.BUSINESS_WECHATWORK)) {
			syncer.syncTagGroupToWechatwork(ctx, tagGroup)
		}
	}
	return nil
}

func (syncer *TagGroupSynchronizer) syncFromWechatwork(ctx context.Context) error {
	for _, groupId := range syncer.NeedSyncFromWechatworkGroupIds {
		wechatworkTagGroup := syncer.GetWechatworkTagGroupById(groupId)
		if wechatworkTagGroup == nil {
			continue
		}
		syncer.syncTagGroupFromWechatwork(ctx, wechatworkTagGroup)
	}
	return nil
}

func (syncer *TagGroupSynchronizer) syncTagGroupToWechatwork(ctx context.Context, tagGroup *pb_member.TagGroup) {
	wechatworkTagGroup := syncer.GetWechatworkTagGroupByName(tagGroup.Name)
	if wechatworkTagGroup != nil {
		syncer.mergeWechatworkTagGroup(ctx, tagGroup, wechatworkTagGroup)
		// 群脉往企微合并后的标签组不需要再从企微往群脉同步了
		core_util.Remove(&syncer.NeedSyncFromWechatworkGroupIds, wechatworkTagGroup.GroupId)
		return
	}
	syncer.createTagGroupToWechatwork(ctx, tagGroup)
}

func (syncer *TagGroupSynchronizer) syncTagGroupFromWechatwork(ctx context.Context, wechatworkTagGroup *component.GroupTagsResponse) {
	tagGroup := syncer.GetTagGroupByName(wechatworkTagGroup.GroupName)
	if tagGroup != nil {
		syncer.mergeWechatworkTagGroup(ctx, tagGroup, wechatworkTagGroup)
		return
	}
	syncer.createTagGroupFromWechatwork(ctx, wechatworkTagGroup)
}

func (syncer *TagGroupSynchronizer) mergeWechatworkTagGroup(ctx context.Context, tagGroup *pb_member.TagGroup, wechatworkTagGroup *component.GroupTagsResponse) {
	wechatworkTagIdMap := core_util.MakeFieldToFieldMapper("Name", "Id", wechatworkTagGroup.Tags)
	wechatworkTags := core_util.ExtractArrayStringField("Name", wechatworkTagGroup.Tags)
	extra := GetBusinessExtra(tagGroup, constant.BUSINESS_WECHATWORK)
	needUpdateBusiness := extra == nil || (!extra.NeedSyncToWechatwork || extra.WechatworkTagGroupId != wechatworkTagGroup.GroupId)
	// 处理需要更新 business 的场景
	if needUpdateBusiness {
		isVisibleInWechatwork := false
		if extra != nil {
			isVisibleInWechatwork = extra.IsVisibleInWechatwork
		}
		newExtra := WechatworkGroupExtra{
			CreateFromWechatwork:  true,
			WechatworkTagGroupId:  wechatworkTagGroup.GroupId,
			NeedSyncToWechatwork:  true,
			IsVisibleInWechatwork: isVisibleInWechatwork,
		}
		syncer.BusinessUpdaters = append(syncer.BusinessUpdaters, &pb_member.UpdateTagGroupBusinessRequest{
			Id:       tagGroup.Id,
			Business: constant.BUSINESS_WECHATWORK,
			Extra:    newExtra.Marshal(),
			Action:   pb_member.UpdateTagGroupBusinessRequest_UPSERT,
		})
		// business 需要更新说明之前和企微标签没有同步，需要全量重新处理外部联系人标签
		// todo: @jarvis.pu 思考 wechatworkTagGroupId 和企微标签组 id 相同时，从群脉由不可见设置为可见此时会认为该标签组不需要全量处理，实际上是应该处理的，因为在未同步期间，两边的外部联系人身上的标签都可能有变化但位同步。
		syncer.UpdateExternalTagsParams.Tags = append(syncer.UpdateExternalTagsParams.Tags, tagGroup.Tags...)
		for _, tag := range wechatworkTagGroup.Tags {
			// 此时有些 tagGroup 中的标签还未同步到企微，因此 tagToWechatworkIdMap 并不完整，会在添加标签到企微后写入对应的 tagId
			syncer.UpdateExternalTagsParams.TagToWechatworkIdMap[tag.Name] = tag.Id
		}
		for _, tag := range wechatworkTagGroup.Tags {
			// 所有的企微标签都需要重新同步外部联系人标签
			syncer.addUpdateExternalTagsParamsNewTagsFromWechatwork(tag.Id, tag.Name)
		}
	}
	// 标签完全相同，不需要合并
	if util.StrArrayDeepEqual(wechatworkTags, tagGroup.Tags) {
		return
	}
	tempTags := util.StrArrayDiff(tagGroup.Tags, wechatworkTags)
	addToWechatworkTags := []string{}
	for _, tag := range tempTags {
		// 标签 utf8 长度超过 30 的不同步到企业微信，否则会报错 https://open.work.weixin.qq.com/devtool/query?e=40058
		if utf8.RuneCount([]byte(tag)) <= 30 {
			addToWechatworkTags = append(addToWechatworkTags, tag)
		}
	}

	addFromWechatworkTags := util.StrArrayDiff(wechatworkTags, tagGroup.Tags)
	if len(addToWechatworkTags) > 0 {
		syncer.addTagsToWechatworkTagGroup(ctx, wechatworkTagGroup, addToWechatworkTags)
		return
	}

	for _, tag := range addFromWechatworkTags {
		syncer.UpsertTagRequests = append(syncer.UpsertTagRequests, &pb_member.UpsertTagRequest{
			Name:    tag,
			GroupId: tagGroup.Id,
		})
		syncer.addUpdateExternalTagsParamsNewTagsFromWechatwork(cast.ToString(wechatworkTagIdMap[tag]), tag)
	}
}

func (syncer *TagGroupSynchronizer) createTagGroupToWechatwork(ctx context.Context, tagGroup *pb_member.TagGroup) {
	addTags := []string{}
	for _, tag := range tagGroup.Tags {
		// 标签 utf8 长度超过 30 的不同步到企业微信，否则会报错 https://open.work.weixin.qq.com/devtool/query?e=40058
		if utf8.RuneCount([]byte(tag)) <= 30 {
			addTags = append(addTags, tag)
		}
	}
	if len(addTags) == 0 {
		return
	}

	req := genAddTagsRequest("", tagGroup.Name, addTags)
	addTagsResp, err := component.WeConnect.AddTags(ctx, syncer.CorpId, syncer.ChannelId, req)
	if err != nil {
		log.Warn(ctx, "Failed to create tagGroup to wechatwork", log.Fields{
			"corpId":       syncer.CorpId,
			"req":          req,
			"errorMessage": err.Error(),
		})
		return
	}
	syncer.UpdateExternalTagsParams.Tags = append(syncer.UpdateExternalTagsParams.Tags, addTags...)
	// 需要添加的标签在企业微信原本是没有的，所以需要添加后再取 tag 的 id
	for _, tag := range addTagsResp.Tags {
		syncer.UpdateExternalTagsParams.TagToWechatworkIdMap[tag.Name] = tag.Id
	}

	// 不应该修改标签的可见性
	extra := GetBusinessExtra(tagGroup, constant.BUSINESS_WECHATWORK)
	isVisibleInWechatwork := false
	if extra != nil {
		isVisibleInWechatwork = extra.IsVisibleInWechatwork
	}
	newExtra := WechatworkGroupExtra{
		CreateFromWechatwork:  false,
		WechatworkTagGroupId:  addTagsResp.GroupId,
		NeedSyncToWechatwork:  true,
		IsVisibleInWechatwork: isVisibleInWechatwork,
	}
	syncer.BusinessUpdaters = append(syncer.BusinessUpdaters, &pb_member.UpdateTagGroupBusinessRequest{
		Id:       tagGroup.Id,
		Business: constant.BUSINESS_WECHATWORK,
		Extra:    newExtra.Marshal(),
		Action:   pb_member.UpdateTagGroupBusinessRequest_UPSERT,
	})
}

func (syncer *TagGroupSynchronizer) createTagGroupFromWechatwork(ctx context.Context, wechatworkTagGroup *component.GroupTagsResponse) {
	extra := WechatworkGroupExtra{
		CreateFromWechatwork:  true,
		IsVisibleInWechatwork: true,
		WechatworkTagGroupId:  wechatworkTagGroup.GroupId,
	}
	resp, err := pb_client.GetMemberServiceClient().CreateTagGroup(ctx, &pb_member.CreateTagGroupRequest{
		Name: wechatworkTagGroup.GroupName,
		Businesses: []*pb_member.TagGroupBusiness{
			{
				Business: constant.BUSINESS_WECHATWORK,
				Extra:    extra.Marshal(),
			},
		},
	})
	if err != nil {
		return
	}
	for _, tag := range wechatworkTagGroup.Tags {
		syncer.addUpdateExternalTagsParamsNewTagsFromWechatwork(tag.Id, tag.Name)
		syncer.UpsertTagRequests = append(syncer.UpsertTagRequests, &pb_member.UpsertTagRequest{
			Name:    tag.Name,
			GroupId: resp.Id,
		})
	}
}

func (syncer *TagGroupSynchronizer) addTagsToWechatworkTagGroup(ctx context.Context, wechatworkTagGroup *component.GroupTagsResponse, tags []string) {
	req := genAddTagsRequest(wechatworkTagGroup.GroupId, wechatworkTagGroup.GroupName, tags)
	addTagsResp, err := component.WeConnect.AddTags(ctx, syncer.CorpId, syncer.ChannelId, req)
	if err != nil {
		log.Warn(ctx, "Failed to add tags wechatwork tagGroup", log.Fields{
			"corpId":       syncer.CorpId,
			"req":          req,
			"errorMessage": err.Error(),
		})
		return
	}

	syncer.UpdateExternalTagsParams.Tags = append(syncer.UpdateExternalTagsParams.Tags, tags...)
	// 需要添加的标签在企业微信原本是没有的，所以需要添加后再取 tag 的 id
	for _, tag := range addTagsResp.Tags {
		// 不是新添加的标签不需要重新向企微同步外部联系人标签
		if !core_util.StrInArray(tag.Name, &tags) {
			continue
		}
		syncer.UpdateExternalTagsParams.TagToWechatworkIdMap[tag.Name] = tag.Id
	}
}

func (syncer *TagGroupSynchronizer) upsertTags(ctx context.Context) {
	if len(syncer.UpsertTagRequests) == 0 {
		return
	}
	pb_client.GetMemberServiceClient().BatchUpsertTags(ctx, &pb_member.BatchUpsertTagsRequest{
		Tags: syncer.UpsertTagRequests,
	})
}

func (syncer *TagGroupSynchronizer) batchUpdateTagGroupBusiness(ctx context.Context) {
	if len(syncer.BusinessUpdaters) == 0 {
		return
	}
	pb_client.GetMemberServiceClient().BatchUpdateTagGroupBusiness(ctx, &pb_member.BatchUpdateTagGroupBusinessRequest{
		BusinessUpdaters: syncer.BusinessUpdaters,
	})
}

// 客户已经有的标签，在被添加到企业微信后，需创建 job 给对应的外部联系人也添加标签
func (syncer *TagGroupSynchronizer) updateExternalTags(ctx context.Context) {
	params := syncer.UpdateExternalTagsParams
	params.ChannelId = syncer.ChannelId
	params.CorpId = syncer.CorpId
	if len(params.Tags) == 0 && len(params.NewTagsFromWechatwork) == 0 {
		return
	}
	params.Tags = util.StrArrayUnique(params.Tags)
	key := fmt.Sprintf("%s:ec:syncExternalContactTags:%s", util.GetAccountId(ctx), bson.NewObjectId().Hex())
	argsJson := core_util.MarshalInterfaceToString(params)
	ok, err := extension.RedisClient.SetEx(key, 3600, argsJson)
	if !ok {
		log.Warn(ctx, "Failed to create syncExternalContactTags job", log.Fields{
			"error": err,
		})
		return
	}
	_, err = pb_client.GetAccountServiceClient().CreateJob(ctx, &account.CreateJobRequest{
		JobName:      "syncexternalcontacttags",
		Module:       "ec",
		SubModule:    "member",
		FunctionName: "syncExternalContactTags",
		Description:  "每次和企业微信同步新标签后创建一个",
		Args: core_util.MarshalInterfaceToString(map[string]string{
			"key": key,
		}),
	})
	if err != nil {
		log.Warn(ctx, "Failed to create job to sync tags to external contact", log.Fields{
			"params": params,
			"errMsg": err.Error(),
		})
	}
}

func (syncer *TagGroupSynchronizer) GetTagGroupByName(name string) *pb_member.TagGroup {
	for i := range syncer.TagGroups {
		group := syncer.TagGroups[i]
		if group.Name == name {
			return group
		}
	}
	return nil
}

func (syncer *TagGroupSynchronizer) GetWechatworkTagGroupByName(name string) *component.GroupTagsResponse {
	for i := range syncer.WechatworkTagGroups {
		group := syncer.WechatworkTagGroups[i]
		if group.GroupName == name {
			return group
		}
	}
	return nil
}

func (syncer *TagGroupSynchronizer) GetWechatworkTagGroupById(id string) *component.GroupTagsResponse {
	for i := range syncer.WechatworkTagGroups {
		group := syncer.WechatworkTagGroups[i]
		if group.GroupId == id {
			return group
		}
	}
	return nil
}

func (syncer *TagGroupSynchronizer) addUpdateExternalTagsParamsNewTagsFromWechatwork(id, name string) {
	for _, newTagFromWechatwork := range syncer.UpdateExternalTagsParams.NewTagsFromWechatwork {
		if newTagFromWechatwork.TagId == id {
			return
		}
	}
	syncer.UpdateExternalTagsParams.NewTagsFromWechatwork = append(syncer.UpdateExternalTagsParams.NewTagsFromWechatwork, NewTagFromWechatwork{
		TagName: name,
		TagId:   id,
	})
}

func intiTagGroupSynchronizer(ctx context.Context) (*TagGroupSynchronizer, error) {
	syncer := &TagGroupSynchronizer{
		UpdateExternalTagsParams: initUpdateExternalTagsParams(),
	}
	err := syncer.initChannelIdAndCorpId(ctx)
	if err != nil {
		return syncer, nil
	}

	syncer.initNeedSyncFromWechatworkGroupIds(ctx)
	hasVisibleTagGroup, err := syncer.initTagGroups(ctx)
	if err != nil {
		return nil, err
	}
	if !hasVisibleTagGroup && len(syncer.NeedSyncFromWechatworkGroupIds) == 0 {
		return syncer, nil
	}

	err = syncer.initWechatworkTags(ctx)
	if err != nil {
		return syncer, nil
	}

	syncer.filterWechatworkTagGroups()
	err = syncer.replenishTagGroupWithSameNameAsWechatwork(ctx)
	if err != nil {
		return nil, err
	}
	syncer.NeedSync = true
	return syncer, nil
}

func (syncer *TagGroupSynchronizer) initChannelIdAndCorpId(ctx context.Context) error {
	channelId, corpId, err := GetChannelIdAndCorpId(ctx)
	if err != nil {
		return err
	}
	syncer.ChannelId = channelId
	syncer.CorpId = corpId
	return nil
}

func (syncer *TagGroupSynchronizer) initTagGroups(ctx context.Context) (bool, error) {
	// 获取导购可见标签组和企业微信标签组
	// 只获取添加到同步范围内的标签组，对于没有添加同步范围但需要从企微同步到群脉的标签组
	// 需要等同步范围的企业微信标签组获取之后再次获取对应的群脉标签组用于判断是创建还是合并
	tagGroupList, err := GetTagGroupList(ctx, &pb_member.GetTagGroupListRequest{
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: 1000,
		},
		BusinessSelector: &pb_member.TagGroupBusinessSelector{
			Extra: core_util.MarshalInterfaceToString(map[string]interface{}{
				"needSyncToWechatwork": true,
			}),
		},
	})
	if err != nil {
		return false, err
	}
	var hasNeedSyncTagGroup bool
	for i := range tagGroupList.Items {
		item := tagGroupList.Items[i]
		if core_util.StrInArray(item.Name, &SyncTagGroupIgnoreGroupNames) {
			continue
		}
		extra := GetBusinessExtra(item, constant.BUSINESS_WECHATWORK)
		if extra != nil && extra.NeedSyncToWechatwork {
			hasNeedSyncTagGroup = true
		}
		syncer.TagGroups = append(syncer.TagGroups, item)
	}
	return hasNeedSyncTagGroup, nil
}

func (syncer *TagGroupSynchronizer) initWechatworkTags(ctx context.Context) error {
	tagGroups, err := component.WeConnect.ListTags(ctx, syncer.CorpId, syncer.ChannelId, "", []string{})
	if err != nil {
		log.Warn(ctx, "Failed to list tags from wechatwork", log.Fields{
			"corpId": syncer.CorpId,
			"errMsg": err.Error(),
		})
		return err
	}

	syncer.WechatworkTagGroups = tagGroups
	return nil
}

// 获取需要从企业微信同步到群脉，但群脉侧没有标识同步到企业微信的群脉标签组，用于处理合并
func (syncer *TagGroupSynchronizer) replenishTagGroupWithSameNameAsWechatwork(ctx context.Context) error {
	wechatworkGroupNames := core_util.ExtractArrayStringField("GroupName", syncer.WechatworkTagGroups)
	tagGroupNames := core_util.ExtractArrayStringField("Name", syncer.TagGroups)
	diffWechatworkGroupNames := util.StrArrayDiff(wechatworkGroupNames, tagGroupNames)
	if len(diffWechatworkGroupNames) == 0 {
		return nil
	}
	resp, err := GetTagGroupList(ctx, &pb_member.GetTagGroupListRequest{
		GroupNames: diffWechatworkGroupNames,
	})
	if err != nil {
		return nil
	}
	syncer.TagGroups = append(syncer.TagGroups, resp.Items...)
	return nil
}

func (syncer *TagGroupSynchronizer) initNeedSyncFromWechatworkGroupIds(ctx context.Context) {
	wechatworkSetting, _ := ec_client.SettingService.GetWechatworkSetting(ctx, &pb_setting.GetSettingsRequest{})
	if wechatworkSetting == nil {
		return
	}
	if wechatworkSetting.IsSyncPartialTagGroup {
		syncer.NeedSyncFromWechatworkGroupIds = wechatworkSetting.SyncTagGroupIds
		return
	}

	// 全量同步直接把所有企微标签组 id 全部添加到需要同步的标签组 id 列表中
	for _, wechatworkTagGroup := range syncer.WechatworkTagGroups {
		syncer.NeedSyncFromWechatworkGroupIds = append(syncer.NeedSyncFromWechatworkGroupIds, wechatworkTagGroup.GroupId)
	}
}

func (syncer *TagGroupSynchronizer) filterWechatworkTagGroups() {
	newWechatworkTagGroups := []*component.GroupTagsResponse{}
	needSyncGroupNames := []string{}
	for _, tagGroup := range syncer.TagGroups {
		extra := GetBusinessExtra(tagGroup, constant.BUSINESS_WECHATWORK)
		if extra != nil && extra.NeedSyncToWechatwork {
			needSyncGroupNames = append(needSyncGroupNames, tagGroup.Name)
		}
	}
	for i := range syncer.WechatworkTagGroups {
		wechatworkTagGroup := syncer.WechatworkTagGroups[i]
		// 导购任务和日常运营的标签不同步
		if core_util.StrInArray(wechatworkTagGroup.GroupName, &SyncTagGroupIgnoreGroupNames) {
			continue
		}
		// 企微标签组既不在需要从企微同步的范围里又不在群脉往企微同步的范围里，可以直接丢弃
		if !core_util.StrInArray(wechatworkTagGroup.GroupId, &syncer.NeedSyncFromWechatworkGroupIds) &&
			!core_util.StrInArray(wechatworkTagGroup.GroupName, &needSyncGroupNames) {
			continue
		}
		newWechatworkTagGroups = append(newWechatworkTagGroups, wechatworkTagGroup)
	}
	syncer.WechatworkTagGroups = newWechatworkTagGroups
	syncer.uniqueWechatworkTags()
}

func (syncer *TagGroupSynchronizer) uniqueWechatworkTags() {
	existTags := []string{}
	sort.SliceStable(syncer.WechatworkTagGroups, func(i, j int) bool {
		return syncer.WechatworkTagGroups[i].GroupId < syncer.WechatworkTagGroups[j].GroupId
	})
	existGroupNames := []string{}
	newWechatworkTagGroups := []*component.GroupTagsResponse{}
	for i := range syncer.WechatworkTagGroups {
		wechatworkTagGroup := syncer.WechatworkTagGroups[i]
		if util.StrInArray(wechatworkTagGroup.GroupName, &existGroupNames) {
			continue
		}
		existGroupNames = append(existGroupNames, wechatworkTagGroup.GroupName)
		newTags := []component.TagResponse{}
		for j := range wechatworkTagGroup.Tags {
			tag := wechatworkTagGroup.Tags[j]
			if !core_util.StrInArray(tag.Name, &existTags) {
				newTags = append(newTags, tag)
				existTags = append(existTags, tag.Name)
				continue
			}
		}
		wechatworkTagGroup.Tags = newTags
		newWechatworkTagGroups = append(newWechatworkTagGroups, wechatworkTagGroup)
	}
	syncer.WechatworkTagGroups = newWechatworkTagGroups
}

// 初始化 UpdateExternalTagsParams，给 TagToWechatworkIdMap 赋默认值，防止忘记初始化直接使用 map
func initUpdateExternalTagsParams() UpdateExternalTagsParams {
	return UpdateExternalTagsParams{
		TagToWechatworkIdMap: map[string]string{},
	}
}

func genAddTagsRequest(wechatWorkTagGroupId, wechatWorkTagGroupName string, addList []string) component.AddTagsRequest {
	addTagsRequest := component.AddTagsRequest{
		GroupId:   wechatWorkTagGroupId,
		GroupName: wechatWorkTagGroupName,
	}
	for _, tagName := range addList {
		addTagsRequest.Tags = append(addTagsRequest.Tags, component.AddRequestTag{
			Name: tagName,
		})
	}

	return addTagsRequest
}

func GetBusinessExtra(tagGroup *pb_member.TagGroup, business string) *WechatworkGroupExtra {
	for _, b := range tagGroup.Businesses {
		if b.Business == business {
			extra := &WechatworkGroupExtra{}
			json.Unmarshal([]byte(b.Extra), extra)
			return extra
		}
	}

	return nil
}

type TagGroupSynchronizer struct {
	NeedSync                       bool
	ChannelId                      string
	CorpId                         string
	TagGroups                      []*pb_member.TagGroup
	WechatworkTagGroups            []*component.GroupTagsResponse
	NeedSyncFromWechatworkGroupIds []string

	UpdateExternalTagsParams UpdateExternalTagsParams
	BusinessUpdaters         []*pb_member.UpdateTagGroupBusinessRequest
	UpsertTagRequests        []*pb_member.UpsertTagRequest
}

type UpdateExternalTagsParams struct {
	// tags 存放向企微新添加的标签
	Tags                 []string          `json:"tags"`
	TagToWechatworkIdMap map[string]string `json:"tagToWechatworkIdMap"`
	// newTagsFromWechatwork 用于存放企微向群脉新添加的标签
	NewTagsFromWechatwork []NewTagFromWechatwork `json:"newTagsFromWechatwork"`
	CorpId                string                 `json:"corpId"`
	ChannelId             string                 `json:"channelId"`
}

type NewTagFromWechatwork struct {
	TagName string `json:"tagName"`
	TagId   string `json:"tagId"`
}
