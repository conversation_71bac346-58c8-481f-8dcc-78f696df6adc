package order

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	core_error "mairpc/core/errors"
	core_errors "mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/core/util"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	ec_order "mairpc/proto/ec/order"
	pb_ec_order "mairpc/proto/ec/order"
	ec_store "mairpc/proto/ec/store"
	"mairpc/service/ec/client"
	"mairpc/service/ec/model/order"
	order_model "mairpc/service/ec/model/order"
	"mairpc/service/ec/service"
	share_util "mairpc/service/share/util"
)

// 更新单张发票时不建议使用 orderId 或 number，“商品合并开票”时会将所有关联发票更新
func (OrderService) UpdateInvoice(ctx context.Context, req *ec_order.UpdateInvoiceRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if req.Id == "" && req.OrderId == "" && req.OrderNumber == "" {
		return nil, core_error.NewInvalidArgumentErrorWithMessage("id", "id,orderId and orderNumber cannot be empty at the same time")
	}
	if req.Invoice != nil {
		req.Invoice.Name = strings.ReplaceAll(strings.TrimSpace(req.Invoice.Name), " ", "")
		req.Invoice.TaxID = strings.ReplaceAll(strings.TrimSpace(req.Invoice.TaxID), " ", "")
	}
	var (
		invoices []order_model.Invoice
		err      error
	)
	if req.Id != "" {
		invoice, err := order_model.CInvoice.GetById(ctx, bson.ObjectIdHex(req.Id))
		if err != nil {
			return nil, err
		}
		invoices = append(invoices, invoice)
	} else if req.OrderId != "" {
		invoices, err = order_model.CInvoice.GetByOrderId(ctx, bson.ObjectIdHex(req.OrderId))
	} else if req.OrderNumber != "" {
		invoices, err = order_model.CInvoice.GetByOrderNumber(ctx, req.OrderNumber)
	}
	if err != nil {
		return nil, err
	}
	if len(invoices) == 0 {
		return nil, errors.New("Failed to get invoices")
	}
	if req.Status == order_model.INVOICE_STATUS_REBILLING {
		if err := handleRebilling(ctx, req, invoices); err != nil {
			return nil, err
		}
		return &response.EmptyResponse{}, nil
	}
	for _, invoice := range invoices {
		if req.Invoice != nil {
			if err := updateInvoice(ctx, req, invoice); err != nil {
				return nil, err
			}
			return &response.EmptyResponse{}, nil
		}
		if req.Status != "" {
			invoice.Status = req.Status
			operator, extra := getOperatorAndExtra(ctx)
			extra["reason"] = req.FailedReason
			statusList := core_util.ExtractArrayFieldV2("Status", "", invoice.Histories)
			if invoice.Status == order_model.INVOICE_STATUS_PENDING {
				if util.StrInArray(order_model.INVOICE_STATUS_REBILLING, &statusList) {
					invoice.GenerateHistory(operator, extra)
				}
			} else {
				// 接单时更新发票状态为 pending，不记录 history
				invoice.GenerateHistory(operator, extra)
			}
		}
		if req.Result != nil {
			if req.Result.FileUrl != "" {
				invoice.FileUrl = req.Result.FileUrl
			}
			if req.Result.Code != "" {
				invoice.Code = req.Result.Code
			}
			if req.Result.Number != "" {
				invoice.Number = req.Result.Number
			}
			if req.Result.InvoicedAt != "" {
				invoice.InvoicedAt = parseInvoicedAt(req.Result.InvoicedAt)
			} else if invoice.InvoicedAt.IsZero() {
				invoice.InvoicedAt = time.Now()
			}
			if req.Result.Amount != 0 {
				invoice.Amount = req.Result.Amount
			}
		}
		if req.Status == order_model.INVOICE_STATUS_ISSUED {
			invoice.InvoicedAt = time.Now()
			if req.Result != nil && req.Result.InvoicedAt != "" {
				invoice.InvoicedAt = parseInvoicedAt(req.Result.InvoicedAt)
			}
		}
		if req.RedConfirmUuid != "" {
			invoice.RedConfirmUuid = req.RedConfirmUuid
		}
		if req.SerialNo != "" {
			invoice.SerialNo = req.SerialNo
		}
		if err := invoice.Update(ctx); err != nil {
			return nil, err
		}
	}
	// 处理订单状态
	if req.Status != "" {
		if err := handleOrderInvoiceStatus(ctx, invoices, req.Status); err != nil {
			return nil, err
		}
	}
	return &response.EmptyResponse{}, nil
}

func getOperatorAndExtra(ctx context.Context) (string, bson.M) {
	userId := util.GetUserId(ctx)
	if userId == "" {
		return "system", bson.M{}
	}
	var (
		operator string
		extra    = bson.M{}
	)
	switch util.GetUserRole(ctx) {
	case "user":
		user, err := service.GetUserById(ctx, userId)
		if err != nil {
			log.Warn(ctx, "Failed to get user by id", log.Fields{
				"id":     userId,
				"errMsg": err.Error(),
			})
			return "", bson.M{}
		}
		operator = fmt.Sprintf("user:%s", userId)
		extra["userName"] = user.Name
	case "staff":
		staff, err := service.GetStaff(ctx, &ec_store.StaffDetailRequest{StaffId: userId})
		if err != nil {
			log.Warn(ctx, "Failed to get staff by id", log.Fields{
				"staffId": userId,
				"errMsg":  err.Error(),
			})
			return "", bson.M{}
		}
		operator = fmt.Sprintf("staff:%s", userId)
		extra["staffName"] = staff.Name
	}
	return operator, extra
}

func parseInvoicedAt(timeStr string) time.Time {
	formats := []string{
		"20060102150405",      // Format: YYYYMMDDHHMMSS
		"2006-01-02 15:04:05", // Format: YYYY-MM-DD HH:MM:SS
		util.RFC3339Mili,
	}
	for _, format := range formats {
		parsedTime, err := time.Parse(format, timeStr)
		if err == nil && !parsedTime.IsZero() {
			return parsedTime
		}
	}
	return time.Now()
}

func updateInvoice(ctx context.Context, req *ec_order.UpdateInvoiceRequest, invoice order_model.Invoice) error {
	copier.Instance(nil).From(req.Invoice).CopyTo(&invoice)
	if !util.StrInArray(invoice.Status, &[]string{order_model.INVOICE_STATUS_PENDING, ""}) {
		return core_error.NewCanNotEditError("id")
	}
	if invoice.MemberId.Hex() != req.MemberId {
		return core_error.NewCanNotEditError("id")
	}
	if err := invoice.Valid(); err != nil {
		return err
	}
	if err := invoice.Update(ctx); err != nil {
		return err
	}
	return nil
}

func handleRebilling(
	ctx context.Context,
	req *ec_order.UpdateInvoiceRequest,
	invoices []order_model.Invoice,
) error {
	if req.Invoice == nil || req.Invoice.Name == "" {
		return errors.New("invoice name is required when rebilling")
	}
	invoiceSetting, err := client.SettingService.GetInvoiceSetting(ctx, &request.EmptyRequest{})
	if err != nil {
		return err
	}
	orderIds := invoices[0].MergedOrderIds
	orderIds = append(orderIds, invoices[0].OrderId)
	orders, err := order.COrder.GetByIds(ctx, orderIds)
	if err != nil {
		return err
	}
	// 重开时默认自动红冲
	if !invoiceSetting.IsReissueAllowed {
		return errors.New("rebilling is not supported, please check your invoice setting")
	}
	var invoicesToReissue []order_model.Invoice
	for _, invoice := range invoices {
		if invoice.Status == order_model.INVOICE_STATUS_DELETED || invoice.IsRed {
			continue
		}
		statusList := core_util.ExtractArrayFieldV2("Status", "", invoice.Histories)
		if util.StrInArray(order_model.INVOICE_STATUS_REBILLING, &statusList) {
			return errors.New("the invoice can only be reissued once")
		}
		if invoice.Status != order_model.INVOICE_STATUS_ISSUED {
			return errors.New("invoice have never been issued")
		}
		invoicesToReissue = append(invoicesToReissue, invoice)
	}
	invoiceReissueDaysLimit := invoiceSetting.InvoiceReissueDaysLimit
	if invoiceReissueDaysLimit == 0 {
		invoiceReissueDaysLimit = 60
	}
	for _, order := range orders {
		if order.Status == order_model.ORDER_STATUS_COMPLETED && time.Now().After(order.CompletedAt.AddDate(0, 0, int(invoiceReissueDaysLimit))) {
			return fmt.Errorf("order: %s was completed more than %d day", order.Id, invoiceReissueDaysLimit)
		}
	}
	operator, extra := getOperatorAndExtra(ctx)
	for _, invoice := range invoicesToReissue {
		copier.Instance(nil).From(req.Invoice).CopyTo(&invoice)
		invoice.Status = order_model.INVOICE_STATUS_REBILLING
		invoice.FileUrl = ""
		invoice.GenerateHistory(operator, extra)
		if err := invoice.Update(ctx); err != nil {
			return err
		}
	}
	if err := order_model.BatchUpdateInvoiceByIds(ctx, orderIds, order_model.OrderInvoice{
		Needed:         true,
		Status:         order_model.INVOICE_STATUS_REBILLING,
		InvoiceSetting: invoiceSetting.InvoiceSetting,
	}); err != nil {
		log.Warn(ctx, "Failed to update order's invoice by ids", log.Fields{
			"orderIds": orderIds,
			"errMsg":   err.Error(),
		})
	}
	return nil
}

func handleOrderInvoiceStatus(ctx context.Context, invoices []order_model.Invoice, status string) error {
	invoice := invoices[0]
	orderIds := invoice.MergedOrderIds
	orderIds = append(orderIds, invoice.OrderId)
	listInvoicesResp, _ := client.OrderService.ListInvoices(ctx, &pb_ec_order.ListInvoicesRequest{
		OrderIds: share_util.MongoIdsToStrs(orderIds),
		IsRed:    false,
	})
	if listInvoicesResp == nil || len(listInvoicesResp.Items) == 0 {
		return core_errors.NewInvalidArgumentError("invoice")
	}
	canUpdateOrderStatus := true
	for _, invoice := range listInvoicesResp.Items {
		if invoice.Status != status {
			canUpdateOrderStatus = false
			break
		}
	}
	// 只有全部发票状态一致时才更新订单的发票状态
	if canUpdateOrderStatus {
		if len(invoice.MergedOrderIds) > 0 {
			if err := order_model.BatchUpdateInvoiceByIds(ctx, orderIds, order_model.OrderInvoice{
				Needed:         true,
				Status:         status,
				InvoiceSetting: invoice.InvoiceSetting,
			}); err != nil {
				log.Warn(ctx, "Failed to update order's invoice by ids", log.Fields{
					"orderIds": orderIds,
					"errMsg":   err.Error(),
				})
			}
		} else {
			copier.Instance(nil).From(listInvoicesResp.Items[0]).CopyTo(&invoice)
			order, err := GenInvoiceOrder(ctx, invoice.OrderType, invoice.OrderId)
			if err != nil {
				return err
			}
			order.UpdateInvoiceStatus(ctx, &invoice)
		}
	}
	return nil
}
