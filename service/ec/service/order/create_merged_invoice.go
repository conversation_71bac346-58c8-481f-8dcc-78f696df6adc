package order

import (
	"errors"
	"fmt"
	"strings"
	"time"

	core_errors "mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	ec_order "mairpc/proto/ec/order"
	pb_ec_order "mairpc/proto/ec/order"
	"mairpc/proto/ec/setting"
	"mairpc/service/ec/client"
	"mairpc/service/ec/model/order"
	model_order "mairpc/service/ec/model/order"
	order_model "mairpc/service/ec/model/order"
	"mairpc/service/share/component/invoice"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

// 目前仅零售订单支持线上合并开票（百望），不支持直接创建红票，支持发票重开，支持商品合并
func (OrderService) CreateMergedInvoice(ctx context.Context, req *ec_order.CreateMergedInvoiceRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	invoiceSetting, err := client.SettingService.GetInvoiceSetting(ctx, &request.EmptyRequest{})
	if err != nil {
		return nil, err
	}
	if !invoiceSetting.IsMergeInvoicing || invoiceSetting.Provider != invoice.PROVIDER_BAIWANG || invoiceSetting.InvoiceSetting != order_model.INVOICE_SETTING_ONLINE {
		return nil, errors.New("merge invoicing is not supported, please check your invoice setting")
	}
	if req.IsProxyOrder {
		return nil, errors.New("proxy order is not supported")
	}
	var redisKeys []string
	for _, orderId := range req.OrderIds {
		key := fmt.Sprintf(EC_CREATE_INVOICE_LOCK_KEY, util.GetAccountId(ctx), orderId)
		redisKeys = append(redisKeys, key)
		ok, _ := extension.RedisClient.SetNX(key, "true", 5)
		if !ok {
			return nil, core_errors.NewTooManyRequestsError("orderId")
		}
	}
	if req.IsRebilling {
		_, rpcErr := client.OrderService.UpdateInvoice(ctx, &pb_ec_order.UpdateInvoiceRequest{
			OrderId: req.OrderIds[0],
			Status:  order_model.INVOICE_STATUS_REBILLING,
			Invoice: req.Invoice,
		})
		if rpcErr != nil {
			return nil, rpcErr
		}
		return &response.EmptyResponse{}, nil
	}
	orders, err := order.COrder.GetByIds(ctx, util.ToMongoIds(req.OrderIds))
	if err != nil {
		return nil, err
	}
	invoices, _ := order_model.CInvoice.GetByOrderIds(ctx, util.ToMongoIds(req.OrderIds))
	if len(invoices) > 0 {
		return nil, core_errors.NewAlreadyExistsError("orderId")
	}
	defer func() {
		for _, key := range redisKeys {
			extension.RedisClient.Del(key)
		}
	}()
	invoiceIssueDaysLimit := invoiceSetting.InvoiceIssueDaysLimit
	if invoiceIssueDaysLimit == 0 {
		invoiceIssueDaysLimit = 60
	}
	orderIdToOrderNumberMap := make(map[string]string)
	for _, order := range orders {
		if order.Status == order_model.ORDER_STATUS_COMPLETED && time.Now().After(order.CompletedAt.AddDate(0, 0, int(invoiceIssueDaysLimit))) {
			return nil, fmt.Errorf("order: %s was completed more than %d day", order.Id, invoiceIssueDaysLimit)
		}
		if util.StrInArray(order.Status, &[]string{order_model.ORDER_STATUS_UNPAID, order_model.ORDER_STATUS_PAID}) {
			return nil, fmt.Errorf("Invalid status of order: %s", order.Id)
		}
		orderIdToOrderNumberMap[order.Id.Hex()] = order.Number
	}
	if err := createInvoice(ctx, req, invoiceSetting, orderIdToOrderNumberMap); err != nil {
		return nil, err
	}
	if err := order_model.BatchUpdateInvoiceByIds(ctx, util.ToMongoIds(req.OrderIds), order_model.OrderInvoice{
		Needed:         true,
		Status:         model_order.INVOICE_STATUS_PENDING,
		InvoiceSetting: invoiceSetting.InvoiceSetting,
	}); err != nil {
		log.Warn(ctx, "Failed to update invoices for orders", log.Fields{
			"orderIds": req.OrderIds,
			"errMsg":   err.Error(),
		})
	}
	return &response.EmptyResponse{}, nil
}

func createInvoice(ctx context.Context, req *ec_order.CreateMergedInvoiceRequest, invoiceSetting *setting.InvoiceSetting, orderIdToOrderNumberMap map[string]string) error {
	var (
		invoice     = &order_model.Invoice{}
		mainOrderId string
	)
	if req.Invoice != nil {
		req.Invoice.Name = strings.ReplaceAll(strings.TrimSpace(req.Invoice.Name), " ", "")
		req.Invoice.TaxID = strings.ReplaceAll(strings.TrimSpace(req.Invoice.TaxID), " ", "")
		if req.Invoice.InvoiceType == "" {
			req.Invoice.InvoiceType = "general"
		}
	}
	copier.Instance(nil).From(req.Invoice).CopyTo(invoice)
	if err := invoice.Valid(); err != nil {
		return err
	}
	invoice.MemberId = bson.ObjectIdHex(req.MemberId)
	mainOrderId = req.OrderIds[0]
	if len(req.OrderIds) > 1 {
		invoice.MergedOrderIds = util.ToMongoIds(util.RemoveStrArrayElem(req.OrderIds, mainOrderId))
		for orderId, number := range orderIdToOrderNumberMap {
			if orderId == mainOrderId {
				continue
			}
			invoice.MergedOrderNumbers = append(invoice.MergedOrderNumbers, number)
		}
	}
	invoice.InvoiceSetting = invoiceSetting.InvoiceSetting
	invoice.OrderId = bson.ObjectIdHex(mainOrderId)
	invoice.OrderNumber = orderIdToOrderNumberMap[mainOrderId]
	invoice.OrderType = order.INVOICE_ORDER_TYPE_ORDER
	invoice.Status = order.INVOICE_STATUS_PENDING
	member, err := GetMember(ctx, req.MemberId)
	if err != nil {
		return err
	}
	invoice.GenerateHistory(fmt.Sprintf("memberId:%s", req.MemberId), bson.M{
		"memberName": member.Name,
	})
	if err := invoice.Insert(ctx); err != nil {
		return err
	}
	return nil
}
