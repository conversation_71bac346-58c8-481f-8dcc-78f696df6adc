package order

import (
	"fmt"
	"os"
	"strings"

	"mairpc/core/component"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	pb_client "mairpc/proto/client"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/proto/ec/order"
	ec_order "mairpc/proto/ec/order"
	"mairpc/proto/ec/setting"
	"mairpc/service/ec/client"
	model_order "mairpc/service/ec/model/order"
	"mairpc/service/share/component/invoice"
	"mairpc/service/share/component/oss"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

const (
	EC_CHECK_INVOICE_STATUS_LOCK_KEY = "%s:ec:check-invoice-status" // ${accountId}:check-invoice-status
	EC_ISSUE_RED_INVOICE_LOCK_KEY    = "%s:ec:issue-red-invoice:%s" // ${accountId}:issue-red-invoice:${blueInvoiceId}
)

func (self OrderService) CheckInvoiceStatus(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	key := fmt.Sprintf(EC_CHECK_INVOICE_STATUS_LOCK_KEY, util.GetAccountId(ctx))
	ok, _ := extension.RedisClient.SetNX(key, "true", 10*60) // TODO job 执行时间超过锁时间会导致锁失效
	if !ok {
		return &response.EmptyResponse{}, nil
	}
	component.GO(ctx, func(ctx context.Context) {
		defer extension.RedisClient.Del(key)
		listInvoicesRequest := &order.ListInvoicesRequest{
			ListCondition:  &request.ListCondition{Page: 1, PerPage: 500, OrderBy: []string{"createdAt"}},
			InvoiceSetting: model_order.INVOICE_SETTING_ONLINE,
			InvoiceType:    model_order.INVOICE_TYPE_GENERAL,
			Status: []string{
				model_order.INVOICE_STATUS_REBILLING,
				model_order.INVOICE_STATUS_REVERSING,
				model_order.INVOICE_STATUS_PENDING,
				model_order.INVOICE_STATUS_INVOICING,
			},
		}
		invoiceSetting, err := client.SettingService.GetInvoiceSetting(ctx, &request.EmptyRequest{})
		if err != nil {
			return
		}
		for {
			listInvoicesResp, err := self.ListInvoices(ctx, listInvoicesRequest)
			if err != nil {
				break
			}
			if len(listInvoicesResp.Items) == 0 {
				break
			}
			var orderIds []string
			for _, item := range listInvoicesResp.Items {
				orderIds = append(orderIds, item.OrderId)
				if len(item.MergedOrderIds) > 0 {
					orderIds = append(orderIds, item.MergedOrderIds...)
				}
			}
			ordersResp, err := self.ListOrders(ctx, &order.ListOrdersRequest{
				Ids:           orderIds,
				ListCondition: &request.ListCondition{Page: 1, PerPage: uint32(len(orderIds))},
				WithoutTotal:  true,
			})
			if err != nil {
				break
			}
			orderMap := core_util.MakeMapper("Id", ordersResp.Items)
			for _, item := range listInvoicesResp.Items {
				isAllOrdersCompleted := true
				invoiceOrderIds := item.MergedOrderIds
				invoiceOrderIds = append(invoiceOrderIds, item.OrderId)
				for _, orderId := range invoiceOrderIds {
					o, ok := orderMap[orderId].(*order.OrderDetail)
					if !ok || o.Status != model_order.ORDER_STATUS_COMPLETED {
						if o != nil && o.Status == model_order.ORDER_STATUS_CANCELED && item.IsRed {
							continue
						}
						isAllOrdersCompleted = false
						break
					}
				}
				if !isAllOrdersCompleted {
					continue
				}
				// rebilling 状态的去申请红字确认单，状态设置 reversing
				if item.Status == model_order.INVOICE_STATUS_REBILLING && self.isRedConfirmationApplicable(ctx, item.Id, item.SerialNo) {
					resp, err := client.OrderService.CreateRedConfirmation(ctx, &ec_order.CreateRedConfirmationRequest{
						BlueInvoiceId: item.Id,
					})
					if err != nil {
						self.UpdateInvoice(ctx, &order.UpdateInvoiceRequest{
							Id:           item.Id,
							Status:       model_order.INVOICE_STATUS_FAILED,
							FailedReason: err.Error(),
						})
						extension.RedisClient.Del(fmt.Sprintf(EC_ISSUE_RED_INVOICE_LOCK_KEY, util.GetAccountId(ctx), item.Id))
					} else {
						self.UpdateInvoice(ctx, &order.UpdateInvoiceRequest{
							Id:             item.Id,
							Status:         model_order.INVOICE_STATUS_REVERSING,
							RedConfirmUuid: resp.RedConfirmUuid,
							SerialNo:       bson.NewObjectId().Hex(), // 重开蓝票时使用新的流水号
						})
					}
				}
				// pending reversing 状态的去开票
				if item.Status == model_order.INVOICE_STATUS_PENDING || item.Status == model_order.INVOICE_STATUS_REVERSING {
					if item.IsRed {
						if !self.isRedConfirmationApplicable(ctx, item.BlueInvoiceId, item.SerialNo) {
							continue
						}
						resp, err := client.OrderService.CreateRedConfirmation(ctx, &ec_order.CreateRedConfirmationRequest{
							BlueInvoiceId: item.BlueInvoiceId,
							InvoiceId:     item.Id,
						})
						if err != nil {
							self.UpdateInvoice(ctx, &order.UpdateInvoiceRequest{
								Id:           item.Id,
								Status:       model_order.INVOICE_STATUS_FAILED,
								FailedReason: err.Error(),
							})
							extension.RedisClient.Del(fmt.Sprintf(EC_ISSUE_RED_INVOICE_LOCK_KEY, util.GetAccountId(ctx), item.BlueInvoiceId))
						} else {
							self.UpdateInvoice(ctx, &order.UpdateInvoiceRequest{
								Id:             item.Id,
								Status:         model_order.INVOICE_STATUS_INVOICING,
								RedConfirmUuid: resp.RedConfirmUuid,
							})
						}
						continue
					}
					_, err := client.OrderService.InvoiceV2(ctx, &order.InvoiceRequest{
						Id:       item.Id,
						OrderId:  item.OrderId,
						MemberId: item.MemberId,
					})
					if err != nil {
						log.Warn(ctx, "Failed to invoice", log.Fields{
							"errMsg":    err.Error(),
							"orderId":   item.OrderId,
							"invoiceId": item.Id,
						})
						continue
					}
				}
				// 开票中的，检查开票状态，开票成功设置已开票
				if item.Status == model_order.INVOICE_STATUS_INVOICING {
					if item.IsRed {
						_, err := client.OrderService.InvoiceV2(ctx, &order.InvoiceRequest{
							Id:       item.Id,
							OrderId:  item.OrderId,
							MemberId: item.MemberId,
						})
						if err != nil {
							log.Warn(ctx, "Failed to issue red invoice", log.Fields{
								"errMsg":         err.Error(),
								"orderId":        item.OrderId,
								"invoiceId":      item.Id,
								"redConfirmUuid": item.RedConfirmUuid,
							})
						}
						component.GO(ctx, func(ctx context.Context) {
							updateBlueInvoiceHistories(ctx, bson.ObjectIdHex(item.BlueInvoiceId), model_order.INVOICE_STATUS_PARTIALLY_REVERSED, item.MemberId)
						})
						continue
					}
					invoiceRequestor := invoice.GetInvoiceClient(invoiceSetting.Provider, invoiceSetting.IsTest)
					var (
						err             error
						downloadRequest = genDownloadRequest(item, invoiceSetting)
						resp            *invoice.DownloadResponse
					)
					if invoiceSetting.Provider == invoice.PROVIDER_BAIWANG {
						resp, err = self.downloadBaiwangInvoice(ctx, item, downloadRequest, invoiceRequestor)
					} else {
						resp, err = self.downloadNuoeInvoice(ctx, item, downloadRequest, invoiceRequestor)
					}
					if err != nil {
						continue
					}
					updateInvoiceFileUrl(ctx, item.Id, resp)
				}
			}
			listInvoicesRequest.ListCondition.Page++
		}
	})
	return &response.EmptyResponse{}, nil
}

func (self OrderService) isRedConfirmationApplicable(ctx context.Context, blueInvoiceId, serialNo string) bool {
	redInvoiceRedisKey := fmt.Sprintf(EC_ISSUE_RED_INVOICE_LOCK_KEY, util.GetAccountId(ctx), blueInvoiceId)
	if ok, _ := extension.RedisClient.SetNX(redInvoiceRedisKey, "1", 60*30); !ok {
		log.Warn(ctx, "Red confirmation exists to be confirmed, Please wait for 30 minutes", log.Fields{
			"blueInvoiceId": blueInvoiceId,
			"serialNo":      serialNo,
		})
		return false
	}
	return true
}

func (self OrderService) downloadBaiwangInvoice(ctx context.Context, item *order.InvoiceResponse, downloadRequest *invoice.DownloadRequest, invoiceRequestor invoice.InvoiceRequestor) (*invoice.DownloadResponse, error) {
	resp, err := invoiceRequestor.Download(ctx, downloadRequest)
	if err != nil {
		if strings.Contains(err.Error(), invoice.BAIWANG_INVOICE_NOT_EXISTS_ERROR) {
			log.Warn(ctx, invoice.BAIWANG_INVOICE_NOT_EXISTS_ERROR, log.Fields{
				"invoiceId":        item.Id,
				"drawerTaxpayerId": downloadRequest.DrawerTaxpayerId,
			})
			return nil, err
		}
		if strings.Contains(err.Error(), invoice.BAIWANG_INVOICE_URL_EMPTY_ERROR) {
			component.GO(ctx, func(ctx context.Context) {
				if err := invoiceRequestor.CreateEInvoiceUrl(ctx, downloadRequest); err != nil {
					log.Warn(ctx, "failed to create baiwang einvoiceUrl", log.Fields{
						"invoiceId":        item.Id,
						"drawerTaxpayerId": downloadRequest.DrawerTaxpayerId,
					})
				}
			})
			return nil, err
		}
		_, rpcErr := self.UpdateInvoice(ctx, &order.UpdateInvoiceRequest{
			Id:           item.Id,
			Status:       model_order.INVOICE_STATUS_FAILED,
			FailedReason: err.Error(),
		})
		if rpcErr != nil {
			log.Warn(ctx, "Failed to update invoice", log.Fields{
				"errMsg":    rpcErr.Error(),
				"orderId":   item.OrderId,
				"invoiceId": item.Id,
			})
		}
		return nil, err
	}
	return resp, nil
}

func (self OrderService) downloadNuoeInvoice(ctx context.Context, item *order.InvoiceResponse, downloadRequest *invoice.DownloadRequest, invoiceRequestor invoice.InvoiceRequestor) (*invoice.DownloadResponse, error) {
	_, err := invoiceRequestor.Download(ctx, downloadRequest)
	if err != nil {
		_, rpcErr := self.UpdateInvoice(ctx, &order.UpdateInvoiceRequest{
			Id:           item.Id,
			Status:       model_order.INVOICE_STATUS_FAILED,
			FailedReason: err.Error(),
		})
		if rpcErr != nil {
			log.Warn(ctx, "Failed to update invoice", log.Fields{
				"errMsg":    err.Error(),
				"orderId":   item.OrderId,
				"invoiceId": item.Id,
			})
		}
		return nil, err
	}
	downloadRequest.PdfXzfs = "2"
	resp, err := invoiceRequestor.Download(ctx, downloadRequest)
	if err != nil {
		log.Warn(ctx, "Failed to download invoice", log.Fields{
			"errMsg":    err.Error(),
			"resp":      resp,
			"invoiceId": item.Id,
			"orderId":   downloadRequest.OrderInfo.Id,
		})
		return nil, err
	}
	return resp, nil
}

func genDownloadRequest(invoiceDetail *order.InvoiceResponse, invoiceSetting *setting.InvoiceSetting) *invoice.DownloadRequest {
	downloadRequest := &invoice.DownloadRequest{
		PdfXzfs: "0",
		InvoiceRequest: invoice.InvoiceRequest{
			OrderInfo: invoice.OrderInfo{
				Id:     invoiceDetail.OrderId,
				Number: invoiceDetail.OrderNumber,
			},
			Invoice: invoice.Invoice{
				SerialNo: func() string {
					if invoiceDetail.SerialNo == "" {
						return invoiceDetail.Id
					}
					return invoiceDetail.SerialNo
				}(),
				Name: invoiceDetail.Name,
			},
		},
	}
	copier.Instance(nil).From(invoiceSetting.PublicParameters).CopyTo(downloadRequest)
	copier.Instance(nil).From(invoiceSetting.TaxSetting).CopyTo(downloadRequest)
	return downloadRequest
}

func updateInvoiceFileUrl(ctx context.Context, invoiceId string, resp *invoice.DownloadResponse) (string, string) {
	// 上传 pdf
	filePath := fmt.Sprintf("%s.pdf", bson.NewObjectId().Hex())
	out, err := os.Create(filePath)
	if err != nil {
		log.Warn(ctx, "Failed to create file", log.Fields{
			"errMsg":   err.Error(),
			"filePath": filePath,
		})
		return "", ""
	}
	defer out.Close()
	err = util.DownloadTo(filePath, resp.Url)
	if err != nil {
		log.Warn(ctx, "Failed to download pdf", log.Fields{
			"errMsg": err.Error(),
			"resp":   resp,
		})
		return "", ""
	}
	defer os.Remove(filePath)
	key := fmt.Sprintf("%s/modules/ec/invoice/%s/%s", util.GetAccountId(ctx), invoiceId, filePath)
	client := oss.OSSCDNClient(ctx)
	err = client.PutObject(ctx, key, filePath, nil)
	if err != nil {
		log.Warn(ctx, "Failed to put object", log.Fields{
			"errMsg": err.Error(),
		})
		return "", ""
	}

	ossServiceSetting, _ := pb_client.GetAccountServiceClient().GetOssServiceSetting(ctx, &request.EmptyRequest{})
	service := OrderService{}
	_, rpcErr := service.UpdateInvoice(ctx, &order.UpdateInvoiceRequest{
		Id:     invoiceId,
		Status: model_order.INVOICE_STATUS_ISSUED,
		Result: &order.InvoiceResult{
			FileUrl:    fmt.Sprintf("%s/%s", ossServiceSetting.Cdn.Domain, key),
			Code:       resp.Code,
			Number:     resp.Number,
			InvoicedAt: resp.InvoicedAt,
			Amount:     resp.Amount,
		},
	})
	if rpcErr != nil {
		log.Warn(ctx, "Failed to update invoice by status issued", log.Fields{
			"errMsg": rpcErr.Error(),
		})
	}
	return resp.Code, resp.Number
}
