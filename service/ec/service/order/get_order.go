package order

import (
	"context"
	"fmt"
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	pb_ec_order "mairpc/proto/ec/order"
	pb_ec_store "mairpc/proto/ec/store"
	"mairpc/proto/member"
	ec_client "mairpc/service/ec/client"
	ec_model "mairpc/service/ec/model"
	ec_model_order "mairpc/service/ec/model/order"
	ec_order "mairpc/service/ec/model/order"
	ec_store "mairpc/service/ec/model/store"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"
	"sort"
	"time"
)

func (OrderService) GetOrder(ctx context.Context, req *pb_ec_order.GetOrderRequest) (*pb_ec_order.OrderDetail, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	if req.Id == "" && req.Number == "" && req.TradeNo == "" {
		return nil, errors.NewNotExistsError("Id and number and tradeNo can't both be empty.")
	}
	condition := share_model.Base.GenDefaultCondition(ctx)

	if req.Id != "" {
		condition["_id"] = bson.ObjectIdHex(req.Id)
	}
	if req.Number != "" {
		condition["number"] = req.Number
	}
	if req.MemberId != "" {
		condition["memberId"] = bson.ObjectIdHex(req.MemberId)
	}
	if req.TradeNo != "" {
		condition["tradeNo"] = req.TradeNo
	}
	order := &ec_model_order.Order{}
	err := share_model.Base.GetByCondition(ctx, condition, ec_model_order.C_ORDER, order)
	if err != nil {
		if req.Id != "" {
			delete(condition, "_id")
			condition["outTradeId"] = bson.ObjectIdHex(req.Id)
			err := share_model.Base.GetByCondition(ctx, condition, ec_model_order.C_ORDER, order)
			if err != nil {
				return nil, errors.NewNotExistsError("order")
			}
		}
		return nil, errors.NewNotExistsError("order")
	}
	store, err := ec_store.CStore.GetByIdContainDeleted(ctx, order.StoreId)
	if err != nil {
		return nil, errors.NewNotExistsError("storeId")
	}

	settingArrayMap, err := getOrderProductInterfaceSettings(ctx, []ec_model_order.Order{*order})
	if err != nil {
		return nil, err
	}
	orderDetail := formatCityExpressStore(ctx, formatOrderDetailWithStore(ctx, order, store, settingArrayMap[order.Id]))
	if len(req.ExtraFields) > 0 && util.StrInArray("Member", &req.ExtraFields) {
		formatMemberInOrderDetail(ctx, orderDetail)
	}
	if !order.PaidAt.IsZero() && order.Status == ec_model_order.ORDER_STATUS_CANCELED && order.RefundStatus == ec_model_order.ORDER_REFUND_STATUS_REFUNDED {
		orderDetail.IsDeliveryFeeRefunded = true
	}
	if orderDetail.PickupNumber != "" {
		if order.Method == ec_order.ORDER_DELIVERY_METHOD_CITY_EXPRESS {
			if order.Logistics.ExpectDeliveryAt.Day() != time.Now().Day() {
				orderDetail.PickupNumberWithPickupDate = fmt.Sprintf("%s（%s）", orderDetail.PickupNumber, order.Logistics.ExpectDeliveryAt.Format("0102"))
			} else {
				orderDetail.PickupNumberWithPickupDate = orderDetail.PickupNumber
			}
		} else {
			// 如果是非当天的预约单，需要拼上日期
			if order.Reservation.MemberTime.Day() != time.Now().Day() {
				orderDetail.PickupNumberWithPickupDate = fmt.Sprintf("%s（%s）", orderDetail.PickupNumber, order.Reservation.MemberTime.Format("0102"))
			} else {
				orderDetail.PickupNumberWithPickupDate = orderDetail.PickupNumber
			}
		}
	}
	return orderDetail, nil
}

func formatCityExpressStore(ctx context.Context, orderDetail *pb_ec_order.OrderDetail) *pb_ec_order.OrderDetail {
	storeDetail, err := ec_client.StoreService.GetStore(ctx, &request.DetailRequest{Id: orderDetail.Store.Id})
	if err != nil {
		log.Warn(ctx, "store not exist", log.Fields{})
		return orderDetail
	}
	orderDetail.Store.IsCityExpressStore = storeDetail.IsCityExpressStore
	orderDetail.Store.CityExpressSetting = storeDetail.CityExpressSetting
	return orderDetail
}

func formatMemberInOrderDetail(ctx context.Context, orderDetail *pb_ec_order.OrderDetail) {
	memberDetail, err := client.GetMemberServiceClient().GetMember(ctx, &member.MemberDetailRequest{
		Id: orderDetail.MemberId,
	})
	if err != nil {
		return
	}
	orderDetail.Member = &pb_ec_order.OrderMember{
		Id:    memberDetail.Id,
		Name:  memberDetail.Name,
		Phone: memberDetail.Phone,
	}
}

func formatOrderProductSetting(orderDetail *pb_ec_order.OrderDetail, settings []interface{}) error {
	for index, product := range orderDetail.Products {
		for _, setting := range settings {
			if product.Id == setting.(*ec_model_order.OrderProductSetting).ProductId.Hex() {
				orderProductSettingDetail := &pb_ec_order.RedeemSetting{}
				copier.Instance(nil).From(setting.(*ec_model_order.OrderProductSetting).RedeemedSetting).CopyTo(&orderProductSettingDetail)
				orderDetail.Products[index].RedeemSetting = orderProductSettingDetail
			}
		}
	}
	return nil
}

func formatVirtualProductPickupCode(order *ec_model_order.Order, orderDetail *pb_ec_order.OrderDetail) {
	for _, product := range order.Products {
		redeemedCodeMap := map[interface{}]interface{}{}
		if len(product.Logistics) > 0 {
			redeemedCodeMap = core_util.MakeMapper("PickupCode", product.Logistics)
		}
		for index, productDetail := range orderDetail.Products {
			if product.OutTradeId.Hex() == productDetail.OutTradeId {
				codeDetails := []*pb_ec_order.PickupCodeDetail{}
				for _, pickupCode := range product.PickupCodes {
					if redeemedCode, ok := redeemedCodeMap[pickupCode]; ok {
						codeDetails = append(codeDetails, formatUsedCode(redeemedCode.(ec_model_order.ProductLogisticsInfo)))
					} else {
						if len(product.PickupCodeRedeemPeriods) > 0 {
							for _, period := range product.PickupCodeRedeemPeriods {
								if period.Code != pickupCode {
									continue
								}
								if period.Status == ec_model_order.PICKUP_CODE_STATUS_USED {
									continue
								}
								codeDetail := &pb_ec_order.PickupCodeDetail{
									Code:   period.Code,
									Status: period.Status,
								}
								codeDetails = append(codeDetails, codeDetail)
								break
							}
						} else {
							codeDetail := &pb_ec_order.PickupCodeDetail{
								Code:   pickupCode,
								Status: ec_model_order.PICKUP_CODE_STATUS_UNUSED,
							}
							codeDetails = append(codeDetails, codeDetail)
						}
					}
				}
				for _, codeDetail := range codeDetails {
					for _, codeRedeemPeriod := range product.PickupCodeRedeemPeriods {
						if codeDetail.Code != codeRedeemPeriod.Code {
							continue
						}
						codeDetail.RedeemPeriod = &pb_ec_order.PickupCodeRedeemPeriod{
							Type:    product.RedeemPeriod.Type,
							StartAt: codeRedeemPeriod.StartAt.Format(time.RFC3339),
							EndAt:   codeRedeemPeriod.EndAt.Format(time.RFC3339),
							Status:  codeRedeemPeriod.Status,
						}
					}
				}
				sortPickupCode(codeDetails)
				orderDetail.Products[index].PickupCodes = codeDetails
			}
		}
	}
}

func sortPickupCode(codeDetails []*pb_ec_order.PickupCodeDetail) {
	sort.Slice(codeDetails, func(i, j int) bool {
		return codeDetails[i].Status < codeDetails[j].Status
	})
}

func formatUsedCode(redeemedCode ec_model_order.ProductLogisticsInfo) *pb_ec_order.PickupCodeDetail {
	codeDetail := &pb_ec_order.PickupCodeDetail{
		Code:       redeemedCode.PickupCode,
		Status:     ec_model_order.PICKUP_CODE_STATUS_USED,
		RedeemedAt: core_util.FormatTimeToRFC3339(redeemedCode.ShippedAt),
	}
	copier.Instance(nil).From(redeemedCode.ProcessBy).CopyTo(codeDetail)
	return codeDetail
}

func formatOrderDetailWithStore(ctx context.Context, order *ec_model_order.Order, store ec_store.Store, settings []interface{}) *pb_ec_order.OrderDetail {
	orderDetail := formatOrderDetail(ctx, order)
	orderDetail.Reservation.Time = ec_model.FormatOrderReservation(order.Reservation.Time, store)
	orderDetail.Reservation.MemberTime = ec_model.FormatOrderReservation(order.Reservation.MemberTime, store)
	storeDetail := &pb_ec_store.StoreDetail{}
	core_util.CopyRFC3339(store, storeDetail)
	orderDetail.Store = storeDetail
	tradeRecords := []*pb_ec_order.TradeRecord{}
	copier.Instance(nil).From(order.TradeRecords).CopyTo(&tradeRecords)
	orderDetail.TradeRecords = tradeRecords
	orderDetail.Type = order.GetVirtualType()
	if order.IsVirtualType() {
		if settings == nil {
			log.Warn(ctx, "Virtual product order setting can not empty", log.Fields{
				"order": order,
			})
			return orderDetail
		}
		err := formatOrderProductSetting(orderDetail, settings)
		if err != nil {
			log.Warn(ctx, "Format orderProductSetting failed", log.Fields{
				"order":  order,
				"errMsg": err,
			})
			return orderDetail
		}
		formatVirtualProductPickupCode(order, orderDetail)
	}
	return orderDetail
}
