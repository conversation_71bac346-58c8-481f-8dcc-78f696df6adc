package order

import (
	"context"
	"mairpc/core/extension"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	pb_distribution "mairpc/proto/ec/distribution"
	"mairpc/proto/ec/order"
	ec_client "mairpc/service/ec/client"
	ec_model_order "mairpc/service/ec/model/order"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"

	"github.com/spf13/cast"
)

func (OrderService) GetPromoterOrderAmount(ctx context.Context, req *order.GetPromoterOrderAmountRequest) (*order.GetPromoterOrderAmountResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if req.StaffId != "" {
		req.StaffIds = []string{req.StaffId}
	}
	promoterType := "staff"
	listPromotersResp, err := ec_client.DistributionService.ListPromoters(ctx, &pb_distribution.ListPromotersRequest{
		ListCondition: &request.ListCondition{Page: 1, PerPage: uint32(len(req.StaffIds))},
		StaffIds:      req.StaffIds,
		Type:          promoterType,
	})
	if err != nil {
		return nil, err
	}
	promoterIds := core_util.ExtractArrayStringField("Id", listPromotersResp.Items)

	queryContext := core_util.CtxWithReadSecondaryPreferred(ctx)

	totalAmount := getOrderTotalAmount(queryContext, req, promoterIds, promoterType)
	saleAmount := getOrderSaleAmount(queryContext, req, promoterIds, promoterType)

	condition := share_model.Base.GenDefaultCondition(ctx)
	condition["distribution.promoterId"] = bson.M{"$in": util.ToMongoIds(promoterIds)}
	condition["distribution.promoterType"] = promoterType
	condition["paidAt"] = bson.M{
		"$exists": true,
	}
	if req.StoreId != "" {
		condition["storeId"] = bson.ObjectIdHex(req.StoreId)
	}

	totalCompletedOrder, err := getCompletedOrderTotal(queryContext, condition)
	if err != nil {
		return nil, err
	}

	totalRefundedOrder, err := getRefundedOrderTotal(queryContext, condition)
	if err != nil {
		return nil, err
	}

	totalOrders := 0
	for _, promoter := range listPromotersResp.Items {
		totalOrders += int(promoter.TotalOrders)
	}
	if req.StoreId != "" {
		totalOrders, err = getOrderTotal(queryContext, condition)
		if err != nil {
			return nil, err
		}
	}
	return &order.GetPromoterOrderAmountResponse{
		TotalAmount:         totalAmount,
		SaleAmount:          saleAmount,
		TotalOrder:          uint64(totalOrders),
		TotalCompletedOrder: uint64(totalCompletedOrder),
		TotalRefundedOrder:  uint64(totalRefundedOrder),
	}, nil
}

func getCompletedOrderTotal(ctx context.Context, condition bson.M) (int, error) {
	condition["status"] = ec_model_order.ORDER_STATUS_COMPLETED
	defer delete(condition, "status")
	return extension.DBRepository.Count(ctx, ec_model_order.C_ORDER, condition)
}

func getOrderTotal(ctx context.Context, condition bson.M) (int, error) {
	return extension.DBRepository.Count(ctx, ec_model_order.C_ORDER, condition)
}

func getRefundedOrderTotal(ctx context.Context, condition bson.M) (int, error) {
	condition["status"] = ec_model_order.ORDER_STATUS_CANCELED
	defer delete(condition, "status")
	return extension.DBRepository.Count(ctx, ec_model_order.C_ORDER, condition)
}

// 统计推广金额（全部订单）
func getOrderTotalAmount(ctx context.Context, req *order.GetPromoterOrderAmountRequest, promoterIds []string, promoterType string) uint64 {
	var result = []bson.M{}
	pipeline := []bson.M{
		bson.M{
			"$match": bson.M{
				"accountId":                 util.GetAccountIdAsObjectId(ctx),
				"isDeleted":                 false,
				"distribution.promoterId":   bson.M{"$in": util.ToMongoIds(promoterIds)},
				"distribution.promoterType": promoterType,
				"paidAt":                    bson.M{"$exists": true},
			},
		},
		bson.M{
			"$group": bson.M{
				"_id":         "$distribution.promoterId",
				"totalAmount": bson.M{"$sum": "$payAmount"},
			},
		},
	}

	if req.DateRange != nil {
		pipeline[0]["$match"].(bson.M)["createdAt"] = util.ParseStringDateRange(req.DateRange)
	}

	if req.StoreId != "" {
		pipeline[0]["$match"].(bson.M)["storeId"] = bson.ObjectIdHex(req.StoreId)
	}
	if extension.DBRepository.Aggregate(ctx, ec_model_order.C_ORDER, pipeline, false, &result) != nil {
		return 0
	}
	totalAmount := uint64(0)
	for _, item := range result {
		totalAmount += cast.ToUint64(item["totalAmount"])
	}
	return totalAmount
}

// 获取销售金额（已付款订单），需扣除邮费和已退款金额
func getOrderSaleAmount(ctx context.Context, req *order.GetPromoterOrderAmountRequest, promoterIds []string, promoterType string) uint64 {
	var result = bson.M{}
	pipeline := []bson.M{
		bson.M{
			"$match": bson.M{
				"accountId":                 util.GetAccountIdAsObjectId(ctx),
				"isDeleted":                 false,
				"distribution.promoterId":   bson.M{"$in": util.ToMongoIds(promoterIds)},
				"distribution.promoterType": promoterType,
				"paidAt":                    bson.M{"$exists": true},
				"status":                    bson.M{"$ne": ec_model_order.ORDER_REFUND_STATUS_CANCELED},
			},
		},
		bson.M{
			"$group": bson.M{
				"_id":         "",
				"payAmount":   bson.M{"$sum": "$payAmount"},
				"deliveryFee": bson.M{"$sum": "$logistics.fee"},
			},
		},
	}

	if req.DateRange != nil {
		pipeline[0]["$match"].(bson.M)["createdAt"] = util.ParseStringDateRange(req.DateRange)
	}

	if req.StoreId != "" {
		pipeline[0]["$match"].(bson.M)["storeId"] = bson.ObjectIdHex(req.StoreId)
	}

	if extension.DBRepository.Aggregate(ctx, ec_model_order.C_ORDER, pipeline, true, &result) != nil {
		return 0
	}
	// 获取到所有未取消订单的支付金额和邮费总和
	payAmount := cast.ToUint64(result["payAmount"])
	deliveryFee := cast.ToUint64(result["deliveryFee"])
	// 销售额需要减去邮费
	saleAmount := payAmount - deliveryFee

	// 还存在部分订单部分退款也需要将退款金额计算出来扣除
	saleAmount = saleAmount - getPromoterRefundSaleAmount(ctx, req, promoterIds)

	return saleAmount
}

func getPromoterRefundSaleAmount(ctx context.Context, req *order.GetPromoterOrderAmountRequest, promoterIds []string) uint64 {
	condition := bson.M{
		"accountId":               util.GetAccountIdAsObjectId(ctx),
		"isDeleted":               false,
		"distribution.promoterId": bson.M{"$in": promoterIds},
		"histories.status":        ec_model_order.ORDER_STATUS_PAID,
		"refundStatus": bson.M{
			"$gt": "",
		},
		"status": bson.M{"$ne": ec_model_order.ORDER_REFUND_STATUS_CANCELED},
	}
	if req.DateRange != nil {
		condition["createdAt"] = util.ParseStringDateRange(req.DateRange)
	}

	if req.StoreId != "" {
		condition["storeId"] = bson.ObjectIdHex(req.StoreId)
	}
	orderIds, _ := ec_model_order.COrder.DistinctIds(ctx, condition)
	if len(orderIds) == 0 {
		return 0
	}
	pipeline := []bson.M{
		bson.M{
			"$match": bson.M{
				"accountId": util.GetAccountIdAsObjectId(ctx),
				"isDeleted": false,
				"orderId":   bson.M{"$in": orderIds},
				"status": bson.M{
					"$in": []string{
						ec_model_order.ORDER_REFUND_STATUS_PENDING,
						ec_model_order.ORDER_REFUND_STATUS_REFUNDING,
						ec_model_order.ORDER_REFUND_STATUS_REFUNDED,
						ec_model_order.ORDER_REFUND_STATUS_FAILED,
					},
				},
			},
		},
		bson.M{
			"$group": bson.M{
				"_id":          "",
				"refundAmount": bson.M{"$sum": "$refundAmount"},
				"expressFee":   bson.M{"$sum": "$expressFee"},
			},
		},
	}
	var result = bson.M{}
	if extension.DBRepository.Aggregate(ctx, ec_model_order.C_ORDER_REFUND, pipeline, true, &result) != nil {
		return 0
	}
	refundAmount := cast.ToUint64(result["refundAmount"])
	expressFee := cast.ToUint64(result["expressFee"])
	return refundAmount - expressFee
}
