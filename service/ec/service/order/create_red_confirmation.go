package order

import (
	"errors"
	core_errors "mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/openapi/business/util"
	"mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	"mairpc/proto/common/types"
	pb_ec_order "mairpc/proto/ec/order"
	"mairpc/proto/ec/setting"
	"mairpc/service/ec/client"
	order_model "mairpc/service/ec/model/order"
	share_invoice "mairpc/service/share/component/invoice"
	share_util "mairpc/service/share/util"

	"golang.org/x/net/context"
)

// 申请发票红字确认单（目前仅支持百望）
func (self OrderService) CreateRedConfirmation(ctx context.Context, req *pb_ec_order.CreateRedConfirmationRequest) (*pb_ec_order.CreateRedConfirmationResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	invoiceSetting, err := client.SettingService.GetInvoiceSetting(ctx, &request.EmptyRequest{})
	if err != nil {
		return nil, err
	}
	if invoiceSetting.InvoiceSetting != order_model.INVOICE_SETTING_ONLINE || invoiceSetting.Provider != share_invoice.PROVIDER_BAIWANG {
		return nil, errors.New("red confirm cannot be created, please check your setting")
	}
	invoice, err := order_model.CInvoice.GetById(ctx, bson.ObjectIdHex(req.BlueInvoiceId))
	if err != nil {
		return nil, err
	}
	// 发票重开时使用原蓝票信息申请红字确认单
	statusList := core_util.ExtractArrayFieldV2("Status", "", invoice.Histories)
	if !util.StrInArray(order_model.INVOICE_STATUS_ISSUED, &statusList) && !util.StrInArray(order_model.INVOICE_STATUS_INVOICING, &statusList) {
		return nil, errors.New("blue invoice have never been issued")
	}
	blueInvoiceSerialNo := invoice.SerialNo
	// 非重开时使用具体红票信息申请红字确认单
	if req.InvoiceId != "" {
		invoice, err = order_model.CInvoice.GetById(ctx, bson.ObjectIdHex(req.InvoiceId))
		if err != nil {
			return nil, err
		}
	}
	invoiceClient := share_invoice.GetInvoiceClient(invoiceSetting.Provider, invoiceSetting.IsTest)
	if invoiceClient == nil {
		return nil, core_errors.NewInvalidArgumentError("invoiceSetting")
	}
	if !invoice.IsRed && invoice.Status != order_model.INVOICE_STATUS_DELETED {
		listRedInvoicesResp, _ := client.OrderService.ListInvoices(ctx, &pb_ec_order.ListInvoicesRequest{
			BlueInvoiceId: invoice.Id.Hex(),
		})
		if listRedInvoicesResp != nil && len(listRedInvoicesResp.Items) > 0 {
			// 发票部分商品红冲后申请重开，需要重置商品信息
			invoiceItems, err := handleRebillingInvoiceItems(ctx, invoice)
			if err != nil {
				return nil, err
			}
			invoice.InvoiceItems = *invoiceItems
		}
	}
	request, err := getCreateRedConfirmationRequest(ctx, blueInvoiceSerialNo, invoice, invoiceSetting)
	if err != nil {
		return nil, err
	}
	resp, err := invoiceClient.CreateRedConfirmation(ctx, request)
	if err != nil {
		return nil, err
	}
	return &pb_ec_order.CreateRedConfirmationResponse{
		RedConfirmUuid: resp.RedConfirmUuid,
	}, nil
}

func getCreateRedConfirmationRequest(
	ctx context.Context,
	blueInvoiceSerialNo string,
	invoice order_model.Invoice,
	invoiceSetting *setting.InvoiceSetting,
) (*share_invoice.CreateRedConfirmationRequest, error) {
	member, _ := GetMember(ctx, invoice.MemberId.Hex())
	var invoiceProducts []share_invoice.InvoiceProduct
	copier.Instance(nil).From(invoice.InvoiceItems.InvoiceProducts).CopyTo(&invoiceProducts)
	request := &share_invoice.CreateRedConfirmationRequest{
		InvoiceRequest: share_invoice.InvoiceRequest{
			OrderInfo: share_invoice.OrderInfo{
				Id:          invoice.OrderId.Hex(),
				Number:      invoice.OrderNumber,
				DeliveryFee: invoice.InvoiceItems.DeliveryFee,
			},
			Invoice: share_invoice.Invoice{
				Name: invoice.Name,
				Phone: func() string {
					if member != nil {
						return member.Phone
					}
					return invoice.Phone
				}(),
				Type:        invoice.Type,
				TaxID:       invoice.TaxID,
				Email:       invoice.Email,
				Address:     invoice.Address,
				BankName:    invoice.BankName,
				BankAccount: invoice.BankAccount,
				Status:      invoice.Status,
				IsRed:       invoice.IsRed,
			},
			Products: invoiceProducts,
		},
		BlueInvoiceSerialNo: blueInvoiceSerialNo,
	}
	if invoice.InvoiceItems.DeliveryFee > 0 {
		resp, err := client.SettingService.ListInvoiceTemplates(ctx, &setting.ListInvoiceTemplatesRequest{
			Type: "deliveryFee",
		})
		if err != nil || resp == nil || len(resp.Items) == 0 {
			return nil, core_errors.NewInvalidArgumentError("deliveryFeeTemplate")
		}
		deliveryFeeTemplate := resp.Items[0]
		request.DeliveryFeeTemplate = &ec.InvoiceTemplate{
			TaxClassificationCode: deliveryFeeTemplate.TaxClassificationCode,
			TaxClassificationName: deliveryFeeTemplate.TaxClassificationName,
			FavorablePolicy:       deliveryFeeTemplate.FavorablePolicy,
			TaxRate:               deliveryFeeTemplate.TaxRate,
			Name:                  deliveryFeeTemplate.Name,
		}
	}
	copier.Instance(nil).From(invoiceSetting.PublicParameters).CopyTo(request)
	copier.Instance(nil).From(invoiceSetting.TaxSetting).CopyTo(request)
	return request, nil
}

func handleRebillingInvoiceItems(ctx context.Context, invoice order_model.Invoice) (*order_model.InvoiceItems, error) {
	orderIds := invoice.MergedOrderIds
	orderIds = append(orderIds, invoice.OrderId)
	orderRefunds, err := client.OrderService.ListOrderRefunds(ctx, &pb_ec_order.ListOrderRefundsRequest{
		OrderIds: share_util.MongoIdsToStrs(orderIds),
		Status:   []string{"approved", "returned", "pending", "refunding", "failed", "refunded"},
	})
	if err != nil {
		log.Warn(ctx, "Failed to call ListOrderRefunds", log.Fields{"orderIds": orderIds, "errMsg": err.Error()})
	}
	var (
		invoiceItems                   = &order_model.InvoiceItems{}
		orderProducts                  []*order_model.OrderProduct
		invoiceTemplateIds, productIds []string
	)
	for _, invoiceProduct := range invoice.InvoiceItems.InvoiceProducts {
		orderProducts = append(orderProducts, invoiceProduct.OrderProduct)
		invoiceTemplateIds = append(invoiceTemplateIds, invoiceProduct.InvoiceTemplate.Id.Hex())
		productIds = append(productIds, invoiceProduct.OrderProduct.Id.Hex())
	}
	productsResp, _ := client.ProductService.ListProducts(ctx, &ec.ListProductsRequest{
		EcProductIds: productIds,
		ExistsInvoiceTemplateId: &types.BoolValue{
			Value: true,
		},
		ContainDeleted: true,
		ListCondition:  &request.ListCondition{Page: 1, PerPage: uint32(len(productIds))},
	})
	resp, _ := client.SettingService.ListInvoiceTemplates(ctx, &setting.ListInvoiceTemplatesRequest{
		Ids:           invoiceTemplateIds,
		ListCondition: &request.ListCondition{Page: 1, PerPage: uint32(len(invoiceTemplateIds))},
	})
	invoiceTemplateMap := core_util.MakeMapper("Id", resp.Items)
	invoiceProducts := genInvoiceProducts(ctx, orderProducts, productsResp.Items, orderRefunds, invoiceTemplateMap, invoice.InvoiceContents)
	copier.Instance(nil).From(invoiceProducts).CopyTo(&invoiceItems.InvoiceProducts)
	invoice.InvoiceItems.InvoiceProducts = invoiceItems.InvoiceProducts
	if err := invoice.Update(ctx); err != nil {
		return nil, err
	}
	return invoiceItems, err
}
