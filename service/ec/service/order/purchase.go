package order

import (
	"encoding/json"
	"fmt"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	proto_client "mairpc/proto/client"
	common "mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	mairpc_common_types "mairpc/proto/common/types"
	pb_distribution "mairpc/proto/ec/distribution"
	"mairpc/proto/ec/marketing"
	pb_marketing "mairpc/proto/ec/marketing"
	pb_order "mairpc/proto/ec/order"
	pb_ec_product "mairpc/proto/ec/product"
	pb_setting "mairpc/proto/ec/setting"
	pb_store "mairpc/proto/ec/store"
	"mairpc/proto/ec/wallet"
	pb_eccampaign_discount_campaign "mairpc/proto/eccampaign/discountCampaign"
	pb_eccampaign_plus_buy "mairpc/proto/eccampaign/plusBuy"
	pb_eccampaign_random_discount "mairpc/proto/eccampaign/randomDiscount"
	pb_member "mairpc/proto/member"
	pb_product "mairpc/proto/product"
	"mairpc/service/ec/client"
	ec_client "mairpc/service/ec/client"
	ec_share_model "mairpc/service/ec/model"
	distribution_model "mairpc/service/ec/model/distribution"
	marketing_model "mairpc/service/ec/model/marketing"
	"mairpc/service/ec/model/order"
	ec_order "mairpc/service/ec/model/order"
	ec_product "mairpc/service/ec/model/product"
	"mairpc/service/ec/model/setting"
	setting_model "mairpc/service/ec/model/setting"
	ec_store "mairpc/service/ec/model/store"
	ec_store_product "mairpc/service/ec/model/storeProduct"
	ec_store_warehouse "mairpc/service/ec/model/storeWarehouse"
	ec_wallet "mairpc/service/ec/model/wallet"
	share_service "mairpc/service/ec/service"
	mall_service "mairpc/service/ec/service/mall"
	"mairpc/service/ec/share"
	optional_package "mairpc/service/eccampaign/model/optionalPackage"
	plus_buy "mairpc/service/eccampaign/model/plusBuy"
	eccampaign_share "mairpc/service/eccampaign/share"
	"mairpc/service/share/constant"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/spf13/cast"

	"mairpc/core/extension/bson"

	"golang.org/x/net/context"
)

const (
	MEMBER_PRIVILEGE_DISCOUNT      = "消费折扣"
	MEMBER_PRIVILEGE_FREE_SHIPPING = "包邮"

	OPERATOR_DEDUCT   = "deduct"
	OPERATOR_ROLLBACK = "rollback"

	PROVINCE_FORMATTION = `(省|市|(壮族|回族|维吾尔)?自治区|特别行政区)$`
)

func (OrderService) Purchase(ctx context.Context, req *common.PurchaseRequest) (*pb_order.OrderDetail, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	builder, err := GenerateBuilder(ctx, req, true, true)
	if err != nil {
		return nil, err
	}

	return builder.Create(ctx)
}

// 生成预售活动交易记录
func generateOrderTradeRecords(order *ec_order.Order, builder *OrderBuilder) {
	if builder.CampaignConfig == nil {
		return
	}
	if !builder.IsDiscountEnable(DISCOUNT_TYPE_CAMPAIGN) {
		return
	}
	for _, c := range builder.CampaignConfig.Campaigns {
		tradeRecords := []ec_order.TradeRecord{}
		copier.Instance(nil).From(c.TradeRecords).CopyTo(&tradeRecords)
		order.TradeRecords = append(order.TradeRecords, tradeRecords...)
	}
}

// 检查门店状态
func checkStore(ctx context.Context, builder *OrderBuilder) error {
	// 查询门店详情
	store, err := builder.GetStore(ctx)
	if err != nil {
		return err
	}
	// 是否忽略门店状态
	if !builder.IsIgnoreStoreStatus {
		// 如果不是代销订单，判断代销小店关联的门店的状态是否开启
		if !builder.isMall && store.Status != ec_store.STORE_STATUS_OPEN {
			return errors.NewInvalidArgumentError("storeId")
		}
	}
	return nil
}

// 计算优惠券
func calcOrderDiscount(ctx context.Context, builder *OrderBuilder, order *ec_order.Order, member *pb_member.MemberDetailResponse, couponTypes []string, redeem bool) error {
	// 黑名单用户不能使用优惠券
	if member == nil || member.BlockedStatus == 2 {
		return nil
	}
	if len(builder.DiscountIds) == 0 {
		return nil
	}
	return generateDiscountInfo(ctx, order, builder, couponTypes, redeem)
}

// 计算使用兑换券
func calcExchangeDiscount(ctx context.Context, builder *OrderBuilder, order *ec_order.Order, member *pb_member.MemberDetailResponse, redeem bool) error {
	if member == nil || member.BlockedStatus == 2 {
		return nil
	}
	if len(builder.DiscountIds) == 0 {
		return nil
	}
	// 根据传入的 discountIds 查询客户优惠券
	membershipDiscounts, err := builder.GetMembershipDiscounts(ctx)
	if err != nil {
		return err
	}
	// 检查优惠券是否能叠加使用
	err = checkMembershipDiscounts(membershipDiscounts)
	if err != nil {
		return err
	}
	// 获取门店详情
	store, err := builder.GetStore(ctx)
	if err != nil {
		return err
	}
	utm := &mairpc_common_types.Utm{}
	copier.Instance(nil).From(order.Utm).CopyTo(&utm)

	for _, m := range membershipDiscounts {
		if m.Coupon.Type != ec_order.COUPON_TYPE_EXCHANGE {
			continue
		}
		// 检查优惠券是否能正常使用
		if err := checkMembershipDiscount(order, m, store); err != nil {
			return err
		}
		calcDiscount(ctx, order, m, builder)
		// 兑换券使用记录记录兑换商品
		couponProduct := getCouponProduct(order, m)
		if redeem {
			couponDiscountAmount := getDiscountOrReduction(m, order)
			err = RedeemCoupon(ctx, m.Code, order.Id.Hex(), order.Number, order.StoreId.Hex(), couponProduct, utm, couponDiscountAmount)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// 计算储值卡
func calcPrepaidCard(ctx context.Context, builder *OrderBuilder, order *ec_order.Order) error {
	if builder.member.BlockedStatus == 2 {
		builder.PrepaidCardIds = []string{}
	}
	if len(builder.PrepaidCardIds) == 0 {
		return nil
	}
	if isPrepaidCardDisabled(order) {
		if !builder.isCreateOrder && builder.autoSelectDiscount {
			return nil
		}
		return errors.NewInvalidArgumentErrorWithMessage("prepaidCardIds", "can not use prepaidCard")
	}
	if builder.usePrepaidCardFirst && len(builder.prioritizedPrepaidCardIds) > 0 {
		builder.PrepaidCardIds = builder.prioritizedPrepaidCardIds
	}
	prepaidCardMap, err := builder.GetPrepaidCardMap(ctx, builder.PrepaidCardIds)
	if err != nil {
		return err
	}
	for _, prepaidCardId := range builder.PrepaidCardIds {
		prepaidCard, ok := prepaidCardMap[prepaidCardId].(*wallet.PrepaidCard)
		if !ok {
			return errors.NewInvalidArgumentError("PrepaidCardIds")
		}
		prepaidCardAvailable := true
		// 若任一商品不能使用当前的储值卡，那么跳过
		for _, product := range order.Products {
			for _, limit := range product.PrepaidCardLimits {
				if !limit.CanUsePaidCard(prepaidCard.StoredValueCardId) {
					prepaidCardAvailable = false
				}
			}
			if !OrderCanUsePrepaidCard(ctx, order, prepaidCard) {
				prepaidCardAvailable = false
			}
		}
		if !prepaidCardAvailable {
			continue
		}
		err = checkPrepaidCard(prepaidCard, order)
		if err != nil {
			return err
		}
		var deductAmount uint64
		if uint64(prepaidCard.Amount) >= order.PayAmount-order.Logistics.Fee {
			deductAmount = order.PayAmount - order.Logistics.Fee
		} else {
			deductAmount = uint64(prepaidCard.Amount)
		}
		if deductAmount == 0 {
			// 已经抵扣到 0
			continue
		}
		var tradeNo, outTradeSeq, transactionId string
		if builder.isCreateOrder {
			store, _ := builder.GetStore(ctx)
			tradeNo, outTradeSeq, transactionId, err = deductPrepaidCard(ctx, order, prepaidCardId, deductAmount, store.Code, store.Name)
			if err != nil {
				return err
			}
		}
		order.PrepaidCards = append(order.PrepaidCards, ec_order.OrderPrepaidCard{
			Id:            bson.ObjectIdHex(prepaidCardId),
			Number:        prepaidCard.Number,
			Amount:        deductAmount,
			TradeNo:       tradeNo,
			OutTradeSeq:   outTradeSeq,
			TransactionId: transactionId,
		})

		// 商品分摊礼品卡金额
		totalProductPayAmount := uint64(0)
		for _, p := range order.Products {
			totalProductPayAmount += p.PayAmount
		}
		restAmount := uint64(0)
		shareAmount := deductAmount
		for i, p := range order.Products {
			amount := uint64(0)
			if i == len(order.Products)-1 {
				amount = shareAmount
				// 因为是向下取整，多出来分摊金额的会累积到最后一个商品
				// 如果分摊到最后一个商品时剩下的金额大于商品的支付金额，则继续处理多出来的金额
				if amount > order.Products[i].PayAmount {
					restAmount = amount - order.Products[i].PayAmount
					amount = order.Products[i].PayAmount
				}
			} else {
				amount = p.PayAmount * deductAmount / totalProductPayAmount
				shareAmount -= amount
			}
			order.Products[i].PayAmount -= amount
			// 存在 amount 为 0 的情况，如果 amount 为 0 时 append PrepaidCards 会导致退款出问题
			// 例如购买两个商品，一个用兑换券兑换了，另外一个用储值卡，就会出现这种问题
			if amount > 0 {
				order.Products[i].PrepaidCards = append(order.Products[i].PrepaidCards, ec_order.OrderProductPrepaidCard{
					Id:     bson.ObjectIdHex(prepaidCardId),
					Number: prepaidCard.Number,
					Amount: amount,
				})
			}
		}
		for restAmount != 0 {
			for i := range order.Products {
				if order.Products[i].PayAmount > 0 && len(order.Products[i].PrepaidCards) > 0 {
					l := len(order.Products[i].PrepaidCards)
					restAmount -= 1
					order.Products[i].PrepaidCards[l-1].Amount += 1
					order.Products[i].PayAmount -= 1
					if restAmount == 0 {
						break
					}
				}
			}
		}

		order.PayAmount -= deductAmount
	}
	return nil
}

func deductPrepaidCard(ctx context.Context, order *ec_order.Order, prepaidCardId string, deductAmount uint64, storeCode, storeName string) (string, string, string, error) {
	request := &wallet.PrepaidCardDefrayRequest{
		PrepaidCardId: prepaidCardId,
		OrderId:       order.Id.Hex(),
		MemberId:      order.MemberId.Hex(),
		Amount:        deductAmount,
		OrderNumber:   order.Number,
		StoreCode:     storeCode,
		StoreName:     storeName,
	}
	resp, err := client.WalletService.PrepaidCardDefray(ctx, request)
	if err != nil {
		return "", "", "", err
	}
	return resp.TradeNo, resp.OutTradeSeq, resp.TransactionId, nil
}

func checkPrepaidCard(prepaidCard *wallet.PrepaidCard, order *ec_order.Order) error {
	if prepaidCard.MemberId != order.MemberId.Hex() {
		return errors.NewInvalidArgumentError("PrepaidCardIds")
	}
	if prepaidCard.Status != ec_wallet.STATUS_BOUND {
		return errors.NewInvalidArgumentErrorWithMessage("prepaidCardIds", "prepaid card status error")
	}
	startAt := core_util.ParseRFC3339(prepaidCard.StartAt)
	endAt := core_util.ParseRFC3339(prepaidCard.EndAt)
	if startAt.After(time.Now()) || endAt.Before(time.Now()) {
		return errors.NewInvalidArgumentErrorWithMessage("prepaidCardIds", "prepaid card has expired")
	}
	if prepaidCard.Amount <= 0 {
		return errors.NewInvalidArgumentErrorWithMessage("prepaidCardIds", "insufficient balance of prepaid card")
	}
	return nil
}

func rollbackPrepaidCard(ctx context.Context, order *ec_order.Order, storeCode, reason string) {
	request := &wallet.RevokePrepaidCardDefrayRequest{
		OrderId:   order.Id.Hex(),
		MemberId:  order.MemberId.Hex(),
		StoreCode: storeCode,
		Reason:    reason,
	}
	for _, prepaidCard := range order.PrepaidCards {
		copier.Instance(nil).From(prepaidCard).CopyTo(request)
		request.PrepaidCardId = prepaidCard.Id.Hex()
		request.OrderCreatedAt = order.CreatedAt.Format(core_util.RFC3339Mili)
		request.OrderNumber = order.Number
		_, err := ec_client.WalletService.RevokePrepaidCardDefray(ctx, request)
		if err != nil {
			log.Warn(ctx, "failed to rollback prepaid card", log.Fields{
				"orderNumber":   order.Number,
				"memberId":      order.MemberId.Hex(),
				"prepaidCardId": prepaidCard.Id.Hex(),
				"errMsg":        err.Error(),
			})
		}
	}
}

func calcStoredValue(ctx context.Context, builder *OrderBuilder, order *ec_order.Order) error {
	if builder.member.BlockedStatus == 2 {
		builder.UseStoredValue = false
	}
	if !builder.UseStoredValue || isStoredValueDisabled(order) {
		return nil
	}
	if order.PayAmount == 0 {
		return nil
	}
	balance, err := builder.GetStoredValueBalance(ctx)
	if err != nil {
		return err
	}
	if balance == 0 {
		return nil
	}

	var (
		deductAmount         uint64
		deductDeliveryAmount uint64
	)
	if balance >= order.PayAmount {
		deductAmount = order.PayAmount
		deductDeliveryAmount = order.Logistics.Fee
	} else {
		deductAmount = balance
		if deductAmount > order.PayAmount-order.Logistics.Fee {
			// 抵扣了部分邮费
			deductDeliveryAmount = deductAmount - (order.PayAmount - order.Logistics.Fee)
		}
	}
	order.PayAmount -= deductAmount
	order.StoredValue = share_model.StoredValue{
		Amount:         deductAmount,
		TradeNo:        order.Number,
		DeliveryAmount: deductDeliveryAmount,
	}
	payment := ""
	switch order.Payment {
	case ec_order.PAYMENT_WECHAT:
		payment = "online"
	case ec_order.PAYMENT_OFFLINE:
		payment = "pos"
	}
	if builder.isCreateOrder {
		store, _ := builder.GetStore(ctx)
		resp, err := ec_client.WalletService.StoredValuePay(ctx, &wallet.StoredValuePayRequest{
			MemberId:  order.MemberId.Hex(),
			TradeNo:   order.StoredValue.TradeNo,
			OrderId:   order.Id.Hex(),
			StoreId:   order.StoreId.Hex(),
			Amount:    deductAmount,
			Payment:   payment,
			StoreCode: store.Code,
		})
		if err != nil {
			return err
		}
		order.StoredValue.TransactionId = resp.TransId
	}

	products := []*share_model.Product{}
	for _, p := range order.Products {
		if p.PayAmount == 0 {
			continue
		}
		products = append(products, &share_model.Product{
			OutTradeId: p.OutTradeId.Hex(),
			PayAmount:  p.PayAmount,
		})
	}
	share_model.ShareAmount(deductAmount-deductDeliveryAmount, products)
	for _, p := range products {
		for i := range order.Products {
			if p.OutTradeId != order.Products[i].OutTradeId.Hex() {
				continue
			}
			copier.Instance(nil).From(order.StoredValue).CopyTo(&order.Products[i].StoredValue)
			order.Products[i].StoredValue.Amount = p.Amount
			order.Products[i].PayAmount -= p.Amount
		}
	}
	return nil
}

func rollbackStoredValue(ctx context.Context, order *ec_order.Order, storeCode string) {
	if order.StoredValue.TransactionId == "" || order.StoredValue.Amount == 0 {
		return
	}
	ec_client.WalletService.StoredValueRefund(ctx, &wallet.StoredValueRefundRequest{
		MemberId:      order.MemberId.Hex(),
		TradeNo:       bson.NewObjectId().Hex(),
		TransId:       order.StoredValue.TransactionId,
		Amount:        order.StoredValue.Amount,
		RefundOrderId: order.Id.Hex(),
		OutTradeNo:    order.StoredValue.TradeNo,
		StoreCode:     storeCode,
	})
}

func preCalcMemberPaidCardDiscount(ctx context.Context, builder *OrderBuilder, order *ec_order.Order) error {
	if len(builder.MemberPaidCardRecordIds) == 0 || isMemberPaidCardDisabled(order) {
		return nil
	}
	for _, memberPaidCardRecordId := range builder.MemberPaidCardRecordIds {
		memberPaidCardRecord := builder.GetMemberPaidCardRecord(ctx, memberPaidCardRecordId)
		if err := checkMemberPaidCardRecord(memberPaidCardRecord); err != nil {
			return err
		}
		if memberPaidCardRecord == nil {
			continue
		}
		var privilege *pb_member.MemberLevelPrivilege
		for i, p := range memberPaidCardRecord.MemberPaidCard.Privileges {
			if p.Name == MEMBER_PRIVILEGE_DISCOUNT && p.Privilege.IsDefault {
				privilege = memberPaidCardRecord.MemberPaidCard.Privileges[i]
			}
		}
		if privilege == nil {
			continue
		}
		if privilege.IsEnabled {
			if privilege.DiscountSetting != nil && privilege.DiscountSetting.Limit != 0 && memberPaidCardRecord.RemainderDiscountLimit == 0 {
				continue
			}
			for i, p := range order.Products {
				if !isPrivilegeProduct(privilege, p) {
					continue
				}
				if privilege.Privilege.IsDiscountLimit && len(builder.PrepaidCardIds) > 0 {
					order.Products[i].DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_PREPAID_CARD)
				}
				if privilege.Privilege.IsDiscountLimit && len(builder.DiscountIds) > 0 {
					order.Products[i].DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_COUPON)
				}
				if !privilege.Privilege.CanUseWithCoupons {
					order.Products[i].DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_BENEFIT_CARD)
				}
				if !privilege.Privilege.CanUseWithPrepaidCards {
					order.Products[i].DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_BENEFIT_CARD)
				} else {
					if privilege.Privilege.PrepaidCardLimit != nil {
						privilege.Privilege.PrepaidCardLimit.Scene = share_model.SCENE_PRIVILEGE
						order.Products[i].PrepaidCardLimits = share_model.MergePrepaidCardLimit(order.Products[i].PrepaidCardLimits, privilege.Privilege.PrepaidCardLimit)
					}
				}
			}
		}
	}
	return nil
}

func calcMemberPaidCardDiscount(ctx context.Context, builder *OrderBuilder, order *ec_order.Order) error {
	if builder.member.BlockedStatus == 2 {
		builder.MemberPaidCardRecordIds = []string{}
	}
	if len(builder.MemberPaidCardRecordIds) == 0 || isMemberPaidCardDisabled(order) {
		return nil
	}
	for _, memberPaidCardRecordId := range builder.MemberPaidCardRecordIds {
		memberPaidCardRecord := builder.GetMemberPaidCardRecord(ctx, memberPaidCardRecordId)
		if err := checkMemberPaidCardRecord(memberPaidCardRecord); err != nil {
			return err
		}
		if memberPaidCardRecord == nil {
			continue
		}
		tempMemberPaidCard := ec_order.MemberPaidCard{
			Id:       util.ToMongoId(memberPaidCardRecord.CardId),
			Type:     memberPaidCardRecord.CardType,
			RecordId: util.ToMongoId(memberPaidCardRecord.Id),
			Number:   memberPaidCardRecord.CardNumber,
		}
		order.MemberPaidCards = append(order.MemberPaidCards, tempMemberPaidCard)
		var privilege *pb_member.MemberLevelPrivilege
		for i, p := range memberPaidCardRecord.MemberPaidCard.Privileges {
			if p.Name == MEMBER_PRIVILEGE_DISCOUNT && p.Privilege.IsDefault {
				privilege = memberPaidCardRecord.MemberPaidCard.Privileges[i]
			}
		}
		if privilege == nil {
			continue
		}
		if privilege.IsEnabled {
			memberPaidCardSetting := builder.GetMemberPaidCardSetting(ctx)
			if privilege.DiscountSetting != nil && privilege.DiscountSetting.Limit != 0 && memberPaidCardRecord.RemainderDiscountLimit == 0 {
				continue
			}
			discounter := NewMemberPaidCardDiscounter(memberPaidCardRecord, privilege, memberPaidCardSetting.DiscountPriceType)
			discounter.Calc(ctx, order)
			for i, p := range order.Products {
				if !isPrivilegeProduct(privilege, p) {
					continue
				}
				if privilege.Privilege.IsDiscountLimit && len(builder.PrepaidCardIds) > 0 {
					order.Products[i].DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_PREPAID_CARD)
				}
				if privilege.Privilege.IsDiscountLimit && len(builder.DiscountIds) > 0 {
					order.Products[i].DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_COUPON)
				}
				if !privilege.Privilege.CanUseWithCoupons {
					order.Products[i].DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_BENEFIT_CARD)
				}
				if !privilege.Privilege.CanUseWithPrepaidCards {
					order.Products[i].DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_BENEFIT_CARD)
				} else {
					order.Products[i].PrepaidCardLimits = share_model.MergePrepaidCardLimit(order.Products[i].PrepaidCardLimits, privilege.Privilege.PrepaidCardLimit)
				}
			}
		}
	}
	if builder.isCreateOrder {
		// 扣减额度
		for _, d := range order.Discounts {
			if d.Type != ec_order.ORDER_DISCOUNT_TYPE_BENEFIT_CARD {
				continue
			}
			err := ec_share_model.UpdateMemberPaidCardDiscountLimit(ctx, OPERATOR_DEDUCT, d.Id, order.Id, d.SuitTotalAmount)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func rollbackMemberPaidCardDiscount(ctx context.Context, order *ec_order.Order) {
	// 扣减额度
	for _, d := range order.Discounts {
		if d.Type != ec_order.ORDER_DISCOUNT_TYPE_BENEFIT_CARD {
			continue
		}
		err := ec_share_model.UpdateMemberPaidCardDiscountLimit(ctx, OPERATOR_ROLLBACK, d.Id, order.Id, 0)
		if err != nil {
			log.Warn(ctx, "Failed to rollback member paid card discount limit", log.Fields{
				"recordId": d.Id.Hex(),
				"orderId":  order.Id.Hex(),
				"errMsg":   err.Error(),
			})
		}
	}
}

type ProductSku struct {
	Id           string
	sku          string
	count        uint64
	campaignType string
	campaignId   string
	cartId       bson.ObjectId
}

func generateOrderProducts(ctx context.Context, order *order.Order, builder *OrderBuilder, productPurchaseCountMap map[bson.ObjectId]uint64) error {
	productSkus := extractProductSkuArrays(ctx, builder)
	if len(productSkus) == 0 {
		return errors.NewInvalidArgumentErrorWithMessage("products", "products is empty")
	}
	for _, ps := range productSkus {
		if ps.count == 0 {
			return errors.NewInvalidArgumentErrorWithMessage("products", "product count can not be zero")
		}
	}

	ids := core_util.ExtractArrayStringField("Id", productSkus)
	ecProductMap, err := builder.GetEcProductMap(ctx, ids, nil)
	if err != nil {
		return err
	}

	productIds := []string{}
	for _, ecProduct := range ecProductMap {
		productIds = append(productIds, ecProduct.(ec_product.Product).ProductId.Hex())
	}
	productMap, err := builder.GetProductMap(ctx, productIds)
	if err != nil {
		return err
	}

	orderProductPurchaseStats := []*ec_order.OrderProductPurchaseStats{}
	if productPurchaseCountMap != nil {
		orderProductPurchaseStats, err = builder.GetOrderProductPurchaseStats(ctx, ids)
		if err != nil {
			return err
		}
	}
	purchaseStatsMap := map[string]*ec_order.OrderProductPurchaseStats{}
	for _, stats := range orderProductPurchaseStats {
		purchaseStatsMap[stats.ProductId.Hex()] = stats
	}
	// 商品各规格购买计数总和的 map
	productCountMap := map[string]int64{}
	for i, id := range ids {
		productCountMap[id] = productCountMap[id] + int64(productSkus[i].count)
	}
	for i, id := range ids {
		ecProduct, ok := ecProductMap[bson.ObjectIdHex(id)].(ec_product.Product)
		if !ok {
			return errors.NewNotExistsError("product")
		}
		// 在计算订单金额时，商品下架不报错
		if !ecProduct.HasSku(productSkus[i].sku) || (!builder.isMall && builder.isCreateOrder && ecProduct.GetShelveStatus() != ec_product.SHELVE_STATUS_SHELVED) {
			return errors.NewNotExistsError("product")
		}
		// 此忽略虚拟商品新加的无需物流设置
		if builder.isCreateOrder && builder.Method != ec_order.ORDER_DELIVERY_METHOD_NO_EXPRESS && !builder.isMall && !core_util.StrInArray(builder.Method, &ecProduct.DeliveryMethods) {
			return errors.NewInvalidArgumentErrorWithMessage("products", "unsupported method")
		}
		product, ok := productMap[ecProduct.ProductId.Hex()].(*pb_product.ProductDetailResponse)
		if !ok {
			return errors.NewNotExistsError("product")
		}
		if productPurchaseCountMap != nil {
			member, err := builder.GetMember(ctx)
			if err != nil {
				return err
			}
			limitStatus := checkProductPurchaseLimit(ctx, member, ecProduct.PurchaseLimit, purchaseStatsMap[ecProduct.Id.Hex()], productCountMap[id])
			if limitStatus != "" {
				return errors.NewNotExistsErrorWithMessage("product", fmt.Sprintf("Exceed product limit, %s", limitStatus))
			}
			periodType := ecProduct.PurchaseLimit.PeriodType
			err = ec_order.COrderProductPurchaseStats.IncrMemberPurchaseCount(ctx, bson.ObjectIdHex(id), bson.ObjectIdHex(builder.MemberId), periodType, int(productSkus[i].count))
			if err != nil {
				return err
			}
			productPurchaseCountMap[bson.ObjectIdHex(id)] += productSkus[i].count
		}
		orderProduct, err := getBasicOrderProduct(ctx, ecProduct, product, productSkus[i], order, builder, false)
		if err != nil {
			return err
		}
		if orderProduct == nil {
			continue
		}
		orderProduct.DisplayPrice = orderProduct.Price
		orderProduct.OriginTotal = orderProduct.Total
		order.Products = append(order.Products, *orderProduct)
	}

	if builder.isCreateOrder {
		if len(order.Products) == 0 {
			return errors.NewInvalidArgumentErrorWithMessage("products", "products is empty")
		}
		isAllProductsStoreUnshelved := true // 是否所有商品门店下架
		hasProductStoreUnshelved := false   // 是否存在商品门店下架
		for _, op := range order.Products {
			storeProduct := builder.GetStoreProduct(ctx, op.ProductId)
			if !CheckStoreShelveStatus(builder.GetDeliverySetting(ctx), storeProduct, op.Spec.Sku, order.Method) {
				hasProductStoreUnshelved = true
			} else {
				isAllProductsStoreUnshelved = false
			}
		}
		if isAllProductsStoreUnshelved {
			return errors.NewInvalidArgumentErrorWithMessage("products", "all products store status error")
		}
		if hasProductStoreUnshelved {
			return errors.NewInvalidArgumentErrorWithMessage("products", "partial products store status error")
		}
	}
	return nil
}

func checkMinPurchaseCount(ctx context.Context, order *order.Order, builder *OrderBuilder) error {
	if order.IsGrouponOrder() {
		return nil
	}
	ids := core_util.ExtractArrayStringField("Id", order.Products)
	ecProductMap, err := builder.GetEcProductMap(ctx, ids, nil)
	if err != nil {
		return err
	}

	// 买赠活动，只有赠品上有 Campaigns 字段。任选打包活动，商品上没有 Campaigns 字段。需要特殊处理
	presentCampaignProductIds := []string{}
	optionalPackageCampaignProductIds := []string{}
	for _, c := range order.Campaigns {
		if c.Type == ec_order.CAMPAIGN_TYPE_PRESENT {
			presentCampaignProductIds = append(presentCampaignProductIds, util.MongoIdsToStrs(c.ProductIds)...)
		}
		if c.Type == ec_order.CAMPAIGN_TYPE_OPTIONAL_PACKAGE {
			for _, p := range order.Products {
				if util.StrInArray(ec_order.CAMPAIGN_TYPE_OPTIONAL_PACKAGE, &p.CampaignTags) {
					optionalPackageCampaignProductIds = append(optionalPackageCampaignProductIds, p.Id.Hex())
				}
			}
		}
	}

	for _, p := range order.Products {
		if len(p.Campaigns) > 0 || util.StrInArray(p.Id.Hex(), &presentCampaignProductIds) || util.StrInArray(p.Id.Hex(), &optionalPackageCampaignProductIds) {
			continue
		}
		ecProduct := ecProductMap[p.Id].(ec_product.Product)
		// 为 0 表示未设置最低购买数量
		if ecProduct.MinPurchaseCount == 0 {
			continue
		}
		purchaseCount := uint64(0)
		for _, op := range order.Products {
			if op.Id != p.Id || len(p.Campaigns) > 0 || util.StrInArray(op.Id.Hex(), &presentCampaignProductIds) {
				continue
			}
			purchaseCount += op.Total
		}
		if uint32(purchaseCount) < ecProduct.MinPurchaseCount {
			return errors.NewInvalidArgumentErrorWithMessage("product", fmt.Sprintf("%s:%s", p.Id.Hex(), "less than the minimum purchase count"))
		}
	}
	return nil
}

func generateOrderProductAccessories(ctx context.Context, order *ec_order.Order, builder *OrderBuilder) error {
	if len(builder.ProductAccessories) == 0 {
		return nil
	}

	productAccessoryIds := core_util.ToStringArray(core_util.ExtractArrayField("Id", builder.ProductAccessories))
	productAccessories, err := ec_product.CProductAccessory.GetByIds(ctx, util.ToMongoIds(productAccessoryIds))
	if err != nil {
		return err
	}
	productAccessoriesMap := core_util.MakeMapper("Id", productAccessories)
	for _, op := range builder.ProductAccessories {
		if !util.StrInArray(op.Id, &productAccessoryIds) {
			return errors.NewInvalidArgumentError("productAccessories")
		}
		productAccessory, ok := productAccessoriesMap[bson.ObjectIdHex(op.Id)].(ec_product.ProductAccessory)
		if !ok {
			return errors.NewInvalidArgumentError("productAccessories")
		}
		if err := validProductAccessory(op, productAccessory); err != nil {
			return err
		}
		tempProductAccessory := ec_order.OrderProductAccessory{
			Id:      productAccessory.Id,
			Name:    productAccessory.Name,
			Type:    productAccessory.Type,
			Count:   op.Count,
			Message: op.Message,
		}
		order.ProductAccessories = append(order.ProductAccessories, tempProductAccessory)
	}

	return nil
}

func validProductAccessory(orderProductAccessory *common.ProductAccessoryRequest, productAccessory ec_product.ProductAccessory) error {
	switch productAccessory.Type {
	case ec_product.PRODUCT_ACCESSORY_TYPE_CUSTOM:
		if productAccessory.CustomAccessory.IsRequired && orderProductAccessory.Message == "" {
			return errors.NewInvalidArgumentErrorWithMessage("productAccessory.message", "required")
		}
	case ec_product.PRODUCT_ACCESSORY_TYPE_NORMAL:
		if orderProductAccessory.Count > productAccessory.NormalAccessory.Limit {
			return errors.NewInvalidArgumentErrorWithMessage("productAccessory.count", "limit")
		}
	}
	return nil
}

// 提取下单商品 sku 列表
func extractProductSkuArrays(ctx context.Context, builder *OrderBuilder) []ProductSku {
	// 存在参与的活动配置信息且需要使用活动配置中的商品时，从活动配置中提取商品 sku 信息
	// campaignConfig 是通用活动配置，由活动通用参数设置自己的活动折扣等信息，当前秒杀、预售、付邮试用等活动均使用此参数
	if builder.CampaignConfig != nil && builder.CampaignConfig.OverrideProduct {
		return extractProductSkuFormCampaignConfig(ctx, builder)
	}
	productSkus := []ProductSku{}
	// 点击直接购买
	if builder.isPurchase {
		var campaignExists bool
		// builder.Campaigns 为下单时传入的活动参数
		for _, orderCampaign := range builder.Campaigns {
			switch orderCampaign.Type {
			// 促销套餐活动
			case ec_order.CAMPAIGN_TYPE_PACKAGE:
				campaignExists = true
				// 根据传入的促销套餐活动 ids 查询活动详情
				ids := core_util.ExtractArrayStringField("Id", orderCampaign.PackageCampaigns)
				campaignMap, err := builder.GetCampaigns(ctx, ec_order.CAMPAIGN_TYPE_PACKAGE, ids, nil)
				if err != nil {
					log.Warn(ctx, "Failed to get package campaign", log.Fields{
						"ids":    ids,
						"errMsg": err.Error(),
					})
					continue
				}
				for _, packageCampaign := range orderCampaign.PackageCampaigns {
					packageCampaignDetail, ok := campaignMap[packageCampaign.Id].(*marketing.PackageCampaignDetail)
					if !ok {
						continue
					}
					switch packageCampaignDetail.Type {
					// 固定套餐
					case marketing_model.PACKAGE_CAMPAIGN_TYPE_SETTLED:
						// 遍历促销套餐活动的商品
						for _, product := range packageCampaign.Products {
							for _, campaignProduct := range packageCampaignDetail.Products {
								if product.Id != campaignProduct.Id {
									continue
								}
								for _, campaignProductSku := range campaignProduct.Skus {
									if product.Sku != campaignProductSku.Sku {
										continue
									}
									productSkus = append(productSkus, ProductSku{Id: campaignProduct.Id, sku: campaignProductSku.Sku, count: uint64(campaignProduct.Count)})
								}
							}

						}

					case marketing_model.PACKAGE_CAMPAIGN_TYPE_FREE:
						// TODO 自由搭配
						continue
					}
				}
			case ec_order.CAMPAIGN_TYPE_OPTIONAL_PACKAGE:
				campaignExists = true
				optionalPackageId := orderCampaign.OptionalPackageCampaigns[0].Id
				campaignMap, err := builder.GetCampaigns(ctx, ec_order.CAMPAIGN_TYPE_OPTIONAL_PACKAGE, []string{optionalPackageId}, nil)
				if err != nil {
					log.Warn(ctx, "Failed to get optional package campaign", log.Fields{
						"id":     optionalPackageId,
						"errMsg": err.Error(),
					})
					continue
				}
				optionalPackageCampaign, ok := campaignMap[bson.ObjectIdHex(optionalPackageId)].(optional_package.OptionalPackage)
				if !ok {
					continue
				}
				var skuExists bool
				for _, p := range optionalPackageCampaign.Products {
					if p.Id.Hex() != builder.ProductId {
						continue
					}
					if core_util.StrInArray(builder.Sku, &p.Skus) {
						skuExists = true
						break
					}
				}
				if !skuExists {
					campaignExists = false
					continue
				}
				productSkus = append(productSkus, ProductSku{Id: builder.ProductId, sku: builder.Sku, count: uint64(builder.Count), campaignId: optionalPackageId, campaignType: ec_order.CAMPAIGN_TYPE_OPTIONAL_PACKAGE})
			}
		}
		if !campaignExists && builder.ProductId == "" {
			return nil
		}
		if campaignExists {
			return productSkus
		}
		productSkus = append(productSkus, ProductSku{Id: builder.ProductId, sku: builder.Sku, count: uint64(builder.Count)})
	} else {
		// 通过购物车购买
		cartProducts := builder.GetCartProducts(ctx)
		for _, p := range cartProducts {
			tempProductSku := ProductSku{Id: p.ProductId.Hex(), sku: p.Sku, count: p.Total, cartId: p.Id}
			if len(p.Campaigns) > 0 {
				// 目前只有任选打包活动
				tempProductSku.campaignType = p.Campaigns[0].Type
				tempProductSku.campaignId = p.Campaigns[0].Id.Hex()
			}
			productSkus = append(productSkus, tempProductSku)
		}
	}
	return productSkus
}

// 从活动配置中提取商品 sku 信息
func extractProductSkuFormCampaignConfig(ctx context.Context, builder *OrderBuilder) []ProductSku {
	productSkus := []ProductSku{}
	productCountMap := make(map[string]uint64)
	for _, campaign := range builder.CampaignConfig.Campaigns {
		if campaign.Type == "proxyOrder" {
			if builder.isPurchase {
				productSkus = append(productSkus, ProductSku{Id: builder.ProductId, sku: builder.Sku, count: uint64(builder.Count)})
			} else {
				// 代客下单，导购下单时，需要从购物车中获取 addedBy == proxyOrder 的商品
				// 代客下单，客户下单时，需要从购物车中获取 addedBy == proxyOrder && proxyOrderId == proxyId 的商品
				builder.cartProductAddedBy = "proxyOrder"
				builder.cartProductProxyOrderId = campaign.Id
				cartProducts := builder.GetCartProducts(ctx)
				for _, p := range cartProducts {
					tempProductSku := ProductSku{Id: p.ProductId.Hex(), sku: p.Sku, count: p.Total, cartId: p.Id}
					productSkus = append(productSkus, tempProductSku)
				}
			}
			return productSkus
		}

		for _, product := range campaign.Products {
			key := fmt.Sprintf("%s:%s", product.Id, product.Sku)
			if count, ok := productCountMap[key]; ok {
				productCountMap[key] = count + uint64(product.Count)
			} else {
				productCountMap[key] = uint64(product.Count)
			}
		}
	}
	for key, count := range productCountMap {
		keySlice := strings.Split(key, ":")
		productSkus = append(productSkus, ProductSku{Id: keySlice[0], sku: keySlice[1], count: count})
	}
	return productSkus
}

// 获取订单对应的分销员详情
func getOrderServiceStaffPromoter(ctx context.Context, builder *OrderBuilder) *distribution_model.Promoter {
	var serviceStaffPromoter *distribution_model.Promoter
	if builder.PromoterMemberId != "" {
		serviceStaffPromoter, _ = distribution_model.CPromoter.GetUnFrozenByMemberId(ctx, bson.ObjectIdHex(builder.PromoterMemberId))
	}
	if builder.StaffId != "" {
		serviceStaffPromoter, _ = distribution_model.CPromoter.GetPromoterByStaffId(ctx, bson.ObjectIdHex(builder.StaffId))
	}

	if serviceStaffPromoter != nil {
		if serviceStaffPromoter.Type != distribution_model.PROMOTER_TYPE_STAFF {
			return nil
		}
		// 导购不存在或不在职不可分佣
		staff, err := builder.GetStaffById(ctx, serviceStaffPromoter.StaffId.Hex())
		if err != nil || staff == nil || staff.Status != 1 {
			return nil
		}
	}
	return serviceStaffPromoter
}

// 获取订单客户绑定的分销员详情
func getOrderMemberBoundStaffPromoter(ctx context.Context, builder *OrderBuilder, memberId string) *distribution_model.Promoter {
	var boundedPromoter *distribution_model.Promoter
	staffMember, err := builder.GetBoundStaffMember(ctx)
	if err == nil {
		boundedPromoter, _ = distribution_model.CPromoter.GetUnfrozenPromoterByStaffId(ctx, bson.ObjectIdHex(staffMember.Staff.Id))
	} else {
		log.Warn(ctx, "Get staff member failed.", log.Fields{"err": err.Error()})
		promoterMember, err := ec_client.DistributionService.GetPromoterMember(ctx, &pb_distribution.GetPromoterMemberRequest{
			MemberId: memberId,
			Status:   distribution_model.PROMOTER_MEMBER_STATUS_BOUND,
		})
		if err == nil {
			boundedPromoter, _ = distribution_model.CPromoter.GetUnFrozenByMemberId(ctx, bson.ObjectIdHex(promoterMember.Promoter.MemberId))
		}
	}

	if boundedPromoter != nil {
		if boundedPromoter.Type != distribution_model.PROMOTER_TYPE_STAFF {
			return nil
		}
		// 导购不存在或不在职不可分佣
		staff, err := builder.GetStaffById(ctx, boundedPromoter.StaffId.Hex())
		if err != nil || staff == nil || staff.Status != 1 {
			return nil
		}
	}
	return boundedPromoter
}

func canExistsServicePromoterGetProportion(ctx context.Context, dSetting *distribution_model.DistributionSetting, boundedPromoter *distribution_model.Promoter) bool {
	// 是否允许服务导购分佣
	return dSetting.Rule.IsServiceStaffFirst
}

// 绑定导购是否能分佣
func canExistsBoundPromoterGetProportion(ctx context.Context, dSetting *distribution_model.DistributionSetting, builder *OrderBuilder, boundedPromoter *distribution_model.Promoter) bool {
	// 是否是绑定导购的推广链接订单
	isFromShareLink := builder.StaffId == boundedPromoter.StaffId.Hex() || (builder.PromoterMemberId == boundedPromoter.MemberId.Hex() && boundedPromoter.MemberId.Hex() != "")
	switch dSetting.Rule.BoundStaffProportionType {
	case distribution_model.BOUND_STAFF_PROPORTION_TYPE_ANY:
		return true
	case distribution_model.BOUND_STAFF_PROPORTION_TYPE_LINK:
		if isFromShareLink {
			return true
		}
	default:
		log.Warn(ctx, "Invalid distribution setting", log.Fields{"setting": dSetting})
	}
	return false
}

// 处理导购自己下单的分销信息
func buildSelfStaffDist(ctx context.Context, order *ec_order.Order, builder *OrderBuilder) bool {
	staffSetting := builder.GetStaffDistributionSetting(ctx)
	if staffSetting == nil || !staffSetting.Enabled {
		return false
	}

	member, err := builder.GetMember(ctx)
	if err != nil {
		log.Warn(ctx, "invalid memberId", log.Fields{
			"memberId": order.MemberId.Hex(),
		})
		return false
	}
	if member.Phone == "" {
		return false
	}
	// 通过下单客户的手机号查找导购，判断是否是在职导购自己下单，如果是则给导购自己分佣
	staff, err := builder.GetStaffByPhone(ctx, member.Phone)
	if err == nil && staff != nil && staff.Status == 1 {
		promoter, _ := distribution_model.CPromoter.GetUnfrozenPromoterByStaffId(ctx, util.ToMongoId(staff.Id))
		if promoter != nil {
			setDist(ctx, order, promoter, staffSetting, builder)
			return true
		}
		return false
	}

	return false
}

// 处理导购分销
func buildStaffDist(ctx context.Context, order *ec_order.Order, builder *OrderBuilder) bool {
	staffSetting := builder.GetStaffDistributionSetting(ctx)
	if staffSetting == nil || !staffSetting.Enabled {
		return false
	}
	servicePromoter, boundedPromoter := getOrderServiceStaffPromoter(ctx, builder), getOrderMemberBoundStaffPromoter(ctx, builder, order.MemberId.Hex())

	if servicePromoter != nil && (servicePromoter.Frozen || servicePromoter.Type != distribution_model.PROMOTER_TYPE_STAFF) {
		servicePromoter = nil
	}

	// 服务导购存在时，处理所有服务导购可以分佣的情况
	if servicePromoter != nil && canExistsServicePromoterGetProportion(ctx, staffSetting, boundedPromoter) {
		setDist(ctx, order, servicePromoter, builder.GetStaffDistributionSetting(ctx), builder)
		return true
	}

	// 服务导购不存在或者服务导购存在但无法分佣，处理所有绑定导购可以分佣的情况
	if boundedPromoter != nil && canExistsBoundPromoterGetProportion(ctx, staffSetting, builder, boundedPromoter) {
		setDist(ctx, order, boundedPromoter, staffSetting, builder)
		return true
	}

	return false
}

// 处理大众分销员自己下单的分销信息
func buildSelfMemberDist(ctx context.Context, order *order.Order, builder *OrderBuilder) bool {
	// 大众分销一定是商品分销，因此必须大众分销商品存在才可以分销
	if !isMemberDistributionProductExists(builder.ecProductMap) {
		return false
	}
	dSetting := builder.GetMemberDistributionSetting(ctx)
	// 分销未启用禁止分佣；不允许绑定自己时禁止分佣给自己
	if dSetting == nil || !dSetting.Enabled {
		return false
	}
	// 即使大众分销员拥有绑定导购，大众分销员自己下单也优先分佣给自己，https://gitlab.maiscrm.com/mai/home/<USER>/issues/41079
	memberSelfPromoter, _ := distribution_model.CPromoter.GetUnFrozenByMemberId(ctx, order.MemberId)
	if memberSelfPromoter == nil || memberSelfPromoter.Frozen || memberSelfPromoter.Type != distribution_model.PROMOTER_TYPE_MEMBER {
		return false
	}
	// 禁止绑定时不生成分销订单
	if !dSetting.Rule.CanBindSelf && memberSelfPromoter != nil {
		return true
	}
	staffSetting := builder.GetStaffDistributionSetting(ctx)
	// 已开启导购分销时，成为导购分销员后不能获得大众分销员的佣金权益 https://gitlab.maiscrm.com/mai/home/<USER>/issues/34908#note_3649094
	if staffSetting != nil && staffSetting.Enabled {
		staff, err := builder.GetStaffByPhone(ctx, memberSelfPromoter.Phone)
		if err == nil && staff != nil && staff.Status == 1 {
			// 需要给自己的大众分销员分销时，也需要判断是否有相同手机号的导购，如果有也不分佣
			// 由于需要优先给自己的大众分销员分佣，当下单客户自己的大众分销员存在时，不再触发导购分销
			// 此时既不进行大众分销，也不进行导购分销，所以直接 return true 终止分销
			return true
		}
	}
	setDist(ctx, order, memberSelfPromoter, dSetting, builder)
	return true
}

// 处理大众分销
func buildMemberDist(ctx context.Context, order *order.Order, builder *OrderBuilder) bool {
	staffSetting := builder.GetStaffDistributionSetting(ctx)
	dSetting := builder.GetMemberDistributionSetting(ctx)
	if dSetting == nil || !dSetting.Enabled {
		return false
	}

	var promoter *distribution_model.Promoter
	// 大众分销只有设置为不绑定关系的时候，才会用创建订单时传过来的 promoterMemberId，否则找绑定导购
	if builder.PromoterMemberId != "" {
		serviceMemberPromoter, _ := distribution_model.CPromoter.GetByMemberId(ctx, bson.ObjectIdHex(builder.PromoterMemberId))
		if serviceMemberPromoter != nil && !serviceMemberPromoter.Frozen &&
			serviceMemberPromoter.Type == distribution_model.PROMOTER_TYPE_MEMBER &&
			dSetting.Rule.BoundType == distribution_model.BOUNDTYPE_UNBOUND {
			promoter = serviceMemberPromoter
		}
		// 如果大众分销设置为支付时绑定，则这里处理 order.promoterIdForBinding 字段，在 orderPaidWebhook 中调用接口完成绑定
		if serviceMemberPromoter != nil && !serviceMemberPromoter.Frozen &&
			dSetting.Rule.PromoterMemberBindMode == distribution_model.PROMOTER_MEMBER_BIND_MODE_PAY &&
			dSetting.Rule.BoundType != distribution_model.BOUNDTYPE_UNBOUND {
			order.PromoterMemberIdForBinding = bson.ObjectIdHex(builder.PromoterMemberId)
			// 查询客户是否有绑定的分销员
			promoterMember, _ := ec_client.DistributionService.GetPromoterMember(ctx, &pb_distribution.GetPromoterMemberRequest{
				MemberId: bson.ObjectId.Hex(order.MemberId),
				Status:   distribution_model.PROMOTER_MEMBER_STATUS_BOUND,
			})
			if promoterMember != nil {
				boundedMemberPromoter, _ := distribution_model.CPromoter.GetById(ctx, bson.ObjectIdHex(promoterMember.Promoter.PromoterId))
				// 是否能绑定到服务导购
				if canBoundToOtherMemberPromoter(boundedMemberPromoter, dSetting, order, promoterMember) {
					promoter = serviceMemberPromoter
				}
			} else {
				promoter = serviceMemberPromoter
			}
		}
	}

	promoterMember, rpcErr := ec_client.DistributionService.GetPromoterMember(ctx, &pb_distribution.GetPromoterMemberRequest{
		MemberId: bson.ObjectId.Hex(order.MemberId),
		Status:   distribution_model.PROMOTER_MEMBER_STATUS_BOUND,
	})
	if promoter == nil {
		// 没有找到服务导购，查找绑定导购
		if rpcErr != nil {
			return false
		}
		boundedMemberPromoter, err := distribution_model.CPromoter.GetById(ctx, bson.ObjectIdHex(promoterMember.Promoter.PromoterId))
		if !canBoundToOtherMemberPromoter(boundedMemberPromoter, dSetting, order, promoterMember) {
			order.PromoterMemberIdForBinding = bson.NilObjectId
		}
		if err != nil || boundedMemberPromoter.Type != distribution_model.PROMOTER_TYPE_MEMBER || boundedMemberPromoter.Frozen {
			return false
		}
		promoter = boundedMemberPromoter
	} else {
		// 如果找到了服务导购，且服务导购关闭了拉新，且服务导购和当前下单人不是绑定关系，那么不分佣
		if promoter.IsNewUserDisallowed && (promoterMember != nil && promoterMember.Promoter.PromoterId != promoter.Id.Hex() || promoterMember == nil) {
			return false
		}
	}

	// 已开启导购分销后，导购分销员后不再获得大众分销员的佣金权益 https://gitlab.maiscrm.com/mai/home/<USER>/issues/34908#note_3649094
	if promoter != nil && staffSetting != nil && staffSetting.Enabled {
		staff, err := builder.GetStaffByPhone(ctx, promoter.Phone)
		if err == nil && staff != nil && staff.Status == 1 {
			return false
		}
	}

	// 如果要绑定的大众分销在黑名单中，不能绑定客户
	if order.PromoterMemberIdForBinding.Valid() {
		promoterMember, _ := share.GetMemberById(ctx, order.PromoterMemberIdForBinding, nil)
		if promoterMember != nil && promoterMember.BlockedStatus == 2 {
			order.PromoterMemberIdForBinding = bson.NilObjectId
		}
	}

	// 处理大众分销
	// 分销员自己购买一定会有佣金（未冻结状态）
	// 自己不是分销员的需要看是否在有效期内
	if promoterMember != nil && !promoterMember.Promoter.Permanent {
		boundAt, err := util.TransStrToTime(promoterMember.Promoter.BoundAt)
		if err != nil {
			log.Warn(ctx, "trans boundAt to time failed", log.Fields{
				"promoterMember": promoterMember,
				"errMsg":         err,
			})
			return false
		}
		// 客户不是永久有效并且不在分销员的有效期内
		if dSetting.Rule.BoundType == distribution_model.BOUNDTYPE_PERIOD &&
			boundAt.Add(time.Hour*time.Duration(dSetting.Rule.AvailablePeriod)*24).Before(time.Now()) {
			return false
		}
	}
	ecProductIds := core_util.ExtractArrayStringField("Id", order.Products)
	builder.GetEcProductMap(ctx, ecProductIds, nil)
	setDist(ctx, order, promoter, dSetting, builder)
	return true
}

// 是否可以绑定到其他大众分销员
func canBoundToOtherMemberPromoter(boundedMemberPromoter *distribution_model.Promoter, setting *distribution_model.DistributionSetting, order *order.Order, promoterMember *pb_distribution.GetPromoterMemberResponse) bool {
	// 如果没有绑定的分销员或者绑定的分销员不是大众分销员则可以再绑定
	if boundedMemberPromoter == nil || boundedMemberPromoter.Type != distribution_model.PROMOTER_TYPE_MEMBER {
		return true
	}
	// 如果已绑定和待绑定是同一个人则不应再处理
	if boundedMemberPromoter.MemberId.Hex() == order.PromoterMemberIdForBinding.Hex() {
		return false
	}

	switch setting.Rule.ReplaceType {
	// 如果不允许抢客则也不能绑定
	case distribution_model.REPLACETYPE_NOTALLOW:
		return false
	// 如果允许抢客则可以再绑定
	case distribution_model.REPLACETYPE_ALLOW:
		return true
	// 如果保护期内禁止抢客则计算保护期是否到期
	case distribution_model.REPLACETYPE_PERIOD:
		if util.MustTransStrToTime(promoterMember.Promoter.BoundAt).Add(time.Hour * time.Duration(setting.Rule.ProtectionPeriod) * 24).Before(time.Now()) {
			return true
		}
	}

	return false
}

// BuildDistribution 生成分销信息
func BuildDistribution(ctx context.Context, order *order.Order, builder *OrderBuilder) {
	// 扫码购订单不生成分销订单
	if order.IsScanBuy() {
		return
	}
	if builder.IsChainRetail(ctx) {
		buildMemberDist(ctx, order, builder)
		return
	}

	if buildSelfStaffDist(ctx, order, builder) {
		return
	}
	// 当下单客户自己是分销员时，优先给自己分佣
	// https://gitlab.maiscrm.com/mai/home/<USER>/issues/41079
	if buildSelfMemberDist(ctx, order, builder) {
		return
	}
	if buildStaffDist(ctx, order, builder) {
		return
	}
	buildMemberDist(ctx, order, builder)
}

func isMemberDistributionProductExists(ecProductMap map[interface{}]interface{}) bool {
	for _, item := range ecProductMap {
		ecProduct := item.(ec_product.Product)
		if ecProduct.Distribution.Enabled {
			return true
		}
	}
	return false
}

// 积分抵扣减积分
func deductScoreByOrder(ctx context.Context, order *ec_order.Order, channelId string) error {
	extraInfo := map[string]string{
		"orderId": order.Id.Hex(),
	}
	metaBytes, _ := json.Marshal(extraInfo)
	req := &pb_member.UpdateScoreRequest{
		Ids:         []string{order.MemberId.Hex()},
		Score:       -int64(order.PayScore),
		Brief:       "purchase_deduct_score",
		Description: "积分抵扣",
		Meta:        string(metaBytes),
		ChannelId:   channelId,
		Origin:      constant.WEAPP,
	}
	_, err := share_service.UpdateScore(ctx, req)
	return err
}

func returnScoreByOrder(ctx context.Context, order *ec_order.Order) {
	extraInfo := map[string]string{
		"orderId": order.Id.Hex(),
	}
	ec_order.ReturnScore(ctx, order.MemberId.Hex(), order.PayScore, extraInfo)
}

func buildCampaigns(ctx context.Context, order *ec_order.Order, builder *OrderBuilder, ignoreCheckPresent bool) error {
	if !builder.IsDiscountEnable(DISCOUNT_TYPE_CAMPAIGN) || len(builder.MemberPaidCardRecordIds) > 0 {
		return nil
	}
	// 参与买赠活动
	for _, campaign := range builder.Campaigns {
		if campaign.Type == ec_order.CAMPAIGN_TYPE_PRESENT {
			return buildPresentCampaign(ctx, order, campaign, builder, ignoreCheckPresent)
		}
		if campaign.Type == ec_order.CAMPAIGN_TYPE_GROUPON {
			return checkGrouponCampaignBeforeCreate(ctx, order, campaign, builder) // 这里只做检查，支付成功的订单才算参加了拼团活动。
		}
	}

	return nil
}

func buildPresentCampaign(ctx context.Context, order *ec_order.Order, campaign *common.OrderCampaign, builder *OrderBuilder, ignoreCheckPresent bool) error {
	if len(order.Products) == 0 {
		// 订单无正常商品
		return nil
	}
	presentCampaignIds := core_util.ToStringArray(core_util.ExtractArrayField("Id", campaign.PresentCampaigns))
	idToPresentMap, err := builder.GetCampaigns(ctx, ec_order.CAMPAIGN_TYPE_PRESENT, presentCampaignIds, nil)
	if err != nil {
		return err
	}

	for _, c := range campaign.PresentCampaigns {
		pc := idToPresentMap[c.Id].(*marketing.PresentCampaignDetail)
		if pc.Rule.ProductType != order.Products[0].Type {
			return errors.NewInvalidArgumentErrorWithMessage("presentCampaign", "invalid productType")
		}
	}
	isProductStockEnabled := order.Method == ec_order.ORDER_DELIVERY_METHOD_EXPRESS && order.Operator == setting_model.DELIVERY_SETTING_OPERATOR_USER && ec_share_model.IsProductStockEnabled(ctx, nil)
	presentsResp, err := getPresents(ctx, order, campaign, ignoreCheckPresent, isProductStockEnabled)
	if err != nil {
		return err
	}

	if len(presentsResp.Presents) == 0 {
		return nil
	}

	rollbackMap := make(map[string]int)

OUTER:
	// 减库存
	for _, present := range presentsResp.Presents {
		presentCampaign, ok := idToPresentMap[present.CampaignId].(*marketing.PresentCampaignDetail)
		if !ok {
			return errors.NewNotExistsError("presentCampaign")
		}
		err := canPresented(ctx, present, presentCampaign, isProductStockEnabled, ignoreCheckPresent)
		if err != nil {
			if !builder.isCreateOrder {
				continue
			}
			return err
		}
		if builder.isCreateOrder {
			if isProductStockEnabled {
				err := ec_product.CProduct.ReduceStock(ctx, present.Spec.Sku, int(present.PresentTotal))
				if err != nil {
					tempCampaign := idToPresentMap[present.CampaignId].(*marketing.PresentCampaignDetail)
					for _, d := range tempCampaign.Discounts {
						if d.Level != present.Level {
							continue
						}
						if d.DiscountType != "" {
							// 一个优惠内同时有赠品和折扣，赠品库存不足时，不能影响下单
							continue OUTER
						}
					}
					return errors.NewInvalidArgumentErrorWithMessage("present", "赠品已赠完")
				}
				rollbackMap[present.Spec.Sku] = int(present.PresentTotal)
			}

			err = marketing_model.CPresentCampaign.UpdatePresentStatistics(ctx, present.CampaignId, present)
			// 库存减扣成功后赠送赠品
			if err != nil {
				log.Warn(ctx, "Failed to deduct present inventory.", log.Fields{
					"errorMessage": err.Error(),
					"campaignId":   present.CampaignId,
					"presentId":    present.Id,
					"presentLevel": present.Level,
				})
				for k, v := range rollbackMap {
					ec_product.CProduct.ReturnStock(ctx, k, v)
				}
				return errors.NewInvalidArgumentErrorWithMessage("present", "赠品已赠完")
			}
		}
		err = setOrderPresents(ctx, present, order, builder)
		if err != nil {
			return err
		}
	}
	return nil
}

// 获取赠品
func getPresents(ctx context.Context, order *ec_order.Order, campaign *common.OrderCampaign, ignoreCheckPresent, isProductStockEnabled bool) (*pb_marketing.GetPresentsResponse, error) {
	req := &pb_marketing.GetPresentsRequest{
		MemberId:           order.MemberId.Hex(),
		StoreId:            order.StoreId.Hex(),
		IgnoreCheckPresent: ignoreCheckPresent,
		IsProductStockEnable: &mairpc_common_types.BoolValue{
			Value: isProductStockEnabled,
		},
	}
	if len(campaign.PresentCampaigns) != 0 {
		copier.Instance(nil).From(campaign.PresentCampaigns).CopyTo(&req.PresentCampaigns)
	}
	return ec_client.MarketingService.GetPresents(ctx, req)

}

// 判断是否能赠送
func canPresented(ctx context.Context, present *pb_marketing.PresentDetail, campaign *marketing.PresentCampaignDetail, isProductStockEnabled, ignoreCheckPresent bool) error {
	for _, d := range campaign.Discounts {
		if p := getMatchedPresent(present, d); p == nil {
			continue
		}
		if d.DiscountType != "" {
			return nil
		}
	}
	// 检查库存
	if isProductStockEnabled {
		stockStatus, err := ec_product.CProduct.GetStatus(ctx, present.Spec.Sku, int(present.PresentTotal))
		if err != nil {
			return err
		}
		if stockStatus != ec_product.STATUS_ENOUGH {
			// 库存不足
			return errors.NewInvalidArgumentErrorWithMessage("campaign", "present is understoked")
		}
	}
	if ignoreCheckPresent {
		return nil
	}
	if present.IsTotalLimit {
		for _, d := range campaign.Discounts {
			p := getMatchedPresent(present, d)
			if p == nil {
				continue
			}
			if present.PresentTotal > p.Total {
				return errors.NewInvalidArgumentErrorWithMessage("campaign", "present is understoked")
			}
		}
	}
	return nil
}

// 获取买赠活动中，和 present 匹配的赠品
func getMatchedPresent(present *pb_marketing.PresentDetail, discount *marketing.DiscountDetail) *marketing.PresentDetail {
	if discount.Level != present.Level {
		return nil
	}
	for _, p := range discount.Presents {
		if p.Spec.Sku == present.Spec.Sku {
			return p
		}
	}
	return nil
}

// 订单上添加赠品信息
func setOrderPresents(ctx context.Context, present *pb_marketing.PresentDetail, order *ec_order.Order, builder *OrderBuilder) error {
	presentMap, err := builder.GetCampaigns(ctx, ec_order.CAMPAIGN_TYPE_PRESENT, nil, nil)
	if err != nil {
		return err
	}
	presentCampaign := presentMap[present.CampaignId].(*marketing.PresentCampaignDetail)
	discountId := ""
	for _, discount := range presentCampaign.Discounts {
		if present.Level == discount.Level {
			discountId = discount.DiscountId
		}
	}
	if discountId == "" {
		return errors.NewInvalidArgumentError("presentCampaign")
	}

	productIds := core_util.ToStringArray(core_util.ExtractArrayField("Id", presentCampaign.Rule.Products))
	tmpPresent := formatPresent(ctx, present, bson.ObjectIdHex(present.CampaignId), bson.ObjectIdHex(discountId), productIds)
	if tmpPresent != nil {
		order.Products = append(order.Products, *tmpPresent)
		order.Campaigns = append(order.Campaigns, ec_order.Campaign{
			Id:           bson.ObjectIdHex(present.CampaignId),
			Title:        presentCampaign.Title,
			Type:         ec_order.CAMPAIGN_TYPE_PRESENT,
			PresentLevel: present.Level,
			DiscountId:   bson.ObjectIdHex(discountId),
			ProductIds:   util.ToMongoIds(productIds),
			Count:        present.Count,
		})
	}
	return nil
}

// 计算超值换购活动折扣
func buildPlusBuyCampaign(ctx context.Context, order *ec_order.Order, campaign *common.OrderCampaign, builder *OrderBuilder, index int) error {
	if len(campaign.PlusBuyCampaigns) == 0 {
		return nil
	}
	ids := core_util.ToStringArray(core_util.ExtractArrayField("Id", campaign.PlusBuyCampaigns))
	campaignMap, err := builder.GetCampaigns(ctx, ec_order.CAMPAIGN_TYPE_PLUS_BUY, ids, nil)
	if err != nil {
		return err
	}
	var (
		productIds        []string
		plusBuyProductMap map[interface{}]interface{}
	)
	for i, c := range campaign.PlusBuyCampaigns {
		if len(c.Products) == 0 {
			continue
		}
		plusBuyCampaign, ok := campaignMap[bson.ObjectIdHex(c.Id)].(plus_buy.PlusBuy)
		if !ok {
			return errors.NewInvalidArgumentError("campaigns")
		}
		totalAmount := calcPlusBuyCondition(plusBuyCampaign.Rule.Type, plusBuyCampaign.Rule.Products, order.Products, nil)
		if totalAmount < plusBuyCampaign.Price {
			// 不满足换购条件，清空选中的换购商品
			campaign.PlusBuyCampaigns[i].Products = nil
			builder.Campaigns[index].PlusBuyCampaigns[i].Products = nil
			continue
		}
		err = checkPlusBuyCampaign(plusBuyCampaign, c)
		if err != nil {
			return err
		}
		for _, p := range c.Products {
			productIds = append(productIds, p.Id)
		}
	}
	if len(productIds) > 0 {
		plusBuyProductMap, err = builder.GetPlusBuyProductMap(ctx, productIds)
		if err != nil {
			return err
		}
	}
	for _, c := range campaign.PlusBuyCampaigns {
		if len(c.Products) == 0 {
			continue
		}
		plusBuyCampaign := campaignMap[bson.ObjectIdHex(c.Id)].(plus_buy.PlusBuy)
		var (
			campaignProductIds []bson.ObjectId
			joinRequest        = &pb_eccampaign_plus_buy.JoinPlusBuyRequest{
				Id:          c.Id,
				MemberId:    builder.MemberId,
				OrderNumber: order.Number,
			}
			skuRemaining = make(map[string]uint64)
		)
		if !builder.isCreateOrder {
			record, err := proto_client.GetEccampaignPlusBuyServiceClient().GetMemberRecord(ctx, &request.DetailWithMemberIdRequest{
				Id:       c.Id,
				MemberId: builder.MemberId,
			})
			if err != nil {
				return err
			}
			for _, p := range record.ProductStats {
				skuRemaining[p.Sku] = p.Remaining
			}
		}
		for i, op := range order.Products {
			if ec_order.CheckProductCampaignExistence(op, ec_order.CAMPAIGN_TYPE_PACKAGE) || ec_order.CheckProductCampaignExistence(op, ec_order.CAMPAIGN_TYPE_PLUS_BUY) {
				continue
			}
			if plusBuyCampaign.Rule.Type == plus_buy.PLUS_BUY_RULE_TYPE_INCLUDE {
				include := false
				for _, p := range plusBuyCampaign.Rule.Products {
					if p.Id != op.Id {
						continue
					}
					include = util.StrInArray(op.Spec.Sku, &p.Skus)
				}
				if !include {
					continue
				}
				campaignProductIds = append(campaignProductIds, op.Id)
			}
			if plusBuyCampaign.IsDiscountLimit && len(builder.PrepaidCardIds) > 0 {
				order.Products[i].DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_PREPAID_CARD)
			}
			if !plusBuyCampaign.CanUseCoupon {
				order.Products[i].DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_CAMPAIGN)
			}
			if !plusBuyCampaign.CanUsePrepaidCard {
				order.Products[i].DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_CAMPAIGN)
			} else {
				order.Products[i].PrepaidCardLimits = share_model.MergeModelPrepaidCardLimit(order.Products[i].PrepaidCardLimits, plusBuyCampaign.PrepaidCardLimit)
			}
			order.Products[i].DisableDiscount(DISCOUNT_TYPE_MEMBER, DISCOUNT_TYPE_CAMPAIGN)
			order.Products[i].DisableDiscount(DISCOUNT_TYPE_BENEFIT_CARD, DISCOUNT_TYPE_CAMPAIGN)
			order.Products[i].CampaignTags = append(order.Products[i].CampaignTags, ec_order.CAMPAIGN_TYPE_PLUS_BUY)
			order.Products[i].IsDiscountLimit = plusBuyCampaign.IsDiscountLimit
		}
		orderCampaign := ec_order.Campaign{
			Id:         plusBuyCampaign.Id,
			Title:      plusBuyCampaign.Name,
			Type:       ec_order.CAMPAIGN_TYPE_PLUS_BUY,
			ProductIds: campaignProductIds,
		}
		orderDiscount := ec_order.OrderDiscount{
			Id:           plusBuyCampaign.Id,
			Title:        plusBuyCampaign.Name,
			Type:         ec_order.ORDER_DISCOUNT_TYPE_CAMPAIGN,
			CampaignType: ec_order.CAMPAIGN_TYPE_PLUS_BUY,
		}
		for _, p := range c.Products {
			if plusBuyProductMap == nil {
				continue
			}
			productResp, ok := plusBuyProductMap[p.Id].(*pb_ec_product.ProductResponse)
			if !ok {
				continue
			}
			product := genPlusBuyProduct(plusBuyCampaign, p, productResp)
			discount := ec_order.OrderDiscount{
				Id:           plusBuyCampaign.Id,
				Title:        plusBuyCampaign.Name,
				Type:         ec_order.ORDER_DISCOUNT_TYPE_CAMPAIGN,
				CampaignType: ec_order.CAMPAIGN_TYPE_PLUS_BUY,
				Amount:       int64((product.OriginPrice - product.Price) * product.Total),
			}
			product.Discounts = append(product.Discounts, discount)
			product.Campaigns = append(product.Campaigns, orderCampaign)
			orderDiscount.Amount += discount.Amount
			joinRequest.OrderProductInfos = append(joinRequest.OrderProductInfos, &pb_eccampaign_plus_buy.OrderProductInfo{
				Sku:   product.Spec.Sku,
				Total: product.Total,
			})
			remaining, ok := skuRemaining[product.Spec.Sku]
			if ok && remaining < product.Total {
				return errors.NewInvalidArgumentError("total")
			}
			order.Products = append(order.Products, product)
		}
		if len(joinRequest.OrderProductInfos) > 0 && builder.isCreateOrder {
			_, err := proto_client.GetEccampaignPlusBuyServiceClient().JoinPlusBuy(ctx, joinRequest)
			if err != nil {
				return err
			}
		}
		order.Discounts = append(order.Discounts, orderDiscount)
		order.Campaigns = append(order.Campaigns, orderCampaign)
	}
	return nil
}

// 获取超值换购活动商品
func genPlusBuyProduct(plusBuyCampaign plus_buy.PlusBuy, product *common.PlusBuyProductRequest, productResp *pb_ec_product.ProductResponse) ec_order.OrderProduct {
	orderProduct := ec_order.OrderProduct{
		Id:                    util.ToMongoId(productResp.Ec.Id),
		OutTradeId:            bson.NewObjectId(),
		Name:                  productResp.Product.Name,
		Spec:                  getProductSpec(product.Sku, *productResp.Product),
		Total:                 1,
		CategoryId:            util.ToMongoId(productResp.Product.Category.Id),
		ProductId:             util.ToMongoId(productResp.Product.Id),
		Number:                productResp.Product.Number,
		BarCode:               productResp.Product.BarCode,
		Type:                  productResp.Product.Type,
		DisableMemberRefund:   productResp.Ec.DisableMemberRefund,
		IsPrepaidCardDisabled: productResp.Ec.IsPrepaidCardDisabled,
		CampaignTags:          []string{"plusBuyProduct"},
		SubType:               productResp.Ec.SubType,
	}
	for _, sku := range productResp.Ec.Skus {
		if sku.Sku == product.Sku {
			orderProduct.StoredValueCardId = util.ToMongoId(sku.StoredValueCardId)
		}
	}
	orderProduct.Picture = orderProduct.Spec.Picture
	if orderProduct.Picture == "" && len(productResp.Product.Pictures) > 0 {
		orderProduct.Picture = productResp.Product.Pictures[0].Url
	}
	if orderProduct.IsPrepaidCardDisabled {
		orderProduct.DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, "product")
	}
	if productResp.Ec.Type == ec_order.ORDER_TYPE_COUPON {
		orderProduct.DisableDiscount(DISCOUNT_TYPE_COUPON, "couponProduct")
		orderProduct.CouponId = util.ToMongoId(productResp.Ec.Skus[0].CouponId)
	}
	if productResp.Ec.Type == ec_order.ORDER_TYPE_VIRTUAL {
		copier.Instance(nil).From(productResp.Ec.RedeemPeriod).CopyTo(&orderProduct.RedeemPeriod)
	}
	orderProduct.DisableDiscount(DISCOUNT_TYPE_MEMBER, "product")
	orderProduct.DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_CAMPAIGN)
	if !plusBuyCampaign.CanUsePrepaidCard {
		orderProduct.DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_CAMPAIGN)
	}
	for _, productSku := range productResp.Ec.Skus {
		if product.Sku != productSku.Sku {
			continue
		}
		for _, p := range plusBuyCampaign.PlusBuyRule.Products {
			if p.Sku != productSku.Sku {
				continue
			}
			orderProduct.Price = p.Price
			break
		}
		orderProduct.TotalAmount = orderProduct.Price * orderProduct.Total
		orderProduct.OriginPrice = uint64(productSku.Price)
		break
	}
	return orderProduct
}

// 校验超值换购活动
func checkPlusBuyCampaign(plusBuyCampaign plus_buy.PlusBuy, campaign *common.PlusBuyCampaign) error {
	if plusBuyCampaign.StartAt.After(time.Now()) || plusBuyCampaign.EndAt.Before(time.Now()) {
		return errors.NewInvalidArgumentErrorWithMessage("campaigns", "campaign status error")
	}
	if len(campaign.Products) > 1 && plusBuyCampaign.PlusBuyRule.Type == plus_buy.PLUS_BUY_RULE_TYPE_SIGNLE {
		return errors.NewInvalidArgumentError("campaigns")
	}
	for _, p := range campaign.Products {
		exists := false
		for _, cp := range plusBuyCampaign.PlusBuyRule.Products {
			if cp.Id.Hex() != p.Id {
				continue
			}
			if cp.Sku != p.Sku {
				continue
			}
			exists = true
		}
		if !exists {
			return errors.NewInvalidArgumentError("campaigns")
		}
	}
	return nil
}

// 计算限时折扣活动
func buildDiscountCampaign(ctx context.Context, order *ec_order.Order, campaign *common.OrderCampaign, builder *OrderBuilder) error {
	if len(campaign.DiscountCampaigns) == 0 {
		return nil
	}
	ids := core_util.ToStringArray(core_util.ExtractArrayField("Id", campaign.DiscountCampaigns))
	campaignMap, err := builder.GetCampaigns(ctx, ec_order.CAMPAIGN_TYPE_DISCOUNT, ids, nil)
	if err != nil {
		return err
	}
	member, err := builder.GetMember(ctx)
	if err != nil {
		return err
	}
	for _, dc := range campaign.DiscountCampaigns {
		discountCampaign, ok := campaignMap[dc.Id].(*pb_eccampaign_discount_campaign.DiscountCampaignDetail)
		if !ok {
			continue
		}
		startAt := core_util.ParseRFC3339(discountCampaign.StartAt)
		endAt := core_util.ParseRFC3339(discountCampaign.EndAt)
		originalStartAt := core_util.ParseRFC3339(discountCampaign.OriginalStartAt)
		originalEndAt := core_util.ParseRFC3339(discountCampaign.OriginalEndAt)
		if startAt.After(time.Now()) ||
			originalStartAt.After(time.Now()) ||
			endAt.Before(time.Now()) ||
			originalEndAt.Before(time.Now()) {
			return errors.NewInvalidArgumentErrorWithMessage("discountCampaigns", "campaign status error")
		}
		var orderCountMap map[string]uint64
		if discountCampaign.Rule.LimitMember > 0 {
			orderCountMap = builder.getCampaignOrderCountMap(ctx, builder.MemberId, ec_order.CAMPAIGN_TYPE_DISCOUNT, ids)
		}
		priceIndex := -1
		if discountCampaign.Rule.RuleType == "tag" {
			priceIndex = eccampaign_share.GetApplicableRuleIndex(discountCampaign.Rule.PriceRules, member)
		}
		indexMap := make(map[string][]int)
		amountMap := make(map[string][]uint64)
		countMap := make(map[string][]uint64)
		skuTotal := make(map[string]uint64) // 单个 sku 下单数量中，能参与活动的数量
	OUTERLOOP:
		for i, op := range order.Products {
			for _, p := range discountCampaign.Products {
				if op.Id.Hex() != p.Id {
					continue
				}
				for _, sku := range p.Skus {
					if sku.Sku != op.Spec.Sku {
						continue
					}
					// 设置了活动库存，库存为 0 了，不能参与活动，只能以原价购买
					if sku.TotalStock > 0 && sku.Stock == 0 {
						continue OUTERLOOP
					}

					productTotal := op.Total
					if sku.TotalStock > 0 && sku.Stock < productTotal {
						productTotal = sku.Stock
					}
					indexMap[op.Id.Hex()] = append(indexMap[op.Id.Hex()], i)
					amountMap[op.Id.Hex()] = append(amountMap[op.Id.Hex()],
						func() uint64 {
							if priceIndex >= 0 {
								return op.PayAmount - productTotal*sku.RulePrices[priceIndex] - (op.Total-productTotal)*sku.Price
							}
							// 商品优惠金额 = 商品总的支付金额 - 参与活动的商品的金额 - 未参与活动（无库存）的商品的金额
							return op.PayAmount - productTotal*sku.CampaignPrice - (op.Total-productTotal)*sku.Price
						}())
					countMap[op.Id.Hex()] = append(countMap[op.Id.Hex()], productTotal)
					skuTotal[op.Spec.Sku] = productTotal
					continue OUTERLOOP
				}
			}
		}
		if len(indexMap) == 0 {
			continue
		}
		var (
			indexArray  []int
			amountArray []uint64
		)
		for productId, indexes := range indexMap {
			if discountCampaign.Rule.LimitMember != uint64(0) {
				capacity := discountCampaign.Rule.LimitMember - orderCountMap[fmt.Sprintf("%s:%s", discountCampaign.Id, productId)]
				array := GetMaxValueIndex(cast.ToIntSlice(countMap[productId]), cast.ToIntSlice(amountMap[productId]), int(capacity))
				for _, i := range array {
					indexArray = append(indexArray, indexes[i])
					amountArray = append(amountArray, amountMap[productId][i])
				}
			} else {
				indexArray = append(indexArray, indexes...)
				amountArray = append(amountArray, amountMap[productId]...)
			}
		}
		tempCampaign := ec_order.Campaign{
			Id:    util.ToMongoId(discountCampaign.Id),
			Title: discountCampaign.Title,
			Type:  ec_order.CAMPAIGN_TYPE_DISCOUNT,
			Extra: map[string]string{
				"displayName": discountCampaign.DisplayName,
			},
		}
		tempDiscount := ec_order.OrderDiscount{
			Id:    util.ToMongoId(discountCampaign.Id),
			Title: discountCampaign.Title,
			Type:  ec_order.CAMPAIGN_TYPE_DISCOUNT,
		}
		for i, index := range indexArray {
			orderProduct := order.Products[index]
			if discountCampaign.Rule.LimitMember != uint64(0) {
				key := fmt.Sprintf("%s:%s", discountCampaign.Id, orderProduct.Id.Hex())
				count, ok := orderCountMap[key]
				if ok && count+orderProduct.Total > discountCampaign.Rule.LimitMember {
					// 超出限购的商品不参与活动
					continue
				}
				orderCountMap[key] += orderProduct.Total
			}
			// 计算活动价格
			tempProductDiscount := tempDiscount
			tempProductDiscount.Amount = int64(amountArray[i])
			orderProduct.Discounts = append(orderProduct.Discounts, tempProductDiscount)
			orderProduct.Campaigns = append(orderProduct.Campaigns, tempCampaign)
			if discountCampaignCount, ok := skuTotal[orderProduct.Spec.Sku]; ok {
				orderProduct.DiscountCampaignCount = discountCampaignCount
			}
			tempDiscount.Amount += tempProductDiscount.Amount

			if discountCampaign.Rule.IsDiscountLimit && len(builder.PrepaidCardIds) > 0 {
				orderProduct.DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_PREPAID_CARD)
			}
			if !discountCampaign.Rule.CanUseCoupon {
				orderProduct.DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_CAMPAIGN)
			} else {
				orderProduct.CouponLimits = share_model.MergeCouponLimit(orderProduct.CouponLimits, discountCampaign.Rule.CouponLimit)
			}
			if !discountCampaign.Rule.CanUsePrepaidCard {
				orderProduct.DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_CAMPAIGN)
			} else {
				orderProduct.PrepaidCardLimits = share_model.MergePrepaidCardLimit(orderProduct.PrepaidCardLimits, discountCampaign.Rule.PrepaidCardLimit)
			}

			orderProduct.DisableDiscount(DISCOUNT_TYPE_MEMBER, DISCOUNT_TYPE_CAMPAIGN)
			orderProduct.DisableDiscount(DISCOUNT_TYPE_BENEFIT_CARD, DISCOUNT_TYPE_CAMPAIGN)
			orderProduct.IsDiscountLimit = discountCampaign.Rule.IsDiscountLimit
			orderProduct.CampaignTags = append(orderProduct.CampaignTags, ec_order.CAMPAIGN_TYPE_DISCOUNT)

			order.Products[index] = orderProduct
			// 扣除活动库存
			err = ReduceDiscountCampaignProductStock(ctx, orderProduct, discountCampaign, builder)
			if err != nil {
				return errors.NewInvalidArgumentErrorWithMessage("discountCampaigns", "stock not enough")
			}
		}
		if tempDiscount.Amount > 0 {
			order.Discounts = append(order.Discounts, tempDiscount)
			order.Campaigns = append(order.Campaigns, tempCampaign)
		}
	}
	return nil
}

func buildRandomDiscountCampaign(ctx context.Context, order *ec_order.Order, campaign *common.OrderCampaign, builder *OrderBuilder) error {
	if campaign.RandomDiscountCampaign == nil {
		return nil
	}
	recordId := campaign.RandomDiscountCampaign.RecordId
	if len(campaign.RandomDiscountCampaign.Ids) > 0 && campaign.RandomDiscountCampaign.AutoSelectRecordId {
		resp, err := proto_client.GetEccampaignRandomDiscountServiceClient().CheckMemberRandomDiscounts(ctx, &pb_eccampaign_random_discount.CheckMemberRandomDiscountsRequest{
			Ids:      campaign.RandomDiscountCampaign.Ids,
			MemberId: builder.MemberId,
		})
		if err != nil {
			return err
		}
		recordId = resp.MaxDiscountAmountRecordId
	}
	if recordId == "" {
		return nil
	}
	recordDetail, err := proto_client.GetEccampaignRandomDiscountServiceClient().GetRandomDiscountRecord(ctx, &pb_eccampaign_random_discount.GetRandomDiscountRecordRequest{
		Id:          recordId,
		ExtraFields: []string{"discountDetail"},
	})
	if err != nil {
		return err
	}
	// 随机立减参与记录生成时已经校验过客户准入条件了，这里只要验证一下参与记录还没过期即可
	expireAt := core_util.ParseRFC3339(recordDetail.ExpireAt)
	campaignEndAt := core_util.ParseRFC3339(recordDetail.DiscountDetail.Time.EndAt)
	if expireAt.Before(time.Now()) || campaignEndAt.Before(time.Now()) || recordDetail.Status != "unused" {
		return errors.NewInvalidArgumentErrorWithMessage("randomDiscountCampaign", "record expired")
	}
	// 校验随机立减活动本身是否已过期
	endAt := core_util.ParseRFC3339(recordDetail.DiscountDetail.Time.EndAt)
	if endAt.Before(time.Now()) {
		return errors.NewInvalidArgumentErrorWithMessage("randomDiscountCampaign", "campaign ended")
	}
	productIds := core_util.ToStringArray(core_util.ExtractArrayField("Id", order.Products))
	ecProductMap, err := builder.GetEcProductMap(ctx, productIds, nil)
	if err != nil {
		return err
	}
	var (
		indexArr           []int
		restDiscountAmount = recordDetail.DiscountAmount
		skus               []string
	)
	for _, product := range recordDetail.DiscountDetail.ProductSetting.Products {
		skus = append(skus, product.Skus...)
	}
	// 过滤出需要参与折扣的商品
	for i, op := range order.Products {
		ecProduct := ecProductMap[op.Id].(ec_product.Product)
		for _, c := range ecProduct.Campaigns {
			// 只有参与了当前立减活动且实付金额不为零的商品才参与分摊优惠金额
			if c.Id.Hex() == recordDetail.DiscountId &&
				op.PayAmount > 0 &&
				util.StrInArray(op.Spec.Sku, &skus) {
				indexArr = append(indexArr, i)
				break
			}
		}
	}
	// 排序，从实付金额小的商品开始分摊优惠，防止存在实付金额比平均优惠金额还小的情况
	sort.Slice(indexArr, func(i, j int) bool {
		return order.Products[indexArr[i]].PayAmount < order.Products[indexArr[j]].PayAmount
	})
	if builder.isCreateOrder {
		// 没有商品是活动商品时，返回报错“商品活动已失效”
		if len(indexArr) == 0 {
			return errors.NewInvalidArgumentErrorWithMessage("randomDiscountCampaign", "product campaign removed")
		}
		// 核销此次随机立减
		_, err = proto_client.GetEccampaignRandomDiscountServiceClient().RedeemRandomDiscountRecord(ctx, &pb_eccampaign_random_discount.RedeemRandomDiscountRecordRequest{
			Id:          recordDetail.Id,
			OrderId:     order.Id.Hex(),
			OrderNumber: order.Number,
		})
		if err != nil {
			return err
		}
	}
	for i, index := range indexArr {
		op := order.Products[index]
		discountAmount := restDiscountAmount / int64(len(indexArr)-i)
		if i == len(indexArr)-1 {
			discountAmount = restDiscountAmount
		}
		payAmount := int64(op.PayAmount)
		if payAmount < discountAmount {
			discountAmount = payAmount
		}
		restDiscountAmount -= discountAmount
		op.Discounts = append(op.Discounts, ec_order.OrderDiscount{
			Id:     util.ToMongoId(recordDetail.DiscountId),
			Title:  recordDetail.DiscountDetail.Name,
			Type:   ec_order.CAMPAIGN_TYPE_RANDOM_DISCOUNT,
			Amount: discountAmount,
		})
		op.Campaigns = append(op.Campaigns, ec_order.Campaign{
			Id:       util.ToMongoId(recordDetail.DiscountId),
			Title:    recordDetail.DiscountDetail.Name,
			Type:     ec_order.CAMPAIGN_TYPE_RANDOM_DISCOUNT,
			RecordId: util.ToMongoId(recordDetail.Id),
		})
		op.IsDiscountLimit = recordDetail.DiscountDetail.IsDiscountLimit
		if op.IsDiscountLimit {
			if len(builder.DiscountIds) > 0 {
				op.DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_COUPON)
			}
			if len(builder.PrepaidCardIds) > 0 {
				op.DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_PREPAID_CARD)
			}
		}
		if !recordDetail.DiscountDetail.CanUseCoupon {
			op.DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_CAMPAIGN)
		} else {
			op.CouponLimits = share_model.MergeCouponLimit(op.CouponLimits, recordDetail.DiscountDetail.CouponLimit)
		}
		if !recordDetail.DiscountDetail.CanUsePrepaidCard {
			op.DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_CAMPAIGN)
		} else {
			op.PrepaidCardLimits = share_model.MergePrepaidCardLimit(op.PrepaidCardLimits, recordDetail.DiscountDetail.PrepaidCardLimit)
		}
		op.CampaignTags = append(op.CampaignTags, ec_order.CAMPAIGN_TYPE_RANDOM_DISCOUNT)
		order.Products[index] = op
	}
	if restDiscountAmount < recordDetail.DiscountAmount {
		order.Discounts = append(order.Discounts, ec_order.OrderDiscount{
			Id:     util.ToMongoId(recordDetail.DiscountId),
			Title:  recordDetail.DiscountDetail.Name,
			Type:   ec_order.CAMPAIGN_TYPE_RANDOM_DISCOUNT,
			Amount: recordDetail.DiscountAmount - restDiscountAmount,
		})
		order.Campaigns = append(order.Campaigns, ec_order.Campaign{
			Id:       util.ToMongoId(recordDetail.DiscountId),
			RecordId: util.ToMongoId(recordDetail.Id),
			Title:    recordDetail.DiscountDetail.Name,
			Type:     ec_order.CAMPAIGN_TYPE_RANDOM_DISCOUNT,
		})

	}
	return nil
}

func ReduceDiscountCampaignProductStock(ctx context.Context, orderProduct ec_order.OrderProduct, campaign *pb_eccampaign_discount_campaign.DiscountCampaignDetail, builder *OrderBuilder) error {
	if !builder.isCreateOrder {
		return nil
	}
	for _, p := range campaign.Products {
		for _, sku := range p.Skus {
			// totalStock 为 0 时，未设置活动库存，不需要扣减库存
			if orderProduct.Spec.Sku == sku.Sku && sku.TotalStock == 0 {
				return nil
			}
		}
	}
	req := &pb_eccampaign_discount_campaign.UpdateDiscountCampaignStockRequest{
		Id:        campaign.Id,
		ProductId: orderProduct.Id.Hex(),
		Sku:       orderProduct.Spec.Sku,
		Stock:     orderProduct.DiscountCampaignCount,
		Operation: "reduce",
	}
	_, err := proto_client.GetEccampaignDiscountCampaignServiceClient().UpdateDiscountCampaignStock(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

// 扣减商品库存
func checkAndReduceProductsStock(ctx context.Context, order *ec_order.Order, builder *OrderBuilder) (ec_product.StockOperator, map[string]int, error) {
	reducedStockMap := map[string]int{}
	stock := getStockForCheck(ctx, order, builder)
	if stock == nil {
		// 不校验库存
		return stock, reducedStockMap, nil
	}

	isProductUnderstocked := false   // 用于判断是否有商品库存不足
	isProductSoldOut := false        // 用于判断是否有商品售罄
	isAllProductUnderstocked := true // 判断是否所有商品都是库存不足，仅 isProductUnderStock 为 true 时有效
	isAllProductSoldOut := true      // 判断是否所有商品已售罄，仅 isProductSoldOut 为 true 时有效
	for _, product := range order.Products {
		// 储值卡商品不校验库存
		if product.SubType == ec_order.ORDER_SUB_TYPE_STORED_VALUE_CARD {
			continue
		}
		sku := product.Spec.Sku
		total := int(product.Total)
		if ec_order.CheckProductCampaignExistence(product, ec_order.CAMPAIGN_TYPE_PRESENT) || ec_order.CheckProductCampaignExistence(product, ec_order.CAMPAIGN_TYPE_PRESENT_COUPON) {
			// 买赠活动商品库存另算
			continue
		}
		ctx = context.WithValue(ctx, "storeId", order.StoreId)
		err := stock.ReduceStock(ctx, sku, total)
		if err != nil {
			if isPresentCouponProduct(product) {
				return stock, reducedStockMap, errors.NewInvalidArgumentErrorWithMessage("discount", "present is understoked")
			}
			// 扣库存失败判断是否已售罄
			stockStatus, err := stock.GetStatus(ctx, sku, total)
			if err != nil {
				return nil, nil, err
			}
			if stockStatus == ec_product.STATUS_SOLD_OUT {
				isProductSoldOut = true
			} else {
				isProductUnderstocked = true
				isAllProductSoldOut = false
			}
			continue // 只有减扣库存成功的才需要添加到回退库存的 map
		} else {
			isAllProductUnderstocked = false
			isAllProductSoldOut = false
		}
		reducedStockMap[product.Spec.Sku] = total
	}

	if isProductUnderstocked || isProductSoldOut {
		if isProductSoldOut {
			if isAllProductSoldOut {
				// 全部商品已售罄
				return stock, reducedStockMap, errors.NewInvalidArgumentErrorWithMessage("stock", "all products sold out.")
			}
			if isProductUnderstocked {
				// 部分商品售罄，部分商品库存不足
				return stock, reducedStockMap, errors.NewInvalidArgumentErrorWithMessage("stock", "partial products are understocked or sold out.")
			}
			// 仅部分商品售罄
			return stock, reducedStockMap, errors.NewInvalidArgumentErrorWithMessage("stock", "partial products sold out.")
		}
		if isProductUnderstocked {
			if isAllProductUnderstocked {
				// 全部商品库存不足,且没有已售罄商品
				return stock, reducedStockMap, errors.NewInvalidArgumentErrorWithMessage("stock", "all products are understocked.")
			}
			// 部分商品库存不足，且没有已售罄商品
			return stock, reducedStockMap, errors.NewInvalidArgumentErrorWithMessage("stock", "partial products are understocked.")
		}
	}
	return stock, reducedStockMap, nil
}

func getStockForCheck(ctx context.Context, order *ec_order.Order, builder *OrderBuilder) ec_product.StockOperator {
	if builder.isPurchase && order.IsConsignment() {
		return builder.GetConsignmentStock(ctx, order)
	}
	return getStock(ctx, order)
}

func getStock(ctx context.Context, order *ec_order.Order) ec_product.StockOperator {
	if order.IsCouponType() || order.IsVirtualType() {
		return ec_product.CProduct
	}
	isPickUpMallOrder, err := mall_service.IsPickUpMallOrder(ctx, order.StoreId.Hex())
	if err != nil {
		log.Warn(ctx, "check is pick up mall order failed", log.Fields{"error": err})
	}
	if isPickUpMallOrder { // 小店自提校验小店自提库存
		return mall_service.GenMallProductStockByStoreId(order.StoreId)
	} else {
		var ds *pb_setting.GetDeliverySettingResponse
		ds, _ = ec_client.SettingService.GetDeliverySetting(ctx, &request.EmptyRequest{})
		if ds == nil {
			return nil
		}
		switch order.Method {
		case ec_order.ORDER_DELIVERY_METHOD_PICKUP, ec_order.ORDER_DELIVERY_METHOD_CITY_EXPRESS:
			if !ec_share_model.IsStoreProductStockEnabled(ctx) {
				return nil
			}
			return ec_store_warehouse.CStoreProductStock
		case "":
			// method 为空时，说明是在购物车校验库存。
			// 快递配送开启+总部发货时，只要开启了门店自提或者同城配送就不校验库存，因为不确定使用哪种配送方式，需要进一步确定。
			if ds.Logistics.Enabled && ds.Logistics.ShipSetting.Operator == setting_model.DELIVERY_SETTING_OPERATOR_USER && (ds.CityExpress.Enabled || ds.Reservation.Enabled) {
				return nil
			}
		}

		// 走到这里说明是快递配送或者购物车校验库存（没有混合配送方式）的情况
		switch ds.Logistics.ShipSetting.Operator {
		case setting_model.DELIVERY_SETTING_OPERATOR_USER:
			if ec_share_model.IsProductStockEnabled(ctx, ds) {
				return ec_product.CProduct
			}
		case setting_model.DELIVERY_SETTING_OPERATOR_STAFF:
			if ec_share_model.IsStoreProductStockEnabled(ctx) {
				return ec_store_warehouse.CStoreProductStock
			}
		}
	}
	return nil
}

func isPresentCouponProduct(product ec_order.OrderProduct) bool {
	for _, c := range product.Campaigns {
		if c.Type == ec_order.CAMPAIGN_TYPE_PRESENT_COUPON {
			return true
		}
	}
	return false
}

// 格式化赠品信息
func formatPresent(ctx context.Context, present *pb_marketing.PresentDetail, campaignId, discountId bson.ObjectId, productIds []string) *ec_order.OrderProduct {
	product, err := GetProductById(ctx, present.ProductId)
	if err != nil || product == nil {
		return nil
	}

	resp := &ec_order.OrderProduct{
		Id:           bson.ObjectIdHex(present.Id),
		OutTradeId:   bson.NewObjectId(),
		Name:         present.Name,
		CategoryId:   bson.ObjectIdHex(product.Category.Id),
		Picture:      present.Picture,
		Total:        present.PresentTotal,
		OriginPrice:  present.OriginPrice,
		Price:        present.Price,
		DisplayPrice: present.Price,
		Spec:         getProductSpec(present.Spec.Sku, *product),
		BarCode:      product.BarCode,
		ProductId:    bson.ObjectIdHex(present.ProductId),
		Number:       present.Number,
		Campaigns: append([]ec_order.Campaign{}, ec_order.Campaign{
			Id:           campaignId,
			Type:         ec_order.CAMPAIGN_TYPE_PRESENT,
			PresentLevel: present.Level,
			DiscountId:   discountId,
			ProductIds:   util.ToMongoIds(productIds),
			Count:        present.Count,
		}),
		CouponId:    util.ToMongoId(present.CouponId),
		Type:        product.Type,
		OriginTotal: present.PresentTotal,
	}
	if resp.Type == ec_order.ORDER_TYPE_VIRTUAL {
		copier.Instance(nil).From(present.ProductRedeemPeriod).CopyTo(&resp.RedeemPeriod)
	}

	return resp
}

// 校验拼团活动
func checkGrouponCampaignBeforeCreate(ctx context.Context, order *ec_order.Order, campaign *common.OrderCampaign, builder *OrderBuilder) error {
	if !builder.isPurchase {
		return errors.NewInvalidArgumentErrorWithMessage("campaigns.id", "purchase can not join groupon")
	}
	grouponCampaignMap, err := builder.GetCampaigns(ctx, ec_order.CAMPAIGN_TYPE_GROUPON, []string{campaign.GrouponCampaigns.Id}, nil)
	if err != nil {
		return err
	}
	grouponCampaign, ok := grouponCampaignMap[campaign.GrouponCampaigns.Id].(*pb_marketing.GrouponCampaignDetail)
	if !ok {
		return errors.NewNotExistsError("campaign")
	}
	if err := checkGrouponLimit(ctx, order, grouponCampaign, builder); err != nil {
		return err
	}
	if time.Now().Before(core_util.ParseRFC3339(grouponCampaign.StartAt)) || time.Now().After(core_util.ParseRFC3339(grouponCampaign.EndAt)) {
		// 活动不在进行中
		return errors.NewInvalidArgumentErrorWithMessage("campaigns.id", "campaign status error")
	}
	tempCampaign := ec_order.Campaign{
		Id:            bson.ObjectIdHex(campaign.GrouponCampaigns.Id),
		Type:          ec_order.CAMPAIGN_TYPE_GROUPON,
		Title:         grouponCampaign.Title,
		GrouponStatus: marketing_model.GROUPON_RECORD_STATUS_RUNNING, // 新开团的订单也要写入拼团状态，以便 ListOrders 排除
	}
	if campaign.GrouponCampaigns.GrouponId != "" {
		// 加入拼团
		if grouponCampaign.Type == marketing_model.GROUPON_CAMPAIGN_TYPE_NEW && hasPaidOrder(ctx, order.MemberId) {
			return errors.NewInvalidArgumentErrorWithMessage("campaigns.id", "campaign type error")
		}

		orderCount, err := builder.GetGrouponRecordOrderCount(ctx, grouponCampaign.Id, campaign.GrouponCampaigns.GrouponId)
		if err != nil {
			return err
		}
		if uint64(orderCount) >= grouponCampaign.RequiredCount {
			return errors.NewNotEnabledError("groupon count limit")
		}
		grouponRecord, err := builder.GetGrouponRecord(ctx, campaign.GrouponCampaigns.GrouponId)
		if err != nil {
			return err
		}
		if grouponRecord.Status != marketing_model.GROUPON_RECORD_STATUS_RUNNING {
			return errors.NewInvalidArgumentErrorWithMessage("grouponRecordId", "groupon is not running")
		}
		tempCampaign.GrouponStatus = grouponRecord.Status
		tempCampaign.GrouponRecordEndAt = grouponRecord.EndAt
		tempCampaign.GrouponRecordId = bson.ObjectIdHex(campaign.GrouponCampaigns.GrouponId)
	}

	order.Campaigns = append(order.Campaigns, tempCampaign)
	order.MaskType = ec_order.MASK_TYPE_GROUPON
	return nil
}

func hasPaidOrder(ctx context.Context, memberId bson.ObjectId) bool {
	selector := bson.M{
		"accountId":        util.GetAccountIdAsObjectId(ctx),
		"memberId":         memberId,
		"histories.status": "paid",
		"isDeleted":        false,
	}
	return ec_order.Common.Exist(ctx, ec_order.C_ORDER, selector)
}

// 判断商品是否受区域限购影响
func canByProductInArea(ctx context.Context, order *ec_order.Order, builder *OrderBuilder, areaRestriction ec_product.AreaRestriction) bool {
	if builder.memberAddress == nil {
		return true
	}
	if order.Operator == setting.DELIVERY_SETTING_OPERATOR_STAFF || order.Method == ec_order.ORDER_DELIVERY_METHOD_PICKUP {
		// 门店发货或者自提的情况不检查区域限购
		return true
	}
	// 代销小店订单不受区域限购
	if len(order.Tags) != 0 && core_util.ContainsAll(&ec_order.MALL_ORDER_TAGS, &order.Tags) {
		return true
	}
	if builder.CampaignConfig != nil && builder.CampaignConfig.AreaRestrictionDisable {
		return true
	}
	province := builder.memberAddress.Province
	city := fmt.Sprintf("%s:%s", builder.memberAddress.Province, builder.memberAddress.City)
	district := fmt.Sprintf("%s:%s:%s", builder.memberAddress.Province, builder.memberAddress.City, builder.memberAddress.District)
	if order.Method == ec_order.ORDER_DELIVERY_METHOD_EXPRESS {
		ds := builder.GetDeliverySetting(ctx)
		if ds != nil && ds.Logistics.ShipSetting.Type == LOGISTICS_SHIP_SETTING_TYPE_PARTY_AREAS_NON_SHIPPING {
			intersections := core_util.GetArraysIntersection([]string{province, city, district}, ds.Logistics.ShipSetting.NonShippingAreas)
			if len(intersections) > 0 {
				return false
			}
			noSuffixAreas := removeAreasProvinceSuffix(ds.Logistics.ShipSetting.NonShippingAreas)
			intersections = core_util.GetArraysIntersection([]string{province, city, district}, noSuffixAreas)
			if len(intersections) > 0 {
				return false
			}
		}
	}

	intersections := core_util.GetArraysIntersection([]string{province, city, district}, areaRestriction.Areas)
	if len(intersections) == 0 {
		noSuffixAreas := removeAreasProvinceSuffix(areaRestriction.Areas)
		intersections = core_util.GetArraysIntersection([]string{province, city, district}, noSuffixAreas)
	}
	return len(intersections) > 0 == areaRestriction.IsAllowed
}

// 删除购物车商品
func removeBoughtCartProducts(ctx context.Context, order *ec_order.Order) {
	productIds := []bson.ObjectId{}
	for _, product := range order.Products {
		productIds = append(productIds, product.Id)
	}
	if util.StrInArray(ec_order.ORDER_TAGS_PROXY_ORDER, &order.Tags) {
		removeProxyOrderCartProducts(ctx, order, productIds)
		return
	}
	ec_order.CCart.RemoveCheckedCartProductsByMemberIdAndProductIds(ctx, order.MemberId.Hex(), productIds)
}

func removeProxyOrderCartProducts(ctx context.Context, order *ec_order.Order, productIds []bson.ObjectId) {
	proxyOrderId := ""
	if order.Extra != nil && order.Extra["storeSn"] != nil {
		proxyOrderId = order.Extra["proxyOrderId"].(string)
	}
	condition := bson.M{
		"accountId": order.AccountId,
		"memberId":  order.MemberId,
		"checked":   true,
		"productId": bson.M{
			"$in": productIds,
		},
		"addedBy": ec_order.ORDER_TAGS_PROXY_ORDER,
	}
	if proxyOrderId != "" {
		condition["campaignId"] = proxyOrderId
	}
	ec_order.CCart.RemoveCartProductsByCondition(ctx, condition)
}

// 计算会员权益
func preCalcMemberDiscount(ctx context.Context, order *ec_order.Order, builder *OrderBuilder) error {
	member, err := builder.GetMember(ctx)
	if err != nil {
		return err
	}
	if !canUseMemberDiscount(builder, member) {
		return nil
	}

	privilege, err := builder.GetMemberDiscountPrivilege(ctx)
	if err != nil {
		return err
	}
	if privilege == nil || !privilege.IsEnabled || privilege.Discount == 0 {
		return nil
	}
	for i, p := range order.Products {
		if !p.IsMemberDiscountEnabled {
			continue
		}
		order.Products[i].IsDiscountLimit = privilege.Privilege.IsDiscountLimit
		if order.Products[i].IsDiscountLimit {
			if len(builder.DiscountIds) > 0 {
				order.Products[i].DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_COUPON)
			}
			if len(builder.PrepaidCardIds) > 0 {
				order.Products[i].DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_PREPAID_CARD)
			}
		}
		if !privilege.Privilege.CanUseWithPrepaidCards && len(builder.PrepaidCardIds) > 0 {
			// 储值卡和会员折扣不能同时使用时，如果已选中储值卡则会员折扣不可使用
			order.Products[i].DisableDiscount(DISCOUNT_TYPE_MEMBER, DISCOUNT_TYPE_PREPAID_CARD)
			continue
		}
		if !privilege.Privilege.CanUseWithCoupons {
			// 优惠券和会员折扣不能共用时，兼容旧的逻辑，也就是如果选择了优惠券将不使用会员折扣，新的自动选择优惠券逻辑由后端选择，不会存在
			if builder.DiscountGroup == nil && len(builder.DiscountIds) > 0 {
				order.Products[i].DisableDiscount(DISCOUNT_TYPE_MEMBER, DISCOUNT_TYPE_COUPON)
				continue
			}
			order.Products[i].DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_MEMBER)
		}
		if !privilege.Privilege.CanUseWithPrepaidCards {
			order.Products[i].DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_MEMBER)
		} else {
			if privilege.Privilege.PrepaidCardLimit != nil {
				privilege.Privilege.PrepaidCardLimit.Scene = share_model.SCENE_PRIVILEGE
				order.Products[i].PrepaidCardLimits = share_model.MergePrepaidCardLimit(order.Products[i].PrepaidCardLimits, privilege.Privilege.PrepaidCardLimit)
			}
		}
		order.Products[i].DisableDiscount(DISCOUNT_TYPE_BENEFIT_CARD, DISCOUNT_TYPE_MEMBER)
	}
	return nil
}

func calcMemberDiscount(ctx context.Context, order *ec_order.Order, builder *OrderBuilder) error {
	member, err := builder.GetMember(ctx)
	if err != nil {
		return err
	}
	if !canUseMemberDiscount(builder, member) {
		return nil
	}
	privilege, err := builder.GetMemberDiscountPrivilege(ctx)
	if err != nil {
		return err
	}
	if privilege == nil || !privilege.IsEnabled || privilege.Discount == 0 {
		return nil
	}
	memberLevel := uint64(member.Level)
	if privilege.MemberType == "paidMember" {
		memberLevel = 0
	}
	discounter := NewMemberDiscounter(privilege, memberLevel)
	discounter.Calc(ctx, order)
	return nil
}

func canUseMemberDiscount(builder *OrderBuilder, member *pb_member.MemberDetailResponse) bool {
	if !builder.IsDiscountEnable(DISCOUNT_TYPE_MEMBER) {
		return false
	}
	if member.BlockedStatus == 2 || member.IsDisabled {
		return false
	}
	if builder.isMall {
		return false
	}
	if len(builder.MemberPaidCardRecordIds) > 0 {
		return false
	}
	if isCampaignDisableMemberDiscount(builder) {
		return false
	}
	return true
}

func isCampaignDisableMemberDiscount(builder *OrderBuilder) bool {
	if builder.CampaignConfig != nil && len(builder.CampaignConfig.Campaigns) > 0 {
		for _, item := range builder.CampaignConfig.Campaigns {
			if item.MemberDiscountDisabled {
				return true
			}
		}
	}
	return false
}

// 计算活动折扣
func calcCampaignDiscount(ctx context.Context, order *ec_order.Order, builder *OrderBuilder) error {
	// 黑名单用户不能享受活动优惠
	if builder.member.BlockedStatus == 2 {
		return nil
	}
	if !builder.IsDiscountEnable(DISCOUNT_TYPE_CAMPAIGN) {
		return nil
	}
	if len(builder.MemberPaidCardRecordIds) > 0 {
		// 权益卡订单不参与活动
		return nil
	}
	builder.Campaigns = groupCampaigns(builder.Campaigns)
	for index, campaign := range builder.Campaigns {
		// 计算支付金额
		calcPayAmount(order)
		switch campaign.Type {
		case ec_order.CAMPAIGN_TYPE_GROUPON: // 拼团活动
			err := calcGrouponDiscount(ctx, order, builder, campaign)
			if err != nil {
				return err
			}
		case ec_order.CAMPAIGN_TYPE_PACKAGE: // 促销套餐活动
			err := calcPackageDiscount(ctx, order, builder, campaign)
			if err != nil {
				return err
			}
		case ec_order.CAMPAIGN_TYPE_PRESENT: // 买赠活动
			err := calcPresentDiscount(ctx, order, builder, campaign, index)
			if err != nil {
				return err
			}
		case ec_order.CAMPAIGN_TYPE_OPTIONAL_PACKAGE: // 任选打包活动
			err := calcOptionalPackageDiscount(ctx, order, builder, campaign)
			if err != nil {
				return err
			}
		case ec_order.CAMPAIGN_TYPE_PLUS_BUY: // 超值换购活动
			err := buildPlusBuyCampaign(ctx, order, campaign, builder, index)
			if err != nil {
				return err
			}
		case ec_order.CAMPAIGN_TYPE_DISCOUNT: // 限时折扣活动
			err := buildDiscountCampaign(ctx, order, campaign, builder)
			if err != nil {
				return err
			}
		case ec_order.CAMPAIGN_TYPE_RANDOM_DISCOUNT: // 随机立减活动
			err := buildRandomDiscountCampaign(ctx, order, campaign, builder)
			if err != nil {
				return err
			}
		}
	}
	if builder.CampaignConfig == nil {
		return nil
	}
	// 计算支付金额
	calcPayAmount(order)
	// 秒杀、预售、付邮试用等活动的计算逻辑
	// 相关活动参与入口：JoinFlashSaleCampaign、JoinPresellCampaign、JoinTrialCampaign
	for _, campaign := range builder.CampaignConfig.Campaigns {
		// 代客下单，借用 CampaignConfig 字段标识，不用计算优惠
		if campaign.Type == "proxyOrder" {
			continue
		}
		tempCampaign := ec_order.Campaign{}
		copier.Instance(nil).From(campaign).CopyTo(&tempCampaign)
		tempDiscount := ec_order.OrderDiscount{
			Id:           bson.ObjectIdHex(campaign.Id),
			Title:        campaign.Title,
			Type:         ec_order.ORDER_DISCOUNT_TYPE_CAMPAIGN,
			CampaignType: campaign.Type,
		}
		order.Campaigns = append(order.Campaigns, tempCampaign)
		tempConfigDiscountMap := map[string]*ec_order.OrderDiscount{}
		for i, p := range order.Products {
			for _, cp := range campaign.Products {
				if p.Id.Hex() != cp.Id {
					continue
				}
				if p.Spec.Sku != cp.Sku {
					continue
				}
				if campaign.MemberDisabled || campaign.MemberDiscountDisabled {
					p.DisableDiscount(DISCOUNT_TYPE_MEMBER, DISCOUNT_TYPE_CAMPAIGN)
				}
				if campaign.IsMemberPaidCardDisabled {
					p.DisableDiscount(DISCOUNT_TYPE_BENEFIT_CARD, DISCOUNT_TYPE_CAMPAIGN)
				}
				if campaign.IsScoreDisabled {
					p.DisableDiscount(DISCOUNT_TYPE_SCORE, DISCOUNT_TYPE_CAMPAIGN)
				}
				if campaign.IsDiscountLimit && len(builder.PrepaidCardIds) > 0 {
					p.DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_PREPAID_CARD)
				}
				if campaign.CouponDisabled {
					p.DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_CAMPAIGN)
				}
				if campaign.PrepaidCardDisabled {
					p.DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_CAMPAIGN)
				}
				if !util.StrInArray(campaign.Type, &[]string{ec_order.CAMPAIGN_TYPE_PERIOD_BUY}) {
					p.CampaignTags = append(p.CampaignTags, campaign.Type)
				}
				p.CouponLimits = share_model.MergeCouponLimit(p.CouponLimits, campaign.CouponLimit)
				p.PrepaidCardLimits = share_model.MergePrepaidCardLimit(p.PrepaidCardLimits, campaign.PrepaidCardLimit)
				p.IsDiscountLimit = campaign.IsDiscountLimit
				p.Campaigns = append(p.Campaigns, tempCampaign)
				productDiscount := tempDiscount
				// 产品卡计算折扣时，商品的价格不需要显示优惠后的价格
				if campaign.Type != ec_order.TYPE_PRODUCT_CARD && !builder.isCreateOrder {
					p.DisplayPrice = cp.Price
				}

				// 处理通过配置传进来的折扣信息
				if len(cp.Discounts) > 0 {
					configDiscounts := []ec_order.OrderDiscount{}
					copier.Instance(nil).From(cp.Discounts).CopyTo(&configDiscounts)
					p.Discounts = append(p.Discounts, configDiscounts...)
					// 把活动 id，type 和 campaignType 一样的优惠加到一起
					for _, d := range configDiscounts {
						key := fmt.Sprintf("%s:%s:%s", d.Id, d.Type, d.CampaignType)
						if _, ok := tempConfigDiscountMap[key]; !ok {
							tempDiscount := d
							tempConfigDiscountMap[key] = &tempDiscount
							continue
						}
						tempConfigDiscountMap[key].Amount += d.Amount
					}
				}

				newTotalAmount := uint64(cp.Price) * p.Total
				if p.TotalAmount <= newTotalAmount {
					order.Products[i] = p
					continue
				}
				productDiscount.Amount = int64(p.TotalAmount - newTotalAmount)
				tempDiscount.Amount += productDiscount.Amount
				p.Discounts = append(p.Discounts, productDiscount)
				p.TotalAmount = newTotalAmount

				order.Products[i] = p
			}
		}
		order.Discounts = append(order.Discounts, tempDiscount)
		for _, discount := range tempConfigDiscountMap {
			order.Discounts = append(order.Discounts, *discount)
		}
		// 处理传入的赠品
		err := setCampaignPresents(ctx, order, campaign, builder)
		if err != nil {
			return err
		}
	}
	return nil
}

// 把活动分组
func groupCampaigns(campaigns []*common.OrderCampaign) []*common.OrderCampaign {
	groupedCampaigns := []*common.OrderCampaign{}
	for i, c := range campaigns {
		switch c.Type {
		case ec_order.CAMPAIGN_TYPE_PACKAGE, ec_order.CAMPAIGN_TYPE_PRESENT:
			index := -1
			for j, gc := range groupedCampaigns {
				if gc.Type == c.Type {
					index = j
				}
			}
			if index == -1 {
				groupedCampaigns = append(groupedCampaigns, campaigns[i])
			} else {
				groupedCampaigns[index].PresentCampaigns = append(groupedCampaigns[index].PresentCampaigns, campaigns[i].PresentCampaigns...)
				groupedCampaigns[index].PackageCampaigns = append(groupedCampaigns[index].PackageCampaigns, campaigns[i].PackageCampaigns...)
			}
		default:
			groupedCampaigns = append(groupedCampaigns, campaigns[i])
		}
	}
	campaignWeight := map[string]int{
		ec_order.CAMPAIGN_TYPE_PACKAGE:  10,
		ec_order.CAMPAIGN_TYPE_DISCOUNT: 9,
		ec_order.CAMPAIGN_TYPE_PRESENT:  8,
	}
	sort.Slice(groupedCampaigns, func(i, j int) bool {
		if campaignWeight[groupedCampaigns[i].Type] > campaignWeight[groupedCampaigns[j].Type] {
			return true
		}
		return false
	})
	return groupedCampaigns
}

// 计算拼团活动折扣
func calcGrouponDiscount(ctx context.Context, order *ec_order.Order, builder *OrderBuilder, campaign *common.OrderCampaign) error {
	discount := ec_order.OrderDiscount{
		Type: ec_order.ORDER_DISCOUNT_TYPE_GROUPON,
	}
	grouponCampaignMap, err := builder.GetCampaigns(ctx, ec_order.CAMPAIGN_TYPE_GROUPON, []string{campaign.GrouponCampaigns.Id}, nil)
	if err != nil {
		return err
	}
	grouponCampaign, ok := grouponCampaignMap[campaign.GrouponCampaigns.Id].(*pb_marketing.GrouponCampaignDetail)
	if !ok {
		return errors.NewNotExistsError("campaign")
	}
	if !grouponCampaign.Rule.CanUsePrepaidCard && len(builder.PrepaidCardIds) > 0 {
		return errors.NewNotEnabledError("prepaidCards")
	}
	for i, product := range order.Products {
		if grouponCampaign.Product.Id != product.Id.Hex() {
			continue
		}
		for _, item := range grouponCampaign.Skus {
			if item.Sku == product.Spec.Sku {
				if grouponCampaign.Rule.IsDiscountLimit {
					if len(builder.PrepaidCardIds) > 0 {
						product.DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_PREPAID_CARD)
					}
				}
				if !grouponCampaign.Rule.CanUseCoupon {
					product.DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_CAMPAIGN)
				}
				if !grouponCampaign.Rule.CanUsePrepaidCard {
					product.DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_CAMPAIGN)
				}
				product.DisableDiscount(DISCOUNT_TYPE_MEMBER, DISCOUNT_TYPE_CAMPAIGN)
				product.DisableDiscount(DISCOUNT_TYPE_BENEFIT_CARD, DISCOUNT_TYPE_CAMPAIGN)
				product.CouponLimits = share_model.MergeCouponLimit(product.CouponLimits, grouponCampaign.Rule.CouponLimit)
				product.PrepaidCardLimits = share_model.MergePrepaidCardLimit(product.PrepaidCardLimits, grouponCampaign.Rule.PrepaidCardLimit)
				product.CampaignTags = append(product.CampaignTags, ec_order.CAMPAIGN_TYPE_GROUPON)
				product.IsDiscountLimit = grouponCampaign.Rule.IsDiscountLimit
				if !builder.isCreateOrder {
					product.DisplayPrice = item.Price
				}
				newTotalAmount := item.Price * product.Total
				if product.TotalAmount < newTotalAmount {
					discount.Amount = 0
				} else {
					discount.Amount = int64(product.TotalAmount - newTotalAmount)
				}
				product.Discounts = append(product.Discounts, discount)
				product.TotalAmount = newTotalAmount
				order.Products[i] = product
			}
		}
	}
	order.Discounts = append(order.Discounts, discount)
	return nil
}

// 计算促销套餐活动折扣
func calcPackageDiscount(ctx context.Context, order *ec_order.Order, builder *OrderBuilder, campaign *common.OrderCampaign) error {
	ids := core_util.ExtractArrayStringField("Id", campaign.PackageCampaigns)
	packageCampaignMap, err := builder.GetCampaigns(ctx, ec_order.CAMPAIGN_TYPE_PACKAGE, ids, nil)
	if err != nil {
		return err
	}
	countMap := builder.getCampaignOrderCountMap(ctx, builder.MemberId, ec_order.CAMPAIGN_TYPE_PACKAGE, ids)
	for _, packageCampaign := range campaign.PackageCampaigns {
		packageId := bson.NewObjectId()
		packageCampaignDetail, ok := packageCampaignMap[packageCampaign.Id].(*pb_marketing.PackageCampaignDetail)
		if !ok {
			return errors.NewNotExistsError("campaign")
		}
		orderDiscount := ec_order.OrderDiscount{
			Id:      bson.ObjectIdHex(packageCampaign.Id),
			Type:    ec_order.ORDER_DISCOUNT_TYPE_PACKAGE,
			Title:   packageCampaignDetail.Title,
			Picture: packageCampaignDetail.Picture,
		}
		tempCampaign := ec_order.Campaign{
			Id:        bson.ObjectIdHex(packageCampaign.Id),
			Title:     packageCampaignDetail.Title,
			Type:      ec_order.CAMPAIGN_TYPE_PACKAGE,
			PackageId: packageId,
		}
		err := checkPackageCampaign(packageCampaignDetail, builder, countMap)
		if err != nil {
			return err
		}
		skus := core_util.ToStringArray(core_util.ExtractArrayField("Sku", packageCampaign.Products))
		switch packageCampaignDetail.Type {
		case marketing_model.PACKAGE_CAMPAIGN_TYPE_SETTLED:
			for _, campaignProduct := range packageCampaignDetail.Products {
				for _, campaignProductSku := range campaignProduct.Skus {
					if !core_util.StrInArray(campaignProductSku.Sku, &skus) {
						continue
					}
					for i, p := range order.Products {
						if p.Id.Hex() != campaignProduct.Id {
							continue
						}
						if p.Spec.Sku != campaignProductSku.Sku {
							continue
						}
						if p.Total != uint64(campaignProduct.Count) {
							continue
						}
						isCalculated := false
						for _, discount := range p.Discounts {
							if discount.Type == ec_order.ORDER_DISCOUNT_TYPE_PACKAGE {
								isCalculated = true
								break
							}
						}
						if isCalculated {
							continue
						}
						discountAmount := int64(p.TotalAmount) - int64(campaignProduct.Count*campaignProductSku.Price)
						if discountAmount < 0 {
							discountAmount = 0
						}
						productDiscount := ec_order.OrderDiscount{
							Id:      bson.ObjectIdHex(packageCampaign.Id),
							Type:    ec_order.ORDER_DISCOUNT_TYPE_PACKAGE,
							Title:   packageCampaignDetail.Title,
							Picture: packageCampaignDetail.Picture,
							Amount:  discountAmount,
						}
						order.Products[i].DisableDiscount(DISCOUNT_TYPE_MEMBER, DISCOUNT_TYPE_CAMPAIGN)
						order.Products[i].DisableDiscount(DISCOUNT_TYPE_BENEFIT_CARD, DISCOUNT_TYPE_CAMPAIGN)
						if packageCampaignDetail.IsDiscountLimit && len(builder.PrepaidCardIds) > 0 {
							order.Products[i].DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_PREPAID_CARD)
						}
						if !packageCampaignDetail.IsUseCouponAllowed {
							order.Products[i].DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_CAMPAIGN)
						} else {
							order.Products[i].CouponLimits = share_model.MergeCouponLimit(order.Products[i].CouponLimits, packageCampaignDetail.CouponLimit)
						}
						if !packageCampaignDetail.CanUsePrepaidCard {
							order.Products[i].DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_CAMPAIGN)
						} else {
							order.Products[i].PrepaidCardLimits = share_model.MergePrepaidCardLimit(order.Products[i].PrepaidCardLimits, packageCampaignDetail.PrepaidCardLimit)
						}
						order.Products[i].IsDiscountLimit = packageCampaignDetail.IsDiscountLimit
						order.Products[i].TotalAmount = uint64(campaignProductSku.Price) * order.Products[i].Total
						order.Products[i].Discounts = append(order.Products[i].Discounts, productDiscount)
						order.Products[i].Campaigns = append(order.Products[i].Campaigns, tempCampaign)
						order.Products[i].CampaignTags = append(order.Products[i].CampaignTags, ec_order.CAMPAIGN_TYPE_PACKAGE)
						orderDiscount.Amount += productDiscount.Amount
						break
					}
				}
			}
		}
		order.Discounts = append(order.Discounts, orderDiscount)
		order.Campaigns = append(order.Campaigns, tempCampaign)
	}
	return nil
}

// 计算买赠活动折扣
func calcPresentDiscount(ctx context.Context, order *ec_order.Order, builder *OrderBuilder, campaign *common.OrderCampaign, index int) error {
	ids := core_util.ToStringArray(core_util.ExtractArrayField("Id", campaign.PresentCampaigns))
	ids = util.StrArrayDiff(ids, []string{""})
	if len(ids) == 0 {
		return nil
	}
	existCampaignIds := core_util.ToStringArray(core_util.ExtractArrayField("Id", order.Campaigns))
	for _, id := range ids {
		if util.StrInArray(id, &existCampaignIds) {
			// 如果传入重复的 campaignId 会导致计算多次优惠
			return errors.NewInvalidArgumentErrorWithMessage("campaigns", "repeated campaignId")
		}
	}
	if len(ids) != len(util.StrArrayUnique(ids)) {
		return errors.NewInvalidArgumentErrorWithMessage("campaigns", "repeated campaignId")
	}
	presentCampaignMap, err := builder.GetCampaigns(ctx, ec_order.CAMPAIGN_TYPE_PRESENT, ids, nil)
	if err != nil {
		return err
	}

	for i, c := range campaign.PresentCampaigns {
		presentCampaign, ok := presentCampaignMap[c.Id].(*marketing.PresentCampaignDetail)
		if !ok {
			return errors.NewInvalidArgumentErrorWithMessage("time", "买赠活动已结束")
		}
		// 前端传入的 totalAmount 和 productCount 不可信，需自行计算
		skus := []string{}
		for _, product := range presentCampaign.Rule.Products {
			for _, pSku := range product.Skus {
				skus = append(skus, pSku.Sku)
			}
		}

		skuStockMap := map[string]uint64{}
		for _, skuStock := range presentCampaign.Rule.SkuStocks {
			skuStockMap[skuStock.Sku] = skuStock.Stock
		}

		ReducePresentProductStock(ctx, presentCampaign, skus, order, builder)
		totalAmount, productCount := calcPresentCampaignCondition(presentCampaign.Rule.Type, skus, order)
		campaign.PresentCampaigns[i].TotalAmount = totalAmount
		campaign.PresentCampaigns[i].ProductCount = productCount
		tmpAmount := presentCampaign.Discounts[0].OrderAmount
		tmpCount := presentCampaign.Discounts[0].ProductCount
		var discount *pb_marketing.DiscountDetail
		for i, d := range presentCampaign.Discounts {
			if presentCampaign.Type == marketing_model.PRESENT_CAMPAIGN_TYPE_AMOUNT && totalAmount >= d.OrderAmount && tmpAmount <= d.OrderAmount {
				discount = &pb_marketing.DiscountDetail{}
				core_util.DeepCopy(presentCampaign.Discounts[i], discount)
				tmpAmount = d.OrderAmount
			}
			if presentCampaign.Type == marketing_model.PRESENT_CAMPAIGN_TYPE_PIECE && productCount >= d.ProductCount && tmpCount <= d.ProductCount {
				discount = &pb_marketing.DiscountDetail{}
				core_util.DeepCopy(presentCampaign.Discounts[i], discount)
				tmpCount = d.ProductCount
			}
		}

		if discount == nil {
			continue
		}

		for i, op := range order.Products {
			if ec_order.CheckProductCampaignExistence(op, ec_order.CAMPAIGN_TYPE_PACKAGE) {
				continue
			}
			if presentCampaign.Rule.Type == marketing_model.PRESENT_CAMPAIGN_TYPE_INCLUDE && !core_util.StrInArray(op.Spec.Sku, &skus) {
				continue
			}
			if presentCampaign.IsDiscountLimit && len(builder.PrepaidCardIds) > 0 {
				order.Products[i].DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_PREPAID_CARD)
			}
			if !presentCampaign.CanUseCoupon {
				order.Products[i].DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_CAMPAIGN)
			} else {
				order.Products[i].CouponLimits = share_model.MergeCouponLimit(order.Products[i].CouponLimits, presentCampaign.CouponLimit)
			}
			if !presentCampaign.CanUsePrepaidCard {
				order.Products[i].DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_CAMPAIGN)
			} else {
				order.Products[i].PrepaidCardLimits = share_model.MergePrepaidCardLimit(order.Products[i].PrepaidCardLimits, presentCampaign.PrepaidCardLimit)
			}
			order.Products[i].DisableDiscount(DISCOUNT_TYPE_MEMBER, DISCOUNT_TYPE_CAMPAIGN)
			order.Products[i].DisableDiscount(DISCOUNT_TYPE_BENEFIT_CARD, DISCOUNT_TYPE_CAMPAIGN)
			order.Products[i].IsDiscountLimit = presentCampaign.IsDiscountLimit
			// 设置了库存且 sku 存在库存或者满减赠活动没设置库存才加 CampaignTags
			if stock, ok := skuStockMap[op.Spec.Sku]; ok && stock > 0 || !ok || len(presentCampaign.Rule.SkuStocks) == 0 {
				order.Products[i].CampaignTags = append(order.Products[i].CampaignTags, ec_order.CAMPAIGN_TYPE_PRESENT)
			}
		}

		if discount.DiscountType == "" {
			continue
		}
		discountCount, _ := getPresentCampaignDiscountCount(ctx, discount, builder.MemberId, presentCampaign.Type, totalAmount, productCount)
		generatePresentDiscount(ctx, order, presentCampaign, discount, discountCount)
	}
	builder.Campaigns[index] = campaign
	return nil
}

func ReducePresentProductStock(ctx context.Context, presentCampaign *marketing.PresentCampaignDetail, skus []string, order *ec_order.Order, builder *OrderBuilder) {
	stockMap := map[string]uint64{}
	for _, stock := range presentCampaign.Rule.SkuStocks {
		stockMap[stock.Sku] = stock.Stock
	}
	for i, op := range order.Products {
		if op.IsCampaignDisabled {
			continue
		}
		if ec_order.CheckProductCampaignExistence(op, ec_order.CAMPAIGN_TYPE_PACKAGE) {
			continue
		}
		if presentCampaign.Rule.Type == marketing_model.PRESENT_CAMPAIGN_TYPE_INCLUDE && !core_util.StrInArray(op.Spec.Sku, &skus) {
			continue
		}
		// 买赠活动购买商品数据超过库存，计算金额和商品购买数量时不能计算超出库存的，超出库存的可以购买，但不能用于买赠活动计算优惠
		total := op.Total
		stock, ok := stockMap[op.Spec.Sku]
		if ok && op.Total > stock {
			total = stock
		}
		// 下单并且有设置库存时才扣减库存
		if builder.isCreateOrder && ok {
			// 尝试扣减库存
			for {
				err := marketing_model.CPresentCampaign.ReduceStock(ctx, util.ToMongoId(presentCampaign.Id), op.Spec.Sku, int(total))
				if err == nil {
					break
				}
				total--
				if total == 0 {
					break
				}
			}
		}
		op.PresentDiscountCount = total
		order.Products[i] = op
	}
}

func calcPresentCampaignCondition(ruleType string, skus []string, order *ec_order.Order) (totalAmount, productCount uint64) {
	for _, op := range order.Products {
		if op.IsCampaignDisabled {
			continue
		}
		if ec_order.CheckProductCampaignExistence(op, ec_order.CAMPAIGN_TYPE_PACKAGE) {
			continue
		}
		if ruleType == marketing_model.PRESENT_CAMPAIGN_TYPE_INCLUDE && !core_util.StrInArray(op.Spec.Sku, &skus) {
			continue
		}
		// 满减赠活动库存充足
		if op.PresentDiscountCount == op.Total {
			// 活动在下单逻辑中是优先计算的，还不存在其它折扣，所以若商品参与单个活动计算之前 op.PayAmount 等于 op.Price * op.Total
			totalAmount += op.PayAmount
			productCount += op.Total
		} else {
			// 满减增活动库存不足，只能部分商品参与满减增活动
			// 限时折扣和满减增支持叠加，优先参与限时折扣， discounts 中会有限时折扣，需要将限时折扣折扣金额减去
			price := op.Price
			for _, d := range op.Discounts {
				if d.Type == ec_order.CAMPAIGN_TYPE_DISCOUNT {
					price = price - d.Discount/op.Total
				}
			}
			totalAmount += price * op.PresentDiscountCount
			productCount += op.PresentDiscountCount
		}
	}
	return
}

func getPresentCampaignDiscountCount(ctx context.Context, discount *pb_marketing.DiscountDetail, memberId, campaignType string, totalAmount, productCount uint64) (uint64, bool) {
	if discount.Type == marketing_model.PRESENT_CAMPAIGN_DISCOUNT_TYPE_ONCE {
		return 1, true
	}

	cycleLimit := uint64(0)
	if discount.CycleLimit != 0 {
		usedCount := calcOrderDiscountCycleCount(ctx, memberId, discount.DiscountId)
		if discount.CycleLimit <= usedCount {
			return 0, true
		}
		cycleLimit = discount.CycleLimit - usedCount
	}
	if campaignType == marketing_model.PRESENT_CAMPAIGN_TYPE_AMOUNT {
		return getCount(totalAmount/discount.OrderAmount, cycleLimit)
	}
	return getCount(productCount/discount.ProductCount, cycleLimit)
}

func calcOrderDiscountCycleCount(ctx context.Context, memberId, discountId string) uint64 {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"accountId":            util.GetAccountIdAsObjectId(ctx),
				"isDeleted":            false,
				"memberId":             bson.ObjectIdHex(memberId),
				"campaigns.discountId": bson.ObjectIdHex(discountId),
				"status": bson.M{
					"$ne": ec_order.ORDER_STATUS_CANCELED,
				},
			},
		},
		{
			"$project": bson.M{
				"campaigns": 1,
			},
		},
		{
			"$unwind": "$campaigns",
		},
		{
			"$match": bson.M{
				"campaigns.discountId": bson.ObjectIdHex(discountId),
			},
		},
		{
			"$group": bson.M{
				"_id":       "$_id",
				"campaigns": bson.M{"$first": "$campaigns"},
			},
		},
		{
			"$group": bson.M{
				"_id": "",
				"count": bson.M{
					"$sum": "$campaigns.count",
				},
			},
		},
	}
	result := bson.M{}
	err := extension.DBRepository.Aggregate(ctx, ec_order.C_ORDER, pipeline, true, &result)
	if err != nil {
		log.Warn(ctx, "Failed to aggregate order", log.Fields{
			"pipeline": pipeline,
			"errMsg":   err.Error(),
		})
		return 0
	}
	return cast.ToUint64(result["count"])
}

func getCount(count, limit uint64) (uint64, bool) {
	if limit == 0 || limit > count {
		return count, false
	}
	return limit, true
}

// 生成买赠活动折扣信息
func generatePresentDiscount(ctx context.Context, order *ec_order.Order, campaign *marketing.PresentCampaignDetail, discount *pb_marketing.DiscountDetail, discountCount uint64) {
	discount.DiscountValue *= discountCount
	discounter := GetDiscounterByPresentCampaign(campaign, discount)
	discounter.Calc(ctx, order)
	productIds := core_util.ToObjectIdArray(core_util.ExtractArrayField("Id", campaign.Rule.Products))
	orderCampaign := ec_order.Campaign{
		Id:         bson.ObjectIdHex(campaign.Id),
		Title:      campaign.Title,
		Type:       ec_order.CAMPAIGN_TYPE_PRESENT,
		DiscountId: bson.ObjectIdHex(discount.DiscountId),
		ProductIds: productIds,
	}
	if discount.Type == marketing_model.PRESENT_CAMPAIGN_DISCOUNT_TYPE_CYCLE {
		if discountCount == 0 {
			return
		}
		orderCampaign.Count = discountCount
	}
	order.Campaigns = append(order.Campaigns, orderCampaign)
}

// 计算任选打包活动折扣
func calcOptionalPackageDiscount(ctx context.Context, order *ec_order.Order, builder *OrderBuilder, campaign *common.OrderCampaign) error {
	ids := core_util.ExtractArrayStringField("Id", campaign.OptionalPackageCampaigns)
	if len(ids) == 0 {
		removeDisabledOptionalPackageCampaign(order, nil)
		return nil
	}
	campaigns, err := builder.GetCampaigns(ctx, ec_order.CAMPAIGN_TYPE_OPTIONAL_PACKAGE, ids, nil)
	if err != nil {
		return err
	}
	enabledCampaignIds := []string{}
	countMap := builder.getCampaignOrderCountMap(ctx, builder.MemberId, ec_order.CAMPAIGN_TYPE_OPTIONAL_PACKAGE, ids)
	for _, id := range ids {
		var (
			productCount         uint64            // 活动商品数量
			purchasedCount       uint64            // 当前用户已参与当前活动次数
			campaignCount        uint64            // 当前订单参与当前活动次数
			campaignProductCount uint64            // 当前订单参与活动商品数量
			spuCountMap          map[string]uint64 // 一个 spu 参与计数的次数
			unavailableSpus      []string          // 不符合条件的 spu
			campaignPrice        uint64            // 参与活动的价格
		)
		c, ok := campaigns[bson.ObjectIdHex(id)].(optional_package.OptionalPackage)
		if !ok {
			continue
		}
		campaignPrice = c.Price
		if err := checkOptionalCampaign(c, builder, countMap); err != nil {
			return err
		}
		purchasedCount = countMap[c.Id.Hex()]
		productMap := core_util.MakeMapper("Id", c.Products)
		spuCountMap = map[string]uint64{}
		for _, p := range order.Products {
			if !p.ExistCampaign(c) {
				continue
			}
			if productMap[p.Id] != nil {
				if util.StrInArray(p.Id.Hex(), &unavailableSpus) {
					continue
				}
				limitCount := productMap[p.Id].(optional_package.Product).PurchaseLimit
				spuPurchasedCount := spuCountMap[p.Id.Hex()]
				availableCount := uint64(limitCount) - spuPurchasedCount
				// 如果有 spu 限购且剩余限购数量不满足总数，则此 spu 所有的 sku 均不计数
				if limitCount > 0 && availableCount < p.Total {
					productCount -= spuPurchasedCount
					unavailableSpus = append(unavailableSpus, p.Id.Hex())
				} else {
					productCount += p.Total
					spuCountMap[p.Id.Hex()] = spuCountMap[p.Id.Hex()] + p.Total
				}
			}
		}
		// 阶梯优惠
		if c.DiscountRule == ec_order.OPTIONAL_PACKAGE_LADDER {
			if productCount < c.PackageLevels[0].ProductCount {
				continue
			}
			if productCount >= c.PackageLevels[len(c.PackageLevels)-1].ProductCount {
				campaignProductCount = c.PackageLevels[len(c.PackageLevels)-1].ProductCount
				campaignPrice = c.PackageLevels[len(c.PackageLevels)-1].Price
			} else {
				for i := range c.PackageLevels {
					if productCount >= c.PackageLevels[i].ProductCount && productCount < c.PackageLevels[i+1].ProductCount {
						campaignProductCount = c.PackageLevels[i].ProductCount
						campaignPrice = c.PackageLevels[i].Price
						break
					}
				}
			}
			campaignCount = 1
			if c.IsPurchaseLimitEnabled && c.LimitCount-purchasedCount <= 0 {
				campaignProductCount = 0
				campaignCount = 0
			}
		} else { // 循环优惠
			if productCount < c.ProductCount {
				continue
			}
			campaignCount = productCount / c.ProductCount
			if c.IsPurchaseLimitEnabled {
				max := c.LimitCount - purchasedCount
				if max < campaignCount {
					campaignCount = max
				}
			}
			campaignProductCount = campaignCount * c.ProductCount
		}

		// 有可能部分商品不满足活动条件，优先选取价格高的商品参与活动。
		// 为了不打乱商品顺序，这里把当前活动商品都找出来按价格排序。
		campaignProducts := []ec_order.OrderProduct{}
		for i, p := range order.Products {
			if !p.ExistCampaign(c) {
				continue
			}
			for j, orderCampaign := range p.Campaigns {
				if c.Id == orderCampaign.Id {
					order.Products[i].Campaigns[j].Title = c.Name
				}
			}
			campaignProducts = append(campaignProducts, order.Products[i])
		}
		sort.Slice(campaignProducts, func(i, j int) bool {
			return campaignProducts[i].Price > campaignProducts[j].Price
		})
		normalProducts, nCampaignProducts := []ec_order.OrderProduct{}, []ec_order.OrderProduct{}
		var totalCampaignProductAmount uint64
		spuCountMap = map[string]uint64{}
		for i, p := range campaignProducts {
			if campaignProductCount == 0 {
				campaignProducts[i].Campaigns = nil
				normalProducts = append(normalProducts, campaignProducts[i])
				continue
			}
			spuLimitCount := uint64(productMap[p.Id].(optional_package.Product).PurchaseLimit)
			spuPurchasedCount := spuCountMap[p.Id.Hex()]
			availableCount := spuLimitCount - spuPurchasedCount
			if p.Total <= campaignProductCount {
				// 没有 spu 限购或者有限购但是符合条件的参加活动
				if spuLimitCount == 0 || (spuLimitCount > 0 && p.Total <= availableCount && !util.StrInArray(p.Id.Hex(), &unavailableSpus)) {
					campaignProductCount -= p.Total
					nCampaignProducts = append(nCampaignProducts, campaignProducts[i])
					totalCampaignProductAmount += p.TotalAmount
					continue
				}
			}
			// 当前商品项部分商品能参与活动，需要拆分为两个商品项
			tempProduct := ec_order.OrderProduct{}
			copier.Instance(nil).From(p).CopyTo(&tempProduct)
			tempProduct.OutTradeId = bson.NewObjectId()
			tempProduct.OriginalOutTradeId = p.OutTradeId
			tempProduct.Total = p.Total - campaignProductCount
			// 超出 spu 限购的不参加活动
			if util.StrInArray(p.Id.Hex(), &unavailableSpus) {
				tempProduct.Total = p.Total
			}
			tempProduct.TotalAmount = tempProduct.Total * tempProduct.Price
			tempProduct.Campaigns = nil
			normalProducts = append(normalProducts, tempProduct)
			p.Total -= tempProduct.Total
			p.TotalAmount = p.Total * p.Price
			campaignProducts[i] = p
			campaignProductCount -= p.Total
			nCampaignProducts = append(nCampaignProducts, campaignProducts[i])
			totalCampaignProductAmount += p.TotalAmount
		}
		order.Campaigns = append(order.Campaigns, ec_order.Campaign{
			Id:    c.Id,
			Title: c.Name,
			Type:  ec_order.CAMPAIGN_TYPE_OPTIONAL_PACKAGE,
			Count: campaignCount,
		})
		for i, p := range order.Products {
			for _, cp := range nCampaignProducts {
				if cp.OutTradeId == p.OutTradeId {
					if c.IsDiscountLimit && len(builder.PrepaidCardIds) > 0 {
						cp.DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_PREPAID_CARD)
					}
					if !c.IsCouponEnabled {
						cp.DisableDiscount(DISCOUNT_TYPE_COUPON, DISCOUNT_TYPE_CAMPAIGN)
					} else {
						cp.CouponLimits = share_model.MergeModelCouponLimit(cp.CouponLimits, c.CouponLimit)
					}
					if !c.CanUsePrepaidCard {
						cp.DisableDiscount(DISCOUNT_TYPE_PREPAID_CARD, DISCOUNT_TYPE_CAMPAIGN)
					} else {
						cp.PrepaidCardLimits = share_model.MergeModelPrepaidCardLimit(cp.PrepaidCardLimits, c.PrepaidCardLimit)
					}
					cp.DisableDiscount(DISCOUNT_TYPE_MEMBER, DISCOUNT_TYPE_CAMPAIGN)
					cp.DisableDiscount(DISCOUNT_TYPE_BENEFIT_CARD, DISCOUNT_TYPE_CAMPAIGN)
					if cp.Total > 0 {
						cp.CampaignTags = append(cp.CampaignTags, ec_order.CAMPAIGN_TYPE_OPTIONAL_PACKAGE)
					}
					cp.IsDiscountLimit = c.IsDiscountLimit
					order.Products[i] = cp
				}
			}
		}
		for _, np := range normalProducts {
			var exists bool
			for i, p := range order.Products {
				if p.OutTradeId == np.OutTradeId {
					order.Products[i] = np
					exists = true
				}
			}
			if !exists {
				// 若存在 sku 相同的普通商品，则合并
				var merged bool
				for i, p := range order.Products {
					if p.Spec.Sku == np.Spec.Sku && len(p.Campaigns) == 0 {
						order.Products[i].Total += np.Total
						order.Products[i].TotalAmount += np.TotalAmount
						merged = true
					}
				}
				if !merged {
					order.Products = append(order.Products, np)
				}
			}
		}
		// 因为上面有可能有的商品一个都不满足活动限制，所以导致有 total 为 0 的商品，需要去掉
		var temp []ec_order.OrderProduct
		for _, product := range order.Products {
			if product.Total == 0 {
				continue
			}
			temp = append(temp, product)
		}
		order.Products = temp
		discountAmount := uint64(0)
		if totalCampaignProductAmount > campaignPrice*campaignCount {
			discountAmount = totalCampaignProductAmount - campaignPrice*campaignCount
		}
		discounter := NewOptionalPackageCampaignDiscounter(c, discountAmount)
		discounter.Calc(ctx, order)
		enabledCampaignIds = append(enabledCampaignIds, id)
	}
	removeDisabledOptionalPackageCampaign(order, enabledCampaignIds)
	return nil
}

// 校验任选打包活动
func checkOptionalCampaign(campaign optional_package.OptionalPackage, builder *OrderBuilder, countMap map[string]uint64) error {
	if campaign.StartAt.After(time.Now()) || campaign.EndAt.Before(time.Now()) {
		return errors.NewInvalidArgumentErrorWithMessage("campaigns", "optional package campaign status error")
	}
	purchasedCount := countMap[campaign.Id.Hex()]
	if campaign.IsPurchaseLimitEnabled && campaign.LimitCount != 0 && purchasedCount >= campaign.LimitCount {
		return errors.NewInvalidArgumentErrorWithMessage("campaigns", "optional package campaign limit")
	}
	return nil
}

func removeDisabledOptionalPackageCampaign(order *ec_order.Order, excludeCampaignIds []string) {
	// 去除未参加任选活动打包商品的 campaigns
	for i, p := range order.Products {
		if len(p.Campaigns) == 0 {
			continue
		}
		nCampaigns := []ec_order.Campaign{}
		for _, c := range p.Campaigns {
			if c.Type == ec_order.CAMPAIGN_TYPE_OPTIONAL_PACKAGE && !core_util.StrInArray(c.Id.Hex(), &excludeCampaignIds) {
				continue
			}
			nCampaigns = append(nCampaigns, c)
		}
		order.Products[i].Campaigns = nCampaigns
	}
}

// 校验促销套餐活动
func checkPackageCampaign(campaign *pb_marketing.PackageCampaignDetail, builder *OrderBuilder, countMap map[string]uint64) error {
	startAt := core_util.ParseRFC3339(campaign.StartAt)
	endAt := core_util.ParseRFC3339(campaign.EndAt)
	if startAt.After(time.Now()) || endAt.Before(time.Now()) {
		return errors.NewInvalidArgumentErrorWithMessage("campaigns", "package campaign status error")
	}
	if builder.isCreateOrder && campaign.Store.Type == marketing_model.PACKAGE_CAMPAIGN_STORE_TYPE_BY_INCLUDED_IDS {
		storeIds := core_util.ToStringArray(core_util.ExtractArrayField("Id", campaign.Store.CampaignStores))
		if !core_util.StrInArray(builder.StoreId, &storeIds) {
			return errors.NewInvalidArgumentErrorWithMessage("campaigns", "package campaign store inapplicable")
		}
	}
	if campaign.Limit != 0 && countMap != nil {
		if count, ok := countMap[campaign.Id]; ok && count+1 > campaign.Limit {
			return errors.NewInvalidArgumentErrorWithMessage("campaigns", "package campaign limit")
		} else {
			countMap[campaign.Id]++
		}
	}
	return nil
}

// CanSellInStore 校验门店是否限售
func CanSellInStore(storeRestriction ec_product.StoreRestriction, store *pb_store.StoreDetail) bool {
	switch storeRestriction.Type {
	case ec_product.STORE_RESTRICTION_TYPE_AREA:
		provinceCity := fmt.Sprintf("%s:%s", store.Location.Province, store.Location.City)
		provinceCityDistrict := fmt.Sprintf("%s:%s", provinceCity, store.Location.District)
		return (util.StrInArray(store.Location.Province, &storeRestriction.Areas) ||
			util.StrInArray(provinceCity, &storeRestriction.Areas) ||
			util.StrInArray(provinceCityDistrict, &storeRestriction.Areas)) == storeRestriction.IsAllowed
	case ec_product.STORE_RESTRICTION_TYPE_STORE:
		storeIds := util.MongoIdsToStrs(storeRestriction.StoreIds)
		return util.StrInArray(store.Id, &storeIds) == storeRestriction.IsAllowed
	case ec_product.STORE_RESTRICTION_TYPE_STORE_TYPE:
		storeTypeIds := util.MongoIdsToStrs(storeRestriction.EntityIds)
		return util.StrInArray(store.StoreTypeId, &storeTypeIds) == storeRestriction.IsAllowed
	case ec_product.STORE_RESTRICTION_TYPE_STORE_LEVEL:
		storeLevelIds := util.MongoIdsToStrs(storeRestriction.EntityIds)
		return util.StrInArray(store.LevelId, &storeLevelIds) == storeRestriction.IsAllowed
	case ec_product.STORE_RESTRICTION_TYPE_DISTRIBUTOR:
		if storeRestriction.IsAllowed {
			if len(core_util.IntersectStringSlice(store.DistributorIds, util.MongoIdsToStrs(storeRestriction.StoreIds))) > 0 {
				return true
			}
			return false
		} else {
			if len(core_util.IntersectStringSlice(store.DistributorIds, util.MongoIdsToStrs(storeRestriction.StoreIds))) == 0 {
				return true
			}
			return false
		}
	case ec_product.STORE_RESTRICTION_TYPE_STORE_TAG:
		return len(core_util.IntersectStringSlice(storeRestriction.Tags, store.Tags)) > 0 == storeRestriction.IsAllowed
	}
	return true
}

// CheckStoreShelveStatus 校验门店商品商家状态
func CheckStoreShelveStatus(ds *pb_setting.GetDeliverySettingResponse, storeProduct *ec_store_product.StoreProduct, sku, method string) bool {
	if ds != nil && method == "express" {
		if ds.Logistics.Enabled && ds.Logistics.ShipSetting.Operator == setting.DELIVERY_SETTING_OPERATOR_USER {
			// 下单时选择快递配送且物流设置为总部发货时，不判断门店商品状态
			return true
		}
	}
	if storeProduct != nil {
		for _, s := range storeProduct.Skus {
			if s.Sku != sku {
				continue
			}
			if s.Status == ec_store_product.STATUS_UNSHELVED {
				return false
			}
		}
	}
	return true
}

// 订单上添加活动赠品信息
func setCampaignPresents(ctx context.Context, order *ec_order.Order, campaign *common.Campaign, builder *OrderBuilder) error {
	if len(campaign.Presents) == 0 {
		return nil
	}
	presentIds := core_util.ToStringArray(core_util.ExtractArrayStringField("Id", campaign.Presents))
	ecProductMap, err := builder.GetEcProductMap(ctx, presentIds, nil)
	if err != nil {
		return err
	}
	productIds := []string{}
	for _, ecProduct := range ecProductMap {
		productIds = append(productIds, ecProduct.(ec_product.Product).ProductId.Hex())
	}
	productMap, err := builder.GetProductMap(ctx, productIds)
	if err != nil {
		return err
	}
	for _, p := range campaign.Presents {
		ecProduct := ecProductMap[bson.ObjectIdHex(p.Id)].(ec_product.Product)
		product := productMap[ecProduct.ProductId.Hex()].(*pb_product.ProductDetailResponse)
		productSku := ProductSku{Id: p.Id, sku: p.Sku, count: uint64(p.Count)}
		tempPresent, err := getBasicOrderProduct(ctx, ecProduct, product, productSku, order, builder, true)
		if err != nil {
			return err
		}
		tempPresent.IsPresent = true
		tempPresent.PayAmount = 0
		order.Products = append(order.Products, *tempPresent)
	}
	return nil
}

func checkMemberPaidCardRecord(record *pb_member.MemberPaidCardRecord) error {
	if record.ExpireAt != "" && core_util.ParseRFC3339(record.ExpireAt).Before(time.Now()) {
		return errors.NewInvalidArgumentErrorWithMessage("memberPaidCardRecords", "expired")
	}
	return nil
}

func removeAreasProvinceSuffix(areas []string) []string {
	for i := range areas {
		areaSlice := strings.Split(areas[i], ":")
		areaSlice[0] = regexp.MustCompile(PROVINCE_FORMATTION).ReplaceAllString(areaSlice[0], "")
		areas[i] = strings.Join(areaSlice, ":")
	}
	return areas
}
