package order

import (
	"encoding/json"
	"mairpc/core/log"
	"mairpc/core/util/copier"
	"mairpc/service/ec/service"
	"strings"

	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	"mairpc/proto/ec/order"
	ec_client "mairpc/service/ec/client"
	ec_order "mairpc/service/ec/model/order"
	ec_share "mairpc/service/ec/share"
	"mairpc/service/share/util"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

func (OrderService) GetOrderRefund(ctx context.Context, req *order.GetOrderRefundRequest) (*order.OrderRefundDetail, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	accountId := util.GetAccountIdAsObjectId(ctx)
	orderRefund := ec_order.OrderRefund{}

	if req.Id == "" && req.OrderId == "" && req.Number == "" {
		return nil, errors.NewNotExistsError("id and orderId can't both empty")
	}
	if req.OrderId != "" {
		refund, err := ec_order.COrderRefund.FindOneByOrderId(ctx, bson.ObjectIdHex(req.OrderId))
		if err != nil && err != bson.ErrNotFound {
			return nil, err
		}
		if err == bson.ErrNotFound {
			return &order.OrderRefundDetail{}, nil
		}
		orderRefund = *refund
	}
	if req.Id != "" {
		err := ec_order.Common.GetById(ctx, bson.ObjectIdHex(req.Id), accountId, "false", ec_order.C_ORDER_REFUND, &orderRefund)
		if err != nil && err != bson.ErrNotFound {
			return nil, err
		}
		if err == bson.ErrNotFound {
			return &order.OrderRefundDetail{}, nil
		}
	}
	if req.Number != "" {
		refund, err := ec_order.COrderRefund.GetByNumber(ctx, req.Number)
		if err != nil && err != bson.ErrNotFound {
			return nil, err
		}
		if err == bson.ErrNotFound {
			return &order.OrderRefundDetail{}, nil
		}
		orderRefund = *refund
	}

	orderRefundDetail := formatOrderRefund(ctx, &orderRefund)
	if orderRefund.OrderId.Hex() != "" {
		order, err := ec_order.COrder.GetById(ctx, orderRefund.OrderId)
		if err != nil && err != bson.ErrNotFound {
			return nil, err
		}
		if order.Id.Hex() != "" {
			copier.Instance(nil).From(order.ProductAccessories).CopyTo(&orderRefundDetail.ProductAccessories)
		}
	}

	if len(orderRefundDetail.Histories) > 0 && strings.HasPrefix(orderRefundDetail.Histories[0].Operator, "user") {
		strs := strings.Split(orderRefundDetail.Histories[0].Operator, ":")
		if len(strs) >= 2 {
			userId := strs[1]
			if bson.IsObjectIdHex(userId) {
				user, err := service.GetUserById(ctx, userId)
				if err != nil {
					log.Warn(ctx, "Failed to get user", log.Fields{
						"userId": userId,
						"errMsg": err.Error(),
					})
				}
				if user != nil {
					orderRefundDetail.UserName = user.Name
				}
			}
		}
	}

	return orderRefundDetail, nil
}

func formatOrderRefund(ctx context.Context, orderRefund *ec_order.OrderRefund) *order.OrderRefundDetail {
	var needDesensitizeMember bool
	if core_util.GetUserRole(ctx) == "user" {
		needDesensitizeMember, _ = ec_share.NeedDesensitize(ctx)
	}
	orderRefundDetail := &order.OrderRefundDetail{}
	resetFieldMethods := map[string]interface{}{
		"Histories.Extra": func(extra bson.M) string {
			if memberName, ok := extra["memberName"]; ok && needDesensitizeMember {
				extra["memberName"] = ec_share.DesensitizeName(needDesensitizeMember, cast.ToString(memberName))
			}
			e, _ := json.Marshal(extra)
			if string(e) == "null" {
				return ""
			}
			return string(e)
		},
		"Extra": func(extra bson.M) string {
			wechatFreightInsurance := extra[ec_order.WECHAT_FREIGHT_INSURANCE_EXTRA]
			if wechatFreightInsurance == nil {
				return ""
			}
			return core_util.MarshalInterfaceToString(wechatFreightInsurance)
		},
	}

	core_util.FormatRFC3339(orderRefund, orderRefundDetail, resetFieldMethods, map[string]string{})
	copier.Instance(nil).RegisterResetDiffField([]copier.DiffFieldPair{
		{
			Origin:  "PrepaidCardRefunds",
			Targets: []string{"PrepaidCardRefunds", "PrepaidCards"},
		},
	}).From(orderRefund.Products).CopyTo(&orderRefundDetail.Products)
	copier.Instance(nil).From(orderRefund.PrepaidCardRefunds).CopyTo(&orderRefundDetail.PrepaidCardRefunds)
	for i := range orderRefundDetail.Products {
		if orderRefundDetail.Products[i].RefundAmount == 0 {
			orderRefundDetail.Products[i].RefundAmount = orderRefundDetail.Products[i].PayAmount
		}
	}
	if orderRefund.RefundNumber == "" {
		refundNumbers, _ := getWechatRefundNumbers(ctx, orderRefund.Id.Hex())
		orderRefundDetail.WechatShopRefundNumbers = refundNumbers
	}
	return orderRefundDetail
}

func getWechatRefundNumbers(ctx context.Context, refundId string) ([]string, error) {
	resp, err := ec_client.WechatShopService.GetRefundNumbersByRefundId(ctx, &request.DetailRequest{
		Id: refundId,
	})
	if err != nil {
		return []string{}, nil
	}
	return resp.Values, nil
}
