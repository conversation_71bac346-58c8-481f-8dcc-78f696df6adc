package order

import (
	"mairpc/core/errors"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	"mairpc/proto/ec/order"
	ec_order_model "mairpc/service/ec/model/order"

	"mairpc/service/ec/model/setting"
	"mairpc/service/ec/model/store"
	"mairpc/service/mall/codes"
	"mairpc/service/share/util"
	"time"

	"mairpc/core/extension/bson"

	"golang.org/x/net/context"
)

func (OrderService) CreateComments(ctx context.Context, req *order.CreateCommentsRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	orderBeforeComment, err := ec_order_model.COrder.GetById(ctx, bson.ObjectIdHex(req.OrderId))
	if err != nil {
		return nil, codes.NewError(codes.OrderNotFound)
	}

	if orderBeforeComment.IsCommented {
		return nil, errors.NewAlreadyExistsError("OrderComment")
	}

	cStore, err := store.CStore.GetById(ctx, orderBeforeComment.StoreId)
	if err != nil {
		return nil, errors.NewNotExistsError("StoreId")
	}
	settings, err := setting.CSettings.Get(ctx)
	if err != nil {
		return nil, err
	}

	// 设置评价的状态
	commentStatus := ec_order_model.COMMENT_STATUS_APPROVED
	if settings.IsAuditEnabled {
		commentStatus = ec_order_model.COMMENT_STATUS_PENDING
	}
	// 设置评价分值
	expressRating, productRating, serviceRating := req.ExpressRating, req.ProductRating, req.ServiceRating
	if expressRating == 0 && productRating == 0 && serviceRating == 0 {
		orderComments, err := ec_order_model.CComment.GetByOrderNumber(ctx, orderBeforeComment.Number)
		if err != nil {
			return nil, errors.NewNotExistsError("ProductComment")
		}
		expressRating = orderComments[0].ExpressRating
		productRating = orderComments[0].ProductRating
		serviceRating = orderComments[0].ServiceRating
	}
	productMap := make(map[string]ec_order_model.OrderProduct, len(orderBeforeComment.Products))
	for _, product := range orderBeforeComment.Products {
		productMap[product.Spec.Sku] = product
	}
	var comments []ec_order_model.Comment
	instance := copier.Instance(nil)
	for _, productComment := range req.ProductComments {
		product, ok := productMap[productComment.Sku]
		if !ok {
			continue
		}
		if product.IsCommented {
			return nil, errors.NewAlreadyExistsError("ProductComment")
		}
		comment := ec_order_model.Comment{
			Id:            bson.NewObjectId(),
			AccountId:     util.GetAccountIdAsObjectId(ctx),
			MemberId:      bson.ObjectIdHex(req.MemberId),
			Status:        commentStatus,
			UpdatedAt:     time.Now(),
			CreatedAt:     time.Now(),
			ExpressRating: expressRating,
			ProductRating: productRating,
			ServiceRating: serviceRating,
		}

		var properties []string
		for _, property := range product.Spec.Properties {
			properties = append(properties, property.Value)
		}
		instance.From(product).CopyTo(&comment.Product)
		instance.From(cStore).CopyTo(&comment.Store)
		instance.From(productComment).CopyTo(&comment)
		instance.From(orderBeforeComment).CopyTo(&comment.Order)
		comment.ChannelId = orderBeforeComment.Channel.ChannelId
		comment.Product.Spec.Properties = properties
		if len(comment.Pictures) > 0 {
			comment.HasPicture = true
		}
		if comment.Comment != "" || comment.HasPicture {
			comment.HasComment = true
		}
		comments = append(comments, comment)
	}

	if len(comments) == 0 {
		return &response.EmptyResponse{}, nil
	}
	err = ec_order_model.CComment.CreateComments(ctx, comments)
	if err != nil {
		return nil, err
	}
	for _, comment := range comments {
		err = ec_order_model.COrder.UpdateOrderProductsCommented(ctx, comment)
		if err != nil {
			return nil, err
		}
	}

	orderAfterComment, err := ec_order_model.COrder.GetById(ctx, bson.ObjectIdHex(req.OrderId))
	if err != nil {
		return nil, codes.NewError(codes.OrderNotFound)
	}

	if needUpdateOrderCommented(orderAfterComment) {
		err = ec_order_model.COrder.UpdateOrderCommented(ctx, orderAfterComment.Id)
		if err != nil {
			return nil, err
		}
	}

	for _, comment := range comments {
		for _, product := range orderAfterComment.Products {
			if comment.Product.Spec.Sku == product.Spec.Sku && product.IsCommented {
				ec_order_model.CComment.SendProductCommentCreateEvent(ctx, comment)
			}
		}
	}

	if needSendCommentServiceEvent(orderBeforeComment) {
		ec_order_model.CComment.SendOrderServiceCommentCreateEvent(ctx, orderBeforeComment, req.ProductRating, req.ServiceRating, req.ExpressRating)
	}

	return &response.EmptyResponse{}, nil
}

func needSendCommentServiceEvent(orderBeforeComment ec_order_model.Order) bool {
	for index := range orderBeforeComment.Products {
		if orderBeforeComment.Products[index].IsCommented {
			return false
		}
	}
	return true
}

func needUpdateOrderCommented(order ec_order_model.Order) bool {
	for _, product := range order.Products {
		if !product.IsCommented && product.RefundStatus == "" {
			return false
		}
	}
	return true
}
