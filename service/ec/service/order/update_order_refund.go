package order

import (
	"fmt"
	mairpc "mairpc/core/client"
	"mairpc/core/util/copier"
	"mairpc/proto/account"
	"mairpc/proto/coupon"
	"time"

	"mairpc/core/errors"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	"mairpc/proto/ec/order"
	pb_order "mairpc/proto/ec/order"
	ec_client "mairpc/service/ec/client"
	ec_order "mairpc/service/ec/model/order"
	setting_model "mairpc/service/ec/model/setting"
	ec_store "mairpc/service/ec/model/store"
	"mairpc/service/ec/share"
	"mairpc/service/share/component"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"

	"golang.org/x/net/context"
)

const (
	ORDER_REFUND_OPERATE_TYPE_AGREE_TO_RETURN            = "agreeToReturn"           // 后台同意退货
	ORDER_REFUND_OPERATE_TYPE_CONFIRM_RECEIPT_AND_REFUND = "confirmReceiptAndRefund" // 确认收货并退款
	ORDER_REFUND_OPERATE_TYPE_GOODS_RETURNED             = "goodsReturned"           // 用户已退货
	ORDER_REFUND_OPERATE_TYPE_AGREE_TO_REFUND            = "agreeToRefund"           // 同意退款
	ORDER_REFUND_OPERATE_TYPE_REFUND_REFUSED             = "refundRefused"           // 拒绝退款
	ORDER_REFUND_OPERATE_TYPE_UPDATE_APPLICATION         = "updateApplication"       // 用户修改请求
	ORDER_REFUND_OPERATE_TYPE_APPLICATION_CANCELED       = "applicationCanceled"     // 用户撤销申请
)

func isOperateMatchRole(req *order.UpdateOrderRefundRequest) bool {
	if req.MemberId != "" && core_util.StrInArray(req.OperateType, &[]string{
		ORDER_REFUND_OPERATE_TYPE_AGREE_TO_RETURN,
		ORDER_REFUND_OPERATE_TYPE_CONFIRM_RECEIPT_AND_REFUND,
		ORDER_REFUND_OPERATE_TYPE_AGREE_TO_REFUND,
		ORDER_REFUND_OPERATE_TYPE_REFUND_REFUSED,
	}) {
		return false
	}

	if req.StaffId != "" && !core_util.StrInArray(req.OperateType, &[]string{
		ORDER_REFUND_OPERATE_TYPE_CONFIRM_RECEIPT_AND_REFUND,
		ORDER_REFUND_OPERATE_TYPE_AGREE_TO_RETURN,
		ORDER_REFUND_OPERATE_TYPE_AGREE_TO_REFUND,
		ORDER_REFUND_OPERATE_TYPE_REFUND_REFUSED,
	}) {
		return false
	}

	if req.MemberId == "" && req.StaffId == "" && core_util.StrInArray(req.OperateType, &[]string{
		ORDER_REFUND_OPERATE_TYPE_GOODS_RETURNED,
		ORDER_REFUND_OPERATE_TYPE_UPDATE_APPLICATION,
		ORDER_REFUND_OPERATE_TYPE_APPLICATION_CANCELED,
	}) {
		return false
	}

	return true
}

func isOperateMatchStatus(operationType, orderRefundStatus string) bool {
	switch operationType {
	case ORDER_REFUND_OPERATE_TYPE_AGREE_TO_RETURN: // 同意退货退款
		if !core_util.StrInArray(orderRefundStatus, &[]string{
			ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT,
			ec_order.ORDER_REFUND_STATUS_REJECTED,
		}) {
			return false
		}

	case ORDER_REFUND_OPERATE_TYPE_CONFIRM_RECEIPT_AND_REFUND: // 确认收到退货并发起退款
		if !core_util.StrInArray(orderRefundStatus, &[]string{
			ec_order.ORDER_REFUND_STATUS_APPROVED,
			ec_order.ORDER_REFUND_STATUS_RETURNED,
		}) {
			return false
		}

	case ORDER_REFUND_OPERATE_TYPE_GOODS_RETURNED: // 退货并填写退货物流
		if orderRefundStatus != ec_order.ORDER_REFUND_STATUS_APPROVED {
			return false
		}

	case ORDER_REFUND_OPERATE_TYPE_AGREE_TO_REFUND: // 同意退款
		if !core_util.StrInArray(orderRefundStatus, &[]string{
			ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT,
			ec_order.ORDER_REFUND_STATUS_REJECTED,
		}) {
			return false
		}

	case ORDER_REFUND_OPERATE_TYPE_REFUND_REFUSED: // 拒绝退款申请
		if !core_util.StrInArray(orderRefundStatus, &[]string{
			ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT,
			ec_order.ORDER_REFUND_STATUS_APPROVED,
			ec_order.ORDER_REFUND_STATUS_RETURNED,
		}) {
			return false
		}

	case ORDER_REFUND_OPERATE_TYPE_UPDATE_APPLICATION: // 更新退款申请
		if !core_util.StrInArray(orderRefundStatus, &[]string{
			ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT,
			ec_order.ORDER_REFUND_STATUS_APPROVED,
			ec_order.ORDER_REFUND_STATUS_REJECTED,
		}) {
			return false
		}

	case ORDER_REFUND_OPERATE_TYPE_APPLICATION_CANCELED: // 取消申请
		if !core_util.StrInArray(orderRefundStatus, &[]string{
			ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT,
			ec_order.ORDER_REFUND_STATUS_APPROVED,
			ec_order.ORDER_REFUND_STATUS_REJECTED,
			ec_order.ORDER_REFUND_STATUS_RETURNED,
		}) {
			return false
		}

	}
	return true
}

func (OrderService) UpdateOrderRefund(ctx context.Context, req *order.UpdateOrderRefundRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if req.Id == "" && req.Number == "" {
		return nil, errors.NewInvalidArgumentErrorWithMessage("id", "Id and number cannot be both empty")
	}
	if !isOperateMatchRole(req) {
		return nil, errors.NewNotExistsError("operateType")
	}

	orderRefund := ec_order.OrderRefund{}

	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	if req.Id != "" {
		selector["_id"] = bson.ObjectIdHex(req.Id)
	}
	if req.Number != "" {
		selector["number"] = req.Number
	}
	err := ec_order.Common.GetByCondition(ctx, selector, ec_order.C_ORDER_REFUND, &orderRefund)
	if err != nil {
		return nil, errors.NewNotExistsError("id")
	}
	if !isOperateMatchStatus(req.OperateType, orderRefund.Status) {
		return nil, errors.NewInvalidArgumentErrorWithMessage("id", "Invalid operation on order")
	}
	if req.RefundAmount > 0 {
		err = changeRefundAmount(&orderRefund, req.RefundAmount, req.RefundAmountChangeReason)
		if err != nil {
			return nil, err
		}
	}
	order, err := ec_order.COrder.GetById(ctx, orderRefund.OrderId)
	if err != nil {
		return nil, err
	}
	// 退运费
	if req.IsRefundDeliveryFee && !order.IsDeliveryFeeRefunded {
		orderRefund.RefundAmount += order.Logistics.Fee - order.StoredValue.DeliveryAmount
		// 如果当前订单用储值余额支付了邮费，且退掉的余额里不包含邮费，那么这里要加上，场景：发货后申请退款，生成的 refund 不含邮费，后台通过审核时可以指定退邮费
		if order.StoredValue.DeliveryAmount > 0 && orderRefund.RefundStoredValue.DeliveryAmount == 0 {
			orderRefund.RefundStoredValue.DeliveryAmount = order.StoredValue.DeliveryAmount
			orderRefund.RefundStoredValue.Amount += order.StoredValue.DeliveryAmount
			orderRefund.NeedUpdateRefundStoredValue = true
		}
		orderRefund.ExpressFee += order.Logistics.Fee
		order.IsDeliveryFeeRefunded = true
		order.UpdateIsDeliveryFeeRefunded(ctx)
	}
	switch req.OperateType {
	case ORDER_REFUND_OPERATE_TYPE_AGREE_TO_RETURN: // 同意退货退款
		orderRefund.Status = ec_order.ORDER_REFUND_STATUS_APPROVED
		extra := bson.M{"location": getRefundLocation(ctx, order)}
		operator := ""
		if req.StaffId != "" {
			operator = "staff:" + req.StaffId
			staff, _ := ec_store.CStaff.GetById(ctx, bson.ObjectIdHex(req.StaffId))
			extra["staffName"] = staff.Name
		} else {
			operator = getOperator(ctx, req.MemberId, false)
			extra["userName"] = getOperatorName(ctx, req.MemberId)
		}
		orderRefund.AuditedAt = time.Now()
		orderRefund.GenerateHistory(operator, "", extra)
		if req.Remarks != "" {
			orderRefund.Remarks = req.Remarks
		}
		orderRefund.CreateFreightInsuranceReturnId(ctx)
		// 状态更新成同意退款
		orderRefund.UpdateRefundStatus(ctx, req)

	case ORDER_REFUND_OPERATE_TYPE_CONFIRM_RECEIPT_AND_REFUND: // 确认收到退货并发起退款
		orderRefund.Status = ec_order.ORDER_REFUND_STATUS_CONFIRM_RETURNED
		extra := bson.M{
			"location": getRefundLocation(ctx, order),
		}
		operator := ""
		if req.StaffId != "" {
			operator = "staff:" + req.StaffId
			staff, _ := ec_store.CStaff.GetById(ctx, bson.ObjectIdHex(req.StaffId))
			extra["staffName"] = staff.Name
		} else {
			operator = getOperator(ctx, "", false)
			extra["userName"] = getOperatorName(ctx, "")
		}
		ec_order.ReturnStockWhenConfirmReceipt(ctx, orderRefund)
		orderRefund.GenerateHistory(operator, "", extra)

		orderRefund.Status = ec_order.ORDER_REFUND_STATUS_PENDING
		orderRefund.GenerateHistory(operator, "", extra)
		if req.Remarks != "" {
			orderRefund.Remarks = req.Remarks
		}
		orderRefund.ClaimFreightInsurance(ctx)
		orderRefund.UpdateRefundStatus(ctx, req)

	case ORDER_REFUND_OPERATE_TYPE_GOODS_RETURNED: // 退货并填写退货物流
		orderRefund.Status = ec_order.ORDER_REFUND_STATUS_RETURNED
		extra := bson.M{
			"refundLogistics": req.RefundLogistics,
			"memberName":      getOperatorName(ctx, req.MemberId),
		}
		orderRefund.Logistics = ec_order.LogisticsDetail{
			DeliveryName: req.RefundLogistics.DeliveryName,
			WaybillId:    req.RefundLogistics.WaybillId,
			DeliveredAt:  time.Now(),
		}
		orderRefund.GenerateHistory(getOperator(ctx, req.MemberId, false), "", extra)
		if req.Remarks != "" {
			orderRefund.Remarks = req.Remarks
		}
		orderRefund.UpdateRefundStatus(ctx, req)
		orderRefund.UpdateRefundLogistics(ctx)

	case ORDER_REFUND_OPERATE_TYPE_AGREE_TO_REFUND: // 同意退款
		orderRefund.Status = ec_order.ORDER_REFUND_STATUS_APPROVED
		orderRefund.AuditedAt = time.Now()
		operator := ""
		extra := bson.M{}
		if req.StaffId != "" {
			operator = "staff:" + req.StaffId
			staff, _ := ec_store.CStaff.GetById(ctx, bson.ObjectIdHex(req.StaffId))
			extra["staffName"] = staff.Name
		} else {
			operator = getOperator(ctx, req.MemberId, false)
			extra["userName"] = getOperatorName(ctx, req.MemberId)
		}
		if req.Remarks != "" {
			orderRefund.Remarks = req.Remarks
		}
		orderRefund.GenerateHistory(operator, "", extra)

		orderRefund.Status = ec_order.ORDER_REFUND_STATUS_PENDING
		orderRefund.GenerateHistory(operator, "", extra)
		orderRefund.UpdateRefundStatus(ctx, req)

		if req.StaffId == "" {
			share.CreateAuditLogByCoroutine(ctx, &account.CreateAuditLogRequest{
				OperatorId:       core_util.GetUserId(ctx),
				OperationObject:  "订单",
				OperationDetails: fmt.Sprintf("将订单「%s」审核退款", orderRefund.OrderNumber),
				AppId:            "retail",
			})
		}

	case ORDER_REFUND_OPERATE_TYPE_REFUND_REFUSED: // 拒绝退款申请
		orderRefund.Status = ec_order.ORDER_REFUND_STATUS_REJECTED
		orderRefund.AuditedAt = time.Now()
		operator := ""
		extra := bson.M{}
		if req.StaffId != "" {
			operator = "staff:" + req.StaffId
			staff, _ := ec_store.CStaff.GetById(ctx, bson.ObjectIdHex(req.StaffId))
			extra["staffName"] = staff.Name
		} else {
			operator = getOperator(ctx, req.MemberId, false)
			extra["userName"] = getOperatorName(ctx, req.MemberId)
		}
		orderRefund.GenerateHistory(operator, req.RejectedReason, extra)
		orderRefund.UpdateRefundStatus(ctx, req)
		// 取消退款时，如果退款单中包含退运费，需要修改订单 isDeliveryFeeRefunded 字段
		if orderRefund.ExpressFee > 0 {
			order.IsDeliveryFeeRefunded = false
			order.UpdateIsDeliveryFeeRefunded(ctx)
		}

	case ORDER_REFUND_OPERATE_TYPE_UPDATE_APPLICATION: // 更新退款申请
		orderRefund.Status = ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT
		orderRefund.Description = req.Description
		if req.RefundReason != "" {
			orderRefund.Reason = req.RefundReason
		}
		// 支持仅退款或退货退款
		if req.RefundType != "" {
			if orderRefund.RefundType == ec_order.ORDER_REFUND_TYPE_ONLY_REFUND && req.RefundType == ec_order.ORDER_REFUND_TYPE_RETURN_AND_REFUND {
				orderRefund.OmsProcessor.NeedUpdateRefundNo = true
			}
			orderRefund.RefundType = req.RefundType
		}
		if req.DeliveryStatus != "" {
			orderRefund.DeliveryStatus = req.DeliveryStatus
		}
		extra := bson.M{
			"memberName": getOperatorName(ctx, req.MemberId),
		}
		orderRefund.GenerateHistory(getOperator(ctx, req.MemberId, false), req.RefundReason, extra)
		orderRefund.Histories[len(orderRefund.Histories)-1].Description = req.Description
		if req.Certificate != nil && (len(req.Certificate.Images) > 0 || req.Certificate.Video.Url != "") {
			certificate := ec_order.Certificate{}
			copier.Instance(nil).From(req.Certificate).CopyTo(&certificate)
			orderRefund.Histories[len(orderRefund.Histories)-1].Certificate = certificate
			orderRefund.Certificate = certificate
		} else {
			orderRefund.Certificate = ec_order.Certificate{}
		}
		orderRefund.UpdateRefundStatus(ctx, nil)
		// 总部发货时不打印小票
		if !(order.Operator == ec_order.LOGISTICS_OPERATOR_TYPE_USER && order.Method == ec_order.ORDER_DELIVERY_METHOD_EXPRESS) {
			ec_client.OrderService.PrintRefundTicket(ctx, &pb_order.PrintTicketRequest{
				OrderId:  orderRefund.Id.Hex(),
				MemberId: orderRefund.MemberId.Hex(),
				StoreId:  orderRefund.StoreId.Hex(),
			})
		}
		orderRefund.CreateNotification(ctx, ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT)
	case ORDER_REFUND_OPERATE_TYPE_APPLICATION_CANCELED: // 取消申请
		orderRefund.Status = ec_order.ORDER_REFUND_STATUS_CANCELED
		orderRefund.GenerateHistory(getOperator(ctx, req.MemberId, false), "memberCanceled", bson.M{})
		orderRefund.UnbindFreightInsuranceReturnId(ctx)
		orderRefund.UpdateRefundStatus(ctx, nil)

		// 取消退款时，如果退款单中包含退运费，需要修改订单 isDeliveryFeeRefunded 字段
		if orderRefund.ExpressFee > 0 {
			order.IsDeliveryFeeRefunded = false
			order.UpdateIsDeliveryFeeRefunded(ctx)
		}

		// 如果取消退款的退款单中包含满增券，且用户取消退款后满足满赠条件，则更新赠品退款状态使赠品仍可赠送
		handlePresent(ctx, orderRefund)
		// 取消申请打印取消退款单小票,总部发货时不打印小票
		if !(order.Operator == ec_order.LOGISTICS_OPERATOR_TYPE_USER && order.Method == ec_order.ORDER_DELIVERY_METHOD_EXPRESS) {
			ec_client.OrderService.PrintRefundTicket(ctx, &pb_order.PrintTicketRequest{
				OrderId:  orderRefund.Id.Hex(),
				MemberId: orderRefund.MemberId.Hex(),
				StoreId:  orderRefund.StoreId.Hex(),
				Type:     PRINT_TICKET_TYPE_CANCELLED_REFUND_ORDER,
				Remarks:  "memberCanceled",
			})
		}
	}
	SendRefundUpdatedEvent(ctx, orderRefund, order, req)
	return &response.EmptyResponse{}, nil
}

func changeRefundAmount(orderRefund *ec_order.OrderRefund, refundAmount uint64, refundAmountChangeReason string) error {
	originalRefundAmount := orderRefund.RefundAmount
	originalRefundAmount += orderRefund.RefundStoredValue.Amount
	for _, item := range orderRefund.PrepaidCardRefunds {
		originalRefundAmount += item.RefundAmount
	}
	if refundAmount >= originalRefundAmount {
		return errors.NewInvalidArgumentError("refundAmount")
	}
	reducedAmount := originalRefundAmount - refundAmount
	orderRefund.RefundAmountChangeReason = refundAmountChangeReason
	// 从微信退款中扣除
	if orderRefund.RefundAmount >= reducedAmount {
		orderRefund.RefundAmount -= reducedAmount
		for i := range orderRefund.Products {
			if orderRefund.Products[i].RefundAmount >= reducedAmount {
				orderRefund.Products[i].RefundAmount -= reducedAmount
				reducedAmount = 0
			} else {
				reducedAmount -= orderRefund.Products[i].RefundAmount
				orderRefund.Products[i].RefundAmount = 0
			}
		}
		return nil
	}
	reducedAmount -= orderRefund.RefundAmount
	orderRefund.RefundAmount = 0
	// 从储值退款中扣除
	if orderRefund.RefundStoredValue.Amount >= reducedAmount {
		orderRefund.RefundStoredValue.Amount -= reducedAmount
		for i := range orderRefund.Products {
			if orderRefund.Products[i].RefundStoredValue.Amount >= reducedAmount {
				orderRefund.Products[i].RefundStoredValue.Amount -= reducedAmount
				reducedAmount = 0
			} else {
				reducedAmount -= orderRefund.Products[i].RefundStoredValue.Amount
				orderRefund.Products[i].RefundStoredValue.Amount = 0
			}
		}
		return nil
	}
	reducedAmount -= orderRefund.RefundStoredValue.Amount
	orderRefund.RefundStoredValue.Amount = 0
	for i := range orderRefund.Products {
		orderRefund.Products[i].RefundStoredValue.Amount = 0
	}
	// 从储值卡退款中扣除
	for i := range orderRefund.Products {
		for j := range orderRefund.Products[i].PrepaidCardRefunds {
			if orderRefund.Products[i].PrepaidCardRefunds[j].RefundAmount > reducedAmount {
				orderRefund.Products[i].PrepaidCardRefunds[j].RefundAmount -= reducedAmount
				reducedAmount = 0
			} else {
				reducedAmount -= orderRefund.Products[i].PrepaidCardRefunds[j].RefundAmount
				orderRefund.Products[i].PrepaidCardRefunds[j].RefundAmount = 0
			}
		}
	}
	for i := range orderRefund.PrepaidCardRefunds {
		orderRefund.PrepaidCardRefunds[i].RefundAmount = 0
	}
	for i := range orderRefund.Products {
		for j := range orderRefund.Products[i].PrepaidCardRefunds {
			for k := range orderRefund.PrepaidCardRefunds {
				if orderRefund.PrepaidCardRefunds[k].PrepaidCardId != orderRefund.Products[i].PrepaidCardRefunds[j].PrepaidCardId {
					continue
				}
				orderRefund.PrepaidCardRefunds[k].RefundAmount += orderRefund.Products[i].PrepaidCardRefunds[j].RefundAmount
			}
		}
	}
	return nil
}

func handlePresent(ctx context.Context, orderRefund ec_order.OrderRefund) {
	presentDiscountIds := []string{}
	for _, p := range orderRefund.Products {
		for _, d := range p.Discounts {
			if d.Type == "present" {
				presentDiscountIds = append(presentDiscountIds, d.Id.Hex())
			}
		}
	}
	rollbackDiscountIds := orderRefund.RefundExtra.RollbackDiscounts
	cancelRollbackPresentDiscountIds := core_util.ToStringArray(core_util.GetArraysIntersection(rollbackDiscountIds, presentDiscountIds))
	if len(cancelRollbackPresentDiscountIds) == 0 {
		return
	}
	order, _ := ec_order.COrder.GetById(ctx, orderRefund.OrderId)
	for _, presentDiscountId := range cancelRollbackPresentDiscountIds {
		var totalAmount uint64
		for _, product := range order.Products {
			if product.RefundStatus == "" || product.RefundStatus == ec_order.ORDER_REFUND_STATUS_CANCELED {
				for _, discount := range product.Discounts {
					if discount.Id.Hex() != presentDiscountId {
						continue
					}
					totalAmount += product.Price * product.Total
				}
			}
		}
		resp, callErr := mairpc.RPCProxy.Call(
			getCouponServiceClient,
			"CouponService.GetMembershipDiscount",
			ctx,
			&coupon.GetMembershipDiscountRequest{
				Id: presentDiscountId,
			},
		)
		if callErr != nil {
			log.Warn(ctx, "Failed to get membershipDiscount", log.Fields{
				"membershipDiscountId": presentDiscountId,
				"errMsg":               callErr.Error(),
			})
			return
		}
		presentDiscount := resp.(*coupon.MembershipDiscount)
		if presentDiscount.Coupon.DiscountCondition != nil && core_util.Float64MultiHundred(presentDiscount.Coupon.DiscountCondition.Value) <= int64(totalAmount) {
			needUpdate := false
			for i, product := range order.Products {
				for _, campaign := range product.Campaigns {
					if campaign.Type != ec_order.CAMPAIGN_TYPE_PRESENT_COUPON || campaign.DiscountId.Hex() != presentDiscountId {
						continue
					}
					if order.Products[i].RefundStatus == ec_order.ORDER_REFUND_STATUS_REFUNDED {
						needUpdate = true
						order.Products[i].RefundStatus = ""
					}
				}
			}
			if needUpdate {
				err := order.UpdateProducts(ctx)
				if err != nil {
					log.Warn(ctx, "Failed to update order products", log.Fields{
						"orderId": order.Id.Hex(),
						"errMsg":  err.Error(),
					})
				}
			}
		}
	}
}

func getRefundLocation(ctx context.Context, order ec_order.Order) setting_model.ContactAddress {
	if order.Operator == setting_model.DELIVERY_SETTING_OPERATOR_USER {
		setting, err := setting_model.CDeliverySetting.Get(ctx)
		if err != nil {
			return setting_model.ContactAddress{}
		}

		return setting.Logistics.ShipSetting.ReturnAddress
	}

	store, err := ec_store.CStore.GetById(ctx, order.StoreId)
	if err != nil {
		return setting_model.ContactAddress{}
	}

	resp := setting_model.ContactAddress{
		Name: store.Name,
		Tel:  store.Phone,
		Address: setting_model.Address{
			Province: store.Location.Province,
			District: store.Location.District,
			City:     store.Location.City,
			Detail:   store.Location.Name,
		},
	}

	return resp
}

func SendRefundUpdatedEvent(ctx context.Context, orderRefund ec_order.OrderRefund, order ec_order.Order, req *order.UpdateOrderRefundRequest) {
	eventProperties := map[string]interface{}{
		"orderRefundId":  orderRefund.Id.Hex(),
		"refundAmount":   orderRefund.RefundAmount,
		"openId":         order.Channel.OpenId,
		"refundType":     orderRefund.RefundType,
		"refundReason":   orderRefund.Reason,
		"deliveryStatus": orderRefund.DeliveryStatus,
		"operateType":    req.OperateType,
		"orderOperator":  order.Operator,
		"orderStoreId":   order.StoreId,
		"staffId":        req.StaffId,
		"description":    orderRefund.Description,
		"memberId":       orderRefund.MemberId.Hex(),
		"isWechatUpdate": req.IsWechatUpdate,
	}
	if req.RefundLogistics != nil {
		eventProperties["deliveryName"] = req.RefundLogistics.DeliveryName
		eventProperties["waybillId"] = req.RefundLogistics.WaybillId
	}
	if req.OperateType == ORDER_REFUND_OPERATE_TYPE_UPDATE_APPLICATION {
		eventProperties["description"] = req.Description
		if req.RefundReason != "" {
			eventProperties["refundReason"] = req.RefundReason
		}
		if req.RefundType != "" {
			eventProperties["refundType"] = req.RefundType
		}
		if req.DeliveryStatus != "" {
			eventProperties["deliveryStatus"] = req.DeliveryStatus
		}
	}
	storeType := orderRefund.GetStoreType()
	if storeType != "" {
		eventProperties["storeType"] = storeType
	}
	distributorId := orderRefund.GetDistributorId()
	if distributorId != "" {
		eventProperties["distributorId"] = distributorId
	}
	eventBody := component.CustomerEventBody{
		Id:              fmt.Sprintf("%s:%s", component.MAIEVENT_ORDER_REFUND_UPDATED, bson.NewObjectId().Hex()),
		AccountId:       util.GetAccountId(ctx),
		MsgType:         component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:         component.MAIEVENT_ORDER_REFUND_UPDATED,
		CreateTime:      time.Now().UnixNano() / 1e6,
		EventProperties: eventProperties,
	}
	eventBody.SendCustomerEventWithBusinessTag(ctx)
}
