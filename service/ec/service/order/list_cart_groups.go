package order

import (
	"context"
	"fmt"
	mairpc "mairpc/core/client"
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/common/ec"
	common "mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/proto/coupon"
	ec_order "mairpc/proto/ec/order"
	"mairpc/proto/ec/product"
	"mairpc/proto/ec/setting"
	ec_store "mairpc/proto/ec/store"
	pb_member "mairpc/proto/member"
	pb_product "mairpc/proto/product"
	"mairpc/service/ec/client"
	ec_model "mairpc/service/ec/model"
	marketing_model "mairpc/service/ec/model/marketing"
	order_model "mairpc/service/ec/model/order"
	ec_product_model "mairpc/service/ec/model/product"
	ec_store_product "mairpc/service/ec/model/storeProduct"
	share_service "mairpc/service/ec/service"
	ec_share "mairpc/service/ec/share"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"
	"sort"
	"time"
)

const (
	PRODUCT_TYPE_PRODUCT = "product"
	PRODUCT_TYPE_COUPON  = "coupon"
	PRODUCT_TYPE_VIRTUAL = "virtual"

	STATUS_CAMPAIGN_DISABLE_SELL = "campaign_disable_sell" // 活动禁售
)

func (OrderService) ListCartGroups(ctx context.Context, req *ec_order.ListCartGroupsRequest) (*ec_order.ListCartGroupsResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	cartProducts, err := getMemberCartProducts(ctx, req)
	if err != nil {
		return nil, err
	}
	if len(cartProducts) == 0 {
		return &ec_order.ListCartGroupsResponse{}, nil
	}
	member, err := GetMember(ctx, req.MemberId)
	if err != nil {
		return nil, err
	}
	store := ec_share.GetBriefStore(ctx, req.StoreId)
	if store == nil {
		return nil, errors.NewNotExistsError("storeId")
	}

	productsResp, err := getProducts(ctx, req.StoreId, cartProducts)
	if err != nil {
		return nil, err
	}

	privilege, _ := share_service.GetMemberPrivilege(ctx, req.MemberId, MEMBER_PRIVILEGE_DISCOUNT)
	// 获取每个商品参与的活动，按活动分组
	options := []OrderBuilderOption{
		WithPurchaseRequest(&common.PurchaseRequest{StoreId: req.StoreId, MemberId: req.MemberId}),
		WithMember(member),
		WithStore(store),
		WithMemberDiscountPrivilege(privilege),
	}
	if req.FilterStore {
		options = append(options, WithFilterStore(true))
	}
	builder := GenerateBuilderWithOptions(options...)
	builder.cartProductAddedBy = req.AddedBy
	builder.cartProductProxyOrderId = req.ProxyOrderId

	respCartProducts, err := formatCartProducts(ctx, req, cartProducts, productsResp.Items, privilege, store, builder)
	if err != nil {
		return nil, err
	}
	cartCampaigns, err := formatCartCampaigns(ctx, cartProducts, respCartProducts, builder)
	if err != nil {
		log.Warn(ctx, "Failed to formatCartCampaigns", log.Fields{
			"errMsg":            err.Error(),
			"cartProductsCount": len(cartProducts),
		})
	}
	invalidCartCampaigns, validCartCampaigns := filterCartCampaigns(cartCampaigns, cartProducts)
	validCartCampaigns = checkHasCoupon(ctx, validCartCampaigns, req.StoreId, member)

	var calcOrderAmountResp *ec.CalculateOrderAmountResponse
	uncheckInvalidProducts(ctx, invalidCartCampaigns, cartProducts)
	// 代客下单，直接到计算订单金额页，如果 uncheck，GetOrderDeliveryMethods 接口会报错，这里不处理
	if !isProxyOrderMember(req) {
		uncheckInvalidProducts(ctx, validCartCampaigns, cartProducts)
	}
	if hasCheckedProduct(validCartCampaigns) {
		calcOrderAmountResp, err = calcOrderAmount(ctx, req, validCartCampaigns, builder)
		if err != nil {
			return nil, err
		}
	}
	// 代客下单，导购下单时不需要处理活动 tip 和 赠品
	if !isProxyOrderStaff(req) {
		formatCampaignsTips(ctx, validCartCampaigns, calcOrderAmountResp, builder)
		validCartCampaigns = formatPresents(validCartCampaigns, calcOrderAmountResp)
	}
	return formatCartGroupsResponse(ctx, invalidCartCampaigns, validCartCampaigns, calcOrderAmountResp, store, member)
}

// 是否是代客下单导购下单
func isProxyOrderStaff(req *ec_order.ListCartGroupsRequest) bool {
	return req.AddedBy == "proxyOrder" && req.ProxyOrderId == ""
}

// 是否是代客下单客户下单
func isProxyOrderMember(req *ec_order.ListCartGroupsRequest) bool {
	return req.AddedBy == "proxyOrder" && req.ProxyOrderId != ""
}

func getMemberCartProducts(ctx context.Context, req *ec_order.ListCartGroupsRequest) ([]order_model.CartProduct, error) {
	orderBys := []string{"-createdAt"}
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"memberId":  bson.ObjectIdHex(req.MemberId),
		"storeId": bson.M{
			"$exists": false,
		},
	}
	// 脉盟小店商城需要加 storeId 参数
	if req.FilterStore {
		condition["storeId"] = util.ToMongoId(req.StoreId)
	}
	if req.AddedBy != "" {
		condition["addedBy"] = req.AddedBy
		if req.AddedBy == "proxyOrder" {
			if req.ProxyOrderId != "" {
				condition["proxyOrderId"] = bson.ObjectIdHex(req.ProxyOrderId)
			} else {
				condition["proxyOrderId"] = bson.M{
					"$exists": false,
				}
			}
		}
	}
	cartProducts, err := order_model.CCart.GetAllByCondition(ctx, condition, orderBys)
	if err != nil {
		return nil, err
	}
	return cartProducts, nil
}

func getProducts(ctx context.Context, storeId string, cartProducts []order_model.CartProduct) (*product.ListProductsResponse, error) {
	ecProductIds := core_util.ToStringArray(core_util.ExtractArrayField("ProductId", cartProducts))
	productsResp, err := client.ProductService.ListProducts(ctx, &ec.ListProductsRequest{
		EcProductIds: ecProductIds,
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: uint32(len(ecProductIds)),
		},
		ContainDeleted:          true,
		StoreId:                 storeId,
		ContainStoreRestriction: true,
	})
	if err != nil {
		return nil, err
	}
	return productsResp, nil
}

// format 购物车商品，返回 []*ec_order.CartProduct
// 1.检查商品参与的活动，过滤掉当前客户不能参加的活动
// 2.检查限购
// 3.检查商品状态，将对应状态赋值到 cartProduct.Status 中
// 4.设置会员价
// 5.检查选中状态
func formatCartProducts(ctx context.Context, req *ec_order.ListCartGroupsRequest, cartProducts []order_model.CartProduct, Products []*product.ProductResponse, privilege *pb_member.MemberLevelPrivilege, store *ec_store.StoreDetail, builder *OrderBuilder) ([]*ec_order.CartProduct, error) {
	isProxyOrderStaff := isProxyOrderStaff(req)
	groupProducts := []*ec_order.CartProduct{}
	productMap := core_util.MakeMapper("Ec.Id", Products)
	productIds := core_util.ToObjectIdArray(core_util.ExtractArrayField("Ec.ProductId", Products))
	storeProducts, err := ec_store_product.CStoreProduct.GetAllByProductIdsAndStoreId(ctx, productIds, util.ToMongoId(req.StoreId))
	if err != nil {
		return nil, err
	}
	storeProductMapper := core_util.MakeMapper("ProductId", storeProducts)
	ds, err := client.SettingService.GetDeliverySetting(ctx, &request.EmptyRequest{})
	if err != nil {
		return nil, err
	}
	isProductStockEnabled := ec_model.IsProductStockEnabled(ctx, ds)
	// 代客下单只需要返回购物车商品，其余不需要处理
	if isProxyOrderStaff {
		for _, cartProduct := range cartProducts {
			productResp := productMap[cartProduct.ProductId.Hex()].(*product.ProductResponse)
			applicableSkus := core_util.ToStringArray(core_util.ExtractArrayField("Sku", productResp.Ec.Skus))
			tempGroupProduct := &ec_order.CartProduct{
				Id:                cartProduct.Id.Hex(),
				ProductId:         productResp.Ec.Id,
				OriginalProductId: productResp.Product.Id,
				ProductName:       productResp.Product.Name,
				Number:            productResp.Product.Number,
				Total:             int64(cartProduct.Total),
				Checked:           cartProduct.Checked,
				Specs:             &ec_order.SkuSpec{Sku: cartProduct.Sku, Properties: cartProduct.Properties},
				ProductType:       productResp.Product.Type,
				PackageId:         cartProduct.PackageId.Hex(),
				ApplicableSkus:    applicableSkus,
				LimitCount:        productResp.Ec.Limit,
				PurchaseLimit:     productResp.Ec.PurchaseLimit,
				IsIncTotalEnabled: true,
				MinPurchaseCount:  productResp.Ec.MinPurchaseCount,
				PaymentMethod:     productResp.Ec.PaymentMethod,
				AddedBy:           cartProduct.AddedBy,
				Status: func() string {
					status := ec_product_model.SHELVE_STATUS_SHELVED // 代客下单分销员下单不商品库存、限购等，商品默认是上架状态
					existsSku := false
					for _, sku := range productResp.Ec.Skus {
						if sku.Sku == cartProduct.Sku {
							existsSku = true
						}
					}
					if productResp.Ec.IsDeleted || !existsSku {
						status = ec_product_model.PRODUCT_STATUS_DELETED
					}
					if productResp.Ec.ShelveStatus != ec_product_model.SHELVE_STATUS_SHELVED {
						status = productResp.Ec.ShelveStatus
					}
					return status
				}(),
			}
			if tempGroupProduct.ProductType == "" {
				tempGroupProduct.ProductType = "product"
			}
			formatCartProductSpecs(productResp, tempGroupProduct)
			formatCartProductPrice(productResp, tempGroupProduct)
			if tempGroupProduct.Picture == "" {
				tempGroupProduct.Picture = productResp.Product.Pictures[0].Url
			}
			groupProducts = append(groupProducts, tempGroupProduct)
		}
		return groupProducts, nil
	}

	err = formatSkuStockForMaimengConsignment(ctx, Products, storeProductMapper)
	if err != nil {
		return nil, err
	}

	invalidPackageStatusMap := make(map[string]string)
	ecProductIds := core_util.ToObjectIdArray(core_util.ExtractArrayField("Ec.Id", Products))
	statsList, err := order_model.COrderProductPurchaseStats.GetByProductIdsAndMemberId(ctx, util.ToMongoId(req.MemberId), ecProductIds)
	if err != nil {
		return nil, err
	}
	statsMap := core_util.MakeMapper("ProductId", statsList)
	// 商品各规格购买计数总和的 map
	productCountMap := map[string]int64{}
	for _, cp := range cartProducts {
		productCountMap[cp.ProductId.Hex()] = productCountMap[cp.ProductId.Hex()] + int64(cp.Total)
	}

	for _, cartProduct := range cartProducts {
		productResp := productMap[cartProduct.ProductId.Hex()].(*product.ProductResponse)
		applicableSkus := core_util.ToStringArray(core_util.ExtractArrayField("Sku", productResp.Ec.Skus))
		tempGroupProduct := &ec_order.CartProduct{
			Id:                cartProduct.Id.Hex(),
			ProductId:         productResp.Ec.Id,
			OriginalProductId: productResp.Product.Id,
			ProductName:       productResp.Product.Name,
			Number:            productResp.Product.Number,
			Total:             int64(cartProduct.Total),
			Checked:           cartProduct.Checked,
			Specs:             &ec_order.SkuSpec{Sku: cartProduct.Sku, Properties: cartProduct.Properties},
			ProductType:       productResp.Product.Type,
			PackageId:         cartProduct.PackageId.Hex(),
			ApplicableSkus:    applicableSkus,
			LimitCount:        productResp.Ec.Limit,
			PurchaseLimit:     productResp.Ec.PurchaseLimit,
			IsIncTotalEnabled: true,
			MinPurchaseCount:  productResp.Ec.MinPurchaseCount,
			PaymentMethod:     productResp.Ec.PaymentMethod,
			AddedBy:           cartProduct.AddedBy,
		}
		if tempGroupProduct.ProductType == "" {
			tempGroupProduct.ProductType = "product"
		}
		for _, c := range productResp.Ec.Campaigns {
			if c.StartAt == "" || (time.Now().After(core_util.ParseRFC3339(c.StartAt)) && time.Now().Before(core_util.ParseRFC3339(c.EndAt))) {
				// 过滤当前客户不能参加的活动
				memberFilter := share_model.UnmarshalFromExtraString(c.Extra)
				var groupIds []string
				if memberFilter.HasGroupLimit() {
					groupIds = builder.GetMemberGroupIds(ctx)
				}
				ok := memberFilter.CheckMember(ctx, builder.member, groupIds)
				if ok {
					tempGroupProduct.Campaigns = append(tempGroupProduct.Campaigns, &ec_order.BriefCampaign{Id: c.Id, Type: c.Type})
				}
			}
		}
		var purchaseStats *order_model.OrderProductPurchaseStats
		stats, ok := statsMap[util.ToMongoId(productResp.Ec.Id)]
		if ok {
			purchaseStats = stats.(*order_model.OrderProductPurchaseStats)
		}
		periodType := tempGroupProduct.PurchaseLimit.PeriodType
		purchaseLimit := ec_product_model.PurchaseLimit{}
		copier.Instance(nil).From(productResp.Ec.PurchaseLimit).CopyTo(&purchaseLimit)
		tempGroupProduct.Status = checkProductPurchaseLimit(ctx, builder.member, purchaseLimit, purchaseStats, productCountMap[cartProduct.ProductId.Hex()])
		if purchaseStats != nil {
			if purchaseStats.PeriodType == periodType && purchaseStats.Date == order_model.GetDateByPeriodType(periodType, time.Now()) {
				tempGroupProduct.PurchasedCount = purchaseStats.Count
			}
		}
		if tempGroupProduct.LimitCount != 0 && tempGroupProduct.PurchasedCount+uint64(tempGroupProduct.Total) == tempGroupProduct.LimitCount {
			tempGroupProduct.IsIncTotalEnabled = false
		}

		// 达到限购不允许选中但是允许调整数量
		if tempGroupProduct.Status == ec_product_model.STATUS_PURCHASE_LIMIT {
			tempGroupProduct.IsIncTotalEnabled = true
			tempGroupProduct.IsCheckBoxEnabled = false
		}

		formatCartProductSpecs(productResp, tempGroupProduct)
		formatCartProductPrice(productResp, tempGroupProduct)
		var storeProduct *ec_store_product.StoreProduct
		if sp, ok := storeProductMapper[util.ToMongoId(productResp.Ec.ProductId)].(ec_store_product.StoreProduct); ok {
			storeProduct = &sp
		}
		formatCartProductStatus(ctx, productResp, tempGroupProduct, isProductStockEnabled, store, ds, storeProduct)
		if cartProduct.PackageId.Valid() {
			tempGroupProduct.IsIncTotalEnabled = false
			if tempGroupProduct.Status != ec_product_model.SHELVE_STATUS_SHELVED {
				invalidPackageStatusMap[cartProduct.PackageId.Hex()] = tempGroupProduct.Status
			}
		}
		if tempGroupProduct.Picture == "" {
			tempGroupProduct.Picture = productResp.Product.Pictures[0].Url
		}
		if tempGroupProduct.Checked && productResp.Ec.IsMemberDiscountEnabled && privilege != nil {
			tempGroupProduct.MemberDiscountPrice = tempGroupProduct.Price * int64(privilege.Discount) / 100
		}
		groupProducts = append(groupProducts, tempGroupProduct)
	}
	invalidStatusMap := make(map[string]string)
	for _, cartProduct := range cartProducts {
		if status, ok := invalidPackageStatusMap[cartProduct.PackageId.Hex()]; ok {
			invalidStatusMap[cartProduct.Id.Hex()] = status
		}
	}
	for i, product := range groupProducts {
		if status, ok := invalidStatusMap[product.Id]; ok {
			groupProducts[i].Status = status
		}
	}
	formatCheckStatus(groupProducts)
	return groupProducts, nil
}

// listProducts 接口已经处理连锁零售商租户，所以不需要考虑
func formatSkuStockForMaimengConsignment(ctx context.Context, productsResp []*product.ProductResponse, storeProductMapper map[interface{}]interface{}) error {
	if !ec_model.IsMaimengRetail(ctx) {
		return nil
	}

	// 脉盟小店租户下小店商品 storeProduct 对应 product 表的 fields 字段为空，拷贝该字段可以复用代码逻辑
	for _, productResp := range productsResp {
		if storeProduct, ok := storeProductMapper[util.ToMongoId(productResp.Ec.ProductId)].(ec_store_product.StoreProduct); ok {
			productFields := []*pb_product.Field{}
			copier.Instance(nil).From(storeProduct.Fields).CopyTo(&productFields)
			productResp.Product.Fields = productFields
		}
	}

	err := ec_model.FormatSkuStockForConsignment(ctx, productsResp)
	if err != nil {
		return err
	}
	return nil
}

func formatCheckStatus(groupProducts []*ec_order.CartProduct) {
	checkedProductType := ""
	for _, p := range groupProducts {
		if p.Status != ec_product_model.SHELVE_STATUS_SHELVED {
			continue
		}
		if p.Checked {
			checkedProductType = p.ProductType
			break
		}
	}
	for i, p := range groupProducts {
		if (checkedProductType == "" || p.ProductType == checkedProductType) && p.Status != ec_product_model.STATUS_PURCHASE_LIMIT {
			// 没有勾选任何商品时，所有商品都可以勾选；
			// 勾选了某一类商品时，只能勾选同类商品。
			groupProducts[i].IsCheckBoxEnabled = true
		}
	}
}

func formatCartProductSpecs(productResp *product.ProductResponse, groupProduct *ec_order.CartProduct) {
	for _, skus := range productResp.Product.Skus {
		if skus.Sku != groupProduct.Specs.Sku {
			continue
		}

		if len(skus.Properties) == 0 {
			continue
		}

		for _, propertyName := range skus.Properties {
			for _, matchSpec := range productResp.Product.Specifications {
				for _, matchProperties := range matchSpec.Properties {
					if matchProperties.Name == propertyName {
						groupProduct.Picture = matchProperties.Picture.Url
						return
					}
				}
			}
		}
	}
}

func formatCartProductPrice(productResp *product.ProductResponse, groupProduct *ec_order.CartProduct) {
	for _, skus := range productResp.Ec.Skus {
		if skus.Sku != groupProduct.Specs.Sku {
			continue
		}
		groupProduct.Price = skus.Price
	}
}

func formatCartProductStatus(ctx context.Context, productResp *product.ProductResponse, groupProduct *ec_order.CartProduct, isProductStockEnabled bool, store *ec_store.StoreDetail, ds *setting.GetDeliverySettingResponse, storeProduct *ec_store_product.StoreProduct) {
	existsSku := false
	limitStatus := groupProduct.Status
	for _, sku := range productResp.Ec.Skus {
		if sku.Sku == groupProduct.Specs.Sku {
			existsSku = true
		}
	}
	if productResp.Ec.IsDeleted || !existsSku {
		groupProduct.Status = ec_product_model.PRODUCT_STATUS_DELETED
		return
	}

	if productResp.Ec.ShelveStatus != ec_product_model.SHELVE_STATUS_SHELVED {
		groupProduct.Status = productResp.Ec.ShelveStatus
		return
	}
	groupProduct.Status = ec_product_model.SHELVE_STATUS_SHELVED
	isStockEnabledForProduct := share_service.CheckProductStockEnabled(isProductStockEnabled, ds, productResp.Ec.DeliveryMethods, productResp.Product.Type)
	isRetailerAccount := ec_model.IsMaimengRetail(ctx) || ec_model.IsChainRetail(ctx)
	isStoredValueCardProduct := productResp.Ec.SubType == ec_product_model.C_PRODUCT_SUB_TYPE_STORED_VALUE_CARD
	isStockEnabledForRetailer := false
	if isRetailerAccount {
		if !ec_model.IsConsignmentProduct(productResp.Product.Fields) {
			isStockEnabledForRetailer = isStockEnabledForProduct
		} else {
			isStockEnabledForRetailer = ec_model.GetFieldByName(productResp.Product.Fields, ec_store_product.FIELD_NAME_CONSIGNMENT_STOCK_ENABLED) != nil
		}
	}
	if (!isStoredValueCardProduct && !isRetailerAccount && isStockEnabledForProduct) || (!isStoredValueCardProduct && isRetailerAccount && isStockEnabledForRetailer) {
		isAllSkusSoldOut := true
		for _, s := range productResp.Ec.Skus {
			if isAllSkusSoldOut && s.Stock != 0 {
				isAllSkusSoldOut = false
			}
		}
		if isAllSkusSoldOut {
			groupProduct.Status = ec_product_model.STATUS_ALL_SKUS_SOLD_OUT
			return
		}
		for _, s := range productResp.Ec.Skus {
			if s.Sku != groupProduct.Specs.Sku {
				continue
			}
			groupProduct.Stock = s.Stock
			if s.Stock == 0 {
				groupProduct.Status = ec_product_model.STATUS_SOLD_OUT
				return
			}
			if s.Stock == groupProduct.Total {
				groupProduct.IsIncTotalEnabled = false
			}
			if s.Stock < groupProduct.Total {
				groupProduct.Status = ec_product_model.STATUS_UNDERSTOCKED
				return
			}
		}
	}
	modelStoreRestriction := ec_product_model.StoreRestriction{}
	copier.Instance(nil).From(productResp.Ec.StoreRestriction).CopyTo(&modelStoreRestriction)
	if !CanSellInStore(modelStoreRestriction, store) {
		groupProduct.Status = ec_product_model.STATUS_STORE_RESTRICTION
		return
	}
	method := "express"
	if groupProduct.AddedBy == "scanToBuy" {
		method = "pickup"
	}
	if !CheckStoreShelveStatus(ds, storeProduct, groupProduct.Specs.Sku, method) {
		groupProduct.Status = ec_product_model.STATUS_STORE_UNSHELVED
		return
	}
	for _, c := range productResp.Ec.Campaigns {
		if c.DisableSell && time.Now().Before(core_util.ParseRFC3339(c.StartAt)) {
			groupProduct.Status = STATUS_CAMPAIGN_DISABLE_SELL
		}
		if c.Type == marketing_model.CAMPAIGN_TYPE_PRESELL && time.Now().After(core_util.ParseRFC3339(c.StartAt)) && time.Now().Before(core_util.ParseRFC3339(c.EndAt)) {
			groupProduct.Status = STATUS_CAMPAIGN_DISABLE_SELL
		}
	}

	if limitStatus != "" {
		groupProduct.Status = limitStatus
		return
	}
}

// 根据商品参与的活动生成 CartCampaign，没有参与活动的商品放到默认的 CartCampaign 中
func formatCartCampaigns(ctx context.Context, cartProducts []order_model.CartProduct, respCartProducts []*ec_order.CartProduct, builder *OrderBuilder) ([]*ec_order.CartCampaign, error) {
	cartCampaigns := []*ec_order.CartCampaign{}
	// 代下单导购下单不参与任何活动
	if builder.cartProductAddedBy == "proxyOrder" && builder.cartProductProxyOrderId == "" {
		cartCampaigns = []*ec_order.CartCampaign{
			{
				Products: respCartProducts,
			},
		}
		return cartCampaigns, nil
	}

	for _, c := range CartCampaigns {
		// tmpRespCartProducts 是未参与该活动的商品
		tmpCampaignGroup, tmpRespCartProducts, err := c.formatCampaignGroup(ctx, cartProducts, respCartProducts, builder)
		if err != nil {
			return nil, err
		}
		cartCampaigns = append(cartCampaigns, tmpCampaignGroup...)
		respCartProducts = tmpRespCartProducts
	}
	// 未参与活动的商品，放到默认 CartCampaign 中，除了 Products，其余字段都为空
	for i := range respCartProducts {
		cartCampaigns = append(cartCampaigns, &ec_order.CartCampaign{
			Products: []*ec_order.CartProduct{respCartProducts[i]},
		})
	}
	return cartCampaigns, nil
}

func filterCartCampaigns(cartCampaigns []*ec_order.CartCampaign, cartProducts []order_model.CartProduct) (invalidCartCampaigns []*ec_order.CartCampaign, validCartCampaigns []*ec_order.CartCampaign) {
	for i, cartCampaign := range cartCampaigns {
		// 促销套餐分组不可拆分
		if cartCampaign.Type == marketing_model.CAMPAIGN_TYPE_PACKAGE {
			if cartCampaign.Status != CAMPAIGN_STATUS_RUNNING || cartCampaign.Products[0].Status != ec_product_model.SHELVE_STATUS_SHELVED {
				invalidCartCampaigns = append(invalidCartCampaigns, cartCampaigns[i])
			} else {
				validCartCampaigns = append(validCartCampaigns, cartCampaigns[i])
			}
			continue
		}
		validCartProducts := []*ec_order.CartProduct{}
		invalidCartProducts := []*ec_order.CartProduct{}
		for index, p := range cartCampaign.Products {
			if core_util.StrInArray(p.Status, &[]string{ec_product_model.SHELVE_STATUS_SHELVED, ec_product_model.STATUS_UNDERSTOCKED}) {
				validCartProducts = append(validCartProducts, cartCampaign.Products[index])
			} else {
				invalidCartProducts = append(invalidCartProducts, cartCampaign.Products[index])
			}
		}
		invalidCartCampaign, validCartCampaign := &ec_order.CartCampaign{}, &ec_order.CartCampaign{}
		if len(validCartProducts) != 0 {
			copier.Instance(nil).From(cartCampaign).CopyTo(validCartCampaign)
			validCartCampaign.Products = validCartProducts
			validCartCampaigns = append(validCartCampaigns, validCartCampaign)
		}
		if len(invalidCartProducts) != 0 {
			copier.Instance(nil).From(cartCampaign).CopyTo(invalidCartCampaign)
			invalidCartCampaign.Products = invalidCartProducts
			invalidCartCampaigns = append(invalidCartCampaigns, invalidCartCampaign)
		}
	}
	createdAtMap := core_util.MakeFieldToFieldMapper("Id", "CreatedAt", cartProducts)
	invalidCartCampaigns = sortCartCampaigns(invalidCartCampaigns, createdAtMap)
	validCartCampaigns = sortCartCampaigns(validCartCampaigns, createdAtMap)
	return
}

func formatRequestCampaigns(cartCampaigns []*ec_order.CartCampaign) (campaigns []*ec.OrderCampaign) {
	for _, c := range CartCampaigns {
		if tempRequestCampaigns := c.formatCampaignRequest(cartCampaigns); tempRequestCampaigns != nil {
			campaigns = append(campaigns, tempRequestCampaigns)
		}
	}
	return
}

func calcOrderAmount(ctx context.Context, req *ec_order.ListCartGroupsRequest, campaigns []*ec_order.CartCampaign, oldBuilder *OrderBuilder) (*ec.CalculateOrderAmountResponse, error) {
	purchaseRequest := formatPurchaseRequest(req, campaigns)
	builder, err := GenerateBuilder(ctx, purchaseRequest, false, false)
	if err != nil {
		return nil, err
	}
	builder.campaignsMap = oldBuilder.campaignsMap
	builder.store = oldBuilder.store
	builder.member = oldBuilder.member
	builder.filterStore = oldBuilder.filterStore
	builder.cartProductAddedBy = oldBuilder.cartProductAddedBy
	builder.cartProductProxyOrderId = oldBuilder.cartProductProxyOrderId
	if req.AddedBy == "proxyOrder" {
		builder.CampaignConfig = &ec.OrderCampaignConfig{
			Campaigns: []*ec.Campaign{
				{
					Type: "proxyOrder",
					Id:   req.ProxyOrderId,
				},
			},
			OverrideProduct: true,
		}
	}
	order, err := generateBasicOrder(ctx, builder)
	if err != nil {
		return nil, err
	}

	err = generateOrderProducts(ctx, order, builder, nil)
	if err != nil {
		return nil, err
	}

	invalidProducts, invalidProductStatusMap := filterInvalidProducts(ctx, builder, order, nil)
	discountIds, err := getMostFavorableCoupons(ctx, order, builder)
	if err != nil {
		return nil, err
	}
	builder.DiscountIds = discountIds
	// 导购代客下单，不能使用任何优惠券
	if req.AddedBy == "proxyOrder" && req.ProxyOrderId == "" {
		builder.DiscountIds = []string{}
	}

	err = calcCampaignDiscount(ctx, order, builder)
	if err != nil {
		return nil, err
	}

	// 计算订单总金额
	calcOrderTotalAmount(order)

	member, err := builder.GetMember(ctx)
	if err != nil {
		return nil, errors.NewNotExistsError("memberId")
	}
	err = preCalcMemberDiscount(ctx, order, builder)
	if err != nil {
		return nil, err
	}
	err = calcOrderDiscount(ctx, builder, order, member, []string{order_model.COUPON_TYPE_EXCHANGE}, false)
	if err != nil {
		return nil, err
	}
	err = calcMemberDiscount(ctx, order, builder)
	if err != nil {
		return nil, err
	}
	err = calcOrderDiscount(ctx, builder, order, member, []string{order_model.COUPON_TYPE_DISCOUNT, order_model.COUPON_TYPE_CASH}, false)
	if err != nil {
		return nil, err
	}
	// 计算订单和商品的支付金额（未加配送费）
	calcPayAmountAndScore(ctx, order, builder, member)

	err = buildCampaigns(ctx, order, builder, true)
	if err != nil {
		return nil, err
	}
	err = calcOrderDiscount(ctx, builder, order, member, []string{order_model.COUPON_TYPE_PRESENT}, false)
	if err != nil {
		return nil, err
	}

	products, err := formatProductDetails(ctx, order, invalidProducts, invalidProductStatusMap, nil, builder)
	if err != nil {
		return nil, err
	}
	resp := &ec.CalculateOrderAmountResponse{}
	copier.Instance(nil).From(order).CopyTo(resp)
	resp.Products = products
	return resp, nil
}

func formatPurchaseRequest(req *ec_order.ListCartGroupsRequest, campaigns []*ec_order.CartCampaign) *ec.PurchaseRequest {
	requestCampaigns := formatRequestCampaigns(campaigns)
	return &ec.PurchaseRequest{
		MemberId:  req.MemberId,
		StoreId:   req.StoreId,
		Campaigns: requestCampaigns,
	}
}

func formatCampaignsTips(ctx context.Context, campaigns []*ec_order.CartCampaign, calcOrderAmountResponse *ec.CalculateOrderAmountResponse, builder *OrderBuilder) error {
	for _, c := range CartCampaigns {
		err := c.formatCampaignTips(ctx, campaigns, calcOrderAmountResponse, builder)
		if err != nil {
			return err
		}
	}
	return nil
}

func formatCartGroupsResponse(ctx context.Context, invalidCartCampaigns, validCartCampaigns []*ec_order.CartCampaign, calcOrderAmountResponse *ec.CalculateOrderAmountResponse, store *ec_store.StoreDetail, member *pb_member.MemberDetailResponse) (*ec_order.ListCartGroupsResponse, error) {
	hasCoupon, err := haveStoreCoupon(ctx, store.Id, member)
	if err != nil {
		return nil, err
	}

	var (
		totalAmount int64
		payAmount   int64
		discounts   []*ec.DiscountInfo
	)
	if calcOrderAmountResponse != nil {
		totalAmount = int64(calcOrderAmountResponse.TotalAmount)
		payAmount = int64(calcOrderAmountResponse.PayAmount)
		discounts = calcOrderAmountResponse.Discounts
		discountAdded := make(map[string]bool)
		for i, c := range validCartCampaigns {
			for j, p := range c.Products {
				for _, orderProduct := range calcOrderAmountResponse.Products {
					// 返回参与满减赠活动的商品数量
					if orderProduct.Sku == p.Specs.Sku {
						validCartCampaigns[i].Products[j].PresentDiscountCount = orderProduct.PresentDiscountCount
					}
					// 单个商品参与两个活动时会存在两个 validCartCampaigns 包含这个商品，这里判断一下 validCartCampaigns 中的商品折扣是否已经添加过
					added := discountAdded[validCartCampaigns[i].Products[j].Specs.Sku]
					if orderProduct.Sku == p.Specs.Sku && !added {
						validCartCampaigns[i].Products[j].Discounts = append(validCartCampaigns[i].Products[j].Discounts, orderProduct.Discounts...)
						discountAdded[validCartCampaigns[i].Products[j].Specs.Sku] = true
					}
				}
			}
		}
	}

	// 处理商品活动叠加的情况
	validCartCampaigns = handleCombinedCampaign(validCartCampaigns)
	filterProductCampaigns(validCartCampaigns)

	return &ec_order.ListCartGroupsResponse{
		ValidGroups: []*ec_order.CartGroup{
			{
				GroupId:   store.Id,
				GroupName: store.Name,
				GroupType: "store",
				Campaigns: validCartCampaigns,
				HasCoupon: hasCoupon,
			},
		},
		InvalidGroups: []*ec_order.CartGroup{
			{
				GroupId:   store.Id,
				GroupName: store.Name,
				GroupType: "store",
				Campaigns: invalidCartCampaigns,
				HasCoupon: hasCoupon,
			},
		},
		TotalAmount: totalAmount,
		PayAmount:   payAmount,
		Discounts:   discounts,
	}, nil
}

func handleCombinedCampaign(cartCampaigns []*ec_order.CartCampaign) []*ec_order.CartCampaign {
	// 是否有限时折扣活动 cartCampaign。限时折扣由于限购无法购买商品，此时 cartCampaigns 中只有买赠活动，没有限时折扣活动
	haveDiscountCartCampaign := false
	for _, cartCampaign := range cartCampaigns {
		if cartCampaign.Type == order_model.CAMPAIGN_TYPE_DISCOUNT {
			haveDiscountCartCampaign = true
		}
	}
	// 同时参与限时折扣和满减赠的购物车中展示限时折扣
	campaignTypes := []string{order_model.CAMPAIGN_TYPE_DISCOUNT, order_model.CAMPAIGN_TYPE_PRESENT}
	for i, cartCampaign := range cartCampaigns {
		if cartCampaign.Type == order_model.CAMPAIGN_TYPE_DISCOUNT {
			for j, cartProduct := range cartCampaign.Products {
				// 商品同时参与两个活动时加上活动标签
				if util.StrArrayDeepEqual(getProductJoinedCampaignTypes(cartProduct), campaignTypes) {
					cartCampaign.Products[j].Labels = append(cartCampaign.Products[j].Labels, cartCampaign.Label)
				}
			}
		}
		if cartCampaign.Type == order_model.CAMPAIGN_TYPE_PRESENT {
			filteredProducts := []*ec_order.CartProduct{}
			for j, cartProduct := range cartCampaign.Products {
				// 判断商品是否同时参与了限时折扣和满减赠
				productJoinedCampaignTypes := getProductJoinedCampaignTypes(cartProduct)
				if util.StrArrayDeepEqual(productJoinedCampaignTypes, campaignTypes) {
					cartCampaign.Products[j].Labels = append(cartCampaign.Products[j].Labels, cartCampaign.Label)
					continue
				}
				// 若商品上存在限时折扣和满减赠，参与限时折扣后不满足满减赠，此时也需要过滤掉满减活动分组中的商品
				productCampaignTypes := []string{}
				for _, c := range cartProduct.Campaigns {
					productCampaignTypes = append(productCampaignTypes, c.Type)
				}
				// 限时折扣由于限购无法购买商品，cartCampaigns 中只有买赠活动，没有限时折扣活动，这种情况下不能过滤掉买赠活动
				if haveDiscountCartCampaign && util.StrArrayDeepEqual(productCampaignTypes, campaignTypes) && !util.StrArrayDeepEqual(productJoinedCampaignTypes, campaignTypes) {
					continue
				}
				filteredProducts = append(filteredProducts, cartCampaign.Products[j])
			}
			cartCampaigns[i].Products = filteredProducts
		}
	}
	filteredCampaigns := []*ec_order.CartCampaign{}
	for i, cartCampaign := range cartCampaigns {
		if cartCampaign.Type == order_model.CAMPAIGN_TYPE_PRESENT && len(cartCampaign.Products) == 0 {
			continue
		}
		filteredCampaigns = append(filteredCampaigns, cartCampaigns[i])
	}
	return filteredCampaigns
}

// 获取商品上时间参与了计算的活动
func getProductJoinedCampaignTypes(cartProduct *ec_order.CartProduct) []string {
	productCampaignTypes := []string{}
	for _, d := range cartProduct.Discounts {
		if d.Type == order_model.CAMPAIGN_TYPE_DISCOUNT {
			productCampaignTypes = append(productCampaignTypes, d.Type)
		}
		if d.CampaignType == order_model.CAMPAIGN_TYPE_PRESENT {
			productCampaignTypes = append(productCampaignTypes, d.CampaignType)
		}
	}
	return productCampaignTypes
}

func sortCartCampaigns(cartCampaigns []*ec_order.CartCampaign, timeMap map[interface{}]interface{}) []*ec_order.CartCampaign {
	greatestTimeMap := make(map[string]time.Time, len(cartCampaigns))
	for _, cartCampaign := range cartCampaigns {
		greatestTime := timeMap[bson.ObjectIdHex(cartCampaign.Products[0].Id)].(time.Time)
		for _, p := range cartCampaign.Products {
			tempTime := timeMap[bson.ObjectIdHex(p.Id)].(time.Time)
			if tempTime.After(greatestTime) {
				greatestTime = tempTime
			}
		}
		key := generateSortKey(cartCampaign.Products)
		greatestTimeMap[key] = greatestTime
	}
	sort.Slice(cartCampaigns, func(i, j int) bool {
		return greatestTimeMap[generateSortKey(cartCampaigns[i].Products)].After(greatestTimeMap[generateSortKey(cartCampaigns[j].Products)])
	})
	return cartCampaigns
}

func generateSortKey(products []*ec_order.CartProduct) (key string) {
	for _, p := range products {
		key = fmt.Sprintf("%s:%s", key, p.Id)
	}
	return
}

func checkHasCoupon(ctx context.Context, validCartCampaigns []*ec_order.CartCampaign, storeId string, member *pb_member.MemberDetailResponse) []*ec_order.CartCampaign {
	// 黑名单不能使用优惠券
	if member.BlockedStatus == 2 {
		return validCartCampaigns
	}

	productIds := []string{}
	for _, carCampaign := range validCartCampaigns {
		tempProductIds := core_util.ToStringArray(core_util.ExtractArrayField("OriginalProductId", carCampaign.Products))
		productIds = append(productIds, util.StrArrayDiff(tempProductIds, productIds)...)
	}
	// ExistProductCoupon 接口中 productIds required
	if len(productIds) == 0 {
		return validCartCampaigns
	}
	resp, err := mairpc.RPCProxy.Call(
		getCouponServiceClient,
		"CouponService.ExistProductCoupon",
		ctx,
		&coupon.ExistProductCouponRequest{
			ProductIds: productIds,
			ShowAll:    false,
			StoreId:    storeId,
		},
	)
	if err != nil {
		log.Warn(ctx, "Failed to call exist product coupon", log.Fields{
			"errMsg":     err.Error(),
			"productIds": productIds,
			"storeId":    storeId,
		})
		return validCartCampaigns
	}
	existCouponProductIds := resp.(*coupon.ExistProductCouponResponse).Ids
	for ci, cartCampaign := range validCartCampaigns {
		for pi, product := range cartCampaign.Products {
			if core_util.StrInArray(product.OriginalProductId, &existCouponProductIds) {
				validCartCampaigns[ci].Products[pi].HasCoupon = true
			}
		}
	}
	return validCartCampaigns
}

func hasCheckedProduct(cartCampaigns []*ec_order.CartCampaign) bool {
	for _, c := range cartCampaigns {
		for _, p := range c.Products {
			if p.Checked && p.Status == ec_product_model.SHELVE_STATUS_SHELVED {
				return true
			}
		}
	}
	return false
}

func formatPresents(campaigns []*ec_order.CartCampaign, calcOrderAmountResponse *ec.CalculateOrderAmountResponse) []*ec_order.CartCampaign {
	if calcOrderAmountResponse == nil {
		return campaigns
	}

	for _, p := range calcOrderAmountResponse.Products {
	OUTER:
		for _, c := range p.Campaigns {
			if core_util.StrInArray(c.Type, &[]string{order_model.CAMPAIGN_TYPE_PRESENT, order_model.CAMPAIGN_TYPE_PRESENT_COUPON}) && len(c.ProductIds) != 0 {
				tempCartProduct := &ec_order.CartProduct{}
				copier.Instance(copier.NewOption().SetOverwriteOriginalCopyField(true)).RegisterResetDiffField([]copier.DiffFieldPair{
					{
						Origin:  "Name",
						Targets: []string{"ProductName"},
					},
					{
						Origin:  "Count",
						Targets: []string{"Total"},
					},
					{
						Origin:  "OriginPrice",
						Targets: []string{"OriginalPrice"},
					},
					{
						Origin:  "Id",
						Targets: []string{"ProductId"},
					},
					{
						Origin:  "Type",
						Targets: []string{"ProductType"},
					},
				}).From(p).CopyTo(tempCartProduct)
				tempCartProduct.Specs = &ec_order.SkuSpec{Sku: p.Sku, Properties: p.Properties}
				tempCartProduct.IsPresent = true
				tempCartProduct.Checked = true
				if tempCartProduct.ProductType == "" {
					tempCartProduct.ProductType = "product"
				}
				if c.Type == order_model.CAMPAIGN_TYPE_PRESENT {
					// 满减活动赠品
					tempCartProduct.PresentType = "campaign"
					for i, cartCampaign := range campaigns {
						if cartCampaign.Id != c.Id {
							continue
						}
						campaigns[i].Products = append(campaigns[i].Products, tempCartProduct)
					}
				} else {
					// 买赠券赠品
					tempCartProduct.PresentType = "coupon"
					for i := len(campaigns) - 1; i >= 0; i-- {
						for _, p := range campaigns[i].Products {
							if p.Checked {
								campaigns[i].Products = append(campaigns[i].Products, tempCartProduct)
								continue OUTER
							}
						}
					}
				}
			}
		}
	}
	return campaigns
}

func uncheckInvalidProducts(ctx context.Context, invalidCartCampaigns []*ec_order.CartCampaign, cartProducts []order_model.CartProduct) {
	cartProductsMap := core_util.MakeMapper("Id", cartProducts)
	for _, campaign := range invalidCartCampaigns {
		for _, p := range campaign.Products {
			if p.Status == ec_product_model.SHELVE_STATUS_SHELVED {
				continue
			}
			if cartProduct, ok := cartProductsMap[bson.ObjectIdHex(p.Id)].(order_model.CartProduct); ok {
				err := cartProduct.UncheckCartProduct(ctx)
				if err != nil {
					log.Warn(ctx, "Failed to uncheck cartProduct", log.Fields{
						"id":     cartProduct.Id.Hex(),
						"errMsg": err.Error(),
					})
				}
			}
		}
	}
}

// 过滤未参加活动的商品上的 campaigns
func filterProductCampaigns(cartCampaigns []*ec_order.CartCampaign) {
	for _, cartCampaign := range cartCampaigns {
		if cartCampaign.Id != "" {
			continue
		}
		for _, p := range cartCampaign.Products {
			p.Campaigns = []*ec_order.BriefCampaign{}
		}
	}
}

func haveStoreCoupon(ctx context.Context, storeId string, member *pb_member.MemberDetailResponse) (bool, error) {
	// 黑名单不能使用优惠券
	if member.BlockedStatus == 2 {
		return false, nil
	}
	req := &request.DetailRequest{
		Id: storeId,
	}

	resp, err := mairpc.RPCProxy.Call(
		getCouponServiceClient,
		"CouponService.ExistsCouponForStore",
		ctx,
		req,
	)

	if err != nil {
		return false, err
	}
	hasCoupon := resp.(*response.BoolResponse).Value
	return hasCoupon, nil
}
