package order

import (
	"sort"
	"time"

	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	"mairpc/proto/common/types"
	ec_order "mairpc/proto/ec/order"
	"mairpc/service/ec/client"
	order_model "mairpc/service/ec/model/order"
	"mairpc/service/share/util"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

const (
	INVOICE_RECORD_STATUS_ISSUED            = "issued"           // 已开票
	INVOICE_RECORD_STATUS_PARTIALLY_ISSUED  = "partiallyIssued"  // 部分开票成功
	INVOICE_RECORD_STATUS_DELETED           = "deleted"          // 已退票
	INVOICE_RECORD_STATUS_PARTIALLY_DELETED = "partiallyDeleted" // 部分退票
	INVOICE_RECORD_STATUS_FAILED            = "failed"           // 开票失败
	INVOICE_RECORD_STATUS_PENDING           = "pending"          // 待开票
)

func (OrderService) ListInvoiceRecords(ctx context.Context, req *ec_order.ListInvoiceRecordsRequest) (*ec_order.ListInvoiceRecordsResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	condition := genListInvoiceRecordsCondition(ctx, req)
	page, pageSize := util.ParsePagingCondition(req.ListCondition)
	orderBy := []string{"-createdAt"}
	if req.ListCondition != nil && len(req.ListCondition.OrderBy) != 0 {
		orderBy = req.ListCondition.OrderBy
	}
	invoices, _ := order_model.CInvoice.GetAllByPagination(ctx, condition, page, pageSize, orderBy)
	var (
		orderIdToBlueInvoicesMap = make(map[string][]order_model.Invoice)
		orderIdToRedInvoicesMap  = make(map[string][]order_model.Invoice)
		resp                     = []*ec_order.InvoiceRecord{}
	)
	for _, invoice := range invoices {
		if invoice.IsRed {
			orderIdToRedInvoicesMap[invoice.OrderId.Hex()] = append(orderIdToRedInvoicesMap[invoice.OrderId.Hex()], invoice)
			continue
		}
		orderIdToBlueInvoicesMap[invoice.OrderId.Hex()] = append(orderIdToBlueInvoicesMap[invoice.OrderId.Hex()], invoice)
	}
	for _, blueInvoices := range orderIdToBlueInvoicesMap {
		var totalAmount uint64
		for _, invoice := range blueInvoices {
			for _, product := range invoice.InvoiceItems.InvoiceProducts {
				totalAmount += product.OrderProduct.PayAmount
			}
			totalAmount += invoice.InvoiceItems.DeliveryFee
		}
		orderIds := []string{blueInvoices[0].OrderId.Hex()}
		if len(blueInvoices[0].MergedOrderIds) > 0 {
			for _, orderId := range blueInvoices[0].MergedOrderIds {
				orderIds = append(orderIds, orderId.Hex())
			}
		}
		if totalAmount == 0 {
			// 实际的开票金额会在开票时进行计算，此处金额仅用于开票前相关记录的显示
			totalAmount = getInvoiceRecordsAmount(ctx, orderIds)
		}
		resp = append(resp, &ec_order.InvoiceRecord{
			Name:           blueInvoices[0].Name,
			InvoiceType:    blueInvoices[0].InvoiceType,
			InvoiceSetting: blueInvoices[0].InvoiceSetting,
			CreatedAt:      blueInvoices[0].CreatedAt.Format("2006-01-02 15:04:05"),
			Amount:         totalAmount,
			Status:         getInvoiceRecordsStatus(blueInvoices, orderIdToRedInvoicesMap),
			OrderIds:       orderIds,
		})
	}
	sort.Slice(resp, func(i, j int) bool {
		t1, _ := time.Parse("2006-01-02 15:04:05", resp[i].CreatedAt)
		t2, _ := time.Parse("2006-01-02 15:04:05", resp[j].CreatedAt)
		if req.ListCondition != nil && len(req.ListCondition.OrderBy) != 0 && util.StrInArray("createdAt", &req.ListCondition.OrderBy) {
			return t2.After(t1)
		}
		return t1.After(t2)
	})
	return &ec_order.ListInvoiceRecordsResponse{
		Total: cast.ToUint64(len(resp)),
		Items: resp,
	}, nil
}

func genListInvoiceRecordsCondition(ctx context.Context, req *ec_order.ListInvoiceRecordsRequest) bson.M {
	cond := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	if req.MemberId != "" {
		cond["memberId"] = util.ToMongoId(req.MemberId)
	}
	// 导购查看代下单订单发票时，memberId 是导购自己的，不能放到查询条件中
	if req.OrderType == "proxyOrder" {
		delete(cond, "memberId")
	}
	if req.CreatedAt != nil {
		cond["createdAt"] = util.ParseDateRange(req.CreatedAt)
	}
	return cond
}

func getInvoiceRecordsStatus(blueInvoices []order_model.Invoice, orderIdToRedInvoicesMap map[string][]order_model.Invoice) string {
	var status string
	statusList := core_util.ExtractArrayFieldV2("Status", "", blueInvoices)
	if core_util.StrInArray(order_model.INVOICE_STATUS_ISSUED, &statusList) {
		status = INVOICE_RECORD_STATUS_ISSUED
		for _, invoice := range blueInvoices {
			if invoice.Status != order_model.INVOICE_STATUS_ISSUED {
				status = INVOICE_RECORD_STATUS_PARTIALLY_ISSUED
				break
			}
		}
		orderIds := blueInvoices[0].MergedOrderIds
		orderIds = append(orderIds, blueInvoices[0].OrderId)
		for _, orderId := range orderIds {
			if orderIdToRedInvoicesMap[orderId.Hex()] != nil {
				status = INVOICE_RECORD_STATUS_PARTIALLY_ISSUED
				break
			}
		}
	} else if core_util.StrInArray(order_model.INVOICE_STATUS_DELETED, &statusList) {
		status = INVOICE_RECORD_STATUS_DELETED
		for _, invoice := range blueInvoices {
			if invoice.Status != order_model.INVOICE_STATUS_DELETED {
				status = INVOICE_RECORD_STATUS_PARTIALLY_DELETED
				break
			}
		}
	} else if core_util.StrInArray(order_model.INVOICE_STATUS_FAILED, &statusList) {
		status = INVOICE_RECORD_STATUS_FAILED
		if core_util.StrInArray("", &statusList) ||
			core_util.StrInArray(order_model.INVOICE_STATUS_PENDING, &statusList) ||
			core_util.StrInArray(order_model.INVOICE_STATUS_INVOICING, &statusList) ||
			core_util.StrInArray(order_model.INVOICE_STATUS_REBILLING, &statusList) {
			status = INVOICE_RECORD_STATUS_PENDING
		}
	} else {
		status = INVOICE_RECORD_STATUS_PENDING
		isAllInvoicesClosed := true
		for _, invoice := range blueInvoices {
			if invoice.Status != order_model.INVOICE_STATUS_CLOSED {
				isAllInvoicesClosed = false
				break
			}
		}
		if isAllInvoicesClosed {
			status = INVOICE_RECORD_STATUS_DELETED
		}
	}
	return status
}

func getInvoiceRecordsAmount(
	ctx context.Context,
	orderIds []string,
) uint64 {
	var (
		productIds             []string
		productIdToProductsMap = make(map[string][]*order_model.OrderProduct)
		orderProducts          []*order_model.OrderProduct
		amount                 uint64
	)
	orders, err := order_model.COrder.GetByIds(ctx, util.ToMongoIds(orderIds))
	if err != nil {
		log.Warn(ctx, "Failed to get orders by orderIds", log.Fields{
			"errMsg":   err.Error(),
			"orderIds": orderIds,
		})
		return 0
	}
	for _, order := range orders {
		for _, product := range order.Products {
			productIdToProductsMap[product.Id.Hex()] = append(productIdToProductsMap[product.Id.Hex()], &product)
			if core_util.StrInArray(product.Id.Hex(), &productIds) {
				continue
			}
			productIds = append(productIds, product.Id.Hex())
		}
	}
	productsResp, err := client.ProductService.ListProducts(ctx, &ec.ListProductsRequest{
		EcProductIds: productIds,
		ExistsInvoiceTemplateId: &types.BoolValue{
			Value: true,
		},
		ContainDeleted: true,
		ListCondition:  &request.ListCondition{Page: 1, PerPage: uint32(len(productIds))},
	})
	if err != nil {
		log.Warn(ctx, "Failed to get products by productIds", log.Fields{
			"errMsg":     err.Error(),
			"productIds": productIds,
		})
		return 0
	}
	for _, p := range productsResp.Items {
		if p.Ec == nil || p.Ec.InvoiceTemplateId == "" {
			continue
		}
		orderProducts = append(orderProducts, productIdToProductsMap[p.Ec.Id]...)
	}
	for _, op := range orderProducts {
		orderProduct := op
		if orderProduct.Type != "product" {
			if orderProduct.Total == uint64(orderProduct.RefundTotal) {
				continue
			}
			amount += (orderProduct.PayAmount / orderProduct.Total) * (orderProduct.Total - uint64(orderProduct.RefundTotal))
		} else {
			orderRefunds, _ := client.OrderService.ListOrderRefunds(ctx, &ec_order.ListOrderRefundsRequest{
				OrderIds: orderIds,
				Status:   []string{"approved", "returned", "pending", "refunding", "failed", "refunded"},
			})
			refundProduct := getRefundProduct(orderRefunds, orderProduct.OutTradeId.Hex())
			if refundProduct != nil {
				if refundProduct.RefundAmount == orderProduct.PayAmount {
					continue
				}
				amount += orderProduct.PayAmount - refundProduct.RefundAmount
			} else {
				amount += orderProduct.PayAmount
			}
		}
	}
	amount += getDeliveryFee(orders)
	return amount
}
