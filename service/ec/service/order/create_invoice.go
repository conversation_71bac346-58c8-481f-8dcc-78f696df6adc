package order

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"mairpc/core/component"
	core_errors "mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/proto/common/types"
	ec_order "mairpc/proto/ec/order"
	pb_ec_order "mairpc/proto/ec/order"
	ec_product "mairpc/proto/ec/product"
	"mairpc/proto/ec/setting"
	"mairpc/service/ec/client"
	ec_model_notification "mairpc/service/ec/model/notification"
	"mairpc/service/ec/model/order"
	order_model "mairpc/service/ec/model/order"
	share_invoice "mairpc/service/share/component/invoice"
	"mairpc/service/share/util"
)

const (
	EC_CREATE_INVOICE_LOCK_KEY = "%s:ec:create-invoice:%s" // ${accountId}:ec:create-invoice:${orderId}
)

func (OrderService) CreateInvoice(ctx context.Context, req *ec_order.CreateInvoiceRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	key := fmt.Sprintf(EC_CREATE_INVOICE_LOCK_KEY, util.GetAccountId(ctx), req.OrderId)
	ok, _ := extension.RedisClient.SetNX(key, "true", 5)
	if !ok {
		return nil, core_errors.NewTooManyRequestsError("orderId")
	}
	defer extension.RedisClient.Del(key)
	invoiceSetting, err := client.SettingService.GetInvoiceSetting(ctx, &request.EmptyRequest{})
	if err != nil {
		return nil, err
	}
	if invoiceSetting.InvoiceSetting == order_model.INVOICE_SETTING_UNSUPPORTED {
		return nil, core_errors.NewNotEnabledError("invoice")
	}
	if req.Invoice != nil {
		req.Invoice.Name = strings.ReplaceAll(strings.TrimSpace(req.Invoice.Name), " ", "")
		req.Invoice.TaxID = strings.ReplaceAll(strings.TrimSpace(req.Invoice.TaxID), " ", "")
		if req.Invoice.InvoiceType == "" {
			req.Invoice.InvoiceType = "general"
		}
	}
	order, err := GenInvoiceOrder(ctx, req.OrderType, util.ToMongoId(req.OrderId))
	if err != nil {
		return nil, err
	}
	// 代客下单时，导购操作客户发票信息，memberId 需要设置为客户的 memberId
	if req.OrderType == "proxyOrder" {
		req.OrderType = "order"
		req.MemberId = order.GetMemberId()
	}
	if req.IsRed {
		if invoiceSetting.Provider != share_invoice.PROVIDER_BAIWANG || invoiceSetting.InvoiceSetting != order_model.INVOICE_SETTING_ONLINE || !invoiceSetting.IsReversible {
			return nil, errors.New("red invoice is not supported")
		}
		if err := handleRedInvoices(ctx, req); err != nil {
			return nil, err
		}
		return &response.EmptyResponse{}, nil
	}
	existsCondition := order_model.Common.GenDefaultCondition(ctx)
	existsCondition["orderId"] = util.ToMongoId(req.OrderId)
	if order_model.Common.Exist(ctx, order_model.C_INVOICE, existsCondition) {
		return nil, core_errors.NewAlreadyExistsError("orderId")
	}
	invoice := &order_model.Invoice{}
	copier.Instance(nil).From(req.Invoice).CopyTo(invoice)
	if err := invoice.Valid(); err != nil {
		return nil, err
	}
	invoice.InvoiceSetting = invoiceSetting.InvoiceSetting
	if err := order.CanCreate(req, invoiceSetting); err != nil {
		return nil, err
	}
	invoice.MemberId = bson.ObjectIdHex(req.MemberId)
	invoice.OrderId = bson.ObjectIdHex(req.OrderId)
	order.FormatInvoice(invoice)
	operator := fmt.Sprintf("memberId:%s", req.MemberId)
	member, err := GetMember(ctx, req.MemberId)
	if err != nil {
		return nil, err
	}
	extra := bson.M{
		"memberName": member.Name,
	}
	invoice.GenerateHistory(operator, extra)
	if err := invoice.Insert(ctx); err != nil {
		return nil, err
	}
	order.UpdateInvoiceStatus(ctx, invoice)

	// 设置线下开票时店长端需要有展示发票通知
	if req.OrderType == "order" && invoiceSetting.InvoiceSetting == order_model.INVOICE_SETTING_OFFLINE {
		createNotification(ctx, req.OrderId)
	}

	return &response.EmptyResponse{}, nil
}

func createNotification(ctx context.Context, orderId string) {
	o, _ := order_model.COrder.GetById(ctx, bson.ObjectIdHex(orderId))
	// 脉盟商城/连锁零售商城代销订单不需要创建通知
	if o.IsConsignmentOrder() {
		return
	}
	notification := ec_model_notification.Notification{
		StoreId:     o.StoreId,
		OrderNumber: o.Number,
		OrderId:     o.Id,
		MemberId:    o.MemberId,
	}
	notification.Type = ec_model_notification.NOTIFICATION_TYPE_INVOICE
	notification.Content = ec_model_notification.NOTIFICATION_CONTENT_INVOICE
	err := notification.Create(ctx)
	if err != nil {
		log.Warn(ctx, "Failed to create invoice notification", log.Fields{
			"orderNumber":  o.Number,
			"errorMessage": err.Error(),
		})
	}
}

func handleRedInvoices(ctx context.Context, req *ec_order.CreateInvoiceRequest) error {
	orderRefunds, _ := client.OrderService.ListOrderRefunds(ctx, &ec_order.ListOrderRefundsRequest{
		OrderIds: []string{req.OrderId},
		Status:   []string{"approved", "returned", "pending", "refunding", "failed", "refunded"},
	})
	if orderRefunds == nil || len(orderRefunds.Items) == 0 {
		return fmt.Errorf("Failed to get refund orders by orderId: %s", req.OrderId)
	}
	order, err := order.COrder.GetById(ctx, bson.ObjectIdHex(req.OrderId))
	if err != nil {
		return err
	}
	listBlueInvoicesResp, _ := client.OrderService.ListInvoices(ctx, &ec_order.ListInvoicesRequest{
		OrderIds: []string{req.OrderId},
		Status:   []string{order_model.INVOICE_STATUS_INVOICING, order_model.INVOICE_STATUS_ISSUED, order_model.INVOICE_STATUS_CLOSED},
		IsRed:    false,
	})
	if listBlueInvoicesResp == nil || len(listBlueInvoicesResp.Items) == 0 {
		return errors.New("Failed to get issued blue invoices")
	}
	listRedInvoicesResp, _ := client.OrderService.ListInvoices(ctx, &ec_order.ListInvoicesRequest{
		OrderIds: []string{req.OrderId},
		IsRed:    true,
	})
	defer func() {
		component.GO(ctx, func(ctx context.Context) {
			// 当蓝票所有商品都创建红票后，将原蓝票状态标记为“已退票”
			deleteBlueInvoices(ctx, order, listBlueInvoicesResp)
		})
	}()
	var (
		issuedOrderRefundIds []string
		unissuedOrderRefunds []ec_order.OrderRefundDetail
	)
	if listRedInvoicesResp != nil && len(listRedInvoicesResp.Items) != 0 {
		for _, redInvoice := range listRedInvoicesResp.Items {
			var extra map[string]string
			if err := json.Unmarshal([]byte(redInvoice.Extra), &extra); err != nil {
				log.Warn(ctx, "Failed to unmarshal redInvoice.Extra", log.Fields{
					"id":    redInvoice.Id,
					"extra": redInvoice.Extra,
				})
				return err
			}
			if util.StrInArray(extra["orderRefundId"], &issuedOrderRefundIds) {
				continue
			}
			issuedOrderRefundIds = append(issuedOrderRefundIds, extra["orderRefundId"])
		}
	}
	// 幂等，防止重复创建红票
	for _, orderRefund := range orderRefunds.Items {
		if len(issuedOrderRefundIds) > 0 && util.StrInArray(orderRefund.Id, &issuedOrderRefundIds) {
			continue
		}
		unissuedOrderRefunds = append(unissuedOrderRefunds, *orderRefund)
	}
	if len(unissuedOrderRefunds) == 0 {
		return nil
	}
	for _, unissuedOrderRefund := range unissuedOrderRefunds {
		commonRedInvoice := getCommonRedInvoice(ctx, req, order, unissuedOrderRefund)
		if commonRedInvoice == nil {
			return fmt.Errorf("Failed to create red invoices for order refund: %s", unissuedOrderRefund.Id)
		}
		if err := createRedInvoices(ctx, commonRedInvoice, unissuedOrderRefund, listBlueInvoicesResp.Items); err != nil {
			return err
		}
	}
	return nil
}

func deleteBlueInvoices(ctx context.Context, order order_model.Order, listBlueInvoicesResp *pb_ec_order.ListInvoicesResponse) error {
	for _, blueInvoice := range listBlueInvoicesResp.Items {
		if !isBlueInvoiceDeleted(ctx, blueInvoice) {
			continue
		}
		_, err := client.OrderService.UpdateInvoice(ctx, &pb_ec_order.UpdateInvoiceRequest{
			Id:     blueInvoice.Id,
			Status: order_model.INVOICE_STATUS_DELETED,
		})
		if err != nil {
			log.Warn(ctx, "Failed to update invoice status", log.Fields{
				"invoiceId": blueInvoice.Id,
				"errMsg":    err.Error(),
			})
			return err
		}
		if order.Status == order_model.ORDER_STATUS_CANCELED {
			order.UpdateInvoiceStatus(order_model.INVOICE_STATUS_DELETED)
			if err := order.UpdateInvoice(ctx); err != nil {
				log.Warn(ctx, "Failed to update order invoice status", log.Fields{
					"orderId": order.Id,
					"errMsg":  err.Error(),
				})
				return err
			}
		}
	}
	return nil
}

// 蓝票是否已全部红冲
func isBlueInvoiceDeleted(ctx context.Context, blueInvoice *pb_ec_order.InvoiceResponse) bool {
	var (
		blueInvoiceTotalPayAmount, redInvoicesTotalRefundAmount uint64
		blueInvoiceProductIds                                   []string
	)
	blueInvoiceTotalPayAmount += blueInvoice.InvoiceItems.DeliveryFee
	for _, blueInvoiceProduct := range blueInvoice.InvoiceItems.InvoiceProducts {
		blueInvoiceTotalPayAmount += blueInvoiceProduct.OrderProduct.PayAmount
		blueInvoiceProductIds = append(blueInvoiceProductIds, blueInvoiceProduct.OrderProduct.Id)
	}
	redInvoicesResp, _ := client.OrderService.ListInvoices(ctx, &ec_order.ListInvoicesRequest{
		BlueInvoiceId: blueInvoice.Id,
		IsRed:         true,
	})
	if redInvoicesResp == nil || len(redInvoicesResp.Items) == 0 {
		return false
	}
	statusList := core_util.ExtractArrayFieldV2("Status", "", blueInvoice.Histories)
	for _, redInvoice := range redInvoicesResp.Items {
		// 存在蓝票部分红冲时冲掉运费后又重开的情况，此时新蓝票运费为 0，不再计入红冲运费时的运费金额
		if blueInvoice.InvoiceItems.DeliveryFee > 0 {
			redInvoicesTotalRefundAmount += redInvoice.InvoiceItems.DeliveryFee
		}
		for _, redInvoiceProduct := range redInvoice.InvoiceItems.InvoiceProducts {
			// 如果发票部分红冲后，又重新开票，新蓝票金额会比原本的蓝票金额小，再次退款时，历史红冲商品金额不再计入
			if util.StrInArray(order_model.INVOICE_STATUS_REVERSING, &statusList) && !core_util.StrInArray(redInvoiceProduct.OrderProduct.Id, &blueInvoiceProductIds) {
				continue
			}
			redInvoicesTotalRefundAmount += redInvoiceProduct.OrderProduct.PayAmount
		}
	}
	if redInvoicesTotalRefundAmount == blueInvoiceTotalPayAmount {
		return true
	}
	return false
}

func getCommonRedInvoice(
	ctx context.Context,
	req *ec_order.CreateInvoiceRequest,
	order order_model.Order,
	unissuedOrderRefund pb_ec_order.OrderRefundDetail,
) *order_model.Invoice {
	commonRedInvoice := &order_model.Invoice{}
	copier.Instance(nil).From(req.Invoice).CopyTo(commonRedInvoice)
	if err := commonRedInvoice.Valid(); err != nil {
		log.Warn(ctx, "Invalid red invoice", log.Fields{
			"errMsg": err.Error(),
		})
		return nil
	}
	commonRedInvoice.IsRed = true
	commonRedInvoice.MemberId = bson.ObjectIdHex(req.MemberId)
	commonRedInvoice.OrderId = bson.ObjectIdHex(req.OrderId)
	commonRedInvoice.OrderNumber = order.Number
	commonRedInvoice.InvoiceSetting = order_model.INVOICE_SETTING_ONLINE
	commonRedInvoice.OrderType = order_model.INVOICE_ORDER_TYPE_ORDER
	commonRedInvoice.Status = order_model.INVOICE_STATUS_PENDING
	operator := fmt.Sprintf("memberId:%s", req.MemberId)
	member, err := GetMember(ctx, req.MemberId)
	if err != nil {
		log.Warn(ctx, "Failed to get member by memberId", log.Fields{
			"errMsg": err.Error(),
		})
		return nil
	}
	extra := bson.M{
		"memberName": member.Name,
	}
	commonRedInvoice.GenerateHistory(operator, extra)
	existedExtra := make(map[string]string)
	existedExtra["orderRefundId"] = unissuedOrderRefund.Id
	commonExtra, _ := json.Marshal(existedExtra)
	commonRedInvoice.Extra = string(commonExtra)
	return commonRedInvoice
}

func createRedInvoices(
	ctx context.Context,
	commonRedInvoice *order_model.Invoice,
	orderRefund pb_ec_order.OrderRefundDetail,
	blueInvoices []*pb_ec_order.InvoiceResponse,
) error {
	var (
		productIds, invoiceTemplateIds []string
		productIdToTemplateIdMap       = make(map[string]string)
		productIdToProductsMap         = make(map[string][]*ec_order.RefundProduct)
	)
	for _, product := range orderRefund.Products {
		productIdToProductsMap[product.Id] = append(productIdToProductsMap[product.Id], product)
		if util.StrInArray(product.Id, &productIds) {
			continue
		}
		productIds = append(productIds, product.Id)
	}
	productsResp, err := client.ProductService.ListProducts(ctx, &ec.ListProductsRequest{
		EcProductIds: productIds,
		ExistsInvoiceTemplateId: &types.BoolValue{
			Value: true,
		},
		ContainDeleted: true,
		ListCondition:  &request.ListCondition{Page: 1, PerPage: uint32(len(productIds))},
	})
	if err != nil {
		return err
	}
	if productsResp == nil || len(productsResp.Items) == 0 {
		return errors.New("Failed to get products by productIds")
	}
	for _, p := range productsResp.Items {
		if p.Ec.InvoiceTemplateId == "" {
			continue
		}
		invoiceTemplateIds = append(invoiceTemplateIds, p.Ec.InvoiceTemplateId)
		productIdToTemplateIdMap[p.Ec.Id] = p.Ec.InvoiceTemplateId
	}
	if len(invoiceTemplateIds) == 0 {
		return errors.New("invoice templates of products is empty")
	}
	resp, err := client.SettingService.ListInvoiceTemplates(ctx, &setting.ListInvoiceTemplatesRequest{
		Ids:           invoiceTemplateIds,
		ListCondition: &request.ListCondition{Page: 1, PerPage: uint32(len(invoiceTemplateIds))},
	})
	if err != nil || resp == nil || len(resp.Items) == 0 {
		return core_errors.NewInvalidArgumentError("invoiceTemplate")
	}
	invoiceTemplateMap := core_util.MakeMapper("Id", resp.Items)
	for _, invoiceTemplate := range resp.Items {
		// 如果商品开蓝票时拆出多张发票（支持商品合并开票），则红冲时也需要拆票
		if !invoiceTemplate.IsMergeInvoicing {
			continue
		}
		var unusedTemplateIds []string
		for productId := range productIdToProductsMap {
			if util.StrInArray(productIdToTemplateIdMap[productId], &unusedTemplateIds) {
				continue
			}
			unusedTemplateIds = append(unusedTemplateIds, productIdToTemplateIdMap[productId])
		}
		if len(productIdToProductsMap) <= 1 || len(unusedTemplateIds) <= 1 {
			break
		}
		var orderRefundProducts []*pb_ec_order.RefundProduct
		for productId, templateId := range productIdToTemplateIdMap {
			if invoiceTemplate.Id != templateId {
				continue
			}
			products := productIdToProductsMap[productId]
			orderRefundProducts = append(orderRefundProducts, products...)
			delete(productIdToProductsMap, productId)
		}
		invoiceRefundProducts := genRefundInvoiceProducts(ctx, orderRefundProducts, productsResp.Items, invoiceTemplateMap)
		if len(invoiceRefundProducts) == 0 {
			log.Warn(ctx, "Failed to get order refund invoice products", log.Fields{
				"invoiceTemplateId": invoiceTemplate.Id,
				"products":          orderRefundProducts,
			})
			continue
		}
		if err := createRedInvoice(ctx, commonRedInvoice, blueInvoices, invoiceRefundProducts, orderRefund); err != nil {
			return err
		}
	}
	var orderRefundProducts []*pb_ec_order.RefundProduct
	for _, products := range productIdToProductsMap {
		orderRefundProducts = append(orderRefundProducts, products...)
	}
	invoiceRefundProducts := genRefundInvoiceProducts(ctx, orderRefundProducts, productsResp.Items, invoiceTemplateMap)
	if err := createRedInvoice(ctx, commonRedInvoice, blueInvoices, invoiceRefundProducts, orderRefund); err != nil {
		return err
	}
	return nil
}

func createRedInvoice(
	ctx context.Context,
	commonRedInvoice *order_model.Invoice,
	blueInvoices []*pb_ec_order.InvoiceResponse,
	invoiceRefundProducts []share_invoice.InvoiceProduct,
	orderRefund pb_ec_order.OrderRefundDetail,
) error {
	var refundAmount uint64
	for _, invoiceRefundProduct := range invoiceRefundProducts {
		refundAmount += invoiceRefundProduct.OrderProduct.PayAmount
	}
	refundAmount += orderRefund.ExpressFee
	// 如果全部退款商品及运费金额实付都是 0 则不创建红票
	if refundAmount == 0 {
		return nil
	}
	blueInvoicesMap := core_util.MakeMapper("Id", blueInvoices)
	newRedInvoice := &order_model.Invoice{}
	copier.Instance(nil).From(commonRedInvoice).CopyTo(newRedInvoice)
	blueInvoiceId := getBlueInvoiceId(ctx, blueInvoices, invoiceRefundProducts)
	if blueInvoiceId == bson.NilObjectId {
		return errors.New("Failed to get parent blue invoice id")
	}
	newRedInvoice.BlueInvoiceId = blueInvoiceId
	// 如果是对主票红冲，且退款单包含运费，则该红票需要冲掉部分或全部运费
	if orderRefund.ExpressFee > 0 && blueInvoicesMap[blueInvoiceId.Hex()].(*pb_ec_order.InvoiceResponse).InvoiceItems.DeliveryFee > 0 {
		newRedInvoice.InvoiceItems.DeliveryFee = orderRefund.ExpressFee
	}
	copier.Instance(nil).From(invoiceRefundProducts).CopyTo(&newRedInvoice.InvoiceItems.InvoiceProducts)
	newRedInvoice.Id = bson.NewObjectId()
	if err := newRedInvoice.Insert(ctx); err != nil {
		return err
	}
	component.GO(ctx, func(ctx context.Context) {
		updateBlueInvoiceHistories(ctx, blueInvoiceId, order_model.INVOICE_STATUS_PARTIALLY_REVERSING, commonRedInvoice.MemberId.Hex())
	})
	return nil
}

// 订单商品部分退款时维护原蓝票历史状态
func updateBlueInvoiceHistories(ctx context.Context, blueInvoiceId bson.ObjectId, status, memberId string) {
	listBlueInvoicesResp, _ := client.OrderService.ListInvoices(ctx, &ec_order.ListInvoicesRequest{
		Id: blueInvoiceId.Hex(),
	})
	if listBlueInvoicesResp == nil || len(listBlueInvoicesResp.Items) == 0 {
		return
	}
	if isBlueInvoiceDeleted(ctx, listBlueInvoicesResp.Items[0]) {
		return
	}
	condition := order_model.Common.GenDefaultConditionById(ctx, blueInvoiceId)
	operator := fmt.Sprintf("memberId:%s", memberId)
	member, _ := GetMember(ctx, memberId)
	extra := bson.M{
		"memberName": member.Name,
	}
	updater := bson.M{
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
		"$push": bson.M{
			"histories": bson.M{
				"status":    status,
				"operator":  operator,
				"createdAt": time.Now(),
				"extra":     extra,
			},
		},
	}
	if err := extension.DBRepository.UpdateOne(ctx, order_model.C_INVOICE, condition, updater); err != nil {
		log.Warn(ctx, "Failed to update blue invoice histories", log.Fields{
			"blueInvoiceId": blueInvoiceId.Hex(),
			"status":        status,
		})
	}
}

func getBlueInvoiceId(ctx context.Context, blueInvoices []*pb_ec_order.InvoiceResponse, invoiceRefundProducts []share_invoice.InvoiceProduct) bson.ObjectId {
	var (
		productIds                   []string
		blueInvoiceIdToProductIdsMap = make(map[string][]string)
	)
	for _, invoiceProduct := range invoiceRefundProducts {
		productIds = append(productIds, invoiceProduct.OrderProduct.Id)
	}
	for _, blueInvoice := range blueInvoices {
		var blueInvoiceProductIds []string
		for _, blueInvoiceProduct := range blueInvoice.InvoiceItems.InvoiceProducts {
			blueInvoiceProductIds = append(blueInvoiceProductIds, blueInvoiceProduct.OrderProduct.Id)
		}
		blueInvoiceIdToProductIdsMap[blueInvoice.Id] = blueInvoiceProductIds
	}
	for blueInvoiceId, blueInvoiceProductIds := range blueInvoiceIdToProductIdsMap {
		for _, productId := range productIds {
			if util.StrInArray(productId, &blueInvoiceProductIds) {
				return bson.ObjectIdHex(blueInvoiceId)
			}
		}
	}
	return bson.NilObjectId
}

func genRefundInvoiceProducts(
	ctx context.Context,
	orderRefundProducts []*pb_ec_order.RefundProduct,
	products []*ec_product.ProductResponse,
	invoiceTemplateMap map[interface{}]interface{}) []share_invoice.InvoiceProduct {
	invoiceProducts := []share_invoice.InvoiceProduct{}
	for _, op := range orderRefundProducts {
		orderRefundProduct := op
		ecProduct := &ec_product.ProductResponse{}
		for _, p := range products {
			if op.Id != p.Ec.Id {
				continue
			}
			ecProduct = p
		}
		if ecProduct.Ec == nil {
			log.Warn(ctx, "Product not found", log.Fields{
				"ecProductId": op.Id,
			})
			continue
		}
		var pbOrderProduct = &pb_ec_order.OrderProduct{
			Id:        orderRefundProduct.Id,
			Name:      orderRefundProduct.Name,
			Total:     orderRefundProduct.Total,
			Price:     orderRefundProduct.Price,
			PayAmount: orderRefundProduct.RefundAmount,
			Number:    orderRefundProduct.Number,
			Spec: &pb_ec_order.OrderProductSpec{
				Sku: orderRefundProduct.Spec.Sku,
			},
		}
		invoiceProduct := share_invoice.InvoiceProduct{
			OrderProduct:    pbOrderProduct,
			InvoiceTemplate: invoiceTemplateMap[ecProduct.Ec.InvoiceTemplateId].(*ec.InvoiceTemplate),
		}
		invoiceProducts = append(invoiceProducts, invoiceProduct)
	}
	return invoiceProducts
}
