package order

import (
	"context"
	"fmt"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/service/ec/client"
	ec_model "mairpc/service/ec/model"
	order_model "mairpc/service/ec/model/order"
	ec_store "mairpc/service/ec/model/store"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"
	"strings"
	"time"
)

var EC_SEND_PICKUP_ORDER_NOTIFICATION_REDIS_KEY = "ec:%s:sendPickupOrderNotification"

func (OrderService) SendPickupOrderNotification(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	key := fmt.Sprintf(EC_SEND_PICKUP_ORDER_NOTIFICATION_REDIS_KEY, util.GetAccountId(ctx))
	ok, _ := extension.RedisClient.SetNX(key, "true", 60*30)
	if !ok {
		return &response.EmptyResponse{}, nil
	}
	defer extension.RedisClient.Del(key)
	setting, err := client.SettingService.GetDeliverySetting(ctx, &request.EmptyRequest{})
	if err != nil {
		return nil, err
	}
	if setting.Reservation.NotificationSetting == nil ||
		setting.Reservation.NotificationSetting.Enabled == false {
		return &response.EmptyResponse{}, nil
	}
	sendAt := time.Now()
	if setting.Reservation.NotificationSetting.Hour > 0 {
		sendAt = sendAt.Add(time.Duration(setting.Reservation.NotificationSetting.Hour) * time.Hour)
	}
	if setting.Reservation.NotificationSetting.Minute > 0 {
		sendAt = sendAt.Add(time.Duration(setting.Reservation.NotificationSetting.Minute) * time.Minute)
	}

	sendNotificationMessage(ctx, sendAt)
	return &response.EmptyResponse{}, nil
}

func sendNotificationMessage(ctx context.Context, sendAt time.Time) {
	condition := bson.M{
		"accountId":              util.GetAccountIdAsObjectId(ctx),
		"isDeleted":              false,
		"status":                 order_model.ORDER_STATUS_ACCEPTED,
		"method":                 order_model.ORDER_DELIVERY_METHOD_PICKUP,
		"reservation.hasSent":    false,
		"reservation.memberTime": bson.M{"$lte": sendAt},
	}
	pageSize := 200
	for {
		orders, _ := order_model.COrder.GetAllByConditionAndLimit(ctx, condition, []string{"_id"}, pageSize)
		if len(orders) == 0 {
			break
		}
		for _, order := range orders {
			store, err := ec_store.CStore.GetByIdContainDeleted(ctx, order.StoreId)
			if err != nil {
				log.Warn(ctx, "Failed to get store", log.Fields{
					"storeId":      order.StoreId.Hex(),
					"errorMessage": err.Error(),
				})
				continue
			}
			memberTimeStr := ec_model.FormatOrderReservation(order.Reservation.MemberTime, store)
			memberTimeStr = ConvertReservationTimeStr(memberTimeStr)
			basicPlaceholderValueMap := map[string]string{
				order_model.PLACEHOLDER_RESERVATION_TIME: memberTimeStr,
			}
			order.NotifyCustomer(ctx, constant.MESSAGE_RULE_ORDER_PICKUP_INCOMPLETE, basicPlaceholderValueMap)
			order.MarkReservationSent(ctx)
		}
		if len(orders) < pageSize {
			break
		}
		condition["_id"] = bson.M{"$gt": orders[len(orders)-1].Id}
	}
}

func ConvertReservationTimeStr(original string) string {
	if original == "" {
		return ""
	}
	parts := strings.Split(original, " ")
	if len(parts) != 2 {
		return original
	}
	dateStr, timeStr := parts[0], parts[1]
	reservationTime, err := time.Parse("2006/01/02", dateStr)
	if err != nil {
		return original
	}
	newDateStr := reservationTime.Format("2006年1月2日")
	timeRange := strings.Replace(timeStr, "-", "至", 1)
	return fmt.Sprintf("%s%s", newDateStr, timeRange)
}
