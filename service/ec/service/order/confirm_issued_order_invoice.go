package order

import (
	"mairpc/core/errors"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	pb_order "mairpc/proto/ec/order"
	ec_order "mairpc/service/ec/model/order"
	ec_store "mairpc/service/ec/model/store"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"

	"golang.org/x/net/context"
)

func (self OrderService) ConfirmIssuedOrderInvoice(ctx context.Context, req *pb_order.ConfirmIssuedOrderInvoiceRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	order := ec_order.Order{}
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       bson.ObjectIdHex(req.Id),
		"isDeleted": false,
	}
	err := ec_order.Common.GetByCondition(ctx, condition, ec_order.C_ORDER, &order)
	if err != nil {
		return nil, errors.NewNotExistsError("id")
	}

	enabledStatus := []string{
		ec_order.ORDER_STATUS_ACCEPTED,
		ec_order.ORDER_STATUS_PAID,
		ec_order.ORDER_STATUS_SHIPPED,
		ec_order.ORDER_STATUS_PARTIAL_SHIPPED,
		ec_order.ORDER_STATUS_COMPLETED,
	}
	if !util.StrInArray(order.Status, &enabledStatus) {
		return nil, errors.NewNotExistsError("id")
	}

	var invoicesResp *pb_order.ListInvoicesResponse
	if req.InvoiceId != "" {
		invoicesResp, err = self.ListInvoices(ctx, &pb_order.ListInvoicesRequest{Id: req.InvoiceId})
	} else {
		invoicesResp, err = self.ListInvoices(ctx, &pb_order.ListInvoicesRequest{OrderIds: []string{order.Id.Hex()}})
	}
	if err == nil && invoicesResp.Total != 0 {
		// 提交了发票抬头
		for _, invoice := range invoicesResp.Items {
			_, err := self.UpdateInvoice(ctx, &pb_order.UpdateInvoiceRequest{Id: invoice.Id, Status: ec_order.INVOICE_STATUS_ISSUED})
			if err != nil {
				return nil, err
			}
		}
		return &response.EmptyResponse{}, nil
	}
	// 未提交发票抬头:包括未申请线上开票和旧的开发票订单
	if order.Invoice.Needed {
		// 旧的开发票订单
		if req.StaffId == "" && req.MemberId == "" {
			return nil, errors.NewInvalidParamsError(map[string]interface{}{
				"memberId": "Required when staffId is empty",
				"staffId":  "Required when memberId is empty",
			})
		}
		enabledInvoiceStatus := []string{
			ec_order.INVOICE_STATUS_PENDING,
		}
		if !util.StrInArray(order.Invoice.Status, &enabledInvoiceStatus) {
			return nil, errors.NewNotExistsError("id")
		}
	} else {
		order.Invoice.Needed = true
	}
	operator := ""
	extra := bson.M{}

	if req.StaffId != "" { // 店员确认开票
		staff, err := ec_store.CStaff.GetById(ctx, bson.ObjectIdHex(req.StaffId))
		if err != nil {
			return nil, errors.NewNotExistsError("staffId")
		}
		operator = "staff:" + req.StaffId
		extra = bson.M{
			"staffName": staff.Name,
		}
	}
	if req.MemberId != "" { // 客户确认开票
		operator = "member:" + req.MemberId
	}
	generateOrderHistory(&order, operator, "确认已开票", extra)
	order.UpdateInvoiceStatus(ec_order.INVOICE_STATUS_ISSUED)

	err = order.UpdateInvoice(ctx)
	if err != nil {
		return nil, err
	}
	return &response.EmptyResponse{}, nil
}
