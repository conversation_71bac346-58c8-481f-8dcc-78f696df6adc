package order

import (
	"errors"
	"strings"

	core_errors "mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	pb_client "mairpc/proto/client"
	"mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	"mairpc/proto/common/types"
	"mairpc/proto/ec/order"
	pb_ec_order "mairpc/proto/ec/order"
	pb_ec_product "mairpc/proto/ec/product"
	"mairpc/proto/ec/setting"
	pb_product "mairpc/proto/product"
	"mairpc/service/ec/client"
	model_order "mairpc/service/ec/model/order"
	order_model "mairpc/service/ec/model/order"
	share_invoice "mairpc/service/share/component/invoice"
	share_util "mairpc/service/share/util"

	"golang.org/x/net/context"
)

func (self OrderService) InvoiceV2(ctx context.Context, req *pb_ec_order.InvoiceRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if req.Id == "" {
		return nil, errors.New("id is required")
	}
	invoiceSetting, err := client.SettingService.GetInvoiceSetting(ctx, &request.EmptyRequest{})
	if err != nil {
		return nil, err
	}
	if invoiceSetting.InvoiceSetting != "online" || !util.StrInArray("general", &invoiceSetting.InvoiceTypes) {
		return nil, core_errors.NewInvalidArgumentError("invoiceSetting")
	}
	listInvoicesResp, _ := client.OrderService.ListInvoices(ctx, &pb_ec_order.ListInvoicesRequest{
		Id: req.Id,
	})
	if listInvoicesResp == nil || len(listInvoicesResp.Items) == 0 {
		return nil, core_errors.NewInvalidArgumentError("invoice")
	}
	if err := handleInvoices(ctx, listInvoicesResp.Items[0], req, invoiceSetting); err != nil {
		return nil, err
	}
	return &response.EmptyResponse{}, nil
}

func handleInvoices(ctx context.Context, invoice *pb_ec_order.InvoiceResponse, req *pb_ec_order.InvoiceRequest, invoiceSetting *setting.InvoiceSetting) error {
	if invoice.InvoiceType != "general" && invoice.InvoiceType != "" {
		return core_errors.NewInvalidArgumentError("invoice.invoiceType")
	}
	if invoice.IsRed || invoice.Status == model_order.INVOICE_STATUS_REVERSING {
		invoiceClient := share_invoice.GetInvoiceClient(invoiceSetting.Provider, invoiceSetting.IsTest)
		if invoiceClient == nil {
			return core_errors.NewInvalidArgumentError("invoiceSetting")
		}
		request := &share_invoice.InvoiceRequest{
			Invoice: share_invoice.Invoice{
				SerialNo: func() string {
					if invoice.SerialNo == "" {
						return invoice.Id
					}
					return invoice.SerialNo
				}(),
				TaxID:          invoice.TaxID,
				RedConfirmUuid: invoice.RedConfirmUuid,
			},
		}
		copier.Instance(nil).From(invoiceSetting.PublicParameters).CopyTo(request)
		copier.Instance(nil).From(invoiceSetting.TaxSetting).CopyTo(request)
		if err := invoiceClient.IssueRedInvoice(ctx, request); err != nil {
			log.Warn(ctx, "Failed to issue red invoice by red confirm uuid", log.Fields{
				"errMsg":         err.Error(),
				"invoiceId":      invoice.Id,
				"redConfirmUuid": invoice.RedConfirmUuid,
			})
			client.OrderService.UpdateInvoice(ctx, &pb_ec_order.UpdateInvoiceRequest{
				Id:           invoice.Id,
				Status:       order_model.INVOICE_STATUS_FAILED,
				FailedReason: err.Error(),
			})
			return err
		}
		updateInvoiceRequest := &order.UpdateInvoiceRequest{
			Id:     invoice.Id,
			Status: model_order.INVOICE_STATUS_ISSUED,
		}
		if !invoice.IsRed {
			updateInvoiceRequest.Status = model_order.INVOICE_STATUS_PENDING
			updateInvoiceRequest.SerialNo = bson.NewObjectId().Hex()
		}
		client.OrderService.UpdateInvoice(ctx, updateInvoiceRequest)
		return nil
	}
	orderIds := invoice.MergedOrderIds
	orderIds = append(orderIds, invoice.OrderId)
	if invoice.InvoiceItems != nil && len(invoice.InvoiceItems.InvoiceProducts) > 0 {
		if err := invoicing(ctx, invoice, invoiceSetting, req); err != nil {
			return err
		}
		return nil
	}
	orders, err := order_model.COrder.GetByIds(ctx, share_util.ToMongoIds(orderIds))
	if err != nil {
		return err
	}
	orderRefunds, err := client.OrderService.ListOrderRefunds(ctx, &pb_ec_order.ListOrderRefundsRequest{
		OrderIds: orderIds,
		Status:   []string{"approved", "returned", "pending", "refunding", "failed", "refunded"},
	})
	if err != nil {
		log.Warn(ctx, "Failed to call ListOrderRefunds", log.Fields{"orderIds": orderIds, "errMsg": err.Error()})
	}
	var (
		productIds, invoiceTemplateIds []string
	)
	productIdToTemplateIdMap := make(map[string]string)
	productIdToProductsMap := make(map[string][]*order_model.OrderProduct)
	for _, order := range orders {
		for _, product := range order.Products {
			productIdToProductsMap[product.Id.Hex()] = append(productIdToProductsMap[product.Id.Hex()], &product)
			if util.StrInArray(product.Id.Hex(), &productIds) {
				continue
			}
			productIds = append(productIds, product.Id.Hex())
		}
	}
	productsResp, err := client.ProductService.ListProducts(ctx, &ec.ListProductsRequest{
		EcProductIds: productIds,
		ExistsInvoiceTemplateId: &types.BoolValue{
			Value: true,
		},
		ContainDeleted: true,
		ListCondition:  &request.ListCondition{Page: 1, PerPage: uint32(len(productIds))},
	})
	if err != nil {
		return err
	}
	if productsResp == nil || len(productsResp.Items) == 0 {
		return errors.New("Failed to get products by productIds, please check your invoice templates")
	}
	for _, p := range productsResp.Items {
		if p.Ec.InvoiceTemplateId == "" {
			continue
		}
		invoiceTemplateIds = append(invoiceTemplateIds, p.Ec.InvoiceTemplateId)
		productIdToTemplateIdMap[p.Ec.Id] = p.Ec.InvoiceTemplateId
	}
	if len(invoiceTemplateIds) == 0 {
		return errors.New("invoice templates of products is empty")
	}
	resp, err := client.SettingService.ListInvoiceTemplates(ctx, &setting.ListInvoiceTemplatesRequest{
		Ids:           invoiceTemplateIds,
		ListCondition: &request.ListCondition{Page: 1, PerPage: uint32(len(invoiceTemplateIds))},
	})
	if err != nil || resp == nil || len(resp.Items) == 0 {
		return core_errors.NewInvalidArgumentError("invoiceTemplate")
	}
	invoiceTemplateMap := util.MakeMapper("Id", resp.Items)
	for _, invoiceTemplate := range resp.Items {
		if !invoiceTemplate.IsMergeInvoicing {
			continue
		}
		var unusedTemplateIds []string
		for productId := range productIdToProductsMap {
			if util.StrInArray(productIdToTemplateIdMap[productId], &unusedTemplateIds) {
				continue
			}
			unusedTemplateIds = append(unusedTemplateIds, productIdToTemplateIdMap[productId])
		}
		if len(productIdToProductsMap) <= 1 || len(unusedTemplateIds) <= 1 {
			break
		}
		var orderProducts []*order_model.OrderProduct
		for productId, templateId := range productIdToTemplateIdMap {
			if invoiceTemplate.Id != templateId {
				continue
			}
			products := productIdToProductsMap[productId]
			orderProducts = append(orderProducts, products...)
			delete(productIdToProductsMap, productId)
		}
		invoiceProducts := genInvoiceProducts(ctx, orderProducts, productsResp.Items, orderRefunds, invoiceTemplateMap, invoice.InvoiceContents)
		if len(invoiceProducts) == 0 {
			log.Warn(ctx, "Failed to get invoice products", log.Fields{
				"invoiceTemplateId": invoiceTemplate.Id,
				"products":          orderProducts,
			})
			continue
		}
		var newInvoice = &order_model.Invoice{}
		copier.Instance(nil).From(invoice).CopyTo(newInvoice)
		newInvoice.Id = bson.NewObjectId()
		copier.Instance(nil).From(invoiceProducts).CopyTo(&newInvoice.InvoiceItems.InvoiceProducts)
		newInvoice.InvoiceItems.DeliveryFee = 0
		if err := newInvoice.Insert(ctx); err != nil {
			log.Warn(ctx, "Failed to create subInvoice", log.Fields{
				"errMsg": err.Error(),
			})
		}
		var subInvoice = &pb_ec_order.InvoiceResponse{}
		copier.Instance(nil).From(newInvoice).CopyTo(subInvoice)
		if err := invoicing(ctx, subInvoice, invoiceSetting, req); err != nil {
			log.Warn(ctx, "Failed to invoice", log.Fields{
				"errMsg":     err.Error(),
				"invoice.id": subInvoice.Id,
			})
		}
	}
	var (
		orderProducts []*order_model.OrderProduct
		mainInvoice   = &order_model.Invoice{}
	)
	for _, products := range productIdToProductsMap {
		orderProducts = append(orderProducts, products...)
	}
	invoiceProducts := genInvoiceProducts(ctx, orderProducts, productsResp.Items, orderRefunds, invoiceTemplateMap, invoice.InvoiceContents)
	invoice.InvoiceItems.DeliveryFee = getDeliveryFee(orders)
	copier.Instance(nil).From(invoiceProducts).CopyTo(&invoice.InvoiceItems.InvoiceProducts)
	copier.Instance(nil).From(invoice).CopyTo(mainInvoice)
	if err := mainInvoice.Update(ctx); err != nil {
		return err
	}
	if err := invoicing(ctx, invoice, invoiceSetting, req); err != nil {
		return err
	}
	return nil
}

func invoicing(ctx context.Context, invoice *pb_ec_order.InvoiceResponse, invoiceSetting *setting.InvoiceSetting, req *pb_ec_order.InvoiceRequest) error {
	invoiceClient := share_invoice.GetInvoiceClient(invoiceSetting.Provider, invoiceSetting.IsTest)
	if invoiceClient == nil {
		return core_errors.NewInvalidArgumentError("invoiceSetting")
	}
	if invoice.InvoiceItems.InvoiceProducts == nil {
		return core_errors.NewInvalidArgumentError("invoiceProducts")
	}
	var invoiceProducts []share_invoice.InvoiceProduct
	copier.Instance(nil).From(invoice.InvoiceItems.InvoiceProducts).CopyTo(&invoiceProducts)
	request := &share_invoice.InvoiceRequest{
		OrderInfo: share_invoice.OrderInfo{
			Id:          invoice.OrderId,
			Number:      invoice.OrderNumber,
			DeliveryFee: invoice.InvoiceItems.DeliveryFee,
		},
		Invoice: share_invoice.Invoice{
			SerialNo: func() string {
				if invoice.SerialNo == "" {
					return invoice.Id
				}
				return invoice.SerialNo
			}(),
			Name:           invoice.Name,
			Type:           invoice.Type,
			TaxID:          invoice.TaxID,
			Email:          invoice.Email,
			Phone:          invoice.Phone,
			Address:        invoice.Address,
			BankName:       invoice.BankName,
			BankAccount:    invoice.BankAccount,
			Status:         invoice.Status,
			RedConfirmUuid: invoice.RedConfirmUuid,
		},
		Products: invoiceProducts,
	}
	if invoiceSetting.Provider == share_invoice.PROVIDER_BAIWANG {
		// 百望购方号码不用申请发票时填的手机号，而用 member.phone
		// https://gitlab.maiscrm.com/mai/home/<USER>/issues/53881#note_5018412
		member, _ := GetMember(ctx, req.MemberId)
		if member != nil {
			request.Invoice.Phone = member.Phone
		}
	}
	if invoice.InvoiceItems.DeliveryFee > 0 {
		resp, err := client.SettingService.ListInvoiceTemplates(ctx, &setting.ListInvoiceTemplatesRequest{
			Type: "deliveryFee",
		})
		if err != nil || resp == nil || len(resp.Items) == 0 {
			return core_errors.NewInvalidArgumentError("deliveryFeeTemplate")
		}
		deliveryFeeTemplate := resp.Items[0]
		request.DeliveryFeeTemplate = &ec.InvoiceTemplate{
			TaxClassificationCode: deliveryFeeTemplate.TaxClassificationCode,
			TaxClassificationName: deliveryFeeTemplate.TaxClassificationName,
			FavorablePolicy:       deliveryFeeTemplate.FavorablePolicy,
			TaxRate:               deliveryFeeTemplate.TaxRate,
			Name:                  deliveryFeeTemplate.Name,
		}
	}
	copier.Instance(nil).From(invoiceSetting.PublicParameters).CopyTo(request)
	copier.Instance(nil).From(invoiceSetting.TaxSetting).CopyTo(request)
	var rpcErr error
	defer func() {
		if rpcErr != nil {
			log.Warn(ctx, "Failed to update invoice by failed status", log.Fields{
				"invoiceSettingId": invoiceSetting.Id,
				"errorMessage":     rpcErr.Error(),
			})
		}
	}()
	invoiceResp, err := invoiceClient.Invoice(ctx, request)
	if err != nil {
		_, rpcErr = client.OrderService.UpdateInvoice(ctx, &pb_ec_order.UpdateInvoiceRequest{
			Id:           invoice.Id,
			Status:       order_model.INVOICE_STATUS_FAILED,
			FailedReason: err.Error(),
		})
		return err
	}
	if invoiceResp == nil {
		return nil
	}
	if invoiceResp.Url != "" {
		updateInvoiceFileUrl(ctx, invoice.Id, &share_invoice.DownloadResponse{
			Url:        invoiceResp.Url,
			Code:       invoiceResp.Code,
			Number:     invoiceResp.Number,
			InvoicedAt: invoiceResp.InvoicedAt,
			Amount:     invoiceResp.Amount,
		})
		return nil
	}
	_, rpcErr = client.OrderService.UpdateInvoice(ctx, &pb_ec_order.UpdateInvoiceRequest{
		Id:     invoice.Id,
		Status: order_model.INVOICE_STATUS_INVOICING,
		Result: &pb_ec_order.InvoiceResult{
			Code:       invoiceResp.Code,
			Number:     invoiceResp.Number,
			InvoicedAt: invoiceResp.InvoicedAt,
			Amount:     invoiceResp.Amount,
		},
	})
	return nil
}

func genInvoiceProducts(
	ctx context.Context,
	orderProducts []*order_model.OrderProduct,
	products []*pb_ec_product.ProductResponse,
	orderRefunds *pb_ec_order.ListOrderRefundsResponse,
	invoiceTemplateMap map[interface{}]interface{},
	invoiceContents []string) []share_invoice.InvoiceProduct {
	categoryMap := getProductCategoryMap(ctx)
	invoiceProducts := []share_invoice.InvoiceProduct{}
	for _, op := range orderProducts {
		orderProduct := op
		ecProduct := &pb_ec_product.ProductResponse{}
		for _, p := range products {
			if op.Id.Hex() != p.Ec.Id {
				continue
			}
			ecProduct = p
		}
		if ecProduct.Ec == nil {
			log.Warn(ctx, "Product not found", log.Fields{
				"ecProductId": op.Id,
			})
			continue
		}
		if orderProduct.Type != "product" {
			if orderProduct.Total == uint64(orderProduct.RefundTotal) {
				continue
			}
			orderProduct.PayAmount = (orderProduct.PayAmount / orderProduct.Total) * (orderProduct.Total - uint64(orderProduct.RefundTotal))
			orderProduct.TotalAmount = orderProduct.Price * (orderProduct.Total - uint64(orderProduct.RefundTotal))
			orderProduct.Total = orderProduct.Total - uint64(orderProduct.RefundTotal)
		} else {
			refundProduct := getRefundProduct(orderRefunds, orderProduct.OutTradeId.Hex())
			if refundProduct != nil {
				if refundProduct.RefundAmount == orderProduct.PayAmount {
					continue
				}
				orderProduct.PayAmount = orderProduct.PayAmount - refundProduct.RefundAmount
			}
		}
		var isSameProduct bool
		if len(invoiceProducts) > 0 {
			for index, invoiceProduct := range invoiceProducts {
				if invoiceProduct.OrderProduct.Number != orderProduct.Number ||
					invoiceProduct.OrderProduct.Spec.Sku != "" && invoiceProduct.OrderProduct.Spec.Sku != orderProduct.Spec.Sku {
					continue
				}
				invoiceProducts[index].OrderProduct.PayAmount += orderProduct.PayAmount
				invoiceProducts[index].OrderProduct.Total += orderProduct.Total
				invoiceProducts[index].OrderProduct.TotalAmount += orderProduct.TotalAmount
				isSameProduct = true
				break
			}
		}
		if isSameProduct {
			continue
		}
		if categoryName, ok := categoryMap[orderProduct.CategoryId.Hex()]; ok && len(invoiceContents) > 0 {
			content := []string{}
			if util.StrInArray("productCategory", &invoiceContents) {
				content = append(content, categoryName)
			}
			if util.StrInArray("productDetail", &invoiceContents) {
				content = append(content, orderProduct.Name)
			}
			orderProduct.Name = strings.Join(content, " ")
		}
		var pbOrderProduct = &pb_ec_order.OrderProduct{}
		copier.Instance(nil).From(orderProduct).CopyTo(pbOrderProduct)
		invoiceProduct := share_invoice.InvoiceProduct{
			OrderProduct:    pbOrderProduct,
			InvoiceTemplate: invoiceTemplateMap[ecProduct.Ec.InvoiceTemplateId].(*ec.InvoiceTemplate),
		}
		invoiceProducts = append(invoiceProducts, invoiceProduct)
	}
	return invoiceProducts
}

func getRefundProduct(orderRefundResp *pb_ec_order.ListOrderRefundsResponse, outTradeId string) *pb_ec_order.RefundProduct {
	if orderRefundResp == nil {
		return nil
	}
	for _, r := range orderRefundResp.Items {
		for _, rp := range r.Products {
			if rp.OutTradeId == outTradeId {
				return rp
			}
		}
	}
	return nil
}

func getProductCategoryMap(ctx context.Context) map[string]string {
	m := make(map[string]string)
	resp, err := pb_client.GetProductServiceClient().ListCategories(ctx, &pb_product.ListCategoriesRequest{
		Type:      "product",
		Unlimited: true,
	})
	if err != nil {
		return m
	}
	for _, item := range resp.Items {
		m[item.Id] = item.Name
	}
	return m
}

func getDeliveryFee(orders []order_model.Order) uint64 {
	var deliveryFee uint64
	for _, order := range orders {
		if order.IsDeliveryFeeRefunded {
			continue
		}
		deliveryFee += order.Logistics.Fee
	}
	return deliveryFee
}
