package order

import (
	"context"
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	common "mairpc/proto/common/ec"
	pb_order "mairpc/proto/ec/order"
	pb_product "mairpc/proto/product"
	ec_order "mairpc/service/ec/model/order"
	order_model "mairpc/service/ec/model/order"
	ec_product "mairpc/service/ec/model/product"
	"mairpc/service/share/util"
	"time"
)

func (OrderService) CreateSimpleOrder(ctx context.Context, req *pb_order.CreateSimpleOrderRequest) (*pb_order.OrderDetail, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	purchaseRequest := &common.PurchaseRequest{}
	core_util.CopyFieldsRFC3339(req, purchaseRequest)

	builder, err := GenerateBuilder(ctx, purchaseRequest, false, true)
	if err != nil {
		return nil, err
	}

	order, err := generateBasicOrder(ctx, builder)
	if err != nil {
		return nil, err
	}
	if req.DeliveryFee > 0 {
		order.Logistics.Fee = req.DeliveryFee
	}
	if req.Remarks != "" {
		order.Remarks = req.Remarks
	}

	err = generateProducts(ctx, order, builder, req.Products)
	if err != nil {
		return nil, err
	}

	buildMemberDist(ctx, order, builder)
	calcOrderTotalAmount(order)
	calcPayAmount(order)

	member, err := builder.GetMember(ctx)
	if err != nil {
		return nil, err
	}
	extra := bson.M{
		"memberName": member.Name,
	}

	if order.PayAmount == 0 {
		order.Status = order_model.ORDER_STATUS_PAID
		order.PaidAt = time.Now()
		order.CreatedAt = time.Now()
		generateOrderHistory(order, "member:"+builder.MemberId, builder.Message, extra) // paid history

		AutoAcceptedOrder(ctx, order, builder.Message, extra)
	} else {
		generateOrderHistory(order, "member:"+builder.MemberId, builder.Message, extra) // unpaid history
	}
	order.ProfitTypes = append(order.ProfitTypes, ec_order.PROFIT_TYPE_PROFITSHARING)
	if order.Distribution.PromoterId.Hex() != "" && order.Distribution.Amount != 0 {
		order.ProfitTypes = append(order.ProfitTypes, ec_order.PROFIT_TYPE_DISTRIBUTION)
	}
	// 为了代销订单发送 maievent-distribute-order 事件，这里默认给 order.distribution 赋值
	if order.IsConsignmentOrder() {
		for _, orderProduct := range order.Products {
			order.Distribution.StoreDistributionAmount += orderProduct.StoreDistributionAmount
		}
		// 发送 maievent-store-distribute-order 事件时需要 PromoterId 不为空，将 StoreId 需要设置为 PromoterId
		if order.Distribution.PromoterId.IsZero() {
			order.Distribution.PromoterId = order.StoreId
		}
	}
	if number, err := generateUniqueCode("order", "M"); err != nil {
		return nil, err
	} else {
		order.Number = number
	}
	if len(req.Campaigns) > 0 {
		campaigns := []ec_order.Campaign{}
		for _, c := range req.Campaigns {
			campaign := ec_order.Campaign{
				Id:    util.ToMongoId(c.Id),
				Title: c.Title,
				Type:  c.Type,
			}
			campaigns = append(campaigns, campaign)
		}
		order.Campaigns = campaigns
	}
	err = order.Create(ctx)
	if err != nil {
		return nil, err
	}
	handleZeroPayAmountOrderCreated(ctx, order)
	return formatOrderDetail(ctx, order), nil
}

func generateProducts(ctx context.Context, order *order_model.Order, builder *OrderBuilder, products []*pb_order.OrderProductRequest) error {
	ids := core_util.ExtractArrayStringField("Id", products)
	ecProductMap, err := builder.GetEcProductMap(ctx, ids, nil)
	if err != nil {
		return err
	}
	productIds := []string{}
	for _, ecProduct := range ecProductMap {
		productIds = append(productIds, ecProduct.(ec_product.Product).ProductId.Hex())
	}
	productMap, err := builder.GetProductMap(ctx, productIds)
	if err != nil {
		return err
	}
	for _, p := range products {
		ecProduct, ok := ecProductMap[bson.ObjectIdHex(p.Id)].(ec_product.Product)
		if !ok {
			return errors.NewNotExistsError("product")
		}
		product, ok := productMap[ecProduct.ProductId.Hex()].(*pb_product.ProductDetailResponse)
		if !ok {
			return errors.NewNotExistsError("product")
		}
		orderProduct := order_model.OrderProduct{
			Id:                      ecProduct.Id,
			OutTradeId:              bson.NewObjectId(),
			Name:                    product.Name,
			Spec:                    getProductSpec(p.Sku, *product),
			Total:                   p.Total,
			CategoryId:              bson.ObjectIdHex(product.Category.Id),
			ProductId:               bson.ObjectIdHex(product.Id),
			Number:                  product.Number,
			BarCode:                 product.BarCode,
			Type:                    product.Type,
			DisableMemberRefund:     ecProduct.DisableMemberRefund,
			Tags:                    product.Tags,
			Price:                   p.Price,
			TotalAmount:             p.TotalAmount,
			PayAmount:               p.PayAmount,
			StoreDistributionAmount: p.StoreDistributionAmount,
			DistributionAmount:      p.DistributionAmount,
			OriginPrice:             p.OriginPrice,
			Source:                  product.Source,
		}
		if len(p.Campaigns) > 0 {
			campaigns := []order_model.Campaign{}
			copier.Instance(nil).From(p.Campaigns).CopyTo(&campaigns)
			orderProduct.Campaigns = campaigns
		}
		orderProduct.Picture = orderProduct.Spec.Picture
		if orderProduct.Picture == "" && len(product.Pictures) > 0 {
			orderProduct.Picture = product.Pictures[0].Url
		}
		// 商品是优惠券
		if product.Type == ec_order.ORDER_TYPE_COUPON && ecProduct.SubType != ec_order.ORDER_SUB_TYPE_STORED_VALUE_CARD {
			orderProduct.CouponId = ecProduct.Skus[0].CouponId
		}
		// 商品是储值卡
		if product.Type == ec_order.ORDER_TYPE_COUPON && ecProduct.SubType == ec_order.ORDER_SUB_TYPE_STORED_VALUE_CARD {
			orderProduct.StoredValueCardId = ecProduct.Skus[0].StoredValueCardId
		}
		order.Products = append(order.Products, orderProduct)
	}
	return nil
}
