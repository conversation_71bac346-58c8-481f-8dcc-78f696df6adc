package order

import (
	"encoding/json"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	pb_client "mairpc/proto/client"
	common "mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	pb_ec_order "mairpc/proto/ec/order"
	pb_retailer "mairpc/proto/ec/retailer"
	"mairpc/proto/ec/store"
	pb_member "mairpc/proto/member"
	"mairpc/proto/product"
	ec_client "mairpc/service/ec/client"
	ec_model "mairpc/service/ec/model"
	ec_distribution "mairpc/service/ec/model/distribution"
	"mairpc/service/ec/model/marketing"
	ec_model_order "mairpc/service/ec/model/order"
	ec_model_store "mairpc/service/ec/model/store"
	"mairpc/service/ec/service"
	"mairpc/service/share"
	"mairpc/service/share/component"
	"mairpc/service/share/util"
	"regexp"

	"golang.org/x/net/context"
)

func (OrderService) ListOrders(ctx context.Context, req *pb_ec_order.ListOrdersRequest) (*pb_ec_order.ListOrdersResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	pageIndex, pageSize := util.ParsePagingCondition(req.ListCondition)
	orderBys := []string{"-createdAt"}
	if req.ListCondition != nil && len(req.ListCondition.OrderBy) != 0 {
		orderBys = req.ListCondition.OrderBy
	}
	condition, err := GetListOrdersCondition(ctx, req, []string{})
	if err != nil {
		return nil, err
	}
	if condition == nil {
		return &pb_ec_order.ListOrdersResponse{
			Total: 0,
		}, nil
	}
	var (
		orders    []ec_model_order.Order
		total     int
		indexHint = "accountId,isDeleted,status,maskType,createdAt"
	)
	shouldUseIndexHint := shouldUseOrderIndexHint(ctx, condition)
	if req.WithoutTotal {
		if shouldUseIndexHint {
			ec_model_order.Common.GetAllByPaginationWithoutCountWithHint(ctx, condition, pageIndex, pageSize, orderBys, ec_model_order.C_ORDER, indexHint, &orders)
		} else {
			ec_model_order.Common.GetAllByPaginationWithoutCount(ctx, condition, pageIndex, pageSize, orderBys, ec_model_order.C_ORDER, &orders)
		}
	} else {
		total = ec_model_order.Common.GetAllByPagination(ctx, condition, pageIndex, pageSize, orderBys, ec_model_order.C_ORDER, &orders)
	}

	storeIds := []string{}
	memberIds := []string{}
	orderAccountId := util.GetAccountIdAsObjectId(ctx)
	for _, order := range orders {
		orderAccountId = order.AccountId
		memberIds = append(memberIds, order.MemberId.Hex())
		if !util.StrInArray(order.StoreId.Hex(), &storeIds) {
			storeIds = append(storeIds, order.StoreId.Hex())
		}
	}

	condition = bson.M{
		"_id": bson.M{
			"$in": util.ToMongoIds(storeIds),
		},
		"accountId": orderAccountId,
	}
	stores := []ec_model_store.Store{}
	_, err = ec_model_order.Common.GetAllByCondition(ctx, condition, []string{}, 0, ec_model_store.C_STORE, &stores)
	if err != nil {
		return nil, err
	}
	storeMap := map[string]ec_model_store.Store{}
	for _, store := range stores {
		storeMap[store.Id.Hex()] = store
	}

	settingArrayMap, err := getOrderProductInterfaceSettings(ctx, orders)
	if err != nil {
		return nil, err
	}

	orderMemberMap := map[string]*pb_ec_order.OrderMember{}
	if len(memberIds) > 0 {
		members, err := service.GetMembers(core_util.DuplicateContextWithAid(ctx, orderAccountId.Hex()), &pb_member.MemberDetailListRequest{
			Ids: util.StrArrayUnique(memberIds),
		})
		if err != nil {
			return nil, err
		}

		for _, member := range members.Members {
			orderMemberMap[member.Id] = &pb_ec_order.OrderMember{
				Id:    member.Id,
				Name:  member.Name,
				Phone: member.Phone,
			}
		}
	}

	orderDetails := []*pb_ec_order.OrderDetail{}
	for _, order := range orders {
		orderDetail := formatOrderDetailWithStore(ctx, &order, storeMap[order.StoreId.Hex()], settingArrayMap[order.Id])
		orderDetail.Member = orderMemberMap[orderDetail.MemberId]
		if order.IsChainRetailOrder() && order.IsConsignmentOrder() {
			orderDetail.Distribution.ProfitSharingStatus = formatConsignmentOrderProfitSharingStatus(ctx, order)
		}
		orderDetails = append(orderDetails, orderDetail)
	}

	return &pb_ec_order.ListOrdersResponse{
		Total: uint64(total),
		Items: orderDetails,
	}, nil
}

func getOrderProductInterfaceSettings(ctx context.Context, orders []ec_model_order.Order) (map[interface{}][]interface{}, error) {
	orderIds := core_util.ToObjectIdArray(core_util.ExtractArrayField("Id", orders))
	settings, err := ec_model_order.COrderProductSetting.GetByOrderIds(ctx, orderIds)
	if err != nil {
		return nil, err
	}
	settingArrayMap := core_util.MakeArrayMapper("OrderId", settings)
	return settingArrayMap, err
}

func GetListOrdersCondition(ctx context.Context, req *pb_ec_order.ListOrdersRequest, accountIds []string) (bson.M, error) {
	condition := ec_model_order.Common.GenDefaultCondition(ctx)
	if ec_model.IsSplitOrderAccount(ctx) {
		req.IgnoreTags = append(req.IgnoreTags, ec_model_order.SPLIT_ORDER_TAG_ORIGIN_ORDER)
	}

	if req.SearchOriginalOrder != nil && req.SearchOriginalOrder.Value {
		req.IgnoreTags = util.RemoveStrArrayElems(req.IgnoreTags, []string{ec_model_order.SPLIT_ORDER_TAG_ORIGIN_ORDER})
	}

	if len(accountIds) > 0 {
		condition["accountId"] = bson.M{
			"$in": util.ToMongoIds(accountIds),
		}
	}

	if req.MemberId != "" {
		member, err := pb_client.GetMemberServiceClient().GetMember(ctx, &pb_member.MemberDetailRequest{
			Id:          req.MemberId,
			ExtraFields: []string{"Properties"},
		})
		if err != nil {
			return nil, err
		}
		if share.IsChainRetailer(member) {
			distributionRetailerDetail, err := component.Retailer.GetDistributionRetailer(ctx, &component.GetDistributionRetailerRequest{Id: member.Phone})
			if err != nil {
				return nil, err
			}
			if !bson.IsObjectIdHex(distributionRetailerDetail.MaiAccountId) {
				return nil, nil
			}
			condition["accountId"] = bson.ObjectIdHex(distributionRetailerDetail.MaiAccountId)
		} else if share.IsMaimengStore(member) {
			distributionConfig, err := component.Retailer.GetDistributionConfig(ctx)
			if err != nil {
				return nil, err
			}
			if !bson.IsObjectIdHex(distributionConfig.MaimengStoreAccountId) {
				return nil, nil
			}
			condition["accountId"] = bson.ObjectIdHex(distributionConfig.MaimengStoreAccountId)
			resp, _ := ec_client.StoreService.ListStoresByMemberId(ctx, &store.ListStoresByMemberIdRequest{MemberId: req.MemberId})
			if resp == nil || len(resp.Items) != 1 {
				return nil, nil
			}
			req.StoreIds = []string{resp.Items[0].Id}
		} else {
			condition["memberId"] = bson.ObjectIdHex(req.MemberId)
			condition["memberDeleted"] = false
		}
	}

	if req.IncludeMemberDeleted != nil {
		if req.IncludeMemberDeleted.Value {
			delete(condition, "memberDeleted")
		} else {
			condition["memberDeleted"] = false
		}
	}

	if len(req.Ids) > 0 {
		condition["_id"] = bson.M{
			"$in": core_util.ToObjectIdArray(req.Ids),
		}
	}

	if len(req.Tags) > 0 {
		// tags 筛选条件一定存在
		tagCondition := bson.M{}
		if util.StrInArray(ec_model_order.SPLIT_ORDER_TAG_ORIGIN_ORDER, &req.Tags) {
			util.RemoveStrArrayElems(req.IgnoreTags, []string{ec_model_order.SPLIT_ORDER_TAG_ORIGIN_ORDER})
		}
		if req.MustContainAllTags {
			tagCondition["$all"] = req.Tags
		} else {
			tagCondition["$in"] = req.Tags
		}
		condition["tags"] = tagCondition
	}

	if req.Distributor != nil && len(req.Distributor.Ids) > 0 {
		condition["distributorIds"] = bson.M{
			"$in": core_util.ToObjectIdArray(req.Distributor.Ids),
		}
	}

	if len(req.HistoryStatus) > 0 {
		condition["histories.status"] = bson.M{"$in": req.HistoryStatus}
	}

	if req.Operator != "" {
		condition["operator"] = req.Operator
	}

	if req.Distributor != nil && len(req.Distributor.Ids) > 0 {
		condition["distributorIds"] = bson.M{
			"$in": util.ToMongoIds(req.Distributor.Ids),
		}
	}

	if len(req.Status) > 0 {
		if len(req.Status) == 1 && util.StrInArray(ec_model_order.ORDER_STATUS_UNASSIGNED, &req.Status) && req.StoreId != "" {
			// 查找“已拒订单”：此门店曾经拒过的订单
			condition["histories.storeId"] = util.ToMongoId(req.StoreId)
			condition["histories.status"] = ec_model_order.ORDER_STATUS_UNASSIGNED
		} else if util.StrInArray(ec_model_order.ORDER_REFUND_STATUS_REFUNDED, &req.Status) {
			condition["refundStatus"] = ec_model_order.ORDER_REFUND_STATUS_REFUNDED
		} else if util.StrInArray(ec_model_order.ORDER_STATUS_UNCOMMENTED, &req.Status) {
			condition["isCommented"] = false
			condition["status"] = ec_model_order.ORDER_STATUS_COMPLETED
		} else {
			condition["status"] = bson.M{
				"$in": req.Status,
			}
		}
	}

	if len(req.ExcludedStatus) > 0 {
		if _, ok := condition["status"]; ok {
			condition["status"].(bson.M)["$not"] = bson.M{
				"$in": req.ExcludedStatus,
			}
		} else {
			condition["status"] = bson.M{
				"$not": bson.M{
					"$in": req.ExcludedStatus,
				},
			}
		}
	}

	if req.ProductId != "" {
		condition["products.productId"] = bson.ObjectIdHex(req.ProductId)
	}

	if req.RefundStatus != "" {
		if req.RefundStatus == ec_model_order.ORDER_REFUND_STATUS_REFUNDING {
			condition["products.refundStatus"] = bson.M{
				"$in": []string{
					ec_model_order.ORDER_REFUND_STATUS_PENDING,
					ec_model_order.ORDER_REFUND_STATUS_REFUNDING,
				},
			}
		} else {
			condition["products.refundStatus"] = req.RefundStatus
		}
	}

	storeIds := []bson.ObjectId{}
	if len(req.StoreIds) > 0 {
		storeIds = util.ToMongoIds(req.StoreIds)
	}
	if req.Locations != "" {
		locations := []*pb_ec_order.OrderLocationFilter{}
		json.Unmarshal([]byte(req.Locations), &locations)
		if len(locations) > 0 {
			condition["storeId"] = bson.M{"$in": GetStoreIdsByLocation(ctx, locations)}
		}
	}

	if req.OmsPushStatus != "" {
		condition["omsProcessor.pushStatus"] = req.OmsPushStatus
	}

	if req.SearchType != "" && req.SearchKey != "" {
		prefixSearchKey := util.GetPrefixSearchStrRegex(req.SearchKey)
		switch req.SearchType {
		case "order":
			condition["number"] = req.SearchKey
			// 存在 E 开头加上 23 个数字的订单编号 bson.IsObjectIdHex() 判断会为 true
			matched, _ := regexp.MatchString("^E\\d{23}$", req.SearchKey)
			if bson.IsObjectIdHex(req.SearchKey) && !matched {
				delete(condition, "number")
				condition["_id"] = util.ToMongoId(req.SearchKey)
			}
			// 交易单号
			if len(req.SearchKey) > 24 && core_util.IsAllNumber(req.SearchKey) {
				delete(condition, "number")
				condition["tradeNo"] = req.SearchKey
			}

			if !ec_model_order.Common.Exist(ctx, ec_model_order.C_ORDER, condition) {
				delete(condition, "number")
				resp, _ := pb_client.GetProductServiceClient().SearchProduct(ctx, &product.SearchProductRequest{
					SearchKeyword: req.SearchKey,
					ListCondition: &request.ListCondition{Page: 1, PerPage: 200},
				})
				productIds := []bson.ObjectId{}
				if resp != nil {
					productIds = core_util.ToObjectIdArray(core_util.ExtractArrayField("Id", resp.Items))
				}
				condition["products.productId"] = bson.M{"$in": productIds}
			}
		case "contact":
			condition["$or"] = []bson.M{
				{"contact.name": prefixSearchKey},
				{"contact.phone": req.SearchKey},
			}
		case "logistics":
			condition["products.logistics.waybillId"] = req.SearchKey
		case "member":
			memberListResponse, err := SearchMember(ctx, req.SearchKey, []string{})
			if err != nil {
				return bson.M{}, err
			}
			memberIds := []bson.ObjectId{}
			for _, item := range memberListResponse.Members {
				memberIds = append(memberIds, bson.ObjectIdHex(item.Id))
			}

			condition["memberId"] = bson.M{
				"$in": memberIds,
			}
		case "store":
			condition["storeId"] = bson.ObjectIdHex(req.SearchKey)
		case "remarks":
			condition["remarks"] = util.GetFuzzySearchStrRegex(req.SearchKey)
		default:
			// do nothing
		}
	}

	if req.StoreId != "" { // 来自于店长小程序的请求
		if !util.StrInArray(ec_model_order.ORDER_STATUS_UNASSIGNED, &req.Status) { // 排除查找“已拒订单”的 api
			storeIds = append(storeIds, util.ToMongoId(req.StoreId))
		}

		if req.SearchKey != "" {
			condition["$or"] = []bson.M{
				{"number": req.SearchKey},
				{"contact.phone": req.SearchKey},
			}
		}

		// 店长小程序端 屏蔽正在拼团的订单
		condition["campaigns.grouponStatus"] = bson.M{
			"$ne": marketing.GROUPON_RECORD_STATUS_RUNNING,
		}
	}

	if len(storeIds) > 0 {
		condition["storeId"] = bson.M{"$in": storeIds}
	}

	if timeSpan := util.ParseDateRange(req.CreatedTime); len(timeSpan) > 0 {
		condition["createdAt"] = timeSpan
	}
	if timeSpan := util.ParseDateRange(req.CompletedTime); len(timeSpan) > 0 {
		condition["completedAt"] = timeSpan
	}
	if amountSpan := util.ParseIntegerRange(req.PayAmount); len(amountSpan) > 0 {
		condition["payAmount"] = amountSpan
	}

	// DeliveryMethod 和 DeliveryMethods 同时存在时，DeliveryMethod 优先级更高
	if req.DeliveryMethod != "" {
		condition["method"] = req.DeliveryMethod
	}
	if len(req.DeliveryMethods) > 0 && req.DeliveryMethod == "" {
		condition["method"] = bson.M{
			"$in": req.DeliveryMethods,
		}
	}

	if req.Campaign != nil {
		if req.Campaign.Type == "none" { // 不参加任何活动的订单
			condition["campaigns"] = bson.M{
				"$exists": false,
			}
		} else {
			condition["campaigns"] = formatCampaignCondition(req.Campaign)
		}
	}

	if len(req.Numbers) != 0 {
		condition["number"] = bson.M{
			"$in": req.Numbers,
		}
	}

	if len(req.ExcludedCampaigns) != 0 {
		norConditions := []bson.M{}
		for _, c := range req.ExcludedCampaigns {
			norConditions = append(norConditions, bson.M{"campaigns": formatCampaignCondition(c)})
		}
		condition["$nor"] = norConditions
	}

	if len(req.GrouponRecordIds) != 0 {
		condition["campaigns.grouponRecordId"] = bson.M{"$in": util.ToMongoIds(req.GrouponRecordIds)}
	}

	if len(req.IgnoreTags) > 0 {
		if _, ok := condition["tags"]; ok {
			condition["tags"].(bson.M)["$nin"] = req.IgnoreTags
		} else {
			condition["tags"] = bson.M{
				"$nin": req.IgnoreTags,
			}
		}
	}

	if timeSpan := util.ParseDateRange(req.UpdatedAt); len(timeSpan) > 0 {
		condition["updatedAt"] = timeSpan
	}

	if len(req.DiscountTypes) > 0 {
		condition["discounts.type"] = bson.M{"$in": req.DiscountTypes}
	}

	if len(req.DiscountIds) > 0 {
		condition["discounts.id"] = bson.M{"$in": util.ToMongoIds(req.DiscountIds)}
	}

	if len(req.ExcludedDiscountTypes) > 0 {
		condition["discounts.type"] = bson.M{"$not": bson.M{"$in": req.ExcludedDiscountTypes}}
	}

	if req.Invoice != nil {
		if req.Invoice.Status != "" {
			condition["invoice.status"] = req.Invoice.Status
		}
		condition["invoice.needed"] = req.Invoice.Needed
	}
	if req.TicketNeeded != nil {
		if req.TicketNeeded.Value {
			condition["ticket.needed"] = true
		} else {
			condition["ticket"] = bson.M{"$exists": false}
		}
	}
	if core_util.StrInArray(req.Payment, &[]string{ec_model_order.PAYMENT_WECHAT, ec_model_order.PAYMENT_OFFLINE}) {
		condition["payment"] = req.Payment
	}
	if req.Payment == ec_model_order.PAYMENT_PREPAIDCARD {
		condition["prepaidCards"] = bson.M{"$exists": true}
	}
	if req.ExistsMessage != nil {
		condition["message"] = bson.M{
			"$exists": req.ExistsMessage.Value,
		}
	}

	if req.MaskType != "" {
		condition["maskType"] = req.MaskType
	}

	if req.MemberPaidCard != nil {
		tmpCondition := bson.M{}
		if req.MemberPaidCard.Id != "" {
			tmpCondition["id"] = util.ToMongoId(req.MemberPaidCard.Id)
		}
		if req.MemberPaidCard.Type != "" {
			tmpCondition["type"] = req.MemberPaidCard.Type
		}
		if req.MemberPaidCard.RecordId != "" {
			tmpCondition["recordId"] = util.ToMongoId(req.MemberPaidCard.RecordId)
		}
		condition["memberPaidCards"] = bson.M{
			"$elemMatch": tmpCondition,
		}
	}

	if len(req.Origins) > 0 {
		condition["channel.origin"] = bson.M{
			"$in": req.Origins,
		}
	}

	if req.PickupPassword != "" {
		condition["pickupPassword"] = req.PickupPassword
	}

	if bson.IsObjectIdHex(req.TransferBillId) {
		condition["distribution.transferBillId"] = bson.ObjectIdHex(req.TransferBillId)
		condition["distribution.promoterType"] = ec_distribution.PROMOTER_TYPE_STAFF
	}

	if req.IsDistribution != nil {
		condition["distribution.promoterId"] = bson.M{"$exists": req.IsDistribution.Value}
		if req.IsDistribution.Value {
			condition["paidAt"] = bson.M{"$exists": true}
		}
	}

	return util.FormatConditionContainedOr(condition), nil
}

func formatCampaignCondition(campaignDetail *common.CampaignDetail) bson.M {
	campaignCondition := bson.M{}
	if campaignDetail.Id != "" {
		campaignCondition["id"] = bson.ObjectIdHex(campaignDetail.Id)
	}
	if campaignDetail.Type != "" {
		campaignCondition["type"] = campaignDetail.Type
	}
	if campaignDetail.GrouponRecordId != "" {
		campaignCondition["grouponRecordId"] = bson.ObjectIdHex(campaignDetail.GrouponRecordId)
	}
	if campaignDetail.DiscountId != "" {
		campaignCondition["discountId"] = bson.ObjectIdHex(campaignDetail.DiscountId)
	}
	if campaignDetail.GrouponStatus != "" {
		campaignCondition["grouponStatus"] = campaignDetail.GrouponStatus
	}
	return bson.M{"$elemMatch": campaignCondition}
}

func formatConsignmentOrderProfitSharingStatus(ctx context.Context, order ec_model_order.Order) string {
	bills, _ := ec_client.RetailerService.ListRetailerBills(ctx, &pb_retailer.ListRetailerBillsRequest{
		Type:      "income",
		StoreId:   order.StoreId.Hex(),
		OrderType: "consignment",
		OrderId:   order.Id.Hex(),
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: 10,
		},
	})

	if len(bills.Items) == 0 {
		return "pending"
	}

	isAllPending, hasFailed := true, false
	succeedCount, refundedCount, actualProfitSharingBillCount := 0, 0, 0
	for _, bill := range bills.Items {
		if bill.IncomeType == "paymentFee" {
			continue
		}
		if bill.IncomeType == "receipt" {
			continue
		}

		actualProfitSharingBillCount++
		if bill.Status != "pending" {
			isAllPending = false
		}
		switch bill.Status {
		case "failed":
			hasFailed = true
		case "success":
			succeedCount++
		case "refunded":
			refundedCount++
		}
	}

	if refundedCount == actualProfitSharingBillCount {
		return "refunded"
	}
	if succeedCount == actualProfitSharingBillCount {
		return "success"
	}
	if isAllPending {
		return "pending"
	}
	if hasFailed {
		return "failed"
	}
	return "processing"
}

func shouldUseOrderIndexHint(ctx context.Context, condition bson.M) bool {
	if !ec_model.IsSplitOrderAccount(ctx) {
		return false
	}
	excludeFields := []string{"$or", "$and", "$nor"}
	for _, field := range excludeFields {
		if _, exists := condition[field]; exists {
			return false
		}
	}
	indexFields := []string{"status", "maskType", "createdAt"}
	covered := 2
	for _, field := range indexFields {
		if _, exists := condition[field]; exists {
			covered++
		}
	}

	return covered >= 4
}
