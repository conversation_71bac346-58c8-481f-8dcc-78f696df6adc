package order

import (
	"fmt"
	"strings"
	"time"

	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/account"
	proto_client "mairpc/proto/client"
	"mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	pb_marketing "mairpc/proto/ec/marketing"
	pb_order "mairpc/proto/ec/order"
	pb_setting "mairpc/proto/ec/setting"
	wechat_shop "mairpc/proto/ec/wechatShop"
	"mairpc/service/ec/client"
	ec_client "mairpc/service/ec/client"
	ec_order "mairpc/service/ec/model/order"
	"mairpc/service/ec/model/product"
	ec_settings "mairpc/service/ec/model/setting"
	"mairpc/service/ec/service"
	ec_erp "mairpc/service/ec/service/order/erp"
	"mairpc/service/ec/share"
	"mairpc/service/share/constant"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

const (
	ORDER_REMARK_ALL_PRODUCT_REFUND = "商品全部退款"
	EC_REFUND_ORDER_REDIS_KEY       = "ec:%s:refundOrder:%s" // ec:{accountId}:refundOrder:{orderId}
)

func (orderService OrderService) RefundOrder(ctx context.Context, req *pb_order.RefundOrderRequest) (*pb_order.RefundOrderResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	accountId := util.GetAccountIdAsObjectId(ctx)
	if req.RefundType == "" {
		req.RefundType = ec_order.ORDER_REFUND_TYPE_ONLY_REFUND
	}

	key := fmt.Sprintf(EC_REFUND_ORDER_REDIS_KEY, accountId.Hex(), req.Id)
	ok, _ := extension.RedisClient.SetNX(key, "true", 5)
	if !ok {
		return nil, errors.NewTooManyRequestsError("id")
	}
	defer extension.RedisClient.Del(key)

	order, err := getOrder(ctx, req.Id, req.MemberId)
	if err != nil {
		return nil, err
	}

	operator := getOperator(ctx, order.MemberId.Hex(), req.IsSystem)
	isRefundByMember := !isUserOperator(operator)
	if !order.CanRefund(ctx, isRefundByMember, req.WechatRefundId) {
		return nil, errors.NewInvalidArgumentErrorWithMessage("id", "Order cannot be refunded")
	}
	// 订单已发货时用户主动退款打印小票
	isNeedPrintRefundTicket := !core_util.ContainsString(&ec_order.NOT_NEED_PRINT_REFUND_TICKET_ORDER_STATUS, order.Status) ||
		isRefundByMember && order.Status == ec_order.ORDER_STATUS_SHIPPED

	toRefundStatus, err := getToRefundStatus(ctx, order, req, req.IsSystem)
	if err != nil {
		return nil, err
	}

	orderRefundId := bson.NewObjectId()
	refundProducts, err := genRefundProducts(ctx, &order, toRefundStatus, req, isRefundByMember, orderRefundId)
	if err != nil {
		return nil, err
	}

	// isAllAquireRefund 是否所有商品都被退款
	// isAllUnRefundShipped 是否所有未退款的商品都已经发货
	isAllAquireRefund, isAllUnRefundShipped, err := getIsAllAquireRefundAndIsAllUnRefundShipped(ctx, &order, req.IsCancelOrder)
	if err != nil {
		return nil, err
	}

	var (
		needSyncToOMS bool
		needAudit     bool
	)
	if toRefundStatus == ec_order.ORDER_REFUND_STATUS_PENDING {
		needAudit, needSyncToOMS, err = CheckErpShipped(ctx, order, refundProducts)
		if err != nil {
			return nil, err
		}
		if needAudit {
			toRefundStatus = ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT
			refundOutTradeIds := core_util.ToStringArray(core_util.ExtractArrayField("OutTradeId", refundProducts))
			for i := range order.Products {
				if core_util.StrInArray(order.Products[i].OutTradeId.Hex(), &refundOutTradeIds) {
					order.Products[i].RefundStatus = toRefundStatus
				}
			}
		}
	}

	refundExtra := genRefundExtra(req, toRefundStatus, isAllAquireRefund, refundProducts)
	orderRefund, err := generateBasicOrderRefund(ctx, order, req, refundProducts, orderRefundId, refundExtra, needSyncToOMS)
	if err != nil {
		return nil, err
	}
	defer func() {
		if order.Invoice.Needed {
			listInvoicesResp, _ := client.OrderService.ListInvoices(ctx, &pb_order.ListInvoicesRequest{
				OrderIds: []string{order.Id.Hex()},
				Status:   []string{ec_order.INVOICE_STATUS_INVOICING, ec_order.INVOICE_STATUS_ISSUED, ec_order.INVOICE_STATUS_CLOSED},
				IsRed:    false,
			})
			if listInvoicesResp != nil && len(listInvoicesResp.Items) > 0 {
				_, err := client.OrderService.CreateInvoice(ctx, &pb_order.CreateInvoiceRequest{
					OrderId: order.Id.Hex(),
					Invoice: &ec.InvoiceRequest{
						Type:            listInvoicesResp.Items[0].Type,
						Name:            listInvoicesResp.Items[0].Name,
						TaxID:           listInvoicesResp.Items[0].TaxID,
						Address:         listInvoicesResp.Items[0].Address,
						Phone:           listInvoicesResp.Items[0].Phone,
						BankName:        listInvoicesResp.Items[0].BankName,
						BankAccount:     listInvoicesResp.Items[0].BankAccount,
						InvoiceType:     listInvoicesResp.Items[0].InvoiceType,
						InvoiceContents: listInvoicesResp.Items[0].InvoiceContents,
					},
					MemberId: order.MemberId.Hex(),
					IsRed:    true,
				})
				if err != nil {
					log.Warn(ctx, "Failed to create red invoice for refund order", log.Fields{
						"errMsg":  err.Error(),
						"orderId": order.Id.Hex(),
					})
				}
				_, err = ec_order.COrderRefund.UpdateInvoiceStatus(ctx, order.Id, ec_order.INVOICE_STATUS_ISSUED)
				if err != nil {
					log.Warn(ctx, "Failed to update invoice status for refund order", log.Fields{
						"errMsg":  err.Error(),
						"orderId": order.Id.Hex(),
					})
				}
			}
		}
	}()

	if order.Status == ec_order.ORDER_STATUS_SHIPPED && order.Operator == ec_settings.DELIVERY_SETTING_OPERATOR_STAFF {
		order.NotifyStaffs(ctx, constant.MESSAGE_RULE_SHIPPED_APPLY_REFUND)
	}

	{ // 注意：逻辑原因，必须先更新 order 再创建 orderRefund
		err = updateOrder(ctx, order, *orderRefund, isAllUnRefundShipped, operator)
		if err != nil {
			return nil, err
		}
		err = ec_order.CLogistics.UpdateProductRefundStatusByOrder(ctx, &order)
		if err != nil {
			return nil, err
		}
		// 创建退款记录
		err = orderRefund.Create(ctx)
		if err != nil {
			return nil, err
		}

		createRefundAuditLog(ctx, req, operator, orderRefund.OrderNumber)
	}
	// 发起退款事件
	orderRefund.SendRefundApplyEvent(ctx, order, bson.M{
		"wechatRefundId": req.WechatRefundId,
	})

	if toRefundStatus == ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT && order.Operator == ec_settings.DELIVERY_SETTING_OPERATOR_STAFF {
		orderRefund.CreateNotification(ctx, toRefundStatus)
	}
	if isNeedPrintRefundTicket && order.Operator == ec_settings.DELIVERY_SETTING_OPERATOR_STAFF {
		ec_client.OrderService.PrintRefundTicket(ctx, &pb_order.PrintTicketRequest{
			OrderId:  orderRefund.Id.Hex(),
			MemberId: orderRefund.MemberId.Hex(),
			StoreId:  orderRefund.StoreId.Hex(),
		})
	}

	if orderRefund.RefundExtra.NeedAudit {
		err := orderRefund.UpdateRefundStatus(ctx, nil)
		if err != nil {
			log.Error(ctx, "Failed to update order refund", log.Fields{
				"id":           orderRefund.Id.Hex(),
				"errorMessage": err.Error(),
			})
		}
	}

	// 如果订单中已有虚拟商品核销，且退款所有未使用的虚拟商品，则发送虚拟全部已核销事件
	if order.GetVirtualType() == ec_order.ORDER_TYPE_VIRTUAL {
		existRedeemedProduct := false
		for _, p := range order.Products {
			if ec_order.CheckProductCampaignExistence(p, ec_order.CAMPAIGN_TYPE_PRESENT) {
				continue
			}
			if len(p.Logistics) > 0 {
				existRedeemedProduct = true
				break
			}
		}
		if existRedeemedProduct && IsVirtualProductOrderRedeemedAll(ctx, order) {
			order.SendVirtualProductOrderRedeemedAllEvent(ctx)
		}
	}

	return &pb_order.RefundOrderResponse{
		Id: orderRefundId.Hex(),
	}, nil
}

func getOrder(ctx context.Context, orderId, memberId string) (ec_order.Order, error) {
	// 查询需要退款的订单
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       bson.ObjectIdHex(orderId),
		"memberId":  bson.ObjectIdHex(memberId),
		"status": bson.M{
			"$in": []string{
				ec_order.ORDER_STATUS_PAID,
				ec_order.ORDER_STATUS_UNASSIGNED,
				ec_order.ORDER_STATUS_ACCEPTED,
				ec_order.ORDER_STATUS_COMPLETED,
				ec_order.ORDER_STATUS_PARTIAL_SHIPPED,
				ec_order.ORDER_STATUS_SHIPPED,
				ec_order.ORDER_STATUS_UNPAID, // 预售订单支持后台退定金，尾款未付时 status 为 unpaid
			},
		},
		"isDeleted": false,
	}
	order := ec_order.Order{}
	err := ec_order.Common.GetByCondition(ctx, condition, ec_order.C_ORDER, &order)
	if err != nil {
		return order, errors.NewNotExistsError("id")
	}
	return order, nil
}

func getIsAllAquireRefundAndIsAllUnRefundShipped(ctx context.Context, order *ec_order.Order, isCancelOrder bool) (bool, bool, error) {
	// 是否所有商品都被退款
	isAllAquireRefund := true
	// 是否所有未退款的商品都已经发货
	isAllUnRefundShipped := order.Status == ec_order.ORDER_STATUS_PARTIAL_SHIPPED
	if isCancelOrder {
		return isAllAquireRefund, isAllUnRefundShipped, nil
	}

	for _, product := range order.Products {
		// 如果商品已全部退款，买赠券的赠品必定不满足条件，如果商品未全部退款，无论赠品退不退不影响 isAllAquireRefund 的值
		if ec_order.CheckProductCampaignExistence(product, ec_order.CAMPAIGN_TYPE_PRESENT_COUPON) {
			continue
		}
		if core_util.StrInArray(product.RefundStatus, &[]string{"", ec_order.ORDER_REFUND_STATUS_CANCELED}) {
			if ec_order.CheckProductCampaignExistence(product, ec_order.CAMPAIGN_TYPE_PRESENT) {
				discountOrderAmount, err := getCampaignDiscountOrderAmount(ctx, product)
				if err != nil {
					return false, false, err
				}
				// 如果赠品是满 0 元即赠送，在部分商品退款的情况下一定不会被退款，仍然可以正常发货
				// 而非 0 元赠送的赠品在所有商品退款后一定会回退库存
				if discountOrderAmount == 0 {
					isAllAquireRefund = false
				}
			} else {
				isAllAquireRefund = false
			}
		}
		// 判断 isAllUnRefundShipped
		if ec_order.IsInRefundProcess(product.RefundStatus) || ec_order.CheckProductCampaignExistence(product, ec_order.CAMPAIGN_TYPE_PRESENT) {
			continue // 已在退款流程的和买赠活动赠品不考虑
		}
		for _, l := range product.Logistics {
			product.Total -= l.ProductCount
		}
		if product.Total != 0 {
			isAllUnRefundShipped = false
		}
		if !isAllAquireRefund && !isAllUnRefundShipped {
			break
		}
	}
	return isAllAquireRefund, isAllUnRefundShipped, nil
}

func genRefundProducts(ctx context.Context, order *ec_order.Order, toRefundStatus string, req *pb_order.RefundOrderRequest, isRefundByMember bool, orderRefundId bson.ObjectId) ([]ec_order.RefundProduct, error) {
	refundProducts := []ec_order.RefundProduct{}
	if req.IsCancelOrder {
		for index, orderProduct := range order.Products {
			if ec_order.IsInRefundProcess(orderProduct.RefundStatus) {
				continue
			}

			orderProduct.RefundStatus = toRefundStatus
			orderProduct.OrderRefundId = orderRefundId
			order.Products[index] = orderProduct

			refundProducts = append(refundProducts, ec_order.ConvertOrderProductToRefundProduct(orderProduct))
		}
		return refundProducts, nil
	}

	// 仅退款运费
	if req.IsRefundDeliveryFee && len(req.ProductSkus) == 0 {
		return refundProducts, nil
	}

	refundAmount := req.RefundAmount
	if refundAmount != 0 && len(order.TradeRecords) != 0 {
		// 预售活动特殊处理
		if order.Status != ec_order.ORDER_STATUS_UNPAID {
			// 自定义退款金额，优先退尾款上的金额，多于尾款的部分用于退定金
			if req.RefundAmount >= order.PayAmount-order.Logistics.Fee {
				refundAmount = order.PayAmount - order.Logistics.Fee
			}
		}
	}

	var err error
	refundProducts, err = getRefundProductsBySku(ctx, order, req.ProductSkus, isRefundByMember, refundAmount, req.VirtualRefundType)
	if err != nil {
		return nil, err
	}
	if len(refundProducts) == 0 {
		return nil, errors.NewNotExistsError("productSkus")
	}
	for _, refundProduct := range refundProducts {
		for index, orderProduct := range order.Products {
			if orderProduct.OutTradeId.Hex() != refundProduct.OutTradeId.Hex() {
				continue
			}
			orderProduct.OrderRefundId = orderRefundId
			orderProduct.RefundStatus = toRefundStatus
			order.Products[index] = orderProduct
			break
		}
	}
	return refundProducts, nil
}

func handleUnusedVirtualProductRefund(ctx context.Context, memberId string, orderProduct *ec_order.OrderProduct) (uint64, error) {
	var (
		unusedTotal uint64 = 0
		err         error
	)
	switch orderProduct.Type {
	case product.C_PRODUCT_TYPE_COUPON:
		if orderProduct.SubType == product.C_PRODUCT_SUB_TYPE_STORED_VALUE_CARD {
			unusedTotal = orderProduct.Total
		} else {
			unusedTotal, err = ec_order.RefundCouponProduct(ctx, memberId, orderProduct, orderProduct.Total)
			if err != nil {
				break
			}
		}
	case product.C_PRODUCT_TYPE_VIRTUAL:
		var usedTotal uint64 = 0
		for _, logistic := range orderProduct.Logistics {
			usedTotal += logistic.ProductCount
		}
		unusedTotal = uint64(len(orderProduct.PickupCodes)) - usedTotal
	}
	return unusedTotal, err
}

func genRefundExtra(req *pb_order.RefundOrderRequest, toRefundStatus string, isAllAquireRefund bool, refundProducts []ec_order.RefundProduct) ec_order.Extra {
	refundExtra := ec_order.Extra{
		NeedAudit:          toRefundStatus == ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT,
		IsCancelOrder:      req.IsCancelOrder,
		IsAllAquireRefund:  isAllAquireRefund,
		AssignToFirstStore: req.AssignToFirstStore,
		RefundProductSkus:  req.ProductSkus,
		RefundProducts:     refundProducts,
	}
	refundExtra.SetIsAllRefund(isAllAquireRefund && toRefundStatus == ec_order.ORDER_REFUND_STATUS_PENDING)
	return refundExtra
}

func generateBasicOrderRefund(ctx context.Context, order ec_order.Order, req *pb_order.RefundOrderRequest, refundProducts []ec_order.RefundProduct, orderRefundId bson.ObjectId, refundExtra ec_order.Extra, needSyncToOMS bool) (*ec_order.OrderRefund, error) {
	setting, err := ec_client.SettingService.GetSettings(ctx, &pb_setting.GetSettingsRequest{})
	if err != nil {
		return nil, err
	}
	if len(order.PrepaidCards) > 0 || order.StoredValue.Amount > 0 {
		// 自定义退款金额
		if req.RefundAmount > 0 {
			shareRefundProductRefundAmount(order, refundProducts)
		} else {
			genRefundProductPrepaidCardRefund(order, refundProducts)
		}
	}
	refundAmount, isContainExpressFee := calRefundProductsAmount(ctx, order, refundProducts, refundExtra.IsAllAquireRefund, req.IsRefundDeliveryFee)

	orderRefund := &ec_order.OrderRefund{
		Id:            orderRefundId,
		AccountId:     order.AccountId,
		ChannelId:     order.Channel.ChannelId,
		ChannelOrigin: order.Channel.Origin,
		MemberId:      order.MemberId,
		Contact: ec_order.OrderRefundContact{
			Name:  order.Contact.Name,
			Phone: order.Contact.Phone,
		},
		OrderId:                  order.Id,
		StoreId:                  order.StoreId,
		DistributorIds:           order.DistributorIds,
		TransactionId:            order.TradeNo,
		OrderNumber:              order.Number,
		OrderTotalFee:            order.PayAmount,
		Method:                   order.Method,
		Operator:                 order.Operator,
		RefundType:               req.RefundType,
		Products:                 refundProducts,
		RefundAmount:             refundAmount,
		PayAmount:                order.PayAmount,
		Status:                   ec_order.ORDER_REFUND_STATUS_PENDING,
		OrderStatus:              order.Status,
		Reason:                   req.Reason,
		Description:              req.Description,
		OrderCreatedAt:           order.CreatedAt,
		OrderPaidAt:              order.PaidAt,
		RefundExtra:              refundExtra,
		Campaigns:                order.Campaigns,
		Tags:                     order.Tags,
		PaymentProvider:          setting.PaymentProvider,
		CustomRefundAmountReason: req.CustomRefundAmountReason,
		OrderOutTradeId:          order.OutTradeId,
		StoredValue:              order.StoredValue,
		Remarks:                  req.Remarks,
	}

	if util.StrInArray(ec_order.ORDER_TAGS_NOT_DIVIDE, &orderRefund.Tags) {
		orderRefund.Tags = append(orderRefund.Tags, ec_order.ORDER_REFUND_TAGS_WAITING_DIVIDE_REVERSE)
	}

	if len(order.PrepaidCards) > 0 {
		// 计算每张礼卡应退金额
		for _, refundProduct := range orderRefund.Products {
			for _, productPrepaidRefund := range refundProduct.PrepaidCardRefunds {
				match := false
				// orderRefund 退款单上已存在退款的 prepaidCard 时， 加上商品应退的礼品卡金额 productPrepaidRefund.RefundAmount
				for i := range orderRefund.PrepaidCardRefunds {
					if orderRefund.PrepaidCardRefunds[i].PrepaidCardId == productPrepaidRefund.PrepaidCardId {
						match = true
						orderRefund.PrepaidCardRefunds[i].RefundAmount += productPrepaidRefund.RefundAmount
					}
				}
				// orderRefund 退款单上不存在退款的 prepaidCard，则添加
				if !match {
					orderPrepaidCard := ec_order.OrderPrepaidCard{}
					for i, item := range order.PrepaidCards {
						if item.Id == productPrepaidRefund.PrepaidCardId {
							orderPrepaidCard = order.PrepaidCards[i]
						}
					}
					orderRefund.PrepaidCardRefunds = append(orderRefund.PrepaidCardRefunds, ec_order.PrepaidCardRefund{
						Id:            bson.NewObjectId(),
						PrepaidCardId: productPrepaidRefund.PrepaidCardId,
						Number:        productPrepaidRefund.Number,
						Amount:        orderPrepaidCard.Amount,
						OriginTradeNo: orderPrepaidCard.TradeNo,
						TradeNo:       bson.NewObjectId().Hex(),
						RefundAmount:  productPrepaidRefund.RefundAmount,
						Status:        ec_order.ORDER_REFUND_STATUS_PENDING,
						OutTradeSeq:   orderPrepaidCard.OutTradeSeq,
						TransactionId: orderPrepaidCard.TransactionId,
					})
				}
			}
		}
	}

	if order.StoredValue.Amount > 0 {
		orderRefund.RefundStoredValue = share_model.RefundStoredValue{
			TradeNo:       bson.NewObjectId().Hex(),
			TransactionId: order.StoredValue.TransactionId,
		}
		for _, refundProduct := range orderRefund.Products {
			orderRefund.RefundStoredValue.Amount += refundProduct.RefundStoredValue.Amount
		}
		if isContainExpressFee {
			orderRefund.RefundStoredValue.DeliveryAmount = order.StoredValue.DeliveryAmount
			orderRefund.RefundStoredValue.Amount += order.StoredValue.DeliveryAmount
		}
	}

	if isContainExpressFee {
		orderRefund.ExpressFee = order.Logistics.Fee
	}

	if setting.PaymentProvider == "" {
		orderRefund.PaymentProvider = ec_order.PAYMENT_PROVIDER_MAI
	}

	if refundExtra.NeedAudit {
		orderRefund.Status = ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT
	}
	if req.DeliveryStatus != "" {
		orderRefund.DeliveryStatus = req.DeliveryStatus
	}

	if bson.IsObjectIdHex(core_util.GetUserId(ctx)) {
		orderRefund.Type = ec_order.ORDER_REFUND_TYPE_PORTAL
	} else {
		orderRefund.Type = ec_order.ORDER_REFUND_TYPE_MEMBER
	}

	if number, err := generateUniqueCode("refund-order", "R"); err != nil {
		return nil, err
	} else {
		orderRefund.Number = number
	}

	if order.Invoice.Needed {
		orderRefund.InvoiceStatus = ec_order.ORDER_REFUND_STATUS_PENDING
	}

	extra := bson.M{
		"productSkus": req.ProductSkus,
		"refundType":  req.RefundType,
		"memberName": func(histories []ec_order.OrderHistory) string {
			nickName := ""
			for _, history := range histories {
				if history.Extra != nil {
					if val, ok := history.Extra["memberName"]; ok {
						nickName = val.(string)
					}
				}
			}
			return nickName
		}(order.Histories),
	}
	orderRefund.GenerateHistory(getOperator(ctx, req.MemberId, req.IsSystem), req.Reason, extra)
	orderRefund.Histories[len(orderRefund.Histories)-1].Description = req.Description
	if req.Certificate != nil && (len(req.Certificate.Images) > 0 || req.Certificate.Video.Url != "") {
		certificate := ec_order.Certificate{}
		copier.Instance(nil).From(req.Certificate).CopyTo(&certificate)
		orderRefund.Certificate = certificate
		orderRefund.Histories[len(orderRefund.Histories)-1].Certificate = certificate
	}

	if needSyncToOMS {
		orderRefund.OmsProcessor.Status = ec_order.OMS_ORDER_PUSH_STATUS_PENDING
		orderRefund.OmsProcessor.RefundNo = bson.NewObjectId().Hex()
	}

	if core_util.StrInArray(ec_order.ORDER_TAGS_WECHAT_SHOP, &order.Tags) {
		orderRefund.Tags = append(orderRefund.Tags, ec_order.ORDER_TAGS_WECHAT_SHOP)
	}

	// 生成定金退款记录
	genTradeRecordRefunds(&order, orderRefund, req)

	return orderRefund, nil
}

func updateOrder(ctx context.Context, order ec_order.Order, orderRefund ec_order.OrderRefund, isAllUnRefundShipped bool, operator string) error {
	if orderRefund.RefundExtra.AssignToFirstStore && order.Status == ec_order.ORDER_STATUS_UNASSIGNED {
		order.StoreId = order.Histories[0].StoreId
		order.Status = ec_order.ORDER_STATUS_PAID
	}
	if isAllUnRefundShipped && order.IsNotVirtualAndCouponProduct() {
		order.Status = ec_order.ORDER_STATUS_SHIPPED
		order.Logistics.ShippedAt = time.Now()
	}
	if orderRefund.ExpressFee != 0 {
		order.IsDeliveryFeeRefunded = true
	}
	generateOrderHistory(&order, operator, orderRefund.Reason, bson.M{})
	err := order.UpdateWhenRefund(ctx)
	if err != nil {
		return err
	}
	return nil
}

func createRefundAuditLog(ctx context.Context, req *pb_order.RefundOrderRequest, operator, orderNumber string) {
	if !req.IsCancelOrder && isUserOperator(operator) && !req.IsSystem {
		share.CreateAuditLogByCoroutine(ctx, &account.CreateAuditLogRequest{
			OperatorId:       core_util.GetUserId(ctx),
			OperationObject:  "订单",
			OperationDetails: fmt.Sprintf("将订单「%s」发起退款", orderNumber),
			AppId:            "retail",
		})
	}
}

func shareRefundProductRefundAmount(order ec_order.Order, refundProducts []ec_order.RefundProduct) {
	for i := range refundProducts {
		for _, orderProduct := range order.Products {
			if refundProducts[i].OutTradeId != orderProduct.OutTradeId || (len(orderProduct.PrepaidCards) == 0 && orderProduct.StoredValue.Amount == 0) {
				continue
			}
			var (
				productTotalPayAmount        uint64 // 总支付金额（微信+储值卡+储值余额支付）
				prepaidCardTotalPayAmount    uint64 // 储值卡总支付
				productWechatRefundAmount    uint64 // 微信应退金额
				prepaidCardTotalRefundAmount uint64 // 储值卡应退金额
				storedValueTotalRefundAmount uint64 // 储值余额应退金额
			)
			productTotalPayAmount = orderProduct.PayAmount + orderProduct.StoredValue.Amount
			for _, item := range orderProduct.PrepaidCards {
				prepaidCardTotalPayAmount += item.Amount
				productTotalPayAmount += item.Amount
			}
			productWechatRefundAmount = uint64(util.DivideFloatWithRound(util.MultiplyFloatWithRound(float64(refundProducts[i].RefundAmount), float64(orderProduct.PayAmount), 0), float64(productTotalPayAmount), 0))

			prepaidCardTotalRefundAmount = uint64(util.DivideFloatWithRound(util.MultiplyFloatWithRound(float64(refundProducts[i].RefundAmount), float64(prepaidCardTotalPayAmount), 0), float64(productTotalPayAmount), 0))
			storedValueTotalRefundAmount = refundProducts[i].RefundAmount - productWechatRefundAmount - prepaidCardTotalRefundAmount
			refundProducts[i].RefundAmount = productWechatRefundAmount
			refundProducts[i].RefundStoredValue = share_model.RefundStoredValue{
				Amount: storedValueTotalRefundAmount,
			}
			if prepaidCardTotalRefundAmount == 0 {
				continue
			}
			shareAmount := prepaidCardTotalRefundAmount
			// 每张卡分摊应退金额
			for j, prepaidCard := range orderProduct.PrepaidCards {
				prepaidCardRefundAmount := uint64(0)
				if j == len(orderProduct.PrepaidCards)-1 {
					prepaidCardRefundAmount = shareAmount
				} else {
					prepaidCardRefundAmount = uint64(util.DivideFloatWithRound(util.MultiplyFloatWithRound(float64(prepaidCardTotalRefundAmount), float64(prepaidCard.Amount), 0), float64(prepaidCardTotalPayAmount), 0))
					shareAmount -= prepaidCardRefundAmount
				}
				refundProducts[i].PrepaidCardRefunds = append(refundProducts[i].PrepaidCardRefunds, ec_order.ProductPrepaidCardRefund{
					PrepaidCardId: prepaidCard.Id,
					Number:        prepaidCard.Number,
					RefundAmount:  prepaidCardRefundAmount,
					Amount:        prepaidCard.Amount,
				})
			}
		}
	}
}

func genRefundProductPrepaidCardRefund(order ec_order.Order, refundProducts []ec_order.RefundProduct) {
	for i := range refundProducts {
		for _, orderProduct := range order.Products {
			if refundProducts[i].OutTradeId != orderProduct.OutTradeId {
				continue
			}
			refundProducts[i].RefundStoredValue = share_model.RefundStoredValue{
				TradeNo: orderProduct.StoredValue.TradeNo,
				Amount:  orderProduct.StoredValue.Amount,
			}
			for _, prepaidCard := range orderProduct.PrepaidCards {
				refundProducts[i].PrepaidCardRefunds = append(refundProducts[i].PrepaidCardRefunds, ec_order.ProductPrepaidCardRefund{
					PrepaidCardId: prepaidCard.Id,
					Number:        prepaidCard.Number,
					RefundAmount:  prepaidCard.Amount,
					Amount:        prepaidCard.Amount,
				})
			}
		}
	}
}

func getRefundProductsBySku(ctx context.Context, order *ec_order.Order, productSkus []*pb_order.ProductSKU, isRefundByMember bool, refundAmount uint64, virtualRefundType string) ([]ec_order.RefundProduct, error) {
	// 有套餐商品的存在导致通过 productId 和 sku 无法定位商品，需要借助 PackageId
	refundProducts := []ec_order.RefundProduct{}
	for _, productSku := range productSkus {
		for _, orderProduct := range order.Products {
			if bson.IsObjectIdHex(productSku.OutTradeId) {
				if productSku.OutTradeId != orderProduct.OutTradeId.Hex() {
					continue
				}
			} else {
				if orderProduct.Id.Hex() != productSku.Id {
					continue
				}
				if orderProduct.Spec.Sku != productSku.Sku {
					continue
				}
				if isRefundByMember && orderProduct.DisableMemberRefund {
					continue
				}
				if productSku.PackageId != "" {
					tempPackageIds := core_util.ToStringArray(core_util.ExtractArrayField("PackageId", orderProduct.Campaigns))
					if !core_util.StrInArray(productSku.PackageId, &tempPackageIds) {
						continue
					}
				} else {
					if ec_order.CheckProductCampaignExistence(orderProduct, ec_order.CAMPAIGN_TYPE_PACKAGE) {
						continue
					}
				}
				// 买赠活动赠品不可退款，productSkus 中不会有买赠活动赠品的 sku
				// 为了防止取到和退款商品相同规格的买赠活动赠品，这里将其筛选排除
				if ec_order.CheckProductCampaignExistence(orderProduct, ec_order.CAMPAIGN_TYPE_PRESENT) {
					continue
				}
			}
			if !core_util.StrInArray(orderProduct.RefundStatus, &[]string{"", ec_order.ORDER_REFUND_STATUS_CANCELED}) {
				return nil, errors.NewInvalidArgumentErrorWithMessage("productSkus", "Contains refunded product sku")
			}
			refundProduct := ec_order.ConvertOrderProductToRefundProduct(orderProduct)
			refundProducts = append(refundProducts, refundProduct)
		}
	}
	if len(refundProducts) == 0 {
		return nil, errors.NewInvalidArgumentErrorWithMessage("products", "There is no refundable products")
	}
	if order.IsVirtual() {
		for rpIndex, rp := range refundProducts {
			for i, op := range order.Products {
				if rp.OutTradeId != op.OutTradeId {
					continue
				}
				if ec_order.CheckProductCampaignExistence(op, ec_order.CAMPAIGN_TYPE_PRESENT) {
					continue
				}
				rp.Total = op.Total
				refundTotal, err := handleUnusedVirtualProductRefund(ctx, order.MemberId.Hex(), &op)
				if err != nil {
					return nil, err
				}
				if virtualRefundType == ec_order.VIRTUAL_ORDER_REFUND_TYPE_UNUSED {
					rp.Total = refundTotal
				}
				order.Products[i].RefundTotal = refundTotal
				if rp.Total == op.Total {
					rp.PayAmount = op.PayAmount
				} else {
					singlePrice := float64(op.PayAmount) / float64(op.Total)
					rp.PayAmount = uint64(float64(rp.Total) * singlePrice)
				}
				rp.RefundAmount = rp.PayAmount
				refundProducts[rpIndex] = rp
			}
		}
	}
	if refundAmount != 0 {
		// 自定义退款金额不能大于应退金额
		totalRefundAmount := uint64(0)
		for i, refundProduct := range refundProducts {
			for _, orderProduct := range order.Products {
				if orderProduct.OutTradeId != refundProduct.OutTradeId {
					continue
				}
				for _, item := range orderProduct.PrepaidCards {
					refundProducts[i].RefundAmount += item.Amount
				}
				refundProducts[i].RefundAmount += orderProduct.StoredValue.Amount
			}
			totalRefundAmount += refundProducts[i].RefundAmount
		}
		if totalRefundAmount < refundAmount {
			return nil, errors.NewInvalidArgumentErrorWithMessage("refundAmount", "refundAmount is more than payAmount")
		}
		// 按应退金额分摊自定义退款金额
		shareAmount := refundAmount
		for i := range refundProducts {
			if i == len(refundProducts)-1 {
				// 最后一个商品直接为分摊完的剩余金额
				refundProducts[i].RefundAmount = shareAmount
				break
			}
			tmpAmount := shareAmount * refundProducts[i].RefundAmount / totalRefundAmount
			refundProducts[i].RefundAmount = tmpAmount
			shareAmount -= tmpAmount
		}
	}
	return refundProducts, nil
}

func isUserOperator(operator string) bool {
	return strings.HasPrefix(operator, "user")
}

func getOperator(ctx context.Context, memberId string, isSystem bool) string {
	operator := "member:" + memberId
	operatorId := core_util.GetUserId(ctx)
	if bson.IsObjectIdHex(operatorId) {
		operator = "user:" + operatorId
	}
	if isSystem {
		operator = "user:system"
	}
	return operator
}

func getOperatorName(ctx context.Context, memberId string) string {
	if memberId != "" {
		member, err := GetMember(ctx, memberId)
		if err != nil {
			return ""
		}

		return member.Name
	}

	operatorId := core_util.GetUserId(ctx)
	resp, _ := service.GetUserByIds(ctx, []string{operatorId})
	if resp != nil && len(resp.Items) > 0 {
		return resp.Items[0].Name
	}

	return ""
}

func calRefundProductsAmount(ctx context.Context, order ec_order.Order, products []ec_order.RefundProduct, isAllAquireRefund, isRefundDeliveryFee bool) (uint64, bool) {
	var refundAmount uint64
	isContainExpressFee := false
	for _, product := range products {
		refundAmount += product.RefundAmount
	}
	// 预售活动退定金，refundAmount 为定金金额
	if len(order.TradeRecords) > 0 && order.Status == ec_order.ORDER_STATUS_UNPAID {
		refundAmount = 0
		for _, t := range order.TradeRecords {
			refundAmount += t.PayAmount
		}
	}

	if order.IsDeliveryFeeRefunded {
		return refundAmount, isContainExpressFee
	}
	// 视频号场景中，是否退运费只取决于是否发货
	if resp, _ := ec_client.WechatShopService.GetWechatOrder(ctx, &wechat_shop.GetWechatOrderRequest{OrderId: order.Id.Hex()}); resp != nil {
		isRefundDeliveryFee = false
	}

	// 物流订单，未发货时 SKU 全退才退运费，已发货则不退运费
	if isRefundDeliveryFee || (util.StrInArray(order.Status, &ec_order.REFUND_LOGISTICS_FEE_STATUS) && isAllAquireRefund) {
		switch order.Method {
		case ec_order.ORDER_DELIVERY_METHOD_EXPRESS, ec_order.ORDER_DELIVERY_METHOD_CITY_EXPRESS:
			refundAmount += order.Logistics.Fee - order.StoredValue.DeliveryAmount
			isContainExpressFee = true
		}
	}
	return refundAmount, isContainExpressFee
}

// 判断此次退款 refundStatus 应该变为什么状态
func getToRefundStatus(ctx context.Context, order ec_order.Order, req *pb_order.RefundOrderRequest, isSystem bool) (string, error) {
	userId := core_util.GetUserId(ctx)
	if userId != "" || isSystem {
		return ec_order.ORDER_REFUND_STATUS_PENDING, nil
	}

	// 如果订单已完成，售后需要审核，目前只有微信侧售后回调会进入此分支
	if order.Status == ec_order.ORDER_STATUS_COMPLETED {
		return ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT, nil
	}

	// 根据交易设置判断是否需要审核

	orderSetting, err := ec_client.SettingService.GetOrderSetting(ctx, &request.EmptyRequest{})
	if err != nil {
		return "", errors.NewNotExistsError("orderSetting")
	}
	switch orderSetting.RefundNeedAuditStatus {
	case ec_settings.NEED_AUDIT_STATUS_ACCEPTED:
		if order.Status == ec_order.ORDER_STATUS_ACCEPTED || order.Status == ec_order.ORDER_STATUS_SHIPPED || order.Status == ec_order.ORDER_STATUS_PARTIAL_SHIPPED {
			if order.Method == ec_order.ORDER_DELIVERY_METHOD_PICKUP {
				switch orderSetting.RefundControl {
				case ec_settings.REFUND_CONTROL_ALLOW:
					return ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT, nil
				case ec_settings.REFUND_CONTROL_NOT_ALLOW:
					return "", errors.NewInvalidArgumentErrorWithMessage("orderSetting", "待提货订单无法退款")
				}
			}
			return ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT, nil
		}
	default:
		if order.Status == ec_order.ORDER_STATUS_SHIPPED {
			return ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT, nil
		}
	}

	if order.Status == ec_order.ORDER_STATUS_PARTIAL_SHIPPED {
		if req.IsCancelOrder {
			return "", errors.NewInvalidArgumentErrorWithMessage("id", "Invalid operation on order")
		}

		refundedUniqueIds := []string{}
		for _, p := range req.ProductSkus {
			key := fmt.Sprintf("%s%s", p.Id, p.Sku)
			refundedUniqueIds = append(refundedUniqueIds, key, p.OutTradeId)
		}
		isAllShipped, isAllUnShipped := true, true
		for _, p := range order.Products {
			// 退款不包含赠品，因此不检查赠品的状态
			if ec_order.CheckProductCampaignExistence(p, ec_order.CAMPAIGN_TYPE_PRESENT) {
				continue
			}
			key := fmt.Sprintf("%s%s", p.Id.Hex(), p.Spec.Sku)
			if !util.StrInArray(key, &refundedUniqueIds) && !util.StrInArray(p.OutTradeId.Hex(), &refundedUniqueIds) {
				continue
			}

			if len(p.Logistics) > 0 {
				isAllUnShipped = false
			} else {
				isAllShipped = false
			}
		}
		if !isAllShipped && !isAllUnShipped { // 不允许混合请求退款
			return "", errors.NewInvalidArgumentErrorWithMessage("id", "Invalid operation on order")
		}
		if isAllShipped {
			return ec_order.ORDER_REFUND_STATUS_WAITINGAUDIT, nil
		}
	}
	return ec_order.ORDER_REFUND_STATUS_PENDING, nil
}

func getCampaignDiscountOrderAmount(ctx context.Context, product ec_order.OrderProduct) (uint64, error) {
	for _, campaign := range product.Campaigns {
		if campaign.Type == ec_order.CAMPAIGN_TYPE_PRESENT {
			presentCampaign, err := ec_client.MarketingService.GetPresentCampaign(ctx, &pb_marketing.GetPresentCampaignRequest{
				Id: campaign.Id.Hex(),
			})
			if err != nil {
				return 0, err
			}
			for _, discount := range presentCampaign.Discounts {
				if discount.DiscountId != campaign.DiscountId.Hex() {
					continue
				}
				if presentCampaign.Type == "piece" {
					return discount.ProductCount, nil
				}
				return discount.OrderAmount, nil
			}
		}
	}

	return 0, errors.NewNotExistsError("presentCampaign")
}

func genTradeRecordRefunds(order *ec_order.Order, orderRefund *ec_order.OrderRefund, req *pb_order.RefundOrderRequest) {
	// 退款商品为空时，仅退运费，不需要处理定金
	if len(orderRefund.Products) == 0 {
		return
	}
	if len(order.TradeRecords) == 0 {
		return
	}
	totalTradeAmount := uint64(0)
	for _, t := range order.TradeRecords {
		totalTradeAmount += t.PayAmount
	}

	for _, t := range order.TradeRecords {
		refundAmount := t.PayAmount
		if req.RefundAmount != 0 {
			if order.Status == ec_order.ORDER_STATUS_UNPAID {
				// 只退定金
				// TODO 当存在多个 tradeRecord 时，需要处理取整后需要分摊金额的问题。
				refundAmount = req.RefundAmount * t.PayAmount / totalTradeAmount
			} else {
				if req.RefundAmount <= order.PayAmount-order.Logistics.Fee {
					continue
				}
				// 自定义退款金额多于尾款的部分用于退定金
				refundAmount = (req.RefundAmount - (order.PayAmount - order.Logistics.Fee)) * t.PayAmount / totalTradeAmount
				if refundAmount <= 0 {
					continue
				}
			}
			orderRefund.Products[0].RefundAmount += refundAmount
			orderRefund.RefundAmount += refundAmount
		}
		tradeRecordRefund := ec_order.TradeRecordRefund{
			Id:            bson.NewObjectId(),
			TradeRecordId: t.Id,
			CreatedAt:     time.Now(),
			TotalFee:      t.PayAmount,
			RefundAmount:  refundAmount,
			Status:        ec_order.ORDER_REFUND_STATUS_PENDING,
		}
		orderRefund.TradeRecordRefunds = append(orderRefund.TradeRecordRefunds, tradeRecordRefund)
	}
	return
}

func CheckErpShipped(ctx context.Context, order ec_order.Order, refundProduct []ec_order.RefundProduct) (needAudit, needSyncToOMS bool, err error) {
	if order.Method != ec_order.ORDER_DELIVERY_METHOD_EXPRESS {
		return
	}

	unshippedRefundingProducts := []string{}
	for _, rp := range refundProduct {
		for _, p := range order.Products {
			if p.OutTradeId != rp.OutTradeId {
				continue
			}
			if len(p.Logistics) == 0 {
				unshippedRefundingProducts = append(unshippedRefundingProducts, rp.OutTradeId.Hex())
			}
		}
	}
	if len(unshippedRefundingProducts) == 0 {
		return
	}

	settings, subErr := ec_settings.CSettings.Get(ctx)
	if subErr != nil {
		err = subErr
		return
	}
	if !settings.IsErpConfigured() {
		return
	}
	if !core_util.StrInArray(settings.ErpProvider, &[]string{ec_settings.ERP_PROVIDER_WDT, ec_settings.ERP_PROVIDER_WDT_ULTIMATE}) {
		needAudit = true
		return
	}
	erp := ec_erp.InitThirdPartyErp(*settings)
	if erp == nil {
		return
	}
	result, erpErr := erp.QueryShippedGoods(ctx, ec_order.QueryShippedGoodsRequest{OrderId: order.Id.Hex()})
	if erpErr != nil {
		err = erpErr
		needAudit = true
		proto_client.GetAccountServiceClient().CreateAlert(ctx, &account.CreateAlertRequest{
			Module:         "ec",
			Assignee:       "derrick.teng",
			ExtraReceivers: []string{"alvin.zeng", "jarvis.pu"},
			Name:           "callWDTApiError",
			Priority:       "P1",
			Context: core_util.MarshalInterfaceToString(map[string]interface{}{
				"error":     err.Error(),
				"orderId":   order.Id.Hex(),
				"accountId": util.GetAccountId(ctx),
			}),
		})
	} else {
		needAudit = len(core_util.IntersectStringSlice(result.ShippedGoods, unshippedRefundingProducts)) > 0
		// 存在未发货的商品在 ERP 上已发货，此时应该走售后退款，需要推退款单
		needSyncToOMS = needAudit
	}

	return
}
