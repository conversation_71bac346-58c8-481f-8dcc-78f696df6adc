package order

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	pb_client "mairpc/proto/client"
	"mairpc/proto/common/request"
	ec_order "mairpc/proto/ec/order"
	model_order "mairpc/service/ec/model/order"
	order_model "mairpc/service/ec/model/order"
	"mairpc/service/share/util"
)

const (
	PREVIEW_IMAGE_URL_FORMAT = "%s/api/image/from-pdf/%s"
)

func (OrderService) ListInvoices(ctx context.Context, req *ec_order.ListInvoicesRequest) (*ec_order.ListInvoicesResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	condition := genListInvoicesCondition(ctx, req)
	page, pageSize := util.ParsePagingCondition(req.ListCondition)
	orderBy := []string{"-createdAt"}
	if req.ListCondition != nil && len(req.ListCondition.OrderBy) != 0 {
		orderBy = req.ListCondition.OrderBy
	}
	invoices, total := order_model.CInvoice.GetAllByPagination(ctx, condition, page, pageSize, orderBy)
	invoiceResponses := []*ec_order.InvoiceResponse{}
	for _, invoice := range invoices {
		invoiceResponses = append(invoiceResponses, formatInvoice(ctx, invoice))
	}
	return &ec_order.ListInvoicesResponse{
		Total: uint64(total),
		Items: invoiceResponses,
	}, nil
}

func genListInvoicesCondition(ctx context.Context, req *ec_order.ListInvoicesRequest) bson.M {
	cond := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	if req.Id != "" {
		cond["_id"] = util.ToMongoId(req.Id)
	}
	if len(req.OrderIds) != 0 {
		cond["$or"] = []bson.M{
			{
				"orderId": bson.M{
					"$in": util.ToMongoIds(req.OrderIds),
				},
			},
			{
				"mergedOrderIds": bson.M{
					"$in": util.ToMongoIds(req.OrderIds),
				},
			},
		}
	}
	if req.MemberId != "" {
		cond["memberId"] = util.ToMongoId(req.MemberId)
	}
	// 导购查看代下单订单发票时，memberId 是导购自己的，不能放到查询条件中
	if req.OrderType == "proxyOrder" {
		delete(cond, "memberId")
	}
	if req.InvoicedAt != nil {
		cond["invoicedAt"] = util.ParseDateRange(req.InvoicedAt)
	}
	if len(req.Status) > 0 {
		cond["status"] = bson.M{"$in": req.Status}
	}
	if req.InvoiceSetting != "" {
		cond["invoiceSetting"] = req.InvoiceSetting
	}
	if req.InvoiceType != "" {
		if req.InvoiceType == order_model.INVOICE_TYPE_GENERAL {
			cond["$or"] = []bson.M{
				{
					"invoiceType": bson.M{"$in": []string{"", order_model.INVOICE_TYPE_GENERAL}},
				},
				{
					"invoiceType": bson.M{"$exists": false},
				},
			}
		} else {
			cond["invoiceType"] = req.InvoiceType
		}
	}
	if req.IsRed {
		cond["isRed"] = true
	}
	if req.BlueInvoiceId != "" {
		cond["blueInvoiceId"] = util.ToMongoId(req.BlueInvoiceId)
	}
	return util.FormatConditionContainedOr(cond)
}

func formatInvoice(ctx context.Context, invoice order_model.Invoice) *ec_order.InvoiceResponse {
	invoiceResp := &ec_order.InvoiceResponse{}
	copier.Instance(nil).RegisterTransformer(copier.Transformer{
		"Histories": func(histories []order_model.InvoiceHistory) []*ec_order.InvoiceHistory {
			pbHistories := []*ec_order.InvoiceHistory{}
			statusList := core_util.ExtractArrayFieldV2("Status", "", histories)
			var isInvoiceIssued bool
			for _, h := range histories {
				tempPbHistory := &ec_order.InvoiceHistory{}
				copier.Instance(nil).From(h).CopyTo(tempPbHistory)
				switch h.Status {
				case order_model.INVOICE_STATUS_PENDING, "":
					tempPbHistory.OperatorDetail = func() string {
						if util.StrInArray(model_order.INVOICE_STATUS_REBILLING, &statusList) && isInvoiceIssued {
							return "原发票已红冲"
						}
						if len(invoice.MergedOrderIds) > 0 {
							return "用户提交合并订单开票申请"
						}
						return "用户提交发票申请"
					}()
				case order_model.INVOICE_STATUS_INVOICING:
					tempPbHistory.OperatorDetail = "开票中"
				case order_model.INVOICE_STATUS_ISSUED:
					isInvoiceIssued = true
					tempPbHistory.OperatorDetail = func() string {
						if h.Operator == "system" {
							return "开票成功"
						}
						return "标记已开发票"
					}()
				case order_model.INVOICE_STATUS_CLOSED:
					if util.StrInArray(model_order.INVOICE_STATUS_ISSUED, &statusList) {
						continue
					}
					tempPbHistory.OperatorDetail = func() string {
						return "全部商品退款，无需开发票"
					}()
				case order_model.INVOICE_STATUS_FAILED:
					tempPbHistory.OperatorDetail = "开票失败"
				case order_model.INVOICE_STATUS_REBILLING:
					tempPbHistory.OperatorDetail = func() string {
						if len(invoice.MergedOrderIds) > 0 {
							return "用户提交重新合并订单开票申请"
						}
						return "用户提交重新开票申请"
					}()
				case order_model.INVOICE_STATUS_REVERSING:
					tempPbHistory.OperatorDetail = "原发票红冲中"
				case order_model.INVOICE_STATUS_DELETED:
					tempPbHistory.OperatorDetail = "发票已全部红冲"
				case order_model.INVOICE_STATUS_PARTIALLY_REVERSING:
					tempPbHistory.OperatorDetail = "部分冲红处理中"
				case order_model.INVOICE_STATUS_PARTIALLY_REVERSED:
					tempPbHistory.OperatorDetail = "部分冲红成功"
				}
				extraBytes, _ := json.Marshal(h.Extra)
				tempPbHistory.Extra = string(extraBytes)
				pbHistories = append(pbHistories, tempPbHistory)
			}
			return pbHistories
		},
	}).From(invoice).CopyTo(invoiceResp)
	if invoiceResp.FileUrl != "" {
		ossServiceSetting, _ := pb_client.GetAccountServiceClient().GetOssServiceSetting(ctx, &request.EmptyRequest{})
		invoiceResp.PreviewImageUrl = fmt.Sprintf(PREVIEW_IMAGE_URL_FORMAT, ossServiceSetting.Cdn.Domain, base64.URLEncoding.EncodeToString([]byte(invoiceResp.FileUrl)))
	}
	copier.Instance(nil).From(invoice.InvoiceItems.InvoiceProducts).CopyTo(&invoiceResp.InvoiceItems.InvoiceProducts)
	invoiceResp.InvoiceItems.DeliveryFee = invoice.InvoiceItems.DeliveryFee
	return invoiceResp
}
