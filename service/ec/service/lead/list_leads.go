package lead

import (
	"context"

	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	pb_ec_lead "mairpc/proto/ec/lead"
	ec_store "mairpc/proto/ec/store"
	"mairpc/service/ec/client"
	"mairpc/service/ec/model/lead"
	"mairpc/service/share/util"
	store_model "mairpc/service/store/model"
)

func (LeadService) ListLeads(ctx context.Context, req *pb_ec_lead.ListLeadsRequest) (*pb_ec_lead.ListLeadsResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	condition := getLeadsCondition(ctx, req)

	total, leads, err := lead.CLead.FindByCondition(ctx, util.FormatPagingCondition(condition, req.ListCondition))

	if err != nil {
		return nil, err
	}

	resp := &pb_ec_lead.ListLeadsResponse{
		TotalCount: uint64(total),
	}

	// 创建 staffId 到 staff 的映射
	staffMap := make(map[string]store_model.DmsStaff)
	// 创建 distributorId 到 distributor 的映射
	distributorMap := make(map[string]*store_model.Distributor)

	// 收集所有需要查询的 staffId 和 distributorId
	var staffIds []bson.ObjectId
	var distributorIds []bson.ObjectId
	for _, lead := range *leads {
		if !lead.StaffId.IsZero() {
			staffIds = append(staffIds, lead.StaffId)
		}
		if !lead.DistributorId.IsZero() {
			distributorIds = append(distributorIds, lead.DistributorId)
		}
	}

	// 批量查询 staff 信息
	if len(staffIds) > 0 {
		staffs, err := store_model.CDmsStaff.GetByIds(ctx, staffIds)
		if err == nil {
			for _, staff := range staffs {
				staffMap[staff.Id.Hex()] = staff
			}
		}
	}

	// 批量查询 distributor 信息
	if len(distributorIds) > 0 {
		distributors, err := store_model.CDistributor.GetByIds(ctx, distributorIds)
		if err == nil {
			for _, distributor := range distributors {
				distributorMap[distributor.Id.Hex()] = &distributor
			}
		}
	}

	// 处理每个 lead 并添加额外信息
	for _, lead := range *leads {
		// 创建基本的 LeadDetail
		leadDetail := formatLeadDetail(ctx, &lead)

		// 添加 staff 信息
		if !lead.StaffId.IsZero() {
			if staff, ok := staffMap[lead.StaffId.Hex()]; ok {
				leadDetail.StaffName = staff.Name
				leadDetail.StaffEmail = staff.Email
			}
		}

		// 添加 distributor 信息
		if !lead.DistributorId.IsZero() {
			if distributor, ok := distributorMap[lead.DistributorId.Hex()]; ok {
				leadDetail.DistributorName = distributor.Name
			}
		}

		resp.Items = append(resp.Items, leadDetail)
	}

	return resp, nil
}

func getLeadsCondition(ctx context.Context, req *pb_ec_lead.ListLeadsRequest) bson.M {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}
	if !req.ContainDeleted {
		condition["isDeleted"] = false
	}

	if req.Status != nil {
		condition["status"] = bson.M{"$in": req.Status}
	}

	staffId := core_util.GetUserId(ctx)
	staffDetail, err := client.StoreService.GetStaff(ctx, &ec_store.StaffDetailRequest{
		StaffId: staffId,
	})

	if req.UpdatedAtRange != nil {
		condition["updatedAt"] = util.ParseDateRange(req.UpdatedAtRange)
	}

	if req.ThirdPartyUpdatedAt != nil {
		condition["thirdPartyUpdatedAt"] = util.ParseDateRange(req.ThirdPartyUpdatedAt)
	}

	if err == nil && staffDetail != nil {
		isStoreManager := false

		if len(staffDetail.Roles) > 0 {
			businessRole := staffDetail.Roles[0]

			if len(businessRole.DistributorRoles) > 0 {
				distributorRole := businessRole.DistributorRoles[0]
				if len(distributorRole.Roles) > 0 {
					for _, role := range distributorRole.Roles {
						if role.Name == "店长" {
							isStoreManager = true
							break
						}
					}
				}
			}
		}

		if isStoreManager {
			distributor, err := store_model.CDmsDistributor.GetById(ctx, bson.ObjectIdHex(staffDetail.CurrentStoreId))
			if err == nil {
				if distributor.Type == 2 {
					condition["distributorId"] = bson.ObjectIdHex(staffDetail.CurrentStoreId)
				} else if !distributor.ParentId.IsZero() {
					condition["distributorId"] = distributor.ParentId
				}
			}
		} else {
			condition["staffId"] = bson.ObjectIdHex(staffId)
		}
	}

	if req.SearchStaffId != "" {
		condition["staffId"] = bson.ObjectIdHex(req.SearchStaffId)
	}

	if req.SearchKey != "" {
		condition["$or"] = []bson.M{
			{"name": bson.M{"$regex": req.SearchKey, "$options": "i"}},
			{"phone": req.SearchKey},
		}
	}

	if len(req.InvitationTypes) > 0 {
		condition["invitationType"] = bson.M{"$in": req.InvitationTypes}
	}

	if len(req.IntentionLevels) > 0 {
		condition["intentionLevel"] = bson.M{"$in": req.IntentionLevels}
	}

	return condition
}
