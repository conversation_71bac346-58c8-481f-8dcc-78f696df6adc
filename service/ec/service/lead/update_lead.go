package lead

import (
	"context"
	"time"

	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	pb_ec_lead "mairpc/proto/ec/lead"
	"mairpc/service/ec/model/lead"
	"mairpc/service/share/model"
)

func (LeadService) UpdateLead(ctx context.Context, req *pb_ec_lead.UpdateLeadRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	if !bson.IsObjectIdHex(req.Id) {
		return nil, errors.NewInvalidArgumentError("id")
	}

	leadId := bson.ObjectIdHex(req.Id)
	dbLead, err := lead.CLead.FindById(ctx, leadId)
	if err != nil {
		return nil, errors.NewNotExistsError(req.Id)
	}

	selector := model.Base.GenDefaultConditionById(ctx, leadId)
	setter := bson.M{}

	if req.ThirdPartyUpdatedAt != "" {
		setter["thirdPartyUpdatedAt"], _ = time.Parse(core_util.RFC3339Mili, req.ThirdPartyUpdatedAt)
	} else {
		setter["updatedAt"] = time.Now()
	}

	if req.Name != "" {
		setter["name"] = req.Name
	}

	if req.Phone != "" {
		setter["phone"] = req.Phone
	}

	if req.IntentionLevel > 0 {
		setter["intentionLevel"] = req.IntentionLevel
	}

	if req.PurchaseBudget > 0 {
		setter["purchaseBudget"] = req.PurchaseBudget
	}

	if req.TrialProductName != "" {
		setter["trialProductName"] = req.TrialProductName
	}

	if req.TrialAt != "" {
		trialAt, err := time.Parse("2006-01-02", req.TrialAt)
		if err == nil {
			setter["trialAt"] = trialAt
		}
	}

	if req.ProductConditionIntention != "" {
		setter["productConditionIntention"] = req.ProductConditionIntention
	}

	if req.IntentionProductName != "" {
		setter["intentionProductName"] = req.IntentionProductName
	}

	if req.Industry != "" {
		setter["industry"] = req.Industry
	}

	if req.Remark != "" {
		setter["remark"] = req.Remark
	}

	if len(req.Properties) > 0 {
		dbProperties := dbLead.Properties
		for _, property := range req.Properties {
			for i, dbProperty := range dbProperties {
				if property.Id == dbProperty.Id.Hex() {
					dbProperties[i].StringValue = property.StringValue
					dbProperties[i].IntegerValue = property.IntegerValue
					dbProperties[i].BooleanValue = property.BooleanValue
				} else {
					dbProperties = append(dbProperties, lead.LeadPropertyStruct{
						Id:           bson.ObjectIdHex(property.Id),
						Name:         property.Name,
						NameCN:       property.NameCN,
						Order:        property.Order,
						Type:         property.Type,
						StringValue:  property.StringValue,
						IntegerValue: property.IntegerValue,
						BooleanValue: property.BooleanValue,
					})
				}
			}
		}
		setter["properties"] = dbProperties
	}

	if req.StaffId != "" {
		setter["staffId"] = bson.ObjectIdHex(req.StaffId)
		setter["isAssigned"] = true
	}

	if dbLead.StaffId.Hex() != req.StaffId {
		setter["status"] = lead.STATUS_PROSPECTIVE
	}

	updater := bson.M{"$set": setter}
	if err := lead.CLead.Update(ctx, selector, updater); err != nil {
		return nil, err
	}

	return &response.EmptyResponse{}, nil
}
