package lead

import (
	"context"
	"time"

	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	pb_ec_lead "mairpc/proto/ec/lead"
	"mairpc/service/ec/model/lead"
	"mairpc/service/share/model"
)

func (LeadService) UpdateLeadStatus(ctx context.Context, req *pb_ec_lead.UpdateLeadStatusRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	leadId := bson.ObjectIdHex(req.LeadId)
	_, err := lead.CLead.FindById(ctx, leadId)
	if err != nil {
		return nil, errors.NewNotExistsError(req.LeadId)
	}

	now := time.Now()
	selector := model.Base.GenDefaultConditionById(ctx, leadId)
	setter := bson.M{
		"updatedAt": now,
		"status":    req.Status,
	}

	updater := bson.M{"$set": setter}
	if err := lead.CLead.Update(ctx, selector, updater); err != nil {
		return nil, err
	}

	return &response.EmptyResponse{}, nil
}
