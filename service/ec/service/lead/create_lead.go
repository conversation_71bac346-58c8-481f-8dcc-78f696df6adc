package lead

import (
	"context"
	"time"

	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	pb_ec_lead "mairpc/proto/ec/lead"
	lead_model "mairpc/service/ec/model/lead"
	"mairpc/service/share/util"
	store_model "mairpc/service/store/model"
)

func (LeadService) CreateLead(ctx context.Context, req *pb_ec_lead.CreateLeadRequest) (*pb_ec_lead.LeadDetail, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	newLead := &lead_model.Lead{
		Id:                        bson.NewObjectId(),
		AccountId:                 util.GetAccountIdAsObjectId(ctx),
		IsDeleted:                 false,
		Name:                      req.Name,
		Phone:                     req.Phone,
		Source:                    req.Source,
		InvitationType:            req.InvitationType,
		IsAssigned:                false,
		Gender:                    req.Gender,
		Industry:                  req.Industry,
		IntentionLevel:            req.IntentionLevel,
		PurchaseBudget:            req.PurchaseBudget,
		LostReason:                req.LostReason,
		CloseReason:               req.CloseReason,
		NextPlan:                  req.NextPlan,
		TrialProductName:          req.TrialProductName,
		ProductConditionIntention: req.ProductConditionIntention,
		IntentionProductName:      req.IntentionProductName,
		Remark:                    req.Remark,
		SourceActivity:            req.SourceActivity,
	}

	if req.TrialAt != "" {
		trialAt, err := time.Parse("2006-01-02", req.TrialAt)
		if err != nil {
			return nil, err
		}
		newLead.TrialAt = trialAt
	}

	if req.NextFollowUpAt != "" {
		nextFollowUpAt, err := time.Parse("2006-01-02", req.NextFollowUpAt)
		if err != nil {
			return nil, err
		}
		newLead.NextFollowUpAt = nextFollowUpAt
	}

	if req.InvitedAt != "" {
		invitedAt, err := time.Parse("2006-01-02", req.InvitedAt)
		if err != nil {
			return nil, err
		}
		newLead.InvitedAt = invitedAt
	}

	for _, property := range req.Properties {
		newLead.Properties = append(newLead.Properties, lead_model.LeadPropertyStruct{
			Id:           bson.ObjectIdHex(property.Id),
			Name:         property.Name,
			NameCN:       property.NameCN,
			Order:        property.Order,
			Type:         property.Type,
			StringValue:  property.StringValue,
			IntegerValue: property.IntegerValue,
			BooleanValue: property.BooleanValue,
		})
	}

	if req.MemberId != "" {
		newLead.MemberId = bson.ObjectIdHex(req.MemberId)
	}

	if req.ThirdPartyId != "" {
		newLead.ThirdPartyId = req.ThirdPartyId
	}

	if req.ThirdPartyUpdatedAt != "" {
		newLead.ThirdPartyUpdatedAt, _ = time.Parse(core_util.RFC3339Mili, req.ThirdPartyUpdatedAt)
	}

	if req.ThirdMemberId != "" {
		newLead.ThirdMemberId = req.ThirdMemberId
	}

	newLead.StaffId = bson.ObjectIdHex(req.StaffId)
	newLead.IsAssigned = true

	if req.DistributorId != "" {
		newLead.DistributorId = bson.ObjectIdHex(req.DistributorId)
	} else {
		staff, err := store_model.CDmsStaff.GetById(ctx, bson.ObjectIdHex(req.StaffId))
		if err == nil && !staff.CurrentStoreId.IsZero() {
			currentDistributorId := staff.CurrentStoreId
			distributor, err := store_model.CDmsDistributor.GetById(ctx, currentDistributorId)
			if err == nil {
				if distributor.Type == 2 {
					newLead.DistributorId = currentDistributorId
				} else if !distributor.ParentId.IsZero() {
					newLead.DistributorId = distributor.ParentId
				}
			}
		}
	}

	if req.Status != "" {
		newLead.Status = req.Status
	} else {
		newLead.Status = "prospective"
	}

	if err := newLead.Insert(ctx); err != nil {
		return nil, err
	}

	return formatLeadDetail(ctx, newLead), nil
}

func formatLeadDetail(ctx context.Context, lead *lead_model.Lead) *pb_ec_lead.LeadDetail {
	detail := &pb_ec_lead.LeadDetail{
		Id:                        lead.Id.Hex(),
		CreatedAt:                 lead.CreatedAt.Format(time.RFC3339),
		Name:                      lead.Name,
		Phone:                     lead.Phone,
		Status:                    lead.Status,
		Source:                    lead.Source,
		InvitationType:            lead.InvitationType,
		IsAssigned:                lead.IsAssigned,
		Gender:                    lead.Gender,
		Industry:                  lead.Industry,
		IntentionLevel:            lead.IntentionLevel,
		PurchaseBudget:            lead.PurchaseBudget,
		LostReason:                lead.LostReason,
		CloseReason:               lead.CloseReason,
		NextPlan:                  lead.NextPlan,
		TrialProductName:          lead.TrialProductName,
		ProductConditionIntention: lead.ProductConditionIntention,
		IntentionProductName:      lead.IntentionProductName,
		Remark:                    lead.Remark,
		SourceActivity:            lead.SourceActivity,
		IsDeleted:                 lead.IsDeleted,
	}

	if !lead.UpdatedAt.IsZero() {
		detail.UpdatedAt = lead.UpdatedAt.Format(time.RFC3339)
	}

	if !lead.InvitedAt.IsZero() {
		detail.InvitedAt = lead.InvitedAt.Format(time.RFC3339)
	}

	if !lead.MemberId.IsZero() {
		detail.MemberId = lead.MemberId.Hex()
	}

	if lead.ThirdMemberId != "" {
		detail.ThirdMemberId = lead.ThirdMemberId
	}

	if lead.ThirdPartyId != "" {
		detail.ThirdPartyId = lead.ThirdPartyId
	}

	if !lead.ThirdPartyUpdatedAt.IsZero() {
		detail.ThirdPartyUpdatedAt = lead.ThirdPartyUpdatedAt.Format(time.RFC3339)
	}

	if !lead.DistributorId.IsZero() {
		detail.DistributorId = lead.DistributorId.Hex()

		distributor, err := store_model.CDmsDistributor.GetById(ctx, lead.DistributorId)
		if err == nil {
			detail.DistributorName = distributor.Name
		}
	}

	if !lead.StaffId.IsZero() {
		detail.StaffId = lead.StaffId.Hex()
		dmsStaff, err := store_model.CDmsStaff.GetById(ctx, lead.StaffId)
		if err == nil {
			detail.StaffName = dmsStaff.Name
			detail.StaffEmail = dmsStaff.Email
		}
	}

	if !lead.LastFollowUpAt.IsZero() {
		detail.LastFollowUpAt = lead.LastFollowUpAt.Format(time.RFC3339)
	}

	if !lead.NextFollowUpAt.IsZero() {
		detail.NextFollowUpAt = lead.NextFollowUpAt.Format(time.RFC3339)
	}

	if !lead.TrialAt.IsZero() {
		detail.TrialAt = lead.TrialAt.Format(time.RFC3339)
	}

	// 获取最新的状态记录
	statusRecords, err := lead_model.CLeadFollowUpRecord.GetLatestStatusRecords(ctx, lead.Id)
	if err == nil {
		if convertedRecord, ok := statusRecords["converted"]; ok && !convertedRecord.CreatedAt.IsZero() {
			detail.ConvertedAt = convertedRecord.CreatedAt.Format(time.RFC3339)
		}

		if lostRecord, ok := statusRecords["lost"]; ok && !lostRecord.CreatedAt.IsZero() {
			detail.LostAt = lostRecord.CreatedAt.Format(time.RFC3339)
		}

		if closedRecord, ok := statusRecords["closed"]; ok && !closedRecord.CreatedAt.IsZero() {
			detail.ClosedAt = closedRecord.CreatedAt.Format(time.RFC3339)
		}
	}

	var properties []*pb_ec_lead.LeadProperty
	for _, prop := range lead.Properties {
		properties = append(properties, &pb_ec_lead.LeadProperty{
			Id:           prop.Id.Hex(),
			Name:         prop.Name,
			NameCN:       prop.NameCN,
			Order:        prop.Order,
			Type:         prop.Type,
			StringValue:  prop.StringValue,
			IntegerValue: prop.IntegerValue,
			BooleanValue: prop.BooleanValue,
		})
	}
	detail.Properties = properties

	return detail
}
