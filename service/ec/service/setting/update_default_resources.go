package setting

import (
	"context"
	"mairpc/core/extension"
	pb_account "mairpc/proto/account"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
)

// 仅用于一次性迁移，production 迁移完成后（release 之后）请及时删除代码,避免重复迁移产生问题。初始化数据应该放在 InitDefaultResources
func (self SettingService) UpdateDefaultResources(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	key := "ec:update-default-resources"
	ok, _ := extension.RedisClient.SetNX(key, "1", 5*60)
	if !ok {
		return &response.EmptyResponse{}, nil
	}
	client.GetAccountServiceClient().CreateJob(ctx, &pb_account.CreateJobRequest{
		JobName:      "migratetransferbill",
		Module:       "ec",
		SubModule:    "profitsharing",
		FunctionName: "migrateTransferBill",
		Description:  "上线收益记录关联订单功能时创建一个",
	})
	return &response.EmptyResponse{}, nil
}
