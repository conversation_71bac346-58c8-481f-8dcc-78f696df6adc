package store_warehouse

import (
	"context"
	"encoding/json"
	sys_errors "errors"
	"fmt"
	"mairpc/core/client"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/account"
	"mairpc/proto/common/response"
	pb_store_warehouse "mairpc/proto/ec/storeWarehouse"
	store_warehouse_model "mairpc/service/ec/model/storeWarehouse"
	"mairpc/service/share/util"
	"strings"
)

var (
	StoreStockInBillsHeader  = []string{"供应商", "入库日期", "入库时间", "入库类型", "业务编号", "SKU编号", "外部SKU编号", "入库数量", "成本价", "门店编号", "备注"}
	StoreStockOutBillsHeader = []string{"出库日期", "出库时间", "出库类型", "业务编号", "SKU编号", "外部SKU编号", "出库数量", "成本价", "门店编号", "备注"}
)

func (StoreWarehouseService) ImportStoresProductStockBills(ctx context.Context, req *pb_store_warehouse.ImportStockBillsRequest) (*response.JobResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	if err := validateStoreFile(ctx, req.FileUrl, req.Type); err != nil {
		return nil, err
	}

	reqByte, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	fileUrlSlice := strings.Split(req.FileUrl, "/")
	displayName := fileUrlSlice[len(fileUrlSlice)-1]

	resp, rpcErr := client.Run(
		"AccountService.CreateJob",
		ctx,
		&account.CreateJobRequest{
			JobName:      "importstoresproductstockbills",
			Module:       "ec",
			SubModule:    "storeWarehouse",
			FunctionName: "importStoresProductStockBills",
			Description:  "群脉零售后台每次导入多门店单商品出入库明细创建一个",
			Type:         "import",
			Args:         fmt.Sprintf(`{"request": %s,"userId": "%s"}`, string(reqByte), core_util.GetUserId(ctx)),
			DisplayName:  displayName,
		},
	)

	if rpcErr != nil {
		return nil, err
	}

	jobInfo := resp.(*account.CreateJobResponse)

	return &response.JobResponse{
		JobId: jobInfo.JobId,
	}, nil
}

func validateStoreFile(ctx context.Context, fileUrl string, t string) error {
	header := GetStoreHeader(t)
	tableReader, err := util.GetTableReader(ctx, fileUrl)
	if err != nil {
		return err
	}
	defer tableReader.ClearFile()
	if tableReader.RealMaxRow() == 0 {
		return sys_errors.New(EMPTY_FILE)
	}
	if tableReader.RealColumn(1) < len(header) {
		return sys_errors.New(CONSISTENT_WITH_TEMPLETE)
	}
	for i, item := range tableReader.GetRow(1) {
		if item != header[i] {
			return sys_errors.New(CONSISTENT_WITH_TEMPLETE)
		}
	}
	return nil
}

func GetStoreHeader(t string) []string {
	switch t {
	case store_warehouse_model.TYPE_STOCK_BILL_IN:
		return StoreStockInBillsHeader
	case store_warehouse_model.TYPE_STOCK_BILL_OUT:
		return StoreStockOutBillsHeader
	}
	return nil
}
