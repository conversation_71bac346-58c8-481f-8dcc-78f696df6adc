package store_warehouse

import (
	"mairpc/core/errors"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	pb_ec_store_warehouse "mairpc/proto/ec/storeWarehouse"
	store_warehouse_model "mairpc/service/ec/model/storeWarehouse"
	"mairpc/service/ec/service/order"

	"golang.org/x/net/context"

	"mairpc/core/extension/bson"
)

func (StoreWarehouseService) CreateStoreStockBill(ctx context.Context, req *pb_ec_store_warehouse.CreateStockBillRequest) (*pb_ec_store_warehouse.CreateStockBillResponse, error) {
	err := validators.ValidateRequest(req)
	if err != nil {
		return nil, err
	}
	if req.Operator == "" {
		return nil, errors.NewInvalidArgumentError("operator")
	}
	stockBills := []store_warehouse_model.StoreStockBill{}
	failedProducts := []*pb_ec_store_warehouse.StockBillProduct{}
	tempStockBill := store_warehouse_model.StoreStockBill{}
	core_util.FormatRFC3339(req, &tempStockBill, map[string]interface{}{
		"ChangedAt": core_util.ParseRFC3339,
	}, map[string]string{})
	if req.Number != "" {
		tempStockBill.Number = req.Number
	} else {
		tempStockBill.Number = store_warehouse_model.GenStockBillNumber(req.Type)
	}
	if req.Provider != "" {
		existedProvider, err := store_warehouse_model.CStoreStockProvider.FindByName(ctx, req.Provider)
		if existedProvider == nil || err != nil {
			return nil, errors.NewInvalidArgumentError("provider")
		}
		tempStockBill.Provider = store_warehouse_model.Provider{
			Id:   existedProvider.Id,
			Name: existedProvider.Name,
		}
	}
	tempStockBill.Operator = req.Operator
	tempStockBill.Remarks = req.Remarks
	tempStockBill.Type = req.Type
	if req.OppositeStore != nil {
		tempStockBill.OppositeStore.Id = bson.ObjectIdHex(req.OppositeStore.Id)
		tempStockBill.OppositeStore.Name = req.OppositeStore.Name
		tempStockBill.OppositeStore.Code = req.OppositeStore.Code
	}

	productIds := []bson.ObjectId{}
	storeIds := []bson.ObjectId{}
	for _, n := range req.Stores {
		tempStockBill.Store.Id = bson.ObjectIdHex(n.Id)
		tempStockBill.Store.Name = n.Name
		tempStockBill.Store.Code = n.Code
		for i, p := range req.Products {
			if p.Count == 0 {
				failedProducts = append(failedProducts, req.Products[i])
				continue
			}
			tempStockBill.ProductId = bson.ObjectIdHex(p.Id)
			tempStockBill.Sku = p.Sku
			tempStockBill.Count = p.Count
			tempStockBill.Price = p.Price
			switch tempStockBill.Operator {
			case store_warehouse_model.OPERATOR_STOCK_BILL_REFUND:
				err = store_warehouse_model.CStoreProductStock.ReturnAvailableStock(ctx, tempStockBill.Sku, int(p.Count), tempStockBill.Store.Id, tempStockBill.ProductId)
			case store_warehouse_model.OPERATOR_STOCK_BILL_SHIP:
				// 销售出库在商品流转过程中处理过了，所以这里跳过
			default:
				if tempStockBill.Type == store_warehouse_model.TYPE_STOCK_BILL_IN {
					err = store_warehouse_model.CStoreProductStock.IncStoreStock(ctx, tempStockBill.Sku, int(p.Count), tempStockBill.Store.Id, tempStockBill.ProductId)
				} else {
					err = store_warehouse_model.CStoreProductStock.DecStoreStock(ctx, tempStockBill.Sku, int(p.Count), tempStockBill.Store.Id, tempStockBill.ProductId)
				}
			}

			if err != nil {
				// 修改库存失败，该商品出入库失败
				req.Products[i].StoreId = n.Id
				log.Warn(ctx, "Failed to change product stock", log.Fields{
					"storeId":   n.Id,
					"productId": p.Id,
					"sku":       p.Sku,
					"count":     p.Count,
					"error":     err.Error(),
				})
				failedProducts = append(failedProducts, req.Products[i])
				continue
			}
			productIds = append(productIds, tempStockBill.ProductId)
			stockBills = append(stockBills, tempStockBill)
			storeIds = append(storeIds, tempStockBill.Store.Id)
		}
	}
	if len(stockBills) == 0 {
		return &pb_ec_store_warehouse.CreateStockBillResponse{FailedProducts: failedProducts}, nil
	}
	storeProductStocks, err := store_warehouse_model.CStoreProductStock.GetAllByIds(ctx, productIds, storeIds)

	if err != nil {
		log.Warn(ctx, "Failed to get products", log.Fields{
			"storeProductStocks": storeProductStocks,
			"err":                err.Error(),
		})
	}
	stockBills = order.FillStoreAvailableStock(stockBills, storeProductStocks)
	err = store_warehouse_model.CStoreStockBill.BatchInsert(ctx, stockBills)
	return &pb_ec_store_warehouse.CreateStockBillResponse{FailedProducts: failedProducts}, err
}
