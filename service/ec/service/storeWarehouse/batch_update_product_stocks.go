package store_warehouse

import (
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	pb_ec_store_warehouse "mairpc/proto/ec/storeWarehouse"
	store_warehouse_model "mairpc/service/ec/model/storeWarehouse"

	"golang.org/x/net/context"
)

func (StoreWarehouseService) BatchUpdateProductStocks(ctx context.Context, req *pb_ec_store_warehouse.BatchUpdateProductStocksRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	var storeIds []bson.ObjectId
	for _, item := range req.Items {
		storeIds = append(storeIds, bson.ObjectIdHex(item.Id))
	}

	if len(storeIds) == 0 {
		return nil, errors.NewInvalidArgumentErrorWithMessage("items", "items is required")
	}
	productStocks, err := store_warehouse_model.CStoreProductStock.GetByStoreIds(ctx, req.Sku, storeIds)
	if err != nil {
		return nil, err
	}
	storeStockMap := core_util.MakeMapperV2("StoreId", bson.NewObjectId(), productStocks)
	var stockBills []store_warehouse_model.StoreStockBill
	number := store_warehouse_model.GenStockBillNumber(req.Type)
	provider := &store_warehouse_model.StoreStockProvider{}
	if req.Provider != "" {
		provider, _ = store_warehouse_model.CStoreStockProvider.FindByName(ctx, req.Provider)
		if provider == nil {
			return nil, errors.NewInvalidArgumentError("provider")
		}
	}
	for _, item := range req.Items {
		stock, ok := storeStockMap[bson.ObjectIdHex(item.Id)]
		if ok {
			if req.Type == store_warehouse_model.TYPE_STOCK_BILL_IN {
				store_warehouse_model.CStoreProductStock.IncStoreStock(ctx, req.Sku, int(req.Count), bson.ObjectIdHex(item.Id), bson.ObjectIdHex(req.ProductId))
			} else {
				if uint64(stock.AvailableStock) < req.Count {
					req.Count = uint64(stock.AvailableStock)
				}
				store_warehouse_model.CStoreProductStock.DecStoreStock(ctx, req.Sku, int(req.Count), bson.ObjectIdHex(item.Id), bson.ObjectIdHex(req.ProductId))
			}
			stockBill := store_warehouse_model.StoreStockBill{
				Number: number,
				Store: store_warehouse_model.Store{
					Id:   bson.ObjectIdHex(item.Id),
					Name: item.Name,
					Code: item.Code,
				},
				ProductId:    bson.ObjectIdHex(req.ProductId),
				Sku:          req.Sku,
				Organization: item.Name,
				Type:         req.Type,
				Operator:     store_warehouse_model.OPERATOR_STOCK_BILL_MANUAL,
				Remarks:      req.Remarks,
				Price:        req.Price,
			}
			if provider != nil {
				stockBill.Provider = store_warehouse_model.Provider{
					Id:   provider.Id,
					Name: provider.Name,
				}
			}
			if req.Type == store_warehouse_model.TYPE_STOCK_BILL_OUT && req.Count > uint64(stock.AvailableStock) {
				stockBill.Count = uint64(stock.AvailableStock)
				stockBill.AvailableStock = 0
			} else if req.Type == store_warehouse_model.TYPE_STOCK_BILL_OUT && req.Count <= uint64(stock.AvailableStock) {
				stockBill.Count = req.Count
				stockBill.AvailableStock = stock.AvailableStock - int(req.Count)
			} else {
				stockBill.Count = req.Count
				stockBill.AvailableStock = stock.AvailableStock + int(req.Count)
			}
			stockBills = append(stockBills, stockBill)
		}
	}
	store_warehouse_model.CStoreStockBill.BatchInsert(ctx, stockBills)
	return &response.EmptyResponse{}, nil
}
