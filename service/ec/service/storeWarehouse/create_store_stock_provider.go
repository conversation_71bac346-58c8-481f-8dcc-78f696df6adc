package store_warehouse

import (
	"mairpc/core/errors"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	store_warehouse_model "mairpc/service/ec/model/storeWarehouse"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

func (StoreWarehouseService) CreateStoreStockProvider(ctx context.Context, req *request.StringRequest) (*response.EmptyResponse, error) {
	err := validators.ValidateRequest(req)
	if err != nil {
		return nil, err
	}

	existedProvider, _ := store_warehouse_model.CStoreStockProvider.FindByName(ctx, req.Value)
	if existedProvider != nil {
		return nil, errors.NewAlreadyExistsError("name")
	}

	provider := &store_warehouse_model.StoreStockProvider{}
	provider.Name = req.Value
	provider.AccountId = util.GetAccountIdAsObjectId(ctx)
	provider.IsDeleted = false
	err = provider.Create(ctx)
	if err != nil {
		return nil, err
	}
	return &response.EmptyResponse{}, nil
}
