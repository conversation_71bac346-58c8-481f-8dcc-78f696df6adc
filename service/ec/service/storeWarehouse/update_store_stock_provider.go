package store_warehouse

import (
	"time"

	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	pb_ec_store_warehouse "mairpc/proto/ec/storeWarehouse"
	store_warehouse_model "mairpc/service/ec/model/storeWarehouse"

	"golang.org/x/net/context"
)

func (StoreWarehouseService) UpdateStoreStockProvider(ctx context.Context, req *pb_ec_store_warehouse.UpdateStoreStockProviderRequest) (*response.EmptyResponse, error) {
	err := validators.ValidateRequest(req)
	if err != nil {
		return nil, err
	}

	existedProvider, _ := store_warehouse_model.CStoreStockProvider.FindByName(ctx, req.Name)
	if existedProvider != nil && existedProvider.Id.Hex() != req.Id {
		return nil, errors.NewAlreadyExistsError("name")
	}

	provider := &store_warehouse_model.StoreStockProvider{
		Id: bson.ObjectIdHex(req.Id),
	}
	err = provider.UpdateName(ctx, req.Name)
	if err != nil {
		return nil, err
	}
	provider.Name = req.Name
	provider.UpdatedAt = time.Now()
	return &response.EmptyResponse{}, nil
}
