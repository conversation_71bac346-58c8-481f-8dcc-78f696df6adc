package store_warehouse

import (
	"mairpc/core/log"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/ec/product"
	pb_ec_store_warehouse "mairpc/proto/ec/storeWarehouse"
	"mairpc/service/ec/client"
	store_warehouse_model "mairpc/service/ec/model/storeWarehouse"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

func (StoreWarehouseService) GetProductStockBillDetail(ctx context.Context, req *pb_ec_store_warehouse.GetProductStockBillDetailRequest) (*pb_ec_store_warehouse.GetProductStockBillDetailResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	condition := store_warehouse_model.Common.GenDefaultCondition(ctx)
	condition["number"] = req.Number
	pageCondition := util.FormatPagingCondition(condition, req.ListCondition)
	stockBills, total := store_warehouse_model.CStoreStockBill.FindByPagination(ctx, pageCondition)
	items := formatProductStockBills(ctx, stockBills, req.Number)
	return &pb_ec_store_warehouse.GetProductStockBillDetailResponse{
		Total: uint64(total),
		Items: items,
	}, nil
}

func formatProductStockBills(ctx context.Context, stockBills []store_warehouse_model.StoreStockBill, number string) (details []*pb_ec_store_warehouse.ProductStockBillDetail) {
	for _, item := range stockBills {
		temp := &pb_ec_store_warehouse.ProductStockBillDetail{}
		copier.Instance(nil).From(item).CopyTo(&temp)
		temp.Provider = &pb_ec_store_warehouse.Provider{
			Id:   item.Provider.Id.Hex(),
			Name: item.Provider.Name,
		}
		productResp, err := client.ProductService.GetProduct(ctx, &product.GetProductRequest{ProductId: item.ProductId.Hex()})
		if err != nil {
			log.Warn(ctx, "get product failed", log.Fields{
				"productId": item.ProductId.Hex(),
				"err":       err.Error(),
			})
		} else {
			temp.ProductName = productResp.Product.Name
			temp.AvailableStock = uint64(item.AvailableStock)
			for _, s := range productResp.Ec.Skus {
				if s.Sku != item.Sku {
					continue
				}
				temp.Properties = s.Properties
				temp.ProductPrice = uint64(s.Price)
			}
		}
		if item.Type == store_warehouse_model.TYPE_STOCK_BILL_OUT {
			temp.ChangedCount = -int64(item.Count)
		} else {
			temp.ChangedCount = int64(item.Count)
		}
		temp.TotalStock = uint64(item.AvailableStock + int(temp.ChangedCount))
		details = append(details, temp)
	}
	return
}
