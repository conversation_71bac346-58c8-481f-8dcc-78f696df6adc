package store_warehouse

import (
	"fmt"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	"mairpc/proto/ec/order"
	pb_product "mairpc/proto/ec/product"
	pb_ec_store_warehouse "mairpc/proto/ec/storeWarehouse"
	"mairpc/service/ec/client"
	store_warehouse_model "mairpc/service/ec/model/storeWarehouse"
	"mairpc/service/ec/service"
	"mairpc/service/share/util"
	"time"

	"github.com/spf13/cast"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.org/x/net/context"
)

func (StoreWarehouseService) ListGroupProductStockBills(ctx context.Context, req *pb_ec_store_warehouse.ListStoreProductStockBillsRequest) (*pb_ec_store_warehouse.ListStoreProductStockBillsResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	var common *pb_ec_store_warehouse.ListProductStockBillsRequest
	copier.Instance(nil).From(req).CopyTo(&common)
	condition, err := getListProductStockBillsCondition(ctx, common)
	if err != nil {
		return nil, err
	}
	condition = handleOperators(req.Operator, condition)
	numberPipeline := genGroupProductStockBillNumberPipeline(ctx, condition, common)
	numberResult := []bson.M{}
	err = extension.DBRepository.Aggregate(ctx, store_warehouse_model.C_STORE_STOCK_BILL, numberPipeline, false, &numberResult)
	if err != nil {
		return nil, err
	}

	countCondition := condition
	if _, ok := condition["$or"]; !ok {
		numbers := core_util.ExtractArrayFieldV2("_id", "", numberResult)
		if len(numbers) > 0 {
			condition["number"] = bson.M{"$in": numbers}
		}
		countCondition, _ = getListProductStockBillsCondition(ctx, common)
		countCondition = handleOperators(req.Operator, countCondition)
	}
	pipeline := genGroupProductStockBillPipeline(ctx, condition, common)
	result := []bson.M{}
	err = extension.DBRepository.Aggregate(ctx, store_warehouse_model.C_STORE_STOCK_BILL, pipeline, false, &result)
	if err != nil {
		return nil, err
	}

	countPipeline := genGroupProductStockBillCountPipeline(ctx, countCondition, req)
	countResult := bson.M{}
	extension.DBRepository.Aggregate(ctx, store_warehouse_model.C_STORE_STOCK_BILL, countPipeline, true, &countResult)
	items, err := formatGroupStockBills(ctx, result)
	if err != nil {
		return nil, err
	}
	return &pb_ec_store_warehouse.ListStoreProductStockBillsResponse{
		Total: cast.ToUint64(countResult["count"]),
		Items: items,
	}, nil
}

func genGroupProductStockBillNumberPipeline(ctx context.Context, condition bson.M, req *pb_ec_store_warehouse.ListProductStockBillsRequest) []bson.M {
	sorter := util.FormatSorter(req.ListCondition.OrderBy)
	bsonDSorter := util.BsonMToD(sorter)
	bsonDSorter = append(bsonDSorter, bson.E{Key: "_id", Value: 1})
	return []bson.M{
		{
			"$match": condition,
		},
		{
			"$group": bson.M{
				"_id":       "$number",
				"changedAt": bson.M{"$first": "$changedAt"},
				"createdAt": bson.M{"$first": "$createdAt"},
			},
		},
		{
			"$sort": bsonDSorter,
		},
		{
			"$skip": (req.ListCondition.Page - 1) * req.ListCondition.PerPage,
		},
		{
			"$limit": req.ListCondition.PerPage,
		},
	}
}

func genGroupProductStockBillPipeline(ctx context.Context, condition bson.M, req *pb_ec_store_warehouse.ListProductStockBillsRequest) []bson.M {
	sorter := util.FormatSorter(req.ListCondition.OrderBy)
	bsonDSorter := util.BsonMToD(sorter)
	bsonDSorter = append(bsonDSorter, bson.E{Key: "_id", Value: 1})
	return []bson.M{
		{
			"$match": condition,
		},
		{
			"$sort": sorter,
		},
		{
			"$group": bson.M{
				"_id":            "$number",
				"type":           bson.M{"$first": "$type"},
				"changedAt":      bson.M{"$first": "$changedAt"},
				"businessNumber": bson.M{"$first": "$businessNumber"},
				"operator":       bson.M{"$first": "$operator"},
				"updatedBy":      bson.M{"$first": "$updatedBy"},
				"updatedAt":      bson.M{"$first": "$updatedAt"},
				"createdAt":      bson.M{"$first": "$createdAt"},
				"productIds":     bson.M{"$addToSet": "$productId"},
				"stores":         bson.M{"$addToSet": "$store"},
				"organization":   bson.M{"$addToSet": "$organization"},
				"count":          bson.M{"$sum": "$count"},
				"provider":       bson.M{"$first": "$provider"},
			},
		},
		{
			"$sort": bsonDSorter,
		},
	}
}

func genGroupProductStockBillCountPipeline(ctx context.Context, condition bson.M, req *pb_ec_store_warehouse.ListStoreProductStockBillsRequest) []bson.M {
	return []bson.M{
		{
			"$match": condition,
		},
		{
			"$group": bson.M{
				"_id": "$number",
			},
		},
		{
			"$group": bson.M{
				"_id":   "null",
				"count": bson.M{"$sum": 1},
			},
		},
	}
}

func formatGroupStockBills(ctx context.Context, results []bson.M) (details []*pb_ec_store_warehouse.StoreProductStockBill, err error) {
	userIds, productIds, orderNos := []string{}, []bson.ObjectId{}, []string{}
	for _, r := range results {
		operator := cast.ToString(r["operator"])
		if updatedBy, ok := r["updatedBy"].(bson.ObjectId); ok {
			if !util.StrInArray(updatedBy.Hex(), &userIds) && !util.StrInArray(operator, &[]string{store_warehouse_model.OPERATOR_STOCK_BILL_REFUND, store_warehouse_model.OPERATOR_STOCK_BILL_SHIP}) {
				userIds = append(userIds, updatedBy.Hex())
			}
		}
		for _, v := range r["productIds"].([]interface{}) {
			productIds = append(productIds, v.(bson.ObjectId))
		}
		businessNumber := cast.ToString(r["businessNumber"])
		if businessNumber != "" && util.StrInArray(operator, &[]string{store_warehouse_model.OPERATOR_STOCK_BILL_REFUND, store_warehouse_model.OPERATOR_STOCK_BILL_SHIP}) {
			//销售出库或退货入库需要 memberId 和 orderId
			orderNos = append(orderNos, businessNumber)
		}
	}

	userResp, err := service.GetUserByIds(ctx, userIds)
	if err != nil {
		return
	}
	var productResp *pb_product.ListProductsByIdsResponse
	if len(productIds) != 0 {
		productResp, err = client.ProductService.ListProductsByIds(ctx, &request.IdListRequest{Ids: util.MongoIdsToStrs(productIds)})
		if err != nil {
			return
		}
	}
	orderResp, err := client.OrderService.ListOrders(ctx, &order.ListOrdersRequest{
		Numbers:      orderNos,
		WithoutTotal: true,
	})
	if err != nil {
		return
	}
	orderMap := formatOrderMap(orderResp)
	userNameMap := formatUserNameMap(userResp)
	productNameMap := formatProductNameMap(productResp)
	for _, r := range results {
		temp := &pb_ec_store_warehouse.StoreProductStockBill{
			Number:         cast.ToString(r["_id"]),
			ChangedAt:      cast.ToTime(r["changedAt"]).Format(time.RFC3339),
			BusinessNumber: cast.ToString(r["businessNumber"]),
			Operator:       cast.ToString(r["operator"]),
			Count:          cast.ToUint64(r["count"]),
			Type:           cast.ToString(r["type"]),
		}

		provider := store_warehouse_model.Provider{}
		if v, ok := r["provider"].(primitive.M); ok {
			provider.Id = (v["id"].(bson.ObjectId))
			provider.Name = v["name"].(string)
			temp.Provider = &pb_ec_store_warehouse.Provider{
				Id:   provider.Id.Hex(),
				Name: provider.Name,
			}
		}

		if !util.StrInArray(temp.Operator, &[]string{store_warehouse_model.OPERATOR_STOCK_BILL_REFUND, store_warehouse_model.OPERATOR_STOCK_BILL_SHIP}) {
			temp.UpdatorName = userNameMap[r["updatedBy"].(bson.ObjectId).Hex()]
		}

		tempStores := []string{}
		for _, v := range r["stores"].([]interface{}) {
			if store, ok := v.(primitive.M); ok {
				tempStores = append(tempStores, fmt.Sprintf("%s/%s", store["name"], store["code"]))
			}
		}
		temp.Stores = tempStores

		for _, v := range r["organization"].([]interface{}) {
			if v.(string) == "" {
				continue
			}
			if temp.Organization == "" {
				temp.Organization = v.(string)
				continue
			}
			temp.Organization = fmt.Sprintf("%s；%s", temp.Organization, v.(string))
		}
		tempProductIds := []string{}
		for _, v := range r["productIds"].([]interface{}) {
			tempProductIds = append(tempProductIds, v.(bson.ObjectId).Hex())
		}
		for _, id := range tempProductIds {
			if len(temp.ProductName) > 100 {
				break
			}
			if temName, ok := productNameMap[id]; ok {
				if temp.ProductName == "" {
					temp.ProductName = temName
				} else {
					temp.ProductName = fmt.Sprintf("%s；%s", temp.ProductName, temName)
				}
			}
		}
		if orderDetail, ok := orderMap[temp.BusinessNumber]; ok {
			temp.OrderId = orderDetail.Id
			temp.MemberId = orderDetail.MemberId
		}
		details = append(details, temp)
	}
	return
}

func handleOperators(operator string, condition bson.M) bson.M {
	if operator == "" {
		op := []string{store_warehouse_model.OPERATOR_STOCK_BILL_REFUND, store_warehouse_model.OPERATOR_STOCK_BILL_SHIP, store_warehouse_model.OPERATOR_STOCK_BILL_PURCHASE_IN, store_warehouse_model.OPERATOR_STOCK_BILL_ALLOCATE_IN, store_warehouse_model.OPERATOR_STOCK_BILL_ALLOCATE_OUT, store_warehouse_model.OPERATOR_STOCK_BILL_REFUND_TO_SUPPLIER, store_warehouse_model.OPERATOR_STOCK_BILL_OTHERS}
		condition["operator"] = bson.M{
			"$in": op,
		}
	}
	return condition
}
