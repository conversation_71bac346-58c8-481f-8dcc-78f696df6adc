package store_warehouse

import (
	"mairpc/core/extension/bson"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	store_warehouse_model "mairpc/service/ec/model/storeWarehouse"

	"golang.org/x/net/context"
)

func (StoreWarehouseService) DeleteStoreStockProviderById(ctx context.Context, req *request.DetailRequest) (*response.EmptyResponse, error) {
	err := validators.ValidateRequest(req)
	if err != nil {
		return nil, err
	}

	err = store_warehouse_model.CStoreStockProvider.DeleteById(ctx, bson.ObjectIdHex(req.Id))
	if err != nil {
		return nil, err
	}
	return &response.EmptyResponse{}, nil
}
