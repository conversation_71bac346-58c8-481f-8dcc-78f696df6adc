package store_warehouse

import (
	"errors"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/account"
	"mairpc/proto/common/request"
	"mairpc/proto/ec/order"
	"mairpc/proto/ec/product"
	pb_ec_store_warehouse "mairpc/proto/ec/storeWarehouse"
	"mairpc/service/ec/client"
	store_warehouse_model "mairpc/service/ec/model/storeWarehouse"
	"mairpc/service/ec/service"
	"mairpc/service/share/util"

	"golang.org/x/net/context"

	"mairpc/core/extension/bson"
)

func (StoreWarehouseService) ListProductStockBills(ctx context.Context, req *pb_ec_store_warehouse.ListProductStockBillsRequest) (*pb_ec_store_warehouse.ListProductStockBillsResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if req.Sku == "" || req.ProductId == "" {
		return nil, errors.New("Can not get source for empty Sku or ProductId")
	}
	condition, err := getListProductStockBillsCondition(ctx, req)
	if err != nil {
		return nil, err
	}
	pageCondition := util.FormatPagingCondition(condition, req.ListCondition)
	stockBills, total := store_warehouse_model.CStoreStockBill.FindByPagination(ctx, pageCondition)
	items, err := formatStockBills(ctx, stockBills)
	if err != nil {
		return nil, err
	}
	return &pb_ec_store_warehouse.ListProductStockBillsResponse{
		Total: uint64(total),
		Items: items,
	}, nil
}

func formatStockBills(ctx context.Context, stockBills []store_warehouse_model.StoreStockBill) (details []*pb_ec_store_warehouse.ProductStockBill, err error) {
	userIds, productIds, orderNos := []string{}, []string{}, []string{}
	for _, item := range stockBills {
		if !util.StrInArray(item.UpdatedBy.Hex(), &userIds) && !util.StrInArray(item.Operator, &[]string{store_warehouse_model.OPERATOR_STOCK_BILL_REFUND, store_warehouse_model.OPERATOR_STOCK_BILL_SHIP}) {
			userIds = append(userIds, item.UpdatedBy.Hex())
		}
		if !util.StrInArray(item.ProductId.Hex(), &productIds) {
			productIds = append(productIds, item.ProductId.Hex())
		}
		if item.BusinessNumber != "" && util.StrInArray(item.Operator, &[]string{store_warehouse_model.OPERATOR_STOCK_BILL_REFUND, store_warehouse_model.OPERATOR_STOCK_BILL_SHIP}) {
			//销售出库或退货入库需要 memberId 和 orderId
			orderNos = append(orderNos, item.BusinessNumber)
		}
	}

	userResp, err := service.GetUserByIds(ctx, userIds)
	if err != nil {
		return
	}
	var productResp *product.ListProductsByIdsResponse
	if len(productIds) != 0 {
		productResp, err = client.ProductService.ListProductsByIds(ctx, &request.IdListRequest{Ids: productIds})
		if err != nil {
			return
		}
	}
	var orderResp *order.ListOrdersResponse
	if len(orderNos) > 0 {
		orderResp, err = client.OrderService.ListOrders(ctx, &order.ListOrdersRequest{
			Numbers:      orderNos,
			WithoutTotal: true,
		})
		if err != nil {
			return
		}
	}
	orderMap := formatOrderMap(orderResp)
	userNameMap := formatUserNameMap(userResp)
	productNameMap := formatProductNameMap(productResp)
	for _, item := range stockBills {
		temp := &pb_ec_store_warehouse.ProductStockBill{}
		core_util.CopyRFC3339(item, temp)
		if !util.StrInArray(item.Operator, &[]string{store_warehouse_model.OPERATOR_STOCK_BILL_REFUND, store_warehouse_model.OPERATOR_STOCK_BILL_SHIP}) {
			temp.UpdatorName = userNameMap[item.UpdatedBy.Hex()]
		}
		temp.ProductName = productNameMap[item.ProductId.Hex()]
		if orderDetail, ok := orderMap[item.BusinessNumber]; ok {
			temp.OrderId = orderDetail.Id
			temp.MemberId = orderDetail.MemberId
		}
		temp.StoreName = item.Store.Name
		temp.StoreCode = item.Store.Code
		temp.AvailableStock = uint64(item.AvailableStock)
		temp.Price = item.Price
		details = append(details, temp)
	}
	return
}

func formatUserNameMap(userResp *account.UserListResponse) map[string]string {
	nameMap := make(map[string]string)
	for _, item := range userResp.Items {
		nameMap[item.Id] = item.Name
	}
	return nameMap
}

func formatProductNameMap(productResp *product.ListProductsByIdsResponse) map[string]string {
	nameMap := make(map[string]string)
	if productResp != nil {
		for _, item := range productResp.Products {
			nameMap[item.Id] = item.Name
		}
	}
	return nameMap
}

func formatOrderMap(orderResp *order.ListOrdersResponse) map[string]*order.OrderDetail {
	orderMap := make(map[string]*order.OrderDetail)
	if orderResp != nil {
		for i, o := range orderResp.Items {
			orderMap[o.Number] = orderResp.Items[i]
		}
	}
	return orderMap
}

func getListProductStockBillsCondition(ctx context.Context, req *pb_ec_store_warehouse.ListProductStockBillsRequest) (bson.M, error) {
	condition := store_warehouse_model.Common.GenDefaultCondition(ctx)
	if bson.IsObjectIdHex(req.ProductId) {
		condition["productId"] = bson.ObjectIdHex(req.ProductId)
	}
	if req.Sku != "" {
		condition["sku"] = req.Sku
	}
	if req.Operator != "" {
		condition["operator"] = req.Operator
	}

	if req.Providers != nil && len(req.Providers) > 0 {
		condition["provider.id"] = bson.M{
			"$in": util.ToMongoIds(req.Providers),
		}
	}

	if req.StoreId != "" {
		condition["store.id"] = bson.ObjectIdHex(req.StoreId)
	}
	if req.Store != "" {
		storeIds, _ := GetStoreIdsByQuery(ctx, req.Store)
		condition["store.id"] = bson.M{
			"$in": storeIds,
		}
	}
	if req.UpdateBy != "" {
		condition["updatedBy"] = bson.ObjectIdHex(req.UpdateBy)
	}
	if req.Type != "" {
		condition["type"] = req.Type
	}
	if req.TimeRange != nil {
		condition["changedAt"] = util.ParseStringDateRange(req.TimeRange)
	}
	if req.QueryString != "" {
		productIds, err := GetProductIdsByQuery(ctx, req.QueryString)
		if err != nil {
			return bson.M{}, err
		}
		skus, _, err := GetProductSkusByExternalSkus(ctx, []string{req.QueryString})
		if err != nil {
			return bson.M{}, err
		}
		orCondition := []bson.M{
			{
				"productId": bson.M{
					"$in": productIds,
				},
			},
			{
				"sku": bson.M{
					"$in": skus,
				},
			},
			{
				"sku": req.QueryString,
			},
			{
				"number": req.QueryString,
			},
			{
				"businessNumber": req.QueryString,
			},
		}
		condition["$or"] = orCondition
		condition = util.FormatConditionContainedOr(condition)
	}
	return condition, nil
}
