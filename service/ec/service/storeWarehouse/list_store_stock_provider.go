package store_warehouse

import (
	"mairpc/core/extension/bson"
	pb_ec_store_warehouse "mairpc/proto/ec/storeWarehouse"
	store_warehouse_model "mairpc/service/ec/model/storeWarehouse"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

func (StoreWarehouseService) ListStoreStockProvider(ctx context.Context, req *pb_ec_store_warehouse.ListStoreStockProviderRequest) (*pb_ec_store_warehouse.ListStoreStockProviderResponse, error) {
	condition := share_model.Base.GenDefaultCondition(ctx)
	if req.QueryString != "" {
		condition["name"] = bson.M{"$regex": req.QueryString, "$options": "i"}
	}

	total, providers, err := store_warehouse_model.CStoreStockProvider.FindByCondition(ctx, util.FormatPagingCondition(condition, req.ListCondition))
	if err != nil {
		return nil, err
	}
	resp := &pb_ec_store_warehouse.ListStoreStockProviderResponse{
		Total: int32(total),
	}
	for _, p := range providers {
		resp.List = append(resp.List, &pb_ec_store_warehouse.StoreStockProviderResponse{
			Id:        p.Id.Hex(),
			AccountId: p.AccountId.Hex(),
			Name:      p.Name,
			CreatedAt: p.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
			UpdatedAt: p.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
		})
	}
	return resp, nil
}
