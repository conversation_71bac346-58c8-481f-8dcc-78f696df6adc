package retailer

import (
	"fmt"
	core_component "mairpc/core/component"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	ec_model "mairpc/service/ec/model"
	ec_order "mairpc/service/ec/model/order"
	ec_profitsharing "mairpc/service/ec/model/profitsharing"
	ec_retailer "mairpc/service/ec/model/retailer"
	share_component "mairpc/service/share/component"
	"mairpc/service/share/util"
	"strings"
	"time"

	"github.com/asaskevich/govalidator"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

const (
	LOCK_KEY_CHECK_AND_UPDATE_RETAILER_BILL      = "ec:check_and_update_retailer_bill:%s"
	LOCK_LIFETIME_CHECK_AND_UPDATE_RETAILER_BILL = 180 // 3 min
)

// 通过 orderReceiverProfit 的状态来更新 retailerBill 的状态
func (RetailerService) CheckAndUpdateRetailerBillStatusJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	if !ec_model.IsMaimengRetail(ctx) && !ec_model.IsChainRetail(ctx) {
		return &response.EmptyResponse{}, nil
	}

	lockKey := fmt.Sprintf(LOCK_KEY_CHECK_AND_UPDATE_RETAILER_BILL, util.GetAccountId(ctx))
	if ok, _ := extension.RedisClient.SetNX(lockKey, "1", LOCK_LIFETIME_CHECK_AND_UPDATE_RETAILER_BILL); !ok {
		return &response.EmptyResponse{}, nil
	}
	defer extension.RedisClient.Del(lockKey)

	billIter, err := ec_retailer.CRetailerBill.IterateProcessing(ctx)
	if err != nil {
		return nil, err
	}
	defer billIter.Close()

	billStatusMap := map[string]string{}
	bill := ec_retailer.RetailerBill{}
	for billIter.Next(&bill) {
		if !bill.OrderReceiverProfitId.Valid() {
			continue
		}
		logFields := log.Fields{
			"billId":                bill.Id.Hex(),
			"orderReceiverProfitId": bill.OrderReceiverProfitId.Hex(),
		}
		profit, err := ec_order.COrderReceiverProfit.GetById(ctx, bill.OrderReceiverProfitId)
		if err != nil {
			logFields["errorMessage"] = err.Error()
			log.Error(ctx, "Failed to find orderReceiverProfit for retailerBill", logFields)
			continue
		}
		if profit.Status != ec_order.ORDER_RECEIVER_PROFIT_STATUS_SUCCESS &&
			profit.Status != ec_order.ORDER_RECEIVER_PROFIT_STATUS_FAILED {
			continue
		}

		// 收入和支出类型的账单同时查出处理
		retailerBills := ec_retailer.CRetailerBill.GetAllByCondition(ctx, bson.M{
			"accountId":             util.GetAccountIdAsObjectId(ctx),
			"isDeleted":             false,
			"order.id":              bill.Order.Id,
			"orderReceiverProfitId": bill.OrderReceiverProfitId,
		})
		if len(retailerBills) == 0 {
			continue
		}
		logFields["retailerBills"] = core_util.ExtractArrayStringField("Id", retailerBills)
		for _, retailerBill := range retailerBills {
			if status, ok := billStatusMap[retailerBill.Id.Hex()]; ok && status != ec_retailer.BILL_STATUS_PROCESSING {
				continue
			}
			switch profit.Status {
			case ec_order.ORDER_RECEIVER_PROFIT_STATUS_SUCCESS:
				retailerBill.Status = ec_retailer.BILL_STATUS_SUCCESS
				retailerBill.DetailId = getDivideDetailNo(ctx, retailerBill.TransferBillId)
				retailerBill.FinishedAt = profit.FinishedAt
			case ec_order.ORDER_RECEIVER_PROFIT_STATUS_FAILED:
				retailerBill.Status = ec_retailer.BILL_STATUS_FAILED
				retailerBill.FailedMessage = profit.Remarks
			}
			err = retailerBill.Update(ctx)
			if err != nil {
				logFields["retailerBillId"] = retailerBill.Id.Hex()
				logFields["errorMessage"] = err.Error()
				log.Error(ctx, "Failed to update retailer bill status by orderReceiverProfit", logFields)
				continue
			}
			billStatusMap[retailerBill.Id.Hex()] = retailerBill.Status
			if retailerBill.Status == ec_retailer.BILL_STATUS_SUCCESS {
				tempBill := retailerBill
				core_component.GO(ctx, func(ctx context.Context) {
					UpsertDistributionBill(ctx, tempBill)
				})
			}
		}
		if profit.Status == ec_order.ORDER_RECEIVER_PROFIT_STATUS_SUCCESS {
			orderId := bill.Order.Id
			core_component.GO(ctx, func(ctx context.Context) {
				updateRetailerOrderDiviedStatus(ctx, orderId)
			})
		}
	}
	return &response.EmptyResponse{}, nil
}

func getDivideDetailNo(ctx context.Context, transferBillId bson.ObjectId) string {
	bill, err := ec_profitsharing.CTransferBill.GetById(ctx, transferBillId)
	if err != nil {
		return ""
	}
	// TODO: 改用 detailIds
	return bill.DetailId
}

func UpsertDistributionBill(ctx context.Context, bill ec_retailer.RetailerBill) {
	amount := cast.ToString(util.DivideFloatWithRound(float64(bill.TotalAmount), 100, 2))
	divideDesc := bill.GetDivideDesc(ctx, "", bill.Order.Id, bill.Order.Number, amount)
	req := &share_component.UpsertDistributionBillRequest{
		OuterId:        bill.Id.Hex(),
		Source:         "CONSIGNMENT", // 经销商那边区分账单来源，CONSIGNMENT 零售业务账单、DISTRIBUTION 批销业务账单
		AccountId:      bill.AccountId.Hex(),
		CreatedAt:      bill.CreatedAt.Format(time.RFC3339),
		UpdatedAt:      bill.UpdatedAt.Format(time.RFC3339),
		IsDeleted:      bill.IsDeleted,
		BrandId:        bill.Order.BrandId.Hex(),
		BusinessType:   FormatBusinessType(bill.BusinessType),
		Description:    divideDesc,
		DivideDetailNo: bill.DetailId,
		Type:           strings.ToUpper(bill.Type),
		IncomeType: func() string {
			if bill.IncomeType != "" {
				return FormatCategory(bill, bill.IncomeType)
			}
			if bill.SpentType != "" {
				return FormatCategory(bill, bill.SpentType)
			}
			return ""
		}(),
		Subject:           FormatDistributionBillAccount(ctx, bill.Subject),
		Opposite:          FormatDistributionBillAccount(ctx, bill.Opposite),
		Order:             FormatDistributionBillOrder(ctx, bill),
		Status:            strings.ToUpper(bill.Status),
		ProcessingAt:      bill.ProcessingAt.Format(time.RFC3339),
		FinishedAt:        bill.FinishedAt.Format(time.RFC3339),
		TotalAmount:       int64(bill.TotalAmount),
		YeepayAccountType: bill.YeepayAccountType,
	}
	_, err := share_component.Retailer.UpsertDistributionBill(ctx, req)
	if err != nil {
		log.Warn(ctx, "Failed to upsert distribution bill", log.Fields{
			"req": req,
			"err": err.Error(),
		})
	}
}

func CamelCaseToUpperUnderscore(str string) string {
	return strings.ToUpper(govalidator.CamelCaseToUnderscore(str))
}

func FormatBusinessType(t string) string {
	return CamelCaseToUpperUnderscore(t)
}

func FormatCategory(dbBill ec_retailer.RetailerBill, t string) string {
	if t == ec_retailer.BILL_INCOME_CATEGORY_RECEIPT {
		return "INCOME_FEE"
	}
	if dbBill.IsPlatformFeeCondForD1() {
		return "PLATFORM_DIVIDE_FEE"
	}
	return CamelCaseToUpperUnderscore(t)
}

func FormatDistributionBillAccount(ctx context.Context, dbBillAccount ec_retailer.Account) share_component.BillAccount {
	account := share_component.BillAccount{}
	copier.Instance(nil).From(dbBillAccount).CopyTo(&account)
	account.ParentMerchantNo = ec_retailer.GetParentMerchantNo(ctx)
	if account.Type == ec_retailer.BILL_ACCOUNT_TYPE_RETAIL {
		account.Type = "RETAILER"
	} else {
		account.Type = CamelCaseToUpperUnderscore(account.Type)
	}
	account.SubType = CamelCaseToUpperUnderscore(account.SubType)
	return account
}

func FormatDistributionBillOrder(ctx context.Context, bill ec_retailer.RetailerBill) share_component.Order {
	order := share_component.Order{}
	copier.Instance(nil).From(bill.Order).CopyTo(&order)
	order.Payment = "WECHAT"
	order.MemberId = bill.Order.Member.Id.Hex()
	order.MerchantNo = ec_retailer.GetYeepayMerchantNo(ctx)
	order.ParentMerchantNo = ec_retailer.GetParentMerchantNo(ctx)

	retailerOrder, _ := ec_retailer.CRetailerOrder.GetBySubOrderId(ctx, bill.Order.Id)
	if retailerOrder.Id.Valid() {
		subOrder := retailerOrder.GetSubOrder(bill.Order.Id.Hex())
		order.IsMasterPay = retailerOrder.IsAggPay()
		order.MasterOrderId = retailerOrder.Id.Hex()
		order.TradeNo = subOrder.TradeNo
		order.YeepayTradeNo = subOrder.YeePayOrderNo
		if retailerOrder.IsAggPay() {
			order.TradeNo = retailerOrder.TradeNo
			order.YeepayTradeNo = retailerOrder.YeePayOrderNo
		}
	}

	ecOrder, _ := ec_order.COrder.GetById(ctx, bill.Order.Id)
	if ecOrder.Id.Valid() {
		order.PayAmount = int64(ecOrder.PayAmount)
		order.TotalAmount = int64(ecOrder.TotalAmount)
	}
	return order
}

func updateRetailerOrderDiviedStatus(ctx context.Context, subOrderId bson.ObjectId) {
	retailOrder, _ := ec_retailer.CRetailerOrder.GetBySubOrderId(ctx, subOrderId)
	if !retailOrder.Id.Valid() {
		return
	}
	for _, subOrder := range retailOrder.SubOrders {
		if subOrder.Id.Hex() == subOrderId.Hex() && subOrder.DivideStatus == ec_retailer.DIVIDE_STATUS_SUCCESS {
			return
		}
	}

	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"order.id":  subOrderId,
		"isDeleted": false,
	}
	orderReceiverProfits, err := ec_order.COrderReceiverProfit.GetAllByCondition(ctx, condition)
	if err != nil {
		log.Warn(ctx, "Failed to get order receiver profit", log.Fields{
			"err": err.Error(),
		})
		return
	}
	successCount := 0
	for _, bill := range orderReceiverProfits {
		if bill.Status == ec_order.ORDER_RECEIVER_PROFIT_STATUS_SUCCESS {
			successCount += 1
		}
	}
	log.Warn(ctx, "updateRetailerOrderDiviedStatus log", log.Fields{
		"subOrderId":           subOrderId.Hex(),
		"successCount":         successCount,
		"orderReceiverProfits": orderReceiverProfits,
	})
	// orderReceiverProfits 肯定有多条，只有一条时只可能是支付后立即分账的品牌货款，无需更新订分账状态
	if len(orderReceiverProfits) == successCount && len(orderReceiverProfits) > 1 {
		for _, subOrder := range retailOrder.SubOrders {
			if subOrder.Id.Hex() == subOrderId.Hex() {
				subOrder.DivideStatus = ec_retailer.DIVIDE_STATUS_SUCCESS
			}
		}
		retailOrder.Update(ctx)
	}
}
