package retailer

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"mairpc/core/component"
	"mairpc/core/component/yeepay"
	core_error "mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/core/validators"
	"mairpc/proto/common/response"
	pb_retailer "mairpc/proto/ec/retailer"
	ec_wallet "mairpc/proto/ec/wallet"
	"mairpc/service/ec/client"
	ec_distribution "mairpc/service/ec/model/distribution"
	ec_order "mairpc/service/ec/model/order"
	ec_profitsharing "mairpc/service/ec/model/profitsharing"
	ec_retailer "mairpc/service/ec/model/retailer"
	ec_store "mairpc/service/ec/model/store"
	order_service "mairpc/service/ec/service/order"
	"mairpc/service/share/util"
	"time"

	"github.com/spf13/cast"
)

// https://open.yeepay.com/docs/apis/INDUSTRY_SOLUTION/GENERAL/fwssfk/jiaoyi/options__rest__v1.0__trade__refund#anchor7
type RefundNotifyData struct {
	OrderId                  string `json:"orderId"`                  // 收款交易对应的商户收款请求单号
	ErrorMessage             string `json:"errorMessage"`             // 只有退款失败时回传
	BankPromotionInfoDTOList string `json:"bankPromotionInfoDTOList"` // 当有微信/支付宝/云闪付渠道侧营销退款退回优惠时会展示此字段
	UniqueOrderNo            string `json:"uniqueOrderNo"`            // 收款交易对应在易宝的收款单号
	RefundRequestId          string `json:"refundRequestId"`          // 退款请求号
	RefundSuccessDate        string `json:"refundSuccessDate"`        // 只有退款成功时回传
	OrderAmount              string `json:"orderAmount"`              // 订单金额，下单请求金额，单位:元
	CashRefundFee            string `json:"cashRefundFee"`            // 当支付方式为微信/支付宝/云闪付且参加渠道侧优惠时，退款可能退优惠，此字段为扣除优惠后实际退回用户金额
	UniqueRefundNo           string `json:"uniqueRefundNo"`           // 商户退款请求对应在易宝的退款单号
	ParentMerchantNo         string `json:"parentMerchantNo"`         // 交易发起方商编
	YpPromotionInfoDTOList   string `json:"ypPromotionInfoDTOList"`
	RealRefundAmount         string `json:"realRefundAmount"`  // 用户付手续费场景下,实际退款金额=退款金额 退费金额
	RefundRequestDate        string `json:"refundRequestDate"` // 退款请求时间
	MerchantNo               string `json:"merchantNo"`        // 收款商户商编
	RefundAmount             string `json:"refundAmount"`      // 退款金额
	Status                   string `json:"status"`            // SUCCESS：退款成功FAILED:退款失败CANCEL:退款关闭(商户线下通知易宝结束该笔退款后返回该状态)
	ReturnMerchantFee        string `json:"returnMerchantFee"` // 退还商户手续费
}

const (
	REFUND_STATUS_SUCCESS = "SUCCESS"
	REFUND_STATUS_FAILED  = "FAILED"

	LOCK_KEY_RETAILER_ORDER      = "%s:ec:retailerOrder:%s"
	LOCK_LIFETIME_RETAILER_ORDER = 60
)

func (RetailerService) OrderRefundNotify(ctx context.Context, req *pb_retailer.YeePayNotify) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if req.Response == "" || req.CustomerIdentification == "" || util.GetAccountId(ctx) == "" {
		return &response.EmptyResponse{}, nil
	}
	dataStr, err := yeepay.DecryptData(ctx, req.Response)
	if err != nil {
		log.Warn(ctx, "Decrypt refund notify data fail", log.Fields{
			"errMessage": err.Error(),
		})
		return nil, err
	}

	data := RefundNotifyData{}
	err = json.Unmarshal([]byte(dataStr), &data)
	if err != nil {
		return nil, err
	}

	log.Warn(ctx, "OrderRefundNotify data", log.Fields{
		"data": data,
	})

	// 防止多次回调，这里使用协程处理
	component.GO(ctx, func(ctx context.Context) {
		err := handleOrderRefundNotifyData(ctx, data)
		if err != nil {
			log.Warn(ctx, "Handle orderRefundNotify data error", log.Fields{
				"errMessage": err.Error(),
				"data":       data,
			})
		}
	})
	return &response.EmptyResponse{}, nil
}

func handleOrderRefundNotifyData(ctx context.Context, data RefundNotifyData) error {
	orderRefund, err := ec_order.COrderRefund.GetById(ctx, util.ToMongoId(data.RefundRequestId))
	if err != nil {
		return err
	}
	order, err := ec_order.COrder.GetById(ctx, orderRefund.OrderId)
	if err != nil {
		return err
	}

	switch data.Status {
	case REFUND_STATUS_SUCCESS:
		retailerOrder, err := getRetailerOrderByOrderRefund(ctx, orderRefund)
		if err != nil {
			return err
		}
		if retailerOrder == nil {
			return errors.New("RetailerOrder not found")
		}
		nowUnixMilli, err := getRetailerOrderLock(ctx, retailerOrder.Id.Hex())
		if err != nil {
			return err
		}
		defer delRetailerOrderLock(ctx, retailerOrder.Id.Hex(), nowUnixMilli)
		retailerOrder = ec_retailer.CRetailerOrder.GetById(ctx, retailerOrder.Id)
		// 历史订单没有 receivers，需要兼容一下
		retailerOrder.UpdateReceivers(ctx, orderRefund.OrderId.Hex())
		updateOrderRefundStatus(ctx, orderRefund, ec_order.ORDER_REFUND_STATUS_REFUNDED, "易宝退款完成")
		orderRefund.SendRefundEvent(ctx)
		orderRefund.SendRefundProductEvent(ctx)

		handleDistributionOrderRefund(ctx, order, orderRefund)
		updateRetailerOrderAmount(ctx, retailerOrder)
		handleAllRelevantBillsAmount(ctx, *retailerOrder, orderRefund.OrderId, orderRefund.Id)
		ec_retailer.CRetailerBill.UpdateRefundFields(ctx, orderRefund.OrderId, int64(orderRefund.RefundAmount))
		handleRetailerBillStatus(ctx, *retailerOrder, orderRefund.OrderId)
		retailerOrder.IncDiscountCampaignSales(ctx, orderRefund.OrderId.Hex(), true)
	case REFUND_STATUS_FAILED:
		updateOrderRefundStatus(ctx, orderRefund, ec_order.ORDER_REFUND_STATUS_FAILED, data.ErrorMessage)
	}
	return nil
}

func getRetailerOrderLock(ctx context.Context, retailerOrderId string) (string, error) {
	lockKey := fmt.Sprintf(LOCK_KEY_RETAILER_ORDER, util.GetAccountId(ctx), retailerOrderId)
	threshold := time.Now().Add(10 * time.Second)
	for {
		now := time.Now()
		value := cast.ToString(now.UnixMilli())
		ok, _ := extension.RedisClient.SetNX(lockKey, value, LOCK_LIFETIME_RETAILER_ORDER)
		if ok {
			return value, nil
		}
		if now.After(threshold) {
			return "", core_error.NewTooManyRequestsError("retailerOrderId")
		}
		time.Sleep(time.Millisecond * 500)
	}
}

func delRetailerOrderLock(ctx context.Context, retailerOrderId string, nowUnixMilli string) {
	lockKey := fmt.Sprintf(LOCK_KEY_RETAILER_ORDER, util.GetAccountId(ctx), retailerOrderId)
	value, _ := extension.RedisClient.Get(lockKey)
	if value == nowUnixMilli {
		extension.RedisClient.Del(lockKey)
	}
}

func handleDistributionOrderRefund(ctx context.Context, order ec_order.Order, orderRefund *ec_order.OrderRefund) {
	if !order.Distribution.PromoterId.Valid() {
		return
	}
	// 退款时大众分销部分的预分账单还没有进行分账才可以减扣大众分销员的累计收益和待结算收益以及客户的累计贡献佣金
	if !ec_order.COrderReceiverProfit.IsDistOrderProcessed(ctx, order.Id) {
		// 导购分销退款后还可以继续分账，所以减扣金额时只减退款商品的佣金
		refundDistributionAmount := uint64(0)
		refundProductCount := uint64(0)
		for _, product := range order.Products {
			for _, refundProduct := range orderRefund.Products {
				if product.OutTradeId.Hex() == refundProduct.OutTradeId.Hex() {
					refundDistributionAmount += product.DistributionAmount
					refundProductCount += product.Total
				}
			}
		}
		handleDistributionOrder(ctx, order, refundDistributionAmount, refundProductCount)
		// 更新 distribution amount
		if order.Distribution.Amount >= refundDistributionAmount {
			order.Distribution.Amount -= refundDistributionAmount
			// 已全部退款，更新 profitSharingStatus
			if order.Distribution.Amount == 0 && order.Status == ec_order.ORDER_STATUS_CANCELED {
				order.Distribution.ProfitSharingStatus = ec_order.DISTRIBUTION_STATUS_REFUNDED
			}
		}
		order.UpdateDistribution(ctx)
		order_service.HandelDistOrderRefund(ctx, orderRefund)
	}
}

func handleDistributionOrder(ctx context.Context, order ec_order.Order, refundAmount, refundProductCount uint64) {
	// 非分销订单或者已经处理过的分销订单不作处理
	if order.Distribution.PromoterId.Hex() == "" ||
		order.Distribution.ProfitSharingStatus == ec_order.DISTRIBUTION_STATUS_REFUNDED {
		return
	}

	promoter, err := ec_distribution.CPromoter.GetById(ctx, order.Distribution.PromoterId)
	if err != nil {
		log.Warn(ctx, "Failed to get promoter", log.Fields{
			"promoterId":   order.Distribution.PromoterId,
			"errorMessage": err.Error(),
		})
		return
	}

	if ec_order.NeedDecProfitAmount(ctx, &order) {
		// 扣除分销员的累计收益和待结算收益
		err = promoter.IncProfitAmount(ctx, -int64(refundAmount), -int64(refundAmount))
		if err != nil {
			log.Warn(ctx, "Failed to update promoter total profit amount", log.Fields{
				"promoterId":        promoter.Id,
				"totalProfitAmount": -order.Distribution.Amount,
				"errorMessage":      err.Error(),
			})
		}
	}

	// 扣减累计分销商品数
	err = promoter.UpdatePromoterCountField(ctx, &ec_distribution.PromoterCount{
		Product: -int64(refundProductCount),
	}, util.GetMonthCountKey(order.PaidAt, 0))
	if err != nil {
		log.Warn(ctx, "Failed to update promoter total products", log.Fields{
			"promoterId":   promoter.Id,
			"errorMessage": err.Error(),
		})
	}
}

func getRetailerOrderByOrderRefund(ctx context.Context, orderRefund *ec_order.OrderRefund) (*ec_retailer.RetailerOrder, error) {
	condition := bson.M{
		"accountId":    util.GetAccountIdAsObjectId(ctx),
		"isDeleted":    false,
		"subOrders.id": orderRefund.OrderId,
	}
	updater := bson.M{
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
		"$push": bson.M{
			"refundOrderIds": orderRefund.Id,
		},
	}
	retailerOrder, err := ec_retailer.CRetailerOrder.FindAndApply(ctx, condition, updater)
	if err != nil {
		return nil, err
	}
	return retailerOrder, nil
}

func updateOrderRefundStatus(ctx context.Context, orderRefund *ec_order.OrderRefund, status string, msg string) error {
	orderRefund.Status = status
	orderRefund.GenerateHistory(ec_order.OPERATOR_SYSTEM, msg, bson.M{})
	// 储值卡金额回退
	if status == ec_order.ORDER_REFUND_STATUS_REFUNDED {
		for i, prepaidCardRefund := range orderRefund.PrepaidCardRefunds {
			if prepaidCardRefund.Status == ec_order.ORDER_REFUND_STATUS_REFUNDED {
				continue
			}
			req := &ec_wallet.PartialRevokeAmountRequest{
				PrepaidCardId: prepaidCardRefund.PrepaidCardId.Hex(),
				Amount:        prepaidCardRefund.RefundAmount,
				MemberId:      orderRefund.MemberId.Hex(),
				OrderId:       orderRefund.OrderId.Hex(),
				TradeNo:       prepaidCardRefund.OriginTradeNo,
				RefundTradeNo: prepaidCardRefund.TradeNo,
				OrderNumber:   orderRefund.OrderNumber,
				SystemDate:    orderRefund.OrderId.Time().Format(time.RFC3339),
				TradeSeq:      prepaidCardRefund.OutTradeSeq,
				SystemSeq:     prepaidCardRefund.TransactionId,
				Reason:        "订单退款",
			}
			_, err := client.WalletService.PartialRevokeAmount(ctx, req)
			if err != nil {
				log.Warn(ctx, "Failed to refund prepaid card", log.Fields{
					"errMsg":            err.Error(),
					"prepaidCardRefund": prepaidCardRefund,
					"req":               req,
				})
				orderRefund.PrepaidCardRefunds[i].Status = ec_order.ORDER_REFUND_STATUS_FAILED
			} else {
				orderRefund.PrepaidCardRefunds[i].Status = ec_order.ORDER_REFUND_STATUS_REFUNDED
			}
		}
	}

	selector := bson.M{
		"_id":       orderRefund.Id,
		"accountId": orderRefund.AccountId,
	}
	settor := bson.M{
		"histories": orderRefund.Histories,
		"status":    status,
		"updatedAt": time.Now(),
	}
	if len(orderRefund.PrepaidCardRefunds) > 0 {
		settor["prepaidCardRefunds"] = orderRefund.PrepaidCardRefunds
	}
	if status == ec_order.ORDER_REFUND_STATUS_REFUNDED {
		orderRefund.RefundedAt = time.Now()
		settor["refundedAt"] = orderRefund.RefundedAt
	}
	if status == ec_order.ORDER_REFUND_STATUS_FAILED {
		settor["failedReason"] = msg
	}
	updater := bson.M{
		"$set": settor,
	}

	err := ec_order.Common.UpdateOne(ctx, ec_order.C_ORDER_REFUND, "false", selector, updater)
	if err != nil {
		return err
	}

	productSkus := []ec_order.ProductSkuRefundStatus{}
	for _, product := range orderRefund.Products {
		productSkus = append(productSkus, ec_order.ProductSkuRefundStatus{
			Status:        orderRefund.Status,
			OutTradeId:    product.OutTradeId,
			OrderRefundId: orderRefund.Id,
		})
	}

	err = ec_order.COrder.UpdateProductRefundStatus(ctx, orderRefund.OrderId, productSkus)
	if err != nil {
		log.Error(ctx, "Failed to update yeepay order refund status", log.Fields{
			"id":           orderRefund.OrderId.Hex(),
			"refundId":     orderRefund.Id.Hex(),
			"errorMessage": err.Error(),
		})
	}

	ec_order.HandleOrderRefundStatusChanged(ctx, orderRefund, orderRefund.Status, nil)
	return nil
}

// 退款完成后重新计订单相关金额
// 可能多次回调，所以不能依赖回调中的 refundOrderId，需查询 OrderRefund 计算
func updateRetailerOrderAmount(ctx context.Context, retailerOrder *ec_retailer.RetailerOrder) {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id": bson.M{
			"$in": retailerOrder.RefundOrderIds,
		},
		"isDeleted": false,
	}
	refundOrders, _ := ec_order.COrderRefund.GetAllByCondition(ctx, condition)
	refundOrderMap := map[string][]ec_order.OrderRefund{}
	for _, refundOrder := range refundOrders {
		if _, ok := refundOrderMap[refundOrder.OrderId.Hex()]; ok {
			refundOrderMap[refundOrder.OrderId.Hex()] = append(refundOrderMap[refundOrder.OrderId.Hex()], refundOrder)
		} else {
			refundOrderMap[refundOrder.OrderId.Hex()] = []ec_order.OrderRefund{refundOrder}
		}
	}

	totalRefundAmount := uint64(0)
	for _, subOrder := range retailerOrder.SubOrders {
		subOrderRefundAmount := uint64(0)
		if refundOrders, ok := refundOrderMap[subOrder.Id.Hex()]; ok {
			for _, refundOrder := range refundOrders {
				subOrderRefundAmount += refundOrder.RefundAmount
				totalRefundAmount += refundOrder.RefundAmount
			}
		}
		subOrder.RefundAmount = subOrderRefundAmount
	}
	retailerOrder.RefundAmount = totalRefundAmount
	// 注意 SetProfitAmount 一定要放在最后一步
	retailerOrder.SetProfitAmount(ctx, true)
	retailerOrder.Update(ctx)
}

func handleAllRelevantBillsAmount(ctx context.Context, retailerOrder ec_retailer.RetailerOrder, orderId, refundId bson.ObjectId) {
	for _, subOrder := range retailerOrder.SubOrders {
		order, err := ec_order.COrder.GetById(ctx, subOrder.Id)
		if err != nil {
			continue
		}

		if retailerOrder.IsAggPay() {
			handleAllRelevantBillsAmountForSubOrder(ctx, subOrder, order, refundId)
			continue
		}

		if subOrder.Id.Hex() == orderId.Hex() {
			handleAllRelevantBillsAmountForSubOrder(ctx, subOrder, order, refundId)
			break
		}
	}
}

func handleAllRelevantBillsAmountForSubOrder(ctx context.Context, subOrder *ec_retailer.SubOrder, order ec_order.Order, refundId bson.ObjectId) {
	// 子单已分账完成，不需要做后续操作
	if subOrder.IsDivided() {
		log.Warn(ctx, "SubOrder divided for handleAllRelevantBillsAmountForSubOrder", log.Fields{
			"subOrder": subOrder,
			"refundId": refundId.Hex(),
		})
		return
	}
	deleteOldBillData(ctx, subOrder.Id)
	updateProfitAmountWithDiscountCampaign(ctx, &order, *subOrder)
	subOrder.ProductProfitAmount = GetSubOrderStoreProfitAmount(ctx, &order, *subOrder)
	subOrder.OriginalAmount = GetD0ConsignmentSubOrderOriginalPrice(*subOrder, order)
	subOrder.StaffProfitAmount, subOrder.ActualStaffProfitAmount = GetSubOrderStaffProfitAmount(ctx, &order, *subOrder)
	isOrderCompleted := order.HasBeenCompleted()
	if isOrderCompleted {
		CreateOrderReceiverProfit(ctx, subOrder, order)
	} else {
		onlyUpdateAmountForRetailerBills(ctx, subOrder, order, refundId)
	}
}

func handleRetailerBillStatus(ctx context.Context, retailerOrder ec_retailer.RetailerOrder, orderId bson.ObjectId) {
	// 合单支付/子单支付，子订单未全部退款时，这里不需要处理，账单金额数据会在 createOrderReceiverProfit/onlyUpdateAmountForRetailerBills 中更新
	subOrderIds := []bson.ObjectId{}
	for _, subOrder := range retailerOrder.SubOrders {
		if subOrder.IsDivided() {
			continue
		}
		if !subOrder.IsRefunded(ctx) {
			continue
		}
		if retailerOrder.IsAggPay() {
			subOrderIds = append(subOrderIds, subOrder.Id)
			continue
		}
		if subOrder.Id.Hex() == orderId.Hex() {
			subOrderIds = append(subOrderIds, subOrder.Id)
			break
		}
	}
	if len(subOrderIds) == 0 {
		return
	}

	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"order.id": bson.M{
			"$in": subOrderIds,
		},
		"status": bson.M{"$ne": ec_retailer.BILL_STATUS_REFUNDED},
	}
	oldRetailerBills := ec_retailer.CRetailerBill.GetAllByCondition(ctx, condition)
	err := ec_retailer.CRetailerBill.UpdateStatus(ctx, condition, ec_retailer.BILL_STATUS_REFUNDED)
	if err == nil {
		for _, oldRetailerBill := range oldRetailerBills {
			oldRetailerBill.Status = ec_retailer.BILL_STATUS_REFUNDED
			oldRetailerBill.UpdatedAt = time.Now()
			UpsertDistributionBill(ctx, oldRetailerBill)
		}
	}
}

func checkExistOldBillData(ctx context.Context, orderId bson.ObjectId) bool {
	profits, _ := ec_order.COrderReceiverProfit.GetByOrderIds(ctx, []bson.ObjectId{orderId})
	if len(profits) > 0 {
		return true
	}
	return false
}

func deleteOldBillData(ctx context.Context, orderId bson.ObjectId) {
	profits, _ := ec_order.COrderReceiverProfit.GetByOrderIds(ctx, []bson.ObjectId{orderId})
	billIds := []bson.ObjectId{}
	profitIds := []bson.ObjectId{}
	for _, profit := range profits {
		if profit.TransferBillId.Valid() {
			billIds = append(billIds, profit.TransferBillId)
		}
		profitIds = append(profitIds, profit.Id)
	}
	if len(profitIds) > 0 {
		ec_order.COrderReceiverProfit.DeleteByIds(ctx, profitIds, "订单退款删除")
	}
	if len(billIds) > 0 {
		ec_profitsharing.CTransferBill.DeleteByIds(ctx, billIds, "订单退款删除")
	}
}

func onlyUpdateAmountForRetailerBills(ctx context.Context, subOrder *ec_retailer.SubOrder, order ec_order.Order, refundId bson.ObjectId) {
	// 子订单已经全部退款，不需要更改账单金额
	if subOrder.IsRefunded(ctx) {
		log.Warn(ctx, "SubOrder all product refunded for update retailer bill amount", log.Fields{
			"subOrder": subOrder,
			"refundId": refundId.Hex(),
		})
		return
	}

	err := subOrder.CheckAmount()
	if err != nil {
		log.Warn(ctx, "Check subOrder amount fail for update retailer bill amount", log.Fields{
			"subOrder":   subOrder,
			"errMessage": err.Error(),
			"refundId":   refundId.Hex(),
		})
		return
	}

	store, err := ec_store.CStore.GetById(ctx, order.StoreId)
	if err != nil {
		log.Warn(ctx, "Get store fail for update retailer bill amount", log.Fields{
			"subOrder":   subOrder,
			"errMessage": err.Error(),
			"refundId":   refundId.Hex(),
		})
		return
	}
	subOrder.FormatSubOrderType(ctx, store)
	receivers := subOrder.Receivers
	if !subOrder.CheckProfitSharingReceivers(store, receivers) {
		log.Warn(ctx, "Check profit sharing receiver count fail for update retailer bill amount", log.Fields{
			"subOrder":  subOrder,
			"receivers": receivers,
			"refundId":  refundId.Hex(),
		})
		return
	}

	profitAmountMap, err := CalProfitAmount(ctx, receivers, *subOrder)
	if err != nil {
		log.Warn(ctx, "Cal profit amount fail for update retailer bill amount", log.Fields{
			"subOrder":     subOrder,
			"errorMessage": err.Error(),
			"refundId":     refundId.Hex(),
		})
		return
	}

	if !subOrder.CheckProfitAmount(profitAmountMap) {
		log.Warn(ctx, "check profit amount fail for update retailer bill amount", log.Fields{
			"subOrder":        subOrder,
			"profitAmountMap": profitAmountMap,
			"refundId":        refundId.Hex(),
		})
		return
	}

	for _, r := range receivers {
		profitAmount := profitAmountMap[r.RelationType]
		if profitAmount == 0 {
			log.Warn(ctx, "Sub order profitAmount amount is zero for update retailer bill amount", log.Fields{
				"subOrder": subOrder,
				"receiver": r,
				"refundId": refundId.Hex(),
			})
			UpdateRetailerBillsWhenOrderStatusChanges(ctx, *subOrder, r, "", 0, store)
			continue
		}
		UpdateRetailerBillsWhenOrderStatusChanges(ctx, *subOrder, r, "", profitAmount, store)
	}
	// 单独更新易宝手续费账单
	updatePaymentFeeBillAmount(ctx, profitAmountMap[ec_retailer.PAYMENT_FEE_YEEPAY], subOrder, order, receivers, store)
	// 单独更新脉盟小店/连锁零售商收单账单
	updateReceiptBillAmount(ctx, profitAmountMap, subOrder, order)
}
