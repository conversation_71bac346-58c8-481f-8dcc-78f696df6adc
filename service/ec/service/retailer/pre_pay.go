package retailer

import (
	"context"
	"encoding/json"
	"fmt"
	"mairpc/core/component/yeepay"
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	common "mairpc/proto/common/ec"
	pb_ec_product "mairpc/proto/ec/product"
	pb_retailer "mairpc/proto/ec/retailer"
	ec_client "mairpc/service/ec/client"
	ec_model "mairpc/service/ec/model"
	ec_distribution "mairpc/service/ec/model/distribution"
	ec_order "mairpc/service/ec/model/order"
	ec_retailer "mairpc/service/ec/model/retailer"
	ec_store "mairpc/service/ec/model/store"
	ec_store_product "mairpc/service/ec/model/storeProduct"
	"mairpc/service/ec/service"
	scan_model "mairpc/service/eccampaign/model/scanToBuy"
	model_product "mairpc/service/product/model"
	"mairpc/service/share/util"
	"strings"
	"unicode/utf8"

	"github.com/spf13/cast"
)

const (
	NOTIFY_URL      = "%s/v1/yeepay/paidNotify?accountId=%s"
	LIQUIDATION_URL = "%s/v1/yeepay/liquidationNotify?accountId=%s"
)

var useTutelageAccountIds = []string{
	"64d49ed42b7d7e029a11ed02", // 今世缘小店
	"6524e9d91bd2576d33206093", // 妈咪爱商贸
	"654d8bc51405912c3f3dfe55", // 海王艇仔
	"658251dca356b243c17a8445", // 平泉市平泉镇领婴婴儿用品店
	"664302d313399c775e7f7e93", // 隽达汽车用品服务
	"66cd43bd4efa1f2cf970a3d4", // 赣榆区塔山镇安心母爱孕婴生活馆
	"643760a877a8415b6c6b4b03", // 连锁零售商
	"670f7618fd1a802df1333ed3", // 脉盟派
	"67ff331e875a364454265323", // 超班咖啡
}

func (RetailerService) PrePay(ctx context.Context, req *pb_retailer.PrePayRequest) (*common.PrePayResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	if req.ChannelId == "" || req.OpenId == "" {
		return nil, errors.NewInvalidArgumentErrorWithMessage("channel and openId", "can't be empty")
	}

	condition := ec_retailer.Common.GenDefaultConditionById(ctx, bson.ObjectIdHex(req.Id))
	condition["memberId"] = bson.ObjectIdHex(req.MemberId)
	condition["status"] = ec_retailer.ORDER_UNPAID_STATUS
	retailerOrder := ec_retailer.CRetailerOrder.GetByCondition(ctx, condition)

	var ecOrder *ec_order.Order
	// 子单支付/连锁零售商城直接购买
	if retailerOrder == nil {
		err := ec_order.Common.GetByCondition(ctx, condition, ec_order.C_ORDER, &ecOrder)
		if err != nil {
			return nil, errors.NewNotExistsErrorWithMessage("id", err.Error())
		}
		tmpRetailerOrder := getRetailerOrder(ctx, req.Id)
		// 直接购买
		if !tmpRetailerOrder.Id.Valid() {
			// 直接购买的订单需要创建 RetailerOrder
			rOrder := createRetailerOrder(ctx, ecOrder)
			// 直接购买的代销订单需要更新商品佣金
			updateStoreProductDistributionAmount(ctx, ecOrder)
			// 商品参与限时特价活动时，需要根据活动设置更新门店和导购佣金
			updateProfitAmountWithDiscountCampaign(ctx, ecOrder, *rOrder.SubOrders[0])
		} else {
			// 子单支付：商品参与限时特价活动时，需要根据活动设置更新门店和导购佣金
			updateProfitAmountWithDiscountCampaign(ctx, ecOrder, *tmpRetailerOrder.SubOrders[0])
		}
	} else {
		// 商品参与限时特价活动时，需要根据活动设置更新门店和导购佣金
		for _, subOrder := range retailerOrder.SubOrders {
			if len(subOrder.Campaigns) == 0 {
				continue
			}
			order, _ := ec_order.COrder.GetById(ctx, subOrder.Id)
			if order.Id.Valid() {
				updateProfitAmountWithDiscountCampaign(ctx, &order, *subOrder)
			}
		}
	}
	if retailerOrder == nil && ecOrder == nil {
		return nil, errors.NewNotExistsError("id")
	}

	return prePay(ctx, req, retailerOrder, ecOrder)
}

func prePay(ctx context.Context, req *pb_retailer.PrePayRequest, retailerOrder *ec_retailer.RetailerOrder, ecOrder *ec_order.Order) (*common.PrePayResponse, error) {
	yeepayClient := yeepay.NewYeepay(yeepay.YEEPAY_APP_KEY, core_util.GetMaimengPrivateKey())
	preReq := genAggpayPrePayRequest(ctx, req, retailerOrder, ecOrder)
	resp := &common.PrePayResponse{}
	var err error
	if ecOrder != nil && ecOrder.Payment == ec_order.PAYMENT_ALIPAY && util.StrInArray(ec_order.ORDER_TAGS_SCAN_BUY, &ecOrder.Tags) {
		resp, err = aggpayPrePay(ctx, yeepayClient, preReq)
	} else if req.UseTutelage || util.StrInArray(util.GetAccountId(ctx), &useTutelageAccountIds) {
		req := &yeepay.AggpayTutelagePrePayRequest{}
		copier.Instance(nil).From(preReq).CopyTo(req)
		resp, err = aggpayTutelagePrePay(ctx, yeepayClient, req)
	} else {
		resp, err = aggpayPrePay(ctx, yeepayClient, preReq)
	}
	if err != nil {
		log.Warn(ctx, "Yeepay prepay response error", log.Fields{
			"req":    preReq,
			"errMsg": err.Error(),
		})
		return nil, err
	}
	return resp, nil
}

func genAggpayPrePayRequest(ctx context.Context, req *pb_retailer.PrePayRequest, retailerOrder *ec_retailer.RetailerOrder, ecOrder *ec_order.Order) *yeepay.AggpayPrePayRequest {
	orderId := getOrderId(retailerOrder, ecOrder)
	orderAmount := getTotalFee(retailerOrder, ecOrder)
	goodsName := getGoodsName(retailerOrder, ecOrder)
	appId := getAppId(ctx, req.ChannelId)

	payway := "MINI_PROGRAM"
	channel := "WECHAT"
	if ecOrder != nil && ecOrder.Payment != "" && ecOrder.Payment == ec_order.PAYMENT_ALIPAY && util.StrInArray(ec_order.ORDER_TAGS_SCAN_BUY, &ecOrder.Tags) {
		payway = "USER_SCAN"
		channel = "ALIPAY"
	}

	preReq := yeepay.AggpayPrePayRequest{
		ParentMerchantNo: ec_retailer.GetParentMerchantNo(ctx),
		MerchantNo:       ec_retailer.GetYeepayMerchantNo(ctx),
		OrderId:          orderId,
		OrderAmount:      orderAmount,
		CsUrl:            fmt.Sprintf(LIQUIDATION_URL, util.GetBussinessDomain(), util.GetAccountId(ctx)),
		GoodsName:        goodsName,
		FundProcessType:  "DELAY_SETTLE",
		Scene:            "OFFLINE",
		UserIp:           util.GetClientIp(ctx),
		PayWay:           payway,
		Channel:          channel,
		AppId:            appId,
		UserId:           req.OpenId,
		YpPromotionInfo:  getYpPromotionInfo(retailerOrder, ecOrder, req),
	}
	// 微信支付风控，根据配置切换支付通道为 MINI_PROGRAM_WECHAT_ONLINE
	merchantNoSettings := ec_retailer.CMerchantNoSettings.GetByMerchantNo(ctx, preReq.MerchantNo)
	if merchantNoSettings != nil && merchantNoSettings.PayType == ec_retailer.PAY_TYPE_MINI_PROGRAM_WECHAT_ONLINE {
		preReq.Scene = "ONLINE"
	}
	return &preReq
}

func getYpPromotionInfo(retailerOrder *ec_retailer.RetailerOrder, ecOrder *ec_order.Order, req *pb_retailer.PrePayRequest) string {
	ypPromotionEnable := true
	if req.Extra != "" {
		extraMap := map[string]interface{}{}
		json.Unmarshal([]byte(req.Extra), &extraMap)
		if value, ok := extraMap["ypPromotionEnable"]; ok {
			ypPromotionEnable = cast.ToBool(value)
		}
	}

	if !ypPromotionEnable {
		return ""
	}
	ypPromotionInfo := bson.M{
		"type": "CUSTOM_REDUCTION",
	}
	if retailerOrder != nil {
		if retailerOrder.YpPromotionAmount == 0 {
			return ""
		}
		ypPromotionInfo["amount"] = cast.ToFloat64(retailerOrder.YpPromotionAmount) / 100
	}
	if ecOrder != nil {
		amount, ok := ecOrder.Extra["ypPromotionAmount"]
		if !ok || ok && amount == 0 {
			return ""
		}
		ypPromotionInfo["amount"] = cast.ToFloat64(amount) / 100
	}
	ypPromotionInfos := []bson.M{
		ypPromotionInfo,
	}
	byte, _ := json.Marshal(ypPromotionInfos)
	return cast.ToString(byte)
}

func getAppId(ctx context.Context, channelId string) string {
	channel, err := service.GetChannel(ctx, channelId)
	if err != nil {
		log.Warn(ctx, "Get channel fail", log.Fields{
			"errMsg":    err.Error(),
			"channelId": channelId,
		})
		return ""
	}
	return channel.AppId
}

func getOrderId(retailerOrder *ec_retailer.RetailerOrder, ecOrder *ec_order.Order) string {
	var orderId string
	if retailerOrder != nil {
		orderId = retailerOrder.Id.Hex()
	}
	if ecOrder != nil {
		orderId = ecOrder.Id.Hex()
	}
	return orderId
}

func getTotalFee(retailerOrder *ec_retailer.RetailerOrder, ecOrder *ec_order.Order) float64 {
	var totalFee float64
	if retailerOrder != nil {
		totalFee = cast.ToFloat64(retailerOrder.PayAmount) / 100
	}
	if ecOrder != nil {
		totalFee = cast.ToFloat64(ecOrder.PayAmount) / 100
	}
	return totalFee
}

func getGoodsName(retailerOrder *ec_retailer.RetailerOrder, ecOrder *ec_order.Order) string {
	var goodsName string
	if retailerOrder != nil {
		products := []ec_order.OrderProduct{}
		for _, splitProduct := range retailerOrder.SplitProducts {
			for _, product := range splitProduct.Products {
				products = append(products, *product)
			}
		}
		goodsName = genGoodsName(products)
	}
	if ecOrder != nil {
		goodsName = genGoodsName(ecOrder.Products)
	}
	return goodsName
}

func genGoodsName(products []ec_order.OrderProduct) string {
	productsNames := []string{}
	for _, product := range products {
		if product.Name != "" {
			productsNames = append(productsNames, product.Name)
		}
	}
	goodsName := strings.Join(productsNames, ",")
	if utf8.RuneCountInString(goodsName) > 85 {
		goodsName = fmt.Sprintf("%s...", util.SubRuneStr(goodsName, 82))
	}
	return util.RemoveYeepaySpecialChars(goodsName)
}

func aggpayPrePay(ctx context.Context, yeepayClient *yeepay.Yeepay, preReq *yeepay.AggpayPrePayRequest) (*common.PrePayResponse, error) {
	resp, err := yeepayClient.Juhezhifu.AggpayPrePay(ctx, preReq)
	if err != nil {
		return nil, err
	}
	if resp.Code != "00000" {
		return nil, errors.NewInternal(resp.Code + ":" + resp.Message)
	}
	log.Warn(ctx, "Yeepay prepay response result", log.Fields{
		"req":  preReq,
		"resp": resp,
	})
	if preReq.PayWay == "USER_SCAN" && preReq.Channel == "ALIPAY" {
		err = scan_model.CScanToBuy.UpdatePrePayTn(ctx, bson.ObjectIdHex(preReq.OrderId), resp.PrePayTn)
		if err != nil {
			log.Warn(ctx, "pre pay update order status failed", log.Fields{
				"errMsg": err.Error(),
				"preReq": preReq,
			})
			return nil, err
		}
		return &common.PrePayResponse{}, nil
	}
	return genPrePayResponseWithPrePayTn(resp.PrePayTn)
}

func aggpayTutelagePrePay(ctx context.Context, yeepayClient *yeepay.Yeepay, preReq *yeepay.AggpayTutelagePrePayRequest) (*common.PrePayResponse, error) {
	resp, err := yeepayClient.Juhezhifu.AggpayTutelagePrePay(ctx, preReq)
	if err != nil {
		return nil, err
	}
	if resp.Code != "00000" {
		return nil, errors.NewInternal(resp.Code + ":" + resp.Message)
	}
	log.Warn(ctx, "Yeepay tutelage prepay response result", log.Fields{
		"req":  preReq,
		"resp": resp,
	})
	return &common.PrePayResponse{
		TutelageAppId:   resp.AppId,
		MiniProgramPath: resp.PrePayTn,
	}, nil
}

func genPrePayResponseWithPrePayTn(prePayTn string) (*common.PrePayResponse, error) {
	prePayResponse := common.PrePayResponse{}
	err := json.Unmarshal([]byte(prePayTn), &prePayResponse)
	if err != nil {
		return nil, err
	}
	return &prePayResponse, nil
}

func createRetailerOrder(ctx context.Context, ecOrder *ec_order.Order) *ec_retailer.RetailerOrder {
	subOrder := &ec_retailer.SubOrder{
		Id:            ecOrder.Id,
		Number:        ecOrder.Number,
		PayAmount:     ecOrder.PayAmount,
		Status:        ecOrder.Status,
		DistributorId: getDistributorId(ctx, ecOrder),
		Type: func() string {
			if core_util.StrInArray(ec_order.ORDER_TAGS_DISTRIBUTION, &ecOrder.Tags) {
				return ec_retailer.MAIMENG_STORE_PRODUCT_DISTRIBUTION
			} else {
				return ec_retailer.MAIMENG_STORE_PRODUCT_CONSIGNMENT
			}
		}(),
		Products: []ec_retailer.SubOrderProduct{
			{
				ProductId: ecOrder.Products[0].Id.Hex(),
			},
		},
	}

	// 如果订单参与了限时特价活动，需要更新 campaigns 字段
	for _, c := range ecOrder.Campaigns {
		if c.Type != ec_order.CAMPAIGN_TYPE_DISCOUNT {
			continue
		}
		discountCampaign, err := ec_retailer.CDiscountCampaign.GetByDiscountCampaignId(ctx, c.Id)
		if err != nil || !discountCampaign.Id.Valid() {
			continue
		}

		campaign := &ec_retailer.Campaign{
			ProductId:          ecOrder.Products[0].Id.Hex(),
			CampaignId:         discountCampaign.Id.Hex(),
			Total:              ecOrder.Products[0].Total,
			Sku:                ecOrder.Products[0].Spec.Sku,
			Title:              discountCampaign.Title,
			DiscountCampaignId: discountCampaign.DiscountCampaignId.Hex(),
		}

		// 设置 CurrentSales，下单时该商品 sku 在限时特价活动中的销量
		if discountCampaign.Id.Valid() {
			for _, product := range discountCampaign.Products {
				for _, sku := range product.Skus {
					if sku.Sku == ecOrder.Products[0].Spec.Sku {
						campaign.CurrentSales = sku.Sales + campaign.Total
					}
				}
			}
		}

		// 设置 CurrentStaffSales，下单时该商品 sku 在限时特价活动中导购推广的销量
		if ecOrder.Distribution.PromoterId != "" {
			promoter, _ := ec_distribution.CPromoter.GetById(ctx, ecOrder.Distribution.PromoterId)
			if promoter.Id.Valid() {
				subOrder.StaffId = promoter.StaffId.Hex()
				discountCampaignStaffSales, _ := ec_retailer.CDiscountCampaign.GetDiscountCampaignStaffSales(
					ctx,
					discountCampaign.Id,
					promoter.StaffId,
					ecOrder.Products[0].Id,
				)
				if discountCampaignStaffSales.Id.Valid() {
					for _, sku := range discountCampaignStaffSales.Skus {
						if sku.Sku == campaign.Sku {
							campaign.CurrentStaffSales = sku.Sales + campaign.Total
						}
					}
				}
			}
		}
		// 直接购买只能购买同一种商品，这里只需要加一条数据
		subOrder.Campaigns = []*ec_retailer.Campaign{campaign}
	}

	// D0 代销订单,设置 subOrder.Products,dZeroDistributorAccountId
	store, _ := ec_store.CStore.GetById(ctx, ecOrder.StoreId)
	if subOrder.IsD0ConsignmentOrder(store) {
		dZeroDistributorAccountId := ""
		dOneDistributorId := ""
		for _, distributor := range store.DistributionStoreInfo.Distributors {
			if distributor.Type == "D0" {
				dZeroDistributorAccountId = distributor.AccountId
			}
			if distributor.Type == "D1" {
				dOneDistributorId = distributor.Id
			}
		}
		subOrder.DZeroDistributorAccountId = dZeroDistributorAccountId
		dZeroDistributorCtx := core_util.CtxWithAccountID(ctx, subOrder.DZeroDistributorAccountId)
		storeProduct, _ := ec_store_product.CStoreProduct.GetByEcProductIdAndStoreId(ctx, ecOrder.Products[0].Id, ecOrder.StoreId)
		subOrderProduct := ec_retailer.GetSubOrderProduct(dZeroDistributorCtx, &ecOrder.Products[0], &storeProduct, dOneDistributorId)
		if subOrderProduct != nil {
			subOrder.Products = []ec_retailer.SubOrderProduct{*subOrderProduct}
		}

	}

	order := ec_retailer.RetailerOrder{
		AccountId:   util.GetAccountIdAsObjectId(ctx),
		MemberId:    ecOrder.MemberId,
		StoreId:     ecOrder.StoreId,
		TotalAmount: ecOrder.TotalAmount,
		PayAmount:   ecOrder.PayAmount,
		Status:      ecOrder.Status,
		Channel:     &ecOrder.Channel,
		SubOrders:   []*ec_retailer.SubOrder{subOrder},
	}
	if number, err := util.GenerateUniqueCode("order", "RO"); err != nil {
		log.Warn(ctx, "failed to generate unique code for retailer order", log.Fields{
			"retailerOrder": order,
			"errMsg":        err.Error(),
		})
		return nil
	} else {
		order.Number = number
	}
	err := order.Create(ctx)
	if err != nil {
		log.Warn(ctx, "failed to create retailer order", log.Fields{
			"retailerOrder": order,
			"errMsg":        err.Error(),
		})
		return nil
	}
	return &order
}

func getDistributorId(ctx context.Context, ecOrder *ec_order.Order) string {
	if core_util.StrInArray(ec_order.ORDER_TAGS_DISTRIBUTION, &ecOrder.Tags) {
		return ecOrder.StoreId.Hex()
	} else {
		// 代销商品
		store, _ := ec_store.CStore.GetById(ctx, ecOrder.StoreId)
		if !store.Id.Valid() {
			log.Warn(ctx, "failed to get ec store", log.Fields{
				"storeId": ecOrder.StoreId.Hex(),
			})
			return ""
		}
		var distributor ec_store.Distributor
		for i, d := range store.DistributionStoreInfo.Distributors {
			if core_util.StrInArray(d.Id, &ecOrder.Tags) {
				distributor = store.DistributionStoreInfo.Distributors[i]
				break
			}
		}
		if distributor.Type != "D0" {
			return distributor.Id
		}
		// DO代销商品
		product, err := ec_client.ProductService.GetProduct(ctx, &pb_ec_product.GetProductRequest{ProductId: ecOrder.Products[0].Id.Hex()})
		if err != nil {
			log.Warn(ctx, "failed to get product", log.Fields{
				"productId": ecOrder.Products[0].Id.Hex(),
				"errMsg":    err.Error(),
			})
			return distributor.Id
		}
		brandId := ""
		for _, f := range product.Product.Fields {
			if f.Name == model_product.FIELD_NAME_BRAND {
				brandId = f.ValueStringArray[0]
			}
		}
		if brandId != "" {
			return fmt.Sprintf("%s_%s", distributor.Id, brandId)
		}
		return distributor.Id
	}
}

func updateStoreProductDistributionAmount(ctx context.Context, ecOrder *ec_order.Order) {
	ecOrder.ProfitTypes = append(ecOrder.ProfitTypes, ec_order.PROFIT_TYPE_PROFITSHARING)
	if ecOrder.Distribution.PromoterId.Hex() != "" && ecOrder.Distribution.Amount != 0 {
		ecOrder.ProfitTypes = append(ecOrder.ProfitTypes, ec_order.PROFIT_TYPE_DISTRIBUTION)
	}
	orderProducts := []ec_order.OrderProduct{}
	for _, product := range ecOrder.Products {
		condition := bson.M{
			"accountId":   util.GetAccountIdAsObjectId(ctx),
			"storeId":     ecOrder.StoreId,
			"ecProductId": product.Id,
		}
		storeProduct, _ := ec_store_product.CStoreProduct.GetByCondition(ctx, condition)
		if storeProduct.Id.Valid() {
			for _, sku := range storeProduct.Skus {
				if sku.Sku == product.Spec.Sku {
					product.DistributionAmount = sku.StaffProfitAmount * product.Total
					product.StoreDistributionAmount = sku.ProfitAmount * product.Total
				}
			}
		}
		orderProducts = append(orderProducts, product)
	}
	ecOrder.Products = orderProducts
	ecOrder.UpdateProductsAndProfitTypes(ctx)
}

// 更新导购分佣的经销订单的 distributionAmount
func updateStaffProfitAmount(ctx context.Context, ecOrder *ec_order.Order) {
	if !util.StrInArray(ec_order.PROFIT_TYPE_DISTRIBUTION, &ecOrder.ProfitTypes) {
		ecOrder.ProfitTypes = append(ecOrder.ProfitTypes, ec_order.PROFIT_TYPE_DISTRIBUTION)
	}
	orderProducts := []ec_order.OrderProduct{}
	for _, product := range ecOrder.Products {
		condition := bson.M{
			"accountId":   util.GetAccountIdAsObjectId(ctx),
			"storeId":     ecOrder.StoreId,
			"ecProductId": product.Id,
		}
		storeProduct, _ := ec_store_product.CStoreProduct.GetByCondition(ctx, condition)
		if storeProduct.Id.Valid() {
			for _, sku := range storeProduct.Skus {
				if sku.Sku == product.Spec.Sku {
					product.DistributionAmount = sku.StaffProfitAmount * product.Total
				}
			}
		}
		orderProducts = append(orderProducts, product)
	}
	ecOrder.Products = orderProducts
	ecOrder.UpdateProductsAndProfitTypes(ctx)
}

// 订单中商品有参与限时活动时，根据活动设置，更新 distributionAmount
func updateProfitAmountWithDiscountCampaign(ctx context.Context, order *ec_order.Order, subOrder ec_retailer.SubOrder) {
	discountCampaignProductMap := map[string][]ec_order.OrderProduct{}
	productIdDiscountCampaignIdMap := map[string]string{}
	for _, p := range order.Products {
		if p.RefundStatus == ec_order.ORDER_REFUND_STATUS_REFUNDED {
			continue
		}
		// 有 discountCampaignId 表示参与了限时特价活动
		discountCampaignId := subOrder.GetDiscountCampaignId(p.Id.Hex())
		if discountCampaignId == "" {
			continue
		}

		if v, ok := discountCampaignProductMap[p.Id.Hex()]; ok {
			v = append(v, p)
			discountCampaignProductMap[p.Id.Hex()] = v
		} else {
			discountCampaignProductMap[p.Id.Hex()] = []ec_order.OrderProduct{p}
		}
		productIdDiscountCampaignIdMap[p.Id.Hex()] = discountCampaignId
	}

	if len(productIdDiscountCampaignIdMap) == 0 {
		return
	}

	staffProfitAmountMap := calDiscountCampaignProfitAmount(
		ctx,
		discountCampaignProductMap,
		productIdDiscountCampaignIdMap,
		ec_retailer.DISCOUNT_CAMPAIGN_STAFF_TYPE,
		subOrder,
	)

	storeProfitAmountMap := calDiscountCampaignProfitAmount(
		ctx,
		discountCampaignProductMap,
		productIdDiscountCampaignIdMap,
		ec_retailer.DISCOUNT_CAMPAIGN_CHANNEL_TYPE,
		subOrder,
	)

	updateEcOrderProducts(ctx, order, staffProfitAmountMap, storeProfitAmountMap)
}

func updateEcOrderProducts(ctx context.Context, order *ec_order.Order, staffProfitAmountMap, storeProfitAmountMap map[string]uint64) {
	updatedStaffProfitAmountProductIds := []string{}
	updatedStoreProfitAmountProductIds := []string{}
	orderProducts := []ec_order.OrderProduct{}
	staffDistributionAmount := uint64(0)
	storeDistributionAmount := uint64(0)
	for _, p := range order.Products {
		if p.RefundStatus == ec_order.ORDER_REFUND_STATUS_REFUNDED {
			orderProducts = append(orderProducts, p)
			continue
		}
		// 小店参与限时折扣时没有导购佣金，只需要更新连锁零售商的导购佣金
		if ec_model.IsChainRetail(ctx) {
			// 同一个商品的不同规格的佣金是一起算的，设置佣金时，只需要给其中一个设置
			if v, ok := staffProfitAmountMap[p.Id.Hex()]; ok {
				p.DistributionAmount = 0
				if !util.StrInArray(p.Id.Hex(), &updatedStaffProfitAmountProductIds) {
					p.DistributionAmount = v
					updatedStaffProfitAmountProductIds = append(updatedStaffProfitAmountProductIds, p.Id.Hex())
					staffDistributionAmount += v
				}
			}
		}

		if v, ok := storeProfitAmountMap[p.Id.Hex()]; ok {
			p.StoreDistributionAmount = 0
			if !util.StrInArray(p.Id.Hex(), &updatedStoreProfitAmountProductIds) {
				p.StoreDistributionAmount = v
				updatedStoreProfitAmountProductIds = append(updatedStoreProfitAmountProductIds, p.Id.Hex())
				storeDistributionAmount += v
			}
		}
		orderProducts = append(orderProducts, p)
	}
	order.Products = orderProducts
	order.UpdateProducts(ctx)
	// 连锁零售商需要更新导购佣金
	if ec_model.IsChainRetail(ctx) && staffDistributionAmount > 0 {
		order.Distribution.Amount = staffDistributionAmount
	}
	if storeDistributionAmount > 0 {
		order.Distribution.StoreDistributionAmount = storeDistributionAmount
	}
	order.UpdateDistribution(ctx)
}
