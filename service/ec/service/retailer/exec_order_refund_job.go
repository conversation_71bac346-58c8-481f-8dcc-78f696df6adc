package retailer

import (
	"errors"
	"fmt"
	"mairpc/core/component/yeepay"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	ec_wallet "mairpc/proto/ec/wallet"
	"mairpc/service/ec/client"
	ec_model "mairpc/service/ec/model"
	ec_order "mairpc/service/ec/model/order"
	ec_profit "mairpc/service/ec/model/profitsharing"
	ec_retailer "mairpc/service/ec/model/retailer"
	"mairpc/service/share/util"
	"strconv"
	"time"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

const (
	REFUND_NOTIFY_URL   = "%s/v1/yeepay/refundNotify?accountId=%s"
	REFUND_SUCCESS_CODE = "OPR00000" // OPR00000表示成功
)

var (
	retailerOrderRefundMap = map[string]string{}
)

func (RetailerService) ExecOrderRefundJob(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	if !ec_model.IsMaimengRetail(ctx) && !ec_model.IsChainRetail(ctx) {
		return &response.EmptyResponse{}, nil
	}

	getNeedRefundOrdersCondition := bson.M{
		"accountId":       util.GetAccountIdAsObjectId(ctx),
		"status":          ec_order.ORDER_REFUND_STATUS_PENDING,
		"paymentProvider": ec_order.PAYMENT_PROVIDER_MAI,
		"tags":            bson.M{"$in": ec_order.YEEPAY_ORDER_TAGS},
		"isDeleted":       false,
	}

	it, err := ec_order.COrderRefund.Iterate(ctx, getNeedRefundOrdersCondition, []string{})
	if err != nil {
		log.Warn(ctx, "Failed to iterate maimeng refunds", log.Fields{})
		return nil, err
	}

	defer it.Close()
	refund := ec_order.OrderRefund{}
	for it.Next(&refund) {
		if getLastRefundTriggerTime(refund).Before(time.Now().Add(time.Hour * -12)) { // 估算的退款时间，僵尸退款单自动失败
			refund.RefundFailed(ctx, "退款超时")
			continue
		}

		// 合单支付的多笔退款单退款需要间隔执行，每次 job 只会执行一笔
		// 因为每次 job 执行多笔合单的退款单会影响账单的重新计算
		retailerOrderId := GetAggPayRetailerOrderId(ctx, refund.OrderId)
		if retailerOrderId != "" {
			if value, ok := retailerOrderRefundMap[retailerOrderId]; ok && value != "" {
				continue
			} else {
				retailerOrderRefundMap[retailerOrderId] = refund.Id.Hex()
			}
		}

		// 品牌货款分账回退
		err = divideBackBrandAmount(ctx, refund)
		if err != nil {
			log.Error(ctx, "Failed to divide back brand amount", log.Fields{
				"errorMessage":  err.Error(),
				"orderRefundId": refund.Id,
			})
			continue
		}
		// 剩余待分账金额分给收单商户号
		err = divideToReceiptMerchantNo(ctx, refund)
		if err != nil {
			log.Error(ctx, "Failed to complete divide", log.Fields{
				"errorMessage":  err.Error(),
				"orderRefundId": refund.Id,
				"status":        refund.Status,
			})
			continue
		}
		err = refundOrder(ctx, refund)
		if err != nil {
			log.Error(ctx, "Failed to exec maimeng order refund", log.Fields{
				"errorMessage":  err.Error(),
				"orderRefundId": refund.Id,
				"status":        refund.Status,
			})
		}
	}

	return &response.EmptyResponse{}, nil
}

func getLastRefundTriggerTime(orderRefund ec_order.OrderRefund) time.Time {
	var t time.Time
	for _, h := range orderRefund.Histories {
		if h.Status == ec_order.ORDER_REFUND_STATUS_PENDING {
			t = h.CreatedAt
		}
	}
	return t
}

func refundOrder(ctx context.Context, refund ec_order.OrderRefund) error {
	if refund.RefundAmount == 0 {
		updateOrderRefundStatus(ctx, &refund, ec_order.ORDER_REFUND_STATUS_REFUNDED, "退款完成")
		return nil
	}

	orderId, err := ec_retailer.GetOrderId(ctx, refund.OrderId)
	if err != nil {
		return err
	}

	notifyUrl := fmt.Sprintf(REFUND_NOTIFY_URL, util.GetBussinessDomain(), util.GetAccountId(ctx))
	yeepayClient := yeepay.NewYeepay(yeepay.YEEPAY_APP_KEY, core_util.GetMaimengPrivateKey())
	refundReq := &yeepay.TradeRefundRequest{
		ParentMerchantNo: ec_retailer.GetParentMerchantNo(ctx),
		MerchantNo:       ec_retailer.GetYeepayMerchantNo(ctx),
		OrderId:          orderId,
		RefundRequestId:  refund.Id.Hex(),
		RefundAmount:     strconv.FormatFloat(float64(refund.RefundAmount)/100, 'f', 2, 64),
		Description:      util.RemoveYeepaySpecialChars(refund.Reason),
		NotifyUrl:        notifyUrl,
	}
	resp, err := yeepayClient.Jiaoyi.TradeRefund(ctx, refundReq)
	if err != nil {
		return err
	}

	log.Warn(ctx, "Get yeepay refund response", log.Fields{
		"refundId":        refund.Id.Hex(),
		"ecOrderId":       refund.OrderId.Hex(),
		"retailerOrderId": orderId,
		"refundReq":       refundReq,
		"resp":            resp,
	})
	if resp.Code != REFUND_SUCCESS_CODE {
		updateOrderRefundStatus(ctx, &refund, ec_order.ORDER_REFUND_STATUS_FAILED, resp.Message)
		return errors.New(resp.Code + ":" + resp.Message)
	}

	refund.Status = ec_order.ORDER_REFUND_STATUS_REFUNDING
	refund.GenerateHistory(ec_order.OPERATOR_SYSTEM, "易宝退款中", bson.M{})
	refund.RefundNumber = resp.UniqueRefundNo

	// 储值卡金额回退
	for i, prepaidCardRefund := range refund.PrepaidCardRefunds {
		if prepaidCardRefund.Status == ec_order.ORDER_REFUND_STATUS_REFUNDED {
			continue
		}
		req := &ec_wallet.PartialRevokeAmountRequest{
			PrepaidCardId: prepaidCardRefund.PrepaidCardId.Hex(),
			Amount:        prepaidCardRefund.RefundAmount,
			MemberId:      refund.MemberId.Hex(),
			OrderId:       refund.OrderId.Hex(),
			TradeNo:       prepaidCardRefund.OriginTradeNo,
			RefundTradeNo: prepaidCardRefund.TradeNo,
			OrderNumber:   refund.OrderNumber,
			SystemDate:    refund.OrderId.Time().Format(time.RFC3339),
			TradeSeq:      prepaidCardRefund.OutTradeSeq,
			SystemSeq:     prepaidCardRefund.TransactionId,
			Reason:        "订单退款",
		}
		_, err := client.WalletService.PartialRevokeAmount(ctx, req)
		if err != nil {
			log.Warn(ctx, "Failed to refund prepaid card", log.Fields{
				"errMsg":            err.Error(),
				"prepaidCardRefund": prepaidCardRefund,
				"req":               req,
			})
			refund.PrepaidCardRefunds[i].Status = ec_order.ORDER_REFUND_STATUS_FAILED
		} else {
			refund.PrepaidCardRefunds[i].Status = ec_order.ORDER_REFUND_STATUS_REFUNDED
		}
	}

	selector := bson.M{
		"_id":       refund.Id,
		"accountId": refund.AccountId,
	}
	setter := bson.M{
		"histories":    refund.Histories,
		"status":       refund.Status,
		"refundNumber": refund.RefundNumber,
		"updatedAt":    time.Now(),
	}
	if len(refund.PrepaidCardRefunds) > 0 {
		setter["prepaidCardRefunds"] = refund.PrepaidCardRefunds
	}
	updater := bson.M{
		"$set": setter,
	}

	return ec_order.Common.UpdateOne(ctx, ec_order.C_ORDER_REFUND, "false", selector, updater)
}

func GetAggPayRetailerOrderId(ctx context.Context, subOrderId bson.ObjectId) string {
	retailerOrder, err := ec_retailer.CRetailerOrder.GetBySubOrderId(ctx, subOrderId)
	if err != nil || !retailerOrder.Id.Valid() {
		return ""
	}

	if retailerOrder.IsAggPay() {
		return retailerOrder.Id.Hex()
	}
	return ""
}

// 回退品牌货款分账
func divideBackBrandAmount(ctx context.Context, refund ec_order.OrderRefund) error {
	if !util.StrInArray(ec_order.ORDER_TAGS_BRAND_DIVIDE_TYPE_AFTER_PAID, &refund.Tags) {
		return nil
	}

	order, err := ec_order.COrder.GetById(ctx, refund.OrderId)
	if err != nil {
		return err
	}

	retailerOrder, err := ec_retailer.CRetailerOrder.GetBySubOrderId(ctx, refund.OrderId)
	if err != nil {
		return err
	}
	subOrder := retailerOrder.GetSubOrder(refund.OrderId.Hex())
	if subOrder.BrandMerchantNo == "" {
		return nil
	}

	condition := bson.M{
		"accountId":        refund.AccountId,
		"orderTradeNo":     order.TradeNo,
		"receiver.account": subOrder.BrandMerchantNo,
	}
	bill, err := ec_profit.CTransferBill.GetOneByCondition(ctx, condition)
	if err != nil {
		return err
	}

	// 没有易宝分账明细单号，不回退
	if bill.DetailId == "" && len(bill.DetailIds) == 0 {
		return nil
	}
	// 已经回退过，不再回退
	if len(bill.BackRecords) > 0 {
		return nil
	}
	amount := refund.GetDivideBackAmount(ctx, order, bill)
	if amount == 0 {
		log.Warn(ctx, "Divide back amount is zero", log.Fields{})
		return nil
	}

	orderId := order.Id.Hex()
	if retailerOrder.IsAggPay() {
		orderId = retailerOrder.Id.Hex()
	}

	strAmount := cast.ToString(util.DivideFloatWithRound(float64(amount), 100, 2))
	detail := fmt.Sprintf(
		`[{"amount":"%s","divideDetailNo":"%s","divideBackReason":"%s"}]`,
		strAmount, // 易宝金额以元为单位
		bill.DetailId,
		"订单退款回退品牌货款",
	)
	req := &yeepay.DivideBackRequest{
		ParentMerchantNo:    ec_retailer.GetParentMerchantNo(ctx),
		MerchantNo:          ec_retailer.GetYeepayMerchantNo(ctx),
		DivideRequestId:     bill.Id.Hex(),
		OrderId:             orderId,
		UniqueOrderNo:       order.TradeNo,
		DivideBackRequestId: fmt.Sprintf("back_%s_%s_%s", bill.Id.Hex(), refund.Id.Hex(), strAmount),
		DivideBackDetail:    detail,
	}
	client := yeepay.NewYeepay(yeepay.YEEPAY_APP_KEY, core_util.GetMaimengPrivateKey())
	resp, err := client.Jiaoyi.DivideBack(ctx, req)
	if err != nil {
		log.Warn(ctx, "Divide back error", log.Fields{
			"req":  req,
			"resp": resp,
		})
		return nil
	}

	if resp.Code != "OPR00000" {
		log.Warn(ctx, "Divide back fail", log.Fields{
			"req":         req,
			"resp":        resp,
			"bill":        bill,
			"orderRefund": refund,
		})
	}
	bill.UpdateBackRecords(ctx, resp, amount)
	return nil
}

// 如果是合单支付，只要有一个子单支付后分过账，其余子单退款时都要将退款金额分账给收单商户号
// 如果是子单单独支付，只有子单支付后分过账，才需要将退款金额分账给收单商户号，其余子单退款时不需要分账
func divideToReceiptMerchantNo(ctx context.Context, refund ec_order.OrderRefund) error {
	order, err := ec_order.COrder.GetById(ctx, refund.OrderId)
	if err != nil {
		return err
	}

	retailerOrder, err := ec_retailer.CRetailerOrder.GetBySubOrderId(ctx, refund.OrderId)
	if err != nil {
		return err
	}
	// 如果是合单支付，只要有一个子单支付后分过账，其余子单退款时都要将退款金额分账给收单商户号
	if retailerOrder.IsAggPay() {
		brandDivideType := ""
		for _, subOrder := range retailerOrder.SubOrders {
			if subOrder.BrandDivideType == ec_retailer.BRAND_DIVIDE_TYPE_AFTER_PAID {
				brandDivideType = subOrder.BrandDivideType
			}
		}
		if brandDivideType == "" {
			return nil
		}
	} else {
		// 如果是子单单独支付，只有子单支付后分过账，才需要将退款金额分账给收单商户号，其余子单退款时不需要分账
		if !util.StrInArray(ec_order.ORDER_TAGS_BRAND_DIVIDE_TYPE_AFTER_PAID, &refund.Tags) {
			return nil
		}
	}

	shareAmount := uint64(0)
	for _, subOrder := range retailerOrder.SubOrders {
		if subOrder.Id.Hex() == refund.OrderId.Hex() {
			if subOrder.ProfitAmount == 0 {
				return nil
			}
			shareAmount = subOrder.ProfitAmount - subOrder.BrandProfitAmount
			subOrder.ProfitAmount = 0
		}
	}
	retailerOrder.Update(ctx)

	parentMerchantNo := ec_retailer.GetParentMerchantNo(ctx)
	merchantNo := ec_retailer.GetYeepayMerchantNo(ctx)
	detail := fmt.Sprintf(
		`[{"amount":"%s","ledgerNo":"%s","divideDetailDesc":"%s"}]`,
		cast.ToString(util.DivideFloatWithRound(float64(shareAmount), 100, 2)), // 易宝金额以元为单位
		merchantNo,
		fmt.Sprintf("订单%s退款时，分账金额分给收单商户号", order.Number),
	)

	orderId := order.Id.Hex()
	if retailerOrder.IsAggPay() {
		orderId = retailerOrder.Id.Hex()
	}

	req := yeepay.DivideApplyRequest{
		ParentMerchantNo: parentMerchantNo,
		MerchantNo:       merchantNo,
		UniqueOrderNo:    order.TradeNo,
		OrderId:          orderId,
		DivideRequestId:  fmt.Sprintf("%s_%s", refund.Id.Hex(), time.Now().Format("20060102150405")),
		DivideDetail:     detail,
	}
	log.Warn(ctx, "divideToReceiptMerchantNo request", log.Fields{
		"req": req,
	})

	client := yeepay.NewYeepay(yeepay.YEEPAY_APP_KEY, core_util.GetMaimengPrivateKey())
	resp, err := client.Jiaoyi.DivideApply(ctx, &req)
	if err != nil {
		log.Warn(ctx, "Divide apply failed", log.Fields{
			"errMessage": err.Error(),
			"req":        req,
			"resp":       resp,
		})
		return err
	}

	if resp.Code != "OPR00000" {
		log.Warn(ctx, "Divide apply failed", log.Fields{
			"req":  req,
			"resp": resp,
		})
		return errors.New(fmt.Sprintf("%s_%s", resp.Code, resp.Message))
	}
	return nil
}
