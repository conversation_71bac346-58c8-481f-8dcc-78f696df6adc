package retailer

import (
	"encoding/json"
	"errors"
	"mairpc/core/component"
	"mairpc/core/component/yeepay"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	pb_retailer "mairpc/proto/ec/retailer"
	pb_member "mairpc/proto/member"
	ec_order "mairpc/service/ec/model/order"
	ec_retailer "mairpc/service/ec/model/retailer"
	ec_store "mairpc/service/ec/model/store"
	share_component "mairpc/service/share/component"
	"mairpc/service/share/util"
	"mairpc/service/trade/share"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

type LiquidationNotifyData struct {
	ParentMerchantNo string `json:"parentMerchantNo"` // 交易发起方的商户编号
	MerchantFee      string `json:"merchantFee"`      // 商户手续费
	OrderAmount      string `json:"orderAmount"`      // 单位:元，下单请求收款的金额
	OrderId          string `json:"orderId"`          // 交易下单传入的商户收款请求号
	UniqueOrderNo    string `json:"uniqueOrderNo"`    // 商户收款请求号对应在易宝的收款订单号
	YpSettleAmount   string `json:"ypSettleAmount"`   // 收款订单清算后给商户的入账金额
	Status           string `json:"status"`           // SUCCESS（订单清算成功）
	MerchantNo       string `json:"merchantNo"`       // 收款商户编号
	CsSuccessDate    string `json:"csSuccessDate"`    // 清算完成时间。格式：yyyy-MM-dd HH:mm:ss
}

func (RetailerService) LiquidationNotify(ctx context.Context, req *pb_retailer.YeePayNotify) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	if req.Response == "" || req.CustomerIdentification == "" || util.GetAccountId(ctx) == "" {
		return &response.EmptyResponse{}, nil
	}
	dataStr, err := yeepay.DecryptData(ctx, req.Response)
	if err != nil {
		log.Warn(ctx, "Decrypt data fail", log.Fields{
			"errMessage": err.Error(),
		})
		return nil, err
	}

	data := LiquidationNotifyData{}
	err = json.Unmarshal([]byte(dataStr), &data)
	if err != nil {
		return nil, err
	}

	if data.Status != "SUCCESS" {
		return nil, errors.New("Status has not succeeded! " + data.Status)
	}

	// 防止多次回调，使用协程处理
	component.GO(ctx, func(ctx context.Context) {
		err := handleOrderLiquidationNotifyData(ctx, data)
		if err != nil {
			log.Warn(ctx, "Handle OrderLiquidationNotify data error", log.Fields{
				"errMessage": err.Error(),
				"data":       data,
			})
		}
	})

	return &response.EmptyResponse{}, nil
}

func handleOrderLiquidationNotifyData(ctx context.Context, data LiquidationNotifyData) error {
	retailerOrder := getRetailerOrder(ctx, data.OrderId)
	retailerOrder.UpdateReceivers(ctx, data.OrderId)

	if !isRetailerOrderStatusUpdated(ctx, retailerOrder, data) {
		channelOrderId, err := ec_retailer.GetChannelOrderId(ctx, data.OrderId)
		if err != nil {
			log.Warn(ctx, "Get channel orderId fail", log.Fields{
				"errMessage":      err.Error(),
				"retailerOrderId": retailerOrder.Id.Hex(),
			})
			return err
		}
		updateRetailerOrder(ctx, retailerOrder, data, channelOrderId)
	}
	retailerOrder.IncDiscountCampaignSales(ctx, data.OrderId, false)
	ecOrderIds := getEcOrderIds(*retailerOrder, data.OrderId)
	err := callEcOrderPaidWebhook(ctx, data, *retailerOrder, ecOrderIds)
	if err != nil {
		log.Warn(ctx, "Call ec orderPaidWebhook fail", log.Fields{
			"errMessage":      err.Error(),
			"retailerOrderId": retailerOrder.Id.Hex(),
			"ecOrderIds":      ecOrderIds,
		})
		return err
	}
	return nil
}

func getRetailerOrder(ctx context.Context, orderId string) *ec_retailer.RetailerOrder {
	order := ec_retailer.CRetailerOrder.GetById(ctx, util.ToMongoId(orderId))
	if order != nil {
		return order
	}
	retailerOrder, _ := ec_retailer.CRetailerOrder.GetBySubOrderId(ctx, util.ToMongoId(orderId))
	return &retailerOrder
}

// 判断是否已经更新状态，避免多次通知重复更新 subOrders
func isRetailerOrderStatusUpdated(ctx context.Context, retailerOrder *ec_retailer.RetailerOrder, data LiquidationNotifyData) bool {
	if retailerOrder.Id.Hex() == data.OrderId {
		return retailerOrder.Status == ec_retailer.ORDER_PAID_STATUS
	} else {
		for _, subOrder := range retailerOrder.SubOrders {
			if subOrder.Id.Hex() == data.OrderId {
				return subOrder.Status == ec_retailer.ORDER_PAID_STATUS
			}
		}
	}
	return false
}

func updateRetailerOrder(ctx context.Context, order *ec_retailer.RetailerOrder, data LiquidationNotifyData, channelOrderId string) {
	// 总单支付
	if order.Id.Hex() == data.OrderId {
		order.TradeNo = channelOrderId
		order.YeePayOrderNo = data.UniqueOrderNo
		order.Status = ec_retailer.ORDER_PAID_STATUS
		for _, subOrder := range order.SubOrders {
			subOrder.Status = ec_retailer.ORDER_PAID_STATUS
		}
	} else {
		paidSubOrderCount := 0
		for _, subOrder := range order.SubOrders {
			if subOrder.Id.Hex() == data.OrderId {
				subOrder.TradeNo = channelOrderId
				subOrder.YeePayOrderNo = data.UniqueOrderNo
				subOrder.Status = ec_retailer.ORDER_PAID_STATUS
			}
			if subOrder.Status == ec_retailer.ORDER_PAID_STATUS {
				paidSubOrderCount++
			}
		}
		if len(order.SubOrders) == paidSubOrderCount {
			order.Status = ec_retailer.ORDER_PAID_STATUS
		}
	}
	order.SetProfitAmount(ctx, false)
	order.Update(ctx)
}

func getEcOrderIds(order ec_retailer.RetailerOrder, orderId string) []string {
	orderIds := []string{}
	if order.Id.Hex() != orderId {
		return append(orderIds, orderId)
	}
	for _, subOrder := range order.SubOrders {
		orderIds = append(orderIds, subOrder.Id.Hex())
	}
	return orderIds
}

func callEcOrderPaidWebhook(ctx context.Context, liquidationNotifyData LiquidationNotifyData, retailerOrder ec_retailer.RetailerOrder, ecOrderIds []string) error {
	store, err := ec_store.CStore.GetById(ctx, retailerOrder.StoreId)
	if err != nil {
		return err
	}
	member, err := GetMember(ctx, &pb_member.MemberDetailRequest{Id: retailerOrder.MemberId.Hex()})
	if err != nil {
		return err
	}

	orderPaidWebhookData := share_component.OrderPaidWebhookData{
		AccountId:   util.GetAccountId(ctx),
		TradeNo:     liquidationNotifyData.UniqueOrderNo,
		TotalFee:    share.Yuan2Fen(cast.ToFloat64(liquidationNotifyData.OrderAmount)),
		TradeStatus: ec_order.ORDER_STATUS_PAID,
	}
	for _, orderId := range ecOrderIds {
		bills := ec_retailer.CRetailerBill.GetAllBillsOfOrder(ctx, store.Id, bson.ObjectIdHex(orderId), ec_retailer.BILL_TYPE_INCOME)
		if len(bills) > 0 {
			continue
		}

		err := callOrderPaidWebhookForSubOrder(ctx, orderId, orderPaidWebhookData, member, store, retailerOrder)
		if err != nil {
			log.Warn(ctx, "Failed to call orderPaidWebhook for subOrder", log.Fields{
				"errMessage":    err.Error(),
				"subOrderId":    orderId,
				"retailerOrder": retailerOrder,
			})
			continue
		}
	}

	return nil
}

func callOrderPaidWebhookForSubOrder(ctx context.Context, orderId string, orderPaidWebhookData share_component.OrderPaidWebhookData, member *pb_member.MemberDetailResponse, store ec_store.Store, retailerOrder ec_retailer.RetailerOrder) error {
	orderPaidWebhookData.OutTradeNo = orderId
	orderPaidWebhookDataStr, err := json.Marshal(orderPaidWebhookData)
	if err != nil {
		return err
	}

	webhookReq := &request.MaiWebhookRequest{
		Body: string(orderPaidWebhookDataStr),
	}
	_, err = client.GetEcOrderServiceClient().OrderPaidWebhook(ctx, webhookReq)
	if err != nil {
		log.Warn(ctx, "Call orderPaidWebhook fail", log.Fields{
			"errMessage":   err.Error(),
			"req":          webhookReq,
			"subEcOrderId": orderId,
		})
		return err
	}
	subOrderMap := core_util.MakeMapper("Id", retailerOrder.SubOrders)
	if val, ok := subOrderMap[bson.ObjectIdHex(orderId)]; ok {
		subOrder := val.(*ec_retailer.SubOrder)
		component.GO(ctx, func(ctx context.Context) {
			ecOrder, err := ec_order.COrder.GetById(ctx, util.ToMongoId(orderId))
			if err != nil {
				log.Warn(ctx, "Failed to get ec order", log.Fields{
					"errMessage":   err.Error(),
					"subEcOrderId": orderId,
				})
				return
			}

			if !ecOrder.HasPaidHistory() {
				log.Warn(ctx, "SubOrder status has not paid", log.Fields{
					"errMessage":   err.Error(),
					"subEcOrderId": orderId,
				})
				return
			}
			profitAmountMap, err := GetProfitAmountMap(ctx, subOrder, ecOrder, store)
			if err != nil {
				log.Warn(ctx, "Get subOrder profitAmountMap fail", log.Fields{
					"errMessage": err.Error(),
					"subOrder":   subOrder,
				})
				return
			}
			err = CreateRetailerBills(ctx, subOrder, ecOrder, member, store, profitAmountMap)
			if err != nil {
				log.Warn(ctx, "Failed to create retailer bills for retailer subOrder", log.Fields{
					"errMessage":   err.Error(),
					"subEcOrderId": orderId,
				})
				return
			}
			// 如果品牌设置的支付后立即分账，则需要创建品牌预分账单，更新 subOrder.BrandProfitAmount 以及更新 ecOrder.Tags
			if subOrder.BrandDivideType == ec_retailer.BRAND_DIVIDE_TYPE_AFTER_PAID {
				receiverOrderProfit := createOrderReceiverProfitForBrand(ctx, ecOrder, profitAmountMap, subOrder, store)
				for _, o := range retailerOrder.SubOrders {
					if o.Id.Hex() == orderId {
						o.BrandProfitAmount = receiverOrderProfit.ProfitAmount
					}
				}
				retailerOrder.Update(ctx)
				ecOrder.Tags = append(ecOrder.Tags, ec_order.ORDER_TAGS_BRAND_DIVIDE_TYPE_AFTER_PAID)
				ecOrder.UpdateTags(ctx)
			}
		})
	}
	return nil
}

func GetProfitAmountMap(ctx context.Context, subOrder *ec_retailer.SubOrder, ecOrder ec_order.Order, store ec_store.Store) (map[string]uint64, error) {
	err := subOrder.CheckAmount()
	if err != nil {
		log.Warn(ctx, "Check subOrder amount fail", log.Fields{
			"subOrder":   subOrder,
			"errMessage": err.Error(),
		})
		return nil, err
	}
	subOrder.FormatSubOrderType(ctx, store)
	subOrder.ProductProfitAmount = GetSubOrderStoreProfitAmount(ctx, &ecOrder, *subOrder)
	subOrder.OriginalAmount = GetD0ConsignmentSubOrderOriginalPrice(*subOrder, ecOrder)
	subOrder.StaffProfitAmount, subOrder.ActualStaffProfitAmount = GetSubOrderStaffProfitAmount(ctx, &ecOrder, *subOrder)
	receivers := subOrder.Receivers
	if !subOrder.CheckProfitSharingReceivers(store, receivers) {
		log.Warn(ctx, "Check profit sharing receiver count fail", log.Fields{
			"subOrder":  subOrder,
			"receivers": receivers,
		})
		return nil, errors.New("Check profit sharing Receiver count fail")
	}
	profitAmountMap, err := CalProfitAmount(ctx, receivers, *subOrder)
	if err != nil {
		return nil, err
	}
	if !subOrder.CheckProfitAmount(profitAmountMap) {
		log.Warn(ctx, "Check profit amount fail", log.Fields{
			"subOrder":        subOrder,
			"profitAmountMap": profitAmountMap,
		})
		return nil, err
	}
	return profitAmountMap, nil
}
