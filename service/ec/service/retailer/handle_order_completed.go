package retailer

import (
	"context"
	"encoding/json"
	"mairpc/core/component"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/core/validators"
	common "mairpc/proto/common/request"
	"mairpc/proto/common/response"
	pb_member "mairpc/proto/member"
	ec_model "mairpc/service/ec/model"
	ec_order "mairpc/service/ec/model/order"
	ec_profitsharing "mairpc/service/ec/model/profitsharing"
	ec_retailer "mairpc/service/ec/model/retailer"
	ec_setting "mairpc/service/ec/model/setting"
	ec_store "mairpc/service/ec/model/store"
	"mairpc/service/share/util"
	"time"
)

func (RetailerService) HandleOrderCompleted(ctx context.Context, req *common.CustomerEventRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	properties, err := getProperties(req.Properties)
	if err != nil {
		return nil, err
	}

	orderId := util.ToMongoId(properties["id"].(string))
	order, err := ec_order.COrder.GetById(ctx, orderId)
	if err != nil {
		return nil, err
	}

	if !order.IsYeepayForD2R() {
		return &response.EmptyResponse{}, nil
	}
	retailerOrder, err := ec_retailer.CRetailerOrder.GetBySubOrderId(ctx, orderId)
	if err != nil {
		return nil, err
	}
	nowUnixMilli, err := getRetailerOrderLock(ctx, retailerOrder.Id.Hex())
	if err != nil {
		return nil, err
	}
	defer delRetailerOrderLock(ctx, retailerOrder.Id.Hex(), nowUnixMilli)
	new := ec_retailer.CRetailerOrder.GetById(ctx, retailerOrder.Id)
	retailerOrder = *new
	// 为了保证可分账金额的准确，这里再次设置可分账金额
	retailerOrder.SetProfitAmount(ctx, true)
	retailerOrder.Update(ctx)

	subOrder := retailerOrder.GetSubOrder(orderId.Hex())
	subOrder.ProductProfitAmount = GetSubOrderStoreProfitAmount(ctx, &order, *subOrder)
	subOrder.OriginalAmount = GetD0ConsignmentSubOrderOriginalPrice(*subOrder, order)
	subOrder.StaffProfitAmount, subOrder.ActualStaffProfitAmount = GetSubOrderStaffProfitAmount(ctx, &order, *subOrder)
	// 为脉盟订单创建预分账记录
	CreateOrderReceiverProfit(ctx, subOrder, order)
	UpdateSubOrderStatus(ctx, retailerOrder, orderId.Hex())
	return &response.EmptyResponse{}, nil
}

func UpdateSubOrderStatus(ctx context.Context, retailerOrder ec_retailer.RetailerOrder, orderId string) {
	for _, subOrder := range retailerOrder.SubOrders {
		if subOrder.Id.Hex() == orderId {
			subOrder.Status = ec_retailer.ORDER_COMPLETED_STATUS
		}
	}
	retailerOrder.Update(ctx)
}

func getProperties(req string) (map[string]interface{}, error) {
	var properties map[string]interface{}
	err := json.Unmarshal([]byte(req), &properties)
	if err != nil {
		return nil, err
	}
	return properties, nil
}

// 为脉盟订单创建预分账账单
func CreateOrderReceiverProfit(ctx context.Context, subOrder *ec_retailer.SubOrder, order ec_order.Order) {
	if order.IsNotDivide() {
		return
	}
	// 子订单已经全部退款，不需要创建预分账账单
	if subOrder.IsRefunded(ctx) {
		log.Warn(ctx, "SubOrder all product refunded", log.Fields{
			"subOrder": subOrder,
		})
		return
	}

	err := subOrder.CheckAmount()
	if err != nil {
		log.Warn(ctx, "Check subOrder amount fail", log.Fields{
			"subOrder":   subOrder,
			"errMessage": err.Error(),
		})
		return
	}
	store, err := ec_store.CStore.GetById(ctx, order.StoreId)
	if err != nil {
		log.Warn(ctx, "Get store fail", log.Fields{
			"subOrder":   subOrder,
			"errMessage": err.Error(),
		})
		return
	}
	subOrder.FormatSubOrderType(ctx, store)

	receivers := subOrder.Receivers
	if !subOrder.CheckProfitSharingReceivers(store, receivers) {
		log.Warn(ctx, "Check profit sharing receiver count fail", log.Fields{
			"subOrder":  subOrder,
			"receivers": receivers,
		})
		return
	}
	profitAmountMap, err := CalProfitAmount(ctx, receivers, *subOrder)
	if err != nil {
		log.Warn(ctx, "Cal profit amount fail", log.Fields{
			"subOrder":     subOrder,
			"errorMessage": err.Error(),
		})
		return
	}

	if !subOrder.CheckProfitAmount(profitAmountMap) {
		log.Warn(ctx, "check profit amount fail", log.Fields{
			"subOrder":        subOrder,
			"profitAmountMap": profitAmountMap,
		})
		return
	}
	createOrderReceiverProfitWithReceivers(ctx, order, receivers, profitAmountMap, subOrder, store)
}

func createOrderReceiverProfitWithReceivers(
	ctx context.Context,
	order ec_order.Order,
	receivers []ec_profitsharing.ProfitSharingReceiver,
	profitAmountMap map[string]uint64,
	subOrder *ec_retailer.SubOrder,
	store ec_store.Store,
) {
	orderInfo := ec_order.COrderReceiverProfit.GetOrderInfo(ctx, order)
	for _, r := range receivers {
		// 品牌分账接收方已经创建过了，不需要再创建
		if subOrder.BrandDivideType == ec_retailer.BRAND_DIVIDE_TYPE_AFTER_PAID && r.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_BRAND {
			continue
		}
		profitAmount := profitAmountMap[r.RelationType]
		if profitAmount == 0 {
			log.Warn(ctx, "Sub order profitAmount amount is zero", log.Fields{
				"subOrder": subOrder,
				"receiver": r,
			})
			UpdateRetailerBillsWhenOrderStatusChanges(ctx, *subOrder, r, "", 0, store)
			continue
		}
		shareAt := order.CompletedAt.Add(time.Hour * 24 * time.Duration(7)) // 代销订单的结算周期为“T+7”
		// 零售业务经销订单完成后立即结算
		if order.IsDistributionOrder() {
			shareAt = time.Now()
		}
		receiverOrderProfit := ec_order.OrderReceiverProfit{
			Id:                   bson.NewObjectId(),
			Order:                orderInfo,
			ReceiverId:           r.Id,
			Proportion:           r.Proportion,
			ProfitAmount:         profitAmount,
			CycleType:            ec_setting.PROFITSHARING_CYCLE_TYPE_DAY,
			TransferChannel:      ec_order.TRANSFER_CHANNEL_YEEPAY,
			ProfitType:           r.ProfitType,
			ShareAt:              shareAt,
			SingleOrderProfitCap: r.SingleOrderProfitCap,
		}
		if r.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_STAFF {
			receiverOrderProfit.Promoter = ec_order.Promoter{
				Id:   order.Distribution.PromoterId,
				Type: order.Distribution.PromoterType,
			}
		}
		err := receiverOrderProfit.CreateNotCheckAmount(ctx)
		if err != nil {
			log.Warn(ctx, "Failed to create maimeng orderReceiverProfit", log.Fields{
				"function":            "createForMaiMengOrder",
				"orderReceiverProfit": receiverOrderProfit,
				"errorMessage":        err.Error(),
			})
		} else {
			// r 变量在循环里指向同一块内存地址，直接在下面协程里使用会出问题
			tempReceiver := r
			component.GO(ctx, func(ctx context.Context) {
				UpdateRetailerBillsWhenOrderStatusChanges(ctx, *subOrder, tempReceiver, receiverOrderProfit.Id.Hex(), receiverOrderProfit.ProfitAmount, store)
			})
		}
	}
	// 单独更新易宝手续费账单
	updatePaymentFeeBillAmount(ctx, profitAmountMap[ec_retailer.PAYMENT_FEE_YEEPAY], subOrder, order, receivers, store)
	// 单独更新脉盟小店/连锁零售商收单账单
	updateReceiptBillAmount(ctx, profitAmountMap, subOrder, order)
}

func createOrderReceiverProfitForBrand(
	ctx context.Context,
	order ec_order.Order,
	profitAmountMap map[string]uint64,
	subOrder *ec_retailer.SubOrder,
	store ec_store.Store,
) ec_order.OrderReceiverProfit {
	orderInfo := ec_order.COrderReceiverProfit.GetOrderInfo(ctx, order)
	for _, r := range subOrder.Receivers {
		if r.RelationType != ec_profitsharing.RECEIVER_RELATION_TYPE_BRAND {
			continue
		}
		profitAmount := profitAmountMap[r.RelationType]
		if profitAmount == 0 {
			log.Warn(ctx, "Sub order profitAmount amount is zero", log.Fields{
				"subOrder": subOrder,
				"receiver": r,
			})
			UpdateRetailerBillsWhenOrderStatusChanges(ctx, *subOrder, r, "", 0, store)
			continue
		}
		receiverOrderProfit := ec_order.OrderReceiverProfit{
			Id:                   bson.NewObjectId(),
			Order:                orderInfo,
			ReceiverId:           r.Id,
			Proportion:           r.Proportion,
			ProfitAmount:         profitAmount,
			CycleType:            ec_setting.PROFITSHARING_CYCLE_TYPE_DAY,
			TransferChannel:      ec_order.TRANSFER_CHANNEL_YEEPAY,
			ProfitType:           r.ProfitType,
			ShareAt:              time.Now(),
			SingleOrderProfitCap: r.SingleOrderProfitCap,
		}
		err := receiverOrderProfit.CreateNotCheckAmount(ctx)
		if err != nil {
			log.Warn(ctx, "Failed to create maimeng orderReceiverProfit", log.Fields{
				"function":            "createForMaiMengOrder",
				"orderReceiverProfit": receiverOrderProfit,
				"errorMessage":        err.Error(),
			})
		}
		UpdateRetailerBillsWhenOrderStatusChanges(ctx, *subOrder, r, receiverOrderProfit.Id.Hex(), receiverOrderProfit.ProfitAmount, store)
		return receiverOrderProfit
	}
	return ec_order.OrderReceiverProfit{}
}

func CalProfitAmount(ctx context.Context, receivers []ec_profitsharing.ProfitSharingReceiver, subOrder ec_retailer.SubOrder) (map[string]uint64, error) {
	if subOrder.IsDistributionOrder() {
		return CalDistributionProfitAmount(ctx, receivers, subOrder)
	}
	return CalD0WithD1StoreConsignmentProfitAmount(ctx, receivers, subOrder)
}

// 计算经销订单分账金额
// 分账接收方：平台（固定比例手续费3%，单笔最高1.5，按字段单独算），经销商D1(平台手续费分成)，小店
//
// payAmount 子单总支付金额
// profitAmount 子单可分账金额
// yeepayserviceAmount = payAmount - profitAmount 易宝服务费
// platformServiceAmount = payAmount * 3%，如果大于 1.5元，则为 1.5元 平台手续费
// storeAmount = payAmount - yeepayserviceAmount - platformServiceAmount = profitAmount - platformServiceAmount 小店实际分账金额
func CalDistributionProfitAmount(ctx context.Context, receivers []ec_profitsharing.ProfitSharingReceiver, subOrder ec_retailer.SubOrder) (map[string]uint64, error) {
	payAmount := subOrder.GetActualPayAmount()
	profitAmount := subOrder.ProfitAmount
	platformServiceAmount, platformServiceSubAmount := ec_retailer.CalPlatformServiceAmount(receivers, payAmount)
	storeAmount := profitAmount - platformServiceAmount - platformServiceSubAmount
	yeepayServiceAmount := payAmount - profitAmount
	if ec_retailer.HaveStaffReceiver(receivers) {
		storeAmount = storeAmount - subOrder.ActualStaffProfitAmount
	}
	log.Warn(ctx, "CalDistributionProfitAmount log", log.Fields{
		"payAmount":                payAmount,
		"profitAmount":             profitAmount,
		"platformServiceAmount":    platformServiceAmount,
		"platformServiceSubAmount": platformServiceSubAmount,
		"yeepayServiceAmount":      yeepayServiceAmount,
		"staffProfitAmount":        subOrder.StaffProfitAmount,
		"actualStaffProfitAmount":  subOrder.ActualStaffProfitAmount,
	})
	profitAmountMap := map[string]uint64{}
	// 经销订单分给平台的手续费
	profitAmountMap[ec_profitsharing.RECEIVER_RELATION_TYPE_PLATFORM] = platformServiceAmount
	// 平台手续费分给D1经销商
	profitAmountMap[ec_profitsharing.RECEIVER_RELATION_TYPE_PLATFORM_SUB] = platformServiceSubAmount
	// 小店货款分账
	profitAmountMap[ec_profitsharing.RECEIVER_RELATION_TYPE_STORE_OWNER] = storeAmount
	// 易宝支付手续费
	profitAmountMap[ec_retailer.PAYMENT_FEE_YEEPAY] = yeepayServiceAmount
	// 导购分佣
	if ec_retailer.HaveStaffReceiver(receivers) {
		profitAmountMap[ec_profitsharing.RECEIVER_RELATION_TYPE_STAFF] = subOrder.ActualStaffProfitAmount
	}
	return profitAmountMap, nil
}

// 计算 D0/D1 代销订单分账金额
//
// payAmount 子单总支付金额
// profitAmount 子单可分账金额
// originalAmount 子单商品成本价,需要分给品牌
// productProfitAmount 不同商品分佣金额之和
//
// yeepayServiceAmount 易宝服务费
// platformServiceAmount 平台服务费
// platformServiceSubAmount 平台服务费经销商分成
// storeProfitAmount = productProfitAmount - serviceAmount - platformServiceAmount = 小店实际分账金额
// d1DistributionProfitAmount 如果是脉凯盟经销订单，需要固定给门店入驻的第一个经销商分账，固定比例 2%
// distributionAmount = payAmount - productProfitAmount - d1DistributionProfitAmount D1 经销商分账金额
func CalD0WithD1StoreConsignmentProfitAmount(ctx context.Context, receivers []ec_profitsharing.ProfitSharingReceiver, subOrder ec_retailer.SubOrder) (map[string]uint64, error) {
	payAmount := subOrder.GetActualPayAmount()
	profitAmount := subOrder.ProfitAmount
	originalAmount := subOrder.OriginalAmount
	productProfitAmount := subOrder.ProductProfitAmount

	storeProfitAmount := productProfitAmount
	yeepayServiceAmount := payAmount - profitAmount
	platformServiceAmount, platformServiceSubAmount := ec_retailer.CalPlatformServiceAmount(receivers, payAmount)
	d1DistributionProfitAmount := calDOneDistributionProfitAmount(subOrder, receivers, payAmount)
	distributionAmount := payAmount - productProfitAmount - d1DistributionProfitAmount

	if ec_retailer.HaveBrandReceiver(receivers) {
		distributionAmount = distributionAmount - originalAmount
	}

	if ec_model.IsChainRetail(ctx) {
		// 如果参与了活动，目前只有限时特价活动，并且开启了佣金设置，导购佣金不用从渠道佣金中扣除，而是使用活动设置的分销员佣金，从经销商分账金额扣除
		if subOrder.CheckCampaigns(ctx) {
			// 导购佣金从经销商分账金额扣除
			if ec_retailer.HaveStaffReceiver(receivers) {
				distributionAmount = distributionAmount - subOrder.ActualStaffProfitAmount
			}
		} else {
			// 导购佣金从渠道佣金中扣除
			if ec_retailer.HaveStaffReceiver(receivers) {
				storeProfitAmount = productProfitAmount - subOrder.ActualStaffProfitAmount
			}
		}
	}

	// D0 脉盟新商代销分佣要分给：易宝（支付手续费）、脉凯盟（平台服务费）、D1(平台服务费分成)
	totalServiceAmount := yeepayServiceAmount + platformServiceAmount + platformServiceSubAmount
	if distributionAmount >= totalServiceAmount {
		distributionAmount = distributionAmount - yeepayServiceAmount - platformServiceAmount - platformServiceSubAmount
	} else {
		// 不够分的异常情况处理
		if distributionAmount > yeepayServiceAmount {
			// 够分易宝手续费，平台手续费少扣
			totalPlatformServiceAmount := distributionAmount - yeepayServiceAmount
			platformServiceAmount, platformServiceSubAmount = ec_retailer.CalPlatformServiceAmountWithTotalPlatformServiceAmount(receivers, totalPlatformServiceAmount)
			distributionAmount = 0
		} else {
			// 不够分易宝手续费，平台手续费、代销分佣直接为 0，验算逻辑会报错
			platformServiceAmount = 0
			platformServiceSubAmount = 0
			distributionAmount = 0
		}
	}

	profitAmountMap := map[string]uint64{}
	// 平台手续费
	profitAmountMap[ec_profitsharing.RECEIVER_RELATION_TYPE_PLATFORM] = platformServiceAmount
	// 平台手续费分给D1经销商
	profitAmountMap[ec_profitsharing.RECEIVER_RELATION_TYPE_PLATFORM_SUB] = platformServiceSubAmount
	// 小店
	profitAmountMap[ec_profitsharing.RECEIVER_RELATION_TYPE_STORE_OWNER] = storeProfitAmount
	// 经销商
	profitAmountMap[ec_profitsharing.RECEIVER_RELATION_TYPE_DISTRIBUTOR] = distributionAmount
	// D0代销订单，需要分给D1经销商
	profitAmountMap[ec_profitsharing.RECEIVER_RELATION_TYPE_D_ONE_DISTRIBUTOR] = d1DistributionProfitAmount
	// 易宝支付手续费
	profitAmountMap[ec_retailer.PAYMENT_FEE_YEEPAY] = yeepayServiceAmount
	// 品牌货款
	if ec_retailer.HaveBrandReceiver(receivers) {
		profitAmountMap[ec_profitsharing.RECEIVER_RELATION_TYPE_BRAND] = originalAmount
	}
	// 导购分佣
	if ec_retailer.HaveStaffReceiver(receivers) {
		profitAmountMap[ec_profitsharing.RECEIVER_RELATION_TYPE_STAFF] = subOrder.ActualStaffProfitAmount
	}
	log.Warn(ctx, "CalD0WithD1StoreConsignmentProfitAmount log", log.Fields{
		"receivers":               receivers,
		"subOrder":                subOrder,
		"payAmount":               payAmount,
		"profitAmount":            profitAmount,
		"yeepayServiceAmount":     yeepayServiceAmount,
		"productProfitAmount":     productProfitAmount,
		"originalAmount":          subOrder.OriginalAmount,
		"staffProfitAmount":       subOrder.StaffProfitAmount,
		"actualStaffProfitAmount": subOrder.ActualStaffProfitAmount,
		"profitAmountMap":         profitAmountMap,
	})
	return profitAmountMap, nil
}

// D0代销订单分账给D1经销商的钱
func calDOneDistributionProfitAmount(subOrder ec_retailer.SubOrder, receivers []ec_profitsharing.ProfitSharingReceiver, payAmount uint64) uint64 {
	dOneReceiver := ec_profitsharing.ProfitSharingReceiver{}
	for _, receiver := range receivers {
		if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_D_ONE_DISTRIBUTOR {
			dOneReceiver = receiver
		}
	}

	// D0 的代销订单，门店 D0 入驻，不需要给 D1 分账
	if dOneReceiver.RelationType == "" {
		return 0
	}

	proportion := util.DivideFloat(dOneReceiver.Proportion, 100)
	// 兼容旧的逻辑
	if len(subOrder.Products) == 0 {
		return ec_retailer.CalAmountWithProportion(payAmount, proportion)
	}

	// 新的 D1 分账金额 = 跟随系统设置的商品的佣金 + 独立设置佣金商品的商品的佣金
	amount := uint64(0)
	followAmount := uint64(0)
	for _, subOrderProduct := range subOrder.Products {
		if subOrderProduct.CommissionType == ec_retailer.CONSIGNMENT_COMMISSION_TYPE_FOLLOW {
			followAmount += subOrderProduct.Price * subOrderProduct.Total
			continue
		}
		if subOrderProduct.CommissionType == ec_retailer.CONSIGNMENT_COMMISSION_TYPE_ALONE {
			for _, skuAmount := range subOrderProduct.SkuAmounts {
				if subOrderProduct.Sku != skuAmount.Sku {
					continue
				}
				if skuAmount.DOneCommissionAmount > 0 {
					amount += skuAmount.DOneCommissionAmount * subOrderProduct.Total
					break
				}
				if skuAmount.DOneDefaultCommissionAmount > 0 {
					amount += skuAmount.DOneDefaultCommissionAmount * subOrderProduct.Total
				}
			}
		}
	}
	return ec_retailer.CalAmountWithProportion(followAmount, proportion) + amount
}

// 获取代销订单门店分佣金额
func GetSubOrderStoreProfitAmount(ctx context.Context, order *ec_order.Order, subOrder ec_retailer.SubOrder) uint64 {
	// 非代销订单不用计算
	if !order.IsConsignmentOrder() {
		return 0
	}
	productProfitAmount := uint64(0)
	for _, p := range order.Products {
		if p.RefundStatus == ec_order.ORDER_REFUND_STATUS_REFUNDED {
			continue
		}
		productProfitAmount += p.StoreDistributionAmount
	}
	return productProfitAmount
}

// 获取连锁零售商代销/经销订单导购分佣金额
// 脉盟小店经销订单没有导购佣金，代销订单导购分佣金额在订单创建时加到 storeDistributionAmount
func GetSubOrderStaffProfitAmount(ctx context.Context, order *ec_order.Order, subOrder ec_retailer.SubOrder) (uint64, uint64) {
	if !ec_model.IsChainRetail(ctx) {
		return 0, 0
	}
	staffProfitAmount := uint64(0)
	actualStaffProfitAmount := uint64(0)
	for _, p := range order.Products {
		if p.RefundStatus == ec_order.ORDER_REFUND_STATUS_REFUNDED {
			continue
		}
		// 计算实际需要分给导购的金额，需要商品是分销商品，也就是订单中的 isDistribution 是 true
		if p.IsDistribution {
			actualStaffProfitAmount += p.DistributionAmount
		}
		staffProfitAmount += p.DistributionAmount
	}

	return staffProfitAmount, actualStaffProfitAmount
}

// 计算限时特价活动佣金
func calDiscountCampaignProfitAmount(ctx context.Context,
	discountCampaignProductMap map[string][]ec_order.OrderProduct,
	productIdDiscountCampaignIdMap map[string]string,
	ruleType string,
	subOrder ec_retailer.SubOrder,
) map[string]uint64 {
	productIdProfitAmountMap := map[string]uint64{}
	for id, products := range discountCampaignProductMap {
		skus := []ec_retailer.Sku{}
		for _, product := range products {
			sku := ec_retailer.Sku{
				Sku:   product.Spec.Sku,
				Count: product.Total,
			}
			for _, c := range subOrder.Campaigns {
				if c.Sku == sku.Sku {
					sku.StaffSales = c.CurrentStaffSales
					sku.Sales = c.CurrentSales
				}
			}
			skus = append(skus, sku)
		}

		orderProduct := ec_retailer.OrderProduct{
			Id:   id,
			Skus: skus,
		}
		discountCampaignId := productIdDiscountCampaignIdMap[id]
		selector := bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"_id":       util.ToMongoId(discountCampaignId),
		}
		campaign, _ := ec_retailer.CDiscountCampaign.GetByCondition(ctx, selector)
		isDisabled, profitAmount := campaign.CalProfitAmountWithType(ctx, ruleType, orderProduct)
		if !isDisabled {
			productIdProfitAmountMap[id] = profitAmount
		}
	}
	return productIdProfitAmountMap
}

// 获取D0代销订单商品成本价
func GetD0ConsignmentSubOrderOriginalPrice(subOrder ec_retailer.SubOrder, order ec_order.Order) uint64 {
	if util.IsMongoId(subOrder.DistributorId) {
		return 0
	}
	originalPrice := uint64(0)
	for _, p := range order.Products {
		if p.RefundStatus == ec_order.ORDER_REFUND_STATUS_REFUNDED {
			continue
		}
		originalPrice += p.OriginPrice * p.Total
	}
	return originalPrice
}

// 更新非零金额，非空预分账 id，软删除零金额分账账单
func UpdateRetailerBillsWhenOrderStatusChanges(ctx context.Context, subOrder ec_retailer.SubOrder, receiver ec_profitsharing.ProfitSharingReceiver, receiverOrderProfitId string, amount uint64, store ec_store.Store) error {
	var cond *bson.M
	if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_PLATFORM {
		cond = ec_retailer.CRetailerBill.GenPlatformFeeCondForPlatform(ctx, store.Id, subOrder.Id)
	}
	if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_PLATFORM_SUB {
		cond = ec_retailer.CRetailerBill.GenPlatformFeeCondForD1(ctx, store.Id, subOrder.Id, ec_retailer.IsD0EqualD1(ctx, store))
	}
	if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_STORE_OWNER {
		categoryType := ec_retailer.BILL_CATEGORY_CONSIGNMENT_COMMISSION
		if subOrder.IsDistributionOrder() {
			categoryType = ec_retailer.BILL_CATEGORY_DISTRIBUTION
		}
		subjectSubType := ec_retailer.BILL_ACCOUNT_SUB_TYPE_RETAIL_STORE
		if ec_model.IsChainRetail(ctx) {
			subjectSubType = ec_retailer.BILL_ACCOUNT_SUB_TYPE_RETAIL_CHAIN
		}
		cond = ec_retailer.CRetailerBill.GenStoreBillCond(ctx, store.Id, subOrder.Id, categoryType, subjectSubType)
	}
	if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_DISTRIBUTOR {
		if subOrder.Type == ec_retailer.MAIMENG_STORE_PRODUCT_D_ZERO_DISTRIBUTION || subOrder.IsD0CommonConsignmentOrder(ctx, store) {
			cond = ec_retailer.CRetailerBill.GenD0DistributorBillCond(ctx, store.Id, subOrder.Id)
		} else {
			cond = ec_retailer.CRetailerBill.GenDistributorBillCond(ctx, store.Id, subOrder.Id)
		}
	}
	if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_BRAND {
		cond = ec_retailer.CRetailerBill.GenBrandBillCond(ctx, store.Id, subOrder.Id)
	}
	if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_STAFF {
		cond = ec_retailer.CRetailerBill.GenStaffBillCond(ctx, store.Id, subOrder.Id)
	}
	if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_D_ONE_DISTRIBUTOR {
		cond = ec_retailer.CRetailerBill.GenD1DistributorBillCond(ctx, store.Id, subOrder.Id)
	}

	if cond != nil {
		logFields := log.Fields{
			"condition":             cond,
			"amount":                amount,
			"receiverOrderProfitId": receiverOrderProfitId,
		}
		retailerBills := ec_retailer.CRetailerBill.GetAllByCondition(ctx, *cond)
		if len(retailerBills) == 0 {
			log.Warn(ctx, "Failed to find retailer bill to update after order status changes", logFields)
		}
		for _, retailerBill := range retailerBills {
			if amount == 0 {
				return retailerBill.Delete(ctx)
			}
			logFields["retailerBillId"] = retailerBill.Id.Hex()
			if err := retailerBill.UpdateOrderReceiverProfitIdAndAmount(ctx, receiverOrderProfitId, amount); err != nil {
				logFields["errMsg"] = err.Error()
				log.Warn(ctx, "Failed to update retailer bill after order status changes", logFields)
				return err
			} else {
				log.Warn(ctx, "Succeed to update retailer bill after order status changes", logFields)
			}
		}
	}
	return nil
}

func updatePaymentFeeBillAmount(ctx context.Context, profitAmount uint64, subOrder *ec_retailer.SubOrder, order ec_order.Order, receivers []ec_profitsharing.ProfitSharingReceiver, store ec_store.Store) {
	cond := ec_retailer.CRetailerBill.GenPaymentFeeCond(ctx, order.StoreId, order.Id)
	retailerBills := ec_retailer.CRetailerBill.GetAllByCondition(ctx, *cond)
	if len(retailerBills) == 0 {
		log.Warn(ctx, "Failed to find paymentFee bill", log.Fields{
			"subOrder": subOrder,
			"cond":     cond,
		})

		// 合单手续费按子单实际金额占比平摊并且进行了四舍五入，难免会出现某几分钱手续费承担方（指的是子单）因为比例的变化而发生变更
		// 对于这种的情况为了保证账单能对上，需要在此补上变更了承担方的手续费账单
		if profitAmount > 0 {
			logFields := log.Fields{
				"storeId":      store.Id.Hex(),
				"subOrder":     subOrder,
				"profitAmount": profitAmount,
			}
			member, err := GetMember(ctx, &pb_member.MemberDetailRequest{Id: order.MemberId.Hex()})
			if err != nil {
				logFields["err"] = err.Error()
				log.Warn(ctx, "Failed to find member for make up the paymentFee bill", logFields)
				return
			}
			baseIncomeRetailerBill := genBaseRetailerBillByType(ctx, *subOrder, order, member, receivers, store, ec_retailer.BILL_TYPE_INCOME)
			baseSpentRetailerBill := genBaseRetailerBillByType(ctx, *subOrder, order, member, receivers, store, ec_retailer.BILL_TYPE_SPENT)
			paymentFeeBills := genPaymentFeeRetailerBills(baseIncomeRetailerBill, baseSpentRetailerBill, profitAmount)
			err = ec_retailer.CRetailerBill.BatchInsert(ctx, paymentFeeBills)
			if err != nil {
				logFields["err"] = err.Error()
			}
			log.Warn(ctx, "Result for make up the paymentFee bill", logFields)
			return
		}
		return
	}

	for _, retailerBill := range retailerBills {
		if profitAmount == 0 {
			err := retailerBill.Delete(ctx)
			if err != nil {
				log.Warn(ctx, "Failed to delete paymentFee bill", log.Fields{
					"subOrder": subOrder,
					"cond":     cond,
					"type":     retailerBill.Type,
				})
			} else {
				if retailerBill.Type == ec_retailer.BILL_TYPE_SPENT {
					retailerBill.IsDeleted = true
					UpsertDistributionBill(ctx, retailerBill)
				}
			}
			continue
		}
		err := retailerBill.UpdateOrderReceiverProfitIdAndAmount(ctx, "", profitAmount)
		if err != nil {
			log.Warn(ctx, "Failed to update paymentFee bill amount", log.Fields{
				"subOrder": subOrder,
				"cond":     cond,
				"type":     retailerBill.Type,
			})
		} else {
			if retailerBill.Type == ec_retailer.BILL_TYPE_SPENT {
				newCond := ec_retailer.Common.GenDefaultConditionById(ctx, retailerBill.Id)
				newRetailerBill := ec_retailer.CRetailerBill.GetByCondition(ctx, newCond)
				UpsertDistributionBill(ctx, *newRetailerBill)
			}
		}
	}
}

func updateReceiptBillAmount(ctx context.Context, profitAmountMap map[string]uint64, subOrder *ec_retailer.SubOrder, order ec_order.Order) {
	cond := ec_retailer.CRetailerBill.GenReceiptBillCond(ctx, order.StoreId, order.Id)
	retailerBill := ec_retailer.CRetailerBill.GetByCondition(ctx, *cond)
	if retailerBill == nil {
		log.Warn(ctx, "Failed to find receipt bill", log.Fields{
			"subOrder": subOrder,
			"cond":     cond,
		})
		return
	}
	profitAmount := GetReceiptBillTotalAmount(profitAmountMap)
	if profitAmount == 0 {
		err := retailerBill.Delete(ctx)
		if err != nil {
			log.Warn(ctx, "Failed to delete receipt bill", log.Fields{
				"subOrder": subOrder,
				"cond":     cond,
			})
		} else {
			retailerBill.IsDeleted = true
			UpsertDistributionBill(ctx, *retailerBill)
		}
		return
	}
	err := retailerBill.UpdateOrderReceiverProfitIdAndAmount(ctx, "", profitAmount)
	if err != nil {
		log.Warn(ctx, "Failed to update receipt bill amount", log.Fields{
			"subOrder": subOrder,
			"cond":     cond,
		})
	} else {
		newCond := ec_retailer.Common.GenDefaultConditionById(ctx, retailerBill.Id)
		newRetailerBill := ec_retailer.CRetailerBill.GetByCondition(ctx, newCond)
		UpsertDistributionBill(ctx, *newRetailerBill)
	}
}
