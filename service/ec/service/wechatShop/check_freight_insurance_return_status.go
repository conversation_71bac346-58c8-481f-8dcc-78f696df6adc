package wechatShop

import (
	"context"
	"fmt"
	"mairpc/core/component"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	pb_ec_logistics "mairpc/proto/ec/logistic"
	ec_model_order "mairpc/service/ec/model/order"
	"mairpc/service/share/util"
	"sync"
	"time"
)

const (
	FREIGHT_INSURANCE_RETURN_STATUS_LOCK = "%s:ec:wechatShop:checkFreightInsuranceReturnStatus"
)

func (WechatShopService) CheckFreightInsuranceReturnStatus(ctx context.Context, req *request.EmptyRequest) (*response.EmptyResponse, error) {
	component.GO(core_util.CtxWithReadSecondaryPreferred(ctx), checkFreightInsuranceReturnStatus)
	return &response.EmptyResponse{}, nil
}

func checkFreightInsuranceReturnStatus(ctx context.Context) {
	resp, err := client.GetEcLogisticServiceClient().GetDeliveryFeeTemplate(ctx, &pb_ec_logistics.GetDeliveryFeeTemplateRequest{
		Method: "express",
	})
	if err != nil || !resp.IsFreightInsuranceEnabled {
		return
	}
	var (
		key       = fmt.Sprintf(FREIGHT_INSURANCE_RETURN_STATUS_LOCK, core_util.GetAccountId(ctx))
		ok, _     = extension.RedisClient.SetNX(key, "1", 60)
		condition = ec_model_order.Common.GenDefaultCondition(ctx)
		lastId    bson.ObjectId
		wg        = &sync.WaitGroup{}
		startAt   = time.Now()
	)
	if !ok {
		return
	}
	defer extension.RedisClient.Del(key)
	condition["status"] = ec_model_order.ORDER_REFUND_STATUS_APPROVED
	condition["tags"] = ec_model_order.ORDER_TAGS_WITH_WECHAT_FREIGHT_INSURANCE
	condition["refundType"] = ec_model_order.ORDER_REFUND_TYPE_RETURN_AND_REFUND
	pool, err := util.NewGoroutinePoolWithPanicHandler(10, util.WithContext(ctx))
	if err != nil {
		return
	}
	defer pool.Release()
	for time.Since(startAt) < time.Second*30 {
		var refunds []ec_model_order.OrderRefund
		if lastId.Valid() {
			condition["_id"] = bson.M{
				"$gt": lastId,
			}
		}
		extension.DBRepository.FindAll(ctx, ec_model_order.C_ORDER_REFUND, condition, []string{"_id"}, 100, &refunds)
		if len(refunds) == 0 {
			break
		}
		lastId = refunds[len(refunds)-1].Id
		for _, refund := range refunds {
			wg.Add(1)
			pool.Submit(func() {
				defer wg.Done()
				refreshFreightInsuranceReturnStatus(ctx, refund)
			})
		}
		wg.Wait()
	}
}
