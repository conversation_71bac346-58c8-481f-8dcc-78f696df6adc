package wechatShop

import (
	"context"
	"encoding/json"
	"errors"
	"mairpc/core/extension/bson"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	pb_ec_order "mairpc/proto/ec/order"
	ec_model_order "mairpc/service/ec/model/order"

	"github.com/qiniu/qmgo"
)

type baseEvent struct {
	ToUserName   string `json:"ToUserName"`
	FromUserName string `json:"FromUserName"`
	CreateTime   int64  `json:"CreateTime,string"`
	MsgType      string `json:"MsgType"`
	Event        string `json:"Event"`
}

// 此事件有两种触发情况：
// 订单完成发货时
// 订单结算时（手动确认收货会导致结算）
type orderSettlementEvent struct {
	baseEvent
	TransactionId           string `json:"transaction_id"`                   // 支付单号
	MerchantId              string `json:"merchant_id"`                      // 商户号
	SubMerchantId           string `json:"sub_merchant_id"`                  // 二级商户号
	MerchantTradeNo         string `json:"merchant_trade_no"`                // 商户订单号
	PayTime                 int64  `json:"pay_time,string"`                  // 支付成功时间
	ShippedTime             int64  `json:"shipped_time,string"`              // 发货时间
	EstimatedSettlementTime int64  `json:"estimated_settlement_time,string"` // 预计结算时间
	ConfirmReceiveMethod    int64  `json:"confirm_receive_method,string"`    // 确认收货方式
	ConfirmReceiveTime      int64  `json:"confirm_receive_time,string"`      // 确认收货时间
	SettlementTime          int64  `json:"settlement_time,string"`           // 订单结算时间
	Type                    string `json:"type"`
}

const (
	EVENT_TRADE_MANAGE_ORDER_SETTLEMENT = "trade_manage_order_settlement"
)

func (WechatShopService) HandleWechatTradeManagementEvents(ctx context.Context, req *request.MaiWebhookRequest) (*response.EmptyResponse, error) {
	// 暂时只接入这一个事件
	event := orderSettlementEvent{}
	err := json.Unmarshal([]byte(req.Body), &event)
	if err != nil {
		return nil, err
	}
	resp := &response.EmptyResponse{}
	if event.Type != EVENT_TRADE_MANAGE_ORDER_SETTLEMENT {
		return resp, nil
	}
	// 暂时只处理订单结算导致的回调
	if event.ConfirmReceiveTime == 0 || !bson.IsObjectIdHex(event.MerchantTradeNo) {
		return resp, nil
	}
	order, err := ec_model_order.COrder.GetById(ctx, bson.ObjectIdHex(event.MerchantTradeNo))
	if order.Id.IsZero() {
		// 商家改价后，传给微信的订单号是 outTradeId，需要用 outTradeId 来查询订单
		order, err = ec_model_order.COrder.GetByOutTradeId(ctx, bson.ObjectIdHex(event.MerchantTradeNo))
	}
	if errors.Is(err, qmgo.ErrNoSuchDocuments) {
		return resp, nil
	}
	if err != nil {
		return nil, err
	}
	// 因为微信只有在完成全部发货后才会给用户推送消息，所以只处理已发货状态的订单
	if order.Status != ec_model_order.ORDER_STATUS_SHIPPED {
		return resp, nil
	}
	return client.GetEcOrderServiceClient().CompleteOrder(ctx, &pb_ec_order.CompleteOrderRequest{
		Id:       order.Id.Hex(),
		Reason:   "confirmed from wechat",
		MemberId: order.MemberId.Hex(),
	})
}
