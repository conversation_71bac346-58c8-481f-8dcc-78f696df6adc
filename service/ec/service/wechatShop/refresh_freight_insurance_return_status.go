package wechatShop

import (
	"context"
	wechat_trade "mairpc/core/component/wechat/trade"
	"mairpc/core/extension/bson"
	"mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	pb_order "mairpc/proto/ec/order"
	ec_model_order "mairpc/service/ec/model/order"
)

func (WechatShopService) RefreshFreightInsuranceReturnStatus(ctx context.Context, req *request.DetailRequest) (*response.EmptyResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	refund, err := ec_model_order.COrderRefund.GetById(ctx, bson.ObjectIdHex(req.Id))
	if err != nil {
		return nil, err
	}
	refreshFreightInsuranceReturnStatus(ctx, *refund)
	return &response.EmptyResponse{}, nil
}

func refreshFreightInsuranceReturnStatus(ctx context.Context, refund ec_model_order.OrderRefund) {
	if !refund.HasFreightInsurance() || refund.Status != ec_model_order.ORDER_REFUND_STATUS_APPROVED {
		return
	}
	returnId := refund.GetFreightInsuranceReturnId()
	if returnId == "" {
		return
	}
	resp, err := wechat_trade.NewWechatTradeClient(refund.ChannelId, false).FreightInsurance.GetFreightInsuranceReturnDetail(ctx, returnId)
	if err != nil {
		return
	}
	// 未退货不处理
	if resp.Status == wechat_trade.FREIGHT_INSURANCE_RETURN_STATUS_EMPTY || resp.WaybillId == "" {
		return
	}
	// 如果是上门取件的情况，需要等到已揽件才更新退货状态
	if resp.Status == wechat_trade.FREIGHT_INSURANCE_RETURN_STATUS_PICK_UP && util.IndexOfArray(resp.OrderStatus, []int{1, 2, 3, 4, 6}) < 0 {
		return
	}
	// 到这里说明使用了微信退货组件，无论是预约上门取件还是自行寄回，都需要将退货物流同步到退款单上并标记为已退货
	client.GetEcOrderServiceClient().UpdateOrderRefund(ctx, &pb_order.UpdateOrderRefundRequest{
		Id:          refund.Id.Hex(),
		OperateType: "goodsReturned",
		MemberId:    refund.MemberId.Hex(),
		RefundLogistics: &pb_order.RefundLogisticsInfo{
			WaybillId:    resp.WaybillId,
			DeliveryName: resp.DeliveryName,
		},
	})
}
