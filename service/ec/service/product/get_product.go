package product

import (
	"mairpc/core/errors"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/core/validators"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	pb_ec_marketing "mairpc/proto/ec/marketing"
	ec_product "mairpc/proto/ec/product"
	pb_discount_campaign "mairpc/proto/eccampaign/discountCampaign"
	"mairpc/proto/product"
	ec_client "mairpc/service/ec/client"
	ec_model "mairpc/service/ec/model"
	ec_marketing "mairpc/service/ec/model/marketing"
	product_model "mairpc/service/ec/model/product"
	store_model "mairpc/service/ec/model/store"
	ec_store_product "mairpc/service/ec/model/storeProduct"
	"mairpc/service/ec/service"
	"mairpc/service/ec/share"
	"mairpc/service/share/util"
	"time"

	"github.com/spf13/cast"

	"mairpc/core/extension/bson"

	"golang.org/x/net/context"
)

func (ProductService) GetProduct(ctx context.Context, req *ec_product.GetProductRequest) (*ec_product.ProductResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	if req.ProductId == "" && req.Sku == "" && req.Number == "" && req.CouponId == "" && req.StoredValueCardId == "" {
		return nil, errors.NewInvalidArgumentError("productId")
	}

	var (
		ecProduct product_model.Product
		err       error
	)

	condition := genGetProductCondition(ctx, req)
	if req.Number != "" {
		productResp, err := client.GetProductServiceClient().GetProduct(ctx, &product.GetProductRequest{Number: req.Number})
		if err != nil {
			return nil, err
		}
		condition["productId"] = bson.ObjectIdHex(productResp.Id)
	}
	ecProduct, err = product_model.CProduct.GetByCondition(ctx, condition)

	if err != nil {
		return nil, errors.NewNotExistsError("ec.product")
	}

	resp, err := formatProductResponse(ctx, ecProduct, req.StoreId, req.MemberId, req.HasCoupon, req.ContainDeleted, req.IgnoreFields)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func genGetProductCondition(ctx context.Context, req *ec_product.GetProductRequest) bson.M {
	condition := product_model.Common.GenDefaultCondition(ctx)
	if req.ProductId != "" {
		condition["_id"] = util.ToMongoId(req.ProductId)
	}
	if req.ContainDeleted {
		delete(condition, "isDeleted")
	}
	if req.Sku != "" {
		condition["skus.sku"] = req.Sku
	}
	if req.CouponId != "" {
		condition["skus.couponId"] = bson.ObjectIdHex(req.CouponId)
	}
	if req.StoredValueCardId != "" {
		condition["skus.storedValueCardId"] = bson.ObjectIdHex(req.StoredValueCardId)
	}
	return condition
}

func formatProductResponse(ctx context.Context, ecProduct product_model.Product, storeId, memberId string, hasCoupon, containDeleted bool, ignoreFields []string) (*ec_product.ProductResponse, error) {
	resp := &ec_product.ProductResponse{}

	if storeId != "" {
		store, err := store_model.CStore.GetById(ctx, bson.ObjectIdHex(storeId))
		if err != nil {
			return nil, errors.NewNotExistsError("storeId")
		}

		ecProduct.ReplacePricesByLocation(store.Location.Province, store.Location.City, storeId, util.MongoIdsToStrs(store.DistributorIds))
	}

	req := &product.SearchProductRequest{
		Ids:            []string{ecProduct.ProductId.Hex()},
		ShowIntro:      true,
		ContainDeleted: containDeleted,
	}
	products, err := SearchProduct(ctx, req)

	if err != nil {
		return nil, err
	}
	if len(products.Items) == 0 {
		return nil, errors.NewNotExistsError("product")
	}
	resp.Product = products.Items[0]
	if util.StrInArray("campaigns", &ignoreFields) {
		ecProduct.Campaigns = []product_model.Campaign{}
	}
	sortProductCampaigns(&ecProduct)
	if util.StrInArray("userInfo", &ignoreFields) {
		var productSetting *product_model.ProductSetting
		if products.Items[0].Type == product_model.C_PRODUCT_TYPE_VIRTUAL {
			productSetting, _ = product_model.CProductSetting.GetByProductId(ctx, ecProduct.Id)
		}
		ds, _ := ec_client.SettingService.GetDeliverySetting(ctx, &request.EmptyRequest{})
		resp.Ec = formatEcProduct(ecProduct, products.Items[0], service.IsStockEnabled(ctx, ds), productSetting, ds)
	} else {
		resp.Ec = formatEcProductWithUserInfo(ctx, ecProduct, products.Items[0])
	}
	if hasCoupon {
		getCouponInfoForProducts(ctx, []*ec_product.ProductResponse{resp}, true, storeId)
	}
	var presentCampaignId bson.ObjectId
	var discountCampaignId string
	for _, campaign := range ecProduct.Campaigns {
		if campaign.StartAt.Before(time.Now()) && campaign.EndAt.After(time.Now()) && campaign.Type == ec_marketing.CAMPAIGN_TYPE_PRESENT {
			presentCampaignId = campaign.Id
		}
		if campaign.Type == CAMPAIGN_TYPE_DISCOUNT {
			discountCampaignId = campaign.Id.Hex()
		}
	}

	if storeId != "" {
		if discountCampaignId != "" {
			discountCampaign, _ := client.GetEccampaignDiscountCampaignServiceClient().GetDiscountCampaign(ctx, &pb_discount_campaign.GetDiscountCampaignRequest{Id: discountCampaignId})
			if discountCampaign != nil {
				// 限时折扣活动无库存了，将商品上的活动信息移除
				if !product_model.HaveDiscountCampaignStock(ecProduct.Id.Hex(), discountCampaign) {
					campaigns := []product_model.Campaign{}
					for _, c := range ecProduct.Campaigns {
						if c.Id.Hex() != discountCampaignId {
							campaigns = append(campaigns, c)
						}
					}
					ecProduct.Campaigns = campaigns
				}
			}
		}

		if presentCampaignId != "" {
			presentCampaign, err := ec_client.MarketingService.GetPresentCampaign(ctx, &pb_ec_marketing.GetPresentCampaignRequest{Id: presentCampaignId.Hex(), MemberId: memberId})
			if err != nil {
				log.Warn(ctx, "Failed to get present campaign", log.Fields{"errMsg": err.Error(), "presentCampaignId": presentCampaignId.Hex()})
			} else {
				if !presentCampaign.CanMemberJoin || !presentCampaign.HaveStock {
					campaigns := []product_model.Campaign{}
					for _, campaign := range ecProduct.Campaigns {
						if campaign.Id != presentCampaignId {
							campaigns = append(campaigns, campaign)
						}
					}
					ecProduct.Campaigns = campaigns
				} else {
					existSkus := []string{}
					for _, p := range presentCampaign.Rule.Products {
						for _, sku := range p.Skus {
							existSkus = append(existSkus, sku.Sku)
						}
					}

					skuStockMap := map[string]uint64{}
					for _, s := range presentCampaign.Rule.SkuStocks {
						skuStockMap[s.Sku] = s.Stock
					}

					for i, sku := range resp.Ec.Skus {
						if !core_util.StrInArray(sku.Sku, &existSkus) {
							continue
						}
						// 未设置库存或者库存为 0 是不返回 campaignPrice
						if stock, ok := skuStockMap[sku.Sku]; !ok || ok && stock == 0 {
							continue
						}
						campaignPrice := product_model.CalcPresentCampaignPrice(presentCampaign, sku.Price)
						if campaignPrice >= 0 {
							if resp.Ec.Skus[i].Extra == nil {
								resp.Ec.Skus[i].Extra = make(map[string]string)
							}
							resp.Ec.Skus[i].Extra["campaignPrice"] = cast.ToString(campaignPrice)
						}
					}
				}
			}
		}
		ds, _ := ec_client.SettingService.GetDeliverySetting(ctx, &request.EmptyRequest{})
		if ds != nil && ds.Logistics.Enabled && ds.Logistics.ShipSetting.Operator == "staff" {
			storeProduct, _ := ec_store_product.CStoreProduct.GetByProductIdAndStoreId(ctx, ecProduct.ProductId, bson.ObjectIdHex(storeId))
			resp.Ec.StoreShelveStatus = storeProduct.Status
			for i, sku := range resp.Ec.Skus {
				for _, s := range storeProduct.Skus {
					if sku.Sku != s.Sku {
						continue
					}
					resp.Ec.Skus[i].StoreShelveStatus = s.Status
					break
				}
			}
			// 脉盟小店租户下小店商品 storeProduct 对应 product 表的 fields 字段为空，拷贝该字段可以复用代码逻辑
			if storeProduct.Id.Valid() && ec_model.IsMaimengRetail(ctx) {
				productFields := []*product.Field{}
				copier.Instance(nil).From(storeProduct.Fields).CopyTo(&productFields)
				resp.Product.Fields = productFields
			}
		}
	}

	err = ec_model.FormatSkuStockForConsignment(ctx, []*ec_product.ProductResponse{resp})
	if err != nil {
		return nil, err
	}
	if resp.Ec.Source == "" {
		resp.Ec.Source = "ec"
	}

	if len(resp.Ec.Campaigns) > 0 {
		// 格式化 product campaigns display 和 extra
		productCampaigns := make(map[string][]*ec_product.Campaign)
		productCampaigns[resp.Ec.Id] = resp.Ec.Campaigns
		productCampaigns = product_model.FormatCampaigns(ctx, storeId, memberId, productCampaigns, []product_model.Product{ecProduct})
		resp.Ec.Campaigns = productCampaigns[resp.Ec.Id]
	}

	if memberId != "" {
		member, _ := share.GetMemberById(ctx, util.ToMongoId(memberId), nil)
		if member != nil && member.BlockedStatus == 2 {
			// 黑名单用户商品详情不显示活动信息
			resp.Ec.Campaigns = []*ec_product.Campaign{}
		}
		productResp := []*ec_product.ProductResponse{resp}
		err := formatMemberDiscountPrice(ctx, memberId, productResp)
		if err != nil {
			return nil, err
		}
		resp = productResp[0]
	}

	return resp, nil
}

func getCouponInfoForProducts(ctx context.Context, products []*ec_product.ProductResponse, showAll bool, storeId string) {
	productIds := []string{}
	productMap := map[string]*ec_product.ProductResponse{}
	for _, product := range products {
		productIds = append(productIds, product.Ec.ProductId)
		productMap[product.Ec.ProductId] = product
	}

	hasCouponIds, _ := checkCouponInfo(ctx, productIds, storeId, showAll)
	for _, id := range hasCouponIds {
		if _, ok := productMap[id]; ok {
			productMap[id].Ec.HasCoupon = true
		}
	}
}
