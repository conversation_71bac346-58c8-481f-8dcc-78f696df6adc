package staff

import (
	"context"
	"fmt"
	core_component "mairpc/core/component"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/opapp/taobao/backend/util"
	pb_account "mairpc/proto/account"
	pb_client "mairpc/proto/client"
	pb_common_request "mairpc/proto/common/request"
	pb_ec_groupchat "mairpc/proto/ec/groupchat"
	pb_ec_staff "mairpc/proto/ec/staff"
	pb_ec_store "mairpc/proto/ec/store"
	pb_member "mairpc/proto/member"
	model_ec_groupchat "mairpc/service/ec/model/groupchat"
	model_ec_store "mairpc/service/ec/model/store"
	ec_share "mairpc/service/ec/service"
	share_component "mairpc/service/share/component"
	share_util "mairpc/service/share/util"
	"sort"
	"sync"
	"time"
)

const (
	TYPE_USER          = "user"
	TYPE_EXTERNAL_USER = "external_user"
	TYPE_ROOM          = "room"

	SEARCH_TYPE_NAME     = "name"
	SEARCH_TYPE_PHONE    = "phone"
	SEARCH_TYPE_STAFF_ID = "staffId"
	SEARCH_TYPE_STAFF_NO = "staffNo"
)

func (StaffService) ListChatMessageHistoryContacts(ctx context.Context, req *pb_ec_staff.ListChatMessageHistoryContactsRequest) (*pb_ec_staff.ListChatMessageHistoryContactsResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}
	channel, err := ec_share.GetWechatworkChannel(ctx, "")
	if err != nil {
		return nil, err
	}
	info := &ListChatMessageHistoryContactsInfo{channel: channel}
	if req.ListCondition == nil {
		req.ListCondition = &pb_common_request.ListCondition{
			Page:    1,
			PerPage: 20,
		}
	}
	if req.Type == TYPE_ROOM {
		resp := info.buildRoomChatMessageHistoryContacts(ctx, req)
		return resp, nil
	}
	request, hasResult, err := info.buildListChatMessageHistoryContactsRequest(ctx, req)
	if err != nil {
		return nil, err
	}
	if !hasResult || request == nil {
		return &pb_ec_staff.ListChatMessageHistoryContactsResponse{}, nil
	}
	response, err := share_component.WeConnect.ListChatMessageHistoryPreviews(ctx, channel.CorpId, *request)
	if err != nil {
		return nil, err
	}
	return info.formatChatMessageHistoryContacts(ctx, req, request, response)
}

type GroupChatWrapper struct {
	Id              bson.ObjectId
	ChatId          string
	Owner           string
	Name            string
	LastMessageTime int64
	CreatedAt       int64
}

type ListChatMessageHistoryContactsInfo struct {
	channel      *pb_account.ChannelDetailResponse
	staff        *pb_ec_store.StaffDetail
	staffs       []*pb_ec_store.BriefStaffDetail
	staffMembers []*pb_ec_staff.BriefMemberDetail
	groupChats   []*pb_ec_groupchat.Groupchat
	member       *pb_member.MemberDetailResponse
	wechatNo     string
}

func (info *ListChatMessageHistoryContactsInfo) buildListChatMessageHistoryContactsRequest(ctx context.Context, req *pb_ec_staff.ListChatMessageHistoryContactsRequest) (*share_component.ListChatMessageHistoryPreviewsRequest, bool, error) {
	request := &share_component.ListChatMessageHistoryPreviewsRequest{
		IsAllUser: false,
		PageNum:   int(req.ListCondition.Page),
		PageSize:  int(req.ListCondition.PerPage),
	}
	switch req.Type {
	case TYPE_USER:
		staff, err := ec_share.GetStaff(ctx, &pb_ec_store.StaffDetailRequest{StaffId: req.SearchId})
		if err != nil {
			return nil, false, err
		}
		info.staff = staff
		request.MainId = staff.StaffNo
		switch req.ContactType {
		case TYPE_USER:
			request.IsAllUser = true
			if req.SearchKey != "" {
				tempReq := &pb_ec_store.ListStaffsRequest{
					ListCondition: &pb_common_request.ListCondition{
						Page:    1,
						PerPage: 100,
						OrderBy: []string{"-createdAt"},
					},
				}
				if bson.IsObjectIdHex(req.SearchKey) {
					tempReq.StaffIds = []string{req.SearchKey}
				} else {
					tempReq.SearchKey = req.SearchKey
				}
				tempResp, err := ec_share.ListBriefStaffs(ctx, tempReq, false)
				if err != nil {
					return nil, false, err
				}
				if len(tempResp.Items) == 0 {
					return nil, false, nil
				}
				info.staffs = tempResp.Items
				request.RelatedIds = core_util.ExtractArrayStringField("StaffNo", tempResp.Items)
			}
		case TYPE_EXTERNAL_USER:
			if req.SearchKey != "" {
				tempResp, err := pb_client.GetEcStaffServiceClient().ListStaffMembers(ctx, &pb_ec_staff.ListStaffMembersRequest{
					StaffId:         staff.Id,
					MemberSearchKey: req.SearchKey,
					ListCondition: &pb_common_request.ListCondition{
						Page:    1,
						PerPage: 100,
						OrderBy: []string{"-createdAt"},
					},
				})
				if err != nil {
					return nil, false, err
				}
				if len(tempResp.Items) == 0 {
					return nil, false, nil
				}
				info.staffMembers = tempResp.Items
				request.RelatedIds = core_util.ExtractArrayStringField("OpenId", tempResp.Items)
			}
		case TYPE_ROOM:
			// 获取当前用户所在的全部群
			selector := bson.M{
				"accountId": share_util.GetAccountIdAsObjectId(ctx),
				"userId":    staff.StaffNo,
			}
			chatIds := []string{}
			err := model_ec_groupchat.CGroupchatMember.Distinct(ctx, selector, "chatId", &chatIds)
			if err != nil {
				return nil, false, err
			}
			if len(chatIds) == 0 {
				return nil, true, nil
			}
			tempReq := &pb_ec_groupchat.ListGroupchatsRequest{
				ChatIds:          chatIds,
				Name:             req.SearchKey,
				Owners:           req.RoomChatStaffNos,
				IsContainDeleted: true,
				ListCondition:    &pb_common_request.ListCondition{Page: 1, PerPage: 999},
			}
			tempResp, err := pb_client.GetEcGroupchatServiceClient().ListGroupchats(ctx, tempReq)
			if err != nil {
				return nil, false, err
			}
			if len(tempResp.Items) == 0 {
				return nil, true, nil
			}
			info.groupChats = tempResp.Items
			request.RoomIds = core_util.ExtractArrayStringField("ChatId", tempResp.Items)
		default:
			return nil, false, fmt.Errorf("invalid contactType: %s", req.ContactType)
		}
	case TYPE_EXTERNAL_USER:
		member, err := ec_share.GetMember(ctx, req.SearchId, []string{"SocialMember"})
		if err != nil {
			return nil, false, err
		}
		info.member = member
		openId := ""
		for _, social := range append(member.Socials, member.OriginFrom) {
			if social.Channel == info.channel.ChannelId {
				openId = social.OpenId
				break
			}
		}
		if openId == "" {
			return nil, false, fmt.Errorf("social channel %s not found", info.channel.ChannelId)
		}
		request.MainId = openId
		switch req.ContactType {
		case TYPE_USER:
			if len(req.DistributorIds) == 0 && req.SearchKey == "" {
				break
			}
			tempReq := &pb_ec_store.ListStaffsRequest{
				ListCondition: &pb_common_request.ListCondition{
					Page:    1,
					PerPage: 100,
					OrderBy: []string{"-createdAt"},
				},
			}
			if len(req.DistributorIds) > 0 {
				tempReq.DistributorIds = req.DistributorIds
			}
			if bson.IsObjectIdHex(req.SearchKey) {
				tempReq.StaffIds = []string{req.SearchKey}
			} else {
				tempReq.SearchKey = req.SearchKey
			}
			tempResp, err := ec_share.ListBriefStaffs(ctx, tempReq, false)
			if err != nil {
				return nil, false, err
			}
			if len(tempResp.Items) == 0 {
				return nil, false, nil
			}
			info.staffs = tempResp.Items
			request.RelatedIds = core_util.ExtractArrayStringField("StaffNo", tempResp.Items)
		case TYPE_ROOM:
			// 获取当前用户所在的全部群
			selector := bson.M{
				"accountId": share_util.GetAccountIdAsObjectId(ctx),
				"userId":    openId,
			}
			chatIds := []string{}
			err := model_ec_groupchat.CGroupchatMember.Distinct(ctx, selector, "chatId", &chatIds)
			if err != nil {
				return nil, false, err
			}
			if len(chatIds) == 0 {
				return nil, true, nil
			}
			tempReq := &pb_ec_groupchat.ListGroupchatsRequest{
				ChatIds:          chatIds,
				Name:             req.SearchKey,
				Owners:           req.RoomChatStaffNos,
				IsContainDeleted: true,
				ListCondition:    &pb_common_request.ListCondition{Page: 1, PerPage: 999},
			}
			tempResp, err := pb_client.GetEcGroupchatServiceClient().ListGroupchats(ctx, tempReq)
			if err != nil {
				return nil, false, err
			}
			if len(tempResp.Items) == 0 {
				return nil, true, nil
			}
			info.groupChats = tempResp.Items
			request.RoomIds = core_util.ExtractArrayStringField("ChatId", tempResp.Items)
		default:
			return nil, false, fmt.Errorf("invalid contactType: %s", req.ContactType)
		}
	case TYPE_ROOM:
		return nil, true, nil
	}
	return request, true, nil
}

func (info *ListChatMessageHistoryContactsInfo) formatChatMessageHistoryContacts(
	ctx context.Context,
	req *pb_ec_staff.ListChatMessageHistoryContactsRequest,
	request *share_component.ListChatMessageHistoryPreviewsRequest,
	response *share_component.ListChatMessageHistoryPreviewsResponse,
) (*pb_ec_staff.ListChatMessageHistoryContactsResponse, error) {
	var (
		resp           = &pb_ec_staff.ListChatMessageHistoryContactsResponse{Total: response.TotalAmount}
		staffMap       = make(map[string]*pb_ec_store.BriefStaffDetail)
		staffMemberMap = make(map[string]*pb_ec_staff.BriefMemberDetail)
		memberMap      = make(map[string]*pb_member.MemberDetailResponse)
		groupChatMap   = make(map[string]*pb_ec_groupchat.Groupchat)
		ids            = make([]string, len(response.Results))
	)
	for i, result := range response.Results {
		ids[i] = getOtherChatMessageHistoryPreviewId(result, request.MainId)
	}
	if info.staffs != nil && len(info.staffs) > 0 {
		staffMap = core_util.MakeMapperV2("StaffNo", "", info.staffs)
	} else if req.ContactType == TYPE_USER && len(ids) > 0 {
		tempResp, err := ec_share.ListBriefStaffs(ctx, &pb_ec_store.ListStaffsRequest{StaffNos: ids}, true)
		if err != nil {
			return nil, err
		}
		staffMap = core_util.MakeMapperV2("StaffNo", "", tempResp.Items)
	}
	if info.staffMembers != nil && len(info.staffMembers) > 0 {
		staffMemberMap = core_util.MakeMapperV2("OpenId", "", info.staffMembers)
	} else if req.ContactType == TYPE_EXTERNAL_USER {
		tempResp, err := pb_client.GetMemberServiceClient().GetMembers(ctx, &pb_member.MemberDetailListRequest{OpenIds: ids})
		if err != nil {
			return nil, err
		}
		for i, member := range tempResp.Members {
			for _, social := range append(member.Socials, member.OriginFrom) {
				if social.Channel == info.channel.ChannelId {
					memberMap[social.OpenId] = tempResp.Members[i]
					break
				}
			}
		}
	}
	if info.groupChats != nil && len(info.groupChats) > 0 {
		groupChatMap = core_util.MakeMapperV2("ChatId", "", info.groupChats)
	} else if req.ContactType == TYPE_ROOM {
		tempResp, err := pb_client.GetEcGroupchatServiceClient().ListGroupchats(ctx, &pb_ec_groupchat.ListGroupchatsRequest{
			ChatIds:          ids,
			IsContainDeleted: true,
			ListCondition:    &pb_common_request.ListCondition{Page: 1, PerPage: 999},
		})
		if err != nil {
			return nil, err
		}
		groupChatMap = core_util.MakeMapperV2("ChatId", "", tempResp.Items)
	}
	for i, result := range response.Results {
		contact := &pb_ec_staff.ChatMessageHistoryContact{
			WechatNo:           ids[i],
			LastMessageType:    result.MessageType,
			LastMessageTime:    time.UnixMilli(result.MessageTime).Format(core_util.RFC3339Mili),
			LastMessageContent: getChatMessageHistoryPreviewContent(result),
		}
		switch req.ContactType {
		case TYPE_USER:
			if staffDetail, ok := staffMap[contact.WechatNo]; ok {
				contact.Id = staffDetail.Id
				contact.Name = staffDetail.Name
				contact.Avatar = staffDetail.Avatar
				contact.StaffStatus = staffDetail.Status
			}
		case TYPE_EXTERNAL_USER:
			if memberDetail, ok := staffMemberMap[contact.WechatNo]; ok {
				contact.Id = memberDetail.Id
				contact.Name = memberDetail.Name
				contact.Avatar = memberDetail.Avatar
			}
			if memberDetail, ok := memberMap[contact.WechatNo]; ok {
				contact.Id = memberDetail.Id
				contact.Name = memberDetail.Name
				contact.Avatar = memberDetail.Avatar
			}
		case TYPE_ROOM:
			if groupChatDetail, ok := groupChatMap[contact.WechatNo]; ok {
				contact.Id = groupChatDetail.Id
				contact.Name = groupChatDetail.Name
				contact.GroupchatOwner = groupChatDetail.Owner
				contact.GroupChatName = groupChatDetail.Name
			}
		}
		resp.Items = append(resp.Items, contact)
	}
	return resp, nil
}

func (info *ListChatMessageHistoryContactsInfo) buildRoomChatMessageHistoryContacts(ctx context.Context, req *pb_ec_staff.ListChatMessageHistoryContactsRequest) *pb_ec_staff.ListChatMessageHistoryContactsResponse {
	var (
		wg                      = &sync.WaitGroup{}
		groupChatMessageTimeMap = map[string]share_component.ChatMessageHistoryPreview{}
		staffMap                = map[string]model_ec_store.Staff{}
		groupChatWrappers       = []GroupChatWrapper{}
	)

	// 从 weconnect 查出所有群聊的最新聊天时间
	wg.Add(1)
	core_component.GO(ctx, func(ctx context.Context) {
		defer wg.Done()
		tempReq := &share_component.ListChatMessageHistoryPreviewsRequest{
			IsAllRoom: true,
			PageNum:   1,
			PageSize:  1000,
		}
		for {
			tempResp, err := share_component.WeConnect.ListChatMessageHistoryPreviews(ctx, info.channel.CorpId, *tempReq)
			if err != nil {
				return
			}
			if len(tempResp.Results) == 0 {
				return
			}
			for _, result := range tempResp.Results {
				groupChatMessageTimeMap[result.RoomId] = result
			}
			tempReq.PageNum++
		}
	})

	// 从数据库查出全部群
	wg.Add(1)
	core_component.GO(core_util.CtxWithReadSecondaryPreferred(ctx), func(newCtx context.Context) {
		defer wg.Done()
		selector := bson.M{
			"accountId":       share_util.GetAccountIdAsObjectId(newCtx),
			"hasMessageAudit": true,
		}
		if req.SearchKey != "" {
			selector["name"] = share_util.GetFuzzySearchStrRegex(req.SearchKey)
		}
		if len(req.RoomChatGroupIds) > 0 {
			selector["groupId"] = bson.M{"$in": util.ToMongoIds(req.RoomChatGroupIds)}
		}
		if len(req.RoomChatTags) > 0 {
			selector["tags"] = bson.M{"$in": req.RoomChatTags}
		}
		if len(req.RoomChatStaffNos) > 0 {
			selector["owner"] = bson.M{"$in": req.RoomChatStaffNos}
		}
		field := bson.M{
			"_id":       1,
			"name":      1,
			"owner":     1,
			"createdAt": 1,
			"chatId":    1,
		}
		var owners []string
		for {
			groupChats, err := model_ec_groupchat.CGroupchat.FindAllWithFields(newCtx, selector, field, []string{"createdAt"}, 1000)
			if err != nil {
				return
			}
			if len(groupChats) == 0 {
				break
			}
			for _, chat := range groupChats {
				groupChatWrappers = append(groupChatWrappers, GroupChatWrapper{
					Id:              chat.Id,
					ChatId:          chat.ChatId,
					Owner:           chat.Owner,
					Name:            chat.Name,
					LastMessageTime: 0,
					CreatedAt:       chat.CreatedAt.UnixMilli(),
				})
				owners = append(owners, chat.Owner)
			}
			selector["_id"] = bson.M{"$gt": groupChats[len(groupChats)-1].Id.Hex()}
		}
		if len(owners) > 0 {
			staffs, err := model_ec_store.CStaff.GetByStaffNos(ctx, owners)
			if err != nil {
				return
			}
			for i, staff := range staffs {
				staffMap[staff.StaffNo] = staffs[i]
			}
		}
	})

	wg.Wait()
	for i, wrapper := range groupChatWrappers {
		if preview, ok := groupChatMessageTimeMap[wrapper.ChatId]; ok {
			groupChatWrappers[i].LastMessageTime = preview.MessageTime
		}
	}
	sort.Slice(groupChatWrappers, func(i, j int) bool {
		if groupChatWrappers[i].LastMessageTime > groupChatWrappers[j].LastMessageTime {
			return true
		}
		return groupChatWrappers[i].CreatedAt > groupChatWrappers[j].CreatedAt
	})

	resp := &pb_ec_staff.ListChatMessageHistoryContactsResponse{Total: int64(len(groupChatWrappers))}
	page, perPage := int(req.ListCondition.Page), int(req.ListCondition.PerPage)
	for i, j := (page-1)*perPage, 0; i < page*perPage && i < len(groupChatWrappers); i++ {
		wrapper := groupChatWrappers[i]
		resp.Items = append(resp.Items, &pb_ec_staff.ChatMessageHistoryContact{
			Id:            wrapper.Id.Hex(),
			WechatNo:      wrapper.ChatId,
			Name:          wrapper.Name,
			GroupChatName: wrapper.Name,
		})
		if preview, ok := groupChatMessageTimeMap[wrapper.ChatId]; ok {
			resp.Items[j].LastMessageType = preview.MessageType
			resp.Items[j].LastMessageTime = time.UnixMilli(wrapper.LastMessageTime).Format(core_util.RFC3339Mili)
			resp.Items[j].LastMessageContent = getChatMessageHistoryPreviewContent(preview)
		}
		if staff, ok := staffMap[wrapper.Owner]; ok {
			resp.Items[j].GroupchatOwner = &pb_ec_groupchat.GroupchatOwner{
				Id:      staff.Id.Hex(),
				Name:    staff.Name,
				StaffNo: staff.StaffNo,
			}
		}
		j++
	}
	return resp
}

func getChatMessageHistoryPreviewContent(preview share_component.ChatMessageHistoryPreview) string {
	switch preview.MessageType {
	case "TEXT":
		return preview.LastData.Content
	case "LINK":
		return preview.LastData.LinkUrl
	case "FILE":
		return preview.LastData.FileName
	default:
		return preview.LastData.Content
	}
}

func getOtherChatMessageHistoryPreviewId(preview share_component.ChatMessageHistoryPreview, id string) string {
	if preview.RoomId != "" {
		return preview.RoomId
	}
	if preview.From != id {
		return preview.From
	}
	return preview.To
}
