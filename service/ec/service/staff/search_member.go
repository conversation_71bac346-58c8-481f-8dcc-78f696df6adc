package staff

import (
	"mairpc/core/util/copier"
	"strings"
	"sync"
	"time"

	"mairpc/core/component"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	"mairpc/core/validators"
	"mairpc/proto/common/origin"
	"mairpc/proto/common/request"
	"mairpc/proto/common/types"
	ec_staff "mairpc/proto/ec/staff"
	ec_store "mairpc/proto/ec/store"
	pb_member "mairpc/proto/member"
	ec_client "mairpc/service/ec/client"
	staff_model "mairpc/service/ec/model/staff"
	store_model "mairpc/service/ec/model/store"
	"mairpc/service/ec/service"
	ec_service "mairpc/service/ec/service"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"

	"github.com/panjf2000/ants/v2"
	"github.com/spf13/viper"

	"golang.org/x/net/context"
)

const (
	MEMBER_STATUS_ALL      = "all"
	MEMBER_STATUS_LOSE     = "lose"    // 状态为已流失
	MEMBER_STATUS_NOT_LOSE = "notLose" // 状态为未流失
	batchSize              = 50        // 单次最多查询的导购数量
)

func (StaffService) SearchMember(ctx context.Context, req *ec_staff.SearchMemberRequest) (*ec_staff.SearchMemberResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	childStoreIds := []string{}
	// 只能获取权限范围内门店下属导购的绑定客户
	if req.Distributor != nil && len(req.Distributor.Ids) > 0 {
		var err error
		childStoreIds, err = getChildStoreIds(ctx, req.Distributor.Ids)
		if err != nil {
			return &ec_staff.SearchMemberResponse{}, nil
		}
		if len(req.StoreIds) == 0 {
			req.StoreIds = childStoreIds
		}
	}

	staffIds, _ := FilterStaffIdsAndStaffNos(ctx, req.StaffSearchKey, childStoreIds, []string{})
	if len(staffIds) == 0 && req.StaffSearchKey != "" {
		return &ec_staff.SearchMemberResponse{}, nil
	}

	staffMembers, hasResult := getStaffMembersByStore(ctx, staffIds, req.StoreIds, req.RelationStatus, childStoreIds, false, []string{})
	if !hasResult {
		return &ec_staff.SearchMemberResponse{}, nil
	}

	channelDetail, err := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err != nil {
		return nil, err
	}
	searchReq := &pb_member.SearchMemberRequest{
		SearchKeyWord: req.MemberSearchKey,
		Tags:          req.Tags,
		Socials:       []*origin.SocialSearchInfo{},
		ListCondition: req.ListCondition,
	}
	searchSocial := &origin.SocialSearchInfo{
		Origin: &types.StringValue{
			Value: constant.WECONTACT,
		},
		Channel: &types.StringValue{
			Value: channelDetail.ChannelId,
		},
	}
	if req.Status == MEMBER_STATUS_LOSE {
		searchSocial.Subscribed = &types.BoolValue{
			Value: false,
		}
	}
	if req.Status == MEMBER_STATUS_NOT_LOSE {
		searchSocial.Subscribed = &types.BoolValue{
			Value: true,
		}
	}
	searchReq.Socials = append(searchReq.Socials, searchSocial)
	if len(staffMembers) > 0 {
		memberIds := []bson.ObjectId{}
		for _, s := range staffMembers {
			memberIds = append(memberIds, s.MemberId)
		}
		searchReq.Ids = util.MongoIdsToStrs(memberIds)
	}

	if req.DateRange != nil {
		for _, social := range searchReq.Socials {
			social.FirstSubscribeTime = req.DateRange
		}
	}

	resp, err := ec_service.SearchMember(ctx, searchReq)
	if err != nil {
		return nil, err
	}

	return &ec_staff.SearchMemberResponse{
		Total: resp.TotalCount,
		Items: formatMembers(ctx, resp.Members, staffMembers, channelDetail.ChannelId, staffIds, req.DistributorIds, req.StoreIds, false, false),
	}, nil
}

func FilterStaffIdsAndStaffNos(ctx context.Context, searchKey string, storeIds, reqStaffIds []string) ([]string, []string) {
	if searchKey == "" && len(storeIds) == 0 && len(reqStaffIds) == 0 {
		return []string{}, []string{}
	}
	var (
		staffIds []string
		staffNos []string
		pageSize = 1000
		onlyOnce bool
	)
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	if len(reqStaffIds) > 0 {
		selector["_id"] = bson.M{"$in": util.ToMongoIds(reqStaffIds)}
	}
	if searchKey != "" {
		selector["$or"] = []bson.M{
			{
				"name": util.GetFuzzySearchStrRegex(searchKey),
			},
			{
				"staffNo": searchKey,
			},
			{
				"phone": searchKey,
			},
		}
		env := viper.GetString("env")
		if strings.Contains(env, "biostime") {
			pageSize = 20
			onlyOnce = true
		}
	}
	if len(storeIds) > 0 {
		selector["accessibleDistributorIds"] = bson.M{"$in": util.ToMongoIds(storeIds)}
	}
	for {
		staffs, err := store_model.CStaff.GetAllByConditionAndLimitWithFields(ctx, util.FormatConditionContainedOr(selector), bson.M{
			"_id":     1,
			"staffNo": 1,
		}, []string{"_id"}, pageSize)
		if err != nil {
			return []string{}, []string{}
		}
		for _, staff := range staffs {
			staffIds = append(staffIds, staff.Id.Hex())
			staffNos = append(staffNos, staff.StaffNo)
		}
		if len(staffs) == 0 || len(staffs) < pageSize || onlyOnce {
			break
		}
		newIdSelector := bson.M{"$gt": staffs[len(staffs)-1].Id}
		if cond, ok := selector["_id"].(bson.M); ok {
			if value, ok := cond["$in"]; ok {
				newIdSelector["$in"] = value
			}
		}
		selector["_id"] = newIdSelector
	}
	return staffIds, staffNos
}

// 通过搜索条件以及 staffMember 表中的关联关系，确定 memberId 范围
func getStaffMembersByStore(ctx context.Context, staffIds, storeIds, relationStatus, permissionStoreIds []string, isSearchByStatus bool, memberIds []string) ([]staff_model.StaffMember, bool) {
	if len(staffIds) == 0 && len(storeIds) == 0 {
		return []staff_model.StaffMember{}, true
	}

	selector := bson.M{
		"accountId":      util.GetAccountIdAsObjectId(ctx),
		"relationStatus": staff_model.STAFF_MEMBER_RELATION_BOUND,
	}

	// 不存在所属导购的条件时，直接按照接口参数筛选导购状态，存在所属导购的条件时，默认只筛选绑定状态的数据
	if len(relationStatus) > 0 && len(staffIds) == 0 && len(permissionStoreIds) == 0 || isSearchByStatus {
		selector["relationStatus"] = bson.M{"$in": relationStatus}
	}

	if isSearchByStatus && len(memberIds) > 0 {
		selector["memberId"] = bson.M{"$in": util.ToMongoIds(memberIds)}
	}

	if len(staffIds) > 0 {
		selector["staffId"] = bson.M{"$in": util.ToMongoIds(staffIds)}
	}

	// 此处限制关联门店也需要在权限可见门店范围内
	if len(storeIds) > 0 && len(permissionStoreIds) > 0 {
		selectStoreIds := util.StrArrayDuplicate(storeIds, permissionStoreIds)
		selector["storeId"] = bson.M{"$in": util.ToMongoIds(selectStoreIds)}
	} else if len(storeIds) > 0 || len(permissionStoreIds) > 0 {
		selectStoreIds := append(storeIds, permissionStoreIds...)
		selector["storeId"] = bson.M{"$in": util.ToMongoIds(selectStoreIds)}
	}

	env := viper.GetString("env")
	if strings.Contains(env, "biostime") && len(staffIds) > batchSize {
		return findStaffMembersInParallel(ctx, selector, staffIds)
	}
	staffMembers, err := staff_model.CStaffMember.FindAll(ctx, selector, []string{})
	if err != nil || len(staffMembers) == 0 {
		return nil, false
	}

	return staffMembers, true
}

// staffIds 不为空会只返回所属导购 Id 在 staffIds 中的数据
func formatMembers(ctx context.Context, members []*pb_member.MemberDetailResponse, staffMembers []staff_model.StaffMember, channelId string, staffIds, distributorIds, storeIds []string, isTransfer, searchByAdded bool) []*ec_staff.MemberDetail {
	if len(members) == 0 {
		return nil
	}
	// 获取 staffMember，以便能在返回值中加上 member、staff 与 store 的关联关系
	staffMembers = ensureStaffMembers(ctx, members, staffMembers, channelId, staffIds, distributorIds, searchByAdded)
	staffMemberMap := genStaffMemberMap(staffMembers)
	staffMap := map[string]store_model.Staff{}
	staffMemberRelations := getStaffMemberRelations(ctx)
	// 获取 store、staff 以便能在返回值中加上门店名与店员名之类的展示信息
	storeIdMap, memberStoreMap := getStoreByStaffMembers(ctx, staffMembers)
	memberStaffMap := getStaffByStaffMembers(ctx, staffMembers)

	// 格式化返回值
	result := []*ec_staff.MemberDetail{}
	for _, member := range members {
		openId, unionId := ec_service.GetWeContactOpenIdAndUnionId(member, channelId)
		m := &ec_staff.MemberDetail{
			Id:         member.Id,
			Name:       member.Name,
			Phone:      member.Phone,
			Avatar:     member.Avatar,
			OpenId:     openId,
			UnionId:    unionId,
			Staff:      &ec_staff.Staff{},
			Store:      &ec_staff.Store{},
			Stores:     getLinkedStores(ctx, member, &storeIdMap),
			Tags:       member.Tags,
			Level:      uint64(member.Level),
			CardNumber: member.CardNumber,
		}

		staff, ok := memberStaffMap[member.Id]
		if ok {
			m.Staff.Id = staff.Id
			m.Staff.Name = staff.Name
			m.Staff.StaffNo = staff.StaffNo
			m.Staff.Phone = staff.Phone
			m.Staff.StoreIds = staff.StoreIds
		}
		relationStaffs := []*ec_staff.RelationStaff{}
		for _, property := range member.Properties {
			if util.StrInArray(property.Name, &staffMemberRelations) {
				relationStaffs = append(relationStaffs, &ec_staff.RelationStaff{
					Name:            property.Name,
					RelationStaffId: property.GetValueString().Value,
				})
			}
		}
		for _, relationStaff := range relationStaffs {
			value, ok := staffMap[relationStaff.RelationStaffId]
			if ok {
				relationStaff.RelationStaffName = value.Name
				relationStaff.RelationStaffNo = value.StaffNo
			} else {
				if relationStaff.RelationStaffId == "" || !bson.IsObjectIdHex(relationStaff.RelationStaffId) {
					continue
				}
				staff, err := store_model.CStaff.GetById(ctx, util.ToMongoId(relationStaff.RelationStaffId))
				if err != nil {
					continue
				}
				relationStaff.RelationStaffName = staff.Name
				relationStaff.RelationStaffNo = staff.StaffNo
				staffMap[relationStaff.RelationStaffId] = staff
			}
		}
		m.RelationStaffs = relationStaffs
		stores := memberStoreMap[member.Id]
		if len(stores) > 0 {
			m.Store.Id = stores[0].Id
			m.Store.Name = stores[0].Name
			if len(storeIds) > 0 {
				for _, store := range stores {
					if util.StrInArray(store.Id, &storeIds) {
						m.Store.Id = store.Id
						m.Store.Name = store.Name
						break
					}
				}
			}
		}

		staffMember, ok := staffMemberMap[member.Id]
		if ok {
			m.Staff.RelatedAt = staffMember.CreatedAt.Format(time.RFC3339)
			m.Staff.BoundAt = staffMember.BoundAt.Format(time.RFC3339)
			m.Staff.AddedAt = staffMember.AddedAt.Format(time.RFC3339)
			m.Staff.RelationStatus = staffMember.RelationStatus
			m.Staff.AddWay = staff_model.FormatAddWay(staffMember.AddWay)
		}

		origins := append(member.Socials, member.OriginFrom)
		m.CreatedAt = ""
		for _, o := range origins {
			if o.Origin == constant.WECONTACT && o.Channel == channelId && o.FirstSubscribeTime > 0 {
				m.CreatedAt = time.Unix(o.FirstSubscribeTime, 0).Format(time.RFC3339)
			}
			if o.Origin == constant.WECONTACT && o.Channel == channelId && o.UnsubscribeTime > 0 {
				m.UnsubscribeTime = time.Unix(o.UnsubscribeTime, 0).Format(time.RFC3339)
			}
		}
		for _, social := range append([]*origin.OriginInfo{member.OriginFrom}, member.Socials...) {
			if social.Origin == constant.WECONTACT && social.Channel == channelId {
				m.Subscribed = social.Subscribed
				staffUserIds := strings.Split(social.Extra, ",")
				// 客户只有一个导购，无法分配导购
				if len(staffUserIds) <= 1 {
					m.OneContact = true
					break
				}
				condition := bson.M{
					"accountId":      util.GetAccountIdAsObjectId(ctx),
					"memberId":       util.ToMongoId(member.Id),
					"relationStatus": staff_model.STAFF_MEMBER_RELATION_ADDED,
				}
				staffMember, _ := staff_model.CStaffMember.FindOne(ctx, condition)
				if !staffMember.Id.Valid() {
					m.OneContact = true
					break
				}
				// 客户加了多个导购，并且当前登录用户有权限看到这些导购，则可以分配导购
				// storeIds 是当前登录用户权限 distributorIds 下子门店
				if len(distributorIds) > 0 && len(storeIds) > 0 {
					staffCount := getMemberStaffCount(ctx, staffUserIds, storeIds)
					if staffCount <= 1 {
						m.OneContact = true
						break
					}
				}
				// 合并客户会导致客户有两个外部联系人渠道，这里只需要匹配到第一个渠道就行了
				break
			}
		}
		if isTransfer && staff != nil {
			formatTransferMember(ctx, member, m, util.ToMongoId(staff.Id))
		}
		result = append(result, m)
	}
	return result
}

// 确保获取到用户绑定的数据, staffIds 不为空会验证 staffId，只有 staffId 在 staffIds 中的数据才会被返回
// 如果 staffIds 不为空，则 staffMembers 的查找条件必须包含 staffIds
func ensureStaffMembers(ctx context.Context, members []*pb_member.MemberDetailResponse, staffMembers []staff_model.StaffMember, channelId string, staffIds, distributorIds []string, searchByAdded bool) []staff_model.StaffMember {
	needSearch := false
OUTER:
	for _, member := range members {
		for _, staffMember := range staffMembers {
			if member.Id == staffMember.MemberId.Hex() && staffMember.RelationStatus == staff_model.STAFF_MEMBER_RELATION_BOUND {
				continue OUTER
			}
		}
		needSearch = true
	}
	if needSearch {
		memberIds := []string{}
		for _, member := range members {
			memberIds = append(memberIds, member.Id)
		}
		selector := bson.M{
			"accountId":      util.GetAccountIdAsObjectId(ctx),
			"memberId":       bson.M{"$in": util.ToMongoIds(memberIds)},
			"relationStatus": staff_model.STAFF_MEMBER_RELATION_BOUND,
		}
		// 好友导购查询需要展示绑定导购数据
		if len(staffIds) > 0 && !searchByAdded {
			selector["staffId"] = bson.M{"$in": util.ToMongoIds(staffIds)}
		}
		if len(distributorIds) > 0 {
			selector["storeId"] = bson.M{"$in": util.ToMongoIds(distributorIds)}
		}
		staffMembers, _ = staff_model.CStaffMember.FindAll(ctx, selector, []string{})

		boundStaffMemberIds := []string{}
		for _, staffMember := range staffMembers {
			boundStaffMemberIds = append(boundStaffMemberIds, staffMember.MemberId.Hex())
		}

		// 无绑定导购的客户获取最后其绑定过的记录
		deletedMemberIds := util.StrArrayDiff(memberIds, boundStaffMemberIds)
		membersLastBound := getMembersLastBound(ctx, util.ToMongoIds(deletedMemberIds), util.ToMongoIds(staffIds), util.ToMongoIds(distributorIds))
		staffMembers = append(staffMembers, membersLastBound...)
	}

	return staffMembers
}

func getMembersLastBound(ctx context.Context, memberIds, staffIds, distributorIds []bson.ObjectId) []staff_model.StaffMember {
	if len(memberIds) == 0 {
		return []staff_model.StaffMember{}
	}

	result := []staff_model.StaffMember{}
	sliceStart := 0
	wg := sync.WaitGroup{}
	for sliceStart < len(memberIds) {
		subMemberIds := []bson.ObjectId{}
		if sliceStart+100 >= len(memberIds) {
			subMemberIds = memberIds[sliceStart:]
		} else {
			subMemberIds = memberIds[sliceStart : sliceStart+100]
		}
		sliceStart += 100
		wg.Add(1)
		component.GO(ctx, func(ctx context.Context) {
			defer wg.Done()
			condMemberIds := []bson.ObjectId{}
			condMemberIds = append(condMemberIds, subMemberIds...)
			condition := bson.M{
				"accountId": util.GetAccountIdAsObjectId(ctx),
				"memberId": bson.M{
					"$in": condMemberIds,
				},
			}
			if len(staffIds) > 0 {
				condition["staffId"] = bson.M{
					"$in": staffIds,
				}
			}
			if len(distributorIds) > 0 {
				condition["distributorIds"] = bson.M{
					"$in": distributorIds,
				}
			}

			staffMembers, err := staff_model.CStaffMember.FindAll(ctx, condition, []string{})
			if err == nil {
				foundMemberIds := []string{}
				for _, staffMember := range staffMembers {
					if util.StrInArray(staffMember.MemberId.Hex(), &foundMemberIds) {
						continue
					}
					foundMemberIds = append(foundMemberIds, staffMember.MemberId.Hex())
					result = append(result, staffMember)
				}
			}
		})
	}
	wg.Wait()

	return result
}

func getStoreByStaffMembers(ctx context.Context, staffMembers []staff_model.StaffMember) (map[string][]*ec_staff.Store, map[string][]*ec_staff.Store) {
	storeIds := make([]bson.ObjectId, 0, len(staffMembers))
	for _, staffMember := range staffMembers {
		if !staffMember.StoreId.IsZero() {
			storeIds = append(storeIds, staffMember.StoreId)
		}
	}
	storeResp, err := ec_client.StoreService.ListBriefStores(ctx, &ec_store.ListStoresRequest{
		Ids:   util.MongoIdsToStrs(storeIds),
		Types: []int32{3},
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: 1000,
		},
	})

	storeMap := map[string][]*ec_staff.Store{}
	if err != nil {
		log.Error(ctx, err.Error(), log.Fields{})
		return nil, nil
	}
	for _, s := range storeResp.Items {
		store := &ec_staff.Store{}
		copier.Instance(nil).From(s).CopyTo(store)
		storeMap[s.Id] = append(storeMap[s.Id], store)
	}
	memberStoreMap := map[string][]*ec_staff.Store{}
	for _, staffMember := range staffMembers {
		if staffMember.RelationStatus == staff_model.STAFF_MEMBER_RELATION_BOUND {
			if stores, ok := storeMap[staffMember.StoreId.Hex()]; ok {
				memberStoreMap[staffMember.MemberId.Hex()] = stores
			}
		} else {
			if _, ok := memberStoreMap[staffMember.MemberId.Hex()]; !ok {
				if stores, ok := storeMap[staffMember.StoreId.Hex()]; ok {
					memberStoreMap[staffMember.MemberId.Hex()] = stores
				}
			}
		}
	}
	return memberStoreMap, storeMap
}

func getLinkedStores(ctx context.Context, memberDetail *pb_member.MemberDetailResponse, storeMap *map[string][]*ec_staff.Store) []*ec_staff.Store {
	var (
		linkedStoreIds []string
		result         []*ec_staff.Store
	)
	for _, property := range memberDetail.Properties {
		if property.Property.PropertyId == "ec_linked_stores" {
			for _, storeId := range property.GetValueArray().Value {
				stores := (*storeMap)[storeId]
				if len(stores) > 0 {
					result = append(result, stores...)
					continue
				}
				linkedStoreIds = append(linkedStoreIds, storeId)
			}
			break
		}
	}
	if len(linkedStoreIds) > 0 {
		storeResp, err := ec_client.StoreService.ListBriefStores(ctx, &ec_store.ListStoresRequest{
			Ids:   linkedStoreIds,
			Types: []int32{3},
			ListCondition: &request.ListCondition{
				Page:    1,
				PerPage: uint32(len(linkedStoreIds)),
			},
		})
		if err == nil {
			for _, s := range storeResp.Items {
				store := &ec_staff.Store{}
				copier.Instance(nil).From(s).CopyTo(store)
				(*storeMap)[s.Id] = append((*storeMap)[s.Id], store)
				result = append(result, store)
			}
		}
	}
	return result
}

func getStaffByStaffMembers(ctx context.Context, staffMembers []staff_model.StaffMember) map[string]*ec_store.StaffDetail {
	staffIds := []bson.ObjectId{}
	for _, staffMember := range staffMembers {
		if staffMember.StaffId.Hex() != "" {
			staffIds = append(staffIds, staffMember.StaffId)
		} else {
			log.Error(ctx, "InvalidStaff", log.Fields{
				"staffMemberId": staffMember.Id.Hex(),
			})
		}
	}
	staffResp, err := ec_client.StoreService.ListStaffs(ctx, &ec_store.ListStaffsRequest{
		StaffIds:      util.MongoIdsToStrs(staffIds),
		ListCondition: &request.ListCondition{PerPage: 999},
	})
	staffMap := map[string]*ec_store.StaffDetail{}
	if err != nil {
		log.Error(ctx, err.Error(), log.Fields{})
		return staffMap
	}
	for _, s := range staffResp.Items {
		staffMap[s.Id] = s
	}
	memberStaffMap := map[string]*ec_store.StaffDetail{}
	for _, staffMember := range staffMembers {
		if staffMember.RelationStatus == staff_model.STAFF_MEMBER_RELATION_BOUND {
			if staff, ok := staffMap[staffMember.StaffId.Hex()]; ok {
				memberStaffMap[staffMember.MemberId.Hex()] = staff
			}
		} else {
			if _, ok := memberStaffMap[staffMember.MemberId.Hex()]; !ok {
				if staff, ok := staffMap[staffMember.StaffId.Hex()]; ok {
					memberStaffMap[staffMember.MemberId.Hex()] = staff
				}
			}
		}
	}
	return memberStaffMap
}

func genStaffMemberMap(staffMembers []staff_model.StaffMember) map[string]staff_model.StaffMember {
	result := map[string]staff_model.StaffMember{}
	for _, staffMember := range staffMembers {
		if staffMember.RelationStatus == staff_model.STAFF_MEMBER_RELATION_BOUND {
			result[staffMember.MemberId.Hex()] = staffMember
		} else {
			if _, ok := result[staffMember.MemberId.Hex()]; !ok {
				result[staffMember.MemberId.Hex()] = staffMember
			}
		}
	}

	return result
}

func getChildStoreIds(ctx context.Context, distributorIds []string) ([]string, error) {
	if len(distributorIds) == 0 {
		return []string{}, nil
	}
	storeIdsResp, err := ec_client.StoreService.GetStoreIds(ctx, &ec_store.ListStoresRequest{
		Distributor: &types.DistributorSelector{
			Ids:         distributorIds,
			SelectField: "distributorIds",
		},
		Types: []int32{3},
	})
	if err != nil {
		return nil, err
	}
	return storeIdsResp.Ids, nil
}

func formatTransferMember(ctx context.Context, member *pb_member.MemberDetailResponse, result *ec_staff.MemberDetail, staffId bson.ObjectId) {
	result.Gender = member.Gender
	result.IsMember = member.IsActivated
	result.IsNonTransferable = staff_model.CStaffMember.IsNonTransferable(ctx, util.ToMongoId(member.Id), staffId)
	result.IsTransferPending = staff_model.CStaffMember.IsTransferPending(ctx, util.ToMongoId(member.Id), staffId)
}

func getMemberStaffCount(ctx context.Context, staffNos, storeIds []string) int {
	// 条件太多，count 会慢，这里先用 staffNo 条件查出来再计算 count
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"staffNo": bson.M{
			"$in": staffNos,
		},
	}
	staffs, _ := store_model.CStaff.GetAllByCondition(ctx, condition)
	count := 0
	for _, staff := range staffs {
		if staff.Source != "wechatwork" {
			continue
		}
		if staff.Status != 1 {
			continue
		}
		if len(util.StrArrayDuplicate(util.MongoIdsToStrs(staff.StoreIds), storeIds)) > 0 {
			count++
		}
	}
	return count
}

func getStaffMemberRelations(ctx context.Context) []string {
	relations := []string{}
	staffMemberRelations, err := staff_model.CStaffMemberRelation.GetAllRelations(ctx)
	if err != nil {
		return relations
	}
	for _, staffMemberRelation := range staffMemberRelations {
		relations = append(relations, staffMemberRelation.Name)
	}
	return relations
}
func findStaffMembersInParallel(ctx context.Context, selector bson.M, staffIds []string) ([]staff_model.StaffMember, bool) {
	var (
		wg           sync.WaitGroup
		mu           sync.Mutex
		staffMembers []staff_model.StaffMember
		poolSize     = 10
	)
	pool, err := ants.NewPool(poolSize)
	if err != nil {
		panic(err)
	}
	defer pool.Release()
	for i := 0; i < len(staffIds); i += batchSize {
		end := i + batchSize
		if end > len(staffIds) {
			end = len(staffIds)
		}
		wg.Add(1)
		pool.Submit(func() {
			defer wg.Done()
			data, _ := bson.Marshal(selector)
			var tempSelector bson.M
			bson.Unmarshal(data, &tempSelector)
			tempSelector["staffId"] = bson.M{"$in": util.ToMongoIds(staffIds[i:end])}
			tempstaffMembers := []staff_model.StaffMember{}
			pageSize := 1000
			for {
				results, err := staff_model.CStaffMember.GetAllByConditionAndLimit(ctx, tempSelector, []string{"_id"}, pageSize)
				if err != nil {
					break
				}
				tempstaffMembers = append(tempstaffMembers, results...)
				if len(results) < pageSize {
					break
				}
				tempSelector["_id"] = bson.M{"$gt": results[len(results)-1].Id}
			}
			mu.Lock()
			staffMembers = append(staffMembers, tempstaffMembers...)
			mu.Unlock()
		})
	}
	wg.Wait()
	if len(staffMembers) == 0 {
		return nil, false
	}
	return staffMembers, true
}
