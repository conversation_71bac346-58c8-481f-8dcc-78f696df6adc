@every 5m EcService.MallService.UpdateCommissionStatus retail
@every 301s EcService.MallService.BatchUpdateMallProductStats retail
@every 302s EcService.MallService.AutoCloseMallByStock retail
@every 30s EcService.OrderService.HandleUnpaidOrders retail
@every 31s EcService.OrderService.BatchUpdateRefundStatus retail
@every 1m EcService.OrderService.ExecOrderRefundJob retail
@every 10m EcService.OrderService.UpdatePrintTicketStatus retail
@every 303s EcService.OrderService.AutoCompleteOrderJob retail
@every 10s EcService.OrderService.PushOrdersToOmsJob retail
@every 62s EcService.SettingService.UpdatePrinterStatus retail
0 0 2,10 * * * EcService.OrderService.ExecDataAnalyses retail
@every 3h EcService.ContentService.UpdateWeappData retail
@every 63s EcService.DistributionService.UnbindPromoterMemberJob retail
@every 11s EcService.OrderService.PushRefundOrderJob retail
@every 1m EcService.OrderService.AutoCancelOrderRefundJob retail
0 0 1 1 * * EcService.DistributionService.PromoterStatisticsJob wechatwork
0 0 23 * * * EcService.DistributionService.UpdatePromoterCountJob wechatwork,retail
0 0 2 * * * EcService.DistributionService.IntegrateDistributionSetting
@every 12s EcService.ProfitsharingService.CreateShareBillForDailyJob retail
0 0 3 * * * EcService.ProfitsharingService.RetryTransferJob wechatwork,retail
0 0 1 16 * * EcService.ProfitsharingService.CreateShareBillForMonthlyJob wechatwork,retail
@every 30s EcService.ProfitsharingService.ExecTransferJob wechatwork,retail
@every 13s EcService.ProfitsharingService.CheckAndUpdateProfitsharingStatusJob wechatwork,retail
0 0 3 * * * EcService.StoreService.SyncWechatcpDepartmentAndUserJob wechatwork
0 0 3 * * * EcService.StoreService.CustomSyncWechatcpDepartmentAndUserJob lianhuahuashang
@every 64s EcService.StaffTaskService.CreateRepeatedStaffTaskJob wechatwork
@every 5s EcService.StaffTaskService.CreateStaffTaskJob wechatwork
@every 304s EcService.StaffTaskService.ResendTaskJob wechatwork
@every 305s EcService.StaffTaskService.GetMassMessageResult wechatwork
@every 30s EcService.StaffTaskService.UpdateExpiredStaffTaskStatusJob wechatwork
0 0 1 * * * EcService.GroupchatService.SyncGroupchatJob
0 0 5/12 * * * EcService.GroupchatService.StatsGroupchatJob
0 0 2 * * * EcService.GroupchatService.StatsGroupchatDrainageJob wechatwork
@every 14s EcService.StaffTaskService.OpenTaskJob wechatwork
@every 65s EcService.StaffTaskService.AddTaskTagJob wechatwork
@every 5m EcService.MemberService.SyncTagGroups wechatwork
@every 5m EcService.MemberService.SyncStrategyExternalContactTags wechatwork
@every 1h EcService.StaffService.StatsStaffMembersAndAmountHourlyJob
0 0 0 1 1,4,7,10 ? EcService.StaffService.StatsStaffMembersQuarterlyJob wechatwork
0 0 0 1 1,4,7,10 ? EcService.StaffService.StatsStaffAmountQuarterlyJob wechatwork
@every 1h EcService.StaffCampaignService.StatsStaffIncentiveOverviewCacheJob wechatwork
@every 66s EcService.MarketingService.CheckAndUpdateGrouponRecord retail
0 0 10 * * * EcService.StaffTaskService.SendTaskProgressMessageJob wechatwork
@every 67s EcService.StaffTaskService.SendUrgeMessageJob wechatwork
@every 68s EcService.MallService.WarnStockJob retail
0 0 1 * * * EcService.StaffTaskService.UpdateUrgeTimeJob wechatwork
# @every 69s EcService.WarehouseService.SyncProductStockFromProduct retail
0 30 3 * * * EcService.OrderService.CreateSystemDefaultComments retail
@every 15s EcService.OrderService.SyncLogisticsFromOmsJob retail
@every 16s EcService.OrderService.SyncOrderRefundFromOmsJob retail
@every 59s EcService.ProductService.SyncProductStockJob retail
@every 1h EcService.ProductService.CheckMismatchedExternalSkusJob retail
@every 1h EcService.ProductService.PushGoodsToErpJob retail
0 0 3 * * * EcService.DistributionService.CreateDefaultStaffPromoters wechatwork
0 0 9-23/2 * * EcService.StaffTaskService.HandleStaffTouchMembers wechatwork
0 10 0 * * * EcService.StaffTaskService.CountStaffTouchMemberData wechatwork
@every 17s EcService.MemberService.SyncMemberTagsJob wechatwork
@every 306s EcService.OrderService.SyncSpentRecords retail
@every 317s EcService.OrderService.SyncIncomeRecords retail
0 */10 * * * ? EcService.StaffTaskService.SyncOperationMembersJob
0 */10 * * * ? EcService.StaffTaskService.SyncDaliyOperationMembersJob
@every 70s EcService.MemberService.RemoveExpiredMemberStoreJob retail
@every 1h EcService.StaffService.StatsLiteStaffMembersHourlyJob sales
0 0 0 1 1,4,7,10 ? EcService.StaffService.StatsLiteStaffMembersQuarterlyJob sales
0 * * * * ? EcService.StaffCampaignService.RollbackCouponEndpointStock wechatwork
0 40 3 * * * EcService.StoreService.StoreDailyStatsJob wechatwork
0 * * * * ? EcService.MarketingService.RemindWillStartFlashSaleCampaign retail
@every 1h EcService.PromotionService.AutoCreateStorePromotionMonitorPoint retail
@every 6h EcService.MarketingService.InitFlashSaleCampaignStock retail
@every 5s EcService.MarketingService.SyncFlashSaleCampaignStock retail
@every 5s EcService.MarketingService.HandlePresellProductStatus retail
@every 30s EcService.MarketingService.HandlePresellCampaignUnpaidOrders retail
0 */10 * * * ? EcService.MarketingService.SendBalanceMessage retail
0 0 23 * * * EcService.MarketingService.UpdateFissionSubscribeCallback retail
@every 71s EcService.ProductService.SyncCouponProductSkuStockJob retail
@every 18s EcService.StaffCampaignService.CreateStaffDrainageActivitiesJob wechatwork
@every 19s EcService.StaffCampaignService.OpenDrainageActivitiesJob wechatwork
@every 72s EcService.StaffCampaignService.UpdateRepeatedGroupchatQrcodeStatusJob wechatwork
0 0 0 * * ? EcService.StaffCampaignService.RestoreDrainageMonitors wechatwork
0 0 * * * ? EcService.StaffCampaignService.SendDrainageMonitorEmailsJob wechatwork
0 0 10 * * * EcService.StaffCampaignService.SendGroupchatDrainageQrcodeExpiredJob wechatwork
@every 73s EcService.StaffCampaignService.SendGroupchatDrainageQrcodeMessageJob wechatwork
@every 74s EcService.StaffCampaignService.CloseExpiredDrainageActivityJob wechatwork
@every 45s EcService.StaffCampaignService.UpdateCouponCampaignsJob wechatwork
@every 21s EcService.StaffCampaignService.SendDrainageActivityNotificationJob wechatwork
0 0 * * * ? EcService.OrderService.DeliverVirtualPresellOrders retail
@every 75s EcService.StaffTaskService.UpdateOperationTimeJob wechatwork
@every 76s EcService.ScheduleService.SendScheduledJobNotification wechatwork
0 */10 * * * ? EcService.WalletService.HandleMemberProductCards retail
@every 77s EcService.WalletService.HandleExpiredProductCards retail
@every 1h EcService.WalletService.UpdateProductCardStatusJob retail
0 */1 * * * ? EcService.ContentService.PublishWeappPages retail
0 0 2 * * *  EcService.ProductService.ClearStoppedCampaigns retail
0 0 12 * * ? EcService.OrderService.RemindVirtualProductWillExpire retail
@every 10m EcService.StaffTaskService.DeletedStaffOperationTagJob wechatwork
@every 78s EcService.ScheduleService.CheckRpaGroupSentStatus wechatwork
@every 79s EcService.SubscriptionService.ProcessSubscribeMessageTasks retail
@every 2m EcService.SubscriptionService.UpdateSubscribeMessageCountJob retail
@every 80s EcService.StoreMatterService.EnablePatrolPlanJob wechatwork
0 0 10 * * * EcService.StoreMatterService.SendPatrolBriefingJob wechatwork
0 0 10 * * * EcService.StoreMatterService.SendNeedRecitifyNotificationJob wechatwork
@every 81s EcService.StoreMatterService.SendPatrolNotificationJob wechatwork
@every 307s EcService.OrderService.CheckInvoiceStatus retail
0 0 0 * * ? EcService.ProductService.HandleExpiredProduct retail
@every 308s EcService.SupplierService.SyncSupplierOrders retail
@every 309s EcService.SupplierService.CommissionChannelPromoter retail
0 0 0 * * ? EcService.StoreProductService.SyncStoreProductJob
@every 82s EcService.WeworkLicenseService.GetRenewalWeworkLicenseResultJob wechatwork
@every 1h EcService.WeworkLicenseService.UpdateWeworkLicenseMessageJob wechatwork
0 0 3 * * * EcService.WeworkLicenseService.UpdateWeworkLicenseStatusJob wechatwork
0 0 4 * * * EcService.WeworkLicenseService.UpsertWechatworkExpirationReminderJob wechatwork
@every 30m EcService.StaffService.UpdateStaffTransferLogStatusJob wechatwork
0 0 23 * * * EcService.StaffService.SyncResignedStaffResourceJob wechatwork
0 0 10 * * * EcService.StaffService.SendDistributorLeaderMessage wechatwork
0 0 4 * * ? EcService.WarehouseService.SyncProductStockJob
0 */05 * * * ? EcService.StaffTaskService.GetStaffOperationMassMessageResult wechatwork
0 */10 * * * ? EcService.WalletService.HandlePrepaidCards retail
0 */10 * * * ? EcService.CardProductService.HandleMemberRedeemCards retail
# 每十分钟获取全部核销的虚拟商品分销订单触发分销
@every 10m EcService.OrderService.HandleVirtualOrderProfit retail
0 0 16 * * ?  EcService.OrderingMallService.SyncChannelOrders
@every 20s EcService.OrderingMallService.HandleRefundedChannelOrders retail
@every 5m EcService.RetailerService.SyncStoreProductSales retail
@every 10m EcService.ProductService.AutoUnshelvedProducts
@every 3m EcService.RetailerService.ExecOrderRefundJob retail
0 0 3 * * * EcService.RetailerService.StatsStoreViewJob retail
@every 13s EcService.RetailerService.CheckAndUpdateRetailerBillStatusJob retail
@every 1m EcService.MemberService.HandleUnownedMembers wechatwork
0 0 1 * * ? EcService.StaffTaskService.StatsCustomerOperationDaily wechatwork
@every 1h EcService.ChainRetailService.SyncMicroRuwangRecordStatus retail
@every 1m EcService.ChainRetailService.HandleUnpaidConsignmentOrder retail
@every 1h EcService.MemberService.SyncMemberTagsContinuously
@every 1m EcService.ProductService.AutoPublishProducts retail
@every 10m EcService.ProductService.SyncSuitesJob retail
0 0 * * * ? EcService.StaffTaskService.CalculateEventStatsJob
@every 30s EcService.YeepayMerchantService.SyncMerchantRuwangStatus retail
0 0 1 * * ? EcService.StaffTaskService.StatsStaffTaskDaily wechatwork
0 10 1 * * ? EcService.StaffService.StatsStaffContactJob
0 0 0 * * ? EcService.StoreService.AutoOpenAndCloseStore retail
@every 1h EcService.RetailerService.AutoSyncBrandProfitSharingReceiver retail
0 */5 * * * ? EcService.ContentService.SendRadarMaterialReadRemindJob wechatwork
@every 10m EcService.OrderService.HandleDivideBackRecord retail
@every 10m EcService.ChainRetailService.HandleDivideBackRecord retail
0 0 23 * * * EcService.StoreService.SyncWechatMemberCount wechatwork
0 0 4 * * * EcService.StoreService.CalculateStoreRatingJob
@every 30s EcService.CardProductService.BatchUpdateCardOrderRefundStatus retail
@every 1m EcService.ProfitsharingService.SendAmountWaitConfirmNotification retail
@every 1m EcService.OrderService.HandleUnpaidOrdersWithShouqianba retail
@every 10m EcService.CardProductService.UpdateRedeemCardStatusRegularly retail
@every 10s EcService.OrderService.SyncOrderStatusToProxyOrderJob retail
@every 10m EcService.DistributionService.ExecXinHangJiaPaymentJob retail
@every 1m EcService.OrderService.SendPickupOrderNotification retail
@every 1m EcService.WechatShopService.CheckFreightInsuranceReturnStatus retail
