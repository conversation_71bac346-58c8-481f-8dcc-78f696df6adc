package store_warehouse

import (
	"encoding/json"
	"fmt"
	"os"

	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/proto/account"
	"mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	"mairpc/proto/ec/product"
	pb_ec_store_warehouse "mairpc/proto/ec/storeWarehouse"
	"mairpc/service/ec/client"
	store_warehouse_model "mairpc/service/ec/model/storeWarehouse"
	"mairpc/service/ec/service"
	store_warehouse_service "mairpc/service/ec/service/storeWarehouse"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"

	"github.com/spf13/cobra"
	"golang.org/x/net/context"
)

var (
	exportGroupProductStockBillsHeader = "供应商,单据编号,出入库时间,业务编号,门店名称,门店编号,商品名,商品条码,SKU编码/SKU条码,出入库类型,出入库数量,变更人\n"
	hyphen                             = "-"
)

func init() {
	StoreWarehouseCmd.AddCommand(exportGroupProductStockBills)
}

type exportDate struct {
	provider       string
	number         string
	changedAt      string
	businessNumber string
	storeName      string
	storeCode      string
	productName    string
	barCode        string
	sku            string
	skuBarCode     string
	operator       string
	count          string
	updatorName    string
}

var exportGroupProductStockBills = &cobra.Command{
	Use: "exportGroupProductStockBills",
	RunE: func(cmd *cobra.Command, args []string) error {

		jobOptions := util.GetArgs(args)
		ctx := util.GetContextInJob(args)
		ctx = core_util.CtxWithReadSecondaryPreferred(ctx)
		jobId := core_util.GetSreadminJobName()
		err := job_util.BeginToExport(ctx, jobId)
		if err != nil {
			return err
		}

		req := &pb_ec_store_warehouse.ListStoreProductStockBillsRequest{}
		byteData, err := json.Marshal(jobOptions["request"])
		if err != nil {
			return err
		}
		err = json.Unmarshal(byteData, req)
		if err != nil {
			return err
		}
		job, _ := job_util.GetJobStatus(ctx, jobId)
		url, err := job_util.ExportFile(
			ctx,
			job.Name,
			func(f *os.File) error {
				f.WriteString(exportGroupProductStockBillsHeader)

				var page uint32 = 1
				var perPage uint32 = 1000
				for {
					req.ListCondition.Page = page
					req.ListCondition.PerPage = perPage
					resp, totalCount, err := getGroupProductStockBills(ctx, req)
					if err != nil {
						return err
					}
					for _, date := range resp {
						line := fmt.Sprintf(
							"%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
							date.provider,
							date.number,
							date.changedAt,
							date.businessNumber,
							date.storeName,
							date.storeCode,
							date.productName,
							date.barCode,
							fmt.Sprintf("%s/%s", date.sku, date.skuBarCode),
							date.operator,
							date.count,
							date.updatorName,
						)
						f.WriteString(line)
					}

					if uint32(totalCount) <= uint32(page*perPage) {
						break
					}
					page++
				}
				f.Sync()
				return nil
			},
		)
		if err != nil {
			return err
		}

		err = job_util.ExportSucceed(ctx, jobId, url, job.Name)
		if err != nil {
			return err
		}

		return nil
	},
}

func getGroupProductStockBills(ctx context.Context, req *pb_ec_store_warehouse.ListStoreProductStockBillsRequest) ([]exportDate, int, error) {
	condition, _ := getListProductStockBillsCondition(ctx, req)
	pageCondition := util.FormatPagingCondition(condition, req.ListCondition)
	stockBills, totalCount := store_warehouse_model.CStoreStockBill.FindByPagination(ctx, pageCondition)
	var productIds []string
	var userIds []string
	for _, r := range stockBills {
		productIds = append(productIds, r.ProductId.Hex())
		if !util.StrInArray(r.UpdatedBy.Hex(), &userIds) && !util.StrInArray(r.Operator, &[]string{store_warehouse_model.OPERATOR_STOCK_BILL_REFUND, store_warehouse_model.OPERATOR_STOCK_BILL_SHIP}) {
			userIds = append(userIds, r.UpdatedBy.Hex())
		}
	}
	resp, err := client.ProductService.ListProducts(ctx, &ec.ListProductsRequest{
		EcProductIds: productIds,
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: uint32(len(productIds)),
		},
	})
	if err != nil {
		return nil, 0, err
	}

	userResp, err := service.GetUserByIds(ctx, userIds)
	if err != nil {
		return nil, 0, err
	}
	productMap := formatProductMap(resp.Items)
	userNameMap := formatUserNameMap(userResp)

	var date []exportDate
	for _, bill := range stockBills {
		temp := exportDate{
			number:         bill.Number,
			changedAt:      bill.ChangedAt.Format("2006/01/02 15:04:05"),
			businessNumber: bill.BusinessNumber,
			storeName:      bill.Store.Name,
			storeCode:      bill.Store.Code,
			sku:            bill.Sku,
			operator:       formatOperator(bill.Operator),
			count:          formatCount(bill.Count, bill.Type),
		}

		if bill.Provider.Name != "" {
			temp.provider = bill.Provider.Name
		} else {
			temp.provider = hyphen
		}

		product := productMap[bill.ProductId.Hex()]
		if product != nil {
			temp.productName = product.Product.Name
			temp.barCode = product.Product.BarCode
			for _, s := range product.Product.Skus {
				if s.Sku != bill.Sku {
					continue
				}
				temp.skuBarCode = s.BarCode
			}
		}
		if !util.StrInArray(bill.Operator, &[]string{store_warehouse_model.OPERATOR_STOCK_BILL_REFUND, store_warehouse_model.OPERATOR_STOCK_BILL_SHIP}) {
			temp.updatorName = userNameMap[bill.UpdatedBy.Hex()]
		}
		date = append(date, temp)
	}
	return date, totalCount, nil
}

func formatOperator(operator string) string {
	switch operator {
	case store_warehouse_model.OPERATOR_STOCK_BILL_REFUND:
		return "销售退货入库"
	case store_warehouse_model.OPERATOR_STOCK_BILL_SHIP:
		return "销售出库"
	case store_warehouse_model.OPERATOR_STOCK_BILL_ALLOCATE_IN:
		return "调拨入库"
	case store_warehouse_model.OPERATOR_STOCK_BILL_ALLOCATE_OUT:
		return "调拨出库"
	case store_warehouse_model.OPERATOR_STOCK_BILL_PURCHASE_IN:
		return "采购入库"
	case store_warehouse_model.OPERATOR_STOCK_BILL_REFUND_TO_SUPPLIER:
		return "退货给供应商"
	case store_warehouse_model.OPERATOR_STOCK_BILL_MANUAL:
		return "手动调整"
	case store_warehouse_model.OPERATOR_STOCK_BILL_OTHERS:
		return "其他原因"
	}
	return ""
}

func formatCount(count uint64, billType string) string {
	switch billType {
	case store_warehouse_model.TYPE_STOCK_BILL_IN:
		return fmt.Sprintf("+%d", count)
	case store_warehouse_model.TYPE_STOCK_BILL_OUT:
		return fmt.Sprintf("-%d", count)
	}
	return ""
}

func getListProductStockBillsCondition(ctx context.Context, req *pb_ec_store_warehouse.ListStoreProductStockBillsRequest) (bson.M, error) {
	condition := store_warehouse_model.Common.GenDefaultCondition(ctx)
	if req.TimeRange != nil {
		condition["changedAt"] = util.ParseStringDateRange(req.TimeRange)
	}
	if req.Operator != "" {
		condition["operator"] = req.Operator
	} else {
		op := []string{store_warehouse_model.OPERATOR_STOCK_BILL_REFUND, store_warehouse_model.OPERATOR_STOCK_BILL_SHIP, store_warehouse_model.OPERATOR_STOCK_BILL_PURCHASE_IN, store_warehouse_model.OPERATOR_STOCK_BILL_ALLOCATE_IN, store_warehouse_model.OPERATOR_STOCK_BILL_ALLOCATE_OUT, store_warehouse_model.OPERATOR_STOCK_BILL_REFUND_TO_SUPPLIER, store_warehouse_model.OPERATOR_STOCK_BILL_OTHERS}
		condition["operator"] = bson.M{
			"$in": op,
		}
	}
	if req.Store != "" {
		storeIds, _ := store_warehouse_service.GetStoreIdsByQuery(ctx, req.Store)
		condition["store.id"] = bson.M{
			"$in": storeIds,
		}
	}
	if req.QueryString != "" {
		productIds, err := store_warehouse_service.GetProductIdsByQuery(ctx, req.QueryString)
		if err != nil {
			return bson.M{}, err
		}
		skus, _, err := store_warehouse_service.GetProductSkusByExternalSkus(ctx, []string{req.QueryString})
		if err != nil {
			return bson.M{}, err
		}
		orCondition := []bson.M{
			{
				"productId": bson.M{
					"$in": productIds,
				},
			},
			{
				"sku": bson.M{
					"$in": skus,
				},
			},
			{
				"sku": req.QueryString,
			},
			{
				"number": req.QueryString,
			},
			{
				"businessNumber": req.QueryString,
			},
		}
		condition["$or"] = orCondition
		condition = util.FormatConditionContainedOr(condition)
	}
	return condition, nil
}

func formatUserNameMap(userResp *account.UserListResponse) map[string]string {
	nameMap := make(map[string]string)
	for _, item := range userResp.Items {
		nameMap[item.Id] = item.Name
	}
	return nameMap
}

func formatProductMap(items []*product.ProductResponse) map[string]*product.ProductResponse {
	productMap := make(map[string]*product.ProductResponse)
	for _, item := range items {
		productMap[item.Ec.Id] = item
	}
	return productMap
}
