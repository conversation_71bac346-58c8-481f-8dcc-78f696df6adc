package store_warehouse

import (
	"context"
	"fmt"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/common/request"
	pb_store "mairpc/proto/ec/store"
	pb_store_warehouse "mairpc/proto/ec/storeWarehouse"
	ec_client "mairpc/service/ec/client"
	model_store_warehouse "mairpc/service/ec/model/storeWarehouse"
	store_warehouse_model "mairpc/service/ec/model/storeWarehouse"
	"mairpc/service/ec/service/order"
	service_store_warehouse "mairpc/service/ec/service/storeWarehouse"
	"mairpc/service/share/component/oss"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"
	"os"
	"strings"

	"github.com/spf13/cast"
	"github.com/spf13/cobra"
	"github.com/tealeg/xlsx"
)

func init() {
	StoreWarehouseCmd.AddCommand(importStoresProductStockBills)
}

var importStoresProductStockBills = &cobra.Command{
	Use: "importStoresProductStockBills",
	RunE: func(cmd *cobra.Command, args []string) error {
		ctx := util.GetContextInJob(args)
		jobId := core_util.GetSreadminJobName()
		err := job_util.BeginToImport(ctx, jobId)
		if err != nil {
			return err
		}

		jobOptions := util.GetArgs(args)
		ctx = core_util.CtxWithUserID(ctx, cast.ToString(jobOptions["userId"]))
		req, err := getReqFromArgs(jobOptions["request"])
		if err != nil {
			return err
		}

		importer := genStoreImporter(req)

		err = importer.StoreImport(ctx)
		if err != nil {
			return err
		}
		var url string
		if len(importer.failedSheet.Rows) > 1 {
			url, err = job_util.ExportFile(
				ctx,
				getFailedFileName(importer.stockBillType),
				func(f *os.File) error {
					file := xlsx.NewFile()
					file.AppendSheet(*importer.failedSheet, "Sheet1")
					file.Save(f.Name())
					f.Sync()
					return nil
				},
			)
			if err != nil {
				err := job_util.ImportFailed(ctx, jobId, "", "")
				return err
			}
		}

		sourceUrl, err := oss.OSSClient(ctx).SignUrl(ctx, req.FileUrl, oss.HTTPGet, "", oss.EXPIRATION_SECONDS, nil)
		if err != nil {
			log.Warn(ctx, "Failed to get sourceUrl", log.Fields{
				"errMsg": err.Error(),
			})
		}
		sourceUrl = strings.ReplaceAll(sourceUrl, "%2F", "/")
		err = job_util.ImportSucceed(ctx, jobId, url, sourceUrl, "", importer.maxRow-len(importer.failedSheet.Rows)-1, len(importer.failedSheet.Rows)-1)
		if err != nil {
			return err
		}

		return nil
	},
}

func genStoreImporter(req *pb_store_warehouse.ImportStockBillsRequest) *StockBillImporter {
	importer := &StockBillImporter{
		stockBillType: req.Type,
		fileUrl:       req.FileUrl,
		headers:       service_store_warehouse.GetStoreHeader(req.Type),
		validFuncMap:  genStoreValidFuncMapByType(req.Type),
		operators:     getOperatorsByType(req.Type),
	}
	importer.InitFailedSheet()
	return importer
}

func (importer *StockBillImporter) StoreImport(ctx context.Context) error {
	filePath := util.GetTmpFilePath(bson.NewObjectId().Hex())
	err := oss.OSSClient(ctx).GetObject(ctx, importer.fileUrl, filePath)
	if err != nil {
		return err
	}
	tableReader, err := core_util.GetXlsxReader(filePath)
	if err != nil {
		return err
	}
	importer.maxRow = tableReader.RealMaxRow()
	var (
		currentBillBeginRowIndex int
		currentBillEndRowIndex   int
		billUnique               string
		skus                     []string
		externalSkus             []string
	)
	rows := tableReader.GetCachedRows()
	for i := 2; i < importer.maxRow; i++ {
		var (
			row               = rows[i]
			currentBillUnique = getBillUnique(importer.stockBillType, importer.headers, row)
		)

		rowProvider := getRowItemByIndex(row, getProviderIndex(importer.stockBillType, importer.headers))
		if provider == "" && rowProvider != "" {
			provider = rowProvider
			resp, err := model_store_warehouse.CStoreStockProvider.FindByName(ctx, provider)
			// 如果没有找到供应商，则创建一个新的供应商
			if resp == nil || err != nil {
				ec_client.StoreWarehouseService.CreateStoreStockProvider(ctx, &request.StringRequest{
					Value: provider,
				})
			}
		}

		if billUnique == "" {
			// 一条出入库信息的开始
			currentBillBeginRowIndex = i
			billUnique = currentBillUnique
			skus = append(skus, getRowItemByIndex(row, getSkuIndex(importer.stockBillType, importer.headers)))
			externalSkus = append(externalSkus, getRowItemByIndex(row, getExternalSkuIndex(importer.stockBillType, importer.headers)))
			continue
		}
		if billUnique == currentBillUnique {
			// 同一条出入库信息
			skus = append(skus, getRowItemByIndex(row, getSkuIndex(importer.stockBillType, importer.headers)))
			externalSkus = append(externalSkus, getRowItemByIndex(row, getExternalSkuIndex(importer.stockBillType, importer.headers)))
			continue
		}
		currentBillEndRowIndex = i - 1
		// 一条出入库信息结束
		err := importer.importStoreBill(ctx, rows, currentBillBeginRowIndex, currentBillEndRowIndex, skus, externalSkus)
		if err != nil {
			return err
		}

		billUnique = currentBillUnique
		skus = []string{getRowItemByIndex(row, getSkuIndex(importer.stockBillType, importer.headers))}
		externalSkus = append(externalSkus, getRowItemByIndex(row, getExternalSkuIndex(importer.stockBillType, importer.headers)))
		currentBillBeginRowIndex = i
	}
	err = importer.importStoreBill(ctx, rows, currentBillBeginRowIndex, importer.maxRow-1, skus, externalSkus)
	if err != nil {
		return err
	}

	return nil
}

func getStoreInfo(ctx context.Context, code string) (storeInfo *pb_store_warehouse.UpdateStoreStock, err error) {
	storeResp, err := ec_client.StoreService.ListStores(ctx, &pb_store.ListStoresRequest{SearchKey: code})
	if err != nil {
		log.Warn(ctx, "faild to get store", log.Fields{
			"searchKey": code,
		})
		return nil, err
	}

	if len(storeResp.Items) > 0 {
		item := storeResp.Items[0]
		storeInfo = &pb_store_warehouse.UpdateStoreStock{
			Id:   item.Id,
			Code: item.Code,
			Name: item.Name,
		}
	}
	return
}

func (importer *StockBillImporter) importStoreBill(ctx context.Context, rows [][]string, start, end int, skus, externalSkus []string) error {
	importer.GetProductStocks(ctx, skus, externalSkus)
	req := &pb_store_warehouse.CreateStockBillRequest{
		Type: importer.stockBillType,
	}

	sku := getRowItemByIndex(rows[start], getSkuIndex(importer.stockBillType, importer.headers))
	externalSku := getRowItemByIndex(rows[start], getExternalSkuIndex(importer.stockBillType, importer.headers))
	for i := start; i <= end; i++ {
		row := rows[i]
		if err := importer.ValidateRow(ctx, row); err != "" {
			importer.AddFailedRow(row, err)
			continue
		}
		if getRowItemByIndex(row, getSkuIndex(importer.stockBillType, importer.headers)) != sku {
			importer.AddFailedRow(row, fmt.Sprintf(NOT_UNIQUE_ERROR_FORMAT, importer.headers[getExternalSkuIndex(importer.stockBillType, importer.headers)]))
			continue
		}
		if getRowItemByIndex(row, getExternalSkuIndex(importer.stockBillType, importer.headers)) != externalSku {
			importer.AddFailedRow(row, fmt.Sprintf(NOT_UNIQUE_ERROR_FORMAT, importer.headers[getExternalSkuIndex(importer.stockBillType, importer.headers)]))
			continue
		}
		resp, err := getStoreInfo(ctx, getRowItemByIndex(row, getStoreCodeIndex(importer.stockBillType, importer.headers)))
		if err != nil {
			importer.AddFailedRow(row, fmt.Sprintf(NOT_EXISTS_ERROR_FORMAT, importer.headers[getStoreCodeIndex(importer.stockBillType, importer.headers)]))
			continue
		}
		if len(req.Products) == 0 {
			req.BusinessNumber = getRowItemByIndex(row, getBusinessIndex(importer.stockBillType, importer.headers))
			req.Operator = transOperator(getRowItemByIndex(row, getOperatorIndex(importer.stockBillType, importer.headers)))
			req.ChangedAt = getChangedAt(getRowItemByIndex(row, getDateIndex(importer.stockBillType, importer.headers)), getRowItemByIndex(row, getTimeIndex(importer.stockBillType, importer.headers)))
			req.Remarks = getRowItemByIndex(row, getRemarkIndex(importer.stockBillType, importer.headers))
		}

		rowProvider := getRowItemByIndex(row, getProviderIndex(importer.stockBillType, importer.headers))
		if rowProvider != provider {
			importer.AddFailedRow(row, fmt.Sprintf(NOT_UNIQUE_ERROR_FORMAT, importer.headers[getProviderIndex(importer.stockBillType, importer.headers)]))
			continue
		}
		req.Provider = provider

		sku := strings.TrimSpace(getRowItemByIndex(row, getSkuIndex(importer.stockBillType, importer.headers)))
		skus := []string{sku}
		if sku == "" {
			skus = importer.externalSkuMap[strings.TrimSpace(getRowItemByIndex(row, getExternalSkuIndex(importer.stockBillType, importer.headers)))]
		}
		for _, sku = range skus {
			productStock := importer.storeProductStocksMap[sku]
			p := &pb_store_warehouse.StockBillProduct{
				Id:      productStock.ProductId.Hex(),
				Sku:     productStock.Sku,
				Count:   cast.ToUint64(getRowItemByIndexWithDefaultValue(row, getCountIndex(importer.stockBillType, importer.headers), "0")),
				StoreId: resp.Id,
			}
			if getRowItemByIndex(row, getPriceIndex(importer.stockBillType, importer.headers)) != "" {
				p.Price = cast.ToUint64(formatPrice(getRowItemByIndex(row, getPriceIndex(importer.stockBillType, importer.headers))))
			}
			if checkStoreProduct(ctx, p.StoreId, p.Sku, p.Id, int(p.Count), importer) != "" {
				importer.AddFailedRow(row, checkStoreProduct(ctx, p.StoreId, p.Sku, p.Id, int(p.Count), importer))
				continue
			}

			req.Products = append(req.Products, p)
			req.Stores = append(req.Stores, resp)
		}
	}
	if len(req.Products) > 0 {
		log.Warn(ctx, "batch insert before ...", log.Fields{
			"req": req,
		})
		resp, err := batchInsertStockBill(ctx, req)
		if err != nil {
			return err
		}
		failedSkus := core_util.ExtractArrayStringField("Sku", resp.FailedProducts)
		if len(failedSkus) == 0 {
			return nil
		}
		for i := start; i <= end; i++ {
			row := rows[i]
			if core_util.StrInArray(getRowItemByIndex(row, getSkuIndex(importer.stockBillType, importer.headers)), &failedSkus) {
				importer.AddFailedRow(row, "出库数量大于门店可售库存")
			}
		}
	}
	return nil
}

func genStoreValidFuncMapByType(t string) map[int]ValidColFunc {
	headers := service_store_warehouse.GetStoreHeader(t)
	return map[int]ValidColFunc{
		getProviderIndex(t, headers):    ValidateProvider,
		getDateIndex(t, headers):        ValidateDate,
		getTimeIndex(t, headers):        ValidateTime,
		getOperatorIndex(t, headers):    ValidateOperator,
		getBusinessIndex(t, headers):    ValidateBusinessNumber,
		getSkuIndex(t, headers):         ValidateSku,
		getCountIndex(t, headers):       ValidateCount,
		getExternalSkuIndex(t, headers): ValidateExternalSku,
		getPriceIndex(t, headers):       ValidatePrice,
		getStoreCodeIndex(t, headers):   ValidateStoreCode,
		getRemarkIndex(t, headers):      ValidateRemark,
	}
}

func checkStoreProduct(ctx context.Context, storeId string, sku string, productId string, count int, importer *StockBillImporter) string {
	storeResp, err := model_store_warehouse.CStoreProductStock.GetByProductIdAndSkuAndStoreId(ctx, bson.ObjectIdHex(productId), sku, bson.ObjectIdHex(storeId))
	if err != nil {
		log.Warn(ctx, "failed to get store product stock", log.Fields{
			"storeId":   storeId,
			"productId": productId,
			"sku":       sku,
			"count":     count,
		})
		if err == bson.ErrNotFound {
			return "该门店商品不存在"
		}
		return "failed to get store product stock"
	}
	if importer.stockBillType == model_store_warehouse.TYPE_STOCK_BILL_OUT && storeResp.AvailableStock < count {
		return "出库数量大于门店可售库存"
	}
	return ""
}

func batchInsertStockBill(ctx context.Context, req *pb_store_warehouse.CreateStockBillRequest) (resp *pb_store_warehouse.CreateStockBillResponse, err error) {
	stockBills := []model_store_warehouse.StoreStockBill{}
	failedProducts := []*pb_store_warehouse.StockBillProduct{}
	tempStockBill := model_store_warehouse.StoreStockBill{}
	core_util.FormatRFC3339(req, &tempStockBill, map[string]interface{}{
		"ChangedAt": core_util.ParseRFC3339,
	}, map[string]string{})
	tempStockBill.Operator = req.Operator
	tempStockBill.Remarks = req.Remarks
	tempStockBill.Type = req.Type
	tempStockBill.Number = model_store_warehouse.GenStockBillNumber(req.Type)
	if req.Provider != "" {
		existedProvider, _ := model_store_warehouse.CStoreStockProvider.FindByName(ctx, req.Provider)
		tempStockBill.Provider = model_store_warehouse.Provider{
			Id:   existedProvider.Id,
			Name: existedProvider.Name,
		}
	}

	productIds := []bson.ObjectId{}
	storeIds := []bson.ObjectId{}
	for i, p := range req.Products {
		if p.Count == 0 {
			failedProducts = append(failedProducts, req.Products[i])
			continue
		}
		for _, s := range req.Stores {
			if s.Id == p.StoreId {
				tempStockBill.Store = model_store_warehouse.Store{
					Id:   bson.ObjectIdHex(s.Id),
					Name: s.Name,
					Code: s.Code,
				}
			}
		}
		tempStockBill.ProductId = bson.ObjectIdHex(p.Id)
		tempStockBill.Sku = p.Sku
		tempStockBill.Count = p.Count
		tempStockBill.Price = p.Price
		if tempStockBill.Type == model_store_warehouse.TYPE_STOCK_BILL_IN {
			err = model_store_warehouse.CStoreProductStock.IncStoreStock(ctx, tempStockBill.Sku, int(p.Count), tempStockBill.Store.Id, tempStockBill.ProductId)
		} else {
			err = model_store_warehouse.CStoreProductStock.DecStoreStock(ctx, tempStockBill.Sku, int(p.Count), tempStockBill.Store.Id, tempStockBill.ProductId)
		}
		if err != nil {
			// 修改库存失败，该商品出入库失败
			log.Warn(ctx, "Failed to change product stock", log.Fields{
				"store":     tempStockBill.Store,
				"productId": p.Id,
				"sku":       p.Sku,
				"count":     p.Count,
				"price":     p.Price,
				"error":     err.Error(),
			})
			failedProducts = append(failedProducts, req.Products[i])
			resp.FailedProducts = failedProducts
			return resp, err
		}
		productIds = append(productIds, tempStockBill.ProductId)
		storeIds = append(storeIds, tempStockBill.Store.Id)
		stockBills = append(stockBills, tempStockBill)
	}

	storeProductStocks, err := store_warehouse_model.CStoreProductStock.GetAllByIds(ctx, productIds, storeIds)
	if err != nil {
		log.Warn(ctx, "Failed to get products", log.Fields{
			"storeProductStocks": storeProductStocks,
			"err":                err.Error(),
		})
	}
	stockBills = order.FillStoreAvailableStock(stockBills, storeProductStocks)
	err = store_warehouse_model.CStoreStockBill.BatchInsert(ctx, stockBills)
	return &pb_store_warehouse.CreateStockBillResponse{FailedProducts: failedProducts}, err
}
