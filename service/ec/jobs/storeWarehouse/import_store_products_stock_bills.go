package store_warehouse

import (
	"context"
	"encoding/json"
	"fmt"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/common/request"
	pb_store "mairpc/proto/ec/store"
	pb_store_warehouse "mairpc/proto/ec/storeWarehouse"
	ec_client "mairpc/service/ec/client"
	model_store_warehouse "mairpc/service/ec/model/storeWarehouse"
	service_store_warehouse "mairpc/service/ec/service/storeWarehouse"
	"mairpc/service/share/component/oss"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"github.com/spf13/cobra"
	"github.com/tealeg/xlsx"
	"github.com/xuri/excelize/v2"
)

type StockBillImporter struct {
	stockBillType         string
	headers               []string
	operators             []string
	fileUrl               string
	storeProductStocksMap map[string]model_store_warehouse.StoreProductStock
	externalSkuMap        map[string][]string
	failedSheet           *xlsx.Sheet
	validFuncMap          map[int]ValidColFunc
	maxRow                int
}

type ValidColFunc func(importer *StockBillImporter, row []string, index int) string

const (
	REQUIRED_ERROR_FORMAT   = "%s为必填"
	INCORRECT_ERROR_FORMAT  = "%s不正确"
	NOT_EXISTS_ERROR_FORMAT = "%s不存在"
	NOT_UNIQUE_ERROR_FORMAT = "%s不唯一"

	DATE_LAYOUT = "01/02/2006"
	TIME_LAYOUT = "3:04:05 PM"

	STOCK_IN_FAILED_FILE_NAME  = "私域商城_库存_出入库管理_批量入库_失败原因_%s.xlsx"
	STOCK_OUT_FAILED_FILE_NAME = "私域商城_库存_出入库管理_批量出库_失败原因_%s.xlsx"
)

var provider string

func init() {
	StoreWarehouseCmd.AddCommand(importStoreProductsStockBills)
}

var importStoreProductsStockBills = &cobra.Command{
	Use: "importStoreProductsStockBills",
	RunE: func(cmd *cobra.Command, args []string) error {
		ctx := util.GetContextInJob(args)
		jobId := core_util.GetSreadminJobName()
		err := job_util.BeginToImport(ctx, jobId)
		if err != nil {
			return err
		}

		jobOptions := util.GetArgs(args)
		ctx = core_util.CtxWithUserID(ctx, cast.ToString(jobOptions["userId"]))
		req, err := getReqFromArgs(jobOptions["request"])
		if err != nil {
			return err
		}

		importer := genImporter(req)

		err = importer.Import(ctx)
		if err != nil {
			return err
		}
		var url string
		if len(importer.failedSheet.Rows) > 1 {
			url, err = job_util.ExportFile(
				ctx,
				getFailedFileName(importer.stockBillType),
				func(f *os.File) error {
					file := xlsx.NewFile()
					file.AppendSheet(*importer.failedSheet, "Sheet1")
					file.Save(f.Name())
					f.Sync()
					return nil
				},
			)
			if err != nil {
				err := job_util.ImportFailed(ctx, jobId, "", "")
				return err
			}
		}

		sourceUrl, err := oss.OSSClient(ctx).SignUrl(ctx, req.FileUrl, oss.HTTPGet, "", oss.EXPIRATION_SECONDS, nil)
		if err != nil {
			log.Warn(ctx, "Failed to get sourceUrl", log.Fields{
				"errMsg": err.Error(),
			})
		}
		sourceUrl = strings.ReplaceAll(sourceUrl, "%2F", "/")
		err = job_util.ImportSucceed(ctx, jobId, url, sourceUrl, "", importer.maxRow-len(importer.failedSheet.Rows)-1, len(importer.failedSheet.Rows)-1)
		if err != nil {
			return err
		}

		return nil
	},
}

func getReqFromArgs(args interface{}) (*pb_store_warehouse.ImportStockBillsRequest, error) {
	req := &pb_store_warehouse.ImportStockBillsRequest{}
	byteData, err := json.Marshal(args)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(byteData, req)
	if err != nil {
		return nil, err
	}
	return req, nil
}

func genImporter(req *pb_store_warehouse.ImportStockBillsRequest) *StockBillImporter {
	importer := &StockBillImporter{
		stockBillType: req.Type,
		fileUrl:       req.FileUrl,
		headers:       service_store_warehouse.GetHeader(req.Type),
		validFuncMap:  genValidFuncMapByType(req.Type),
		operators:     getOperatorsByType(req.Type),
	}
	importer.InitFailedSheet()
	return importer
}

func (importer *StockBillImporter) Import(ctx context.Context) error {
	filePath := util.GetTmpFilePath(bson.NewObjectId().Hex())
	err := oss.OSSClient(ctx).GetObject(ctx, importer.fileUrl, filePath)
	if err != nil {
		return err
	}
	tableReader, err := core_util.GetXlsxReader(filePath)
	if err != nil {
		return err
	}
	importer.maxRow = tableReader.RealMaxRow()
	var (
		currentBillBeginRowIndex int
		currentBillEndRowIndex   int
		billUnique               string
		skus                     []string
		externalSkus             []string
	)
	rows := tableReader.GetCachedRows()
	for i := 2; i < importer.maxRow; i++ {
		var (
			row               = rows[i]
			currentBillUnique = getBillUnique(importer.stockBillType, importer.headers, row)
		)
		rowProvider := getRowItemByIndex(row, getProviderIndex(importer.stockBillType, importer.headers))
		if provider == "" && rowProvider != "" {
			provider = rowProvider
			resp, err := model_store_warehouse.CStoreStockProvider.FindByName(ctx, provider)
			// 如果没有找到供应商，则创建一个新的供应商
			if resp == nil || err != nil {
				ec_client.StoreWarehouseService.CreateStoreStockProvider(ctx, &request.StringRequest{
					Value: provider,
				})
			}
		}
		if billUnique == "" {
			// 一条出入库信息的开始
			currentBillBeginRowIndex = i
			billUnique = currentBillUnique
			skus = append(skus, getRowItemByIndex(row, getSkuIndex(importer.stockBillType, importer.headers)))
			externalSkus = append(externalSkus, getRowItemByIndex(row, getExternalSkuIndex(importer.stockBillType, importer.headers)))
			continue
		}
		if billUnique == currentBillUnique {
			// 同一条出入库信息
			skus = append(skus, getRowItemByIndex(row, getSkuIndex(importer.stockBillType, importer.headers)))
			externalSkus = append(externalSkus, getRowItemByIndex(row, getExternalSkuIndex(importer.stockBillType, importer.headers)))
			continue
		}
		currentBillEndRowIndex = i - 1
		// 一条出入库信息结束
		err := importer.importBill(ctx, rows, currentBillBeginRowIndex, currentBillEndRowIndex, skus, externalSkus)
		if err != nil {
			return err
		}

		billUnique = currentBillUnique
		skus = []string{getRowItemByIndex(row, getSkuIndex(importer.stockBillType, importer.headers))}
		externalSkus = append(externalSkus, getRowItemByIndex(row, getExternalSkuIndex(importer.stockBillType, importer.headers)))
		currentBillBeginRowIndex = i
	}
	err = importer.importBill(ctx, rows, currentBillBeginRowIndex, importer.maxRow-1, skus, externalSkus)
	if err != nil {
		return err
	}

	return nil
}

func getBillUnique(t string, header []string, row []string) string {
	return fmt.Sprintf("%s-%s-%s-%s-%s",
		getRowItemByIndex(row, getDateIndex(t, header)),
		getRowItemByIndex(row, getTimeIndex(t, header)),
		getRowItemByIndex(row, getOperatorIndex(t, header)),
		getRowItemByIndex(row, getBusinessIndex(t, header)),
		getRowItemByIndex(row, getRemarkIndex(t, header)),
	)
}

func getRowItemByIndex(row []string, index int) string {
	if index >= len(row) {
		return ""
	}
	return row[index]
}

func getRowItemByIndexWithDefaultValue(row []string, index int, defaultValue string) string {
	item := getRowItemByIndex(row, index)
	if item == "" {
		return defaultValue
	}
	return item
}

func (importer *StockBillImporter) importBill(ctx context.Context, rows [][]string, start, end int, skus, externalSkus []string) error {
	importer.GetProductStocks(ctx, skus, externalSkus)
	req := &pb_store_warehouse.CreateStockBillRequest{
		Type: importer.stockBillType,
	}
	storeCode := getRowItemByIndex(rows[start], getStoreCodeIndex(importer.stockBillType, importer.headers))
	storeResp, err := ec_client.StoreService.ListStores(ctx, &pb_store.ListStoresRequest{
		SearchKey: storeCode,
	})
	if err != nil {
		return err
	}
	item := storeResp.Items[0]
	storeId := item.Id
	storeInfo := []*pb_store_warehouse.UpdateStoreStock{
		{
			Id:   storeId,
			Code: storeCode,
			Name: item.Name,
		},
	}
	req.Stores = storeInfo

	for i := start; i <= end; i++ {
		row := rows[i]
		if err := importer.ValidateRow(ctx, row); err != "" {
			importer.AddFailedRow(row, err)
			continue
		}
		if getRowItemByIndex(row, getStoreCodeIndex(importer.stockBillType, importer.headers)) != storeCode {
			if importer.stockBillType == model_store_warehouse.TYPE_STOCK_BILL_IN {
				importer.AddFailedRow(row, "在单门店多商品导入模式下，门店编号不唯一")
			} else {
				importer.AddFailedRow(row, "在单门店多商品导出模式下，门店编号不唯一")
			}
			continue
		}
		if len(req.Products) == 0 {
			req.BusinessNumber = getRowItemByIndex(row, getBusinessIndex(importer.stockBillType, importer.headers))
			req.Operator = transOperator(getRowItemByIndex(row, getOperatorIndex(importer.stockBillType, importer.headers)))
			req.ChangedAt = getChangedAt(getRowItemByIndex(row, getDateIndex(importer.stockBillType, importer.headers)), getRowItemByIndex(row, getTimeIndex(importer.stockBillType, importer.headers)))
			req.Remarks = getRowItemByIndex(row, getRemarkIndex(importer.stockBillType, importer.headers))
		}

		rowProvider := getRowItemByIndex(row, getProviderIndex(importer.stockBillType, importer.headers))
		if rowProvider != provider {
			importer.AddFailedRow(row, fmt.Sprintf(NOT_UNIQUE_ERROR_FORMAT, importer.headers[getProviderIndex(importer.stockBillType, importer.headers)]))
			continue
		}
		req.Provider = provider

		sku := strings.TrimSpace(getRowItemByIndex(row, getSkuIndex(importer.stockBillType, importer.headers)))
		skus := []string{sku}
		if sku == "" {
			skus = importer.externalSkuMap[strings.TrimSpace(getRowItemByIndex(row, getExternalSkuIndex(importer.stockBillType, importer.headers)))]
		}
		for _, sku = range skus {
			productStock := importer.storeProductStocksMap[sku]
			p := &pb_store_warehouse.StockBillProduct{
				Id:      productStock.ProductId.Hex(),
				Sku:     productStock.Sku,
				Count:   cast.ToUint64(getRowItemByIndexWithDefaultValue(row, getCountIndex(importer.stockBillType, importer.headers), "0")),
				StoreId: storeId,
			}
			if getRowItemByIndex(row, getPriceIndex(importer.stockBillType, importer.headers)) != "" {
				p.Price = cast.ToUint64(formatPrice(getRowItemByIndex(row, getPriceIndex(importer.stockBillType, importer.headers))))
			}
			req.Products = append(req.Products, p)
		}
	}

	if len(req.Products) > 0 {
		resp, err := ec_client.StoreWarehouseService.CreateStoreStockBill(ctx, req)
		if err != nil {
			return err
		}

		failedSkus := core_util.ExtractArrayStringField("Sku", resp.FailedProducts)
		if len(failedSkus) == 0 {
			return nil
		}
		for i := start; i <= end; i++ {
			row := rows[i]
			if core_util.StrInArray(getRowItemByIndex(row, getSkuIndex(importer.stockBillType, importer.headers)), &failedSkus) {
				importer.AddFailedRow(row, "出库数量不能大于可售库存")
			}
		}
	}
	return nil
}

func getChangedAt(dateStr, timeStr string) string {
	// 前面已经检查过时间格式，这里直接忽略报错
	dateTime, _ := time.Parse(DATE_LAYOUT, dateStr)
	t, _ := time.Parse(TIME_LAYOUT, timeStr)

	return time.Date(dateTime.Year(), dateTime.Month(), dateTime.Day(), t.Hour(), t.Minute(), t.Second(), 0, time.Local).Format(time.RFC3339)
}

func (importer *StockBillImporter) GetProductStocks(ctx context.Context, skus, externalSkus []string) {
	for i := range externalSkus {
		externalSkus[i] = strings.TrimSpace(externalSkus[i])
	}
	for i := range skus {
		skus[i] = strings.TrimSpace(skus[i])
	}
	skus = util.StrArrayDiff(skus, []string{""})
	if len(externalSkus) != 0 {
		var (
			err     error
			newSkus []string
		)
		newSkus, importer.externalSkuMap, err = service_store_warehouse.GetProductSkusByExternalSkus(ctx, externalSkus)
		if err != nil {
			log.Warn(ctx, "Failed to get skus by external skus", log.Fields{"errMsg": err.Error()})
			return
		}
		skus = append(skus, newSkus...)
		skus = core_util.StrArrayUnique(skus)
	}

	if len(skus) != 0 {
		productStocks, _ := model_store_warehouse.CStoreProductStock.GetBySkus(ctx, skus)
		importer.storeProductStocksMap = make(map[string]model_store_warehouse.StoreProductStock)
		for i, productStock := range productStocks {
			importer.storeProductStocksMap[productStock.Sku] = productStocks[i]
		}
	}
}

func (importer *StockBillImporter) InitFailedSheet() {
	failedSheet, _ := xlsx.NewFile().AddSheet("Sheet1")
	failedHeader := failedSheet.AddRow()
	for _, item := range importer.headers {
		failedHeader.AddCell().Value = item
	}
	failedHeader.AddCell().Value = "失败原因"
	importer.failedSheet = failedSheet
}

func (importer *StockBillImporter) ValidateRow(ctx context.Context, row []string) string {
	for i := 0; i < len(importer.headers); i++ {
		if i >= len(row) {
			row = append(row, "")
		}
		if err := importer.validFuncMap[i](importer, row, i); err != "" {
			return err
		}
	}
	return ""
}

func (importer *StockBillImporter) AddFailedRow(row []string, failedReason string) {
	failedRow := importer.failedSheet.AddRow()
	for i, data := range row {
		cell := failedRow.AddCell()
		cell.Value = data
		if i == getDateIndex(importer.stockBillType, importer.headers) {
			t, err := time.Parse(DATE_LAYOUT, data)
			if err == nil {
				// Excel 里日期存的是从 1900-01-01 计算的天数，Unix 时间戳是从 1970-01-01 计算的秒数，这里转换为天数再加上相差的 70 年
				cell.SetValue(t.Unix()/(24*60*60) + 25569)
				cell.SetFormat("yyyy/m/d")
			}
		}
		if i == getTimeIndex(importer.stockBillType, importer.headers) {
			if v, err := cast.ToFloat64E(data); data != "" && err == nil {
				if _, err = excelize.ExcelDateToTime(v, false); err == nil {
					cell.SetFloat(v)
					cell.SetFormat("[$-F400]h:mm:ss AM/PM")
				}
			}
		}
	}
	for {
		if len(failedRow.Cells) >= len(importer.headers)+1 {
			break
		}
		failedRow.AddCell()
	}
	failedRow.Cells[len(importer.headers)].Value = failedReason
}

func ValidateDate(importer *StockBillImporter, row []string, index int) string {
	if getRowItemByIndex(row, index) == "" {
		return fmt.Sprintf(REQUIRED_ERROR_FORMAT, importer.headers[index])
	}
	if _, err := time.Parse(DATE_LAYOUT, getRowItemByIndex(row, index)); err != nil {
		return fmt.Sprintf(INCORRECT_ERROR_FORMAT, importer.headers[index])
	}
	return ""
}

func ValidateTime(importer *StockBillImporter, row []string, index int) string {
	if getRowItemByIndex(row, index) == "" {
		return fmt.Sprintf(REQUIRED_ERROR_FORMAT, importer.headers[index])
	}
	if _, err := time.Parse(TIME_LAYOUT, strings.ToUpper(getRowItemByIndex(row, index))); err != nil {
		return fmt.Sprintf(INCORRECT_ERROR_FORMAT, importer.headers[index])
	}
	return ""
}

func ValidateOperator(importer *StockBillImporter, row []string, index int) string {
	if getRowItemByIndex(row, index) == "" {
		return fmt.Sprintf(REQUIRED_ERROR_FORMAT, importer.headers[index])
	}
	if !core_util.StrInArray(getRowItemByIndex(row, index), &importer.operators) {
		return fmt.Sprintf(NOT_EXISTS_ERROR_FORMAT, importer.headers[index])
	}
	return ""
}

func ValidateBusinessNumber(importer *StockBillImporter, row []string, index int) string {
	return ""
}

func ValidateStoreCode(importer *StockBillImporter, row []string, index int) string {
	storeCode := strings.TrimSpace(getRowItemByIndex(row, index))
	if storeCode == "" {
		return fmt.Sprintf(REQUIRED_ERROR_FORMAT, importer.headers[index])
	}
	return ""
}

func ValidateSku(importer *StockBillImporter, row []string, index int) string {
	sku := strings.TrimSpace(getRowItemByIndex(row, index))
	externalSku := strings.TrimSpace(getRowItemByIndex(row, getExternalSkuIndex(importer.stockBillType, importer.headers)))
	if sku == "" && externalSku == "" {
		return "SKU编号和外部SKU编号至少一个不能为空"
	}
	if sku != "" && externalSku != "" {
		return "SKU编号和外部SKU编号不能同时存在"
	}
	if sku != "" {
		if _, ok := importer.storeProductStocksMap[sku]; !ok {
			return fmt.Sprintf(NOT_EXISTS_ERROR_FORMAT, importer.headers[index])
		}
	}
	if externalSku != "" {
		if _, ok := importer.externalSkuMap[externalSku]; !ok {
			return fmt.Sprintf(NOT_EXISTS_ERROR_FORMAT, importer.headers[getExternalSkuIndex(importer.stockBillType, importer.headers)])
		}
	}
	return ""
}

func ValidateExternalSku(importer *StockBillImporter, row []string, index int) string {
	return ""
}

func ValidateCount(importer *StockBillImporter, row []string, index int) string {
	if getRowItemByIndex(row, index) == "" {
		return fmt.Sprintf(REQUIRED_ERROR_FORMAT, importer.headers[index])
	}
	count, err := cast.ToIntE(getRowItemByIndex(row, index))
	if err != nil || count <= 0 {
		return fmt.Sprintf(INCORRECT_ERROR_FORMAT, importer.headers[index])
	}
	return ""
}

func ValidatePrice(importer *StockBillImporter, row []string, index int) string {
	if getRowItemByIndex(row, index) == "" {
		return ""
	}
	price, err := strconv.ParseFloat(getRowItemByIndex(row, index), 64)
	if err != nil || price <= 0 || price >= 1000000 {
		return fmt.Sprintf(INCORRECT_ERROR_FORMAT, importer.headers[index])
	}
	return ""
}

func ValidateRemark(importer *StockBillImporter, row []string, index int) string {
	return ""
}

func ValidateProvider(importer *StockBillImporter, row []string, index int) string {
	return ""
}

func getProviderIndex(t string, headers []string) int {
	return core_util.IndexOfArray("供应商", headers)
}

func getDateIndex(t string, headers []string) int {
	if t == model_store_warehouse.TYPE_STOCK_BILL_IN {
		return core_util.IndexOfArray("入库日期", headers)
	} else {
		return core_util.IndexOfArray("出库日期", headers)
	}
}

func getTimeIndex(t string, headers []string) int {
	if t == model_store_warehouse.TYPE_STOCK_BILL_IN {
		return core_util.IndexOfArray("入库时间", headers)
	} else {
		return core_util.IndexOfArray("出库时间", headers)
	}
}

func getCountIndex(t string, headers []string) int {
	if t == model_store_warehouse.TYPE_STOCK_BILL_IN {
		return core_util.IndexOfArray("入库数量", headers)
	} else {
		return core_util.IndexOfArray("出库数量", headers)
	}
}

func getOperatorIndex(t string, headers []string) int {
	if t == model_store_warehouse.TYPE_STOCK_BILL_IN {
		return core_util.IndexOfArray("入库类型", headers)
	} else {
		return core_util.IndexOfArray("出库类型", headers)
	}
}

func getBusinessIndex(t string, headers []string) int {
	return core_util.IndexOfArray("业务编号", headers)
}

func getStoreCodeIndex(t string, headers []string) int {
	return core_util.IndexOfArray("门店编号", headers)
}

func getSkuIndex(t string, headers []string) int {
	return core_util.IndexOfArray("SKU编号", headers)
}

func getExternalSkuIndex(t string, headers []string) int {
	return core_util.IndexOfArray("外部SKU编号", headers)
}

func getPriceIndex(t string, headers []string) int {
	return core_util.IndexOfArray("成本价", headers)
}

func getRemarkIndex(t string, headers []string) int {
	return core_util.IndexOfArray("备注", headers)
}

func genValidFuncMapByType(t string) map[int]ValidColFunc {
	headers := service_store_warehouse.GetHeader(t)
	return map[int]ValidColFunc{
		getProviderIndex(t, headers):    ValidateProvider,
		getDateIndex(t, headers):        ValidateDate,
		getTimeIndex(t, headers):        ValidateTime,
		getOperatorIndex(t, headers):    ValidateOperator,
		getBusinessIndex(t, headers):    ValidateBusinessNumber,
		getStoreCodeIndex(t, headers):   ValidateStoreCode,
		getSkuIndex(t, headers):         ValidateSku,
		getCountIndex(t, headers):       ValidateCount,
		getExternalSkuIndex(t, headers): ValidateExternalSku,
		getPriceIndex(t, headers):       ValidatePrice,
		getRemarkIndex(t, headers):      ValidateRemark,
	}
}

func getOperatorsByType(t string) []string {
	if t == model_store_warehouse.TYPE_STOCK_BILL_IN {
		return []string{"采购入库", "其他原因"}
	}
	return []string{"退货给供应商", "其他原因"}
}

func transOperator(o string) string {
	transMap := map[string]string{
		"采购入库":   "purchase_in",
		"其他原因":   "others",
		"退货给供应商": "refund_to_supplier",
	}
	return transMap[o]
}

func getFailedFileName(t string) string {
	fileName := STOCK_IN_FAILED_FILE_NAME
	if t == model_store_warehouse.TYPE_STOCK_BILL_OUT {
		fileName = STOCK_OUT_FAILED_FILE_NAME
	}
	return fmt.Sprintf(fileName, util.GetJobTimestamp(time.Now()))
}

func formatPrice(price string) string {
	if price != "" {
		arr := strings.Split(price, ".")
		if len(arr) > 1 {
			if len(arr[1]) < 2 {
				arr[1] = arr[1] + strings.Repeat("0", 2-len(arr[1]))
			} else {
				arr[1] = arr[1][:2]
			}
			integerPart := strings.TrimLeft(arr[0], "0")
			if integerPart == "" {
				return arr[1]
			}
			price = integerPart + arr[1]
		} else {
			integerPart := strings.TrimLeft(arr[0], "0")
			if integerPart == "" {
				integerPart = "0"
			}
			price = integerPart + "00"
		}
		return price
	}
	return price
}
