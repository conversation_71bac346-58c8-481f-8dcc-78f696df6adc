package member

import (
	"context"
	core_component "mairpc/core/component"
	"mairpc/core/extension"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	ec_member_service "mairpc/service/ec/service/member"
	"mairpc/service/share/component"
	"mairpc/service/share/util"
	"strings"
	"sync"
	"time"

	"github.com/spf13/cobra"
)

type MemberTagsContinuouslySyncer struct {
	CorpId    string
	ChannelId string

	ProduceCompleted    bool
	IsException         bool
	IsApiFreqOutOfLimit bool

	InfoLogsToEditWechatworkTagsReq chan component.EditMemberTagsRequest
	EditWechatworkTagsReq           chan component.EditMemberTagsRequest
	InfoLogs                        chan MemberInfoLogs

	WechatworkConsumerNum int // 消费 EditWechatworkTagsReq 的协程数，最末端的消费者，需要更多数量，保证定时放入的固定数量的请求能被全部消耗
	InfoLogConsumerNum    int // 处理 infoLogs 的任务协程数
	BatchSize             int // 和 EditWechatworkTagsReq 的容量一致，用于定时获取固定数量的 EditMemberTagsRequest 放入 EditWechatworkTagsReq 中

	Wg             sync.WaitGroup
	SameMemberLock sync.Map

	FirstCursor string // 处理的第一条 memberInfoLog 的 id
	LastCursor  string // 处理的最后一条 memberInfoLog 的 id
}

type MemberInfoLogs struct {
	MemberId string
	InfoLogs []*ec_member_service.MemberTagsChangeDetail
	Order    int
}

func init() {
	MemberCmd.AddCommand(syncMemberTagsContinuously)
}

var syncMemberTagsContinuously = &cobra.Command{
	Use: "syncMemberTagsContinuously",
	RunE: func(cmd *cobra.Command, args []string) error {
		// 此 job 一般有定时任务触发，为了方便区分定时任务中触发的不同租户日志，这里替换 ctx 中的 reqId
		ctx := core_util.CtxWithRequestID(util.GetContextInJob(args), core_util.NewUUIDWithServiceName())
		startAt := time.Now()
		accountId := util.GetAccountId(ctx)
		// 传入每小时需要处理的数据量，接口存在速率限制，消耗最多的是获取外部联系人详情接口，限制不超过 15w/h
		// 这里为了稳妥，考虑计算会有一次外部联系人接口调用，企微变更后产生的回调又有一次外部联系人接口调用
		// 因此设定速率为每小时处理 4w 条变更
		hourlyLimit := 40000
		// 健合 ANC-DTC 设定速率为每小时 1w
		if util.StrInArray(accountId, &[]string{
			"63745a6313aa7a746d47bbd2", // ANC-DTC
			"63563d405d12704b7f295692", // 妈妈100
		}) {
			hourlyLimit = 10000
		}
		// 味全设定速率为每小时 2w
		if accountId == "609897549104ab069e4589ac" {
			hourlyLimit = 20000
		}
		syncer := initMemberTagsContinuouslySyncer(hourlyLimit)
		lockKey := ec_member_service.GetSyncMemberTagsContinuouslyLockKey(ctx)
		ok, _ := extension.RedisClient.SetNX(lockKey, "1", ec_member_service.SYNC_MEMBER_TAGS_CONTINUOUSLY_DURATION)
		if !ok {
			return nil
		}
		defer extension.RedisClient.Del(lockKey)
		syncer.consumeEditWechatworkTagsRequest(ctx)
		syncer.distributeEditWechatworkTagsRequest(ctx)
		syncer.consumeMemberInfoLogs(ctx)
		syncer.produceMemberInfoLogs(ctx)
		syncer.Wg.Wait()
		syncer.afterProcess()
		log.Warn(ctx, "Sync member tags continuously", log.Fields{
			"firstCursor":         syncer.FirstCursor,
			"lastCursor":          syncer.LastCursor,
			"startAt":             startAt.Format(core_util.COMMON_TIME_LAYOUT),
			"endAt":               time.Now().Format(core_util.COMMON_TIME_LAYOUT),
			"isException":         syncer.IsException,
			"isApiFreqOutOfLimit": syncer.IsApiFreqOutOfLimit,
		})
		return nil
	},
}

func initMemberTagsContinuouslySyncer(hourlyRate int) *MemberTagsContinuouslySyncer {
	// 此 job 获取标签变更记录的频率固定为每 5s 一次，即每小时固定请求标签变更记录 720 次，因此每次获取的变更记录数量为 hourlyRate/720
	// 目前看来同步比较慢的情况下处理一个客户需要 2s 左右，为了保证消费速率大于等于生产速率我们预期都按照每条数据 2s 来处理
	// 每批数据在下一批数据获取前大概能执行两轮，需要保证并发量需要维持在每批获取到的变更记录数量的一半，即 (hourlyRate/720)/2
	batchSize := hourlyRate / (60 * 60 / 5)
	consumerNum := batchSize / 2
	result := &MemberTagsContinuouslySyncer{
		InfoLogConsumerNum:              consumerNum,
		WechatworkConsumerNum:           consumerNum,
		BatchSize:                       batchSize,
		InfoLogs:                        make(chan MemberInfoLogs, batchSize),
		InfoLogsToEditWechatworkTagsReq: make(chan component.EditMemberTagsRequest, batchSize),
		EditWechatworkTagsReq:           make(chan component.EditMemberTagsRequest, batchSize),
		Wg:                              sync.WaitGroup{},
		SameMemberLock:                  sync.Map{},
	}
	return result
}

// 向 InfoLogs 管道中放入客户标签变更记录
func (syncer *MemberTagsContinuouslySyncer) produceMemberInfoLogs(ctx context.Context) {
	syncer.Wg.Add(1)
	core_component.GO(ctx, func(ctx context.Context) {
		defer syncer.Wg.Done()
		// 初始化 InfoLogs 管道，缓存大小为 150
		req, err := ec_member_service.GetGetMemberInfoLogsRequest(ctx)
		if err != nil {
			return
		}
		syncer.FirstCursor = req.Cursor
		req.Limit = int64(syncer.BatchSize)
		defer func() {
			ec_member_service.SetMemberTagsNewCursor(ctx, syncer.LastCursor)
			syncer.ProduceCompleted = true
		}()

		var (
			nextPushTime           time.Time
			getMemberInfoLogsEnded bool
		)
		// 用于保存由于同步时客户被锁定未同步的标签删除记录
		removeChangeLockedMap := map[string][]*ec_member_service.MemberTagsChangeDetail{}
		for {
			// 当后续流程中发生关键步骤错误时需要退出
			if syncer.IsException {
				return
			}

			// 先处理上一轮由于同步时客户被锁定未同步的标签删除记录
			removeChangeLockedMap = syncer.pushMemberInfoLogsMap(removeChangeLockedMap)
			if getMemberInfoLogsEnded {
				// MemberInfoLogsMap 已经取完，后续只处理剩余被锁定未同步的标签删除记录
				if len(removeChangeLockedMap) == 0 {
					return
				}
				continue
			}

			memberInfoLogsMap, newCursor, err := ec_member_service.GetMemberInfoLogsMap(ctx, req)
			if err != nil {
				syncer.IsException = true
				log.Warn(ctx, "Get member info logs map exception", log.Fields{
					"errMsg": err.Error(),
				})
				return
			}
			if newCursor == "" {
				getMemberInfoLogsEnded = true
				continue
			}

			if nextPushTime.IsZero() {
				nextPushTime = time.Now()
			}
			// 放入 infoLog 也需要限制速度，因为一条 infoLog 可能并不会产生编辑企业微信标签请求
			// 但一定会产生一条获取外部联系人详情的请求，不限制的话可能会超过接口频率限制
			time.Sleep(nextPushTime.Sub(time.Now()))

			// 每 5s 写入一批数据，每批数据 150 条以内
			syncer.LastCursor = newCursor
			req.Cursor = newCursor
			lockedMap := syncer.pushMemberInfoLogsMap(memberInfoLogsMap)
			// 合并由于同步时客户被锁定未同步的标签删除记录
			for memberId, infoLogs := range lockedMap {
				existedInfoLogs, ok := removeChangeLockedMap[memberId]
				if !ok {
					removeChangeLockedMap[memberId] = infoLogs
					continue
				}
				existedInfoLogs = append(existedInfoLogs, infoLogs...)
				removeChangeLockedMap[memberId] = existedInfoLogs
			}

			// 每一轮都需要刷新 redis 中的 cursor，防止 job 意外终止来不及保存 cursor
			ec_member_service.SetMemberTagsNewCursor(ctx, newCursor)
			nextPushTime = nextPushTime.Add(time.Second * 5)
		}
	})
}

// 消费管道中的 MemberInfoLogs，负责把 MemberInfoLogs 生成 EditMemberTagsRequest
func (syncer *MemberTagsContinuouslySyncer) consumeMemberInfoLogs(ctx context.Context) {
	for i := 0; i < syncer.InfoLogConsumerNum; i++ {
		syncer.Wg.Add(1)
		core_component.GO(ctx, func(ctx context.Context) {
			defer syncer.Wg.Done()
			for {
				if ec_member_service.IsExternalUserApiOutOfLimit(ctx) {
					time.Sleep(time.Minute * 10)
					continue
				}
				infoLogs := syncer.getMemberInfoLogs()
				if infoLogs != nil {
					reqList := syncer.generateEditWechatworkTagsReqList(ctx, infoLogs)
					for i := range reqList {
						req := reqList[i]
						syncer.pushEditWechatworkTagsReqFromInfoLogConsumer(req)
					}
				}
				if infoLogs == nil && syncer.ProduceCompleted {
					break
				}
			}
		})
	}
	return
}

// 以固定速率(150条/5s，约 10w条/1h)分发企业微信标签更新请求
func (syncer *MemberTagsContinuouslySyncer) distributeEditWechatworkTagsRequest(ctx context.Context) {
	syncer.Wg.Add(1)
	core_component.GO(ctx, func(ctx context.Context) {
		defer syncer.Wg.Done()

		var nextPushTime time.Time
		for {
			reqList := []component.EditMemberTagsRequest{}
			for i := 0; i < syncer.BatchSize; i++ {
				req := syncer.getEditWechatworkTagsReqFromInfoLogConsumer()
				if req != nil {
					reqList = append(reqList, *req)
					continue
				}
				if syncer.ProduceCompleted {
					break
				}
			}
			if len(reqList) == 0 && syncer.ProduceCompleted {
				return
			}
			if nextPushTime.IsZero() {
				nextPushTime = time.Now()
			}
			// 等待至 nextPushTime 的时间点再往管道中放入数据
			// 即使 nextPushTime.Sub(time.Now()) 的结果是负数也不会出问题，相当于无等待时间
			time.Sleep(nextPushTime.Sub(time.Now()))
			for i := range reqList {
				req := reqList[i]
				syncer.pushEditWechatworkTagsRequest(req)
			}
			nextPushTime = nextPushTime.Add(time.Second * 5)
		}
	})
}

// 消费编辑企业微信标签请求
func (syncer *MemberTagsContinuouslySyncer) consumeEditWechatworkTagsRequest(ctx context.Context) {
	for i := 0; i < syncer.WechatworkConsumerNum; i++ {
		syncer.Wg.Add(1)
		core_component.GO(ctx, func(ctx context.Context) {
			defer syncer.Wg.Done()
			for {
				req := syncer.getEditWechatworkTagsRequest()
				if req != nil {
					err := component.WeConnect.EditMemberTags(ctx, syncer.CorpId, syncer.ChannelId, "", *req)
					if err != nil {
						log.Warn(ctx, "Failed to edit member tags", log.Fields{
							"corpId":       syncer.CorpId,
							"req":          req,
							"errorMessage": err.Error(),
						})
					}
					continue
				}
				if syncer.ProduceCompleted {
					return
				}
			}
		})
	}
}

func (syncer *MemberTagsContinuouslySyncer) lockMember(memberId string) bool {
	_, ok := syncer.SameMemberLock.LoadOrStore(memberId, "1")
	return !ok
}

func (syncer *MemberTagsContinuouslySyncer) unlockMember(memberId string) {
	syncer.SameMemberLock.Delete(memberId)
}

func (syncer *MemberTagsContinuouslySyncer) pushMemberInfoLogsMap(memberInfoLogsMap map[string][]*ec_member_service.MemberTagsChangeDetail) map[string][]*ec_member_service.MemberTagsChangeDetail {
	// 用于保存由于同步时客户被锁定未同步的标签删除记录
	removeChangeLockedMap := map[string][]*ec_member_service.MemberTagsChangeDetail{}
	for memberId, infoLogs := range memberInfoLogsMap {
		// 说明该 member 正在同步中
		if !syncer.lockMember(memberId) {
			if removeLogs := extractRemoveTagsChange(infoLogs); len(removeLogs) > 0 {
				// 标签删除同步依赖于删除记录，因此不能直接丢弃，需要存起来放在下一轮继续同步
				removeChangeLockedMap[memberId] = removeLogs
			}
			continue
		}
		syncer.pushMemberInfoLogs(MemberInfoLogs{
			MemberId: memberId,
			InfoLogs: infoLogs,
		})
	}
	return removeChangeLockedMap
}

func (syncer *MemberTagsContinuouslySyncer) pushEditWechatworkTagsRequest(req component.EditMemberTagsRequest) {
	syncer.EditWechatworkTagsReq <- req
}

func (syncer *MemberTagsContinuouslySyncer) getEditWechatworkTagsRequest() *component.EditMemberTagsRequest {
	select {
	case req := <-syncer.EditWechatworkTagsReq:
		return &req
	case <-time.After(time.Second * 5):
		return nil
	}
}

func (syncer *MemberTagsContinuouslySyncer) pushEditWechatworkTagsReqFromInfoLogConsumer(req component.EditMemberTagsRequest) {
	syncer.InfoLogsToEditWechatworkTagsReq <- req
}

func (syncer *MemberTagsContinuouslySyncer) getEditWechatworkTagsReqFromInfoLogConsumer() *component.EditMemberTagsRequest {
	select {
	case req := <-syncer.InfoLogsToEditWechatworkTagsReq:
		return &req
	case <-time.After(time.Second * 5):
		return nil
	}
}

func (syncer *MemberTagsContinuouslySyncer) pushMemberInfoLogs(infoLogs MemberInfoLogs) {
	// 向管道中写入需要同步的客户
	syncer.InfoLogs <- infoLogs
}

func (syncer *MemberTagsContinuouslySyncer) getMemberInfoLogs() *MemberInfoLogs {
	select {
	case infoLogs := <-syncer.InfoLogs:
		return &infoLogs
	case <-time.After(time.Second * 5):
		return nil
	}
}

func (syncer *MemberTagsContinuouslySyncer) generateEditWechatworkTagsReqList(ctx context.Context, infoLogs *MemberInfoLogs) []component.EditMemberTagsRequest {
	// 此方法在客户与企业微信差异计算完成后即可释放客户锁
	defer syncer.unlockMember(infoLogs.MemberId)
	var result []component.EditMemberTagsRequest
	tagsSyncer := ec_member_service.InitTagsSyncer(ctx, false)
	if tagsSyncer == nil {
		log.Warn(ctx, "Init tags syncer exception", log.Fields{})
		syncer.IsException = true
		return result
	}
	if syncer.ChannelId == "" {
		syncer.ChannelId = tagsSyncer.ChannelId
	}
	if syncer.CorpId == "" {
		syncer.CorpId = tagsSyncer.CorpId
	}
	memberTagsSyncer, err := tagsSyncer.InitMemberTagsSynchronizer(ctx, ec_member_service.SyncMemberTagsParams{
		MemberId:          infoLogs.MemberId,
		TagsChangeDetails: infoLogs.InfoLogs,
		FromWechatwork:    true,
		ToWechatwork:      true,
	})
	// 超限的话算异常
	if err != nil && strings.Contains(err.Error(), "out of limit") {
		syncer.IsException = true
		syncer.IsApiFreqOutOfLimit = true
		return result
	}
	if memberTagsSyncer == nil {
		return result
	}
	memberTagsSyncer.SyncFromWechatWork(ctx)
	return memberTagsSyncer.GetEditWechatworkTagsReqList(ctx)
}

func (syncer *MemberTagsContinuouslySyncer) afterProcess() {
	close(syncer.InfoLogs)
	close(syncer.InfoLogsToEditWechatworkTagsReq)
	close(syncer.EditWechatworkTagsReq)
}

func extractRemoveTagsChange(changes []*ec_member_service.MemberTagsChangeDetail) []*ec_member_service.MemberTagsChangeDetail {
	result := []*ec_member_service.MemberTagsChangeDetail{}
	for i, c := range changes {
		if len(util.StrArrayDiff(c.Tags.From, c.Tags.To)) > 0 {
			result = append(result, changes[i])
		}
	}
	return result
}
