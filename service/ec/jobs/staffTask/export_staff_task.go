package staff_task

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	proto_client "mairpc/proto/client"
	"mairpc/proto/common/origin"
	"mairpc/proto/common/request"
	pb_staff "mairpc/proto/ec/staff"
	pb_task "mairpc/proto/ec/staffTask"
	ec_store "mairpc/proto/ec/store"
	pb_store "mairpc/proto/ec/store"
	pb_member "mairpc/proto/member"
	ec_client "mairpc/service/ec/client"
	ec_model "mairpc/service/ec/model"
	groupchat_model "mairpc/service/ec/model/groupchat"
	task_model "mairpc/service/ec/model/staffTask"
	store_model "mairpc/service/ec/model/store"
	"mairpc/service/ec/service"
	share "mairpc/service/ec/service"
	ec_staffTask "mairpc/service/ec/service/staffTask"
	ec_share "mairpc/service/ec/share"
	"mairpc/service/share/constant"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"

	"github.com/spf13/cobra"
	"github.com/xuri/excelize/v2"
	"golang.org/x/net/context"
)

var (
	haveLinks         = false // 分析内容中是否包含链接
	havePoster        = false // 分享内容中是否包含海报
	haveCouponPoster  = false // 分享内容中是否包含优惠券海报
	haveProductPoster = false // 分享内容中是否包含商品海报
	haveOtherPoster   = false // 分享内容中是否包含其它海报

	haveWeappCards       = false // 分享内容中是否包含小程序卡片
	haveProductWeappCard = false // 分享内容中是否包含有商品信息的小程序卡片
	haveCouponWeappCard  = false // 分享内容中是否包含有卡片信息的小程序卡片
	haveOtherWeappCard   = false // 分享内容中是否包含有其他信息的小程序卡片
	isGroupMessage       = false // 是否是群群发任务
	isFeedback           = false // 是否是回访任务

	needDesensitizeMember = false
	needDesensitizeStaff  = false
)

func init() {
	StaffTaskCmd.AddCommand(exportStaffTask)
}

var exportStaffTask = &cobra.Command{
	Use: "exportStaffTask",
	RunE: func(cmd *cobra.Command, args []string) error {
		jobOptions := util.GetArgs(args)
		ctx := util.GetContextInJob(args)
		ctx = core_util.CtxWithReadSecondaryPreferred(ctx)

		jobId := core_util.GetSreadminJobName()
		err := job_util.BeginToExport(ctx, jobId)
		if err != nil {
			return err
		}

		req, err := getReqParams(jobOptions["request"])
		if err != nil {
			return err
		}

		job, _ := job_util.GetJobStatus(ctx, jobId)
		url, err := job_util.ExportFile(
			ctx,
			job.Name,
			func(f *os.File) error {
				xlsxWriter := core_util.NewXlsxWriter()
				sheet := xlsxWriter.NewSheet("数据概览")
				allTaskStats := []task_model.TaskStats{}
				taskStats := task_model.TaskStats{}
				if req.TaskStatsId != "" {
					taskStats, err = task_model.CTaskStats.GetById(ctx, util.ToMongoId(req.TaskStatsId))
					if err != nil {
						return err
					}
					allTaskStats = append(allTaskStats, taskStats)
				}

				task := task_model.Task{}
				if taskStats.Task.Id.Valid() {
					task = taskStats.Task
				} else {
					err = task.GetById(ctx, bson.ObjectIdHex(req.TaskId))
					if err != nil {
						return err
					}
				}
				setGlobalVariableValue(task)
				if len(req.TaskStatsIds) > 0 {
					allTaskStats, err = task_model.CTaskStats.GetByIds(ctx, util.ToMongoIds(req.TaskStatsIds))
					if err != nil {
						return err
					}
				}
				if req.TaskStatsId == "" && len(req.TaskStatsIds) == 0 {
					allTaskStats, err = task_model.CTaskStats.GetAllByTaskId(ctx, util.ToMongoId(req.TaskId))
					if err != nil {
						return err
					}
				}

				// 判断敏感信息权限
				needDesensitizeMember, _ = ec_share.NeedDesensitize(ctx)
				needDesensitizeStaff, _ = ec_share.NeedDesensitizeStaffInfo(ctx)

				taskStats.FormatTask(ctx, &task, req.DistributorIds)
				// 导出任务基本信息
				exportTaskInfo(ctx, sheet, task, allTaskStats, req)
				// 导出任务数据概览
				exportTaskStatsOverview(ctx, allTaskStats, sheet, req)
				if req.TaskStatsId == "" {
					sheet.Flush()
					sheet = xlsxWriter.NewSheet("任务进度")
				}
				// 导出任务进度 title
				exportStoreProgressRateTitle(sheet, req)

				isFirst := true
				for _, result := range allTaskStats {
					// 导出门店以及导购任务数据
					allStaffIds, staffNos, err := exportDistributorStats(ctx, result, req, sheet)
					if err != nil {
						return err
					}
					if len(staffNos) > 0 {
						allStaffIds = util.StrArrayUnique(allStaffIds)
						staffNos = util.StrArrayUnique(staffNos)
						// 导出客户信息
						exportMemberInfoSheet(ctx, result, task, allStaffIds, staffNos, xlsxWriter, isFirst)
						isFirst = false
					}
				}
				sheet.Flush()

				// 导出目标触达/未触达客户
				if req.TaskStatsId != "" {
					err = exportStaffTaskMember(ctx, xlsxWriter, task, allTaskStats[0], req)
					if err != nil {
						log.Warn(ctx, "export staff task member fail", log.Fields{
							"errMsg": err.Error(),
						})
					}
				}
				err = xlsxWriter.WriteToFile(f)
				if err != nil {
					log.Warn(ctx, "Save file error", log.Fields{
						"err":    err,
						"errMsg": err.Error(),
					})
				}
				err = f.Sync()
				if err != nil {
					log.Warn(ctx, "Sync file error", log.Fields{
						"err":    err,
						"errMsg": err.Error(),
					})
				}
				log.Warn(ctx, "End export staff task detail", log.Fields{
					"task": task,
				})
				return nil
			},
		)
		if err != nil {
			log.Warn(ctx, "ExportFile error", log.Fields{
				"err":    err,
				"errMsg": err.Error(),
			})
			return err
		}

		err = job_util.ExportSucceed(ctx, jobId, url, job.Name)
		if err != nil {
			log.Warn(ctx, "ExportSucceed error", log.Fields{
				"err":    err,
				"errMsg": err.Error(),
			})
			return err
		}

		return nil
	},
}

func exportStaffTaskMember(ctx context.Context, writer *core_util.XlsxWriter, task task_model.Task, taskStats task_model.TaskStats, req *pb_task.ListStaffTasksRequest) error {
	touchMemberSheet := writer.NewSheet("已触达客户")
	row := touchMemberSheet.AddRow()
	row.AddCells("已触达客户")
	row.Flush()
	addMemberInfoHeaderRow(touchMemberSheet, "已触达客户")
	unTouchMemberSheet := writer.NewSheet("未触达客户")
	row = unTouchMemberSheet.AddRow()
	row.AddCells("未触达客户")
	row.Flush()
	addMemberInfoHeaderRow(unTouchMemberSheet, "未触达客户")

	condition := ec_staffTask.GetListStaffTaskCondition(ctx, req)
	// 导出触达客户
	memberCondition := condition
	if task.UseFile {
		memberCondition["completedAt"] = bson.M{
			"$exists": true,
		}
		memberCondition["isDeleted"] = false
	} else {
		memberCondition["taskStatsId"] = util.ToMongoId(req.TaskStatsId)
		delete(condition, "startTime")
		delete(condition, "endTime")
		delete(memberCondition, "status")
		delete(memberCondition, "isDeleted")
	}

	touchMemberMap := map[string][]string{}
	channelId := ""
	channelDetail, err := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err == nil {
		channelId = channelDetail.ChannelId
	}
	for {
		taskTouchMembers := getTaskTouchMembers(ctx, task, memberCondition)
		if len(taskTouchMembers) == 0 {
			break
		}

		memberIds := core_util.ToStringArray(core_util.ExtractArrayField("MemberId", taskTouchMembers))
		memberMap := GetMemberMap(ctx, memberIds)
		storeIds := []string{}
		staffIds := core_util.ToStringArray(core_util.ExtractArrayField("StaffId", taskTouchMembers))
		staffMap, _ := ec_share.GetStaffMap(ctx, util.StrArrayUnique(staffIds))
		for _, staff := range staffMap {
			storeIds = append(storeIds, staff.DistributorIds...)
		}
		storeMap, _ := ec_share.GetStoreMap(ctx, util.StrArrayUnique(storeIds), []int32{3})
		for _, touchMember := range taskTouchMembers {
			if touchMember.HasTouched {
				storeNames, storeCodes := []string{}, []string{}
				phone, staffNo, staffName, unionId, nickname := "", "", "", "", ""
				// 客户 id 暂时没有明确的规则，脱敏规则保持和 unionId/openId 一致
				memberId := ec_share.DesensitizeUnionId(needDesensitizeMember, touchMember.MemberId.Hex())
				if value, ok := memberMap[touchMember.MemberId.Hex()]; ok {
					phone = ec_share.DesensitizeTel(needDesensitizeMember, value.Phone)
					nickname, unionId = GetMemberWecontactInfo(value, channelId)
					unionId = ec_share.DesensitizeUnionId(needDesensitizeMember, unionId)
					staff, ok := staffMap[touchMember.StaffId.Hex()]
					if ok {
						staffNo = ec_share.DesensitizeStaffNo(needDesensitizeStaff, staff.StaffNo)
						staffName = ec_share.DesensitizeName(needDesensitizeStaff, staff.Name)
						for _, storeId := range staff.DistributorIds {
							if storeValue, ok := storeMap[storeId]; ok {
								storeNames = append(storeNames, storeValue.Name)
								storeCodes = append(storeCodes, storeValue.Code)
							}
						}
					}
				}
				if phone == "" {
					phone = "-"
				}
				touchMemberRow := touchMemberSheet.AddRow()
				touchMemberRow.AddCells(nickname, unionId, memberId, phone, staffName, staffNo, strings.Join(storeNames, "、"), strings.Join(storeCodes, "、"), touchMember.Relation, touchMember.CreatedAt)
				touchMemberRow.Flush()
				touchMemberMap[touchMember.StaffId.Hex()] = append(touchMemberMap[touchMember.StaffId.Hex()], touchMember.MemberId.Hex())
			}
		}
		memberCondition["_id"] = bson.M{"$gt": taskTouchMembers[len(taskTouchMembers)-1].Id}
	}
	touchMemberSheet.Flush()
	// 导出未触达客户
	staffTaskCondition := condition
	delete(staffTaskCondition, "taskStatsId")
	delete(staffTaskCondition, "_id")
	staffTaskCondition["startTime"] = taskStats.StartTime
	staffTaskCondition["endTime"] = taskStats.EndTime
	staffTaskCondition["completedAt"] = bson.M{
		"$exists": false,
	}
	staffTaskCondition["isDeleted"] = false
	staffTaskPageSize := 10
	for {
		staffTasks, _ := task_model.CStaffTask.GetAllByConditionAndLimit(ctx, staffTaskCondition, []string{"_id"}, staffTaskPageSize)
		if len(staffTasks) == 0 {
			break
		}
		for _, staffTask := range staffTasks {
			memberIds := staffTask.GetMemberIds(ctx)
			if _, ok := touchMemberMap[staffTask.StaffId.Hex()]; !ok {
				exportUnTouchedMember(ctx, memberIds, unTouchMemberSheet, task, staffTask, channelId)
				continue
			}
			unTouchedMemberIds := util.StrArrayDiff(memberIds, touchMemberMap[staffTask.StaffId.Hex()])
			exportUnTouchedMember(ctx, unTouchedMemberIds, unTouchMemberSheet, task, staffTask, channelId)
			delete(touchMemberMap, staffTask.StaffId.Hex())
		}
		staffTaskCondition["_id"] = bson.M{"$gt": staffTasks[len(staffTasks)-1].Id}
	}
	unTouchMemberSheet.Flush()
	return nil
}

func getTaskTouchMembers(ctx context.Context, task task_model.Task, condition bson.M) []task_model.TaskTouchMember {
	if !task.UseFile {
		taskTouchMembers, _ := task_model.CTaskTouchMember.GetAllByConditionAndLimit(ctx, condition, []string{"_id"}, 100)
		return taskTouchMembers
	}

	staffTasks, _ := task_model.CStaffTask.GetAllByConditionAndLimit(ctx, condition, []string{"_id"}, 10)
	taskTouchMembers := []task_model.TaskTouchMember{}
	if len(staffTasks) == 0 {
		return taskTouchMembers
	}
	for _, staffTask := range staffTasks {
		touchMembers := staffTask.GetTaskTouchMembersFromFile(ctx)
		taskTouchMembers = append(taskTouchMembers, touchMembers...)
		log.Warn(ctx, "Get staffTask log", log.Fields{
			"staffId":      staffTask.StaffId.Hex(),
			"touchMembers": len(touchMembers),
		})
	}
	if len(taskTouchMembers) == 0 {
		return taskTouchMembers
	}
	// 将 staffTasks 最后一条数据的 id 赋值到 taskTouchMembers 最后一条数据上，以便下次循环可以正常查询 staffTask
	taskTouchMembers[len(taskTouchMembers)-1].Id = staffTasks[len(staffTasks)-1].Id
	return taskTouchMembers
}

func getReqParams(request interface{}) (*pb_task.ListStaffTasksRequest, error) {
	req := &pb_task.ListStaffTasksRequest{}
	byteData, err := json.Marshal(request)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(byteData, req)
	if err != nil {
		return nil, err
	}
	return req, nil
}

func getExcelizeCellStyle(haveBorder, isBold bool) *excelize.Style {
	style := &excelize.Style{}
	if haveBorder {
		style.Border = []excelize.Border{
			{
				Type:  "top",
				Color: "000000",
				Style: 1,
			},
			{
				Type:  "left",
				Color: "000000",
				Style: 1,
			},
			{
				Type:  "right",
				Color: "000000",
				Style: 1,
			},
			{
				Type:  "bottom",
				Color: "000000",
				Style: 1,
			},
		}
	}
	if isBold {
		style.Font = &excelize.Font{
			Bold: true,
		}
	}
	return style
}

func exportTaskInfo(ctx context.Context, sheet *core_util.XlsxSheet, task task_model.Task, taskStats []task_model.TaskStats, req *pb_task.ListStaffTasksRequest) {
	createdName := "-"
	if task.CreatedBy.Valid() {
		if resp, _ := share.GetUserById(ctx, task.CreatedBy.Hex()); resp != nil {
			createdName = resp.Name
		}
	}
	taskLeaderName := "-"
	taskLeaderNames := task.GetTaskLeaderNames(ctx)
	if len(taskLeaderNames) > 0 {
		taskLeaderName = strings.Join(taskLeaderNames, "、")
	}
	startTime, endTime, sendTime := "", "", ""
	if len(taskStats) == 0 {
		return
	}
	if len(taskStats) == 1 {
		startTime = taskStats[0].StartTime.Format("2006/01/02")
		endTime = taskStats[0].EndTime.Format("2006/01/02")
		sendTime = taskStats[0].CreatedAt.Format("2006-01-02 15:04:05")
	} else {
		startTime = taskStats[0].Task.SentAt.Format("2006/01/02")
		endTime = taskStats[len(taskStats)-1].Task.Time.EndTime.Format("2006/01/02")
		sendTime = taskStats[0].Task.SentAt.Format("2006-01-02 15:04:05")
	}
	taskInfoTitles := []string{"任务名称", "任务类型", "任务时间", "任务创建人", "任务负责人", "任务创建时间", "任务下发时间"}
	taskInfoMap := map[string]string{
		"任务名称":   task.Name,
		"任务类型":   task.GetTaskTypeStr(),
		"任务时间":   fmt.Sprintf("%s-%s", startTime, endTime),
		"任务创建人":  createdName,
		"任务负责人":  taskLeaderName,
		"任务创建时间": task.CreatedAt.Format("2006-01-02 15:04:05"),
		"任务下发时间": sendTime,
	}
	if req.TaskStatsId == "" {
		taskInfoTitles = []string{"任务名称", "任务类型", "任务周期", "任务时间", "任务创建人", "任务负责人", "任务创建时间", "任务下发时间"}
		taskTimeType := ""
		switch task.Time.Type {
		case task_model.STAFF_TASK_TIME_DAILY:
			taskTimeType = "每日重复"
			break
		case task_model.STAFF_TASK_TIME_WEEKLY:
			taskTimeType = "每周重复"
			break
		case task_model.STAFF_TASK_TIME_MONTHLY:
			taskTimeType = "每月重复"
			break
		}
		taskInfoMap["任务周期"] = taskTimeType
	}
	for _, title := range taskInfoTitles {
		row := sheet.AddRow()
		cell := row.AddCells(title)
		cell.SetStyle(&excelize.Style{
			Font: &excelize.Font{
				Bold: true,
			},
		})
		cell.AddCellsToNext(taskInfoMap[title])
		row.Flush()
	}
	if task.Type == task_model.STAFF_TASK_TYPE_GROUP_MESSAGE {
		isGroupMessage = true
	}
	if task.Type == task_model.STAFF_TASK_TYPE_FEEDBACK {
		isFeedback = true
	}
}

func exportTaskStatsOverview(ctx context.Context, taskStats []task_model.TaskStats, sheet *core_util.XlsxSheet, req *pb_task.ListStaffTasksRequest) {
	overviewTitles := []string{"数据概览", "执行导购数", "完成导购数", "当前完成率", "目标客户数", "非好友客户数", "拓客数", "触达客户数", "触达率"}

	if isGroupMessage {
		overviewTitles = []string{"数据概览", "执行导购数", "完成导购数", "当前完成率", "目标群/客户数", "触达群/客户数", "触达率"}
	}
	if req.TaskStatsId == "" {
		overviewTitles = getTaskstatsExtraTitles(taskStats)
	} else {
		if havePoster {
			overviewTitles = append(overviewTitles, "海报扫码客户数", "海报扫码率")
		}
		if haveWeappCards {
			overviewTitles = append(overviewTitles, "小程序打开客户数", "小程序打开率")
		}
		if haveProductPoster || haveProductWeappCard {
			overviewTitles = append(overviewTitles, "下单客户数", "下单率", "销售额")
		}
		if haveCouponPoster || haveCouponWeappCard {
			overviewTitles = append(overviewTitles, "领券客户数", "核券客户数", "核券率")
		}
		if haveLinks {
			overviewTitles = append(overviewTitles, "链接打开客户数", "链接打开率")
		}
	}
	if len(taskStats) == 1 {
		overviewMap := getOverviewMap(ctx, taskStats[0])
		overviewMap["数据概览"] = ""
		// 分隔行
		sheet.AddNewEmptyRow()
		for key, title := range overviewTitles {
			if key == 0 {
				row := sheet.AddRow()
				cell := row.AddCells(title)
				cell.SetStyle(&excelize.Style{
					Font: &excelize.Font{
						Bold: true,
					},
				})
				row.Flush()
			} else {
				row := sheet.AddRow()
				titleCell := row.AddCells(title)
				titleCell.SetStyle(getExcelizeCellStyle(true, true))

				valueCell := row.AddCells(overviewMap[title])
				valueCell.SetStyle(getExcelizeCellStyle(true, false))
				row.Flush()
			}
		}
	} else {
		// 分隔行
		sheet.AddNewEmptyRow()
		titleStyle := getExcelizeCellStyle(true, true)
		titleRow := sheet.AddRow()
		for _, title := range overviewTitles {
			titleCell := titleRow.AddCells(title)
			titleCell.SetStyle(titleStyle)
		}
		titleRow.Flush()
		for _, result := range taskStats {
			overviewMap := getOverviewMap(ctx, result)
			valueRow := sheet.AddRow()
			timeCell := valueRow.AddCells(fmt.Sprintf("%s至%s", result.StartTime.Format("2006-01-02 15:04:05"), result.Task.Time.EndTime.Format("2006-01-02 15:04:05")))
			timeCell.SetStyle(titleStyle)
			for _, title := range overviewTitles {
				if title == "任务时间" {
					continue
				}
				cellValue := "-"
				if value, ok := overviewMap[title]; ok {
					cellValue = value
				}
				valueCell := valueRow.AddCells(cellValue)
				valueCell.SetStyle(titleStyle)
			}
			valueRow.Flush()
		}
	}
}

func getTaskstatsExtraTitles(taskStats []task_model.TaskStats) []string {
	memberCountTitle := "目标客户数"
	touchMemberCountTitle := "触达客户数"
	if isGroupMessage {
		memberCountTitle = "目标群/客户数"
		touchMemberCountTitle = "触达群/客户数"
	}
	overviewTitles := []string{"任务时间", "执行导购数", "完成导购数", "当前完成率", memberCountTitle, touchMemberCountTitle, "触达率"}
	for _, result := range taskStats {
		setGlobalVariableValue(result.Task)
		if havePoster && !util.StrInArray("海报扫码客户数", &overviewTitles) && !util.StrInArray("海报扫码率", &overviewTitles) {
			overviewTitles = append(overviewTitles, "海报扫码客户数", "海报扫码率")
		}
		if haveWeappCards && !util.StrInArray("小程序打开客户数", &overviewTitles) && !util.StrInArray("小程序打开率", &overviewTitles) {
			overviewTitles = append(overviewTitles, "小程序打开客户数", "小程序打开率")
		}
		if haveProductPoster || haveProductWeappCard && !util.StrInArray("下单客户数", &overviewTitles) && !util.StrInArray("下单率", &overviewTitles) && !util.StrInArray("销售额", &overviewTitles) {
			overviewTitles = append(overviewTitles, "下单客户数", "下单率", "销售额")
		}
		if haveCouponPoster || haveCouponWeappCard && !util.StrInArray("领券客户数", &overviewTitles) && !util.StrInArray("核券客户数", &overviewTitles) && !util.StrInArray("核券率", &overviewTitles) {
			overviewTitles = append(overviewTitles, "领券客户数", "核券客户数", "核券率")
		}
		if haveLinks && !util.StrInArray("链接打开客户数", &overviewTitles) && !util.StrInArray("链接打开率", &overviewTitles) {
			overviewTitles = append(overviewTitles, "链接打开客户数", "链接打开率")
		}
	}
	return overviewTitles
}

func getOverviewMap(ctx context.Context, taskStats task_model.TaskStats) map[string]string {
	completedMemberCount := taskStats.CompletedMemberCount
	if taskStats.CompletedMemberCount > taskStats.MemberCount {
		completedMemberCount = taskStats.MemberCount
	}
	externalMemberCount, addedCount := getExternalMemberCount(ctx, taskStats.TaskId, "")

	overviewMap := map[string]string{
		"执行导购数":    fmt.Sprintf("%d", taskStats.TotalStaffCount),
		"完成导购数":    fmt.Sprintf("%d", taskStats.CompletedStaffCount),
		"当前完成率":    fmt.Sprintf("%.2f%%", share.CalcRate(int64(taskStats.CompletedStaffCount), int64(taskStats.TotalStaffCount))),
		"触达客户数":    fmt.Sprintf("%d", completedMemberCount),
		"触达率":      fmt.Sprintf("%.2f%%", share.CalcRate(int64(completedMemberCount), int64(taskStats.MemberCount))),
		"海报扫码客户数":  fmt.Sprintf("%d", taskStats.ScannedCount),
		"海报扫码率":    fmt.Sprintf("%.2f%%", share.CalcRate(int64(taskStats.ScannedCount), int64(taskStats.CompletedMemberCount))),
		"下单客户数":    fmt.Sprintf("%d", taskStats.OrderCount),
		"下单率":      fmt.Sprintf("%.2f%%", share.CalcRate(int64(taskStats.OrderCount), int64(taskStats.CompletedMemberCount))),
		"销售额":      fmt.Sprintf("%.2f", taskStats.OrderAmount/100),
		"领券客户数":    fmt.Sprintf("%d", taskStats.ReceivedCouponCount),
		"核券客户数":    fmt.Sprintf("%d", taskStats.RedeemedCouponCount),
		"核券率":      fmt.Sprintf("%.2f%%", share.CalcRate(int64(taskStats.RedeemedCouponCount), int64(taskStats.ReceivedCouponCount))),
		"链接打开客户数":  fmt.Sprintf("%d", taskStats.OpenedLinkCount),
		"链接打开率":    fmt.Sprintf("%.2f%%", share.CalcRate(int64(taskStats.OpenedLinkCount), int64(taskStats.CompletedMemberCount))),
		"小程序打开客户数": fmt.Sprintf("%d", taskStats.OpenedWechatappCount),
		"小程序打开率":   fmt.Sprintf("%.2f%%", share.CalcRate(int64(taskStats.OpenedWechatappCount), int64(taskStats.CompletedMemberCount))),
	}
	if isGroupMessage {
		overviewMap["目标群/客户数"] = fmt.Sprintf("%d/%d", taskStats.GroupchatCount, taskStats.MemberCount)
		overviewMap["触达群/客户数"] = fmt.Sprintf("%d/%d", taskStats.CompletedGroupchatCount, completedMemberCount)
	} else {
		overviewMap["目标客户数"] = fmt.Sprintf("%d", taskStats.MemberCount)
		overviewMap["触达客户数"] = fmt.Sprintf("%d", completedMemberCount)
		overviewMap["非好友客户数"] = fmt.Sprintf("%d", externalMemberCount)
		overviewMap["拓客数"] = fmt.Sprintf("%d", addedCount)
	}
	return overviewMap
}

// 导出任务进度 title
func exportStoreProgressRateTitle(sheet *core_util.XlsxSheet, req *pb_task.ListStaffTasksRequest) {
	if req.TaskStatsId != "" || len(req.TaskStatsIds) == 0 {
		// 分隔行
		sheet.AddNewEmptyRow()
	}
	barStyle := getExcelizeCellStyle(false, true)
	moduleRow := sheet.AddRow()
	moduleCell := moduleRow.AddCells("任务进度")
	moduleCell.SetStyle(barStyle)
	moduleRow.Flush()
	organizationName := "部门"
	titleStyle := getExcelizeCellStyle(true, true)
	row := sheet.AddRow()
	if req.TaskStatsId == "" {
		taskTimeCell := row.AddCells("任务时间")
		taskTimeCell.SetStyle(titleStyle)
	}
	distributorCell := row.AddCells(organizationName)
	distributorCell.SetStyle(titleStyle)

	distributorCompleteRateCell := row.AddCells(fmt.Sprintf("%s完成率", organizationName))
	distributorCompleteRateCell.SetStyle(titleStyle)

	nameCell := row.AddCells("门店名称")
	nameCell.SetStyle(titleStyle)

	completeRateCell := row.AddCells("完成率")
	completeRateCell.SetStyle(titleStyle)

	staffNameCell := row.AddCells("导购名称")
	staffNameCell.SetStyle(titleStyle)

	staffStatusCell := row.AddCells("导购状态")
	staffStatusCell.SetStyle(titleStyle)

	staffNoCell := row.AddCells("导购编号")
	staffNoCell.SetStyle(titleStyle)

	storeNameCell := row.AddCells("导购所属门店")
	storeNameCell.SetStyle(titleStyle)

	storeCodeCell := row.AddCells("门店编号")
	storeCodeCell.SetStyle(titleStyle)

	statusCell := row.AddCells("完成状态")
	statusCell.SetStyle(titleStyle)

	timeCell := row.AddCells("完成时间")
	timeCell.SetStyle(titleStyle)

	memberCountCell := &core_util.XlsxCell{}
	completedMemberCountCell := &core_util.XlsxCell{}
	externalMemberCountCell := &core_util.XlsxCell{}
	addCountCell := &core_util.XlsxCell{}
	completedMemberCountCellName := "触达客户数"
	if isGroupMessage {
		memberCountCell = row.AddCells("目标群/客户数")
		completedMemberCountCellName = "触达群/客户数"
	} else {
		memberCountCell = row.AddCells("目标客户数")
		externalMemberCountCell = row.AddCells("非好友客户数")
		addCountCell = row.AddCells("拓客数")
		externalMemberCountCell.SetStyle(titleStyle)
		addCountCell.SetStyle(titleStyle)
	}
	completedMemberCountCell = row.AddCells(completedMemberCountCellName)
	memberCountCell.SetStyle(titleStyle)
	completedMemberCountCell.SetStyle(titleStyle)
	completedMemberRateCell := row.AddCells("触达率")
	completedMemberRateCell.SetStyle(titleStyle)

	if havePoster {
		scannedCountCell := row.AddCells("海报扫码客户数")
		scannedCountCell.SetStyle(titleStyle)

		scannedCountRateCell := row.AddCells("海报扫码率")
		scannedCountRateCell.SetStyle(titleStyle)
	}

	if haveWeappCards {
		scannedCountCell := row.AddCells("小程序打开客户数")
		scannedCountCell.SetStyle(titleStyle)

		scannedCountRateCell := row.AddCells("小程序打开率")
		scannedCountRateCell.SetStyle(titleStyle)
	}

	if haveProductPoster || haveProductWeappCard {
		orderCountCell := row.AddCells("下单客户数")
		orderCountCell.SetStyle(titleStyle)

		orderRateCell := row.AddCells("下单率")
		orderRateCell.SetStyle(titleStyle)

		orderAmountCell := row.AddCells("销售额")
		orderAmountCell.SetStyle(titleStyle)
	}

	if haveCouponPoster || haveCouponWeappCard {
		receivedCouponCountCell := row.AddCells("领券客户数")
		receivedCouponCountCell.SetStyle(titleStyle)

		redeemedCouponCountCell := row.AddCells("核券客户数")
		redeemedCouponCountCell.SetStyle(titleStyle)

		redeemedCouponRateCell := row.AddCells("核券率")
		redeemedCouponRateCell.SetStyle(titleStyle)
	}

	if haveLinks {
		openedLinkCountCell := row.AddCells("链接打开客户数")
		openedLinkCountCell.SetStyle(titleStyle)

		openedLinkRateCell := row.AddCells("链接打开率")
		openedLinkRateCell.SetStyle(titleStyle)
	}
	row.Flush()
}

// 导出门店导购任务
func exportStoreAndStaffTask(ctx context.Context, taskStats task_model.TaskStats, distributorStats task_model.StoreStats, storeStatsArray []task_model.StoreStats, req *pb_task.ListStaffTasksRequest, sheet *core_util.XlsxSheet) ([]string, []string, error) {
	log.Warn(ctx, "Start exportStoreAndStaffTask", log.Fields{
		"distributorId": distributorStats.StoreId,
	})
	allStaffIds := []string{}
	allStaffNos := []string{}
	style := getExcelizeCellStyle(true, false)
	centerStyle := getExcelizeCellStyle(true, false)
	centerStyle.Alignment = &excelize.Alignment{
		Vertical:   "center",
		Horizontal: "center",
	}

	// 导出周期性任务时，需要再每一行第一个单元格添加任务事件
	isNeedAddTimeCell := false
	if req.TaskStatsId == "" {
		isNeedAddTimeCell = true
	}

	var (
		staffCount        = 0
		taskTimeMergeCell *core_util.XlsxCell
	)

	// 导出门店和导购数据
	isFirstRow := true
	for _, storeStats := range storeStatsArray {
		store, err := store_model.CStore.GetById(ctx, storeStats.StoreId)
		log.Warn(ctx, "Get store detail", log.Fields{
			"store": store,
			"err":   err,
		})
		if err != nil {
			continue
		}

		staffs, err := ec_model.ListStaffs(ctx, getListStaffsRequest(storeStats.StoreId, req))
		if err != nil || len(staffs) == 0 {
			log.Warn(ctx, "Get empty staff", log.Fields{
				"storeId": storeStats.StoreId.Hex(),
				"err":     err,
			})
			continue
		}

		staffIds := []string{}
		storeIds := []string{}
		staffMap := map[string]store_model.Staff{}
		for _, staff := range staffs {
			staffIds = append(staffIds, staff.Id.Hex())
			staffMap[staff.Id.Hex()] = staff
			storeIds = append(storeIds, util.MongoIdsToStrs(staff.StoreIds)...)
		}
		storeMap, _ := ec_share.GetStoreMap(ctx, util.StrArrayUnique(storeIds), []int32{3})
		staffTasks, err := getStaffTasks(ctx, taskStats, staffIds)
		if err != nil || len(staffTasks) == 0 {
			log.Warn(ctx, "Get empty staffTask", log.Fields{
				"storeId": storeStats.StoreId.Hex(),
				"err":     err,
			})
			continue
		}
		log.Warn(ctx, "Start export staff stats", log.Fields{
			"distributorId": distributorStats.StoreId,
			"storeId":       store.Id,
		})

		// 设置任务时间、零售公司、零售公司完成率
		var firstRow *core_util.XlsxRow
		distributorFirstCellValue := distributorStats.StoreName
		if isFirstRow {
			firstRow = sheet.AddRow()
			if isNeedAddTimeCell {
				centerStyle := getExcelizeCellStyle(true, false)
				centerStyle.Alignment = &excelize.Alignment{
					Horizontal: "Center",
					Vertical:   "Center",
				}
				taskTimeCell := firstRow.AddCells(fmt.Sprintf("%s-%s", taskStats.StartTime.Format("2006-01-02 15:04:05"), taskStats.EndTime.Format("2006-01-02 15:04:05")))
				taskTimeCell.SetStyle(centerStyle)
				taskTimeMergeCell = taskTimeCell
			}
			// jala 需要导出所有层级的部门，特殊处理下
			if util.GetAccountId(ctx) == "616199033e54b27c2534c222" {
				value := GetAllParentStoreName(ctx, distributorStats.StoreId.Hex())
				if value != "" {
					distributorFirstCellValue = fmt.Sprintf("%s/%s", distributorStats.StoreName, value)
				} else {
					distributorFirstCellValue = distributorStats.StoreName
				}
			}

			distributorFirstCell := firstRow.AddCells(distributorFirstCellValue)
			distributorFirstCell.SetStyle(centerStyle)
			distributorSeccondCell := firstRow.AddCells(fmt.Sprintf("%.2f%%", share.CalcRate(int64(distributorStats.CompletedStaffCount), int64(distributorStats.TotalStaffCount))))
			distributorSeccondCell.SetStyle(centerStyle)
		}

		for key, staffTask := range staffTasks {
			allStaffIds = append(allStaffIds, staffTask.StaffId.Hex())
			allStaffNos = append(allStaffNos, staffMap[staffTask.StaffId.Hex()].StaffNo)
			var row *core_util.XlsxRow
			if isFirstRow && key == 0 {
				row = firstRow
				isFirstRow = false
			} else {
				row = sheet.AddRow()
				if isNeedAddTimeCell {
					row.AddEmptyCell()
				}
				distributorNameCell := row.AddCells(distributorFirstCellValue)
				distributorNameCell.SetStyle(centerStyle)
				distributorCompleteRateCell := row.AddCells(fmt.Sprintf("%.2f%%", share.CalcRate(int64(distributorStats.CompletedStaffCount), int64(distributorStats.TotalStaffCount))))
				distributorCompleteRateCell.SetStyle(centerStyle)
			}

			var (
				storeName    = ""
				completeRate = ""
			)
			storeName = store.Name
			completeRate = "0.00%"
			if storeStats.TotalStaffCount > 0 && storeStats.CompletedStaffCount > 0 {
				completeRate = fmt.Sprintf("%.2f%%", share.CalcRate(int64(storeStats.CompletedStaffCount), int64(storeStats.TotalStaffCount)))
			}
			storeNameCell := row.AddCells(storeName)
			completeRateCell := row.AddCells(completeRate)
			storeNameCell.SetStyle(centerStyle)
			completeRateCell.SetStyle(centerStyle)

			nameCell := row.AddCells(ec_share.DesensitizeName(needDesensitizeStaff, staffMap[staffTask.StaffId.Hex()].Name))
			nameCell.SetStyle(style)

			statusStr := "在职"
			if staffMap[staffTask.StaffId.Hex()].Status != 1 {
				statusStr = "已离职"
			}
			staffStatusCell := row.AddCells(statusStr)
			staffStatusCell.SetStyle(style)

			staffNoCell := row.AddCells(ec_share.DesensitizeStaffNo(needDesensitizeStaff, staffMap[staffTask.StaffId.Hex()].StaffNo))
			staffNoCell.SetStyle(style)

			storeNames := []string{}
			storeCodes := []string{}
			for _, storeId := range staffMap[staffTask.StaffId.Hex()].StoreIds {
				if value, ok := storeMap[storeId.Hex()]; ok {
					storeNames = append(storeNames, value.Name)
					storeCodes = append(storeCodes, value.Code)
				}
			}
			staffStoreNamesCell := row.AddCells(strings.Join(storeNames, "、"))
			staffStoreNamesCell.SetStyle(style)
			staffStoreCodesCell := row.AddCells(strings.Join(storeCodes, "、"))
			staffStoreCodesCell.SetStyle(style)

			statusCellValue := "未完成"
			if !staffTask.CompletedAt.IsZero() {
				statusCellValue = "已完成"
			}
			statusCell := row.AddCells(statusCellValue)
			statusCell.SetStyle(style)

			timeCellValue := staffTask.CompletedAt.Format("2006-01-02 15:04:05")
			if staffTask.CompletedAt.IsZero() {
				timeCellValue = "-"
			}
			timeCell := row.AddCells(timeCellValue)
			timeCell.SetStyle(style)

			completedMemberCount := staffTask.CompletedMemberCount
			if staffTask.CompletedMemberCount > staffTask.MemberCount {
				completedMemberCount = staffTask.MemberCount
			}
			memberCountCell := &core_util.XlsxCell{}
			completedMemberCountCell := &core_util.XlsxCell{}
			externalMemberCountCell := &core_util.XlsxCell{}
			addCountCell := &core_util.XlsxCell{}
			completedMemberCountCellValue := fmt.Sprintf("%d", completedMemberCount)
			if isGroupMessage {
				memberCountCell = row.AddCells(fmt.Sprintf("%d/%d", staffTask.GroupchatCount, staffTask.MemberCount))
				completedMemberCountCellValue = fmt.Sprintf("%d/%d", staffTask.CompletedGroupchatCount, completedMemberCount)
			} else {
				memberCountCell = row.AddCells(fmt.Sprintf("%d", staffTask.MemberCount))
				externalMemberCount, addCount := getExternalMemberCount(ctx, staffTask.TaskId, staffTask.StaffId)
				externalMemberCountCell = row.AddCells(fmt.Sprintf("%d", externalMemberCount))
				addCountCell = row.AddCells(fmt.Sprintf("%d", addCount))
				externalMemberCountCell.SetStyle(style)
				addCountCell.SetStyle(style)
			}
			completedMemberCountCell = row.AddCells(completedMemberCountCellValue)
			memberCountCell.SetStyle(style)
			completedMemberCountCell.SetStyle(style)

			completedMemberRateCell := row.AddCells(fmt.Sprintf("%.2f%%", share.CalcRate(int64(completedMemberCount), int64(staffTask.MemberCount))))
			completedMemberRateCell.SetStyle(style)

			if havePoster {
				scannedCountCell := row.AddCells(fmt.Sprintf("%d", staffTask.ScannedCount))
				scannedCountCell.SetStyle(style)

				scannedCountRateCell := row.AddCells(fmt.Sprintf("%.2f%%", staffTask.ScannedRate*100))
				scannedCountRateCell.SetStyle(style)
			}

			if haveWeappCards {
				openedCountCell := row.AddCells(fmt.Sprintf("%d", staffTask.OpenedWechatappCount))
				openedCountCell.SetStyle(style)

				openedCountRateCell := row.AddCells(fmt.Sprintf("%.2f%%", staffTask.OpenedWechatappRate*100))
				openedCountRateCell.SetStyle(style)
			}

			if haveProductPoster || haveProductWeappCard {
				orderCountCell := row.AddCells(fmt.Sprintf("%d", staffTask.OrderCount))
				orderCountCell.SetStyle(style)

				orderRateCell := row.AddCells(fmt.Sprintf("%.2f%%", staffTask.OrderRate*100))
				orderRateCell.SetStyle(style)

				orderAmountCell := row.AddCells(fmt.Sprintf("%.2f", staffTask.OrderAmount*0.01))
				orderAmountCell.SetStyle(style)
			}

			if haveCouponPoster || haveCouponWeappCard {
				receivedCouponCountCell := row.AddCells(fmt.Sprintf("%d", staffTask.ReceivedCouponCount))
				receivedCouponCountCell.SetStyle(style)

				redeemedCouponCountCell := row.AddCells(fmt.Sprintf("%d", staffTask.RedeemedCouponCount))
				redeemedCouponCountCell.SetStyle(style)

				redeemedCouponRateCell := row.AddCells(fmt.Sprintf("%.2f%%", staffTask.RedeemedCouponRate*100))
				redeemedCouponRateCell.SetStyle(style)
			}

			if haveLinks {
				openedLinkCountCell := row.AddCells(fmt.Sprintf("%d", staffTask.OpenedLinkCount))
				openedLinkCountCell.SetStyle(style)

				openedLinkRateCell := row.AddCells(fmt.Sprintf("%.2f%%", staffTask.OpenedLinkRate*100))
				openedLinkRateCell.SetStyle(style)
			}
			if row != nil {
				row.Flush()
			}
		}

		staffCount += len(staffTasks)
	}
	if staffCount > 1 {
		if isNeedAddTimeCell {
			taskTimeMergeCell.MergeCells(0, staffCount-1)
		}
	}
	log.Warn(ctx, "End exportStoreAndStaffTask", log.Fields{
		"distributorId": distributorStats.StoreId,
	})
	return allStaffIds, allStaffNos, nil
}

// 获取需要导出的门店 id
func getStoreIds(req *pb_task.ListStaffTasksRequest) []string {
	storeIds := req.StoreIds
	if len(req.StoreIdAndStaffIds) > 0 {
		for _, value := range req.StoreIdAndStaffIds {
			storeIds = append(storeIds, value.StoreId)
		}
	}
	return storeIds
}

func getListStaffsRequest(storeId bson.ObjectId, req *pb_task.ListStaffTasksRequest) *pb_store.ListOriginalStaffsRequest {
	request := &pb_store.ListOriginalStaffsRequest{
		StoreIds: []string{storeId.Hex()},
		Source:   store_model.STORE_SOURCE_WECHATWORK,
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: 1000,
		},
	}
	if req.StaffName != "" {
		request.SearchKey = req.StaffName
	}
	for _, value := range req.StoreIdAndStaffIds {
		if storeId.Hex() == value.StoreId {
			request.StaffIds = value.StaffIds
		}
	}
	return request
}

func getStaffTasks(ctx context.Context, taskStats task_model.TaskStats, staffIds []string) ([]task_model.StaffTask, error) {
	staffTaskCondition := bson.M{
		"staffId":   bson.M{"$in": util.ToMongoIds(staffIds)},
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"taskId":    taskStats.TaskId,
		"startTime": taskStats.StartTime,
		"endTime":   taskStats.EndTime,
		"isDeleted": false,
	}
	staffTasks, err := task_model.CStaffTask.FindAll(ctx, staffTaskCondition, []string{})
	if err != nil {
		return []task_model.StaffTask{}, err
	}
	return staffTasks, nil
}

/*
 * 导出文件右边客户信息 sheet，数据源为 taskTouchMember
 * 1. 除群群发任务，需要根据 staffTask 中缓存的目标客户（memberIds）筛选
 * 2. 群群发任务，规则是"按群主时"，则需要判断客户是否在群主为导购的群内，规则是按群组时，判断客户是否在规则群和导购管理的群的交集内
 */
func exportMemberInfoSheet(ctx context.Context, taskStats task_model.TaskStats, task task_model.Task, staffIds, staffNos []string, writer *core_util.XlsxWriter, isFirst bool) error {
	log.Warn(ctx, "Start exportMemberInfoSheet", log.Fields{
		"taskId":   task.Id,
		"staffIds": staffIds,
		"staffNos": staffNos,
	})
	titleStyle := getExcelizeCellStyle(false, true)
	var (
		hasScannedSheet        *core_util.XlsxSheet
		hasOrderedSheet        *core_util.XlsxSheet
		hasReceivedCouponSheet *core_util.XlsxSheet
		hasRedeemedCouponSheet *core_util.XlsxSheet
		hasOpenedLinkSheet     *core_util.XlsxSheet
		hasOpenedWeappSheet    *core_util.XlsxSheet
		addedMemberSheet       *core_util.XlsxSheet
	)

	defer func() {
		if hasScannedSheet != nil {
			hasScannedSheet.Flush()
		}
		if hasOrderedSheet != nil {
			hasOrderedSheet.Flush()
		}
		if hasReceivedCouponSheet != nil {
			hasReceivedCouponSheet.Flush()
		}
		if hasRedeemedCouponSheet != nil {
			hasRedeemedCouponSheet.Flush()
		}
		if hasOpenedLinkSheet != nil {
			hasOpenedLinkSheet.Flush()
		}
		if hasOpenedWeappSheet != nil {
			hasOpenedWeappSheet.Flush()
		}
		if addedMemberSheet != nil {
			addedMemberSheet.Flush()
		}
	}()

	if isFirst && havePoster {
		hasScannedSheet = writer.NewSheet("海报扫码客户")
		if task.Time.Type == "" {
			writeStatsTime(hasScannedSheet, task.Time)
		}
		titleRow := hasScannedSheet.AddRow()
		titleCell := titleRow.AddCells("海报扫码客户")
		titleCell.SetStyle(titleStyle)
		titleRow.Flush()
		addMemberInfoHeaderRow(hasScannedSheet, "海报扫码客户")
	}

	if isFirst && (haveProductPoster || haveProductWeappCard) {
		hasOrderedSheet = writer.NewSheet("下单客户")
		if task.Time.Type == "" {
			writeStatsTime(hasOrderedSheet, task.Time)
		}
		titleRow := hasOrderedSheet.AddRow()
		titleCell := titleRow.AddCells("下单客户")
		titleCell.SetStyle(titleStyle)
		titleRow.Flush()
		addMemberInfoHeaderRow(hasOrderedSheet, "下单客户")
	}

	if isFirst && (haveCouponPoster || haveCouponWeappCard) {
		hasReceivedCouponSheet = writer.NewSheet("领券客户")
		if task.Time.Type == "" {
			writeStatsTime(hasReceivedCouponSheet, task.Time)
		}
		titleRow := hasReceivedCouponSheet.AddRow()
		titleCell := titleRow.AddCells("领券客户")
		titleCell.SetStyle(titleStyle)
		titleRow.Flush()
		addMemberInfoHeaderRow(hasReceivedCouponSheet, "领券客户")

		hasRedeemedCouponSheet = writer.NewSheet("核券客户")
		if task.Time.Type == "" {
			writeStatsTime(hasRedeemedCouponSheet, task.Time)
		}
		titleRow = hasRedeemedCouponSheet.AddRow()
		titleCell = titleRow.AddCells("核券客户")
		titleCell.SetStyle(titleStyle)
		titleRow.Flush()
		addMemberInfoHeaderRow(hasRedeemedCouponSheet, "核券客户")
	}

	if isFirst && haveLinks {
		hasOpenedLinkSheet = writer.NewSheet("链接打开客户")
		if task.Time.Type == "" {
			writeStatsTime(hasOpenedLinkSheet, task.Time)
		}
		titleRow := hasOpenedLinkSheet.AddRow()
		titleCell := titleRow.AddCells("链接打开客户")
		titleCell.SetStyle(titleStyle)
		titleRow.Flush()
		addMemberInfoHeaderRow(hasOpenedLinkSheet, "链接打开客户")
	}

	if isFirst && haveWeappCards {
		hasOpenedWeappSheet = writer.NewSheet("小程序打开客户")
		if task.Time.Type == "" {
			writeStatsTime(hasOpenedWeappSheet, task.Time)
		}
		titleRow := hasOpenedWeappSheet.AddRow()
		titleCell := titleRow.AddCells("小程序打开客户")
		titleCell.SetStyle(titleStyle)
		titleRow.Flush()
		addMemberInfoHeaderRow(hasOpenedWeappSheet, "小程序打开客户")
	}

	if isFirst && isFeedback {
		addedMemberSheet = writer.NewSheet("拓客明细")
		titleRow := addedMemberSheet.AddRow()
		titleCell := titleRow.AddCells("拓客明细")
		centerStyle := getExcelizeCellStyle(true, false)
		centerStyle.Alignment = &excelize.Alignment{
			Horizontal: "Center",
			Vertical:   "Center",
		}
		titleCell.SetStyle(centerStyle)
		titleRow.Flush()
		titleCell.MergeCells(11, 0)
		headerStyle := getExcelizeCellStyle(true, true)
		headerRow := addedMemberSheet.AddRow()
		headerRow.AddCells("客户昵称", "unionId", "memberId", "手机号", "执行导购", "导购编号", "导购所属门店", "门店编号", "执行导购与客户的关系", "是否添加好友", "执行呼叫次数", "执行添加次数")
		headerRow.SetStyle(headerStyle)
		headerRow.Flush()
	}

	channelId := ""
	channelDetail, err := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err == nil {
		channelId = channelDetail.ChannelId
	}

	if isFeedback {
		condition := bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"taskId":    task.Id,
			"staffId":   bson.M{"$in": util.ToMongoIds(staffIds)},
			"isDeleted": false,
		}
		exportFeedbackMemberInfo(ctx, addedMemberSheet, channelId, task.Id, condition)
	}

	baseCondition := bson.M{
		"accountId":   util.GetAccountIdAsObjectId(ctx),
		"taskId":      task.Id,
		"taskStatsId": taskStats.Id,
		"isDeleted":   false,
	}
	// 除了群群发任务，导出的客户信息都在导购的绑定客户或好友范围内
	if task.Type != task_model.STAFF_TASK_TYPE_GROUP_MESSAGE && len(staffIds) > 0 {
		baseCondition["staffId"] = bson.M{
			"$in": util.ToMongoIds(staffIds),
		}
	}
	chatIds := []string{}
	// 群群发任务，导出的客户信息在任务规则和导购管理的群组范围内
	if task.Type == task_model.STAFF_TASK_TYPE_GROUP_MESSAGE {
		selector := task_model.Common.GenDefaultCondition(ctx)
		selector["owner"] = bson.M{
			"$in": staffNos,
		}
		groupchats, err := groupchat_model.CGroupchat.FindAll(ctx, selector, []string{})
		if err != nil {
			return err
		}
		chatIds = core_util.ToStringArray(core_util.ExtractArrayField("ChatId", groupchats))
		if task.ApplicableChat.Type == task_model.APPLICABLE_CHAT_CHAT {
			intersectionResult := core_util.GetArraysIntersection(chatIds, task.ApplicableChat.ChatIds)
			chatIds = core_util.ToStringArray(intersectionResult)
		}
	}
	if havePoster {
		baseCondition["hasScanned"] = true
		exportMembersByStatus(ctx, hasScannedSheet, chatIds, baseCondition, channelId)
		delete(baseCondition, "hasScanned")
	}

	if haveProductPoster || haveProductWeappCard {
		baseCondition["hasOrdered"] = true
		exportMembersByStatus(ctx, hasOrderedSheet, chatIds, baseCondition, channelId)
		delete(baseCondition, "hasOrdered")
	}

	if haveCouponPoster || haveCouponWeappCard {
		baseCondition["hasReceivedCoupon"] = true
		exportMembersByStatus(ctx, hasReceivedCouponSheet, chatIds, baseCondition, channelId)
		delete(baseCondition, "hasReceivedCoupon")

		baseCondition["hasRedeemedCoupon"] = true
		exportMembersByStatus(ctx, hasRedeemedCouponSheet, chatIds, baseCondition, channelId)
		delete(baseCondition, "hasRedeemedCoupon")
	}

	if haveLinks {
		baseCondition["hasOpenedLink"] = true
		exportMembersByStatus(ctx, hasOpenedLinkSheet, chatIds, baseCondition, channelId)
		delete(baseCondition, "hasOpenedLink")
	}

	if haveWeappCards {
		baseCondition["hasOpenedWechatapp"] = true
		exportMembersByStatus(ctx, hasOpenedWeappSheet, chatIds, baseCondition, channelId)
		delete(baseCondition, "hasOpenedWechatapp")
	}

	log.Warn(ctx, "End exportMemberInfoSheet", log.Fields{
		"taskId": task.Id,
	})
	return nil
}

func exportMembersByStatus(ctx context.Context, sheet *core_util.XlsxSheet, chatIds []string, selector bson.M, channelId string) error {
	log.Warn(ctx, "Start exportMembersByStatus", log.Fields{
		"selector": selector,
	})
	var pageSize = 500
	var pageIndex = 1
	for {
		pageCondition := extension.PagingCondition{
			Selector:  selector,
			PageSize:  pageSize,
			PageIndex: pageIndex,
		}
		taskTouchMembers, _, err := task_model.CTaskTouchMember.FindByPagination(ctx, pageCondition)
		if err != nil {
			log.Warn(ctx, "Failed to find task touch member in export task stats", log.Fields{
				"errMsg": err.Error(),
				"err":    err,
			})
			return nil
		}
		memberIds := []string{}
		staffIds := []string{}
		storeIds := []string{}
		for _, item := range taskTouchMembers {
			memberIds = append(memberIds, item.MemberId.Hex())
			staffIds = append(staffIds, item.StaffId.Hex())
		}
		staffMap, _ := ec_share.GetStaffMap(ctx, util.StrArrayUnique(staffIds))
		for _, staff := range staffMap {
			storeIds = append(storeIds, staff.DistributorIds...)
		}
		storeMap, _ := ec_share.GetStoreMap(ctx, util.StrArrayUnique(storeIds), []int32{3})

		if len(memberIds) > 0 {
			log.Warn(ctx, "Staff task export members for sheet", log.Fields{
				"pageCondition": pageCondition,
				"memberIds":     memberIds,
				"chatIds":       chatIds,
			})
			memberMap := GetMemberMap(ctx, memberIds)
			for _, touchMember := range taskTouchMembers {
				value, ok := memberMap[touchMember.MemberId.Hex()]
				if !ok {
					continue
				}
				nickname, unionId := GetMemberWecontactInfo(value, channelId)
				unionId = ec_share.DesensitizeUnionId(needDesensitizeMember, unionId)
				memberId := ec_share.DesensitizeUnionId(needDesensitizeMember, value.Id)
				phone := ec_share.DesensitizeTel(needDesensitizeMember, value.Phone)
				if phone == "" {
					phone = "-"
				}
				staffNo := ""
				staffName := ""
				storeNames := []string{}
				storeCodes := []string{}
				staff, ok := staffMap[touchMember.StaffId.Hex()]
				if ok {
					staffNo = ec_share.DesensitizeStaffNo(needDesensitizeStaff, staff.StaffNo)
					staffName = ec_share.DesensitizeName(needDesensitizeStaff, staff.Name)
					for _, storeId := range staff.DistributorIds {
						if value, ok := storeMap[storeId]; ok {
							storeNames = append(storeNames, value.Name)
							storeCodes = append(storeCodes, value.Code)
						}
					}
				}
				row := sheet.AddRow()
				row.AddCells(nickname, unionId, memberId, phone, staffName, staffNo, strings.Join(storeNames, "、"), strings.Join(storeCodes, "、"), touchMember.Relation)
				if selector["hasOrdered"] == true {
					row.AddCells(touchMember.OrderAt)
				}
				row.Flush()
			}
		}

		if len(taskTouchMembers) < pageSize {
			break
		}
		pageIndex++
	}
	log.Warn(ctx, "End exportMembersByStatus", log.Fields{
		"selector": selector,
	})
	return nil
}

func getMemberUnionId(ctx context.Context, member *pb_member.MemberDetailResponse, taskType string, chatIds []string) string {
	unionId := ""
	socials := append([]*origin.OriginInfo{member.OriginFrom}, member.Socials...)
	for _, social := range socials {
		if social.UnionId != "" && taskType != task_model.STAFF_TASK_TYPE_GROUP_MESSAGE {
			unionId = social.UnionId
			break
		}
		if social.OpenId != "" && social.Origin == "wecontact" && checkMemberNeedExport(ctx, chatIds, social.OpenId) {
			unionId = social.UnionId
			break
		}
	}
	return unionId
}

func setGlobalVariableValue(task task_model.Task) {
	for _, setting := range task.ShareSetting {
		if len(setting.Links) > 0 {
			haveLinks = true
		}

		if len(setting.Posters) > 0 {
			havePoster = true
			for _, poster := range setting.Posters {
				if poster.Type == task_model.POSTER_TYPE_PRODUCT {
					haveProductPoster = true
				} else if poster.Type == task_model.POSTER_TYPE_COUPON {
					haveCouponPoster = true
				} else {
					haveOtherPoster = true
				}
			}
		}

		if len(setting.WeappCards) > 0 {
			haveWeappCards = true
			for _, weappCard := range setting.WeappCards {
				if weappCard.Type == task_model.POSTER_TYPE_PRODUCT {
					haveProductWeappCard = true
				} else if weappCard.Type == task_model.POSTER_TYPE_COUPON {
					haveCouponWeappCard = true
				} else {
					haveOtherWeappCard = true
				}
			}
		}
	}
}

func exportDistributorStats(
	ctx context.Context,
	taskStats task_model.TaskStats,
	req *pb_task.ListStaffTasksRequest,
	sheet *core_util.XlsxSheet) ([]string, []string, error) {
	log.Warn(ctx, "start exportDistributorStats", log.Fields{
		"taskId": taskStats.TaskId,
	})
	allStaffIds := []string{}
	allStaffNos := []string{}
	storeIds := getStoreIds(req)
	exportedStoreIds := []string{}

	allDistributorStats, err := getAllDistributorStoreStats(ctx, taskStats.TaskId, taskStats.Id, req.DistributorIds)
	if err != nil {
		return nil, nil, err
	}
	rootStatsMap, distributorStats, rootStoreIds, err := prepareExportData(allDistributorStats, storeIds)
	if err != nil {
		return nil, nil, err
	}

	log.Warn(ctx, "rootStoreIds log", log.Fields{
		"rootStoreIds": rootStoreIds,
		"taskId":       taskStats.TaskId,
	})

	for _, distributorStatsItem := range distributorStats {
		// 判断是否是零售公司，如果不是，不需要导出
		// jala 变更过根部门，为了导出之前的数据，这里不再检查是否是根节点
		if !util.StrInArray(distributorStatsItem.StoreId.Hex(), &rootStoreIds) {
			continue
		}
		// 获取需要导出的 storeStats
		needExportStoreStats, ok := rootStatsMap[distributorStatsItem.DepartmentId]
		if !ok {
			continue
		}
		needExportStoreIds := core_util.ToStringArray(core_util.ExtractArrayField("StoreId", needExportStoreStats))
		log.Warn(ctx, "export distributorStatsItem log", log.Fields{
			"storeId": distributorStatsItem.StoreId.Hex(),
			"taskId":  taskStats.TaskId,
		})
		// 导出 storeStats 和 staffTask
		staffIds, staffNos, err := exportStoreAndStaffTask(ctx, taskStats, distributorStatsItem, needExportStoreStats, req, sheet)
		if err != nil {
			continue
		}

		exportedStoreIds = append(exportedStoreIds, needExportStoreIds...)
		allStaffIds = append(allStaffIds, staffIds...)
		allStaffNos = append(allStaffNos, staffNos...)
	}
	allStoreIds, err := getAllStoreStatsStoreIds(ctx, taskStats)
	if err != nil {
		return nil, nil, err
	}

	storeIds = append(storeIds, allStoreIds...)
	// 没有父级的门店需要特殊处理
	needExportStoreStatsWithoutParentStores, err := getNeedExportStoreStatsWithoutParentStore(ctx, taskStats, storeIds, exportedStoreIds)
	if err != nil {
		return nil, nil, err
	}

	for _, storeStats := range needExportStoreStatsWithoutParentStores {
		// 检查是否选择特定门店导出
		if len(req.StoreIds) > 0 && !util.StrInArray(storeStats.StoreId.Hex(), &req.StoreIds) || len(req.DistributorIds) > 0 {
			continue
		}
		// 检查零售公司是否有父级部门，如果有，则该零售公司为中间层，不导出，否则，导出
		if storeStats.ParentDepartmentId != "" && len(distributorStats) > 0 {
			parentDepartmentCondition := bson.M{
				"accountId":    util.GetAccountIdAsObjectId(ctx),
				"isDeleted":    false,
				"departmentId": storeStats.ParentDepartmentId,
			}
			parentDistributors, _ := store_model.CStore.GetAllByCondition(ctx, parentDepartmentCondition)
			if len(parentDistributors) > 0 {
				continue
			}
		}
		memberIds, staffNos, _ := exportStoreAndStaffTask(ctx, taskStats, storeStats, []task_model.StoreStats{storeStats}, req, sheet)
		allStaffIds = append(allStaffIds, memberIds...)
		allStaffNos = append(allStaffNos, staffNos...)
	}

	log.Warn(ctx, "end exportDistributorStats", log.Fields{
		"taskId": taskStats.TaskId,
	})
	return allStaffIds, allStaffNos, nil
}

func getRootStoreIds(ctx context.Context, distributorStats []task_model.StoreStats) ([]string, error) {
	allStoreIds := core_util.ToStringArray(core_util.ExtractArrayField("StoreId", distributorStats))
	rootStoreIds := []string{}
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       bson.M{"$in": util.ToMongoIds(allStoreIds)},
	}
	dbStores, err := store_model.CStore.GetAllByCondition(ctx, condition)
	if err != nil {
		return nil, err
	}
	for _, store := range dbStores {
		if !util.StrInArray(store.ParentId.Hex(), &allStoreIds) {
			rootStoreIds = append(rootStoreIds, store.Id.Hex())
		}
	}
	return rootStoreIds, nil
}

func getAllStoreStatsStoreIds(ctx context.Context, taskStats task_model.TaskStats) ([]string, error) {
	noDisStoreCondition := bson.M{
		"accountId":   util.GetAccountIdAsObjectId(ctx),
		"taskId":      taskStats.TaskId,
		"taskStatsId": taskStats.Id,
		"type":        3,
	}
	stats, err := task_model.CStoreStats.GetAllByCondition(ctx, noDisStoreCondition)
	if err != nil {
		return nil, err
	}
	storeIds := core_util.ToStringArray(core_util.ExtractArrayField("StoreId", stats))
	return storeIds, nil
}

func getAllDistributorStoreStats(ctx context.Context, taskId, taskStatsId bson.ObjectId, distributorIds []string) ([]task_model.StoreStats, error) {
	distributorCondition := bson.M{
		"accountId":   util.GetAccountIdAsObjectId(ctx),
		"taskId":      taskId,
		"taskStatsId": taskStatsId,
	}
	if len(distributorIds) > 0 {
		distributorCondition["storeId"] = bson.M{
			"$in": util.ToMongoIds(distributorIds),
		}
	}
	storeStats := []task_model.StoreStats{}
	pageSize := 200
	for {
		results, err := task_model.CStoreStats.GetAllByConditionAndLimit(ctx, distributorCondition, []string{"_id"}, pageSize)
		if err != nil {
			return nil, err
		}
		storeStats = append(storeStats, results...)
		if len(results) < pageSize {
			break
		}
		distributorCondition["_id"] = bson.M{"$gt": results[len(results)-1].Id}
	}

	return storeStats, nil
}

func prepareExportData(allDistributorStats []task_model.StoreStats, reqStoreIds []string) (map[string][]task_model.StoreStats, []task_model.StoreStats, []string, error) {
	var (
		rootStatsMap      = make(map[string][]task_model.StoreStats)
		rootStats         = []task_model.StoreStats{}
		rootDepartmentIds = []string{}
		rootStoreIds      = []string{}
		departmentIds     = []string{}
		distributors      = []task_model.StoreStats{}
	)
	for _, stats := range allDistributorStats {
		departmentIds = append(departmentIds, stats.DepartmentId)
		if stats.Type == 3 {
			if len(reqStoreIds) > 0 && !util.StrInArray(stats.StoreId.Hex(), &reqStoreIds) {
				continue
			}
			rootStatsMap[stats.ParentDepartmentId] = append(rootStatsMap[stats.ParentDepartmentId], stats)
		} else if stats.Type == 2 {
			distributors = append(distributors, stats)
		}
	}
	for i := 0; i < len(distributors); i++ {
		for _, d := range distributors {
			if stores, ok := rootStatsMap[d.DepartmentId]; ok {
				if d.ParentDepartmentId != "" && util.StrInArray(d.ParentDepartmentId, &departmentIds) {
					rootStatsMap[d.ParentDepartmentId] = append(rootStatsMap[d.ParentDepartmentId], stores...)
					delete(rootStatsMap, d.DepartmentId)
				}
			}
		}
	}
	for _, stats := range distributors {
		if _, ok := rootStatsMap[stats.DepartmentId]; ok {
			if !util.StrInArray(stats.DepartmentId, &rootDepartmentIds) {
				rootDepartmentIds = append(rootDepartmentIds, stats.DepartmentId)
				rootStoreIds = append(rootStoreIds, stats.StoreId.Hex())
				rootStats = append(rootStats, stats)
			}
		}
	}
	return rootStatsMap, rootStats, rootStoreIds, nil
}

func getNeedExportStoreStatsWithoutParentStore(ctx context.Context, taskStats task_model.TaskStats, reqStoreIds []string, exportedStoreIds []string) ([]task_model.StoreStats, error) {
	noDisStoreCondition := bson.M{
		"accountId":   util.GetAccountIdAsObjectId(ctx),
		"taskId":      taskStats.TaskId,
		"taskStatsId": taskStats.Id,
		"type":        3,
		"$and": []bson.M{
			{
				"storeId": bson.M{
					"$nin": util.ToMongoIds(exportedStoreIds),
				},
			},
		},
	}
	if len(reqStoreIds) > 0 {
		noDisStoreCondition["$and"] = append(noDisStoreCondition["$and"].([]bson.M), bson.M{
			"storeId": bson.M{
				"$in": util.ToMongoIds(reqStoreIds),
			},
		})
	}

	return task_model.CStoreStats.GetAllByCondition(ctx, noDisStoreCondition)
}

func checkMemberNeedExport(ctx context.Context, chatIds []string, openId string) bool {
	if len(chatIds) > 0 {
		groupMemberSelector := task_model.Common.GenDefaultCondition(ctx)
		groupMemberSelector["chatId"] = bson.M{
			"$in": chatIds,
		}
		groupMemberSelector["userId"] = openId
		count, err := groupchat_model.CGroupchatMember.Count(ctx, groupMemberSelector)
		log.Warn(ctx, "Get groupchats by owner", log.Fields{
			"groupMemberSelector": groupMemberSelector,
			"count":               count,
			"err":                 err,
		})
		if count > 0 {
			return true
		}
	}

	return false
}

func addMemberInfoHeaderRow(sheet *core_util.XlsxSheet, sheetType string) {
	memberInfoHeaderRow := sheet.AddRow()
	memberInfoHeaderRow.AddCells("客户昵称", "unionId", "memberId", "手机号", "执行导购", "导购编号", "导购所属门店", "门店编号", "执行导购与客户的关系")
	switch sheetType {
	case "已触达客户":
		memberInfoHeaderRow.AddCells("触达时间")
	case "下单客户":
		memberInfoHeaderRow.AddCells("下单时间")
	}
	memberInfoHeaderRow.Flush()
}

func getListMemberMap(ctx context.Context, memberIds []string, perPage uint32) map[string]*pb_staff.MemberDetail {
	memberMap := map[string]*pb_staff.MemberDetail{}
	memberResponse, err := ec_client.StaffService.ListMembers(ctx, &pb_staff.ListMembersRequest{
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: perPage},
		MemberIds: memberIds,
	})
	if err != nil {
		log.Warn(ctx, "Failed to get member map", log.Fields{
			"error": err.Error(),
		})
		return memberMap
	}
	for _, item := range memberResponse.Items {
		member := item
		memberMap[member.Id] = member
	}
	return memberMap
}

func GetMemberMap(ctx context.Context, memberIds []string) map[string]*pb_member.MemberDetailResponse {
	memberMap := map[string]*pb_member.MemberDetailResponse{}
	if len(memberIds) == 0 {
		return memberMap
	}
	searchReq := &pb_member.SearchMemberRequest{
		Ids:        memberIds,
		UseScroll:  true,
		ScrollSize: 500,
	}
	for {
		response, err := proto_client.GetMemberServiceClient().SearchMember(ctx, searchReq)
		if err != nil || len(response.Members) == 0 {
			break
		}
		searchReq.ScrollId = response.ScrollId
		for _, member := range response.Members {
			memberMap[member.Id] = member
		}
	}
	return memberMap
}

func exportUnTouchedMember(ctx context.Context, unTouchedMemberIds []string, unTouchMemberSheet *core_util.XlsxSheet, task task_model.Task, staffTask task_model.StaffTask, channelId string) {
	if task.UseFile {
		memberMap := GetMemberMap(ctx, unTouchedMemberIds)
		staffTaskMembers := staffTask.GetStaffTaskMembersFromFile(ctx)
		staffIds := []string{}
		for _, staffTaskMember := range staffTaskMembers {
			if !util.StrInArray(staffTaskMember.MemberId.Hex(), &unTouchedMemberIds) {
				continue
			}
			staffIds = append(staffIds, util.MongoIdsToStrs(staffTaskMember.StaffIds)...)
		}
		storeIds := []string{}
		staffMap, _ := ec_share.GetStaffMap(ctx, util.StrArrayUnique(staffIds))
		for _, staff := range staffMap {
			storeIds = append(storeIds, staff.DistributorIds...)
		}
		storeMap, _ := ec_share.GetStoreMap(ctx, util.StrArrayUnique(storeIds), []int32{3})
		for _, staffTaskMember := range staffTaskMembers {
			if !util.StrInArray(staffTaskMember.MemberId.Hex(), &unTouchedMemberIds) {
				continue
			}
			exportUnTouchedMemberRow(unTouchMemberSheet, staffTaskMember, memberMap, staffMap, storeMap, channelId)
		}
		return
	}

	i := 0
	needBreak := false
	for {
		nextLength := i + 100
		if nextLength >= len(unTouchedMemberIds)-1 {
			nextLength = len(unTouchedMemberIds)
			needBreak = true
		}
		tempMemberIds := unTouchedMemberIds[i:nextLength]
		memberMap := GetMemberMap(ctx, tempMemberIds)
		condition := bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"isDeleted": false,
			"taskId":    staffTask.TaskId,
			"memberId":  bson.M{"$in": util.ToMongoIds(tempMemberIds)},
		}
		staffTaskMembers, _ := task_model.CStaffTaskMember.GetAllByConditionAndLimit(ctx, condition, []string{"_id"}, 100)
		staffIds := []string{}
		for _, taskMember := range staffTaskMembers {
			staffIds = append(staffIds, util.MongoIdsToStrs(taskMember.StaffIds)...)
		}
		storeIds := []string{}
		staffMap, _ := ec_share.GetStaffMap(ctx, util.StrArrayUnique(staffIds))
		for _, staff := range staffMap {
			storeIds = append(storeIds, staff.DistributorIds...)
		}
		storeMap, _ := ec_share.GetStoreMap(ctx, util.StrArrayUnique(storeIds), []int32{3})
		for _, taskMember := range staffTaskMembers {
			exportUnTouchedMemberRow(unTouchMemberSheet, taskMember, memberMap, staffMap, storeMap, channelId)
		}
		if needBreak {
			break
		}
		i = nextLength
	}
}

func exportUnTouchedMemberRow(
	unTouchMemberSheet *core_util.XlsxSheet,
	taskMember task_model.StaffTaskMember,
	memberMap map[string]*pb_member.MemberDetailResponse,
	staffMap map[string]*ec_store.BriefStaffDetail,
	storeMap map[string]*ec_store.BriefStoreDetail,
	channelId string) {
	storeNames, storeCodes := []string{}, []string{}
	phone, staffName, staffNo, unionId, nickname, relation := "-", "", "", "", "", ""
	memberId := ec_share.DesensitizeUnionId(needDesensitizeMember, taskMember.MemberId.Hex())
	if value, ok := memberMap[taskMember.MemberId.Hex()]; ok {
		phone = ec_share.DesensitizeTel(needDesensitizeMember, value.Phone)
		nickname, unionId = GetMemberWecontactInfo(value, channelId)
		unionId = ec_share.DesensitizeUnionId(needDesensitizeMember, unionId)
		staff, ok := staffMap[taskMember.StaffIds[0].Hex()]
		if ok {
			staffNo = ec_share.DesensitizeStaffNo(needDesensitizeStaff, staff.StaffNo)
			staffName = ec_share.DesensitizeName(needDesensitizeStaff, staff.Name)
			for _, storeId := range staff.DistributorIds {
				if storeValue, ok := storeMap[storeId]; ok {
					storeNames = append(storeNames, storeValue.Name)
					storeCodes = append(storeCodes, storeValue.Code)
				}
			}
			relation = taskMember.GetRelationByStaffId(taskMember.StaffIds[0])
		}
	}

	unTouchMemberRow := unTouchMemberSheet.AddRow()
	unTouchMemberRow.AddCells(nickname, unionId, memberId, phone, staffName, staffNo, strings.Join(storeNames, "、"), strings.Join(storeCodes, "、"), relation)
	unTouchMemberRow.Flush()
}

func getStoreMap(ctx context.Context, members []*pb_member.MemberDetailResponse, staffMemberMap map[string]*pb_staff.MemberDetail) (map[string]*pb_store.BriefStoreDetail, error) {
	storeIds := []string{}
	for _, member := range members {
		staffMember, ok := staffMemberMap[member.Id]
		if !ok {
			continue
		}
		if staffMember.Staff == nil {
			continue
		}
		storeIds = append(storeIds, staffMember.Staff.StoreIds...)
	}
	return ec_share.GetStoreMap(ctx, util.StrArrayUnique(storeIds), []int32{3})
}

func GetMemberWecontactInfo(member *pb_member.MemberDetailResponse, channelId string) (string, string) {
	socials := member.Socials
	socials = append(socials, member.OriginFrom)

	for _, social := range socials {
		if social.Origin == "wecontact" && social.Channel == channelId {
			return social.Nickname, social.UnionId
		}
	}

	return "", ""
}

func getExternalMemberCount(ctx context.Context, taskId, staffId bson.ObjectId) (uint64, uint64) {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"taskId":    taskId,
	}
	if staffId.Valid() {
		condition["staffId"] = staffId
	}
	externalMemberCount, _ := task_model.CStaffTaskExternalMember.CountByCondition(ctx, condition)
	condition["isCompleted"] = true
	addedCount, _ := task_model.CStaffTaskExternalMember.CountByCondition(ctx, condition)
	return externalMemberCount, addedCount
}

func writeStatsTime(sheet *core_util.XlsxSheet, taskTime task_model.Time) {
	timeRow := sheet.AddRow()
	endTime := taskTime.EndTime.Add(7 * 24 * time.Hour)
	if time.Now().Before(endTime) {
		endTime = time.Now()
	}
	titleCell := timeRow.AddCells("数据统计时间")
	titleCell.SetStyle(getExcelizeCellStyle(false, true))
	timeRow.AddCells(fmt.Sprintf("%s-%s", taskTime.StartTime.Format("2006/01/02"), endTime.Format("2006/01/02")))
	timeRow.Flush()
	sheet.AddRow()
}

func exportFeedbackMemberInfo(ctx context.Context, sheet *core_util.XlsxSheet, channelId string, taskId bson.ObjectId, condition bson.M) error {
	var pageSize = 500
	var pageIndex = 1
	for {
		pageCondition := extension.PagingCondition{
			Selector:  condition,
			PageSize:  pageSize,
			PageIndex: pageIndex,
		}
		externalMembers, _, err := task_model.CStaffTaskExternalMember.FindByPagination(ctx, pageCondition)
		if err != nil {
			log.Warn(ctx, "Failed to find staffTaskExternalMember", log.Fields{
				"errMsg": err.Error(),
				"err":    err,
			})
			return err
		}
		var (
			memberIds []string
			staffIds  []string
			storeIds  []string
		)
		for _, item := range externalMembers {
			memberIds = append(memberIds, item.MemberId.Hex())
			staffIds = append(staffIds, item.StaffId.Hex())
		}
		staffMap, _ := ec_share.GetStaffMap(ctx, util.StrArrayUnique(staffIds))
		for _, staff := range staffMap {
			storeIds = append(storeIds, staff.DistributorIds...)
		}
		storeMap, _ := ec_share.GetStoreMap(ctx, util.StrArrayUnique(storeIds), []int32{3})

		if len(memberIds) > 0 {
			taskMemberCondition := bson.M{
				"accountId": util.GetAccountIdAsObjectId(ctx),
				"isDeleted": false,
				"taskId":    taskId,
				"memberId":  bson.M{"$in": util.ToMongoIds(memberIds)},
			}
			taskMemberMap := map[string]task_model.StaffTaskMember{}
			taskMembers, err := task_model.CStaffTaskMember.GetAllByConditionAndLimit(ctx, taskMemberCondition, nil, len(memberIds))
			if err != nil {
				log.Warn(ctx, "Failed to find staffTaskMember", log.Fields{
					"errMsg": err.Error(),
					"err":    err,
				})
			}
			for _, taskMember := range taskMembers {
				taskMemberMap[taskMember.MemberId.Hex()] = taskMember
			}
			memberMap := GetMemberMap(ctx, memberIds)
			for _, externalMember := range externalMembers {
				var (
					staffNo    string
					staffName  string
					storeNames []string
					storeCodes []string
				)
				value, ok := memberMap[externalMember.MemberId.Hex()]
				if !ok {
					continue
				}
				nickname, unionId := GetMemberWecontactInfo(value, channelId)
				unionId = ec_share.DesensitizeUnionId(needDesensitizeMember, unionId)
				memberId := ec_share.DesensitizeUnionId(needDesensitizeMember, value.Id)
				phone := ec_share.DesensitizeTel(needDesensitizeMember, value.Phone)
				if phone == "" {
					phone = "-"
				}
				staff, ok := staffMap[externalMember.StaffId.Hex()]
				if ok {
					staffNo = ec_share.DesensitizeStaffNo(needDesensitizeStaff, staff.StaffNo)
					staffName = ec_share.DesensitizeName(needDesensitizeStaff, staff.Name)
					for _, storeId := range staff.DistributorIds {
						if storeValue, ok := storeMap[storeId]; ok {
							storeNames = append(storeNames, storeValue.Name)
							storeCodes = append(storeCodes, storeValue.Code)
						}
					}
				}
				relation := ""
				for _, staffRelation := range taskMemberMap[externalMember.MemberId.Hex()].StaffRelations {
					if staffRelation.StaffId.Hex() == externalMember.StaffId.Hex() {
						relation = staffRelation.Relation
						break
					}
				}
				addStatus := "未添加"
				if externalMember.IsCompleted {
					addStatus = "已添加"
				}
				row := sheet.AddRow()
				row.AddCells(nickname, unionId, memberId, phone, staffName, staffNo, strings.Join(storeNames, "、"), strings.Join(storeCodes, "、"), relation, addStatus, externalMember.CallCount, externalMember.AddCount)
				row.Flush()
			}
		}
		if len(externalMembers) < pageSize {
			break
		}
		pageIndex++
	}
	return nil
}
