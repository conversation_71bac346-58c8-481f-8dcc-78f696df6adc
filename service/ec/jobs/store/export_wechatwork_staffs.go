package store

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"strings"

	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	"mairpc/proto/ec/setting"
	"mairpc/proto/ec/store"
	pb_store "mairpc/proto/store"
	ec_client "mairpc/service/ec/client"
	wework_license_model "mairpc/service/ec/model/weworkLicense"
	ec_share "mairpc/service/ec/share"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"

	"github.com/spf13/cobra"
)

var (
	wechatworkStaffHeader = []string{"员工账号", "员工姓名", "所属节点/角色", "员工状态", "创建时间"}
	statusMap             = map[int64]string{
		1: "在职",
		2: "同步失败",
		5: "已离职",
	}
	licenseMap = map[string]string{
		wework_license_model.WEWORK_LICENSE_STATUS_ACTIVATED:    "已激活",
		wework_license_model.WEWORK_LICENSE_STATUS_INACTIVATED:  "未激活",
		wework_license_model.WEWORK_LICENSE_STATUS_EXPIRED:      "已逾期",
		wework_license_model.WEWORK_LICENSE_STATUS_EXPIRED_SOON: "30天内到期",
	}
)

func init() {
	StoreCmd.AddCommand(exportWechatworkStaffs)
}

var exportWechatworkStaffs = &cobra.Command{
	Use: "exportWechatworkStaffs",
	RunE: func(cmd *cobra.Command, args []string) error {
		jobOptions := util.GetArgs(args)
		ctx := util.GetContextInJob(args)
		ctx = core_util.CtxWithReadSecondaryPreferred(ctx)
		req, err := getListStaffsReq(jobOptions["req"])
		if err != nil {
			return err
		}
		jobId := core_util.GetSreadminJobName()
		err = job_util.BeginToExport(ctx, jobId)
		if err != nil {
			return err
		}
		job, _ := job_util.GetJobStatus(ctx, jobId)
		isDesensitizedStaffInfo, err := ec_share.NeedDesensitizeStaffInfo(ctx)
		if err != nil {
			log.Warn(ctx, "Failed to obtain view_staff_sensitive_info operation", log.Fields{
				"err": err,
			})
		}
		url, err := job_util.ExportFile(
			ctx,
			job.Name,
			func(f *os.File) error {
				w := csv.NewWriter(f)
				// 写入表格标题
				weworkLicenseEnabled := false
				header := wechatworkStaffHeader
				setting, err := client.GetEcSettingServiceClient().GetWechatworkSetting(ctx, &setting.GetSettingsRequest{})
				if err == nil && setting.IsWeworkLicenseEnabled {
					weworkLicenseEnabled = true
				}
				if weworkLicenseEnabled {
					header = append(header, "激活状态", "有效期")
				}
				w.Write(header)
				req.ListCondition.Page = 1
				req.ListCondition.PerPage = uint32(100)
				distributorMap := map[string]*pb_store.DistributorDetail{}
				for {
					resp, err := ec_client.StoreService.ListStaffs(ctx, req)
					if err != nil {
						return err
					}
					for _, staff := range resp.Items {
						writeSingleStaffData(ctx, w, staff, isDesensitizedStaffInfo, weworkLicenseEnabled, distributorMap)
					}
					w.Flush()
					if len(resp.Items) == 0 || len(resp.Items) < int(req.ListCondition.PerPage) {
						break
					}
					req.ListCondition.Page++
				}
				return nil
			},
		)
		if err != nil {
			return err
		}

		err = job_util.ExportSucceed(ctx, jobId, url, job.Name)
		if err != nil {
			return err
		}

		return nil
	},
}

func writeSingleStaffData(ctx context.Context, w *csv.Writer, staff *store.StaffDetail, isDesensitizedStaffInfo,
	weworkLicenseEnabled bool, distributorMap map[string]*pb_store.DistributorDetail) {
	createdAt := util.MustTransStrToTime(staff.CreatedAt)
	roles := []string{}
	distributorNamesMap := getDistributorNamesMap(ctx, staff, distributorMap)
	for _, role := range staff.Roles {
		if role.Business == "wechatworkStaff" {
			for _, distributorRole := range role.DistributorRoles {
				if len(distributorRole.Distributors) == 0 {
					continue
				}
				distributorIds := core_util.ToStringArray(core_util.ExtractArrayField("Id", distributorRole.Distributors))
				if len(distributorIds) == 0 {
					continue
				}
				distributorNames := distributorNamesMap[distributorIds[0]]
				roleNames := core_util.ToStringArray(core_util.ExtractArrayField("Name", distributorRole.Roles))
				roles = append(roles, fmt.Sprintf("%s：%s", distributorNames, strings.Join(roleNames, "、")))
				delete(distributorNamesMap, distributorIds[0])
			}
		}
	}
	for _, noRoleDistributor := range distributorNamesMap {
		roles = append(roles, fmt.Sprintf("%s：-", noRoleDistributor))
	}
	if len(roles) == 0 {
		roles = append(roles, "-")
	}

	line := []string{
		ec_share.DesensitizeStaffNo(isDesensitizedStaffInfo, staff.StaffNo) + "\t",
		ec_share.DesensitizeName(isDesensitizedStaffInfo, staff.Name),
		strings.Join(roles, "\n"),
		statusMap[staff.Status],
		createdAt.Format("2006-01-02 15:04:05"),
	}
	if weworkLicenseEnabled {
		weworkLicense, err := wework_license_model.CWeworkLicense.GetByStaffId(ctx, util.ToMongoId(staff.Id))
		if err == nil {
			endAt := ""
			if !weworkLicense.EndAt.IsZero() {
				endAt = weworkLicense.EndAt.Format("2006-01-02 15:04:05")
			}
			line = append(line, licenseMap[weworkLicense.Status], endAt)
		}
	}
	w.Write(line)
}

func getDistributorNamesMap(ctx context.Context, staff *store.StaffDetail,
	distributorMap map[string]*pb_store.DistributorDetail) map[string]string {
	ids := []string{}
	tempDistributorMap := make(map[string]*pb_store.DistributorDetail, len(staff.DistributorIds))
	distributorNamesMap := make(map[string]string, len(staff.AccessibleDistributors))
	for _, distributorId := range staff.DistributorIds {
		if value, ok := distributorMap[distributorId]; ok {
			tempDistributorMap[distributorId] = value
			continue
		}
		ids = append(ids, distributorId)
	}
	if len(ids) > 0 {
		resp, err := client.GetStoreServiceClient().ListDistributors(ctx, &pb_store.ListDistributorsRequest{
			ListCondition: &request.ListCondition{
				Page:    1,
				PerPage: uint32(len(ids)),
			},
			Ids: ids,
		})
		if err != nil {
			return map[string]string{}
		}
		for _, distributor := range resp.Items {
			distributorMap[distributor.Id] = distributor
			tempDistributorMap[distributor.Id] = distributor
		}
	}
	for _, accessibleDistributor := range staff.AccessibleDistributors {
		names := []string{accessibleDistributor.Name}
		parentId := accessibleDistributor.ParentId
		for {
			value, ok := tempDistributorMap[parentId]
			if !ok {
				break
			}
			names = append(names, value.Name)
			parentId = value.ParentId
		}
		util.ReverseStrs(names)
		distributorNamesMap[accessibleDistributor.Id] = strings.Join(names, "/")
	}
	return distributorNamesMap
}
