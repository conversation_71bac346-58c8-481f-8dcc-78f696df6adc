package order

import (
	"context"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/service/ec/model/order"
	"mairpc/service/share/util"
	"sync"

	"github.com/panjf2000/ants/v2"
	"github.com/spf13/cobra"
)

func init() {
	OrderCmd.AddCommand(migrateComments)
}

var migrateComments = &cobra.Command{
	Use: "migrateComments",
	RunE: func(cmd *cobra.Command, args []string) error {
		pool, err := ants.NewPool(5)
		if err != nil {
			return err
		}
		defer pool.Release()
		wg := &sync.WaitGroup{}
		ctx := util.GetContextInJob(args)
		util.ExecActivatedAccountsIterative(ctx, []string{"retail"}, func(ctx context.Context) error {
			wg.Add(1)
			pool.Submit(func() {
				defer wg.Done()
				condition := order.Common.GenDefaultCondition(ctx)
				pageSize := 500
				for {
					comments, err := order.CComment.GetAllByConditionAndLimitWithFields(ctx, condition,
						bson.M{"_id": 1, "comment": 1, "pictures": 1}, []string{"_id"}, pageSize)
					if err != nil {
						return
					}
					ids := []bson.ObjectId{}
					hasCommentIds := []bson.ObjectId{}
					for _, comment := range comments {
						if comment.Comment != "" || len(comment.Pictures) > 0 {
							hasCommentIds = append(hasCommentIds, comment.Id)
							continue
						}
						ids = append(ids, comment.Id)
					}
					if len(ids) > 0 {
						updateHasComment(ctx, ids, false)
					}
					if len(hasCommentIds) > 0 {
						updateHasComment(ctx, hasCommentIds, true)
					}
					if len(comments) == 0 || len(comments) < pageSize {
						break
					}
					condition["_id"] = bson.M{"$gt": comments[len(comments)-1].Id}
				}
			})
			return nil
		})
		wg.Wait()
		return nil
	},
}

func updateHasComment(ctx context.Context, ids []bson.ObjectId, value bool) {
	if len(ids) == 0 {
		return
	}
	selector := bson.M{
		"_id":       bson.M{"$in": ids},
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updator := bson.M{
		"$set": bson.M{
			"hasComment": value,
		},
	}
	extension.DBRepository.UpdateAll(ctx, order.C_COMMENT, selector, updator)
}
