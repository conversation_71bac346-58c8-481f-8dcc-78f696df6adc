package order

import (
	"fmt"
	"mairpc/core/component/yeepay"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	ec_order "mairpc/service/ec/model/order"
	"mairpc/service/share/util"
	"time"

	"github.com/spf13/cobra"
	"golang.org/x/net/context"
)

func init() {
	OrderCmd.AddCommand(yeepayDivideComplete)
}

var yeepayDivideComplete = &cobra.Command{
	Use: "yeepayDivideComplete",
	RunE: func(cmd *cobra.Command, args []string) error {
		ctx := core_util.CtxWithRequestID(context.Background(), util.GetReqIdFromArgs(args))
		accountId := util.GetAccountIdFromArgs(args)
		if accountId != "" {
			ctx = core_util.DuplicateContextWithAid(ctx, accountId)
			orders, _ := getOrders(ctx)
			handleOrders(ctx, orders)
		}
		return nil
	},
}

func handleOrders(ctx context.Context, orders []ec_order.Order) {
	client := yeepay.NewYeepay(yeepay.YEEPAY_APP_KEY, core_util.GetMaimengPrivateKey())
	for _, order := range orders {
		parentMerchantNo, merchantNo := order.GetYeepayMerchants()
		if !needDivideComplete(ctx, order, parentMerchantNo, merchantNo) {
			continue
		}
		err := divideComplete(ctx, order, client, parentMerchantNo, merchantNo)
		if err != nil {
			continue
		}
		log.Warn(ctx, "Order divide complete", log.Fields{
			"orderId":     order.Id.Hex(),
			"orderNumber": order.Number,
		})
		order.Tags = append(order.Tags, ec_order.ORDER_TAGS_DIVIDE_COMPLETE)
		order.UpdateTags(ctx)
	}
}

func needDivideComplete(ctx context.Context, order ec_order.Order, parentMerchantNo, merchantNo string) bool {
	// 已经分账完成了，无须处理
	if util.StrInArray(ec_order.ORDER_TAGS_DIVIDE_COMPLETE, &order.Tags) {
		return false
	}
	yeepayOrder, err := ec_order.GetYeePayOrder(ctx, order.Id.Hex(), parentMerchantNo, merchantNo)
	if err != nil {
		log.Warn(ctx, "GetYeePayOrder fail", log.Fields{
			"errMessage": err.Error(),
			"orderId":    order.Id.Hex(),
		})
		return false
	}
	// 非分账不用处理
	if yeepayOrder != nil && yeepayOrder.FundProcessType != "DELAY_SETTLE" {
		return false
	}
	// 待分账金额为 0，不需要处理
	if yeepayOrder.UnSplitAmount == 0 {
		return false
	}
	return true
}

func divideComplete(ctx context.Context, order ec_order.Order, client *yeepay.Yeepay, parentMerchantNo, merchantNo string) error {
	req := yeepay.DivideCompleteRequest{
		ParentMerchantNo: parentMerchantNo,
		MerchantNo:       merchantNo,
		OrderId:          order.Id.Hex(),
		UniqueOrderNo:    order.TradeNo,
		DivideRequestId:  fmt.Sprintf("%s_%s", order.Id.Hex(), time.Now().Format("**************")),
		DivideDetailDesc: "分账完结",
	}
	resp, err := client.Jiaoyi.DivideComplete(ctx, &req)
	if err != nil {
		log.Warn(ctx, "DivideComplete fail", log.Fields{
			"errMessage": err.Error(),
			"req":        req,
			"resp":       resp,
			"orderId":    order.Id.Hex(),
		})
		return err
	}
	return nil
}

func getOrders(ctx context.Context) ([]ec_order.Order, error) {
	condition := bson.M{
		"accountId":   util.GetAccountIdAsObjectId(ctx),
		"tags":        "yeepay",
		"profitTypes": "distribution",
		"paidAt": bson.M{
			"$exists": true,
		},
	}
	return ec_order.COrder.GetAllByCondition(ctx, condition)
}
