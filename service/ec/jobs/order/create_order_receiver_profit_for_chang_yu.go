package order

import (
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/service/ec/model/order"
	"mairpc/service/share/util"

	"github.com/spf13/cobra"
)

func init() {
	OrderCmd.AddCommand(createOrderReceiverProfitForChangYu)
}

var createOrderReceiverProfitForChangYu = &cobra.Command{
	Use: "createOrderReceiverProfitForChangYu",
	RunE: func(cmd *cobra.Command, args []string) error {
		ctx := util.GetContextInJob(args)
		argsMap, err := getReqArgs(util.GetArgs(args)["request"])
		if err != nil {
			return err
		}

		var (
			orderNumbers []string
		)
		if numbers, ok := argsMap["orderNumbers"]; ok {
			orderNumbers = core_util.ToStringArray(numbers)
		}

		if len(orderNumbers) > 0 {
			for _, number := range orderNumbers {
				o, err := order.COrder.GetByNumber(ctx, number)
				if err != nil {
					log.Error(ctx, "Get order failed", log.Fields{
						"err":    err.Error(),
						"number": number,
					})
					continue
				}
				if o.Status == order.ORDER_STATUS_COMPLETED {
					order.COrderReceiverProfit.CreateForZhangYu(ctx, *o)
				}
			}
		}
		return nil
	},
}
