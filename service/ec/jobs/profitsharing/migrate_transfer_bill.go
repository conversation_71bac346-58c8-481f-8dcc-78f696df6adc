package profitsharing

import (
	"github.com/spf13/cobra"
	"golang.org/x/net/context"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	order_model "mairpc/service/ec/model/order"
	profit_model "mairpc/service/ec/model/profitsharing"
	"mairpc/service/share/util"
)

func init() {
	ProfitsharingCmd.AddCommand(migrateTransferBill)
}

var migrateTransferBill = &cobra.Command{
	Use: "migrateTransferBill",
	RunE: func(cmd *cobra.Command, args []string) error {
		util.ExecActivatedAccountsIterative(util.GetContextInJob(args), []string{"retail"}, func(ctx context.Context) error {
			migrateTransferBillForOneAccount(ctx)
			return nil
		})
		return nil
	},
}

func migrateTransferBillForOneAccount(ctx context.Context) {
	var (
		lastId    = bson.NilObjectId
		condition = bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"source":    profit_model.BIll_SOURCE_MEMBER_DISTRIBUTION,
		}
	)
	for {
		if lastId.Valid() {
			condition["_id"] = bson.M{"$gt": lastId}
		}
		var (
			bills []profit_model.TransferBill
			docs  []interface{}
		)
		extension.DBRepository.FindAllWithFields(ctx, profit_model.C_TRANSFER_BILL, condition, bson.M{
			"_id":        1,
			"orderCount": 1,
			"receiver":   1,
		}, []string{"_id"}, 100, &bills)
		if len(bills) == 0 {
			break
		}
		lastId = bills[len(bills)-1].Id
		for _, bill := range bills {
			if bill.OrderCount != 0 || bill.Receiver.PromoterId.IsZero() {
				continue
			}
			orderCount := 0
			profit, _ := order_model.COrderReceiverProfit.GetByTransferBillId(ctx, bill.Id)
			if profit.Id.Valid() {
				orderCount = 1
			} else {
				orderCount, _ = extension.DBRepository.Count(ctx, order_model.C_ORDER, bson.M{
					"accountId":                      util.GetAccountIdAsObjectId(ctx),
					"isDeleted":                      false,
					"distribution.profitSharingType": 3,
					"distribution.promoterId":        bill.Receiver.PromoterId,
					"distribution.transferBillId":    bill.Id,
				})
			}
			docs = append(docs, bson.M{"_id": bill.Id}, bson.M{"$set": bson.M{"orderCount": orderCount}})
		}
		if len(docs) > 0 {
			extension.DBRepository.BatchUpdateUnordered(ctx, profit_model.C_TRANSFER_BILL, docs...)
		}
	}
}
