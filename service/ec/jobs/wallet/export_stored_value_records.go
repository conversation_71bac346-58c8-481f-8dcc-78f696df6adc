package wallet

import (
	"fmt"
	"os"
	"time"

	"mairpc/core/log"
	core_util "mairpc/core/util"
	pb_wallet "mairpc/proto/ec/wallet"
	"mairpc/service/ec/client"
	job_share "mairpc/service/ec/jobs/share"
	ec_order "mairpc/service/ec/model/order"
	ec_wallet "mairpc/service/ec/model/wallet"
	ec_share "mairpc/service/ec/share"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"

	"github.com/spf13/cobra"
)

var (
	storedValueRecordHeader     = []string{"交易ID", "来源门店", "消费方式", "交易时间", "交易类型", "交易金额（元）", "交易后余额（元）"}
	storedValueSpentOrderHeader = []string{"ID", "来源门店", "消费方式", "订单编号", "下单人", "手机号", "消费时间", "消费金额（元）", "卡内余额（元）", "订单状态", "交易订单", "使用门店名称", "使用门店编号"}
)

func init() {
	WalletCmd.AddCommand(exportStoredValueRecords)
}

var exportStoredValueRecords = &cobra.Command{
	Use: "exportStoredValueRecords",
	RunE: func(cmd *cobra.Command, args []string) error {
		jobOptions := util.GetArgs(args)
		ctx := util.GetContextInJob(args)
		ctx = core_util.CtxWithReadSecondaryPreferred(ctx)
		jobId := core_util.GetSreadminJobName()
		err := job_util.BeginToExport(ctx, jobId)
		if err != nil {
			return err
		}

		req := &pb_wallet.ListStoredValueRecordsRequest{}
		err = unmarshalData(jobOptions["request"], req)
		if err != nil {
			return err
		}

		job, _ := job_util.GetJobStatus(ctx, jobId)
		url, err := job_util.ExportFile(
			ctx,
			job.Name,
			func(f *os.File) error {
				xlsxWriter := core_util.NewXlsxWriter()
				sheet := xlsxWriter.NewSheet("Sheet1")
				row := sheet.AddRow()
				headers := storedValueRecordHeader
				if exportSpentOrders(req) {
					headers = storedValueSpentOrderHeader
				}
				for _, item := range headers {
					row.AddCells(item)
				}
				row.Flush()

				isDesensitized, _ := ec_share.NeedDesensitize(ctx)
				for {
					resp, err := client.WalletService.ListStoredValueRecords(ctx, req)
					if err != nil {
						return err
					}
					if len(resp.Items) == 0 {
						break
					}
					for _, item := range resp.Items {
						row := sheet.AddRow()
						line := genRecordLine(item)
						if exportSpentOrders(req) {
							line = genSpentOrderLine(isDesensitized, item)
						}
						for _, ele := range line {
							row.AddCells(ele)
						}
						row.Flush()
					}
					// 判断是否已查完
					if req.ListCondition.Page*req.ListCondition.PerPage > uint32(resp.Total) {
						break
					}
					req.ListCondition.Page += 1
				}
				sheet.Flush()
				xlsxWriter.WriteToFile(f)
				f.Sync()
				return nil
			},
		)
		if err != nil {
			log.Warn(ctx, "ExportFile error", log.Fields{
				"err":    err,
				"errMsg": err.Error(),
			})
			return err
		}

		err = job_util.ExportSucceed(ctx, jobId, url, job.Name)
		if err != nil {
			log.Warn(ctx, "ExportSucceed error", log.Fields{
				"err":    err,
				"errMsg": err.Error(),
			})
			return err
		}
		return nil
	},
}

func exportSpentOrders(req *pb_wallet.ListStoredValueRecordsRequest) bool {
	if util.StrArrayEqual(req.RecordTypes, []string{ec_wallet.STORED_VALUE_RECORD_TYPE_SPENT}) && req.ShowOrder {
		return true
	}
	return false
}

func genRecordLine(item *pb_wallet.StoredValueRecord) []string {
	recordAt, _ := time.Parse(core_util.RFC3339Mili, item.RecordAt)
	var storeName string
	if item.Store != nil {
		storeName = item.Store.Name
	}
	return []string{
		item.TransactionId,
		storeName,
		formatStoredValueRecordPayment(item.Payment),
		job_share.FormatTimeToStr(recordAt),
		formatStoredValueRecordType(item.Type),
		fmt.Sprintf("%.2f", float64(item.Amount)/100),
		fmt.Sprintf("%.2f", float64(item.Balance)/100),
	}
}

func genSpentOrderLine(isDesensitized bool, item *pb_wallet.StoredValueRecord) []string {
	recordAt, _ := time.Parse(core_util.RFC3339Mili, item.RecordAt)
	var storeName string
	if item.Store != nil {
		storeName = item.Store.Name
	}
	return []string{
		item.TransactionId,
		storeName,
		formatStoredValueRecordPayment(item.Payment),
		func() string {
			if item.Order != nil {
				return item.Order.Number
			}
			return ""
		}(),
		ec_share.DesensitizeName(isDesensitized, item.Member.Name),
		ec_share.DesensitizeTel(isDesensitized, item.Member.Phone),
		job_share.FormatTimeToStr(recordAt),
		fmt.Sprintf("%.2f", float64(item.Amount)/100),
		fmt.Sprintf("%.2f", float64(item.Balance)/100),
		func() string {
			if item.Order != nil {
				return ec_order.FormatOrderStatus(item.Order.Status, item.Order.Method)
			}
			return ""
		}(),
		func() string {
			if item.Order == nil {
				return item.TradeNo
			}
			return ""
		}(),
		func() string {
			if item.Store != nil {
				return item.Store.Name
			}
			return ""
		}(),
		func() string {
			if item.Store != nil {
				return item.Store.Code
			}
			return ""
		}(),
	}
}

func formatStoredValueRecordType(typ string) string {
	switch typ {
	case ec_wallet.STORED_VALUE_RECORD_TYPE_STORED:
		return "充值"
	case ec_wallet.STORED_VALUE_RECORD_TYPE_SPENT:
		return "消费"
	case ec_wallet.STORED_VALUE_RECORD_TYPE_STORED_REFUND:
		return "充值退款"
	case ec_wallet.STORED_VALUE_RECORD_TYPE_SPENT_REFUND:
		return "消费退款"
	}
	return ""
}

func formatStoredValueRecordPayment(payment string) string {
	switch payment {
	case ec_wallet.STORED_VALUE_PAYMENT_ONLINE:
		return "线上商城"
	case ec_wallet.STORED_VALUE_PAYMENT_POS:
		return "POS机"
	}
	return ""
}
