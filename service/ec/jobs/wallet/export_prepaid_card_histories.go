package wallet

import (
	"encoding/csv"
	"encoding/json"
	"os"
	"strings"

	core_util "mairpc/core/util"
	pb_request "mairpc/proto/common/request"
	pb_wallet "mairpc/proto/ec/wallet"
	ec_model_wallet "mairpc/service/ec/model/wallet"
	service_wallet "mairpc/service/ec/service/wallet"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"

	"github.com/spf13/cast"
	"github.com/spf13/cobra"
)

var (
	exportPrepaidCardHistoriesFileHeader = []string{"储值卡名称", "储值卡编码", "卡号", "储值卡类型", "储值卡批次", "客户姓名", "手机号", "交易类型", "交易订单", "交易金额(元)", "卡内余额(元)", "交易时间", "交易订单", "使用门店名称", "使用门店编号"}
)

func init() {
	WalletCmd.AddCommand(exportPrepaidCardHistories)
}

var exportPrepaidCardHistories = &cobra.Command{
	Use: "exportPrepaidCardHistories",
	RunE: func(cmd *cobra.Command, args []string) error {
		jobOptions := util.GetArgs(args)
		ctx := util.GetContextInJob(args)
		ctx = core_util.CtxWithReadSecondaryPreferred(ctx)
		jobId := core_util.GetSreadminJobName()
		err := job_util.BeginToExport(ctx, jobId)
		if err != nil {
			return err
		}
		req := &pb_wallet.ListPrepaidCardHistoriesRequest{}
		byteData, err := json.Marshal(jobOptions["req"])
		if err != nil {
			return err
		}
		err = json.Unmarshal(byteData, req)
		if err != nil {
			return err
		}
		limit := 500
		pageIndex, pageSize := uint32(1), uint32(200)
		orderBys := getExportListConditionOrderBys(req.ListCondition)
		req.ListCondition.OrderBy = orderBys
		isExportById := len(orderBys) == 1 && strings.Index(orderBys[0], "_id") != -1
		condition := service_wallet.GetListPrepaidCardHistoryCondition(ctx, req)
		job, _ := job_util.GetJobStatus(ctx, jobId)
		url, err := job_util.ExportFile(
			ctx,
			job.Name,
			func(f *os.File) error {
				w := csv.NewWriter(f)
				w.Write(exportPrepaidCardHistoriesFileHeader)
				for {
					prepaidCardHistories := []ec_model_wallet.PrepaidCardHistory{}
					if isExportById {
						condition = service_wallet.GetListPrepaidCardHistoryCondition(ctx, req)
						_, err := ec_model_wallet.Common.GetAllByCondition(ctx, condition, orderBys, limit, ec_model_wallet.C_PREPAID_CARD_HISTORY, &prepaidCardHistories)
						if err != nil {
							return err
						}
					} else {
						ec_model_wallet.Common.GetAllByPagination(ctx, condition, pageIndex, pageSize, orderBys, ec_model_wallet.C_PREPAID_CARD_HISTORY, &prepaidCardHistories)
					}
					if len(prepaidCardHistories) == 0 {
						break
					}
					histories := service_wallet.FormatPrepaidCardHistorys(ctx, prepaidCardHistories)
					prepaidCardIds := util.StrArrayNonEmpty(core_util.ExtractArrayStringField("PrepaidCardId", histories))
					prepaidCardIdBriefInfoMap := service_wallet.GetPrepaidCardIdBriefInfoMap(ctx, util.ToMongoIds(prepaidCardIds), true)
					for _, item := range histories {
						line := []string{
							getPrepaidCardName(item),
							getPrepaidCardNumber(item),
							item.Number,
							getPrepaidCardType(item),
							getBatchNumber(item, prepaidCardIdBriefInfoMap),
							item.MemberName,
							item.MemberPhone,
							func(transactionType string) string {
								switch transactionType {
								case "consume":
									return "消费"
								case "refund":
									return "退款"
								}
								return ""
							}(item.Type),
							item.OrderNumber,
							cast.ToString(cast.ToFloat64(item.Amount) / 100),
							cast.ToString(cast.ToFloat64(item.Balance) / 100),
							util.MustTransStrToTime(item.CreatedAt).Format("2006-01-02 15:04:05"),
							func() string {
								// 第三方订单
								if item.OrderId == "" {
									return item.OrderNumber
								}
								return ""
							}(),
							item.StoreName,
							item.StoreCode,
						}
						w.Write(line)
					}
					if isExportById {
						req.LastId = prepaidCardHistories[len(prepaidCardHistories)-1].Id.Hex()
					} else {
						pageIndex++
					}
					w.Flush()
				}
				return nil
			},
		)
		if err != nil {
			return err
		}
		err = job_util.ExportSucceed(ctx, jobId, url, job.Name)
		if err != nil {
			return err
		}
		return nil
	},
}

func getExportListConditionOrderBys(condition *pb_request.ListCondition) []string {
	if condition == nil || len(condition.OrderBy) == 0 {
		return []string{"_id"}
	}

	if len(condition.OrderBy) == 1 {
		tempOrderBy := strings.Replace(condition.OrderBy[0], "createdAt", "_id", -1)
		return []string{tempOrderBy}
	}
	return condition.OrderBy
}

func getPrepaidCardName(item *pb_wallet.PrepaidCardHistory) string {
	if item.PrepaidCardName != "" {
		return item.PrepaidCardName
	}
	return "-"
}

func getPrepaidCardNumber(item *pb_wallet.PrepaidCardHistory) string {
	if item.PrepaidCardNumber != "" {
		return item.PrepaidCardNumber
	}
	return "-"
}

func getBatchNumber(item *pb_wallet.PrepaidCardHistory, briefInfoMap map[string]service_wallet.PrepaidCardBriefInfo) string {
	briefInfo, ok := briefInfoMap[item.PrepaidCardId]
	batchNumber := "-"
	if ok && briefInfo.StoreValueCardBatchNumber != "" {
		batchNumber = briefInfo.StoreValueCardBatchNumber
	}
	return batchNumber
}

func getPrepaidCardType(item *pb_wallet.PrepaidCardHistory) string {
	if item.CardType == "" {
		return "-"
	}
	switch item.CardType {
	case "online":
		return "线上"
	case "offline":
		return "线下"
	default:
		return item.CardType
	}
}
