package store

import (
	"time"

	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

var (
	CChainCorp = ChainCorp{}
)

const C_CHAIN_CORP = "ec.chainCorp"

type ChainCorp struct {
	Id                 bson.ObjectId `bson:"_id"`
	AccountId          bson.ObjectId `bson:"accountId"`
	ChannelId          string        `bson:"channelId"`
	ChainCorpChannelId string        `bson:"chainCorpChannelId"`
	AppId              string        `bson:"appId"`
	CorpName           string        `bson:"corpName"`
	Name               string        `bson:"name"`
	CorpId             string        `bson:"corpId"`
	CodePrefix         string        `bson:"codePrefix"` // 下游企业所属门店 code 前缀
	CreatedAt          time.Time     `bson:"createdAt"`
	UpdatedAt          time.Time     `bson:"updatedAt"`
	IsDeleted          bool          `bson:"isDeleted"`
	LicenseCount       int64         `bson:"licenseCount"`
}

func (o *ChainCorp) Create(ctx context.Context) error {
	o.Id = bson.NewObjectId()
	o.CreatedAt = time.Now()
	o.UpdatedAt = o.CreatedAt
	o.IsDeleted = false
	_, err := extension.DBRepository.Insert(ctx, C_CHAIN_CORP, *o)
	if err != nil {
		return err
	}
	return nil
}

func (o *ChainCorp) FindAll(ctx context.Context) ([]ChainCorp, error) {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	result := []ChainCorp{}
	err := extension.DBRepository.FindAll(ctx, C_CHAIN_CORP, condition, []string{}, 0, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (*ChainCorp) FindOne(ctx context.Context, selector bson.M) (ChainCorp, error) {
	result := ChainCorp{}
	err := extension.DBRepository.FindOne(ctx, C_CHAIN_CORP, selector, &result)
	return result, err
}

func (*ChainCorp) Have(ctx context.Context) bool {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	count, _ := extension.DBRepository.Count(ctx, C_CHAIN_CORP, condition)
	return count > 0
}

func (*ChainCorp) Count(ctx context.Context) int {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	count, _ := extension.DBRepository.Count(ctx, C_CHAIN_CORP, condition)
	return count
}

func (*ChainCorp) FindByPagination(ctx context.Context, page extension.PagingCondition) ([]ChainCorp, int, error) {
	var result []ChainCorp
	total, err := extension.DBRepository.FindByPagination(ctx, C_CHAIN_CORP, page, &result)
	if err != nil {
		return nil, 0, err
	}
	return result, total, nil
}

func (*ChainCorp) UpdateLicenseCount(ctx context.Context, chainCorpChannelId string, count int64) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["chainCorpChannelId"] = chainCorpChannelId
	updater := bson.M{
		"$inc": bson.M{
			"licenseCount": count,
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_CHAIN_CORP, selector, updater)
}
