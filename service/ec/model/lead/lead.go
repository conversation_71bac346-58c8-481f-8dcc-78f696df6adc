package lead

import (
	"context"
	"time"

	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/service/share/model"
)

const (
	C_LEAD = "ec.lead"

	STATUS_PROSPECTIVE = "prospective" // 潜客
	STATUS_FOLLOWING   = "following"   // 跟进中
	STATUS_CONVERTED   = "converted"   // 成交客户
	STATUS_LOST        = "lost"        // 流失客户
	STATUS_CLOSED      = "closed"      // 已关闭
)

var (
	CLead = &Lead{}
)

// LeadProperty 线索属性
type LeadPropertyStruct struct {
	Id           bson.ObjectId `bson:"id"`                     // leadProperty _id
	Name         string        `bson:"name"`                   // leadProperty name
	NameCN       string        `bson:"nameCN"`                 // leadProperty nameCN
	Order        uint32        `bson:"order"`                  // 字段排序
	StringValue  string        `bson:"stringValue,omitempty"`  // 字段值
	IntegerValue uint32        `bson:"integerValue,omitempty"` // 数字值
	BooleanValue bool          `bson:"booleanValue,omitempty"` // 布尔值
	DateValue    time.Time     `bson:"dateValue,omitempty"`    // 日期值
	Type         string        `bson:"type"`                   // 属性类型
}

type Lead struct {
	Id                        bson.ObjectId        `bson:"_id"`
	AccountId                 bson.ObjectId        `bson:"accountId"`
	CreatedAt                 time.Time            `bson:"createdAt"`
	UpdatedAt                 time.Time            `bson:"updatedAt"`
	IsDeleted                 bool                 `bson:"isDeleted"`
	Name                      string               `bson:"name"`                                // 客户名称
	Phone                     string               `bson:"phone"`                               // 手机号码
	MemberId                  bson.ObjectId        `bson:"memberId,omitempty"`                  // 关联的客户ID
	ThirdMemberId             string               `bson:"thirdMemberId,omitempty"`             // 第三方客户ID
	ThirdPartyId              string               `bson:"thirdPartyId,omitempty"`              // 第三方ID
	ThirdPartyUpdatedAt       time.Time            `bson:"thirdPartyUpdatedAt,omitempty"`       // 第三方更新时间
	Gender                    string               `bson:"gender,omitempty"`                    // 性别
	Industry                  string               `bson:"industry,omitempty"`                  // 行业
	IntentionLevel            uint32               `bson:"intentionLevel,omitempty"`            // 客户意向等级，支持排序
	PurchaseBudget            uint32               `bson:"purchaseBudget,omitempty"`            // 购买预算，支持排序
	Status                    string               `bson:"status"`                              // 线索状态：prospective（潜客）、following（跟进中）、converted（成交客户）、lost（流失客户）、closed（已关闭）
	LostReason                string               `bson:"lostReason,omitempty"`                // 流失原因
	CloseReason               string               `bson:"closeReason,omitempty"`               // 关闭原因
	Source                    string               `bson:"source"`                              // 线索来源：external（企微外部联系人）、staff（经销商导购手动创建）、import（导入）
	SourceActivity            string               `bson:"sourceActivity,omitempty"`            // 来源激活来源
	InvitationType            string               `bson:"invitationType,omitempty"`            // 邀请类型
	InvitedAt                 time.Time            `bson:"invitedAt,omitempty"`                 // 邀约时间
	DistributorId             bson.ObjectId        `bson:"distributorId,omitempty"`             // 所属经销商ID
	StaffId                   bson.ObjectId        `bson:"staffId,omitempty"`                   // 负责导购ID
	IsAssigned                bool                 `bson:"isAssigned"`                          // 是否已分配导购
	LastFollowUpAt            time.Time            `bson:"lastFollowUpAt,omitempty"`            // 最后跟进时间，支持排序
	NextFollowUpAt            time.Time            `bson:"nextFollowUpAt,omitempty"`            // 下次计划跟进时间，支持排序
	NextPlan                  string               `bson:"nextPlan,omitempty"`                  // 下次计划
	TrialProductName          string               `bson:"trialProductName,omitempty"`          // 试用商品名称
	TrialAt                   time.Time            `bson:"trialAt,omitempty"`                   // 试用时间
	ProductConditionIntention string               `bson:"productConditionIntention,omitempty"` // 商品新旧程度意向 new/used
	IntentionProductName      string               `bson:"intentionProductName,omitempty"`      // 意向商品名称
	Properties                []LeadPropertyStruct `bson:"properties,omitempty"`                // 线索属性
	Remark                    string               `bson:"remark,omitempty"`                    // 备注
}

func (l *Lead) Insert(ctx context.Context) error {
	if l.Id.IsZero() {
		l.Id = bson.NewObjectId()
	}
	now := time.Now()
	l.CreatedAt = now
	l.UpdatedAt = now
	_, err := extension.DBRepository.Insert(ctx, C_LEAD, l)
	return err
}

func (*Lead) Update(ctx context.Context, selector, updater bson.M) error {
	return extension.DBRepository.UpdateOne(ctx, C_LEAD, selector, updater)
}

func (*Lead) UpdateAll(ctx context.Context, selector, updater bson.M) error {
	_, err := extension.DBRepository.UpdateAll(ctx, C_LEAD, selector, updater)
	return err
}

func (*Lead) FindById(ctx context.Context, id bson.ObjectId) (*Lead, error) {
	result := &Lead{}
	selector := model.Base.GenDefaultCondition(ctx)
	selector["_id"] = id
	err := extension.DBRepository.FindOne(ctx, C_LEAD, selector, &result)
	return result, err
}

func (*Lead) FindAll(ctx context.Context, selector bson.M, sorter []string) ([]Lead, error) {
	result := []Lead{}
	err := extension.DBRepository.FindAll(ctx, C_LEAD, selector, sorter, 0, &result)
	return result, err
}

func (*Lead) FindByMemberId(ctx context.Context, memberId bson.ObjectId) (*Lead, error) {
	result := &Lead{}
	selector := model.Base.GenDefaultCondition(ctx)
	selector["memberId"] = memberId
	err := extension.DBRepository.FindOne(ctx, C_LEAD, selector, &result)
	return result, err
}

func (*Lead) FindByThirdMemberId(ctx context.Context, thirdMemberId string) (*Lead, error) {
	result := &Lead{}
	selector := model.Base.GenDefaultCondition(ctx)
	selector["thirdMemberId"] = thirdMemberId
	err := extension.DBRepository.FindOne(ctx, C_LEAD, selector, &result)
	return result, err
}

func (*Lead) FindByPhone(ctx context.Context, phone string) (*Lead, error) {
	result := &Lead{}
	selector := model.Base.GenDefaultCondition(ctx)
	selector["phone"] = phone
	err := extension.DBRepository.FindOne(ctx, C_LEAD, selector, &result)
	return result, err
}

func (*Lead) Count(ctx context.Context, selector bson.M) (int, error) {
	return extension.DBRepository.Count(ctx, C_LEAD, selector)
}

func (*Lead) UpdateProperties(ctx context.Context, id bson.ObjectId, properties []LeadPropertyStruct) error {
	selector := model.Base.GenDefaultConditionById(ctx, id)
	updater := bson.M{
		"$set": bson.M{
			"properties": properties,
			"updatedAt":  time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_LEAD, selector, updater)
}

func (*Lead) FindByCondition(ctx context.Context, pageCondition extension.PagingCondition) (int, *[]Lead, error) {
	result := []Lead{}

	// 检查是否需要特殊处理 NextFollowUpAt 字段的排序
	needsSpecialSort := false
	for _, sorter := range pageCondition.Sortor {
		if sorter == "nextFollowUpAt" || sorter == "-nextFollowUpAt" {
			needsSpecialSort = true
			break
		}
	}

	if !needsSpecialSort {
		total, err := extension.DBRepository.FindByPagination(ctx, C_LEAD, pageCondition, &result)
		return total, &result, err
	}

	return (*Lead)(nil).findByConditionWithNullHandling(ctx, pageCondition, &result)
}

func (*Lead) findByConditionWithNullHandling(ctx context.Context, pageCondition extension.PagingCondition, result *[]Lead) (int, *[]Lead, error) {
	pipeline := []bson.M{}

	if len(pageCondition.Selector) > 0 {
		pipeline = append(pipeline, bson.M{"$match": pageCondition.Selector})
	}

	pipeline = append(pipeline, bson.M{
		"$addFields": bson.M{
			"_nextFollowUpAtIsNull": bson.M{
				"$cond": bson.M{
					"if": bson.M{
						"$or": []interface{}{
							bson.M{"$eq": []interface{}{"$nextFollowUpAt", nil}},
							bson.M{"$eq": []interface{}{bson.M{"$type": "$nextFollowUpAt"}, "missing"}},
						},
					},
					"then": 1,
					"else": 0,
				},
			},
		},
	})

	sortCondition := bson.M{}
	sortCondition["_nextFollowUpAtIsNull"] = 1
	for _, sorter := range pageCondition.Sortor {
		if sorter == "nextFollowUpAt" {
			sortCondition["nextFollowUpAt"] = 1
		} else if sorter == "-nextFollowUpAt" {
			sortCondition["nextFollowUpAt"] = -1
		} else if sorter != "" {
			if sorter[0] == '-' {
				sortCondition[sorter[1:]] = -1
			} else {
				sortCondition[sorter] = 1
			}
		}
	}

	pipeline = append(pipeline, bson.M{"$sort": sortCondition})
	pipeline = append(pipeline, bson.M{
		"$project": bson.M{
			"_nextFollowUpAtIsNull": 0,
		},
	})

	countPipeline := []bson.M{}
	copy(countPipeline, pipeline)
	countPipeline = append(countPipeline, bson.M{"$count": "total"})

	var countResult []bson.M
	err := extension.DBRepository.Aggregate(ctx, C_LEAD, countPipeline, false, &countResult)
	if err != nil {
		return 0, nil, err
	}

	total := 0
	if len(countResult) > 0 {
		if totalValue, ok := countResult[0]["total"]; ok {
			if totalInt, ok := totalValue.(int32); ok {
				total = int(totalInt)
			} else if totalInt64, ok := totalValue.(int64); ok {
				total = int(totalInt64)
			}
		}
	}

	skip := (pageCondition.PageIndex - 1) * pageCondition.PageSize
	pipeline = append(pipeline, bson.M{"$skip": skip})
	pipeline = append(pipeline, bson.M{"$limit": pageCondition.PageSize})

	err = extension.DBRepository.Aggregate(ctx, C_LEAD, pipeline, false, result)
	if err != nil {
		return 0, nil, err
	}

	return total, result, nil
}
