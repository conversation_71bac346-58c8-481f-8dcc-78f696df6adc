package chain_retail

import (
	"context"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/service/share/util"
	"time"
)

var CYeepayMicroRuwangRecord = &YeepayMicroRuwangRecord{}

const (
	C_YEEPAY_MICRO_RUWANG_RECORD = "ec.yeepayMicroRuwangRecord"

	STATUS_SUBMITTED        = "SUBMITTED"        // 已提交审核
	STATUS_REVIEWING        = "REVIEWING"        // 申请审核中
	STATUS_REVIEW_BACK      = "REVIEW_BACK"      // 申请已驳回
	STATUS_BUSINESS_OPENING = "BUSINESS_OPENING" // 业务开通中
	STATUS_COMPLETED        = "COMPLETED"        // 申请已完成
	STATUS_FAILED           = "FAILED"           // 提交审核失败

	TYPE_MICRO    = "micro"    // 分账接收方
	TYPE_MERCHANT = "merchant" // 入驻商户
)

type YeepayMicroRuwangRecord struct {
	Id           bson.ObjectId `bson:"_id,omitempty"`
	AccountId    bson.ObjectId `bson:"accountId"`
	CreatedAt    time.Time     `bson:"createdAt"`
	UpdatedAt    time.Time     `bson:"updatedAt"`
	IsDeleted    bool          `bson:"isDeleted"`
	StaffId      bson.ObjectId `bson:"staffId"`
	MemberId     bson.ObjectId `bson:"memberId"`
	Phone        string        `bson:"phone"`
	SellerInfo   SellerInfo    `bson:"sellerInfo"`
	AuditOpinion string        `bson:"auditOpinion"` // 审核意见
	Status       string        `bson:"status"`
	Type         string        `bson:"type"` // 商户类型 micro(分账接收方) merchant(入驻商户)，为空是默认是 micro
}

type Location struct {
	City     string `bson:"city"`
	Detail   string `bson:"detail"`
	District string `bson:"district"`
	FullName string `bson:"fullName"`
	Province string `bson:"province"`
}

type SellerInfo struct {
	BusinessRole     string          `bson:"businessRole"`
	ParentMerchantNo string          `bson:"parentMerchantNo"`
	MerchantNo       string          `bson:"merchantNo"`
	BankAccountInfo  BankAccountInfo `bson:"bankAccountInfo"`
	ProductInfo      []ProductInfo   `bson:"productInfo"`
	IdCardInfo       IdCardInfo      `bson:"idCardInfo"`
}

type BankAccountInfo struct {
	AccountBank         string `bson:"accountBank"`
	AccountName         string `bson:"accountName"`
	AccountNumber       string `bson:"accountNumber"`
	BankAccountType     string `bson:"bankAccountType"`
	BankAddressCode     string `bson:"bankAddressCode"`
	BankName            string `bson:"bankName"`
	SettlementDirection string `bson:"settlementDirection"`
}

type FunctionServiceQualificationInfo struct {
	ShareCertificate string `bson:"shareCertificate"`
	ShareScene       string `bson:"shareScene"`
}

type ProductInfo struct {
	FixedRate     string `bson:"fixedRate"`
	MinRate       string `bson:"minRate"`
	PaymentMethod string `bson:"paymentMethod"`
	PercentRate   string `bson:"percentRate"`
	ProductCode   string `bson:"productCode"`
	RateType      string `bson:"rateType"`
	Undertaker    string `bson:"undertaker"`
}

type IdCardInfo struct {
	CardPeriodBegin string `bson:"cardPeriodBegin"`
	CardPeriodEnd   string `bson:"cardPeriodEnd"`
	IdCardAddress   string `bson:"idCardAddress"`
	IdCardCopy      string `bson:"idCardCopy"`
	IdCardName      string `bson:"idCardName"`
	IdCardNational  string `bson:"idCardNational"`
	IdCardNumber    string `bson:"idCardNumber"`
}

func (y *YeepayMicroRuwangRecord) Create(ctx context.Context) error {
	if !y.Id.Valid() {
		y.Id = bson.NewObjectId()
	}
	y.CreatedAt = time.Now()
	y.UpdatedAt = time.Now()
	y.AccountId = util.GetAccountIdAsObjectId(ctx)
	_, err := extension.DBRepository.Insert(ctx, C_YEEPAY_MICRO_RUWANG_RECORD, y)
	if err != nil {
		return err
	}
	return nil
}

func (y *YeepayMicroRuwangRecord) GetById(ctx context.Context, id bson.ObjectId) *YeepayMicroRuwangRecord {
	selector := Common.GenDefaultConditionById(ctx, id)
	data := YeepayMicroRuwangRecord{}
	err := extension.DBRepository.FindOne(ctx, C_YEEPAY_MICRO_RUWANG_RECORD, selector, &data)
	if err != nil || !data.Id.Valid() {
		return nil
	}
	return &data
}

func (y *YeepayMicroRuwangRecord) GetByStaffId(ctx context.Context, staffId bson.ObjectId) *YeepayMicroRuwangRecord {
	selector := Common.GenDefaultCondition(ctx)
	selector["staffId"] = staffId
	data := YeepayMicroRuwangRecord{}
	err := extension.DBRepository.FindOne(ctx, C_YEEPAY_MICRO_RUWANG_RECORD, selector, &data)
	if err != nil || !data.Id.Valid() {
		return nil
	}
	return &data
}

func (y *YeepayMicroRuwangRecord) GetByCondition(ctx context.Context, selector bson.M) (YeepayMicroRuwangRecord, error) {
	data := YeepayMicroRuwangRecord{}
	err := extension.DBRepository.FindOne(ctx, C_YEEPAY_MICRO_RUWANG_RECORD, selector, &data)
	return data, err
}

func (y *YeepayMicroRuwangRecord) Update(ctx context.Context) error {
	selector := Common.GenDefaultConditionById(ctx, y.Id)
	settor := bson.M{
		"sellerInfo":   y.SellerInfo,
		"status":       y.Status,
		"auditOpinion": y.AuditOpinion,
		"phone":        y.Phone,
		"type":         y.Type,
		"updatedAt":    time.Now(),
	}
	updator := bson.M{
		"$set": settor,
	}
	err := extension.DBRepository.UpdateOne(ctx, C_YEEPAY_MICRO_RUWANG_RECORD, selector, updator)
	return err
}

func (*YeepayMicroRuwangRecord) Iterate(ctx context.Context, condition bson.M) (extension.IterWrapper, error) {
	return extension.DBRepository.Iterate(ctx, C_YEEPAY_MICRO_RUWANG_RECORD, condition, nil)
}
