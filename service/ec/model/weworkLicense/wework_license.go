package weworkLicense

import (
	"context"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/service/share/util"
	"time"
)

const (
	C_WEWORK_LICENSE                   = "ec.weworkLicense"
	WEWORK_LICENSE_STATUS_ACTIVATED    = "activated"
	WEWORK_LICENSE_STATUS_INACTIVATED  = "inactivated"
	WEWORK_LICENSE_STATUS_EXPIRED      = "expired"
	WEWORK_LICENSE_STATUS_EXPIRED_SOON = "expiredSoon"
)

var CWeworkLicense = &WeworkLicense{}

type WeworkLicense struct {
	Id                 bson.ObjectId `bson:"_id"`
	AccountId          bson.ObjectId `bson:"accountId"`
	StaffId            bson.ObjectId `bson:"staffId"`
	Status             string        `bson:"status"`
	ChainCorpChannelId string        `bson:"chainCorpChannelId,omitempty"`
	StartAt            time.Time     `bson:"startAt,omitempty"`
	EndAt              time.Time     `bson:"endAt,omitempty"`
	CreatedAt          time.Time     `bson:"createdAt"`
	UpdatedAt          time.Time     `bson:"updatedAt"`
	IsDeleted          bool          `bson:"isDeleted"`
}

func (*WeworkLicense) Count(ctx context.Context, selector bson.M) (int, error) {
	return extension.DBRepository.Count(ctx, C_WEWORK_LICENSE, selector)
}

func (*WeworkLicense) CountByStatus(ctx context.Context, status string) (int, error) {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"status":    status,
	}
	return extension.DBRepository.Count(ctx, C_WEWORK_LICENSE, condition)
}

func (self *WeworkLicense) Create(ctx context.Context) error {
	self.Id = bson.NewObjectId()
	self.AccountId = util.GetAccountIdAsObjectId(ctx)
	self.IsDeleted = false
	self.CreatedAt, self.UpdatedAt = time.Now(), time.Now()
	_, err := extension.DBRepository.Insert(ctx, C_WEWORK_LICENSE, self)
	return err
}

func (*WeworkLicense) BatchUpsert(ctx context.Context, docs []interface{}) error {
	_, err := extension.DBRepository.BatchUpsert(ctx, C_WEWORK_LICENSE, docs...)
	if err != nil && len(err.WriteErrors) > 0 {
		return err
	}
	return nil
}

func (*WeworkLicense) GetAllByCondition(ctx context.Context, condition bson.M) ([]WeworkLicense, error) {
	results := []WeworkLicense{}
	err := extension.DBRepository.FindAll(ctx, C_WEWORK_LICENSE, condition, []string{}, 0, &results)
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (*WeworkLicense) GetByStaffId(ctx context.Context, staffId bson.ObjectId) (WeworkLicense, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["staffId"] = staffId
	result := WeworkLicense{}
	err := extension.DBRepository.FindOne(ctx, C_WEWORK_LICENSE, selector, &result)
	return result, err
}

func (*WeworkLicense) UpdateStatusByEndAt(ctx context.Context, staffId bson.ObjectId, status string, endAt time.Time) error {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"staffId":   staffId,
	}
	updater := bson.M{
		"$set": bson.M{
			"status":    status,
			"endAt":     endAt,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_WEWORK_LICENSE, condition, updater)
}

func (*WeworkLicense) UpdateStatus(ctx context.Context, staffId bson.ObjectId, status string) error {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"staffId":   staffId,
	}
	updater := bson.M{
		"$set": bson.M{
			"status":    status,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_WEWORK_LICENSE, condition, updater)
}

func (*WeworkLicense) DeleteAllByCondition(ctx context.Context, selector bson.M) error {
	updater := bson.M{
		"$set": bson.M{
			"isDeleted": true,
			"updatedAt": time.Now(),
		},
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_WEWORK_LICENSE, selector, updater)
	return err
}

func (self *WeworkLicense) Upsert(ctx context.Context) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"staffId":   self.StaffId,
		"isDeleted": false,
	}
	setter := bson.M{
		"updatedAt": time.Now(),
		"startAt":   self.StartAt,
		"endAt":     self.EndAt,
		"status":    self.Status,
	}
	updater := bson.M{
		"$set": setter,
		"$setOnInsert": bson.M{
			"createdAt": time.Now(),
		},
	}
	if self.Status == WEWORK_LICENSE_STATUS_INACTIVATED {
		delete(setter, "startAt")
		delete(setter, "endAt")
		updater["$unset"] = bson.M{
			"startAt": "",
			"endAt":   "",
		}
	}
	_, err := extension.DBRepository.Upsert(ctx, C_WEWORK_LICENSE, selector, updater)
	if err != nil {
		return err
	}
	return nil
}
