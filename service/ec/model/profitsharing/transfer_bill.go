package profitsharing

import (
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	core_component "mairpc/core/component"
	"mairpc/core/component/yeepay"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	pb_account "mairpc/proto/account"
	pb_client "mairpc/proto/client"
	"mairpc/proto/common/request"
	pb_ec_distribution "mairpc/proto/ec/distribution"
	pb_order "mairpc/proto/ec/order"
	pb_member "mairpc/proto/member"
	"mairpc/service/ec/client"
	distribution_model "mairpc/service/ec/model/distribution"
	ec_share "mairpc/service/ec/share"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	"mairpc/service/share/model"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"

	qmgo_options "github.com/qiniu/qmgo/options"
	"github.com/spf13/cast"
	driver_options "go.mongodb.org/mongo-driver/mongo/options"
	"golang.org/x/net/context"
)

/*
 * 分账记录表
 * 分账发生时，每一次与微信（weconnect）结算生成一条记录
 * 分账的数据从预分账表（ReceiverOrderProfit）中取
 */

const (
	C_TRANSFER_BILL = "ec.transferBill"

	BILL_STATUS_PENDING       = "pending"
	BILL_STATUS_PROCESSING    = "processing"   // 分账处理中与 wechat 返回的 result 一致
	BILL_STATUS_SUCCESS       = "success"      // 分账成功与 wechat 返回的 result 一致
	BILL_STATUS_FAILED        = "failed"       // 发放失败
	BILL_STATUS_NOT_ISSUED    = "notIssued"    // 线下未结算
	BILL_STATUS_ISSUED        = "issued"       // 线下已结算
	BILL_STATUS_NO_COMMISSION = "noCommission" // 无需结算

	TRANSFER_CHANNEL_WECHAT_BALANCE       = "wechatBalance"
	TRANSFER_CHANNEL_WECHAT_PROFITSHARING = "wechatProfitsharing"
	TRANSFER_CHANNEL_WECHAT_BANK          = "wechatBank"
	TRANSFER_CHANNEL_OFFLINE              = "offline"
	TRANSFER_CHANNEL_YEEPAY               = "yeePay" // 易宝支付

	BILL_SOURCE_STAFF_DISTRIBUTION  = "staff_distribution"  // 分账来源为导购分销
	BILL_SOURCE_STAFF_INVITATION    = "staff_invitation"    // 分账来源为导购拉新激励
	BILL_SOURCE_STAFF_REDEMPTION    = "staff_redemption"    // 分账来源为导购卡券核销激励
	BIll_SOURCE_MEMBER_DISTRIBUTION = "member_distribution" // 分账来源为大众分销
	BILL_SOURCE_PROFITSHARING       = "profitsharing"
	BILL_SOURCE_COMMISSION          = "commission"

	BILL_TRANSFER_WECONNECT_STATUS_ERROR     = "ERROR"     // weconnect 请求微信 api 失败
	BILL_TRANSFER_WECONNECT_STATUS_FAILED    = "FAILED"    // 发放失败
	BILL_TRANSFER_WECONNECT_STATUS_BANK_FAIL = "BANK_FAIL" // 银行退票
	BILL_TRANSFER_WECONNECT_STATUS_FINISHED  = "FINISHED"  //
	BILL_TRANSFER_WECONNECT_STATUS_SUCCESS   = "SUCCESS"   //
	BILL_TRANSFER_WECONNECT_STATUS_CLOSED    = "CLOSED"    // 交易关闭,

	BILL_FAILED_MESSAGE                = "线下结算" // 失败原因
	BILL_FAILED_MESSAGE_MEMBER_BLOCKED = "黑名单用户不参与分佣"

	BACK_RECORDS_STATUS_PROCESSING = "PROCESSING" // 分账回退处理中
	BACK_RECORDS_STATUS_SUCCESS    = "SUCCESS"    // 分账回退成功

	WECHAT_TRANSFER_V3_STATUS_PROCESSING        = "PROCESSING"
	WECHAT_TRANSFER_V3_STATUS_WAIT_USER_CONFIRM = "WAIT_USER_CONFIRM"
	WECHAT_TRANSFER_V3_STATUS_TRANSFERING       = "TRANSFERING"
	WECHAT_TRANSFER_V3_STATUS_SUCCESS           = "SUCCESS"
	WECHAT_TRANSFER_V3_STATUS_FAIL              = "FAIL"
)

var (
	CTransferBill = &TransferBill{}

	triggerTransferChannels = []string{
		TRANSFER_CHANNEL_WECHAT_BALANCE,
		TRANSFER_CHANNEL_WECHAT_PROFITSHARING,
		TRANSFER_CHANNEL_WECHAT_BANK,
		TRANSFER_CHANNEL_YEEPAY,
	}

	WeconnectTransferResult = []string{
		BILL_TRANSFER_WECONNECT_STATUS_ERROR,
		BILL_TRANSFER_WECONNECT_STATUS_FAILED,
		BILL_TRANSFER_WECONNECT_STATUS_BANK_FAIL,
		BILL_TRANSFER_WECONNECT_STATUS_FINISHED,
		BILL_TRANSFER_WECONNECT_STATUS_SUCCESS,
	}
)

type TransferBill struct {
	Id           bson.ObjectId `bson:"_id,omitempty"`
	AccountId    bson.ObjectId `bson:"accountId"`
	OutTradeNo   string        `bson:"outTradeNo"` // 保证一个订单只分一次
	OrderTradeNo string        `bson:"orderTradeNo"`
	// Deprecated
	DetailId                 string               `bson:"detailId,omitempty"` // 微信流水号，与 weconnect 保持一致，转账失败没有流水号;易宝分账明细单号，失败没有
	Receiver                 BriefReceiver        `bson:"receiver"`
	ShareAmount              uint64               `bson:"shareAmount"`
	Income                   uint64               `bson:"income"`
	Tax                      uint64               `bson:"tax"`
	SingleOrderProfitCap     uint64               `bson:"singleOrderProfitCap"`
	Status                   string               `bson:"status"`
	Description              string               `bson:"description,omitempty"` // 分账描述
	FailedMessage            string               `bson:"failedMessage,omitempty"`
	Remarks                  string               `bson:"remarks"`
	FinishedAt               time.Time            `bson:"finishedAt,omitempty"`
	CreatedAt                time.Time            `bson:"createdAt"`
	UpdatedAt                time.Time            `bson:"updatedAt"`
	NeedRetryByWechatBalance bool                 `bson:"needRetryByWechatBalance"`
	IsDeleted                bool                 `bson:"isDeleted"`
	Source                   string               `bson:"source"`
	WechatTradeNos           []bson.ObjectId      `bson:"wechatTradeNos"`
	NeedRefreshFromWechat    bool                 `bson:"needRefreshFromWechat"`
	SubBills                 []SubBill            `bson:"subBills,omitempty"`
	DetailIds                []string             `bson:"detailIds,omitempty"`   // 子单分账的情况下，此字段表示所有子单的流水号
	BackRecords              []BackRecord         `bson:"backRecords,omitempty"` // 分账回退记录，零售使用易宝支付且支付后立即分账的租户，退款时会存在此字段
	WechatTransferV3Info     WechatTransferV3Info `bson:"wechatTransferV3Info,omitempty"`
	MarkAsCompletedBy        bson.ObjectId        `bson:"markAsCompletedBy,omitempty"` // 标记结算人
	OrderCount               int                  `bson:"orderCount"`

	// Exector 相关参数，用于通用执行流程
	ExecStatus string `bson:"execStatus"`
	// 用于限制执行时间间隔，防止被连续重复执行
	ExecTime time.Time       `bson:"execTime"`
	ExecLogs []model.ExecLog `bson:"execLogs"`
	// 仅用于子单执行过程中标识为临时账单
	IsSubBill bool `bson:"-"`
}

type WechatTransferV3Info struct {
	ConfirmCode      string    `bson:"confirmCode"`
	Status           string    `bson:"status"`
	IsMemberNotified bool      `bson:"notified"`
	CreatedAt        time.Time `bson:"createdAt"`
}

type BackRecord struct {
	ShareAmount         uint64    `bson:"shareAmount"`          // 原始账单分账金额
	Amount              uint64    `bson:"amount"`               // 回退金额
	DivideBackRequestId string    `bson:"divideBackRequestId"`  // 分账回退请求号
	BackAt              time.Time `bson:"backAt"`               // 分账回退时间
	Detail              string    `bson:"detail"`               // 分账资金归还明细
	FailReason          string    `bson:"failReason,omitempty"` // 失败原因
	Status              string    `bson:"status,omitempty"`     // 分账回退记录状态，默认 PROCESSING
}

type BriefReceiver struct {
	Id              bson.ObjectId `bson:"id"`
	TransferChannel string        `bson:"transferChannel"`
	AppId           string        `bson:"appId"`
	ChannelId       string        `bson:"channelId"`
	Name            string        `bson:"name"`
	Account         string        `bson:"account"`
	AccountType     string        `bson:"accountType"`
	BankCode        string        `bson:"bankCode,omitempty"`
	PromoterId      bson.ObjectId `bson:"promoterId,omitempty"`
	// 仅用于 transferChannel 为 wechatBank 和 wechatBalance 时，判断是否是使用“商家转账到零钱”接口
	IsV2Transfer bool `bson:"-"`
	// 仅用于 transferChannel 为 wechatBalance 时，判断是否是使用 2025 上线的“商家转账”接口
	IsV3Transfer bool `bson:"-"`
}

type SubBill struct {
	Id                   bson.ObjectId        `bson:"id"`
	WechatTradeNos       []bson.ObjectId      `bson:"wechatTradeNos"`
	DetailId             string               `bson:"detailId,omitempty"` // 微信流水号，与 weconnect 保持一致，转账失败没有流水号;易宝分账明细单号，失败没有
	Status               string               `bson:"status"`
	Income               uint64               `bson:"income"`
	TransferDate         time.Time            `bson:"transferDate"`
	FailedMessage        string               `bson:"failedMessage,omitempty"`
	FinishedAt           time.Time            `bson:"finishedAt,omitempty"`
	WechatTransferV3Info WechatTransferV3Info `bson:"wechatTransferV3Info,omitempty"`
}

type TransferRule struct {
	IsV2Transfer   bool
	IsV3Transfer   bool
	SingleGap      uint64 // 单次转账上限，单位：分
	DailyGap       uint64 // 单日转账上限，单位：分
	SplitThreshold uint64 // 账单拆分阈值，账单总金额超过阈值才会拆分，单位：分
}

func (bill *TransferBill) Create(ctx context.Context) error {
	// Attention: do not set Id
	bill.CreatedAt = time.Now()
	bill.UpdatedAt = time.Now()
	bill.WechatTradeNos = []bson.ObjectId{bson.NewObjectId()}
	// 设置需要执行的数据的 execStatus 为 pending，并添加 execLogs，用于 execTransferJob 中使用通用执行流程
	if util.StrInArray(bill.Receiver.TransferChannel, &triggerTransferChannels) && bill.Status == BILL_STATUS_PENDING {
		bill.ExecStatus = share_model.EXEC_STATUS_PENDING
		bill.ExecTime = time.Now()
		// execLogs 非必填，通用执行流程默认逻辑查询需要执行的数据只依赖 execStatus
		// 但默认逻辑中也会添加 execLogs，为了让 execStatus 的变化过程在 execLogs 中有完整的体现
		// 所以这里从 execStatus 的第一个状态便开始记录 execLogs 了
		bill.ExecLogs = []share_model.ExecLog{
			{
				ExecStatus: share_model.EXEC_STATUS_PENDING,
				RecordedAt: time.Now(),
			},
		}
	}
	_, err := extension.DBRepository.Insert(ctx, C_TRANSFER_BILL, bill)
	return err
}

func (*TransferBill) GetById(ctx context.Context, id bson.ObjectId) (TransferBill, error) {
	selector := Common.GenDefaultConditionById(ctx, id)

	transferBill := TransferBill{}
	err := extension.DBRepository.FindOne(ctx, C_TRANSFER_BILL, selector, &transferBill)
	return transferBill, err
}

func (TransferBill) GetByIds(ctx context.Context, ids []bson.ObjectId) ([]TransferBill, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = bson.M{
		"$in": ids,
	}
	transferBill := []TransferBill{}
	err := extension.DBRepository.FindAll(ctx, C_TRANSFER_BILL, selector, []string{}, 0, &transferBill)
	return transferBill, err
}

func (TransferBill) GetOneByCondition(ctx context.Context, selector bson.M) (TransferBill, error) {
	transferBill := TransferBill{}
	err := extension.DBRepository.FindOne(ctx, C_TRANSFER_BILL, selector, &transferBill)
	return transferBill, err
}

func (*TransferBill) GetAllByCondition(ctx context.Context, condition bson.M) ([]TransferBill, error) {
	transferBills := []TransferBill{}
	err := extension.DBRepository.FindAll(ctx, C_TRANSFER_BILL, condition, []string{}, 0, &transferBills)
	if err != nil {
		return nil, err
	}
	return transferBills, nil
}

func (TransferBill) GetByOrderTradeNo(ctx context.Context, orderTradeNo string) ([]TransferBill, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["orderTradeNo"] = orderTradeNo

	transferBill := []TransferBill{}
	err := extension.DBRepository.FindAll(ctx, C_TRANSFER_BILL, selector, []string{}, 0, &transferBill)
	return transferBill, err
}

func (TransferBill) GetByReceiverIdWithoutOffline(ctx context.Context, receiverId bson.ObjectId, extra bson.M) ([]TransferBill, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["receiver.id"] = receiverId
	for k, v := range extra {
		selector[k] = v
	}
	selector["receiver.transferChannel"] = bson.M{
		"$in": triggerTransferChannels,
	}
	transferBill := []TransferBill{}
	err := extension.DBRepository.FindAll(ctx, C_TRANSFER_BILL, selector, []string{}, 0, &transferBill)
	return transferBill, err
}

func (*TransferBill) GetByOutTradeNo(ctx context.Context, outTradeNo string) (TransferBill, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["outTradeNo"] = outTradeNo

	transferBill := TransferBill{}
	err := extension.DBRepository.FindOne(ctx, C_TRANSFER_BILL, selector, &transferBill)
	return transferBill, err
}

func (*TransferBill) GetByWechatTradeNo(ctx context.Context, wechatTradeNo bson.ObjectId) (TransferBill, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["wechatTradeNos"] = bson.M{
		"$elemMatch": bson.M{
			"$eq": wechatTradeNo,
		},
	}

	transferBill := TransferBill{}
	err := extension.DBRepository.FindOne(ctx, C_TRANSFER_BILL, selector, &transferBill)
	return transferBill, err
}

func (bill *TransferBill) UpdateToProcessing(ctx context.Context) error {
	bill.Status = BILL_STATUS_PROCESSING
	selector := bson.M{
		"accountId": bill.AccountId,
		"_id":       bill.Id,
		"isDeleted": false,
		"status":    BILL_STATUS_PENDING,
	}

	updater := bson.M{
		"$set": bson.M{
			"status":               BILL_STATUS_PROCESSING,
			"updatedAt":            time.Now(),
			"wechatTransferV3Info": bill.WechatTransferV3Info,
		},
	}

	return extension.DBRepository.UpdateOne(ctx, C_TRANSFER_BILL, selector, updater)
}

func (TransferBill) Count(ctx context.Context, condition bson.M) (int, error) {
	return extension.DBRepository.Count(ctx, C_TRANSFER_BILL, condition)
}

func (TransferBill) UpdateToPendingFromFailed(ctx context.Context, billIds []bson.ObjectId) (int, error) {
	selector := share_model.Base.GenDefaultCondition(ctx)
	selector["status"] = BILL_STATUS_FAILED
	selector["_id"] = bson.M{
		"$in": billIds,
	}
	selector["subBills"] = bson.M{
		"$exists": true,
	}
	setter := bson.M{
		"status":                  BILL_STATUS_PENDING,
		"execStatus":              share_model.EXEC_STATUS_PENDING,
		"execTime":                time.Now(),
		"retriedAt":               time.Now(),
		"updatedAt":               time.Now(),
		"subBills.$[elem].status": BILL_STATUS_PENDING,
		"execLogs":                []share_model.ExecLog{},
	}

	opt := qmgo_options.UpdateOptions{}
	arrayFilters := driver_options.ArrayFilters{
		Filters: []interface{}{
			bson.M{
				"elem.status": BILL_STATUS_FAILED,
			},
		},
	}
	opt.UpdateOptions = driver_options.Update().SetArrayFilters(arrayFilters)
	if _, err := extension.DBRepository.UpdateAllWithOptions(ctx, C_TRANSFER_BILL, selector, bson.M{"$set": setter}, opt); err != nil {
		return 0, err
	}

	delete(selector, "subBills")
	delete(setter, "subBills.$[elem].status")
	return extension.DBRepository.UpdateAll(ctx, C_TRANSFER_BILL, selector, bson.M{"$set": setter})
}

func (TransferBill) UpdateDescription(ctx context.Context, id bson.ObjectId, description string) error {
	selector := share_model.Base.GenDefaultConditionById(ctx, id)

	updater := bson.M{
		"$set": bson.M{
			"description": description,
			"updatedAt":   time.Now(),
		},
	}

	return extension.DBRepository.UpdateOne(ctx, C_TRANSFER_BILL, selector, updater)
}

// 更新 needRefreshFromWechat 为 true，会同时更新 status 为 processing，因为这种状态就是等待获取结果的。
func (*TransferBill) UpdateNeedRefreshFromWechat(ctx context.Context, id bson.ObjectId) error {
	condition := Common.GenDefaultConditionById(ctx, id)
	updater := bson.M{
		"$set": bson.M{
			"updatedAt":             time.Now(),
			"needRefreshFromWechat": true,
			"status":                BILL_STATUS_PROCESSING,
			"execStatus":            "pending",
			"execTime":              time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_TRANSFER_BILL, condition, updater)
}

// 设置为需要通过微信转账重试发放佣金等收益
// 注意：这个方法只是标记在发起转账时通过微信余额转账的方式发起转账，但触发转账还是需要依赖 status 和 execStatus
func (TransferBill) SetNeedRetryByWechatBalance(ctx context.Context, billIds []bson.ObjectId) error {
	selector := share_model.Base.GenDefaultCondition(ctx)
	selector["_id"] = bson.M{
		"$in": billIds,
	}

	updater := bson.M{
		"$set": bson.M{
			"needRetryByWechatBalance": true,
			"updatedAt":                time.Now(),
		},
	}

	_, err := extension.DBRepository.UpdateAll(ctx, C_TRANSFER_BILL, selector, updater)
	return err
}

// 更新账单状态为失败，收益记录中显示为发放失败
func (bill *TransferBill) UpdateToFailed(ctx context.Context) error {
	bill.Status = BILL_STATUS_FAILED
	selector := bson.M{
		"accountId": bill.AccountId,
		"_id":       bill.Id,
		"isDeleted": false,
		"status":    BILL_STATUS_PENDING,
	}

	updater := bson.M{
		"$set": bson.M{
			"status":        BILL_STATUS_FAILED,
			"failedMessage": bill.FailedMessage,
			"updatedAt":     time.Now(),
		},
	}

	err := extension.DBRepository.UpdateOne(ctx, C_TRANSFER_BILL, selector, updater)
	if err != nil {
		return err
	}
	// 同步更新发放状态到分销订单
	bill.UpdateOrderDistributionStatus(ctx)
	return nil
}

// 更新分账执行结果，只会是执行成功或者执行失败
func (bill *TransferBill) UpdateProfitResult(ctx context.Context) error {
	selector := bson.M{
		"accountId": bill.AccountId,
		"_id":       bill.Id,
		"isDeleted": false,
		"status":    BILL_STATUS_PROCESSING,
	}

	setter := bson.M{
		"status":    bill.Status,
		"updatedAt": time.Now(),
	}
	// 只有当账单状态是 failed 的时候才需要更新 needRetryByWechatBalance 为 false
	if bill.Status == BILL_STATUS_FAILED {
		setter["needRetryByWechatBalance"] = false
	} else {
		// 非失败状态将失败信息置空
		bill.FailedMessage = ""
		setter["failedMessage"] = ""
	}
	if bill.FailedMessage != "" {
		setter["failedMessage"] = bill.FailedMessage
	}
	if bill.FinishedAt.Unix() > 0 {
		setter["finishedAt"] = bill.FinishedAt
	}
	// 微信流水号，只有成功了才有值
	if bill.DetailId != "" {
		setter["detailId"] = bill.DetailId
		bill.DetailIds = util.StrArrayUnique(append(bill.DetailIds, bill.DetailId))
	}
	if len(bill.SubBills) > 0 {
		setter["subBills"] = bill.SubBills
	}
	setter["detailIds"] = bill.DetailIds
	setter["wechatTransferV3Info"] = bill.WechatTransferV3Info

	updater := bson.M{
		"$set": setter,
	}

	err := extension.DBRepository.UpdateOne(ctx, C_TRANSFER_BILL, selector, updater)
	if err != nil {
		return err
	}

	bill.UpdateOrderDistributionStatus(ctx)

	// 如果账单未执行结束，不发事件也不更新个税信息。
	if !CTransferBill.Finished(bill.Status) {
		return nil
	}

	// 发送分佣事件
	bill.SendPromoterMonthlyDistributionProfitEvent(ctx)

	// 分销员佣金到账发送短信通知
	err = bill.sendCommissionReceivedSMS(ctx)
	if err != nil {
		log.Warn(ctx, "Failed to send commission notification", log.Fields{
			"errorMessage": err.Error(),
		})
	}

	// 更新分销员的个税信息
	return bill.UpdatePromoterCountTax(ctx)
}

// 更新拆分过的账单执行结果
func (bill *TransferBill) UpdateProfitResultBySubBills(ctx context.Context) error {
	if len(bill.SubBills) == 0 {
		return nil
	}
	// 此时 bill 的状态应该是 processing 状态
	var (
		finished             = true
		failed               = false
		existsFailedMessages []string // 记录相同的子单报错信息。
		subBillDetailIds     []string
		finishedAt           []time.Time
	)
	bill.FailedMessage = ""

	for _, subBill := range bill.SubBills {
		if subBill.Status == BILL_STATUS_SUCCESS {
			subBillDetailIds = append(subBillDetailIds, subBill.DetailId)
			finishedAt = append(finishedAt, subBill.FinishedAt)
		}
		// 只要存在子账单的状态是 processing 或者 pending 状态，bill 整单的状态就应该是 processing
		if !CTransferBill.Finished(subBill.Status) {
			finished = false
		}
		// 账单全部分账结束后，只要有任意子单为 failed 状态，bill 整单的状态就应该是 failed
		if subBill.Status == BILL_STATUS_FAILED {
			failed = true
			if subBill.FailedMessage != "" && !util.StrInArray(subBill.FailedMessage, &existsFailedMessages) {
				existsFailedMessages = append(existsFailedMessages, subBill.FailedMessage)
				if bill.FailedMessage == "" {
					bill.FailedMessage = subBill.FailedMessage
				} else {
					bill.FailedMessage = fmt.Sprintf("%s|%s", bill.FailedMessage, subBill.FailedMessage)
				}
			}

		}
	}
	if finished {
		bill.Status = BILL_STATUS_SUCCESS
		bill.DetailIds = subBillDetailIds
		if failed {
			bill.Status = BILL_STATUS_FAILED
		} else if len(finishedAt) > 0 {
			// 如果都成功了，取最晚结束的子单的 finishedAt 作为主单的 finishedAt
			sort.Slice(finishedAt, func(i, j int) bool {
				return finishedAt[i].After(finishedAt[j])
			})
			bill.FinishedAt = finishedAt[0]
		}
	}

	return bill.UpdateProfitResult(ctx)
}

func (bill *TransferBill) UpdateOrderDistributionStatus(ctx context.Context) {
	promoterType := ""
	if bill.Source != BILL_SOURCE_STAFF_DISTRIBUTION && bill.Source != BIll_SOURCE_MEMBER_DISTRIBUTION {
		return
	}
	if bill.Source == BILL_SOURCE_STAFF_DISTRIBUTION {
		promoterType = "staff"
	} else {
		promoterType = "member"
	}
	core_component.GO(ctx, func(ctx context.Context) {
		// 仅处理分销月结
		_, err := client.OrderService.UpdateMonthlyDistribution(ctx, &pb_order.UpdateMonthlyDistributionRequest{
			BillId: bill.Id.Hex(),
			Type:   promoterType,
		})
		if err != nil {
			log.Warn(ctx, "Update order distribution status failed.", log.Fields{
				"err":    err.Error(),
				"billId": bill.Id.Hex(),
			})
		}
	})
}

func (bill *TransferBill) sendCommissionReceivedSMS(ctx context.Context) error {
	if bill.Status != BILL_STATUS_SUCCESS {
		return nil
	}
	condition := bson.M{
		"_id":       bill.Receiver.Id,
		"accountId": bill.AccountId,
		"isDeleted": false,
	}
	receiver, err := CProfitSharingReceiver.GetByDefaultCondition(ctx, condition)
	if err != nil {
		return err
	}
	if !util.StrInArray(receiver.ProfitType, &[]string{"distribution", "commission"}) {
		return nil
	}
	phone := ""
	rule := ""
	// 需要确保 promoterId 存在，否则会 panic，profitsharing 等类型的账单是没有 promoterId 的
	if receiver.ProfitType == "distribution" && bill.Receiver.PromoterId.Valid() {
		promoter, err := distribution_model.CPromoter.GetById(ctx, bill.Receiver.PromoterId)
		if err != nil {
			return err
		}
		phone = promoter.Phone
		if promoter.Type == "staff" {
			rule = constant.MESSAGE_RULE_GUIDE_COMMISSION_RECEIVED
		}
		if promoter.Type == "member" {
			rule = constant.MESSAGE_RULE_COMMISSION_RECEIVED
		}
	}
	if receiver.ProfitType == "commission" {
		if len(receiver.StoreIds) == 0 {
			return nil
		}
		store, err := client.StoreService.GetStore(ctx, &request.DetailRequest{Id: receiver.StoreIds[0].Hex()})
		if err != nil {
			return err
		}
		phone = store.Contact.Phone
		rule = constant.MESSAGE_RULE_CONSIGNMENT_COMMISSION_RECEIVED
	}

	income := strconv.FormatFloat(float64(bill.Income)/100, 'f', -1, 64)

	member, err := ec_share.GetMemberByPhone(ctx, phone)
	if err != nil {
		return err
	}

	distribution_model.NotifyCustomer(ctx, member, rule, income)
	return nil
}

// 只处理分销月结的个税信息
func (bill *TransferBill) UpdatePromoterCountTax(ctx context.Context) error {
	// 需要确保 promoterId 存在，否则会 panic，profitsharing 等类型的账单是没有 promoterId 的
	if bill.Tax == 0 || !bill.Receiver.PromoterId.Valid() {
		return nil
	}

	promoter, err := distribution_model.CPromoter.GetById(ctx, bill.Receiver.PromoterId)
	if err != nil {
		return err
	}
	if promoter.Type != distribution_model.PROMOTER_TYPE_STAFF && promoter.Type != distribution_model.PROMOTER_TYPE_MEMBER {
		return nil
	}

	// 获取分销设置
	dSetting, err := distribution_model.CDistributionSetting.GetByType(ctx, promoter.Type)
	if err != nil {
		return err
	}
	// 只有分销月结需要更新
	if dSetting.PromoterCommission.Cycle != distribution_model.CYCLE_MONTH {
		return nil
	}

	// 账单发放佣金状态不成功不处理分销员上月个税信息
	if bill.Status != BILL_STATUS_SUCCESS {
		// 如果发放佣金失败，也需要标识已分账
		if bill.Status == BILL_STATUS_FAILED {
			promoter.UpdatePromoterCountField(ctx, &distribution_model.PromoterCount{HasProfited: true}, util.GetMonthCountKey(time.Now(), -1))
		}
		return nil
	}

	return promoter.UpdatePromoterCountField(ctx, &distribution_model.PromoterCount{
		Amount:      int64(bill.ShareAmount),
		Income:      int64(bill.Income),
		Tax:         int64(bill.Tax),
		HasProfited: true,
	}, util.GetMonthCountKey(time.Now(), -1))
}

func (bill *TransferBill) SendPromoterMonthlyDistributionProfitEvent(ctx context.Context) {
	// 需要确保 promoterId 存在，否则会 panic，profitsharing 等类型的账单是没有 promoterId 的
	if bill.Status != BILL_STATUS_SUCCESS || !bill.Receiver.PromoterId.Valid() {
		return
	}

	promoter, err := distribution_model.CPromoter.GetById(ctx, bill.Receiver.PromoterId)
	if err != nil {
		return
	}

	dSetting, err := distribution_model.CDistributionSetting.GetByType(ctx, promoter.Type)
	if err != nil {
		return
	}
	if dSetting.PromoterCommission.Cycle != distribution_model.CYCLE_MONTH {
		return
	}

	receiver, err := CProfitSharingReceiver.GetByReceiverAccount(ctx, promoter.OpenId)
	if err != nil {
		return
	}

	// 分销月结以外的都通过预分账表处理且事件都在 CheckAndUpdateProfitsharingStatusJob 中处理了
	if receiver.Type != RECEIVER_TYPE_OPENID || receiver.ProfitType != PROFIT_TYPE_DISTRIBUTION {
		return
	}

	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_ORDER_PROFIT_AMOUNT + ":" + bill.Id.Hex(),
		AccountId:  bill.AccountId.Hex(),
		MemberId:   promoter.MemberId.Hex(), // 事件的接收方是导购对应的 member
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_ORDER_PROFIT_AMOUNT,
		CreateTime: bill.FinishedAt.UnixNano() / 1e6,
		// 分销月结携带扣税信息
		EventProperties: map[string]interface{}{
			"promoterId":   promoter.Id.Hex(),
			"profitAmount": bill.ShareAmount,
			"promoterType": promoter.Type,
			"tax":          bill.Tax,
			"income":       bill.Income,
			"staffId":      promoter.StaffId.Hex(),
		},
	}
	if eventBody.MemberId == "" {
		eventBody.ClientId = promoter.Id.Hex()
	}

	eventBody.Send(ctx)
}

func (*TransferBill) Iterate(ctx context.Context, selector bson.M, sorter []string) (extension.IterWrapper, error) {
	it, err := extension.DBRepository.Iterate(ctx, C_TRANSFER_BILL, selector, sorter)
	return it, err
}

func (b *TransferBill) GenTradeNoForSubBills(ctx context.Context) error {
	needUpdate := false
	for i, bill := range b.SubBills {
		if isWechatTradeNosError(bill.FailedMessage) && b.Status == BILL_STATUS_FAILED {
			b.SubBills[i].WechatTradeNos = append(b.SubBills[i].WechatTradeNos, bson.NewObjectId())
			needUpdate = true
		}
	}
	if needUpdate {
		condition := Common.GenDefaultConditionById(ctx, b.Id)
		updater := bson.M{
			"$set": bson.M{
				"subBills": b.SubBills,
			},
		}
		return extension.DBRepository.UpdateOne(ctx, C_TRANSFER_BILL, condition, updater)
	}
	return nil
}

func (TransferBill) BatchGenTradeNo(ctx context.Context, billIds []bson.ObjectId) error {
	selector := share_model.Base.GenDefaultCondition(ctx)
	selector["_id"] = bson.M{
		"$in": billIds,
	}

	docs := []interface{}{}
	for _, billId := range billIds {
		filter := bson.M{
			"accountId": util.GetAccountIdAsObjectId(ctx),
			"isDeleted": false,
			"_id":       billId,
		}
		updater := bson.M{
			"$push": bson.M{
				"wechatTradeNos": bson.NewObjectId(),
			},
			"$set": bson.M{
				"updatedAt": time.Now(),
			},
		}
		docs = append(docs, filter, updater)
	}

	_, bulkErr := extension.DBRepository.BatchUpdate(ctx, C_TRANSFER_BILL, docs...)
	if bulkErr != nil && len(bulkErr.WriteErrors) > 0 {
		return bulkErr
	}
	return nil
}

func (*TransferBill) DeleteByIds(ctx context.Context, ids []bson.ObjectId, remarks string) (int, error) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       bson.M{"$in": ids},
	}
	setter := bson.M{
		"isDeleted": true,
		"updatedAt": time.Now(),
	}
	if remarks != "" {
		setter["remarks"] = remarks
	}
	updator := bson.M{
		"$set": setter,
	}
	return extension.DBRepository.UpdateAll(ctx, C_TRANSFER_BILL, selector, updator)
}

func (*TransferBill) UpdateReceiverChannelId(ctx context.Context, receiverId bson.ObjectId, channelId string) error {
	condition := bson.M{
		"accountId":   util.GetAccountIdAsObjectId(ctx),
		"isDeleted":   false,
		"receiver.id": receiverId,
	}
	updater := bson.M{
		"$set": bson.M{
			"receiver.channelId": channelId,
			"updatedAt":          time.Now(),
		},
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_TRANSFER_BILL, condition, updater)
	return err
}

func (bill *TransferBill) IsWechatBalanceTransfer() bool {
	if bill.Receiver.TransferChannel == TRANSFER_CHANNEL_WECHAT_BALANCE {
		return true
	}
	return bill.NeedRetryByWechatBalance &&
		bill.Receiver.TransferChannel == TRANSFER_CHANNEL_WECHAT_PROFITSHARING &&
		bill.Receiver.AccountType != RECEIVER_TYPE_MERCHANT
}

func (bill *TransferBill) SplitByRule(ctx context.Context, rule TransferRule) error {
	// 不是商户转账到零钱的账单不拆分
	if !bill.IsWechatBalanceTransfer() {
		return nil
	}

	// 账单总金额不超过阈值不拆分
	if bill.Income <= rule.SplitThreshold {
		return nil
	}

	// 0.3 元以下无法转账，为了方便处理小于 0.3 元的子单，除群脉测试3 以外，其他租户要求单日上限不小于 0.6 元、单次上限不小于 0.6 元
	if bill.AccountId.Hex() != "5e7873c4c3307000f272c9e2" && (rule.DailyGap < 60 || rule.SingleGap < 60 || bill.Income <= 60) {
		return nil
	}

	if rule.SingleGap > rule.DailyGap {
		rule.SingleGap = rule.DailyGap
	}
	// 总账单金额不超过单次转账限制不拆分
	if bill.Income <= rule.SingleGap {
		return nil
	}

	now := time.Now()
	date := util.GetDayStartTime(now)
	dateBillCount := 0
	var dateIncome, incomeBalance uint64

	// 如果已有子单，则重新检查未转账或者转账失败的子单，判断是否符合 rule，不符合重新拆分
	if len(bill.SubBills) > 0 {
		subBills := []SubBill{}
		needSplit := false
		for i := range bill.SubBills {
			subBill := bill.SubBills[i]
			// 对于进行中和已完成的字段不重新检查规则
			if util.StrInArray(subBill.Status, &[]string{
				BILL_STATUS_PROCESSING,
				BILL_STATUS_SUCCESS,
			}) {
				subBills = append(subBills, subBill)
				continue
			}
			// 如果子单金额不超过规则单日上限，不再拆分
			// 如果子单报错内容是“查询结果超时”，不再拆分，因为可能已经发起过转账了，只是结果未更新
			if subBill.Income <= rule.SingleGap || strings.Contains(subBill.FailedMessage, "查询结果超时") {
				subBills = append(subBills, subBill)
				continue
			}
			log.Warn(ctx, "Split subBill again", log.Fields{
				"billId":  bill.Id.Hex(),
				"subBill": subBill,
				"rule":    rule,
			})
			needSplit = true
			incomeBalance += subBill.Income
		}
		if !needSplit {
			// 不需要再次拆分，直接返回
			return nil
		}
		bill.SubBills = subBills
	} else {
		incomeBalance = bill.Income
	}

	for incomeBalance > 0 {
		dateBillCount += 1
		subBill := SubBill{
			Id:             bson.NewObjectId(),
			WechatTradeNos: []bson.ObjectId{bson.NewObjectId()},
			Status:         BILL_STATUS_PENDING,
			Income:         rule.SingleGap,
			TransferDate:   date,
		}
		if subBill.Income > incomeBalance {
			subBill.Income = incomeBalance
		}
		if dateIncome+subBill.Income > rule.DailyGap {
			subBill.Income = rule.DailyGap - dateIncome
		}

		dateIncome += subBill.Income
		incomeBalance -= subBill.Income

		bill.SubBills = append(bill.SubBills, subBill)
		// 本轮拆分后达到当日可转账上限，或者单日子单数量达到 10 后
		// 重置日期和计数，下一轮开始重新以新的日期进行拆分生于金额
		if dateIncome == rule.DailyGap || dateBillCount == 10 {
			date = date.Add(time.Hour * 24)
			dateIncome = 0
			dateBillCount = 0
		}
	}

	// 当某个子单日期当日上限余数小于 0.3 元或者待拆分金额小于 0.3 元时
	// 该子单的金额可能小于 0.3 元，无法正常转账，需要从前面的子单中拆出 0.3 元增补
	for i := len(bill.SubBills) - 1; i > 0; i-- {
		if bill.SubBills[i].Income < 30 {
			for j := i - 1; j >= 0; j-- {
				if bill.SubBills[j].Income >= 60 {
					bill.SubBills[i].Income += 30
					bill.SubBills[j].Income -= 30
					break
				}
			}
		}
	}

	return extension.DBRepository.UpdateOne(ctx, C_TRANSFER_BILL, bson.M{
		"_id": bill.Id,
	}, bson.M{"$set": bson.M{
		"subBills":  bill.SubBills,
		"updatedAt": time.Now(),
	}})
}

func (*TransferBill) Finished(status string) bool {
	return util.StrInArray(status, &[]string{
		BILL_STATUS_SUCCESS,
		BILL_STATUS_FAILED,
	})
}

func (bill *TransferBill) GetNextExecTime() time.Time {
	// 默认五分钟后再次执行
	nextExecTime := time.Now().Add(time.Minute * 5)
	if bill.Receiver.IsV3Transfer {
		createdAt := bill.WechatTransferV3Info.CreatedAt
		waitConfirm := bill.WechatTransferV3Info.Status == WECHAT_TRANSFER_V3_STATUS_WAIT_USER_CONFIRM
		for _, subBill := range bill.SubBills {
			if subBill.WechatTransferV3Info.Status == WECHAT_TRANSFER_V3_STATUS_WAIT_USER_CONFIRM {
				waitConfirm = true
				if createdAt.After(subBill.WechatTransferV3Info.CreatedAt) {
					createdAt = subBill.WechatTransferV3Info.CreatedAt
				}
			}
		}
		if waitConfirm {
			nextExecTime = time.Now().Add(time.Hour * 12)
			// 最后一次查验时间设置为超时时间的一分钟后
			if !createdAt.IsZero() && createdAt.Add(time.Hour*24).Before(nextExecTime) {
				nextExecTime = createdAt.Add(time.Hour*24 + time.Minute)
			}
			if nextExecTime.Before(time.Now()) {
				return time.Now().Add(time.Minute * 5)
			}
		}
	}
	if len(bill.SubBills) == 0 {
		return nextExecTime
	}
	// 获取次日开始时间
	nextDate := util.GetStartTimeOfDay(time.Now().Add(time.Hour * 24))
	// 判断下一个子单日期之前的
	allBeforeNextDateFinished := true
	// 默认下个执行日期为最后一个子单的执行日期
	nextTransferDateInSubBills := bill.SubBills[len(bill.SubBills)-1].TransferDate
	for _, subBill := range bill.SubBills {
		if subBill.TransferDate.Before(nextDate) {
			if !CTransferBill.Finished(subBill.Status) {
				// 只要存在次日开始前未完成的子单，下次执行时间都为五分钟后
				allBeforeNextDateFinished = false
			}
			continue
		}
		if subBill.TransferDate.Before(nextTransferDateInSubBills) {
			nextTransferDateInSubBills = subBill.TransferDate
		}
	}
	if !allBeforeNextDateFinished {
		return nextExecTime
	}
	return nextTransferDateInSubBills
}

func (bill *TransferBill) Retry(ctx context.Context, needRetryByWechatBalance bool) error {
	if bill.Status != BILL_STATUS_FAILED {
		return errors.NewInvalidArgumentErrorWithMessage("status", "can only retry failed record")
	}
	billIds := []bson.ObjectId{bill.Id}
	if needRetryByWechatBalance {
		err := bill.SetNeedRetryByWechatBalance(ctx, billIds)
		if err != nil {
			return err
		}
	}
	if strings.HasSuffix(bill.FailedMessage, "查询结果超时") && len(bill.SubBills) == 0 {
		return bill.ReProcessing(ctx)
	}
	_, err := bill.UpdateToPendingFromFailed(ctx, billIds)
	return err
}

func (bill *TransferBill) NeedGenNewTradeNo() bool {
	return isWechatTradeNosError(bill.FailedMessage)
}

func isWechatTradeNosError(msg string) bool {
	return util.StrInArray(msg, &[]string{
		"商户员工（转账验密人）超时未验密",
		"对应单号已超出重试期,请查单确认后决定是否换单请求",
		"对应单号已超出重试期;请查单确认后决定是否换单请求",
		"转账关闭",
	})
}

func (bill *TransferBill) Update(ctx context.Context) {
	condition := Common.GenDefaultConditionById(ctx, bill.Id)
	updater := bson.M{
		"$set": bson.M{
			"backRecords": bill.BackRecords,
			"shareAmount": bill.ShareAmount,
			"income":      bill.Income,
			"updatedAt":   time.Now(),
		},
	}
	extension.DBRepository.UpdateOne(ctx, C_TRANSFER_BILL, condition, updater)
}

func (bill *TransferBill) MarkAsCompleted(ctx context.Context, id bson.ObjectId) error {
	now := time.Now()
	condition := Common.GenDefaultConditionById(ctx, id)
	setter := bson.M{
		"status":     BILL_STATUS_ISSUED,
		"finishedAt": now,
		"updatedAt":  now,
	}
	userId := core_util.GetUserId(ctx)
	if bson.IsObjectIdHex(userId) {
		setter["markAsCompletedBy"] = bson.ObjectIdHex(userId)
	}
	updater := bson.M{"$set": setter}
	err := extension.DBRepository.UpdateOne(ctx, C_TRANSFER_BILL, condition, updater)
	if err != nil {
		return err
	}
	bill.UpdateOrderDistributionStatus(ctx)
	return nil
}

func (bill *TransferBill) UpdateBackRecords(ctx context.Context, resp *yeepay.DivideBackResponse, amount uint64) {
	// 原始分账金额会随着退款更新，需要将最初的分账金额记录下来，用来计算最后一次分账回退金额
	shareAmount := bill.ShareAmount
	if len(bill.BackRecords) > 0 {
		shareAmount = bill.BackRecords[0].ShareAmount
	}
	backRecord := BackRecord{
		ShareAmount:         shareAmount,
		Amount:              amount,
		DivideBackRequestId: resp.DivideBackRequestId,
		Detail:              resp.DivideBackDetail,
		BackAt:              time.Now(),
		FailReason:          resp.FailReason,
		Status:              BACK_RECORDS_STATUS_PROCESSING,
	}
	bill.BackRecords = append(bill.BackRecords, backRecord)
	condition := bson.M{
		"_id":       bill.Id,
		"accountId": bill.AccountId,
	}
	updater := bson.M{
		"$set": bson.M{
			"backRecords": bill.BackRecords,
			"updatedAt":   time.Now(),
		},
	}
	extension.DBRepository.UpdateOne(ctx, C_TRANSFER_BILL, condition, updater)
}

func (bill *TransferBill) GetMember(ctx context.Context) (*pb_member.MemberDetailResponse, error) {
	promoter, err := pb_client.GetEcDistributionServiceClient().GetPromoter(ctx, &pb_ec_distribution.GetPromoterRequest{
		PromoterId: bill.Receiver.PromoterId.Hex(),
		IsBasic:    true,
	})
	if err != nil {
		return nil, err
	}
	return pb_client.GetMemberServiceClient().GetMember(ctx, &pb_member.MemberDetailRequest{
		Id: promoter.MemberId,
	})
}
func (bill *TransferBill) NotifyConfirm(ctx context.Context, rule *pb_account.NotificationSetting) {
	var (
		openId    = bill.Receiver.Account
		channelId = bill.Receiver.ChannelId
		amount    = bill.Income
	)
	bill.WechatTransferV3Info.IsMemberNotified = true
	for i, subBill := range bill.SubBills {
		if !subBill.WechatTransferV3Info.IsMemberNotified && subBill.WechatTransferV3Info.Status == WECHAT_TRANSFER_V3_STATUS_WAIT_USER_CONFIRM {
			amount += subBill.Income
			bill.SubBills[i].WechatTransferV3Info.IsMemberNotified = true
		}
	}
	defer func() {
		condition := Common.GenDefaultConditionById(ctx, bill.Id)
		setter := bson.M{
			"wechatTransferV3Info": bill.WechatTransferV3Info,
		}
		if len(bill.SubBills) > 0 {
			setter = bson.M{
				"subBills": bill.SubBills,
			}
		}
		extension.DBRepository.UpdateOne(ctx, C_TRANSFER_BILL, condition, bson.M{"$set": setter})
	}()
	member, err := bill.GetMember(ctx)
	if err != nil {
		return
	}
	placeholderValueMap := map[string]string{
		"CommissionSum": cast.ToString(float64(amount) / 100),
		"Month":         fmt.Sprintf("%s月", bill.CreatedAt.AddDate(0, -1, 0).Month()),
		"Status":        "待提现",
		"Remarks": func() string {
			if bill.Remarks != "" {
				return bill.Remarks
			}
			return bill.Description
		}(),
	}
	sendSubscribeMsg(ctx, member, rule, placeholderValueMap, openId, channelId, "distribution/pages/distribution-withdraw-records/index")
}

func sendSubscribeMsg(ctx context.Context, member *pb_member.MemberDetailResponse, noticeSetting *pb_account.NotificationSetting, placeholderValueMap map[string]string, openId, channelId, page string) error {
	if noticeSetting.SubscribeMessage == nil || !noticeSetting.SubscribeMessage.Enabled {
		return nil
	}
	subscribeMsg := &component.SubscribeMsg{}
	subscribeMsg.OriginId = openId
	subscribeMsg.AppSecret = false
	subscribeMsg.SubscribeMessage.TemplateId = noticeSetting.SubscribeMessage.Id
	if page != "" {
		subscribeMsg.SubscribeMessage.Page = page
	}

	var (
		subscribeMsgData = make(map[string]map[string]string)
		messagePairs     []*pb_account.NotificationMessagePair
	)
	for _, data := range noticeSetting.SubscribeMessage.Data {
		value := ec_share.FormatText(data.Value, placeholderValueMap)
		for k, v := range constant.PARAMETERS_LIMIT_MAP {
			valueRune := []rune(value)
			if strings.Contains(data.Key, k) && len(valueRune) > v {
				value = string(valueRune[:v])
				break
			}
		}
		subscribeMsgData[data.Key] = map[string]string{
			"value": value,
		}
		messagePairs = append(messagePairs, &pb_account.NotificationMessagePair{Key: data.Name, Value: value})
	}
	subscribeMsg.SubscribeMessage.Data = subscribeMsgData
	logReq := &pb_account.CreateMessageNotificationLogRequest{
		NotificationSettingId: noticeSetting.Id,
		Type:                  share_model.MESSAGE_NOTIFICATION_LOG_TYPE_SUBSCRIBE_MESSAGE,
		Status:                share_model.MESSAGE_NOTIFICATION_LOG_STATUS_SUCCEED,
		ChannelId:             channelId,
		MemberInfo: &pb_account.NotificationMember{
			MemberId: member.Id,
			Phone:    member.Phone,
			Name:     member.Name,
		},
		Message: &pb_account.NotificationMessage{
			Title: noticeSetting.SubscribeMessage.Title,
			Data:  messagePairs,
		},
	}
	defer pb_client.GetAccountServiceClient().CreateMessageNotificationLog(ctx, logReq)
	result, err := component.WeConnect.SendSubscribeMessage(ctx, channelId, subscribeMsg)
	if err != nil {
		logReq.Status = share_model.MESSAGE_NOTIFICATION_LOG_STATUS_FAILED
		logReq.Description = share_model.GenMessageNotificationLogDescription(share_model.MESSAGE_NOTIFICATION_LOG_TYPE_SUBSCRIBE_MESSAGE, result.ErrCode)
	}
	return err
}

func (bill *TransferBill) ReProcessing(ctx context.Context) error {
	condition := Common.GenDefaultConditionById(ctx, bill.Id)
	updater := bson.M{
		"$set": bson.M{
			"status":     BILL_STATUS_PROCESSING,
			"execStatus": BILL_STATUS_PENDING,
			"execLogs":   []share_model.ExecLog{},
			"execTime":   time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_TRANSFER_BILL, condition, updater)
}

func (bill *TransferBill) UpdateToPending(ctx context.Context) error {
	condition := Common.GenDefaultConditionById(ctx, bill.Id)
	updater := bson.M{
		"$set": bson.M{
			"status": BILL_STATUS_PENDING,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_TRANSFER_BILL, condition, updater)
}
