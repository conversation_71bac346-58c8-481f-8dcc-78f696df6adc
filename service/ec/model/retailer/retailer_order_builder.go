package retailer

import (
	"context"
	"encoding/json"
	"fmt"
	"mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/proto/client"
	common "mairpc/proto/common/ec"
	common_ec "mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	"mairpc/proto/common/response"
	pb_logistic "mairpc/proto/ec/logistic"
	pb_ec_order "mairpc/proto/ec/order"
	pb_ec_retailer "mairpc/proto/ec/retailer"
	pb_store "mairpc/proto/ec/store"
	pb_member "mairpc/proto/member"
	pb_product "mairpc/proto/product"
	ec_client "mairpc/service/ec/client"
	ec_model "mairpc/service/ec/model"
	ec_distribution "mairpc/service/ec/model/distribution"
	ec_logistic "mairpc/service/ec/model/logistic"
	ec_model_logistic "mairpc/service/ec/model/logistic"
	ec_order "mairpc/service/ec/model/order"
	ec_product "mairpc/service/ec/model/product"
	ec_setting "mairpc/service/ec/model/setting"
	ec_store "mairpc/service/ec/model/store"
	ec_store_product "mairpc/service/ec/model/storeProduct"
	ec_service "mairpc/service/ec/service"
	ec_service_order "mairpc/service/ec/service/order"
	"mairpc/service/share/component"
	"mairpc/service/share/util"
	"sort"
	"strings"
	"time"

	"github.com/spf13/cast"
)

// D2R佣金处理逻辑
// 门店佣金(渠道佣金)
//   1.经销订单没有门店佣金
//   2.代销订单门店佣金存在 ec.order.products.storeDistributionAmount 中，具体赋值逻辑在 service/ec/model/retailer/retailer_order_builder.go 文件 genBasicOrderProduct 方法中
//   3.有活动时（如限时特价），此时 sku 中的门店分佣金额已经不对，会在 retailer 服务 PrePay 方法中重新更新一遍，具体逻辑见 updateProfitAmountWithDiscountCampaign 方法
// 导购佣金
//   1.脉盟小店，脉盟小店没有导购，不会有导购佣金账单产生
//     1.经销商品没有导购佣金
//     2.代销商品，本身如果设置了佣金，需要将佣金发放给门店，具体赋值逻辑在 service/ec/model/retailer/retailer_order_builder.go 文件 genBasicOrderProduct 方法中
//   2.连锁零售商
//     1.经销商品和代销商品非分销订单，在 createSimpleOrder 时，商品中 distributionAmount 字段传入
//     1.经销商品和代销商品分销订单，存在 ec.order.products.distributionAmount 中，具体赋值逻辑在 service/ec/service/order/create_order.go 文件 calcDistributionAmount 方法中
//     2.有活动时（如限时特价），分销金额会变动，此时 sku 中的分销金额已经不对，会在 retailer 服务 PrePay 方法中重新更新一遍，具体逻辑见 updateProfitAmountWithDiscountCampaign 方法
//
// 注意：连锁零售商直接购买时，调用的是零售的 purchase 接口，门店和导购佣金会在 retailer 服务 PrePay 方法中重新更新一遍，具体逻辑见 updateStoreProductDistributionAmount 方法

var (
	CRetailerOrderBuilder              = &RetailerOrderBuilder{}
	MAXIMUM_DELIVERY_DISTANCE_EXCEEDED = "maximum delivery distance exceeded"
)

type RetailerOrderBuilder struct {
	MemberId                   string
	StoreId                    string
	MemberAddressId            string
	PromoterMemberId           string
	StaffId                    string
	Payment                    string // 支付方式，微信支付（wechat）
	Store                      *ec_store.Store
	StoreDetail                *pb_store.StoreDetail
	MemberAddress              *pb_member.MemberAddressDetail
	ProductSkus                []*ProductSku
	StoreProductMap            map[string]*ec_store_product.StoreProduct
	SplitProducts              []*SplitProduct
	CartProducts               []*ec_order.CartProduct
	EcProductIds               []string
	EcProductMap               map[interface{}]interface{}
	ProductIds                 []string
	ProductMap                 map[string]*pb_product.ProductDetailResponse
	TotalAmount                uint64 // 订单总金额
	PayAmount                  uint64 // 实际支付金额
	Channel                    *ec_order.OrderChannel
	Order                      *RetailerOrder
	Options                    *Options
	EcOrderTags                []string
	OrderProductPurchaseStats  []*ec_order.OrderProductPurchaseStats
	ProductPurchaseCountMap    map[bson.ObjectId]uint64
	InvalidProductSkus         []*ProductSku
	InvalidProductSkuStatusMap map[string]string
	ReducedConsignmentStockMap map[string]int
	TotalDeliveryFee           uint64 // 总运费，目前只有小店同城配送时有运费
	DistributionMethod         string // 经销商品配送方式
	Extra                      string // 额外信息，json 格式
}

type ProductSku struct {
	ProductId             string
	BrandId               string
	Sku                   string
	Count                 uint64
	SourcePrice           uint64
	Price                 uint64
	Properties            []string
	Stock                 uint64
	IsStockEnabled        bool
	CampaignId            string // D2R 限时特价活动 id ec.discountCampaign._id
	DiscountCampaignId    string // 零售限时折扣 eccampaign.discountCampaign._id
	DiscountCampaignTitle string
}

func (builder *RetailerOrderBuilder) GenBuilder(ctx context.Context, options ...Option) error {
	builder.Options = LoadOptions(options...)
	builder.EcOrderTags = []string{
		ec_order.ORDER_TAGS_MAIMENG, // maimeng 区分订单
	}
	// 这里调用顺序会有依赖，不要随意挪动
	builder.SetStore(ctx)
	err := builder.CheckStore(ctx)
	if err != nil {
		return err
	}
	err = builder.setCartProducts(ctx)
	if err != nil {
		return err
	}
	builder.SetEcProductIds()
	err = builder.SetStoreProductMap(ctx)
	if err != nil {
		return err
	}
	builder.SetProductIds(ctx)
	builder.SetProductMap(ctx)
	builder.SetProductSkus(ctx)
	err = builder.CheckProductAndPurchaseLimit(ctx)
	if err != nil {
		return err
	}
	err = builder.CheckAndReduceConsignmentProductsStock(ctx)
	if err != nil {
		return err
	}
	err = builder.FilterInvalidProducts(ctx)
	if err != nil {
		return err
	}
	builder.SetMemberAddress(ctx)
	builder.SetStaffId(ctx)
	builder.SetSplitProduct(ctx)
	builder.CalTotalAmount()
	builder.CalPayAmount()
	return nil
}

func (builder *RetailerOrderBuilder) SetStore(ctx context.Context) {
	if builder.Options.IsTest {
		return
	}
	store, _ := ec_store.CStore.GetById(ctx, util.ToMongoId(builder.StoreId))
	if store.Id.Valid() {
		builder.Store = &store
	}
	storeDetail, _ := ec_client.StoreService.GetStore(ctx, &request.DetailRequest{Id: builder.StoreId})
	if storeDetail.Id != "" {
		builder.StoreDetail = storeDetail
	}
}

func (builder *RetailerOrderBuilder) CheckStore(ctx context.Context) error {
	// 门店未开启，不能结算和下单
	if builder.Store != nil && builder.Store.Status != ec_store.STORE_STATUS_OPEN {
		return errors.NewInvalidArgumentError("storeId")
	}
	return nil
}

func (builder *RetailerOrderBuilder) SetStaffId(ctx context.Context) error {
	if builder.PromoterMemberId == "" {
		return nil
	}
	promoter, _ := ec_distribution.CPromoter.GetByMemberId(ctx, util.ToMongoId(builder.PromoterMemberId))
	if promoter.Id.Valid() {
		builder.StaffId = promoter.StaffId.Hex()
	}
	return nil
}

func (builder *RetailerOrderBuilder) setCartProducts(ctx context.Context) error {
	if builder.Options.IsPurchase {
		return nil
	}
	if builder.Options.IsTest {
		builder.setCartProductsWithTest(ctx)
		return nil
	}
	cartProducts := ec_order.CCart.GetCheckedCartProductsByMemberIdAndStoreId(ctx, builder.StoreId, builder.MemberId)
	for _, cartProduct := range cartProducts {
		tempCartProduct := cartProduct
		builder.CartProducts = append(builder.CartProducts, &tempCartProduct)
	}
	if len(builder.CartProducts) == 0 {
		return errors.NewNotExistsError("cartProducts")
	}
	return nil
}

func (builder *RetailerOrderBuilder) setCartProductsWithTest(ctx context.Context) error {
	cartProducts := []*ec_order.CartProduct{}
	for _, product := range builder.Options.Products {
		cartProduct := &ec_order.CartProduct{
			ProductId: product.Id,
			Checked:   true,
		}
		if len(product.Skus) > 0 {
			cartProduct.Sku = product.Skus[0].Sku
		}
		field := product.GetFieldByFieldName("count")
		if field != nil {
			cartProduct.Total = cast.ToUint64(field.ValueString)
		}
		cartProducts = append(cartProducts, cartProduct)
	}
	builder.CartProducts = cartProducts
	return nil
}

func (builder *RetailerOrderBuilder) SetEcProductIds() {
	if builder.Options.IsPurchase {
		for _, productSku := range builder.ProductSkus {
			builder.EcProductIds = append(builder.EcProductIds, productSku.ProductId)
		}
		return
	}
	for _, cartProduct := range builder.CartProducts {
		builder.EcProductIds = append(builder.EcProductIds, cartProduct.ProductId.Hex())
	}
}

func (builder *RetailerOrderBuilder) SetStoreProductMap(ctx context.Context) error {
	if builder.Options.IsTest {
		builder.setStoreProductMapWithTest()
		return nil
	}
	if len(builder.EcProductIds) == 0 {
		return nil
	}
	storeProducts, err := ec_store_product.CStoreProduct.GetAllByEcProductIdsAndStoreId(ctx, util.ToMongoIds(builder.EcProductIds), util.ToMongoId(builder.StoreId))
	if err != nil {
		return err
	}
	storeProductMap := map[string]*ec_store_product.StoreProduct{}
	for _, storeProduct := range storeProducts {
		if builder.Options.IsCreateOrder && storeProduct.Status == ec_store_product.STATUS_UNSHELVED {
			return errors.NewInvalidArgumentError("productId")
		}
		tempStoreProduct := storeProduct
		storeProductMap[storeProduct.EcProductId.Hex()] = &tempStoreProduct
	}
	builder.StoreProductMap = storeProductMap
	return nil
}

func (builder *RetailerOrderBuilder) setStoreProductMapWithTest() {
	storeProductMap := map[string]*ec_store_product.StoreProduct{}
	for _, product := range builder.Options.Products {
		storeProductMap[product.Id.Hex()] = product
	}
	builder.StoreProductMap = storeProductMap
}

func (builder *RetailerOrderBuilder) SetProductIds(ctx context.Context) {
	if len(builder.EcProductIds) == 0 {
		return
	}
	if builder.Options.IsTest {
		return
	}
	ecProducts, err := ec_product.CProduct.GetByIds(ctx, util.ToMongoIds(builder.EcProductIds))
	if err != nil {
		return
	}
	productIds := []string{}
	for _, ecProduct := range ecProducts {
		tempEcProduct := ecProduct
		productIds = append(productIds, tempEcProduct.ProductId.Hex())
	}
	builder.ProductIds = productIds
}

func (builder *RetailerOrderBuilder) SetProductMap(ctx context.Context) {
	if len(builder.ProductIds) == 0 {
		return
	}
	resp, err := ec_model.GetProductsByIds(ctx, builder.ProductIds)
	if err != nil {
		return
	}
	productMap := map[string]*pb_product.ProductDetailResponse{}
	for _, product := range resp.Items {
		productMap[product.Id] = product
	}
	builder.ProductMap = productMap
}

func (builder *RetailerOrderBuilder) SetProductSkus(ctx context.Context) {
	if !builder.Options.IsPurchase {
		for _, cartProduct := range builder.CartProducts {
			builder.ProductSkus = append(builder.ProductSkus, &ProductSku{
				Sku:       cartProduct.Sku,
				Count:     cartProduct.Total,
				ProductId: cartProduct.ProductId.Hex(),
			})
		}
	}

	// 设置商品价格，sku properties，brandId
	for _, productSku := range builder.ProductSkus {
		if storeProduct, ok := builder.StoreProductMap[productSku.ProductId]; ok {
			brandField := storeProduct.GetFieldByFieldName(ec_store_product.FIELD_NAME_BRAND)
			if brandField != nil && len(brandField.ValueStringArray) > 0 && brandField.ValueStringArray[0] != "" {
				productSku.BrandId = brandField.ValueStringArray[0]
			}
			for _, sku := range storeProduct.Skus {
				if productSku.Sku == sku.Sku {
					productSku.Price = sku.Price
					productSku.Properties = sku.Properties
				}
			}
			// 设置限时特价活动价格
			if storeProduct.IsConsignmentProduct() {
				discountCampaign := DiscountCampaign{}
				// 脉盟小店需要判断商品参加的是下单门店的限时特价活动，因此需要加 storeId 条件
				if ec_model.IsMaimengRetail(ctx) {
					discountCampaign, _ = CDiscountCampaign.GetByProductIdAndStoreId(ctx, storeProduct.EcProductId, util.ToMongoId(builder.StoreId))
				}
				if ec_model.IsChainRetail(ctx) {
					discountCampaign, _ = CDiscountCampaign.GetByProductId(ctx, storeProduct.EcProductId)
				}
				if discountCampaign.Id.Valid() {
					for _, product := range discountCampaign.Products {
						if product.Id.Hex() != storeProduct.EcProductId.Hex() {
							continue
						}
						for _, sku := range product.Skus {
							if sku.Sku != productSku.Sku {
								continue
							}
							productSku.CampaignId = discountCampaign.Id.Hex()
							productSku.DiscountCampaignId = discountCampaign.DiscountCampaignId.Hex()
							productSku.DiscountCampaignTitle = discountCampaign.Title
							productSku.SourcePrice = productSku.Price
							productSku.Price = uint64(sku.DiscountPrice)
						}
					}
				}
			}
		}
	}
}

func (builder *RetailerOrderBuilder) CheckProductAndPurchaseLimit(ctx context.Context) error {
	if builder.Options.IsTest {
		return nil
	}
	if len(builder.ProductSkus) == 0 {
		return nil
	}
	for _, ps := range builder.ProductSkus {
		if ps.Count == 0 {
			return errors.NewInvalidArgumentErrorWithMessage("products", "product count can not be zero")
		}
	}

	ids := core_util.ExtractArrayStringField("ProductId", builder.ProductSkus)
	ecProductMap, err := builder.GetEcProductMap(ctx, ids, nil)
	if err != nil {
		return err
	}

	purchaseStatsMap := map[string]*ec_order.OrderProductPurchaseStats{}
	// 商品各规格购买计数总和的 map
	productCountMap := map[string]int64{}
	if builder.Options.IsCreateOrder && builder.ProductPurchaseCountMap != nil {
		purchaseStatsMap, productCountMap, err = builder.GetPurchaseStatsMapAndProductCountMap(ctx, ids)
		if err != nil {
			return err
		}
	}

	for i, id := range ids {
		ecProduct, ok := ecProductMap[bson.ObjectIdHex(id)].(ec_product.Product)
		if !ok {
			return errors.NewNotExistsError("product")
		}
		if !ecProduct.HasSku(builder.ProductSkus[i].Sku) {
			return errors.NewNotExistsError("product")
		}

		if builder.Options.IsCreateOrder && builder.ProductPurchaseCountMap != nil {
			limitStatus := checkProductPurchaseCount(ecProduct.PurchaseLimit, purchaseStatsMap[ecProduct.Id.Hex()], productCountMap[id])
			if limitStatus != "" {
				return errors.NewNotExistsErrorWithMessage("product", fmt.Sprintf("Exceed product limit, %s", limitStatus))
			}
			periodType := ecProduct.PurchaseLimit.PeriodType
			err = ec_order.COrderProductPurchaseStats.IncrMemberPurchaseCount(ctx, bson.ObjectIdHex(id), bson.ObjectIdHex(builder.MemberId), periodType, int(builder.ProductSkus[i].Count))
			if err != nil {
				return err
			}
			builder.ProductPurchaseCountMap[bson.ObjectIdHex(id)] += builder.ProductSkus[i].Count
		}
	}
	return nil
}

func (builder *RetailerOrderBuilder) CheckAndReduceConsignmentProductsStock(ctx context.Context) error {
	if builder.Options.IsTest {
		return nil
	}
	if !builder.Options.IsCreateOrder {
		return nil
	}
	if builder.ReducedConsignmentStockMap == nil {
		return nil
	}

	isProductUnderstocked := false   // 用于判断是否有商品库存不足
	isProductSoldOut := false        // 用于判断是否有商品售罄
	isAllProductUnderstocked := true // 判断是否所有商品都是库存不足，仅 isProductUnderStock 为 true 时有效
	isAllProductSoldOut := true      // 判断是否所有商品已售罄，仅 isProductSoldOut 为 true 时有效
	for _, productSku := range builder.ProductSkus {
		_, ok := builder.StoreProductMap[productSku.ProductId]
		if !ok {
			return errors.NewNotExistsError("storeProduct")
		}
		consignmentProduct := builder.GetConsignmentProductStock(ctx, productSku)
		if consignmentProduct == nil {
			continue
		}
		err := consignmentProduct.ReduceStock(ctx, productSku.Sku, int(productSku.Count))
		if err != nil {
			// 扣库存失败判断是否已售罄
			skuStockStatus, err := consignmentProduct.GetStatus(ctx, productSku.Sku, int(productSku.Count))
			if err != nil {
				return err
			}
			if skuStockStatus == ec_product.STATUS_SOLD_OUT {
				isProductSoldOut = true
			} else {
				isProductUnderstocked = true
				isAllProductSoldOut = false
			}
			continue // 只有减扣库存成功的才需要添加到回退库存的 map
		} else {
			isAllProductUnderstocked = false
			isAllProductSoldOut = false
		}
		key := fmt.Sprintf("%s:%s", consignmentProduct.AccountId, productSku.Sku)
		builder.ReducedConsignmentStockMap[key] = int(productSku.Count)
	}
	if isProductUnderstocked || isProductSoldOut {
		if isProductSoldOut {
			if isAllProductSoldOut {
				// 全部商品已售罄
				return errors.NewInvalidArgumentErrorWithMessage("stock", "all products sold out.")
			}
			if isProductUnderstocked {
				// 部分商品售罄，部分商品库存不足
				return errors.NewInvalidArgumentErrorWithMessage("stock", "partial products are understocked or sold out.")
			}
			// 仅部分商品售罄
			return errors.NewInvalidArgumentErrorWithMessage("stock", "partial products sold out.")
		}
		if isProductUnderstocked {
			if isAllProductUnderstocked {
				// 全部商品库存不足,且没有已售罄商品
				return errors.NewInvalidArgumentErrorWithMessage("stock", "all products are understocked.")
			}
			// 部分商品库存不足，且没有已售罄商品
			return errors.NewInvalidArgumentErrorWithMessage("stock", "partial products are understocked.")
		}
	}
	return nil
}

func (builder *RetailerOrderBuilder) GetConsignmentProductStock(ctx context.Context, productSku *ProductSku) *component.MaiRetailerConsignmentProduct {
	storeProduct, _ := builder.StoreProductMap[productSku.ProductId]
	return storeProduct.GetConsignmentProductStock(ctx)
}

func (builder *RetailerOrderBuilder) GetEcProductMap(ctx context.Context, ids []string, productIds []string) (map[interface{}]interface{}, error) {
	if builder.EcProductMap != nil {
		var (
			existsIds        []string
			existsProductIds []string
		)
		for id, ecProduct := range builder.EcProductMap {
			existsIds = append(existsIds, id.(bson.ObjectId).Hex())
			existsProductIds = append(existsProductIds, ecProduct.(ec_product.Product).ProductId.Hex())
		}
		ids = util.StrArrayDiff(ids, existsIds)
		productIds = util.StrArrayDiff(productIds, existsProductIds)
		if len(ids) == 0 && len(productIds) == 0 {
			return builder.EcProductMap, nil
		}
	}
	var ecProducts []ec_product.Product
	if len(ids) > 0 {
		tmpProducts, err := ec_product.CProduct.GetByIds(ctx, util.ToMongoIds(ids))
		if err != nil {
			return nil, err
		}
		ecProducts = append(ecProducts, tmpProducts...)
	}
	if len(productIds) > 0 {
		tmpProducts, err := ec_product.CProduct.GetByProductIds(ctx, util.ToMongoIds(productIds))
		if err != nil {
			return nil, err
		}
		ecProducts = append(ecProducts, tmpProducts...)
	}
	ecProductMap := core_util.MakeMapper("Id", ecProducts)
	if builder.EcProductMap == nil {
		builder.EcProductMap = ecProductMap
	} else {
		for k, v := range ecProductMap {
			builder.EcProductMap[k] = v
		}
	}

	return builder.EcProductMap, nil
}

func (builder *RetailerOrderBuilder) GetPurchaseStatsMapAndProductCountMap(ctx context.Context, ids []string) (map[string]*ec_order.OrderProductPurchaseStats, map[string]int64, error) {
	orderProductPurchaseStats, err := builder.GetOrderProductPurchaseStats(ctx, ids)
	if err != nil {
		return nil, nil, err
	}

	purchaseStatsMap := map[string]*ec_order.OrderProductPurchaseStats{}
	for _, stats := range orderProductPurchaseStats {
		purchaseStatsMap[stats.ProductId.Hex()] = stats
	}
	// 商品各规格购买计数总和的 map
	productCountMap := map[string]int64{}
	for i, id := range ids {
		productCountMap[id] += int64(builder.ProductSkus[i].Count)
	}
	return purchaseStatsMap, productCountMap, nil
}

func (builder *RetailerOrderBuilder) GetOrderProductPurchaseStats(ctx context.Context, ids []string) ([]*ec_order.OrderProductPurchaseStats, error) {
	if builder.OrderProductPurchaseStats != nil {
		return builder.OrderProductPurchaseStats, nil
	}
	orderProductPurchaseStats, err := ec_order.COrderProductPurchaseStats.GetByProductIdsAndMemberId(ctx, bson.ObjectIdHex(builder.MemberId), util.ToMongoIds(ids))
	if err != nil {
		return nil, err
	}
	builder.OrderProductPurchaseStats = orderProductPurchaseStats
	return orderProductPurchaseStats, nil
}

func checkProductPurchaseCount(purchaseLimit ec_product.PurchaseLimit, purchaseStats *ec_order.OrderProductPurchaseStats, purchaseCount int64) string {
	if purchaseStats != nil {
		date := ec_order.GetDateByPeriodType(purchaseLimit.PeriodType, time.Now())
		if purchaseStats.PeriodType != purchaseLimit.PeriodType || purchaseStats.Date != date {
			purchaseStats.Count = 0
			purchaseStats.Date = date
		}
	}
	if purchaseLimit.PeriodType == "" {
		return ""
	}
	// 不限购
	if purchaseLimit.Count == 0 {
		return ""
	}

	var statsCount int64
	if purchaseStats != nil {
		statsCount = int64(purchaseStats.Count)
	}
	if purchaseCount+statsCount > purchaseLimit.Count {
		return ec_product.STATUS_PURCHASE_LIMIT
	}
	return ""
}

func (builder *RetailerOrderBuilder) FilterInvalidProducts(ctx context.Context) error {
	if builder.Options.IsTest {
		return nil
	}
	if len(builder.ProductSkus) == 0 {
		return nil
	}
	if builder.Options.IsCreateOrder {
		return nil
	}

	productSkus := builder.ProductSkus
	// 按照优惠金额从大到小排序，限时特价活动限购数量时，用来将优惠金额最大的商品保留下来
	// 比如限购1件，购物车中同商品有三件，只保留优惠金额最大的一件，另外的两件会将状态设置为 campaign_limit
	// 在购物车列表接口， campaign_limit 状态的商品会失效掉
	// 在计算订单金额接口，campaign_limit 状态的商品会移除，不会返回
	sort.SliceStable(productSkus, func(i, j int) bool {
		iDiscountAmount := productSkus[i].SourcePrice - productSkus[i].Price
		jDiscountAmount := productSkus[j].SourcePrice - productSkus[j].Price
		if iDiscountAmount > jDiscountAmount {
			return true
		}
		return false
	})

	ids := core_util.ExtractArrayStringField("ProductId", productSkus)
	ecProductMap, _ := builder.GetEcProductMap(ctx, ids, nil)
	purchaseStatsMap, productCountMap, err := builder.GetPurchaseStatsMapAndProductCountMap(ctx, ids)
	if err != nil {
		return err
	}

	campaignIds := core_util.ExtractArrayStringField("CampaignId", builder.ProductSkus)
	discountCampaignIds := core_util.ExtractArrayStringField("DiscountCampaignId", builder.ProductSkus)
	campaignsMap := GetCampaignsMapWithIds(ctx, campaignIds)
	campaignOrderCountMap := GetCampaignOrderCountMap(ctx, builder.MemberId, discountCampaignIds)

	invalidProductSkus := []*ProductSku{}
	invalidProductSkuStatusMap := make(map[string]string)
	validProductSkus := []*ProductSku{}

	canPurchaseProductCountMap := map[string]uint64{}
	for i, id := range ids {
		if _, ok := canPurchaseProductCountMap[id]; !ok {
			canPurchaseProductCountMap[id] = 0
		}
		// 处理限时特价活动和库存
		// 限时特价和商品都有限购时，以限时特价为准
		if builder.ProductSkus[i].CampaignId != "" {
			if campaign, ok := campaignsMap[builder.ProductSkus[i].CampaignId]; ok {
				stock := campaign.GetSkuStock(builder.ProductSkus[i].ProductId, builder.ProductSkus[i].Sku)
				builder.ProductSkus[i].Stock = stock
				if !campaign.HavePurchaseCount(ctx, builder.ProductSkus[i].ProductId, builder.ProductSkus[i].Count, canPurchaseProductCountMap[id], campaignOrderCountMap) {
					invalidProductSkus = append(invalidProductSkus, builder.ProductSkus[i])
					invalidProductSkuStatusMap[builder.ProductSkus[i].Sku] = "campaign_limit" // 活动限购
					continue
				}
				if !campaign.HaveStock(id, builder.ProductSkus[i].Sku, builder.ProductSkus[i].Count) {
					invalidProductSkuStatusMap[builder.ProductSkus[i].Sku] = "sold_out" // 所选规格已售罄
					continue
				}
				canPurchaseProductCountMap[id] = canPurchaseProductCountMap[id] + builder.ProductSkus[i].Count
			}
		} else { // 处理商品限购和库存
			ecProduct := ecProductMap[bson.ObjectIdHex(id)].(ec_product.Product)
			limitStatus := checkProductPurchaseCount(ecProduct.PurchaseLimit, purchaseStatsMap[ecProduct.Id.Hex()], productCountMap[id])
			if limitStatus != "" {
				invalidProductSkus = append(invalidProductSkus, builder.ProductSkus[i])
				invalidProductSkuStatusMap[builder.ProductSkus[i].Sku] = limitStatus
				continue
			}

			// 库存不足也要计算订单金额
			consignmentProduct := builder.GetConsignmentProductStock(ctx, builder.ProductSkus[i])
			if consignmentProduct != nil {
				skuStock, _ := consignmentProduct.GetStock(ctx, builder.ProductSkus[i].Sku)
				builder.ProductSkus[i].Stock = cast.ToUint64(skuStock)
				builder.ProductSkus[i].IsStockEnabled = true
				skuStockStatus := ec_product.STATUS_ENOUGH
				if skuStock <= 0 {
					skuStockStatus = ec_product.STATUS_SOLD_OUT
				}
				if uint64(skuStock) < builder.ProductSkus[i].Count {
					skuStockStatus = ec_product.STATUS_UNDERSTOCKED
				}
				if skuStockStatus != ec_product.STATUS_ENOUGH {
					invalidProductSkuStatusMap[builder.ProductSkus[i].Sku] = skuStockStatus
				}
			}
		}

		validProductSkus = append(validProductSkus, builder.ProductSkus[i])
	}
	builder.ProductSkus = validProductSkus
	builder.InvalidProductSkus = invalidProductSkus
	builder.InvalidProductSkuStatusMap = invalidProductSkuStatusMap
	return nil
}

func (builder *RetailerOrderBuilder) SetMemberAddress(ctx context.Context) {
	if builder.MemberAddressId == "" {
		return
	}
	memberAddress, err := ec_service.GetMemberAddressById(ctx, builder.MemberAddressId, builder.MemberId)
	if err != nil {
		return
	}
	builder.MemberAddress = memberAddress
}

func (builder *RetailerOrderBuilder) SetSplitProduct(ctx context.Context) {
	builder.SplitProducts = builder.SplitProduct(ctx, builder.ProductSkus)
}

func (builder *RetailerOrderBuilder) SplitProduct(ctx context.Context, needSplitProductSkus []*ProductSku) []*SplitProduct {
	splitProductSkuMap := map[string][]ProductSku{}
	for _, productSku := range needSplitProductSkus {
		if storeProduct, ok := builder.StoreProductMap[productSku.ProductId]; ok {
			distributorId := storeProduct.GetDistributorId()
			if distributorId == "" {
				continue
			}
			splitProductSkuMap[distributorId] = append(splitProductSkuMap[distributorId], *productSku)
		}
	}

	// D0 按照商品品牌拆单
	d0BrandIds := []string{}
	for distributorId, productSkus := range splitProductSkuMap {
		if distributorId != builder.getD0DistributorId() {
			continue
		}
		delete(splitProductSkuMap, distributorId)
		for _, productSku := range productSkus {
			d0BrandIds = append(d0BrandIds, productSku.BrandId)
			splitProductSkuMap[productSku.BrandId] = append(splitProductSkuMap[productSku.BrandId], productSku)
		}
	}

	splitProducts := []*SplitProduct{}
	for distributorId, productSkus := range splitProductSkuMap {
		productIds := []bson.ObjectId{}
		orderProducts := []*ec_order.OrderProduct{}
		isDistributionProduct := false
		for _, productSku := range productSkus {
			if storeProduct, ok := builder.StoreProductMap[productSku.ProductId]; ok {
				if storeProduct.IsDistributionProduct() {
					isDistributionProduct = true
				}
				productIds = append(productIds, util.ToMongoId(productSku.ProductId))
				orderProducts = append(orderProducts, builder.genBasicOrderProduct(ctx, storeProduct.ProductId.Hex(), productSku))
			}
		}
		if util.StrInArray(distributorId, &d0BrandIds) {
			distributorId = fmt.Sprintf("%s_%s", builder.getD0DistributorId(), distributorId)
		}
		splitProduct := &SplitProduct{
			DistributorId: distributorId,
			ProductIds:    productIds,
			Products:      orderProducts,
			Logistics: &LogisticsInfo{
				Fee: 0, // 目前运费都是 0
			},
			Campaigns: builder.genCampaigns(ctx, productSkus), // 限时特价活动信息
		}
		if isDistributionProduct {
			splitProduct.Type = MAIMENG_STORE_PRODUCT_DISTRIBUTION
		} else {
			splitProduct.Type = MAIMENG_STORE_PRODUCT_CONSIGNMENT
		}
		splitProducts = append(splitProducts, splitProduct)
	}
	return splitProducts
}

// 生成限时特价活动信息
func (builder *RetailerOrderBuilder) genCampaigns(ctx context.Context, productSkus []ProductSku) []*Campaign {
	campaigns := []*Campaign{}
	for _, productSku := range productSkus {
		if productSku.DiscountCampaignId == "" {
			continue
		}

		campaign := &Campaign{
			CampaignId:         productSku.CampaignId,
			DiscountCampaignId: productSku.DiscountCampaignId,
			Title:              productSku.DiscountCampaignTitle,
			ProductId:          productSku.ProductId,
			Total:              productSku.Count,
			Sku:                productSku.Sku,
		}

		// 设置 CurrentSales，下单时该商品 sku 在限时特价活动中的销量
		discountCampaign, _ := CDiscountCampaign.GetById(ctx, util.ToMongoId(productSku.CampaignId))
		if discountCampaign.Id.Valid() {
			for _, product := range discountCampaign.Products {
				for _, sku := range product.Skus {
					if sku.Sku == productSku.Sku {
						campaign.CurrentSales = sku.Sales + productSku.Count
					}
				}
			}
		}

		// 设置 CurrentStaffSales，下单时该商品 sku 在限时特价活动中导购推广的销量
		if builder.StaffId != "" {
			discountCampaignStaffSales, _ := CDiscountCampaign.GetDiscountCampaignStaffSales(
				ctx,
				util.ToMongoId(productSku.CampaignId),
				util.ToMongoId(builder.StaffId),
				util.ToMongoId(productSku.ProductId),
			)
			log.Warn(ctx, "GenCampaigns log", log.Fields{
				"discountCampaignStaffSales": discountCampaignStaffSales,
				"productSku":                 productSku,
				"staffId":                    builder.StaffId,
			})
			if discountCampaignStaffSales.Id.Valid() {
				for _, sku := range discountCampaignStaffSales.Skus {
					if sku.Sku == productSku.Sku {
						campaign.CurrentStaffSales = sku.Sales + productSku.Count
					}
				}
			}
		}
		campaigns = append(campaigns, campaign)
	}
	return campaigns
}

func (builder *RetailerOrderBuilder) getD0DistributorId() string {
	if builder.Store != nil {
		for _, distributor := range builder.Store.DistributionStoreInfo.Distributors {
			if distributor.Type == "D0" {
				return distributor.Id
			}
		}
	}
	return ""
}

func (builder *RetailerOrderBuilder) getDOneDistributorId() string {
	if builder.Store != nil {
		for _, distributor := range builder.Store.DistributionStoreInfo.Distributors {
			if distributor.Type == "D1" {
				return distributor.Id
			}
		}
	}
	return ""
}

func (builder *RetailerOrderBuilder) getDZeroDistributorAccountId() string {
	if builder.Store != nil {
		for _, distributor := range builder.Store.DistributionStoreInfo.Distributors {
			if distributor.Type == "D0" {
				return distributor.AccountId
			}
		}
	}
	return ""
}

func (builder *RetailerOrderBuilder) genBasicOrderProduct(ctx context.Context, productId string, productSku ProductSku) *ec_order.OrderProduct {
	orderProduct := &ec_order.OrderProduct{
		Id:          util.ToMongoId(productSku.ProductId),
		Price:       productSku.Price,
		PayAmount:   productSku.Price,
		Total:       productSku.Count,
		TotalAmount: productSku.Price * productSku.Count,
	}
	sku := productSku.Sku
	if product, ok := builder.ProductMap[productId]; ok {
		orderProduct.Name = product.Name
		orderProduct.ProductId = util.ToMongoId(product.Id)
		orderProduct.Number = product.Number
		orderProduct.BarCode = product.BarCode
		orderProduct.Tags = product.Tags
		if product.Category != nil && product.Category.Id != "" {
			orderProduct.CategoryId = bson.ObjectIdHex(product.Category.Id)
		}
		if product.Brand != nil && bson.IsObjectIdHex(product.Brand.Id) {
			orderProduct.BrandId = bson.ObjectIdHex(product.Brand.Id)
		}
		if sku != "" {
			orderProduct.Spec = ec_service_order.GetProductSpec(sku, *product)
		}
		orderProduct.Picture = orderProduct.Spec.Picture
		if orderProduct.Picture == "" && len(product.Pictures) > 0 {
			orderProduct.Picture = product.Pictures[0].Url
		}
	}
	// 设置代销商品佣金/佣金比例
	if storeProduct, ok := builder.StoreProductMap[productSku.ProductId]; ok {
		typeField := storeProduct.GetFieldByFieldName(ec_store_product.FIELD_NAME_TYPE)
		if typeField != nil && typeField.ValueString == MAIMENG_STORE_PRODUCT_CONSIGNMENT {
			for _, sku := range storeProduct.Skus {
				if productSku.Sku == sku.Sku {
					orderProduct.OriginPrice = sku.OriginalPrice
					// 商品只支持设置佣金
					orderProduct.StoreDistributionAmount = sku.ProfitAmount * orderProduct.Total
					// 导购佣金也需要设置
					// 如果是分销订单，会在 calcDistributionAmount 中重新计算
					// 如果参与了限时特价活动，会在 prePay 中更新
					orderProduct.DistributionAmount = sku.StaffProfitAmount * orderProduct.Total
				}
			}
		}
	}
	return orderProduct
}

func (builder *RetailerOrderBuilder) CalTotalAmount() {
	var totalAmount uint64 = 0
	for _, productSku := range builder.ProductSkus {
		totalAmount += productSku.Price * productSku.Count
	}
	builder.TotalAmount = totalAmount
}

func (builder *RetailerOrderBuilder) CalPayAmount() {
	// 目前还没有优惠，实际支付金额 = 商品总金额 + 运费
	// 第一期运费都为 0
	builder.PayAmount = builder.TotalAmount
	for _, splitProduct := range builder.SplitProducts {
		if splitProduct.Logistics != nil {
			builder.PayAmount += splitProduct.Logistics.Fee
		}
	}
}

func (*RetailerOrderBuilder) CalculateOrderAmount(ctx context.Context, req *pb_ec_retailer.CalculateOrderAmountRequest) (*pb_ec_retailer.CalculateOrderAmountResponse, error) {
	builder := RetailerOrderBuilder{
		MemberId:           req.MemberId,
		StoreId:            req.StoreId,
		Payment:            ec_order.PAYMENT_WECHAT,
		MemberAddressId:    req.MemberAddressId,
		DistributionMethod: req.DistributionMethod,
	}
	if req.IsPurchase {
		builder.ProductSkus = []*ProductSku{
			{
				Sku:       req.Sku,
				ProductId: req.ProductId,
				Count:     req.Count,
			},
		}
	}
	err := builder.GenBuilder(ctx, WithIsPurchase(req.IsPurchase))
	if err != nil {
		return nil, err
	}
	return builder.genCalculateOrderAmountResponse(ctx)
}

func (builder *RetailerOrderBuilder) genCalculateOrderAmountResponse(ctx context.Context) (*pb_ec_retailer.CalculateOrderAmountResponse, error) {
	resp := &pb_ec_retailer.CalculateOrderAmountResponse{
		TotalAmount: builder.TotalAmount,
		PayAmount:   builder.PayAmount,
	}

	purchaseStatsMap, _, err := builder.GetPurchaseStatsMapAndProductCountMap(ctx, nil)
	if err != nil {
		return nil, err
	}
	productPurchasedCountMap := map[string]uint64{}
	for productId, stats := range purchaseStatsMap {
		productPurchasedCountMap[productId] = stats.Count
	}

	invalidSplitProducts := []*SplitProduct{}
	invalidSplitProductMap := map[interface{}]interface{}{}
	if len(builder.InvalidProductSkus) > 0 {
		invalidSplitProducts = builder.SplitProduct(ctx, builder.InvalidProductSkus)
		invalidSplitProductMap = core_util.MakeMapper("DistributorId", invalidSplitProducts)
	}

	productSkuMap := core_util.MakeMapper("Sku", builder.ProductSkus)
	respSplitProducts := []*pb_ec_retailer.SplitProduct{}
	for _, splitProduct := range builder.SplitProducts {
		products := []*pb_ec_retailer.Product{}
		for _, orderProduct := range splitProduct.Products {
			respProduct := &pb_ec_retailer.Product{}
			ecProduct, ok := builder.EcProductMap[orderProduct.Id].(ec_product.Product)
			if !ok {
				return nil, errors.NewNotExistsError("product")
			}
			copier.Instance(nil).From(ecProduct).CopyTo(respProduct)
			copier.Instance(nil).RegisterIgnoreTargetFields([]copier.FieldKey{"Type", "SubType"}).From(orderProduct).CopyTo(respProduct)
			respProduct.Count = orderProduct.Total
			respProduct.Sku = orderProduct.Spec.Sku
			respProduct.PurchasedCount = productPurchasedCountMap[respProduct.Id]
			if productSku, ok := productSkuMap[orderProduct.Spec.Sku].(*ProductSku); ok {
				respProduct.Stock = productSku.Stock
				// 限时特价商品，确认订单页面要显示原价
				if productSku.CampaignId != "" {
					respProduct.Price = productSku.SourcePrice
				}
			}
			properties := []string{}
			for _, property := range orderProduct.Spec.Properties {
				properties = append(properties, property.Value)
			}
			respProduct.Properties = properties
			if storeProduct, ok := builder.StoreProductMap[orderProduct.Id.Hex()]; ok {
				respProduct.Status = storeProduct.Status
			}
			if invalidStatus, ok := builder.InvalidProductSkuStatusMap[orderProduct.Spec.Sku]; ok && invalidStatus != "" {
				respProduct.Status = invalidStatus
			}
			formatProductCampaigns(ctx, respProduct, splitProduct)
			products = append(products, respProduct)
		}

		if invalidSplitProduct, ok := invalidSplitProductMap[splitProduct.DistributorId].(*SplitProduct); ok {
			for _, invalidOrderProduct := range invalidSplitProduct.Products {
				ecProduct, ok := builder.EcProductMap[invalidOrderProduct.Id].(ec_product.Product)
				if !ok {
					return nil, errors.NewNotExistsError("product")
				}
				invalidStatus := builder.InvalidProductSkuStatusMap[invalidOrderProduct.Spec.Sku]
				// 代销限时特价限购商品不需要展示在结算页面
				if invalidStatus == "campaign_limit" {
					continue
				}
				respInvalidProductResp := builder.GenInvalidProductResponse(invalidOrderProduct, ecProduct, invalidStatus, productPurchasedCountMap)
				respInvalidProduct := &pb_ec_retailer.Product{}
				copier.Instance(nil).From(respInvalidProductResp).CopyTo(&respInvalidProduct)
				formatProductCampaigns(ctx, respInvalidProduct, splitProduct)
				products = append(products, respInvalidProduct)
			}
		}

		respSplitProduct := &pb_ec_retailer.SplitProduct{
			DistributorId: splitProduct.DistributorId,
			Products:      products,
			Type:          splitProduct.Type,
		}
		if splitProduct.Type == MAIMENG_STORE_PRODUCT_DISTRIBUTION {
			if len(products) > 0 && products[0].Type == "coupon" {
				respSplitProduct.DeliveryMethods = []string{ec_order.ORDER_DELIVERY_METHOD_NO_EXPRESS}
			} else {
				respSplitProduct.DeliveryMethods = GetDeliveryMethods(ctx, builder.Store)
			}
		} else {
			// 代销商品只支持快递
			respSplitProduct.DeliveryMethods = []string{ec_order.ORDER_DELIVERY_METHOD_EXPRESS}
		}
		respSplitProducts = append(respSplitProducts, respSplitProduct)
	}

	respInvalidSplitProducts := []*pb_ec_retailer.SplitProduct{}
	existingDistributorIds := core_util.ExtractArrayStringField("DistributorId", respSplitProducts)
	for _, invalidSplitProduct := range invalidSplitProducts {
		if util.StrInArray(invalidSplitProduct.DistributorId, &existingDistributorIds) {
			continue
		}
		products := []*pb_ec_retailer.Product{}
		for _, invalidOrderProduct := range invalidSplitProduct.Products {
			ecProduct, ok := builder.EcProductMap[invalidOrderProduct.Id].(ec_product.Product)
			if !ok {
				return nil, errors.NewNotExistsError("product")
			}
			invalidStatus := builder.InvalidProductSkuStatusMap[invalidOrderProduct.Spec.Sku]
			// 代销限时特价限购商品不需要展示在结算页面
			if invalidStatus == "campaign_limit" {
				continue
			}

			respInvalidProductResp := builder.GenInvalidProductResponse(invalidOrderProduct, ecProduct, invalidStatus, productPurchasedCountMap)
			respInvalidProduct := pb_ec_retailer.Product{}
			copier.Instance(nil).From(respInvalidProductResp).CopyTo(&respInvalidProduct)
			if productSku, ok := productSkuMap[invalidOrderProduct.Spec.Sku].(*ProductSku); ok {
				// 限时特价商品，确认订单页面要显示原价
				if productSku.CampaignId != "" {
					respInvalidProduct.Price = productSku.SourcePrice
				}
			}
			products = append(products, &respInvalidProduct)
		}
		respInvalidSplitProduct := &pb_ec_retailer.SplitProduct{
			DistributorId: invalidSplitProduct.DistributorId,
			Products:      products,
			Type:          invalidSplitProduct.Type,
		}
		if invalidSplitProduct.Type == MAIMENG_STORE_PRODUCT_DISTRIBUTION {
			respInvalidSplitProduct.DeliveryMethods = GetDeliveryMethods(ctx, builder.Store)
		} else {
			// 代销商品只支持快递
			respInvalidSplitProduct.DeliveryMethods = []string{ec_order.ORDER_DELIVERY_METHOD_EXPRESS}
		}
		respInvalidSplitProducts = append(respInvalidSplitProducts, respInvalidSplitProduct)
	}

	resp.SplitProducts = append(resp.SplitProducts, respSplitProducts...)
	resp.SplitProducts = append(resp.SplitProducts, respInvalidSplitProducts...)
	builder.formatDiscounts(ctx, resp, respInvalidSplitProducts)

	errMsg := builder.checkCityExpressDistance(ctx)
	if errMsg != "" {
		resp.Error = errMsg
	}
	if errMsg == "" {
		err = builder.calDeliveryFee(ctx)
		if err != nil {
			log.Warn(ctx, "Calc shippingFee error", log.Fields{
				"errMsg": err.Error(),
				"resp":   resp,
			})
		}
	}

	builder.genDeliveryInfos(resp)
	resp.CityExpressDiffPrice = builder.calcDiffStoreBasePrice()
	resp.PayAmount += builder.TotalDeliveryFee
	return resp, nil
}

func (builder *RetailerOrderBuilder) checkCityExpressDistance(ctx context.Context) string {
	if !builder.needCalDeliveryFee() {
		return ""
	}

	store := builder.StoreDetail
	if store.CityExpressSetting == nil {
		return ""
	}
	if builder.MemberAddress == nil {
		return ""
	}
	distance, err := ec_logistic.GetDistance(ctx, &pb_logistic.CalculateDeliveryFeeRequest{
		FromCoordinate: &response.Coordinate{
			Latitude:  float64(store.Location.Latitude),
			Longitude: float64(store.Location.Longitude),
		},
		ToCoordinate: &response.Coordinate{
			Latitude:  builder.MemberAddress.Latitude,
			Longitude: builder.MemberAddress.Longitude,
		},
	})
	if err != nil {
		return ""
	}
	if distance > uint64(store.CityExpressSetting.Range*1000) {
		return MAXIMUM_DELIVERY_DISTANCE_EXCEEDED
	}
	return ""
}

func (builder *RetailerOrderBuilder) calDeliveryFee(ctx context.Context) error {
	if !builder.needCalDeliveryFee() {
		return nil
	}

	template := ec_model_logistic.CDeliveryFeeTemplate.GetTemplateByMethod(ctx, ec_order.ORDER_DELIVERY_METHOD_CITY_EXPRESS)
	if template == nil {
		return nil
	}
	totalDeliveryFee := uint64(0)
	for _, splitProduct := range builder.SplitProducts {
		if splitProduct.Type != MAIMENG_STORE_PRODUCT_DISTRIBUTION {
			continue
		}
		req := builder.genCalculateDeliveryFeeRequest(splitProduct)
		deliveryFee, err := template.CalDeliveryFee(ctx, req)
		if err != nil {
			return err
		}
		splitProduct.DeliveryFee = deliveryFee
		totalDeliveryFee += deliveryFee
	}
	builder.TotalDeliveryFee = totalDeliveryFee
	return nil
}

func (builder *RetailerOrderBuilder) genCalculateDeliveryFeeRequest(splitProduct *SplitProduct) *pb_logistic.CalculateDeliveryFeeRequest {
	var productAmount, weight, pieces uint64
	for _, product := range splitProduct.Products {
		if ec_order.IsPresent(*product) {
			continue
		}
		if product.Spec.Weight == 0 {
			// 当未设置商品重量时按照默认 0.001kg 计算
			weight += 1 * product.Total
		} else {
			weight += product.Spec.Weight * product.Total
		}
		productAmount += product.TotalAmount
		pieces += product.Total
	}
	store := builder.StoreDetail
	req := &pb_logistic.CalculateDeliveryFeeRequest{
		Price:          productAmount,
		Weight:         weight,
		Pieces:         pieces,
		Address:        fmt.Sprintf("%s:%s:%s", store.Location.Province, store.Location.City, store.Location.District),
		PayAmount:      productAmount,
		FromCoordinate: store.Coordinate,
	}
	if builder.MemberAddress != nil {
		req.ToCoordinate = &response.Coordinate{Longitude: builder.MemberAddress.Longitude, Latitude: builder.MemberAddress.Latitude}
	}
	if store.CityExpressSetting != nil && store.CityExpressSetting.DeliveryFeeTemplate != nil {
		req.StoreDeliveryFeeTemplate = store.CityExpressSetting.DeliveryFeeTemplate
	}
	return req
}

func (builder *RetailerOrderBuilder) genDeliveryInfos(resp *pb_ec_retailer.CalculateOrderAmountResponse) {
	if !builder.needCalDeliveryFee() {
		return
	}
	// 目前只有小店订单同城配送有邮费，这里直接设置
	resp.DeliveryInfos = []*common.DeliveryInfo{
		{
			Method:      ec_order.ORDER_DELIVERY_METHOD_CITY_EXPRESS,
			DeliveryFee: builder.TotalDeliveryFee,
			PayAmount:   builder.TotalDeliveryFee,
		},
	}
}

func (builder *RetailerOrderBuilder) calcDiffStoreBasePrice() uint64 {
	if !builder.needCalDeliveryFee() {
		return 0
	}

	store := builder.StoreDetail
	if store.CityExpressSetting != nil && store.CityExpressSetting.BasePrice != 0 {
		for _, splitProduct := range builder.SplitProducts {
			if splitProduct.Type != MAIMENG_STORE_PRODUCT_DISTRIBUTION {
				continue
			}
			var totalAmount uint64
			for _, p := range splitProduct.Products {
				totalAmount += p.TotalAmount
			}
			if totalAmount < store.CityExpressSetting.BasePrice {
				return store.CityExpressSetting.BasePrice - totalAmount
			}
		}
	}
	return 0
}

func (builder *RetailerOrderBuilder) needCalDeliveryFee() bool {
	// 创建订单时，如果经销品是同城快送，要计算邮费
	if builder.Options.IsCreateOrder {
		for _, splitProduct := range builder.SplitProducts {
			if splitProduct.Type == MAIMENG_STORE_PRODUCT_DISTRIBUTION && splitProduct.Method == ec_order.ORDER_DELIVERY_METHOD_CITY_EXPRESS {
				return true
			}
		}
		return false
	}
	// 计算订单金额时，distributionMethod 是同城快送时，要计算邮费
	if builder.DistributionMethod == ec_order.ORDER_DELIVERY_METHOD_CITY_EXPRESS {
		return true
	}
	return false
}

func (builder *RetailerOrderBuilder) formatDiscounts(ctx context.Context, resp *pb_ec_retailer.CalculateOrderAmountResponse, respInvalidSplitProducts []*pb_ec_retailer.SplitProduct) {
	discounts := []*pb_ec_retailer.DiscountInfo{}
	invalidProductSkus := []string{}
	for _, invalidSplitProduct := range respInvalidSplitProducts {
		for _, product := range invalidSplitProduct.Products {
			invalidProductSkus = append(invalidProductSkus, product.Sku)
		}
	}
	for _, productSku := range builder.ProductSkus {
		if productSku.CampaignId == "" {
			continue
		}
		if util.StrInArray(productSku.Sku, &invalidProductSkus) {
			continue
		}
		discount := &pb_ec_retailer.DiscountInfo{
			Id:           productSku.DiscountCampaignId,
			Title:        productSku.DiscountCampaignTitle,
			Amount:       (int64(productSku.SourcePrice) - int64(productSku.Price)) * int64(productSku.Count),
			Type:         "discountCampaign",
			CampaignType: "discountCampaign",
		}
		discounts = append(discounts, discount)
	}

	amount := uint64(0)
	for _, discount := range discounts {
		amount += uint64(discount.Amount)
	}
	if amount > 0 {
		resp.DiscountGroup = []*pb_ec_retailer.Discount{
			{
				Type:        "campaign",
				HasSelected: true,
				Enabled:     true,
				Amount:      amount,
			},
		}
	}
	resp.Discounts = discounts
}

func formatProductCampaigns(ctx context.Context, product *pb_ec_retailer.Product, splitProduct *SplitProduct) {
	for _, c := range splitProduct.Campaigns {
		if c.ProductId != product.Id {
			continue
		}
		campaign, err := CDiscountCampaign.GetById(ctx, util.ToMongoId(c.CampaignId))
		if err != nil {
			continue
		}
		respCampaign := &pb_ec_retailer.Campaign{
			Id:    c.DiscountCampaignId,
			Title: campaign.Title,
			Type:  "discountCampaign",
			Extra: map[string]string{
				"displayName":         campaign.DisplayName,
				"displayDiscountName": campaign.DisplayDiscountName,
			},
		}
		product.Campaigns = []*pb_ec_retailer.Campaign{respCampaign}
		product.CampaignTags = []string{"discountCampaign"}
	}
}

func (builder *RetailerOrderBuilder) GenInvalidProductResponse(invalidOrderProduct *ec_order.OrderProduct, ecProduct ec_product.Product, invalidStatus string, productPurchasedCountMap map[string]uint64) *common_ec.ProductDetail {
	respInvalidProduct := &common_ec.ProductDetail{}
	copier.Instance(nil).From(ecProduct).CopyTo(respInvalidProduct)
	copier.Instance(nil).From(invalidOrderProduct).CopyTo(respInvalidProduct)
	respInvalidProduct.Count = invalidOrderProduct.Total
	respInvalidProduct.Sku = invalidOrderProduct.Spec.Sku
	respInvalidProduct.PurchasedCount = productPurchasedCountMap[respInvalidProduct.Id]
	productSkuMap := core_util.MakeMapper("Sku", builder.ProductSkus)
	if productSku, ok := productSkuMap[invalidOrderProduct.Spec.Sku].(*ProductSku); ok {
		respInvalidProduct.Stock = productSku.Stock
	}
	respInvalidProduct.Status = invalidStatus
	properties := []string{}
	for _, property := range invalidOrderProduct.Spec.Properties {
		properties = append(properties, property.Value)
	}
	respInvalidProduct.Properties = properties
	return respInvalidProduct
}

func GetDeliveryMethods(ctx context.Context, store *ec_store.Store) []string {
	setting, err := ec_setting.CDeliverySetting.Get(ctx)
	if err != nil {
		return nil
	}
	enabledMethods, applicableMethods := []string{}, []string{}
	if setting.CityExpress.Enabled {
		cityExpressStore, _ := ec_store.CCityExpressStore.FindByStoreId(ctx, store.Id)
		if cityExpressStore.Id.Valid() {
			enabledMethods = append(enabledMethods, ec_order.ORDER_DELIVERY_METHOD_CITY_EXPRESS)
			applicableMethods = append(applicableMethods, ec_order.ORDER_DELIVERY_METHOD_CITY_EXPRESS)
		}
	}
	if setting.Logistics.Enabled {
		enabledMethods = append(enabledMethods, ec_order.ORDER_DELIVERY_METHOD_EXPRESS)
		if store == nil || isStoreTypeApplicable(setting.Logistics.StoreType, store.SubType) {
			applicableMethods = append(applicableMethods, ec_order.ORDER_DELIVERY_METHOD_EXPRESS)
		}
	}
	if setting.Reservation.Enabled {
		enabledMethods = append(enabledMethods, ec_order.ORDER_DELIVERY_METHOD_PICKUP)
		if store == nil || isStoreTypeApplicable(setting.Reservation.StoreType, store.SubType) {
			applicableMethods = append(applicableMethods, ec_order.ORDER_DELIVERY_METHOD_PICKUP)
		}
	}
	if store != nil && store.DeliverySetting.IsDefault {
		applicableMethods = core_util.ToStringArray(core_util.GetArraysIntersection(store.DeliverySetting.DeliveryMethods, enabledMethods))
	}
	return applicableMethods
}

func isStoreTypeApplicable(storeType string, subType int) bool {
	switch storeType {
	case ec_setting.STORE_TYPE_VIRTUAL:
		return int32(subType) == ec_store.STORE_SUBTYPPE_ONLINE_STORE
	case ec_setting.STORE_TYPE_PHYSICAL:
		return int32(subType) == ec_store.STORE_SUBTYPPE_NORMAL_STORE
	default:
		return true
	}
}

func (RetailerOrderBuilder) CreateOrder(ctx context.Context, req *pb_ec_retailer.CreateOrderRequest) (*pb_ec_retailer.OrderDetail, error) {
	builder := &RetailerOrderBuilder{
		MemberId: req.MemberId,
		StoreId:  req.StoreId,
		Payment:  ec_order.PAYMENT_WECHAT,
		Channel: &ec_order.OrderChannel{
			ChannelId: req.ChannelId,
			OpenId:    req.OpenId,
		},
		MemberAddressId: req.MemberAddressId,
	}
	if req.IsPurchase {
		builder.ProductSkus = []*ProductSku{
			{
				Sku:       req.Sku,
				ProductId: req.ProductId,
				Count:     req.Count,
			},
		}
	}

	builder.ProductPurchaseCountMap = make(map[bson.ObjectId]uint64)
	builder.ReducedConsignmentStockMap = make(map[string]int)
	err := builder.GenBuilder(ctx,
		WithIsPurchase(req.IsPurchase),
		WithIsCreateOrder(true),
	)
	defer func() {
		if err != nil && len(builder.ReducedConsignmentStockMap) > 0 {
			for key, count := range builder.ReducedConsignmentStockMap {
				arr := strings.Split(key, ":")
				distributorAccountId, sku := arr[0], arr[1]
				consignmentProduct := component.InitRetailerConsignmentProduct(distributorAccountId)
				err := consignmentProduct.ReturnStock(ctx, sku, int(count))
				if err != nil {
					log.Error(ctx, "Return consignment stock failed", log.Fields{
						"order":                builder.Order,
						"distributorAccountId": distributorAccountId,
						"sku":                  sku,
						"count":                count,
						"errMsg":               err.Error(),
					})
				}
			}
		}
	}()
	defer func() {
		if err != nil && len(builder.ProductPurchaseCountMap) > 0 {
			for productId, count := range builder.ProductPurchaseCountMap {
				err := ec_order.COrderProductPurchaseStats.DecrMemberPurchaseCount(ctx, productId, bson.ObjectIdHex(builder.MemberId), int(count))
				if err != nil {
					log.Error(ctx, "Decr member purchase count failed", log.Fields{
						"order":  builder.Order,
						"errMsg": err,
					})
				}
			}
		}
	}()
	if err != nil {
		return nil, err
	}
	builder.formatSplitProducts(req)
	errMsg := builder.checkCityExpressDistance(ctx)
	if errMsg != "" {
		return nil, errors.NewInvalidArgumentErrorWithMessage("storeId", MAXIMUM_DELIVERY_DISTANCE_EXCEEDED)
	}
	builder.calDeliveryFee(ctx)
	err = builder.CreateRetailerOrder(ctx)
	if err != nil {
		log.Warn(ctx, "Create retailer order fail", log.Fields{
			"errMessage": err.Error(),
			"builder":    builder,
		})
		return nil, err
	}
	subOrders := builder.CreateEcOrder(ctx)
	builder.removeBoughtCartProducts(ctx, subOrders)
	builder.Order.UpdateSubOrders(ctx, subOrders)
	return builder.genOrderDetail(), nil
}

func (builder *RetailerOrderBuilder) CreateRetailerOrder(ctx context.Context) error {
	order := RetailerOrder{
		AccountId:     util.GetAccountIdAsObjectId(ctx),
		MemberId:      util.ToMongoId(builder.MemberId),
		StoreId:       util.ToMongoId(builder.StoreId),
		TotalAmount:   builder.TotalAmount + builder.TotalDeliveryFee,
		PayAmount:     builder.PayAmount + builder.TotalDeliveryFee,
		Status:        ORDER_UNPAID_STATUS,
		Channel:       builder.Channel,
		SplitProducts: builder.SplitProducts,
		DeliveryFee:   builder.TotalDeliveryFee,
	}
	if builder.Extra != "" {
		extraMap := map[string]interface{}{}
		json.Unmarshal([]byte(builder.Extra), &extraMap)
		if ypPromotionAmount, ok := extraMap["ypPromotionAmount"]; ok {
			order.YpPromotionAmount = cast.ToUint64(ypPromotionAmount.(float64))
		}
	}
	if order.PayAmount == 0 {
		order.Status = ORDER_PAID_STATUS
	}
	if number, err := util.GenerateUniqueCode("order", "RO"); err != nil {
		return err
	} else {
		order.Number = number
	}
	if !builder.Options.IsTest {
		err := order.Create(ctx)
		if err != nil {
			return nil
		}
	}
	builder.Order = &order
	return nil
}

func (builder *RetailerOrderBuilder) formatSplitProducts(req *pb_ec_retailer.CreateOrderRequest) {
	for _, splitProduct := range builder.SplitProducts {
		for _, reqSplitProduct := range req.SplitProducts {
			if splitProduct.DistributorId == reqSplitProduct.DistributorId {
				if reqSplitProduct.Contact != nil {
					copier.Instance(nil).From(reqSplitProduct.Contact).CopyTo(&splitProduct.Contact)
				}
				if reqSplitProduct.Reservation != nil {
					copier.Instance(nil).From(reqSplitProduct.Reservation).CopyTo(&splitProduct.Reservation)
				}
				splitProduct.Remarks = reqSplitProduct.Remarks
				splitProduct.Method = reqSplitProduct.Method
				splitProduct.ExpectDeliveryAt = reqSplitProduct.ExpectDeliveryAt
				splitProduct.ExpectDeliveryAtLabel = reqSplitProduct.ExpectDeliveryAtLabel
			}
		}
	}
	for _, splitProduct := range builder.SplitProducts {
		// 设置快递配送和同城快送收货人、收货地址信息
		if splitProduct.Method == ec_order.ORDER_DELIVERY_METHOD_EXPRESS || splitProduct.Method == ec_order.ORDER_DELIVERY_METHOD_CITY_EXPRESS {
			if builder.MemberAddress != nil {
				splitProduct.Contact = &OrderContact{
					Name:  builder.MemberAddress.Name,
					Phone: builder.MemberAddress.Phone,
					Address: ContactAddress{
						Province:  builder.MemberAddress.Province,
						City:      builder.MemberAddress.City,
						District:  builder.MemberAddress.District,
						Detail:    strings.Trim(builder.MemberAddress.Detail, "\n"),
						Longitude: builder.MemberAddress.Longitude,
						Latitude:  builder.MemberAddress.Latitude,
					},
				}
			}
		}
		// 设置同城快送预计送达时间
		if splitProduct.Method == ec_order.ORDER_DELIVERY_METHOD_CITY_EXPRESS && splitProduct.ExpectDeliveryAt > 0 {
			if splitProduct.Logistics == nil {
				splitProduct.Logistics = &LogisticsInfo{
					ExpectDeliveryAt:      util.TransIntTimestampToTime(splitProduct.ExpectDeliveryAt),
					ExpectDeliveryAtLabel: splitProduct.ExpectDeliveryAtLabel,
				}
			} else {
				splitProduct.Logistics.ExpectDeliveryAt = util.TransIntTimestampToTime(splitProduct.ExpectDeliveryAt)
			}
		}
	}
}

func (builder *RetailerOrderBuilder) CreateEcOrder(ctx context.Context) []*SubOrder {
	subOrders := []*SubOrder{}
	for _, splitProduct := range builder.SplitProducts {
		resp := &pb_ec_order.OrderDetail{}
		if !builder.Options.IsTest {
			req := builder.genCreateSimpleOrderRequest(splitProduct)
			var err error
			resp, err = client.GetEcOrderServiceClient().CreateSimpleOrder(ctx, req)
			if err != nil {
				log.Warn(ctx, "Create simple order fail", log.Fields{
					"errMessage": err.Error(),
					"req":        req,
				})
				continue
			}
		} else {
			resp.Id = bson.NewObjectId().Hex()
			resp.Number = ""
			resp.Status = ec_order.ORDER_STATUS_PAID
			payAmount := uint64(0)
			for _, product := range splitProduct.Products {
				payAmount += product.Price * product.Total
			}
			resp.PayAmount = payAmount
		}

		productIds := []string{}
		for _, product := range resp.Products {
			productIds = append(productIds, product.Id)
		}

		subOrder := &SubOrder{
			Id:            util.ToMongoId(resp.Id),
			Number:        resp.Number,
			PayAmount:     resp.PayAmount,
			Status:        resp.Status,
			DistributorId: splitProduct.DistributorId,
			Type:          splitProduct.Type,
			ProductIds:    productIds,
			Campaigns:     splitProduct.Campaigns,
		}
		if builder.Options.IsTest {
			subOrder.IsTest = true
		}
		// 经销订单，同城配送时需要计算邮费
		if splitProduct.Type == MAIMENG_STORE_PRODUCT_DISTRIBUTION && splitProduct.Method == ec_order.ORDER_DELIVERY_METHOD_CITY_EXPRESS {
			subOrder.DeliveryFee = splitProduct.DeliveryFee
		}

		// D0 代销订单，设置 subOrder.Products,dZeroDistributorAccountId
		if subOrder.IsD0ConsignmentOrder(*builder.Store) {
			subOrder.Products = builder.GetSubOrderProducts(ctx, splitProduct)
			subOrder.DZeroDistributorAccountId = builder.getDZeroDistributorAccountId()
		}
		subOrders = append(subOrders, subOrder)
	}

	// 子订单价格从小到大排序
	sort.SliceStable(subOrders, func(i, j int) bool {
		if subOrders[i].PayAmount < subOrders[j].PayAmount {
			return true
		}
		return false
	})
	if builder.Order != nil {
		builder.Order.SubOrders = subOrders
	}
	return subOrders
}

func (builder *RetailerOrderBuilder) GetSubOrderProducts(ctx context.Context, splitProduct *SplitProduct) []SubOrderProduct {
	subOrderProducts := []SubOrderProduct{}
	dZeroDistributorCtx := core_util.CtxWithAccountID(ctx, builder.getDZeroDistributorAccountId())
	dOneDistributorId := builder.getDOneDistributorId()
	for _, product := range splitProduct.Products {
		productId := product.Id.Hex()
		if storeProduct, ok := builder.StoreProductMap[productId]; ok {
			subOrderProduct := GetSubOrderProduct(dZeroDistributorCtx, product, storeProduct, dOneDistributorId)
			if subOrderProduct == nil {
				continue
			}
			subOrderProducts = append(subOrderProducts, *subOrderProduct)
		}
	}
	return subOrderProducts
}

func GetSubOrderProduct(
	dZeroDistributorCtx context.Context,
	orderProduct *ec_order.OrderProduct,
	storeProduct *ec_store_product.StoreProduct,
	dOneDistributorId string) *SubOrderProduct {
	productId := orderProduct.Id.Hex()
	// 同步过来的代销商品 sku 是 consignmentProductId + "_" + sku
	orderProductSku := orderProduct.Spec.Sku
	skus := strings.Split(orderProductSku, "_")
	if len(skus) > 1 {
		orderProductSku = skus[1]
	}
	subOrderProduct := &SubOrderProduct{
		ProductId: productId,
		Price:     orderProduct.PayAmount / orderProduct.Total,
		Total:     orderProduct.Total,
		Sku:       orderProductSku,
	}

	field := storeProduct.GetFieldByFieldName(ec_store_product.FIELD_NAME_COMMISSION_TYPE)
	if field != nil && field.ValueString != "" {
		subOrderProduct.CommissionType = field.ValueString
	} else {
		return nil
	}

	consignmentProductIdField := storeProduct.GetFieldByFieldName(ec_store_product.FIELD_NAME_CONSIGNMENT_PRODUCT_ID)
	if consignmentProductIdField != nil && consignmentProductIdField.ValueString != "" {
		subOrderProduct.ConsignmentProductId = consignmentProductIdField.ValueString
	}

	subOrderProduct.SkuAmounts = getSkuAmounts(dZeroDistributorCtx, dOneDistributorId, subOrderProduct.ConsignmentProductId)
	return subOrderProduct
}

func getSkuAmounts(dZeroDistributorCtx context.Context, distributorId, consignmentProductId string) []SkuAmount {
	if distributorId == "" || consignmentProductId == "" {
		log.Warn(dZeroDistributorCtx, "GetSkuAmounts params invalid", log.Fields{
			"distributorId":        distributorId,
			"consignmentProductId": consignmentProductId,
		})
		return nil
	}

	req := &component.GetConsignmentCommissionSettingRequest{
		DistributorId:        distributorId,
		ConsignmentProductId: consignmentProductId,
	}
	consignmentCommissionSetting, err := component.Retailer.GetConsignmentCommissionSetting(dZeroDistributorCtx, req)
	if err != nil {
		log.Warn(dZeroDistributorCtx, "Failed to get consignmentCommissionSetting", log.Fields{
			"errMessage": err.Error(),
			"req":        req,
		})
		return nil
	}
	log.Warn(dZeroDistributorCtx, "Consignment commission setting", log.Fields{
		"consignmentCommissionSetting": consignmentCommissionSetting,
		"req":                          req,
	})

	skuAmounts := []SkuAmount{}
	for _, skuSetting := range consignmentCommissionSetting.Skus {
		skuAmount := SkuAmount{
			Sku:                         skuSetting.Sku,
			DOneDefaultCommissionAmount: skuSetting.DefaultCommissionAmount,
			DOneCommissionAmount:        skuSetting.CommissionAmount,
		}
		skuAmounts = append(skuAmounts, skuAmount)
	}
	return skuAmounts
}

func GetDistributorId(distributorId string) string {
	return strings.Split(distributorId, "_")[0]
}

func GetD0BrandId(distributorId string) string {
	tempIds := strings.Split(distributorId, "_")
	if len(tempIds) == 2 {
		return tempIds[1]
	}
	return ""
}

func (builder *RetailerOrderBuilder) genCreateSimpleOrderRequest(splitProduct *SplitProduct) *pb_ec_order.CreateSimpleOrderRequest {
	req := &pb_ec_order.CreateSimpleOrderRequest{
		MemberId:         builder.MemberId,
		PromoterMemberId: builder.PromoterMemberId,
		StoreId:          builder.StoreId,
		Method:           splitProduct.Method,
		Remarks:          splitProduct.Remarks,
		MemberAddressId:  builder.MemberAddressId,
		Payment:          builder.Payment,
		Tags: []string{
			splitProduct.Type, // 区分经销商和代销商
			GetDistributorId(splitProduct.DistributorId), // 区分不同经销商
		},
		ExpectDeliveryAt:      splitProduct.ExpectDeliveryAt,
		ExpectDeliveryAtLabel: splitProduct.ExpectDeliveryAtLabel,
		Extra:                 builder.Extra,
	}
	if splitProduct.Type == MAIMENG_STORE_PRODUCT_CONSIGNMENT {
		d0BrandId := GetD0BrandId(splitProduct.DistributorId)
		if d0BrandId != "" {
			req.Tags = append(req.Tags, ec_order.ORDER_TAGS_BRAND_PREFIX+d0BrandId)
		}
	} else {
		if splitProduct.Method == ec_order.ORDER_DELIVERY_METHOD_CITY_EXPRESS {
			req.DeliveryFee = splitProduct.DeliveryFee
		}
	}
	req.Tags = append(req.Tags, builder.EcOrderTags...)
	if splitProduct.Contact != nil {
		req.Contact = &pb_ec_order.ContactRequest{
			Name:  splitProduct.Contact.Name,
			Phone: splitProduct.Contact.Phone,
		}
	}
	// 门店自提订单不需要收货地址
	if req.Method == ec_order.ORDER_DELIVERY_METHOD_PICKUP {
		req.MemberAddressId = ""
	}
	if splitProduct.Reservation != nil {
		req.Reservation = &pb_ec_order.ReservationInfo{
			Time:       splitProduct.Reservation.Time.Format(core_util.RFC3339Mili),
			MemberTime: splitProduct.Reservation.MemberTime.Format(core_util.RFC3339Mili),
		}
	}
	if builder.Channel != nil {
		req.ChannelId = builder.Channel.ChannelId
		req.OpenId = builder.Channel.OpenId
	}
	products := []*pb_ec_order.OrderProductRequest{}
	for _, product := range splitProduct.Products {
		productReq := &pb_ec_order.OrderProductRequest{}
		copier.Instance(nil).From(product).CopyTo(productReq)
		productReq.Sku = product.Spec.Sku
		products = append(products, productReq)
		if len(splitProduct.Campaigns) > 0 {
			campaignIds := []string{}
			for _, c := range splitProduct.Campaigns {
				if c.ProductId == product.Id.Hex() && !util.StrInArray(c.DiscountCampaignId, &campaignIds) {
					productReq.Campaigns = []*pb_ec_order.CampaignInfo{
						{
							Id:    c.DiscountCampaignId,
							Title: c.Title,
							Type:  "discountCampaign",
						},
					}
					campaignIds = append(campaignIds, c.DiscountCampaignId)
				}
			}
		}
	}
	if len(splitProduct.Campaigns) > 0 {
		campaigns := []*pb_ec_order.CampaignInfo{}
		for _, c := range splitProduct.Campaigns {
			campaign := &pb_ec_order.CampaignInfo{
				Id:    c.DiscountCampaignId,
				Title: c.Title,
				Type:  "discountCampaign",
			}
			campaigns = append(campaigns, campaign)
		}
		req.Campaigns = campaigns
	}
	req.Products = products
	return req
}

func (builder *RetailerOrderBuilder) removeBoughtCartProducts(ctx context.Context, subOrders []*SubOrder) {
	if !builder.Options.IsPurchase {
		productIds := []string{}
		for _, subOrder := range subOrders {
			productIds = append(productIds, subOrder.ProductIds...)
		}
		productIds = core_util.StrArrayUnique(productIds)
		ec_order.CCart.RemoveCheckedCartProductsByMemberIdAndStoreIdAndProductIds(ctx, builder.Order.MemberId.Hex(), builder.Order.StoreId.Hex(), util.ToMongoIds(productIds))
	}
}

func (builder *RetailerOrderBuilder) genOrderDetail() *pb_ec_retailer.OrderDetail {
	orderDetail := &pb_ec_retailer.OrderDetail{}
	copier.Instance(nil).From(builder.Order).CopyTo(orderDetail)
	return orderDetail
}
