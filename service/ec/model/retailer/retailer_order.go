package retailer

import (
	"context"
	"errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	proto_client "mairpc/proto/client"
	"mairpc/proto/common/request"
	ec_distribution "mairpc/service/ec/model/distribution"
	ec_order "mairpc/service/ec/model/order"
	ec_profitsharing "mairpc/service/ec/model/profitsharing"
	ec_store "mairpc/service/ec/model/store"
	"mairpc/service/share/component"
	"mairpc/service/share/util"
	"sort"
	"strings"
	"time"

	"github.com/qiniu/qmgo"
	"github.com/spf13/cast"
)

const (
	C_RETAILER_ORDER       = "ec.retailerOrder"
	ORDER_UNPAID_STATUS    = "unpaid"    // 未支付
	ORDER_PAID_STATUS      = "paid"      // 支付
	ORDER_SHIPPED_STATUS   = "shipped"   // 已发货，只要有一单发货，就算已发货
	ORDER_COMPLETED_STATUS = "completed" // 交易完成
	ORDER_CANCELED_STATUS  = "canceled"  // 交易取消

	DIVIDE_STATUS_SUCCESS = "success" // 分账完成

	PAYMENT_FEE_YEEPAY = "PAYMENT_FEE_YEEPAY"

	MAIMENG_STORE_PRODUCT_DISTRIBUTION        = "distribution"      // 经销商品
	MAIMENG_STORE_PRODUCT_CONSIGNMENT         = "consignment"       // 代销商品
	MAIMENG_STORE_PRODUCT_D_ZERO_DISTRIBUTION = "dZeroDistribution" // D0特殊代销订单，下单门店非 D0 入驻
	ORDER_TAGS_MAIMENG                        = "maimeng"

	DISTRIBUTOR_TYPE_D_ZERO = "D0" // 脉凯盟经销商
	DISTRIBUTOR_TYPE_D_ONE  = "D1" // 门店首次入驻的经销商
	DISTRIBUTOR_TYPE_D_X    = "DX" // 门店非首次入驻的经销商

	CONSIGNMENT_COMMISSION_TYPE_FOLLOW = "FOLLOW" // d0 给 d1 分佣跟随全局
	CONSIGNMENT_COMMISSION_TYPE_ALONE  = "ALONE"  // d0 给 d1 分佣单个 sku 独立设置

	BRAND_DIVIDE_TYPE_AFTER_PAID = "afterPaid" // 支付后立即分账
)

var (
	CRetailerOrder = &RetailerOrder{}
)

type RetailerOrder struct {
	Id                bson.ObjectId                            `bson:"_id,omitempty"`
	AccountId         bson.ObjectId                            `bson:"accountId"`
	CreatedAt         time.Time                                `bson:"createdAt"`
	UpdatedAt         time.Time                                `bson:"updatedAt"`
	MemberId          bson.ObjectId                            `bson:"memberId"`
	IsDeleted         bool                                     `bson:"isDeleted"`
	Number            string                                   `bson:"number"`
	StoreId           bson.ObjectId                            `bson:"storeId"`
	TotalAmount       uint64                                   `bson:"totalAmount"`
	PayAmount         uint64                                   `bson:"payAmount"`
	DeliveryFee       uint64                                   `bson:"deliveryFee,omitempty"`   // 邮费，小店经销订单如果是同城配送，存在运费。PayAmount 和 TotalAmount 已经加了 deliveryFee
	RefundAmount      uint64                                   `bson:"refundAmount,omitempty"`  // 订单实际退款金额
	TradeNo           string                                   `bson:"tradeNo,omitempty"`       // 微信支付流水号，合单支付时有值
	YeePayOrderNo     string                                   `bson:"yeePayOrderNo,omitempty"` // 易宝订单号，合单支付时有值
	PaidAt            time.Time                                `bson:"paidAt,omitempty"`
	CompletedAt       time.Time                                `bson:"completedAt,omitempty"`
	Status            string                                   `bson:"status"`                 // 订单当前状态 待付款：unpaid 已支付：paid 已发货：shipped 交易成功：completed 交易关闭：canceled
	RefundStatus      string                                   `bson:"refundStatus,omitempty"` // 订单中有商品发起过退款就设成 refunded
	TradeRecords      []*ec_order.TradeRecord                  `bson:"tradeRecords,omitempty"`
	Channel           *ec_order.OrderChannel                   `bson:"channel"`
	SplitProducts     []*SplitProduct                          `bson:"splitProducts"`
	SubOrders         []*SubOrder                              `bson:"subOrders,omitempty"`
	ProfitAmount      uint64                                   `bson:"profitAmount,omitempty"`      // 可分账金额
	AliPayUrl         string                                   `bson:"aliPayUrl"`                   // 支付宝扫码支付 url
	RefundOrderIds    []bson.ObjectId                          `bson:"refundOrderIds"`              // 已处理的退款单 ids
	Receivers         []ec_profitsharing.ProfitSharingReceiver `bson:"receivers"`                   // 分账接收方
	YpPromotionAmount uint64                                   `bson:"ypPromotionAmount,omitempty"` // 易宝支付优惠金额
	IsTest            bool                                     `bson:"-"`
}

type SubOrder struct {
	Id                        bson.ObjectId                            `bson:"id,omitempty"`                      // ec.order._id
	Number                    string                                   `bson:"number"`                            // 订单编号 ec.order.number
	Status                    string                                   `bson:"status"`                            // 子订单状态
	DivideStatus              string                                   `bson:"divideStatus,omitempty"`            // 子订单分账状态，分账完成时会更新为 success
	TradeNo                   string                                   `bson:"tradeNo,omitempty"`                 // 子订单微信支付流水号，子单单独支付时有值
	YeePayOrderNo             string                                   `bson:"yeePayOrderNo,omitempty"`           // 子订单易宝订单号，子单单独支付时有值
	ProfitAmount              uint64                                   `bson:"profitAmount,omitempty"`            // 子单可分账金额
	StaffProfitAmount         uint64                                   `bson:"staffProfitAmount,omitempty"`       // 子单导购分佣金额，按订单中单个商品导购佣金计算
	ActualStaffProfitAmount   uint64                                   `bson:"actualStaffProfitAmount,omitempty"` // 子单导购实际分佣金额，按订单中单个商品导购佣金计算，商品必须是分销商品才会计算
	PayAmount                 uint64                                   `bson:"payAmount,omitempty"`               // 订单实际支付金额
	RefundAmount              uint64                                   `bson:"refundAmount,omitempty"`            // 订单实际退款金额
	OriginalAmount            uint64                                   `bson:"originalAmount,omitempty"`          // 订单中商品成本价
	DistributorId             string                                   `bson:"distributorId"`                     // 经销商 id，如果是经销商品，为小店 id，如果是代销商品，为经销商 id，D0代销商品，为 distributorId_brandId
	Type                      string                                   `bson:"type"`                              // 订单类型，distribution 经销订单，consignment 代销订单
	Receivers                 []ec_profitsharing.ProfitSharingReceiver `bson:"receivers"`                         // 子单分账接收方
	Products                  []SubOrderProduct                        `bson:"products"`                          // 子订单商品
	DZeroDistributorAccountId string                                   `bson:"dZeroDistributorAccountId"`         // D0 经销商 accountId
	StaffId                   string                                   `bson:"staffId"`                           // 分销导购 id
	Campaigns                 []*Campaign                              `bson:"campaigns"`                         // 商品参与的活动
	DeliveryFee               uint64                                   `bson:"deliveryFee,omitempty"`             // 邮费
	YpPromotionAmount         uint64                                   `bson:"ypPromotionAmount,omitempty"`       // 易宝优惠金额
	BrandDivideType           string                                   `bson:"brandDivideType,omitempty"`         // 品牌分账类型，默认为空，afterPaid（支付后立即分账）
	BrandMerchantNo           string                                   `bson:"brandMerchantNo,omitempty"`         // 品牌分账商户号，默认为空，brandDivideType 为 afterPaid 时，有值
	BrandProfitAmount         uint64                                   `bson:"brandProfitAmount,omitempty"`       // 子单已完成品牌分账金额
	ProductIds                []string                                 `bson:"-"`
	IsTest                    bool                                     `bson:"-"`
	ProductProfitAmount       uint64                                   `bson:"-"` // 代销商品分佣金额
	TotalAmount               uint64                                   `bson:"-"`
}

type Campaign struct {
	ProductId          string `bson:"productId"`
	Total              uint64 `bson:"total"`
	CampaignId         string `bson:"campaignId"`         // D2R 限时特价活动 id ec.discountCampaign._id
	DiscountCampaignId string `bson:"discountCampaignId"` // 零售限时折扣 eccampaign.discountCampaign._id
	Title              string `bson:"title"`
	Sku                string `bson:"sku"`
	CurrentSales       uint64 `bson:"currentSales"`      // 下单时活动商品销量，包含本次下单的数量
	CurrentStaffSales  uint64 `bson:"currentStaffSales"` // 下单时活动商品导购推广销量，包含本次下单的数量
}

type SplitProduct struct {
	DistributorId         string                   `bson:"distributorId,omitempty"`         // 经销商 id，如果是经销商品，为小店 id，如果是代销商品，为经销商 id，D0代销商品，为 distributorId_brandId
	Type                  string                   `bson:"type,omitempty"`                  // 商品类型，distribution 经销商品，consignment 代销商品
	ProductIds            []bson.ObjectId          `bson:"productIds,omitempty"`            // 商品 ids
	Products              []*ec_order.OrderProduct `bson:"products,omitempty"`              // 订单商品
	Remarks               string                   `bson:"remarks,omitempty"`               // 订单备注
	Contact               *OrderContact            `bson:"contact,omitempty"`               // 自提信息，只有经销商品有值
	Reservation           *Reservation             `bson:"reservation,omitempty"`           // 自提预约信息，只有经销商品有值
	Logistics             *LogisticsInfo           `bson:"logistics,omitempty"`             // 配送信息
	Method                string                   `bson:"method,omitempty"`                // 配送方式
	ExpectDeliveryAt      int64                    `bson:"expectDeliveryAt,omitempty"`      // 同城快送送达时间
	ExpectDeliveryAtLabel string                   `bson:"expectDeliveryAtLabel,omitempty"` // 期望送达时间文本
	Campaigns             []*Campaign              `bson:"campaigns,omitempty"`             // 商品参与的活动
	DeliveryFee           uint64                   `bson:"deliveryFee,omitempty"`           // 运费，目前只有小店同城配送时有运费
}

type SubOrderProduct struct {
	ProductId            string      `bson:"productId"`                // 商品 id
	ConsignmentProductId string      `bson:"consignmentProductId"`     // 代销商品 id
	CommissionType       string      `bson:"commissionType,omitempty"` // 分佣类型 FOLLOW 跟随全局, ALONE 独立规则
	Sku                  string      `bson:"sku"`                      // 商品规格
	Price                uint64      `bson:"price"`                    // 商品单价
	Total                uint64      `bson:"total"`                    // 商品购买数量
	SkuAmounts           []SkuAmount `bson:"skuAmounts"`               // sku 对应的 D1 分销金额
}

type SkuAmount struct {
	Sku                         string `bson:"sku"`                         // 商品 sku
	DOneDefaultCommissionAmount uint64 `bson:"dOneDefaultCommissionAmount"` // D0 分给 D1 的默认佣金
	DOneCommissionAmount        uint64 `bson:"dOneCommissionAmount"`        // D0 分给 D1 的佣金
}

type OrderContact struct {
	Name    string         `bson:"name,omitempty"`
	Phone   string         `bson:"phone,omitempty"`
	Address ContactAddress `bson:"address"`
}

type ContactAddress struct {
	Province  string  `bson:"province"`
	City      string  `bson:"city"`
	District  string  `bson:"district"`
	Detail    string  `bson:"detail"`
	Longitude float64 `bson:"longitude"`
	Latitude  float64 `bson:"latitude"`
}
type Reservation struct {
	// 提货时间段开始的时间
	Time time.Time `bson:"time,omitempty"`
	// 客户提货时间
	MemberTime time.Time `bson:"memberTime,omitempty"`
}

type LogisticsInfo struct {
	Fee                   uint64    `bson:"fee"`
	ProcessBy             string    `bson:"processBy"`
	ShippedAt             time.Time `bson:"shippedAt,omitempty"`
	ExpectDeliveryAt      time.Time `bson:"expectDeliveryAt,omitempty"`
	ExpectDeliveryAtLabel string    `bson:"expectDeliveryAtLabel,omitempty"`
	FreeReason            string    `bson:"freeReason,omitempty"`
}

func (order *RetailerOrder) Create(ctx context.Context) error {
	order.Id = bson.NewObjectId()
	order.CreatedAt = time.Now()
	order.UpdatedAt = order.CreatedAt
	order.IsDeleted = false
	_, err := extension.DBRepository.Insert(ctx, C_RETAILER_ORDER, *order)
	if err != nil {
		return err
	}
	return nil
}

func (order *RetailerOrder) GetById(ctx context.Context, id bson.ObjectId) *RetailerOrder {
	condition := Common.GenDefaultConditionById(ctx, id)
	result := RetailerOrder{}
	err := extension.DBRepository.FindOne(ctx, C_RETAILER_ORDER, condition, &result)
	if err != nil || !result.Id.Valid() {
		return nil
	}
	return &result
}

func (order *RetailerOrder) GetByCondition(ctx context.Context, condition bson.M) *RetailerOrder {
	result := RetailerOrder{}
	err := extension.DBRepository.FindOne(ctx, C_RETAILER_ORDER, condition, &result)
	if err != nil || !result.Id.Valid() {
		return nil
	}
	return &result
}

func (r *RetailerOrder) Update(ctx context.Context) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       r.Id,
	}
	updater := bson.M{
		"$set": bson.M{
			"subOrders":     r.SubOrders,
			"payAmount":     r.PayAmount,
			"profitAmount":  r.ProfitAmount,
			"refundAmount":  r.RefundAmount,
			"status":        r.Status,
			"tradeNo":       r.TradeNo,
			"yeePayOrderNo": r.YeePayOrderNo,
			"receivers":     r.Receivers,
			"updatedAt":     time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_RETAILER_ORDER, selector, updater)
}

func (r *RetailerOrder) UpdateSubOrders(ctx context.Context, subOrders []*SubOrder) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       r.Id,
	}
	updater := bson.M{
		"$set": bson.M{
			"subOrders": subOrders,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_RETAILER_ORDER, selector, updater)
}

func (r *RetailerOrder) GetBySubOrderId(ctx context.Context, subOrderId bson.ObjectId) (RetailerOrder, error) {
	condition := bson.M{
		"accountId":    util.GetAccountIdAsObjectId(ctx),
		"subOrders.id": subOrderId,
	}
	result := RetailerOrder{}
	err := extension.DBRepository.FindOne(ctx, C_RETAILER_ORDER, condition, &result)
	return result, err
}

// 获取请求易宝接口的 orderId
func GetOrderId(ctx context.Context, subOrderId bson.ObjectId) (string, error) {
	retailerOrder, err := CRetailerOrder.GetBySubOrderId(ctx, subOrderId)
	if err != nil {
		return "", err
	}
	if retailerOrder.IsAggPay() {
		return retailerOrder.Id.Hex(), nil
	}
	return subOrderId.Hex(), nil
}

// 订单是否是合单支付
func (r *RetailerOrder) IsAggPay() bool {
	if r.TradeNo != "" && r.YeePayOrderNo != "" {
		return true
	}
	return false
}

// 设置可分账金额
func (r *RetailerOrder) SetProfitAmount(ctx context.Context, isForce bool) {
	if !isForce && !r.needSetProfitAmount() {
		return
	}

	// 合并支付
	if r.IsAggPay() {
		totalProfitAmount, err := GetUnSplitAmount(ctx, r.Id.Hex())
		if err != nil {
			log.Warn(ctx, "Get unSplit amount fail", log.Fields{
				"errMessage":      err.Error(),
				"retailerOrderId": r.Id.Hex(),
			})
			return
		}
		// 如果订单中存在品牌货款支付后立即分账，需要将品牌货款分账金额加到可分账金额中
		// 实际在创建预分账的时候，不会再为品牌货款创建
		for _, subOrder := range r.SubOrders {
			if subOrder.IsRefunded(ctx) {
				continue
			}
			if subOrder.BrandDivideType == BRAND_DIVIDE_TYPE_AFTER_PAID {
				totalProfitAmount += subOrder.BrandProfitAmount
				continue
			}
		}
		r.ProfitAmount = totalProfitAmount
		r.CalSubOrderProfitAmount(ctx)
		return
	}

	// 子单单独支付
	for _, subOrder := range r.SubOrders {
		profitAmount, err := GetUnSplitAmount(ctx, subOrder.Id.Hex())
		if err != nil {
			log.Warn(ctx, "Get unSplit amount fail", log.Fields{
				"errMessage": err.Error(),
				"subOrder":   subOrder,
			})
			continue
		}
		if !subOrder.IsRefunded(ctx) && subOrder.BrandDivideType == BRAND_DIVIDE_TYPE_AFTER_PAID {
			profitAmount += subOrder.BrandProfitAmount
		}
		subOrder.ProfitAmount = profitAmount
	}
}

// 为每个子单按比例计算可分账金额
func (r *RetailerOrder) CalSubOrderProfitAmount(ctx context.Context) {
	if r.ProfitAmount == 0 {
		log.Warn(ctx, "Retailer order profit amount is zero", log.Fields{
			"retailerOrderId": r.Id.Hex(),
		})
	}
	if r.PayAmount < r.RefundAmount {
		log.Warn(ctx, "Retailer order amount is error", log.Fields{
			"retailerOrderId": r.Id.Hex(),
			"payAmount":       r.PayAmount,
			"refundAmount":    r.RefundAmount,
			"profitAmount":    r.ProfitAmount,
		})
		return
	}

	refundedSubOrderIds := []string{}
	dividedAmount := uint64(0) // 实际分账成功的金额
	for _, subOrder := range r.SubOrders {
		// 子订单全部退款了，可分账金额设置为 0
		if subOrder.IsRefunded(ctx) {
			subOrder.ProfitAmount = 0
			refundedSubOrderIds = append(refundedSubOrderIds, subOrder.Id.Hex())
			continue
		}
		if subOrder.IsDivided() {
			dividedAmount += subOrder.PayAmount - subOrder.RefundAmount
			continue
		}
	}

	getActualAmount := func(s *SubOrder) uint64 {
		if util.StrInArray(s.Id.Hex(), &refundedSubOrderIds) {
			return 0
		}
		if s.IsDivided() {
			return s.PayAmount - s.RefundAmount
		}
		actualAmount := uint64(0)
		// 这里判断下，防止借位情况出现
		if s.PayAmount > s.RefundAmount {
			actualAmount = s.PayAmount - s.RefundAmount
		}
		// 未全额退款且未分账的子单按实付金额从小到大排在最后，加上 r.PayAmount 为了排在最后
		return r.PayAmount + actualAmount
	}
	// 子订单根据 getActualAmount 返回结果从小到大排序
	// 最终子订单顺序为：已全额退款、已分账、未全额退款且未分账
	sort.SliceStable(r.SubOrders, func(i, j int) bool {
		iAmount := getActualAmount(r.SubOrders[i])
		jAmount := getActualAmount(r.SubOrders[j])
		if iAmount < jAmount {
			return true
		}
		return false
	})

	calculatedProfitAmount := uint64(0)
	// 总支付金额需要减去退款的和已分账完成的支付金额
	totalPayAmount := r.PayAmount - r.RefundAmount - dividedAmount
	for index, subOrder := range r.SubOrders {
		// 过滤掉已全额退款、已分账
		if util.StrInArray(subOrder.Id.Hex(), &refundedSubOrderIds) || subOrder.IsDivided() {
			continue
		}
		// 总单可分账金额为 0 后需要更新子单可分账金额为 0
		if r.ProfitAmount == 0 {
			subOrder.ProfitAmount = 0
			continue
		}
		// 防止计算出现误差，最后一笔订单的可分账金额 = 总可分账金额 - 已经算出来的可分账金额
		if index == len(r.SubOrders)-1 {
			subOrder.ProfitAmount = r.ProfitAmount - calculatedProfitAmount
			continue
		}

		// 这里判断下，防止这种情况出现
		if subOrder.PayAmount <= subOrder.RefundAmount {
			subOrder.ProfitAmount = 0
			continue
		}

		// 计算子单可分账金额
		subOrderPayAmount := subOrder.PayAmount - subOrder.RefundAmount
		subProfitAmount := cast.ToUint64(util.DivideFloatWithRound(util.MultiplyFloat(float64(subOrderPayAmount), float64(r.ProfitAmount)), float64(totalPayAmount), 0))
		if subProfitAmount > subOrderPayAmount {
			subProfitAmount = subOrderPayAmount
		}
		subOrder.ProfitAmount = subProfitAmount
		calculatedProfitAmount += subProfitAmount
	}
}

// 是否需要查询易宝订单设置分账金额
func (r *RetailerOrder) needSetProfitAmount() bool {
	if r.ProfitAmount > 0 {
		return false
	}
	return true
}

// 获取子订单
func (r *RetailerOrder) GetSubOrder(subOrderId string) *SubOrder {
	for _, subOrder := range r.SubOrders {
		if subOrder.Id.Hex() == subOrderId {
			return subOrder
		}
	}
	return nil
}

func (r *RetailerOrder) UpdateAliPayUrl(ctx context.Context, url string) error {
	selector := Common.GenDefaultConditionById(ctx, r.Id)
	updater := bson.M{
		"$set": bson.M{
			"updatedAt": time.Now(),
			"aliPayUrl": url,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_RETAILER_ORDER, selector, updater)
}

// distribution 经销订单
// consignment 代销订单，包含D0代销订单，此时 store 是在 D0 入驻
// dZeroConsignment D0 特殊代销订单，store 非在 D0 入驻
func (o *SubOrder) FormatSubOrderType(ctx context.Context, store ec_store.Store) {
	if o.Type == MAIMENG_STORE_PRODUCT_DISTRIBUTION {
		return
	}
	d0DistributorId := ""
	d1DistributorId := ""
	for _, distributor := range store.DistributionStoreInfo.Distributors {
		if distributor.Type == DISTRIBUTOR_TYPE_D_ZERO {
			d0DistributorId = distributor.Id
		}
		if distributor.Type == DISTRIBUTOR_TYPE_D_ONE {
			d1DistributorId = distributor.Id
		}
	}
	//  D0 代销订单，store 非在 D0 入驻
	// d0Receiver 和 d1Receiver distributorId 相同表示小店是D0开发入驻的
	if o.GetDistributorId() == d0DistributorId && d0DistributorId != d1DistributorId {
		o.Type = MAIMENG_STORE_PRODUCT_D_ZERO_DISTRIBUTION
	}
}

func (o *SubOrder) GetDistributorId() string {
	return strings.Split(o.DistributorId, "_")[0]
}

func (o *SubOrder) GetD0BrandId() string {
	tempIds := strings.Split(o.DistributorId, "_")
	if len(tempIds) == 2 {
		return tempIds[1]
	}
	return ""
}

func (o *SubOrder) CheckProfitSharingReceivers(store ec_store.Store, receivers []ec_profitsharing.ProfitSharingReceiver) bool {
	isValidReceiver := true
	platformDistributorEnable := false
	dZeroDistributorEnable := false
	for _, receiver := range receivers {
		if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_PLATFORM {
			platformDistributorEnable = receiver.DistributorInfo.PlatformDistributorEnable
		}
		if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_D_ONE_DISTRIBUTOR {
			dZeroDistributorEnable = receiver.DistributorInfo.DZeroDistributorEnable
		}
	}

	switch o.Type {
	// 经销订单，有两个/三个分账接收方
	case MAIMENG_STORE_PRODUCT_DISTRIBUTION:
		defaultReceiverCount := 2 // 门店、平台
		if platformDistributorEnable && !store.IsDOneDistributorTerminated() {
			defaultReceiverCount += 1 // 平台服务费开启了经销商分佣，且经销商合作状态未终止，接收方多一个
		}
		if HaveStaffReceiver(receivers) {
			defaultReceiverCount += 1 // 有导购分账接收者
		}
		if len(receivers) != defaultReceiverCount {
			isValidReceiver = false
		}
	// 代销订单（包含 store 在 D0 入驻的情况）
	case MAIMENG_STORE_PRODUCT_CONSIGNMENT:
		defaultReceiverCount := 3 // 门店、平台、经销商
		if platformDistributorEnable && !store.IsDOneDistributorTerminated() {
			defaultReceiverCount += 1 // 平台服务费开启了经销商分佣，且经销商合作状态未终止，接收方多一个
		}
		if HaveBrandReceiver(receivers) {
			defaultReceiverCount += 1 // 有品牌分账接收放
		}
		if HaveStaffReceiver(receivers) {
			defaultReceiverCount += 1 // 有导购分账接收者
		}
		if len(receivers) != defaultReceiverCount {
			isValidReceiver = false
		}
	// D0 代销订单，store 非在 D0 入驻
	case MAIMENG_STORE_PRODUCT_D_ZERO_DISTRIBUTION:
		defaultReceiverCount := 3 // 门店、平台、经销商
		if platformDistributorEnable && !store.IsDOneDistributorTerminated() {
			defaultReceiverCount += 1
		}
		if dZeroDistributorEnable {
			defaultReceiverCount += 1
		}
		if HaveBrandReceiver(receivers) {
			defaultReceiverCount += 1
		}
		if HaveStaffReceiver(receivers) {
			defaultReceiverCount += 1
		}
		if len(receivers) != defaultReceiverCount {
			isValidReceiver = false
		}
	}
	return isValidReceiver
}

func HaveBrandReceiver(receivers []ec_profitsharing.ProfitSharingReceiver) bool {
	for _, receiver := range receivers {
		if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_BRAND {
			return true
		}
	}
	return false
}

func HaveStaffReceiver(receivers []ec_profitsharing.ProfitSharingReceiver) bool {
	for _, receiver := range receivers {
		if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_STAFF {
			return true
		}
	}
	return false
}

// 是否是经销订单
func (o *SubOrder) IsDistributionOrder() bool {
	if o.Type == MAIMENG_STORE_PRODUCT_DISTRIBUTION {
		return true
	}
	return false
}

// 是否是代销订单
func (o *SubOrder) IsConsignmentOrder() bool {
	if o.Type == MAIMENG_STORE_PRODUCT_CONSIGNMENT {
		return true
	}
	return false
}

// 获取品牌分账接受方
func (o *SubOrder) GetBrandReceiver() ec_profitsharing.ProfitSharingReceiver {
	var receiver ec_profitsharing.ProfitSharingReceiver
	for _, r := range o.Receivers {
		if r.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_BRAND {
			receiver = r
		}
	}
	return receiver
}

// 设置 brandDivideType
func (o *SubOrder) SetBrandDivideType(ctx context.Context) {
	brandReceiver := o.GetBrandReceiver()
	if !brandReceiver.Id.Valid() {
		return
	}
	brandId := brandReceiver.DistributorInfo.DistributorId
	if brandId == "" {
		return
	}
	distributorCtx := core_util.CtxWithAccountID(ctx, o.DZeroDistributorAccountId)
	brand, err := proto_client.GetProductServiceClient().GetBrand(distributorCtx, &request.DetailRequest{Id: brandId})
	if err != nil {
		log.Warn(ctx, "Get brand fail", log.Fields{
			"subOrder":   o,
			"errMessage": err.Error(),
		})
		return
	}
	if brand != nil {
		o.BrandDivideType = brand.DivideType
	}
}

// 设置 brandMerchantNo
func (o *SubOrder) SetBrandMerchantNo(ctx context.Context) {
	if o.BrandDivideType == "" {
		return
	}
	brandReceiver := o.GetBrandReceiver()
	if !brandReceiver.Id.Valid() {
		return
	}
	receiver := ec_profitsharing.CProfitSharingReceiver.GetById(ctx, brandReceiver.Id)
	if receiver != nil {
		o.BrandMerchantNo = receiver.Account
	}
}

// 验算分账金额
func (o *SubOrder) CheckProfitAmount(profitAmountMap map[string]uint64) bool {
	if o.ProfitAmount == 0 {
		return false
	}
	total := uint64(0)
	for key, account := range profitAmountMap {
		if key == PAYMENT_FEE_YEEPAY {
			continue
		}
		// 相加出现进位（溢出，超过 MaxUint64）说明计算金额出现减法借位（不够减），直接验证不通过
		if total+account < total {
			return false
		}
		total += account
	}
	return total == o.ProfitAmount
}

// 验算子单金额
func (o *SubOrder) CheckAmount() error {
	if o.ProfitAmount == 0 {
		return errors.New("ProfitAmount is zero")
	}
	if o.PayAmount == 0 {
		return errors.New("PayAmount is zero")
	}
	if o.PayAmount < o.ProfitAmount {
		return errors.New("PayAmount is less than profitAmount")
	}
	if o.PayAmount < o.RefundAmount {
		return errors.New("PayAmount is less than refundAmount")
	}
	return nil
}

// D0 普通代销订单，store 在 D0 入驻
func (o *SubOrder) IsD0CommonConsignmentOrder(ctx context.Context, store ec_store.Store) bool {
	if o.Type == MAIMENG_STORE_PRODUCT_DISTRIBUTION {
		return false
	}
	d0DistributorId := ""
	d1DistributorId := ""
	for _, distributor := range store.DistributionStoreInfo.Distributors {
		if distributor.Type == DISTRIBUTOR_TYPE_D_ZERO {
			d0DistributorId = distributor.Id
		}
		if distributor.Type == DISTRIBUTOR_TYPE_D_ONE {
			d1DistributorId = distributor.Id
		}
	}
	if o.GetDistributorId() == d0DistributorId && d0DistributorId == d1DistributorId {
		return true
	}
	return false
}

// D0 是否等于 D1
func IsD0EqualD1(ctx context.Context, store ec_store.Store) bool {
	d0DistributorId := ""
	d1DistributorId := ""
	for _, distributor := range store.DistributionStoreInfo.Distributors {
		if distributor.Type == DISTRIBUTOR_TYPE_D_ZERO {
			d0DistributorId = distributor.Id
		}
		if distributor.Type == DISTRIBUTOR_TYPE_D_ONE {
			d1DistributorId = distributor.Id
		}
	}
	if d0DistributorId == d1DistributorId {
		return true
	}
	return false
}

// 所有子单是否已全部退款
func (r *RetailerOrder) IsAllSubOrderRefunded(ctx context.Context) bool {
	refundedSubOrderCount := 0
	for _, subOrder := range r.SubOrders {
		if subOrder.IsRefunded(ctx) {
			refundedSubOrderCount++
		}
	}
	if len(r.SubOrders) == refundedSubOrderCount {
		return true
	}
	return false
}

// 子单是否已经全部退款
func (o *SubOrder) IsRefunded(ctx context.Context) bool {
	if o.IsTest {
		return false
	}
	order, _ := ec_order.COrder.GetById(ctx, o.Id)
	refundedProductCount := 0
	for _, product := range order.Products {
		if product.RefundStatus == ec_order.ORDER_REFUND_STATUS_REFUNDED {
			refundedProductCount++
		}
	}
	if len(order.Products) == refundedProductCount {
		return true
	}
	return false
}

// 子单是否已经分账完成
func (o *SubOrder) IsDivided() bool {
	if o.IsTest {
		return false
	}
	return o.DivideStatus == DIVIDE_STATUS_SUCCESS
}

func (*RetailerOrder) FindAndApply(ctx context.Context, selector, updater bson.M) (*RetailerOrder, error) {
	result := &RetailerOrder{}
	err := extension.DBRepository.FindAndApply(ctx, C_RETAILER_ORDER, selector, nil, qmgo.Change{
		Update:    updater,
		ReturnNew: true,
	}, result)
	return result, err
}

func (o *SubOrder) GetActualPayAmount() uint64 {
	if o.PayAmount >= o.RefundAmount {
		return o.PayAmount - o.RefundAmount
	}
	return 0
}

// D0 代销订单
func (o *SubOrder) IsD0ConsignmentOrder(store ec_store.Store) bool {
	if o.Type == MAIMENG_STORE_PRODUCT_DISTRIBUTION {
		return false
	}
	d0DistributorId := ""
	for _, distributor := range store.DistributionStoreInfo.Distributors {
		if distributor.Type == DISTRIBUTOR_TYPE_D_ZERO {
			d0DistributorId = distributor.Id
		}
	}

	if o.GetDistributorId() == d0DistributorId {
		return true
	}
	return false
}

func (o *RetailerOrder) UpdateReceivers(ctx context.Context, orderId string) {
	if len(o.Receivers) == 0 {
		receivers := GetProfitSharingReceivers(ctx, o.StoreId)
		if len(receivers) == 0 {
			log.Warn(ctx, "Receivers is empty", log.Fields{
				"retailerOrder": o,
			})
			return
		}
		o.Receivers = receivers
	}

	store, err := ec_store.CStore.GetById(ctx, o.StoreId)
	if err != nil {
		log.Warn(ctx, "Get store fail", log.Fields{
			"retailerOrder": o,
			"errMessage":    err.Error(),
		})
		return
	}

	for _, subOrder := range o.SubOrders {
		// 合单支付时，更新所有字段的 subOrder
		// 单个子单支付时，更新对应子单
		if o.Id.Hex() == orderId || subOrder.Id.Hex() == orderId {
			// 已经有的，不能再更新
			if len(subOrder.Receivers) > 0 {
				continue
			}
			oldType := subOrder.Type
			subOrder.FormatSubOrderType(ctx, store)
			if subOrder.StaffId == "" {
				subOrder.StaffId = subOrder.GetPromoterStaffId(ctx)
			}
			subOrderReceivers := filterProfitSharingReceivers(ctx, o.Receivers, *subOrder, store)
			subOrder.Type = oldType
			if len(subOrderReceivers) == 0 {
				log.Warn(ctx, "subOrder receivers is empty", log.Fields{
					"subOrder": subOrder,
				})
				continue
			}
			subOrder.Receivers = subOrderReceivers
			subOrder.SetBrandDivideType(ctx)
			subOrder.SetBrandMerchantNo(ctx)
		}
	}
	o.Update(ctx)
}

func (o *RetailerOrder) IncDiscountCampaignSales(ctx context.Context, orderId string, isRefund bool) {
	for _, subOrder := range o.SubOrders {
		// 合单支付时，更新所有子单的 subOrder
		// 单个子单支付时，更新对应子单
		if o.Id.Hex() == orderId || subOrder.Id.Hex() == orderId {
			for _, c := range subOrder.Campaigns {
				if c.CampaignId == "" {
					continue
				}
				sales := int(c.Total)
				if isRefund {
					sales = -sales
				}
				CDiscountCampaign.IncSales(ctx, util.ToMongoId(c.CampaignId), util.ToMongoId(c.ProductId), c.Sku, sales)
				if bson.IsObjectIdHex(subOrder.StaffId) {
					discountCampaignStaffSales, _ := CDiscountCampaign.GetDiscountCampaignStaffSales(ctx, util.ToMongoId(c.CampaignId), util.ToMongoId(subOrder.StaffId), util.ToMongoId(c.ProductId))
					if discountCampaignStaffSales.Id.Valid() {
						discountCampaignStaffSales.IncSales(ctx, util.ToMongoId(subOrder.StaffId), util.ToMongoId(c.CampaignId), util.ToMongoId(c.ProductId), c.Sku, sales)
					}
				}
			}
		}
	}
}

func GetProfitSharingReceivers(ctx context.Context, storeId bson.ObjectId) []ec_profitsharing.ProfitSharingReceiver {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"type":      ec_profitsharing.RECEIVER_TYPE_MERCHANT,
		"$or": []bson.M{
			{"storeIds": storeId},
			{"allStores": true},
		},
		"profitType": bson.M{
			"$in": []string{
				ec_profitsharing.PROFIT_TYPE_PROFIT_SHARING,
				ec_profitsharing.PROFIT_TYPE_DISTRIBUTION,
			},
		},
		"isDeleted": false,
	}
	condition = util.FormatConditionContainedOr(condition)
	receivers := ec_profitsharing.CProfitSharingReceiver.GetAllByCondition(ctx, condition)
	return receivers
}

// 经销订单分账接收方：平台(平台手续费)，经销商D1(平台手续费分成)，小店
// 代销订单分账接收方：平台(平台手续费)，经销商D1(平台手续费分成)，小店，经销商
// D0代销订单分账接收方：平台(平台手续费)，经销商D1(平台手续费分成)，小店，D1经销商，D0经销商(目前仅值脉盟经销商)
func filterProfitSharingReceivers(ctx context.Context, receivers []ec_profitsharing.ProfitSharingReceiver, subOrder SubOrder, store ec_store.Store) []ec_profitsharing.ProfitSharingReceiver {
	newReceivers := []ec_profitsharing.ProfitSharingReceiver{}
	d1Receiver := GetDOneDistributorProfitSharingReceiver(receivers, store)
	// 如果是D0的代销订单,小店是D1开发入驻的，需要给D1分账
	if subOrder.Type == MAIMENG_STORE_PRODUCT_D_ZERO_DISTRIBUTION {
		// 经销商分佣未设置，使用默认佣金比例
		if !d1Receiver.DistributorInfo.DZeroDistributorEnable {
			distributorCtx := core_util.CtxWithAccountID(ctx, subOrder.DZeroDistributorAccountId)
			consignmentDefault, err := component.Retailer.GetConsignmentDefault(distributorCtx)
			if err != nil {
				log.Warn(ctx, "Get consignmentDefault fail", log.Fields{
					"errMsg":   err.Error(),
					"subOrder": subOrder,
				})
			}
			log.Warn(ctx, "Get consignmentDefault", log.Fields{
				"consignmentDefault": consignmentDefault,
				"subOrder":           subOrder,
			})
			if consignmentDefault.Id != "" {
				d1Receiver.DistributorInfo.DZeroDistributorEnable = true
				d1Receiver.DistributorInfo.DZeroDistributorProportion = cast.ToFloat64(consignmentDefault.Proportion)
			}
		}
		if d1Receiver.DistributorInfo.DZeroDistributorEnable {
			d1Receiver.Proportion = d1Receiver.DistributorInfo.DZeroDistributorProportion
			d1Receiver.RelationType = ec_profitsharing.RECEIVER_RELATION_TYPE_D_ONE_DISTRIBUTOR
			d1Receiver.ProfitType = ec_profitsharing.PROFIT_TYPE_PERCENTAGE
			newReceivers = append(newReceivers, d1Receiver)
		}
	}
	// 判断是否是扫码购订单
	scanBuyEnable := false
	order, _ := ec_order.COrder.GetById(ctx, subOrder.Id)
	if order.IsScanBuy() {
		scanBuyEnable = true
	}
	// 判断是否是储值卡订单
	storedCardOrder := false
	if order.HaveStoredValueCardProduct() {
		storedCardOrder = true
	}

	for _, receiver := range receivers {
		// 导购分销
		// receiver.DistributorInfo.DistributorId 为导购 staffId
		if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_STAFF &&
			receiver.DistributorInfo.DistributorId == subOrder.StaffId {
			newReceivers = append(newReceivers, receiver)
			continue
		}
		// 供应链品牌 receiver.DistributorInfo.DistributorId 存对应品牌 id
		// 创建订单时，只有D0的代销单 subOrder.DistributorId 才是 d0DistributorId_brandId 格式，所以这里不用检查 subOrder 是否是D0代销单
		if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_BRAND &&
			strings.Contains(subOrder.DistributorId, receiver.DistributorInfo.DistributorId) {
			newReceivers = append(newReceivers, receiver)
			continue
		}
		// 小店
		if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_STORE_OWNER {
			newReceivers = append(newReceivers, receiver)
			continue
		}
		// 平台
		if receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_PLATFORM {
			if receiver.DistributorInfo.ScanValueEnable && scanBuyEnable {
				receiver.Proportion = receiver.DistributorInfo.ScanValueProportion
				receiver.SingleOrderProfitCap = receiver.DistributorInfo.ScanValueSingleOrderProfitCap
			}
			if receiver.DistributorInfo.StoredValueEnable && storedCardOrder {
				receiver.Proportion = receiver.DistributorInfo.StoredValueProportion
				receiver.SingleOrderProfitCap = receiver.DistributorInfo.StoredValueSingleOrderProfitCap
			}
			newReceivers = append(newReceivers, receiver)
			// 平台手续费开启了经销商分成
			if receiver.DistributorInfo.PlatformDistributorEnable {
				d1Receiver.Proportion = receiver.DistributorInfo.PlatformDistributorProportion
				d1Receiver.ProfitType = ec_profitsharing.PROFIT_TYPE_SERVICE_PERCENTAGE
				// 这里是手续费，所有将 RelationType 改为 PLATFORM_SUB
				d1Receiver.RelationType = ec_profitsharing.RECEIVER_RELATION_TYPE_PLATFORM_SUB
				newReceivers = append(newReceivers, d1Receiver)
			}
			continue
		}
		// 经销商
		if (subOrder.Type == MAIMENG_STORE_PRODUCT_CONSIGNMENT ||
			subOrder.Type == MAIMENG_STORE_PRODUCT_D_ZERO_DISTRIBUTION) &&
			receiver.RelationType == ec_profitsharing.RECEIVER_RELATION_TYPE_DISTRIBUTOR &&
			subOrder.GetDistributorId() == receiver.DistributorInfo.DistributorId {
			newReceivers = append(newReceivers, receiver)
		}
	}
	return newReceivers
}

func (s *SubOrder) GetPromoterStaffId(ctx context.Context) string {
	order, _ := ec_order.COrder.GetById(ctx, s.Id)
	if !order.Distribution.PromoterId.Valid() {
		return ""
	}
	promoter, err := ec_distribution.CPromoter.GetById(ctx, order.Distribution.PromoterId)
	if err != nil || promoter == nil {
		log.Warn(ctx, "Get promoter fail", log.Fields{
			"order": order,
		})
		return ""
	}
	return promoter.StaffId.Hex()
}

func (s *SubOrder) GetDiscountCampaignId(productId string) string {
	for _, c := range s.Campaigns {
		if c.ProductId == productId && c.CampaignId != "" {
			return c.CampaignId
		}
	}
	return ""
}

// 检查子订单是否参与了限时特价活动，并且开启了佣金
// 目前一个商品只能参与一个限时特价
func (s *SubOrder) CheckCampaigns(ctx context.Context) bool {
	if len(s.Campaigns) == 0 {
		return false
	}
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       util.ToMongoId(s.Campaigns[0].CampaignId),
	}
	campaign, _ := CDiscountCampaign.GetByCondition(ctx, selector)
	// 是否开启佣金设置，此次改为了统一设置，不再单个商品设置
	for _, p := range campaign.Products {
		if !p.IsDisabled {
			return true
		}
	}
	return false
}

func (o *RetailerOrder) CalYpPromotionAmount() {
	if o.YpPromotionAmount == 0 {
		return
	}
	calculatedYpPromotionAmount := uint64(0)
	for index, subOrder := range o.SubOrders {
		// 防止计算出现误差，最后一笔订单的易宝优惠金额 = 总易宝优惠金额 - 已经算出来的易宝优惠金额
		if index == len(o.SubOrders)-1 {
			subOrder.YpPromotionAmount = o.YpPromotionAmount - calculatedYpPromotionAmount
			continue
		}
		subYpPromotionAmount := cast.ToUint64(util.DivideFloatWithRound(util.MultiplyFloat(float64(subOrder.PayAmount), float64(o.YpPromotionAmount)), float64(o.PayAmount), 0))
		calculatedYpPromotionAmount += subYpPromotionAmount
		subOrder.YpPromotionAmount = subYpPromotionAmount
		o.SubOrders[index] = subOrder
	}
}

func (o *RetailerOrder) UpdateEcOrderExtra(ctx context.Context) {
	if o.YpPromotionAmount == 0 {
		return
	}
	for _, subOrder := range o.SubOrders {
		order, _ := ec_order.COrder.GetById(ctx, subOrder.Id)
		if order.Extra == nil {
			order.Extra = bson.M{}
		}
		order.Extra["ypPromotionAmount"] = subOrder.YpPromotionAmount
		order.UpdateExtra(ctx)
	}
}
