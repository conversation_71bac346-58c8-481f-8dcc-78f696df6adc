package order

import (
	"encoding/json"
	"fmt"
	"maps"
	"math"
	"strconv"
	"strings"
	"time"

	mairpc "mairpc/core/client"
	"mairpc/core/component/shouqianba"
	wechat_trade "mairpc/core/component/wechat/trade"
	"mairpc/core/component/yeepay"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	pb_client "mairpc/proto/client"
	proto_client "mairpc/proto/client"
	"mairpc/proto/common/ec"
	"mairpc/proto/common/request"
	"mairpc/proto/coupon"
	pb_card_product "mairpc/proto/ec/cardProduct"
	"mairpc/proto/ec/mall"
	pb_ec_marketing "mairpc/proto/ec/marketing"
	"mairpc/proto/ec/order"
	pb_order "mairpc/proto/ec/order"
	pb_setting "mairpc/proto/ec/setting"
	pb_store_warehouse "mairpc/proto/ec/storeWarehouse"
	ec_wallet "mairpc/proto/ec/wallet"
	pb_warehouse "mairpc/proto/ec/warehouse"
	pb_eccampaign_discount_campaign "mairpc/proto/eccampaign/discountCampaign"
	pb_eccampaign_random_discount "mairpc/proto/eccampaign/randomDiscount"
	pb_member "mairpc/proto/member"
	"mairpc/service/ec/client"
	ec_client "mairpc/service/ec/client"
	ec_model "mairpc/service/ec/model"
	card_product "mairpc/service/ec/model/cardProduct"
	"mairpc/service/ec/model/distribution"
	ec_distribution "mairpc/service/ec/model/distribution"
	ec_marketing "mairpc/service/ec/model/marketing"
	ec_model_member "mairpc/service/ec/model/member"
	ec_model_notification "mairpc/service/ec/model/notification"
	"mairpc/service/ec/model/product"
	ec_product "mairpc/service/ec/model/product"
	ec_profit "mairpc/service/ec/model/profitsharing"
	ec_setting "mairpc/service/ec/model/setting"
	setting_model "mairpc/service/ec/model/setting"
	"mairpc/service/ec/model/store"
	ec_store "mairpc/service/ec/model/store"
	ec_store_warehouse "mairpc/service/ec/model/storeWarehouse"
	ec_model_wallet "mairpc/service/ec/model/wallet"
	"mairpc/service/ec/model/warehouse"
	share_service "mairpc/service/ec/service"
	mall_service "mairpc/service/ec/service/mall"
	"mairpc/service/ec/share"
	ec_share "mairpc/service/ec/share"
	"mairpc/service/share/component"
	share_component "mairpc/service/share/component"
	"mairpc/service/share/constant"
	share_model "mairpc/service/share/model"
	"mairpc/service/share/util"

	"github.com/spf13/cast"
	"golang.org/x/net/context"
)

const (
	C_ORDER_REFUND = "ec.orderRefund"

	ORDER_REFUND_STATUS_WAITINGAUDIT     = "waitingAudit"    // 待审核
	ORDER_REFUND_STATUS_REJECTED         = "rejected"        // 拒绝申请
	ORDER_REFUND_STATUS_APPROVED         = "approved"        // 同意申请
	ORDER_REFUND_STATUS_RETURNED         = "returned"        // 买家已退货
	ORDER_REFUND_STATUS_CONFIRM_RETURNED = "confirmReturned" // 确认收到退货
	ORDER_REFUND_STATUS_PENDING          = "pending"         // 等待退款
	ORDER_REFUND_STATUS_REFUNDING        = "refunding"       // 退款中
	ORDER_REFUND_STATUS_REFUNDED         = "refunded"        // 已退款
	ORDER_REFUND_STATUS_CANCELED         = "canceled"        // 已取消
	ORDER_REFUND_STATUS_FAILED           = "failed"          // 退款失败
	ORDER_REFUND_STATUS_RETRY            = "retry"           // 重试退款

	VIRTUAL_ORDER_REFUND_TYPE_ALL    = "all"    // 虚拟商品订单全部退款（包括已使用）
	VIRTUAL_ORDER_REFUND_TYPE_UNUSED = "unused" // 虚拟商品订单仅退未使用状态的

	// 以下退款状态仅用于代码中判断退款结果
	ORDER_REFUND_STATUS_SUCCESS    = "success"    // 退款成功
	ORDER_REFUND_STATUS_PROCESSING = "processing" // 退款处理中

	ORDER_REFUND_TYPE_MEMBER            = "member"          // 客户申请退款
	ORDER_REFUND_TYPE_PORTAL            = "portal"          // 后台主动退款
	ORDER_REFUND_TYPE_ONLY_REFUND       = "onlyRefund"      // 仅退款
	ORDER_REFUND_TYPE_RETURN_AND_REFUND = "returnAndRefund" // 退货退款

	END_ORDER_PROFIT_SHARING_FINISHED = "FINISHED" // 分账完结

	OMS_ORDER_REFUND_PUSH_STATUS_PENDING    = "pending"
	OMS_ORDER_REFUND_PUSH_STATUS_PROCESSING = "processing"
	OMS_ORDER_REFUND_PUSH_STATUS_PUSHED     = "pushed"
	OMS_ORDER_REFUND_PUSH_STATUS_FAILED     = "failed"

	ORDER_REFUND_OPERATE_TYPE_AGREE_TO_RETURN            = "agreeToReturn"           // 后台同意退货
	ORDER_REFUND_OPERATE_TYPE_CONFIRM_RECEIPT_AND_REFUND = "confirmReceiptAndRefund" // 确认收货并退款
	ORDER_REFUND_OPERATE_TYPE_GOODS_RETURNED             = "goodsReturned"           // 用户已退货
	ORDER_REFUND_OPERATE_TYPE_AGREE_TO_REFUND            = "agreeToRefund"           // 同意退款
	ORDER_REFUND_OPERATE_TYPE_REFUND_REFUSED             = "refundRefused"           // 拒绝退款
	ORDER_REFUND_OPERATE_TYPE_UPDATE_APPLICATION         = "updateApplication"       // 用户修改请求
	ORDER_REFUND_OPERATE_TYPE_APPLICATION_CANCELED       = "applicationCanceled"     // 用户撤销申请

	PAYMENT_PROVIDER_MAI   = "mai"   // 退款方：mai
	PAYMENT_PROVIDER_OTHER = "other" // 退款方：第三方自行退款

	REFUND_SUCCESS_CODE = "OPR00000" // OPR00000 表示成功

	MEMBERSHIP_DISCOUNT_INVALID_REASON_ORDER_REFUND = "关联订单退款，此优惠券已失效"

	ORDER_REFUND_TAGS_WAITING_DIVIDE_REVERSE = "waitingDivideReverse" // 等待退款分账回退

	WECHAT_FREIGHT_INSURANCE_EXTRA = "wechatFreightInsurance"
)

func IsInRefundProcess(status string) bool {
	return core_util.StrInArray(status, &[]string{
		ORDER_REFUND_STATUS_PENDING,
		ORDER_REFUND_STATUS_REFUNDING,
		ORDER_REFUND_STATUS_REFUNDED,
		ORDER_REFUND_STATUS_FAILED,
	})
}

var COrderRefund = &OrderRefund{}

type OrderRefund struct {
	Id                       bson.ObjectId                 `bson:"_id"`
	AccountId                bson.ObjectId                 `bson:"accountId"`
	ChannelId                string                        `bson:"channelId"`
	ChannelOrigin            string                        `bson:"channelOrigin"`
	MemberId                 bson.ObjectId                 `bson:"memberId"`
	OrderId                  bson.ObjectId                 `bson:"orderId"`
	StoreId                  bson.ObjectId                 `bson:"storeId,omitempty"`
	DistributorIds           []bson.ObjectId               `bson:"distributorIds,omitemtpy"`
	TransactionId            string                        `bson:"transactionId"`
	Number                   string                        `bson:"number"`
	RefundNumber             string                        `bson:"refundNumber"`
	OrderNumber              string                        `bson:"orderNumber"`
	Contact                  OrderRefundContact            `bson:"contact"`
	Method                   string                        `bson:"method"`   // ec.order.method
	Operator                 string                        `bson:"operator"` // ec.order.operator
	RefundType               string                        `bson:"refundType"`
	OrderTotalFee            uint64                        `bson:"orderTotalFee"`
	ExpressFee               uint64                        `bson:"expressFee"`
	Products                 []RefundProduct               `bson:"products,omitemtpy"`
	RefundAmount             uint64                        `bson:"refundAmount"`
	PayAmount                uint64                        `bson:"payAmount"` // 退款单对应订单的支付金额
	RefundScore              uint64                        `bson:"refundScore"`
	Status                   string                        `bson:"status"`
	OrderStatus              string                        `bson:"orderStatus"` // 发起退款时 ec.order.status
	Type                     string                        `bson:"type"`
	Reason                   string                        `bson:"reason"`
	FailedReason             string                        `bson:"failedReason"`
	Description              string                        `bson:"description"`
	Certificate              Certificate                   `bson:"certificate,omitempty"`
	Histories                []RefundOrderHistory          `bson:"histories"`
	DeliveryStatus           string                        `bson:"deliveryStatus"`
	AuditedAt                time.Time                     `bson:"auditedAt,omitempty"`
	RejectedReason           string                        `bson:"rejectedReason,omitempty"`
	Logistics                LogisticsDetail               `bson:"logistics,omitempty"`
	RefundExtra              Extra                         `bson:"refundExtra"`
	RefundedAt               time.Time                     `bson:"refundedAt,omitempty"`
	OmsProcessor             OmsProcessor                  `bson:"omsProcessor,omitempty"`
	Campaigns                []Campaign                    `bson:"campaigns"`
	CreatedAt                time.Time                     `bson:"createdAt"`
	OrderCreatedAt           time.Time                     `bson:"orderCreatedAt"`
	OrderPaidAt              time.Time                     `bson:"orderPaidAt"`
	UpdatedAt                time.Time                     `bson:"updatedAt"`
	IsDeleted                bool                          `bson:"isDeleted"`
	Tags                     []string                      `bson:"tags,omitempty"`
	PaymentProvider          string                        `bson:"paymentProvider"` // 退款方 mai/未填：mai 微信退款，other：由第三方自行退款
	TradeRecordRefunds       []TradeRecordRefund           `bson:"tradeRecordRefunds,omitempty"`
	InvoiceStatus            string                        `bson:"invoiceStatus"`
	CustomRefundAmountReason string                        `bson:"customRefundAmountReason"`
	RefundAmountChangeReason string                        `bson:"refundAmountChangeReason"`
	OrderOutTradeId          bson.ObjectId                 `bson:"orderOutTradeId,omitempty"` // ec.order.outTradeId
	PrepaidCardRefunds       []PrepaidCardRefund           `bson:"prepaidCardRefunds,omitempty"`
	StoredValue              share_model.StoredValue       `bson:"storedValue,omitempty"`
	RefundStoredValue        share_model.RefundStoredValue `bson:"refundStoredValue,omitempty"`
	Remarks                  string                        `bson:"remarks"`
	Extra                    bson.M                        `bson:"extra,omitempty"`
	store                    ec_store.Store

	originOrder *Order // 退款单对应拆分订单的原始单

	NeedUpdateRefundStoredValue bool `bson:"-"`
}

type Certificate struct {
	Images []Image `bson:"images,omitempty"`
	Video  Video   `bson:"video,omitempty"`
}

type Image struct {
	Url string `bson:"url"`
}

type Video struct {
	Url string `bson:"url"`
}

type PrepaidCardRefund struct {
	Id            bson.ObjectId `bson:"id"`
	PrepaidCardId bson.ObjectId `bson:"prepaidCardId"` // ec.prepaidCard._id
	Number        string        `bson:"number"`        // 礼卡卡号
	Amount        uint64        `bson:"amount"`        // 抵扣金额
	OriginTradeNo string        `bson:"originTradeNo"` // 原始终端流水号
	TradeNo       string        `bson:"tradeNo"`       // 退款流水号
	RefundAmount  uint64        `bson:"refundAmount"`
	RefundNumber  string        `bson:"refundNumber"`
	Status        string        `bson:"status"`
	OutTradeSeq   string        `bson:"outTradeSeq"`   // 第三方原始终端号
	TransactionId string        `bson:"transactionId"` // 第三方原始交易流水号
}

type TradeRecordRefund struct {
	Id            bson.ObjectId `bson:"id"`
	TradeRecordId bson.ObjectId `bson:"tradeRecordId"`
	CreatedAt     time.Time     `bson:"createdAt"`
	RefundedAt    time.Time     `bson:"refundedAt,omitempty"`
	TotalFee      uint64        `bson:"totalFee"`
	RefundAmount  uint64        `bson:"refundAmount"`
	RefundNumber  string        `bson:"refundNumber"`
	Status        string        `bson:"status"`
	CheckSns      []string      `bson:"checkSns,omitempty"` // 收钱吧退款 checkSn
}

type OrderRefundContact struct {
	Name  string `bson:"name"`
	Phone string `bson:"phone"`
}

type RefundOrderHistory struct {
	Status      string      `bson:"status"`
	RefundType  string      `bson:"refundType"`
	Extra       bson.M      `bson:"extra,omitempty"`
	Remarks     string      `bson:"remarks"`
	Operator    string      `bson:"operator"`
	CreatedAt   time.Time   `bson:"createdAt"`
	Description string      `bson:"description"`
	Certificate Certificate `bson:"certificate"`
}

type RefundProduct struct {
	Id                 bson.ObjectId                 `bson:"id"`
	Number             string                        `bson:"number"`
	Name               string                        `bson:"name"`
	Total              uint64                        `bson:"total"`
	Price              uint64                        `bson:"price"`
	OriginPrice        uint64                        `bson:"originPrice"`
	PayAmount          uint64                        `bson:"payAmount"`
	PayScore           uint64                        `bson:"payScore"`
	Spec               RefundProductSpec             `bson:"spec"`
	Picture            string                        `bson:"picture"`
	OutTradeId         bson.ObjectId                 `bson:"outTradeId,omitempty"`        // 用于 OMS 退款单标记唯一 ID
	PackageCampaignId  bson.ObjectId                 `bson:"packageCampaignId,omitempty"` // 用于退款时区分套餐商品和普通商品
	PackageId          bson.ObjectId                 `bson:"packageId,omitempty"`         // 用于退款时区分同类型套餐
	Type               string                        `bson:"type"`
	ProductId          bson.ObjectId                 `bson:"productId"`
	RefundAmount       uint64                        `bson:"refundAmount"`
	IsAmountAdjusted   bool                          `bson:"IsAmountAdjusted"`
	Discounts          []OrderDiscount               `bson:"discounts"`
	PrepaidCardRefunds []ProductPrepaidCardRefund    `bson:"prepaidCardRefunds,omitempty"`
	StoredValue        share_model.StoredValue       `bson:"storedValue,omitempty"`
	RefundStoredValue  share_model.RefundStoredValue `bson:"refundStoredValue,omitempty"`
}

type ProductPrepaidCardRefund struct {
	PrepaidCardId bson.ObjectId `bson:"prepaidCardId"` // ec.prepaidCard._id
	Number        string        `bson:"number"`        // 礼卡卡号
	RefundAmount  uint64        `bson:"refundAmount"`
	Amount        uint64        `bson:"amount"` // 抵扣金额
}

type RefundProductSpec struct {
	Sku         string   `bson:"sku"`
	ExternalSku string   `bson:"externalSku,omitempty"`
	Properties  []string `bson:"properties"`
}

type LogisticsDetail struct {
	DeliveryName string    `bson:"deliveryName"`
	WaybillId    string    `bson:"waybillId"`
	DeliveredAt  time.Time `bson:"deliveredAt"`
}

type Extra struct {
	NeedAudit          bool                   `bson:"needAudit"`
	IsCancelOrder      bool                   `bson:"isCancelOrder"`
	IsAllAquireRefund  bool                   `bson:"isAllAquireRefund"`
	IsAllRefund        bool                   `bson:"IsAllRefund"`
	AssignToFirstStore bool                   `bson:"assignToFirstStore"`
	RefundProductSkus  []*pb_order.ProductSKU `bson:"refundProductSkus"`
	RollbackDiscounts  []string               `bson:"rollbackDiscounts"`
	RefundProducts     []RefundProduct        `bson:"refundProducts"`
}

type QueryRefundFromErpResult struct {
	RefundStatus string
	Remarks      string
}

func (e *Extra) SetIsAllRefund(val bool) {
	e.IsAllRefund = val
}

type OmsProcessor struct {
	PushStatus string    `bson:"pushStatus"`
	PushedAt   time.Time `bson:"pushedAt"`
	Status     string    `bson:"status"`
	RetryTimes int       `bson:"retryTimes"`
	// 以下字段为退款单特有字段
	RefundNo         string               `bson:"refundNo,omitempty"`
	DiscardedRefunds []DiscardedOmsRefund `bson:"discardedRefunds,omitempty"`
	// 是否需要更新退款单号，仅用作旺店通企业版退款类型由仅退款变更为退货退款后在网店通生成新的原始退款单
	// 只用作临时参数传递，不写入 db
	NeedUpdateRefundNo bool `bson:"-"`
}

type DiscardedOmsRefund struct {
	RefundNo   string `bson:"refundNo"`
	PushStatus string `bson:"pushStatus"`
}

func (orderRefund *OrderRefund) Create(ctx context.Context) error {
	orderRefund.CreatedAt = time.Now()
	orderRefund.UpdatedAt = time.Now()
	orderRefund.IsDeleted = false
	_, err := extension.DBRepository.Insert(ctx, C_ORDER_REFUND, *orderRefund)
	if err != nil {
		return err
	}

	HandleOrderRefundStatusChanged(ctx, orderRefund, orderRefund.Status, &pb_order.UpdateOrderRefundRequest{})

	return nil
}

func (o *OrderRefund) UpdateRefundLogistics(ctx context.Context) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       o.Id,
		"isDeleted": false,
	}

	setter := bson.M{
		"logistics": o.Logistics,
		"updatedAt": time.Now(),
	}

	updater := bson.M{
		"$set": setter,
	}

	return extension.DBRepository.UpdateOne(ctx, C_ORDER_REFUND, selector, updater)
}

// UpdateRefundStatus 更新退款记录状态
func (orderRefund *OrderRefund) UpdateRefundStatus(ctx context.Context, updateOrderRefundReq *order.UpdateOrderRefundRequest) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       orderRefund.Id,
		"isDeleted": false,
	}
	setter := bson.M{
		"status":         orderRefund.Status,
		"histories":      orderRefund.Histories,
		"deliveryStatus": orderRefund.DeliveryStatus,
		"refundType":     orderRefund.RefundType,
		"description":    orderRefund.Description,
		"reason":         orderRefund.Reason,
		"failedReason":   orderRefund.FailedReason,
		"refundNumber":   orderRefund.RefundNumber,
		"extra":          orderRefund.Extra,
		"updatedAt":      time.Now(),
	}
	if orderRefund.NeedUpdateRefundStoredValue {
		setter["refundStoredValue"] = orderRefund.RefundStoredValue
	}
	if orderRefund.RefundedAt.Unix() > 0 {
		setter["refundedAt"] = orderRefund.RefundedAt
	}
	if orderRefund.AuditedAt.Unix() > 0 {
		setter["auditedAt"] = orderRefund.AuditedAt
	}
	if updateOrderRefundReq != nil && updateOrderRefundReq.RefundAmount > 0 {
		setter["refundAmountChangeReason"] = orderRefund.RefundAmountChangeReason
		setter["refundAmount"] = orderRefund.RefundAmount
		setter["products"] = orderRefund.Products
		setter["refundStoredValue"] = orderRefund.RefundStoredValue
		setter["prepaidCardRefunds"] = orderRefund.PrepaidCardRefunds
	}

	if orderRefund.Remarks != "" {
		setter["remarks"] = orderRefund.Remarks
	}

	if orderRefund.Certificate.Video.Url != "" || len(orderRefund.Certificate.Images) > 0 {
		setter["certificate"] = orderRefund.Certificate
	}

	if updateOrderRefundReq != nil && updateOrderRefundReq.IsRefundDeliveryFee {
		setter["refundAmount"] = orderRefund.RefundAmount
		setter["expressFee"] = orderRefund.ExpressFee
	}

	needPushToOms := isUpdateOmsStatusToPending(ctx, orderRefund)
	if needPushToOms {
		orderRefund.OmsProcessor.PushStatus = OMS_ORDER_PUSH_STATUS_PENDING
		if orderRefund.OmsProcessor.NeedUpdateRefundNo {
			orderRefund.OmsProcessor.DiscardedRefunds = append(orderRefund.OmsProcessor.DiscardedRefunds, DiscardedOmsRefund{
				PushStatus: OMS_ORDER_PUSH_STATUS_PENDING,
				RefundNo:   orderRefund.GetOmsRefundNo(),
			})
			orderRefund.OmsProcessor.RefundNo = bson.NewObjectId().Hex()
		}
		setter["omsProcessor"] = orderRefund.OmsProcessor
	}

	updater := bson.M{
		"$set": setter,
	}

	err := extension.DBRepository.UpdateOne(ctx, C_ORDER_REFUND, selector, updater)
	if err != nil {
		return err
	}

	// 记录订单商品退款状态
	productSkus := []ProductSkuRefundStatus{}
	for _, product := range orderRefund.Products {
		productSkus = append(productSkus, ProductSkuRefundStatus{
			Status:        orderRefund.Status,
			OutTradeId:    product.OutTradeId,
			OrderRefundId: orderRefund.Id,
		})
	}

	// 更新订单中商品退款状态
	err = COrder.UpdateProductRefundStatus(ctx, orderRefund.OrderId, productSkus)
	if err != nil {
		log.Error(ctx, "Failed to update order refund status", log.Fields{
			"id":           orderRefund.OrderId.Hex(),
			"errorMessage": err.Error(),
		})
	}

	HandleOrderRefundStatusChanged(ctx, orderRefund, orderRefund.Status, updateOrderRefundReq)
	return nil
}

// 调 weconnect/yeepay 触发退款，并更新退款单状态为 refunding
func (orderRefund *OrderRefund) Refund(ctx context.Context) error {
	orderRefund.Status = ORDER_REFUND_STATUS_REFUNDING

	setting, _ := pb_client.GetEcSettingServiceClient().GetSettings(ctx, &pb_setting.GetSettingsRequest{})
	if orderRefund.IsYeepayRefundOrder() {
		orderRefund.refundWithYeepay(ctx, setting.YeepayParentMerchantNo, setting.YeepayMerchantNo)
		orderRefund.tradeRecordRefundWithYeepay(ctx, setting.YeepayParentMerchantNo, setting.YeepayMerchantNo)
		orderRefund.yeepayDivideBackWithMerchantNo(ctx, yeepay.PLATFORM_SERVICE_MERCHANT)
	} else if orderRefund.IsShouqianbaRefundOrder() {
		orderRefund.refundWithShouqianba(ctx, setting)
		orderRefund.tradeRecordRefundWithShouqianba(ctx, setting)
	} else {
		orderRefund.refundWithWechat(ctx)
		orderRefund.tradeRecordRefundWithWechat(ctx)
	}

	storeCode := orderRefund.getStoreCode(ctx)
	// 储值卡退款
	orderRefund.prepaidCardRefund(ctx, storeCode)
	// 储值退款
	orderRefund.storedValueRefund(ctx, storeCode)
	return orderRefund.updateOrderRefund(ctx)
}

func (orderRefund *OrderRefund) getStoreCode(ctx context.Context) string {
	store, err := client.StoreService.GetStore(ctx, &request.DetailRequest{Id: orderRefund.StoreId.Hex()})
	if err != nil {
		log.Error(ctx, "Failed to get store", log.Fields{
			"storeId":      orderRefund.StoreId.Hex(),
			"errorMessage": err.Error(),
		})
	}
	storeCode := ""
	if store != nil {
		storeCode = store.Code
	}
	return storeCode
}

func (orderRefund *OrderRefund) prepaidCardRefund(ctx context.Context, storeCode string) {
	for i, prepaidCardRefund := range orderRefund.PrepaidCardRefunds {
		if prepaidCardRefund.Status == ORDER_REFUND_STATUS_REFUNDED {
			continue
		}
		req := &ec_wallet.PartialRevokeAmountRequest{
			PrepaidCardId: prepaidCardRefund.PrepaidCardId.Hex(),
			Amount:        prepaidCardRefund.RefundAmount,
			MemberId:      orderRefund.MemberId.Hex(),
			OrderId:       orderRefund.OrderId.Hex(),
			TradeNo:       prepaidCardRefund.OriginTradeNo,
			RefundTradeNo: prepaidCardRefund.TradeNo,
			OrderNumber:   orderRefund.OrderNumber,
			SystemDate:    orderRefund.OrderId.Time().Format(time.RFC3339),
			TradeSeq:      prepaidCardRefund.OutTradeSeq,
			SystemSeq:     prepaidCardRefund.TransactionId,
			StoreCode:     storeCode,
			Reason:        "订单退款",
		}
		_, err := client.WalletService.PartialRevokeAmount(ctx, req)
		if err != nil {
			log.Warn(ctx, "Failed to refund prepaid card", log.Fields{
				"errMsg":            err.Error(),
				"prepaidCardRefund": prepaidCardRefund,
				"req":               req,
			})
			orderRefund.PrepaidCardRefunds[i].Status = ORDER_REFUND_STATUS_FAILED
		} else {
			orderRefund.PrepaidCardRefunds[i].Status = ORDER_REFUND_STATUS_REFUNDED
		}
	}
}

func (orderRefund *OrderRefund) storedValueRefund(ctx context.Context, storeCode string) {
	if orderRefund.RefundStoredValue.Amount > 0 && orderRefund.RefundStoredValue.Status != ORDER_REFUND_STATUS_REFUNDED {
		resp, err := ec_client.WalletService.StoredValueRefund(ctx, &ec_wallet.StoredValueRefundRequest{
			MemberId:      orderRefund.MemberId.Hex(),
			TradeNo:       orderRefund.RefundStoredValue.TradeNo,
			TransId:       orderRefund.RefundStoredValue.TransactionId,
			Amount:        orderRefund.RefundStoredValue.Amount,
			RefundOrderId: orderRefund.Id.Hex(),
			OutTradeNo:    orderRefund.StoredValue.TradeNo,
			StoreCode:     storeCode,
		})
		if err != nil {
			log.Warn(ctx, "Failed to refund stored value", log.Fields{
				"errMsg":            err.Error(),
				"refundStoredValue": orderRefund.RefundStoredValue,
			})
			orderRefund.RefundStoredValue.Status = ORDER_REFUND_STATUS_FAILED
		} else {
			orderRefund.RefundStoredValue.Status = ORDER_REFUND_STATUS_REFUNDED
			orderRefund.RefundStoredValue.RefundId = resp.RefundId
		}
	}
}

func (orderRefund *OrderRefund) updateOrderRefund(ctx context.Context) error {
	selector := bson.M{
		"_id":       orderRefund.Id,
		"accountId": orderRefund.AccountId,
	}
	setter := bson.M{
		"histories":    orderRefund.Histories,
		"status":       orderRefund.Status,
		"refundNumber": orderRefund.RefundNumber,
		"updatedAt":    time.Now(),
		"failedReason": orderRefund.FailedReason,
	}
	if len(orderRefund.TradeRecordRefunds) > 0 {
		setter["tradeRecordRefunds"] = orderRefund.TradeRecordRefunds
	}
	if len(orderRefund.PrepaidCardRefunds) > 0 {
		setter["prepaidCardRefunds"] = orderRefund.PrepaidCardRefunds
	}
	if orderRefund.RefundStoredValue.Amount > 0 {
		setter["refundStoredValue"] = orderRefund.RefundStoredValue
	}
	updater := bson.M{
		"$set": setter,
	}
	return Common.UpdateOne(ctx, C_ORDER_REFUND, "false", selector, updater)
}

func (orderRefund *OrderRefund) refundWithWechat(ctx context.Context) error {
	if orderRefund.RefundAmount == 0 {
		return nil
	}

	refundFee := orderRefund.getRefundFee()
	if refundFee <= 0 {
		return nil
	}

	orderRefundRequest := &component.OrderRefundRequest{
		ChannelId:   orderRefund.ChannelId,
		OutTradeNo:  orderRefund.GetOrderOutTradeId(),
		OutRefundNo: orderRefund.Id.Hex(),
		TotalFee:    orderRefund.OrderTotalFee,
		RefundFee:   orderRefund.getRefundFee(),
		RefundDesc:  orderRefund.Reason,
		OpUserId:    orderRefund.getOpUserId(ctx),
	}

	result, err := component.WeConnect.OrderRefund(ctx, orderRefund.getOrigin(), orderRefundRequest)
	if err != nil && !strings.Contains(err.Error(), "Unique valid failed for outRefundNo") {
		return err
	}
	if result != nil {
		orderRefund.RefundNumber = result.RefundNo
	}
	return nil
}

func (orderRefund *OrderRefund) refundWithShouqianba(ctx context.Context, setting *pb_setting.SettingsDetail) error {
	if orderRefund.RefundAmount == 0 {
		return nil
	}

	refundFee := orderRefund.getRefundFee()
	if refundFee <= 0 {
		return nil
	}

	refundReq := orderRefund.genShouqianbaRefundRequest(ctx, setting, cast.ToString(refundFee), false)
	client := shouqianba.NewClient(setting.ShouqianbaConfig.AppId)
	resp, err := client.JiaoYi.Refund(ctx, refundReq)
	if err != nil {
		orderRefund.Status = ORDER_REFUND_STATUS_FAILED
		orderRefund.FailedReason = err.Error()
		return err
	}
	if orderRefund.Extra == nil {
		orderRefund.Extra = bson.M{}
	}
	orderRefund.RefundNumber = resp.OrderSn
	orderRefund.Extra["shouqianbaOrderSn"] = resp.OrderSn
	// 退款可能失败，多次重试退款时需要保证唯一性，为了排查问题方便，将所有的退款单号存下来
	// 对退款单来说，shouqianbaCheckSn 中最后一个 checkSn 为当前退款单最新的 checkSn
	if orderRefund.Extra["shouqianbaCheckSn"] == nil {
		orderRefund.Extra["shouqianbaCheckSn"] = []string{resp.CheckSn}
	} else {
		newShouqianbaCheckSn := []string{}
		checkSns := orderRefund.Extra["shouqianbaCheckSn"].([]interface{})
		for _, checkSn := range checkSns {
			newShouqianbaCheckSn = append(newShouqianbaCheckSn, checkSn.(string))
		}
		newShouqianbaCheckSn = append(newShouqianbaCheckSn, resp.CheckSn)
		orderRefund.Extra["shouqianbaCheckSn"] = newShouqianbaCheckSn
	}
	orderRefund.UpdateExtra(ctx)
	return nil
}

func GetShouqianbaStoreSn(ctx context.Context, order Order, defaultStoreSn string) string {
	if order.IsCouponType() {
		return defaultStoreSn
	}
	if order.Extra != nil && order.Extra["storeSn"] != nil {
		return order.Extra["storeSn"].(string)
	}
	store, _ := store.CStore.GetById(ctx, order.StoreId)
	return store.Code
}

func (orderRefund *OrderRefund) genShouqianbaRefundRequest(ctx context.Context, setting *pb_setting.SettingsDetail, refundFee string, isTradeRecordRefund bool) *shouqianba.RefundRequest {
	order, _ := COrder.GetById(ctx, orderRefund.OrderId)
	refundTenders := []shouqianba.RefundTender{}
	key := "shouqianbaOrderTenders"
	if isTradeRecordRefund {
		key = "shouqianbaOrderDepositTenders"
	}
	if tendersStr, ok := order.Extra[key]; ok {
		tenders := []shouqianba.TenderDetail{}
		json.Unmarshal([]byte(tendersStr.(string)), &tenders)
		for _, tender := range tenders {
			refundTender := shouqianba.RefundTender{
				TransactionSn:    fmt.Sprintf("%s_%s", orderRefund.Id.Hex(), time.Now().Format("150405")),
				Amount:           fmt.Sprintf("-%s", refundFee),
				PayStatus:        "0",
				OriginalTenderSn: tender.TenderSn,
				Operation:        "9",
			}
			refundTenders = append(refundTenders, refundTender)
		}
	}

	storeSn := GetShouqianbaStoreSn(ctx, order, setting.ShouqianbaConfig.StoreSn)
	items := []shouqianba.Item{}
	for _, p := range orderRefund.Products {
		item := shouqianba.Item{
			ItemCode:            p.Number,
			ItemDesc:            p.Name,
			ItemQty:             cast.ToString(p.Total),
			ItemPrice:           cast.ToString(p.Price),
			SalesPrice:          cast.ToString(p.Price),
			Type:                "0",
			ReturnStoreSn:       storeSn,
			ReturnWorkstationSn: "0",
			ReturnCheckSn:       orderRefund.OrderId.Hex(),
		}
		items = append(items, item)
	}

	refundReq := &shouqianba.RefundRequest{
		RequestId:     fmt.Sprintf("%s_%s", orderRefund.Id.Hex(), time.Now().Format("20060102150405")),
		BrandCode:     setting.ShouqianbaConfig.BrandCode,
		StoreSn:       storeSn,
		WorkstationSn: "0",
		CheckSn:       fmt.Sprintf("%s_%s", orderRefund.Id.Hex(), time.Now().Format("150405")), // CheckSn 长度 32，为了避免 CheckSn 重复和超过长度，这里只加小时和秒数
		SalesSn:       orderRefund.Number,
		SalesTime:     orderRefund.CreatedAt.Format("2006-01-02T15:04:05Z"),
		Amount:        fmt.Sprintf("-%s", refundFee),
		Currency:      "156",
		Subject:       "订单退款",
		Operator:      orderRefund.StoreId.Hex(),
		IndustryCode:  "0",
		PosInfo:       orderRefund.Number,
		Items:         items,
		Tenders:       refundTenders,
		Extension1:    "群脉订单",
	}
	return refundReq
}

func (orderRefund *OrderRefund) tradeRecordRefundWithShouqianba(ctx context.Context, setting *pb_setting.SettingsDetail) error {
	for i, t := range orderRefund.TradeRecordRefunds {
		if t.RefundAmount == 0 {
			continue
		}
		refundReq := orderRefund.genShouqianbaRefundRequest(ctx, setting, cast.ToString(t.RefundAmount), true)
		client := shouqianba.NewClient(setting.ShouqianbaConfig.AppId)
		resp, err := client.JiaoYi.Refund(ctx, refundReq)
		if err != nil {
			orderRefund.TradeRecordRefunds[i].Status = ORDER_REFUND_STATUS_FAILED
			continue
		}
		orderRefund.TradeRecordRefunds[i].Status = ORDER_REFUND_STATUS_REFUNDING
		orderRefund.TradeRecordRefunds[i].RefundNumber = resp.OrderSn
		orderRefund.TradeRecordRefunds[i].CheckSns = append(orderRefund.TradeRecordRefunds[i].CheckSns, resp.CheckSn)
	}
	return nil
}

func (orderRefund *OrderRefund) refundWithYeepay(ctx context.Context, parentMerchantNo, merchantNo string) error {
	if orderRefund.RefundAmount == 0 {
		return nil
	}

	refundFee := orderRefund.getRefundFee()
	if refundFee <= 0 {
		return nil
	}

	yeepayClient := yeepay.NewYeepay(yeepay.YEEPAY_APP_KEY, core_util.GetMaimengPrivateKey())
	orderId := orderRefund.GetOrderOutTradeId()
	refundReq := &yeepay.TradeRefundRequest{
		ParentMerchantNo: parentMerchantNo,
		MerchantNo:       merchantNo,
		OrderId:          orderId,
		RefundRequestId:  orderRefund.Id.Hex(),
		RefundAmount:     strconv.FormatFloat(float64(refundFee)/100, 'f', 2, 64),
		Description:      util.RemoveYeepaySpecialChars(orderRefund.Reason),
	}
	resp, err := yeepayClient.Jiaoyi.TradeRefund(ctx, refundReq)
	if err != nil {
		log.Warn(ctx, " PeriodBuy order refund fail", log.Fields{
			"errMsg":    err.Error(),
			"refundId":  orderRefund.Id.Hex(),
			"refundReq": refundReq,
			"resp":      resp,
		})
		return err
	}

	log.Warn(ctx, "Get yeepay refund response for refundWithYeepay", log.Fields{
		"refundId":  orderRefund.Id.Hex(),
		"refundReq": refundReq,
		"resp":      resp,
	})

	if resp.Code != REFUND_SUCCESS_CODE {
		orderRefund.Status = ORDER_REFUND_STATUS_FAILED
		orderRefund.FailedReason = resp.Message
	}
	orderRefund.RefundNumber = resp.UniqueRefundNo
	return nil
}

func (orderRefund *OrderRefund) tradeRecordRefundWithWechat(ctx context.Context) error {
	for i, t := range orderRefund.TradeRecordRefunds {
		if t.RefundAmount == 0 {
			continue
		}
		orderRefundRequest := &component.OrderRefundRequest{
			ChannelId:   orderRefund.ChannelId,
			OutTradeNo:  t.TradeRecordId.Hex(),
			OutRefundNo: t.Id.Hex(),
			TotalFee:    t.TotalFee,
			RefundFee:   t.RefundAmount,
			RefundDesc:  orderRefund.Reason,
			OpUserId:    orderRefund.getOpUserId(ctx),
		}
		result, err := component.WeConnect.OrderRefund(ctx, orderRefund.getOrigin(), orderRefundRequest)
		if err != nil && !strings.Contains(err.Error(), "Unique valid failed for outRefundNo") {
			return err
		}
		if result != nil {
			orderRefund.TradeRecordRefunds[i].RefundNumber = result.RefundNo
		}
		orderRefund.TradeRecordRefunds[i].Status = ORDER_REFUND_STATUS_REFUNDED
	}
	return nil
}

func (orderRefund *OrderRefund) tradeRecordRefundWithYeepay(ctx context.Context, parentMerchantNo, merchantNo string) error {
	for i, t := range orderRefund.TradeRecordRefunds {
		if t.RefundAmount == 0 {
			continue
		}
		yeepayClient := yeepay.NewYeepay(yeepay.YEEPAY_APP_KEY, core_util.GetMaimengPrivateKey())
		refundReq := &yeepay.TradeRefundRequest{
			ParentMerchantNo: parentMerchantNo,
			MerchantNo:       merchantNo,
			OrderId:          t.TradeRecordId.Hex(),
			RefundRequestId:  t.Id.Hex(),
			RefundAmount:     strconv.FormatFloat(float64(t.RefundAmount)/100, 'f', 2, 64),
			Description:      util.RemoveYeepaySpecialChars(orderRefund.Reason),
		}
		resp, err := yeepayClient.Jiaoyi.TradeRefund(ctx, refundReq)
		if err != nil {
			log.Warn(ctx, " PeriodBuy order refund fail", log.Fields{
				"errMsg":    err.Error(),
				"refundId":  orderRefund.Id.Hex(),
				"rderId":    orderRefund.OrderId.Hex(),
				"refundReq": refundReq,
				"resp":      resp,
			})
			return err
		}

		log.Warn(ctx, "Get yeepay refund response for tradeRecordRefundWithYeepay", log.Fields{
			"refundId":  orderRefund.Id.Hex(),
			"orderId":   orderRefund.OrderId.Hex(),
			"refundReq": refundReq,
			"resp":      resp,
		})
		orderRefund.TradeRecordRefunds[i].Status = ORDER_REFUND_STATUS_REFUNDED
		if resp.Code != REFUND_SUCCESS_CODE {
			orderRefund.TradeRecordRefunds[i].Status = ORDER_REFUND_STATUS_FAILED
		}
		orderRefund.TradeRecordRefunds[i].RefundNumber = resp.UniqueRefundNo
	}
	return nil
}

func (orderRefund *OrderRefund) getOpUserId(ctx context.Context) string {
	opUserId := core_util.GetUserId(ctx)
	if bson.IsObjectIdHex(opUserId) {
		opUserId = orderRefund.MemberId.Hex()
	}
	return opUserId
}

func (orderRefund *OrderRefund) getOrigin() string {
	origin := "weapp"
	if orderRefund.ChannelOrigin != "" {
		origin = orderRefund.ChannelOrigin
	}
	return origin
}

func (orderRefund *OrderRefund) getRefundFee() uint64 {
	refundFee := orderRefund.RefundAmount
	for _, t := range orderRefund.TradeRecordRefunds {
		refundFee -= t.RefundAmount
	}
	return refundFee
}

func (orderRefund *OrderRefund) RefundFailed(ctx context.Context, msg string) error {
	orderRefund.Status = ORDER_REFUND_STATUS_FAILED
	orderRefund.FailedReason = msg
	orderRefund.Histories = append(orderRefund.Histories, RefundOrderHistory{
		Status:    ORDER_REFUND_STATUS_FAILED,
		Operator:  "system",
		Remarks:   msg,
		CreatedAt: time.Now(),
	})
	return orderRefund.UpdateRefundStatus(ctx, nil)
}

func (orderRefund *OrderRefund) GenerateHistory(operator, remarks string, extra bson.M) {
	history := RefundOrderHistory{
		Status:     orderRefund.Status,
		RefundType: orderRefund.RefundType,
		Remarks:    remarks,
		Operator:   operator,
		Extra:      extra,
		CreatedAt:  time.Now(),
	}
	orderRefund.Histories = append(orderRefund.Histories, history)
}

func (*OrderRefund) Iterate(ctx context.Context, selector bson.M, sorter []string) (extension.IterWrapper, error) {
	it, err := extension.DBRepository.Iterate(ctx, C_ORDER_REFUND, selector, sorter)
	return it, err
}

func (*OrderRefund) GetAllByCondition(ctx context.Context, condition bson.M) ([]OrderRefund, error) {
	refundOrders := []OrderRefund{}
	err := extension.DBRepository.FindAll(ctx, C_ORDER_REFUND, condition, []string{}, GET_ORDERS_LIMIT, &refundOrders)
	if err != nil {
		return nil, err
	}

	return refundOrders, nil
}

func (*OrderRefund) GetAllByConditionWithSort(ctx context.Context, condition bson.M, sort []string) ([]OrderRefund, error) {
	refundOrders := []OrderRefund{}
	err := extension.DBRepository.FindAll(ctx, C_ORDER_REFUND, condition, sort, GET_ORDERS_LIMIT, &refundOrders)
	if err != nil {
		return nil, err
	}

	return refundOrders, nil
}

func (*OrderRefund) GetByCondition(ctx context.Context, condition bson.M) (*OrderRefund, error) {
	refundOrder := &OrderRefund{}
	err := extension.DBRepository.FindOne(ctx, C_ORDER_REFUND, condition, refundOrder)
	if err != nil {
		return nil, err
	}

	return refundOrder, nil
}

func (self *OrderRefund) GetOriginOrderByOutTradeId(ctx context.Context, orderOutTradeId bson.ObjectId) (*Order, error) {
	if self.originOrder != nil {
		return self.originOrder, nil
	}
	originOrder, err := COrder.GetOriginOrderByOutTradeId(ctx, orderOutTradeId)
	if err != nil {
		return nil, err
	}
	self.originOrder = originOrder
	return originOrder, nil
}

type orderProductStatusStats struct {
	isAllRefunded            bool
	isAllUnrefundedShipped   bool
	isAllUnrefundedUnshipped bool
}

// 退款流程开始后，获取订单上各商品状态的统计信息
// 传入的 orderRefund 是触发此函数的退款单，它一定是在已退款流程的
func (order *Order) statsProductStatus(ctx context.Context, excludedOrderRefund OrderRefund) orderProductStatusStats {
	stats := orderProductStatusStats{
		isAllRefunded:            true,
		isAllUnrefundedShipped:   true,
		isAllUnrefundedUnshipped: true,
	}
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"orderId":   order.Id,
		"status": bson.M{
			"$ne": ORDER_REFUND_STATUS_CANCELED,
		},
	}
	orderRefunds, _ := COrderRefund.GetAllByCondition(ctx, condition)
	orderRefunds = append(orderRefunds, excludedOrderRefund)

OUTER_LOOP:
	for _, p := range order.Products {
		if IsPresent(p) {
			continue
		}
		for _, refund := range orderRefunds {
			for _, rp := range refund.Products {
				if p.OutTradeId != rp.OutTradeId {
					continue
				}
				if IsInRefundProcess(p.RefundStatus) && isAllAmountRefund(rp, p) {
					continue OUTER_LOOP
				}
				if order.IsVirtual() && p.Total == p.RefundTotal && isAllAmountRefund(rp, p) {
					continue OUTER_LOOP
				}
			}
		}

		stats.isAllRefunded = false // 能到这里一定是未全部退款
		if order.IsVirtual() {
			stats.isAllUnrefundedUnshipped = false
			return stats
		}
		shippedProductCount := uint64(0)
		for _, l := range p.Logistics {
			shippedProductCount += l.ProductCount
		}
		if shippedProductCount != p.Total { // 发货数不为商品数量
			stats.isAllUnrefundedShipped = false
		}
		if shippedProductCount != 0 { // 发货数不为 0
			stats.isAllUnrefundedUnshipped = false
		}

		if !stats.isAllRefunded && !stats.isAllUnrefundedShipped && !stats.isAllUnrefundedUnshipped {
			break
		}
	}
	if stats.isAllRefunded { // 所有商品已退款后，不存在 unrefunded 的商品，所以设下面两个状态全为 false
		stats.isAllUnrefundedShipped = false
		stats.isAllUnrefundedUnshipped = false
	}
	return stats
}

func isAllAmountRefund(rp RefundProduct, p OrderProduct) bool {
	if rp.RefundAmount == 0 {
		// 没有值说明是在支持部分金额退款前申请的退款，只能全部金额退款。
		return true
	}
	return rp.RefundAmount == p.PayAmount
}

func CheckProductCampaignExistence(product OrderProduct, campaignType string) bool {
	for _, campaign := range product.Campaigns {
		if campaign.Type == campaignType {
			return true
		}
	}

	return false
}

func (order *Order) UpdateVirtualProductRefundTotal(ctx context.Context, orderRefund *OrderRefund) error {
	if order.IsNotVirtualAndCouponProduct() {
		return nil
	}
	for _, rp := range orderRefund.Products {
		for i, op := range order.Products {
			if op.OutTradeId != rp.OutTradeId {
				continue
			}
			op.RefundTotal = rp.Total
			op.RefundStatus = ORDER_REFUND_STATUS_PENDING
			order.Products[i] = op
		}
	}
	return order.UpdateProducts(ctx)
}

// 触发退款后，按订单上所有商品的状态更新订单状态
func (order *Order) UpdateByProductStatusStats(ctx context.Context, stats orderProductStatusStats, orderRefund OrderRefund) {
	operator := OPERATOR_SYSTEM
	if len(orderRefund.Histories) > 0 {
		operator = orderRefund.Histories[0].Operator
	}
	if stats.isAllRefunded { // 处理订单中商品全部取消时，订单状态变为 canceled
		order.Status = ORDER_STATUS_CANCELED
		remarks := "商品全部退款"
		for _, c := range order.Campaigns {
			if c.Type == CAMPAIGN_TYPE_GROUPON {
				remarks = "拼团失败，商品全部退款"
			}
		}
		order.Histories = append(order.Histories, OrderHistory{
			StoreId:   order.StoreId,
			Status:    order.Status,
			Remarks:   remarks,
			Operator:  operator,
			CreatedAt: time.Now(),
		})
		order.Cancel(ctx)
		return
	}

	// 虚拟商品订单每个商品只能退一次款，只能选择退款所有该商品或者退款未核销的
	// 因此如果所有商品均已退款且不是全部退款则标识订单已完成
	if order.IsVirtualType() || order.IsCouponType() {
		// 是否所有商品项都已退款
		isAllProductRefunded := true
		for _, op := range order.Products {
			var shippedCount uint64 = 0
			for _, lg := range op.Logistics {
				shippedCount += lg.ProductCount
			}
			// 全部已核销的商品项不影响判断未核销商品全部退款，因此忽略
			if shippedCount == op.Total {
				continue
			}
			// 未全部核销的商品项一定有退款商品才可能是已完成的订单
			if op.RefundTotal == 0 {
				isAllProductRefunded = false
				break
			}
		}
		if isAllProductRefunded {
			order.Status = ORDER_STATUS_COMPLETED
			order.Histories = append(order.Histories, OrderHistory{
				StoreId:   order.StoreId,
				Status:    order.Status,
				Remarks:   "",
				Operator:  operator,
				CreatedAt: time.Now(),
			})
			if order.CompletedAt.IsZero() {
				order.Complete(ctx)
			}
			return
		}
	}
	if order.Status != ORDER_STATUS_PARTIAL_SHIPPED {
		return
	}

	if stats.isAllUnrefundedShipped {
		order.Status = ORDER_STATUS_SHIPPED
		order.Histories = append(order.Histories, OrderHistory{
			StoreId:   order.StoreId,
			Status:    order.Status,
			Remarks:   "未退款商品全部已发货",
			Operator:  operator,
			CreatedAt: time.Now(),
		})
		order.Logistics.ShippedAt = time.Now()
		order.ShippedOffline(ctx, []string{})
		return
	}

	if stats.isAllUnrefundedUnshipped {
		order.Status = ORDER_STATUS_ACCEPTED
		order.Histories = append(order.Histories, OrderHistory{
			StoreId:   order.StoreId,
			Status:    order.Status,
			Remarks:   "发货商品全部已退款",
			Operator:  operator,
			CreatedAt: time.Now(),
		})
		order.UpdateStatus(ctx)
		return
	}
}

func (order *Order) ReCalcPayAmount(ctx context.Context, orderRefund *OrderRefund) uint64 {
	refundUniqueIds := []string{}
	for _, p := range orderRefund.Products {
		refundUniqueIds = append(refundUniqueIds, p.OutTradeId.Hex())
	}
	newPayAmount := uint64(0)
	for _, p := range order.Products {
		if IsInRefundProcess(p.RefundStatus) ||
			util.StrInArray(p.OutTradeId.Hex(), &refundUniqueIds) {
			continue
		}
		newPayAmount += p.PayAmount
	}
	return newPayAmount
}

func (order *Order) GetProductRefundAmountMap(ctx context.Context) map[string]uint64 {
	productRefundAmountMap := make(map[string]uint64)
	condition := Common.GenDefaultCondition(ctx)
	condition["orderId"] = order.Id
	condition["status"] = bson.M{
		"$nin": []string{
			ORDER_REFUND_STATUS_CANCELED,
			ORDER_REFUND_STATUS_REJECTED,
		},
	}
	refunds, _ := COrderRefund.GetAllByCondition(ctx, condition)
	for _, refund := range refunds {
		for _, product := range refund.Products {
			productRefundAmountMap[product.OutTradeId.Hex()] += product.RefundAmount
		}
	}
	return productRefundAmountMap
}

func (order *Order) ReCalcDistributionAmount(ctx context.Context) uint64 {
	if !util.StrInArray(PROFIT_TYPE_DISTRIBUTION, &order.ProfitTypes) {
		return 0
	}
	var (
		distributionAmount     uint64
		productRefundAmountMap = order.GetProductRefundAmountMap(ctx)
		// 导购分销按上月销售额划定分账比例的情况下，ec.order.products 里不会保存佣金相关的字段，需要用此字段重新计算
		proportion = order.Distribution.LastMonthProportion
		payAmount  = int64(order.GetRealPayAmount())
	)
	if order.Method == ORDER_DELIVERY_METHOD_EXPRESS {
		payAmount -= int64(order.Logistics.Fee)
	}
	for _, refundAmount := range productRefundAmountMap {
		payAmount -= int64(refundAmount)
	}
	for _, p := range order.Products {
		if p.IsDistribution {
			if util.StrInArray(p.RefundStatus, &[]string{
				"",
				ORDER_REFUND_STATUS_REJECTED,
				ORDER_REFUND_STATUS_CANCELED,
			}) {
				distributionAmount += p.DistributionAmount
				continue
			}
			// 协商部分退款的情况下，order.product.refundStatus 不为空，用实付金额减去退款金额重算分账金额
			refundAmount, ok := productRefundAmountMap[p.OutTradeId.Hex()]
			if ok && refundAmount < p.PayAmount {
				if p.DistributionProportion > 0 {
					// 销售额比例分佣
					distributionAmount += cast.ToUint64(util.MultiplyFloat(util.MultiplyFloat(p.DistributionProportion, float64(p.PayAmount-refundAmount)), 0.01))
				} else {
					// 固定金额分佣
					distributionAmount += p.DistributionAmount
				}
			}
		}
	}
	if order.Distribution.PromoterType == distribution.PROMOTER_TYPE_STAFF && distributionAmount == 0 && proportion > 0 && payAmount > 0 {
		distributionAmount = uint64(util.MultiplyFloat(util.MultiplyFloat(float64(payAmount), proportion), 0.01))
	}
	return distributionAmount
}

// HandleOrderRefundStatusChanged 处理退款单状态变更
// ORDER_REFUND_STATUS_APPROVED: 同意退款/退货退款
// ORDER_REFUND_STATUS_PENDING: 发起退款
// ORDER_REFUND_STATUS_REFUNDED: 退款完成
// ORDER_REFUND_STATUS_FAILED: 退款失败
func HandleOrderRefundStatusChanged(ctx context.Context, orderRefund *OrderRefund, toStatus string, updateOrderRefundReq *order.UpdateOrderRefundRequest) {
	order, err := COrder.GetById(ctx, orderRefund.OrderId)
	if err != nil {
		log.Error(ctx, "Failed to get order by id", log.Fields{
			"id":           orderRefund.OrderId.Hex(),
			"errorMessage": err.Error(),
		})
		return
	}
	// 发送售后消息
	basicPlaceholderValueMap := GenBasicPlaceholderValueMap(orderRefund, updateOrderRefundReq, nil)
	sendOrderRefundMsg(ctx, &order, orderRefund, toStatus, updateOrderRefundReq, basicPlaceholderValueMap)
	for _, campaign := range order.Campaigns {
		if campaign.Type == CAMPAIGN_TYPE_GROUPON {
			handleGrouponCampaignWhenCanceled(ctx, campaign, order, toStatus)
		}
	}

	// 更新 WDT 订单状态
	if order.IsNeedSyncToOms() && ec_setting.CSettings.IsNeedSyncToOms(ctx) {
		order.UpdateOmsPushStatusToPending(ctx)
	}

	switch toStatus {
	case ORDER_REFUND_STATUS_PENDING:
		// 回退产品卡
		HandleProductCardRollback(ctx, &order, orderRefund)
		// 回退兑换卡
		HandleMemberRedeemCardRollback(ctx, &order, orderRefund)
		// 回退赠品库存
		HandlePresentCampaignRefund(ctx, &order, orderRefund, false)
		HandlePurchaseLimitedProductRefund(ctx, &order, orderRefund, false)
		HandleGrouponCampaignRefund(ctx, &order)
		order.UpdateVirtualProductRefundTotal(ctx, orderRefund)
		stats := order.statsProductStatus(ctx, *orderRefund)
		// 由于 UpdateByProductStatusStats 会改变订单的状态，需要把退款前订单的状态保存下来，用于发送通知的条件判断
		orderPreStatus := order.Status
		order.UpdateByProductStatusStats(ctx, stats, *orderRefund)
		// 返还优惠券
		rollbackRedeemCoupons(ctx, order, orderRefund, stats)
		// 回退限时折扣库存
		RollbackDiscountCampaignProductStock(ctx, &order, orderRefund, false)
		// 回退随机立减
		RollbackRandomDiscountRecord(ctx, &order, orderRefund, false)

		// 返还积分
		scoreToReturn := uint64(0)
		for _, p := range orderRefund.Products {
			scoreToReturn += p.PayScore
		}
		if scoreToReturn > 0 {
			extraInfo := map[string]string{"orderRefundId": orderRefund.Id.Hex(), "orderId": orderRefund.OrderId.Hex()}
			ReturnScore(ctx, orderRefund.MemberId.Hex(), scoreToReturn, extraInfo)
		}

		// 更新客户对应属性
		orderRefund.RefundExtra.SetIsAllRefund(stats.isAllRefunded)
		orderRefund.UpdateRefundExtra(ctx)
		UpdateMemberEcProperties(ctx, &order, orderRefund)

		// 发送通知
		if stats.isAllRefunded { // 取消订单
			if orderPreStatus == ORDER_STATUS_ACCEPTED { // 待提货状态下的订单发生退款
				if order.Method == ORDER_DELIVERY_METHOD_PICKUP {
					order.NotifyStaffs(ctx, ORDER_STATUS_CANCELED)
				}
				if order.Method == ORDER_DELIVERY_METHOD_EXPRESS {
					order.NotifyStaffs(ctx, constant.MESSAGE_RULE_UNSHIPPED_ALL_REFUNDED)
				}
				order.CreateNotification(ctx, ORDER_STATUS_CANCELED)
			}
			// 回退自提口令
			order.ReturnPickupPassword(ctx)
		} else { // 部分退款
			order.NotifyCustomer(ctx, ORDER_STATUS_PARTIAL_REFUNDED, basicPlaceholderValueMap)

			// 待提货状态下
			// 总部发货不通知店长端
			if orderPreStatus == ORDER_STATUS_ACCEPTED &&
				order.Operator != ec_setting.DELIVERY_SETTING_OPERATOR_USER {
				if order.Method == ORDER_DELIVERY_METHOD_PICKUP {
					order.NotifyStaffs(ctx, ORDER_STATUS_PARTIAL_REFUNDED)
				}
				if order.Method == ORDER_DELIVERY_METHOD_EXPRESS {
					order.NotifyStaffs(ctx, constant.MESSAGE_RULE_UNSHIPPED_PARTIAL_REFUNDED)
				}
				order.CreateNotification(ctx, ORDER_STATUS_PARTIAL_REFUNDED)
			}
		}

		// 分账相关处理
		// 此订单为分账单 && 非使用易宝交易订单（脉盟订单 && 连锁零售商城订单）
		if order.IsTransferOrder() && !order.IsYeepayForD2R() && !order.IsYeepay() {
			// 只有所有分销商品都发起退款的情况下才完成分账
			if order.IsAllDistributionProductsRefunding(ctx) {
				err := order.FinishProfitSharing(ctx)
				if err != nil {
					log.Warn(ctx, "Failed to finish profit Sharing", log.Fields{
						"orderId": order.Id.Hex(),
					})
				}
			}

			newDistributionAmount := order.ReCalcDistributionAmount(ctx)
			// 更新预分账单中要分的钱
			if !COrderReceiverProfit.IsAllProcessed(ctx, order.Id) {
				for _, profitType := range util.StrArrayUnique(order.ProfitTypes) {
					switch profitType {
					case PROFIT_TYPE_PROFITSHARING:
						// 将小比例分账的预分账单改为 noCommission
						COrderReceiverProfit.UpdateUnProcessSmallScaleProfitsToNoCommission(ctx, order.Id)
						// 重新计算大比例分账的预分账单的金额
						COrderReceiverProfit.ReCalcProfitAmount(ctx, order.Id, order.ReCalcPayAmount(ctx, orderRefund), stats)
					case PROFIT_TYPE_COMMISSION:
						// 重新计算分的预分账单的金额
						err := COrderReceiverProfit.ReCalcProfitCommissionAmount(ctx, order.Id, stats)
						if err != nil {
							log.Warn(ctx, "Failed to update profits to noCommission", log.Fields{
								"errorMessage": err.Error(),
							})
						}
					case PROFIT_TYPE_DISTRIBUTION:
						order.UpdateDistributionAmount(ctx, newDistributionAmount)
						COrderReceiverProfit.UpdateDistributionAmount(ctx, newDistributionAmount, order.Id)
					}
				}
			}

			if core_util.StrInArray(PROFIT_TYPE_DISTRIBUTION, &order.ProfitTypes) {
				// 需要分账的实物订单在订单完成前发起退款（此时还未创建预分账单）
				// 虚拟商品订单支付后就算完成，发起退款需要更新分销订单的分销金额
				if !order.IsVirtual() && !order.HasBeenCompleted() || order.IsVirtual() {
					if newDistributionAmount > 0 {
						order.UpdateDistributionAmount(ctx, newDistributionAmount)
					} else {
						order.UpdateDistributionStatus(ctx, order.Id.Hex(), DISTRIBUTION_STATUS_NOCOMMISSION, "", map[string]string{"无佣金原因": "订单完成前发起退款"}, time.Time{})
					}
				}
				// 退款时大众分销部分的预分账单还没有进行分账才可以减扣大众分销员的累计收益和待结算收益以及客户的累计贡献佣金
				if !COrderReceiverProfit.IsDistOrderProcessed(ctx, order.Id) {
					err = HandDistributionOrder(ctx, &order, int64(order.Distribution.Amount)-int64(newDistributionAmount), newDistributionAmount == 0)
					if err != nil {
						log.Error(ctx, "Failed to handle distribution order after order refund.", log.Fields{
							"orderId":      order.Id,
							"errorMessage": err.Error(),
						})
					}
				}
			}
		}
		// 回退库存
		order.ReturnProductStockNotDelivered(ctx, orderRefund)
		// 储值卡退款
		HandleCardOrderRefund(ctx, &order, orderRefund)
	case ORDER_REFUND_STATUS_REFUNDED:
		handleMemberPaidCardRecordRollback(ctx, order, orderRefund)
		haveConfirmReturnedAndRefundStatus := false // 是否有确认收到退货并发起退款，如果有这个 confirmReturned 状态，则已经回退过库存，不用再回退库存。否则可能是后台直接退款，需要回退库存
		for _, history := range orderRefund.Histories {
			if history.Status == ORDER_REFUND_STATUS_CONFIRM_RETURNED {
				haveConfirmReturnedAndRefundStatus = true
			}
		}
		if !haveConfirmReturnedAndRefundStatus {
			ReturnStockWhenConfirmReceipt(ctx, *orderRefund)
		}
	case ORDER_REFUND_STATUS_CANCELED:
		orderRefund.SendCancelRefundEvent(ctx)
		if order.Status == ORDER_STATUS_SHIPPED {
			order.NotifyStaffs(ctx, constant.MESSAGE_RULE_SHIPPED_APPLY_REFUND_CANCEL)
		}
		if order.Status == ORDER_STATUS_ACCEPTED && order.Method == ORDER_DELIVERY_METHOD_EXPRESS {
			order.NotifyStaffs(ctx, constant.MESSAGE_RULE_UNSHIPPED_APPLY_REFUND_CANCEL)
		}
	}
}

// 确认收到退货时，回退库存,生成入库单。
func ReturnStockWhenConfirmReceipt(ctx context.Context, orderRefund OrderRefund) {
	order, err := COrder.GetById(ctx, orderRefund.OrderId)
	if err != nil {
		log.Warn(ctx, "Get refunded order failed when return confirmReturned product stock.", log.Fields{
			"orderId":       orderRefund.OrderId,
			"orderRefundId": orderRefund.Id,
			"errorMessage":  err.Error(),
		})
		return
	}
	// 卡券和虚拟商品不能回退库存
	if order.IsCouponType() || order.IsVirtualType() {
		return
	}

	// 如果需要处理库存，就一定会给 stock 赋值，否则会 return
	var stock ec_product.StockOperator
	if !order.IsRetailerConsignmentOrder(ctx) {
		// 到这里的都是实物订单，根据配送方式看看是否需要校验库存，不需要的 return。
		switch order.Operator {
		case setting_model.DELIVERY_SETTING_OPERATOR_STAFF:
			if !ec_model.IsStoreProductStockEnabled(ctx) {
				return
			}
			stock = ec_store_warehouse.CStoreProductStock
		case setting_model.DELIVERY_SETTING_OPERATOR_USER:
			if !ec_model.IsProductStockEnabled(ctx, nil) {
				return
			}
			stock = ec_product.CProduct
		}
	} else {
		store, err := ec_client.StoreService.GetStore(ctx, &request.DetailRequest{Id: order.StoreId.Hex()})
		if err != nil {
			log.Warn(ctx, "Failed to get order store", log.Fields{
				"orderId": order.Id.Hex(),
				"storeId": order.StoreId.Hex(),
				"err":     err.Error(),
			})
		}
		distributorAccountId := order.GetDistributorAccountId(store)
		if !share.IsDistributorConsignmentStockEnable(ctx, distributorAccountId) {
			return
		}
		stock = component.InitRetailerConsignmentProduct(distributorAccountId)
	}
	// 走到这里就说明需要处理库存，不管是代销还是经销（包含门店发货或总店发货）
	switch order.Operator {
	case setting_model.DELIVERY_SETTING_OPERATOR_STAFF:
		haveShipped := false // 发货过，才会生成退货入库的记录
		for _, history := range order.Histories {
			if history.Status == ORDER_STATUS_SHIPPED || history.Status == ORDER_STATUS_COMPLETED {
				haveShipped = true
				break
			}
		}
		if haveShipped {
			req := GenCreateStoreStockBillReq(ctx, orderRefund, order)
			client.StoreWarehouseService.CreateStoreStockBill(ctx, req)
		}
	case setting_model.DELIVERY_SETTING_OPERATOR_USER:
		req := GenCreateStockBillReq(ctx, orderRefund, order)
		if consignmentProduct, ok := stock.(*component.MaiRetailerConsignmentProduct); ok {
			IncStockAndCreateStockBillForConsignmentRefund(ctx, consignmentProduct, req)
		} else {
			client.WarehouseService.CreateStockBill(ctx, req)
		}
	}
}

func GenCreateStockBillReq(ctx context.Context, orderRefund OrderRefund, order Order) *pb_warehouse.CreateStockBillRequest {
	req := &pb_warehouse.CreateStockBillRequest{
		ChangedAt:      time.Now().Format(time.RFC3339),
		Operator:       warehouse.OPERATOR_STOCK_BILL_REFUND,
		BusinessNumber: orderRefund.OrderNumber,
		Type:           warehouse.TYPE_STOCK_BILL_IN,
	}

	refundProductMap := map[string]RefundProduct{}
	for _, refundProduct := range orderRefund.Products {
		refundProductMap[refundProduct.Spec.Sku] = refundProduct
	}

	products := []*pb_warehouse.StockBillProduct{}
	for _, orderProduct := range order.Products {
		refundProduct, ok := refundProductMap[orderProduct.Spec.Sku]
		if !ok {
			continue
		}
		if len(orderProduct.Logistics) == 0 {
			continue
		}

		count := uint64(0)
		// 只处理已经发货的商品库存
		for _, logistic := range orderProduct.Logistics {
			count += logistic.ProductCount
		}
		temp := &pb_warehouse.StockBillProduct{
			Id:    refundProduct.Id.Hex(),
			Sku:   refundProduct.Spec.Sku,
			Count: count,
		}
		products = append(products, temp)
	}

	req.Products = products
	return req
}

func GenCreateStoreStockBillReq(ctx context.Context, orderRefund OrderRefund, order Order) *pb_store_warehouse.CreateStockBillRequest {
	req := &pb_store_warehouse.CreateStockBillRequest{
		ChangedAt:      time.Now().Format(time.RFC3339),
		Operator:       ec_store_warehouse.OPERATOR_STOCK_BILL_REFUND,
		BusinessNumber: orderRefund.OrderNumber,
		Type:           ec_store_warehouse.TYPE_STOCK_BILL_IN,
	}

	// 添加门店信息
	store, _ := ec_client.StoreService.GetStore(ctx, &request.DetailRequest{Id: order.StoreId.Hex()})
	stockStore := &pb_store_warehouse.UpdateStoreStock{
		Id:   order.StoreId.Hex(),
		Code: store.Code,
		Name: store.Name,
	}
	stores := []*pb_store_warehouse.UpdateStoreStock{}
	req.Stores = append(stores, stockStore)

	// 添加商品信息
	refundProductMap := map[string]RefundProduct{}
	for _, refundProduct := range orderRefund.Products {
		refundProductMap[refundProduct.Spec.Sku] = refundProduct
	}

	products := []*pb_store_warehouse.StockBillProduct{}
	for _, orderProduct := range order.Products {
		refundProduct, ok := refundProductMap[orderProduct.Spec.Sku]
		if !ok {
			continue
		}

		count := uint64(0)
		if order.Method != ORDER_DELIVERY_METHOD_PICKUP {
			// 物流信息为空说明没发货，跳过
			if len(orderProduct.Logistics) == 0 {
				continue
			}
			// 需要支持部分发货，只处理已经发货的商品库存
			for _, logistic := range orderProduct.Logistics {
				count += logistic.ProductCount
			}
		} else {
			// 门店自提没有部分发货的情况，所以直接用商品数量
			count = orderProduct.Total
		}

		temp := &pb_store_warehouse.StockBillProduct{
			Id:      refundProduct.Id.Hex(),
			Sku:     refundProduct.Spec.Sku,
			Count:   count,
			StoreId: order.StoreId.Hex(),
		}
		products = append(products, temp)
	}

	req.Products = products
	return req
}

func IncStockAndCreateStockBillForConsignmentRefund(ctx context.Context, consignmentProduct *component.MaiRetailerConsignmentProduct, req *pb_warehouse.CreateStockBillRequest) {
	consignmentStockBills := []component.ConsignmentStockBill{}
	for _, product := range req.Products {
		err := consignmentProduct.IncStock(ctx, product.Sku, int(product.Count))
		if err != nil {
			// 修改代销商品 sku 库存失败，该商品出入库失败
			log.Warn(ctx, "Failed to inc consignment product stock", log.Fields{
				"product":            product,
				"consignmentProduct": consignmentProduct,
				"error":              err.Error(),
			})
			continue
		}
		oldAvailableStock := uint64(0)
		if consignmentProduct.StockDetail != nil {
			availableStock := consignmentProduct.StockDetail.AvailableStock
			if availableStock >= 0 && uint64(availableStock) > product.Count {
				oldAvailableStock = uint64(availableStock) - product.Count
			}
		}
		consignmentProductId, consignmentProductSku := component.GetConsignmentProductIdAndSku(product.Sku)
		tempStockBill := component.ConsignmentStockBill{
			AccountId:            consignmentProduct.AccountId,
			ChangedAt:            time.Now().Format(time.RFC3339),
			Type:                 warehouse.TYPE_STOCK_BILL_IN,
			BusinessNumber:       req.BusinessNumber,
			ConsignmentProductId: consignmentProductId,
			Operator:             warehouse.OPERATOR_STOCK_BILL_REFUND,
			Sku:                  consignmentProductSku,
			Count:                product.Count,
			AvailableStock:       oldAvailableStock,
		}
		consignmentStockBills = append(consignmentStockBills, tempStockBill)
	}
	if len(consignmentStockBills) == 0 {
		return
	}
	distributorCtx := core_util.DuplicateContextWithAid(ctx, consignmentProduct.AccountId)
	_, err := component.Retailer.CreateConsignmentStockBill(distributorCtx, &component.CreateConsignmentStockBillRequest{Bills: consignmentStockBills})
	if err != nil {
		log.Warn(ctx, "Failed to create stock bills after inc consignment product stock", log.Fields{
			"consignmentStockBills": consignmentStockBills,
			"consignmentProduct":    consignmentProduct,
			"err":                   err.Error(),
		})
	}
}

func handleMemberPaidCardRecordRollback(ctx context.Context, order Order, orderRefund *OrderRefund) {
	var (
		rollbackAmount uint64
		recordId       bson.ObjectId
	)
	for _, op := range orderRefund.Products {
		for _, p := range order.Products {
			if p.OutTradeId != op.OutTradeId {
				continue
			}
			for _, d := range p.Discounts {
				if d.Type == ORDER_DISCOUNT_TYPE_BENEFIT_CARD {
					rollbackAmount += d.SuitTotalAmount
					recordId = d.Id
				}
			}
		}
	}
	if rollbackAmount == 0 {
		return
	}
	ec_model.UpdateMemberPaidCardDiscountLimit(ctx, "rollback", recordId, order.Id, rollbackAmount)
}

func rollbackRedeemCoupons(ctx context.Context, order Order, orderRefund *OrderRefund, stats orderProductStatusStats) {
	getNeedRollbackDiscounts(ctx, order, orderRefund, stats)
	orderId := orderRefund.OrderId.Hex()
	if order.IsSplitSubOrder() &&
		len(orderRefund.RefundExtra.RollbackDiscounts) > 0 &&
		// M88 上线后，拆单完成后会更新 couponLog.orderId 了，所以不需要再去找原单了
		order.CreatedAt.Before(time.Date(2024, 4, 1, 7, 0, 0, 0, time.Local)) {
		originOrder, err := orderRefund.GetOriginOrderByOutTradeId(ctx, order.OutTradeId)
		if err != nil {
			log.Error(ctx, "Failed to rollback discounts", log.Fields{
				"discounts":    orderRefund.RefundExtra.RollbackDiscounts,
				"errorMessage": err.Error(),
			})
			return
		}
		orderId = originOrder.Id.Hex()
	}
	for _, discountId := range orderRefund.RefundExtra.RollbackDiscounts {
		err := share_service.RollbackRedeemCoupon(ctx, discountId, orderId)
		if err != nil {
			log.Error(ctx, "Failed to rollback discounts", log.Fields{
				"discounts":    orderRefund.RefundExtra.RollbackDiscounts,
				"errorMessage": err.Error(),
			})
		}
	}
}

func getNeedRollbackDiscounts(ctx context.Context, order Order, orderRefund *OrderRefund, stats orderProductStatusStats) {
	var (
		refundingDiscounts         []string // 退款的商品使用的优惠券
		refundingPresentDiscounts  []string // 退款商品使用的赠品券
		refundingExchangeDiscounts []string // 退款商品使用的兑换券

		usingDiscounts []string
	)

	for _, p := range orderRefund.Products {
		if p.RefundAmount != p.PayAmount {
			continue
		}
		for _, d := range p.Discounts {
			if d.Type == ORDER_DISCOUNT_TYPE_COUPON {
				refundingDiscounts = append(refundingDiscounts, d.Id.Hex())
			}
			if d.Type == ORDER_DISCOUNT_TYPE_PRESENT {
				refundingPresentDiscounts = append(refundingPresentDiscounts, d.Id.Hex())
			}
			// 已完成的订单不退兑换券
			if d.Type == ORDER_DISCOUNT_TYPE_EXCHANGE && !order.HasBeenCompleted() {
				refundingExchangeDiscounts = append(refundingExchangeDiscounts, d.Id.Hex())
			}
		}
	}
	if stats.isAllRefunded || len(orderRefund.Products) == 0 {
		// 全部退款/只退运费，回退运费券
		for _, d := range order.Discounts {
			if d.Type != COUPON_TYPE_DELIVERY {
				continue
			}
			refundingDiscounts = append(refundingDiscounts, d.Id.Hex())
		}
	}
	if len(refundingDiscounts)+len(refundingPresentDiscounts)+len(refundingExchangeDiscounts) == 0 {
		return
	}

	refundingDiscounts = util.StrArrayUnique(refundingDiscounts)
	refundingPresentDiscounts = util.StrArrayUnique(refundingPresentDiscounts)
	refundingExchangeDiscounts = util.StrArrayUnique(refundingExchangeDiscounts)
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"orderId":   order.Id,
		"status": bson.M{
			"$ne": ORDER_REFUND_STATUS_CANCELED,
		},
	}
	orderRefunds, _ := COrderRefund.GetAllByCondition(ctx, condition)
	orderRefunds = append(orderRefunds, *orderRefund)
	for _, p := range order.Products {
		refundProduct := RefundProduct{}
		for i := range orderRefunds {
			for j, rp := range orderRefunds[i].Products {
				if p.OutTradeId == rp.OutTradeId {
					refundProduct = orderRefunds[i].Products[j]
				}
			}
		}

		if core_util.StrInArray(p.RefundStatus, &[]string{"", ORDER_REFUND_STATUS_CANCELED, ORDER_REFUND_STATUS_WAITINGAUDIT, ORDER_REFUND_STATUS_REJECTED, ORDER_REFUND_STATUS_APPROVED}) ||
			(refundProduct.RefundAmount != refundProduct.PayAmount) {
			// 退款
			for _, d := range p.Discounts {
				if d.Type == ORDER_DISCOUNT_TYPE_COUPON {
					usingDiscounts = append(usingDiscounts, d.Id.Hex())
				}
			}
		}
	}

	shouldRollbackDiscounts := util.StrArrayDiff(refundingDiscounts, usingDiscounts)
	shouldRollbackPresentDiscounts := handlePresentDiscount(ctx, refundingPresentDiscounts, &order)
	shouldRollbackDiscounts = append(shouldRollbackDiscounts, shouldRollbackPresentDiscounts...)
	shouldRollbackDiscounts = append(shouldRollbackDiscounts, refundingExchangeDiscounts...)
	orderRefund.RefundExtra.RollbackDiscounts = shouldRollbackDiscounts
}

func handlePresentDiscount(ctx context.Context, refundingDiscounts []string, order *Order) (shouldRollbackPresentDiscount []string) {
	presentDiscountIds := []string{}
	for _, discount := range order.Discounts {
		if discount.Type != ORDER_DISCOUNT_TYPE_PRESENT {
			continue
		}
		presentDiscountIds = append(presentDiscountIds, discount.Id.Hex())
	}
OUTER:
	for _, presentDiscountId := range presentDiscountIds {
		if !util.StrInArray(presentDiscountId, &refundingDiscounts) {
			return
		}
		// 赠品如果是虚拟商品且已核销则买赠券不退还
		// 赠品如果是实物商品且已发货则买赠券不退还
		for _, product := range order.Products {
			for _, campaign := range product.Campaigns {
				if campaign.Type != CAMPAIGN_TYPE_PRESENT_COUPON || campaign.DiscountId.Hex() != presentDiscountId {
					continue
				}
				switch product.Type {
				case VIRTUAL_ORDER_TYPE_VIRTUAL:
					for _, item := range product.PickupCodeRedeemPeriods {
						if item.Status == PICKUP_CODE_STATUS_USED {
							continue OUTER
						}
					}
				case VIRTUAL_ORDER_TYPE_PRODUCT:
					if len(product.Logistics) > 0 {
						continue OUTER
					}
				}
			}
		}
		// 有使用了买赠券的商品退款，检查剩余商品是否仍满足买赠券条件
		var (
			totalAmount          uint64
			totalPayAmount       uint64
			needRollback         bool
			hasApplicableProduct bool //是否还有适用商品
		)
		for _, product := range order.Products {
			if product.RefundStatus != "" {
				continue
			}
			for _, discount := range product.Discounts {
				if discount.Id.Hex() != presentDiscountId {
					continue
				}
				hasApplicableProduct = true
				totalAmount += product.Price * product.Total
				totalPayAmount += product.PayAmount
			}
		}
		if !hasApplicableProduct {
			needRollback = true
		} else {
			presentDiscount, callErr := proto_client.GetCouponServiceClient().GetMembershipDiscount(ctx, &coupon.GetMembershipDiscountRequest{
				Id: presentDiscountId,
			})
			if callErr != nil {
				return
			}
			if presentDiscount.Coupon.DiscountCondition != nil {
				if presentDiscount.Coupon.DiscountConditionType == "payAmount" {
					if core_util.Float64MultiHundred(presentDiscount.Coupon.DiscountCondition.Value) > int64(totalPayAmount) {
						needRollback = true
					}
				} else {
					if core_util.Float64MultiHundred(presentDiscount.Coupon.DiscountCondition.Value) > int64(totalAmount) {
						needRollback = true
					}
				}
			}
		}
		if needRollback {
			shouldRollbackPresentDiscount = append(shouldRollbackPresentDiscount, presentDiscountId)
			refundPresentCoupon(ctx, order)
		}
	}
	return
}

func refundPresentCoupon(ctx context.Context, order *Order) {
	var needUpdate bool
	for i, product := range order.Products {
		for _, campaign := range product.Campaigns {
			if campaign.Type != CAMPAIGN_TYPE_PRESENT_COUPON {
				continue
			}
			if len(product.Logistics) != 0 {
				//已发货
				needUpdate = true
				order.Products[i].RefundStatus = PRESENT_REFUND_STATUS_SHIPPED_AND_NOT_MATCH_RULES
			} else {
				needUpdate = true
				order.Products[i].RefundStatus = ORDER_REFUND_STATUS_REFUNDED
			}
		}
	}
	if needUpdate {
		order.UpdateProducts(ctx)
	}
}

// 回退订单中没有发货的商品库存，已发货的商品库存需要确认收到退货后通过入库单处理
func (o *Order) ReturnProductStockNotDelivered(ctx context.Context, orderRefund *OrderRefund) {
	// 根据订单的 storeId 查找 mall，查到 mall 并且快递方式为小店自提，则退回小店库存
	isPickUpMallOrder, err := mall_service.IsPickUpMallOrder(ctx, o.StoreId.Hex())
	if err != nil {
		log.Error(ctx, "Failed to call ec.MallService.GetMall", log.Fields{})
	}

	var stock ec_product.StockOperator
	if isPickUpMallOrder {
		stock = mall_service.GenMallProductStockByStoreId(o.StoreId)
	} else if o.IsRetailerConsignmentOrder(ctx) {
		store, err := ec_client.StoreService.GetStore(ctx, &request.DetailRequest{Id: o.StoreId.Hex()})
		if err != nil {
			log.Warn(ctx, "Failed to get order store", log.Fields{
				"orderId": o.Id.Hex(),
				"storeId": o.StoreId.Hex(),
				"err":     err.Error(),
			})
		}
		distributorAccountId := o.GetDistributorAccountId(store)
		if !ec_share.IsDistributorConsignmentStockEnable(ctx, distributorAccountId) {
			return
		}
		stock = component.InitRetailerConsignmentProduct(distributorAccountId)
	} else if o.IsVirtual() {
		stock = ec_product.CProduct
	} else {
		switch o.Operator {
		case setting_model.DELIVERY_SETTING_OPERATOR_STAFF:
			if !ec_model.IsStoreProductStockEnabled(ctx) {
				return
			}
			stock = ec_store_warehouse.CStoreProductStock
		case setting_model.DELIVERY_SETTING_OPERATOR_USER:
			if !ec_model.IsProductStockEnabled(ctx, nil) {
				return
			}
			stock = ec_product.CProduct
		}
	}

	productChecker := func(outTradeId bson.ObjectId) bool {
		return true
	}

	// 不是 nil 的情况下，只处理 orderRefund 中的商品
	if orderRefund != nil {
		productChecker = func(outTradeId bson.ObjectId) bool {
			for _, product := range orderRefund.Products {
				if product.OutTradeId.Hex() == outTradeId.Hex() {
					return true
				}
			}
			return false
		}
	}

	for _, product := range o.Products {
		if !productChecker(product.OutTradeId) {
			continue
		}
		sku := product.Spec.Sku
		count := product.Total
		// 买赠活动的退款处理在其他地方已经处理了
		if !isPickUpMallOrder && CheckProductCampaignExistence(product, CAMPAIGN_TYPE_PRESENT) {
			continue
		}

		for _, l := range product.Logistics {
			count -= l.ProductCount // 已发货的部分，确认收到退货后入库
		}

		if (isPickUpMallOrder && IsInRefundProcess(product.RefundStatus)) || !isPickUpMallOrder {
			ctx = context.WithValue(ctx, "storeId", o.StoreId)
			err := stock.ReturnStock(ctx, sku, int(count))
			if err != nil {
				log.Warn(ctx, "Return product stock failed", log.Fields{
					"sku":          sku,
					"count":        product.Total,
					"errorMessage": err.Error(),
				})
			}
		}
	}
}

func (o *Order) ReturnPickupPassword(ctx context.Context) {
	if o.Method == ORDER_DELIVERY_METHOD_PICKUP && !o.HasBeenCompleted() && o.PickupPassword != "" {
		CPickupPassword.Unlock(ctx, o.PickupPassword, o.StoreId)
	}
}

func sendOrderRefundMsg(ctx context.Context, order *Order, orderRefund *OrderRefund, toStatus string, updateOrderRefundReq *order.UpdateOrderRefundRequest, basicPlaceholderValueMap map[string]string) {
	// 用到 updateOrderRefundReq 的情况需要确认其不为 nil
	if util.StrInArray(toStatus, &[]string{
		ORDER_REFUND_STATUS_REJECTED,
		ORDER_REFUND_STATUS_PENDING,
	}) && updateOrderRefundReq == nil {
		return
	}

	notifyType := ""
	switch toStatus {
	case ORDER_REFUND_STATUS_APPROVED:
		// 设置商家同意退款退货通知
		if orderRefund.RefundType == ORDER_REFUND_TYPE_RETURN_AND_REFUND {
			notifyType = ORDER_REFUND_STATUS_APPROVED
		}
	case ORDER_REFUND_STATUS_REJECTED:
		// 设置商家拒绝退款通知
		if updateOrderRefundReq.OperateType == ORDER_REFUND_OPERATE_TYPE_REFUND_REFUSED {
			notifyType = ORDER_REFUND_OPERATE_TYPE_REFUND_REFUSED
		}
	case ORDER_REFUND_STATUS_PENDING:
		// 设置买家申请退款通知
		if orderRefund.Type == ORDER_REFUND_TYPE_MEMBER {
			notifyType = constant.MESSAGE_RULE_REFUND_APPLIED
		}
		// 设置订单被商家主动发起退款通知
		if orderRefund.Type == ORDER_REFUND_TYPE_PORTAL {
			notifyType = constant.MESSAGE_RULE_REFUND_BY_STAFF
		}
		// 设置商家同意退款后通知
		if updateOrderRefundReq.OperateType == ORDER_REFUND_OPERATE_TYPE_AGREE_TO_REFUND {
			notifyType = ORDER_REFUND_OPERATE_TYPE_AGREE_TO_REFUND
		}
		// 设置商家确认收到退货商品后通知
		if updateOrderRefundReq.OperateType == ORDER_REFUND_OPERATE_TYPE_CONFIRM_RECEIPT_AND_REFUND {
			notifyType = ORDER_REFUND_OPERATE_TYPE_CONFIRM_RECEIPT_AND_REFUND
		}
	}
	// 由于此方法在退款单状态变更时调用，因此 toStatus 可能是其他值，导致这里是空字符串，NotifyCustomer 会报错，这种情况直接返回，减少不必要的请求
	if notifyType == "" {
		return
	}
	order.NotifyCustomer(ctx, notifyType, basicPlaceholderValueMap)
}

func HandDistributionOrder(ctx context.Context, order *Order, decDistributionAmount int64, needUpdateDistributionStatus bool) error {
	// 非分销订单或者已经处理过的分销订单不作处理
	if order.Distribution.PromoterId.Hex() == "" || order.Distribution.ProfitSharingStatus == DISTRIBUTION_STATUS_REFUNDED {
		return nil
	}

	promoter, err := ec_distribution.CPromoter.GetById(ctx, order.Distribution.PromoterId)
	if err != nil {
		log.Warn(ctx, "Failed to get promoter", log.Fields{
			"promoterId":   order.Distribution.PromoterId,
			"errorMessage": err.Error(),
		})
		return err
	}
	// 后台设置为不绑定时，这里会查不到下单的客户
	promoterMember, err := ec_distribution.CPromoterMember.GetByMemberAndPromoterId(ctx, order.MemberId, promoter.Id)
	if err != nil {
		log.Warn(ctx, "Failed to get promoterMember", log.Fields{
			"memberId":     order.MemberId,
			"errorMessage": err.Error(),
		})
	}
	amount := order.Distribution.Amount
	if decDistributionAmount > 0 {
		amount = uint64(decDistributionAmount)
	}
	if NeedDecProfitAmount(ctx, order) {
		// 扣除分销员的累计收益和待结算收益
		err = promoter.IncProfitAmount(ctx, -int64(amount), -int64(amount))
		if err != nil {
			log.Warn(ctx, "Failed to update promoter total profit amount", log.Fields{
				"promoterId":        promoter.Id,
				"totalProfitAmount": -order.Distribution.Amount,
				"errorMessage":      err.Error(),
			})
		}
	}

	// 扣减累计分销商品数
	{
		boundPromoter, _ := distribution.CPromoter.GetById(ctx, order.Distribution.GetBoundPromoterId())
		refundProducts := 0
		for _, p := range order.Products {
			if p.IsDistribution && p.RefundStatus != "" {
				refundProducts += int(p.Total)
			}
		}
		err = boundPromoter.UpdatePromoterCountField(ctx, &ec_distribution.PromoterCount{
			Product: -int64(refundProducts),
		}, util.GetMonthCountKey(order.PaidAt, 0))
		if err != nil {
			log.Warn(ctx, "Failed to update promoter total products", log.Fields{
				"promoterId":   boundPromoter.Id,
				"errorMessage": err.Error(),
			})
		}
	}

	if needUpdateDistributionStatus {
		// 为了防止多个商品在分账的定时任务执行之前分别退款，导致重复减扣分销员
		// 的分账金额为负数，需要标识此订单已经回退过分销员分账金额了
		err = order.UpdateDistributionStatus(ctx, order.Id.Hex(), DISTRIBUTION_STATUS_REFUNDED, "", nil, time.Time{})
		if err != nil {
			log.Warn(ctx, "Failed to update order distribution status", log.Fields{
				"orderId":  order.Id,
				"toStatus": DISTRIBUTION_STATUS_REFUNDED,
			})
		}
	}
	// 扣除客户的累计贡献佣金
	if promoterMember != nil && promoterMember.Id.Hex() != "" {
		err = promoterMember.IncTotalProfitAmount(ctx, -int(amount))
		if err != nil {
			log.Warn(ctx, "Failed to update promoterMember total profit amount", log.Fields{
				"promoterMemberId":  promoterMember.Id,
				"totalProfitAmount": -int64(amount),
				"errorMessage":      err.Error(),
			})
		}
	}

	return nil
}

func NeedDecProfitAmount(ctx context.Context, order *Order) bool {
	// 分销日结在创建 transferBill 时已经扣减了待结算收益
	if order.Distribution.ProfitSharingType != ec_distribution.PROFITSHARING_TYPE_MONTHLY {
		condition := bson.M{
			"accountId":    util.GetAccountIdAsObjectId(ctx),
			"isDeleted":    false,
			"orderTradeNo": order.TradeNo,
		}
		return !Common.Exist(ctx, ec_profit.C_TRANSFER_BILL, condition)
	}
	return true
}

func (self *OrderRefund) getProductNameAndCount() (string, uint64) {
	var productName string
	var productCount uint64
	for index, product := range self.Products {
		if index == 0 {
			productName = product.Name
		} else {
			productName = productName + "," + product.Name
		}
		productCount += product.Total
	}
	return productName, productCount
}

func (self *OrderRefund) SendRefundApplyEvent(ctx context.Context, order Order, extra bson.M) {
	store, _ := ec_store.CStore.GetById(ctx, order.StoreId)
	// 获取小店信息
	resp, err := client.MallService.GetMall(ctx, &mall.GetMallRequest{
		StoreId: order.StoreId.Hex(),
	})
	var from string
	var mallId string
	if err != nil {
		from = "store"
	} else {
		from = "mall"
		mallId = resp.Id
	}

	var campaignId, campaignName string
	for _, c := range self.Campaigns {
		campaignId = c.Id.Hex()
		campaignName = c.Title
	}

	var (
		goodsList []map[string]interface{}
	)
	for _, product := range self.Products {
		goodsList = append(goodsList, map[string]interface{}{
			"productName": product.Name,
			"productSku":  product.Spec.Sku,
			"productId":   product.ProductId.Hex(),
			"price":       product.Price,
			"count":       product.Total,
		})
	}

	productName, productCount := self.getProductNameAndCount()
	eventProperties := map[string]interface{}{
		"tradeId":       self.Id.Hex(),
		"refundId":      self.Number,
		"orderId":       self.OrderNumber,
		"platform":      "mai-retail",
		"storeId":       order.StoreId.Hex(),
		"storeName":     store.Name,
		"storeCode":     store.Code,
		"from":          from,
		"mallId":        mallId,
		"productName":   productName,
		"productCounts": productCount,
		"refundAmount":  self.RefundAmount,
		"isAllRefund":   self.RefundExtra.IsAllRefund,
		"needAudit":     self.RefundExtra.NeedAudit,
		"refundType":    self.RefundType,
		"status":        self.Status,
		"originOrderId": self.OrderId.Hex(),
		"campaignId":    campaignId,
		"campaignName":  campaignName,
		"goodsList":     goodsList,
	}
	storeType := self.GetStoreType()
	if storeType != "" {
		eventProperties["storeType"] = storeType
	}
	distributorId := self.GetDistributorId()
	if distributorId != "" {
		eventProperties["distributorId"] = distributorId
	}

	eventBody := component.CustomerEventBody{
		AccountId:       self.AccountId.Hex(),
		MemberId:        self.MemberId.Hex(),
		MsgType:         component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:         component.MAIEVENT_REFUND_APPLY,
		CreateTime:      time.Now().UnixNano() / 1e6,
		ChannelId:       self.ChannelId,
		OpenId:          order.Channel.OpenId,
		EventProperties: eventProperties,
	}
	for k, v := range extra {
		eventBody.EventProperties[k] = v
	}

	eventBody.SendCustomerEventWithBusinessTag(ctx)
}

func (self *OrderRefund) SendRefundEvent(ctx context.Context) {
	order, _ := COrder.GetById(ctx, self.OrderId)
	store, _ := self.GetStore(ctx)

	var (
		goodsList []map[string]interface{}
	)
	for _, product := range self.Products {
		goodsList = append(goodsList, map[string]interface{}{
			"productName": product.Name,
			"productSku":  product.Spec.Sku,
			"productId":   product.ProductId.Hex(),
			"price":       product.Price,
			"count":       product.Total,
			"payAmount":   product.PayAmount,
		})
	}

	// 获取小店信息
	resp, err := client.MallService.GetMall(ctx, &mall.GetMallRequest{
		StoreId: order.StoreId.Hex(),
	})
	var from string
	var mallId string
	if err != nil {
		from = "store"
	} else {
		from = "mall"
		mallId = resp.Id
	}
	productName, productCount := self.getProductNameAndCount()
	eventProperties := map[string]interface{}{
		"tradeId":           self.Id.Hex(),
		"refundId":          self.Number,
		"orderId":           self.OrderNumber,
		"platform":          "mai-retail",
		"storeId":           order.StoreId.Hex(),
		"storeName":         store.Name,
		"storeCode":         store.Code,
		"from":              from,
		"mallId":            mallId,
		"productName":       productName,
		"productCounts":     productCount,
		"refundAmount":      self.RefundAmount,
		"isAllRefund":       self.RefundExtra.IsAllRefund,
		"id":                self.OrderId.Hex(),
		"number":            self.OrderNumber,
		"refundType":        self.RefundType,
		"status":            self.Status,
		"originOrderId":     self.OrderId.Hex(),
		"goodsList":         goodsList,
		"orderType":         order.GetType(),
		"refundGoodsAmount": self.GetGoodsRefundAmount(),
		"goodsAmount":       self.GetGoodsAmount(order),
	}
	storeType := self.GetStoreType()
	if storeType != "" {
		eventProperties["storeType"] = storeType
	}
	distributorId := self.GetDistributorId()
	if distributorId != "" {
		eventProperties["distributorId"] = distributorId
	}

	eventBody := component.CustomerEventBody{
		AccountId:       self.AccountId.Hex(),
		MemberId:        self.MemberId.Hex(),
		MsgType:         component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:         component.MAIEVENT_REFUND_ORDER,
		CreateTime:      self.RefundedAt.UnixNano() / 1e6,
		ChannelId:       self.ChannelId,
		OpenId:          order.Channel.OpenId,
		EventProperties: eventProperties,
	}

	eventBody.SendCustomerEventWithBusinessTag(ctx)
}

func (self *OrderRefund) SendCancelRefundEvent(ctx context.Context) {
	order, _ := COrder.GetById(ctx, self.OrderId)
	store, _ := self.GetStore(ctx)
	var (
		goodsList        []map[string]interface{}
		refundTotalCount uint64
	)
	for _, product := range self.Products {
		goodsList = append(goodsList, map[string]interface{}{
			"productName":  product.Name,
			"productCount": product.Total,
			"refundAmount": product.Price,
			"productId":    product.ProductId.Hex(),
			"storeId":      order.StoreId.Hex(),
			"storeName":    store.Name,
		})
		refundTotalCount += product.Total
	}
	eventProperties := map[string]interface{}{
		"refundId":          self.Number,
		"number":            self.OrderNumber,
		"platform":          "mai-retail",
		"isAllRefund":       self.RefundExtra.IsAllRefund,
		"refundTotalAmount": self.RefundAmount,
		"refundTotalCount":  refundTotalCount,
		"goodsList":         goodsList,
	}
	eventBody := component.CustomerEventBody{
		AccountId:       self.AccountId.Hex(),
		MemberId:        self.MemberId.Hex(),
		MsgType:         component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:         component.MAIEVENT_CANCEL_REFUND_ORDER,
		CreateTime:      time.Now().UnixNano() / 1e6,
		ChannelId:       self.ChannelId,
		OpenId:          order.Channel.OpenId,
		EventProperties: eventProperties,
	}
	eventBody.SendCustomerEvent(ctx)
}

func (self *OrderRefund) GetGoodsRefundAmount() int64 {
	refundAmount := int64(0)
	for _, product := range self.Products {
		refundAmount += int64(product.RefundAmount)
	}
	return refundAmount
}

func (self *OrderRefund) GetGoodsAmount(order Order) int64 {
	amount := int64(0)
	for _, product := range self.Products {
		for _, orderProduct := range order.Products {
			if orderProduct.OutTradeId.Hex() == product.OutTradeId.Hex() && !CheckProductCampaignExistence(orderProduct, CAMPAIGN_TYPE_PLUS_BUY) {
				amount += int64(product.Price * product.Total)
			}
		}
	}
	return amount
}

func (self *OrderRefund) GetStoreType() string {
	if !self.IsYeepayRefundOrderForD2R() {
		return ""
	}
	if util.StrInArray(ORDER_TAGS_CONSIGNMENT, &self.Tags) {
		return ORDER_TAGS_CONSIGNMENT
	}
	return ORDER_TAGS_DISTRIBUTION
}

func (o *OrderRefund) GetDistributorId() string {
	if !o.IsYeepayRefundOrderForD2R() {
		return ""
	}
	for _, tag := range o.Tags {
		if util.IsMongoId(tag) {
			return tag
		}
	}
	return ""
}

// 是否是脉盟商城退款单
func (self *OrderRefund) IsMaiMengRefundOrder() bool {
	if util.StrInArray(ORDER_TAGS_MAIMENG, &self.Tags) {
		return true
	}
	return false
}

// 是否是脉盟商城/连锁零售商代销退款单
func (self *OrderRefund) IsConsignmentRefundOrder() bool {
	if util.StrInArray(ORDER_TAGS_CONSIGNMENT, &self.Tags) {
		return true
	}
	return false
}

// 是否是连锁零售商退款单
func (self *OrderRefund) IsChainRetailRefundOrder() bool {
	if util.StrInArray(ORDER_TAGS_CHAIN_RETAIL, &self.Tags) {
		return true
	}
	return false
}

// 是否使用易宝退款的 D2R 退款单
func (self *OrderRefund) IsYeepayRefundOrderForD2R() bool {
	if self.IsMaiMengRefundOrder() {
		return true
	}
	if self.IsChainRetailRefundOrder() {
		return true
	}
	return false
}

// 是否使用易宝退款的退款单
func (self *OrderRefund) IsYeepayRefundOrder() bool {
	if util.StrInArray(ORDER_TAGS_YEEPAY, &self.Tags) {
		return true
	}
	return false
}

// 是否是用收钱吧退款的退款单
func (self *OrderRefund) IsShouqianbaRefundOrder() bool {
	if util.StrInArray(ORDER_TAGS_SHOUQIANBA, &self.Tags) {
		return true
	}
	return false
}

// 是否是张裕退款单
func (o *OrderRefund) IsZhangYuRefundOrder() bool {
	if util.StrInArray(o.AccountId.Hex(), &[]string{"649e929030d9ba37953be4d2", "645b0dbe088a96644b319e55"}) {
		return true
	}
	return false
}

func (self *OrderRefund) SendRefundProductEvent(ctx context.Context) {
	store, _ := self.GetStore(ctx)
	var campaignId, campaignName string
	for _, c := range self.Campaigns {
		campaignId = c.Id.Hex()
		campaignName = c.Title
	}
	storeType := self.GetStoreType()
	distributorId := self.GetDistributorId()
	for _, product := range self.Products {
		eventProperties := map[string]interface{}{
			"platform":           "mai-retail",
			"storeId":            self.StoreId.Hex(),
			"storeCode":          store.Code,
			"storeName":          store.Name,
			"orderId":            self.OrderId.Hex(),
			"orderNumber":        self.OrderNumber,
			"productName":        product.Name,
			"productId":          product.ProductId.Hex(),
			"productNumber":      product.Number,
			"productSku":         product.Spec.Sku,
			"productExternalSku": product.Spec.ExternalSku,
			"refundId":           self.Number,
			"refundAmount":       product.PayAmount,
			"outTradeId":         product.OutTradeId,
			"id":                 self.OrderId.Hex(),
			"number":             self.OrderNumber,
			"campaignId":         campaignId,
			"campaignName":       campaignName,
		}
		if storeType != "" {
			eventProperties["storeType"] = storeType
		}
		if distributorId != "" {
			eventProperties["distributorId"] = distributorId
		}

		eventBody := component.CustomerEventBody{
			Id:              component.MAIEVENT_REFUND_PRODUCT + ":" + self.Number + ":" + product.Id.Hex() + ":" + product.Spec.Sku,
			AccountId:       self.AccountId.Hex(),
			MemberId:        self.MemberId.Hex(),
			ChannelId:       self.ChannelId,
			MsgType:         component.MAIEVENT_TYPE_CUSTOMER_EVENT,
			SubType:         component.MAIEVENT_REFUND_PRODUCT,
			CreateTime:      self.RefundedAt.UnixNano() / 1e6,
			EventProperties: eventProperties,
		}
		eventBody.Send(ctx)
	}
}

func UpdateMemberEcProperties(ctx context.Context, order *Order, orderRefund *OrderRefund) error {
	memberDetail, err := ec_share.GetMemberById(ctx, order.MemberId, []string{"Properties"})
	if err != nil {
		return err
	}

	var (
		oldPurchaseCount, oldTotalConsumeAmount float64
	)
	for _, prop := range memberDetail.Properties {
		switch prop.Name {
		case ec_model_member.EC_MEMBER_PROPERTY_NAME_PURCHASE_COUNT:
			valNum := prop.GetValueNumber()

			if valNum != nil {
				oldPurchaseCount = valNum.Value
			}
		case ec_model_member.EC_MEMBER_PROPERTY_NAME_TOTAL_CONSUME_AMOUNT:
			valNum := prop.GetValueNumber()
			if valNum != nil {
				oldTotalConsumeAmount = valNum.Value
			}
		}
	}

	var newPurchaseCount, newPerOrderPrice, newTotalConsumeAmount float64

	isPurchaseAction := orderRefund == nil || orderRefund.Id.Hex() == ""
	if isPurchaseAction { // 购买行为
		newPurchaseCount = oldPurchaseCount + 1
		newTotalConsumeAmount = oldTotalConsumeAmount + float64(order.PayAmount)
	} else { // 退款行为
		newPurchaseCount = oldPurchaseCount
		newTotalConsumeAmount = oldTotalConsumeAmount
	}

	if newPurchaseCount == 0 {
		newPerOrderPrice = 0
	} else {
		newPerOrderPrice = math.Round(newTotalConsumeAmount / newPurchaseCount)
	}

	updateMemberPropertyReq := &pb_member.BatchUpdateMemberPropertyRequest{
		MemberIds: []string{
			order.MemberId.Hex(),
		},
		Properties: []*pb_member.PropertyInfo{
			{
				PropertyId: ec_model_member.EC_MEMBER_PROPERTY_ID_PURCHASE_COUNT,
				Value: &pb_member.PropertyInfo_ValueNumber{
					ValueNumber: &pb_member.PropertyNumberValue{
						Value: newPurchaseCount,
					},
				},
			},
			{
				PropertyId: ec_model_member.EC_MEMBER_PROPERTY_ID_TOTAL_CONSUME_AMOUNT,
				Value: &pb_member.PropertyInfo_ValueNumber{
					ValueNumber: &pb_member.PropertyNumberValue{
						Value: newTotalConsumeAmount,
					},
				},
			},
			{
				PropertyId: ec_model_member.EC_MEMBER_PROPERTY_ID_PRICE_PER_ORDER,
				Value: &pb_member.PropertyInfo_ValueNumber{
					ValueNumber: &pb_member.PropertyNumberValue{
						Value: newPerOrderPrice,
					},
				},
			},
		},
	}

	if isPurchaseAction {
		updateMemberPropertyReq.Properties = append(updateMemberPropertyReq.Properties, &pb_member.PropertyInfo{
			PropertyId: ec_model_member.EC_MEMBER_PROPERTY_ID_LAST_PURCHASE_TIME,
			Value: &pb_member.PropertyInfo_ValueDate{
				ValueDate: &pb_member.PropertyDateValue{
					Value: time.Now().Unix(),
				},
			},
		})
	}
	return share_service.BatchUpdateMemberProperty(ctx, updateMemberPropertyReq)
}

func (o *OrderRefund) GetNeedPushRefundOrder(ctx context.Context) ([]OrderRefund, error) {
	selector := bson.M{
		"accountId":               util.GetAccountIdAsObjectId(ctx),
		"isDeleted":               false,
		"omsProcessor.pushStatus": OMS_ORDER_REFUND_PUSH_STATUS_PENDING,
	}

	orderRefunds := []OrderRefund{}
	err := extension.DBRepository.FindAll(ctx, C_ORDER_REFUND, selector, []string{}, GET_ORDERS_LIMIT, &orderRefunds)
	if err != nil {
		return nil, err
	}

	return orderRefunds, nil
}

func (o *OrderRefund) UpdatePushStatusByIds(ctx context.Context, ids []bson.ObjectId, status string, isProcessing bool) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id": bson.M{
			"$in": ids,
		},
	}
	if isProcessing {
		selector["omsProcessor.pushStatus"] = OMS_ORDER_REFUND_PUSH_STATUS_PROCESSING
	}

	setter := bson.M{
		"omsProcessor.pushStatus": status,
		"omsProcessor.pushedAt":   time.Now(),
		"updatedAt":               time.Now(),
	}

	updater := bson.M{
		"$set": setter,
	}

	_, err := extension.DBRepository.UpdateAll(ctx, C_ORDER_REFUND, selector, updater)

	return err
}

func (o *OrderRefund) UpdatePushStatusAfterPush(ctx context.Context, pushStatus string) error {
	o.OmsProcessor.PushStatus = pushStatus
	o.OmsProcessor.PushedAt = time.Now()
	for i, discardedRefund := range o.OmsProcessor.DiscardedRefunds {
		// discardedRefund 的推送状态只有 pending、pushed、failed，更新结果时只有 pending 状态的是本次推送过的单号
		if discardedRefund.PushStatus == OMS_ORDER_REFUND_PUSH_STATUS_PENDING {
			o.OmsProcessor.DiscardedRefunds[i].PushStatus = pushStatus
		}
	}
	selector := bson.M{
		"accountId":               util.GetAccountIdAsObjectId(ctx),
		"omsProcessor.pushStatus": OMS_ORDER_REFUND_PUSH_STATUS_PROCESSING,
		"_id":                     o.Id,
	}
	updater := bson.M{
		"$set": bson.M{
			"omsProcessor": o.OmsProcessor,
			"updatedAt":    time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER_REFUND, selector, updater)
}

func (o *OrderRefund) RetryPushToOms(ctx context.Context, ids []bson.ObjectId) error {
	selector := bson.M{
		"omsProcessor.pushStatus": OMS_ORDER_REFUND_PUSH_STATUS_PROCESSING,
		"accountId":               util.GetAccountIdAsObjectId(ctx),
		"_id": bson.M{
			"$in": ids,
		},
	}

	setter := bson.M{
		"omsProcessor.pushStatus": OMS_ORDER_REFUND_PUSH_STATUS_PENDING,
		"omsProcessor.pushedAt":   time.Now(),
		"updatedAt":               time.Now(),
	}

	inc := bson.M{
		"omsProcessor.retryTimes": 1,
	}

	updater := bson.M{
		"$set": setter,
		"$inc": inc,
	}

	_, err := extension.DBRepository.UpdateAll(ctx, C_ORDER_REFUND, selector, updater)

	return err
}

func (o *OrderRefund) UpdateOmsPushStatusToPending(ctx context.Context) error {
	// 施华蔻租户，只有「施华蔻官方精品商城」门店走旺店通
	// https://gitlab.maiscrm.com/mai/impl/home/<USER>/issues/7879#note_5096153
	if core_util.GetAccountId(ctx) == "5f8d06e621b41201355c72c2" && o.StoreId.Hex() != "5ff29fd8c61a3b3a240c1323" {
		return nil
	}

	selector := bson.M{
		"accountId": o.AccountId,
		"_id":       o.Id,
	}

	updater := bson.M{
		"$set": bson.M{
			"omsProcessor.pushStatus": OMS_ORDER_REFUND_PUSH_STATUS_PENDING,
			"updatedAt":               time.Now(),
		},
	}

	return extension.DBRepository.UpdateOne(ctx, C_ORDER_REFUND, selector, updater)
}

func (*OrderRefund) BatchUpdateMemberId(ctx context.Context, oldMemberIds []bson.ObjectId, newMemberId bson.ObjectId) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["memberId"] = bson.M{
		"$in": oldMemberIds,
	}

	updator := bson.M{
		"$set": bson.M{
			"memberId":  newMemberId,
			"updatedAt": time.Now(),
		},
	}

	_, err := extension.DBRepository.UpdateAll(ctx, C_ORDER_REFUND, selector, updator)
	return err
}

func (o *OrderRefund) SendDistOrderRefundEvent(ctx context.Context, order *Order) {
	if order.Distribution.PromoterId.Hex() == "" {
		return
	}

	var (
		productCounts uint64
		refundCounts  uint64
	)
	for _, product := range order.Products {
		productCounts += product.Total
	}
	for _, rProduct := range o.Products {
		refundCounts += rProduct.Total
	}
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_ORDER_ORDER_REFUND + ":" + o.Id.Hex(),
		AccountId:  o.AccountId.Hex(),
		MemberId:   o.MemberId.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_ORDER_ORDER_REFUND,
		CreateTime: o.RefundedAt.UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"memberId":      o.MemberId.Hex(),
			"storeId":       o.StoreId,
			"storeCode":     o.store.Code,
			"orderId":       o.OrderNumber,
			"id":            o.Id.Hex(),
			"number":        o.Number,
			"productCounts": productCounts,
			"refundCounts":  refundCounts,
		},
	}

	refundAmount := o.RefundAmount
	if o.Method == ORDER_DELIVERY_METHOD_EXPRESS {
		refundAmount -= o.ExpressFee
	}
	eventBody.EventProperties["refundAmount"] = refundAmount

	promoter, _ := distribution.CPromoter.GetById(ctx, order.Distribution.PromoterId)
	if promoter != nil {
		eventBody.EventProperties["promoterId"] = promoter.Id.Hex()
		if order.Distribution.ParentPromoterId.Valid() {
			eventBody.EventProperties["parentPromoterId"] = order.Distribution.ParentPromoterId.Hex()
		}
		if order.Distribution.SubPromoterId.Valid() {
			eventBody.EventProperties["subPromoterId"] = order.Distribution.SubPromoterId.Hex()
		}
		eventBody.EventProperties["promoterType"] = promoter.Type
		eventBody.EventProperties["staffId"] = promoter.MemberBoundStaff.StaffId.Hex()
		if o.StoreId.Hex() != "" {
			eventBody.EventProperties["storeId"] = o.StoreId.Hex()
		}
	}

	eventBody.Send(ctx)
}

func ReturnScore(ctx context.Context, memberId string, score uint64, extraInfo interface{}) {
	metaBytes, _ := json.Marshal(extraInfo)
	meta := make(map[string]string)
	json.Unmarshal(metaBytes, &meta)
	req := &pb_member.UpdateScoreRequest{
		Ids:         []string{memberId},
		Score:       int64(score),
		Brief:       "refund_rollback_score",
		Description: "积分抵扣返还",
		Meta:        string(metaBytes),
		Origin:      constant.PORTAL,
		BusinessId:  meta["orderId"],
	}
	_, err := share_service.UpdateScore(ctx, req)
	if err != nil {
		log.Error(ctx, "Failed refund score when order refund", log.Fields{
			"memberId":     memberId,
			"errorMessage": err.Error(),
		})
	}
}

func HandlePurchaseLimitedProductRefund(ctx context.Context, order *Order, orderRefund *OrderRefund, isCancelOrder bool) {
	var productIds []string
	refundProductCountMap := map[string]int{}
	if isCancelOrder {
		productIds = core_util.ExtractArrayStringField("Id", order.Products)
		for _, p := range order.Products {
			refundProductCountMap[p.Id.Hex()] = int(p.Total)
		}
	} else {
		productIds = core_util.ExtractArrayStringField("Id", orderRefund.Products)
		for _, p := range orderRefund.Products {
			refundProductCountMap[p.Id.Hex()] = int(p.Total)
		}
	}
	orderProductStatusStats, _ := COrderProductPurchaseStats.GetByProductIdsAndMemberId(ctx, order.MemberId, core_util.ToObjectIdArray(productIds))
	if len(orderProductStatusStats) == 0 {
		return
	}
	purchaseStatsMap := core_util.MakeMapper("ProductId", orderProductStatusStats)
	products, err := ec_client.ProductService.ListProducts(ctx, &ec.ListProductsRequest{
		ListCondition: &request.ListCondition{
			PerPage: uint32(len(productIds)),
		},
		EcProductIds: productIds,
	})
	if err != nil {
		return
	}
	for _, product := range products.Items {
		purchaseLimit := product.Ec.PurchaseLimit
		if purchaseLimit == nil || purchaseLimit.Count == 0 {
			continue
		}
		stats, ok := purchaseStatsMap[bson.ObjectIdHex(product.Ec.Id)]
		if !ok {
			continue
		}
		purchaseStats := stats.(*OrderProductPurchaseStats)
		if purchaseStats.PeriodType != purchaseLimit.PeriodType || purchaseStats.Date != GetDateByPeriodType(purchaseLimit.PeriodType, order.CreatedAt) {
			continue
		}
		c, ok := refundProductCountMap[purchaseStats.ProductId.Hex()]
		if !ok {
			continue
		}
		COrderProductPurchaseStats.DecrMemberPurchaseCount(ctx, purchaseStats.ProductId, order.MemberId, c)
	}
}

// 零售下单储值卡，退款零售订单时，储值卡也要退款
func HandleCardOrderRefund(ctx context.Context, order *Order, orderRefund *OrderRefund) {
	refundMemberPrepaidCardIds := []string{}
	for _, rP := range orderRefund.Products {
		for _, p := range order.Products {
			if p.ProductId.Hex() != rP.ProductId.Hex() {
				continue
			}
			refundMemberPrepaidCardIds = append(refundMemberPrepaidCardIds, util.MongoIdsToStrs(p.MemberPrepaidCardIds)...)
		}
	}
	if len(refundMemberPrepaidCardIds) == 0 {
		return
	}

	resp, err := pb_client.GetEcWalletServiceClient().ListPrepaidCards(ctx, &ec_wallet.ListPrepaidCardsRequest{
		MemberId: order.MemberId.Hex(),
		Ids:      refundMemberPrepaidCardIds,
	})
	if err != nil {
		log.Warn(ctx, "list prepaid cards fail", log.Fields{
			"errMsg": err.Error(),
			"ids":    refundMemberPrepaidCardIds,
		})
		return
	}
	passwords := []string{}
	for _, c := range resp.Items {
		passwords = append(passwords, c.Password)
	}
	req := &pb_card_product.RefundCardOrderRequest{
		OrderId:   order.Extra["cardOrderId"].(string),
		Passwords: passwords,
		Reason:    "零售订单退款",
	}
	pb_client.GetEcCardProductServiceClient().RefundCardOrder(ctx, req)
}

func HandleProductCardRollback(ctx context.Context, order *Order, orderRefund *OrderRefund) {
	// 回退产品卡
	if len(order.Campaigns) > 0 && order.Campaigns[0].Type == TYPE_PRODUCT_CARD {
		// 退款的商品 Ids
		refundProductIds := []string{}
		// 退款商品对应的客户产品卡的 Ids
		rollbackProductCardIds := []string{}
		for _, product := range orderRefund.Products {
			refundProductIds = append(refundProductIds, product.Id.Hex())
		}
		for _, product := range order.Products {
			if util.StrInArray(product.Id.Hex(), &refundProductIds) && len(product.Campaigns) > 0 {
				for _, campaign := range product.Campaigns {
					rollbackProductCardIds = append(rollbackProductCardIds, campaign.Id.Hex())
				}
			}
		}
		if len(rollbackProductCardIds) > 0 {
			rollbackProductCards(ctx, orderRefund.OrderId.Hex(), rollbackProductCardIds)
			orderRefund.SendRefundProductCardExchangeEvent(ctx, order, rollbackProductCardIds)
		}
	}
}

func rollbackProductCards(ctx context.Context, orderId string, productCardIds []string) {
	_, err := client.WalletService.RollbackMemberProductCard(ctx, &ec_wallet.RollbackMemberProductCardRequest{
		Ids:     productCardIds,
		OrderId: orderId,
	})
	if err != nil {
		log.Error(ctx, "Failed to rollback product card", log.Fields{
			"productCardIds": productCardIds,
			"orderId":        orderId,
			"errorMessage":   err.Error(),
		})
	}
}

func (self *OrderRefund) SendRefundProductCardExchangeEvent(ctx context.Context, order *Order, memberProductCardIds []string) {

	isAllRefund := false
	if len(order.Products) == len(self.Products) {
		isAllRefund = true
	}
	memberProductCards, _ := ec_model_wallet.CMemberProductCard.GetByIds(ctx, memberProductCardIds)
	productCardCountMap := map[bson.ObjectId]uint64{}
	productCardIds := []string{}
	for _, memberProductCard := range memberProductCards {
		productCardCountMap[memberProductCard.ProductCardId]++
		productCardIds = append(productCardIds, memberProductCard.ProductCardId.Hex())
	}
	productCardIds = util.StrArrayUnique(productCardIds)
	productCards, err := ec_model_wallet.CProductCard.GetByIds(ctx, util.ToMongoIds(productCardIds))
	if err != nil {
		return
	}
	productCardList := []*map[string]interface{}{}
	for _, productCard := range productCards {
		var count uint64 = 0
		if v, ok := productCardCountMap[productCard.Id]; ok {
			count = v
		} else {
			continue
		}
		productCard := map[string]interface{}{
			"cardNumber": productCard.Number,
			"cardName":   productCard.Name,
			"unitPrice":  productCard.Price,
			"count":      count,
		}
		productCardList = append(productCardList, &productCard)
	}

	eventBody := share_component.CustomerEventBody{
		AccountId: util.GetAccountId(ctx),
		EventProperties: map[string]interface{}{
			"orderId":         order.Id.Hex(),
			"orderNumber":     order.Number,
			"isAllRefund":     isAllRefund,
			"refundId":        self.Id.Hex(),
			"refundPrice":     self.RefundAmount,
			"refundCount":     len(memberProductCardIds),
			"productCardList": productCardList,
		},
		MemberId:   self.MemberId.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    share_component.MAIEVENT_REFUND_PRODUCTCARD_EXCHANGE,
		CreateTime: time.Now().UnixNano() / 1e6,
	}

	eventBody.SendCustomerEvent(ctx)
}

func HandleMemberRedeemCardRollback(ctx context.Context, order *Order, orderRefund *OrderRefund) {
	if len(order.Campaigns) > 0 && order.Campaigns[0].Type == TYPE_REDEEM_CARD {
		// 退款的商品 Ids
		refundProductIds := []string{}
		rollbackMemberRedeemCardIds := []string{}
		for _, product := range orderRefund.Products {
			refundProductIds = append(refundProductIds, product.Id.Hex())
		}
		for _, product := range order.Products {
			if util.StrInArray(product.Id.Hex(), &refundProductIds) && len(product.Campaigns) > 0 {
				for _, campaign := range product.Campaigns {
					rollbackMemberRedeemCardIds = append(rollbackMemberRedeemCardIds, campaign.Id.Hex())
				}
			}
		}
		if len(rollbackMemberRedeemCardIds) > 0 {
			if order.IsSplitSubOrder() {
				originOrder, err := orderRefund.GetOriginOrderByOutTradeId(ctx, order.OutTradeId)
				if err != nil {
					log.Error(ctx, "Failed to rollback redeem card", log.Fields{
						"campaigns":                   order.Campaigns,
						"rollbackMemberRedeemCardIds": rollbackMemberRedeemCardIds,
						"errorMessage":                err.Error(),
					})
					return
				}
				order = originOrder

			}
			RollbackRedeemCards(ctx, order.Id.Hex(), orderRefund.MemberId.Hex(), rollbackMemberRedeemCardIds)
			orderRefund.SendRefundRedeemCardExchangeEvent(ctx, order, rollbackMemberRedeemCardIds)
		}
	}
}

func RollbackRedeemCards(ctx context.Context, orderId, memberId string, memberRedeemCardIds []string) {
	_, err := client.CardProductService.RollbackMemberRedeemCards(ctx, &pb_card_product.RollbackMemberRedeemCardsRequest{
		MemberId: memberId,
		OrderId:  orderId,
		Ids:      memberRedeemCardIds,
	})
	if err != nil {
		log.Error(ctx, "Failed to rollback member redeem cards", log.Fields{
			"memberRedeemCardIds": memberRedeemCardIds,
			"orderId":             orderId,
			"errorMessage":        err.Error(),
		})
	}
}

func (self *OrderRefund) SendRefundRedeemCardExchangeEvent(ctx context.Context, order *Order, memberRedeemCardIds []string) {
	isAllRefund := false
	if len(order.Products) == len(self.Products) {
		isAllRefund = true
	}
	memberRedeemCards, _ := card_product.CMemberRedeemCard.GetByIds(ctx, util.ToMongoIds(memberRedeemCardIds))
	redeemCardIds := core_util.ExtractArrayStringField("RedeemCardId", memberRedeemCards)
	redeemCards, err := card_product.CRedeemCard.GetByIds(ctx, util.ToMongoIds(redeemCardIds))
	if err != nil {
		return
	}
	redeemCardMap := map[string]card_product.RedeemCard{}
	redeemCardCountMap := map[string]uint64{}
	for _, memberRedeemCard := range memberRedeemCards {
		if !memberRedeemCard.RedeemCardId.Valid() {
			continue
		}
		for _, redeemCard := range redeemCards {
			if memberRedeemCard.RedeemCardId.Hex() == redeemCard.Id.Hex() {
				redeemCardMap[memberRedeemCard.Id.Hex()] = redeemCard
				redeemCardCountMap[redeemCard.Number]++
				break
			}
		}
	}
	if len(redeemCardMap) == 0 {
		return
	}
	redeemCardExistedMap := make(map[string]bool)
	exchangeCardList := []*map[string]interface{}{}
	refundCount := uint64(0)
	for _, v := range redeemCardMap {
		if _, ok := redeemCardExistedMap[v.Number]; ok {
			continue
		}
		var count uint64 = 0
		if c, ok := redeemCardCountMap[v.Number]; ok {
			count = c
		} else {
			continue
		}
		redeemCard := map[string]interface{}{
			"cardNumber": v.Number,
			"cardName":   v.Name,
			"cardType":   v.Type,
			"count":      count,
		}
		refundCount += count
		exchangeCardList = append(exchangeCardList, &redeemCard)
		redeemCardExistedMap[v.Number] = true
	}

	eventBody := share_component.CustomerEventBody{
		AccountId: util.GetAccountId(ctx),
		EventProperties: map[string]interface{}{
			"orderId":          order.Id.Hex(),
			"orderNumber":      order.Number,
			"isAllRefund":      isAllRefund,
			"refundId":         self.Id.Hex(),
			"refundPrice":      order.TotalAmount + order.GetLogisticsFee(),
			"refundCount":      refundCount,
			"exchangeCardList": exchangeCardList,
		},
		MemberId:   self.MemberId.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    share_component.MAIEVENT_REFUND_EXCHANGECARD_EXCHANGE,
		CreateTime: time.Now().UnixNano() / 1e6,
	}

	eventBody.SendCustomerEvent(ctx)
}

func HandlePresentCampaignRefund(ctx context.Context, order *Order, orderRefund *OrderRefund, isCancelOrder bool) {
	if len(order.Campaigns) == 0 {
		return
	}
	presentCampaignIds := []bson.ObjectId{}
	for _, c := range order.Campaigns {
		if c.Type == CAMPAIGN_TYPE_PRESENT {
			presentCampaignIds = append(presentCampaignIds, c.Id)
		}
	}
	if len(presentCampaignIds) == 0 {
		return
	}
	presentCampaigns, _ := ec_marketing.CPresentCampaign.GetByIds(ctx, presentCampaignIds)
	if len(presentCampaigns) == 0 {
		return
	}

	// 回滚满减增活动商品库存
	rollbackPresentCampaignStock(ctx, order, orderRefund, presentCampaigns, isCancelOrder)

	if isCancelOrder {
		err := HandlePresentCampaignOrderCancel(ctx, order, presentCampaigns)
		if err != nil {
			log.Warn(ctx, "Failed handle presentCampaign while cancel order", log.Fields{
				"orderId":      order.Id,
				"errorMessage": err.Error(),
			})
		}
		return
	}

	// 发生退款时需要重新计算订单金额时候符合赠送条件
	// 如果不符合则取消赠送，不重新计算是否符合其他赠送等级
	err := HandlePresentCampaignOrderRefund(ctx, order, orderRefund, presentCampaigns)
	if err != nil {
		log.Warn(ctx, "Failed handle presentCampaign while refund order", log.Fields{
			"orderId":      order.Id,
			"errorMessage": err.Error(),
		})
	}
}

func rollbackPresentCampaignStock(ctx context.Context, order *Order, orderRefund *OrderRefund, presentCampaigns []*ec_marketing.PresentCampaign, isCancelOrder bool) {
	for _, product := range order.Products {
		for _, c := range presentCampaigns {
			existsStock := false
			for _, skuStock := range c.Rule.SkuStocks {
				if skuStock.Sku == product.Spec.Sku {
					existsStock = true
				}
			}
			// 取消订单并且有设置库存时才回退库存
			if isCancelOrder && existsStock {
				err := c.ReturnStock(ctx, c.Id, product.Spec.Sku, int(product.PresentDiscountCount))
				if err != nil {
					log.Warn(ctx, "failed to rollback present campaign stock", log.Fields{
						"campaignId":           c.Id,
						"orderId":              order.Id,
						"productId":            product.Id,
						"sku":                  product.Spec.Sku,
						"presentDiscountCount": product.PresentDiscountCount,
						"errMsg":               err.Error(),
					})
				}
			}

			if orderRefund != nil {
				for _, p := range orderRefund.Products {
					if p.Id.Hex() != product.Id.Hex() || p.Spec.Sku != product.Spec.Sku {
						continue
					}
					if existsStock {
						err := c.ReturnStock(ctx, c.Id, product.Spec.Sku, int(product.PresentDiscountCount))
						if err != nil {
							log.Warn(ctx, "failed to rollback present campaign stock", log.Fields{
								"campaignId":           c.Id,
								"orderId":              order.Id,
								"refundId":             orderRefund.Id,
								"productId":            product.Id,
								"sku":                  product.Spec.Sku,
								"presentDiscountCount": product.PresentDiscountCount,
								"errMsg":               err.Error(),
							})
						}
					}
				}
			}
		}
	}
}

func RollbackDiscountCampaignProductStock(ctx context.Context, order *Order, orderRefund *OrderRefund, isCancelOrder bool) {
	if len(order.Campaigns) == 0 {
		return
	}
	discountCampaignIds := []string{}
	for _, c := range order.Campaigns {
		if c.Type == CAMPAIGN_TYPE_DISCOUNT {
			discountCampaignIds = append(discountCampaignIds, c.Id.Hex())
		}
	}
	if len(discountCampaignIds) == 0 {
		return
	}

	req := &pb_eccampaign_discount_campaign.ListDiscountCampaignsRequest{
		Ids: discountCampaignIds,
	}
	resp, _ := proto_client.GetEccampaignDiscountCampaignServiceClient().ListDiscountCampaigns(ctx, req)
	if len(resp.Items) == 0 {
		return
	}

	for _, product := range order.Products {
		for _, c := range resp.Items {
			existsStock := false
			for _, p := range c.Products {
				for _, sku := range p.Skus {
					if product.Spec.Sku == sku.Sku && sku.TotalStock > 0 {
						existsStock = true
					}
				}
			}
			// 取消订单并且有设置库存时才回退库存
			if isCancelOrder && existsStock && product.DiscountCampaignCount > 0 {
				req := &pb_eccampaign_discount_campaign.UpdateDiscountCampaignStockRequest{
					Id:        c.Id,
					ProductId: product.Id.Hex(),
					Sku:       product.Spec.Sku,
					Stock:     product.DiscountCampaignCount,
					Operation: "return",
				}
				_, err := proto_client.GetEccampaignDiscountCampaignServiceClient().UpdateDiscountCampaignStock(ctx, req)
				if err != nil {
					log.Warn(ctx, "failed to rollback discount campaign product stock", log.Fields{
						"campaignId": c.Id,
						"orderId":    order.Id,
						"productId":  product.Id,
						"sku":        product.Spec.Sku,
						"errMsg":     err.Error(),
					})
				}
			}

			if orderRefund != nil {
				for _, p := range orderRefund.Products {
					if p.Id.Hex() != product.Id.Hex() || p.Spec.Sku != product.Spec.Sku {
						continue
					}
					if existsStock && product.DiscountCampaignCount > 0 {
						req := &pb_eccampaign_discount_campaign.UpdateDiscountCampaignStockRequest{
							Id:        c.Id,
							ProductId: product.Id.Hex(),
							Sku:       product.Spec.Sku,
							Stock:     product.DiscountCampaignCount,
							Operation: "return",
						}
						_, err := proto_client.GetEccampaignDiscountCampaignServiceClient().UpdateDiscountCampaignStock(ctx, req)
						if err != nil {
							log.Warn(ctx, "failed to rollback discount campaign product stock", log.Fields{
								"campaignId": c.Id,
								"orderId":    order.Id,
								"refundId":   orderRefund.Id,
								"productId":  product.Id,
								"sku":        product.Spec.Sku,
								"errMsg":     err.Error(),
							})
						}
					}
				}
			}
		}
	}
}

func RollbackRandomDiscountRecord(ctx context.Context, order *Order, orderRefund *OrderRefund, isCancelOrder bool) {
	if len(order.Campaigns) == 0 {
		return
	}
	recordId := ""
	for _, c := range order.Campaigns {
		if c.Type == CAMPAIGN_TYPE_RANDOM_DISCOUNT {
			recordId = c.RecordId.Hex()
			break
		}
	}
	if !bson.IsObjectIdHex(recordId) {
		return
	}
	rollbackFn := func() {
		pb_client.GetEccampaignRandomDiscountServiceClient().RollbackRedeemedRandomDiscountRecord(ctx, &pb_eccampaign_random_discount.RollbackRedeemedRandomDiscountRecordRequest{
			Id:          recordId,
			OrderNumber: order.Number,
		})
	}
	// 如果是取消订单，那么直接回滚即可
	if isCancelOrder {
		rollbackFn()
		return
	}
	var (
		refundProductOutTradeIds []string
		needRollback             = true
	)
	if orderRefund != nil {
		for _, refundProduct := range orderRefund.Products {
			refundProductOutTradeIds = append(refundProductOutTradeIds, refundProduct.OutTradeId.Hex())
		}
	}
	for _, op := range order.Products {
		isDiscountProduct := false
		for _, campaign := range op.Campaigns {
			if campaign.RecordId.Hex() == recordId {
				isDiscountProduct = true
				break
			}
		}
		if !isDiscountProduct {
			continue
		}
		// 如果还有参与了活动的商品没有退款那么就不回滚
		if !IsInRefundProcess(op.RefundStatus) && !util.StrInArray(op.OutTradeId.Hex(), &refundProductOutTradeIds) {
			needRollback = false
		}
	}
	if needRollback {
		rollbackFn()
	}
}

func HandleGrouponCampaignRefund(ctx context.Context, order *Order) {
	if !order.IsGrouponCampaignOrder() {
		return
	}
	for i, c := range order.Campaigns {
		if c.Type != CAMPAIGN_TYPE_GROUPON {
			continue
		}
		if c.GrouponStatus == ec_marketing.GROUPON_RECORD_STATUS_RUNNING {
			order.Campaigns[i].GrouponStatus = ec_marketing.GROUPON_RECORD_STATUS_FAILED
		}
	}
	err := order.UpdateCampaigns(ctx)
	if err != nil {
		log.Warn(ctx, "Failed to update order campaigns", log.Fields{
			"orderId": order.Id.Hex(),
			"errMsg":  err.Error(),
		})
	}
}

func HandlePresentCampaignOrderCancel(ctx context.Context, order *Order, presentCampaigns []*ec_marketing.PresentCampaign) error {
	for _, p := range order.Products {
		if p.Campaigns == nil {
			continue
		}

		// 如果赠品状态已经是 refunded，说明赠品已经被退款，防止多次部分退款的情况下对已经退款的赠品反复回退库存
		if p.RefundStatus == ORDER_REFUND_STATUS_REFUNDED {
			continue
		}

		for _, c := range p.Campaigns {
			if c.Type != CAMPAIGN_TYPE_PRESENT {
				continue
			}

			// 回退库存
			for _, pc := range presentCampaigns {
				if pc.Id != c.Id {
					continue
				}

				for _, present := range pc.Discounts {
					// 多级优惠中可能不同等级使用相同规格的商品，所以需要区分赠品等级
					if present.Id != p.Id || present.Spec.Sku != p.Spec.Sku || present.Level != c.PresentLevel {
						continue
					}
					err := ec_product.CProduct.ReturnStock(ctx, present.Spec.Sku, int(p.Total))
					if err != nil {
						log.Warn(ctx, "Failed to return present stock.", log.Fields{
							"present":         present,
							"presentCampaign": pc,
							"errorMessage":    err.Error(),
						})
					}
					// 进行中的活动可以编辑，所以会出现回退库存时赠品已不再此活动中
					err = ec_marketing.CPresentCampaign.RefundPresentStatistics(ctx, pc.Id, present, p.Total)
					if err != nil {
						log.Warn(ctx, "Failed to rollback present.", log.Fields{
							"present":         present,
							"presentCampaign": pc,
							"errorMessage":    err.Error(),
						})
					}
				}
			}
		}
	}

	newCampaigns := []Campaign{}
	// 订单取消后，移除参与的买赠活动
	for _, campaign := range order.Campaigns {
		if campaign.Type != CAMPAIGN_TYPE_PRESENT {
			newCampaigns = append(newCampaigns, campaign)
		}
	}
	order.Campaigns = newCampaigns

	// 更新订单中赠品的状态以及参与的买赠活动
	return order.UpdatePresentStatusAndCampaigns(ctx)
}

func HandlePresentCampaignOrderRefund(ctx context.Context, order *Order, orderRefund *OrderRefund, presentCampaigns []*ec_marketing.PresentCampaign) error {
	// for presentCampaigns
	// 找到同一个活动的商品
	// 计算退款后的总价
	// 不符合当前级别的条件则加库存并将赠品更新为 refunded
	for _, pc := range presentCampaigns {
		// 先算退款后参与 pc 的总金额
		totalAmount := uint64(0)
		productCount := uint64(0)

		for _, product := range order.Products {
			// 判断商品是否是满赠活动中的商品（注意不是赠品）
			if !isPresentCampaignProduct(product, pc) {
				continue
			}
			// 实物商品和虚拟商品的退款逻辑不一样，需要分别计算
			// 实物商品存在退款状态说明该商品已经退过款，不能计入剩余未退款金额
			if product.RefundStatus == "" {
				totalAmount += product.TotalAmount
				productCount += product.Total
				continue
			}
			// 虚拟商品即使有 refundStatus 也有可能是本次退款流程中的商品
			// 通过判断订单商品是否在本次的退款单中，即可判断是本次流程中退款的商品
			// 本次退款流程中的虚拟商品可能是部分退款，因此需要计算没有退款的金额和数量
			for _, refundProduct := range orderRefund.Products {
				if refundProduct.OutTradeId != product.OutTradeId {
					continue
				}
				// 将订单商品的总金额减去本次退款单的退款金额即可得到该商品本次的退款金额，退款数量同理
				leftCount := product.Total - refundProduct.Total
				leftAmount := product.TotalAmount / product.Total * leftCount
				totalAmount = totalAmount + leftAmount
				productCount = productCount + leftCount
			}
		}
		// 判断订单中的赠品是否还能被赠送
		// 不符合赠送条件的赠品回退库存并将订单中该赠品的状态更新为 refunded
		for index, product := range order.Products {
			if product.Campaigns == nil {
				continue
			}

			// 即使参与某活动的商品被全部被退款，由于订单参与的活动记录还存在，totalAmount 计算为 0
			// 如果不判断当前商品是否已退款直接往后运行会导致已退款的赠品再次回退库存
			if product.RefundStatus == ORDER_REFUND_STATUS_REFUNDED {
				continue
			}

			for j, productPresentCampaign := range product.Campaigns {
				if productPresentCampaign.Id != pc.Id {
					continue
				}

				for _, discount := range pc.Discounts {
					if discount.Level != productPresentCampaign.PresentLevel {
						continue
					}

					var refundCount uint64
					if discount.Type != ec_marketing.PRESENT_CAMPAIGN_DISCOUNT_TYPE_CYCLE {
						if (pc.Type == ec_marketing.PRESENT_CAMPAIGN_TYPE_AMOUNT && totalAmount >= discount.OrderAmount) ||
							(pc.Type == ec_marketing.PRESENT_CAMPAIGN_TYPE_PIECE && productCount >= discount.ProductCount) {
							// 退款后仍满足满赠的条件
							continue
						}
						refundCount = getPresentRefundCount(ctx, order.MemberId.Hex(), &order.Products[index], product.Total)
					} else {
						if discount.Spec.Sku != product.Spec.Sku {
							continue
						}
						newCount := uint64(0)
						switch pc.Type {
						case ec_marketing.PRESENT_CAMPAIGN_TYPE_AMOUNT:
							newCount = totalAmount / discount.OrderAmount
						case ec_marketing.PRESENT_CAMPAIGN_TYPE_PIECE:
							newCount = productCount / discount.ProductCount
						}
						if newCount >= productPresentCampaign.Count {
							continue
						}
						// 每次优惠赠送数量由总赠送数/优惠次数计算，防止活动被修改导致回退数量错误
						refundCount = product.Total / productPresentCampaign.Count * (productPresentCampaign.Count - newCount)
						order.Products[index].Campaigns[j].Count = newCount
						refundCount = getPresentRefundCount(ctx, order.MemberId.Hex(), &order.Products[index], refundCount)
					}

					var orderRefundId bson.ObjectId
					if orderRefund != nil {
						orderRefundId = orderRefund.Id
					}

					// 只要商品被发货，则更新商品状态为已发货但不满足赠送条件
					if IsPresentShipd(&product) {
						// 因退款导致不满足的赠品需要关联退款单
						order.Products[index].OrderRefundId = orderRefundId
						order.Products[index].RefundStatus = PRESENT_REFUND_STATUS_SHIPPED_AND_NOT_MATCH_RULES
					}

					if refundCount > 0 {
						// 退款时，如果订单金额不再满足条件，更新订单上赠品状态为 refunded 的优先级大于回退库存的优先级
						// 因为进行中的活动可编辑，回退库存时赠品可能已经不存在此活动中了
						if discount.Type == ec_marketing.PRESENT_CAMPAIGN_DISCOUNT_TYPE_CYCLE && order.Products[index].Total > refundCount {
							order.Products[index].Total -= refundCount
						} else {
							if discount.Type == ec_marketing.PRESENT_CAMPAIGN_DISCOUNT_TYPE_CYCLE {
								order.Products[index].Total -= refundCount
							}
							order.Products[index].RefundStatus = ORDER_REFUND_STATUS_REFUNDED
							order.Products[index].OrderRefundId = orderRefundId
						}
						err := ec_product.CProduct.ReturnStock(ctx, product.Spec.Sku, int(refundCount))
						if err != nil {
							log.Warn(ctx, "Failed to return present stock while order refunding.", log.Fields{
								"present":         product,
								"presentCampaign": pc,
								"errorMessage":    err.Error(),
							})
						}
						// 退款后订单金额不符合赠送条件，不再赠送该赠品，更新赠品库存
						err = ec_marketing.CPresentCampaign.RefundPresentStatistics(ctx, productPresentCampaign.Id, discount, refundCount)
						if err != nil {
							log.Warn(ctx, "Failed to update presents statistics while order refunding.", log.Fields{
								"orderId":      order.Id.Hex(),
								"product":      product,
								"errorMessage": err.Error(),
							})
						}
					}
				}
			}
		}
	}

	// 更新订单中赠品的状态以及参与的买赠活动
	return order.UpdatePresentStatusAndCampaigns(ctx)
}

func handleGrouponCampaignWhenCanceled(ctx context.Context, campaign Campaign, order Order, toStatus string) {
	if campaign.GrouponStatus == ec_marketing.GROUPON_RECORD_STATUS_SUCCESS {
		return
	}
	condition := Common.GenDefaultCondition(ctx)
	condition["campaigns"] = bson.M{
		"$elemMatch": bson.M{
			"id":              campaign.Id,
			"grouponRecordId": campaign.GrouponRecordId,
		},
	}
	switch toStatus {
	case ORDER_REFUND_STATUS_CANCELED:
		//  用户取消申请
		grouponRecord, err := ec_marketing.CGrouponRecord.Get(ctx, campaign.GrouponRecordId)
		if err != nil {
			log.Warn(ctx, "failed to get groupon record", log.Fields{
				"grouponRecordId": campaign.GrouponRecordId.Hex(),
				"errMsg":          err.Error(),
			})
		}
		if grouponRecord.Status == ec_marketing.GROUPON_RECORD_STATUS_SUCCESS {
			condition["_id"] = order.Id
			updator := bson.M{
				"$set": bson.M{
					"campaigns.$.grouponStatus": ec_marketing.GROUPON_RECORD_STATUS_SUCCESS,
					"updatedAt":                 time.Now(),
				},
			}
			err := extension.DBRepository.UpdateOne(ctx, C_ORDER, condition, updator)
			if err != nil {
				log.Warn(ctx, "failed to update order", log.Fields{
					"orderId": order.Id.Hex(),
					"errMsg":  err.Error(),
				})
			}
			return
		}
		if grouponRecord.Status == ec_marketing.GROUPON_RECORD_STATUS_FAILED {
			// 取消申请时，拼团已经失败了，走拼团失败的逻辑
			SKUs := []*pb_order.ProductSKU{}
			for _, product := range order.Products {
				SKUs = append(SKUs, &pb_order.ProductSKU{
					Id:  product.Id.Hex(),
					Sku: product.Spec.Sku,
				})
			}
			refundRequest := pb_order.RefundOrderRequest{
				Id:          order.Id.Hex(),
				MemberId:    order.MemberId.Hex(),
				Reason:      "未满足拼团成功人数",
				ProductSkus: SKUs,
			}
			_, err := ec_client.OrderService.RefundOrder(ctx, &refundRequest)
			if err != nil {
				log.Warn(ctx, "Failed to refund order", log.Fields{
					"orderId":   order.Id.Hex(),
					"grouponId": campaign.GrouponRecordId.Hex(),
				})
			}
			return
		}
		return
	default:
		condition["status"] = bson.M{"$in": []string{ORDER_STATUS_ACCEPTED, ORDER_STATUS_PAID, ORDER_STATUS_UNASSIGNED, ORDER_STATUS_UNPAID}}
		condition["_id"] = bson.M{"$ne": order.Id}
		err := extension.DBRepository.FindOne(ctx, C_ORDER, condition, bson.M{})
		if err == bson.ErrNotFound { // 拼团已无其他订单，直接失败
			ec_marketing.CGrouponRecord.UpdateStatus(ctx, campaign.GrouponRecordId, ec_marketing.GROUPON_RECORD_STATUS_FAILED)
		}
	}
}

func (OrderRefund) GetOrderRefundStats(ctx context.Context, selector bson.M) (bson.M, error) {
	result := bson.M{}
	pipeline := []bson.M{
		{
			"$match": selector,
		},
		{
			"$group": bson.M{
				"_id":          "null",
				"refundAmount": bson.M{"$sum": "$refundAmount"},
			},
		},
	}
	err := extension.DBRepository.Aggregate(ctx, C_ORDER_REFUND, pipeline, true, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (OrderRefund) GetOrderRefundCount(ctx context.Context, selector bson.M) (int, error) {
	result := bson.M{}
	pipeline := []bson.M{
		{
			"$match": selector,
		},
		{
			"$unwind": "$refundExtra.refundProducts",
		},
		{
			"$group": bson.M{
				"_id":         "null",
				"refundCount": bson.M{"$sum": "$refundExtra.refundProducts.total"}},
		},
	}
	err := extension.DBRepository.Aggregate(ctx, C_ORDER_REFUND, pipeline, true, &result)
	if err != nil {
		return 0, err
	}
	return cast.ToInt(result["refundCount"]), nil
}

func (OrderRefund) FindOneByOrderId(ctx context.Context, orderId bson.ObjectId) (*OrderRefund, error) {
	selector := share_model.Base.GenDefaultCondition(ctx)
	selector["orderId"] = orderId
	orderRefund := OrderRefund{}
	err := extension.DBRepository.FindOne(ctx, C_ORDER_REFUND, selector, &orderRefund)
	if err != nil {
		return nil, err
	}

	return &orderRefund, nil
}

func (OrderRefund) GetByNumber(ctx context.Context, number string) (*OrderRefund, error) {
	orderRefund := OrderRefund{}
	selector := Common.GenDefaultCondition(ctx)
	selector["number"] = number
	err := extension.DBRepository.FindOne(ctx, C_ORDER_REFUND, selector, &orderRefund)
	if err != nil {
		return nil, err
	}
	return &orderRefund, err
}

func (OrderRefund) GetById(ctx context.Context, id bson.ObjectId) (*OrderRefund, error) {
	orderRefund := OrderRefund{}
	selector := Common.GenDefaultConditionById(ctx, id)
	err := extension.DBRepository.FindOne(ctx, C_ORDER_REFUND, selector, &orderRefund)
	if err != nil {
		return nil, err
	}
	return &orderRefund, err
}

func (o OrderRefund) CreateNotification(ctx context.Context, toStatus string) {
	// 脉盟商城/连锁零售商城代销订单不需要创建通知
	if o.IsConsignmentRefundOrder() {
		return
	}
	notification := ec_model_notification.Notification{
		StoreId:           o.StoreId,
		OrderNumber:       o.OrderNumber,
		OrderId:           o.OrderId,
		MemberId:          o.MemberId,
		OrderRefundNumber: o.RefundNumber,
		OrderRefundId:     o.Id,
	}

	switch toStatus {
	case ORDER_REFUND_STATUS_WAITINGAUDIT:
		notification.Type = ec_model_notification.NOTIFICATION_TYPE_ORDER_REFUND
		notification.Content = ec_model_notification.NOTIFICATION_CONTENT_ORDER_REFUND
	}
	err := notification.Create(ctx)
	if err != nil {
		log.Error(ctx, "Failed to create notification", log.Fields{
			"type":         toStatus,
			"orderNumber":  o.Number,
			"errorMessage": err.Error(),
		})
	}
}

func isUpdateOmsStatusToPending(ctx context.Context, orderRefund *OrderRefund) bool {
	settings, err := ec_setting.CSettings.Get(ctx)
	if err != nil {
		return false
	}
	needUpdate := false
	if orderRefund.RefundExtra.NeedAudit {
		if core_util.StrInArray(settings.ErpProvider, &[]string{ec_setting.ERP_PROVIDER_WDT, ec_setting.ERP_PROVIDER_WDT_ULTIMATE}) {
			needUpdate = true
		}
		if core_util.StrInArray(orderRefund.Status, &[]string{
			ORDER_REFUND_STATUS_APPROVED,
			ORDER_REFUND_STATUS_RETURNED,
			ORDER_REFUND_STATUS_PENDING,
			ORDER_REFUND_STATUS_REJECTED,
			ORDER_REFUND_STATUS_CANCELED,
		}) {
			needUpdate = true
		}
	}

	if orderRefund.Status == ORDER_REFUND_STATUS_WAITINGAUDIT && orderRefund.RefundType == ORDER_REFUND_TYPE_ONLY_REFUND {
		// 判断是否需要推送仅退款且状态为待审核的退款单给第三方erp
		if settings.NeedPushWaitingAudit {
			needUpdate = true
		}
	}

	return needUpdate
}

func TransformSpec(orderSpec OrderProductSpec) RefundProductSpec {
	return RefundProductSpec{
		Sku:         orderSpec.Sku,
		ExternalSku: orderSpec.ExternalSku,
		Properties:  TransformProperties(orderSpec.Properties),
	}
}

func ConvertOrderProductToRefundProduct(orderProduct OrderProduct) RefundProduct {
	refundProduct := RefundProduct{}
	core_util.TransformFields(orderProduct, &refundProduct, map[string]interface{}{
		"Id":         func(id bson.ObjectId) bson.ObjectId { return id },
		"Spec":       TransformSpec,
		"OutTradeId": func(outTradeId bson.ObjectId) bson.ObjectId { return outTradeId },
	}, map[string]string{})
	for _, c := range orderProduct.Campaigns {
		if c.Type == CAMPAIGN_TYPE_PACKAGE {
			refundProduct.PackageId = c.PackageId
		}
	}
	refundProduct.RefundAmount = refundProduct.PayAmount
	refundProduct.IsAmountAdjusted = orderProduct.IsAmountAdjusted()
	copier.Instance(nil).From(orderProduct.Discounts).CopyTo(&refundProduct.Discounts)
	return refundProduct
}

func (o *OrderRefund) UpdateRefundExtra(ctx context.Context) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       o.Id,
		"isDeleted": false,
	}
	setter := bson.M{
		"refundExtra": o.RefundExtra,
		"updatedAt":   time.Now(),
	}

	updater := bson.M{
		"$set": setter,
	}

	err := extension.DBRepository.UpdateOne(ctx, C_ORDER_REFUND, selector, updater)
	if err != nil {
		return err
	}
	return nil
}

func (o *OrderRefund) UpdateExtra(ctx context.Context) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       o.Id,
		"isDeleted": false,
	}
	setter := bson.M{
		"extra":     o.Extra,
		"updatedAt": time.Now(),
	}

	updater := bson.M{
		"$set": setter,
	}

	err := extension.DBRepository.UpdateOne(ctx, C_ORDER_REFUND, selector, updater)
	if err != nil {
		return err
	}
	return nil
}

func (o *OrderRefund) GetOmsRefundNo() string {
	if o.OmsProcessor.RefundNo != "" {
		return o.OmsProcessor.RefundNo
	}
	return o.Id.Hex()
}

func (o *OrderRefund) GetStore(ctx context.Context) (ec_store.Store, error) {
	if !o.StoreId.Valid() {
		return ec_store.Store{}, errors.NewNotExistsError("orderRefund.storeId")
	}
	if o.StoreId != o.store.Id {
		store, err := ec_store.CStore.GetById(ctx, o.StoreId)
		if err != nil {
			return ec_store.Store{}, err
		}
		o.store = store
	}
	return o.store, nil
}

func (*OrderRefund) UpdateInvoiceStatus(ctx context.Context, orderId bson.ObjectId, invoiceStatus string) (int, error) {
	condition := Common.GenDefaultCondition(ctx)
	condition["orderId"] = orderId
	updator := bson.M{
		"$set": bson.M{
			"invoiceStatus": invoiceStatus,
			"updatedAt":     time.Now(),
		},
	}
	return extension.DBRepository.UpdateAll(ctx, C_ORDER_REFUND, condition, updator)
}

// refundAmount 已扣除退掉的邮费
func (*OrderRefund) GetOrderRefundAmount(ctx context.Context, orderId bson.ObjectId) (refundAmount uint64, refundDeliveryFee uint64, err error) {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"orderId":   orderId,
		"status":    ORDER_REFUND_STATUS_REFUNDED,
	}
	orderRefunds := []OrderRefund{}
	err = extension.DBRepository.FindAll(ctx, C_ORDER_REFUND, condition, nil, 0, &orderRefunds)
	if err != nil || len(orderRefunds) == 0 {
		return
	}
	for _, o := range orderRefunds {
		refundAmount += o.RefundAmount
		refundDeliveryFee += o.ExpressFee
	}
	refundAmount -= refundDeliveryFee
	return
}

func (*OrderRefund) UpdateDistributorIdsByStoreId(ctx context.Context, storeId bson.ObjectId, distributorIds []bson.ObjectId) error {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"storeId":   storeId,
	}
	updater := bson.M{
		"$set": bson.M{
			"distributorIds": distributorIds,
			"updatedAt":      time.Now(),
		},
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_ORDER_REFUND, condition, updater)
	return err
}

func (o *OrderRefund) GetOrderOutTradeId() string {
	if o.OrderOutTradeId.Hex() != "" {
		return o.OrderOutTradeId.Hex()
	}
	return o.OrderId.Hex()
}

func RefundCouponProduct(ctx context.Context, memberId string, orderProduct *OrderProduct, needRefundCount uint64) (uint64, error) {
	var currentRefundCount uint64 = 0
	unusedMembershipDiscountIds := []string{}
	// 券未发货时，只有全款预售的情况可以退款
	if len(orderProduct.MembershipDiscountIds) == 0 {
		var presellCampaignId bson.ObjectId
		for _, campaign := range orderProduct.Campaigns {
			if campaign.Type != CAMPAIGN_TYPE_PRESELL {
				continue
			}
			presellCampaignId = campaign.Id
		}
		if presellCampaignId.IsZero() {
			return currentRefundCount, nil
		}
		presellCampaign, err := pb_client.GetEcMarketingServiceClient().GetPresellCampaign(ctx, &pb_ec_marketing.GetPresellCampaignRequest{
			Id:             presellCampaignId.Hex(),
			ContainDeleted: true,
		})
		if err != nil {
			return 0, err
		}
		if presellCampaign.Type == ec_marketing.TYPE_FULL_PAYMENT {
			return needRefundCount, nil
		}
		return currentRefundCount, nil
	}
	resp, err := mairpc.Run(
		"CouponService.GetMembershipDiscounts",
		ctx,
		&coupon.GetMembershipDiscountsRequest{
			MemberId: memberId,
			Ids:      util.MongoIdsToStrs(orderProduct.MembershipDiscountIds),
			ListCondition: &request.ListCondition{
				Page:    1,
				PerPage: uint32(len(util.MongoIdsToStrs(orderProduct.MembershipDiscountIds))),
				OrderBy: []string{"-createdAt"},
			},
		},
	)
	if err != nil {
		return 0, err
	}
	for _, membershipDiscount := range resp.(*coupon.MembershipDiscounts).Items {
		if membershipDiscount.Coupon.Status == "unused" && membershipDiscount.Gifting.Status != "gifting" {
			unusedMembershipDiscountIds = append(unusedMembershipDiscountIds, membershipDiscount.Id)
		}
	}
	if len(unusedMembershipDiscountIds) == 0 {
		return currentRefundCount, nil
	}

	if int(needRefundCount) < len(unusedMembershipDiscountIds) {
		unusedMembershipDiscountIds = unusedMembershipDiscountIds[0:needRefundCount]
	}
	for _, unusedId := range unusedMembershipDiscountIds {
		_, rpcErr := mairpc.Run(
			"CouponService.RollbackReceivedCouponToInvalid",
			ctx,
			&coupon.RollbackReceivedCouponToInvalidRequest{
				MembershipDiscountId: unusedId,
				InvalidReason:        MEMBERSHIP_DISCOUNT_INVALID_REASON_ORDER_REFUND,
			},
		)
		if rpcErr != nil {
			log.Error(ctx, "Rollback received coupon failed", log.Fields{
				"accountId":            util.GetAccountId(ctx),
				"membershipDiscountId": unusedId,
				"errMsg":               rpcErr,
			})
			continue
		}
		currentRefundCount += 1
	}
	return currentRefundCount, nil
}

func RefundVirtualProduct(refundCount uint64, orderProduct *OrderProduct) {
	for i, period := range orderProduct.PickupCodeRedeemPeriods {
		if refundCount > 0 && period.Status == PICKUP_CODE_STATUS_UNUSED {
			orderProduct.PickupCodeRedeemPeriods[i].Status = PICKUP_CODE_STATUS_REFUND
			refundCount--
		}
	}
}

func IsPresentShipd(orderProduct *OrderProduct) bool {
	switch orderProduct.Type {
	case VIRTUAL_ORDER_TYPE_COUPON:
		if len(orderProduct.MembershipDiscountIds) > 0 {
			return true
		} else {
			return false
		}
	case VIRTUAL_ORDER_TYPE_VIRTUAL:
		if len(orderProduct.PickupCodes) > 0 {
			return true
		} else {
			return false
		}
	default:
		if len(orderProduct.Logistics) > 0 {
			return true
		} else {
			return false
		}
	}
}

// 返回本次退款中退掉的赠品数量
func getPresentRefundCount(ctx context.Context, memberId string, orderProduct *OrderProduct, needRefundCount uint64) uint64 {
	var refundCount = needRefundCount
	switch orderProduct.Type {
	case VIRTUAL_ORDER_TYPE_COUPON:
		if orderProduct.SubType == product.C_PRODUCT_SUB_TYPE_STORED_VALUE_CARD {
			refundCount = orderProduct.Total
		} else {
			refundCount, _ = RefundCouponProduct(ctx, memberId, orderProduct, needRefundCount)
		}
	case VIRTUAL_ORDER_TYPE_VIRTUAL:
		var canRefundCount uint64
		for _, p := range orderProduct.PickupCodeRedeemPeriods {
			if p.Status == PICKUP_CODE_STATUS_UNUSED {
				canRefundCount += 1
			}
		}
		if refundCount > canRefundCount {
			refundCount = canRefundCount
		}
		RefundVirtualProduct(refundCount, orderProduct)
	default:
		refundCount = orderProduct.Total
		for _, logistics := range orderProduct.Logistics {
			refundCount -= logistics.ProductCount // 已经发货的赠品，赠品库存不应该被退回
		}
		if refundCount > needRefundCount {
			refundCount = needRefundCount
		}
	}
	return refundCount
}

func isPresentCampaignProduct(p OrderProduct, presentCampaign *ec_marketing.PresentCampaign) bool {
	if p.Campaigns != nil {
		return false
	}
	switch presentCampaign.Rule.Type {
	case ec_marketing.PRESENT_CAMPAIGN_TYPE_INCLUDE:
		existSkus := []string{}
		for _, product := range presentCampaign.Rule.RuleProducts {
			existSkus = append(existSkus, product.Skus...)
		}
		return util.ObjectIdInArray(p.Id, &presentCampaign.Rule.ProductIds) || util.StrInArray(p.Spec.Sku, &existSkus)
	case ec_marketing.PRESENT_CAMPAIGN_TYPE_ALL:
		return true
	}
	return false
}

// 获取易宝商户号
func (o *OrderRefund) GetYeepayMerchants() (string, string) {
	if o.IsYeepayRefundOrder() {
		parentMerchantNo := ""
		merchantNo := ""
		for _, tag := range o.Tags {
			if strings.Contains(tag, "parentMerchantNo") {
				parentMerchantNo = strings.Split(tag, "_")[1]
			}
			if strings.Contains(tag, "merchantNo") {
				merchantNo = strings.Split(tag, "_")[1]
			}
		}
		return parentMerchantNo, merchantNo
	}
	return "", ""
}

// 是否支付后立即分账
func (o *OrderRefund) IsDivideAfterPaid() bool {
	if o.IsYeepayRefundOrder() {
		for _, tag := range o.Tags {
			if strings.Contains(tag, "divideType") && strings.Contains(tag, setting_model.DIVIDE_TYPE_AFTER_PAID) {
				return true
			}
		}
	}
	return false
}

// 易宝分账回退，目前只回退支付后立即分账的订单的平台服务费，商户号是 ***********
func (o *OrderRefund) yeepayDivideBackWithMerchantNo(ctx context.Context, merchantNo string) {
	if !o.IsYeepayRefundOrder() {
		return
	}

	if !o.IsDivideAfterPaid() {
		return
	}

	order, err := COrder.GetById(ctx, o.OrderId)
	if err != nil {
		return
	}

	condition := bson.M{
		"accountId":        o.AccountId,
		"orderTradeNo":     order.TradeNo,
		"receiver.account": merchantNo,
	}
	bill, _ := ec_profit.CTransferBill.GetOneByCondition(ctx, condition)
	if !bill.Id.Valid() {
		return
	}
	parentMerchantNo, merchantNo := order.GetYeepayMerchants()
	// 没有易宝分账明细单号，不回退
	if bill.DetailId == "" && len(bill.DetailIds) == 0 {
		return
	}
	amount := o.GetDivideBackAmount(ctx, order, bill)
	if amount == 0 {
		log.Warn(ctx, "Divide back amount is zero", log.Fields{})
		return
	}

	strAmount := cast.ToString(util.DivideFloatWithRound(float64(amount), 100, 2))
	detail := fmt.Sprintf(
		`[{"amount":"%s","divideDetailNo":"%s","divideBackReason":"%s"}]`,
		strAmount, // 易宝金额以元为单位
		bill.DetailId,
		"订单退款回退平台服务费",
	)
	req := &yeepay.DivideBackRequest{
		ParentMerchantNo:    parentMerchantNo,
		MerchantNo:          merchantNo,
		DivideRequestId:     bill.Id.Hex(),
		OrderId:             order.Id.Hex(),
		UniqueOrderNo:       order.TradeNo,
		DivideBackRequestId: fmt.Sprintf("back_%s_%s_%s", bill.Id.Hex(), o.Id.Hex(), strAmount),
		DivideBackDetail:    detail,
	}
	client := yeepay.NewYeepay(yeepay.YEEPAY_APP_KEY, core_util.GetMaimengPrivateKey())
	resp, err := client.Jiaoyi.DivideBack(ctx, req)
	if err != nil {
		log.Warn(ctx, "Divide back error", log.Fields{
			"req": req,
		})
		return
	}

	if resp.Code != "OPR00000" {
		log.Warn(ctx, "Divide back fail", log.Fields{
			"req":         req,
			"resp":        resp,
			"bill":        bill,
			"orderRefund": o,
		})
	}
	bill.UpdateBackRecords(ctx, resp, amount)
}

// 获取需要回退的金额，单位为分
func (o *OrderRefund) GetDivideBackAmount(ctx context.Context, order Order, bill ec_profit.TransferBill) uint64 {
	isAllRefund := false
	if len(order.Products) == len(o.Products) {
		isAllRefund = true
	}
	// 全部退款将所有分账金额都回退
	if isAllRefund {
		return bill.ShareAmount
	}
	backAllAmount := uint64(0)
	for _, r := range bill.BackRecords {
		backAllAmount += r.Amount
	}
	cond := bson.M{
		"accountId": o.AccountId,
		"orderId":   order.Id,
		"isDeleted": false,
	}
	orderRefunds, _ := COrderRefund.GetAllByCondition(ctx, cond)
	refundProductCount := 0
	for _, r := range orderRefunds {
		refundProductCount += len(r.Products)
	}
	// 如果是最后一笔退款单，分账回退金额需要原始分账金额减去已回退金额
	if refundProductCount == len(order.Products) && len(bill.BackRecords) > 0 {
		return bill.BackRecords[0].ShareAmount - backAllAmount
	}

	// 根据退款金额计算需要回退的金额
	backAmount := util.DivideFloatWithRound(util.MultiplyFloat(float64(bill.ShareAmount), float64(o.RefundAmount)), float64(order.PayAmount), 0)
	return cast.ToUint64(backAmount)
}

func (o *OrderRefund) IsRefundDeliveryFee() string {
	if len(o.Products) == 0 {
		return "是"
	}
	return "否"
}

func (o *OrderRefund) UpdateTradeRecordRefunds(ctx context.Context) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       o.Id,
		"isDeleted": false,
	}
	setter := bson.M{
		"tradeRecordRefunds": o.TradeRecordRefunds,
		"updatedAt":          time.Now(),
	}

	updater := bson.M{
		"$set": setter,
	}

	err := extension.DBRepository.UpdateOne(ctx, C_ORDER_REFUND, selector, updater)
	if err != nil {
		return err
	}
	return nil
}

// 是否有正在退款中的交易记录退款单
func (o *OrderRefund) HavingRefundingTradeRecordRefund() bool {
	for _, r := range o.TradeRecordRefunds {
		if r.Status == ORDER_REFUND_STATUS_REFUNDING {
			return true
		}
	}
	return false
}

func (o *OrderRefund) HasFreightInsurance() bool {
	return util.StrInArray(ORDER_TAGS_WITH_WECHAT_FREIGHT_INSURANCE, &o.Tags)
}

func (o *OrderRefund) CreateFreightInsuranceReturnId(ctx context.Context) {
	if !o.HasFreightInsurance() || o.RefundType != ORDER_REFUND_TYPE_RETURN_AND_REFUND {
		return
	}
	order, err := COrder.GetById(ctx, o.OrderId)
	if err != nil {
		return
	}
	setting, err := setting_model.CDeliverySetting.Get(ctx)
	if err != nil {
		return
	}
	returnAddress := setting.Logistics.ShipSetting.ReturnAddress
	var products []wechat_trade.FreightInsuranceProduct
	for _, refundProduct := range o.Products {
		products = append(products, wechat_trade.FreightInsuranceProduct{
			Name:     refundProduct.Name,
			ImageUrl: refundProduct.Picture,
		})
	}
	client := wechat_trade.NewWechatTradeClient(order.Channel.ChannelId, false)
	resp, err := client.FreightInsurance.CreateFreightInsuranceReturnId(ctx, &wechat_trade.CreateFreightInsuranceReturnIdRequest{
		OrderRefundId:          o.Id.Hex(),
		OpenId:                 order.Channel.OpenId,
		TradeNo:                order.TradeNo,
		OrderPathInMiniProgram: fmt.Sprintf(ORDER_DETAIL_PAGE, order.Id.Hex()),
		RefundAddress: wechat_trade.FreightInsuranceAddress{
			Country:  "中国",
			Province: returnAddress.Address.Province,
			City:     returnAddress.Address.City,
			Area:     returnAddress.Address.District,
			Detail:   returnAddress.Address.Detail,
			Name:     returnAddress.Name,
			Phone:    returnAddress.Tel,
		},
		ReceiverAddress: wechat_trade.FreightInsuranceAddress{
			Country:  "中国",
			Province: order.Contact.Address.Province,
			City:     order.Contact.Address.City,
			Area:     order.Contact.Address.District,
			Detail:   order.Contact.Address.Detail,
			Name:     order.Contact.Name,
			Phone:    order.Contact.Phone,
		},
		Products: products,
	})
	if err != nil {
		return
	}
	if len(o.Extra) == 0 {
		o.Extra = bson.M{}
	}
	o.Extra[WECHAT_FREIGHT_INSURANCE_EXTRA] = bson.M{
		"returnId": resp.ReturnId,
	}
}

func (o *OrderRefund) ClaimFreightInsurance(ctx context.Context) {
	if !o.HasFreightInsurance() {
		return
	}
	order, err := COrder.GetById(ctx, o.OrderId)
	if err != nil {
		return
	}
	client := wechat_trade.NewWechatTradeClient(order.Channel.ChannelId, false)
	detail, err := client.FreightInsurance.GetFreightInsuranceReturnDetail(ctx, o.GetFreightInsuranceReturnId())
	if err != nil {
		return
	}
	// 只有自行寄回的订单才需要理赔
	if detail.Status != wechat_trade.FREIGHT_INSURANCE_RETURN_STATUS_SELF {
		return
	}
	resp, err := client.FreightInsurance.ClaimFreightInsurance(ctx, &wechat_trade.ClaimFreightInsuranceRequest{
		OpenId:                order.Channel.OpenId,
		TradeNo:               order.TradeNo,
		RefundWaybillId:       o.Logistics.WaybillId,
		RefundDeliveryCompany: o.Logistics.DeliveryName,
	})
	if err != nil {
		log.Warn(ctx, "Claim freight insurance error", log.Fields{
			"refundId": o.Id,
			"orderId":  order.Id,
			"err":      err.Error(),
		})
		return
	}
	if len(o.Extra) == 0 {
		o.Extra = bson.M{}
	}
	extra := bson.M{
		"reportNo":     resp.ReportNo,
		"isHomePickUp": resp.IsHomePickUp,
	}
	if o.Extra[WECHAT_FREIGHT_INSURANCE_EXTRA] != nil {
		oldExtra, ok := o.Extra[WECHAT_FREIGHT_INSURANCE_EXTRA].(bson.M)
		if ok {
			maps.Copy(extra, oldExtra)
		}
	}
	o.Extra[WECHAT_FREIGHT_INSURANCE_EXTRA] = extra
}

func (o *OrderRefund) UnbindFreightInsuranceReturnId(ctx context.Context) {
	info := o.GetFreightInsuranceExtra()
	if info == nil {
		return
	}
	returnId := o.GetFreightInsuranceReturnId()
	if returnId == "" {
		return
	}
	client := wechat_trade.NewWechatTradeClient(o.ChannelId, false)
	_, err := client.FreightInsurance.UnbindFreightInsuranceReturnId(ctx, returnId)
	if err != nil {
		return
	}
	info["unbind"] = true
	o.Extra[WECHAT_FREIGHT_INSURANCE_EXTRA] = info
}

func (o *OrderRefund) GetFreightInsuranceExtra() bson.M {
	if len(o.Extra) == 0 {
		return nil
	}
	extra := o.Extra[WECHAT_FREIGHT_INSURANCE_EXTRA]
	if extra == nil {
		return nil
	}
	info, ok := extra.(bson.M)
	if !ok {
		return nil
	}
	return info
}

func (o *OrderRefund) GetFreightInsuranceReturnId() string {
	info := o.GetFreightInsuranceExtra()
	if info == nil {
		return ""
	}
	returnId := cast.ToString(info["returnId"])
	if returnId == "" {
		return ""
	}
	return returnId
}
