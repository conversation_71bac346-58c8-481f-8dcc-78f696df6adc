package order

import (
	"context"
	"encoding/json"
	"fmt"
	"mairpc/proto/common/request"
	"mairpc/proto/common/types"
	"mairpc/proto/coupon"
	pb_distribution "mairpc/proto/ec/distribution"
	"mairpc/proto/ec/mall"
	pb_ec_marketing "mairpc/proto/ec/marketing"
	pb_order "mairpc/proto/ec/order"
	"mairpc/proto/ec/store"
	account_model "mairpc/service/account/model"
	"mairpc/service/ec/client"
	ec_client "mairpc/service/ec/client"
	ec_model_product "mairpc/service/ec/model/product"
	ec_setting "mairpc/service/ec/model/setting"
	setting_model "mairpc/service/ec/model/setting"
	ec_store_warehouse "mairpc/service/ec/model/storeWarehouse"
	"mairpc/service/ec/service"
	mall_service "mairpc/service/ec/service/mall"
	optional_package "mairpc/service/eccampaign/model/optionalPackage"
	product_model "mairpc/service/product/model"
	share_model "mairpc/service/share/model"
	"math"
	"reflect"
	"strings"
	"time"

	uuid "github.com/satori/go.uuid"

	core_client "mairpc/core/client"
	core_component "mairpc/core/component"
	"mairpc/core/component/shouqianba"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	pb_account "mairpc/proto/account"
	proto_client "mairpc/proto/client"
	pb_store_warehouse "mairpc/proto/ec/storeWarehouse"
	ec_model "mairpc/service/ec/model"
	distribution_model "mairpc/service/ec/model/distribution"
	ec_distribution "mairpc/service/ec/model/distribution"
	ec_marketing "mairpc/service/ec/model/marketing"
	ec_model_notification "mairpc/service/ec/model/notification"
	profit_model "mairpc/service/ec/model/profitsharing"
	ec_model_setting "mairpc/service/ec/model/setting"
	ec_model_store "mairpc/service/ec/model/store"
	ec_service "mairpc/service/ec/service"
	"mairpc/service/ec/service/marketing"
	ec_share "mairpc/service/ec/share"
	"mairpc/service/share"
	"mairpc/service/share/component"
	"mairpc/service/share/util"

	"github.com/spf13/cast"
)

const (
	C_ORDER                      = "ec.order"
	ORDER_STATUS_UNPAID          = "unpaid"         // 待付款
	ORDER_STATUS_PAID            = "paid"           // 待接单
	ORDER_STATUS_UNASSIGNED      = "unassigned"     // 待分配
	ORDER_STATUS_ACCEPTED        = "accepted"       // 待提货/待发货
	ORDER_STATUS_SHIPPED         = "shipped"        // 已发货
	ORDER_STATUS_PARTIAL_SHIPPED = "partialShipped" // 部分发货
	ORDER_STATUS_COMPLETED       = "completed"      // 交易成功
	ORDER_STATUS_CANCELED        = "canceled"       // 交易关闭
	ORDER_STATUS_UNCOMMENTED     = "uncommented"    // 待评价，仅出现在接口，不落库

	ORDER_STATUS_DEPOSIT_PAID = "deposit_paid" // 定金已支付，用于 histories.status

	ORDER_DELIVERY_METHOD_PICKUP       = "pickup"      // 自提
	ORDER_DELIVERY_METHOD_EXPRESS      = "express"     // 快递
	ORDER_DELIVERY_METHOD_CITY_EXPRESS = "cityExpress" // 同城快送
	ORDER_DELIVERY_METHOD_NO_EXPRESS   = "noExpress"   // 无需快递

	ORDER_STATUS_PARTIAL_REFUNDED = "partialRefunded" // 部分退款，不体现在数据上，只用于区分操作的场景，如发通知时
	ORDER_REMARK_EXPIRED_CANCELED = "订单超时未付款"

	PRESENT_REFUND_STATUS_SHIPPED_AND_NOT_MATCH_RULES = "shippedAndNotMatchRules" // 赠品已发货，但不满足赠送条件，仅做标识不回退库存

	OPERATOR_SYSTEM = "system"

	// 用于区分特殊订单类型，其余类型均为 normal
	ORDER_TYPE_NORMAL                = "normal"
	ORDER_TYPE_VIRTUAL               = "virtual"
	ORDER_TYPE_COUPON                = "coupon"
	ORDER_TYPE_PICKUP                = "pickup"
	ORDER_SUB_TYPE_STORED_VALUE_CARD = "storedValueCard"

	// applicable product type
	APPLICABLE_PRODUCT_ALL       = "all"
	APPLICABLE_PRODUCT_INCLUDING = "byIncludedIds"
	APPLICABLE_PRODUCT_EXCLUDING = "byExcludedIds"

	COUPON_TYPE_CASH     = "cash"     // 代金券
	COUPON_TYPE_DISCOUNT = "discount" // 折扣券
	COUPON_TYPE_PRESENT  = "present"  // 买赠券
	COUPON_TYPE_DELIVERY = "delivery" // 运费券
	COUPON_TYPE_EXCHANGE = "exchange" // 兑换券

	COUPON_STATUS_UNUSED = "unused"

	PROFIT_SHARING_STATUS_PAID           = "paid"
	PROFIT_SHARING_STATUS_PENDING        = "pending"
	PROFIT_SHARING_STATUS_PROCESSING     = "processingCreateProfitSharingRecord"
	PROFIT_SHARING_STATUS_PROFIT_SHARING = "profitSharing"
	PROFIT_SHARING_STATUS_FINISHED       = "finished"
	PROFIT_SHARING_STATUS_REFUNDED       = "refunded"

	CREATE_PROFIT_SHARING_TIMEOUT = 30 // 单位：分钟
	ORDER_UNPAID_REMIND_DURATION  = 15 // 未支付订单过期前 15 分钟提醒

	EC_RETAIL_STORE_SMS_TOPIC = "群脉SCRM"

	DISTRIBUTION_STATUS_WAITING_PROFIT = "pending"      // 待结算分销订单
	DISTRIBUTION_STATUS_PROFIT_SHARING = "processing"   // 结算中
	DISTRIBUTION_STATUS_PROFITED       = "success"      // 已结算分销订单
	DISTRIBUTION_STATUS_NOCOMMISSION   = "noCommission" // 无佣金分销订单
	DISTRIBUTION_STATUS_FAILED         = "failed"       // 结算失败分销订单
	DISTRIBUTION_STATUS_REFUNDED       = "refunded"     // 结算前已退款

	OMS_ORDER_PUSH_STATUS_PENDING    = "pending"
	OMS_ORDER_PUSH_STATUS_PROCESSING = "processing"
	OMS_ORDER_PUSH_STATUS_PUSHED     = "pushed"
	OMS_ORDER_PUSH_STATUS_FAILED     = "failed"

	PROFIT_TYPE_PROFITSHARING = "profitSharing" // 分账
	PROFIT_TYPE_DISTRIBUTION  = "distribution"  // 分销
	PROFIT_TYPE_COMMISSION    = "commission"    // 分佣

	TIME_FORMAT = "2006-01-02 15:04:05"

	PAYMENT_WECHAT      = "wechat"
	PAYMENT_ALIPAY      = "alipay" // 支付宝支付
	PAYMENT_OFFLINE     = "offline"
	PAYMENT_PREPAIDCARD = "prepaidCard"

	// 活动类型
	CAMPAIGN_TYPE_PRESENT          = "presentCampaign"         // 买赠活动
	CAMPAIGN_TYPE_GROUPON          = "grouponCampaign"         // 拼团活动
	CAMPAIGN_TYPE_PACKAGE          = "packageCampaign"         // 促销活动
	CAMPAIGN_TYPE_PRESENT_COUPON   = "presentCoupon"           // 仅用于区分买赠券的赠品
	CAMPAIGN_TYPE_FLASH_SALE       = "flashSaleCampaign"       // 秒杀活动
	CAMPAIGN_TYPE_PRESELL          = "presellCampaign"         // 预售活动
	CAMPAIGN_TYPE_OPTIONAL_PACKAGE = "optionalPackageCampaign" // 任选打包活动
	CAMPAIGN_TYPE_PLUS_BUY         = "plusBuyCampaign"         // 超值换购
	CAMPAIGN_TYPE_PERIOD_BUY       = "periodBuyCampaign"       // 周期购活动
	CAMPAIGN_TYPE_BARGAIN          = "bargainCampaign"         // 砍价活动
	CAMPAIGN_TYPE_DISCOUNT         = "discountCampaign"        // 限时折扣
	CAMPAIGN_TYPE_RANDOM_DISCOUNT  = "randomDiscountCampaign"  // 随机立减

	OPTIONAL_PACKAGE_LADDER = "ladder" // 任选打包活动阶梯优惠

	TYPE_PRODUCT_CARD = "productCard" // 产品卡
	TYPE_REDEEM_CARD  = "redeemCard"  // 兑换卡

	// 折扣类型
	ORDER_DISCOUNT_TYPE_COUPON              = "coupon"              // 优惠券
	ORDER_DISCOUNT_TYPE_SCORE               = "score"               // 积分
	ORDER_DISCOUNT_TYPE_MEMBER              = "member"              // 会员折扣
	ORDER_DISCOUNT_TYPE_GROUPON             = "groupon"             // 拼团
	ORDER_DISCOUNT_TYPE_PRESENT             = "present"             // 买赠券
	ORDER_DISCOUNT_TYPE_PACKAGE             = "package"             // 促销套餐
	ORDER_DISCOUNT_TYPE_CAMPAIGN            = "campaign"            // 活动优惠
	ORDER_DISCOUNT_TYPE_DELIVERY            = "delivery"            // 运费券
	ORDER_DISCOUNT_TYPE_EXCHANGE            = "exchange"            // 兑换券
	ORDER_DISCOUNT_TYPE_ADJUSTMENT          = "adjustment"          // 商品改价
	ORDER_DISCOUNT_TYPE_DELIVERY_ADJUSTMENT = "delivery_adjustment" // 运费改价
	ORDER_DISCOUNT_TYPE_PERIOD_BUY          = "period_buy"          // 周期购子单优惠
	ORDER_DISCOUNT_TYPE_OPTIONAL_PACKAGE    = "optional_package"    // 任选打包活动优惠
	ORDER_DISCOUNT_TYPE_BENEFIT_CARD        = "benefit_card"        // 权益卡消费折扣
	ORDER_DISCOUNT_TYPE_REDEEM_CARD         = "redeem_card"         // 兑换卡
	ORDER_DISCOUNT_TYPE_DEPOSIT_EXPANSION   = "deposit_expansion"   // 预售活动定金膨胀
	ORDER_DISCOUNT_TYPE_PAYMENT_REDUCE      = "payment_reduce"      // 预售活动尾款立减优惠

	// 订单标签
	ORDER_TAGS_SELF_PURCHASE                 = "self_purchase"              // 自购订单
	ORDER_TAGS_DAIGOU                        = "daigou"                     // 代购订单
	ORDER_TAGS_SHARE                         = "share"                      // 分享订单
	ORDER_TAGS_WECHAT_SHOP                   = "wechatShop"                 // 视频号订单
	ORDER_TAGS_CONSIGNMENT                   = "consignment"                // 代销订单，只有脉盟、连锁零售商城使用
	ORDER_TAGS_DISTRIBUTION                  = "distribution"               // 经销订单，只有脉盟、连锁零售商城使用
	ORDER_TAGS_MAIMENG                       = "maimeng"                    // 标识脉盟订单，只有脉盟使用
	ORDER_TAGS_CHAIN_RETAIL                  = "chain_retail"               // 标识连锁零售商城订单，只有连锁零售商城使用
	ORDER_TAGS_BRAND_PREFIX                  = "brand_"                     // D0代销订单关联品牌 id，只有脉盟、连锁零售商城使用
	ORDER_TAGS_YEEPAY                        = "yeepay"                     // 使用易宝支付的订单
	ORDER_TAGS_SHOUQIANBA                    = "shouqianba"                 // 使用收钱吧支付的订单
	SPLIT_ORDER_TAG_ORIGIN_ORDER             = "splitOriginOrder"           // 被拆分的原始订单添加此 tag
	SPLIT_ORDER_TAG_SUB_ORDER                = "splitSubOrder"              // 拆分出来的子订单添加此 tag
	ORDER_TAGS_NOT_DIVIDE                    = "notDivide"                  // 不需要分账的订单添加此 tag
	ORDER_TAGS_SCAN_BUY                      = "scan_buy"                   // 扫码购订单
	ORDER_TAGS_PROXY_ORDER                   = "proxyOrder"                 // 代客下单订单
	ORDER_TAGS_BRAND_DIVIDE_TYPE_AFTER_PAID  = "brandDivideTypeAfterPaid"   // 品牌货款分账类型：支付后分账。连锁零售商城使用，存在该标签，表示该订单品牌货款在支付后会立即分账
	ORDER_TAGS_WITH_WECHAT_FREIGHT_INSURANCE = "withWechatFreightInsurance" // 订单包含微信运费险
	ORDER_TAGS_DIVIDE_COMPLETE               = "divideComplete"             // 分账完结

	VIRTUAL_ORDER_TYPE_COUPON  = "coupon"  // 付费卡券商品订单
	VIRTUAL_ORDER_TYPE_VIRTUAL = "virtual" // 虚拟商品订单
	VIRTUAL_ORDER_TYPE_PRODUCT = "product" // 实物商品订单

	PICKUP_CODE_STATUS_USED   = "used"
	PICKUP_CODE_STATUS_UNUSED = "unused"
	PICKUP_CODE_STATUS_REFUND = "refunded"

	MALL_RANKED_BY_AMOUNT = "totalAmount"
	MALL_RANKED_BY_MEMBER = "totalMember"
	MALL_RANKED_BY_ORDER  = "totalOrder"

	MALL_MEMBER_RANKED_BY_AMOUNT      = "amount"
	MALL_MEMBER_RANKED_BY_ORDER       = "order"
	MALL_MEMBER_RANKED_BY_AVERAGE     = "average"
	MALL_MEMBER_RANKED_BY_LAST_PAY_AT = "lastPayAt"

	// 发票状态
	INVOICE_STATUS_PENDING             = "pending"            // 待开票
	INVOICE_STATUS_INVOICING           = "invoicing"          // 开票中
	INVOICE_STATUS_ISSUED              = "issued"             // 已开票
	INVOICE_STATUS_FAILED              = "failed"             // 开票失败
	INVOICE_STATUS_CLOSED              = "closed"             // 已关闭
	INVOICE_STATUS_REBILLING           = "rebilling"          // 重新开票
	INVOICE_STATUS_REVERSING           = "reversing"          // 红字发票开具中
	INVOICE_STATUS_DELETED             = "deleted"            // 已退票
	INVOICE_STATUS_PARTIALLY_REVERSING = "partiallyReversing" // 部分冲红处理中
	INVOICE_STATUS_PARTIALLY_REVERSED  = "partiallyReversed"  // 部分冲红成功

	TYPE_NORMAL_UNPAID  = "normal_unpaid"  // 普通未支付订单
	TYPE_DEPOSIT_UNPAID = "deposit_unpaid" // 定金未支付订单
	TYPE_BALANCE_UNPAID = "balance_unpaid" // 定金已支付，尾款未支付

	BUSINESS_RETAIL_PICKUP_CODE = "retailPickupCode" // 用于零售提货码场景生成唯一编码

	MASK_TYPE_GROUPON = "groupon" // 屏蔽拼团订单（仅包含拼团中和拼团失败的）
	MASK_TYPE_UNMASK  = "unmask"  // 未屏蔽

	REDEEM_PERIOD_TIME_LIMIT    = "limit"
	REDEEM_PERIOD_TIME_NO_LIMIT = "nolimit"
	REDEEM_PERIOD_TIME_ABSOLUTE = "absolute"

	DELIVERY_FEE_TYPE_FREE_SHIPPING = "freeShipping"

	INVOICE_SETTING_UNSUPPORTED = "unsupported"
	INVOICE_SETTING_ONLINE      = "online"
	INVOICE_SETTING_OFFLINE     = "offline"

	INVOICE_TYPE_GENERAL = "general"

	ORDER_CANCEL_TYPE_EXPIRED   = "expire"
	ORDER_CANCEL_TYPE_BY_MEMBER = "member"
	ORDER_CANCEL_TYPE_BY_USER   = "user"

	PROMOTER_RECEIVER_TYPE_OPENID   = "OPENID"
	PROMOTER_RECEIVER_TYPE_MERCHANT = "MERCHANT_ID"

	PRODUCT_DISTRIBUTION_MODE_DEFAULT_PROPORTION = "defaultProportion"
	PRODUCT_DISTRIBUTION_MODE_CUSTOM_PROPORTION  = "customProportion"
	PRODUCT_DISTRIBUTION_MODE_CUSTOM_AMOUNT      = "customAmount"

	DISCOUNT_TYPE_CAMPAIGN     = "campaign"
	DISCOUNT_TYPE_MEMBER       = "member"
	DISCOUNT_TYPE_COUPON       = "coupon"
	DISCOUNT_TYPE_BENEFIT_CARD = "benefitCard"
	DISCOUNT_TYPE_PREPAID_CARD = "prepaidCard"
	DISCOUNT_TYPE_SCORE        = "score"
	DISCOUNT_TYPE_STORED_VALUE = "storedValue"
)

var (
	COrder                       = &Order{}
	ORDER_STATUS_TRANSLATION_MAP = map[string]string{
		ORDER_STATUS_UNPAID:     "待付款",
		ORDER_STATUS_PAID:       "待接单",
		ORDER_STATUS_UNASSIGNED: "待分配",
		ORDER_STATUS_ACCEPTED:   "待提货",
		ORDER_STATUS_COMPLETED:  "交易成功",
		ORDER_STATUS_CANCELED:   "交易关闭",
	}
	REFUND_LOGISTICS_FEE_STATUS = []string{
		ORDER_STATUS_PAID,
		ORDER_STATUS_UNASSIGNED,
		ORDER_STATUS_ACCEPTED,
	}
	CAN_REFOUND_ORDER_PAYMENT = []string{
		PAYMENT_WECHAT,
		PAYMENT_ALIPAY,
	}

	// 代销小店订单标签
	MALL_ORDER_TAGS = []string{
		ORDER_TAGS_SELF_PURCHASE,
		ORDER_TAGS_DAIGOU,
		ORDER_TAGS_SHARE,
	}

	// 使用易宝交易的订单标签
	YEEPAY_ORDER_TAGS = []string{
		ORDER_TAGS_MAIMENG,
		ORDER_TAGS_CHAIN_RETAIL,
	}

	// 当订单处于下列状态时退款不打印小票
	NOT_NEED_PRINT_REFUND_TICKET_ORDER_STATUS = []string{
		ORDER_STATUS_PAID,      // 待接单
		ORDER_STATUS_SHIPPED,   // 已发货
		ORDER_STATUS_COMPLETED, // 交易成功
	}
)

// 字段变更需同步到 ec/model/supplier 下的 SupplierOrder
type Order struct {
	Id                              bson.ObjectId           `bson:"_id,omitempty"`
	AccountId                       bson.ObjectId           `bson:"accountId"`
	MemberId                        bson.ObjectId           `bson:"memberId"`
	Number                          string                  `bson:"number"`
	Method                          string                  `bson:"method"`
	Operator                        string                  `bson:"operator"` // 门店发货、总部发货看此字段
	StoreId                         bson.ObjectId           `bson:"storeId"`
	TotalAmount                     uint64                  `bson:"totalAmount"`
	PayAmount                       uint64                  `bson:"payAmount"`
	PayScore                        uint64                  `bson:"payScore"`
	DeductAmountByScore             uint64                  `bson:"deductAmountByScore"`
	Payment                         string                  `bson:"payment,omitempty"`
	PickupCode                      string                  `bson:"pickupCode,omitempty"`
	PickupPassword                  string                  `bson:"pickupPassword,omitempty"`
	TradeNo                         string                  `bson:"tradeNo,omitempty"`
	PaidAt                          time.Time               `bson:"paidAt,omitempty"`
	CompletedAt                     time.Time               `bson:"completedAt,omitempty"`
	Status                          string                  `bson:"status"`
	RefundStatus                    string                  `bson:"refundStatus,omitempty"` // 订单中有商品发起过退款就设成 refunded
	ProfitSharingStatus             string                  `bson:"profitSharingStatus,omitempty"`
	MerchantId                      string                  `bson:"merchantId,omitempty"`
	Remarks                         string                  `bson:"remarks"`
	Message                         string                  `bson:"message,omitempty"`
	Contact                         OrderContact            `bson:"contact"`
	Reservation                     Reservation             `bson:"reservation,omitempty"`
	Logistics                       LogisticsInfo           `bson:"logistics,omitempty"`
	Discounts                       []OrderDiscount         `bson:"discounts"`
	Products                        []OrderProduct          `bson:"products"`
	Histories                       []OrderHistory          `bson:"histories"`
	Distribution                    DistributionDetail      `bson:"distribution,omitempty"`
	OmsProcessor                    OrderOmsProcessor       `bson:"omsProcessor,omitempty"`
	DistributorIds                  []bson.ObjectId         `bson:"distributorIds,omitempty"`
	UpdatedBy                       bson.ObjectId           `bson:"updatedBy,omitempty"`
	MemberDeleted                   bool                    `bson:"memberDeleted"`
	ProcessingCreateProfitSharingAt time.Time               `bson:"processingCreateProfitSharingAt,omitempty"`
	ProfitTypes                     []string                `bson:"profitTypes"`
	CreatedAt                       time.Time               `bson:"createdAt"`
	UpdatedAt                       time.Time               `bson:"updatedAt"`
	IsCommented                     bool                    `bson:"isCommented"`
	IsDeleted                       bool                    `bson:"isDeleted"`
	IsUnpaidReminded                bool                    `bson:"isUnpaidReminded"`
	Channel                         OrderChannel            `bson:"channel"`
	Campaigns                       []Campaign              `bson:"campaigns,omitempty"`
	Extra                           bson.M                  `bson:"extra,omitempty"`
	Tags                            []string                `bson:"tags,omitempty"`
	Utm                             Utm                     `bson:"utm,omitempty"`
	Invoice                         OrderInvoice            `bson:"invoice,omitempty"`            // 订单的发票相关信息
	Ticket                          OrderTicket             `bson:"ticket,omitempty"`             // 订单小票相关信息
	ProductAccessories              []OrderProductAccessory `bson:"productAccessories,omitempty"` // 商品附件
	MemberLevel                     uint64                  `bson:"memberLevel"`                  // 下单时客户会员等级
	PrepaidCards                    []OrderPrepaidCard      `bson:"prepaidCards,omitempty"`
	TradeRecords                    []TradeRecord           `bson:"tradeRecords,omitempty"`
	IsPrePaid                       bool                    `bson:"isPrePaid,omitempty"`
	IsFirstPaid                     bool                    `bson:"isFirstPaid"`
	OutTradeId                      bson.ObjectId           `bson:"outTradeId,omitempty"`
	MaskType                        string                  `bson:"maskType"`
	IsDeliveryFeeRefunded           bool                    `bson:"isDeliveryFeeRefunded"`
	MemberPaidCards                 []MemberPaidCard        `bson:"memberPaidCards,omitempty"`
	PromoterMemberIdForBinding      bson.ObjectId           `bson:"promoterMemberIdForBinding,omitempty"`
	StoredValue                     share_model.StoredValue `bson:"storedValue,omitempty"`
	PickupNumber                    string                  `bson:"pickupNumber,omitempty"` // 提货单号
	BoundStaffId                    bson.ObjectId           `bson:"boundStaffId,omitempty"` // 下单时的绑定导购
}

type MemberPaidCard struct {
	Id       bson.ObjectId `bson:"id"`
	Type     string        `bson:"type"`
	RecordId bson.ObjectId `bson:"recordId"`
	Number   string        `bson:"number"`
}

type TradeRecord struct {
	Id        bson.ObjectId `bson:"id"`
	TradeNo   string        `bson:"tradeNo"`
	Status    string        `bson:"status"`
	PayAmount uint64        `bson:"payAmount"`
	CreatedAt time.Time     `bson:"createdAt"`
	PaidAt    time.Time     `bson:"paidAt,omitempty"`
}

type Utm struct {
	UtmSource   string `bson:"utmSource"`
	UtmMedium   string `bson:"utmMedium"`
	UtmContent  string `bson:"utmContent"`
	UtmCampaign string `bson:"utmCampaign"`
	UtmTerm     string `bson:"utmTerm"`
}

type OrderOmsProcessor struct {
	PushStatus string    `bson:"pushStatus"`
	PushedAt   time.Time `bson:"pushedAt,omitempty"`
}

type OrderChannel struct {
	ChannelId string `bson:"channelId"`
	OpenId    string `bson:"openId"`
	Origin    string `bson:"origin"`
}

type OrderContact struct {
	Name    string         `bson:"name"`
	Phone   string         `bson:"phone"`
	Address ContactAddress `bson:"address"`
}

type ContactAddress struct {
	Province  string  `bson:"province"`
	City      string  `bson:"city"`
	District  string  `bson:"district"`
	Detail    string  `bson:"detail"`
	Longitude float64 `bson:"longitude"`
	Latitude  float64 `bson:"latitude"`
}

type Reservation struct {
	// 提货时间段开始的时间
	Time time.Time `bson:"time,omitempty"`
	// 客户提货时间
	MemberTime time.Time `bson:"memberTime,omitempty"`
	// 是否已发送通知
	HasSent bool `bson:"hasSent"`
}

type LogisticsInfo struct {
	Fee                   uint64    `bson:"fee"`
	ProcessBy             string    `bson:"processBy"`
	ShippedAt             time.Time `bson:"shippedAt,omitempty"`
	ExpectDeliveryAt      time.Time `bson:"expectDeliveryAt,omitempty"`
	ExpectDeliveryAtLabel string    `bson:"expectDeliveryAtLabel,omitempty"`
	FreeReason            string    `bson:"freeReason,omitempty"`
}

type OrderDiscount struct {
	Id              bson.ObjectId `bson:"id,omitempty"`
	Title           string        `bson:"title,omitempty"`
	Amount          int64         `bson:"amount"` // 折扣金额
	Type            string        `bson:"type"`   // 折扣类型，score：积分，coupon：优惠券，present：买赠券，member 会员折扣，deposit_expansion 定金膨胀，payment_reduce 尾款立减，product_card 产品卡抵扣
	Discount        uint64        `bson:"discount"`
	MemberLevel     uint64        `bson:"memberLevel"`
	MemberName      string        `bson:"memberName"`
	Picture         string        `bson:"picture"`
	CampaignType    string        `bson:"campaignType"` // 活动类型，当 type 为 campaign 时有意义
	Operator        string        `bson:"operator,omitempty"`
	OperatedAt      time.Time     `bson:"operatedAt,omitempty"`
	CouponCode      string        `bson:"couponCode,omitempty"`
	CouponType      string        `bson:"couponType,omitempty"`      // 优惠券类型
	SuitTotalAmount uint64        `bson:"suitTotalAmount,omitempty"` // 当前折扣适用总金额
	PriceType       string        `bson:"priceType"`                 // 消费折扣适用对象
}

type OrderProduct struct {
	Id                      bson.ObjectId             `bson:"id"`
	Name                    string                    `bson:"name"`
	Picture                 string                    `bson:"picture"`
	Total                   uint64                    `bson:"total"`
	RefundTotal             uint64                    `bson:"refundTotal"`
	Price                   uint64                    `bson:"price"`
	OriginPrice             uint64                    `bson:"originPrice"`
	TotalAmount             uint64                    `bson:"totalAmount"`
	PayAmount               uint64                    `bson:"payAmount"`
	PayScore                uint64                    `bson:"payScore"`
	Spec                    OrderProductSpec          `bson:"spec"`
	Discounts               []OrderDiscount           `bson:"discounts"`
	RefundStatus            string                    `bson:"refundStatus,omitempty"`
	OrderRefundId           bson.ObjectId             `bson:"orderRefundId,omitempty"`
	CategoryId              bson.ObjectId             `bson:"categoryId,omitempty"`
	ProductId               bson.ObjectId             `bson:"productId,omitempty"`
	Number                  string                    `bson:"number"`
	BarCode                 string                    `bson:"barCode,omitempty"`
	IsDistribution          bool                      `bson:"isDistribution"`
	IsDefaultProportion     bool                      `bson:"isdefaultproportion"` // 由于历史原因，该字段在 db 为小写
	DistributionMode        string                    `bson:"distributionMode"`
	DistributionProportion  float64                   `bson:"distributionProportion,omitempty"`
	DistributionAmount      uint64                    `bson:"distributionAmount,omitempty"`
	StoreDistributionAmount uint64                    `bson:"storeDistributionAmount,omitempty"` //  仅用于脉盟小店和连锁零售商城
	DistributionCoupon      DistributionCoupon        `bson:"distributionCoupon,omitempty"`
	OutTradeId              bson.ObjectId             `bson:"outTradeId,omitempty"` // 用于 OMS 订单标记唯一 ID
	Logistics               []ProductLogisticsInfo    `bson:"logistics,omitempty"`
	Campaigns               []Campaign                `bson:"campaigns,omitempty"` // 商品参与的活动
	IsCommented             bool                      `bson:"isCommented"`
	IsCouponEnabled         bool                      `bson:"isCouponEnabled,omitempty"`
	IsMemberDiscountEnabled bool                      `bson:"isMemberDiscountEnabled,omitempty"`
	DisableMemberRefund     bool                      `bson:"disableMemberRefund"`
	PickupCodes             []string                  `bson:"pickupCodes,omitempty"`
	Type                    string                    `bson:"type"`
	SubType                 string                    `bson:"subType,omitempty"`
	CouponId                bson.ObjectId             `bson:"couponId,omitempty"`
	StoredValueCardId       bson.ObjectId             `bson:"storedValueCardId,omitempty"`
	MembershipDiscountIds   []bson.ObjectId           `bson:"membershipDiscountIds,omitempty"`
	MemberPrepaidCardIds    []bson.ObjectId           `bson:"memberPrepaidCardIds,omitempty"` // 商品是储值卡时，记录发放给客户的储值卡的 id
	IsPresent               bool                      `bson:"isPresent"`
	RedeemPeriod            RedeemPeriod              `bson:"RedeemPeriod,omitempty"`
	PickupCodeRedeemPeriods []PickupCodeRedeemPeriod  `bson:"pickupCodeRedeemPeriods,omitempty"`
	IsFreeShipping          bool                      `bson:"isFreeShipping"`
	PrepaidCards            []OrderProductPrepaidCard `bson:"prepaidCards,omitempty"`
	OriginPayAmount         uint64                    `bson:"originPayAmount,omitempty"` // 记录迁移礼品卡前的商品 payAmount，避免迁移出现问题找不回原支付金额
	BrandId                 bson.ObjectId             `bson:"brandId,omitempty"`         // 品牌
	Tags                    []string                  `bson:"tags,omitempty"`            // 标签
	OriginTotal             uint64                    `bson:"originTotal"`               // 生成此 product 时，product 的数量。
	Source                  string                    `bson:"source,omitempty"`
	CampaignTags            []string                  `bson:"campaignTags,omitempty"`
	StoredValue             share_model.StoredValue   `bson:"storedValue,omitempty"`
	PresentDiscountCount    uint64                    `bson:"presentDiscountCount,omitempty"`  // 确认订单页面展示参与买赠活动的商品数量
	DiscountCampaignCount   uint64                    `bson:"discountCampaignCount,omitempty"` // 确认订单页面展示参与限时折扣活动的商品数量

	FreeShippingMethod       []string                       `bson:"-"`
	DisplayPrice             uint64                         `bson:"-"` // 确认订单页面展示价格
	CartId                   bson.ObjectId                  `bson:"-"`
	OriginalOutTradeId       bson.ObjectId                  `bson:"-"` // 用于标记拆分自哪个商品
	IsPrepaidCardDisabled    bool                           `bson:"-"`
	IsScoreDisabled          bool                           `bson:"-"`
	IsStoredValueDisabled    bool                           `bson:"-"`
	IsMemberPaidCardDisabled bool                           `bson:"-"`
	DisableReasonMap         map[string]string              `bson:"-"` // 记录优惠券会员折扣等无法使用的原因
	IsDiscountLimit          bool                           `bson:"-"`
	IsCampaignDisabled       bool                           `bson:"-"`
	CouponLimits             []share_model.CouponLimit      `bson:"-"`
	PrepaidCardLimits        []share_model.PrepaidCardLimit `bson:"-"`
}

type RedeemPeriod struct {
	Type    string    `bson:"type"`
	Days    uint32    `bson:"days"`
	StartAt time.Time `bson:"startAt"`
	EndAt   time.Time `bson:"endAt"`
}

type PickupCodeRedeemPeriod struct {
	Code    string    `bson:"code"`
	StartAt time.Time `bson:"startAt"`
	EndAt   time.Time `bson:"endAt"`
	Status  string    `bson:"status"`
}

type ProductLogisticsInfo struct {
	ProcessBy    LogisticsProcessBy `bson:"processBy"`
	DeliveryId   string             `bson:"deliveryId"`
	DeliveryName string             `bson:"deliveryName"`
	WaybillId    string             `bson:"waybillId"`
	ShippedAt    time.Time          `bson:"shippedAt,omitempty"`
	ProductCount uint64             `bson:"productCount"`
	DeliverBy    string             `bson:"deliverBy"`
	Courier      Courier            `bson:"courier,omitempty"`
	PickupCode   string             `bson:"pickupCode,omitempty"`
	Custom       string             `bson:"custom,omitempty"`
	// 仅用于自行配送时标记为当次发货内容，不保存到 db
	IsCurrentShipment bool `bson:"-"`
}

type Courier struct {
	Name  string `bson:"name"`
	Phone string `bson:"phone"`
}

type OrderHistory struct {
	StoreId   bson.ObjectId `bson:"storeId"`
	Extra     bson.M        `bson:"extra,omitempty"`
	Status    string        `bson:"status"`
	Remarks   string        `bson:"remarks"`
	Operator  string        `bson:"operator"`
	CreatedAt time.Time     `bson:"createdAt"`
}

type DistributionDetail struct {
	PromoterId              bson.ObjectId     `bson:"promoterId,omitempty"`
	ParentPromoterId        bson.ObjectId     `bson:"parentPromoterId,omitempty"`
	SubPromoterId           bson.ObjectId     `bson:"subPromoterId,omitempty"`
	PromoterType            string            `bson:"promoterType"`
	Amount                  uint64            `bson:"amount"`
	ProfitSharingType       int32             `bson:"profitSharingType"`
	ProfitSharedAt          time.Time         `bson:"profitSharedAt,omitempty"`
	ProfitSharingStatus     string            `bson:"profitSharingStatus"`
	ProfitFailedMsg         string            `bson:"profitFailedMsg,omitempty"`
	LastMonthProportion     float64           `bson:"lastMonthProportion,omitempty"`
	TransferBillId          bson.ObjectId     `bson:"transferBillId,omitempty"`          // 仅用于分销月结
	StoreDistributionAmount uint64            `bson:"storeDistributionAmount,omitempty"` //  仅用于脉盟小店和连锁零售商城
	Extra                   map[string]string `bson:"extra,omitempty"`
}

type DistributionCoupon struct {
	Id   bson.ObjectId `bson:"id"`
	Name string        `bson:"name"`
}

type OrderProductSpec struct {
	Sku         string                `bson:"sku"`
	Picture     string                `bson:"picture,omitempty"`
	Properties  []ProductSpecProperty `bson:"properties"`
	ExternalSku string                `bson:"externalSku,omitempty"`
	BarCode     string                `bson:"barCode,omitempty"`
	Weight      uint64                `bson:"weight,omitempty"`
}

type ProductSpecProperty struct {
	Id    string `bson:"id"`
	Value string `bson:"value"`
}

type Campaign struct {
	Id                 bson.ObjectId     `bson:"id,omitempty"`
	Title              string            `bson:"title"`
	Type               string            `bson:"type"`
	PresentLevel       uint64            `bson:"presentLevel,omitempty"` // 赠品等级
	DiscountId         bson.ObjectId     `bson:"discountId,omitempty"`
	GrouponRecordId    bson.ObjectId     `bson:"grouponRecordId,omitempty"` // 拼团记录id
	GrouponStatus      string            `bson:"grouponStatus,omitempty"`   // 拼团状态
	ProductIds         []bson.ObjectId   `bson:"productIds,omitempty"`
	GrouponRecordEndAt time.Time         `bson:"grouponRecordEndAt,omitempty"` // 拼团结束时间
	PackageId          bson.ObjectId     `bson:"packageId,omitempty"`          // 套餐 id
	Count              uint64            `bson:"count,omitempty"`              // 满减循环次数,任选打包参与次数
	RecordId           bson.ObjectId     `bson:"recordId,omitempty"`           // 活动记录 id : 周期购活动时为周期购订单 id
	Extra              map[string]string `bson:"extra,omitempty"`              // 额外参数
}

type RefundRecord struct {
	OrderRefundId bson.ObjectId `bson:"refundOrderId"`
	Count         uint64        `bson:"count"`
	RefundStatus  string        `bson:"refundStatus"`
	RefundAmount  uint64        `bson:"refundAmount"`
	PayAmount     uint64        `bson:"payAmount"` // 退款商品的实付金额
	RefundCodes   []string      `bson:"refundCode"`
	CreatedAt     time.Time     `bson:"createdAt"`
	UpdatedAt     time.Time     `bson:"updatedAt"`
}

type OrderInvoice struct {
	Needed         bool   `bson:"needed"`
	Status         string `bson:"status"`
	InvoiceSetting string `bson:"invoiceSetting"`
	Content        string `bson:"content,omitempty"` // 发票内容，json 字符串，代客下单使用
}

type OrderTicket struct {
	Needed bool `bson:"needed"`
}

type OrderProductAccessory struct {
	Id      bson.ObjectId `bson:"id"` // ec.productAccessory._id
	Name    string        `bson:"name"`
	Type    string        `bson:"type"`
	Message string        `bson:"message"` // 定制附件的提示信息
	Count   uint64        `bson:"count"`   // 附件数量
}

type OrderPrepaidCard struct {
	Id            bson.ObjectId `bson:"id"`            // ec.prepaidCard._id
	Number        string        `bson:"number"`        // 礼卡卡号
	Amount        uint64        `bson:"amount"`        // 抵扣金额
	TradeNo       string        `bson:"tradeNo"`       // 原始终端流水号
	OutTradeSeq   string        `bson:"outTradeSeq"`   // 第三方原始终端号
	TransactionId string        `bson:"transactionId"` // 第三方原始交易流水号
}

type OrderProductPrepaidCard struct {
	Id     bson.ObjectId `bson:"id"`     // ec.prepaidCard._id
	Number string        `bson:"number"` // 礼卡卡号
	Amount uint64        `bson:"amount"` // 抵扣金额
}

type ShipOrderByOmsHandler func(context.Context, *ShipOrder) ShipOrderByOmsResponse
type ShipOrder struct {
	OrderId                string
	DeliveryName           string
	WaybillId              string
	Products               []ShipOrderProduct
	IsPartialShip          bool     // 是否拆单发货
	PartialShipOutTradeIds []string // 拆单发货时才有值
	ShippedAt              time.Time
	Remarks                string
	Extra                  bson.M
}

type ShipOrderProduct struct {
	OutTradeId string
	Count      uint64
}

type ShipOrderByOmsResponse struct {
	Err             error
	IsOrderNotFound bool
}

type QueryShippedGoodsResponse struct {
	ShippedGoods []string
}

type QueryShippedGoodsRequest struct {
	OrderId string
}

func (o *Order) GetType() string {
	if t := o.GetVirtualType(); t != "" {
		return t
	}
	if o.Method == ORDER_DELIVERY_METHOD_PICKUP {
		return ORDER_TYPE_PICKUP
	}
	return ORDER_TYPE_NORMAL
}

func (o *Order) GetRealPayAmount() uint64 {
	payAmount := o.PayAmount
	for _, tradeRecord := range o.TradeRecords {
		payAmount += tradeRecord.PayAmount
	}
	return payAmount
}

// 获取虚拟商品订单类型
func (o *Order) GetVirtualType() string {
	if o.IsCouponType() {
		return ORDER_TYPE_COUPON
	}
	if o.IsVirtualType() {
		return ORDER_TYPE_VIRTUAL
	}
	return ""
}

// IsVirtualType 判断订单商品是否为虚拟商品
func (o *Order) IsVirtualType() bool {
	if len(o.Products) == 0 {
		return false
	}
	return o.Products[0].Type == ORDER_TYPE_VIRTUAL
}

func (o *Order) IsGrouponCampaignOrder() bool {
	return o.isCampaignExists(CAMPAIGN_TYPE_GROUPON)
}

func (o *Order) IsRedeemCardOrder() bool {
	// 兑换卡不能混合下单，所以活动信息中一定只有一条兑换卡的记录
	return len(o.Campaigns) > 0 && o.Campaigns[0].Type == TYPE_REDEEM_CARD
}

func (o *Order) IsPresellCampaignOrder() bool {
	return o.isCampaignExists(CAMPAIGN_TYPE_PRESELL)
}

func (o *Order) IsPeriodBuyCampaignOrder() bool {
	return o.isCampaignExists(CAMPAIGN_TYPE_PERIOD_BUY)
}

func (o *Order) isCampaignExists(campaignType string) bool {
	for _, campaign := range o.Campaigns {
		if campaign.Type == campaignType {
			return true
		}
	}
	return false
}

// IsCouponType 判断订单是否为付费卡券订单
func (o *Order) IsCouponType() bool {
	if len(o.Products) == 0 {
		return false
	}
	return o.Products[0].Type == ORDER_TYPE_COUPON
}

// HaveCouponProduct 判断订单商品中是否有付费卡券-优惠券
func (o *Order) HaveCouponProduct() bool {
	have := false
	if len(o.Products) == 0 {
		return false
	}
	for _, p := range o.Products {
		if p.Type == ORDER_TYPE_COUPON && p.SubType != ORDER_SUB_TYPE_STORED_VALUE_CARD {
			have = true
			break
		}
	}
	return have
}

// HaveStoredValueCardProduct 判断订单商品中是否有储值卡
func (o *Order) HaveStoredValueCardProduct() bool {
	have := false
	if len(o.Products) == 0 {
		return false
	}
	for _, p := range o.Products {
		if p.SubType == ORDER_SUB_TYPE_STORED_VALUE_CARD {
			have = true
			break
		}
	}
	return have
}

func (o *Order) IsVirtual() bool {
	return o.IsCouponType() || o.IsVirtualType()
}

func (o *Order) IsNotVirtualAndCouponProduct() bool {
	return !o.IsCouponType() && !o.IsVirtualType()
}

func (o *Order) IsTransferOrder() bool {
	if len(o.ProfitTypes) != 0 {
		return true
	}

	return false
}

func (o *Order) IsConsignment() bool {
	if len(o.Products) == 0 {
		return false
	}
	return o.Products[0].Source == product_model.PRODUCT_SOURCE_EC_CONSIGNMENT
}

func (o *Order) IsSplitSubOrder() bool {
	return util.StrInArray(SPLIT_ORDER_TAG_SUB_ORDER, &o.Tags) &&
		o.OutTradeId.Valid()
}

func (o *Order) IsSplitOriginOrder() bool {
	return util.StrInArray(SPLIT_ORDER_TAG_ORIGIN_ORDER, &o.Tags)
}

func (o *Order) Create(ctx context.Context) error {
	o.CreatedAt = time.Now()
	o.UpdatedAt = o.CreatedAt
	o.IsDeleted = false
	o.MemberDeleted = false
	_, err := extension.DBRepository.Insert(ctx, C_ORDER, *o)
	if err != nil {
		return err
	}
	o.SendOrderCreatedEvent(ctx, "")
	if o.IsYeepayForD2R() {
		resp, _ := ec_client.RetailerService.GetDistributionConfig(ctx, &request.EmptyRequest{})
		if resp != nil && resp.D0DistributorAccountId != "" {
			o.SendOrderCreatedEvent(ctx, resp.D0DistributorAccountId)
		}
	}
	core_component.GO(ctx, func(ctx context.Context) {
		account_model.SendAccountQuotaConsumeEvent(ctx, account_model.ACCOUNT_QUOTA_TYPE_ORDER, 1)
	})
	return nil
}

func (o *Order) CreateOnly(ctx context.Context) error {
	o.UpdatedAt = o.CreatedAt
	o.IsDeleted = false
	o.MemberDeleted = false
	_, err := extension.DBRepository.Insert(ctx, C_ORDER, *o)
	if err != nil {
		return err
	}
	return nil
}

func (*Order) Insert(ctx context.Context, orders []*Order) error {
	docs := []interface{}{}
	for i := range orders {
		docs = append(docs, orders[i])
	}
	_, err := extension.DBRepository.Insert(ctx, C_ORDER, docs...)
	return err
}

func (o *Order) AddTags(ctx context.Context, tags []string) error {
	condition := Common.GenDefaultConditionById(ctx, o.Id)
	updater := bson.M{
		"$addToSet": bson.M{
			"tags": bson.M{
				"$each": tags,
			},
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, condition, updater)
}

// 只更新状态，以及更新状态时附加的 histories 字段
func (o *Order) UpdateStatus(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"status":    o.Status,
			"updatedAt": time.Now(),
			"histories": o.Histories,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (*Order) DistinctIds(ctx context.Context, condition bson.M) ([]bson.ObjectId, error) {
	var ids []interface{}
	err := extension.DBRepository.Distinct(ctx, C_ORDER, condition, "_id", &ids)
	if err != nil {
		return nil, err
	}
	return core_util.ToObjectIdArray(ids), nil
}

// 核销虚拟商品后更新信息
func (o *Order) UpdateVirtualRedeemInfo(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"products":  o.Products,
			"updatedAt": time.Now(),
			"histories": o.Histories,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

// 更新订单发票状态
func (o *Order) UpdateInvoiceStatus(status string) {
	if o.Invoice.Needed {
		o.Invoice.Status = status
	}
}

// 更新发票信息
func (o *Order) UpdateInvoice(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"invoice":   o.Invoice,
			"updatedAt": time.Now(),
			"histories": o.Histories,
		},
	}
	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}
	return err
}

func BatchUpdateInvoiceByIds(ctx context.Context, ids []bson.ObjectId, invoice OrderInvoice) error {
	selector := share_model.Base.GenDefaultCondition(ctx)
	selector["_id"] = bson.M{"$in": ids}
	updater := bson.M{
		"$set": bson.M{
			"invoice":   invoice,
			"updatedAt": time.Now(),
		},
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_ORDER, selector, updater)
	return err
}

func (o *Order) UpdateOrderContact(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"number":    o.Number,
		"memberId":  o.MemberId,
	}
	updater := bson.M{
		"$set": bson.M{
			"contact":   o.Contact,
			"updatedAt": time.Now(),
		},
	}
	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}
	return nil
}

func (o *Order) Cancel(ctx context.Context) error {
	o.Status = ORDER_STATUS_CANCELED
	// 取消订单后将订单的发票状态改为已关闭
	selector := bson.M{
		"_id":       o.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"status":    o.Status,
			"invoice":   o.Invoice,
			"updatedAt": time.Now(),
			"histories": o.Histories,
		},
	}
	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}
	HandleOrderStatusChanged(ctx, o, ORDER_STATUS_CANCELED)
	return nil
}

func (o *Order) Accept(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": o.AccountId,
		"isDeleted": false,
	}
	// 接单后将发票状态更新为待开票
	updater := bson.M{
		"$set": bson.M{
			"status":         ORDER_STATUS_ACCEPTED,
			"pickupCode":     o.PickupCode,
			"pickupPassword": o.PickupPassword,
			"histories":      o.Histories,
			"invoice":        o.Invoice,
			"updatedAt":      time.Now(),
		},
	}
	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}

	HandleOrderStatusChanged(ctx, o, ORDER_STATUS_ACCEPTED)

	return nil
}

func (o *Order) Assign(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": o.AccountId,
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"storeId":        o.StoreId,
			"distributorIds": o.DistributorIds,
			"status":         ORDER_STATUS_PAID,
			"histories":      o.Histories,
			"updatedAt":      time.Now(),
		},
	}
	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}

	HandleOrderStatusChanged(ctx, o, ORDER_STATUS_PAID)

	return nil
}

func (o *Order) Complete(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": o.AccountId,
		"isDeleted": false,
	}
	o.CompletedAt = time.Now()
	setter := bson.M{
		"status":      ORDER_STATUS_COMPLETED,
		"histories":   o.Histories,
		"completedAt": o.CompletedAt,
		"updatedAt":   time.Now(),
	}
	if o.ProfitSharingStatus != "" {
		setter["profitSharingStatus"] = o.ProfitSharingStatus
	}

	updater := bson.M{
		"$set": setter,
	}

	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}

	isPickUpMallOrder, err := mall_service.IsPickUpMallOrder(ctx, o.StoreId.Hex())
	if err != nil {
		return err
	}

	req := &pb_store_warehouse.CreateStockBillRequest{
		ChangedAt:      time.Now().Format(time.RFC3339),
		Operator:       ec_store_warehouse.OPERATOR_STOCK_BILL_SHIP,
		BusinessNumber: o.Number,
		Type:           ec_store_warehouse.TYPE_STOCK_BILL_OUT,
	}
	products := []*pb_store_warehouse.StockBillProduct{}
	for _, product := range o.Products {
		var stock ec_model_product.StockOperator
		sku := product.Spec.Sku
		count := product.Total

		if IsInRefundProcess(product.RefundStatus) {
			continue
		}

		if isPickUpMallOrder {
			stock = mall_service.GenMallProductStockByStoreId(o.StoreId)
			err := stock.ReduceOccupiedStock(ctx, sku, int(count))
			if err != nil {
				return errors.NewNotExistsError("occupiedStock")
			}
			continue
		}

		switch o.Operator {
		case setting_model.DELIVERY_SETTING_OPERATOR_STAFF:
			if !ec_model.IsStoreProductStockEnabled(ctx) {
				continue
			}
			// 门店自提单才要在 complete 的时候生成销售出库记录，快递不在这里处理，而是在发货时处理。
			if o.Method != ORDER_DELIVERY_METHOD_PICKUP {
				continue
			}
			stock = ec_store_warehouse.CStoreProductStock
			ctx = context.WithValue(ctx, "storeId", o.StoreId)
			err := stock.ReduceOccupiedStock(ctx, sku, int(count))
			if err != nil {
				return errors.NewNotExistsError("occupiedStock")
			}
			temp := &pb_store_warehouse.StockBillProduct{
				Id:      product.Id.Hex(),
				Sku:     sku,
				Count:   count,
				StoreId: o.StoreId.Hex(),
			}
			products = append(products, temp)
		}
	}
	if len(products) > 0 {
		req.Products = products
		store, _ := ec_model_store.CStore.GetById(ctx, o.StoreId)
		stockStore := &pb_store_warehouse.UpdateStoreStock{
			Id:   o.StoreId.Hex(),
			Code: store.Code,
			Name: store.Name,
		}
		stores := []*pb_store_warehouse.UpdateStoreStock{}
		req.Stores = append(stores, stockStore)

		ec_client.StoreWarehouseService.CreateStoreStockBill(ctx, req)
	}
	HandleOrderStatusChanged(ctx, o, ORDER_STATUS_COMPLETED)
	o.SendOrderCompletedEvent(ctx)
	return nil
}

// Reject 拒单，状态更新成 unassigned
func (o *Order) Reject(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": o.AccountId,
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"status":    ORDER_STATUS_UNASSIGNED,
			"histories": o.Histories,
			"updatedAt": time.Now(),
		},
	}

	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}

	HandleOrderStatusChanged(ctx, o, ORDER_STATUS_UNASSIGNED)

	return nil
}

func (o *Order) PaidDeposit(ctx context.Context, tradeRecord TradeRecord) error {
	o.UpdatedAt = time.Now()
	selector := bson.M{
		"_id":             o.Id,
		"accountId":       o.AccountId,
		"isDeleted":       false,
		"tradeRecords.id": tradeRecord.Id,
	}
	updater := bson.M{
		"$set": bson.M{
			"updatedAt":      o.UpdatedAt,
			"tradeRecords.$": tradeRecord,
			"histories":      o.Histories,
		},
	}
	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}

	o.SendDeposidPaidEvent(ctx)

	return nil
}

func (o *Order) Paid(ctx context.Context) error {
	o.PaidAt = time.Now()
	o.UpdatedAt = time.Now()
	selector := bson.M{
		"_id":       o.Id,
		"accountId": o.AccountId,
		"isDeleted": false,
	}
	setter := bson.M{
		"merchantId": o.MerchantId,
		"status":     o.Status,
		"tradeNo":    o.TradeNo,
		"paidAt":     o.PaidAt,
		"invoice":    o.Invoice,
		"histories":  o.Histories,
		"updatedAt":  o.UpdatedAt,
		"extra":      o.Extra,
		"payment":    o.Payment,
	}
	if o.IsVirtualType() {
		setter["products"] = o.Products
	}
	if o.ProfitSharingStatus != "" {
		setter["profitSharingStatus"] = o.ProfitSharingStatus
	}
	if o.PickupCode != "" {
		setter["pickupCode"] = o.PickupCode
	}
	if o.PickupPassword != "" {
		setter["pickupPassword"] = o.PickupPassword
	}
	if IsFirstPaid(ctx, o.MemberId) {
		o.IsFirstPaid = true
		setter["isFirstPaid"] = o.IsFirstPaid
	}
	updater := bson.M{
		"$set": setter,
	}
	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}

	if o.Status == ORDER_STATUS_ACCEPTED {
		// 自动接单时触发一次 paid 的状态变更的处理
		HandleOrderStatusChanged(ctx, o, ORDER_STATUS_PAID)
	}

	HandleOrderStatusChanged(ctx, o, o.Status)

	// 订单付款之后生成参与记录，所以订单付款后发参与买赠活动事件
	// 商品购买时间需要有支付时间，因此在支付后有支付时间再发
	o.SendPurchaseEvent(ctx, "")
	// D2R 终端任务统计需要该事件，连锁零售商租户和小店租户需要给 D0 租户发送事件
	if o.IsYeepayForD2R() {
		resp, _ := ec_client.RetailerService.GetDistributionConfig(ctx, &request.EmptyRequest{})
		if resp != nil && resp.D0DistributorAccountId != "" {
			o.SendPurchaseEvent(ctx, resp.D0DistributorAccountId)
		}
	}
	o.SendPurchaseProductEvent(ctx)

	return nil
}

func (o *Order) OfflinePaid(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": o.AccountId,
		"payment":   o.Payment,
		"isDeleted": false,
	}
	setter := bson.M{
		"status":    o.Status,
		"paidAt":    time.Now(),
		"histories": o.Histories,
		"updatedAt": time.Now(),
	}

	updater := bson.M{
		"$set": setter,
	}
	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}

	HandleOrderStatusChanged(ctx, o, o.Status)

	return nil
}

func (o *Order) SetRemarks(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": o.AccountId,
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"remarks":   o.Remarks,
			"histories": o.Histories,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

// ShippedOffline 更新订单发货状态并发订单发货事件
func (o *Order) ShippedOffline(ctx context.Context, waybillIds []string) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": o.AccountId,
		"isDeleted": false,
	}

	updater := bson.M{
		"$set": bson.M{
			"products":  o.Products,
			"status":    o.Status,
			"logistics": o.Logistics,
			"histories": o.Histories,
			"updatedAt": time.Now(),
		},
	}
	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}

	core_component.GO(ctx, func(ctx context.Context) {
		o.SendOrderShipEvents(ctx, waybillIds)
		proto_client.GetEcWechatShopServiceClient().UploadOrderShippingInfoToWechat(ctx, &request.DetailRequest{
			Id: o.Id.Hex(),
		})
		CreateWechatFreightInsuranceOrder(ctx, o, waybillIds)
	})
	return nil
}

func (o *Order) ShippedOnline(ctx context.Context) error {
	channelId := o.Channel.ChannelId
	if channelId == "" {
		return errors.NewInvalidArgumentErrorWithMessage("channelId", "Order doesn't record channelId")
	}

	data, err := genExpressOrderRequest(ctx, o)
	if err != nil {
		return err
	}

	resp, _ := component.WeConnect.AddExpressOrder(ctx, channelId, data)
	if resp != nil {
		core_component.GO(ctx, func(ctx context.Context) {
			o.SendOrderShipEvents(ctx, []string{resp.WaybillId})
			proto_client.GetEcWechatShopServiceClient().UploadOrderShippingInfoToWechat(ctx, &request.DetailRequest{
				Id: o.Id.Hex(),
			})
			CreateWechatFreightInsuranceOrder(ctx, o, []string{resp.WaybillId})
		})
	}
	// TODO:@robin.cai 更新订单中物流信息 processBy:wechat
	return nil
}

func (o *Order) ShipVirtualProduct(ctx context.Context) error {
	condition := Common.GenDefaultConditionById(ctx, o.Id)
	updater := bson.M{
		"$set": bson.M{
			"products":  o.Products,
			"status":    o.Status,
			"histories": o.Histories,
			"updatedAt": time.Now(),
		},
	}
	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, condition, updater)
	if err != nil {
		return err
	}
	HandleOrderStatusChanged(ctx, o, ORDER_STATUS_ACCEPTED)
	proto_client.GetEcWechatShopServiceClient().UploadOrderShippingInfoToWechat(ctx, &request.DetailRequest{
		Id: o.Id.Hex(),
	})
	return nil
}

func (o *Order) getDeliverySender(ctx context.Context) (component.OrderContact, error) {
	sender := component.OrderContact{}
	if o.Operator == ec_model_setting.DELIVERY_SETTING_OPERATOR_STAFF {
		store, _ := ec_model_store.CStore.GetById(ctx, o.StoreId)
		if store.Id.Hex() == "" || !store.DeliverySetting.IsEnabled {
			return sender, errors.NewNotExistsError("storeId")
		}
		sender = component.OrderContact{
			Name:     store.DeliverySetting.ContactName,
			Tel:      store.DeliverySetting.ContactTel,
			Province: store.Location.Province,
			City:     store.Location.City,
			Area:     store.Location.District,
			Address:  store.Location.Name,
		}
	} else {
		setting, err := ec_model_setting.CDeliverySetting.Get(ctx)
		if err != nil || setting == nil ||
			setting.Logistics.ShipSetting.SenderAddress.Name == "" {
			return sender, errors.NewNotExistsError("deliverySetting")
		}
		sender = component.OrderContact{
			Name:     setting.Logistics.ShipSetting.SenderAddress.Name,
			Tel:      setting.Logistics.ShipSetting.SenderAddress.Tel,
			Province: setting.Logistics.ShipSetting.SenderAddress.Address.Province,
			City:     setting.Logistics.ShipSetting.SenderAddress.Address.City,
			Area:     setting.Logistics.ShipSetting.SenderAddress.Address.District,
			Address:  setting.Logistics.ShipSetting.SenderAddress.Address.Detail,
		}
	}

	return sender, nil
}

func genExpressOrderRequest(ctx context.Context, o *Order) (*component.AddExpressOrderRequest, error) {
	sender, err := o.getDeliverySender(ctx)
	if err != nil {
		return nil, err
	}

	service := component.DeliveryService{
		// TODO:@robin.cai 根据 channelId 获取可用的物流
		// TODO:@robin.cai 各物流公司的标准/普通快递代码不一样，暂时取第一个
		ServiceType: 0,
		ServiceName: "",
	}
	result := &component.AddExpressOrderRequest{
		AddSource:  0, // 小程序订单用 0，app/h5 用 2
		OrderId:    o.Id.Hex(),
		OpenId:     o.Channel.OpenId,
		DeliveryId: "", // TODO:@robin.cai DeliveryId
		BizId:      "", // TODO:@robin.cai 传什么
		Sender:     sender,
		Receiver: component.OrderContact{
			Name:     o.Contact.Name,
			Mobile:   o.Contact.Phone,
			Province: o.Contact.Address.Province,
			City:     o.Contact.Address.City,
			Area:     o.Contact.Address.District,
			Address:  o.Contact.Address.Detail,
		},
		Cargo: component.OrderCargo{ // TODO:@robin.cai
			Count:  1,
			Weight: 1,
			SpaceX: 1,
			SpaceY: 1,
			SpaceZ: 1,
			DetailList: []component.OrderCargoDetail{
				{
					Name:  "",
					Count: 1,
				},
			},
		},
		Insured: component.OrderInsured{
			UseInsured: 0, // 不保价
		},
		Service: service,
	}

	return result, nil
}

func (*Order) DeleteByMember(ctx context.Context, orderId, memberId bson.ObjectId) error {
	selector := bson.M{
		"_id":       orderId,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"memberId":  memberId,
		"status": bson.M{
			"$in": []string{ORDER_STATUS_COMPLETED, ORDER_STATUS_CANCELED},
		},
		"memberDeleted": false,
		"isDeleted":     false,
	}
	updater := bson.M{
		"$set": bson.M{
			"memberDeleted": true,
			"updatedAt":     time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

// 发起退款时，更新订单部分数据
func (o *Order) UpdateWhenRefund(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	setter := bson.M{
		"products":              o.Products,
		"status":                o.Status,
		"histories":             o.Histories,
		"refundStatus":          ORDER_REFUND_STATUS_REFUNDED,
		"tradeRecords":          o.TradeRecords,
		"isDeliveryFeeRefunded": o.IsDeliveryFeeRefunded,
		"updatedAt":             time.Now(),
	}
	if o.ProfitSharingStatus != "" {
		setter["profitSharingStatus"] = o.ProfitSharingStatus
	}

	updater := bson.M{
		"$set": setter,
	}

	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (o *Order) UpdateIsDeliveryFeeRefunded(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	setter := bson.M{
		"isDeliveryFeeRefunded": o.IsDeliveryFeeRefunded,
		"updatedAt":             time.Now(),
	}
	updater := bson.M{
		"$set": setter,
	}

	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

// 发起退款时，更新参与买赠活动订单中的赠品退款状态为 refunded
func (o *Order) UpdatePresentStatusAndCampaigns(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"status":    o.Status,
	}
	setter := bson.M{
		"products":  o.Products,
		"campaigns": o.Campaigns,
		"updatedAt": time.Now(),
	}

	updater := bson.M{
		"$set": setter,
	}

	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}
	return CLogistics.UpdateProductRefundStatusByOrder(ctx, o)
}

func (o *Order) UpdateCampaigns(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"status":    o.Status,
	}
	setter := bson.M{
		"campaigns": o.Campaigns,
		"updatedAt": time.Now(),
	}

	updater := bson.M{
		"$set": setter,
	}

	err := extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}
	return nil
}

func (Order) GetById(ctx context.Context, id bson.ObjectId) (Order, error) {
	order := Order{}
	selector := Common.GenDefaultConditionById(ctx, id)
	err := Common.GetOneByCondition(ctx, selector, C_ORDER, &order)
	return order, err
}

func (Order) GetByOutTradeId(ctx context.Context, id bson.ObjectId) (Order, error) {
	order := Order{}
	selector := Common.GenDefaultCondition(ctx)
	selector["outTradeId"] = id
	err := Common.GetOneByCondition(ctx, selector, C_ORDER, &order)
	return order, err
}

func (Order) GetByIds(ctx context.Context, ids []bson.ObjectId) ([]Order, error) {
	orders := []Order{}
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = bson.M{"$in": ids}
	err := extension.DBRepository.FindAll(ctx, C_ORDER, selector, []string{}, 0, &orders)
	return orders, err
}

func (Order) GetByNumber(ctx context.Context, number string) (*Order, error) {
	order := Order{}
	selector := Common.GenDefaultCondition(ctx)
	selector["number"] = number
	err := Common.GetOneByCondition(ctx, selector, C_ORDER, &order)
	if err != nil {
		return nil, err
	}
	return &order, err
}

func (Order) GetByTradeNo(ctx context.Context, tradeNo string) (*Order, error) {
	order := Order{}
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"tradeNo":   tradeNo,
		"isDeleted": false,
	}

	err := Common.GetOneByCondition(ctx, selector, C_ORDER, &order)
	if err != nil {
		return nil, err
	}

	return &order, nil
}

func (Order) GetAllByTransferBillIdAndPromoterType(ctx context.Context, transferBillId bson.ObjectId, promoterType string) ([]Order, error) {
	orders := []Order{}
	selector := bson.M{
		"accountId":                   util.GetAccountIdAsObjectId(ctx),
		"distribution.transferBillId": transferBillId,
		"distribution.promoterType":   promoterType,
		"isDeleted":                   false,
	}

	_, err := Common.GetAllByCondition(ctx, selector, []string{}, 0, C_ORDER, &orders)
	if err != nil {
		return nil, err
	}

	return orders, nil
}

func (Order) GetAllByTransferBillIds(ctx context.Context, transferBillIds []bson.ObjectId) ([]Order, error) {
	orders := []Order{}
	selector := bson.M{
		"accountId":                   util.GetAccountIdAsObjectId(ctx),
		"distribution.transferBillId": bson.M{"$in": transferBillIds},
		"isDeleted":                   false,
	}

	_, err := Common.GetAllByCondition(ctx, selector, []string{}, 0, C_ORDER, &orders)
	if err != nil {
		return nil, err
	}

	return orders, nil
}

func (Order) CountByPromoterIdAndTransferBillId(ctx context.Context, transferBillId, promoterId bson.ObjectId) uint64 {
	selector := Common.GenDefaultCondition(ctx)
	selector["distribution.promoterId"] = promoterId
	selector["distribution.transferBillId"] = transferBillId
	return Common.CountByCondition(ctx, C_ORDER, selector)
}

func (Order) GetByPickupCode(ctx context.Context, pickupCode string) (*Order, error) {
	order := &Order{}
	selector := Common.GenDefaultCondition(ctx)
	selector["pickupCode"] = pickupCode
	err := Common.GetOneByCondition(ctx, selector, C_ORDER, &order)
	return order, err
}

func (Order) GetByPickupPassword(ctx context.Context, pickupPassword, storeId string) (*Order, error) {
	order := &Order{}
	selector := Common.GenDefaultCondition(ctx)
	selector["status"] = ORDER_STATUS_ACCEPTED
	selector["pickupPassword"] = pickupPassword
	if storeId != "" {
		selector["storeId"] = bson.ObjectIdHex(storeId)
	}
	err := Common.GetOneByCondition(ctx, selector, C_ORDER, &order)
	return order, err
}

func (Order) CheckVirtualOrderPickupCodesExists(ctx context.Context, pickupCodes []string) bool {
	selector := bson.M{}
	accountId := util.GetAccountIdAsObjectId(ctx)
	selector["$or"] = []bson.M{
		{
			"accountId":  accountId,
			"pickupCode": bson.M{"$in": pickupCodes},
			"isDeleted":  false,
		},
		{
			"accountId":            accountId,
			"products.pickupCodes": bson.M{"$in": pickupCodes},
			"isDeleted":            false,
		},
	}
	return Common.Exist(ctx, C_ORDER, selector)
}

func (Order) GetByProductPickupCode(ctx context.Context, pickupCode string) (*Order, error) {
	order := &Order{}
	selector := Common.GenDefaultCondition(ctx)
	selector["products.pickupCodes"] = pickupCode
	err := Common.GetOneByCondition(ctx, selector, C_ORDER, &order)
	return order, err
}

func (*Order) GetNotRemindedUnpaidOrders(ctx context.Context, accountId bson.ObjectId, t time.Time, tags []string) ([]Order, error) {
	orders := []Order{}
	selector := bson.M{
		"accountId":        accountId,
		"status":           ORDER_STATUS_UNPAID,
		"isUnpaidReminded": false,
		"createdAt":        bson.M{"$lt": t},
		"isDeleted":        false,
	}
	if len(tags) > 0 {
		selector["tags"] = bson.M{"$in": tags}
	}

	err := extension.DBRepository.FindAll(ctx, C_ORDER, selector, []string{}, 0, &orders)
	if err != nil {
		return nil, err
	}

	return orders, nil
}

func (*Order) GetExpiredOrdersByAccountId(ctx context.Context, accountId bson.ObjectId, t time.Time, payments []string, tags []string) ([]Order, error) {
	orders := []Order{}
	selector := bson.M{
		"accountId": accountId,
		"payment": bson.M{
			"$in": payments,
		},
		"status":    ORDER_STATUS_UNPAID,
		"createdAt": bson.M{"$lt": t},
		"isDeleted": false,
	}
	if len(tags) > 0 {
		selector["tags"] = bson.M{"$in": tags}
	}

	err := extension.DBRepository.FindAll(ctx, C_ORDER, selector, []string{}, GET_ORDERS_LIMIT, &orders)
	if err != nil {
		return nil, err
	}

	return orders, nil
}

func (Order) GetByCondition(ctx context.Context, condition bson.M) ([]Order, error) {
	orders := []Order{}
	err := extension.DBRepository.FindAll(ctx, C_ORDER, condition, []string{}, 0, &orders)
	return orders, err
}

func (Order) GetSubOrdersByOutTradeId(ctx context.Context, outTradeId bson.ObjectId) ([]Order, error) {
	condition := bson.M{
		"accountId":  util.GetAccountIdAsObjectId(ctx),
		"isDeleted":  false,
		"outTradeId": outTradeId,
		"tags":       SPLIT_ORDER_TAG_SUB_ORDER,
	}
	return COrder.GetByCondition(ctx, condition)
}

func (Order) GetOriginOrderByOutTradeId(ctx context.Context, outTradeId bson.ObjectId) (*Order, error) {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"tags":      SPLIT_ORDER_TAG_ORIGIN_ORDER,
		"$or": []bson.M{
			{
				"outTradeId": outTradeId,
			},
			{
				"_id": outTradeId,
			},
		},
	}
	order := &Order{}
	err := extension.DBRepository.FindOne(ctx, C_ORDER, condition, order)
	if err != nil {
		return nil, err
	}
	return order, nil
}

func (o *Order) Expired(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": o.AccountId,
		"isDeleted": false,
		"status":    ORDER_STATUS_UNPAID,
	}

	newHistory := OrderHistory{
		StoreId:   o.StoreId,
		Status:    ORDER_STATUS_CANCELED,
		Remarks:   ORDER_REMARK_EXPIRED_CANCELED,
		Operator:  OPERATOR_SYSTEM,
		CreatedAt: time.Now(),
	}

	o.Histories = append(o.Histories, newHistory)

	setter := bson.M{
		"status":    ORDER_STATUS_CANCELED,
		"updatedAt": time.Now(),
		"histories": o.Histories,
	}

	// 月结的情况下，订单过期时设置分销状态为无佣金
	if o.Distribution.ProfitSharingType == ec_model_setting.COMMMISSION_TYPE_MONTHLY &&
		o.Distribution.ProfitSharingStatus == PROFIT_SHARING_STATUS_PENDING {
		o.Distribution.ProfitSharingStatus = DISTRIBUTION_STATUS_NOCOMMISSION
		o.Distribution.Extra = map[string]string{
			"无分佣原因": "参与分佣金额为 0",
		}
		setter["distribution.profitSharingStatus"] = DISTRIBUTION_STATUS_NOCOMMISSION
		setter["distribution.extra.无分佣原因"] = "参与分佣金额为 0"
	}

	updater := bson.M{
		"$set": setter,
	}

	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (o *Order) MarkUnpaidReminded(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": o.AccountId,
	}
	updater := bson.M{
		"$set": bson.M{
			"isUnpaidReminded": true,
			"updatedAt":        time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (*Order) Iterate(ctx context.Context, selector bson.M, sorter []string) (extension.IterWrapper, error) {
	it, err := extension.DBRepository.Iterate(ctx, C_ORDER, selector, sorter)
	return it, err
}

type ProductSkuRefundStatus struct {
	OrderRefundId bson.ObjectId
	OutTradeId    bson.ObjectId
	Status        string
}

// UpdateProductRefundStatus 更新 order.Products[].RefundStatus
func (*Order) UpdateProductRefundStatus(ctx context.Context, orderId bson.ObjectId, refundedResult []ProductSkuRefundStatus) error {
	order, err := COrder.GetById(ctx, orderId)
	if err != nil {
		return err
	}

	for index, product := range order.Products {
		for _, result := range refundedResult {
			if product.OutTradeId != result.OutTradeId {
				continue
			}
			product.RefundStatus = result.Status
			order.Products[index] = product
			break
		}
	}

	selector := bson.M{
		"_id":       order.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"products":  order.Products,
			"updatedAt": time.Now(),
		},
	}

	err = extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}
	return CLogistics.UpdateProductRefundStatusByOrder(ctx, &order)
}

func (o *Order) UpdateDistributionStatus(ctx context.Context, id, status, failureMsg string, distributionExtra map[string]string, successTime time.Time) error {
	total, err := o.UpdateAllDistributionStatus(ctx, []bson.ObjectId{bson.ObjectIdHex(id)}, status, failureMsg, distributionExtra, successTime)
	if err != nil {
		return err
	}

	if total == 0 {
		return bson.ErrNotFound
	}
	return nil
}

func (o *Order) UpdateDistributionAmount(ctx context.Context, amount uint64) error {
	condition := Common.GenDefaultConditionById(ctx, o.Id)
	updater := bson.M{
		"$set": bson.M{
			"distribution.amount": amount,
			"updatedAt":           time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, condition, updater)
}

func (o *Order) UpdateAllDistributionStatus(ctx context.Context, ids []bson.ObjectId, status, failureMsg string, distributionExtra map[string]string, successTime time.Time) (int, error) {
	selector := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}

	setter := bson.M{
		"distribution.profitSharingStatus": status,
		"updatedAt":                        time.Now(),
	}
	if status == PROFIT_SHARING_RESULT_SUCCESS && successTime.Unix() > 0 {
		setter["distribution.profitSharedAt"] = successTime
	}
	if failureMsg != "" {
		setter["distribution.profitFailedMsg"] = failureMsg
	}
	if status == DISTRIBUTION_STATUS_NOCOMMISSION {
		setter["distribution.amount"] = 0
	}
	if distributionExtra != nil {
		if o.Distribution.Extra == nil {
			o.Distribution.Extra = map[string]string{}
		}
		for k, v := range distributionExtra {
			o.Distribution.Extra[k] = v
		}
		setter["distribution.extra"] = o.Distribution.Extra
	}

	updater := bson.M{
		"$set": setter,
	}

	return extension.DBRepository.UpdateAll(ctx, C_ORDER, selector, updater)
}

func (*Order) GetNeedCreateProfitSharingRecordOrders(ctx context.Context) ([]Order, error) {
	duration, err := time.ParseDuration("-" + cast.ToString(CREATE_PROFIT_SHARING_TIMEOUT) + "m")
	if err != nil {
		return nil, err
	}

	selector := bson.M{}
	selector["$or"] = []bson.M{
		{
			"profitSharingStatus": PROFIT_SHARING_STATUS_PENDING,
		},
		{
			"profitSharingStatus": PROFIT_SHARING_STATUS_PROCESSING,
			"processingCreateProfitSharingAt": bson.M{
				"$lt": time.Now().Add(duration),
			},
		},
	}

	orders := []Order{}
	err = extension.DBRepository.FindAll(ctx, C_ORDER, selector, []string{}, GET_ORDERS_LIMIT, &orders)
	if err != nil {
		return nil, err
	}

	return orders, nil
}

func (o *Order) FinishProfitSharing(ctx context.Context) error {
	// 此处 outOrderNo 应该为随机生成的唯一 Id
	// 使用 orderId 是为了退款时能够判断是否发起过完结分账
	// 一个订单只会发起一次完结分账，使用 orderId 可以认为唯一
	finishProfitSharingRequest := &component.FinishProfitSharingRequest{
		OutOrderNo:    o.Id.Hex(),
		ChannelId:     o.Channel.ChannelId,
		TransactionId: o.TradeNo,
		Finished:      true,
		Description:   FINISH_PROFIT_SHARING_DESCRIPTION,
	}

	channel, _ := proto_client.GetAccountServiceClient().GetChannel(ctx, &pb_account.ChannelDetailRequest{
		ChannelId: o.Channel.ChannelId,
	})
	if channel != nil {
		finishProfitSharingRequest.AppId = channel.AppId
	}
	_, err := component.WeConnect.FinishProfitSharing(ctx, "WECHAT_APP", finishProfitSharingRequest)

	return err
}

func (o *Order) NotifyCustomer(ctx context.Context, typ string, basicPlaceholderValueMap map[string]string) (err error) {
	defer func() {
		if err != nil {
			log.Warn(ctx, "Failed to send notification to customer", log.Fields{
				"orderId":      o.Id.Hex(),
				"type":         typ,
				"errorMessage": err.Error(),
			})
		}
	}()
	result, err := GetCustomerNotificationSetting(ctx, typ)
	if err != nil {
		return err
	}
	notificationSetting := result.NotificationSetting
	memberDetail, err := ec_share.GetMemberById(ctx, o.MemberId, []string{})
	if err != nil {
		return err
	}
	store := ec_model_store.Store{}
	if o.StoreId.Valid() {
		store, err = ec_model_store.CStore.GetById(ctx, o.StoreId)
		if err != nil {
			return err
		}
	}
	placeholderValueMap := GetReplacePlaceholderValueMap(basicPlaceholderValueMap, memberDetail, o, &store, notificationSetting)
	// 发订阅消息
	if util.StrInArray(typ, &STATUS_NEED_SEND_SUBSCRIBE_MSG) {
		if o.Channel.Origin == "weapp" && len(notificationSetting.SubscribeMessage.Data) > 0 {
			// 发送微信订阅消息
			SendCustomerSubscribeMsg(ctx, notificationSetting, placeholderValueMap, o, memberDetail)
		}
		if o.Channel.Origin == "bytedanceapp" && len(notificationSetting.BytedanceSubscribeMessage.Data) > 0 {
			// 发送抖音订阅消息
			SendBytedanceSubscribeMsg(ctx, notificationSetting, placeholderValueMap, o)
		}
	}

	// 手机号为空就不发短信了
	if memberDetail.Phone == "" {
		return nil
	}
	phone := memberDetail.Phone
	// 未启用不发短信
	if notificationSetting.Text.Enabled == false {
		return nil
	}
	text := notificationSetting.Text.Content
	// 替换短信占位符
	text = ec_share.FormatText(text, placeholderValueMap)

	businessName := uuid.NewV4().String()
	req := &pb_account.SendSmsRequest{
		Phone:        phone,
		Text:         text,
		SmsTopic:     share.GetAccountSmsTopic(ctx),
		Operator:     OPERATOR_SYSTEM,
		BusinessId:   o.Id.Hex(),
		BusinessName: businessName,
		BusinessType: "ec",
		MemberId:     o.MemberId.Hex(),
		MessageType:  pb_account.SendSmsRequest_NOTICE,
	}
	if notificationSetting.Text.SmsTopic != "" {
		req.SmsTopic = notificationSetting.Text.SmsTopic
	}

	if notificationSetting.Text.AliyunqaSms != nil && notificationSetting.Text.AliyunqaSms.TemplateId != "" {
		req = ec_model.GenSendAliyunqaSmsRequest(ctx, memberDetail, notificationSetting, placeholderValueMap)
		if req == nil {
			return nil
		}
	}

	return func() error {
		logReq := &pb_account.CreateMessageNotificationLogRequest{
			NotificationSettingId: notificationSetting.Id,
			Type:                  share_model.MESSAGE_NOTIFICATION_LOG_TYPE_SMS,
			Status:                share_model.MESSAGE_NOTIFICATION_LOG_STATUS_PROCESSING,
			MemberInfo: &pb_account.NotificationMember{
				MemberId: memberDetail.Id,
				Phone:    memberDetail.Phone,
				Name:     memberDetail.Name,
			},
			Message: &pb_account.NotificationMessage{
				Content:      req.Text,
				BusinessId:   o.Id.Hex(),
				BusinessName: businessName,
			},
		}
		defer proto_client.GetAccountServiceClient().CreateMessageNotificationLog(ctx, logReq)

		err := ec_service.SendSms(ctx, req)
		if err != nil {
			logReq.Status = share_model.MESSAGE_NOTIFICATION_LOG_STATUS_FAILED
			logReq.Description = share_model.GenMessageNotificationLogDescription(share_model.MESSAGE_NOTIFICATION_LOG_TYPE_SMS, "")
			log.Warn(ctx, "Failed to send sms to member", log.Fields{
				"errorMessage": err,
			})
		}
		return err
	}()
}

func (o *Order) NotifyStaffs(ctx context.Context, typ string) (err error) {
	staffs, err := ec_model_store.CStaff.GetOnDutyStaffsByStoreId(ctx, o.StoreId)
	if err != nil {
		log.Error(ctx, "Failed to find staffs by storeId", log.Fields{
			"storeId":      o.StoreId.Hex(),
			"errorMessage": err.Error(),
		})
		return err
	}

	result, err := ec_service.GetNoitificationSetting(ctx, BUSINESS, STAFF_NOTIFICATION_SETTING_MAP[typ])
	if err != nil {
		return err
	}
	notificationSetting := result.NotificationSetting
	if notificationSetting.Text.Enabled == false {
		return nil
	}
	text := notificationSetting.Text.Content
	text = strings.Replace(text, fmt.Sprintf("{%s}", PLACEHOLDER_ORDERSN), o.Number, -1)

	for _, staff := range staffs {
		phone := staff.Phone

		businessName := uuid.NewV4().String()
		req := &pb_account.SendSmsRequest{
			Phone:        phone,
			Text:         text,
			SmsTopic:     EC_RETAIL_STORE_SMS_TOPIC,
			Operator:     OPERATOR_SYSTEM,
			BusinessId:   o.Id.Hex(),
			BusinessName: businessName,
			BusinessType: "ec",
			MessageType:  pb_account.SendSmsRequest_NOTICE,
		}

		func() {
			logReq := &pb_account.CreateMessageNotificationLogRequest{
				NotificationSettingId: notificationSetting.Id,
				Type:                  share_model.MESSAGE_NOTIFICATION_LOG_TYPE_SMS,
				Status:                share_model.MESSAGE_NOTIFICATION_LOG_STATUS_PROCESSING,
				MemberInfo: &pb_account.NotificationMember{
					StaffId:  staff.Id.Hex(),
					StoreIds: core_util.ToStringArray(staff.StoreIds),
					StaffNo:  staff.StaffNo,
					Phone:    staff.Phone,
					Name:     staff.Name,
				},
				Message: &pb_account.NotificationMessage{
					Content:      req.Text,
					BusinessId:   o.Id.Hex(),
					BusinessName: businessName,
				},
			}
			defer proto_client.GetAccountServiceClient().CreateMessageNotificationLog(ctx, logReq)

			err := ec_service.SendSms(ctx, req)
			if err != nil {
				logReq.Status = share_model.MESSAGE_NOTIFICATION_LOG_STATUS_FAILED
				logReq.Description = share_model.GenMessageNotificationLogDescription(share_model.MESSAGE_NOTIFICATION_LOG_TYPE_SMS, "")
				log.Error(ctx, "Failed to send sms to staff", log.Fields{
					"staffId":      staff.Id.Hex(),
					"errorMessage": err.Error(),
				})
			}
		}()
	}
	return nil
}

// HandleOrderStatusChanged 订单状态变化后根据不同状态处理对应的逻辑
// ORDER_STATUS_CANCELED: 取消订单（分两种情况：取消订单 和 发起退款导致订单取消）
// ORDER_STATUS_ACCEPTED: 待提货/待发货
// ORDER_STATUS_PAID: 已支付或重新分配到门店
// ORDER_STATUS_COMPLETED: 已完成（分两种情况：客户确认收货 和 店员确认提供）
// ORDER_STATUS_UNASSIGNED: 被拒单
func HandleOrderStatusChanged(ctx context.Context, order *Order, toStatus string) {
	if (toStatus == ORDER_STATUS_PAID || toStatus == ORDER_STATUS_COMPLETED) &&
		order.IsNeedSyncToOms() && ec_model_setting.CSettings.IsNeedSyncToOms(ctx) {
		order.UpdateOmsPushStatusToPending(ctx)
	}
	if (toStatus == ORDER_STATUS_COMPLETED && order.Method == ORDER_DELIVERY_METHOD_PICKUP) ||
		(toStatus == ORDER_STATUS_ACCEPTED && order.Method == ORDER_DELIVERY_METHOD_PICKUP) {
		core_component.GO(ctx, func(ctx context.Context) {
			order.NotifyCustomer(ctx, toStatus, map[string]string{})
		})
	}

	if toStatus == ORDER_STATUS_COMPLETED {
		if order.isNeedCreateOrderReceiverProfit() {
			COrderReceiverProfit.CreateFromOrder(ctx, *order)
			order.SendDistributionCoupon(ctx)
		}
	}

	// 已接单的自提订单视为发货，上传给微信
	if toStatus == ORDER_STATUS_ACCEPTED && order.Method == ORDER_DELIVERY_METHOD_PICKUP {
		proto_client.GetEcWechatShopServiceClient().UploadOrderShippingInfoToWechat(ctx, &request.DetailRequest{
			Id: order.Id.Hex(),
		})
	}

	if util.StrInArray(toStatus, &[]string{ORDER_STATUS_PAID}) && order.Operator != ec_model_setting.DELIVERY_SETTING_OPERATOR_USER {
		order.NotifyStaffs(ctx, toStatus)
	}

	// 订单支付或者订单完成以后触发邀请有礼活动
	if util.StrInArray(toStatus, &[]string{
		ORDER_STATUS_PAID,
		ORDER_STATUS_COMPLETED,
	}) {
		proto_client.GetEcMarketingServiceClient().HandleOrderStatusChange(ctx, &pb_ec_marketing.HandleOrderStatusChangeRequest{
			MemberId: order.MemberId.Hex(),
			ToStatus: toStatus,
			Source:   "retail",
		})
	}

	if toStatus == ORDER_STATUS_PAID && !order.HasUnassigned() {
		order.NotifyCustomer(ctx, ORDER_STATUS_PAID, map[string]string{})
		if !order.PromoterMemberIdForBinding.IsZero() {
			client.DistributionService.BindPromoterMember(ctx, &pb_distribution.BindPromoterMemberRequest{
				PromoterMemberId: order.PromoterMemberIdForBinding.Hex(),
				MemberId:         order.MemberId.Hex(),
			})
		}
	}
	if toStatus == ORDER_STATUS_CANCELED {
		cancelType := ORDER_CANCEL_TYPE_BY_MEMBER
		if strings.HasPrefix(order.Operator, "user") {
			cancelType = ORDER_CANCEL_TYPE_BY_USER
		}
		order.SendOrderCancelEvent(ctx, cancelType)
		if order.Invoice.Needed && order.Invoice.Status != INVOICE_STATUS_ISSUED {
			invoiceResponse, err := client.OrderService.ListInvoices(ctx, &pb_order.ListInvoicesRequest{OrderIds: []string{order.Id.Hex()}})
			if err != nil {
				log.Warn(ctx, "Failed to list invoices", log.Fields{
					"orderId": order.Id,
					"errMsg":  err.Error(),
				})
			} else if invoiceResponse.Total == 0 {
				order.UpdateInvoiceStatus(INVOICE_STATUS_CLOSED)
			} else {
				for _, invoice := range invoiceResponse.Items {
					if invoice.IsRed || invoice.Status == INVOICE_STATUS_ISSUED {
						continue
					}
					client.OrderService.UpdateInvoice(ctx, &pb_order.UpdateInvoiceRequest{Id: invoice.Id, Status: INVOICE_STATUS_CLOSED, FailedReason: "refund"})
				}
			}
		}

		// 订单取消时，月结状态的订单中的分销信息变更为无佣金
		if order.CompletedAt.IsZero() &&
			order.Distribution.ProfitSharingType == ec_model_setting.COMMMISSION_TYPE_MONTHLY &&
			order.Distribution.ProfitSharingStatus == PROFIT_SHARING_STATUS_PENDING {
			order.Distribution.ProfitSharingStatus = DISTRIBUTION_STATUS_NOCOMMISSION
			order.Distribution.Extra = map[string]string{
				"无分佣原因": "参与分佣金额为 0",
			}
			order.UpdateDistribution(ctx)
		}
	}

	if toStatus == ORDER_STATUS_ACCEPTED {
		orderSetting, err := client.SettingService.GetOrderSetting(ctx, &request.EmptyRequest{})
		if err != nil {
			log.Warn(ctx, "Failed to get orderSetting", log.Fields{"err": err.Error()})
		} else if orderSetting.IsAutoAcceptOrder {
			isGrouponOrder := false
			for _, campaign := range order.Campaigns {
				if campaign.Type == CAMPAIGN_TYPE_GROUPON {
					isGrouponOrder = true
					break
				}
			}
			// 拼团订单统一在拼团成功时打印，总部发货时不打印小票
			if !isGrouponOrder && !(order.Operator == LOGISTICS_OPERATOR_TYPE_USER && order.Method == ORDER_DELIVERY_METHOD_EXPRESS) {
				log.Warn(ctx, "auto print ticket", log.Fields{
					"orderNumber": order.Number,
				})
				_, err := client.OrderService.PrintTicket(ctx, &pb_order.PrintTicketRequest{
					OrderId:  order.Id.Hex(),
					MemberId: order.MemberId.Hex(),
					StoreId:  order.StoreId.Hex(),
				})
				if err != nil {
					log.Warn(ctx, "auto print ticket error", log.Fields{
						"orderId":     order.Id,
						"orderNumber": order.Number,
						"errMsg":      err.Error(),
					})
				}
			}
		}
		invoiceResponse, err := client.OrderService.ListInvoices(ctx, &pb_order.ListInvoicesRequest{OrderIds: []string{order.Id.Hex()}})
		if err != nil {
			log.Warn(ctx, "Failed to list invoices", log.Fields{
				"orderId": order.Id,
				"errMsg":  err.Error(),
			})
		} else if invoiceResponse.Total == 0 {
			order.UpdateInvoiceStatus(INVOICE_STATUS_PENDING)
		} else {
			for _, invoice := range invoiceResponse.Items {
				_, err := client.OrderService.UpdateInvoice(ctx, &pb_order.UpdateInvoiceRequest{Id: invoice.Id, Status: INVOICE_STATUS_PENDING})
				if err != nil {
					log.Warn(ctx, "Failed to update invoices", log.Fields{
						"orderId": order.Id,
						"errMsg":  err.Error(),
					})
				}
			}
		}
	}

	if toStatus == ORDER_STATUS_PAID || toStatus == ORDER_STATUS_ACCEPTED {
		err := marketing.HandleGrouponCampaign(ctx, order.Number)
		if err != nil {
			log.Warn(ctx, "Failed to handle groupon campaign", log.Fields{
				"orderId": order.Id.Hex(),
				"error":   err.Error(),
			})
		}
	}

	if toStatus == ORDER_STATUS_PAID && order.IsYeepay() && order.IsDivideAfterPaid() {
		COrderReceiverProfit.CreateFromOrder(ctx, *order)
	}
}

func (o *Order) isNeedCreateOrderReceiverProfit() bool {
	// 虚拟商品导购分销在核销后 15 天后发放佣金，在 HandleVirtualOrderProfit 处理，订单完成时无需创建预分账表
	if o.IsVirtual() && o.Distribution.PromoterType == ec_distribution.PROMOTER_TYPE_STAFF {
		return false
	}
	// 易宝支付，且在支付后已经分账了，订单完成后无需分账
	if o.IsYeepay() && o.IsDivideAfterPaid() {
		return false
	}
	return true
}

func (o *Order) IsNeedSyncToOms() bool {
	if o.Method == ORDER_DELIVERY_METHOD_PICKUP {
		return false
	}
	if o.Operator != ec_model_setting.DELIVERY_SETTING_OPERATOR_USER {
		return false
	}
	for _, c := range o.Campaigns {
		if c.Type == ec_marketing.CAMPAIGN_TYPE_GROUPON && c.GrouponStatus != ec_marketing.GROUPON_RECORD_STATUS_SUCCESS {
			return false
		}
	}
	for _, p := range o.Products {
		if p.Spec.ExternalSku == "" {
			return false
		}
	}
	return true
}

func (o *Order) SendDistributionCoupon(ctx context.Context) {
	if o.Distribution.PromoterType != distribution_model.PROMOTER_TYPE_MEMBER {
		return
	}

	allProductDistributionAmountZero := true
	allProductNotDistribution := true
	for _, op := range o.Products {
		if !op.IsDistribution {
			continue
		}
		allProductNotDistribution = false
		switch op.DistributionMode {
		case ec_model_product.PRODUCT_DISTRIBUTION_MODE_CUSTOM_AMOUNT:
			if op.DistributionAmount > 0 {
				allProductDistributionAmountZero = false
			}
		case ec_model_product.PRODUCT_DISTRIBUTION_MODE_CUSTOM_PROPORTION:
			if !util.IsZeroFloat(op.DistributionProportion) {
				allProductDistributionAmountZero = false
			}
		default:
			allProductDistributionAmountZero = false
		}
	}

	// 当实付金额或分销佣金为0时，不发放分销优惠券
	if o.PayAmount == 0 || o.Distribution.Amount == 0 && !allProductDistributionAmountZero && !allProductNotDistribution {
		log.Warn(ctx, "don't send distribution coupon for zero amount", log.Fields{
			"payAmount":          o.PayAmount,
			"distributionAmount": o.Distribution.Amount,
		})
		return
	}
	var err error
	promoter, err := ec_distribution.CPromoter.GetById(ctx, o.Distribution.PromoterId)
	if err != nil {
		log.Warn(ctx, "send order distribution coupon failed promoter not exists", log.Fields{
			"orderId":    o.Id,
			"promoterId": o.Distribution.PromoterId,
		})
		return
	}
	for _, orderProduct := range o.Products {
		if core_util.IsEmpty(reflect.ValueOf(orderProduct.DistributionCoupon)) {
			continue
		}
		index := 1
		for {
			if index > int(orderProduct.Total) {
				break
			}
			_, mairpcError := core_client.Run(
				"CouponService.IssueCoupon",
				ctx,
				&coupon.IssueCouponRequest{
					CouponId:   orderProduct.DistributionCoupon.Id.Hex(),
					MemberId:   promoter.MemberId.Hex(),
					StoreId:    o.StoreId.Hex(),
					SceneId:    o.Id.Hex(),
					CheckLimit: true,
					IsSystem:   true,
				},
			)
			if mairpcError != nil {
				log.Warn(ctx, "failed to sent order distribution coupon", log.Fields{
					"orderId":  o.Id,
					"couponId": orderProduct.DistributionCoupon.Id,
					"memberId": o.Distribution.PromoterId,
					"errMsg":   mairpcError.Error(),
				})
			}
			index++
		}
	}
}

func (o *Order) CreateNotification(ctx context.Context, toStatus string) {
	// 脉盟商城/连锁零售商代销订单不需要创建通知
	if o.IsConsignmentOrder() {
		return
	}
	notification := ec_model_notification.Notification{
		StoreId:     o.StoreId,
		OrderNumber: o.Number,
		OrderId:     o.Id,
		MemberId:    o.MemberId,
	}

	switch toStatus {
	case ORDER_STATUS_CANCELED:
		notification.Type = ec_model_notification.NOTIFICATION_TYPE_ORDER_CANCELED
		notification.Content = ec_model_notification.NOTIFICATION_CONTENT_ORDER_CHANGED
	case ORDER_STATUS_PARTIAL_REFUNDED:
		notification.Type = ec_model_notification.NOTIFICATION_TYPE_ORDER_CHANGED
		notification.Content = ec_model_notification.NOTIFICATION_CONTENT_ORDER_CANCELED
	}

	// notification 会展示在店长小程序中，总部发货时取消订单不发通知
	if o.Method == ORDER_DELIVERY_METHOD_EXPRESS && o.Operator == ec_model_setting.DELIVERY_SETTING_OPERATOR_USER {
		return
	}
	err := notification.Create(ctx)
	if err != nil {
		log.Error(ctx, "Failed to create notification", log.Fields{
			"type":         toStatus,
			"orderNumber":  o.Number,
			"errorMessage": err.Error(),
		})
	}
}

func IsFirstPaid(ctx context.Context, memberId bson.ObjectId) bool {
	condition := Common.GenDefaultCondition(ctx)
	condition["memberId"] = memberId
	condition["histories.status"] = ORDER_STATUS_PAID
	return !Common.Exist(ctx, C_ORDER, condition)
}

func (o *Order) SendOrderCreatedEvent(ctx context.Context, eventAccountId string) {
	if eventAccountId == "" {
		eventAccountId = o.AccountId.Hex()
	}
	// 获取小店信息
	resp, err := client.MallService.GetMall(ctx, &mall.GetMallRequest{
		StoreId: o.StoreId.Hex(),
	})
	store := getStoreById(ctx, o.StoreId)
	var from string
	var mallId, mallName string
	if err != nil {
		from = "store"
	} else {
		from = "mall"
		mallId = resp.Id
		mallName = resp.Name
	}
	// 计算分佣总和
	mallCommissionAmount, err := mall_service.GetLatestMallCommissionProportionAmount(ctx, o.Id.Hex(), o.StoreId.Hex())
	if err != nil {
		log.Warn(ctx, "calculate total commission amount failed", log.Fields{"error": err})
	}
	var (
		goodsCount uint64
		goodsList  []map[string]interface{}
	)
	var campaignId, campaignName string
	for _, c := range o.Campaigns {
		campaignId = c.Id.Hex()
		campaignName = c.Title
	}
	for _, product := range o.Products {
		goodsCount += product.Total
		goodsList = append(goodsList, map[string]interface{}{
			"productName": product.Name,
			"productSku":  product.Spec.Sku,
			"price":       product.Price,
			"count":       product.Total,
			"originPrice": product.OriginPrice,
			"productId":   product.Id,
			"payAmount":   product.PayAmount,
		})
	}

	eventProperties := map[string]interface{}{
		"memberId":             o.MemberId.Hex(),
		"orderId":              o.Number,
		"platform":             "mai-retail",
		"storeId":              o.StoreId.Hex(),
		"storeName":            store.Name,
		"storeCode":            store.Code,
		"orderAmount":          o.PayAmount,
		"from":                 from,
		"mallId":               mallId,
		"mallName":             mallName,
		"mallCommissionAmount": mallCommissionAmount,
		"utmSource":            o.Utm.UtmSource,
		"utmContent":           o.Utm.UtmContent,
		"utmMedium":            o.Utm.UtmMedium,
		"utmTerm":              o.Utm.UtmTerm,
		"utmCampaign":          o.Utm.UtmCampaign,
		"id":                   o.Id.Hex(),
		"number":               o.Number,
		"productCounts":        goodsCount,
		"goodsList":            goodsList,
		"goodsAmount":          o.GetGoodsAmount(ctx),
		"campaignId":           campaignId,
		"campaignName":         campaignName,
		"discountAmount":       o.GetDiscountAmount(),
		"postalAmount":         o.Logistics.Fee,
	}

	storeType := o.getStoreType()
	if storeType != "" {
		eventProperties["storeType"] = storeType
	}
	distributorId := o.getDistributorId()
	if distributorId != "" {
		eventProperties["distributorId"] = distributorId
	}

	eventBody := component.CustomerEventBody{
		Id:              component.MAIEVENT_ORDER_CREATED + ":" + o.Number,
		AccountId:       eventAccountId,
		MemberId:        o.MemberId.Hex(),
		ChannelId:       o.Channel.ChannelId,
		OpenId:          o.Channel.OpenId,
		MsgType:         component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:         component.MAIEVENT_ORDER_CREATED,
		CreateTime:      o.CreatedAt.UnixNano() / 1e6,
		EventProperties: eventProperties,
	}

	eventBody.SendCustomerEventWithBusinessTag(ctx)
}

func (o *Order) SendPurchaseProductEvent(ctx context.Context) {
	categoryIdsMap := map[string][]string{}
	brandIdMap := map[string]string{}
	threeLevelCatIdKeys := []string{"firstCategoryId", "secondCategoryId", "thirdCategoryId"}
	var campaignId, campaignName string
	for _, c := range o.Campaigns {
		campaignId = c.Id.Hex()
		campaignName = c.Title
	}
	storeType := o.getStoreType()
	for _, product := range o.Products {
		store := getStoreById(ctx, o.StoreId)
		brandId, ok := brandIdMap[product.ProductId.Hex()]
		if !ok {
			brandId = getProductBrandId(ctx, product.ProductId.Hex())
			brandIdMap[product.ProductId.Hex()] = brandId
		}
		eventProperties := map[string]interface{}{
			"orderId":            o.Number,
			"orderCreateTime":    o.CreatedAt.UnixNano() / 1e6,
			"platform":           "mai-retail",
			"memberId":           o.MemberId.Hex(),
			"memberLevel":        o.MemberLevel,
			"storeId":            o.StoreId.Hex(),
			"storeName":          store.Name,
			"storeCode":          store.Code,
			"productId":          product.ProductId.Hex(),
			"productName":        product.Name,
			"productNumber":      product.Number,
			"productSku":         product.Spec.Sku,
			"productExternalSku": product.Spec.ExternalSku,
			"price":              product.Price,
			"payAmount":          product.PayAmount,
			"goodsAmount":        o.GetGoodsAmount(ctx),
			"logisticsFee":       o.Logistics.Fee,
			"count":              product.Total,
			"categoryId":         product.CategoryId.Hex(),
			"brandId":            brandId,
			"utmSource":          o.Utm.UtmSource,
			"utmContent":         o.Utm.UtmContent,
			"utmMedium":          o.Utm.UtmMedium,
			"utmTerm":            o.Utm.UtmTerm,
			"utmCampaign":        o.Utm.UtmCampaign,
			"id":                 o.Id.Hex(),
			"number":             o.Number,
			"outTradeId":         product.OutTradeId,
			"campaignId":         campaignId,
			"campaignName":       campaignName,
		}
		if storeType != "" {
			eventProperties["storeType"] = storeType
		}
		distributorId := o.getDistributorId()
		if distributorId != "" {
			eventProperties["distributorId"] = distributorId
		}
		eventBody := component.CustomerEventBody{
			AccountId:       o.AccountId.Hex(),
			MemberId:        o.MemberId.Hex(),
			ChannelId:       o.Channel.ChannelId,
			OpenId:          o.Channel.OpenId,
			MsgType:         component.MAIEVENT_TYPE_CUSTOMER_EVENT,
			SubType:         component.MAIEVENT_PURCHASE_PRODUCT,
			CreateTime:      o.PaidAt.UnixNano() / 1e6,
			EventProperties: eventProperties,
		}

		threeLevelCatIds, ok := categoryIdsMap[product.CategoryId.Hex()]
		if !ok {
			threeLevelCatIds = getProductCategoryThreeLevelIds(ctx, product.CategoryId.Hex())
			categoryIdsMap[product.CategoryId.Hex()] = threeLevelCatIds
		}
		for i, categoryId := range threeLevelCatIds {
			eventBody.EventProperties[threeLevelCatIdKeys[i]] = categoryId
		}

		eventBody.Send(ctx)
	}
}

func (o *Order) SendDeposidPaidEvent(ctx context.Context) {
	store := getStoreById(ctx, o.StoreId)

	var campaignId, campaignName string
	for _, c := range o.Campaigns {
		campaignId = c.Id.Hex()
		campaignName = c.Title
	}

	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_ORDER_DEPOSIT_PAID + ":" + o.Number,
		AccountId:  o.AccountId.Hex(),
		MemberId:   o.MemberId.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_ORDER_DEPOSIT_PAID,
		CreateTime: o.PaidAt.UnixNano() / 1e6,
		ChannelId:  o.Channel.ChannelId,
		OpenId:     o.Channel.OpenId,
		EventProperties: map[string]interface{}{
			"tradeId":        o.Id.Hex(),
			"orderId":        o.Number,
			"platform":       "mai-retail",
			"memberId":       o.MemberId.Hex(),
			"portalPlatform": "ec",
			"storeId":        o.StoreId.Hex(),
			"storeName":      store.Name,
			"storeCode":      store.Code,
			"campaignId":     campaignId,
			"campaignName":   campaignName,
			"id":             o.Id.Hex(),
			"number":         o.Number,
		},
	}

	eventBody.SendCustomerEventWithBusinessTag(ctx)
}

func (o *Order) SendPurchaseEvent(ctx context.Context, eventAccountId string) {
	if eventAccountId == "" {
		eventAccountId = o.AccountId.Hex()
	}
	var (
		goodsCount uint64
		goodsList  []map[string]interface{}
	)
	for _, product := range o.Products {
		goodsCount += product.Total
		goodsList = append(goodsList, map[string]interface{}{
			"productName": product.Name,
			"productSku":  product.Spec.Sku,
			"productId":   product.ProductId.Hex(),
			"price":       product.Price,
			"count":       product.Total,
			"platform":    "mai-retail",
		})
	}
	unitPrice := uint64(math.Floor(float64(o.PayAmount)/float64(goodsCount) + 0.5))
	store := getStoreById(ctx, o.StoreId)
	storeFirstPayment := false
	newMember := false
	if o.IsFirstPaid {
		// 因为新客首次支付概念范围小于首次支付
		// 仅在第一次支付时判断是否为新客，减少不必要的 api 调用
		newMember = isFissionNewMember(ctx, o.MemberId.Hex())
	}

	selector := bson.M{
		"memberId":  o.MemberId,
		"accountId": o.AccountId,
		"paidAt":    bson.M{"$exists": true},
		"isDeleted": false,
		"storeId":   o.StoreId,
	}
	orderCount, _ := extension.DBRepository.Count(ctx, C_ORDER, selector)
	if orderCount == 1 {
		storeFirstPayment = true
	}

	// 获取小店信息
	resp, err := client.MallService.GetMall(ctx, &mall.GetMallRequest{
		Id: o.StoreId.Hex(),
	})

	var from string
	var mallId, mallName string
	if err != nil {
		from = "store"
	} else {
		from = "mall"
		mallId = resp.Id
		mallName = resp.Name
	}
	// 计算分佣总和
	mallCommissionAmount, err := mall_service.GetLatestMallCommissionProportionAmount(ctx, o.Id.Hex(), o.StoreId.Hex())
	if err != nil {
		log.Warn(ctx, "calculate total commission amount failed", log.Fields{"error": err})
	}
	var campaignId, campaignName string
	var campaignList []map[string]interface{}
	for _, c := range o.Campaigns {
		campaignId = c.Id.Hex()
		campaignName = c.Title
		campaignList = append(campaignList, map[string]interface{}{
			"campaignId":   campaignId,
			"campaignName": campaignName,
		})
	}
	eventProperties := map[string]interface{}{
		"tradeId":               o.Id.Hex(),
		"orderId":               o.Number,
		"platform":              "mai-retail",
		"firstPayment":          o.IsFirstPaid,
		"memberLevel":           o.MemberLevel,
		"newMemberFirstPayment": o.IsFirstPaid && newMember,
		"storeFirstPayment":     storeFirstPayment,
		"portalPlatform":        "ec",
		"storeId":               o.StoreId.Hex(),
		"storeName":             store.Name,
		"storeCode":             store.Code,
		"productKinds":          len(o.Products),
		"productCounts":         goodsCount,
		"goodsAmount":           o.TotalAmount,
		"discountAmount":        o.TotalAmount - o.PayAmount + o.Logistics.Fee,
		"postalAmount":          o.Logistics.Fee,
		"orderAmount":           o.PayAmount,
		"actualAmount":          o.PayAmount + o.StoredValue.DeliveryAmount - o.Logistics.Fee,
		"unitPrice":             unitPrice,
		"from":                  from,
		"mallId":                mallId,
		"mallName":              mallName,
		"orderCreateTime":       o.CreatedAt.UnixNano() / 1e6,
		"goodsList":             goodsList,
		"mallCommissionAmount":  mallCommissionAmount,
		"utmSource":             o.Utm.UtmSource,
		"utmContent":            o.Utm.UtmContent,
		"utmMedium":             o.Utm.UtmMedium,
		"utmTerm":               o.Utm.UtmTerm,
		"utmCampaign":           o.Utm.UtmCampaign,
		"tradeNo":               o.TradeNo,
		"id":                    o.Id.Hex(),
		"number":                o.Number,
		"campaignId":            campaignId,
		"campaignName":          campaignName,
		"campaignList":          campaignList,
	}
	storeType := o.getStoreType()
	if storeType != "" {
		eventProperties["storeType"] = storeType
	}
	distributorId := o.getDistributorId()
	if distributorId != "" {
		eventProperties["distributorId"] = distributorId
	}
	if o.Distribution.PromoterId.Valid() {
		eventProperties["promoterId"] = o.Distribution.PromoterId.Hex()
	}

	eventBody := component.CustomerEventBody{
		Id:              component.MAIEVENT_PURCHASE_PRODUCT_SERVICE + ":" + o.Number,
		AccountId:       eventAccountId,
		MemberId:        o.MemberId.Hex(),
		MsgType:         component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:         component.MAIEVENT_PURCHASE_PRODUCT_SERVICE,
		CreateTime:      o.PaidAt.UnixNano() / 1e6,
		ChannelId:       o.Channel.ChannelId,
		OpenId:          o.Channel.OpenId,
		EventProperties: eventProperties,
	}

	eventBody.SendCustomerEventWithBusinessTag(ctx)
}

func (o *Order) getStoreType() string {
	if !o.IsYeepayForD2R() {
		return ""
	}
	if util.StrInArray(ORDER_TAGS_CONSIGNMENT, &o.Tags) {
		return ORDER_TAGS_CONSIGNMENT
	}
	return ORDER_TAGS_DISTRIBUTION
}

func (o *Order) getDistributorId() string {
	if !o.IsYeepayForD2R() {
		return ""
	}
	for _, tag := range o.Tags {
		if util.IsMongoId(tag) {
			return tag
		}
	}
	return ""
}

func (o *Order) SendOrderCompletedEvent(ctx context.Context) {
	var (
		goodsCount uint64
		goodsList  []map[string]interface{}
	)
	for _, product := range o.Products {
		goodsCount += product.Total
		goodsList = append(goodsList, map[string]interface{}{
			"productName":    product.Name,
			"productSku":     product.Spec.Sku,
			"productId":      product.ProductId.Hex(),
			"price":          product.Price,
			"count":          product.Total,
			"totalAmount":    product.TotalAmount,
			"payAmount":      product.PayAmount,
			"isDistribution": product.IsDistribution,
			"refundStatus":   product.RefundStatus,
		})
	}
	// 获取小店信息
	resp, err := client.MallService.GetMall(ctx, &mall.GetMallRequest{
		StoreId: o.StoreId.Hex(),
	})

	var from string
	var mallId, mallName string
	if err != nil {
		from = "store"
	} else {
		from = "mall"
		mallId = resp.Id
		mallName = resp.Name
	}
	memberPaidCardRecordIds := core_util.ExtractArrayStringField("RecordId", o.MemberPaidCards)
	store := getStoreById(ctx, o.StoreId)
	eventProperties := map[string]interface{}{
		"memberId":                o.MemberId.Hex(),
		"memberLevel":             o.MemberLevel,
		"orderId":                 o.Number,
		"platform":                "mai-retail",
		"storeId":                 o.StoreId.Hex(),
		"storeName":               store.Name,
		"storeCode":               store.Code,
		"goodsAmount":             o.GetGoodsAmount(ctx),
		"logisticsFee":            o.Logistics.Fee,
		"completedAt":             o.CompletedAt.Unix(),
		"from":                    from,
		"mallId":                  mallId,
		"mallName":                mallName,
		"orderCreateTime":         o.CreatedAt.UnixNano() / 1e6,
		"id":                      o.Id.Hex(),
		"number":                  o.Number,
		"goodsList":               goodsList,
		"promoterId":              o.Distribution.PromoterId.Hex(),
		"orderType":               o.GetType(),
		"memberPaidCardRecordIds": memberPaidCardRecordIds,
	}
	storeType := o.getStoreType()
	if storeType != "" {
		eventProperties["storeType"] = storeType
	}
	distributorId := o.getDistributorId()
	if distributorId != "" {
		eventProperties["distributorId"] = distributorId
	}
	eventBody := component.CustomerEventBody{
		Id:              component.MAIEVENT_ORDER_COMPLETED + ":" + o.Number,
		AccountId:       o.AccountId.Hex(),
		MemberId:        o.MemberId.Hex(),
		ChannelId:       o.Channel.ChannelId,
		OpenId:          o.Channel.OpenId,
		MsgType:         component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:         component.MAIEVENT_ORDER_COMPLETED,
		CreateTime:      o.CompletedAt.UnixNano() / 1e6,
		EventProperties: eventProperties,
	}
	eventBody.SendCustomerEventWithBusinessTag(ctx)
}

func (o *Order) SendVirtualProductOrderRedeemedAllEvent(ctx context.Context) {
	var (
		goodsCount uint64
		goodsList  []map[string]interface{}
	)
	for _, product := range o.Products {
		goodsCount += product.Total
		goodsList = append(goodsList, map[string]interface{}{
			"productName":    product.Name,
			"productSku":     product.Spec.Sku,
			"productId":      product.ProductId.Hex(),
			"price":          product.Price,
			"count":          product.Total,
			"totalAmount":    product.TotalAmount,
			"payAmount":      product.PayAmount,
			"isDistribution": product.IsDistribution,
			"refundStatus":   product.RefundStatus,
		})
	}
	// 获取小店信息
	resp, err := client.MallService.GetMall(ctx, &mall.GetMallRequest{
		StoreId: o.StoreId.Hex(),
	})

	var from string
	var mallId, mallName string
	if err != nil {
		from = "store"
	} else {
		from = "mall"
		mallId = resp.Id
		mallName = resp.Name
	}
	store := getStoreById(ctx, o.StoreId)
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_VIRTUAL_GOODS_COMPELTED + ":" + o.Number,
		AccountId:  o.AccountId.Hex(),
		MemberId:   o.MemberId.Hex(),
		ChannelId:  o.Channel.ChannelId,
		OpenId:     o.Channel.OpenId,
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_VIRTUAL_GOODS_COMPELTED,
		CreateTime: o.CompletedAt.UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"memberId":        o.MemberId.Hex(),
			"memberLevel":     o.MemberLevel,
			"orderId":         o.Number,
			"platform":        "mai-retail",
			"storeId":         o.StoreId.Hex(),
			"storeName":       store.Name,
			"storeCode":       store.Code,
			"goodsAmount":     o.GetGoodsAmount(ctx),
			"logisticsFee":    o.Logistics.Fee,
			"completedAt":     o.CompletedAt.Unix(),
			"from":            from,
			"mallId":          mallId,
			"mallName":        mallName,
			"orderCreateTime": o.CreatedAt.UnixNano() / 1e6,
			"id":              o.Id.Hex(),
			"number":          o.Number,
			"goodsList":       goodsList,
			"promoterId":      o.Distribution.PromoterId.Hex(),
			"orderType":       o.GetType(),
		},
	}
	eventBody.SendCustomerEventWithBusinessTag(ctx)
}

func (o *Order) SendOrderShipEvents(ctx context.Context, waybillIds []string) {
	for _, waybillId := range waybillIds {
		logisticInfo := ProductLogisticsInfo{}
		for _, p := range o.Products {
			if logisticInfo.WaybillId != "" {
				break
			}
			for _, l := range p.Logistics {
				if l.WaybillId == waybillId {
					logisticInfo = l
					break
				}
			}
		}

		store := getStoreById(ctx, o.StoreId)
		eventProperties := map[string]interface{}{
			"memberId":     o.MemberId.Hex(),
			"orderId":      o.Number,
			"platform":     "mai-retail",
			"storeId":      o.StoreId.Hex(),
			"storeName":    store.Name,
			"storeCode":    store.Code,
			"deliveryId":   logisticInfo.DeliveryId,
			"deliveryName": logisticInfo.DeliveryName,
			"waybillId":    logisticInfo.WaybillId,
			"shippedAt":    logisticInfo.ShippedAt.Unix(),
			"id":           o.Id.Hex(),
			"number":       o.Number,
			"orderStatus":  o.Status,
		}
		storeType := o.getStoreType()
		if storeType != "" {
			eventProperties["storeType"] = storeType
		}
		distributorId := o.getDistributorId()
		if distributorId != "" {
			eventProperties["distributorId"] = distributorId
		}
		eventBody := component.CustomerEventBody{
			Id:              fmt.Sprintf("%s:%s:%s", component.MAIEVENT_ORDER_SHIP, o.Number, waybillId),
			AccountId:       o.AccountId.Hex(),
			MemberId:        o.MemberId.Hex(),
			ChannelId:       o.Channel.ChannelId,
			MsgType:         component.MAIEVENT_TYPE_CUSTOMER_EVENT,
			SubType:         component.MAIEVENT_ORDER_SHIP,
			CreateTime:      time.Now().UnixNano() / 1e6,
			EventProperties: eventProperties,
		}
		eventBody.SendCustomerEventWithBusinessTag(ctx)
	}
}

func (o *Order) SendOrderCancelEvent(ctx context.Context, cancelType string) {
	// 获取小店信息
	resp, err := client.MallService.GetMall(ctx, &mall.GetMallRequest{
		StoreId: o.StoreId.Hex(),
	})

	var from string
	var mallId, mallName string
	if err != nil {
		from = "store"
	} else {
		from = "mall"
		mallId = resp.Id
		mallName = resp.Name
	}
	store := getStoreById(ctx, o.StoreId)
	eventProperties := map[string]interface{}{
		"memberId":        o.MemberId.Hex(),
		"orderId":         o.Number,
		"platform":        "mai-retail",
		"storeId":         o.StoreId.Hex(),
		"storeName":       store.Name,
		"storeCode":       store.Code,
		"orderCreateTime": o.CreatedAt.Unix(),
		"orderCancelTime": time.Now().Unix(),
		"from":            from,
		"mallId":          mallId,
		"mallName":        mallName,
		"id":              o.Id.Hex(),
		"number":          o.Number,
		"cancelType":      cancelType,
	}
	storeType := o.getStoreType()
	if storeType != "" {
		eventProperties["storeType"] = storeType
	}
	distributorId := o.getDistributorId()
	if distributorId != "" {
		eventProperties["distributorId"] = distributorId
	}
	eventBody := component.CustomerEventBody{
		Id:              component.MAIEVENT_CANCEL_ORDER + ":" + o.Number,
		AccountId:       o.AccountId.Hex(),
		MemberId:        o.MemberId.Hex(),
		ChannelId:       o.Channel.ChannelId,
		OpenId:          o.Channel.OpenId,
		MsgType:         component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:         component.MAIEVENT_CANCEL_ORDER,
		CreateTime:      time.Now().UnixNano() / 1e6,
		EventProperties: eventProperties,
	}
	eventBody.SendCustomerEventWithBusinessTag(ctx)
}

// GetGoodsAmount 会返回当前订单为商品支付的金额
func (o *Order) GetGoodsAmount(ctx context.Context) uint64 {
	payAmount := o.PayAmount

	// 如果支付金额为 0 直接 return，防止因为礼品卡代扣导致返回溢出的 payAmount
	if payAmount == 0 {
		return 0
	}
	// 先减掉运费
	payAmount = payAmount - (o.Logistics.Fee - o.StoredValue.DeliveryAmount)
	// 减掉退款成功的商品
	if o.GetVirtualType() == VIRTUAL_ORDER_TYPE_VIRTUAL {
		for _, p := range o.Products {
			payAmount = payAmount - p.PayAmount/p.Total*p.RefundTotal
		}
	} else {
		refundAmountMap := o.GetProductRefundAmountMap(ctx)
		for _, p := range o.Products {
			refundAmount, ok := refundAmountMap[p.OutTradeId.Hex()]
			if IsInRefundProcess(p.RefundStatus) {
				if !ok || refundAmount > p.PayAmount {
					refundAmount = p.PayAmount
				}
				payAmount = payAmount - refundAmount
			}
		}
	}
	return payAmount
}

func (o *Order) GetDiscountAmount() int64 {
	var discountAmount int64
	for _, discount := range o.Discounts {
		discountAmount += discount.Amount
	}
	return discountAmount
}

func getStoreById(ctx context.Context, storeId bson.ObjectId) ec_model_store.Store {
	store, err := ec_model_store.CStore.GetById(ctx, storeId)
	if err != nil {
		log.Error(ctx, "Failed to get store by id", log.Fields{
			"storeId":      storeId.Hex(),
			"errorMessage": err.Error(),
		})
	}
	return store
}

func isFissionNewMember(ctx context.Context, memberId string) bool {
	resp, maiErr := marketing.IsFissionNewMember(ctx, memberId)

	if maiErr != nil {
		return false
	}

	return resp.Value
}

func (o *Order) GetCompletedTime() time.Time {
	if o.Status != ORDER_STATUS_COMPLETED || len(o.Histories) == 0 {
		return time.Time{}
	}
	for _, history := range o.Histories {
		if history.Status == ORDER_STATUS_COMPLETED {
			return history.CreatedAt
		}
	}
	return time.Time{}
}

func (o *Order) UpdateProfitType(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": o.AccountId,
		"isDeleted": false,
	}

	setter := bson.M{
		"updatedAt":   time.Now(),
		"profitTypes": o.ProfitTypes,
	}

	updater := bson.M{
		"$set": setter,
	}

	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (o *Order) SendOrderDistributionEvent(ctx context.Context) {
	if o.Distribution.PromoterId.Hex() == "" {
		return
	}

	var (
		productCounts uint64
		goodsList     []map[string]interface{}
	)
	for _, product := range o.Products {
		productCounts += product.Total
		good := map[string]interface{}{
			"productName":  product.Name,
			"productSku":   product.Spec.Sku,
			"productId":    product.ProductId.Hex(),
			"price":        product.Price,
			"count":        product.Total,
			"profitAmount": product.DistributionAmount,
		}
		if len(product.Campaigns) > 0 {
			good["campaignId"] = product.Campaigns[0].Id.Hex()
		}
		goodsList = append(goodsList, good)
	}

	var campaignList []map[string]interface{}
	for _, c := range o.Campaigns {
		campaignList = append(campaignList, map[string]interface{}{
			"campaignId":   c.Id.Hex(),
			"campaignName": c.Title,
		})
	}

	eventProperties := map[string]interface{}{
		"promoterId":    o.Distribution.PromoterId.Hex(),
		"memberId":      o.MemberId.Hex(),
		"orderId":       o.Number,
		"id":            o.Id.Hex(),
		"number":        o.Number,
		"totalAmount":   o.PayAmount,
		"postalAmount":  o.Logistics.Fee,
		"goodsAmount":   o.PayAmount - o.Logistics.Fee,
		"goodsList":     goodsList,
		"productCounts": productCounts,
		"campaignList":  campaignList,
	}
	distributorId := o.getDistributorId()
	if distributorId != "" {
		eventProperties["distributorId"] = distributorId
	}
	if o.Distribution.ParentPromoterId.Valid() {
		eventProperties["parentPromoterId"] = o.Distribution.ParentPromoterId.Hex()
	}
	if o.Distribution.SubPromoterId.Valid() {
		eventProperties["subPromoterId"] = o.Distribution.SubPromoterId.Hex()
	}

	eventBody := component.CustomerEventBody{
		Id:              component.MAIEVENT_ORDER_DISTRIBUTION + ":" + o.Id.Hex(),
		AccountId:       o.AccountId.Hex(),
		MemberId:        o.MemberId.Hex(),
		MsgType:         component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:         component.MAIEVENT_ORDER_DISTRIBUTION,
		CreateTime:      o.CreatedAt.UnixNano() / 1e6,
		EventProperties: eventProperties,
	}

	orderAmount := o.PayAmount
	if o.Method == ORDER_DELIVERY_METHOD_EXPRESS && orderAmount > 0 {
		if orderAmount > o.Logistics.Fee {
			orderAmount -= o.Logistics.Fee
		} else {
			orderAmount = 0
		}
	}
	eventBody.EventProperties["orderAmount"] = orderAmount
	eventBody.EventProperties["promoterType"] = o.Distribution.PromoterType
	promoter, _ := client.DistributionService.GetPromoter(ctx, &pb_distribution.GetPromoterRequest{
		PromoterId: o.Distribution.PromoterId.Hex(),
	})
	if promoter != nil && promoter.MemberBoundStaff != nil {
		eventBody.EventProperties["staffId"] = promoter.MemberBoundStaff.StaffId
	}
	if o.StoreId.Hex() != "" {
		eventBody.EventProperties["storeId"] = o.StoreId.Hex()
	}
	// 分销月结的预计收益
	eventBody.EventProperties["profitAmount"] = o.Distribution.Amount

	eventBody.Send(ctx)
}

func (o *Order) SendStoreDistributionEvent(ctx context.Context) {
	if !o.IsConsignmentOrder() {
		return
	}

	var (
		productCounts uint64
		goodsList     []map[string]interface{}
	)
	for _, product := range o.Products {
		productCounts += product.Total
		goodsList = append(goodsList, map[string]interface{}{
			"productName":  product.Name,
			"productSku":   product.Spec.Sku,
			"productId":    product.ProductId.Hex(),
			"price":        product.Price,
			"count":        product.Total,
			"profitAmount": product.StoreDistributionAmount,
		})
	}

	eventProperties := map[string]interface{}{
		"storeId":       o.StoreId.Hex(),
		"memberId":      o.MemberId.Hex(),
		"orderId":       o.Number,
		"id":            o.Id.Hex(),
		"number":        o.Number,
		"totalAmount":   o.PayAmount,
		"postalAmount":  o.Logistics.Fee,
		"goodsAmount":   o.PayAmount - o.Logistics.Fee,
		"goodsList":     goodsList,
		"productCounts": productCounts,
		"profitAmount":  o.Distribution.StoreDistributionAmount,
	}
	distributorId := o.getDistributorId()
	if distributorId != "" {
		eventProperties["distributorId"] = distributorId
	}

	eventBody := component.CustomerEventBody{
		Id:              component.MAIEVENT_STORE_ORDER_DISTRIBUTION + ":" + o.Id.Hex(),
		AccountId:       o.AccountId.Hex(),
		MemberId:        o.MemberId.Hex(),
		MsgType:         component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:         component.MAIEVENT_STORE_ORDER_DISTRIBUTION,
		CreateTime:      o.CreatedAt.UnixNano() / 1e6,
		EventProperties: eventProperties,
	}

	orderAmount := o.PayAmount
	if o.Method == ORDER_DELIVERY_METHOD_EXPRESS && orderAmount > 0 {
		if orderAmount > o.Logistics.Fee {
			orderAmount -= o.Logistics.Fee
		} else {
			orderAmount = 0
		}
	}
	eventBody.EventProperties["orderAmount"] = orderAmount

	eventBody.Send(ctx)
}

func (o *Order) SendOrderDistributionSalesEvent(ctx context.Context) {
	if o.Distribution.PromoterId.Hex() == "" {
		return
	}

	var (
		productCounts uint64
		refundAmount  uint64
		goodsList     []map[string]interface{}
	)
	orderAmount := o.GetRealPayAmount()
	if o.Method == ORDER_DELIVERY_METHOD_EXPRESS {
		orderAmount -= o.Logistics.Fee
	}
	for _, product := range o.Products {
		productCounts += product.Total
		goodsList = append(goodsList, map[string]interface{}{
			"productName": product.Name,
			"productSku":  product.Spec.Sku,
			"productId":   product.ProductId.Hex(),
			"price":       product.Price,
			"count":       product.Total,
		})
		if IsInRefundProcess(product.RefundStatus) {
			refundAmount += product.PayAmount
		}
	}
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_ORDER_DISTRIBUTION_SALES + ":" + o.Id.Hex(),
		AccountId:  o.AccountId.Hex(),
		MemberId:   o.MemberId.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_ORDER_DISTRIBUTION_SALES,
		CreateTime: time.Now().UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"promoterId":    o.Distribution.PromoterId.Hex(),
			"memberId":      o.MemberId.Hex(),
			"orderId":       o.Number,
			"id":            o.Id.Hex(),
			"number":        o.Number,
			"postalAmount":  o.Logistics.Fee,
			"goodsList":     goodsList,
			"productCounts": productCounts,
			"refundAmount":  refundAmount,
			"totalAmount":   o.PayAmount - refundAmount, // 销售总金额，付款金额 - 退款金额
			"orderAmount":   orderAmount,                // 订单金额，付款金额 - 运费
			"salesAmount":   orderAmount - refundAmount, // 销售额应该减去退款商品的金额，推广金额无需减去退款金额
			"promoterType":  o.Distribution.PromoterType,
		},
	}
	if o.Distribution.ParentPromoterId.Valid() {
		eventBody.EventProperties["parentPromoterId"] = o.Distribution.ParentPromoterId.Hex()
	}
	if o.Distribution.SubPromoterId.Valid() {
		eventBody.EventProperties["subPromoterId"] = o.Distribution.SubPromoterId.Hex()
	}
	if o.StoreId.Hex() != "" {
		eventBody.EventProperties["storeId"] = o.StoreId.Hex()
	}
	promoter, _ := client.DistributionService.GetPromoter(ctx, &pb_distribution.GetPromoterRequest{
		PromoterId: o.Distribution.PromoterId.Hex(),
	})
	if promoter != nil && promoter.MemberBoundStaff != nil {
		eventBody.EventProperties["staffId"] = promoter.MemberBoundStaff.StaffId
	}

	eventBody.Send(ctx)
}

func (order *Order) SendOrderRedeemedEvent(ctx context.Context, logistics *Logistics) {
	eventBody := component.CustomerEventBody{
		Id:         fmt.Sprintf("%s:%s:%s", component.MAIEVENT_ORDER_REDEEMED, order.Id.Hex(), logistics.PickupCode),
		AccountId:  logistics.AccountId.Hex(),
		MemberId:   logistics.Member.Id.Hex(),
		ChannelId:  order.Channel.ChannelId,
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_ORDER_REDEEMED,
		CreateTime: logistics.CreatedAt.UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"id":         logistics.Order.Id,
			"number":     logistics.Order.Number,
			"staffId":    logistics.ProcessBy.Staff.Id.Hex(),
			"staffName":  logistics.ProcessBy.Staff.Name,
			"storeId":    logistics.ProcessBy.Store.Id.Hex(),
			"storeCode":  logistics.ProcessBy.Store.Code,
			"userId":     logistics.ProcessBy.User.Id.Hex(),
			"userName":   logistics.ProcessBy.User.Name,
			"pickupCode": logistics.PickupCode,
		},
	}
	eventBody.Send(ctx)
}

func (o *Order) SendOrderProfitAmountEvent(ctx context.Context, profitedAt time.Time) {
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_ORDER_PROFIT_AMOUNT + ":" + o.Id.Hex(),
		AccountId:  o.AccountId.Hex(),
		MemberId:   o.MemberId.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_ORDER_PROFIT_AMOUNT,
		CreateTime: profitedAt.UnixNano() / 1e6,
		// 非分销月结不扣算个税
		EventProperties: map[string]interface{}{
			"promoterId":   o.Distribution.PromoterId.Hex(),
			"memberId":     o.MemberId.Hex(),
			"profitAmount": o.Distribution.Amount,
			"tax":          0,
			"income":       o.Distribution.Amount,
		},
	}

	eventBody.EventProperties["promoterType"] = o.Distribution.PromoterType
	if o.StoreId.Hex() != "" {
		eventBody.EventProperties["storeId"] = o.StoreId.Hex()
	}

	eventBody.Send(ctx)
}

func (o *Order) UpdateProducts(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": o.AccountId,
		"isDeleted": false,
	}

	updater := bson.M{
		"$set": bson.M{
			"updatedAt": time.Now(),
			"products":  o.Products,
		},
	}

	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (o *Order) UpdateProductsAndProfitTypes(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": o.AccountId,
		"isDeleted": false,
	}

	updater := bson.M{
		"$set": bson.M{
			"updatedAt":   time.Now(),
			"products":    o.Products,
			"profitTypes": o.ProfitTypes,
		},
	}

	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (*Order) UpdateOmsPushStatus(ctx context.Context, ids []bson.ObjectId, toStatus string) error {
	if len(ids) == 0 {
		return nil
	}
	selector := bson.M{
		"_id":                     bson.M{"$in": ids},
		"omsProcessor.pushStatus": OMS_ORDER_PUSH_STATUS_PROCESSING,
	}
	updator := bson.M{
		"$set": bson.M{
			"omsProcessor.pushStatus": toStatus,
			"omsProcessor.pushedAt":   time.Now(),
			"updatedAt":               time.Now(),
		},
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_ORDER, selector, updator)
	return err
}

func (*Order) UpdateAllOmsPushStatusToProcessing(ctx context.Context) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["omsProcessor.pushStatus"] = OMS_ORDER_PUSH_STATUS_PENDING

	updator := bson.M{
		"$set": bson.M{
			"omsProcessor.pushStatus": OMS_ORDER_PUSH_STATUS_PROCESSING,
			"updatedAt":               time.Now(),
		},
	}

	_, err := extension.DBRepository.UpdateAll(ctx, C_ORDER, selector, updator)
	return err
}

func (*Order) BatchUpdateMemberId(ctx context.Context, oldMemberIds []bson.ObjectId, newMemberId bson.ObjectId) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["memberId"] = bson.M{
		"$in": oldMemberIds,
	}

	updator := bson.M{
		"$set": bson.M{
			"memberId":  newMemberId,
			"updatedAt": time.Now(),
		},
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_ORDER, selector, updator)
	return err
}

func (o *Order) UpdateOmsPushStatusToPending(ctx context.Context) error {
	// 施华蔻租户，只有「施华蔻官方精品商城」门店走旺店通
	// https://gitlab.maiscrm.com/mai/impl/home/<USER>/issues/7879#note_5096153
	if core_util.GetAccountId(ctx) == "5f8d06e621b41201355c72c2" && o.StoreId.Hex() != "5ff29fd8c61a3b3a240c1323" {
		return nil
	}
	o.OmsProcessor.PushStatus = OMS_ORDER_PUSH_STATUS_PENDING
	selector := Common.GenDefaultConditionById(ctx, o.Id)
	updator := bson.M{
		"$set": bson.M{
			"omsProcessor.pushStatus": OMS_ORDER_PUSH_STATUS_PENDING,
			"updatedAt":               time.Now(),
		},
	}
	return Common.UpdateOne(ctx, C_ORDER, "", selector, updator)
}

func (o *Order) CanRefund(ctx context.Context, isRefundByMember bool, wechatRefundId int64) bool {
	// 普通订单退款状态为 unpaid 时，不予退款
	if len(o.TradeRecords) == 0 && o.Status == ORDER_STATUS_UNPAID {
		return false
	}

	// 预售活动订单退定金确认定金为已付款状态
	if len(o.TradeRecords) > 0 && o.Status == ORDER_STATUS_UNPAID {
		isDepositPaid := false
		for _, t := range o.TradeRecords {
			if t.Status == ORDER_STATUS_PAID {
				isDepositPaid = true
			}
		}
		if !isDepositPaid {
			return false
		}
	}

	// 支付时间在一年前时，不允许退款
	if !o.PaidAt.IsZero() && o.PaidAt.Before(time.Now().AddDate(-1, 0, 0)) {
		return false
	}

	if o.Payment != "" && !util.StrInArray(o.Payment, &CAN_REFOUND_ORDER_PAYMENT) {
		return false
	}

	// 已完成的订单不允许客户主动退款
	// 已完成的订单后台退款时订单完成 n 天后不允许退款
	orderSetting, _ := ec_client.SettingService.GetOrderSetting(ctx, &request.EmptyRequest{})
	allowedRefundCompletedDays := time.Duration(15)
	if orderSetting != nil && orderSetting.AllowedRefundCompletedDays != 0 {
		allowedRefundCompletedDays = time.Duration(orderSetting.AllowedRefundCompletedDays)
	}
	if o.Status == ORDER_STATUS_COMPLETED {
		completedTime := o.GetCompletedTime()
		if completedTime.Unix() > 0 && completedTime.Add(allowedRefundCompletedDays*24*time.Hour).Before(time.Now()) || isRefundByMember {
			// 如果不是微信侧发起退款回调，那么不允许已经完成的订单退款
			if wechatRefundId == 0 {
				return false
			}
		}
	}

	return true
}

func (o *Order) CountPromoterOrder(ctx context.Context, promoter *distribution_model.Promoter, timeSpan bson.M, setting *distribution_model.DistributionSetting, lastMonthKey string) *distribution_model.PromoterCount {
	tmpCount := &distribution_model.PromoterCount{}

	selector := bson.M{
		"accountId":               util.GetAccountIdAsObjectId(ctx),
		"isDeleted":               false,
		"distribution.promoterId": promoter.Id,
		"completedAt":             timeSpan,
	}

	iter, err := o.Iterate(ctx, selector, []string{})
	if err != nil {
		return tmpCount
	}
	defer iter.Close()

	order := &Order{}
	for iter.Next(order) {
		tmpCount.TotalAmount += int64(order.PayAmount)
		if order.Method == ORDER_DELIVERY_METHOD_EXPRESS {
			tmpCount.TotalAmount -= int64(order.Logistics.Fee)
		}
		for _, p := range order.Products {
			if IsInRefundProcess(p.RefundStatus) {
				tmpCount.TotalAmount -= int64(p.TotalAmount)
			}
		}

		tmpCount.Order += 1

		if order.RefundStatus != "" {
			tmpCount.RefundOrder += 1
			tmpCount.TotalAmount -= int64(countRefundAmount(ctx, order.Products))
		}
	}

	if promoter.Type == "member" {
		tmpCount.Amount = COrderReceiverProfit.GetPromoterMonthlyProfitAmount(ctx, promoter.Id)
	}

	return tmpCount
}

func countRefundAmount(ctx context.Context, products []OrderProduct) uint64 {
	tmpRefundAmount := uint64(0)
	if len(products) == 0 {
		return tmpRefundAmount
	}

	for _, p := range products {
		if IsInRefundProcess(p.RefundStatus) {
			tmpRefundAmount += p.PayAmount
		}
	}

	return tmpRefundAmount
}

func (o *Order) UpdateDistribution(ctx context.Context) error {
	selector := bson.M{
		"accountId": o.AccountId,
		"_id":       o.Id,
		"isDeleted": false,
	}

	updater := bson.M{
		"$set": bson.M{
			"updatedAt":    time.Now(),
			"distribution": o.Distribution,
		},
	}

	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (o *Order) HasBeenCompleted() bool {
	for _, h := range o.Histories {
		if h.Status == ORDER_STATUS_COMPLETED {
			return true
		}
	}
	return false
}

func (o *Order) HasBeenShipped() bool {
	for _, h := range o.Histories {
		if h.Status == ORDER_STATUS_SHIPPED {
			return true
		}
	}
	return false
}

func (o *Order) ContainProductRefunded() bool {
	hasBeenRefunded := false
	for _, p := range o.Products {
		if core_util.StrInArray(p.RefundStatus, &[]string{
			ORDER_REFUND_STATUS_REFUNDING,
			ORDER_REFUND_STATUS_REFUNDED,
			ORDER_REFUND_STATUS_FAILED,
		}) {
			hasBeenRefunded = true
		}
	}

	return hasBeenRefunded
}

func (o *Order) IsAllDistributionProductsRefunding(ctx context.Context) bool {
	allRefunded := true
	productRefundAmountMap := o.GetProductRefundAmountMap(ctx)
	for _, p := range o.Products {
		if !p.IsDistribution {
			continue
		}
		if util.StrInArray(p.RefundStatus, &[]string{
			"",
			ORDER_REFUND_STATUS_REJECTED,
			ORDER_REFUND_STATUS_CANCELED,
		}) {
			allRefunded = false
			continue
		}
		// 处理部分退款的情况
		refundAmount, ok := productRefundAmountMap[p.OutTradeId.Hex()]
		if ok && refundAmount < p.PayAmount {
			allRefunded = false
		}
	}
	return allRefunded
}

func (*Order) StatisticMonthlyDistributionAmount(ctx context.Context, receiverId, receiverAccount, receiverType string) (bson.ObjectId, bson.ObjectId, uint64, uint64, bool, int, error) {
	bill, _ := profit_model.CTransferBill.GetByOutTradeNo(ctx, receiverId+"-"+time.Now().Format("2006-01"))
	if bill.Id.Hex() != "" {
		return "", "", 0, 0, false, 0, errors.NewAlreadyExistsError("transferBill")
	}

	var (
		promoter *ec_distribution.Promoter
		err      error
	)
	if receiverType == PROMOTER_RECEIVER_TYPE_MERCHANT {
		promoter, err = distribution_model.CPromoter.GetByMerchantId(ctx, receiverAccount)
	} else {
		promoter, err = distribution_model.CPromoter.GetByOpenId(ctx, receiverAccount)
	}
	if err != nil || promoter.Id.Hex() == "" {
		return "", "", 0, 0, false, 0, errors.NewNotExistsErrorWithMessage("promoter", err.Error())
	}

	dSetting, err := distribution_model.CDistributionSetting.GetByType(ctx, promoter.Type)
	if err != nil {
		return "", "", 0, 0, false, 0, errors.NewNotExistsErrorWithMessage("distributionSetting", err.Error())
	}

	startTime, endTime := util.GetLastMonthTimeSpan(time.Now())
	selector := bson.M{
		"accountId":                        promoter.AccountId,
		"distribution.promoterId":          promoter.Id,
		"distribution.profitSharingType":   ec_model_setting.COMMMISSION_TYPE_MONTHLY,
		"distribution.profitSharingStatus": PROFIT_SHARING_STATUS_PENDING,
		"distribution.transferBillId":      bson.M{"$exists": false},
		"isDeleted":                        false,
		"completedAt": bson.M{
			"$gte": startTime,
			"$lte": endTime,
		},
	}
	// 为大众分销创建 transferBill 时，需要判断是否已存在导购分销员
	if promoter.Type == distribution_model.PROMOTER_TYPE_MEMBER && promoter.Phone != "" {
		staffDistSetting, err := client.DistributionService.GetDistributionSetting(ctx, &pb_distribution.GetDistributionSettingRequest{
			SettingType: "staff",
		})
		if err != nil {
			return "", "", 0, 0, false, 0, errors.NewNotExistsErrorWithMessage("distributionSetting", fmt.Sprintf("Failed to get staff distribution setting, %s", err.Error()))
		}
		if staffDistSetting.Enabled {
			staff, _ := client.StoreService.GetStaff(ctx, &store.StaffDetailRequest{
				Phone: promoter.Phone,
			})
			// 如果导购分销开启且手机号对应的导购已存在，并且仍在职，则不进行大众分销
			if staff != nil && staff.Status == 1 {
				updater := bson.M{
					"$set": bson.M{
						"distribution.profitSharingStatus": DISTRIBUTION_STATUS_FAILED,
						"distribution.profitFailedMsg":     fmt.Sprintf("存在相同手机号的导购分销员：%s", staff.Phone),
					},
				}
				extension.DBRepository.UpdateAll(ctx, C_ORDER, selector, updater)
				return "", "", 0, 0, false, 0, errors.NewInvalidArgumentErrorWithMessage("phone", "Staff promoter already exists")
			}
		}
	}

	iter, err := COrder.Iterate(ctx, selector, []string{})
	if err != nil {
		return "", "", 0, 0, false, 0, err
	}
	defer iter.Close()
	var (
		order = &Order{}
		// 分销员当月的销售额
		salesAmount = uint64(0)
		// 分销员当月的分销佣金
		distAmount          = uint64(0)
		docs                []interface{}
		transferBillId      = bson.NewObjectId()
		orderSalesAmountMap = make(map[bson.ObjectId]uint64)
		orderCount          int
	)
	for iter.Next(order) {
		var (
			orderSalesAmount = order.GetRealPayAmount()
			orderDistAmount  = uint64(0)
		)
		orderCount++
		productRefundAmountMap := order.GetProductRefundAmountMap(ctx)
		salesAmount += order.GetRealPayAmount()
		if order.Method == ORDER_DELIVERY_METHOD_EXPRESS {
			salesAmount -= order.Logistics.Fee
			orderSalesAmount -= order.Logistics.Fee
		}

		for _, p := range order.Products {
			refundAmount, ok := productRefundAmountMap[p.OutTradeId.Hex()]
			if p.RefundStatus != "" {
				if ok && refundAmount < p.PayAmount {
					salesAmount -= refundAmount
					orderSalesAmount -= refundAmount
				} else {
					salesAmount -= p.PayAmount
					orderSalesAmount -= p.PayAmount
				}
			}
			if p.IsDistribution {
				productDistAmount := p.DistributionAmount
				if p.RefundStatus != "" {
					productDistAmount = 0
				}
				if p.RefundStatus != "" && ok && refundAmount < p.PayAmount {
					if p.DistributionProportion > 0 {
						// 销售额比例分佣
						productDistAmount = cast.ToUint64(util.MultiplyFloat(util.MultiplyFloat(p.DistributionProportion, float64(p.PayAmount-refundAmount)), 0.01))
					} else {
						// 固定金额分佣
						// empty
					}
				}
				orderDistAmount += productDistAmount
				distAmount += productDistAmount
			}
		}
		orderSalesAmountMap[order.Id] = orderSalesAmount
		docs = append(docs,
			bson.M{
				"_id": order.Id,
			},
			bson.M{
				"$set": bson.M{
					"distribution.amount":         orderDistAmount,
					"distribution.transferBillId": transferBillId,
					"updatedAt":                   time.Now(),
				},
			})
	}

	if dSetting.PromoterCommission.CommissionType == distribution_model.PROPORTION_TYPE_ORDER {
		distAmount = COrder.GetOrderMonthlyDistAmount(ctx, salesAmount, dSetting, promoter)
		if distAmount > 0 {
			restAmount := distAmount
			index := 0
			docs = []interface{}{}
			for id, amount := range orderSalesAmountMap {
				orderDistAmount := cast.ToUint64(util.MultiplyFloat(float64(distAmount), util.DivideFloat(float64(amount), float64(salesAmount))))
				if index == len(orderSalesAmountMap)-1 {
					// 最后一个直接用 restAmount
					orderDistAmount = restAmount
				} else {
					restAmount -= orderDistAmount
				}
				docs = append(docs,
					bson.M{
						"_id": id,
					},
					bson.M{
						"$set": bson.M{
							"distribution.amount":         orderDistAmount,
							"distribution.transferBillId": transferBillId,
							"updatedAt":                   time.Now(),
						},
					},
				)
				index++
			}
		}
	}

	if distAmount == 0 {
		_, err = extension.DBRepository.UpdateAll(ctx, C_ORDER, selector, bson.M{"$set": bson.M{
			"updatedAt":                        time.Now(),
			"distribution.extra.无分佣原因":         "参与分佣金额为 0",
			"distribution.profitSharingStatus": DISTRIBUTION_STATUS_NOCOMMISSION,
		}})
	} else {
		_, dbErr := extension.DBRepository.BatchUpdateUnordered(ctx, C_ORDER, docs...)
		if dbErr != nil && len(dbErr.WriteErrors) > 0 {
			err = dbErr
		}
	}
	if err != nil {
		return "", "", 0, 0, false, 0, err
	}
	return promoter.Id, transferBillId, distAmount, calTaxForDistAmount(distAmount, dSetting), dSetting.PromoterCommission.DeductTax, orderCount, nil
}

func (*Order) StatisticDistributionAmountByMonth(ctx context.Context, receiverId, promoterId string, dSetting *ec_distribution.DistributionSetting, from, to time.Time, debug bool) (bson.ObjectId, uint64, uint64, int, error) {
	outTradeNo := receiverId + "-" + from.AddDate(0, 1, 0).Format("2006-01")
	bill, _ := profit_model.CTransferBill.GetByOutTradeNo(ctx, outTradeNo)
	if bill.Id.Hex() != "" {
		return "", 0, 0, 0, errors.NewAlreadyExistsError("transferBill")
	}

	promoter, err := distribution_model.CPromoter.GetById(ctx, bson.ObjectIdHex(promoterId))
	if err != nil || promoter.Id.Hex() == "" {
		return "", 0, 0, 0, err
	}

	selector := bson.M{
		"accountId":                        util.GetAccountIdAsObjectId(ctx),
		"distribution.promoterId":          bson.ObjectIdHex(promoterId),
		"distribution.profitSharingType":   ec_model_setting.COMMMISSION_TYPE_MONTHLY,
		"distribution.profitSharingStatus": PROFIT_SHARING_STATUS_PENDING,
		"distribution.transferBillId":      bson.M{"$exists": false},
		"isDeleted":                        false,
		"completedAt": bson.M{
			"$gte": from,
			"$lte": to,
		},
	}

	iter, err := COrder.Iterate(ctx, selector, []string{})
	if err != nil {
		return "", 0, 0, 0, err
	}
	defer iter.Close()

	order := &Order{}
	// 分销员当月的销售额
	salesAmount := uint64(0)
	// 分销员当月的分销佣金
	distAmount := uint64(0)
	orderCount := 0
	for iter.Next(order) {
		orderCount++
		salesAmount += order.PayAmount
		if order.Method == ORDER_DELIVERY_METHOD_EXPRESS {
			salesAmount -= order.Logistics.Fee
		}

		for _, p := range order.Products {
			if IsInRefundProcess(p.RefundStatus) {
				salesAmount -= p.TotalAmount
			}
			if p.IsDistribution && !IsInRefundProcess(p.RefundStatus) {
				distAmount += p.DistributionAmount
			}
		}
	}

	transferBillId := bson.NewObjectId()
	updater := bson.M{
		"$set": bson.M{
			"distribution.transferBillId": transferBillId,
			"updatedAt":                   time.Now(),
		},
	}

	if !debug {
		_, err = extension.DBRepository.UpdateAll(ctx, C_ORDER, selector, updater)
		if err != nil {
			return "", 0, 0, 0, err
		}
	}

	if dSetting.PromoterCommission.CommissionType == distribution_model.PROPORTION_TYPE_ORDER {
		distAmount = COrder.GetOrderMonthlyDistAmount(ctx, salesAmount, dSetting, promoter)
	}

	log.Warn(ctx, "TransferBill", log.Fields{
		"distAmount": distAmount,
		"outTradeNo": outTradeNo,
	})

	return transferBillId, distAmount, calTaxForDistAmount(distAmount, dSetting), orderCount, nil
}

func (Order) UpdateAllCommented(ctx context.Context, ids []bson.ObjectId) error {
	if len(ids) == 0 {
		return nil
	}
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       bson.M{"$in": ids},
	}
	setter := bson.M{
		"isCommented": true,
	}
	// 元祖订单量比较大，更新 1 万后，三方同步时数据库负载会很高，不处理 updatedAt
	if util.GetAccountId(ctx) != "5f96a11479f7565e230fbe58" {
		setter["updatedAt"] = time.Now()
	}

	updator := bson.M{
		"$set": setter,
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_ORDER, selector, updator)
	return err
}

func (*Order) GetOrderMonthlyDistAmount(ctx context.Context, salesAmount uint64, dSetting *distribution_model.DistributionSetting, promoter *distribution_model.Promoter) uint64 {
	if len(dSetting.PromoterCommission.Range) == 0 {
		return 0
	}
	if salesAmount == 0 {
		return 0
	}

	staffId := ""
	if promoter.Type == distribution_model.PROMOTER_TYPE_STAFF {
		staffId = promoter.StaffId.Hex()
	}
	switch dSetting.PromoterCommission.CommissionType {
	case distribution_model.PROPORTION_TYPE_ORDER:
		switch dSetting.PromoterCommission.CalculateType {
		// 根据上月销售额计算比例
		case distribution_model.CALCULATE_TYPE_LAST_MONTH_PROPORTION:
			// 下单订单的上个月，本月结算的上个月的订单
			lastMonthKey := util.GetMonthCountKey(time.Now(), -2)
			matchedRange := dSetting.PromoterCommission.Range[0]
			if lastMonthCount, ok := promoter.Count[lastMonthKey]; ok {
				for _, r := range dSetting.PromoterCommission.Range {
					if lastMonthCount.TotalAmount >= int64(r.Start) && lastMonthCount.TotalAmount < int64(r.End) {
						matchedRange = r
						break
					}
				}
			}
			proportion, _ := matchedRange.GetProportionAndAmount(ctx, -1, staffId)
			return uint64((proportion * float64(salesAmount)) / 100)
			// 根据当月销售额计算提成金额
		case distribution_model.CALCULATE_TYPE_CURRENT_MONTH_AMOUNT:
			for _, r := range dSetting.PromoterCommission.Range {
				if int64(salesAmount) >= int64(r.Start) && int64(salesAmount) < int64(r.End) {
					_, amount := r.GetProportionAndAmount(ctx, -1, staffId)
					return amount
				}
			}
			// 根据当月销售额计算百分比
		case distribution_model.CALCULATE_TYPE_CURRENT_MONTH_PROPORTION:
			for _, r := range dSetting.PromoterCommission.Range {
				if int64(salesAmount) >= int64(r.Start) && int64(salesAmount) < int64(r.End) {
					proportion, _ := r.GetProportionAndAmount(ctx, -1, staffId)
					return uint64((proportion * float64(salesAmount)) / 100)
				}
			}
		}
	case distribution_model.PROPORTION_TYPE_PRODUCT:
		// 商品分佣是在订单上提前计算完成的，这里的 salesAmount 即是总分佣金额
		return uint64((float64(salesAmount)) / 100)
	}

	return 0
}

func calTaxForDistAmount(distAmount uint64, dSetting *distribution_model.DistributionSetting) uint64 {
	if !dSetting.PromoterCommission.DeductTax {
		return 0
	}

	testAccountId := []string{
		"5e7873c4c3307000f272c9e2",
		"5e7873b6f4d23700fa322bf2",
	}

	var tax float64
	calAmount := float64(distAmount) / 100
	if util.StrInArray(dSetting.AccountId.Hex(), &testAccountId) {
		// 当 distAmount 大于等于 0.83 时符合扣个税的条件
		calAmount *= 1000
	}

	if calAmount > 800 && calAmount <= 4000 {
		tax = (calAmount - 800) * 0.2
	} else if calAmount > 4000 && calAmount <= 25000 {
		tax = (calAmount - calAmount*0.2) * 0.2
	} else if calAmount > 25000 && calAmount <= 62500 {
		tax = (calAmount-calAmount*0.2)*0.3 - 2000
	} else if calAmount > 62500 {
		tax = (calAmount-calAmount*0.2)*0.4 - 7000
	}

	if util.StrInArray(dSetting.AccountId.Hex(), &testAccountId) {
		tax /= 1000
	}
	return uint64(math.Floor(tax*100 + 0.5)) // 由于小数转整数是向下取整，所以 + 0.5 来达到四舍五入的效果
}

func (Order) CountOrderWithGroupon(ctx context.Context, campaignId bson.ObjectId, grouponRecordId bson.ObjectId) (int, error) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"status": bson.M{
			"$in": []string{ORDER_STATUS_PAID, ORDER_STATUS_UNASSIGNED, ORDER_STATUS_ACCEPTED, ORDER_STATUS_UNPAID}, // 统计订单时，不计入交易结束的订单
		},
		"campaigns": bson.M{
			"$elemMatch": bson.M{
				"id":              campaignId,
				"type":            CAMPAIGN_TYPE_GROUPON,
				"grouponRecordId": grouponRecordId,
				"grouponStatus":   ec_marketing.GROUPON_RECORD_STATUS_RUNNING,
			},
		},
		"isDeleted": false,
	}
	if ec_model.IsSplitOrderAccount(ctx) {
		selector["tags"] = SPLIT_ORDER_TAG_SUB_ORDER
	}
	return extension.DBRepository.Count(ctx, C_ORDER, selector)
}

func (Order) GetByMemberAndCampaign(ctx context.Context, memberId, campaignId bson.ObjectId) ([]Order, error) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"memberId":  memberId,
		"campaigns": bson.M{
			"$elemMatch": bson.M{
				"id": campaignId,
			},
		},
		"status": bson.M{
			"$in": []string{ORDER_STATUS_PAID, ORDER_STATUS_UNASSIGNED, ORDER_STATUS_ACCEPTED, ORDER_STATUS_UNPAID}, // 统计订单时，不计入未付款和交易结束的订单
		},
	}
	sortor := []string{"-createdAt"}
	result := []Order{}
	err := extension.DBRepository.FindAll(ctx, C_ORDER, selector, sortor, 0, &result)
	return result, err
}

func (Order) UpdateOrderCommented(ctx context.Context, orderId bson.ObjectId) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       orderId,
	}

	setter := bson.M{
		"isCommented": true,
		"updatedAt":   time.Now(),
	}

	updater := bson.M{
		"$set": setter,
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (Order) UpdateOrderProductsCommented(ctx context.Context, comment Comment) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       comment.Order.Id,
		"products": bson.M{
			"$elemMatch": bson.M{"id": comment.Product.Id, "spec.sku": comment.Product.Spec.Sku},
		},
	}

	setter := bson.M{
		"products.$.isCommented": true,
		"updatedAt":              time.Now(),
	}

	updater := bson.M{
		"$set": setter,
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (Order) GetByPagination(ctx context.Context, page extension.PagingCondition) (int, []Order, error) {
	var orders []Order
	total, err := extension.DBRepository.FindByPagination(ctx, C_ORDER, page, &orders)
	if err != nil {
		return 0, nil, err
	}
	return total, orders, nil
}

func (Order) GetAllByPagination(ctx context.Context, pageCond extension.PagingCondition) []Order {
	var orders []Order
	extension.DBRepository.FindByPaginationWithoutCount(ctx, C_ORDER, pageCond, &orders)
	return orders
}

func (Order) GetMemberCount(ctx context.Context, selector bson.M) (int, error) {
	result := bson.M{}
	pipeline := []bson.M{
		{
			"$match": selector,
		},
		{
			"$group": bson.M{"_id": "$memberId"},
		},
		{
			"$group": bson.M{
				"_id":         "null",
				"memberCount": bson.M{"$sum": 1},
			},
		},
	}

	err := extension.DBRepository.Aggregate(ctx, C_ORDER, pipeline, true, &result)
	if err != nil {
		return 0, err
	}
	return cast.ToInt(result["memberCount"]), nil
}

func (Order) GetTotalPieces(ctx context.Context, selector bson.M) (int, error) {
	result := bson.M{}
	pipeline := []bson.M{
		{
			"$match": selector,
		},
		{
			"$unwind": "$products",
		},
		{
			"$group": bson.M{
				"_id":   "$_id",
				"total": bson.M{"$sum": "$products.total"},
			},
		},
		{
			"$group": bson.M{
				"_id":         "null",
				"totalPieces": bson.M{"$sum": "$total"},
			},
		},
	}

	err := extension.DBRepository.Aggregate(ctx, C_ORDER, pipeline, true, &result)
	if err != nil {
		return 0, err
	}
	return cast.ToInt(result["totalPieces"]), nil
}

func (Order) GetMallOrderStats(ctx context.Context, selector bson.M) (bson.M, error) {
	result := bson.M{}
	pipeline := []bson.M{
		{
			"$match": selector,
		},
		{
			"$group": bson.M{
				"_id":         "null",
				"totalAmount": bson.M{"$sum": "$totalAmount"},
				"totalOrder":  bson.M{"$sum": 1},
			},
		},
	}

	err := extension.DBRepository.Aggregate(ctx, C_ORDER, pipeline, true, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (Order) GetStoreAmountAndOrderRankWithSort(ctx context.Context, selector bson.M, sortField string) ([]bson.M, error) {
	group := bson.M{
		"_id": "$storeId",
		"totalAmount": bson.M{
			"$sum": "$payAmount",
		},
		"totalFee": bson.M{
			"$sum": "$logistics.fee",
		},
		"totalOrder": bson.M{
			"$sum": 1,
		},
	}
	project := bson.M{
		"_id": 1,
		"totalAmount": bson.M{
			"$subtract": []string{"$totalAmount", "$totalFee"},
		},
		"totalOrder": 1,
	}
	var sorter bson.M
	if sortField[0:1] == "-" {
		sorter = bson.M{
			sortField[1:]: -1,
		}
	} else {
		sorter = bson.M{
			sortField: 1,
		}
	}

	limit := bson.M{
		"$limit": 10,
	}
	pipeline := []bson.M{
		{"$match": selector},
		{"$group": group},
		{"$project": project},
		{"$sort": sorter},
		limit,
	}

	var result []bson.M
	err := extension.DBRepository.Aggregate(ctx, C_ORDER, pipeline, false, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (Order) GetStoreAmountAndOrderRank(ctx context.Context, selector bson.M) ([]bson.M, error) {
	group := bson.M{
		"_id": "$storeId",
		"totalAmount": bson.M{
			"$sum": "$payAmount",
		},
		"totalFee": bson.M{
			"$sum": "$logistics.fee",
		},
		"totalOrder": bson.M{
			"$sum": 1,
		},
	}
	project := bson.M{
		"_id": 1,
		"totalAmount": bson.M{
			"subtract": []string{"$totalAmount", "$totalFee"},
		},
		"totalOrder": 1,
	}
	limit := bson.M{
		"$limit": 10,
	}
	pipeline := []bson.M{
		{"$match": selector},
		{"$group": group},
		{"$project": project},
		limit,
	}
	var result []bson.M
	err := extension.DBRepository.Aggregate(ctx, C_ORDER, pipeline, false, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (Order) GetStoreMemberRank(ctx context.Context, selector bson.M, sortField string) ([]bson.M, error) {
	groupByStoreIdAndMemberId := bson.M{
		"_id": bson.M{
			"storeId":  "$storeId",
			"memberId": "$memberId",
		},
	}
	groupByStoreId := bson.M{
		"_id": "$_id.storeId",
		"totalMember": bson.M{
			"$sum": 1,
		},
	}
	var sorter bson.M
	if sortField[0:1] == "-" {
		sorter = bson.M{
			sortField[1:]: -1,
		}
	} else {
		sorter = bson.M{
			sortField: 1,
		}
	}

	limit := bson.M{
		"$limit": 10,
	}

	pipeline := []bson.M{
		{"$match": selector},
		{"$group": groupByStoreIdAndMemberId},
		{"$group": groupByStoreId},
		{"$sort": sorter},
		limit,
	}

	var result []bson.M
	err := extension.DBRepository.Aggregate(ctx, C_ORDER, pipeline, false, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (Order) GetMemberOrderStats(ctx context.Context, selector bson.M, listCondition *request.ListCondition) ([]bson.M, error) {
	sortByCreatedAt := bson.M{
		"$sort": bson.M{
			"createdAt": -1,
		},
	}
	group := bson.M{
		"_id": "$memberId",
		"phone": bson.M{
			"$first": "$contact.phone",
		},
		"name": bson.M{
			"$first": "$contact.name",
		},
		"memberId": bson.M{
			"$first": "$memberId",
		},
		"totalAmount": bson.M{
			"$sum": "$totalAmount",
		},
		"totalOrder": bson.M{
			"$sum": 1,
		},
		"lastPayAt": bson.M{
			"$first": "$createdAt",
		},
	}
	project := bson.M{
		"_id":         1,
		"phone":       1,
		"name":        1,
		"memberId":    1,
		"totalAmount": 1,
		"totalOrder":  1,
		"lastPayAt":   1,
		"average": bson.M{
			"$divide": []string{"$totalAmount", "$totalOrder"},
		},
	}

	pipeline := []bson.M{
		{"$match": selector},
		sortByCreatedAt,
		{"$group": group},
		{"$project": project},
	}

	sortField := ""
	if listCondition != nil && len(listCondition.OrderBy) > 0 {
		sortField = listCondition.OrderBy[0]
	}

	if sortField != "" {
		if strings.HasPrefix(sortField, "-") {
			pipeline = append(pipeline, bson.M{
				"$sort": bson.M{
					sortField[1:]: -1,
				},
			})
		} else {
			pipeline = append(pipeline, bson.M{
				"$sort": bson.M{
					sortField: 1,
				},
			})
		}
	}
	if listCondition != nil {
		skip := bson.M{
			"$skip": (listCondition.Page - 1) * listCondition.PerPage,
		}
		limit := bson.M{
			"$limit": listCondition.PerPage,
		}
		pipeline = append(pipeline, skip, limit)
	}

	var result []bson.M
	err := extension.DBRepository.Aggregate(ctx, C_ORDER, pipeline, false, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (Order) GetStoreOrderMemberCount(ctx context.Context, selector bson.M) (int, error) {
	group := bson.M{
		"_id": "$memberId",
	}
	countGroup := bson.M{
		"_id": "null",
		"total": bson.M{
			"$sum": 1,
		},
	}
	pipeline := []bson.M{
		{"$match": selector},
		{"$group": group},
		{"$group": countGroup},
	}

	var result bson.M
	err := extension.DBRepository.Aggregate(ctx, C_ORDER, pipeline, true, &result)
	if err != nil {
		return 0, err
	}
	return cast.ToInt(result["total"]), nil
}

func (*Order) DeleteById(ctx context.Context, orderId bson.ObjectId) error {
	selector := bson.M{
		"_id":       orderId,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"isDeleted": true,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (*Order) UpdateDistributorIdsByStoreId(ctx context.Context, storeId bson.ObjectId, distributorIds []bson.ObjectId) error {
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"storeId":   storeId,
	}
	updater := bson.M{
		"$set": bson.M{
			"distributorIds": distributorIds,
			"updatedAt":      time.Now(),
		},
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_ORDER, condition, updater)
	return err
}

func (o *Order) GetOutTradeId() string {
	if o.OutTradeId.Hex() != "" {
		return o.OutTradeId.Hex()
	}
	return o.Id.Hex()
}

func TransformProperties(orderProperties []ProductSpecProperty) []string {
	properties := []string{}
	for _, property := range orderProperties {
		properties = append(properties, property.Value)
	}
	return properties
}

func (Order) GetTotalDistributionAmountByPromoterIdAndStatus(ctx context.Context, status []string, promoterId bson.ObjectId, dateRange *types.StringDateRange) (map[string]uint64, error) {
	selector := share_model.Base.GenDefaultCondition(ctx)

	selector["distribution.promoterId"] = promoterId
	selector["distribution.profitSharingStatus"] = bson.M{
		"$in": status,
	}

	if dateRange != nil {
		selector["paidAt"] = util.ParseStringDateRange(dateRange)
	}

	result := []bson.M{}
	pipeline := []bson.M{
		{
			"$match": selector,
		},
		{
			"$group": bson.M{
				"_id":   "$distribution.profitSharingStatus",
				"total": bson.M{"$sum": "$distribution.amount"},
			},
		},
	}

	err := extension.DBRepository.Aggregate(ctx, C_ORDER, pipeline, false, &result)
	if err != nil {
		return nil, err
	}
	statsMapper := make(map[string]uint64, len(result))
	for _, stats := range result {
		statsMapper[cast.ToString(stats["_id"])] = cast.ToUint64(stats["total"])
	}
	return statsMapper, nil
}

func (Order) GetTotalAmountByPromoterIdAndStatus(ctx context.Context, promoterId bson.ObjectId, parentPromoterIds []bson.ObjectId, dateRange *types.StringDateRange) (map[string]uint64, error) {
	selector := share_model.Base.GenDefaultCondition(ctx)

	if len(parentPromoterIds) != 0 {
		selector["distribution.promoterId"] = bson.M{"$in": parentPromoterIds}
		selector["distribution.subPromoterId"] = promoterId
	} else {
		selector["distribution.promoterId"] = promoterId
		selector["distribution.subPromoterId"] = bson.M{"$exists": false}
	}

	selector["paidAt"] = bson.M{
		"$exists": true,
	}

	// 累计销售额需要，排除已退款的订单支付金额
	selector["status"] = bson.M{
		"$ne": ORDER_STATUS_CANCELED,
	}

	if dateRange != nil {
		selector["paidAt"] = util.ParseStringDateRange(dateRange)
	}

	result := []bson.M{}
	pipeline := []bson.M{
		{
			"$match": selector,
		},
		{
			"$unwind": "$products",
		},
		{
			"$project": bson.M{
				"payAmount": bson.M{
					"$cond": bson.M{
						"if": bson.M{
							"$eq": []string{"$products.refundStatus", "refunded"}, // 已退款的需要减去
						},
						"then": 0,
						"else": "$products.payAmount",
					},
				},
			},
		},
		{
			"$group": bson.M{
				"_id": "null",
				"total": bson.M{
					"$sum": "$payAmount",
				},
			},
		},
	}

	err := extension.DBRepository.Aggregate(ctx, C_ORDER, pipeline, false, &result)
	if err != nil {
		return nil, err
	}
	statsMapper := make(map[string]uint64, len(result))
	for _, stats := range result {
		statsMapper[cast.ToString(stats["_id"])] = cast.ToUint64(stats["total"])
	}
	return statsMapper, nil
}

func (Order) GetDistProductTotalAmountByPromoterIdAndStatus(ctx context.Context, promoterId bson.ObjectId, parentPromoterIds []bson.ObjectId, dateRange *types.StringDateRange) (map[string]uint64, error) {
	selector := share_model.Base.GenDefaultCondition(ctx)

	if len(parentPromoterIds) != 0 {
		selector["distribution.promoterId"] = bson.M{"$in": parentPromoterIds}
		selector["distribution.subPromoterId"] = promoterId
	} else {
		selector["distribution.promoterId"] = promoterId
		selector["distribution.subPromoterId"] = bson.M{"$exists": false}
	}

	selector["paidAt"] = bson.M{
		"$exists": true,
	}
	selector["status"] = bson.M{
		"$ne": ORDER_STATUS_CANCELED,
	}

	if dateRange != nil {
		selector["paidAt"] = util.ParseStringDateRange(dateRange)
	}

	result := []bson.M{}
	pipeline := []bson.M{
		{
			"$match": selector,
		},
		{
			"$unwind": "$products",
		},
		{
			"$match": bson.M{
				"products.isDistribution": true,
				"products.refundStatus": bson.M{
					"$exists": false,
				},
			},
		},
		{
			"$group": bson.M{
				"_id": "null",
				"total": bson.M{
					"$sum": "$products.payAmount",
				},
			},
		},
	}

	err := extension.DBRepository.Aggregate(ctx, C_ORDER, pipeline, false, &result)
	if err != nil {
		return nil, err
	}

	statsMapper := make(map[string]uint64, len(result))
	for _, stats := range result {
		statsMapper[cast.ToString(stats["_id"])] = cast.ToUint64(stats["total"])
	}
	return statsMapper, nil
}

func (p OrderProduct) IsAmountAdjusted() bool {
	for _, d := range p.Discounts {
		if d.Type == ORDER_DISCOUNT_TYPE_ADJUSTMENT {
			return true
		}
	}
	return false
}

func FormatOrderStatus(status, method string) string {
	switch status {
	case ORDER_STATUS_UNPAID:
		return "待付款"
	case ORDER_STATUS_PAID:
		return "待接单"
	case ORDER_STATUS_UNASSIGNED:
		return "待分配"
	case ORDER_STATUS_ACCEPTED:
		if util.StrInArray(method, &[]string{ORDER_DELIVERY_METHOD_EXPRESS, ORDER_DELIVERY_METHOD_CITY_EXPRESS}) {
			return "待发货"
		}
		return "待提货"
	case ORDER_STATUS_PARTIAL_SHIPPED:
		return "部分发货"
	case ORDER_STATUS_SHIPPED:
		return "已发货"
	case ORDER_STATUS_COMPLETED:
		return "交易成功"
	case ORDER_STATUS_CANCELED:
		return "交易关闭"
	}

	return ""
}

func (o *Order) HasPaidHistory() bool {
	for _, history := range o.Histories {
		if history.Status == ORDER_STATUS_PAID {
			return true
		}
	}
	return false
}

func (o *Order) HasUnassigned() bool {
	for _, history := range o.Histories {
		if history.Status == ORDER_STATUS_UNASSIGNED {
			return true
		}
	}
	return false
}

func (o *Order) HasPaid() bool {
	return !o.PaidAt.IsZero()
}

func getProductBrandId(ctx context.Context, productId string) string {
	product, _ := service.GetProductById(ctx, productId)
	if product == nil || product.Brand == nil {
		return ""
	}
	return product.Brand.Id
}

func getProductCategoryThreeLevelIds(ctx context.Context, id string) []string {
	productCategory, _ := service.GetProductCategoryById(ctx, id)
	if productCategory == nil {
		return []string{}
	}
	parentIds := []string{}
	if productCategory.ParentId != "" {
		parentIds = getProductCategoryThreeLevelIds(ctx, productCategory.ParentId)
	}
	return append(parentIds, id)
}

func IsPresent(p OrderProduct) bool {
	if p.IsPresent {
		return true
	}
	for _, c := range p.Campaigns {
		if core_util.StrInArray(c.Type, &[]string{CAMPAIGN_TYPE_PRESENT, CAMPAIGN_TYPE_PRESENT_COUPON}) {
			return true
		}
	}
	return false
}

func IsPresentCouponPresent(p OrderProduct) bool {
	for _, c := range p.Campaigns {
		if c.Type == CAMPAIGN_TYPE_PRESENT_COUPON {
			return true
		}
	}
	return false
}
func IsPresentCampaignPresent(p OrderProduct) bool {
	for _, c := range p.Campaigns {
		if c.Type == CAMPAIGN_TYPE_PRESENT {
			return true
		}
	}
	return false
}

func UsedCoupon(p OrderProduct) bool {
	for _, d := range p.Discounts {
		if d.IsCoupon() {
			return true
		}
	}
	return false
}

func (p OrderProduct) ExistCampaign(campaign optional_package.OptionalPackage) bool {
	for _, cp := range campaign.Products {
		if cp.Id != p.Id {
			continue
		}
		if util.StrInArray(p.Spec.Sku, &cp.Skus) {
			return true
		}
	}
	return false
}

// 禁用优惠
func (p *OrderProduct) DisableDiscount(t, reason string) {
	if p.DisableReasonMap == nil {
		p.DisableReasonMap = make(map[string]string)
	}
	p.DisableReasonMap[t] = reason
	switch t {
	case DISCOUNT_TYPE_COUPON:
		p.IsCouponEnabled = false
	case DISCOUNT_TYPE_MEMBER:
		p.IsMemberDiscountEnabled = false
	case DISCOUNT_TYPE_PREPAID_CARD:
		p.IsPrepaidCardDisabled = true
	case DISCOUNT_TYPE_BENEFIT_CARD:
		p.IsMemberPaidCardDisabled = true
	case DISCOUNT_TYPE_SCORE:
		p.IsScoreDisabled = true
	case DISCOUNT_TYPE_CAMPAIGN:
		p.IsCampaignDisabled = true
	case DISCOUNT_TYPE_STORED_VALUE:
		p.IsStoredValueDisabled = true
	}
}

func (d OrderDiscount) IsCoupon() bool {
	return util.StrInArray(d.Type, &[]string{ORDER_DISCOUNT_TYPE_COUPON, ORDER_DISCOUNT_TYPE_DELIVERY, ORDER_DISCOUNT_TYPE_PRESENT, ORDER_DISCOUNT_TYPE_EXCHANGE})
}

func (d OrderDiscount) IsCampaign() bool {
	return util.StrInArray(d.Type, &[]string{
		ORDER_DISCOUNT_TYPE_GROUPON,
		ORDER_DISCOUNT_TYPE_PACKAGE,
		ORDER_DISCOUNT_TYPE_DELIVERY,
		ORDER_DISCOUNT_TYPE_OPTIONAL_PACKAGE,
		ORDER_DISCOUNT_TYPE_CAMPAIGN,
		CAMPAIGN_TYPE_BARGAIN,
		CAMPAIGN_TYPE_DISCOUNT,
		CAMPAIGN_TYPE_RANDOM_DISCOUNT,
	})
}

func (o *Order) GetOrderProductPrepaidCardPayAmount(productIndex int) uint64 {
	prepaidCardPayAmount := uint64(0)
	for _, item := range o.Products[productIndex].PrepaidCards {
		prepaidCardPayAmount += item.Amount
	}
	return prepaidCardPayAmount
}

func (p *OrderProduct) GetDistributionMode() string {
	if !p.IsDistribution {
		return ""
	}
	if p.DistributionMode != "" {
		return p.DistributionMode
	}
	if p.IsDefaultProportion {
		return PRODUCT_DISTRIBUTION_MODE_DEFAULT_PROPORTION
	}
	return PRODUCT_DISTRIBUTION_MODE_CUSTOM_PROPORTION
}

// 获取有分销的虚拟商品订单
func (Order) GetProfitVirtualOrders(ctx context.Context, lastId bson.ObjectId) (*[]Order, error) {
	selector := bson.M{
		"accountId":                        util.GetAccountIdAsObjectId(ctx),
		"isDeleted":                        false,
		"status":                           ORDER_STATUS_COMPLETED,
		"distribution.promoterType":        "staff",
		"distribution.profitSharingStatus": "pending",
		"products.type": bson.M{
			"$in": []string{ORDER_TYPE_VIRTUAL, ORDER_TYPE_COUPON},
		},
		"createdAt": bson.M{
			"$lte": time.Now().Add(time.Hour * 24 * -15),
		},
	}
	if lastId.Valid() {
		selector["_id"] = bson.M{
			"$gt": lastId,
		}
	}
	result := []Order{}
	err := extension.DBRepository.FindAll(ctx, C_ORDER, selector, []string{"_id"}, 100, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (o *Order) UpdateProductLogistics(ctx context.Context, outTradeId bson.ObjectId, logistics []ProductLogisticsInfo) error {
	selector := Common.GenDefaultConditionById(ctx, o.Id)
	selector["products.outTradeId"] = outTradeId
	updater := bson.M{
		"$set": bson.M{
			"updatedAt":            time.Now(),
			"products.$.logistics": logistics,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (o *Order) IsGrouponOrder() bool {
	for _, campaign := range o.Campaigns {
		if campaign.Type == CAMPAIGN_TYPE_GROUPON {
			return true
		}
	}
	return false
}

// 是否是脉盟商城订单
func (o *Order) IsMaiMengOrder() bool {
	if util.StrInArray(ORDER_TAGS_MAIMENG, &o.Tags) {
		return true
	}
	return false
}

// 是否是脉盟商城/连锁零售商城代销订单
func (o *Order) IsConsignmentOrder() bool {
	if util.StrInArray(ORDER_TAGS_CONSIGNMENT, &o.Tags) {
		return true
	}
	return false
}

// 是否是脉盟商城/连锁零售商城租户的代销订单
func (o *Order) IsRetailerConsignmentOrder(ctx context.Context) bool {
	if !ec_model.IsMaimengRetail(ctx) && !ec_model.IsChainRetail(ctx) {
		return false
	}
	if util.StrInArray(ORDER_TAGS_CONSIGNMENT, &o.Tags) {
		return true
	}
	return false
}

// 是否是脉盟商城/连锁零售商城经销订单
func (o *Order) IsDistributionOrder() bool {
	if util.StrInArray(ORDER_TAGS_DISTRIBUTION, &o.Tags) {
		return true
	}
	return false
}

// 是否是连锁零售商订单
func (o *Order) IsChainRetailOrder() bool {
	if util.StrInArray(ORDER_TAGS_CHAIN_RETAIL, &o.Tags) {
		return true
	}
	return false
}

// 是否使用易宝交易的 D2R 订单
func (o *Order) IsYeepayForD2R() bool {
	if o.IsMaiMengOrder() {
		return true
	}
	if o.IsChainRetailOrder() {
		return true
	}
	return false
}

// 是否使用易宝交易的订单
func (o *Order) IsYeepay() bool {
	return util.StrInArray(ORDER_TAGS_YEEPAY, &o.Tags)
}

// 是否使用收钱吧支付的订单
func (o *Order) IsShouqianbaPay() bool {
	return util.StrInArray(ORDER_TAGS_SHOUQIANBA, &o.Tags)
}

// 是否是张裕订单
func (o *Order) IsZhangYuOrder() bool {
	if util.StrInArray(o.AccountId.Hex(), &[]string{"649e929030d9ba37953be4d2", "645b0dbe088a96644b319e55"}) {
		return true
	}
	return false
}

// 订单是否不需要分账
func (o *Order) IsNotDivide() bool {
	return util.StrInArray(ORDER_TAGS_NOT_DIVIDE, &o.Tags)
}

// 是否是扫码购订单
func (o *Order) IsScanBuy() bool {
	return util.StrInArray(ORDER_TAGS_SCAN_BUY, &o.Tags)
}

func (o *Order) GetDistributorAccountId(storeDetail *store.StoreDetail) string {
	if storeDetail == nil {
		return ""
	}

	distributorId := o.getDistributorId()
	if distributorId == "" {
		return ""
	}

	distributorAccountId := ""
	for _, distributor := range storeDetail.DistributionStoreInfo.Distributors {
		if distributor.Id == distributorId {
			distributorAccountId = distributor.AccountId
			break
		}
	}
	return distributorAccountId
}

func IsYeepay(ctx context.Context, ecSetting *setting_model.Settings) bool {
	return util.StrInArray(ec_setting.PAYMENT_METHOD_YEEPAY, &ecSetting.Payments)
}

func IsShouqianbaPay(ecSetting *setting_model.Settings) bool {
	return util.StrInArray(ec_setting.PAYMENT_METHOD_SHOUQIANBA, &ecSetting.Payments)
}

// 获取易宝商户号
func (o *Order) GetYeepayMerchants() (string, string) {
	if o.IsYeepay() {
		parentMerchantNo := ""
		merchantNo := ""
		for _, tag := range o.Tags {
			if strings.Contains(tag, "parentMerchantNo") {
				parentMerchantNo = strings.Split(tag, "_")[1]
			}
			if strings.Contains(tag, "merchantNo") {
				merchantNo = strings.Split(tag, "_")[1]
			}
		}
		return parentMerchantNo, merchantNo
	}
	return "", ""
}

// 是否支付后立即分账
func (o *Order) IsDivideAfterPaid() bool {
	if o.IsYeepay() {
		for _, tag := range o.Tags {
			if strings.Contains(tag, "divideType") && strings.Contains(tag, setting_model.DIVIDE_TYPE_AFTER_PAID) {
				return true
			}
		}
	}
	return false
}

func (o *Order) UpdateExtra(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"extra":     o.Extra,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (o *Order) UpdateTags(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"tags":      o.Tags,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

// GetAllSplitOrders 获取当前订单关联的拆单订单
func (o *Order) GetAllSplitOrders(ctx context.Context) ([]Order, error) {
	condition := Common.GenDefaultCondition(ctx)
	condition["tradeNo"] = o.TradeNo
	var orders []Order
	err := extension.DBRepository.FindAll(ctx, C_ORDER, condition, nil, 0, &orders)
	return orders, err
}

// 获取订单应收运费（包括运费券抵扣的）
func (o *Order) GetLogisticsFee() uint64 {
	fee := o.Logistics.Fee
	for _, item := range o.Discounts {
		if item.Type == COUPON_TYPE_DELIVERY {
			fee += uint64(item.Amount)
		}
	}
	return fee
}

func (d DistributionDetail) GetBoundPromoterId() bson.ObjectId {
	if d.SubPromoterId.Valid() && d.ParentPromoterId.Valid() && d.SubPromoterId != d.PromoterId {
		return d.SubPromoterId
	}
	return d.PromoterId
}

func (o *Order) GetShouqianbaPayment() string {
	if o.Extra == nil {
		return o.Payment
	}
	tenders := []shouqianba.TenderDetail{}
	if v, ok := o.Extra["shouqianbaOrderTenders"]; ok {
		json.Unmarshal([]byte(v.(string)), &tenders)
	}
	if v, ok := o.Extra["shouqianbaOrderDepositTenders"]; ok {
		json.Unmarshal([]byte(v.(string)), &tenders)
	}
	if len(tenders) == 0 {
		return o.Payment
	}
	if tenders[0].SubTenderType == 302 || tenders[0].SubTenderType == 402 {
		return PAYMENT_ALIPAY
	}
	return o.Payment
}

func (o *Order) IsProxyOrder() bool {
	if util.StrInArray(ORDER_TAGS_PROXY_ORDER, &o.Tags) {
		return true
	}
	return false
}

func (*Order) GetAllByCondition(ctx context.Context, condition bson.M) ([]Order, error) {
	Orders := []Order{}
	err := extension.DBRepository.FindAll(ctx, C_ORDER, condition, []string{"referredAt"}, 0, &Orders)
	return Orders, err
}

func (o *Order) UpdatePayment(ctx context.Context, id bson.ObjectId, payment string) error {
	selector := bson.M{
		"_id":       id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"payment":   payment,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

// 是否包含包邮商品
func (o *Order) IsHaveFreeProduct() bool {
	for _, p := range o.Products {
		if p.IsFreeShipping {
			return true
		}
	}
	return false
}

func (o *Order) GetOrderAllPayAmount() uint64 {
	prepaidCardsDeductAmount := uint64(0)
	for _, item := range o.PrepaidCards {
		prepaidCardsDeductAmount += item.Amount
	}
	return o.PayAmount + o.DeductAmountByScore + prepaidCardsDeductAmount
}

func (o *Order) MarkReservationSent(ctx context.Context) error {
	selector := bson.M{
		"_id":       o.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"reservation.hasSent": true,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER, selector, updater)
}

func (*Order) GetAllByConditionAndLimit(ctx context.Context, condition bson.M, sort []string, limit int) ([]Order, error) {
	results := []Order{}
	_, err := Common.GetAllByCondition(ctx, condition, sort, limit, C_ORDER, &results)
	return results, err
}

// 订单是否有运费险
func (o *Order) HasFreightInsurance() bool {
	return util.StrInArray(ORDER_TAGS_WITH_WECHAT_FREIGHT_INSURANCE, &o.Tags)
}
