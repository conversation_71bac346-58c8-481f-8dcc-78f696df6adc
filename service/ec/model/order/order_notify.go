package order

import (
	"fmt"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/account"
	pb_account "mairpc/proto/account"
	pb_client "mairpc/proto/client"
	"mairpc/proto/ec/order"
	"mairpc/proto/member"
	pb_member "mairpc/proto/member"
	ec_marketing "mairpc/service/ec/model/marketing"
	ec_model_store "mairpc/service/ec/model/store"
	ec_share "mairpc/service/ec/service"
	"mairpc/service/ec/share"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	model_share "mairpc/service/share/model"
	"mairpc/service/share/util"
	"strconv"
	"strings"

	"github.com/spf13/cast"

	"mairpc/core/extension/bson"

	"golang.org/x/net/context"
)

const (
	BUSINESS               = "retail"
	ORDER_DETAIL_PAGE      = "mall/pages/order-detail/index?orderId=%s"
	GROUPON_DETAIL_PAGE    = "mall/pages/groupon-detail/index?orderId=%s&campaignId=%s&grouponRecordId=%s"
	MALL_ORDER_DETAIL_PAGE = "shop/pages/consumer/order-detail/index?id=%s&shopId=%s"

	// 占位符
	PLACEHOLDER_ORDERSN                      = "OrderSn"
	PLACEHOLDER_VERIFICATIONCODE             = "VerificationCode"
	PLACEHOLDER_PICKUP_PASSWORD              = "PickupPassword"
	PLACEHOLDER_VERIFICATIONTIME             = "VerificationTime"
	PLACEHOLDER_STORENAME                    = "StoreName"
	PLACEHOLDER_LOGIDTICCN                   = "LogidticCn"
	PLACEHOLDER_TRACKINGNO                   = "TrackingNo"
	PLACEHOLDER_MEMBERNAME                   = "MemberName"
	PLACEHOLDER_GENDER                       = "Gender"
	PLACEHOLDER_LOCATIONDETAIL               = "LocationDetail"
	PLACEHOLDER_REFUNDAMOUNT                 = "RefundAmount"
	PLACEHOLDER_REJECTEDREASON               = "RejectedReason"
	PLACEHOLDER_ORDERSTATUS                  = "OrderStatus"
	PLACEHOLDER_DEPOSIT_AMOUNT               = "DepositAmount"
	PLACEHOLDER_BALANCE_AMOUNT               = "BalanceAmount"
	PLACEHOLDER_CONTACT_ADDRESS              = "ContactAddress"
	PLACEHOLDER_PRODUCT_NAME                 = "ProductName"
	PLACEHOLDER_CURRENT_SHIPPED_PRODUCT_NAME = "CurrentShippedProductName"
	PLACEHOLDER_ORDER_TIME                   = "OrderTime"
	PLACEHOLDER_REFUND_TIME                  = "RefundTime"
	PLACEHOLDER_REFUND_REASON                = "RefundReason"
	PLACEHOLDER_REFUND_TYPE                  = "RefundType"
	PLACEHOLDER_ORDER_SHIPPED_AT             = "ShippedAt"
	PLACEHOLDER_RESERVATION_TIME             = "ReservationTime"
)

var (
	NOTIFICATION_SETTING_MAP = map[string]string{
		// 订单状态与订单消息类型推送规则对应关系
		ORDER_STATUS_UNPAID:    constant.MESSAGE_RULE_ORDER_AUTO_CLOSED,
		ORDER_STATUS_PAID:      constant.MESSAGE_RULE_ORDER_PAID,
		ORDER_STATUS_ACCEPTED:  constant.MESSAGE_RULE_ORDER_PICK_UP,
		ORDER_STATUS_COMPLETED: constant.MESSAGE_RULE_ORDER_HAD_PICK_UP,
		ORDER_STATUS_SHIPPED:   constant.MESSAGE_RULE_ORDER_SHIPPED,
		// 售后消息对应消息规则
		constant.MESSAGE_RULE_REFUND_APPLIED:                 constant.MESSAGE_RULE_REFUND_APPLIED,
		ORDER_STATUS_CANCELED:                                constant.MESSAGE_RULE_ORDER_CANCELLED_BY_STAFF,
		constant.MESSAGE_RULE_REFUND_BY_STAFF:                constant.MESSAGE_RULE_REFUND_BY_STAFF,
		ORDER_STATUS_PARTIAL_REFUNDED:                        constant.MESSAGE_RULE_REFUND_AGREED,
		ORDER_REFUND_OPERATE_TYPE_AGREE_TO_REFUND:            constant.MESSAGE_RULE_REFUND_AGREED,
		ORDER_REFUND_OPERATE_TYPE_REFUND_REFUSED:             constant.MESSAGE_RULE_REFUND_REFUSED,
		ORDER_REFUND_STATUS_APPROVED:                         constant.MESSAGE_RULE_REFUND_AND_RETURN_GOODS_AGREED,
		ORDER_REFUND_OPERATE_TYPE_CONFIRM_RECEIPT_AND_REFUND: constant.MESSAGE_RULE_GOODS_RECEIVED,
		// 拼团消息对应规则
		ec_marketing.GROUPON_RECORD_STATUS_SUCCESS: constant.MESSAGE_RULE_GROUPON_SUCCESS,
		ec_marketing.GROUPON_RECORD_STATUS_FAILED:  constant.MESSAGE_RULE_GROUPON_FAILURE,
		// 预售尾款通知对于规则
		constant.MESSAGE_RULE_BALANCE_WILL_START:      constant.MESSAGE_RULE_BALANCE_WILL_START,
		constant.MESSAGE_RULE_BALANCE_WILL_END:        constant.MESSAGE_RULE_BALANCE_WILL_END,
		constant.MESSAGE_RULE_ORDER_PICKUP_INCOMPLETE: constant.MESSAGE_RULE_ORDER_PICKUP_INCOMPLETE,
	}

	// 订单状态与店长消息类型推送规则对应关系
	STAFF_NOTIFICATION_SETTING_MAP = map[string]string{
		ORDER_STATUS_PAID:                                   constant.MESSAGE_RULE_NEW_ORDERS_ASSIGNED,
		ORDER_STATUS_PARTIAL_REFUNDED:                       constant.MESSAGE_RULE_PART_REFUNDED,
		ORDER_STATUS_CANCELED:                               constant.MESSAGE_RULE_ALL_REFUNDED,
		constant.MESSAGE_RULE_UNSHIPPED_PARTIAL_REFUNDED:    constant.MESSAGE_RULE_UNSHIPPED_PARTIAL_REFUNDED,
		constant.MESSAGE_RULE_UNSHIPPED_ALL_REFUNDED:        constant.MESSAGE_RULE_UNSHIPPED_ALL_REFUNDED,
		constant.MESSAGE_RULE_SHIPPED_APPLY_REFUND:          constant.MESSAGE_RULE_SHIPPED_APPLY_REFUND,
		constant.MESSAGE_RULE_SHIPPED_APPLY_REFUND_CANCEL:   constant.MESSAGE_RULE_SHIPPED_APPLY_REFUND_CANCEL,
		constant.MESSAGE_RULE_UNSHIPPED_APPLY_REFUND_CANCEL: constant.MESSAGE_RULE_UNSHIPPED_APPLY_REFUND_CANCEL,
	}

	// 需要发订阅消息的状态
	STATUS_NEED_SEND_SUBSCRIBE_MSG = []string{
		ORDER_STATUS_ACCEPTED,
		ORDER_STATUS_COMPLETED,
		ORDER_STATUS_SHIPPED,
		ORDER_STATUS_CANCELED,
		constant.MESSAGE_RULE_REFUND_BY_STAFF,
		ORDER_REFUND_OPERATE_TYPE_AGREE_TO_REFUND,
		ORDER_REFUND_OPERATE_TYPE_REFUND_REFUSED,
		ORDER_REFUND_STATUS_APPROVED,
		ORDER_REFUND_OPERATE_TYPE_CONFIRM_RECEIPT_AND_REFUND,
		ec_marketing.GROUPON_RECORD_STATUS_SUCCESS,
		ec_marketing.GROUPON_RECORD_STATUS_FAILED,
		constant.MESSAGE_RULE_BALANCE_WILL_START,
		constant.MESSAGE_RULE_BALANCE_WILL_END,
		constant.MESSAGE_RULE_ORDER_PICKUP_INCOMPLETE,
	}
)

func GetCustomerNotificationSetting(ctx context.Context, typ string) (*account.GetNotificationSettingResponse, error) {
	result, err := ec_share.GetNoitificationSetting(ctx, BUSINESS, NOTIFICATION_SETTING_MAP[typ])
	if err != nil {
		return nil, err
	}
	return result, nil
}

func GenBasicPlaceholderValueMap(orderRefund *OrderRefund, updateOrderRefundReq *order.UpdateOrderRefundRequest, logistics *Logistics) map[string]string {
	basicPlaceholderValueMap := map[string]string{}
	if orderRefund != nil {
		basicPlaceholderValueMap[PLACEHOLDER_REFUNDAMOUNT] = strconv.FormatFloat(float64(int(orderRefund.RefundAmount))/100, 'f', -1, 64)
		basicPlaceholderValueMap[PLACEHOLDER_REFUND_TIME] = orderRefund.CreatedAt.Format(TIME_FORMAT)
		basicPlaceholderValueMap[PLACEHOLDER_REFUND_REASON] = orderRefund.Reason
		basicPlaceholderValueMap[PLACEHOLDER_REFUND_TYPE] = func() string {
			if orderRefund.RefundType == ORDER_REFUND_TYPE_ONLY_REFUND {
				return "仅退款"
			}
			return "退货退款"
		}()
	}
	if updateOrderRefundReq != nil {
		basicPlaceholderValueMap[PLACEHOLDER_REJECTEDREASON] = updateOrderRefundReq.RejectedReason
	}
	if logistics != nil {
		basicPlaceholderValueMap[PLACEHOLDER_LOGIDTICCN] = logistics.DeliveryName
		basicPlaceholderValueMap[PLACEHOLDER_TRACKINGNO] = logistics.WaybillId
		basicPlaceholderValueMap[PLACEHOLDER_ORDER_SHIPPED_AT] = logistics.ShippedAt.Format(TIME_FORMAT)
	}
	return basicPlaceholderValueMap
}

func GetReplacePlaceholderValueMap(placeholderValueMap map[string]string, member *member.MemberDetailResponse, order *Order, store *ec_model_store.Store, notificationSetting *account.NotificationSetting) map[string]string {
	placeholderValueMap[PLACEHOLDER_ORDERSN] = order.Number
	placeholderValueMap[PLACEHOLDER_VERIFICATIONCODE] = order.PickupCode
	placeholderValueMap[PLACEHOLDER_PICKUP_PASSWORD] = order.PickupPassword
	placeholderValueMap[PLACEHOLDER_VERIFICATIONTIME] = order.UpdatedAt.Format(TIME_FORMAT)
	placeholderValueMap[PLACEHOLDER_ORDER_TIME] = order.CreatedAt.Format(TIME_FORMAT)
	placeholderValueMap[PLACEHOLDER_MEMBERNAME] = member.Name
	placeholderValueMap[PLACEHOLDER_GENDER] = member.Gender
	placeholderValueMap[PLACEHOLDER_ORDERSTATUS] = FormatOrderStatus(order.Status, order.Method)
	if store != nil {
		placeholderValueMap[PLACEHOLDER_STORENAME] = store.Name
		placeholderValueMap[PLACEHOLDER_LOCATIONDETAIL] = store.Location.Province + store.Location.City + store.Location.District + store.Location.Name
	}
	depositAmount := uint64(0)
	for _, t := range order.TradeRecords {
		depositAmount += t.PayAmount
	}
	placeholderValueMap[PLACEHOLDER_DEPOSIT_AMOUNT] = cast.ToString(float64(depositAmount) / 100)
	placeholderValueMap[PLACEHOLDER_BALANCE_AMOUNT] = cast.ToString(float64(order.PayAmount) / 100)
	placeholderValueMap[PLACEHOLDER_CONTACT_ADDRESS] = order.Contact.Address.Detail
	placeholderValueMap[PLACEHOLDER_PRODUCT_NAME] = order.Products[0].Name
	if len(order.Products) > 1 {
		placeholderValueMap[PLACEHOLDER_PRODUCT_NAME] = fmt.Sprintf("%s等", order.Products[0].Name)
	}
	if placeholderValueMap[PLACEHOLDER_CURRENT_SHIPPED_PRODUCT_NAME] != "" {
		placeholderValueMap[PLACEHOLDER_PRODUCT_NAME] = placeholderValueMap[PLACEHOLDER_CURRENT_SHIPPED_PRODUCT_NAME]
	}
	for placeholder, value := range placeholderValueMap {
		placeholderRule := getPlaceholderRule(placeholder, notificationSetting)
		if placeholderRule == nil {
			continue
		}
		placeholderValueMap[placeholder] = placeholderRule.Filler.Default
		if len(placeholderRule.Filler.ValueTransfer) > 0 {
			newValue, ok := placeholderRule.Filler.ValueTransfer[value]
			if ok {
				placeholderValueMap[placeholder] = newValue
			}
		} else if value != "" {
			placeholderValueMap[placeholder] = value
		}
	}

	return placeholderValueMap
}

func getPlaceholderRule(placeholder string, notificationSetting *account.NotificationSetting) *account.PlaceholderRules {
	for _, placeholderRules := range notificationSetting.PlaceholderRules {
		if placeholder == placeholderRules.Placeholder {
			return placeholderRules
		}
	}
	return nil
}

func SendCustomerSubscribeMsg(ctx context.Context, notificationSetting *account.NotificationSetting, placeholderValueMap map[string]string, order *Order, memberDetail *pb_member.MemberDetailResponse) {
	subscribeMsg := &component.SubscribeMsg{}
	subscribeMsg.OriginId = order.Channel.OpenId
	subscribeMsg.AppSecret = false
	subscribeMsg.SubscribeMessage.TemplateId = notificationSetting.SubscribeMessage.Id
	// 订单详情页
	subscribeMsg.SubscribeMessage.Page = fmt.Sprintf(ORDER_DETAIL_PAGE, order.Id.Hex())
	if notificationSetting.SubscribeMessage.Page != "" {
		subscribeMsg.SubscribeMessage.Page = fmt.Sprintf(notificationSetting.SubscribeMessage.Page, order.Id.Hex())
	}
	// 参加的活动是拼团时跳转拼团详情页
	if len(order.Campaigns) > 0 && notificationSetting.Type == "groupon" {
		for _, campaign := range order.Campaigns {
			if campaign.Type == CAMPAIGN_TYPE_GROUPON {
				subscribeMsg.SubscribeMessage.Page = fmt.Sprintf(GROUPON_DETAIL_PAGE, order.Id.Hex(), campaign.Id.Hex(), campaign.GrouponRecordId.Hex())
				break
			}
		}
	} else if len(order.Tags) > 0 && len(core_util.GetArraysIntersection(order.Tags, MALL_ORDER_TAGS)) > 0 {
		// 代销订单详情页
		subscribeMsg.SubscribeMessage.Page = fmt.Sprintf(MALL_ORDER_DETAIL_PAGE, order.Id.Hex(), order.StoreId.Hex())
	}
	var (
		subscribeMsgData = make(map[string]map[string]string)
		messagePairs     []*pb_account.NotificationMessagePair
	)
	for _, data := range notificationSetting.SubscribeMessage.Data {
		value := share.FormatText(data.Value, placeholderValueMap)
		for k, v := range constant.PARAMETERS_LIMIT_MAP {
			valueRune := []rune(value)
			if strings.Contains(data.Key, k) && len(valueRune) > v {
				value = string(valueRune[:v])
				break
			}
		}
		subscribeMsgData[data.Key] = map[string]string{
			"value": value,
		}
		messagePairs = append(messagePairs, &pb_account.NotificationMessagePair{Key: data.Name, Value: value})
	}
	subscribeMsg.SubscribeMessage.Data = subscribeMsgData

	logReq := &pb_account.CreateMessageNotificationLogRequest{
		NotificationSettingId: notificationSetting.Id,
		Type:                  model_share.MESSAGE_NOTIFICATION_LOG_TYPE_SUBSCRIBE_MESSAGE,
		Status:                model_share.MESSAGE_NOTIFICATION_LOG_STATUS_SUCCEED,
		ChannelId:             order.Channel.ChannelId,
		MemberInfo: &pb_account.NotificationMember{
			MemberId: memberDetail.Id,
			Phone:    memberDetail.Phone,
			Name:     memberDetail.Name,
		},
		Message: &pb_account.NotificationMessage{
			Title: notificationSetting.SubscribeMessage.Title,
			Data:  messagePairs,
		},
	}
	defer pb_client.GetAccountServiceClient().CreateMessageNotificationLog(ctx, logReq)

	result, err := component.WeConnect.SendSubscribeMessage(ctx, order.Channel.ChannelId, subscribeMsg)
	if err != nil {
		logReq.Status = model_share.MESSAGE_NOTIFICATION_LOG_STATUS_FAILED
		logReq.Description = model_share.GenMessageNotificationLogDescription(model_share.MESSAGE_NOTIFICATION_LOG_TYPE_SUBSCRIBE_MESSAGE, result.ErrCode)
		log.Warn(ctx, "Failed to send subscribeMsg to customer", log.Fields{
			"openId":       order.Channel.OpenId,
			"errorMessage": err.Error(),
			"subscribeMsg": subscribeMsg,
		})
	}
}

func SendBytedanceSubscribeMsg(ctx context.Context, notificationSetting *account.NotificationSetting, placeholderValueMap map[string]string, order *Order) {
	subscribeMsg := &component.SubscribeMsg{}
	subscribeMsg.OriginId = order.Channel.OpenId
	subscribeMsg.AppSecret = false
	subscribeMsg.SubscribeMessage.TemplateId = notificationSetting.BytedanceSubscribeMessage.Id

	subscribeMsgData := make(map[string]string)
	for _, data := range notificationSetting.BytedanceSubscribeMessage.Data {
		value := share.FormatText(data.Value, placeholderValueMap)
		subscribeMsgData[data.Key] = value
	}
	subscribeMsg.SubscribeMessage.Data = subscribeMsgData
	_, err := component.WeConnect.SendSubscribeMessage(ctx, order.Channel.ChannelId, subscribeMsg)
	if err != nil {
		log.Warn(ctx, "Failed to send bytedance subscribeMsg to customer", log.Fields{
			"openId":                order.Channel.OpenId,
			"errorMessage":          err.Error(),
			"bytedanceSubscribeMsg": subscribeMsg,
		})
	}
}

func SendGrouponMsg(ctx context.Context, grouponRecordId, status string, placeholderMap map[string]string) error {
	condition := bson.M{
		"accountId":                 util.GetAccountIdAsObjectId(ctx),
		"isDeleted":                 false,
		"campaigns.grouponRecordId": bson.ObjectIdHex(grouponRecordId),
		"campaigns.grouponStatus":   status,
	}
	orders, err := COrder.GetByCondition(ctx, condition)
	if err != nil {
		return err
	}
	if placeholderMap == nil {
		placeholderMap = make(map[string]string)
	}
	for _, order := range orders {
		err = order.NotifyCustomer(ctx, status, placeholderMap)
		if err != nil {
			log.Warn(ctx, "Failed to send notification to customer", log.Fields{
				"orderId":       order.Id.Hex(),
				"grouponStatus": status,
				"errorMessage":  err.Error(),
			})
		}
	}
	return nil
}
