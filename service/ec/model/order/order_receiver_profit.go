package order

import (
	"context"
	pb_profitsharing "mairpc/proto/ec/profitsharing"
	ec_client "mairpc/service/ec/client"
	"mairpc/service/ec/jobs/share"
	mall_service "mairpc/service/ec/service/mall"
	share_model "mairpc/service/share/model"
	"sort"
	"time"

	"mairpc/core/component/yeepay"
	"mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	ec_distribution "mairpc/service/ec/model/distribution"
	ec_profitsharing "mairpc/service/ec/model/profitsharing"
	ec_setting "mairpc/service/ec/model/setting"
	ec_store "mairpc/service/ec/model/store"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"

	"github.com/spf13/cast"
)

/*
 * 预分账表
 * 每个订单（ec.order） 完成时要分给 n 个人，则生成 n 个预分账记录
 * 记录了某个订单各分给谁，多少钱，何时分，分账类型等等信息
 */
const (
	C_ORDER_RECEIVER_PROFIT = "ec.orderReceiverProfit"

	TRANSFER_CHANNEL_WECHAT_BALANCE       = "wechatBalance"
	TRANSFER_CHANNEL_WECHAT_PROFITSHARING = "wechatProfitsharing"
	TRANSFER_CHANNEL_WECHAT_BANK          = "wechatBank"
	TRANSFER_CHANNEL_OFFLINE              = "offline"
	TRANSFER_CHANNEL_YEEPAY               = "yeePay" // 易宝支付

	CYCLE_TYPE_DAY   = "day"
	CYCLE_TYPE_MONTH = "month"
)

var (
	COrderReceiverProfit = &OrderReceiverProfit{}

	ORDER_RECEIVER_PROFIT_STATUS_PENDING         = "pending"
	ORDER_RECEIVER_PROFIT_STATUS_PROCESSING      = "processing"
	ORDER_RECEIVER_PROFIT_STATUS_PENDING_PAYMENT = "pendingPayment" // 等待薪行家代付
	ORDER_RECEIVER_PROFIT_STATUS_SUCCESS         = "success"
	ORDER_RECEIVER_PROFIT_STATUS_FAILED          = "failed"
	ORDER_RECEIVER_PROFIT_STATUS_NOCOMMISSION    = "noCommission"

	ListOrderReceiverProfitStatus = []string{
		ORDER_RECEIVER_PROFIT_STATUS_PENDING,
		ORDER_RECEIVER_PROFIT_STATUS_PROCESSING,
		ORDER_RECEIVER_PROFIT_STATUS_SUCCESS,
		ORDER_RECEIVER_PROFIT_STATUS_FAILED,
	}

	processedStatus = []string{
		ORDER_RECEIVER_PROFIT_STATUS_PROCESSING,
		ORDER_RECEIVER_PROFIT_STATUS_SUCCESS,
		ORDER_RECEIVER_PROFIT_STATUS_FAILED,
	}
)

type OrderReceiverProfit struct {
	Id                   bson.ObjectId  `bson:"_id,omitempty"`
	AccountId            bson.ObjectId  `bson:"accountId"`
	Order                BriefOrderInfo `bson:"order"`
	ReceiverId           bson.ObjectId  `bson:"receiverId"`
	Proportion           float64        `bson:"proportion"`               // n 表示 n%
	ProfitAmount         uint64         `bson:"profitAmount"`             // 应分金额
	ShareAt              time.Time      `bson:"shareAt,omitempty"`        // 月结无此字段
	CycleType            string         `bson:"cycleType"`                // 大比例分账固定为月结，小比例分账固定为日结，分销看设置
	ProfitType           string         `bson:"profitType"`               // 分钱类型：分账 profitshare，分销 distribution，代销分佣 commission
	Status               string         `bson:"status"`                   //
	TransferBillId       bson.ObjectId  `bson:"transferBillId,omitempty"` // 分账账单 ID
	SingleOrderProfitCap uint64         `bson:"singleOrderProfitCap"`
	Promoter             Promoter       `bson:"promoter,omitempty"`   // 分销订单有此字段
	TransferChannel      string         `bson:"transferChannel"`      // 转账渠道
	Remarks              string         `bson:"remarks"`              // 备注
	FinishedAt           time.Time      `bson:"finishedAt,omitempty"` // 实际到账时间
	CreatedAt            time.Time      `bson:"createdAt"`
	UpdatedAt            time.Time      `bson:"updatedAt"`
	IsDeleted            bool           `bson:"isDeleted"`
}

type BriefOrderInfo struct {
	Id          bson.ObjectId `bson:"id"`
	MemberId    bson.ObjectId `bson:"memberId,omitempty"`
	StoreId     bson.ObjectId `bson:"storeId"`
	StoreName   string        `bson:"storeName"`
	Number      string        `bson:"number"`
	TradeNo     string        `bson:"tradeNo"`
	PayAmount   uint64        `bson:"payAmount"`
	PaidAt      time.Time     `bson:"paidAt"`    // 用于重试分账时判断是否需要走余额转账
	CreatedAt   time.Time     `bson:"createdAt"` // 用于月结计算时间查询
	DeliveryFee uint64        `bson:"deliveryFee"`
	Tags        []string      `bson:"tags,omitempty"`
}

type Promoter struct {
	Id   bson.ObjectId `bson:"id"`
	Type string        `bson:"type"`
}

func (profit OrderReceiverProfit) Create(ctx context.Context) error {
	// 分账金额超过付款金额的 30% 视为异常分账金额
	if float64(profit.ProfitAmount) > float64(profit.Order.PayAmount)*0.3 {
		log.Warn(ctx, "Invalid profitAmount for order distribution", log.Fields{
			"orderPayAmount": profit.Order.PayAmount,
			"profitAmount":   profit.ProfitAmount,
			"orderId":        profit.Order.Id.Hex(),
		})
		return errors.NewInvalidArgumentError("profitAmount")
	}
	profit.AccountId = util.GetAccountIdAsObjectId(ctx)
	if profit.Status == "" {
		profit.Status = ORDER_RECEIVER_PROFIT_STATUS_PENDING
	}
	profit.CreatedAt = time.Now()
	profit.UpdatedAt = time.Now()

	_, err := extension.DBRepository.Insert(ctx, C_ORDER_RECEIVER_PROFIT, profit)
	return err
}

func (profit OrderReceiverProfit) CreateNotCheckAmount(ctx context.Context) error {
	profit.AccountId = util.GetAccountIdAsObjectId(ctx)
	profit.Status = ORDER_RECEIVER_PROFIT_STATUS_PENDING
	profit.CreatedAt = time.Now()
	profit.UpdatedAt = time.Now()

	_, err := extension.DBRepository.Insert(ctx, C_ORDER_RECEIVER_PROFIT, profit)
	return err
}

// 根据 order 创建 预分账表
func (OrderReceiverProfit) CreateFromOrder(ctx context.Context, order Order) {
	if len(order.ProfitTypes) == 0 {
		return
	}
	// 脉盟商城、连锁零售商城订单，使用易宝分账，预分账账单在 retailer.HandleOrderCompleted 下创建
	if order.IsYeepayForD2R() {
		return
	}
	if order.IsNotDivide() {
		return
	}

	// 分销月结不用创建 orderReceiverProfit
	// 全部退款的不进行分销（微信分润）
	var err error
	if order.PayAmount != 0 && util.StrInArray(PROFIT_TYPE_DISTRIBUTION, &order.ProfitTypes) && !order.IsAllDistributionProductsRefunding(ctx) {
		if order.Distribution.ProfitSharingType != ec_setting.COMMMISSION_TYPE_MONTHLY {
			err = COrderReceiverProfit.CreateForDistribution(ctx, order)
		} else {
			// 月结时，黑名单用户不参与分佣，分销订单分销状态：结算失败
			if order.Distribution.PromoterType == ec_distribution.PROMOTER_TYPE_MEMBER {
				promoter, _ := ec_distribution.CPromoter.GetById(ctx, order.Distribution.PromoterId)
				if promoter != nil {
					member, _ := share.GetMember(ctx, promoter.MemberId.Hex())
					if member != nil && member.BlockedStatus == 2 {
						order.UpdateDistributionStatus(ctx, order.Id.Hex(), DISTRIBUTION_STATUS_FAILED, ec_profitsharing.BILL_FAILED_MESSAGE_MEMBER_BLOCKED, nil, time.Now())
					}
				}
			}
		}
	}
	if util.StrInArray(PROFIT_TYPE_PROFITSHARING, &order.ProfitTypes) {
		err = COrderReceiverProfit.createForProfitsharing(ctx, order)
	}
	if util.StrInArray(PROFIT_TYPE_COMMISSION, &order.ProfitTypes) {
		err = COrderReceiverProfit.CreateForCommission(ctx, order)
	}
	if err != nil {
		log.Error(ctx, "Failed to create orderReceiverProfit from order.", log.Fields{
			"orderId":      order.Id,
			"errorMessage": err.Error(),
		})
	}
}

func (OrderReceiverProfit) CreateForCommission(ctx context.Context, order Order) error {
	resp, err := ec_client.ProfitsharingService.GetReceiver(ctx, &pb_profitsharing.GetReceiverRequest{
		StoreIds: []string{order.StoreId.Hex()},
	})
	if err != nil {
		return err
	}
	// 计算分佣总和
	mallCommissionAmount, err := mall_service.GetLatestMallCommissionProportionAmount(ctx, order.Id.Hex(), order.StoreId.Hex())
	if err != nil {
		log.Error(ctx, "calculate total commission amount failed", log.Fields{"error": err})
	}

	receiverOrderProfit := OrderReceiverProfit{
		Order:           COrderReceiverProfit.GetOrderInfo(ctx, order),
		ReceiverId:      bson.ObjectIdHex(resp.Receiver.Id),
		ProfitAmount:    mallCommissionAmount,
		CycleType:       CYCLE_TYPE_MONTH,
		TransferChannel: TRANSFER_CHANNEL_WECHAT_BALANCE,
		ProfitType:      ec_profitsharing.PROFIT_TYPE_COMMISSION,
		Status:          ORDER_RECEIVER_PROFIT_STATUS_PENDING,
	}
	err = receiverOrderProfit.Create(ctx)
	return err
}

func (OrderReceiverProfit) CreateForDistribution(ctx context.Context, order Order) error {
	if order.Distribution.PromoterId.Hex() == "" {
		return nil
	}

	orderInfo := COrderReceiverProfit.GetOrderInfo(ctx, order)
	memberPromoterSetting, _ := ec_distribution.CDistributionSetting.GetByType(ctx, ec_distribution.DISTRIBUTION_SETTING_TYPE_MEMBER)
	staffPromoterSetting, _ := ec_distribution.CDistributionSetting.GetByType(ctx, ec_distribution.DISTRIBUTION_SETTING_TYPE_STAFF)

	switch order.Distribution.PromoterType {
	case ec_distribution.PROMOTER_TYPE_MEMBER:
		if memberPromoterSetting == nil {
			return errors.NewNotExistsErrorWithMessage("distributionSetting", "type: member")
		}
	case ec_distribution.PROMOTER_TYPE_STAFF:
		if staffPromoterSetting == nil {
			return errors.NewNotExistsErrorWithMessage("distributionSetting", "type: staff")
		}
	}

	promoter, err := ec_distribution.CPromoter.GetById(ctx, order.Distribution.PromoterId)
	if err != nil {
		return errors.NewNotExistsErrorWithMessage("promoter", err.Error())
	}
	rCondition := bson.M{
		"type":       ec_profitsharing.RECEIVER_TYPE_OPENID,
		"profitType": ec_profitsharing.PROFIT_TYPE_DISTRIBUTION,
		"account":    promoter.OpenId,
	}
	if promoter.ReceiverType == ec_profitsharing.RECEIVER_TYPE_MERCHANT {
		if promoter.Merchant.MerchantId == "" {
			return errors.NewInvalidArgumentError("merchantId")
		}
		rCondition["type"] = ec_profitsharing.RECEIVER_TYPE_MERCHANT
		rCondition["account"] = promoter.Merchant.MerchantId
	}
	receiver, err := ec_profitsharing.CProfitSharingReceiver.GetByDefaultCondition(ctx, rCondition)
	if err != nil {
		return errors.NewNotExistsErrorWithMessage("profitSharingReceiver", err.Error())
	}

	receiverOrderProfit := OrderReceiverProfit{
		Order:        orderInfo,
		ReceiverId:   receiver.Id,
		ProfitAmount: order.Distribution.Amount,
		CycleType: func(pType string) string {
			switch pType {
			case ec_distribution.PROMOTER_TYPE_MEMBER:
				return memberPromoterSetting.PromoterCommission.Cycle
			case ec_distribution.PROMOTER_TYPE_STAFF:
				return staffPromoterSetting.PromoterCommission.Cycle
			}
			return ""
		}(order.Distribution.PromoterType),
		TransferChannel: TRANSFER_CHANNEL_WECHAT_BALANCE,
		ProfitType:      receiver.ProfitType,
		Promoter: Promoter{
			Id:   order.Distribution.PromoterId,
			Type: order.Distribution.PromoterType,
		},
	}

	// 分销日结一定会进入此条件分支
	if order.Distribution.ProfitSharingType != ec_setting.COMMMISSION_TYPE_MONTHLY {
		receiverOrderProfit.TransferChannel = TRANSFER_CHANNEL_WECHAT_PROFITSHARING
		if order.IsShouqianbaPay() {
			receiverOrderProfit.TransferChannel = TRANSFER_CHANNEL_WECHAT_BALANCE
		}
		receiverOrderProfit.ShareAt = ec_setting.GetNeedCommissionAt(time.Now(), uint64(order.Distribution.ProfitSharingType))
		// 虚拟商品订单全部核销后已经在 job 中延迟 15 天，此处不需要再累加相应的时间了
		if order.IsVirtual() && order.Distribution.PromoterType == ec_distribution.PROMOTER_TYPE_STAFF {
			receiverOrderProfit.ShareAt = time.Now()
		}
		// 微信订单分润只能在支付 30 天以内进行，这里为了保证分润成功（防止分账的定时任务执行时已经超过了 30 天），
		// 只要支付时间在分润时间的 29 天以前就采用企业支付的方式支付分润金额。目前只针对导购分销日结进行处理
		if order.PaidAt.Before(receiverOrderProfit.ShareAt.AddDate(0, 0, -29)) {
			receiverOrderProfit.TransferChannel = TRANSFER_CHANNEL_WECHAT_BALANCE
		}
	}

	// 使用易宝支付，并且线下结算时，预分账记录直接失败
	if order.IsYeepay() && order.Distribution.PromoterType == ec_distribution.PROMOTER_TYPE_STAFF && staffPromoterSetting.IsOffline {
		receiverOrderProfit.Remarks = ec_profitsharing.BILL_FAILED_MESSAGE
		receiverOrderProfit.Status = ORDER_RECEIVER_PROFIT_STATUS_FAILED
	}
	if order.IsYeepay() && order.Distribution.PromoterType == ec_distribution.PROMOTER_TYPE_MEMBER && memberPromoterSetting.IsOffline {
		receiverOrderProfit.Remarks = ec_profitsharing.BILL_FAILED_MESSAGE
		receiverOrderProfit.Status = ORDER_RECEIVER_PROFIT_STATUS_FAILED
	}

	// 黑名单用户不参与分佣，分销订单和分佣状态：结算失败
	if order.Distribution.PromoterType == ec_distribution.PROMOTER_TYPE_MEMBER {
		member, _ := share.GetMember(ctx, promoter.MemberId.Hex())
		if member.BlockedStatus == 2 {
			receiverOrderProfit.Remarks = ec_profitsharing.BILL_FAILED_MESSAGE_MEMBER_BLOCKED
			receiverOrderProfit.Status = ORDER_RECEIVER_PROFIT_STATUS_FAILED
			order.UpdateDistributionStatus(ctx, order.Id.Hex(), DISTRIBUTION_STATUS_FAILED, ec_profitsharing.BILL_FAILED_MESSAGE_MEMBER_BLOCKED, nil, time.Now())
		}
	}

	err = receiverOrderProfit.Create(ctx)
	return err
}

func (OrderReceiverProfit) createForProfitsharing(ctx context.Context, order Order) error {
	cSetting, err := ec_setting.CCommissionSetting.GetByAccountId(ctx, order.AccountId)
	if err != nil {
		return errors.NewNotExistsErrorWithMessage("commissionSetting", err.Error())
	}

	// 使用易宝支付
	if order.IsYeepay() {
		COrderReceiverProfit.createForYeepay(ctx, order, cSetting.Type)
		return nil
	}

	switch cSetting.ProportionScaleType {
	case ec_setting.PROPORTION_SCALE_TYPE_LARGE:
		COrderReceiverProfit.createForLargeScale(ctx, order)
	case ec_setting.PROPORTION_SCALE_TYPE_SMALL:
		if order.RefundStatus != "" {
			return nil // 发生过退款不进行小比例分账（微信分润）
		}
		COrderReceiverProfit.createForSmallScale(ctx, order, cSetting.Type)
	default:
		// do nothing
	}

	return nil
}

func (OrderReceiverProfit) createForYeepay(ctx context.Context, order Order, commissionType uint64) error {
	ctx = log.SwitchOnResponseBodyLog(ctx)
	// 张裕租户有拆单逻辑，需要单独处理
	if util.StrInArray(order.AccountId.Hex(), &[]string{"649e929030d9ba37953be4d2", "645b0dbe088a96644b319e55"}) {
		COrderReceiverProfit.CreateForZhangYu(ctx, order)
		return nil
	}

	// 目前走下面逻辑的租户如下
	// 1. 生活好味
	// 2. 海王艇仔
	// 3. 施华蔻 DTC
	parentMerchantNo, merchantNo := order.GetYeepayMerchants()
	// 生活好味旧订单非分账，要判断下
	// 非分账模式，不需要创建预分账单
	if !needCreateOrderReceiverProfit(ctx, order.Id.Hex(), parentMerchantNo, merchantNo) {
		return nil
	}

	receivers := getYeepayReceivers(ctx, order.StoreId)
	if len(receivers) == 0 {
		log.Warn(ctx, "Yeepay receivers is empty", log.Fields{
			"order": order,
		})
		return nil
	}

	unSplitAmount, err := GetUnSplitAmount(ctx, order.Id.Hex(), parentMerchantNo, merchantNo)
	if err != nil {
		return err
	}
	shareAt := ec_setting.GetNeedCommissionAt(time.Now(), commissionType)
	if order.IsDivideAfterPaid() {
		shareAt = time.Now()
	}
	orderInfo := COrderReceiverProfit.GetOrderInfo(ctx, order)
	// 按 proportion 升序排序
	sort.Slice(receivers, func(i, j int) bool {
		return receivers[i].Proportion < receivers[j].Proportion
	})
	calculatedAmount := uint64(0)
	for i, r := range receivers {
		amount := uint64(0)
		if i == len(receivers)-1 && unSplitAmount > calculatedAmount {
			amount = unSplitAmount - calculatedAmount
		} else {
			amount = uint64(util.DivideFloatWithRound(util.MultiplyFloatWithRound(float64(unSplitAmount), r.Proportion, 2), 100, 0))
			calculatedAmount += amount
		}
		receiverOrderProfit := OrderReceiverProfit{
			Order:           orderInfo,
			ReceiverId:      r.Id,
			Proportion:      r.Proportion,
			ProfitAmount:    amount,
			TransferChannel: TRANSFER_CHANNEL_YEEPAY,
			ProfitType:      r.ProfitType,
			ShareAt:         shareAt,
			CycleType:       ec_setting.PROFITSHARING_CYCLE_TYPE_DAY,
		}
		err := receiverOrderProfit.CreateNotCheckAmount(ctx)
		if err != nil {
			log.Warn(ctx, "Failed to create orderReceiverProfit", log.Fields{
				"function":            "createForYeepay",
				"orderReceiverProfit": receiverOrderProfit,
				"errorMessage":        err.Error(),
			})
		}
	}
	return nil
}

// 未使用分账完结接口，资金都是通过分账分出去的，分账接收方主要有下面几个
// 1.平台服务费对应分账接收方
// 2.门店分账接收方
func getYeepayReceivers(ctx context.Context, storeId bson.ObjectId) []ec_profitsharing.ProfitSharingReceiver {
	condition := bson.M{
		"$or": []bson.M{
			{
				"accountId":    util.GetAccountIdAsObjectId(ctx),
				"isDeleted":    false,
				"type":         ec_profitsharing.RECEIVER_TYPE_MERCHANT,
				"relationType": ec_profitsharing.RECEIVER_RELATION_TYPE_PARTNER,
				"account":      yeepay.PLATFORM_SERVICE_MERCHANT,
				"profitType":   ec_profitsharing.PROFIT_TYPE_PROFIT_SHARING,
			},
			{
				"accountId":  util.GetAccountIdAsObjectId(ctx),
				"isDeleted":  false,
				"type":       ec_profitsharing.RECEIVER_TYPE_MERCHANT,
				"storeIds":   storeId,
				"profitType": ec_profitsharing.PROFIT_TYPE_PROFIT_SHARING,
			},
			{
				"accountId":  util.GetAccountIdAsObjectId(ctx),
				"isDeleted":  false,
				"type":       ec_profitsharing.RECEIVER_TYPE_MERCHANT,
				"allStores":  true,
				"profitType": ec_profitsharing.PROFIT_TYPE_PROFIT_SHARING,
			},
		},
	}
	return ec_profitsharing.CProfitSharingReceiver.GetAllByCondition(ctx, condition)
}

func (OrderReceiverProfit) CreateForZhangYu(ctx context.Context, order Order) {
	// 拆单后需要在所有子单都完成后才能分账
	if !IsAllSubOrdersCompleted(ctx, order) {
		return
	}

	subOrders, _ := COrder.GetSubOrdersByOutTradeId(ctx, order.OutTradeId)
	if !checkYeepayProfitAmount(ctx, subOrders) {
		log.Warn(ctx, "Yeepay profit amount error", log.Fields{
			"subOrders": subOrders,
		})
		return
	}
	for _, subOrder := range subOrders {
		if subOrder.Status == ORDER_STATUS_CANCELED {
			continue
		}
		// 修复数据使用，已经分账过的订单不再创建预分账单
		orderReceiverProfit, _ := COrderReceiverProfit.GetByOrderId(ctx, subOrder.Id)
		if orderReceiverProfit.Id.Valid() {
			continue
		}
		createForYeepayWithSubOrder(ctx, subOrder)
	}
}

// 核对分账金额
func checkYeepayProfitAmount(ctx context.Context, subOrders []Order) bool {
	totalProfitAmount := uint64(0)
	for _, o := range subOrders {
		if o.Status == ORDER_STATUS_CANCELED {
			continue
		}
		// 修复数据使用，已经分账过的订单不检查分账金额
		orderReceiverProfit, _ := COrderReceiverProfit.GetByOrderId(ctx, o.Id)
		if orderReceiverProfit.Id.Valid() {
			continue
		}
		totalProfitAmount += getProfitAmount(o)
	}
	parentMerchantNo, merchantNo := subOrders[0].GetYeepayMerchants()
	unSplitAmount, err := GetUnSplitAmount(ctx, subOrders[0].OutTradeId.Hex(), parentMerchantNo, merchantNo)
	if err != nil {
		return false
	}
	if unSplitAmount == totalProfitAmount {
		return true
	}
	log.Warn(ctx, "checkYeepayProfitAmount fail", log.Fields{
		"unSplitAmount":     unSplitAmount,
		"totalProfitAmount": totalProfitAmount,
	})
	return false
}

// 获取剩余可分账金额
func GetUnSplitAmount(ctx context.Context, orderId, parentMerchantNo, merchantNo string) (uint64, error) {
	yeePayOrder, err := GetYeePayOrder(ctx, orderId, parentMerchantNo, merchantNo)
	if err != nil {
		return 0, err
	}
	// 易宝以元为单位，这里需要转成分
	return cast.ToUint64(util.MultiplyFloatWithRound(yeePayOrder.UnSplitAmount, 100, 0)), nil
}

func needCreateOrderReceiverProfit(ctx context.Context, orderId, parentMerchantNo, merchantNo string) bool {
	// 不需要分账，则不创建
	settings, _ := ec_setting.CSettings.Get(ctx)
	if settings.YeepayConfig.DivideType == ec_setting.DIVIDE_TYPE_NOT_DIVIDE {
		return false
	}
	yeePayOrder, _ := GetYeePayOrder(ctx, orderId, parentMerchantNo, merchantNo)
	if yeePayOrder != nil && yeePayOrder.FundProcessType == "DELAY_SETTLE" {
		return true
	}
	return false
}

// 获取易宝订单
func GetYeePayOrder(ctx context.Context, orderId, parentMerchantNo, merchantNo string) (*yeepay.TradeOrderQueryResponse, error) {
	client := yeepay.NewYeepay(yeepay.YEEPAY_APP_KEY, core_util.GetMaimengPrivateKey())
	req := yeepay.TradeOrderQueryRequest{
		ParentMerchantNo: parentMerchantNo,
		MerchantNo:       merchantNo,
		OrderId:          orderId,
	}
	resp, err := client.Jiaoyi.TradeOrderQuery(ctx, &req)
	if err != nil {
		log.Warn(ctx, "Get yeePay order fail", log.Fields{
			"errMessage": err.Error(),
			"req":        req,
			"resp":       resp,
		})
		return nil, err
	}
	return resp, nil
}

func getProfitAmount(order Order) uint64 {
	// 默认一定有 extra，存 map，key actualProductPayAmount value 是 bson.M, bson.M 中 key 是 order.products.outTradeId, value 是去除易宝手续费后订单金额
	amount := uint64(0)
	extra := order.Extra
	for _, p := range order.Products {
		if IsInRefundProcess(p.RefundStatus) {
			continue
		}
		actualProductAmountMap := extra["actualProductPayAmount"].(bson.M)
		amount += cast.ToUint64(actualProductAmountMap[p.OutTradeId.Hex()])
	}
	return amount
}

func IsAllSubOrdersCompleted(ctx context.Context, order Order) bool {
	subOrders, _ := COrder.GetSubOrdersByOutTradeId(ctx, order.OutTradeId)
	completedCount := 0
	for _, o := range subOrders {
		if o.Status == ORDER_STATUS_COMPLETED || o.Status == ORDER_STATUS_CANCELED {
			completedCount++
		}
	}
	if len(subOrders) == completedCount {
		return true
	}
	return false
}

func createForYeepayWithSubOrder(ctx context.Context, order Order) {
	condition := bson.M{
		"accountId": order.AccountId,
		"$or": []bson.M{
			{
				"accountId": order.AccountId,
				"storeIds":  order.StoreId,
			},
			{
				"accountId": order.AccountId,
				"allStores": true,
			},
		},
		"type":       ec_profitsharing.RECEIVER_TYPE_MERCHANT,
		"profitType": ec_profitsharing.PROFIT_TYPE_PROFIT_SHARING,
		"relationType": bson.M{
			"$in": []string{
				ec_profitsharing.RECEIVER_RELATION_TYPE_STORE,
				ec_profitsharing.RECEIVER_RELATION_TYPE_PARTNER,
			},
		},
		"isDeleted": false,
	}
	receivers := ec_profitsharing.CProfitSharingReceiver.GetAllByCondition(ctx, condition)
	orderInfo := COrderReceiverProfit.GetOrderInfo(ctx, order)
	for _, r := range receivers {
		realPayAmount := getProfitAmount(order)
		log.Warn(ctx, "CreateForYeepayWithSubOrder log", log.Fields{
			"receiver":      r,
			"realPayAmount": realPayAmount,
			"order":         order,
		})
		receiverOrderProfit := OrderReceiverProfit{
			Order:           orderInfo,
			ReceiverId:      r.Id,
			Proportion:      r.Proportion,
			ProfitAmount:    uint64((float64(realPayAmount) * r.Proportion) / 100),
			TransferChannel: TRANSFER_CHANNEL_YEEPAY,
			ProfitType:      r.ProfitType,
			ShareAt:         time.Now().AddDate(0, 0, 7),
			CycleType:       ec_setting.PROFITSHARING_CYCLE_TYPE_DAY,
		}

		err := receiverOrderProfit.CreateNotCheckAmount(ctx)
		if err != nil {
			log.Warn(ctx, "Failed to create orderReceiverProfit", log.Fields{
				"function":            "createForYeepay",
				"orderReceiverProfit": receiverOrderProfit,
				"errorMessage":        err.Error(),
			})
		}
	}
}

func (OrderReceiverProfit) createForLargeScale(ctx context.Context, order Order) {
	condition := bson.M{
		"accountId":  order.AccountId,
		"type":       ec_profitsharing.RECEIVER_TYPE_BANK,
		"storeIds":   order.StoreId,
		"profitType": ec_profitsharing.PROFIT_TYPE_PROFIT_SHARING,
		"isDeleted":  false,
	}
	receivers := ec_profitsharing.CProfitSharingReceiver.GetAllByCondition(ctx, condition)
	orderInfo := COrderReceiverProfit.GetOrderInfo(ctx, order)
	transferChannel := TRANSFER_CHANNEL_WECHAT_BANK
	if order.IsYeepay() {
		transferChannel = TRANSFER_CHANNEL_YEEPAY
	}
	for _, r := range receivers {
		realPayAmount := uint64(0)
		for _, p := range order.Products {
			if IsInRefundProcess(p.RefundStatus) {
				continue
			}
			realPayAmount += p.PayAmount
		}

		receiverOrderProfit := OrderReceiverProfit{
			Order:                orderInfo,
			ReceiverId:           r.Id,
			Proportion:           r.Proportion,
			ProfitAmount:         uint64((float64(realPayAmount) * r.Proportion) / 100),
			TransferChannel:      transferChannel,
			CycleType:            ec_setting.PROFITSHARING_CYCLE_TYPE_MONTH,
			ProfitType:           r.ProfitType,
			SingleOrderProfitCap: r.SingleOrderProfitCap,
		}
		if receiverOrderProfit.SingleOrderProfitCap > 0 && receiverOrderProfit.ProfitAmount > receiverOrderProfit.SingleOrderProfitCap {
			receiverOrderProfit.ProfitAmount = receiverOrderProfit.SingleOrderProfitCap
		}

		err := receiverOrderProfit.Create(ctx)
		if err != nil {
			log.Warn(ctx, "Failed to create orderReceiverProfit", log.Fields{
				"function":            "createForLargeScale",
				"orderReceiverProfit": receiverOrderProfit,
				"errorMessage":        err.Error(),
			})
		}
	}
}

func (OrderReceiverProfit) createForSmallScale(ctx context.Context, order Order, commissionType uint64) {
	condition := bson.M{
		"accountId": order.AccountId,
		"type": bson.M{"$in": []string{
			ec_profitsharing.RECEIVER_TYPE_MERCHANT,
			ec_profitsharing.RECEIVER_TYPE_PERSONAL,
		}},
		"$or": []bson.M{
			{"storeIds": order.StoreId},
			{"allStores": true},
		},
		"profitType": ec_profitsharing.PROFIT_TYPE_PROFIT_SHARING,
		"isDeleted":  false,
	}

	condition = util.FormatConditionContainedOr(condition)

	receivers := ec_profitsharing.CProfitSharingReceiver.GetAllByCondition(ctx, condition)
	orderInfo := COrderReceiverProfit.GetOrderInfo(ctx, order)
	transferChannel := TRANSFER_CHANNEL_WECHAT_PROFITSHARING
	if order.IsYeepay() {
		transferChannel = TRANSFER_CHANNEL_YEEPAY
	}
	for _, r := range receivers {
		realPayAmount := uint64(0)
		for _, p := range order.Products {
			if IsInRefundProcess(p.RefundStatus) {
				continue
			}
			realPayAmount += p.PayAmount
		}

		receiverOrderProfit := OrderReceiverProfit{
			Order:                orderInfo,
			ReceiverId:           r.Id,
			Proportion:           r.Proportion,
			ProfitAmount:         uint64((float64(realPayAmount) * r.Proportion) / 100),
			CycleType:            ec_setting.PROFITSHARING_CYCLE_TYPE_DAY,
			TransferChannel:      transferChannel,
			ProfitType:           r.ProfitType,
			ShareAt:              ec_setting.GetNeedCommissionAt(time.Now(), commissionType),
			SingleOrderProfitCap: r.SingleOrderProfitCap,
		}
		if receiverOrderProfit.SingleOrderProfitCap > 0 && receiverOrderProfit.ProfitAmount > receiverOrderProfit.SingleOrderProfitCap {
			receiverOrderProfit.ProfitAmount = receiverOrderProfit.SingleOrderProfitCap
		}

		err := receiverOrderProfit.Create(ctx)
		if err != nil {
			log.Warn(ctx, "Failed to create orderReceiverProfit", log.Fields{
				"function":            "createForSmallScale",
				"orderReceiverProfit": receiverOrderProfit,
				"errorMessage":        err.Error(),
			})
		}
	}
}

func (OrderReceiverProfit) GetOrderInfo(ctx context.Context, order Order) BriefOrderInfo {
	store, err := ec_store.CStore.GetById(ctx, order.StoreId)
	storeName := ""
	if err == nil {
		storeName = store.Name
	}
	return BriefOrderInfo{
		Id:          order.Id,
		StoreId:     order.StoreId,
		StoreName:   storeName,
		TradeNo:     order.TradeNo,
		Number:      order.Number,
		PayAmount:   order.PayAmount,
		PaidAt:      order.PaidAt,
		CreatedAt:   order.CreatedAt,
		MemberId:    order.MemberId,
		DeliveryFee: order.Logistics.Fee,
		Tags:        order.Tags,
	}
}

func (OrderReceiverProfit) IterateForDailyShare(ctx context.Context) (extension.IterWrapper, error) {
	selector := bson.M{
		"accountId":      util.GetAccountIdAsObjectId(ctx),
		"isDeleted":      false,
		"transferBillId": bson.M{"$exists": false},
		"shareAt":        bson.M{"$lte": time.Now()},
		"cycleType":      CYCLE_TYPE_DAY,
		"status":         ORDER_RECEIVER_PROFIT_STATUS_PENDING,
	}
	it, err := extension.DBRepository.Iterate(ctx, C_ORDER_RECEIVER_PROFIT, selector, []string{"createdAt"})
	return it, err
}

func (OrderReceiverProfit) StatisticAndUpdateForMonthlyShare(ctx context.Context, receiverId bson.ObjectId, transferChannel string) (bson.ObjectId, uint64, error) {
	startTime, endTime := util.GetLastMonthTimeSpan(time.Now())

	selector := bson.M{
		"accountId":       util.GetAccountIdAsObjectId(ctx),
		"isDeleted":       false,
		"receiverId":      receiverId,
		"cycleType":       CYCLE_TYPE_MONTH,
		"status":          ORDER_RECEIVER_PROFIT_STATUS_PENDING,
		"transferBillId":  bson.M{"$exists": false},
		"transferChannel": transferChannel,
		"createdAt": bson.M{
			"$gte": startTime,
			"$lte": endTime,
		},
	}
	selectFields := bson.M{"profitAmount": 1}

	profits := []OrderReceiverProfit{}
	err := extension.DBRepository.FindAllWithFields(ctx, C_ORDER_RECEIVER_PROFIT, selector, selectFields, []string{"createdAt"}, 0, &profits)
	if err != nil {
		return "", 0, err
	}
	if len(profits) == 0 {
		return "", 0, nil
	}

	transferBillId := bson.NewObjectId()
	updator := bson.M{
		"$set": bson.M{
			"transferBillId": transferBillId,
			"status":         ORDER_RECEIVER_PROFIT_STATUS_PROCESSING,
			"updatedAt":      time.Now(),
		},
	}
	_, err = extension.DBRepository.UpdateAll(ctx, C_ORDER_RECEIVER_PROFIT, selector, updator)
	if err != nil {
		return "", 0, err
	}

	totalProfitAmount := uint64(0)
	for _, profit := range profits {
		totalProfitAmount += profit.ProfitAmount
	}
	return transferBillId, totalProfitAmount, nil
}

func (OrderReceiverProfit) StatisticMonthlyCommissionAmount(ctx context.Context, receiverId, storeId bson.ObjectId) (bson.ObjectId, []bson.ObjectId, uint64, error) {
	startTime, endTime := util.GetLastMonthTimeSpan(time.Now())
	selector := bson.M{
		"accountId":      util.GetAccountIdAsObjectId(ctx),
		"isDeleted":      false,
		"receiverId":     receiverId,
		"cycleType":      CYCLE_TYPE_MONTH,
		"status":         ORDER_RECEIVER_PROFIT_STATUS_PENDING,
		"transferBillId": bson.M{"$exists": false},
		"profitType":     "commission",
		"order.storeId":  storeId,
		"createdAt": bson.M{
			"$gte": startTime,
			"$lte": endTime,
		},
	}
	profits := []OrderReceiverProfit{}
	err := extension.DBRepository.FindAll(ctx, C_ORDER_RECEIVER_PROFIT, selector, []string{"createdAt"}, 0, &profits)
	if err != nil {
		return "", nil, 0, err
	}
	if len(profits) == 0 {
		return "", nil, 0, nil
	}

	totalProfitAmount := uint64(0)
	var underUpdateCommissionOrderIds []bson.ObjectId
	for _, profit := range profits {
		totalProfitAmount += profit.ProfitAmount
		underUpdateCommissionOrderIds = append(underUpdateCommissionOrderIds, profit.Order.Id)
	}

	if totalProfitAmount == 0 {
		return "", nil, 0, nil
	}

	transferBillId := bson.NewObjectId()
	updater := bson.M{
		"$set": bson.M{
			"transferBillId": transferBillId,
			"status":         ORDER_RECEIVER_PROFIT_STATUS_PROCESSING,
			"updatedAt":      time.Now(),
		},
	}

	_, err = extension.DBRepository.UpdateAll(ctx, C_ORDER_RECEIVER_PROFIT, selector, updater)
	if err != nil {
		return "", nil, 0, err
	}
	return transferBillId, underUpdateCommissionOrderIds, totalProfitAmount, nil
}

func (profit *OrderReceiverProfit) SetProcessing(ctx context.Context, billId bson.ObjectId) error {
	selector := Common.GenDefaultConditionById(ctx, profit.Id)
	selector["status"] = ORDER_RECEIVER_PROFIT_STATUS_PENDING
	selector["transferBillId"] = bson.M{
		"$exists": false,
	}
	updator := bson.M{
		"$set": bson.M{
			"transferBillId": billId,
			"status":         ORDER_RECEIVER_PROFIT_STATUS_PROCESSING,
			"updatedAt":      time.Now(),
		},
	}
	return Common.UpdateOne(ctx, C_ORDER_RECEIVER_PROFIT, "", selector, updator)
}

func (OrderReceiverProfit) IterateProcessing(ctx context.Context) (extension.IterWrapper, error) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"status":    ORDER_RECEIVER_PROFIT_STATUS_PROCESSING,
	}
	it, err := extension.DBRepository.Iterate(ctx, C_ORDER_RECEIVER_PROFIT, selector, []string{"_id"})
	return it, err
}

func (profit *OrderReceiverProfit) BatchUpdateStatusByBillId(ctx context.Context, billId bson.ObjectId) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["transferBillId"] = billId

	setter := bson.M{
		"status":    profit.Status,
		"updatedAt": time.Now(),
	}
	if profit.FinishedAt.Unix() > 0 {
		setter["finishedAt"] = profit.FinishedAt
	}
	if profit.Remarks != "" {
		setter["remarks"] = profit.Remarks
	}

	updator := bson.M{
		"$set": setter,
	}

	_, err := extension.DBRepository.UpdateAll(ctx, C_ORDER_RECEIVER_PROFIT, selector, updator)
	return err
}

func (*OrderReceiverProfit) IsAllSucceed(ctx context.Context, orderId bson.ObjectId) bool {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"order.id":  orderId,
		"status": bson.M{
			"$nin": []string{
				ORDER_RECEIVER_PROFIT_STATUS_SUCCESS,
				ORDER_RECEIVER_PROFIT_STATUS_NOCOMMISSION,
				ORDER_RECEIVER_PROFIT_STATUS_PENDING_PAYMENT, // 分账到微信已经完成了
			},
		},
		"isDeleted": false,
	}

	profit := OrderReceiverProfit{}
	extension.DBRepository.FindOne(ctx, C_ORDER_RECEIVER_PROFIT, selector, &profit)

	if profit.Id.IsZero() {
		return true
	}

	return false
}

func (*OrderReceiverProfit) IsAllProcessed(ctx context.Context, orderId bson.ObjectId) bool {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"order.id":  orderId,
		"status":    ORDER_RECEIVER_PROFIT_STATUS_PENDING,
		"isDeleted": false,
	}

	profit := OrderReceiverProfit{}
	extension.DBRepository.FindOne(ctx, C_ORDER_RECEIVER_PROFIT, selector, &profit)

	if profit.Id.Hex() == "" {
		return true
	}

	return false
}

func (*OrderReceiverProfit) IsDistOrderProcessed(ctx context.Context, orderId bson.ObjectId) bool {
	selector := bson.M{
		"accountId":  util.GetAccountIdAsObjectId(ctx),
		"order.id":   orderId,
		"profitType": PROFIT_TYPE_DISTRIBUTION,
		"status":     bson.M{"$in": processedStatus},
		"isDeleted":  false,
	}

	profit := OrderReceiverProfit{}
	err := extension.DBRepository.FindOne(ctx, C_ORDER_RECEIVER_PROFIT, selector, &profit)
	if err != nil || profit.Id.Hex() == "" {
		return false
	}

	return true
}

// 将用于分销的单和用于小比例分账单的状态改为 noCommission
func (*OrderReceiverProfit) UpdateUnProcessSmallScaleProfitsToNoCommission(ctx context.Context, orderId bson.ObjectId) error {
	selector := bson.M{
		"accountId":  util.GetAccountIdAsObjectId(ctx),
		"order.id":   orderId,
		"status":     ORDER_RECEIVER_PROFIT_STATUS_PENDING,
		"profitType": PROFIT_TYPE_PROFITSHARING,
		"cycleType":  CYCLE_TYPE_DAY,
		"isDeleted":  false,
	}
	updator := bson.M{
		"$set": bson.M{
			"status":       ORDER_RECEIVER_PROFIT_STATUS_NOCOMMISSION,
			"remarks":      "发生退款，不再分账。",
			"profitAmount": 0,
			"updatedAt":    time.Now(),
		},
	}

	_, err := extension.DBRepository.UpdateAll(ctx, C_ORDER_RECEIVER_PROFIT, selector, updator)
	return err
}

func (*OrderReceiverProfit) UpdateDistributionAmount(ctx context.Context, profitAmount uint64, orderId bson.ObjectId) error {
	selector := bson.M{
		"accountId":  util.GetAccountIdAsObjectId(ctx),
		"order.id":   orderId,
		"status":     ORDER_RECEIVER_PROFIT_STATUS_PENDING,
		"profitType": PROFIT_TYPE_DISTRIBUTION,
		"isDeleted":  false,
	}
	updator := bson.M{
		"$set": bson.M{
			"profitAmount": profitAmount,
			"updatedAt":    time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER_RECEIVER_PROFIT, selector, updator)
}

// 重新计算大比例分账应当分的金额
func (*OrderReceiverProfit) ReCalcProfitAmount(ctx context.Context, orderId bson.ObjectId, newPayAmount uint64, stats orderProductStatusStats) error {
	selector := bson.M{
		"accountId":  util.GetAccountIdAsObjectId(ctx),
		"order.id":   orderId,
		"status":     ORDER_RECEIVER_PROFIT_STATUS_PENDING,
		"profitType": PROFIT_TYPE_PROFITSHARING,
		"cycleType":  CYCLE_TYPE_MONTH,
		"isDeleted":  false,
	}
	iter, err := extension.DBRepository.Iterate(ctx, C_ORDER_RECEIVER_PROFIT, selector, []string{"_id"})
	if err != nil {
		return err
	}
	defer iter.Close()

	profit := &OrderReceiverProfit{}
	for iter.Next(profit) {
		newProfitAmount := uint64(float64(newPayAmount) * profit.Proportion / 100)
		profit.ProfitAmount = newProfitAmount
		profit.Remarks = "发生退款，更新分账金额"

		// 未分账之前，商品全部退款的订单对应的预分账单更新为 noCommission
		// 后台不需要显示这种预分账单
		if stats.isAllRefunded {
			profit.ProfitAmount = 0
			profit.Remarks = "商品全部退款，不再分账"
			profit.Status = ORDER_RECEIVER_PROFIT_STATUS_NOCOMMISSION
		}
		err := profit.updateProfitAmount(ctx)

		if err != nil {
			log.Warn(ctx, "Failed to update profit amount", log.Fields{
				"orderReceiverProfitId": profit.Id.Hex(),
				"newProfitAmount":       newProfitAmount,
				"errorMessage":          err.Error(),
			})
		}
	}
	return nil
}

// 重新计算代销分佣订单应当分的钱
func (*OrderReceiverProfit) ReCalcProfitCommissionAmount(ctx context.Context, orderId bson.ObjectId, stats orderProductStatusStats) error {
	// 计算分佣总和
	mallCommissionAmount, err := mall_service.GetLatestMallCommissionProportionAmount(ctx, orderId.Hex(), "")
	if err != nil {
		log.Warn(ctx, "calculate total commission amount failed", log.Fields{"error": err})
	}

	selector := bson.M{
		"accountId":  util.GetAccountIdAsObjectId(ctx),
		"order.id":   orderId,
		"status":     ORDER_RECEIVER_PROFIT_STATUS_PENDING,
		"profitType": PROFIT_TYPE_COMMISSION,
		"cycleType":  CYCLE_TYPE_MONTH,
		"isDeleted":  false,
	}
	iter, err := extension.DBRepository.Iterate(ctx, C_ORDER_RECEIVER_PROFIT, selector, []string{"_id"})
	if err != nil {
		return err
	}
	defer iter.Close()

	profit := &OrderReceiverProfit{}
	for iter.Next(profit) {
		profit.ProfitAmount = mallCommissionAmount
		profit.Remarks = "发生退款，更新分佣金额"

		// 商品全部退款的订单对应的预分账单更新为 noCommission
		if stats.isAllRefunded {
			profit.ProfitAmount = 0
			profit.Remarks = "商品全部退款，不再分佣"
			profit.Status = ORDER_RECEIVER_PROFIT_STATUS_NOCOMMISSION
		}
		err := profit.updateProfitAmount(ctx)

		if err != nil {
			log.Warn(ctx, "Failed to update profit amount", log.Fields{
				"orderReceiverProfitId": profit.Id.Hex(),
				"newProfitAmount":       mallCommissionAmount,
				"errorMessage":          err.Error(),
			})
		}
	}
	return nil
}

func (profit *OrderReceiverProfit) updateProfitAmount(ctx context.Context) error {
	selector := bson.M{
		"_id":       profit.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updator := bson.M{
		"$set": bson.M{
			"status":       profit.Status,
			"profitAmount": profit.ProfitAmount,
			"remarks":      profit.Remarks,
			"updatedAt":    time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_ORDER_RECEIVER_PROFIT, selector, updator)
}

func (*OrderReceiverProfit) GetPromoterMonthlyProfitAmount(ctx context.Context, promoterId bson.ObjectId) int64 {
	profitAmount := int64(0)
	startTime, endTime := util.GetLastMonthTimeSpan(time.Now())
	selector := bson.M{
		"accountId":   util.GetAccountIdAsObjectId(ctx),
		"isDeleted":   false,
		"status":      ORDER_RECEIVER_PROFIT_STATUS_SUCCESS,
		"promoter.id": promoterId,
		"createdAt": bson.M{
			"$gte": startTime,
			"$lte": endTime,
		},
	}

	profits := []OrderReceiverProfit{}
	err := extension.DBRepository.FindAll(ctx, C_ORDER_RECEIVER_PROFIT, selector, []string{}, 0, &profits)
	if err != nil {
		return profitAmount
	}

	for _, p := range profits {
		profitAmount += int64(p.ProfitAmount)
	}

	return profitAmount
}

func (*OrderReceiverProfit) GetProfitAmountByPromoterId(ctx context.Context, promoterId bson.ObjectId) (uint64, uint64) {
	var (
		totalProfitAmount uint64
		profitedAmount    uint64
	)

	totalSelector := bson.M{
		"accountId":   util.GetAccountIdAsObjectId(ctx),
		"isDeleted":   false,
		"promoter.id": promoterId,
		"profitType":  PROFIT_TYPE_DISTRIBUTION,
	}

	totalProfits := []OrderReceiverProfit{}
	err := extension.DBRepository.FindAllWithFields(ctx, C_ORDER_RECEIVER_PROFIT, totalSelector, bson.M{"profitAmount": 1}, []string{}, 0, &totalProfits)
	if err != nil {
		return totalProfitAmount, profitedAmount
	}

	profitedSelector := totalSelector
	profitedSelector["status"] = ORDER_RECEIVER_PROFIT_STATUS_SUCCESS

	profitedProfits := []OrderReceiverProfit{}
	err = extension.DBRepository.FindAllWithFields(ctx, C_ORDER_RECEIVER_PROFIT, totalSelector, bson.M{"profitAmount": 1}, []string{}, 0, &profitedProfits)
	if err != nil {
		return totalProfitAmount, profitedAmount
	}

	for _, t := range totalProfits {
		totalProfitAmount += t.ProfitAmount
	}
	for _, p := range profitedProfits {
		profitedAmount += p.ProfitAmount
	}

	return totalProfitAmount, totalProfitAmount - profitedAmount
}

func (*OrderReceiverProfit) GetByPromoterIds(ctx context.Context, promoterIds []bson.ObjectId) ([]OrderReceiverProfit, error) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"promoter.id": bson.M{
			"$in": promoterIds,
		},
		"isDeleted": false,
	}

	profitOrders := []OrderReceiverProfit{}
	err := extension.DBRepository.FindAll(ctx, C_ORDER_RECEIVER_PROFIT, selector, []string{}, 0, &profitOrders)
	return profitOrders, err
}

func (*OrderReceiverProfit) GetByPromoterIdAndOrderId(ctx context.Context, promoterId, orderId bson.ObjectId) (OrderReceiverProfit, error) {
	selector := bson.M{
		"accountId":   util.GetAccountIdAsObjectId(ctx),
		"promoter.id": promoterId,
		"order.id":    orderId,
		"isDeleted":   false,
	}

	profitOrder := OrderReceiverProfit{}
	err := extension.DBRepository.FindOne(ctx, C_ORDER_RECEIVER_PROFIT, selector, &profitOrder)
	return profitOrder, err
}

func (*OrderReceiverProfit) GetByOrderId(ctx context.Context, orderId bson.ObjectId) (OrderReceiverProfit, error) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"order.id":  orderId,
		"isDeleted": false,
	}

	profitOrder := OrderReceiverProfit{}
	err := extension.DBRepository.FindOne(ctx, C_ORDER_RECEIVER_PROFIT, selector, &profitOrder)
	return profitOrder, err
}

func (*OrderReceiverProfit) GetByOrderIds(ctx context.Context, orderIds []bson.ObjectId) ([]OrderReceiverProfit, error) {
	if len(orderIds) == 0 {
		return nil, nil
	}

	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"order.id": bson.M{
			"$in": orderIds,
		},
		"isDeleted": false,
	}

	profitOrders := []OrderReceiverProfit{}
	err := extension.DBRepository.FindAll(ctx, C_ORDER_RECEIVER_PROFIT, selector, []string{}, 0, &profitOrders)
	return profitOrders, err
}

func (*OrderReceiverProfit) Iterate(ctx context.Context, selector bson.M, sorter []string) (extension.IterWrapper, error) {
	it, err := extension.DBRepository.Iterate(ctx, C_ORDER_RECEIVER_PROFIT, selector, sorter)
	return it, err
}

func (*OrderReceiverProfit) GetAllByCondition(ctx context.Context, condition bson.M) ([]OrderReceiverProfit, error) {
	profitOrder := []OrderReceiverProfit{}
	err := extension.DBRepository.FindAll(ctx, C_ORDER_RECEIVER_PROFIT, condition, []string{}, 0, &profitOrder)
	return profitOrder, err
}

func (*OrderReceiverProfit) GetOneByCondition(ctx context.Context, condition bson.M) (OrderReceiverProfit, error) {
	result := OrderReceiverProfit{}
	err := extension.DBRepository.FindOne(ctx, C_ORDER_RECEIVER_PROFIT, condition, &result)
	return result, err
}

func (*OrderReceiverProfit) GetByBillId(ctx context.Context, billId bson.ObjectId) (OrderReceiverProfit, error) {
	result := OrderReceiverProfit{}
	selector := share_model.Base.GenDefaultCondition(ctx)
	selector["transferBillId"] = billId
	err := extension.DBRepository.FindOne(ctx, C_ORDER_RECEIVER_PROFIT, selector, &result)
	return result, err
}

func (*OrderReceiverProfit) UpdateToProcessingFromFailed(ctx context.Context, billIds []bson.ObjectId) (int, error) {
	selector := share_model.Base.GenDefaultCondition(ctx)
	selector["status"] = ORDER_RECEIVER_PROFIT_STATUS_FAILED
	selector["transferBillId"] = bson.M{
		"$in": billIds,
	}

	updater := bson.M{
		"$set": bson.M{
			"status":    ORDER_RECEIVER_PROFIT_STATUS_PROCESSING,
			"updatedAt": time.Now(),
		},
	}

	return extension.DBRepository.UpdateAll(ctx, C_ORDER_RECEIVER_PROFIT, selector, updater)
}

// 过滤距离订单支付时间超过 29 天的账单 id
func (*OrderReceiverProfit) FilterNeedRetryByWechatBalanceBillIds(ctx context.Context, billIds []bson.ObjectId) ([]bson.ObjectId, error) {
	selector := bson.M{
		"accountId":       util.GetAccountIdAsObjectId(ctx),
		"isDeleted":       false,
		"transferChannel": TRANSFER_CHANNEL_WECHAT_PROFITSHARING,
		"transferBillId": bson.M{
			"$in": billIds,
		},
		"$or": []bson.M{
			{
				"order.paidAt": bson.M{
					"$lt": time.Now().AddDate(0, 0, -29),
				},
			},
			{
				"order.paidAt": bson.M{
					"$exists": false,
				},
			},
		},
	}

	transferBillIds := []interface{}{}
	err := extension.DBRepository.Distinct(ctx, C_ORDER_RECEIVER_PROFIT, selector, "transferBillId", &transferBillIds)
	if err != nil {
		return nil, err
	}
	return core_util.ToObjectIdArray(transferBillIds), nil
}

func (*OrderReceiverProfit) GetById(ctx context.Context, id bson.ObjectId) (OrderReceiverProfit, error) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       id,
		"isDeleted": false,
	}
	o := OrderReceiverProfit{}
	err := extension.DBRepository.FindOne(ctx, C_ORDER_RECEIVER_PROFIT, selector, &o)
	return o, err
}

func (*OrderReceiverProfit) GetByIds(ctx context.Context, ids []bson.ObjectId) ([]OrderReceiverProfit, error) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id": bson.M{
			"$in": ids,
		},
		"isDeleted": false,
	}
	var profits []OrderReceiverProfit
	err := extension.DBRepository.FindAll(ctx, C_ORDER_RECEIVER_PROFIT, selector, nil, 0, &profits)
	return profits, err
}

func (*OrderReceiverProfit) DeleteByIds(ctx context.Context, ids []bson.ObjectId, remarks string) (int, error) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       bson.M{"$in": ids},
	}
	setter := bson.M{
		"isDeleted": true,
		"updatedAt": time.Now(),
	}
	if remarks != "" {
		setter["remarks"] = remarks
	}
	updator := bson.M{
		"$set": setter,
	}
	return extension.DBRepository.UpdateAll(ctx, C_ORDER_RECEIVER_PROFIT, selector, updator)
}

func (profit *OrderReceiverProfit) BatchUpdateSharedAtByOrderId(ctx context.Context, orderId bson.ObjectId, ids []bson.ObjectId) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["order.id"] = orderId
	if len(ids) > 0 {
		selector["_id"] = bson.M{"$in": ids}
	}

	setter := bson.M{
		"updatedAt": time.Now(),
		"shareAt":   time.Now(),
	}

	updator := bson.M{
		"$set": setter,
	}

	_, err := extension.DBRepository.UpdateAll(ctx, C_ORDER_RECEIVER_PROFIT, selector, updator)
	return err
}

func (*OrderReceiverProfit) GetByTransferBillId(ctx context.Context, billId bson.ObjectId) (OrderReceiverProfit, error) {
	condition := Common.GenDefaultCondition(ctx)
	condition["transferBillId"] = billId
	var profit OrderReceiverProfit
	err := extension.DBRepository.FindOne(ctx, C_ORDER_RECEIVER_PROFIT, condition, &profit)
	return profit, err
}
