package order

import (
	"fmt"
	wechat_trade "mairpc/core/component/wechat/trade"
	pb_member "mairpc/proto/member"
	ec_model_logistic "mairpc/service/ec/model/logistic"
	"mairpc/service/share/util"
	"time"

	"mairpc/core/errors"
	"mairpc/core/extension"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"

	"mairpc/core/extension/bson"

	"golang.org/x/net/context"
)

const (
	C_LOGISTICS = "ec.logistics"

	LOGISTICS_STATUS_RESERVED   = "reserved"   // 已发货
	LOGISTICS_STATUS_TAKEN      = "taken"      // 已揽件
	LOGISTICS_STATUS_SHIPPING   = "shipping"   // 配送中
	LOGISTICS_STATUS_DELIVERING = "delivering" // 派送中
	LOGISTICS_STATUS_SIGNED     = "signed"     // 已签收
	LOGISTICS_STATUS_FAILED     = "failed"     // 失败

	LOGISTICS_TYPE_WECHAT        = "wechat"        // 微信物流助手
	LOGISTICS_TYPE_SELF          = "self"          // 其他方式
	LOGISTICS_TYPE_OMS           = "oms"           // 第三方 OMS 系统
	LOGISTICS_TYPE_SELF_DELIVERY = "self_delivery" // 自行配送

	LOGISTICS_OPERATOR_TYPE_USER  = "user"  // 管理后台用户
	LOGISTICS_OPERATOR_TYPE_STAFF = "staff" // 店员
)

var CLogistics = &Logistics{}

type Logistics struct {
	Id           bson.ObjectId      `bson:"_id,omitempty"`
	AccountId    bson.ObjectId      `bson:"accountId"`
	Order        LogisticsOrder     `bson:"order"`
	OutTradeIds  []bson.ObjectId    `bson:"outTradeIds"`
	ProcessBy    LogisticsProcessBy `bson:"processBy"`
	WaybillId    string             `bson:"waybillId"`
	PickupCode   string             `bson:"pickupCode"`
	DeliveryId   string             `bson:"deliveryId"`
	DeliveryName string             `bson:"deliveryName"`
	Products     []LogisticsProduct `bson:"products"`
	Status       string             `bson:"status"`
	Member       Member             `bson:"member"`
	Cargo        Cargo              `bson:"cargo"`
	Insured      Insured            `bson:"insured,omitempty"`
	Service      Service            `bson:"service"`
	Courier      Courier            `bson:"courier,omitempty"`
	ShippedAt    time.Time          `bson:"shippedAt"`
	Trace        LogisticsTrace     `bson:"trace,omitempty"`
	CreatedAt    time.Time          `bson:"createdAt"`
	UpdatedAt    time.Time          `bson:"updatedAt"`
	IsDeleted    bool               `bson:"isDeleted"`
}

type LogisticsTrace struct {
	Package   string    `bson:"package"`
	UpdatedAt time.Time `bson:"updatedAt"`
	Completed bool      `bson:"completed"`
}

type LogisticsOrder struct {
	Id     bson.ObjectId `bson:"id"`
	Method string        `bson:"method"`
	Type   string        `bson:"type"`
	Number string        `bson:"number"`
}

type LogisticsProcessBy struct {
	Store        LogisticsProcessByDetail `bson:"store,omitempty"`
	Staff        LogisticsProcessByDetail `bson:"staff,omitempty"`
	User         LogisticsProcessByDetail `bson:"user,omitempty"`
	OperatorType string                   `bson:"operatorType"`
}

type LogisticsProcessByDetail struct {
	Id    bson.ObjectId `bson:"id"`
	Name  string        `bson:"name"`
	Phone string        `bson:"phone"`
	// staffNo 或者 store.Code，仅用于记录核销事件参数
	Code string `bson:"-"`
}

type LogisticsProduct struct {
	Id           bson.ObjectId    `bson:"id"`
	OutTradeId   bson.ObjectId    `bson:"outTradeId"`
	Name         string           `bson:"name"`
	Total        uint64           `bson:"total"`
	Spec         OrderProductSpec `bson:"spec"`
	Type         string           `bson:"type"`
	RefundStatus string           `bson:"refundStatus"`
}

type Member struct {
	Id        bson.ObjectId `bson:"id"`
	Name      string        `bson:"name"`
	ChannelId string        `bson:"channelId"`
	OpenId    string        `bson:"openId"`
}

type Cargo struct {
	Count      uint64       `bson:"count"`
	Weight     uint64       `bson:"weight"`
	SpaceX     uint64       `bson:"spaceX"` // 包裹长度，字段名与微信保持一致
	SpaceY     uint64       `bson:"spaceY"` // 包裹宽度
	SpaceZ     uint64       `bson:"spaceZ"` // 包裹高度
	DetailList []DetailList `bson:"detailList"`
}

type DetailList struct {
	Name  string `bson:"name"`
	Count uint64 `bson:"count"`
}

type Insured struct {
	Enabled      bool   `bson:"enabled"`
	InsuredValue uint64 `bson:"insuredValue"`
}

type Service struct {
	Type uint64 `bson:"type"`
	Name string `bson:"name"`
}

func (self *LogisticsProcessByDetail) Set(id bson.ObjectId, name, code, phone string) {
	self.Id = id
	self.Name = name
	self.Code = code
	self.Phone = phone
}

func (l *Logistics) Create(ctx context.Context) error {
	l.Id = bson.NewObjectId()
	l.AccountId = util.GetAccountIdAsObjectId(ctx)
	l.CreatedAt = time.Now()
	l.UpdatedAt = l.CreatedAt

	_, err := extension.DBRepository.Insert(ctx, C_LOGISTICS, *l)
	return err
}

func (*Logistics) BatchUpdateMemberId(ctx context.Context, oldMemberIds []bson.ObjectId, newMemberId bson.ObjectId) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["member.id"] = bson.M{
		"$in": oldMemberIds,
	}

	updator := bson.M{
		"$set": bson.M{
			"member.id": newMemberId,
			"updatedAt": time.Now(),
		},
	}

	_, err := extension.DBRepository.UpdateAll(ctx, C_LOGISTICS, selector, updator)
	return err
}

func (Logistics) GetByOrderIdAndWaybillId(ctx context.Context, orderId bson.ObjectId, waybillId string) (Logistics, error) {
	logistics := Logistics{}
	selector := Common.GenDefaultCondition(ctx)
	selector["order.id"] = orderId
	selector["waybillId"] = waybillId
	err := Common.GetOneByCondition(ctx, selector, C_LOGISTICS, &logistics)
	return logistics, err
}

func (Logistics) GetAllByOrderId(ctx context.Context, orderId bson.ObjectId) ([]Logistics, error) {
	logistics := []Logistics{}
	selector := Common.GenDefaultCondition(ctx)
	selector["order.id"] = orderId
	_, err := Common.GetAllByCondition(ctx, selector, []string{}, 0, C_LOGISTICS, &logistics)
	return logistics, err
}

func (Logistics) GetById(ctx context.Context, id bson.ObjectId) (Logistics, error) {
	logistics := Logistics{}
	selector := Common.GenDefaultConditionById(ctx, id)
	err := Common.GetOneByCondition(ctx, selector, C_LOGISTICS, &logistics)
	return logistics, err
}

func (Logistics) GetByPagination(ctx context.Context, page extension.PagingCondition) (int, []Logistics, error) {
	var logistics []Logistics
	total, err := extension.DBRepository.FindByPagination(ctx, C_LOGISTICS, page, &logistics)
	if err != nil {
		return 0, nil, err
	}
	return total, logistics, nil
}

func (Logistics) UpdateProductRefundStatusByOrder(ctx context.Context, order *Order) error {
	logistics, err := CLogistics.GetAllByOrderId(ctx, order.Id)
	if err != nil {
		return err
	}
	docs := []interface{}{}
	orderProductRefundStatusMap := core_util.MakeFieldToFieldMapper("Id", "RefundStatus", order.Products)
	for _, l := range logistics {
		needUpdate := false
		for i, p := range l.Products {
			rs, ok := orderProductRefundStatusMap[p.Id]
			if !ok {
				continue
			}
			refundStatus := rs.(string)
			if p.RefundStatus != refundStatus {
				needUpdate = true
				l.Products[i].RefundStatus = refundStatus
			}
		}
		if !needUpdate {
			continue
		}
		condition := bson.M{
			"_id":       l.Id,
			"accountId": l.AccountId,
		}
		updater := bson.M{
			"$set": bson.M{
				"products": l.Products,
			},
		}
		docs = append(docs, condition, updater)
	}
	if len(docs) == 0 {
		return nil
	}
	_, bulkErr := extension.DBRepository.BatchUpdate(ctx, C_LOGISTICS, docs...)
	if bulkErr != nil && len(bulkErr.WriteErrors) > 0 {
		return bulkErr
	}
	return nil
}

func (Logistics) BatchUpdateTrace(ctx context.Context, logistics []Logistics) error {
	if len(logistics) == 0 {
		return nil
	}
	docs := []interface{}{}
	now := time.Now()
	for _, lg := range logistics {
		condition := bson.M{
			"_id":       lg.Id,
			"accountId": util.GetAccountIdAsObjectId(ctx),
		}
		updater := bson.M{
			"$set": bson.M{
				"trace":     lg.Trace,
				"updatedAt": now,
			},
		}
		docs = append(docs, condition, updater)
	}
	_, bulkErr := extension.DBRepository.BatchUpdate(ctx, C_LOGISTICS, docs...)
	if bulkErr != nil && len(bulkErr.WriteErrors) > 0 {
		return bulkErr
	}
	return nil
}

func GenLogistics(ctx context.Context, order *Order, logisticsInfo *ProductLogisticsInfo, member *pb_member.MemberDetailResponse) (*Logistics, error) {
	// 发货目前有三种
	// 1.通过物流发货
	// 2.提货码提货
	// 3.自行配送，既没有提货码也没有物流单号
	shippedProducts := []LogisticsProduct{}
	outTradeIds := []bson.ObjectId{}
	// 发货商品信息处理分三种情况
	// 1. 快递发货，需要添加所有物流单号与发货单号相同的商品
	// 2. 门店自提订单，一个订单只有一个提货码，提货即添加所有商品信息
	// 3. 虚拟商品，每个虚拟商品都对应一个提货码，因此商品数量固定为 1，需添加指定提货码的商品信息
	for _, p := range order.Products {
		shippedProduct := LogisticsProduct{}
		copier.Instance(nil).From(p).CopyTo(&shippedProduct)
		// 门店自提订单一个提货码直接发货订单所有商品
		if order.GetType() == ORDER_TYPE_PICKUP {
			if order.PickupCode == "" || order.PickupCode != logisticsInfo.PickupCode {
				return nil, errors.NewInvalidArgumentError("pickupCode")
			}
			outTradeIds = append(outTradeIds, p.OutTradeId)
			shippedProducts = append(shippedProducts, shippedProduct)
			continue
		}
		// 虚拟商品提货码发货一个提货码对应一个商品，所以发货数量固定为 1
		if order.GetType() == ORDER_TYPE_VIRTUAL {
			if logisticsInfo.PickupCode == "" || !core_util.StrInArray(logisticsInfo.PickupCode, &p.PickupCodes) {
				continue
			}
			outTradeIds = append(outTradeIds, p.OutTradeId)
			shippedProduct.Total = 1
			shippedProducts = append(shippedProducts, shippedProduct)
			break
		}

		// 线下发货和自行配送发货都需要从 products.logistics 中获取此次发货的物流信息列表
		for _, l := range p.Logistics {
			if !l.IsCurrentShipment {
				continue
			}
			outTradeIds = append(outTradeIds, p.OutTradeId)
			shippedProduct.Total = l.ProductCount
			shippedProducts = append(shippedProducts, shippedProduct)
		}
	}
	if len(shippedProducts) == 0 {
		return nil, errors.NewInvalidArgumentErrorWithMessage("waybillId", "No shipped products")
	}
	logistics := Logistics{
		Order: LogisticsOrder{
			Id:     order.Id,
			Method: order.Method,
			Number: order.Number,
			Type:   order.GetType(),
		},
		ProcessBy:    logisticsInfo.ProcessBy,
		PickupCode:   logisticsInfo.PickupCode,
		WaybillId:    logisticsInfo.WaybillId,
		ShippedAt:    logisticsInfo.ShippedAt,
		DeliveryId:   logisticsInfo.DeliveryId,
		DeliveryName: logisticsInfo.DeliveryName,
		Status:       LOGISTICS_STATUS_RESERVED,
		Courier:      logisticsInfo.Courier,
		OutTradeIds:  outTradeIds,
		Products:     shippedProducts,
	}

	logistics.Member = Member{
		Id:        order.MemberId,
		Name:      member.Name,
		ChannelId: "", // TODO: levi.lei
		OpenId:    "",
	}
	placeholderValueMap := GenBasicPlaceholderValueMap(nil, nil, &logistics)
	placeholderValueMap[PLACEHOLDER_CURRENT_SHIPPED_PRODUCT_NAME] = shippedProducts[0].Name
	if len(shippedProducts) > 1 {
		placeholderValueMap[PLACEHOLDER_CURRENT_SHIPPED_PRODUCT_NAME] = fmt.Sprintf("%s等", shippedProducts[0].Name)
	}
	order.NotifyCustomer(ctx, ORDER_STATUS_SHIPPED, placeholderValueMap)

	return &logistics, nil
}

func (lg *Logistics) Update(ctx context.Context) error {
	selector := Common.GenDefaultConditionById(ctx, lg.Id)
	updater := bson.M{
		"$set": bson.M{
			"updatedAt":    time.Now(),
			"waybillId":    lg.WaybillId,
			"deliveryId":   lg.DeliveryId,
			"deliveryName": lg.DeliveryName,
			"trace":        lg.Trace,
			"processBy":    lg.ProcessBy,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_LOGISTICS, selector, updater)
}

func CreateWechatFreightInsuranceOrder(ctx context.Context, order *Order, waybillIds []string) {
	// 只有快递发货才需要投保运费险
	// 暂时只支持整体发货
	if order.Method != ORDER_DELIVERY_METHOD_EXPRESS ||
		len(waybillIds) != 1 ||
		order.TradeNo == "" ||
		order.Payment != PAYMENT_WECHAT {
		return
	}
	// 运费险未启用
	if !ec_model_logistic.CDeliveryFeeTemplate.GetTemplateByMethod(ctx, order.Method).IsFreightInsuranceEnabled {
		return
	}
	sender, err := order.getDeliverySender(ctx)
	if err != nil {
		return
	}
	client := wechat_trade.NewWechatTradeClient(order.Channel.ChannelId, false)
	resp, err := client.FreightInsurance.CreateFreightInsuranceOrder(ctx, &wechat_trade.CreateFreightInsuranceOrderRequest{
		OpenId:    order.Channel.OpenId,
		TradeNo:   order.TradeNo,
		PaidAt:    order.PaidAt.Unix(),
		PayAmount: int64(order.PayAmount),
		WaybillId: waybillIds[0],
		SenderAddress: wechat_trade.FreightInsuranceAddress{
			Province: sender.Province,
			City:     sender.City,
			District: sender.Area,
			Detail:   sender.Address,
		},
		ReceiverAddress: wechat_trade.FreightInsuranceAddress{
			Province: order.Contact.Address.Province,
			City:     order.Contact.Address.City,
			District: order.Contact.Address.District,
			Detail:   order.Contact.Address.Detail,
		},
		OrderInfo: wechat_trade.FreightInsuranceOrderInfo{
			OrderPathInMiniProgram: fmt.Sprintf(ORDER_DETAIL_PAGE, order.Id.Hex()),
			Products:               []wechat_trade.FreightInsuranceProduct{},
		},
	})
	if err != nil || resp.Number == "" {
		return
	}
	extra := order.Extra
	if len(extra) == 0 {
		extra = bson.M{}
	}
	extra[WECHAT_FREIGHT_INSURANCE_EXTRA] = bson.M{
		"number":  resp.Number,
		"endAt":   resp.EndAt,
		"premium": resp.Premium,
		"amount":  resp.EstimateAmount,
	}
	order.AddTags(ctx, []string{ORDER_TAGS_WITH_WECHAT_FREIGHT_INSURANCE})
	order.Extra = extra
	order.UpdateExtra(ctx)
}
