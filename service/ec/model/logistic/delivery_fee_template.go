package logistic

import (
	"context"
	"fmt"
	"mairpc/core/component"
	"mairpc/core/log"
	"mairpc/core/util/copier"
	"strings"
	"time"

	"github.com/spf13/cast"

	"mairpc/core/errors"
	"mairpc/core/extension"
	pb "mairpc/proto/ec/logistic"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"
)

const (
	C_DELIVERY_FEE_TEMPLATE = "ec.deliveryFeeTemplate"

	CONDITION_FREE_TYPE_AMOUNT              = "amount"
	CONDITION_FREE_TYPE_WEIGHT              = "weight"
	CONDITION_FREE_TYPE_PIECE               = "piece"
	CONDITION_FREE_TYPE_DISTANCE            = "distance"
	CONDITION_FREE_TYPE_AMOUNT_AND_WEIGHT   = "amountAndWeight"
	CONDITION_FREE_TYPE_AMOUNT_AND_PIECE    = "amountAndPiece"
	CONDITION_FREE_TYPE_AMOUNT_AND_DISTANCE = "amountAndDistance"
	CONDITION_FREE_CONTAIN_FREE_PRODUCT     = "containFreeProduct"

	CONDITION_FREE_RULE_PAY_AMOUNT = "payAmount"

	TEMPLATE_METHOD_EXPRESS      = "express"
	TEMPLATE_METHOD_CITY_EXPRESS = "cityExpress"
)

var (
	CDeliveryFeeTemplate = DeliveryFeeTemplate{}
)

type DeliveryFeeTemplate struct {
	Id              bson.ObjectId `bson:"_id"`
	AccountId       bson.ObjectId `bson:"accountId"`
	CreatedAt       time.Time     `bson:"createdAt"`
	UpdatedAt       time.Time     `bson:"updatedAt,omitempty"`
	IsDeleted       bool          `bson:"isDeleted"`
	Name            string        `bson:"name"`
	Type            string        `bson:"type"`
	DeliveryFeeMode string        `bson:"deliveryFeeMode"`
	Method          string        `bson:"method"`
	ConditionFree   bool          `bson:"conditionFree"`
	AmountType      string        `bson:"amountType"`
	// 默认规则，当目标地址没有匹配到相关的计费规则，则使用默认规则，可以理解为适用于其它地区的规则
	DefaultRule               DeliveryFeeTemplateRule       `bson:"defaultRule"`
	Rules                     []DeliveryFeeTemplateRule     `bson:"rules,omitempty"`
	FreeRules                 []DeliveryFeeTemplateFreeRule `bson:"freeRules,omitempty"`
	OrderFreeWithFreeProduct  bool                          `bson:"orderFreeWithFreeProduct,omitempty"` // 含包邮商品时，是否整单包邮
	IsFreightInsuranceEnabled bool                          `bson:"isFreightInsuranceEnabled"`          // 是否开启运费险
}

type DeliveryFeeTemplateRule struct {
	FirstFee         uint64   `bson:"firstFee"`
	AdditionalFee    uint64   `bson:"additionalFee"`
	FirstAmount      uint64   `bson:"firstAmount"`
	AdditionalAmount uint64   `bson:"additionalAmount"`
	Areas            []string `bson:"areas"`
}

type DeliveryFeeTemplateFreeRule struct {
	Type     string   `bson:"type"`
	Amount   uint64   `bson:"amount"`
	Weight   uint64   `bson:"weight"`
	Pieces   uint64   `bson:"pieces"`
	Distance uint64   `bson:"distance"`
	Areas    []string `bson:"areas"`
}

func (self *DeliveryFeeTemplate) Create(ctx context.Context) error {
	self.Id = bson.NewObjectId()
	self.AccountId = util.GetAccountIdAsObjectId(ctx)
	self.CreatedAt = time.Now()
	self.IsDeleted = false

	_, err := extension.DBRepository.Insert(ctx, C_DELIVERY_FEE_TEMPLATE, self)
	return err
}

func (self *DeliveryFeeTemplate) Update(ctx context.Context) error {
	self.UpdatedAt = time.Now()
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"method":    self.Method,
		"isDeleted": false,
	}

	updater := bson.M{
		"$set": bson.M{
			"name":                      self.Name,
			"type":                      self.Type,
			"conditionFree":             self.ConditionFree,
			"amountType":                self.AmountType,
			"deliveryFeeMode":           self.DeliveryFeeMode,
			"defaultRule":               self.DefaultRule,
			"rules":                     self.Rules,
			"freeRules":                 self.FreeRules,
			"updatedAt":                 self.UpdatedAt,
			"orderFreeWithFreeProduct":  self.OrderFreeWithFreeProduct,
			"isFreightInsuranceEnabled": self.IsFreightInsuranceEnabled,
		},
	}

	return extension.DBRepository.UpdateOne(ctx, C_DELIVERY_FEE_TEMPLATE, selector, updater)
}

func (*DeliveryFeeTemplate) GetTemplateByMethod(ctx context.Context, method string) *DeliveryFeeTemplate {
	result := &DeliveryFeeTemplate{}
	if method == "" {
		method = TEMPLATE_METHOD_EXPRESS // 临时处理，前端修改之后会去掉
	}
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"method":    method,
		"isDeleted": false,
	}

	err := extension.DBRepository.FindOne(ctx, C_DELIVERY_FEE_TEMPLATE, condition, result)
	if err != nil {
		result.Id = bson.NewObjectId()
		result.AccountId = util.GetAccountIdAsObjectId(ctx)
		result.CreatedAt = time.Now()
		result.UpdatedAt = time.Now()
		result.DeliveryFeeMode = "area"
		result.FreeRules = []DeliveryFeeTemplateFreeRule{
			{
				Type: "amount",
			},
		}
		result.Method = method
		result.Create(ctx)
	}

	return result
}

func (self *DeliveryFeeTemplate) FindByPagination(ctx context.Context, page extension.PagingCondition) (int, []DeliveryFeeTemplate) {
	deliveryFeeTemplates := []DeliveryFeeTemplate{}
	total, _ := extension.DBRepository.FindByPagination(ctx, C_DELIVERY_FEE_TEMPLATE, page, &deliveryFeeTemplates)
	return total, deliveryFeeTemplates
}

func (self DeliveryFeeTemplate) CalDeliveryFee(ctx context.Context, req *pb.CalculateDeliveryFeeRequest) (uint64, error) {
	var (
		err          error
		distance     uint64
		calRule      DeliveryFeeTemplateRule
		templateType = self.Type
	)

	if self.IsStoreDeliveryFeeMode() {
		if req.StoreDeliveryFeeTemplate == nil {
			return 0, nil
		}
		copier.Instance(nil).From(req.StoreDeliveryFeeTemplate.Rule).CopyTo(&calRule)
		templateType = req.StoreDeliveryFeeTemplate.Type
	} else {
		calRule = self.DefaultRule
		// 如果传递了寄送地址且满足精确到区/县才查找相应规则，否则直接按默认规则计算运费
		if req.Address != "" && len(strings.Split(req.Address, ":")) == 3 {
			for _, rule := range self.Rules {
				if util.ValidateAddress(req.Address, rule.Areas) {
					calRule = rule
					break
				}
			}
		}
	}

	if templateType == "distance" {
		distance, err = GetDistance(ctx, req)
		if err != nil {
			return 0, err
		}
	}

	if self.IsDeliveryFeeFree(req, distance) {
		return 0, nil
	}

	if templateType == "piece" && req.Pieces == 0 {
		return 0, errors.NewInvalidArgumentError("pieces")
	}

	if templateType == "weight" && req.Weight == 0 {
		return 0, errors.NewInvalidArgumentError("weight")
	}

	var amount uint64
	if templateType == "piece" {
		amount = req.Pieces
	}

	if templateType == "weight" {
		amount = req.Weight
	}

	if templateType == "distance" {
		amount = distance
	}

	// 如果续件(重)设置为 0，不可参与后续计算，但可满足固定运费的场景，即无论买多少运费都固定返回首重运费
	if amount <= calRule.FirstAmount || calRule.AdditionalAmount == 0 {
		return calRule.FirstFee, nil
	}

	multi := (amount - calRule.FirstAmount) / calRule.AdditionalAmount
	if multi == 0 {
		multi = 1
	} else if calRule.AdditionalAmount*multi+calRule.FirstAmount != amount {
		multi += 1
	}

	return calRule.FirstFee + multi*calRule.AdditionalFee, nil
}

func GetDistance(ctx context.Context, req *pb.CalculateDeliveryFeeRequest) (uint64, error) {
	if req.FromCoordinate == nil || req.FromCoordinate.Longitude == 0 || req.FromCoordinate.Latitude == 0 {
		return 0, errors.NewInvalidArgumentError("fromCoordinate")
	}
	if req.ToCoordinate == nil || req.ToCoordinate.Longitude == 0 || req.ToCoordinate.Latitude == 0 {
		return 0, errors.NewInvalidArgumentError("toCoordinate")
	}
	distanceKey := fmt.Sprintf("%s-%s-%s-%s", cast.ToString(req.FromCoordinate.Longitude), cast.ToString(req.FromCoordinate.Latitude), cast.ToString(req.ToCoordinate.Longitude), cast.ToString(req.ToCoordinate.Latitude))
	deliveryDistance, _ := CDeliveryDistance.GetByKey(ctx, distanceKey)
	if deliveryDistance.Id.Valid() {
		return deliveryDistance.Distance, nil
	}
	matrix, err := component.TencentLBSClient.GetMatrix(ctx, component.GET_MATRIX_MODE_BICYCLING, []component.GeoLocation{
		{
			Lng: req.FromCoordinate.Longitude,
			Lat: req.FromCoordinate.Latitude,
		},
	}, []component.GeoLocation{
		{
			Lng: req.ToCoordinate.Longitude,
			Lat: req.ToCoordinate.Latitude,
		},
	})
	if err != nil || len(matrix.Rows) == 0 || len(matrix.Rows[0].Elements) == 0 {
		log.Warn(ctx, "Failed to get matrix", log.Fields{
			"errMsg": func() string {
				if err != nil {
					return err.Error()
				}
				return ""
			}(),
			"fromCoordinate": req.FromCoordinate,
			"toCoordinate":   req.ToCoordinate,
			"matrix":         matrix,
		})
		// LBS 计算失败时在代码中计算，代码计算（距离较远时可能误差较大）不存 DB，为了避免 DB 中一直存在一个较大误差的距离
		distance := util.CalDistanceByCoordinate(req.FromCoordinate.Latitude, req.FromCoordinate.Longitude, req.ToCoordinate.Latitude, req.ToCoordinate.Longitude)
		return uint64(distance), nil
	}
	newDeliveryDistance := DeliveryDistance{
		Key:      distanceKey,
		Distance: matrix.Rows[0].Elements[0].Distance,
	}
	newDeliveryDistance.Create(ctx)
	return matrix.Rows[0].Elements[0].Distance, nil
}

func (self DeliveryFeeTemplate) IsDeliveryFeeFree(req *pb.CalculateDeliveryFeeRequest, distance uint64) bool {
	var (
		calRule    DeliveryFeeTemplateFreeRule
		valid      bool
		amountType string
	)

	if self.IsStoreDeliveryFeeMode() {
		if !req.StoreDeliveryFeeTemplate.ConditionFree {
			return false
		}
		copier.Instance(nil).From(req.StoreDeliveryFeeTemplate.FreeRule).CopyTo(&calRule)
		amountType = req.StoreDeliveryFeeTemplate.AmountType
	} else {
		if !self.ConditionFree {
			return false
		}
		// 快递配送，含包邮商品时，整单包邮
		if self.Method == TEMPLATE_METHOD_EXPRESS && self.OrderFreeWithFreeProduct && req.HasFreeProduct {
			return true
		}
		for _, freeRule := range self.FreeRules {
			if util.ValidateAddress(req.Address, freeRule.Areas) {
				calRule = freeRule
				valid = true
				break
			}
		}
		if !valid {
			return false
		}
		amountType = self.AmountType
		// 勾选指定条件包邮，不勾选按订单计算包邮
		if amountType == "" {
			return false
		}
	}

	switch calRule.Type {
	case CONDITION_FREE_TYPE_AMOUNT:
		if amountType == CONDITION_FREE_RULE_PAY_AMOUNT {
			return req.PayAmount >= calRule.Amount
		} else if req.Price >= calRule.Amount {
			return true
		}
	case CONDITION_FREE_TYPE_WEIGHT:
		if calRule.Weight == 0 || req.Weight <= calRule.Weight {
			return true
		}
	case CONDITION_FREE_TYPE_PIECE:
		if req.Pieces >= calRule.Pieces {
			return true
		}
	case CONDITION_FREE_TYPE_DISTANCE:
		if distance < calRule.Distance {
			return true
		}
	case CONDITION_FREE_TYPE_AMOUNT_AND_WEIGHT:
		if amountType == CONDITION_FREE_RULE_PAY_AMOUNT {
			req.Price = req.PayAmount
		}
		if req.Price >= calRule.Amount && (calRule.Weight == 0 || req.Weight <= calRule.Weight) {
			return true
		}
	case CONDITION_FREE_TYPE_AMOUNT_AND_PIECE:
		if amountType == CONDITION_FREE_RULE_PAY_AMOUNT {
			req.Price = req.PayAmount
		}
		if req.Price >= calRule.Amount && req.Pieces >= calRule.Pieces {
			return true
		}
	case CONDITION_FREE_TYPE_AMOUNT_AND_DISTANCE:
		if amountType == CONDITION_FREE_RULE_PAY_AMOUNT {
			req.Price = req.PayAmount
		}
		if req.Price >= calRule.Amount && distance < calRule.Distance {
			return true
		}
	}

	return false
}

// 是否是门店独立运费模式
func (self *DeliveryFeeTemplate) IsStoreDeliveryFeeMode() bool {
	return self.DeliveryFeeMode == "store" && self.Method == "cityExpress"
}
