package setting

import (
	"mairpc/core/extension"
	core_util "mairpc/core/util"
	"mairpc/proto/ec/setting"
	"mairpc/service/share/util"
	"time"

	"mairpc/core/extension/bson"

	"golang.org/x/net/context"
)

const (
	C_DELIVERY_SETTING = "ec.deliverySetting"

	DELIVERY_METHOD_PICKUP = "pickup"
	DAY_LIMIT_THREE        = 3
	TIME_LIMIT_FIFTEEN     = 15

	LOGISTICS_FEE_RULE_SOLID     = "solid"
	LOGISTICS_FEE_RULE_CONDITION = "condition"

	DELIVERY_SETTING_OPERATOR_USER  = "user"  // 总部发货
	DELIVERY_SETTING_OPERATOR_STAFF = "staff" // 门店发货

	DEFAULT_DELIVERY_CITYEXPRESS = "cityExpress"
	DEFAULT_DELIVERY_EXPRESS     = "express"
	DEFAULT_DELIVERY_PICKUP      = "pickup"

	LOGISTICS_SHIP_SETTING_TYPE_ALL                      = "all"
	LOGISTICS_SHIP_SETTING_TYPE_PARTY_AREAS_NON_SHIPPING = "partyAreasNonShipping"

	STORE_TYPE_ALL      = "all"
	STORE_TYPE_PHYSICAL = "physical"
	STORE_TYPE_VIRTUAL  = "virtual"
)

var (
	CDeliverySetting = &DeliverySetting{}

	DEFAULT_DELIVERY = []string{
		DEFAULT_DELIVERY_CITYEXPRESS,
		DEFAULT_DELIVERY_EXPRESS,
		DEFAULT_DELIVERY_PICKUP,
		"", // 未设置
	}
)

type DeliverySetting struct {
	Id              bson.ObjectId   `bson:"_id,omitempty"`
	AccountId       bson.ObjectId   `bson:"accountId"`
	Reservation     Reservation     `bson:"reservation"`
	Logistics       LogisticsDetail `bson:"logistics"`
	CityExpress     CityExpress     `bson:"cityExpress,omitempty"`
	CreatedBy       bson.ObjectId   `bson:"createdBy,omitempty"`
	CreatedAt       time.Time       `bson:"createdAt"`
	UpdatedBy       bson.ObjectId   `bson:"updatedBy,omitempty"`
	UpdatedAt       time.Time       `bson:"updatedAt"`
	DefaultDelivery string          `bson:"defaultDelivery"` // 默认配送方式
	IsDeleted       bool            `bson:"isDeleted"`
}

// 同城快送
type CityExpress struct {
	Enabled                     bool          `bson:"enabled"`
	Name                        string        `bson:"name,omitempty"`
	Fee                         uint64        `bson:"fee"`
	DeliveryDays                uint64        `bson:"deliveryDays"`
	DeliveryDayInterval         uint64        `bson:"deliveryDayInterval"`
	EnableManageDeliveryRule    bool          `bson:"enableManageDeliveryRule"`
	LimitDeliveryDistance       bool          `bson:"limitDeliveryDistance"`
	DistanceRange               DistanceRange `bson:"distanceRange"`
	EnableManageDeliveryFeeRule bool          `bson:"enableManageDeliveryFeeRule"`
	IsThirdDeliveryEnable       bool          `bson:"isThirdDeliveryEnable,omitempty"`
}

type DistanceRange struct {
	Begin uint32 `bson:"begin"`
	End   uint32 `bson:"end"`
}

type Reservation struct {
	Enabled                 bool                `bson:"enabled"`
	EnabledDayLimit         bool                `bson:"enabledDayLimit"`
	DayLimit                int32               `bson:"dayLimit,omitempty"`
	TimeLimit               int32               `bson:"timeLimit,omitempty"`
	IsPickupPasswordEnabled bool                `bson:"isPickupPasswordEnabled,omitempty"`
	FileUrl                 string              `bson:"fileUrl,omitempty"`
	FileName                string              `bson:"fileName,omitempty"`
	StoreType               string              `bson:"storeType,omitempty"`
	NotificationSetting     NotificationSetting `bson:"notificationSetting,omitempty"`
}

type NotificationSetting struct {
	Enabled bool  `bson:"enabled,omitempty"`
	Hour    int64 `bson:"hour,omitempty"`
	Minute  int64 `bson:"minute,omitempty"`
}

type LogisticsDetail struct {
	Enabled     bool                 `bson:"enabled"`
	ShipSetting LogisticsShipSetting `bson:"shipSetting,omitempty"`
	FeeSetting  LogisticsFeeSetting  `bson:"feeSetting,omitempty"`
	StoreType   string               `bson:"storeType,omitempty"`
}

type LogisticsShipSetting struct {
	Operator         string         `bson:"operator"`
	OnlyAddedStore   bool           `bson:"onlyAddedStore"`
	SenderAddress    ContactAddress `bson:"senderAddress,omitempty"`
	ReturnAddress    ContactAddress `bson:"returnAddress,omitempty"`
	Type             string         `bson:"type"`
	NonShippingAreas []string       `bson:"nonShippingAreas"`
}

type ContactAddress struct {
	Name    string  `bson:"name"`
	Tel     string  `bson:"tel"`
	Address Address `bson:"address"`
}

type Address struct {
	Province string `bson:"province"`
	City     string `bson:"city"`
	District string `bson:"district"`
	Detail   string `bson:"detail"`
}

type LogisticsFeeSetting struct {
	FeeRule   string `bson:"feeRule"`
	Fee       uint64 `bson:"fee"`
	Threshold uint64 `bson:"threshold"`
}

func (self *DeliverySetting) Get(ctx context.Context) (*DeliverySetting, error) {
	deliverySetting := new(DeliverySetting)
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}

	extension.DBRepository.FindOne(ctx, C_DELIVERY_SETTING, condition, deliverySetting)
	if !deliverySetting.Id.Valid() {
		deliverySetting, err := deliverySetting.createDefault(ctx)
		if err != nil {
			return nil, err
		}
		return deliverySetting, nil
	}

	return deliverySetting, nil
}

func (self *DeliverySetting) createDefault(ctx context.Context) (*DeliverySetting, error) {
	self.Id = bson.NewObjectId()
	self.AccountId = util.GetAccountIdAsObjectId(ctx)
	self.Reservation = Reservation{
		Enabled:         true,
		EnabledDayLimit: true,
		DayLimit:        DAY_LIMIT_THREE,
		TimeLimit:       TIME_LIMIT_FIFTEEN,
	}

	self.CreatedAt = time.Now()
	self.UpdatedAt = time.Now()
	operatorId := core_util.GetUserId(ctx)
	if bson.IsObjectIdHex(operatorId) {
		self.CreatedBy = bson.ObjectIdHex(operatorId)
		self.UpdatedBy = self.CreatedBy
	}
	self.CityExpress.DeliveryDayInterval = 60
	self.CityExpress.DeliveryDays = 3
	self.Logistics = LogisticsDetail{
		ShipSetting: LogisticsShipSetting{
			Type: LOGISTICS_SHIP_SETTING_TYPE_ALL,
		},
	}

	_, err := extension.DBRepository.Insert(ctx, C_DELIVERY_SETTING, self)
	if err != nil {
		return nil, err
	}

	return self, nil
}

func (*DeliverySetting) Update(ctx context.Context, req *setting.UpdateDeliverySettingRequest) error {
	seletor := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	setter := bson.M{
		"updatedAt": time.Now(),
	}
	if req.Reservation != nil {
		if req.Reservation.Enabled != nil {
			setter["reservation.enabled"] = req.Reservation.Enabled.Value
		}
		if req.Reservation.EnabledDayLimit != nil {
			setter["reservation.enabledDayLimit"] = req.Reservation.EnabledDayLimit.Value
		}
		if req.Reservation.DayLimit != 0 {
			setter["reservation.dayLimit"] = req.Reservation.DayLimit
		}
		if req.Reservation.TimeLimit > 0 && req.Reservation.TimeLimit <= 999 {
			setter["reservation.timeLimit"] = req.Reservation.TimeLimit
		}
		if req.Reservation.IsPickupPasswordEnabled != nil {
			setter["reservation.isPickupPasswordEnabled"] = req.Reservation.IsPickupPasswordEnabled.Value
		}
		if req.Reservation.FileUrl != "" {
			setter["reservation.fileUrl"] = req.Reservation.FileUrl
		}
		if req.Reservation.FileName != "" {
			setter["reservation.fileName"] = req.Reservation.FileName
		}
		if req.Reservation.StoreType != "" {
			setter["reservation.storeType"] = req.Reservation.StoreType
		}
		if req.Reservation.NotificationSetting != nil {
			setter["reservation.notificationSetting"] = req.Reservation.NotificationSetting
		}
	}

	if req.CityExpress != nil {
		if req.CityExpress.Name != "" {
			setter["cityExpress.name"] = req.CityExpress.Name
		}
		if req.CityExpress.Fee != nil {
			setter["cityExpress.fee"] = req.CityExpress.Fee.Value
		}
		if req.CityExpress.Enabled != nil {
			setter["cityExpress.enabled"] = req.CityExpress.Enabled.Value
		}
		if req.CityExpress.DeliveryDays != 0 {
			setter["cityExpress.deliveryDays"] = req.CityExpress.DeliveryDays
		}
		if req.CityExpress.DeliveryDayInterval != 0 {
			setter["cityExpress.deliveryDayInterval"] = req.CityExpress.DeliveryDayInterval
		}
		if req.CityExpress.EnableManageDeliveryRule != nil {
			setter["cityExpress.enableManageDeliveryRule"] = req.CityExpress.EnableManageDeliveryRule.Value
		}
		if req.CityExpress.EnableManageDeliveryFeeRule != nil {
			setter["cityExpress.enableManageDeliveryFeeRule"] = req.CityExpress.EnableManageDeliveryFeeRule.Value
		}
		if req.CityExpress.DistanceRange != nil {
			setter["cityExpress.distanceRange.begin"] = req.CityExpress.DistanceRange.Begin
			setter["cityExpress.distanceRange.end"] = req.CityExpress.DistanceRange.End
		}
		if req.CityExpress.LimitDeliveryDistance != nil {
			setter["cityExpress.limitDeliveryDistance"] = req.CityExpress.LimitDeliveryDistance.Value
		}
	}

	if req.Logistics != nil {
		if req.Logistics.Enabled != nil {
			setter["logistics.enabled"] = req.Logistics.Enabled.Value
		}
		if req.Logistics.FeeSetting != nil {
			setter["logistics.feeSetting"] = bson.M{
				"feeRule":   req.Logistics.FeeSetting.FeeRule,
				"fee":       req.Logistics.FeeSetting.Fee,
				"threshold": req.Logistics.FeeSetting.Threshold,
			}
		}
		if req.Logistics.ShipSetting != nil {
			if req.Logistics.ShipSetting.Operator != "" {
				setter["logistics.shipSetting.operator"] = req.Logistics.ShipSetting.Operator
			}
			if req.Logistics.ShipSetting.ReturnAddress != nil {
				setter["logistics.shipSetting.returnAddress"] = req.Logistics.ShipSetting.ReturnAddress
			}
			if req.Logistics.ShipSetting.SenderAddress != nil {
				setter["logistics.shipSetting.senderAddress"] = req.Logistics.ShipSetting.SenderAddress
			}

			setter["logistics.shipSetting.onlyAddedStore"] = req.Logistics.ShipSetting.OnlyAddedStore

			if req.Logistics.ShipSetting.Type != "" {
				setter["logistics.shipSetting.type"] = req.Logistics.ShipSetting.Type
				setter["logistics.shipSetting.nonShippingAreas"] = req.Logistics.ShipSetting.NonShippingAreas
			}
		}
		if req.Logistics.StoreType != "" {
			setter["logistics.storeType"] = req.Logistics.StoreType
		}
	}

	if req.DefaultDelivery != nil && util.StrInArray(req.DefaultDelivery.Value, &DEFAULT_DELIVERY) {
		setter["defaultDelivery"] = req.DefaultDelivery.Value
	}

	operatorId := core_util.GetUserId(ctx)
	if bson.IsObjectIdHex(operatorId) {
		setter["updatedBy"] = bson.ObjectIdHex(operatorId)
	}

	updater := bson.M{
		"$set": setter,
	}

	return extension.DBRepository.UpdateOne(ctx, C_DELIVERY_SETTING, seletor, updater)
}
