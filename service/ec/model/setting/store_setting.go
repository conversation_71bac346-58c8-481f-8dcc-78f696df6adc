package setting

import (
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/service/share/util"
	"time"

	"golang.org/x/net/context"
)

const (
	C_STORE_SETTING          = "ec.storeSetting"
	ENTRY_RULE_BY_LOCATION   = "byLocation"
	ENTRY_RULE_DEFAULT_STORE = "defaultStore"
)

var (
	CStoreSetting = &StoreSetting{}
)

type StoreSetting struct {
	Id            bson.ObjectId `bson:"_id,omitempty"`
	AccountId     bson.ObjectId `bson:"accountId"`
	EntryRule     EntryRule     `bson:"entryRule"`
	RecommendRule RecommendRule `bson:"recommendRule"`
	CreatedAt     time.Time     `bson:"createdAt"`
	UpdatedAt     time.Time     `bson:"updatedAt"`
	IsDeleted     bool          `bson:"isDeleted"`
}

type RecommendRule struct {
	IsEnabled bool  `bson:"isEnabled"`
	Distance  int64 `bson:"distance"`
}

type EntryRule struct {
	AllowChange               bool         `bson:"allowChange"`               // 携带门店 id 进入商城时是否允许切换门店
	EntryMethod               string       `bson:"entryMethod"`               // 进店方式，可取值 byLocation（通过距离选择门店），defaultStore（通过设置的默认门店）
	DefaultStore              DefaultStore `bson:"defaultStore,omitempty"`    // 默认门店
	AllowChangeWithoutStoreId bool         `bson:"allowChangeWithoutStoreId"` // 未携带门店 id 进入商城时是否允许切换门店
	IsBound                   bool         `bson:"isBound,omitempty"`         // 通过携带门店参数的二维码或小程序卡片进入商城小程序时，消费者与门店是否建立绑定关系
	IsEntryNewStore           bool         `bson:"isEntryNewStore,omitempty"` // isBound 为 true 时有意义。后续通过携带门店参数的二维码或小程序卡片进入商城小程序时，true 表示进入新的带参门店，且更换绑定门店，false 进入上次绑定门店
}

type DefaultStore struct {
	Id   bson.ObjectId `bson:"_id"`
	Name string        `bson:"name"`
}

func (*StoreSetting) Get(ctx context.Context) (*StoreSetting, error) {
	storeSetting := &StoreSetting{}
	condition := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}

	extension.DBRepository.FindOne(ctx, C_STORE_SETTING, condition, storeSetting)
	if storeSetting.Id.Hex() == "" {
		storeSetting, err := storeSetting.createDefault(ctx)
		if err != nil {
			return nil, err
		}
		return storeSetting, nil
	}

	return storeSetting, nil
}

func (*StoreSetting) createDefault(ctx context.Context) (*StoreSetting, error) {
	storeSetting := &StoreSetting{
		Id:        bson.NewObjectId(),
		AccountId: util.GetAccountIdAsObjectId(ctx),
		EntryRule: EntryRule{
			AllowChange:               true,
			EntryMethod:               ENTRY_RULE_BY_LOCATION,
			AllowChangeWithoutStoreId: true,
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	_, err := extension.DBRepository.Insert(ctx, C_STORE_SETTING, storeSetting)
	if err != nil {
		return nil, err
	}

	return storeSetting, nil
}

func (s *StoreSetting) Update(ctx context.Context) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"entryRule":     s.EntryRule,
			"recommendRule": s.RecommendRule,
			"updatedAt":     time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_STORE_SETTING, selector, updater)
}
