package staff_campaign

import (
	"time"

	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/service/share/model"
	"mairpc/service/share/util"

	"golang.org/x/net/context"
)

const (
	C_GROUP_WELCOME_TEMMPLATE = "ec.groupWelcomeTemplate"
)

var CGroupWelcomeTemplate = &GroupWelcomeTemplate{}

type GroupWelcomeTemplate struct {
	Id         bson.ObjectId   `bson:"_id"`
	Name       string          `bson:"name"`
	WelcomeMsg GroupWelcomeMsg `bson:"welcomeMsg"`
	TemplateId string          `bson:"templateId"`
	AccountId  bson.ObjectId   `bson:"accountId"`
	CreatedAt  time.Time       `bson:"createdAt"`
	UpdatedAt  time.Time       `bson:"updatedAt"`
	IsDeleted  bool            `bson:"isDeleted"`
}

type GroupWelcomeMsg struct {
	Text struct {
		Content string `bson:"content,omitempty"`
	} `bson:"text,omitempty"`
	Image struct {
		Url string `bson:"url,omitempty"`
	} `bson:"image,omitempty"`
	MiniProgram struct {
		Title         string `bson:"title,omitempty"`
		ThumbImageUrl string `bson:"thumbImageUrl,omitempty"`
		AppId         string `bson:"appId,omitempty"`
		PagePath      string `bson:"pagePath,omitempty"`
		Desc          string `bson:"desc,omitempty"`
	} `bson:"miniProgram,omitempty"`
	Link struct {
		Url    string `bson:"url,omitempty"`
		Title  string `bson:"title,omitempty"`
		PicUrl string `bson:"picUrl,omitempty"`
		Desc   string `bson:"desc,omitempty"`
	} `bson:"link,omitempty"`
	Video struct {
		Url string `bson:"url,omitempty"`
	} `bson:"video,omitempty"`
	File struct {
		Url string `bson:"url,omitempty"`
	} `bson:"file,omitempty"`
}

func (w *GroupWelcomeTemplate) Upsert(ctx context.Context) error {
	accountId := util.GetAccountIdAsObjectId(ctx)
	condition := bson.M{
		"accountId": accountId,
		"isDeleted": false,
	}
	if !w.Id.IsZero() {
		condition["_id"] = w.Id
	} else {
		condition["_id"] = bson.NewObjectId()
	}

	updator := bson.M{
		"$set": bson.M{
			"name":       w.Name,
			"welcomeMsg": w.WelcomeMsg,
			"updatedAt":  time.Now(),
			"templateId": w.TemplateId,
		},
		"$setOnInsert": bson.M{
			"createdAt": time.Now(),
		},
	}

	_, err := extension.DBRepository.Upsert(ctx, C_GROUP_WELCOME_TEMMPLATE, condition, updator)
	return err
}

func (*GroupWelcomeTemplate) Delete(ctx context.Context, id bson.ObjectId) error {
	selector := Common.GenDefaultConditionById(ctx, id)
	updator := bson.M{
		"$set": bson.M{
			"isDeleted": true,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_GROUP_WELCOME_TEMMPLATE, selector, updator)
}

func (m *GroupWelcomeTemplate) GetById(ctx context.Context, id string) (*GroupWelcomeTemplate, error) {
	var result = GroupWelcomeTemplate{}
	err := extension.DBRepository.FindOne(ctx, C_GROUP_WELCOME_TEMMPLATE, model.Base.GenDefaultConditionById(ctx, bson.ObjectIdHex(id)), &result)
	return &result, err
}

func (*GroupWelcomeTemplate) FindByPagination(ctx context.Context, page extension.PagingCondition) (int, []GroupWelcomeTemplate, error) {
	var templates []GroupWelcomeTemplate
	total, err := extension.DBRepository.FindByPagination(ctx, C_GROUP_WELCOME_TEMMPLATE, page, &templates)
	return total, templates, err
}
