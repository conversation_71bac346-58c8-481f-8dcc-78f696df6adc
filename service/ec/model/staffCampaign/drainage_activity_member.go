package staff_campaign

import (
	"context"
	"fmt"
	"time"

	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/service/share/util"

	"github.com/spf13/cast"
)

var (
	CDrainageActivityMember = &DrainageActivityMember{}
)

const (
	C_DRAINAGE_ACTIVITY_MEMBER = "ec.drainageActivityMember"

	ACTIVITY_MEMBER_STATUS_CREATED   = "created" // 已创建，未开始。点击分享链接进入时，会创建该状态记录
	ACTIVITY_MEMBER_STATUS_SCANNED   = "scanned" // 已扫码，未开始。扫描二维码进入时，会创建该状态记录
	ACTIVITY_MEMBER_STATUS_RUNNING   = "running"
	ACTIVITY_MEMBER_STATUS_FAILED    = "failed"
	ACTIVITY_MEMBER_STATUS_COMPLETED = "completed"

	ACTIVITY_MEMBER_LINK_SOURCE   = "link"   // 通过好友分享的活动链接进入活动
	ACTIVITY_MEMBER_QRCODE_SOURCE = "qrcode" // 通过导购分享的活动二维码进入活动
)

type DrainageActivityMember struct {
	Id                       bson.ObjectId   `bson:"_id"`
	AccountId                bson.ObjectId   `bson:"accountId"`
	CreatedAt                time.Time       `bson:"createdAt"`
	UpdatedAt                time.Time       `bson:"updatedAt"`
	StartAt                  time.Time       `bson:"startAt,omitempty"` // 开始活动时间
	EndAt                    time.Time       `bson:"endAt,omitempty"`   // 开始结束时间
	IsDeleted                bool            `bson:"isDeleted"`
	DrainageActivityId       bson.ObjectId   `bson:"drainageActivityId"`
	StaffId                  bson.ObjectId   `bson:"staffId,omitempty"`
	MemberId                 bson.ObjectId   `bson:"memberId"`
	Name                     string          `bson:"name"`   // 客户姓名
	Avatar                   string          `bson:"avatar"` // 客户头像
	Status                   string          `bson:"status"`
	ActivityType             string          `bson:"activityType"`
	OpenedLinkMemberIds      []bson.ObjectId `bson:"openedLinkMemberIds,omitempty"`      // 当前周期打开分享链接的客户
	TotalOpenedLinkMemberIds []bson.ObjectId `bson:"totalOpenedLinkMemberIds,omitempty"` // 所有周期打开分享链接的客户
	InvitedMembers           []InvitedMember `bson:"invitedMembers,omitempty"`           // 成功邀请的客户
	TotalInvitedMembers      []InvitedMember `bson:"totalInvitedMembers,omitempty"`      // 所有周期成功邀请的客户
	LimitCount               uint64          `bson:"limitCount"`                         // 完成活动需要的邀请人数
	InvitedCount             uint64          `bson:"invitedCount"`                       // 邀请人数
	TotalInvitedCount        uint64          `bson:"totalInvitedCount"`                  // 各个周期总的邀请数量
	ScannedCount             uint64          `bson:"scannedCount"`                       // 扫码次数
	TotalScannedCount        uint64          `bson:"totalScannedCount"`                  // 所有周期总的扫码次数
	FailedCount              uint64          `bson:"failedCount"`                        // 失败次数
	TotalFailedCount         uint64          `bson:"totalFailedCount"`                   // 所有周期总的失败次数
	CompletedCount           uint64          `bson:"completedCount"`                     // 完成次数
	TotalCompletedCount      uint64          `bson:"totalCompletedCount"`                // 所有周期中的完成次数
	ShareCount               uint64          `bson:"shareCount"`                         // 分享次数
	TotalShareCount          uint64          `bson:"totalShareCount"`                    // 所有周期中总的分享次数
	Histories                []History       `bson:"histories,omitempty"`                // 参与活动历史记录
	IsNewMember              bool            `bson:"isNewMember"`                        // 是否是新客
	IsNoParticipating        bool            `bson:"isNoParticipating,omitempty"`        // 是否未参与
	JoinedAt                 time.Time       `bson:"joinedAt,omitempty"`                 // 最近参与时间
	ChatId                   string          `bson:"chatId,omitempty"`                   // 客户群 chatId
	ContactInfo              ContactInfo     `bson:"contactInfo,omitempty"`              // 实物奖励联系信息
	AddedStaffId             bson.ObjectId   `bson:"addedStaffId,omitempty"`             // 实际添加的导购
}

type History struct {
	Status              string          `bson:"status"`
	InvitedCount        uint64          `bson:"invitedCount"`                  // 邀请人数
	OpenedLinkMemberIds []bson.ObjectId `bson:"openedLinkMemberIds,omitempty"` // 当前周期打开分享链接的客户
	InvitedMembers      []InvitedMember `bson:"invitedMembers,omitempty"`      // 成功邀请的客户
	ScannedCount        uint64          `bson:"scannedCount"`                  // 扫码次数
	FailedCount         uint64          `bson:"failedCount"`                   // 失败次数
	CompletedCount      uint64          `bson:"completedCount"`                // 完成次数
	ShareCount          uint64          `bson:"shareCount"`                    // 分享次数
}

type InvitedMember struct {
	MemberId  bson.ObjectId `bson:"memberId"`
	InvitedAt time.Time     `bson:"invitedAt"`
	ChatId    string        `bson:"chatId,omitempty"`
}

type ContactInfo struct {
	Name    string  `bson:"name,omitempty"`
	Phone   string  `bson:"phone,omitempty"`
	Address Address `bson:"address,omitempty"`
}

type Address struct {
	Province string `bson:"province,omitempty"`
	City     string `bson:"city,omitempty"`
	District string `bson:"district,omitempty"`
	Detail   string `bson:"detail,omitempty"`
}

type InvitedMemberArray []InvitedMember

func (m InvitedMemberArray) Len() int { return len(m) }

func (m InvitedMemberArray) Swap(i, j int) { m[i], m[j] = m[j], m[i] }

func (m InvitedMemberArray) Less(i, j int) bool { return m[i].InvitedAt.Before(m[j].InvitedAt) }

func (*DrainageActivityMember) GetById(ctx context.Context, id bson.ObjectId) (DrainageActivityMember, error) {
	result := DrainageActivityMember{}
	selector := Common.GenDefaultConditionById(ctx, id)
	err := extension.DBRepository.FindOne(ctx, C_DRAINAGE_ACTIVITY_MEMBER, selector, &result)
	return result, err
}

func (d *DrainageActivityMember) Create(ctx context.Context) error {
	d.Id = bson.NewObjectId()
	d.CreatedAt = time.Now()
	d.UpdatedAt = d.CreatedAt
	d.IsDeleted = false
	_, err := extension.DBRepository.Insert(ctx, C_DRAINAGE_ACTIVITY_MEMBER, *d)
	if err != nil {
		return err
	}
	return nil
}

func (*DrainageActivityMember) GetByCondition(ctx context.Context, condition bson.M) (DrainageActivityMember, error) {
	result := DrainageActivityMember{}
	err := extension.DBRepository.FindOne(ctx, C_DRAINAGE_ACTIVITY_MEMBER, condition, &result)
	return result, err
}

func (d *DrainageActivityMember) Update(ctx context.Context) error {
	selector := Common.GenDefaultConditionById(ctx, d.Id)
	setter := bson.M{
		"startAt":                  d.StartAt,
		"endAt":                    d.EndAt,
		"status":                   d.Status,
		"openedLinkMemberIds":      d.OpenedLinkMemberIds,
		"totalOpenedLinkMemberIds": d.TotalOpenedLinkMemberIds,
		"invitedMembers":           d.InvitedMembers,
		"totalInvitedMembers":      d.TotalInvitedMembers,
		"invitedCount":             d.InvitedCount,
		"totalInvitedCount":        d.TotalInvitedCount,
		"scannedCount":             d.ScannedCount,
		"totalScannedCount":        d.TotalScannedCount,
		"failedCount":              d.FailedCount,
		"totalFailedCount":         d.TotalFailedCount,
		"completedCount":           d.CompletedCount,
		"totalCompletedCount":      d.TotalCompletedCount,
		"shareCount":               d.ShareCount,
		"totalShareCount":          d.TotalShareCount,
		"histories":                d.Histories,
		"updatedAt":                time.Now(),
		"contactInfo":              d.ContactInfo,
		"isNoParticipating":        d.IsNoParticipating,
	}

	if !d.JoinedAt.IsZero() {
		setter["joinedAt"] = d.JoinedAt
	}
	if d.ChatId != "" {
		setter["chatId"] = d.ChatId
	}
	if d.StaffId != "" {
		setter["staffId"] = d.StaffId
	}
	if d.AddedStaffId.Valid() {
		setter["addedStaffId"] = d.AddedStaffId
	}
	updater := bson.M{
		"$set": setter,
	}
	return Common.UpdateOne(ctx, C_DRAINAGE_ACTIVITY_MEMBER, "false", selector, updater)
}

// 重新参加活动时，重置相关字段
func (d *DrainageActivityMember) Reset(ctx context.Context, activity DrainageActivity) error {
	d.SetHistories()
	d.SetFieldsWithActivity(ctx, activity)
	d.Status = ACTIVITY_MEMBER_STATUS_RUNNING
	d.OpenedLinkMemberIds = []bson.ObjectId{}
	d.InvitedMembers = []InvitedMember{}
	d.InvitedCount = 0
	d.ScannedCount = 0
	d.FailedCount = 0
	d.CompletedCount = 0
	d.ShareCount = 0
	return d.Update(ctx)
}

// 将本次参与活动的数据保存到 history 中
func (d *DrainageActivityMember) SetHistories() {
	history := History{}
	copier.Instance(nil).From(d).CopyTo(&history)
	d.Histories = append(d.Histories, history)
}

func (d *DrainageActivityMember) SetFieldsWithActivity(ctx context.Context, activity DrainageActivity) {
	if !activity.Id.Valid() {
		return
	}
	d.LimitCount = activity.CompletedLimitCount
	d.StartAt = time.Now()
	if activity.CompletedLimitHours > 0 {
		d.EndAt = d.StartAt.Add(time.Hour * time.Duration(activity.CompletedLimitHours))
	}
}

func (DrainageActivityMember) Aggregate(oldCtx context.Context, pipeline []bson.M) ([]bson.M, error) {
	ctx := core_util.CtxWithReadSecondaryPreferred(oldCtx)
	result := []bson.M{}
	err := extension.DBRepository.Aggregate(ctx, C_DRAINAGE_ACTIVITY_MEMBER, pipeline, false, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (d *DrainageActivityMember) AddInvitedMemberId(ctx context.Context, memberId bson.ObjectId, chatId string) error {
	if d.Status != ACTIVITY_MEMBER_STATUS_RUNNING {
		return nil
	}
	CDrainageActivity.Invite(ctx, d.DrainageActivityId, 1)
	// 统计记录总获客数加 1
	stats := DrainageActivityMemberDailyStats{
		DrainageActivityId: d.DrainageActivityId,
		StaffId:            d.StaffId,
		MemberId:           d.MemberId,
		StartOfDay:         util.GetStartTimeOfDay(time.Now()),
		InvitedCount:       1,
	}
	stats.Upsert(ctx)
	d.InvitedMembers = append(d.InvitedMembers, InvitedMember{
		MemberId:  memberId,
		InvitedAt: time.Now(),
		ChatId:    chatId,
	})
	d.TotalInvitedMembers = append(d.TotalInvitedMembers, InvitedMember{
		MemberId:  memberId,
		InvitedAt: time.Now(),
		ChatId:    chatId,
	})
	d.TotalInvitedCount += 1
	d.InvitedCount += 1
	return d.Update(ctx)
}

// 助力用户在邀请者未完成任务前删除了导购或者退了群，要从邀请列表中删除用户
func (d *DrainageActivityMember) RemoveInvitedMemberId(ctx context.Context, memberId bson.ObjectId) error {
	if d.Status != ACTIVITY_MEMBER_STATUS_RUNNING {
		return nil
	}
	// 活动邀请数量减 1
	CDrainageActivity.Invite(ctx, d.DrainageActivityId, -1)
	// 统计记录总获客数减 1
	stats := DrainageActivityMemberDailyStats{
		DrainageActivityId: d.DrainageActivityId,
		StaffId:            d.StaffId,
		MemberId:           d.MemberId,
		StartOfDay:         util.GetStartTimeOfDay(time.Now()),
		InvitedCount:       1,
	}
	stats.IncField(ctx, bson.M{"invitedCount": -1})
	// activityMember invitedMember 和 totalInvitedMembers 字段移除对应的客户
	newInvitedMembers := []InvitedMember{}
	for _, invitedMember := range d.InvitedMembers {
		if invitedMember.MemberId.Hex() == memberId.Hex() {
			continue
		}
		newInvitedMembers = append(newInvitedMembers, invitedMember)
	}
	newTotalInvitedMembers := []InvitedMember{}
	for _, invitedMember := range d.TotalInvitedMembers {
		if invitedMember.MemberId.Hex() == memberId.Hex() {
			continue
		}
		newTotalInvitedMembers = append(newTotalInvitedMembers, invitedMember)
	}
	d.InvitedMembers = newInvitedMembers
	d.TotalInvitedMembers = newTotalInvitedMembers
	d.TotalInvitedCount -= 1
	d.InvitedCount -= 1
	return d.Update(ctx)
}

func (d *DrainageActivityMember) AddOpenedLinkMemberId(ctx context.Context, memberId bson.ObjectId) error {
	d.OpenedLinkMemberIds = util.ToMongoIds(util.StrArrayUnique(util.MongoIdsToStrs(append(d.OpenedLinkMemberIds, memberId))))
	d.TotalOpenedLinkMemberIds = util.ToMongoIds(util.StrArrayUnique(util.MongoIdsToStrs(append(d.TotalOpenedLinkMemberIds, memberId))))
	return d.Update(ctx)
}

func (d *DrainageActivityMember) SetActivityTime(drainageActivity DrainageActivity) {
	d.StartAt = time.Now()
	d.EndAt = drainageActivity.EndTime
	if drainageActivity.CompletedLimitHours > 0 {
		hour, _ := time.ParseDuration(fmt.Sprintf("+%sh", cast.ToString(drainageActivity.CompletedLimitHours)))
		endAt := d.StartAt.Add(hour)
		if drainageActivity.EndTime.After(endAt) {
			d.EndAt = endAt
		}
	}
}

func (d *DrainageActivityMember) UpdateGroupchatId(ctx context.Context, memberId, drainageActivityId bson.ObjectId) error {
	selector := bson.M{
		"accountId":               util.GetAccountIdAsObjectId(ctx),
		"isDeleted":               false,
		"drainageActivityId":      drainageActivityId,
		"invitedMembers.memberId": memberId,
	}
	drainageActivityMember, err := CDrainageActivityMember.GetByCondition(ctx, selector)
	if err != nil {
		return err
	}
	chatId := ""
	for _, invitedMember := range drainageActivityMember.InvitedMembers {
		if invitedMember.MemberId == memberId {
			chatId = invitedMember.ChatId
			break
		}
	}
	condition := bson.M{
		"accountId":          util.GetAccountIdAsObjectId(ctx),
		"isDeleted":          false,
		"memberId":           memberId,
		"drainageActivityId": drainageActivityId,
	}

	updater := bson.M{
		"$set": bson.M{
			"chatId":    chatId,
			"updatedAt": time.Now(),
		},
	}

	return Common.UpdateOne(ctx, C_DRAINAGE_ACTIVITY_MEMBER, "false", condition, updater)
}

func (d *DrainageActivityMember) UpdateIsNewMember(ctx context.Context, memberId, drainageActivityId bson.ObjectId) error {
	condition := bson.M{
		"accountId":          util.GetAccountIdAsObjectId(ctx),
		"isDeleted":          false,
		"memberId":           memberId,
		"drainageActivityId": drainageActivityId,
	}
	updater := bson.M{
		"$set": bson.M{
			"isNewMember": true,
			"updatedAt":   time.Now(),
		},
	}
	return Common.UpdateOne(ctx, C_DRAINAGE_ACTIVITY_MEMBER, "false", condition, updater)
}

// memberId 对应的外部联系人是否完成了引流活动
func (d *DrainageActivityMember) IsCompleted(memberId string, limitCount int) bool {
	if d.MemberId.Hex() == memberId && len(d.InvitedMembers) == limitCount {
		return true
	}
	return false
}

func (*DrainageActivityMember) UpdateStaffId(ctx context.Context, memberId, staffId, addedStaffId, drainageActivityId bson.ObjectId) error {
	condition := bson.M{
		"accountId":          util.GetAccountIdAsObjectId(ctx),
		"isDeleted":          false,
		"memberId":           memberId,
		"drainageActivityId": drainageActivityId,
	}
	setter := bson.M{
		"updatedAt": time.Now(),
	}
	if staffId.Valid() {
		setter["staffId"] = staffId
	}
	if addedStaffId.Valid() {
		setter["addedStaffId"] = addedStaffId
	}
	updater := bson.M{
		"$set": setter,
	}
	return Common.UpdateOne(ctx, C_DRAINAGE_ACTIVITY_MEMBER, "false", condition, updater)
}

func (*DrainageActivityMember) GetAllByCondition(ctx context.Context, condition bson.M) ([]DrainageActivityMember, error) {
	result := []DrainageActivityMember{}
	err := extension.DBRepository.FindAll(ctx, C_DRAINAGE_ACTIVITY_MEMBER, condition, nil, 0, &result)
	return result, err
}
