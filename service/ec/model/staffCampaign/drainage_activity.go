package staff_campaign

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"sync"
	"time"

	"mairpc/core/errors"

	"mairpc/core/extension"
	"mairpc/core/extension/bson"

	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"
	"mairpc/proto/common/origin"
	"mairpc/proto/common/request"
	"mairpc/proto/coupon"
	pb_staff "mairpc/proto/ec/staff"
	pb_store "mairpc/proto/ec/store"
	ec_model "mairpc/service/ec/model"
	staff_model "mairpc/service/ec/model/staff"
	task_model "mairpc/service/ec/model/staffTask"
	store_model "mairpc/service/ec/model/store"
	"mairpc/service/ec/service"
	ec_share "mairpc/service/ec/share"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	"mairpc/service/share/util"

	"github.com/panjf2000/ants/v2"
	"github.com/qiniu/qmgo"
	conf "github.com/spf13/viper"
)

var (
	CDrainageActivity = &DrainageActivity{}
)

const (
	C_DRAINAGE_ACTIVITY = "ec.drainageActivity"

	// 裂变引流活动类型
	DRAINAGE_ACTIVITY_TYPE_PERSONAL  = "personal"
	DRAINAGE_ACTIVITY_TYPE_GROUPCHAT = "groupchat"

	// 引流活动状态
	DRAINAGE_ACTIVITY_STATUS_CREATED = "created" // 未开启
	DRAINAGE_ACTIVITY_STATUS_RUNNING = "running" // 进行中
	DRAINAGE_ACTIVITY_STATUS_ENDED   = "ended"   // 已结束

	// 引流活动奖励类型
	REWARD_TYPE_SCORE   = "score"   // 奖励积分
	REWARD_TYPE_COUPON  = "coupon"  // 奖励优惠券
	REWARD_TYPE_PRODUCT = "product" // 奖励商品

	REWARD_WAY_INITIATOR                 = "initiator"                 // 仅奖励完成任务用户
	REWARD_WAY_INITIATOR_AND_PARTICIPANT = "initiator_and_participant" // 奖励完成任务用户和助力用户

	NEW_MEMBER_POOL_EXCLUDED             = "pool_excluded"             // 未在客户池客户
	NEW_MEMBER_UNOWNED_MEMBER            = "unowned_member"            // 无主客户
	NEW_MEMBER_NOT_DRAINAGE_STAFF_MEMBER = "not_drainage_staff_member" // 未添加引流导购的客户
	NEW_MEMBER_NOT_IN_GROUP              = "not_in_group"              // 在客户池，没进群的用户
	NEW_MEMBER_NOT_IN_ACTIVITY_GROUP     = "not_in_activity_group"     // 在客户池，没进活动群的用户

	// 添加导购的方式
	ADD_STAFF_WAY_DRAINAGE = "drainage" // 添加引流导购
	ADD_STAFF_WAY_RANDOM   = "random"   // 添加随机导购

	MESSAGE_TYPE_MINIPROGRAM_NOTICE     = "MINIPROGRAM_NOTICE"
	END_MESSAGE_TITLE                   = "活动结束提醒"
	END_MESSAGE_CONTENT                 = "%s将于%s结束"
	MESSAGE_PAGE_PATH                   = "pages/drainage-campaign/detail/index?id=%s" // 任务详情路径
	BIOSTIME_MESSAGE_PAGE_PATH          = "wechatwork/sales/pages/drainage-campaign/detail/index?id=%s"
	STANDARD_BIOSTIME_MESSAGE_PAGE_PATH = "standard-wechatwork/sales/pages/drainage-campaign/detail/index?id=%s"

	MEMBER_ACTIVITY_PAGE         = "%s/modules/wechatwork/index.html#/invite?accountId=%s&id=%s&source=link"
	HHGROUP_MEMBER_ACTIVITY_PAGE = "%s/modules/hhgroup/index.html#/invite?accountId=%s&id=%s&source=link"

	BIOSTIME_ENV_NAME = "biostime" // 健合私部环境

	START_MESSAGE_TITLE = "活动开始提醒"
)

type DrainageActivity struct {
	Id                         bson.ObjectId              `bson:"_id"`
	AccountId                  bson.ObjectId              `bson:"accountId"`
	CreatedAt                  time.Time                  `bson:"createdAt"`
	UpdatedAt                  time.Time                  `bson:"updatedAt"`
	CreatedBy                  bson.ObjectId              `bson:"createdBy,omitempty"`
	IsDeleted                  bool                       `bson:"isDeleted"`
	Name                       string                     `bson:"name"`
	Description                string                     `bson:"description"`
	Status                     string                     `bson:"status"`
	Type                       string                     `bson:"type"`
	ApplicableStore            task_model.ApplicableStore `bson:"applicableStore,omitempty"`
	ApplicableStaff            ApplicableScope            `bson:"applicableStaff,omitempty"`
	GroupchatDrainageQrcodeIds []bson.ObjectId            `bson:"groupchatDrainageQrcodeIds"`
	StartTime                  time.Time                  `bson:"startTime"`
	EndTime                    time.Time                  `bson:"endTime"`
	NewMemberCondition         []string                   `bson:"newMemberCondition"`
	CompletedLimitCount        uint64                     `bson:"completedLimitCount"`
	CompletedLimitHours        uint64                     `bson:"completedLimitHours,omitempty"`
	Limit                      DrainageActivityLimit      `bson:"limit"` // 奖励上限
	RemindHours                uint64                     `bson:"remindHours,omitempty"`
	HasSentRemind              bool                       `bson:"hasSentRemind,omitempty"`
	RewardSetting              RewardSetting              `bson:"rewardSetting"`
	ShareSetting               ShareSetting               `bson:"shareSetting"`
	InvitationPageSetting      InvitationPageSetting      `bson:"invitationPageSetting"`
	PageSetting                PageSetting                `bson:"pageSetting"`
	IsSent                     bool                       `bson:"isSent"`                 // 任务是否已下发
	InvitedCount               uint64                     `bson:"invitedCount,omitempty"` // 获客数
	VisitorCount               uint64                     `bson:"visitorCount,omitempty"` // 浏览量
	ChannelInfo                ChannelInfo                `bson:"channelInfo,omitempty"`  // 推广渠道
	IsBlocked                  bool                       `bson:"isBlocked"`              // 活动被封
	AddStaffWay                string                     `bson:"addStaffWay"`
}

type DrainageActivityLimit struct {
	TotalLimit int `bson:"totalLimit,omitempty"` // 总完成数限制，无限制则无值
	DayLimit   int `bson:"dayLimit,omitempty"`   // 单日完成数限制，无限制则无值
}

type RewardSetting struct {
	Way                        string          `bson:"way"`
	InitiatorRewardCouponIds   []bson.ObjectId `bson:"initiatorRewardCouponIds,omitempty"`
	ParticipantRewardCouponIds []bson.ObjectId `bson:"participantRewardCouponIds,omitempty"`
	InitiatorReward            AcitvityReward  `bson:"initiatorReward,omitempty"`
	ParticipantReward          AcitvityReward  `bson:"participantReward,omitempty"`
}

type AcitvityReward struct {
	Type      string          `bson:"type,omitempty"`
	CouponIds []bson.ObjectId `bson:"couponIds,omitempty"`
	Score     uint64          `bson:"score,omitempty"`
	Products  []Product       `bson:"products,omitempty"`
}

type Product struct {
	Name       string   `bson:"name,omitempty"`
	Images     []string `bson:"images,omitempty"`
	Stock      int      `bson:"stock"`      // 奖品剩余库存
	SentStock  int      `bson:"sentStock"`  // 奖品已发放库存
	TotalStock int      `bson:"totalStock"` // 奖品总库存，总库存为零即库存开关未开启
}

type ShareSetting struct {
	Content       string   `bson:"content"`
	PosterStyles  []string `bson:"posterStyles"`
	Rule          string   `bson:"rule"`
	HelpDeskPhone string   `bson:"helpDeskPhone"`
	Position      Position `bson:"position,omitempty"`
}

type Position struct {
	Left   uint64 `bson:"left,omitempty"`
	Top    uint64 `bson:"top,omitempty"`
	Width  uint64 `bson:"width,omitempty"`
	Height uint64 `bson:"height,omitempty"`
}

type InvitationPageSetting struct {
	PageStyles      []string   `bson:"pageStyles"`
	LinkTitle       string     `bson:"linkTitle"`
	LinkDescription string     `bson:"linkDescription"`
	LinkImages      []string   `bson:"linkImages"`
	WelcomeMsg      WelcomeMsg `bson:"welcomeMsg"`
	Position        Position   `bson:"position,omitempty"`
}

type PageSetting struct {
	Style                string               `bson:"style"`
	Images               []string             `bson:"images"`
	CompanyIcon          string               `bson:"companyIcon"`
	LinkTitle            string               `bson:"linkTitle"`
	LinkDescription      string               `bson:"linkDescription"`
	LinkImages           []string             `bson:"linkImages"`
	CustomizePageSetting CustomizePageSetting `bson:"customizePageSetting,omitempty"` // 自定义设置
}

type CustomizePageSetting struct {
	PageShareSetting         PageShareSetting         `bson:"pageShareSetting,omitempty"`         // 页面头图
	CampaignDetailSetting    CampaignDetailSetting    `bson:"campaignDetailSetting,omitempty"`    // 活动详情
	InitiatorResultSetting   InitiatorResultSetting   `bson:"initiatorResultSetting,omitempty"`   // 邀请战绩
	InitiatorRewardSetting   InitiatorRewardSetting   `bson:"initiatorRewardSetting,omitempty"`   // 邀请者领奖
	ParticipantRewardSetting ParticipantRewardSetting `bson:"participantRewardSetting,omitempty"` // 助力者领奖
}

type PageShareSetting struct {
	PageImages          []string            `bson:"pageImages,omitempty"`          // 页面头图
	CompanyIcon         string              `bson:"companyIcon,omitempty"`         // 公司图标
	CampaignRuleSetting CampaignRuleSetting `bson:"CampaignRuleSetting,omitempty"` // 活动规则
}

type CampaignRuleSetting struct {
	ButtonColor string `bson:"buttonColor,omitempty"` // 按钮颜色
	TextColor   string `bson:"textColor,omitempty"`   // 文本颜色
	PopupColor  string `bson:"popupColor,omitempty"`  // 弹窗颜色
}

type CampaignDetailSetting struct {
	CampaignRewardSetting CampaignRewardSetting `bson:"campaignRewardSetting,omitempty"` // 活动奖品
	InviteStepSetting     InviteStepSetting     `bson:"inviteStepSetting,omitempty"`     // 邀请步骤
	InviteProgressSetting InviteProgressSetting `bson:"inviteProgressSetting,omitempty"` // 邀请进度
	InviteButtonSetting   InviteButtonSetting   `bson:"inviteButtonSetting,omitempty"`   // 邀请按钮
	RewardAddressSetting  RewardAddressSetting  `bson:"rewardAddressSetting,omitempty"`  // 查看领奖地址按钮文本颜色
}

type CampaignRewardSetting struct {
	TextColor       string `bson:"textColor,omitempty"`       // 标题文本颜色
	CoverTextColor  string `bson:"coverTextColor,omitempty"`  // 封皮文本颜色
	CoverColor      string `bson:"coverColor,omitempty"`      // 封皮颜色
	BackgroundColor string `bson:"backgroundColor,omitempty"` // 封底颜色
}

type InviteStepSetting struct {
	Title  string   `bson:"title,omitempty"`  // 标题内容
	Color  string   `bson:"color,omitempty"`  // 标题文本颜色
	Images []string `bson:"images,omitempty"` // 步骤图片
}

type InviteProgressSetting struct {
	TextColor            string `bson:"textColor,omitempty"`            // 标题文本颜色
	LeftBackgroundColor  string `bson:"leftBackgroundColor,omitempty"`  // 文本背景左端颜色
	RightBackgroundColor string `bson:"rightBackgroundColor,omitempty"` // 文本背景右端颜色
}

type InviteButtonSetting struct {
	ButtonTextColor  string `bson:"buttonTextColor,omitempty"`  // 按钮文本颜色
	LeftButtonColor  string `bson:"LeftButtonColor,omitempty"`  // 按钮左端颜色
	RightButtonColor string `bson:"RightButtonColor,omitempty"` // 按钮右端颜色
}

type InitiatorResultSetting struct {
	Title             string `bson:"title,omitempty"`             // 标题文本内容
	TitleColor        string `bson:"titleColor,omitempty"`        // 标题文本颜色
	SubfieldIconColor string `bson:"subfieldIconColor,omitempty"` // 分栏图标颜色
	SubfieldTextColor string `bson:"subfieldTextColor,omitempty"` // 分栏文本颜色
}

type InitiatorRewardSetting struct {
	PopupRewardSetting PopupRewardSetting `bson:"popupRewardSetting,omitempty"` // 领奖弹窗
}

type PopupRewardSetting struct {
	LeftButtonColor  string `bson:"LeftButtonColor,omitempty"`  // 按钮左端颜色
	RightButtonColor string `bson:"RightButtonColor,omitempty"` // 按钮右端颜色
}

type RewardAddressSetting struct {
	ButtonTextColor string `bson:"buttonTextColor,omitempty"` // 按钮文本颜色
}

type ParticipantRewardSetting struct {
	PopupRewardSetting PopupRewardSetting `bson:"popupRewardSetting,omitempty"` // 领奖弹窗
}

type ChannelInfo struct {
	ChannelId   string `bson:"channelId,omitempty"`
	ChannelName string `bson:"channelName,omitempty"`
}

func (t *DrainageActivity) Create(ctx context.Context) error {
	t.CreatedAt = time.Now()
	t.UpdatedAt = t.CreatedAt
	t.IsDeleted = false
	_, err := extension.DBRepository.Insert(ctx, C_DRAINAGE_ACTIVITY, *t)
	return err
}

func (*DrainageActivity) GetById(ctx context.Context, id bson.ObjectId) (DrainageActivity, error) {
	result := DrainageActivity{}
	selector := Common.GenDefaultConditionById(ctx, id)
	err := extension.DBRepository.FindOne(ctx, C_DRAINAGE_ACTIVITY, selector, &result)
	return result, err
}

func (d *DrainageActivity) UpdateIsSent(ctx context.Context) error {
	selector := Common.GenDefaultConditionById(ctx, d.Id)
	updater := bson.M{
		"$set": bson.M{
			"isSent":    d.IsSent,
			"updatedAt": time.Now(),
		},
	}
	return Common.UpdateOne(ctx, C_DRAINAGE_ACTIVITY, "false", selector, updater)
}

func (d *DrainageActivity) UpdateHasSentRemind(ctx context.Context) error {
	selector := Common.GenDefaultConditionById(ctx, d.Id)
	updater := bson.M{
		"$set": bson.M{
			"hasSentRemind": d.HasSentRemind,
			"updatedAt":     time.Now(),
		},
	}
	return Common.UpdateOne(ctx, C_DRAINAGE_ACTIVITY, "false", selector, updater)
}

func (d *DrainageActivity) Update(ctx context.Context) error {
	selector := Common.GenDefaultConditionById(ctx, d.Id)
	settor := bson.M{
		"name":                       d.Name,
		"description":                d.Description,
		"applicableStore":            d.ApplicableStore,
		"applicableStaff":            d.ApplicableStaff,
		"groupchatDrainageQrcodeIds": d.GroupchatDrainageQrcodeIds,
		"startTime":                  d.StartTime,
		"endTime":                    d.EndTime,
		"newMemberCondition":         d.NewMemberCondition,
		"completedLimitCount":        d.CompletedLimitCount,
		"completedLimitHours":        d.CompletedLimitHours,
		"limit":                      d.Limit,
		"remindHours":                d.RemindHours,
		"shareSetting":               d.ShareSetting,
		"invitationPageSetting":      d.InvitationPageSetting,
		"pageSetting":                d.PageSetting,
		"status":                     d.Status,
		"channelInfo":                d.ChannelInfo,
		"addStaffWay":                d.AddStaffWay,
	}
	// 活动未开启才允许编辑奖励设置
	if d.Status == DRAINAGE_ACTIVITY_STATUS_CREATED {
		settor["rewardSetting"] = d.RewardSetting
	}
	updater := bson.M{
		"$set": settor,
	}

	return Common.UpdateOne(ctx, C_DRAINAGE_ACTIVITY, "false", selector, updater)
}

func (t *DrainageActivity) GetAllByCondition(ctx context.Context, condition bson.M) ([]DrainageActivity, error) {
	activities := []DrainageActivity{}
	err := extension.DBRepository.FindAll(
		ctx,
		C_DRAINAGE_ACTIVITY,
		condition,
		[]string{"-createdAt"},
		0,
		&activities,
	)
	return activities, err
}

func (t *DrainageActivity) Open(ctx context.Context) error {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       t.Id,
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"status":    DRAINAGE_ACTIVITY_STATUS_RUNNING,
			"updatedAt": time.Now(),
		},
	}
	err := extension.DBRepository.UpdateOne(ctx, C_DRAINAGE_ACTIVITY, selector, updater)
	if err != nil {
		return err
	}
	return CStaffDrainageActivity.UpdateStatus(ctx, t.Id, DRAINAGE_ACTIVITY_STATUS_RUNNING)
}

// 下发活动到符合活动适用范围的导购
func (d *DrainageActivity) Send(ctx context.Context, channelId, appId string) error {
	// 有概率遇到重复下发的情况，还未找到原因，因此在下发时先检查下，避免重复下发
	staffDrainageActivityCount, _ := CStaffDrainageActivity.CountByActivityId(ctx, d.Id)
	if staffDrainageActivityCount > 0 {
		log.Warn(ctx, "Activity has been sent", log.Fields{
			"activityId": d.Id.Hex(),
		})
		return nil
	}
	if d.Type == DRAINAGE_ACTIVITY_TYPE_PERSONAL {
		if d.AddStaffWay == ADD_STAFF_WAY_DRAINAGE {
			d.CreateApplicableStoreSingleDrainageQrcode(ctx, appId, channelId)
			d.CreateApplicableStaffSingleDrainageQrcode(ctx, appId, channelId)
		} else {
			d.CreateApplicableStoreDrainageQrcode(ctx, appId, channelId)
			d.CreateApplicableStaffDrainageQrcode(ctx, appId, channelId)
		}
		return nil
	}
	staffs, err := getStaffsWithApplicableStore(ctx, d.ApplicableStore, "")
	if err != nil {
		log.Warn(ctx, "Get staff withApplicableStore failed", log.Fields{
			"activity":     d,
			"errorMessage": err.Error(),
		})
		return nil
	}

	for _, staff := range staffs {
		d.CreateStaffActivity(ctx, staff.Id, "", "", "")
	}
	d.IsSent = true
	d.UpdateIsSent(ctx)
	return nil
}

func getStaffsWithApplicableStore(ctx context.Context, applicableStore task_model.ApplicableStore, distributorId string) ([]store_model.Staff, error) {
	staffs := []store_model.Staff{}
	storeIds, err := task_model.GetStoreIdsWithApplicableStore(ctx, applicableStore, distributorId)
	if err != nil {
		log.Warn(ctx, "getStaffIdsWithApplicableStore fail", log.Fields{
			"errMessage": err.Error(),
		})
		return staffs, err
	}
	// 没有对应的门店时，不应该给任何导购下发任务
	if applicableStore.Type != task_model.APPLICABLE_STORE_ALL && len(storeIds) == 0 {
		return staffs, nil
	}
	paramsMap := map[string]interface{}{
		"storeIds": storeIds,
	}
	staffs, err = ec_model.ListStaffs(ctx, ec_model.GetListStaffsRequest(paramsMap))
	return staffs, err
}

func (d *DrainageActivity) getContactWayRequest(ctx context.Context, staff store_model.Staff, appId string) component.ContactWayRequest {
	contactWay := component.ContactWayRequest{
		Group:      staff.Id.Hex(),
		SkipVerify: true,
		UserIds:    []string{staff.StaffNo},
		Scene:      SCENE_QRCODE,
		Extra:      "drainage_activity",
		Type:       QRCODE_TYPE_SINGLE,
		WelcomeMsg: d.GetWelcomeMsg(ctx, appId),
	}
	return contactWay
}

func (d *DrainageActivity) getMultiContactWayRequest(ctx context.Context, staffNos []string, storeId, appId string) component.ContactWayRequest {
	contactWay := component.ContactWayRequest{
		Group:      storeId,
		SkipVerify: true,
		UserIds:    staffNos,
		Scene:      SCENE_QRCODE,
		Extra:      "drainage_activity",
		Type:       QRCODE_TYPE_MULTI,
		WelcomeMsg: d.GetWelcomeMsg(ctx, appId),
	}
	return contactWay
}

func (d *DrainageActivity) GetWelcomeMsg(ctx context.Context, appId string) *component.ContactWelcomeMsg {
	pageUrl := MEMBER_ACTIVITY_PAGE
	if strings.Contains(conf.GetString("message-trigger-env"), BIOSTIME_ENV_NAME) {
		pageUrl = HHGROUP_MEMBER_ACTIVITY_PAGE
	}
	redirectUrl := fmt.Sprintf(pageUrl, conf.GetStringMap("extension-request")["domain-h5"], util.GetAccountId(ctx), d.Id.Hex())
	welcomeMsg := component.ContactWelcomeMsg{
		Text: &component.ContactWelcomeMsgText{
			Content: d.InvitationPageSetting.WelcomeMsg.Text.Content,
		},
		Link: &component.ContactWelcomeMsgLink{
			Title:  d.PageSetting.LinkTitle,
			PicUrl: d.PageSetting.LinkImages[0],
			Desc:   d.PageSetting.LinkDescription,
			Url:    GetOauthUrl(ctx, redirectUrl, appId),
		},
	}
	placeholderRule := []component.ContactWelcomeMsgPlaceholderRule{}
	copier.Instance(nil).From(d.InvitationPageSetting.WelcomeMsg.PlaceholderRules).CopyTo(&placeholderRule)
	welcomeMsg.PlaceholderRules = placeholderRule
	return &welcomeMsg
}

// 检查完成次数限制，返回值表示是否可以继续参加活动
func (d *DrainageActivity) CheckLimit(ctx context.Context, memberId, staffId bson.ObjectId) bool {
	// 不限制完成次数
	if d.Limit.TotalLimit == 0 && d.Limit.DayLimit == 0 {
		return true
	}
	condition := bson.M{
		"accountId":          util.GetAccountIdAsObjectId(ctx),
		"drainageActivityId": d.Id,
		"memberId":           memberId,
		"staffId":            staffId,
	}
	// 没有参与记录
	activityMember, err := CDrainageActivityMember.GetByCondition(ctx, condition)
	if err != nil && err == bson.ErrNotFound {
		return true
	}
	// 没有统计数据记录
	condition["startOfDay"] = util.GetStartTimeOfDay(time.Now())
	stats, err := CDrainageActivityMemberDailyStats.GetByCondition(ctx, condition)
	if err != nil && err == bson.ErrNotFound {
		return true
	}
	// 限制完成总次数
	if d.Limit.TotalLimit > 0 && activityMember.TotalCompletedCount >= uint64(d.Limit.TotalLimit) {
		return false
	}
	// 限制单日完成次数
	if d.Limit.DayLimit > 0 && stats.CompletedCount >= uint64(d.Limit.DayLimit) {
		return false
	}
	return true
}

func (d *DrainageActivity) SendReward(ctx context.Context, drainageActivityMember DrainageActivityMember, memberId bson.ObjectId) {
	rewardSetting := d.RewardSetting
	// 仅奖励完成任务用户，助力用户不能发奖励
	if rewardSetting.Way == REWARD_WAY_INITIATOR && drainageActivityMember.MemberId.Hex() != memberId.Hex() {
		return
	}
	// 用户还未完成任务，不能发奖励
	if drainageActivityMember.MemberId.Hex() == memberId.Hex() && len(drainageActivityMember.InvitedMembers) < int(d.CompletedLimitCount) {
		return
	}

	reward := DrainageActivityReward{
		AccountId:          d.AccountId,
		SentAt:             time.Now(),
		MemberId:           memberId,
		StaffId:            drainageActivityMember.StaffId,
		Status:             ACTIVITY_MEMBER_REWARD_STATUS_SENT,
		DrainageActivityId: d.Id,
	}
	var couponIds []string
	newActivity := d
	// 完成奖励
	if drainageActivityMember.MemberId.Hex() == memberId.Hex() {
		reward.Role = REWARD_ROLE_INITIATOR
		reward.Type = d.RewardSetting.InitiatorReward.Type
		reward.Score = d.RewardSetting.InitiatorReward.Score
		reward.Products = d.RewardSetting.InitiatorReward.Products
		couponIds = util.MongoIdsToStrs(d.RewardSetting.InitiatorReward.CouponIds)
	} else {
		// 助力奖励
		reward.Role = REWARD_ROLE_PARTICIPANT
		reward.Type = d.RewardSetting.ParticipantReward.Type
		reward.Score = d.RewardSetting.ParticipantReward.Score
		reward.Products = d.RewardSetting.ParticipantReward.Products
		couponIds = util.MongoIdsToStrs(d.RewardSetting.ParticipantReward.CouponIds)
		reward.InitiatorMemberId = drainageActivityMember.MemberId
	}
	// 实物商品奖品需要减库存发放
	if reward.Type == ACTIVITY_MEMBER_REWARD_TYPE_PRODUCT {
		rewardedRole := "initiatorReward"
		if reward.Role == REWARD_ROLE_PARTICIPANT {
			rewardedRole = "participantReward"
		}
		actualProducts := []Product{}
		for _, p := range reward.Products {
			if p.TotalStock <= 0 { // 库存开关未开启无需减库存
				actualProducts = reward.Products
				break
			}
			new, err := d.ReduceProductStock(ctx, p.Name, rewardedRole, 1)
			if err != nil {
				log.Warn(ctx, "Reward product is understocked or sent out", log.Fields{
					"activityId":   d.Id.Hex(),
					"rewardedRole": rewardedRole,
					"productName":  p.Name,
					"errMsg":       err.Error(),
					"memberId":     memberId,
				})
				continue
			}
			if new != nil {
				newActivity = new
				actualProducts = append(actualProducts, p)
			}
		}
		if len(actualProducts) == 0 {
			return
		}
		reward.Products = actualProducts
	}

	// 防止发送全部优惠券
	if len(couponIds) == 0 && reward.Type == ACTIVITY_MEMBER_REWARD_TYPE_COUPON {
		return
	}

	if reward.Type == ACTIVITY_MEMBER_REWARD_TYPE_COUPON {
		resp, err := ec_share.SearchCoupons(ctx, &coupon.SearchCouponRequest{
			Ids: couponIds,
		})
		if err != nil {
			log.Warn(ctx, "Failed to search coupon", log.Fields{
				"couponIds": couponIds,
				"err":       err,
			})
		}
		rewardCoupons := []Reward{}
		for _, coupon := range resp.Coupons {
			rewardCoupons = append(rewardCoupons, Reward{
				Id:   util.ToMongoId(coupon.Id),
				Name: coupon.Title,
				Type: coupon.Type,
			})
		}
		reward.Rewards = rewardCoupons
	}

	err := reward.Create(ctx)
	defer func() {
		if reward.Type == ACTIVITY_MEMBER_REWARD_TYPE_PRODUCT {
			rewardedRole := "initiatorReward"
			if reward.Role == REWARD_ROLE_PARTICIPANT {
				rewardedRole = "participantReward"
			}
			for _, p := range reward.Products {
				if err != nil && p.TotalStock > 0 { // 发实物商品奖励失败且开启库存设置则需要回退剩余库存
					newActivity.ReturnProductStock(ctx, p.Name, rewardedRole, 1)
					continue
				}
				if err == nil && p.TotalStock == 0 { // 发实物商品奖励成功且未开启库存设置需要增加已发放库存
					d.IncProductSentStock(ctx, p.Name, rewardedRole, 1)
					continue
				}
			}
		}
	}()
	if err != nil {
		return
	}

	// 发放成功之后判断是否要提前结束活动
	if reward.Type == ACTIVITY_MEMBER_REWARD_TYPE_PRODUCT {
		newActivity.CheckProductStockAndClose(ctx)
	}
}

func (d *DrainageActivity) CheckProductStockAndClose(ctx context.Context) {
	if d.Status == DRAINAGE_ACTIVITY_STATUS_ENDED {
		return
	}
	if d.RewardSetting.Way == REWARD_WAY_INITIATOR {
		isAllProductsSentOut := true  // 邀请者实物商品奖励是否发放完
		isProductStockEnabled := true // 邀请者实物商品奖励库存是否开启，任意一个商品有总库存则为开启
		for _, p := range d.RewardSetting.InitiatorReward.Products {
			if p.TotalStock == 0 {
				isProductStockEnabled = false
				isAllProductsSentOut = false
				break
			}
			if p.Stock > 0 {
				isAllProductsSentOut = false
			}
		}
		if isProductStockEnabled && isAllProductsSentOut {
			d.Close(ctx)
		}
		return
	}

	if d.RewardSetting.Way == REWARD_WAY_INITIATOR_AND_PARTICIPANT {
		isInitiatorSentOut := 1      // 邀请者实物商品奖励是否发放完
		isInitiatorStockEnabled := 1 // 邀请者实物商品奖励库存是否开启，任意一个商品有总库存则为开启
		for _, p := range d.RewardSetting.InitiatorReward.Products {
			if p.TotalStock == 0 {
				isInitiatorStockEnabled = 0
				isInitiatorSentOut = 0
				break
			}
			if p.Stock > 0 {
				isInitiatorSentOut = 0
			}
		}
		isParticipantSentOut := 1      // 助力者
		isParticipantStockEnabled := 1 // 助力者
		for _, p := range d.RewardSetting.ParticipantReward.Products {
			if p.TotalStock == 0 {
				isParticipantStockEnabled = 0
				isParticipantSentOut = 0
				break
			}
			if p.Stock > 0 {
				isParticipantSentOut = 0
			}
		}
		// 只开启邀请者或助力者库存，开启库存开关的一方剩余库存为零则结束活动
		if isInitiatorStockEnabled^isParticipantStockEnabled == 1 &&
			isInitiatorSentOut^isParticipantSentOut == 1 {
			d.Close(ctx)
			return
		}
		// 邀请者或助力者库存都开启，邀请者库存为零则结束，不考虑助力者库存是否为零
		if isInitiatorStockEnabled&isParticipantStockEnabled == 1 {
			if isInitiatorSentOut == 1 {
				d.Close(ctx)
				return
			}
		}
	}
}

func (d *DrainageActivity) DeleteById(ctx context.Context) error {
	selector := Common.GenDefaultConditionById(ctx, d.Id)
	updater := bson.M{
		"$set": bson.M{
			"isDeleted": true,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_DRAINAGE_ACTIVITY, selector, updater)
}

func (*DrainageActivity) FindByPagination(ctx context.Context, page extension.PagingCondition) ([]DrainageActivity, int, error) {
	result := []DrainageActivity{}
	total, err := extension.DBRepository.FindByPagination(ctx, C_DRAINAGE_ACTIVITY, page, &result)
	return result, total, err
}

// 创建参与活动记录
func (d *DrainageActivity) CreateDrainageActivityMember(ctx context.Context, staffId, memberId, stauts, source string, isNewMember bool) (DrainageActivityMember, error) {
	activityMember := DrainageActivityMember{
		AccountId:          util.GetAccountIdAsObjectId(ctx),
		DrainageActivityId: d.Id,
		MemberId:           util.ToMongoId(memberId),
		Status:             stauts,
		LimitCount:         d.CompletedLimitCount,
		ActivityType:       d.Type,
		IsNewMember:        isNewMember,
		IsNoParticipating:  isNewMember,
	}
	if staffId != "" {
		activityMember.StaffId = util.ToMongoId(staffId)
	}

	if source == ACTIVITY_MEMBER_QRCODE_SOURCE {
		activityMember.ScannedCount = 1
		activityMember.TotalScannedCount = 1
	}
	member, _ := getMember(ctx, memberId)
	if member != nil {
		activityMember.Name = member.Name
		activityMember.Avatar = member.Avatar
	}
	err := activityMember.Create(ctx)
	if err != nil {
		log.Warn(ctx, "Create activity member failed", log.Fields{
			"errorMessage":   err.Error(),
			"activityMember": activityMember,
		})
	}
	return activityMember, err
}

func (d *DrainageActivity) GetActivityTypeStr() string {
	switch d.Type {
	case DRAINAGE_ACTIVITY_TYPE_PERSONAL:
		return "个人裂变活动"
	case DRAINAGE_ACTIVITY_TYPE_GROUPCHAT:
		return "群裂变活动"
	}
	return ""
}

// 给导购发送通知消息
func (d *DrainageActivity) SendNotificationMessage(ctx context.Context, title string) error {
	channel, err := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err != nil {
		return err
	}
	// 发送小程序通知消息时，需要兼容旧数据，旧数据中 WechatAppId 保存在小程序应用中
	if channel.WechatAppId == "" {
		channel, err = service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WEWORKAPP)
		if err != nil {
			return err
		}
		// 安装了第三方应用，wechatAppId 从配置文件中读取
		if channel.WechatAppId == "" {
			channel.WechatAppId = conf.GetString("component-wework-weappid")
		}
	}

	// appId 为空时不发送通知消息
	if channel.WechatAppId == "" {
		return nil
	}

	staffActivities, err := CStaffDrainageActivity.GetAllByCondition(ctx, bson.M{
		"accountId":          util.GetAccountIdAsObjectId(ctx),
		"drainageActivityId": d.Id,
		"isDeleted":          false,
	})
	if err != nil {
		log.Warn(ctx, "Failed to find staffActivities", log.Fields{
			"errorMessage": err.Error(),
			"accountId":    d.AccountId.Hex(),
			"activityId":   d.Id.Hex(),
		})
		return err
	}

	log.Warn(ctx, "Send notification message", log.Fields{
		"accountId":            d.AccountId.Hex(),
		"title":                title,
		"activityId":           d.Id.Hex(),
		"staffActivitiesCount": len(staffActivities),
	})

	message := d.GetMessage(channel.WechatAppId, title)

	// 使用协程加快发送通知时间
	poolSize := 10
	pool, err := ants.NewPool(poolSize)
	if err != nil {
		panic(err)
	}
	defer pool.Release()
	var wg sync.WaitGroup

	for i := 0; i < len(staffActivities); i += 100 {
		nextLength := i + 100
		if len(staffActivities) < nextLength {
			nextLength = len(staffActivities)
		}
		subStaffActivities := staffActivities[i:nextLength]

		pool.Submit(func() {
			staffCount := 0
			defer wg.Done()
			for _, item := range subStaffActivities {
				staff, err := task_model.GetStaff(ctx, &pb_store.StaffDetailRequest{StaffId: item.StaffId.Hex()})
				if err != nil {
					log.Warn(ctx, "Failed to find staff by id", log.Fields{
						"accountId":       item.AccountId.Hex(),
						"staffActivityId": item.Id.Hex(),
						"activityId":      item.DrainageActivityId.Hex(),
						"staffId":         item.StaffId.Hex(),
					})
					continue
				}

				log.Warn(ctx, "Staff info log", log.Fields{
					"activityId": d.Id.Hex(),
					"staff":      staff,
				})

				// 未激活的导购不发送通知
				if staff.Status != 1 {
					continue
				}
				staffCount++
				d.SendMessage(ctx, channel.ChannelId, staff.StaffNo, message)
			}
			log.Warn(ctx, "Sub staffActivities send message complated", log.Fields{
				"activityId": d.Id.Hex(),
				"staffCount": staffCount,
			})
		})
		wg.Add(1)
	}
	wg.Wait()
	return nil
}

func (d *DrainageActivity) GetMessage(wechatAppId, title string) component.Message {
	path := MESSAGE_PAGE_PATH
	pagePath := fmt.Sprintf(path, d.Id.Hex())
	if strings.Contains(conf.GetString("message-trigger-env"), BIOSTIME_ENV_NAME) {
		path = "/" + STANDARD_BIOSTIME_MESSAGE_PAGE_PATH
		if util.StrInArray(d.AccountId.Hex(), &[]string{"62a93e54732d3177ef691ad2", "63563d405d12704b7f295692"}) {
			path = BIOSTIME_MESSAGE_PAGE_PATH
		}
		path = fmt.Sprintf(path, d.Id.Hex())
		path = url.QueryEscape(path)
		pagePath = "pages/home/<USER>" + path
	}

	message := component.Message{
		MsgType:           MESSAGE_TYPE_MINIPROGRAM_NOTICE,
		AppId:             wechatAppId,
		Title:             title,
		Description:       time.Now().Format("1月2日 15:04"),
		EmphasisFirstItem: false,
		PagePath:          pagePath,
	}
	if title == END_MESSAGE_TITLE {
		message.ContentItems = []component.ContentItem{
			{
				Key:   "活动提醒",
				Value: fmt.Sprintf(END_MESSAGE_CONTENT, d.Name, d.EndTime.Format("2006/01/02 15:04")),
			},
		}
	}
	if title == START_MESSAGE_TITLE {
		message.ContentItems = []component.ContentItem{
			{
				Key:   "活动名称",
				Value: d.Name,
			},
			{
				Key:   "活动描述",
				Value: d.Description,
			},
			{
				Key:   "活动类型",
				Value: d.GetTypeStr(),
			},
			{
				Key:   "活动时间",
				Value: fmt.Sprintf("%s至\n%s", d.StartTime.Format("2006-01-02 15:04:05"), d.EndTime.Format("2006-01-02 15:04:05")),
			},
		}
	}
	return message
}

func (d *DrainageActivity) GetTypeStr() string {
	if d.Type == DRAINAGE_ACTIVITY_TYPE_PERSONAL {
		return "个人裂变活动"
	}
	return "群裂变活动"
}

func (d *DrainageActivity) SendMessage(ctx context.Context, channelId, staffNo string, message component.Message) {
	log.Warn(ctx, "SendMessage log", log.Fields{
		"accountId":  util.GetAccountId(ctx),
		"activityId": d.Id.Hex(),
		"staffNo":    staffNo,
		"message":    message,
	})
	err := component.WeConnect.SendWechatMessage(ctx, channelId, staffNo, &message)
	if err != nil {
		log.Warn(ctx, "Failed to send message", log.Fields{
			"errorMessage": err.Error(),
			"accountId":    d.AccountId.Hex(),
			"taskId":       d.Id.Hex(),
			"staffNo":      staffNo,
			"channelId":    channelId,
			"message":      message,
		})
	}
}

func (d *DrainageActivity) Close(ctx context.Context) error {
	d.Status = DRAINAGE_ACTIVITY_STATUS_ENDED
	d.EndTime = time.Now()
	err := d.Update(ctx)
	if err != nil {
		return err
	}

	// 更新导购活动状态
	err = CStaffDrainageActivity.UpdateStatus(ctx, d.Id, d.Status)
	if err != nil {
		return err
	}

	// 将还未完成活动的客户进度标记为失败
	selector := bson.M{
		"accountId":          util.GetAccountIdAsObjectId(ctx),
		"drainageActivityId": d.Id,
		"status":             ACTIVITY_MEMBER_STATUS_RUNNING,
		"invitedCount": bson.M{
			"$lt": d.CompletedLimitCount,
		},
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"status":    ACTIVITY_MEMBER_STATUS_FAILED,
			"updatedAt": time.Now(),
		},
	}
	_, err = extension.DBRepository.UpdateAll(ctx, C_DRAINAGE_ACTIVITY_MEMBER, selector, updater)
	return err
}

func (d *DrainageActivity) DeleteContactWay(ctx context.Context) error {
	if d.Type == DRAINAGE_ACTIVITY_TYPE_GROUPCHAT {
		return nil
	}
	channel, err := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err != nil {
		return err
	}
	condition := bson.M{
		"accountId":          util.GetAccountIdAsObjectId(ctx),
		"drainageActivityId": d.Id,
	}
	staffDrainageActivities, err := CStaffDrainageActivity.GetAllByCondition(ctx, condition)
	if err != nil {
		return err
	}
	for _, staffDrainageActivity := range staffDrainageActivities {
		component.DeleteContactWay(ctx, channel.ChannelId, staffDrainageActivity.ContactWay.Id.Hex())
	}
	return nil
}

func (d *DrainageActivity) UpdateContactWay(ctx context.Context, appId string) error {
	if d.Type == DRAINAGE_ACTIVITY_TYPE_GROUPCHAT {
		return nil
	}
	channel, err := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err != nil {
		return err
	}
	condition := bson.M{
		"accountId":          util.GetAccountIdAsObjectId(ctx),
		"drainageActivityId": d.Id,
	}
	staffDrainageActivities, err := CStaffDrainageActivity.GetAllByCondition(ctx, condition)
	if err != nil {
		return err
	}
	if d.AddStaffWay == ADD_STAFF_WAY_DRAINAGE {
		for _, staffDrainageActivity := range staffDrainageActivities {
			channelId := channel.ChannelId
			staff, err := store_model.CStaff.GetById(ctx, staffDrainageActivity.StaffId)
			if err != nil {
				continue
			}
			request := d.getContactWayRequest(ctx, staff, appId)
			if staff.ChainCorpChannelId != "" {
				channelId = staff.ChainCorpChannelId
			}
			_, err = component.UpdateContactWay(ctx, channelId, staffDrainageActivity.ContactWay.Id.Hex(), request)
			if err != nil {
				log.Warn(ctx, "Failed to update single contact way", log.Fields{
					"errMessage": err.Error(),
					"staff":      staff,
					"activityId": d.Id.Hex(),
				})
			}
		}
	} else {
		staffNosMap := make(map[string][]string)
		for _, staffDrainageActivity := range staffDrainageActivities {
			staff, err := store_model.CStaff.GetById(ctx, staffDrainageActivity.StaffId)
			if err != nil {
				continue
			}
			storeId := staffDrainageActivity.ContactWay.StoreId.Hex()
			key := storeId + "_" + staffDrainageActivity.ContactWay.Id.Hex()
			staffNosMap[key] = append(staffNosMap[key], staff.StaffNo, staff.ChainCorpChannelId)
		}
		for key, staffNos := range staffNosMap {
			channelId := channel.ChannelId
			temp := strings.Split(key, "_")
			request := d.getMultiContactWayRequest(ctx, staffNos, temp[0], appId)
			if temp[2] != "" {
				channelId = temp[2]
			}
			_, err = component.UpdateContactWay(ctx, channelId, temp[1], request)
			if err != nil {
				log.Warn(ctx, "Failed to update contact way", log.Fields{
					"errMessage": err.Error(),
					"activityId": d.Id.Hex(),
					"key":        key,
				})
				continue
			}
		}
	}

	return nil
}

func (d *DrainageActivity) NeedUpdateContactWay(ctx context.Context, oldSetting InvitationPageSetting) bool {
	if d.InvitationPageSetting.WelcomeMsg.Text.Content != oldSetting.WelcomeMsg.Text.Content ||
		d.InvitationPageSetting.LinkTitle != oldSetting.LinkTitle ||
		d.InvitationPageSetting.LinkDescription != oldSetting.LinkDescription ||
		len(d.InvitationPageSetting.LinkImages) > 0 && len(oldSetting.LinkImages) > 0 && d.InvitationPageSetting.LinkImages[0] != oldSetting.LinkImages[0] {
		return true
	}
	return false
}

func (d *DrainageActivity) Visit(ctx context.Context) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       d.Id,
		"isDeleted": false,
	}
	updater := bson.M{
		"$inc": bson.M{
			"visitorCount": 1,
		},
	}
	change := qmgo.Change{
		Update:    updater,
		ReturnNew: true,
	}
	tempActivity := DrainageActivity{}
	extension.DBRepository.FindAndApply(ctx, C_DRAINAGE_ACTIVITY, selector, []string{}, change, &tempActivity)
}

func (*DrainageActivity) Invite(ctx context.Context, activityId bson.ObjectId, count int) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       activityId,
		"isDeleted": false,
	}
	updater := bson.M{
		"$inc": bson.M{
			"invitedCount": count,
		},
	}
	change := qmgo.Change{
		Update:    updater,
		ReturnNew: true,
	}
	tempActivity := DrainageActivity{}
	extension.DBRepository.FindAndApply(ctx, C_DRAINAGE_ACTIVITY, selector, []string{}, change, &tempActivity)
}

func (d *DrainageActivity) UpdateCompletedActivityMember(ctx context.Context, memberId, staffId bson.ObjectId) {
	selector := bson.M{
		"accountId":          util.GetAccountIdAsObjectId(ctx),
		"drainageActivityId": d.Id,
		"memberId":           memberId,
		"status":             ACTIVITY_MEMBER_STATUS_RUNNING,
		"$expr": bson.M{
			"$gte": []string{"$invitedCount", "$limitCount"},
		},
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"status":    ACTIVITY_MEMBER_STATUS_COMPLETED,
			"updatedAt": time.Now(),
		},
		"$inc": bson.M{
			"completedCount":      1,
			"totalCompletedCount": 1,
		},
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_DRAINAGE_ACTIVITY_MEMBER, selector, updater)
	if err != nil {
		log.Warn(ctx, "Update completed member activity failed", log.Fields{
			"errMessage":         err.Error(),
			"drainageActivityId": d.Id,
			"memberId":           memberId,
		})
	}
}

// 检查引流活动助力客户是否符合新客定义
func (d *DrainageActivity) IsNewMember(ctx context.Context, memberId, staffId string) (bool, error) {
	member, rpcErr := getMember(ctx, memberId)
	if rpcErr != nil {
		return false, errors.NewNotExistsError("member")
	}
	member.Socials = append(member.Socials, member.OriginFrom)
	externalSocial := &origin.OriginInfo{}
	for _, social := range member.Socials {
		if social.Origin == "wecontact" {
			externalSocial = social
		}
	}

	isNewMember := false
	for _, newMemberRule := range d.NewMemberCondition {
		if newMemberRule == NEW_MEMBER_POOL_EXCLUDED && util.TransUnixToTime(member.CreatedAt).Add(5*time.Minute).After(time.Now()) && externalSocial.Channel == "" {
			isNewMember = true
			break
		}
		if newMemberRule == NEW_MEMBER_POOL_EXCLUDED {
			continue
		}
		if d.Type == DRAINAGE_ACTIVITY_TYPE_PERSONAL {
			// 新客规则有无主客户，并且没有外部联系人 social，则是新客
			if newMemberRule == NEW_MEMBER_UNOWNED_MEMBER && externalSocial.Channel == "" {
				isNewMember = true
				break
			}
			// 活动设置添加导购方式为添加引流导购，并且新客规则有未添加引流导购的客户
			if d.AddStaffWay == ADD_STAFF_WAY_DRAINAGE && newMemberRule == NEW_MEMBER_NOT_DRAINAGE_STAFF_MEMBER {
				if staffId == "" {
					return false, errors.NewNotExistsError("staffId")
				}
				isNewMember = d.isNotDrainageStaffMember(ctx, memberId, staffId)
				break
			}
		} else {
			resp, err := ListMemberGroupchats(ctx, []string{memberId})
			if err != nil {
				return false, err
			}
			// 新客规则：未进群客户-在客户池，没进群的用户
			if newMemberRule == NEW_MEMBER_NOT_IN_GROUP && len(resp.Items) == 0 {
				isNewMember = true
				break
			}
			// 新客规则：非活动范围群客户-在客户池，没进活动群的用户
			if newMemberRule == NEW_MEMBER_NOT_IN_ACTIVITY_GROUP {
				// 客户不在任何群
				if len(resp.Items) == 0 {
					isNewMember = true
					break
				}
				memberGroupchats := resp.Items[0].Groupchats
				if len(memberGroupchats) == 0 {
					isNewMember = true
					break
				}
				// 客户所在群不属于活动群
				if len(d.GroupchatDrainageQrcodeIds) > 0 {
					isNewMember = true
					qrcodeResp, err := ListGroupchatQrcodes(ctx, d.GroupchatDrainageQrcodeIds[0].Hex())
					if err != nil {
						return false, err
					}
					activityGroupchatIds := core_util.ToStringArray(core_util.ExtractArrayField("GroupchatId", qrcodeResp.Items))
					for _, groupchat := range memberGroupchats {
						if util.StrInArray(groupchat.Id, &activityGroupchatIds) {
							isNewMember = false
							break
						}
					}
				}
			}
		}
	}
	return isNewMember, nil
}

func (d *DrainageActivity) isNotDrainageStaffMember(ctx context.Context, memberId, staffId string) bool {
	resp, _ := GetStaffMembers(ctx, &pb_staff.GetStaffMembersRequest{
		ListCondition: &request.ListCondition{Page: 1, PerPage: 5},
		StaffIds:      []string{staffId},
		MemberIds:     []string{memberId},
	})
	if len(resp.Items) == 0 {
		return true
	}
	staffMember := resp.Items[0]
	if staffMember.RelationStatus != staff_model.STAFF_MEMBER_RELATION_DELETED {
		return false
	}
	if staffMember.DeletedAt == "" {
		return false
	}
	addedAt := util.MustTransStrToTime(staffMember.AddedAt)
	deletedAt, _ := time.ParseInLocation("2006-01-02 15:04:05", staffMember.DeletedAt, time.Local)
	if deletedAt.Before(d.StartTime) && addedAt.Before(d.StartTime) {
		return true
	}
	return false
}

func (d *DrainageActivity) SendCampaignHelpEvent(ctx context.Context, drainageActivityMember DrainageActivityMember, memberId string) {
	eventBody := genBaseCampaignEventBody(drainageActivityMember, d.Name, component.MAIEVENT_CAMPAIGN_HELP)
	eventBody.MemberId = memberId
	eventBody.EventProperties["inviterId"] = drainageActivityMember.MemberId.Hex()
	eventBody.SendCustomerEventWithBusinessTag(ctx)
}

func (d *DrainageActivity) SendCampaignBeenHelpedEvent(ctx context.Context, drainageActivityMember DrainageActivityMember, memberId string) {
	eventBody := genBaseCampaignEventBody(drainageActivityMember, d.Name, component.MAIEVENT_CAMPAIGN_BEEN_HELPED)
	eventBody.MemberId = drainageActivityMember.MemberId.Hex()
	// 邀请人客户 id
	eventBody.EventProperties["memberId"] = drainageActivityMember.MemberId.Hex()
	// 被邀请人客户 id
	eventBody.EventProperties["inviteeId"] = memberId
	eventBody.SendCustomerEventWithBusinessTag(ctx)
}

func genBaseCampaignEventBody(drainageActivityMember DrainageActivityMember, campaignName, subType string) component.CustomerEventBody {
	return component.CustomerEventBody{
		AccountId:  drainageActivityMember.AccountId.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    subType,
		CreateTime: time.Now().UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"campaignId":   drainageActivityMember.DrainageActivityId.Hex(),
			"campaignName": campaignName,
		},
	}
}

// 发送外部联系人完成引流任务事件
func (d *DrainageActivity) SendDrainageTaskCompleteEvent(ctx context.Context, drainageActivityMember DrainageActivityMember, memberId string) {
	// 用户还未完成任务，不能发事件
	if !drainageActivityMember.IsCompleted(memberId, int(d.CompletedLimitCount)) {
		return
	}

	staffId := drainageActivityMember.StaffId.Hex()
	staff, err := ec_model.GetStaff(ctx, staffId)
	if err != nil {
		log.Warn(ctx, "Get staff fail", log.Fields{
			"staffId":            staffId,
			"drainageActivityId": d.Id.Hex(),
		})
		return
	}
	eventBody := component.CustomerEventBody{
		AccountId:  drainageActivityMember.AccountId.Hex(),
		MemberId:   memberId,
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_DRAINAGE_TASK_COMPLETE,
		CreateTime: time.Now().UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"campaignId":   d.Id.Hex(),
			"campaignName": d.Name,
			"campaignType": d.Type,
			"count":        d.CompletedLimitCount,
			"completedAt":  time.Now().UnixNano() / 1e6,
			"staffId":      staffId,
			"staffName":    staff.Name,
			"memberId":     memberId,
			"rewards":      d.getRewardsProperty(ctx),
		},
	}
	eventBody.SendCustomerEventWithBusinessTag(ctx)
}

func (d *DrainageActivity) getRewardsProperty(ctx context.Context) []map[string]interface{} {
	rewards := []map[string]interface{}{}
	reward := d.RewardSetting.InitiatorReward
	if reward.Type == REWARD_TYPE_SCORE {
		rewards = append(rewards, map[string]interface{}{
			"score": reward.Score,
		})
		return rewards
	}

	if reward.Type == REWARD_TYPE_PRODUCT {
		for _, product := range reward.Products {
			rewards = append(rewards, map[string]interface{}{
				"productName": product.Name,
			})
		}
		return rewards
	}

	if len(reward.CouponIds) == 0 {
		return rewards
	}
	resp, err := ec_share.SearchCoupons(ctx, &coupon.SearchCouponRequest{
		Ids: util.MongoIdsToStrs(reward.CouponIds),
	})
	if err != nil {
		log.Warn(ctx, "Search coupons by couponId fail", log.Fields{
			"errorMessage":     err.Error(),
			"drainageActivity": d.Id.Hex(),
		})
		return nil
	}
	for _, coupon := range resp.Coupons {
		rewards = append(rewards, map[string]interface{}{
			"couponId":   coupon.Id,
			"couponName": coupon.Title,
			"couponType": coupon.Type,
		})
	}
	return rewards
}

func (d *DrainageActivity) CreateApplicableStoreDrainageQrcode(ctx context.Context, appId, channelId string) {
	if d.ApplicableStore.Type == "" {
		return
	}
	var storeIds []string
	if d.ApplicableStore.Type == task_model.APPLICABLE_STORE_ALL {
		storeIds, _ = ec_model.ListStoreIds(ctx, &pb_store.ListStoresRequest{
			Types:  []int32{3},
			Source: store_model.STORE_SOURCE_WECHATWORK,
			ListCondition: &request.ListCondition{
				Page:    1,
				PerPage: 5000,
				OrderBy: []string{"-createdAt"},
			},
		})
	} else {
		storeIds, _ = task_model.GetStoreIdsWithApplicableStore(ctx, d.ApplicableStore, "")
	}
	for _, storeId := range storeIds {
		staffs, _ := ec_model.ListStaffs(ctx, ec_model.GetListStaffsRequest(map[string]interface{}{"storeIds": []string{storeId}}))
		staffNos := []string{}
		for _, staff := range staffs {
			if staff.ChainCorpChannelId != "" {
				channelId = staff.ChainCorpChannelId
			}
			staffNos = append(staffNos, staff.StaffNo)
		}
		contactWayRequest := d.getMultiContactWayRequest(ctx, staffNos, storeId, appId)
		contactWay, err := CreateContactWay(ctx, channelId, contactWayRequest)
		if err != nil {
			log.Warn(ctx, "create drainage activity store qrcode failed", log.Fields{
				"errMsg":            err.Error(),
				"contactWayRequest": contactWayRequest,
			})
			continue
		}
		for _, staff := range staffs {
			d.CreateStaffActivity(ctx, staff.Id, util.ToMongoId(contactWay.Id), util.ToMongoId(storeId), contactWay.ImageUrl)
		}
	}
	d.IsSent = true
	d.UpdateIsSent(ctx)
}

func (d *DrainageActivity) CreateApplicableStaffDrainageQrcode(ctx context.Context, appId, channelId string) {
	if len(d.ApplicableStaff.StaffIds) == 0 && len(d.ApplicableStaff.StoreIds) == 0 {
		return
	}
	var staffs []store_model.Staff
	if len(d.ApplicableStaff.StoreIds) > 0 {
		storeStaffs, _ := ec_model.ListStaffs(ctx, ec_model.GetListStaffsRequest(map[string]interface{}{"storeIds": util.MongoIdsToStrs(d.ApplicableStaff.StoreIds)}))
		staffs = append(staffs, storeStaffs...)
	}
	if len(d.ApplicableStaff.StaffIds) > 0 {
		applicableStaffs, _ := ec_model.ListStaffs(ctx, ec_model.GetListStaffsRequest(map[string]interface{}{"staffIds": util.MongoIdsToStrs(d.ApplicableStaff.StaffIds)}))
		staffs = append(staffs, applicableStaffs...)
	}
	staffNos := []string{}
	for _, staff := range staffs {
		if staff.ChainCorpChannelId != "" {
			channelId = staff.ChainCorpChannelId
		}
		staffNos = append(staffNos, staff.StaffNo)
	}
	// 此场景中的 contactWayRequest 只有 userIds，没有 partyIds 和 tags
	// 企微的此接口必须要有上面三个之一，所以如果 staffNos 为空就不请求了。
	if len(staffNos) == 0 {
		return
	}
	storeId := bson.NewObjectId()
	contactWayRequest := d.getMultiContactWayRequest(ctx, staffNos, storeId.Hex(), appId)
	contactWay, err := CreateContactWay(ctx, channelId, contactWayRequest)
	if err != nil {
		log.Warn(ctx, "create drainage activity staff qrcode failed", log.Fields{
			"errMsg":            err.Error(),
			"contactWayRequest": contactWayRequest,
		})
		return
	}
	for _, staff := range staffs {
		d.CreateStaffActivity(ctx, staff.Id, util.ToMongoId(contactWay.Id), storeId, contactWay.ImageUrl)
	}
	d.IsSent = true
	d.UpdateIsSent(ctx)
}

func (d *DrainageActivity) CreateApplicableStoreSingleDrainageQrcode(ctx context.Context, appId, channelId string) {
	if d.ApplicableStore.Type == "" {
		return
	}
	var storeIds []string
	if d.ApplicableStore.Type == task_model.APPLICABLE_STORE_ALL {
		storeIds, _ = ec_model.ListStoreIds(ctx, &pb_store.ListStoresRequest{
			Types:  []int32{3},
			Source: store_model.STORE_SOURCE_WECHATWORK,
			ListCondition: &request.ListCondition{
				Page:    1,
				PerPage: 5000,
				OrderBy: []string{"-createdAt"},
			},
		})
	} else {
		storeIds, _ = task_model.GetStoreIdsWithApplicableStore(ctx, d.ApplicableStore, "")
	}
	sentStaffIds := []string{}
	for _, storeId := range storeIds {
		staffs, _ := ec_model.ListStaffs(ctx, ec_model.GetListStaffsRequest(map[string]interface{}{"storeIds": []string{storeId}}))
		for _, staff := range staffs {
			if util.StrInArray(staff.Id.Hex(), &sentStaffIds) {
				continue
			}
			contactWayRequest := d.getContactWayRequest(ctx, staff, appId)
			if staff.ChainCorpChannelId != "" {
				channelId = staff.ChainCorpChannelId
			}
			contactWay, err := CreateContactWay(ctx, channelId, contactWayRequest)
			if err != nil {
				log.Warn(ctx, "create drainage activity staff single qrcode failed", log.Fields{
					"errMsg":            err.Error(),
					"contactWayRequest": contactWayRequest,
					"activity":          d,
				})
				continue
			}
			d.CreateStaffActivity(ctx, staff.Id, util.ToMongoId(contactWay.Id), "", contactWay.ImageUrl)
			sentStaffIds = append(sentStaffIds, staff.Id.Hex())
		}
	}
	d.IsSent = true
	d.UpdateIsSent(ctx)
}

func (d *DrainageActivity) CreateApplicableStaffSingleDrainageQrcode(ctx context.Context, appId, channelId string) {
	if len(d.ApplicableStaff.StaffIds) == 0 && len(d.ApplicableStaff.StoreIds) == 0 {
		return
	}
	staffs := []store_model.Staff{}
	if len(d.ApplicableStaff.StoreIds) > 0 {
		storeStaffs, _ := ec_model.ListStaffs(ctx, ec_model.GetListStaffsRequest(map[string]interface{}{"storeIds": util.MongoIdsToStrs(d.ApplicableStaff.StoreIds)}))
		staffs = append(staffs, storeStaffs...)
	}
	if len(d.ApplicableStaff.StaffIds) > 0 {
		applicableStaffs, _ := ec_model.ListStaffs(ctx, ec_model.GetListStaffsRequest(map[string]interface{}{"staffIds": util.MongoIdsToStrs(d.ApplicableStaff.StaffIds)}))
		staffs = append(staffs, applicableStaffs...)
	}
	sentStaffIds := []string{}
	for _, staff := range staffs {
		if util.StrInArray(staff.Id.Hex(), &sentStaffIds) {
			continue
		}
		contactWayRequest := d.getContactWayRequest(ctx, staff, appId)
		if staff.ChainCorpChannelId != "" {
			channelId = staff.ChainCorpChannelId
		}
		contactWay, err := CreateContactWay(ctx, channelId, contactWayRequest)
		if err != nil {
			log.Warn(ctx, "create drainage activity staff single qrcode failed", log.Fields{
				"errMsg":            err.Error(),
				"contactWayRequest": contactWayRequest,
				"activity":          d,
			})
			continue
		}
		d.CreateStaffActivity(ctx, staff.Id, util.ToMongoId(contactWay.Id), "", contactWay.ImageUrl)
		sentStaffIds = append(sentStaffIds, staff.Id.Hex())
	}
	d.IsSent = true
	d.UpdateIsSent(ctx)
}

func (d *DrainageActivity) CreateStaffActivity(ctx context.Context, staffId, contactWayId, storeId bson.ObjectId, imageUrl string) {
	staffActivity := StaffDrainageActivity{
		AccountId:          d.AccountId,
		StaffId:            staffId,
		DrainageActivityId: d.Id,
		Status:             d.Status,
		StartTime:          d.StartTime,
		EndTime:            d.EndTime,
	}

	if contactWayId != "" {
		staffActivity.ContactWay = ContactWay{
			Id:       contactWayId,
			ImageUrl: imageUrl,
		}
		if storeId.Valid() {
			staffActivity.ContactWay.StoreId = storeId
		}
	}

	err := staffActivity.Create(ctx)
	if err != nil {
		log.Warn(ctx, "Create staff drainage activity failed", log.Fields{
			"activity":     staffActivity,
			"errorMessage": err.Error(),
		})
	}
}

// 活动未开始时，开启库存的实物商品奖品，初始化剩余库存等于总库存
func (d *DrainageActivity) InitProductStock() {
	if d.Status != DRAINAGE_ACTIVITY_STATUS_CREATED {
		return
	}
	for idx, p := range d.RewardSetting.InitiatorReward.Products {
		if p.TotalStock > 0 {
			d.RewardSetting.InitiatorReward.Products[idx].Stock = p.TotalStock
			d.RewardSetting.InitiatorReward.Products[idx].SentStock = 0
		}
	}
	for idx, p := range d.RewardSetting.ParticipantReward.Products {
		if p.TotalStock > 0 {
			d.RewardSetting.ParticipantReward.Products[idx].Stock = p.TotalStock
			d.RewardSetting.ParticipantReward.Products[idx].SentStock = 0
		}
	}
}

// 活动进行中发放实物商品奖励，减剩余库存
func (d *DrainageActivity) ReduceProductStock(ctx context.Context, productName, rewardedRole string, count int) (*DrainageActivity, error) {
	rewardedRoles := []string{"initiatorReward", "participantReward"}
	if d.Status != DRAINAGE_ACTIVITY_STATUS_RUNNING || !util.StrInArray(rewardedRole, &rewardedRoles) || count <= 0 {
		return nil, nil
	}
	productKey := fmt.Sprintf("rewardSetting.%s.products", rewardedRole)
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       d.Id,
		productKey: bson.M{
			"$elemMatch": bson.M{
				"name": productName,
				"stock": bson.M{
					"$gte": count,
				},
			},
		},
		"isDeleted": false,
	}

	updater := bson.M{
		"$inc": bson.M{
			productKey + ".$.stock":     -count,
			productKey + ".$.sentStock": count,
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
	}
	change := qmgo.Change{
		Update:    updater,
		ReturnNew: true,
	}

	new := &DrainageActivity{}
	err := extension.DBRepository.FindAndApply(ctx, C_DRAINAGE_ACTIVITY, selector, nil, change, new)
	if err != nil {
		return nil, err
	}
	return new, nil
}

// 活动进行中发放实物商品奖励失败，回退剩余库存
func (d *DrainageActivity) ReturnProductStock(ctx context.Context, productName, rewardedRole string, count int) (*DrainageActivity, error) {
	rewardedRoles := []string{"initiatorReward", "participantReward"}
	if d.Status != DRAINAGE_ACTIVITY_STATUS_RUNNING || !util.StrInArray(rewardedRole, &rewardedRoles) || count <= 0 {
		return nil, nil
	}
	productKey := fmt.Sprintf("rewardSetting.%s.products", rewardedRole)
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       d.Id,
		productKey: bson.M{
			"$elemMatch": bson.M{
				"name": productName,
				"sentStock": bson.M{
					"$gte": count,
				},
			},
		},
		"isDeleted": false,
	}

	updater := bson.M{
		"$inc": bson.M{
			productKey + ".$.sentStock": -count,
			productKey + ".$.stock":     count,
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
	}
	change := qmgo.Change{
		Update:    updater,
		ReturnNew: true,
	}

	new := &DrainageActivity{}
	err := extension.DBRepository.FindAndApply(ctx, C_DRAINAGE_ACTIVITY, selector, nil, change, new)
	if err != nil {
		return nil, err
	}
	return new, nil
}

// 活动进行中修改实物商品总库存，同步变更剩余库存为非零值
// 总库存为零即库存开关未开启，剩余库存为零则已发放完
func (d *DrainageActivity) UpdateProductTotalStock(ctx context.Context, productName, rewardedRole string, count int) error {
	if d.Status != DRAINAGE_ACTIVITY_STATUS_RUNNING || count <= 0 {
		return nil
	}

	var oldRewardProduct *Product
	switch rewardedRole {
	case "initiatorReward":
		for _, p := range d.RewardSetting.InitiatorReward.Products {
			temp := p
			if p.Name == productName {
				oldRewardProduct = &temp
				break
			}
		}
	case "participantReward":
		for _, p := range d.RewardSetting.ParticipantReward.Products {
			temp := p
			if p.Name == productName {
				oldRewardProduct = &temp
				break
			}
		}
	default:
		return nil
	}
	if oldRewardProduct == nil {
		return errors.NewNotExistsErrorWithMessage("product", productName)
	}
	oldTotalStock := oldRewardProduct.TotalStock
	// 库存开关未开启，则不允许开启
	// 实际未发生总库存变更，则不会更新数据
	if oldTotalStock <= 0 || count == oldTotalStock {
		return nil
	}

	productKey := fmt.Sprintf("rewardSetting.%s.products", rewardedRole)
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       d.Id,
		productKey: bson.M{
			"$elemMatch": bson.M{
				"name": productName,
			},
		},
		"isDeleted": false,
	}
	actualCount := count - oldTotalStock
	if count < oldTotalStock {
		// 减总库存，要判断剩余库存够不够减，且剩余库存不能减为零
		selector[productKey] = bson.M{
			"$elemMatch": bson.M{
				"name": productName,
				"stock": bson.M{
					"$gt": oldTotalStock - count,
				},
			},
		}
	}

	updater := bson.M{
		"$inc": bson.M{
			productKey + ".$.stock":      actualCount,
			productKey + ".$.totalStock": actualCount,
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
	}

	err := extension.DBRepository.UpdateOne(ctx, C_DRAINAGE_ACTIVITY, selector, updater)
	if err != nil {
		return err
	}
	return nil
}

// 活动进行中，未开启库存设置，发放实物商品奖励成功，增加实物商品已发放库存
func (d *DrainageActivity) IncProductSentStock(ctx context.Context, productName, rewardedRole string, count int) (*DrainageActivity, error) {
	rewardedRoles := []string{"initiatorReward", "participantReward"}
	if d.Status != DRAINAGE_ACTIVITY_STATUS_RUNNING || !util.StrInArray(rewardedRole, &rewardedRoles) || count <= 0 {
		return nil, nil
	}
	productKey := fmt.Sprintf("rewardSetting.%s.products", rewardedRole)
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"_id":       d.Id,
		productKey: bson.M{
			"$elemMatch": bson.M{
				"name": productName,
			},
		},
		"isDeleted": false,
	}

	updater := bson.M{
		"$inc": bson.M{
			productKey + ".$.sentStock": count,
		},
		"$set": bson.M{
			"updatedAt": time.Now(),
		},
	}
	change := qmgo.Change{
		Update:    updater,
		ReturnNew: true,
	}

	new := &DrainageActivity{}
	err := extension.DBRepository.FindAndApply(ctx, C_DRAINAGE_ACTIVITY, selector, nil, change, new)
	if err != nil {
		return nil, err
	}
	return new, nil
}

func (d *DrainageActivity) CreateGroupWelcomeTemplate(ctx context.Context) {
	if d.Type != DRAINAGE_ACTIVITY_TYPE_GROUPCHAT {
		return
	}
	var groupWelcomeTemplate = &component.GroupWelcomeTemplate{}
	appId := GetWechatChannelAppId(ctx)
	accountChannel, err := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err != nil || accountChannel == nil {
		log.Warn(ctx, "Get wecontact channel fail", log.Fields{})
		return
	}
	welcomeMsg := d.GetWelcomeMsg(ctx, appId)
	copier.Instance(nil).From(welcomeMsg).CopyTo(groupWelcomeTemplate)
	resp, err := component.WeConnect.CreateGroupWelcomeTemplate(ctx, accountChannel.ChannelId, groupWelcomeTemplate)
	if err != nil {
		log.Warn(ctx, "Create groupWelcome template fail", log.Fields{
			"errMsg":             err.Error(),
			"drainageActivityId": d.Id.Hex(),
		})
	}
	if resp != nil {
		d.InvitationPageSetting.WelcomeMsg.TemplateId = resp.TemplateId
		d.Update(ctx)
	}
}

func (d *DrainageActivity) UpdateGroupWelcomeTemplate(ctx context.Context) {
	if d.Type != DRAINAGE_ACTIVITY_TYPE_GROUPCHAT {
		return
	}
	if d.InvitationPageSetting.WelcomeMsg.TemplateId == "" {
		d.CreateGroupWelcomeTemplate(ctx)
		return
	}
	var groupWelcomeTemplate = &component.GroupWelcomeTemplate{}
	appId := GetWechatChannelAppId(ctx)
	accountChannel, err := service.GetOneChannelByBusinessAndOrigin(ctx, constant.BUSINESS_WECHATWORK, constant.WECONTACT)
	if err != nil || accountChannel == nil {
		log.Warn(ctx, "Get wecontact channel fail", log.Fields{})
		return
	}
	welcomeMsg := d.GetWelcomeMsg(ctx, appId)
	copier.Instance(nil).From(welcomeMsg).CopyTo(groupWelcomeTemplate)
	_, err = component.WeConnect.UpdateGroupWelcomeTemplate(ctx, accountChannel.ChannelId, groupWelcomeTemplate)
	if err != nil {
		log.Warn(ctx, "Update groupWelcome template fail", log.Fields{
			"errMsg":             err.Error(),
			"drainageActivityId": d.Id.Hex(),
		})
	}
}

func GetWechatChannelAppId(ctx context.Context) string {
	if util.GetAccountId(ctx) == "62c670fb20482f273b6bd743" {
		return "wx0665ac649846de6a"
	}
	wechatChannel, err := service.GetEnableChannel(ctx, "wechat", "SERVICE_AUTH_ACCOUNT")
	if err != nil {
		log.Warn(ctx, "Failed to get wechat channel", log.Fields{
			"accountId": util.GetAccountId(ctx),
		})
		return ""
	}
	return wechatChannel.AppId
}
