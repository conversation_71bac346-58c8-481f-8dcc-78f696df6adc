package staff_campaign

import (
	"context"
	"fmt"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/common/request"
	pb_store "mairpc/proto/ec/store"
	ec_model "mairpc/service/ec/model"

	"github.com/qiniu/qmgo"
	"github.com/spf13/cast"

	store_model "mairpc/service/ec/model/store"
	"mairpc/service/share/util"
	"time"
)

const (
	C_DRAINAGE_QRCODE                = "ec.drainageQrcode"
	DRAINAGE_QRCODE_RUNNING_STATUS   = "running"
	DRAINAGE_QRCODE_COMPLETED_STATUS = "completed"

	APPLICATION_SCOPE_ALL_STAFF = "allStaff" // 单人引流码，全部导购新增
	APPLICATION_SCOPE_BY_STAFF  = "byStaff"  // 单人/多人引流码，指定导购新增
	APPLICATION_SCOPE_ALL_STORE = "allStore" // 多人引流码，全部门店新增
	APPLICATION_SCOPE_BY_STORE  = "byStore"  // 多人引流码，指定门店新增
	APPLICATION_SCOPE_BY_IMPORT = "byImport" // 单人引流码导入适用导购，多人引流码导入适用门店

	QRCODE_TYPE_SINGLE     = "SINGLE" // 引流码单人类型
	QRCODE_TYPE_MULTI      = "MULTI"  // 引流码多人类型
	QRCODE_TYPE_SINGLE_STR = "单人引流码"  // 引流码单人类型
	QRCODE_TYPE_MULTI_STR  = "多人引流码"  // 引流码多人类型

	SCENE_QRCODE                           = "QRCODE"
	FAILED_CONTACT_WAY_AUTHENTICATION_TYPE = "notAuthentication" // 用户未实名认证
	FAILED_CONTACT_WAY_ACTIVATION_TYPE     = "inactivated"       // 用户未激活

	CREATE_OPERATION = "create"
	UPDATE_OPERATION = "operation"
	DELETE_OPERATION = "delete"
)

var (
	CDrainageQrcode = &DrainageQrcode{}
)

type DrainageQrcode struct {
	Id                   bson.ObjectId       `bson:"_id"`
	AccountId            bson.ObjectId       `bson:"accountId"`
	Name                 string              `bson:"name"`                           // 引流码名称
	ApplicationScope     ApplicationScope    `bson:"applicationScope"`               // 引流码适用范围
	Type                 string              `bson:"type"`                           // 引流码类型，SINGLE 单人，MULTI 多人
	Scene                string              `bson:"scene"`                          // WEAPP 小程序，QRCODE 二维码
	Remark               string              `bson:"remark"`                         // 备注信息
	Tags                 []string            `bson:"tags"`                           // 添加客户打标签
	SkipVerify           bool                `bson:"skipVerify"`                     // 外部客户添加时是否无需验证
	WelcomeMsg           WelcomeMsg          `bson:"welcomeMsg,omitempty"`           // 引流码欢迎语
	Status               string              `bson:"status"`                         // 引流码创建状态，running 创建中，completed 创建完成
	Count                uint64              `bson:"count"`                          // 引流码数量
	StaffCount           uint64              `bson:"staffCount"`                     // 单人码对应的导购数
	StoreCount           uint64              `bson:"storeCount"`                     // 多人码对应的门店数
	ContactWayIds        []string            `bson:"contactWayIds"`                  // 对应联系我二维码 ids，weconnect 库 wechatCPContactWay._id
	DeletedContactWayIds []string            `bson:"deletedContactWayIds,omitempty"` // 导购离职时删除的对应联系我二维码
	ChannelIds           []string            `bson:"channelIds,omitempty"`           // 对应联系我二维码 channelId
	DistributorId        bson.ObjectId       `bson:"distributorId,omitempty"`        // 零售公司 id
	SubDistributorIds    []bson.ObjectId     `bson:"subDistributorIds,omitempty"`    // 子零售公司 id
	DistributorCounts    []DistributorCounts `bson:"distributorCounts,omitempty"`    // 用来记录不同零售公司下引流码数量
	CreatedAt            time.Time           `bson:"createdAt"`
	UpdatedAt            time.Time           `bson:"updatedAt"`
	IsDeleted            bool                `bson:"isDeleted"`
	FailedScope          []FailedScope       `bson:"failedScope,omitempty"` // 创建联系我二维码失败的导购 id 或门店 id
	FailedUrl            string              `bson:"failedUrl,omitempty"`
	CreatedBy            bson.ObjectId       `bson:"createdBy,omitempty"`           // 创建人
	BackupContactWayIds  []string            `bson:"backupContactWayIds,omitempty"` // 用于记录次日零点需要恢复的单人码
	IsSystemCreated      bool                `bson:"isSystemCreated,omitempty"`     // 是否由系统创建
}

type DistributorCounts struct {
	DistributorId string `bson:"distributorId,omitempty"` // 零售公司 id
	Count         uint64 `bson:"count,omitempty"`         // 零售公司下引流码数量
}

type ApplicationScope struct {
	Type             string            `bson:"type"`               // 适用范围类型，allStaff 全部导购适用，allStore 全部门店，byStore 部分门店,byStaff 部分导购,byImport 导入导购或门店
	StoreIds         []string          `bson:"storeIds,omitempty"` // type 为 byStore 或 byImport 时有值
	StaffIds         []string          `bson:"staffIds,omitempty"` // type 为 byStaff 时有值
	ApplicableStaff  []ApplicableStaff `bson:"applicableStaff,omitempty"`
	ExcludedStaffIds []string          `bson:"excludedStaffIds,omitempty"`
}

type ApplicableStaff struct {
	StoreId  string   `bson:"storeId,omitempty"`
	StaffIds []string `bson:"staffIds,omitempty"`
}

type WelcomeMsg struct {
	Text             Text              `bson:"text,omitempty"`
	Images           []Image           `bson:"images,omitempty"`
	Links            []Link            `bson:"links,omitempty"`
	Miniprograms     []Miniprogram     `bson:"miniprograms,omitempty"`
	Videos           []Video           `bson:"videos,omitempty"`
	PlaceholderRules []PlaceholderRule `bson:"placeholderRules,omitempty"`
	TemplateId       string            `bson:"templateId,omitempty"`
}

type Text struct {
	Content string `bson:"content"`
	Order   uint64 `bson:"order"`
}

type Image struct {
	Url   string `bson:"url"`
	Order uint64 `bson:"order"`
}

type Link struct {
	Title  string `bson:"title"`
	PicUrl string `bson:"picUrl"`
	Desc   string `bson:"desc"`
	Url    string `bson:"url"`
	Order  uint64 `bson:"order"`
}

type Miniprogram struct {
	Title         string `bson:"title"`
	AppId         string `bson:"appId"`
	ThumbImageUrl string `bson:"thumbImageUrl"`
	PagePath      string `bson:"pagePath"`
	Order         uint64 `bson:"order"`
	Desc          string `bson:"desc"`
	Icon          string `bson:"icon"`
}

type Video struct {
	Url   string `bson:"url"`
	Order uint64 `bson:"order"`
}

type PlaceholderRule struct {
	Placeholder string `bson:"placeholder"`
	Filler      Filter `bson:"filler,omitempty"`
}

type Filter struct {
	Property string `bson:"property"`
}

type Extra struct {
	StaffId   string   `json:"staffId,omitempty"`
	StoreId   string   `json:"storeId,omitempty"`
	StaffName string   `json:"staffName,omitempty"`
	StoreName string   `json:"storeName,omitempty"`
	StaffIds  []string `json:"staffIds,omitempty"`
}

type FailedScope struct {
	StaffIds []string `bson:"staffIds,omitempty"`
	StoreIds []string `bson:"storeIds,omitempty"`
	Type     string   `bson:"type,omitempty"`
}

func (d *DrainageQrcode) Create(ctx context.Context) error {
	now := time.Now()
	d.Id = bson.NewObjectId()
	d.AccountId = util.GetAccountIdAsObjectId(ctx)
	d.CreatedAt = now
	d.UpdatedAt = now
	d.Status = DRAINAGE_QRCODE_RUNNING_STATUS
	d.Scene = SCENE_QRCODE
	_, err := extension.DBRepository.Insert(ctx, C_DRAINAGE_QRCODE, d)
	return err
}

func (*DrainageQrcode) GetById(ctx context.Context, id bson.ObjectId) (DrainageQrcode, error) {
	result := DrainageQrcode{}
	selector := Common.GenDefaultConditionById(ctx, id)
	err := extension.DBRepository.FindOne(ctx, C_DRAINAGE_QRCODE, selector, &result)
	return result, err
}

func (*DrainageQrcode) GetByIds(ctx context.Context, ids []bson.ObjectId) []DrainageQrcode {
	condition := Common.GenDefaultCondition(ctx)
	condition["_id"] = bson.M{"$in": ids}
	results := []DrainageQrcode{}
	extension.DBRepository.FindAll(ctx, C_DRAINAGE_QRCODE, condition, []string{}, 0, &results)
	return results
}

func (*DrainageQrcode) Delete(ctx context.Context, id bson.ObjectId) error {
	selector := Common.GenDefaultConditionById(ctx, id)
	updator := bson.M{
		"$set": bson.M{
			"isDeleted": true,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_DRAINAGE_QRCODE, selector, updator)
}

func (d *DrainageQrcode) Update(ctx context.Context) error {
	selector := Common.GenDefaultConditionById(ctx, d.Id)
	setter := bson.M{
		"name":                 d.Name,
		"applicationScope":     d.ApplicationScope,
		"remark":               d.Remark,
		"tags":                 d.Tags,
		"skipVerify":           d.SkipVerify,
		"welcomeMsg":           d.WelcomeMsg,
		"count":                d.Count,
		"staffCount":           d.StaffCount,
		"storeCount":           d.StoreCount,
		"contactWayIds":        d.ContactWayIds,
		"deletedContactWayIds": d.DeletedContactWayIds,
		"distributorCounts":    d.DistributorCounts,
		"subDistributorIds":    d.SubDistributorIds,
		"status":               d.Status,
		"failedScope":          d.FailedScope,
		"failedUrl":            d.FailedUrl,
		"updatedAt":            time.Now(),
	}
	updater := bson.M{
		"$set": setter,
	}
	if len(d.BackupContactWayIds) == 0 {
		updater["$unset"] = bson.M{"backupContactWayIds": ""}
	} else {
		setter["backupContactWayIds"] = d.BackupContactWayIds
	}
	return Common.UpdateOne(ctx, C_DRAINAGE_QRCODE, "false", selector, updater)
}

func (*DrainageQrcode) FindByPagination(ctx context.Context, page extension.PagingCondition) ([]*DrainageQrcode, int, error) {
	result := []*DrainageQrcode{}
	total, err := extension.DBRepository.FindByPagination(ctx, C_DRAINAGE_QRCODE, page, &result)
	return result, total, err
}

func (*DrainageQrcode) GetAllByCondition(ctx context.Context, condition bson.M) ([]DrainageQrcode, error) {
	result := []DrainageQrcode{}
	err := extension.DBRepository.FindAll(ctx, C_DRAINAGE_QRCODE, condition, []string{"-CreatedAt"}, 0, &result)
	return result, err
}

func (d *DrainageQrcode) GetTypeStr() string {
	if d.Type == QRCODE_TYPE_SINGLE {
		return QRCODE_TYPE_SINGLE_STR
	}
	return QRCODE_TYPE_MULTI_STR
}

func (d *DrainageQrcode) SetCount(ctx context.Context, distributorIds []string) {
	// 总部查询引流码时，不需要重新设置 count
	if len(distributorIds) == 0 {
		return
	}
	// 引流码是零售公司创建时，不需要重新设置 count
	if d.DistributorId != "" {
		return
	}
	totalCount := uint64(0)
	for _, distributorCount := range d.DistributorCounts {
		if util.StrInArray(distributorCount.DistributorId, &distributorIds) {
			totalCount += distributorCount.Count
		}
	}
	d.Count = uint64(totalCount)
}

func (*DrainageQrcode) GetByCondition(ctx context.Context, condition bson.M) (DrainageQrcode, error) {
	result := DrainageQrcode{}
	err := extension.DBRepository.FindOne(ctx, C_DRAINAGE_QRCODE, condition, &result)
	return result, err
}

func (d *DrainageQrcode) GetApplicationScopeMap(ctx context.Context, ids ...string) (map[string][]string, error) {
	var (
		err                 error
		applicationScopeMap map[string][]string
	)
	if d.Type == QRCODE_TYPE_SINGLE {
		applicationScopeMap, err = d.getStaffMap(ctx, ids...)
	} else {
		applicationScopeMap, err = d.getStoreMap(ctx, ids...)
	}
	if err != nil {
		return applicationScopeMap, err
	}
	return applicationScopeMap, nil
}

func (d *DrainageQrcode) getStaffMap(ctx context.Context, staffIds ...string) (map[string][]string, error) {
	staffs, err := d.getStaffs(ctx, staffIds...)
	if err != nil {
		return nil, err
	}
	staffMap := map[string][]string{}
	for _, staff := range staffs {
		staffMap[staff.Id.Hex()] = []string{staff.StaffNo, staff.Name, staff.CurrentStoreId.Hex(), staff.ChainCorpChannelId}
	}
	return staffMap, nil
}

func (d *DrainageQrcode) getStaffs(ctx context.Context, staffIds ...string) ([]store_model.Staff, error) {
	paramsMap := map[string]interface{}{}
	storeIds := []string{}
	subStoreIds := []string{}
	staffs := []store_model.Staff{}
	storeStaffs := []store_model.Staff{}
	if d.DistributorId != "" {
		var err error
		subStoreIds, err = ec_model.GetChildStoreIds(ctx, []string{d.DistributorId.Hex()}, false)
		if err != nil {
			return nil, err
		}
	}

	if d.ApplicationScope.Type == APPLICATION_SCOPE_BY_STAFF || d.ApplicationScope.Type == APPLICATION_SCOPE_BY_IMPORT {
		if len(d.ApplicationScope.StoreIds) > 0 {
			storeIds = d.ApplicationScope.StoreIds
		}
		if len(d.ApplicationScope.StaffIds) > 0 {
			var err error
			paramsMap["staffIds"] = d.ApplicationScope.StaffIds
			if len(subStoreIds) > 0 {
				paramsMap["storeIds"] = subStoreIds
			}
			staffs, err = ec_model.ListStaffs(ctx, ec_model.GetListStaffsRequest(paramsMap))
			if err != nil {
				return nil, err
			}
		}
	}
	if d.ApplicationScope.Type == APPLICATION_SCOPE_ALL_STAFF {
		var err error
		if len(subStoreIds) > 0 {
			paramsMap["storeIds"] = subStoreIds
		}
		staffs, err = ec_model.ListStaffs(ctx, ec_model.GetListStaffsRequest(paramsMap))
		if err != nil {
			return nil, err
		}
	}

	if len(storeIds) > 0 {
		var err error
		storeIdsMap := map[string]interface{}{}
		if len(subStoreIds) > 0 {
			storeIds = util.StrArrayDuplicate(storeIds, subStoreIds)
		}
		if len(storeIds) > 0 {
			storeIdsMap["storeIds"] = storeIds
			storeStaffs, err = ec_model.ListStaffs(ctx, ec_model.GetListStaffsRequest(storeIdsMap))
			if err != nil {
				return nil, err
			}
		}
	}
	staffs = append(staffs, storeStaffs...)

	if len(staffIds) > 0 {
		var err error
		paramsMap["staffIds"] = staffIds
		staffs, err = ec_model.ListStaffs(ctx, ec_model.GetListStaffsRequest(paramsMap))
		if err != nil {
			return nil, err
		}
	}
	return staffs, nil
}

func (d *DrainageQrcode) getStoreMap(ctx context.Context, storeIds ...string) (map[string][]string, error) {
	req := pb_store.ListStoresRequest{
		Source: store_model.STORE_SOURCE_WECHATWORK,
		Types:  []int32{3},
		ListCondition: &request.ListCondition{
			Page:    1,
			PerPage: 1000,
			OrderBy: []string{"-createdAt"},
		},
	}
	if d.ApplicationScope.Type == APPLICATION_SCOPE_BY_STORE || d.ApplicationScope.Type == APPLICATION_SCOPE_BY_IMPORT {
		req.Ids = d.ApplicationScope.StoreIds
	}
	// 为单独的 storeIds 新增引流码
	if len(storeIds) > 0 {
		req.Ids = storeIds
	}
	// 按导购创建多人引流码
	if d.ApplicationScope.Type == APPLICATION_SCOPE_BY_STAFF {
		return d.getMultiStaffMap(ctx)
	}

	storeMap, err := d.formatStoreMap(ctx, req, d.ApplicationScope.Type)
	if err != nil {
		return nil, err
	}
	return storeMap, nil
}

func (d *DrainageQrcode) getMultiStaffMap(ctx context.Context) (map[string][]string, error) {
	staffs, err := d.getStaffs(ctx)
	if err != nil {
		return nil, err
	}
	staffStr := ""
	chainCorpChannelId := ""
	for _, staff := range staffs {
		staffStr = fmt.Sprintf("%s,%s", staffStr, staff.StaffNo)
		if staff.ChainCorpChannelId != "" {
			chainCorpChannelId = staff.ChainCorpChannelId
		}
	}
	multiStaffMap := map[string][]string{}
	multiStaffMap[bson.NewObjectId().Hex()] = []string{staffStr, chainCorpChannelId}

	return multiStaffMap, nil
}

func (d *DrainageQrcode) formatStoreMap(ctx context.Context, req pb_store.ListStoresRequest, createType string) (map[string][]string, error) {
	storeMap := map[string][]string{}
	if d.DistributorId != "" {
		var err error
		subStoreIds, err := ec_model.GetChildStoreIds(ctx, []string{d.DistributorId.Hex()}, false)
		if err != nil {
			return nil, err
		}
		if len(subStoreIds) > 0 {
			if len(req.Ids) > 0 {
				ids := []string{}
				for _, id := range req.Ids {
					if util.StrInArray(id, &subStoreIds) {
						ids = append(ids, id)
					}
				}
				req.Ids = ids
				if len(req.Ids) == 0 {
					return storeMap, nil
				}
			} else {
				req.Ids = append(req.Ids, subStoreIds...)
			}
		}
	}
	for _, applicableStaff := range d.ApplicationScope.ApplicableStaff {
		paramsMap := map[string]interface{}{}
		paramsMap["distributorIds"] = []string{applicableStaff.StoreId}
		paramsMap["staffIds"] = applicableStaff.StaffIds
		staffs, err := ec_model.ListStaffs(ctx, ec_model.GetListStaffsRequest(paramsMap))
		if err != nil {
			return nil, err
		}
		staffStr := ""
		chainCorpChannelId := ""
		for _, staff := range staffs {
			staffStr = fmt.Sprintf("%s,%s", staffStr, staff.StaffNo)
			if staff.ChainCorpChannelId != "" {
				chainCorpChannelId = staff.ChainCorpChannelId
			}
		}
		applicableStore, err := ec_model.GetStore(ctx, applicableStaff.StoreId)
		if err != nil {
			log.Warn(ctx, "Get applicableStore failed", log.Fields{
				"errorMessage": err.Error(),
				"storeId":      applicableStaff.StoreId,
			})
		}
		storeMap[applicableStaff.StoreId] = []string{staffStr, applicableStore.DepartmentId, applicableStore.Name, chainCorpChannelId}
	}
	if len(req.Ids) > 0 || createType == APPLICATION_SCOPE_ALL_STORE {
		stores, err := ec_model.ListStores(ctx, &req)
		if err != nil {
			return nil, err
		}
		for _, store := range stores {
			storeMap[store.Id] = []string{store.DepartmentId, store.Name, store.ChainCorpChannelId}
		}
	}
	return storeMap, nil
}

func (w *WelcomeMsg) IsEmptyWelcomeMsg() bool {
	if len(w.Images) == 0 && len(w.Links) == 0 && len(w.Miniprograms) == 0 && len(w.Videos) == 0 && w.Text.Content == "" {
		return true
	}
	return false
}

func (*DrainageQrcode) GetAllByConditionAndLimit(ctx context.Context, condition bson.M, sort []string, limit int) ([]DrainageQrcode, error) {
	results := []DrainageQrcode{}
	_, err := Common.GetAllByCondition(ctx, condition, sort, limit, C_DRAINAGE_QRCODE, &results)
	return results, err
}

func (*DrainageQrcode) GetAddAndLostCount(oldCtx context.Context, ids []string) (map[string]uint64, map[string]uint64, error) {
	ctx := core_util.CtxWithReadSecondaryPreferred(oldCtx)
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"accountId": util.GetAccountIdAsObjectId(ctx),
				"drainageQrcodeId": bson.M{
					"$in": util.ToMongoIds(ids),
				},
			},
		},
		{
			"$group": bson.M{
				"_id": "$drainageQrcodeId",
				"addCount": bson.M{
					"$sum": "$addCount",
				},
				"lostCount": bson.M{
					"$sum": "$lostCount",
				},
			},
		},
	}
	var result []bson.M
	err := extension.DBRepository.Aggregate(ctx, C_DRAINAGE_QRCODE_STATS, pipeline, false, &result)
	addCountMap := make(map[string]uint64, len(result))
	lostCountMap := make(map[string]uint64, len(result))
	for _, r := range result {
		addCountMap[core_util.ToObjectId(r["_id"]).Hex()] = cast.ToUint64(r["addCount"])
		lostCountMap[core_util.ToObjectId(r["_id"]).Hex()] = cast.ToUint64(r["lostCount"])
	}
	return addCountMap, lostCountMap, err
}

func (d *DrainageQrcode) FindAndApply(ctx context.Context, contactWayIds, channelIds []string, staffCount, storeCount int, failedScopes []FailedScope) error {
	selector := Common.GenDefaultConditionById(ctx, d.Id)
	updater := bson.M{
		"$set": bson.M{
			"status":     d.Status,
			"channelIds": channelIds,
			"failedUrl":  d.FailedUrl,
			"updatedAt":  time.Now(),
		},
		"$addToSet": bson.M{
			"contactWayIds": bson.M{"$each": contactWayIds},
			"failedScope":   bson.M{"$each": failedScopes},
		},
		"$inc": bson.M{
			"staffCount": staffCount,
			"storeCount": storeCount,
			"count":      len(contactWayIds),
		},
	}

	change := qmgo.Change{
		Update:    updater,
		Upsert:    true,
		ReturnNew: true,
	}
	return extension.DBRepository.FindAndApply(ctx, C_DRAINAGE_QRCODE, selector, nil, change, d)
}
