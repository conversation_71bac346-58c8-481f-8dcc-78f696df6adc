package store_warehouse

import (
	"context"
	"time"

	"mairpc/core/extension"
	"mairpc/core/extension/bson"
)

const (
	C_STORE_STOCK_PROVIDER = "ec.storeStockProvider"
)

type StoreStockProvider struct {
	Id        bson.ObjectId `bson:"_id,omitempty"`
	AccountId bson.ObjectId `bson:"accountId"`
	Name      string        `bson:"name"`
	CreatedAt time.Time     `bson:"createdAt"`
	UpdatedAt time.Time     `bson:"updatedAt"`
	IsDeleted bool          `bson:"isDeleted"`
}

var CStoreStockProvider = &StoreStockProvider{}

// 分页查询
func (*StoreStockProvider) FindByPagination(ctx context.Context, pageCondition extension.PagingCondition) ([]StoreStockProvider, int) {
	var providers []StoreStockProvider
	totalCount, _ := extension.DBRepository.FindByPagination(ctx, C_STORE_STOCK_PROVIDER, pageCondition, &providers)
	return providers, totalCount
}

// 条件查询全部
func (*StoreStockProvider) FindAllByCondition(ctx context.Context, condition bson.M) ([]StoreStockProvider, error) {
	var providers []StoreStockProvider
	err := extension.DBRepository.FindAll(ctx, C_STORE_STOCK_PROVIDER, condition, nil, 0, &providers)
	return providers, err
}

// 创建供应商
func (s *StoreStockProvider) Create(ctx context.Context) error {
	s.Id = bson.NewObjectId()
	s.CreatedAt = time.Now()
	s.UpdatedAt = s.CreatedAt
	s.IsDeleted = false
	_, err := extension.DBRepository.Insert(ctx, C_STORE_STOCK_PROVIDER, s)
	return err
}

// 更新供应商（只更新 name 和 updatedAt 字段）
func (s *StoreStockProvider) UpdateName(ctx context.Context, name string) error {
	update := bson.M{
		"$set": bson.M{
			"name":      name,
			"updatedAt": time.Now(),
		},
	}
	condition := bson.M{"_id": s.Id}
	return extension.DBRepository.UpdateOne(ctx, C_STORE_STOCK_PROVIDER, condition, update)
}

func (*StoreStockProvider) DeleteById(ctx context.Context, id bson.ObjectId) error {
	condition := Common.GenDefaultConditionById(ctx, id)
	update := bson.M{
		"$set": bson.M{
			"isDeleted": true,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_STORE_STOCK_PROVIDER, condition, update)
}

// FindByCondition 支持分页和条件查询，返回总数和结果列表
func (s *StoreStockProvider) FindByCondition(ctx context.Context, pageCondition extension.PagingCondition) (int, []StoreStockProvider, error) {
	var providers []StoreStockProvider
	total, err := extension.DBRepository.FindByPagination(ctx, C_STORE_STOCK_PROVIDER, pageCondition, &providers)
	return total, providers, err
}

func (s *StoreStockProvider) FindByName(ctx context.Context, name string) (*StoreStockProvider, error) {
	var provider StoreStockProvider
	condition := Common.GenDefaultCondition(ctx)
	condition["name"] = name
	err := extension.DBRepository.FindOne(ctx, C_STORE_STOCK_PROVIDER, condition, &provider)
	if err != nil {
		return nil, nil
	}
	return &provider, nil
}
