package store_warehouse

import (
	"context"
	"fmt"
	"time"

	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	"mairpc/service/share/util"
)

const (
	C_STORE_STOCK_BILL = "ec.storeStockBill"

	TYPE_STOCK_BILL_IN  = "in"
	TYPE_STOCK_BILL_OUT = "out"

	OPERATOR_STOCK_BILL_ALL                = "all"
	OPERATOR_STOCK_BILL_REFUND             = "refund"             // 退货入库
	OPERATOR_STOCK_BILL_SHIP               = "ship"               // 销售出库
	OPERATOR_STOCK_BILL_PURCHASE_IN        = "purchase_in"        // 采购入库
	OPERATOR_STOCK_BILL_ALLOCATE_IN        = "allocate_in"        // 调拨入库
	OPERATOR_STOCK_BILL_ALLOCATE_OUT       = "allocate_out"       // 调拨出库
	OPERATOR_STOCK_BILL_REFUND_TO_SUPPLIER = "refund_to_supplier" // 退货给供应商
	OPERATOR_STOCK_BILL_OTHERS             = "others"             // 其他原因
	OPERATOR_STOCK_BILL_MANUAL             = "manual"             // 手动调整

	PRE_STOCK_BILL_IN  = "IB" // 入库编号前缀
	PRE_STOCK_BILL_OUT = "OB" // 出库编号前缀
)

type StoreStockBill struct {
	Id             bson.ObjectId `bson:"_id,omitempty"`
	AccountId      bson.ObjectId `bson:"accountId"`
	Store          Store         `bson:"store"`
	Type           string        `bson:"type"`
	Number         string        `bson:"number"`
	ChangedAt      time.Time     `bson:"changedAt"`
	BusinessNumber string        `bson:"businessNumber"`
	ProductId      bson.ObjectId `bson:"productId"`
	Sku            string        `bson:"sku"`
	Operator       string        `bson:"operator"`
	Count          uint64        `bson:"count"`
	Price          uint64        `bson:"price,omitempty"`
	Remarks        string        `bson:"remarks"`
	AvailableStock int           `bson:"availableStock"`
	CreatedBy      bson.ObjectId `bson:"createdBy,omitempty"`
	CreatedAt      time.Time     `bson:"createdAt"`
	UpdatedBy      bson.ObjectId `bson:"updatedBy,omitempty"`
	UpdatedAt      time.Time     `bson:"updatedAt"`
	IsDeleted      bool          `bson:"isDeleted"`
	Organization   string        `bson:"organization"`
	OppositeStore  Store         `bson:"oppositeStore,omitempty"`
	Provider       Provider      `bson:"provider,omitempty"` // 供应商
}

type Provider struct {
	Id   bson.ObjectId `bson:"id"`
	Name string        `bson:"name"`
}

type Store struct {
	Id   bson.ObjectId `bson:"id"`
	Code string        `bson:"code"`
	Name string        `bson:"name"`
}

var CStoreStockBill = &StoreStockBill{}

func (s *StoreStockBill) BatchInsert(ctx context.Context, models []StoreStockBill) error {
	accountId := util.GetAccountIdAsObjectId(ctx)
	userId := bson.ObjectId("")
	tempUserId := core_util.GetUserId(ctx)
	if bson.IsObjectIdHex(tempUserId) {
		userId = bson.ObjectIdHex(tempUserId)
	}
	docs := []interface{}{}
	for _, m := range models {
		m.Id = bson.NewObjectId()
		m.AccountId = accountId
		m.CreatedAt, m.UpdatedAt = time.Now(), time.Now()
		if m.ChangedAt.IsZero() {
			m.ChangedAt = time.Now()
		}
		m.CreatedBy, m.UpdatedBy = userId, userId
		m.IsDeleted = false
		docs = append(docs, m)
	}
	_, err := extension.DBRepository.Insert(ctx, C_STORE_STOCK_BILL, docs...)
	return err
}

func (*StoreStockBill) FindByPagination(ctx context.Context, pageCondition extension.PagingCondition) ([]StoreStockBill, int) {
	var stockBills []StoreStockBill
	totalCount, _ := extension.DBRepository.FindByPagination(ctx, C_STORE_STOCK_BILL, pageCondition, &stockBills)
	return stockBills, totalCount
}

func GenStockBillNumber(StockBillType string) string {
	preString := ""
	switch StockBillType {
	case TYPE_STOCK_BILL_IN:
		preString = PRE_STOCK_BILL_IN
	case TYPE_STOCK_BILL_OUT:
		preString = PRE_STOCK_BILL_OUT
	}
	randomNumber := core_util.GenRandomNumber(14, 10)
	return fmt.Sprintf("%s%s", preString, randomNumber)
}

func (*StoreStockBill) FindAllByCondition(ctx context.Context, condition bson.M) ([]StoreStockBill, error) {
	var stockBills []StoreStockBill
	err := extension.DBRepository.FindAll(ctx, C_STORE_STOCK_BILL, condition, nil, 0, &stockBills)
	return stockBills, err
}
