package distribution

import (
	"fmt"
	core_component "mairpc/core/component"
	"time"

	"github.com/spf13/cast"

	"mairpc/core/extension"
	core_util "mairpc/core/util"
	store_mode "mairpc/service/ec/model/store"
	"mairpc/service/share/component"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"

	"golang.org/x/net/context"
)

const (
	C_PROMOTER = "ec.promoter"

	PROMOTER_TYPE_MEMBER = "member" // 大众分销员
	PROMOTER_TYPE_STAFF  = "staff"  // 导购分销员

	MEMBER_PROMOTER_PROPERTY_ID = "ecPromoter" // 大众分销员客户属性 propertyId

	PROMOTER_STATUS_APPROVED   = "approved"   // 审核通过
	PROMOTER_STATUS_UNAPPROVED = "unapproved" // 拒绝通过
	PROMOTER_STATUS_PENDING    = "pending"    // 待审核

	// 分账接收方类型
	RECEIVER_TYPE_MERCHANT = "MERCHANT_ID" // 微信商户
	RECEIVER_TYPE_OPENID   = "OPENID"      // 微信用户

	// 更新分销员 count 字段加锁
	LOCK_UPDATE_PROMOTER_COUNT          = "%s:ec:distribution:update-promoter-count:%s"
	LOCK_UPDATE_PROMOTER_COUNT_DURATION = 30

	// 分销员与薪行家签约状态
	SIGN_STATUS_PENDING = "pending" // 签约中
	SIGN_STATUS_FAILED  = "failed"  // 签约失败
	SIGN_STATUS_SUCCESS = "success" // 签约成功
)

var CPromoter = &Promoter{}

type Promoter struct {
	Id                              bson.ObjectId            `bson:"_id,omitempty"`
	AccountId                       bson.ObjectId            `bson:"accountId"`
	Type                            string                   `bson:"type"`
	StaffId                         bson.ObjectId            `bson:"staffId,omitempty"`
	MemberId                        bson.ObjectId            `bson:"memberId,omitempty"`
	MemberBoundStaff                MemberBoundStaff         `bson:"memberBoundStaff,omitempty"`
	Avatar                          string                   `bson:"avatar"`
	Name                            string                   `bson:"name"`
	Phone                           string                   `bson:"phone"`
	OpenId                          string                   `bson:"openId"`
	ReceiverType                    string                   `bson:"receiverType"`
	Merchant                        MerchantInfo             `bson:"merchant"`
	BindingMembers                  int64                    `bson:"bindingMembers"`
	TotalOrders                     int64                    `bson:"totalOrders"`
	TotalPromoteOrders              int64                    `bson:"totalPromoteOrders"`
	TotalCommissionOrders           int64                    `bson:"totalCommissionOrders"`
	TotalAmount                     int64                    `bson:"totalAmount"`
	TotalMembers                    int64                    `bson:"totalMembers"`
	TotalProducts                   int64                    `bson:"totalProducts"`
	TotalPromoteAmount              int64                    `bson:"totalPromoteAmount"`
	TotalProfitAmount               int64                    `bson:"totalProfitAmount"`
	WaitProfitAmount                int64                    `bson:"waitProfitAmount"`
	Frozen                          bool                     `bson:"frozen"` // 是否冻结
	FrozenAt                        time.Time                `bson:"frozenAt,omitempty"`
	FrozenRemark                    string                   `bson:"frozenRemark,omitempty"`
	IsNewUserDisallowed             bool                     `bson:"isNewUserDisallowed"` // 是否禁止拉新
	Count                           map[string]PromoterCount `bson:"count,omitempty"`
	CreatedAt                       time.Time                `bson:"createdAt"`
	UpdatedAt                       time.Time                `bson:"updatedAt"`
	IsDeleted                       bool                     `bson:"isDeleted"`
	Status                          string                   `bson:"status,omitempty"` // 当前 status 字段不确定是否已迁移，因此需要重新迁移，status 为空视为审核已通过，需通过 GetStatus 方法获取审核状态
	ApproverId                      bson.ObjectId            `bson:"approverId,omitempty"`
	ApprovedAt                      time.Time                `bson:"approvedAt,omitempty"`
	DistProductTotalAmount          int64                    `bson:"distProductTotalAmount"`     // 累计分销商品销售额，已付款订单金额中分销商品的金额，分销商品退款时会减少
	DistProductPromoteAmount        int64                    `bson:"distProductPromoteAmount"`   // 累计分销商品推广金额，已付款订单金额中分销商品的金额，退款不减少
	SignStatus                      string                   `bson:"signStatus,omitempty"`       // 分销员和薪行家是否已签约，pending 签约中，failed 签约失败，success 签约成功
	SignErrMsg                      string                   `bson:"signErrMsg,omitempty"`       // 签约失败原因
	SignAt                          time.Time                `bson:"signAt,omitempty"`           // 分销员和薪行家签约时间
	IdCardNumber                    string                   `bson:"idCardNumber,omitempty"`     // 身份证号
	BankAccount                     string                   `bson:"bankAccount,omitempty"`      // 银行卡号
	IdCardA                         string                   `bson:"idCardA,omitempty"`          // 身份证人像面可访问图片 url
	IdCardB                         string                   `bson:"idCardB,omitempty"`          // 身份证国徽面可访问图片 url
	ParentId                        bson.ObjectId            `bson:"parentId,omitempty"`         // 上级分销员
	HistoryParentIds                []bson.ObjectId          `bson:"historyParentIds,omitempty"` // 上级分销员历史
	Areas                           []string                 `bson:"areas,omitempty"`            // 负责区域
	IsPreCreated                    bool                     `bson:"isPreCreated"`               // 是否是预创建
	ParentStats                     []PromoterStat           `bson:"parentStats,omitempty"`
	IsBindMemberByActivationAllowed bool                     `bson:"isBindMemberByActivationAllowed,omitempty"`
}

type PromoterStat struct {
	PromoterId               bson.ObjectId `bson:"promoterId"`
	TotalOrders              int64         `bson:"totalOrders"`
	TotalPromoteOrders       int64         `bson:"totalPromoteOrders"`
	TotalCommissionOrders    int64         `bson:"totalCommissionOrders"`
	TotalAmount              int64         `bson:"totalAmount"`
	TotalMembers             int64         `bson:"totalMembers"`
	TotalProducts            int64         `bson:"totalProducts"`
	TotalPromoteAmount       int64         `bson:"totalPromoteAmount"`
	DistProductTotalAmount   int64         `bson:"distProductTotalAmount"`
	DistProductPromoteAmount int64         `bson:"distProductPromoteAmount"`
}

type PromoterCount struct {
	Member                   int64 `bson:"member"`                   // 月累计推广客户数，该月新增的客户数，只有首次关联分销员时增加，客户和分销员解绑后重新绑定不会增加计数
	Order                    int64 `bson:"order"`                    // 月累计完成订单数
	PromoteOrder             int64 `bson:"promoteOrder"`             // 月累计推广订单数
	LostMember               int64 `bson:"lostMember"`               // 客户流失数，该月与导购解绑的客户数，同一客户在同一个月多次绑定和解绑会分别使该计数减少和增加
	BoundMember              int64 `bson:"boundMember"`              // 该月与客户绑定的客户数，相同客户在不同的月份与导购绑定都会使该月的计数增加
	BindingMembers           int64 `bson:"bindingMembers"`           // 该月时间范围内，和分销员处于绑定中的客户总数，对于当月来说就是实时客户数
	TotalAmount              int64 `bson:"totalAmount"`              // 月累计销售额，已付款订单金额，退款会减少
	RefundOrder              int64 `bson:"refundOrder"`              // 月累计退款订单数
	Amount                   int64 `bson:"amount"`                   // 月累计佣金
	Income                   int64 `bson:"income"`                   // 该月税后收入
	Tax                      int64 `bson:"tax"`                      // 该月扣除个税
	PromoteAmount            int64 `bson:"promoteAmount"`            // 月累计推广金额，已付款订单金额，退款不减少
	Product                  int64 `bson:"product"`                  // 月累计商品销量，完成订单的分销商品数量
	HasProfited              bool  `bson:"hasProfited"`              // 当月是否已结算，用于导购分销月结
	DistProductTotalAmount   int64 `bson:"distProductTotalAmount"`   // 月累计分销商品销售额，已付款订单金额中分销商品的金额，分销商品退款时会减少
	DistProductPromoteAmount int64 `bson:"distProductPromoteAmount"` // 月累计分销商品推广金额，已付款订单金额中分销商品的金额，退款不减少

	// 以下字段仅用于增加计数时做参数传递
	DistributionAmount int64 `bson:"-"` // 订单分销金额
	HasDistProduct     bool  `bson:"-"` // 订单是否存在分销商品
}

type MerchantInfo struct {
	MerchantId   string `bson:"merchantId"`
	MerchantName string `bson:"merchantName"`
}

type MemberBoundStaff struct {
	StaffId            bson.ObjectId `bson:"staffId,omitempty"`
	StaffName          string        `bson:"staffName"`
	StaffPhone         string        `bson:"staffPhone"`
	OldBoundAt         time.Time     `bson:"boudnAt,omitempty"` // 迁移后需删除此字段
	BoundAt            time.Time     `bson:"boundAt,omitempty"`
	LastStaffId        bson.ObjectId `bson:"lastStaffId,omitempty"`
	LastStaffName      string        `bson:"lastStaffName"`
	LastStaffPhone     string        `bson:"lastStaffPhone"`
	LastStaffUnboundAt time.Time     `bson:"lastStaffUnboundAt,omitempty"`
}

func (p *Promoter) Create(ctx context.Context) error {
	_, err := extension.DBRepository.Insert(ctx, C_PROMOTER, p)
	if err != nil {
		return err
	}

	p.SendCreatePromoterEvent(ctx)

	return nil
}

func (p *Promoter) UpsertByDefault(ctx context.Context, selector bson.M) error {
	if p.ReceiverType == "" {
		p.ReceiverType = RECEIVER_TYPE_OPENID
	}
	if !p.Id.Valid() {
		p.Id = bson.NewObjectId()
	}
	if p.CreatedAt.IsZero() {
		p.CreatedAt = time.Now()
	}
	p.UpdatedAt = time.Now()
	p.AccountId = util.GetAccountIdAsObjectId(ctx)
	setter := bson.M{
		"type":             p.Type,
		"accountId":        util.GetAccountIdAsObjectId(ctx),
		"openId":           p.OpenId,
		"avatar":           p.Avatar,
		"name":             p.Name,
		"phone":            p.Phone,
		"receiverType":     p.ReceiverType,
		"frozen":           false,
		"status":           p.Status,
		"updatedAt":        time.Now(),
		"memberBoundStaff": p.MemberBoundStaff,
		"idCardNumber":     p.IdCardNumber,
		"bankAccount":      p.BankAccount,
		"idCardA":          p.IdCardA,
		"idCardB":          p.IdCardB,
	}
	if p.StaffId.Valid() {
		setter["staffId"] = p.StaffId
	}
	if p.MemberId.Valid() {
		setter["memberId"] = p.MemberId
	}
	updater := bson.M{
		"$set": setter,
		"$setOnInsert": bson.M{
			"_id":       p.Id,
			"createdAt": p.CreatedAt,
		},
	}
	upsertId, err := extension.DBRepository.Upsert(ctx, C_PROMOTER, selector, updater)
	if err != nil {
		return err
	}

	if upsertId != nil {
		p.SendCreatePromoterEvent(ctx)
		return nil
	}
	return nil
}

func (p *Promoter) SendCreatePromoterEvent(ctx context.Context) {
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_CREATE_PROMOTER + ":" + p.Id.Hex(),
		AccountId:  p.AccountId.Hex(),
		MemberId:   p.MemberId.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_CREATE_PROMOTER,
		CreateTime: p.CreatedAt.UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"promoterId":   p.MemberId.Hex(),
			"promoterType": p.Type,
		},
	}
	if p.MemberId == "" {
		eventBody.ClientId = p.Id.Hex()
	}

	if p.StaffId.Hex() != "" {
		eventBody.EventProperties["storeId"] = GetStaffStoreId(ctx, p.StaffId).Hex()
		eventBody.EventProperties["staffId"] = p.StaffId.Hex()
	}

	eventBody.Send(ctx)
}

func (*Promoter) GetById(ctx context.Context, id bson.ObjectId) (*Promoter, error) {
	selector := bson.M{
		"_id":       id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}

	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, selector, promoter)
	if err != nil {
		return nil, err
	}

	return promoter, nil
}

func (*Promoter) GetByIdWithoutIsDeleted(ctx context.Context, id bson.ObjectId) (*Promoter, error) {
	selector := bson.M{
		"_id":       id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}

	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, selector, promoter)
	if err != nil {
		return nil, err
	}

	return promoter, nil
}

func (*Promoter) GetByIdContainDeleted(ctx context.Context, id bson.ObjectId) (*Promoter, error) {
	selector := bson.M{
		"_id":       id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}

	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, selector, promoter)
	if err != nil {
		return nil, err
	}

	return promoter, nil
}

func (*Promoter) GetByIds(ctx context.Context, ids []bson.ObjectId) ([]Promoter, error) {
	selector := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}

	promoters := []Promoter{}
	err := extension.DBRepository.FindAll(ctx, C_PROMOTER, selector, []string{}, 0, &promoters)
	if err != nil {
		return nil, err
	}

	return promoters, nil
}

func (p *Promoter) GetMemberPromoterAllByPage(ctx context.Context, pageIndex, pageSize int) ([]Promoter, error) {
	promoters := []Promoter{}

	selector := Common.GenDefaultCondition(ctx)
	selector["type"] = DISTRIBUTION_SETTING_TYPE_MEMBER
	Common.GetAllByPaginationWithoutCount(ctx, selector, uint32(pageIndex), uint32(pageSize), []string{"_id"}, C_PROMOTER, &promoters)

	return promoters, nil
}

func (*Promoter) GetByMemberId(ctx context.Context, memberId bson.ObjectId) (*Promoter, error) {
	selector := bson.M{
		"memberId":  memberId,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, selector, promoter)
	if err != nil {
		return nil, err
	}
	return promoter, nil
}

func (*Promoter) GetUnFrozenByMemberId(ctx context.Context, memberId bson.ObjectId) (*Promoter, error) {
	selector := bson.M{
		"memberId":  memberId,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"frozen":    false,
	}

	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, selector, promoter)
	if err != nil {
		return nil, err
	}

	// 分销员必须已通过审核才可以使用，因此未通过审核返回 nil
	if promoter.GetStatus() != PROMOTER_STATUS_APPROVED {
		return nil, bson.ErrNotFound
	}

	return promoter, nil
}

func (*Promoter) GetPromoterByStaffId(ctx context.Context, staffId bson.ObjectId) (*Promoter, error) {
	selector := bson.M{
		"staffId":   staffId,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, selector, promoter)
	if err != nil {
		return nil, err
	}
	return promoter, nil
}

func (*Promoter) GetUnfrozenPromoterByStaffId(ctx context.Context, staffId bson.ObjectId) (*Promoter, error) {
	selector := bson.M{
		"staffId":   staffId,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
		"frozen":    false,
	}
	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, selector, promoter)
	if err != nil {
		return nil, err
	}
	return promoter, nil
}

func (*Promoter) GetByMemberIdAndType(ctx context.Context, memberId bson.ObjectId, pType string) (*Promoter, error) {
	selector := bson.M{
		"memberId":  memberId,
		"type":      pType,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}

	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, selector, promoter)
	if err != nil {
		return nil, err
	}

	return promoter, nil
}

func (*Promoter) GetByMemberIds(ctx context.Context, memberIds []bson.ObjectId) ([]Promoter, error) {
	selector := bson.M{
		"memberId": bson.M{
			"$in": memberIds,
		},
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}

	promoters := []Promoter{}
	err := extension.DBRepository.FindAll(ctx, C_PROMOTER, selector, []string{}, 0, &promoters)
	if err != nil {
		return nil, err
	}

	return promoters, nil
}

func (*Promoter) GetByDefaultCondition(ctx context.Context, condition bson.M) (*Promoter, error) {
	condition["accountId"] = util.GetAccountIdAsObjectId(ctx)
	condition["isDeleted"] = false
	util.FormatConditionContainedOr(condition)

	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, condition, promoter)
	if err != nil {
		return nil, err
	}

	return promoter, nil
}

func (*Promoter) GetByCondition(ctx context.Context, condition bson.M) (*Promoter, error) {
	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, condition, promoter)
	if err != nil {
		return nil, err
	}
	return promoter, nil
}

func (p *Promoter) UpdateFrozenStatus(ctx context.Context) error {
	selector := bson.M{
		"_id":       p.Id,
		"accountId": p.AccountId,
		"isDeleted": false,
	}

	setter := bson.M{
		"updatedAt":           time.Now(),
		"frozen":              p.Frozen,
		"frozenAt":            p.FrozenAt,
		"frozenRemark":        p.FrozenRemark,
		"isNewUserDisallowed": p.IsNewUserDisallowed,
	}

	updater := bson.M{
		"$set": setter,
	}

	err := extension.DBRepository.UpdateOne(ctx, C_PROMOTER, selector, updater)
	if err != nil {
		return err
	}
	p.SendUpdateStaffPromoterEvent(ctx)
	return nil
}

func (p *Promoter) UpdateSignStatus(ctx context.Context) error {
	selector := bson.M{
		"_id":       p.Id,
		"accountId": p.AccountId,
		"isDeleted": false,
	}

	setter := bson.M{
		"updatedAt":  time.Now(),
		"signStatus": p.SignStatus,
		"signErrMsg": p.SignErrMsg,
		"signAt":     time.Now(),
	}

	updater := bson.M{
		"$set": setter,
	}

	err := extension.DBRepository.UpdateOne(ctx, C_PROMOTER, selector, updater)
	if err != nil {
		return err
	}
	return nil
}

func (p *Promoter) UpdateIsNewUserDisallowedStatus(ctx context.Context) error {
	selector := bson.M{
		"_id":       p.Id,
		"accountId": p.AccountId,
		"isDeleted": false,
	}
	updater := bson.M{
		"$set": bson.M{
			"updatedAt":           time.Now(),
			"isNewUserDisallowed": p.IsNewUserDisallowed,
		},
	}

	err := extension.DBRepository.UpdateOne(ctx, C_PROMOTER, selector, updater)
	if err != nil {
		return err
	}
	return nil
}

func (p *Promoter) UpdateAllByCondition(ctx context.Context, selector, updater bson.M) error {

	_, err := extension.DBRepository.UpdateAll(ctx, C_PROMOTER, selector, updater)

	return err
}

func (p *Promoter) UpdateMemberBoundStaff(ctx context.Context, memberBoundStaff *MemberBoundStaff) error {
	if p.Type != PROMOTER_TYPE_MEMBER {
		return nil
	}
	if memberBoundStaff != nil && p.MemberBoundStaff.StaffId.Valid() && p.MemberBoundStaff.StaffId == memberBoundStaff.StaffId {
		return nil
	}
	selector := bson.M{
		"_id": p.Id,
	}
	if memberBoundStaff == nil {
		// 解绑之前绑定的导购
		if p.MemberBoundStaff.StaffId.Valid() {
			p.MemberBoundStaff = MemberBoundStaff{
				LastStaffId:        p.MemberBoundStaff.StaffId,
				LastStaffUnboundAt: time.Now(),
			}
		}
	} else {
		// 解绑之前绑定的导购
		if p.MemberBoundStaff.StaffId.Valid() {
			memberBoundStaff.LastStaffId = p.MemberBoundStaff.StaffId
			memberBoundStaff.LastStaffUnboundAt = memberBoundStaff.BoundAt
		}
		p.MemberBoundStaff = *memberBoundStaff
	}
	updater := bson.M{
		"$set": bson.M{
			"updatedAt":        time.Now(),
			"memberBoundStaff": p.MemberBoundStaff,
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_PROMOTER, selector, updater)
}

func (p *Promoter) UpdateInfo(ctx context.Context) error {
	selector := bson.M{
		"_id":       p.Id,
		"accountId": p.AccountId,
		"isDeleted": false,
	}

	setter := bson.M{
		"updatedAt":                       time.Now(),
		"receiverType":                    p.ReceiverType,
		"merchant":                        p.Merchant,
		"areas":                           p.Areas,
		"memberId":                        p.MemberId,
		"avatar":                          p.Avatar,
		"openId":                          p.OpenId,
		"isBindMemberByActivationAllowed": p.IsBindMemberByActivationAllowed,
	}

	if p.Name != "" {
		setter["name"] = p.Name
	}
	if p.Phone != "" {
		setter["phone"] = p.Phone
	}

	if p.StaffId != "" {
		setter["staffId"] = p.StaffId
	}

	if p.Status != "" {
		setter["status"] = p.Status
	}

	if p.IdCardNumber != "" {
		setter["idCardNumber"] = p.IdCardNumber
	}

	if p.BankAccount != "" {
		setter["bankAccount"] = p.BankAccount
	}

	if p.IdCardA != "" {
		setter["idCardA"] = p.IdCardA
	}

	if p.IdCardB != "" {
		setter["idCardB"] = p.IdCardB
	}

	updater := bson.M{
		"$set": setter,
	}

	return extension.DBRepository.UpdateOne(ctx, C_PROMOTER, selector, updater)
}

func (*Promoter) IncPromoterFields(ctx context.Context, id bson.ObjectId, increaser bson.M) error {
	promoter, err := CPromoter.GetById(ctx, id)
	if err != nil {
		return err
	}
	selector := bson.M{
		"_id":       id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	setter := bson.M{
		"updatedAt": time.Now(),
	}
	if promoter.ParentId.Valid() {
		parentStat := PromoterStat{}
		index := -1
		for i, stat := range promoter.ParentStats {
			if stat.PromoterId == promoter.ParentId {
				index = i
				parentStat = stat
				break
			}
		}
		parentStat.PromoterId = promoter.ParentId
		parentStat.TotalAmount += cast.ToInt64(increaser["totalAmount"])
		parentStat.TotalMembers += cast.ToInt64(increaser["totalMembers"])
		if index >= 0 {
			promoter.ParentStats[index] = parentStat
		} else {
			promoter.ParentStats = append(promoter.ParentStats, parentStat)
		}
		setter["parentStats"] = promoter.ParentStats
	}
	updater := bson.M{
		"$set": setter,
		"$inc": increaser,
	}
	return extension.DBRepository.UpdateOne(ctx, C_PROMOTER, selector, updater)
}

func (*Promoter) IncTotalAmount(ctx context.Context, id bson.ObjectId, count int) error {
	return CPromoter.IncPromoterFields(ctx, id, bson.M{
		"totalAmount": count,
	})
}

func (*Promoter) IncTotalMembers(ctx context.Context, id bson.ObjectId, count int) error {
	return CPromoter.IncPromoterFields(ctx, id, bson.M{
		"totalMembers": count,
	})
}

func (p *Promoter) IncProfitAmount(ctx context.Context, totalProfitAmount, waitProfitAmount int64) error {
	return CPromoter.IncPromoterFields(ctx, p.Id, bson.M{
		"totalProfitAmount": totalProfitAmount,
		"waitProfitAmount":  waitProfitAmount,
	})
}

func (*Promoter) Iterate(ctx context.Context, selector bson.M, sorter []string) (extension.IterWrapper, error) {
	it, err := extension.DBRepository.Iterate(ctx, C_PROMOTER, selector, sorter)
	return it, err
}

func (p *Promoter) RemoveById(ctx context.Context) error {
	selector := bson.M{
		"_id":       p.Id,
		"accountId": util.GetAccountIdAsObjectId(ctx),
	}

	return extension.DBRepository.RemoveOne(ctx, C_PROMOTER, selector)
}

func (*Promoter) BatchUpdateMemberId(ctx context.Context, oldMemberIds []bson.ObjectId, newMemberId bson.ObjectId) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["memberId"] = bson.M{
		"$in": oldMemberIds,
	}

	updator := bson.M{
		"$set": bson.M{
			"memberId":  newMemberId,
			"updatedAt": time.Now(),
		},
	}

	_, err := extension.DBRepository.UpdateAll(ctx, C_PROMOTER, selector, updator)
	return err
}

func (p *Promoter) UpdateType(ctx context.Context) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = p.Id

	updator := bson.M{
		"$set": bson.M{
			"type":      p.Type,
			"updatedAt": time.Now(),
		},
	}

	_, err := extension.DBRepository.UpdateAll(ctx, C_PROMOTER, selector, updator)
	return err
}

func (p *Promoter) UpdateOpenId(ctx context.Context, openId string) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = p.Id

	updator := bson.M{
		"$set": bson.M{
			"openId":    openId,
			"updatedAt": time.Now(),
		},
	}

	err := extension.DBRepository.UpdateOne(ctx, C_PROMOTER, selector, updator)
	return err
}

func GetStaffStoreId(ctx context.Context, staffId bson.ObjectId) bson.ObjectId {
	staff, err := store_mode.CStaff.GetById(ctx, staffId)
	if err != nil {
		return bson.ObjectId("")
	}

	if staff.CurrentStoreId != "" {
		return staff.CurrentStoreId
	}

	if len(staff.StoreIds) > 0 {
		return staff.StoreIds[0]
	}

	return bson.ObjectId("")
}

func (p *Promoter) Delete(ctx context.Context) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = p.Id
	selector["memberId"] = p.MemberId

	updator := bson.M{
		"$set": bson.M{
			"isDeleted": true,
			"updatedAt": time.Now(),
		},
	}

	err := extension.DBRepository.UpdateOne(ctx, C_PROMOTER, selector, updator)
	if err != nil {
		return err
	}
	return CPromoterMember.DeleteByPromoterId(ctx, p.Id)
}

func (p *Promoter) UpdateCount(ctx context.Context) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = p.Id
	selector["memberId"] = p.MemberId

	updator := bson.M{
		"$set": bson.M{
			"count":     p.Count,
			"updatedAt": time.Now(),
		},
	}

	return extension.DBRepository.UpdateOne(ctx, C_PROMOTER, selector, updator)
}

func (p *Promoter) UpdateStatsField(ctx context.Context) error {
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = p.Id

	updator := bson.M{
		"$set": bson.M{
			"count":                    p.Count,
			"distProductPromoteAmount": p.DistProductPromoteAmount,
			"distProductTotalAmount":   p.DistProductTotalAmount,
			"totalAmount":              p.TotalAmount,
			"totalPromoteAmount":       p.TotalPromoteAmount,
			"totalOrders":              p.TotalOrders,
			"totalPromoteOrders":       p.TotalPromoteOrders,
			"totalCommissionOrders":    p.TotalCommissionOrders,
			"totalProfitAmount":        p.TotalProfitAmount,
			"waitProfitAmount":         p.WaitProfitAmount,
			"parentStats":              p.ParentStats,
			"updatedAt":                time.Now(),
		},
	}

	return extension.DBRepository.UpdateOne(ctx, C_PROMOTER, selector, updator)
}

// 实时更新当月分销员的数据统计
// countKey 为指定的月份键值，如 200601
// TODO: 通过订阅不同的事件处理不同的场景
func (p *Promoter) UpdatePromoterCountField(ctx context.Context, promoterCount *PromoterCount, countKey string) error {
	countValue := PromoterCount{}
	if len(p.Count) != 0 {
		if v, ok := p.Count[countKey]; ok {
			core_util.CopyFieldsRFC3339(v, &countValue)
		}
	} else {
		p.Count = make(map[string]PromoterCount)
	}

	countValue.TotalAmount += promoterCount.TotalAmount
	if countValue.TotalAmount < 0 {
		countValue.TotalAmount = 0
		promoterCount.TotalAmount = 0
	}
	countValue.PromoteAmount += promoterCount.PromoteAmount
	if countValue.PromoteAmount < 0 {
		countValue.PromoteAmount = 0
		promoterCount.PromoteAmount = 0
	}
	countValue.PromoteOrder += promoterCount.PromoteOrder
	if countValue.PromoteOrder < 0 {
		countValue.PromoteOrder = 0
		promoterCount.PromoteOrder = 0
	}
	countValue.Product += promoterCount.Product
	if countValue.Product < 0 {
		countValue.Product = 0
		promoterCount.Product = 0
	}
	countValue.BindingMembers += promoterCount.BindingMembers
	if countValue.BindingMembers < 0 {
		countValue.BindingMembers = 0
		promoterCount.BindingMembers = 0
	}
	countValue.RefundOrder += promoterCount.RefundOrder
	if countValue.RefundOrder < 0 {
		countValue.RefundOrder = 0
		promoterCount.RefundOrder = 0
	}
	countValue.Order += promoterCount.Order
	if countValue.Order < 0 {
		countValue.Order = 0
		promoterCount.Order = 0
	}
	countValue.Amount += promoterCount.Amount
	if countValue.Amount < 0 {
		countValue.Amount = 0
		promoterCount.Amount = 0
	}
	countValue.Member += promoterCount.Member
	if countValue.Member < 0 {
		countValue.Member = 0
		promoterCount.Member = 0
	}
	countValue.LostMember += promoterCount.LostMember
	if countValue.LostMember < 0 {
		countValue.LostMember = 0
		promoterCount.LostMember = 0
	}
	countValue.BoundMember += promoterCount.BoundMember
	if countValue.BoundMember < 0 {
		countValue.BoundMember = 0
		promoterCount.BoundMember = 0
	}
	countValue.Income += promoterCount.Income
	if countValue.Income < 0 {
		countValue.Income = 0
		promoterCount.Income = 0
	}
	countValue.Tax += promoterCount.Tax
	if countValue.Tax < 0 {
		countValue.Tax = 0
		promoterCount.Tax = 0
	}
	if promoterCount.HasProfited {
		countValue.HasProfited = promoterCount.HasProfited
	}
	countValue.DistProductTotalAmount += promoterCount.DistProductTotalAmount
	if countValue.DistProductTotalAmount < 0 {
		countValue.DistProductTotalAmount = 0
		promoterCount.DistProductTotalAmount = 0
	}
	countValue.DistProductPromoteAmount += promoterCount.DistProductPromoteAmount
	if countValue.DistProductPromoteAmount < 0 {
		countValue.DistProductPromoteAmount = 0
		promoterCount.DistProductPromoteAmount = 0
	}

	p.TotalPromoteAmount += promoterCount.PromoteAmount
	p.TotalPromoteOrders += promoterCount.PromoteOrder
	p.TotalAmount += promoterCount.TotalAmount
	p.TotalOrders += promoterCount.Order
	p.TotalProducts += promoterCount.Product
	p.DistProductTotalAmount += promoterCount.DistProductTotalAmount
	p.DistProductPromoteAmount += promoterCount.DistProductPromoteAmount
	if util.GetMonthCountKey(time.Now(), 0) == countKey {
		p.BindingMembers += promoterCount.BindingMembers
	}

	increaser := bson.M{
		"totalPromoteAmount":       promoterCount.PromoteAmount,
		"totalProducts":            promoterCount.Product,
		"totalOrders":              promoterCount.Order,
		"totalAmount":              promoterCount.TotalAmount,
		"bindingMembers":           promoterCount.BindingMembers,
		"totalPromoteOrders":       promoterCount.PromoteOrder,
		"distProductTotalAmount":   promoterCount.DistProductTotalAmount,
		"distProductPromoteAmount": promoterCount.DistProductPromoteAmount,
	}
	// 有分销金额则需要增加总分账金额和待分账金额
	//
	// 只处理大于 0 的情况，分账成功或退款后通过 IncProfitAmount 方法处理
	if promoterCount.DistributionAmount > 0 {
		increaser["totalProfitAmount"] = promoterCount.DistributionAmount
		increaser["waitProfitAmount"] = promoterCount.DistributionAmount
	}
	// 有分销商品则说明是分销订单，增加分销订单计数
	if promoterCount.HasDistProduct {
		increaser["totalCommissionOrders"] = promoterCount.Order
	}

	p.Count[countKey] = countValue
	selector := Common.GenDefaultCondition(ctx)
	selector["_id"] = p.Id
	setter := bson.M{
		"count":     p.Count,
		"updatedAt": time.Now(),
	}
	if p.ParentId.Valid() {
		parentStat := PromoterStat{}
		index := -1
		for i, stat := range p.ParentStats {
			if stat.PromoterId == p.ParentId {
				index = i
				parentStat = stat
				break
			}
		}
		parentStat.PromoterId = p.ParentId
		parentStat.TotalOrders += promoterCount.Order
		parentStat.TotalPromoteOrders += promoterCount.PromoteOrder
		parentStat.TotalCommissionOrders += cast.ToInt64(increaser["totalCommissionOrders"])
		parentStat.TotalAmount += promoterCount.TotalAmount
		parentStat.TotalProducts += promoterCount.Product
		parentStat.TotalPromoteAmount += promoterCount.PromoteAmount
		parentStat.DistProductTotalAmount += promoterCount.DistProductTotalAmount
		parentStat.DistProductPromoteAmount += promoterCount.DistProductPromoteAmount
		if index >= 0 {
			p.ParentStats[index] = parentStat
		} else {
			p.ParentStats = append(p.ParentStats, parentStat)
		}
		setter["parentStats"] = p.ParentStats
	}
	updater := bson.M{
		"$set": setter,
		"$inc": increaser,
	}
	err := extension.DBRepository.UpdateOne(ctx, C_PROMOTER, selector, updater)
	if err != nil {
		return err
	}
	if p.Type == PROMOTER_TYPE_STAFF {
		p.SendUpdateStaffPromoterEvent(ctx)
	}
	return nil
}

func (*Promoter) GetByStaffIds(ctx context.Context, staffIds []bson.ObjectId, containDeleted bool) ([]Promoter, error) {
	selector := bson.M{
		"staffId": bson.M{
			"$in": staffIds,
		},
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}
	if containDeleted {
		delete(selector, "isDeleted")
	}

	promoters := []Promoter{}
	err := extension.DBRepository.FindAll(ctx, C_PROMOTER, selector, []string{}, 0, &promoters)
	if err != nil {
		return nil, err
	}
	return promoters, nil
}

func (*Promoter) GetByStaffId(ctx context.Context, staffId bson.ObjectId) (Promoter, error) {
	selector := bson.M{
		"staffId":   staffId,
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}

	promoter := Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, selector, &promoter)
	return promoter, err
}

func (*Promoter) GetByOpenIdAndType(ctx context.Context, openId, pType string) (*Promoter, error) {
	// 一个分销接受者只能对应一个导购分销员
	selector := Common.GenDefaultCondition(ctx)
	selector["openId"] = openId
	selector["type"] = pType

	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, selector, promoter)
	if err != nil {
		return nil, err
	}

	return promoter, nil
}

func (*Promoter) GetMonthlyStaffBoundPromoterCount(ctx context.Context, memberBoundStaffId bson.ObjectId, monthTime time.Time) (int, error) {
	monthStartAt := util.GetStartTimeOfMonth(monthTime)
	monthEndAt := util.GetEndTimeOfMonth(monthTime)
	condition := bson.M{
		"accountId":                util.GetAccountIdAsObjectId(ctx),
		"isDeleted":                false,
		"memberBoundStaff.staffId": memberBoundStaffId,
		"memberBoundStaff.boundAt": bson.M{
			"$gte": monthStartAt,
			"$lte": monthEndAt,
		},
	}
	return extension.DBRepository.Count(ctx, C_PROMOTER, condition)
}

func (*Promoter) GetByOpenId(ctx context.Context, openId string) (*Promoter, error) {
	// 一个分账接收方只能对应一个分销员
	selector := Common.GenDefaultCondition(ctx)
	selector["openId"] = openId

	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, selector, promoter)
	if err != nil {
		return nil, err
	}

	return promoter, nil
}

func (*Promoter) ListByOpenId(ctx context.Context, openId, promoterType string, includeDeleted bool) ([]Promoter, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["openId"] = openId
	selector["type"] = promoterType
	if includeDeleted {
		delete(selector, "isDeleted")
	}
	var promoters []Promoter
	err := extension.DBRepository.FindAll(ctx, C_PROMOTER, selector, nil, 0, &promoters)
	return promoters, err
}

func (*Promoter) GetByOpenIds(ctx context.Context, openIds []string) ([]Promoter, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["openId"] = bson.M{"$in": openIds}
	var promoters []Promoter
	err := extension.DBRepository.FindAll(ctx, C_PROMOTER, selector, nil, 0, &promoters)
	return promoters, err
}

func (*Promoter) GetByMerchantId(ctx context.Context, merchantId string) (*Promoter, error) {
	// 一个分账接收方只能对应一个分销员
	selector := Common.GenDefaultCondition(ctx)
	selector["merchant.merchantId"] = merchantId

	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, selector, promoter)
	if err != nil {
		return nil, err
	}

	return promoter, nil
}

func (*Promoter) GetByPhone(ctx context.Context, phone, promoterType string) (*Promoter, error) {
	selector := Common.GenDefaultCondition(ctx)
	selector["phone"] = phone
	selector["type"] = promoterType
	promoter := &Promoter{}
	err := extension.DBRepository.FindOne(ctx, C_PROMOTER, selector, promoter)
	if err != nil {
		return nil, err
	}

	return promoter, nil
}

func (p *Promoter) GetStatus() string {
	if p.Status != "" {
		return p.Status
	}
	return PROMOTER_STATUS_APPROVED
}

func (p *Promoter) SendUpdateStaffPromoterEvent(ctx context.Context) {
	if !p.StaffId.Valid() {
		return
	}
	eventBody := component.CustomerEventBody{
		Id:         component.MAIEVENT_UPDATE_STAFF_PROMOTER + ":" + bson.NewObjectId().Hex(),
		AccountId:  p.AccountId.Hex(),
		MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
		SubType:    component.MAIEVENT_UPDATE_STAFF_PROMOTER,
		CreateTime: time.Now().UnixNano() / 1e6,
		EventProperties: map[string]interface{}{
			"promoterId": p.Id,
			"memberId":   p.MemberId.Hex(),
			"staffId":    p.StaffId,
		},
	}

	eventBody.SendBusinessEvent(ctx)
}

func LockUpdatePromoterCount(ctx context.Context, promoterId string, waitLockSeconds int) (string, bool) {
	key := fmt.Sprintf(LOCK_UPDATE_PROMOTER_COUNT, util.GetAccountId(ctx), promoterId)
	deadline := time.Now().Add(time.Second * time.Duration(waitLockSeconds))
	for {
		ok, _ := extension.RedisClient.SetNX(key, "1", LOCK_UPDATE_PROMOTER_COUNT_DURATION)
		if ok {
			return key, ok
		}
		if !time.Now().Before(deadline) {
			return key, ok
		}
		time.Sleep(time.Microsecond * 500)
	}
}

func (*Promoter) UpdateStaffNameAndPhone(ctx context.Context, staffId bson.ObjectId, name, phone string) error {
	condition := Common.GenDefaultCondition(ctx)
	condition["staffId"] = staffId
	setter := bson.M{}
	if name != "" {
		setter["name"] = name
	}
	if phone != "" {
		setter["phone"] = phone
	}
	return extension.DBRepository.UpdateOne(ctx, C_PROMOTER, condition, bson.M{"$set": setter})
}

func (*Promoter) UpdatePhone(ctx context.Context, id bson.ObjectId, phone string) error {
	condition := Common.GenDefaultConditionById(ctx, id)
	updater := bson.M{
		"$set": bson.M{
			"phone":     phone,
			"updatedAt": time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_PROMOTER, condition, updater)
}

// CountSubPromoterByParentId 统计某个一级分销员下属所有的分销员
func (*Promoter) CountSubPromoterByParentId(ctx context.Context, parentId bson.ObjectId) (int, error) {
	condition := Common.GenDefaultCondition(ctx)
	condition["parentId"] = parentId
	return extension.DBRepository.Count(ctx, C_PROMOTER, condition)
}

// CountSubPromoterByParentIds 统计一级分销员下属所有的分销员
func (*Promoter) CountSubPromoterByParentIds(ctx context.Context, parentIds []bson.ObjectId) ([]bson.M, error) {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"accountId": util.GetAccountIdAsObjectId(ctx),
				"isDeleted": false,
				"parentId":  bson.M{"$in": parentIds},
			},
		},
		{
			"$group": bson.M{
				"_id":   "$parentId",
				"count": bson.M{"$sum": 1},
			},
		},
	}
	result := []bson.M{}
	err := extension.DBRepository.Aggregate(ctx, C_PROMOTER, pipeline, false, &result)
	if err != nil {
		return nil, err
	}
	return result, nil
}

// CountSubPromoterTotalMembersByParentId 统计某个一级分销员下属所有的分销员绑定的客户数
func (*Promoter) CountSubPromoterTotalMembersByParentId(ctx context.Context, parentId bson.ObjectId) (int, error) {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"accountId": util.GetAccountIdAsObjectId(ctx),
				"isDeleted": false,
				"parentId":  parentId,
			},
		},
		{
			"$group": bson.M{
				"_id": "$parentId",
				"count": bson.M{
					"$sum": "$totalMembers",
				},
			},
		},
	}
	result := bson.M{}
	err := extension.DBRepository.Aggregate(ctx, C_PROMOTER, pipeline, true, &result)
	if err != nil {
		return 0, err
	}
	return cast.ToInt(result["count"]), nil
}

func (p *Promoter) GetSubPromoters(ctx context.Context) ([]Promoter, error) {
	condition := Common.GenDefaultCondition(ctx)
	condition["parentId"] = p.Id
	var promoters []Promoter
	err := extension.DBRepository.FindAll(ctx, C_PROMOTER, condition, nil, 0, &promoters)
	return promoters, err
}

func (p *Promoter) TransferSubPromoters(ctx context.Context, subPromoters []Promoter, newParentId bson.ObjectId) error {
	condition := Common.GenDefaultCondition(ctx)
	condition["_id"] = bson.M{"$in": core_util.ToObjectIdArray(core_util.ExtractArrayField("Id", subPromoters))}
	updater := bson.M{
		"$set": bson.M{
			"updatedAt": time.Now(),
			"parentId":  newParentId,
		},
		"$push": bson.M{
			"historyParentIds": newParentId,
		},
	}
	_, err := extension.DBRepository.UpdateAll(ctx, C_PROMOTER, condition, updater)
	core_component.GO(ctx, func(ctx context.Context) {
		p.sendTransferPromoterEvent(ctx, subPromoters, newParentId)
	})
	return err
}

func (p *Promoter) sendTransferPromoterEvent(ctx context.Context, subPromoters []Promoter, newParentId bson.ObjectId) {
	createTime := time.Now().UnixNano() / 1e6
	for _, promoter := range subPromoters {
		eventBody := component.CustomerEventBody{
			AccountId:  promoter.AccountId.Hex(),
			MemberId:   promoter.MemberId.Hex(),
			MsgType:    component.MAIEVENT_TYPE_CUSTOMER_EVENT,
			SubType:    component.MAIEVENT_TRANSFER_PROMOTER,
			CreateTime: createTime,
			EventProperties: map[string]interface{}{
				"fromGroupPromoterId": p.Id.Hex(),
				"promoterId":          promoter.Id.Hex(),
				"toGroupPromoterId":   newParentId.Hex(),
			},
		}
		eventBody.Send(ctx)
	}
}

func (*Promoter) GetAllSubPromoterIds(ctx context.Context, parentId bson.ObjectId) []bson.ObjectId {
	condition := Common.GenDefaultCondition(ctx)
	condition["parentId"] = parentId
	var ids []interface{}
	extension.DBRepository.Distinct(ctx, C_PROMOTER, condition, "_id", &ids)
	return core_util.ToObjectIdArray(ids)
}

func (*Promoter) RefreshTotalMembers(ctx context.Context, promoterIds []bson.ObjectId) error {
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"accountId":  util.GetAccountIdAsObjectId(ctx),
				"isDeleted":  false,
				"promoterId": bson.M{"$in": promoterIds},
			},
		},
		{
			"$group": bson.M{
				"_id":          "$promoterId",
				"totalMembers": bson.M{"$sum": 1},
				"bindingMembers": bson.M{
					"$sum": bson.M{
						"$cond": bson.M{
							"if": bson.M{
								"$eq": []interface{}{"$status", PROMOTER_MEMBER_STATUS_BOUND},
							},
							"then": 1,
							"else": 0,
						},
					},
				},
			},
		},
	}
	var (
		result                    []bson.M
		docs                      []interface{}
		promoterTotalMembersMap   = make(map[bson.ObjectId]int64)
		promoterBindingMembersMap = make(map[bson.ObjectId]int64)
	)
	err := extension.DBRepository.Aggregate(ctx, C_PROMOTER_MEMBER, pipeline, false, &result)
	if err != nil {
		return err
	}
	for _, stats := range result {
		promoterTotalMembersMap[core_util.ToObjectId(stats["_id"])] = cast.ToInt64(stats["totalMembers"])
		promoterBindingMembersMap[core_util.ToObjectId(stats["_id"])] = cast.ToInt64(stats["bindingMembers"])
	}
	for _, promoterId := range promoterIds {
		docs = append(docs, bson.M{
			"_id": promoterId,
		}, bson.M{
			"$set": bson.M{
				"totalMembers":   promoterTotalMembersMap[promoterId],
				"bindingMembers": promoterBindingMembersMap[promoterId],
			},
		})
	}
	if len(docs) > 0 {
		_, err := extension.DBRepository.BatchUpdateUnordered(ctx, C_PROMOTER, docs...)
		if err != nil && len(err.WriteErrors) > 0 {
			return err
		}
	}
	return nil
}
