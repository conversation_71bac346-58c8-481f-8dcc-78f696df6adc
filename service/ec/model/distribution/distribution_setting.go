package distribution

import (
	"fmt"
	ec_product "mairpc/service/ec/model/product"
	"time"

	"mairpc/core/extension"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/common/request"
	"mairpc/proto/ec/distribution"
	"mairpc/proto/ec/staff"
	pb_store "mairpc/proto/ec/store"
	ec_client "mairpc/service/ec/client"
	ec_setting "mairpc/service/ec/model/setting"
	"mairpc/service/share/util"

	"mairpc/core/extension/bson"
	ec_model "mairpc/service/ec/model"

	"golang.org/x/net/context"
)

const (
	C_DISTRIBUTION_SETTING    = "ec.distributionSetting"
	DEFAULT_AVAILABLE_PERIOD  = 15 // 客户有效期天数
	DEFAULT_PROTECTION_PERIOD = 15 // 客户保护期天数

	DEFAULT_DISTRIBUTION_PROPORTION = 1 // 默认分销比例

	RECRUITMENT_DEFAULT_TITLE = "注册分销员"
	RECRUITMENT_DEFAULT_IMAGE = "https://statics.maiscrm.com/modules/distribution/<EMAIL>"
	RECRUITMENT_DEFAULT_COLOR = "#EC7768"

	BOUNDTYPE_PERMANENT = "permanent"   // 永久绑定
	BOUNDTYPE_PERIOD    = "boundPeriod" // 有效期绑定
	BOUNDTYPE_UNBOUND   = "unbound"     // 不绑定

	REPLACETYPE_ALLOW    = "allow"         // 允许抢客
	REPLACETYPE_NOTALLOW = "notAllow"      // 不允许抢客
	REPLACETYPE_PERIOD   = "replacePeriod" // 保护期抢客

	DISTRIBUTION_SETTING_TYPE_MEMBER = "member" // 大众分销员设置
	DISTRIBUTION_SETTING_TYPE_STAFF  = "staff"  // 导购分销员设置

	PROPORTION_TYPE_PRODUCT = "product" // 按商品分佣
	PROPORTION_TYPE_ORDER   = "order"   //按订单分佣

	COMMISSION_TYPE_PRODUCT = "product" // 按商品分佣
	COMMISSION_TYPE_ORDER   = "order"   //按订单分佣

	RANGE_DISTRIBUTION_MODE_AMOUNT       = "amount"      // 按固定金额分佣
	RANGE_DISTRIBUTION_MODE_PROPORTION   = "proportion"  // 按提成比例分佣
	RANGE_DISTRIBUTION_MODE_STAFF_LEVELS = "staffLevels" // 按不同导购等级分佣

	PROMOTER_COMMISSION_DISTRIBUTION_MODE_AMOUNT       = "defaultAmount"     // 按固定金额分佣
	PROMOTER_COMMISSION_DISTRIBUTION_MODE_PROPORTION   = "defaultProportion" // 按固定比例分佣
	PROMOTER_COMMISSION_DISTRIBUTION_MODE_STAFF_LEVELS = "staffLevels"       // 按不同导购等级分佣
	PROMOTER_COMMISSION_DISTRIBUTION_MODE_RANGE        = "range"             // 按不同销售额区间分佣，仅按订单分佣时为此类型

	PROMOTER_COMMISSION_STAFF_LEVEL_DISTRIBUTION_MODE_AMOUNT     = "amount"     // 按导购等级分佣，每个等级按固定金额分佣
	PROMOTER_COMMISSION_STAFF_LEVEL_DISTRIBUTION_MODE_PROPORTION = "proportion" // 按导购等级分佣，每个等级按固定比例分佣

	CYCLE_DAY   = "day"
	CYCLE_MONTH = "month"

	CALCULATE_TYPE_CURRENT_MONTH_PROPORTION = "currentMonthProportion"
	CALCULATE_TYPE_LAST_MONTH_PROPORTION    = "lastMonthProportion"
	CALCULATE_TYPE_CURRENT_MONTH_AMOUNT     = "currentMonthAmount"

	PROFITSHARING_TYPE_END_OF_PROTECTION_PERIOD = 1 // 维权期结束分账
	PROFITSHARING_TYPE_TRANSACTION_COMPLETED    = 2 // 订单完成分账
	PROFITSHARING_TYPE_MONTHLY                  = 3 // 分销月结

	POSTER_SHARE_TYPE_PRODUCT = "product" // 商品分享文案
	POSTER_SHARE_TYPE_CUSTOM  = "custom"  // 自定义分享文案

	POSTER_STYPE_BACKGROUND_TYPE_COLOR   = "color"   // 分享海报背景类型 纯色
	POSTER_STYPE_BACKGROUND_TYPE_PICTURE = "picture" // 分享海报背景类型 图片

	POSTER_STYPE_TYPE_LEFT   = "left"   // 二维码左侧样式
	POSTER_STYPE_TYPE_RIGHT  = "right"  // 二维码右侧样式
	POSTER_STYPE_TYPE_TOP    = "top"    // 二维码顶部样式
	POSTER_STYPE_TYPE_BOTTOM = "bottom" // 二维码底侧样式

	BOUND_STAFF_PROPORTION_TYPE_LINK = "link" // 以推广链接方式购买，才对绑定导购分佣
	BOUND_STAFF_PROPORTION_TYPE_ANY  = "any"  // 任意方式购买均对绑定导购分佣

	PROMOTER_MEMBER_BIND_MODE_LINK = "link" // 点击链接即绑定
	PROMOTER_MEMBER_BIND_MODE_PAY  = "pay"  // 付款后绑定

	COMMISSION_ACCOUNT_BANK_CARD_TYPE = "bankCard"      // 税后佣金发放至银行卡
	COMMISSION_ACCOUNT_WECHAT_BALANCE = "wechatBalance" // 税后佣金发放至微信余额

	DISTRIBUTION_AMOUNT_SEND_TO_PARENT = "parent"
	// 连锁零售商大众分销佣金结算方式
	COMMISSION_SETTLEMENT_METHOD_BANK_CARD    = "bankCard"
	COMMISSION_SETTLEMENT_METHOD_FUND_ACCOUNT = "fundAccount"
)

var CDistributionSetting = &DistributionSetting{}

type DistributionSetting struct {
	Id                          bson.ObjectId               `bson:"_id,omitempty"`
	AccountId                   bson.ObjectId               `bson:"accountId"`
	Type                        string                      `bson:"type"`
	Enabled                     bool                        `bson:"enabled"`
	HasEnabled                  bool                        `bson:"hasEnabled"`
	RecruitmentEnabled          bool                        `bson:"recruitmentEnabled,omitempty"`
	RecruitmentType             string                      `bson:"recruitmentType"`
	Rule                        RuleSetting                 `bson:"rule"`
	Commission                  CommissionSetting           `bson:"commission"`
	Recruitment                 RecruitmentContent          `bson:"recruitment"`
	CreatedAt                   time.Time                   `bson:"createdAt"`
	UpdatedAt                   time.Time                   `bson:"updatedAt"`
	IsDeleted                   bool                        `bson:"isDeleted"`
	AutoApproval                bool                        `bson:"autoApproval"`
	AutoApprovalLimit           AutoApprovalLimit           `bson:"autoApprovalLimit,omitempty"`
	NeedApproval                bool                        `bson:"needApproval"`
	PromoterCommission          PromoterCommissionSetting   `bson:"promoterCommission,omitempty"`
	PosterSetting               PosterSetting               `bson:"posterSetting,omitempty"`
	RuleDescription             RuleDescription             `bson:"ruleDescription,omitempty"`
	IsOffline                   bool                        `bson:"isOffline,omitempty"`               // 是否线下结算，使用易宝支付时有效。为 true 时，导购分销所有分佣记录均为失败，分佣记录用于线下分佣
	IsShowDistProductAmount     bool                        `bson:"isShowDistProductAmount,omitempty"` // 是否显示大众分销分销商品销售额，目前只有汉高租户显示
	SubPromoterSetting          SubPromoterSetting          `bson:"subPromoterSetting,omitempty"`
	CommissionSettlementSetting CommissionSettlementSetting `bson:"commissionSettlementSetting,omitempty"` // 结算流程设置
}

type SubPromoterSetting struct {
	AmountSendTo        string `bson:"amountSendTo"`
	AllowCreateByParent bool   `bson:"allowCreateByParent"`
}

type RuleSetting struct {
	BoundType                string `bson:"boundType"`
	AvailablePeriod          uint64 `bson:"availablePeriod"`
	ReplaceType              string `bson:"replaceType"`
	ProtectionPeriod         uint64 `bson:"protectionPeriod"`
	IsServiceStaffFirst      bool   `bson:"isServiceStaffFirst"` // 是否允许服务导购分佣
	BoundStaffProportionType string `bson:"boundStaffProportionType"`
	PromoterMemberBindMode   string `bson:"promoterMemberBindMode"`
	CanBindSelf              bool   `bson:"canBindSelf"`
	IsCarryStoreId           bool   `bson:"isCarryStoreId"` // 分销员分享链接是否携带门店参数
}

type CommissionSetting struct {
	ProfitSharingType   int     `bson:"profitSharingType"`
	ProportionType      string  `bson:"proportionType"`
	Proportion          float64 `bson:"proportion"`
	IsDisplayCommission bool    `bson:"isDisplayCommission"`
}

type RecruitmentContent struct {
	Title string `bson:"title"`
	Image string `bson:"image"`
	Color string `bson:"color"`
}

type RuleDescription struct {
	Content string `bson:"content,omitempty"`
}

type PromoterCommissionSetting struct {
	Cycle            string         `bson:"cycle"`
	CalculateType    string         `bson:"calculateType"`
	DistributionMode string         `bson:"distributionMode"`
	Range            []RangeSetting `bson:"range,omitempty"`
	DeductTax        bool           `bson:"deductTax"`
	// 是否开启过代扣个税
	HasDeductTaxTurnedOn       bool                `bson:"hasDeductTaxTurnedOn"`
	CommissionType             string              `bson:"commissionType"`
	ProductDefaultProfitAmount uint64              `bson:"productDefaultProfitAmount"`
	DefaultProportion          float64             `bson:"defaultProportion"`
	StaffLevels                []StaffLevelSetting `bson:"staffLevels,omitempty"`
	TaxSetting                 TaxSetting          `bson:"taxSetting,omitempty"` // 个税设置
}

type TaxSetting struct {
	DeductTax            bool   `bson:"deductTax,omitempty"`            // 是否代缴个税
	TaxPayer             int    `bson:"taxPayer,omitempty"`             // 个税承担方，0 个人，1 品牌
	AccountType          string `bson:"accountType,omitempty"`          // 税后佣金发放账户类型，bankCard 银行卡，wechatBalance 微信零钱
	DivideType           int    `bson:"divideType,omitempty"`           // 0 群脉与品牌签约，群脉与薪行家签约：群脉按照品牌设置的大众分销分佣规则分账至群脉指定商户号，1 品牌与薪行家直签：群脉仅计算分佣规则，实质不分账
	MerchantNo           string `bson:"merchantNo,omitempty"`           // divideType 为 0 时，微信商户号
	MerchantName         string `bson:"merchantName,omitempty"`         // divideType 为 0 时，微信商户名称
	ServiceFeeProportion int    `bson:"serviceFeeProportion,omitempty"` // 服务费比例 0-100，薪行家代付金额 = 实际金额 + 实际金额 * serviceFeeProportion / 100
	CompanyName          string `bson:"companyName,omitempty"`          // 签约协议中，甲方公司名称
}

type RangeSetting struct {
	Start            uint64              `bson:"start"`
	End              uint64              `bson:"end"`
	DistributionMode string              `bson:"distributionMode"`
	Proportion       float64             `bson:"proportion"`
	Amount           uint64              `bson:"amount"`
	StaffLevels      []StaffLevelSetting `bson:"staffLevels,omitempty"`
}

type StaffLevelSetting struct {
	DistributionMode string  `bson:"distributionMode"`
	Amount           uint64  `bson:"amount,omitempty"`
	Proportion       float64 `bson:"proportion,omitempty"`
	Level            uint64  `bson:"level"`
}

type PosterSetting struct {
	StoreStyle    PosterStyle `bson:"storeStyle,omitempty"`
	ProductStyle  PosterStyle `bson:"productStyle"`
	StorePoster   Poster      `bson:"storePoster,omitempty"`
	ProductPoster Poster      `bson:"productPoster"`
}

type PosterStyle struct {
	Type             string           `bson:"type"`
	Background       PosterBackground `bson:"background"`
	ProductNameColor string           `bson:"productNameColor,omitempty"`
	PriceColor       string           `bson:"priceColor,omitempty"`
	OriginPriceColor string           `bson:"originPriceColor,omitempty"`
	PosterColor      string           `bson:"posterColor,omitempty"`
	StoreNameColor   string           `bson:"storeNameColor,omitempty"`
	CampaignTagColor string           `bson:"campaignTagColor,omitempty"`
}

type PosterBackground struct {
	Type    string `bson:"type,omitempty"`
	Color   string `bson:"color,omitempty"`
	Picture string `bson:"picture,omitempty"`
}

type Poster struct {
	Type string `bson:"type"`           // 分享文案类型
	Text string `bson:"text,omitempty"` // 分享文案
}

type AutoApprovalLimit struct {
	MemberLevel     int64  `bson:"memberLevel"`
	MemberLevelName string `bson:"memberLevelName"`
}

type WithdrawalFrequency struct {
	Mode     string `bson:"mode"`     // 提现频率模式：daily(按天)/monthly(按月)/quarterly(按季)/yearly(按年)
	MaxTimes int    `bson:"maxTimes"` // 最大提现次数
}

type WithdrawalSetting struct {
	Enabled     bool                `bson:"enabled"`     // 是否启用提现设置
	Frequency   WithdrawalFrequency `bson:"frequency"`   // 提现频率配置
	Percentage  int                 `bson:"percentage"`  // 提现比例 1-100
	Description string              `bson:"description"` // 提现规则说明文本
}

type CommissionSettlementSetting struct {
	Method             string            `bson:"method"`             // 结算方式: bankCard-银行卡 fundAccount-资金账户
	WithdrawalSettings WithdrawalSetting `bson:"withdrawalSettings"` // 提现设置
}

func (d *DistributionSetting) GetAllStaffSetting(ctx context.Context) ([]DistributionSetting, error) {
	selector := bson.M{
		"type":      DISTRIBUTION_SETTING_TYPE_STAFF,
		"enabled":   true,
		"isDeleted": false,
	}

	settings := []DistributionSetting{}
	err := extension.DBRepository.FindAll(ctx, C_DISTRIBUTION_SETTING, selector, []string{}, 0, &settings)
	if err != nil {
		return nil, err
	}

	return settings, nil
}

func (d *DistributionSetting) GetByType(ctx context.Context, settingType string) (*DistributionSetting, error) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"type":      settingType,
		"isDeleted": false,
	}

	resp := &DistributionSetting{}
	extension.DBRepository.FindOne(ctx, C_DISTRIBUTION_SETTING, selector, resp)
	if resp.Id.Hex() == "" {
		setting, err := d.InitDefaultSettingWithType(ctx, settingType)
		if err != nil {
			return nil, err
		}

		return setting, nil
	}
	resp.Ensure(ctx)
	return resp, nil
}

func (self *DistributionSetting) Ensure(ctx context.Context) {
	if self.Type == DISTRIBUTION_SETTING_TYPE_STAFF {
		self.ensureStaffDistribution(ctx)
		return
	}
}

func (self *DistributionSetting) ensureStaffDistribution(ctx context.Context) {
	// 当前只对导购分销处理分销设置的一些字段
	if self.Type != DISTRIBUTION_SETTING_TYPE_STAFF {
		return
	}
	if self.PromoterCommission.Cycle == "" {
		// 为初始化 promoterCommission 不做处理
		return
	}
	rangeDistributionMode := ""
	switch self.PromoterCommission.CalculateType {
	case CALCULATE_TYPE_LAST_MONTH_PROPORTION, CALCULATE_TYPE_CURRENT_MONTH_PROPORTION:
		rangeDistributionMode = RANGE_DISTRIBUTION_MODE_PROPORTION
	case CALCULATE_TYPE_CURRENT_MONTH_AMOUNT:
		rangeDistributionMode = RANGE_DISTRIBUTION_MODE_AMOUNT
	default:
		// 已有的数据 calculateType 不为上述三种值时只会是按商品设置默认金额分佣，如果是按 staffLevels 则一定有 distributionMode
		if self.PromoterCommission.CommissionType == COMMISSION_TYPE_PRODUCT && self.PromoterCommission.DistributionMode == "" {
			self.PromoterCommission.DistributionMode = PROMOTER_COMMISSION_DISTRIBUTION_MODE_AMOUNT
		}
		return
	}

	if self.PromoterCommission.DistributionMode != "" || len(self.PromoterCommission.Range) == 0 {
		return
	}
	// 如果 distributionMode 是 staffLevels 则一定不会为空，所以这里一定是 range
	self.PromoterCommission.DistributionMode = PROMOTER_COMMISSION_DISTRIBUTION_MODE_RANGE
	if self.PromoterCommission.Range[0].DistributionMode != "" {
		return
	}
	for i := range self.PromoterCommission.Range {
		self.PromoterCommission.Range[i].DistributionMode = rangeDistributionMode
	}
}

func (d *DistributionSetting) IsStaffLevelsSetting() bool {
	return d.PromoterCommission.DistributionMode == PROMOTER_COMMISSION_DISTRIBUTION_MODE_STAFF_LEVELS ||
		(d.PromoterCommission.DistributionMode == PROMOTER_COMMISSION_DISTRIBUTION_MODE_RANGE &&
			d.PromoterCommission.Range[0].DistributionMode == RANGE_DISTRIBUTION_MODE_STAFF_LEVELS)
}

func (d *DistributionSetting) EnsureStaffLevels(ctx context.Context, staffLevels []*staff.StaffLevel) error {
	if d.IsStaffLevelsSetting() {
		if len(staffLevels) == 0 {
			// 按 staffLevels 计算时，获取 staffLevels 且必须存在
			resp, err := ec_client.StaffService.ListStaffLevels(ctx, &staff.ListStaffLevelsRequest{
				ListCondition: &request.ListCondition{
					Page:    1,
					PerPage: 1000,
					OrderBy: []string{"level"},
				},
			})
			if err != nil {
				return err
			}
			staffLevels = resp.Items
		}
	}
	if d.PromoterCommission.DistributionMode == PROMOTER_COMMISSION_DISTRIBUTION_MODE_STAFF_LEVELS {
		d.PromoterCommission.StaffLevels = ensureStaffLevelSettings(d.PromoterCommission.StaffLevels, staffLevels)
	} else if d.PromoterCommission.DistributionMode == PROMOTER_COMMISSION_DISTRIBUTION_MODE_RANGE {
		for i := range d.PromoterCommission.Range {
			r := d.PromoterCommission.Range[i]
			if r.DistributionMode == RANGE_DISTRIBUTION_MODE_STAFF_LEVELS {
				r.StaffLevels = ensureStaffLevelSettings(r.StaffLevels, staffLevels)
			}
			d.PromoterCommission.Range[i] = r
		}
	}
	return nil
}

func ensureStaffLevelSettings(levelSettings []StaffLevelSetting, levels []*staff.StaffLevel) []StaffLevelSetting {
	result := []StaffLevelSetting{}
	existsLevelSettingMap := core_util.MakeMapperV2("Level", uint64(0), levelSettings)
	for _, level := range levels {
		if existSetting, ok := existsLevelSettingMap[level.Level]; ok {
			result = append(result, existSetting)
			continue
		}
		item := StaffLevelSetting{
			DistributionMode: levelSettings[0].DistributionMode,
			Level:            level.Level,
		}
		result = append(result, item)
	}
	return result
}

func (d *DistributionSetting) GetWithoutAccount(ctx context.Context) ([]DistributionSetting, error) {
	selector := bson.M{
		"isDeleted": false,
	}

	resp := []DistributionSetting{}
	err := extension.DBRepository.FindAll(ctx, C_DISTRIBUTION_SETTING, selector, []string{}, 0, &resp)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
func (d *DistributionSetting) GetAll(ctx context.Context) ([]DistributionSetting, error) {
	selector := bson.M{
		"accountId": util.GetAccountIdAsObjectId(ctx),
		"isDeleted": false,
	}

	resp := []DistributionSetting{}
	err := extension.DBRepository.FindAll(ctx, C_DISTRIBUTION_SETTING, selector, []string{}, 0, &resp)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

func (*DistributionSetting) InitDefaultSettingWithType(ctx context.Context, settingType string) (*DistributionSetting, error) {
	ruleSetting := RuleSetting{
		BoundType:              BOUNDTYPE_PERMANENT,
		AvailablePeriod:        DEFAULT_AVAILABLE_PERIOD,
		ReplaceType:            REPLACETYPE_NOTALLOW,
		ProtectionPeriod:       DEFAULT_PROTECTION_PERIOD,
		PromoterMemberBindMode: PROMOTER_MEMBER_BIND_MODE_LINK,
	}
	if settingType == DISTRIBUTION_SETTING_TYPE_STAFF {
		ruleSetting.IsServiceStaffFirst = true
	}
	if settingType == DISTRIBUTION_SETTING_TYPE_MEMBER {
		ruleSetting.CanBindSelf = true
	}

	commissionSetting := CommissionSetting{
		ProfitSharingType: ec_setting.COMMISSION_TYPE_END_OF_PROTECTION_PERIOD,
		ProportionType:    PROPORTION_TYPE_PRODUCT,
		Proportion:        DEFAULT_DISTRIBUTION_PROPORTION,
	}

	recruitment := RecruitmentContent{
		Title: RECRUITMENT_DEFAULT_TITLE,
		Image: RECRUITMENT_DEFAULT_IMAGE,
		Color: RECRUITMENT_DEFAULT_COLOR,
	}

	defaultSetting := DistributionSetting{
		Id:        bson.NewObjectId(),
		AccountId: util.GetAccountIdAsObjectId(ctx),
		Type:      settingType,
		Enabled:   false,
		Rule:      ruleSetting,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	defaultSetting.GenerateDefaultPosterSetting(ctx)

	defaultSetting.RuleDescription = GenerateDefaultRuleDescription("")

	if settingType == DISTRIBUTION_SETTING_TYPE_MEMBER {
		defaultSetting.RecruitmentEnabled = true
		defaultSetting.Commission = commissionSetting
		defaultSetting.Recruitment = recruitment
		defaultSetting.CommissionSettlementSetting = CommissionSettlementSetting{
			Method: COMMISSION_SETTLEMENT_METHOD_BANK_CARD,
		}
	}

	_, err := extension.DBRepository.Insert(ctx, C_DISTRIBUTION_SETTING, defaultSetting)
	if err != nil {
		return nil, err
	}

	return &defaultSetting, nil
}

func (d *DistributionSetting) Update(ctx context.Context, req *distribution.UpdateDistributionSettingRequest) error {
	selector := bson.M{
		"_id":       d.Id,
		"accountId": d.AccountId,
		"isDeleted": false,
	}

	setter := bson.M{
		"rule":               d.Rule,
		"enabled":            d.Enabled,
		"hasEnabled":         d.HasEnabled,
		"commission":         d.Commission,
		"recruitmentEnabled": d.RecruitmentEnabled,
		"recruitmentType":    d.RecruitmentType,
		"recruitment":        d.Recruitment,
		"promoterCommission": d.PromoterCommission,
		"autoApproval":       d.AutoApproval,
		"autoApprovalLimit":  d.AutoApprovalLimit,
		"needApproval":       d.NeedApproval,
		"posterSetting":      d.PosterSetting,
		"ruleDescription":    d.RuleDescription,
		"subPromoterSetting": d.SubPromoterSetting,
		"updatedAt":          time.Now(),
	}

	updater := bson.M{
		"$set": setter,
	}

	err := extension.DBRepository.UpdateOne(ctx, C_DISTRIBUTION_SETTING, selector, updater)
	if err != nil {
		return err
	}
	if !ec_model.IsChainRetail(ctx) {
		if req.Commission != nil && req.Commission.Proportion != 0 {
			// 更新所有设置为默认分销比例的分销商品的比例和分销佣金
			return ec_product.CProduct.UpdateDefaultProfitProportion(ctx, req.Commission.Proportion)
		}
		if req.PromoterCommission != nil && req.PromoterCommission.ProductDefaultProfitAmount != 0 {
			// 导购分销不用更新分销商品默认分佣金额，商品会在响应值中根据导购分销设置返回默认配置
			if req.Type == DISTRIBUTION_SETTING_TYPE_STAFF {
				return nil
			}
			return ec_product.CProduct.UpdateDefaultProfitAmount(ctx, req.PromoterCommission.ProductDefaultProfitAmount)
		}
	}
	return nil
}

func (*DistributionSetting) UpdatePromoterCommission(ctx context.Context, id bson.ObjectId, promoterCommission PromoterCommissionSetting) error {
	selector := bson.M{
		"_id": id,
	}
	updater := bson.M{
		"$set": bson.M{
			"promoterCommission": promoterCommission,
			"updatedAt":          time.Now(),
		},
	}
	return extension.DBRepository.UpdateOne(ctx, C_DISTRIBUTION_SETTING, selector, updater)
}

func (d *DistributionSetting) GenerateDefaultPosterSetting(ctx context.Context) {
	if d.Type == DISTRIBUTION_SETTING_TYPE_STAFF {
		d.PosterSetting = PosterSetting{
			ProductStyle: PosterStyle{
				Type: POSTER_STYPE_TYPE_RIGHT,
				Background: PosterBackground{
					Type:  POSTER_STYPE_BACKGROUND_TYPE_COLOR,
					Color: "#FFFFFF",
				},
				ProductNameColor: "#000000",
				PriceColor:       "#FF9E20",
				OriginPriceColor: "#A9A9A9",
				PosterColor:      "#000000",
				StoreNameColor:   "#000000",
			},
			ProductPoster: Poster{
				Type: POSTER_SHARE_TYPE_PRODUCT,
				Text: "这个商品很不错，快来看看吧！",
			},
			StoreStyle: PosterStyle{
				Type: POSTER_STYPE_TYPE_RIGHT,
				Background: PosterBackground{
					Type:  POSTER_STYPE_BACKGROUND_TYPE_COLOR,
					Color: "#FFFFFF",
				},
				ProductNameColor: "#000000",
				PriceColor:       "#FF9E20",
				OriginPriceColor: "#A9A9A9",
				PosterColor:      "#000000",
				StoreNameColor:   "#000000",
			},
			StorePoster: Poster{
				Type: POSTER_SHARE_TYPE_CUSTOM,
				Text: "嗨~听说这里有超多好物哦！",
			},
		}
		return
	}

	if d.Type == DISTRIBUTION_SETTING_TYPE_MEMBER {
		d.PosterSetting = PosterSetting{
			ProductStyle: PosterStyle{
				Type: POSTER_STYPE_TYPE_RIGHT,
				Background: PosterBackground{
					Type:  POSTER_STYPE_BACKGROUND_TYPE_COLOR,
					Color: "#FFFFFF",
				},
				ProductNameColor: "#000000",
				PriceColor:       "#FF9E20",
				OriginPriceColor: "#A9A9A9",
				PosterColor:      "#000000",
				StoreNameColor:   "#000000",
			},
			ProductPoster: Poster{
				Type: POSTER_SHARE_TYPE_PRODUCT,
				Text: "这个商品很不错，快来看看吧！",
			},
			StoreStyle: PosterStyle{
				Type: POSTER_STYPE_TYPE_RIGHT,
				Background: PosterBackground{
					Type:  POSTER_STYPE_BACKGROUND_TYPE_COLOR,
					Color: "#FFFFFF",
				},
				ProductNameColor: "#000000",
				PriceColor:       "#FF9E20",
				OriginPriceColor: "#A9A9A9",
				PosterColor:      "#000000",
				StoreNameColor:   "#000000",
			},
			StorePoster: Poster{
				Type: POSTER_SHARE_TYPE_CUSTOM,
				Text: "嗨~听说这里有超多好物哦！",
			},
		}
		return
	}
}

// staffLevel 参数小于 0 时会请求 GetLevelOfStaff 接口获取导购所属等级
func (r *RangeSetting) GetProportionAndAmount(ctx context.Context, staffLevel int64, staffId string) (float64, uint64) {
	if r.DistributionMode != RANGE_DISTRIBUTION_MODE_STAFF_LEVELS {
		return r.Proportion, r.Amount
	}
	if staffLevel < 0 {
		if staffId == "" {
			return 0, 0
		}
		var err error
		resp, err := ec_client.StoreService.GetLevelOfStaff(ctx, &pb_store.StaffDetailRequest{
			StaffId: staffId,
		})
		if err != nil {
			log.Warn(ctx, "Failed to get staff for distribution range staff levels", log.Fields{
				"staffId": staffId,
				"err":     err.Error(),
			})
			return 0, 0
		}
		staffLevel = resp.Value
	}
	// 导购未设置等级时，获取等级 1 的配置
	if staffLevel == 0 {
		staffLevel = 1
	}
	for _, level := range r.StaffLevels {
		if level.Level == uint64(staffLevel) {
			return level.Proportion, level.Amount
		}
	}
	return 0, 0
}

func (d *DistributionSetting) IsProductDistribution() bool {
	switch d.Type {
	case DISTRIBUTION_SETTING_TYPE_STAFF:
		return d.PromoterCommission.CommissionType == PROPORTION_TYPE_PRODUCT
	case DISTRIBUTION_SETTING_TYPE_MEMBER:
		return d.Commission.ProportionType == PROPORTION_TYPE_PRODUCT
	default:
		panic("invalid distributionSetting type")
	}
}

func GenerateDefaultRuleDescription(accountType string) RuleDescription {
	content := "<p><span style=\"font-size: 14px;\"><strong><span style=\"font-size: 20px;\"></span></strong></span></p><p style=\"margin-top: 0px;\"><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444;\">Q</span></strong><strong><span style=\"color: #444444;\">：如何确认商品是否参与分销？如何确认分佣金额？</span></strong></span></p><p style=\"margin-top: 5px;\"><span style=\"color: #444444; font-size: 14px;\">A：您可在“我的”-“分销中心”-“推广商品”页面查看参与分销的商品，推广商品页面展示了商品的分佣比例和预计分佣金额，实际分佣金额根据商品实付金额*分佣比例来计算。</span></p><hr/><p style=\"text-align: left;\"><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444; font-size: 14px;\">Q</span></strong><strong><span style=\"color: #444444; font-size: 14px;\">：推广商品的佣金何时发放？发到哪里？</span></strong></span></p><p style=\"margin-top: 5px;\"><span style=\"color: #444444; font-size: 14px;\">A：%s。</span></p><p><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444; font-size: 14px;\"></span></strong></span></p><hr/><p><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444; font-size: 14px;\">Q</span></strong><strong><span style=\"color: #444444; font-size: 14px;\">：如何查看收益记录？</span></strong><br/></span></p><p style=\"margin-top: 5px;\"><span style=\"color: #444444; font-size: 14px;\">A：您可在“我的”-“分销中心”-“分销业绩”-“累计收益”页面查看。</span></p><p><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444; font-size: 14px;\"></span></strong></span></p><hr/><p><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444; font-size: 14px;\">Q</span></strong><strong><span style=\"color: #444444; font-size: 14px;\">：为什么推广了但是没有收益？</span></strong><br/></span></p><p style=\"margin-top: 5px;\"><span style=\"color: #444444; font-size: 14px;\">A：以下几种情况会导致没有收益：1.推广的商品不参与分佣；2.没有从“推广商品”页面进行分享，而是从其他页面进行的分享；3.订单未完成，未到达分佣时间；4.商品发生退款。</span></p><p style=\"margin-top: 5px;\"><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444; font-size: 14px;\"></span></strong></span></p><hr/><p><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444; font-size: 14px;\">Q</span></strong><strong><span style=\"color: #444444; font-size: 14px;\">：怎样发展客户？</span></strong><br/></span></p><p style=\"margin-top: 5px;\"><span style=\"color: #444444; font-size: 14px;\">A：通过推广店铺或推广商品，可以发展自己的客户。</span></p><p><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444; font-size: 14px;\"></span></strong></span></p><hr/><p><span style=\"font-size: 14px;\"><strong><span style=\"color: #444444; font-size: 14px;\">Q</span></strong><strong><span style=\"color: #444444; font-size: 14px;\">：我的客户为什么会失效？</span></strong><br/></span></p><p style=\"margin-top: 5px;\"><span style=\"color: #444444; font-size: 14px;\">A：超出有效期自动失效；超出保护期后被其他分销员抢客了；客户自己注册了分销员，自己与自己的分销员账户绑定。</span></p>"
	str := "根据品牌方的设置，发放时间为“交易完成”或“售后维权期结束”；届时将直接发放至您的微信账户余额"
	if accountType == COMMISSION_ACCOUNT_BANK_CARD_TYPE {
		str = "根据品牌方的设置，发放时间为“交易完成”或“售后维权期结束”；届时将直接发放至您注册时填写的银行卡账户，发放后预计1-5个工作日到账"
	} else if accountType == COMMISSION_ACCOUNT_WECHAT_BALANCE {
		str = "根据品牌方的设置，发放时间为“交易完成”或“售后维权期结束”；届时将直接发放至您的微信账户余额，发放后预计1-5个工作日到账"
	}

	var description = RuleDescription{
		Content: fmt.Sprintf(content, str),
	}
	return description
}
