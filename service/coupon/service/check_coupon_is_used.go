package service

import (
	"mairpc/core/extension/bson"
	"mairpc/core/validators"
	"mairpc/proto/common/request"
	pb_coupon "mairpc/proto/coupon"
	"mairpc/proto/member"
	campaign_model "mairpc/service/campaign/model"
	share_model "mairpc/service/share/model"
	"time"

	"golang.org/x/net/context"
)

const (
	C_MEMBER_BATCH_OPERATION     = "memberBatchOperation"
	C_PRODUCT_CODE_REWARD_RULE   = "productCodeRewardRule"
	C_CAMPAIGN_CENTER_SIGNIN     = "campaigncenterSignin"
	C_CAMPAIGN_CENTER_LUCKY_DRAW = "campaigncenterLuckydraw"
	C_MARKETO                    = "marketo"
	C_ENTER_STORE_WELFARE        = "ec.enterStoreWelfare"
	C_FLASH_SALE_CAMPAIGN        = "ec.flashSaleCampaign"
	C_PRODUCT                    = "ec.product"

	BUSINESS_INVITATION_CAMPAIGN  = "invitationCampaign"
	BUSINESS_MEMBER_TASK          = "memberTask"
	BUSINESS_MEMBER_MARKETING     = "memberMarketing"
	BUSINESS_DISTRIBUTION_PRODUCT = "distributionProduct"
)

func (CouponService) CheckCouponIsUsed(ctx context.Context, req *request.DetailRequest) (*pb_coupon.CheckCouponIsUsedResponse, error) {
	if err := validators.ValidateRequest(req); err != nil {
		return nil, err
	}

	couponId := bson.ObjectIdHex(req.Id)

	selector := share_model.Base.GenDefaultCondition(ctx)
	selector["tags"] = "群脉零售邀请有礼"
	selector["settings.reward.coupons.id"] = couponId

	doc := bson.M{}
	share_model.Base.GetByCondition(ctx, selector, campaign_model.C_CAMPAIGN, doc)
	if len(doc) > 0 {
		return &pb_coupon.CheckCouponIsUsedResponse{IsUsed: true, Business: "邀请有礼"}, nil
	}

	selector = share_model.Base.GenDefaultCondition(ctx)
	selector["operations.value"] = req.Id

	share_model.Base.GetByCondition(ctx, selector, C_MEMBER_BATCH_OPERATION, doc)
	if len(doc) > 0 {
		return &pb_coupon.CheckCouponIsUsedResponse{IsUsed: true, Business: "批量操作"}, nil
	}

	selector = share_model.Base.GenDefaultCondition(ctx)
	selector["reward.coupons.id"] = couponId

	share_model.Base.GetByCondition(ctx, selector, C_PRODUCT_CODE_REWARD_RULE, doc)
	if len(doc) > 0 {
		return &pb_coupon.CheckCouponIsUsedResponse{IsUsed: true, Business: "产品码活动"}, nil
	}

	selector = share_model.Base.GenDefaultCondition(ctx)
	selector["$or"] = []bson.M{
		{
			"ruleSetting.consecutive.rewards.coupons.id": couponId,
		},
		{
			"ruleSetting.scheduled.rewards.coupons.id": couponId,
		},
		{"ruleSetting.daily.coupons.id": couponId},
	}

	share_model.Base.GetByCondition(ctx, selector, C_CAMPAIGN_CENTER_SIGNIN, doc)
	if len(doc) > 0 {
		return &pb_coupon.CheckCouponIsUsedResponse{IsUsed: true, Business: "签到"}, nil
	}

	selector = share_model.Base.GenDefaultCondition(ctx)
	selector["rewardSetting.prizes"] = bson.M{
		"$elemMatch": bson.M{
			"reward.coupons.id": couponId,
			"isEnabled":         true,
		},
	}

	share_model.Base.GetByCondition(ctx, selector, C_CAMPAIGN_CENTER_LUCKY_DRAW, doc)
	if len(doc) > 0 {
		return &pb_coupon.CheckCouponIsUsedResponse{IsUsed: true, Business: "幸运抽奖"}, nil
	}

	selector = share_model.Base.GenDefaultCondition(ctx)
	selector["workflows.action.coupon.ids"] = req.Id
	share_model.Base.GetByCondition(ctx, selector, C_MARKETO, doc)
	if len(doc) > 0 {
		return &pb_coupon.CheckCouponIsUsedResponse{IsUsed: true, Business: "智能营销"}, nil
	}

	listMemberMarketingsReq := &member.ListMemberMarketingsRequest{
		ListCondition: &request.ListCondition{
			PerPage: 999,
		},
		Business: "loyalty",
		Type:     "marketing",
	}
	resp, _ := ListMemberMarketings(ctx, listMemberMarketingsReq)
	if resp != nil && len(resp.Items) > 0 {
		for _, item := range resp.Items {
			if len(item.Rewards) == 0 {
				continue
			}
			for _, reward := range item.Rewards {
				if reward == nil || reward.Content == nil || reward.Content.Rewards == nil || len(reward.Content.Rewards.Coupons) == 0 {
					continue
				}
				for _, coupon := range reward.Content.Rewards.Coupons {
					if coupon == nil {
						continue
					}
					if coupon.Id == req.Id {
						return &pb_coupon.CheckCouponIsUsedResponse{IsUsed: true, Business: "会员营销"}, nil
					}
				}
			}
		}
	}

	// 检测进店福利中使用使用该优惠券
	selector = share_model.Base.GenDefaultCondition(ctx)
	selector["coupons.id"] = bson.ObjectIdHex(req.Id)
	share_model.Base.GetByCondition(ctx, selector, C_ENTER_STORE_WELFARE, doc)
	if len(doc) > 0 {
		return &pb_coupon.CheckCouponIsUsedResponse{IsUsed: true, Business: "进店福利"}, nil
	}

	listMemberTasksReq := &member.ListMemberTasksRequest{
		Type:      "invitation",
		CouponIds: []string{req.Id},
	}
	listMemberTasksReqResp, _ := ListMemberTasks(ctx, listMemberTasksReq)
	if listMemberTasksReqResp != nil && len(listMemberTasksReqResp.Items) > 0 {
		return &pb_coupon.CheckCouponIsUsedResponse{IsUsed: true, Business: "会员任务"}, nil
	}

	// 检测秒杀活动中使用使用该优惠券
	selector = share_model.Base.GenDefaultCondition(ctx)
	selector["$or"] = []bson.M{
		{
			"periodType":      "once",
			"endAt":           bson.M{"$gt": time.Now()},
			"coupon.couponId": bson.ObjectIdHex(req.Id),
		},
		{
			"periodType":      bson.M{"$in": []string{"weekly", "monthly"}},
			"isStopped":       false,
			"coupon.couponId": bson.ObjectIdHex(req.Id),
		},
	}
	share_model.Base.GetByCondition(ctx, selector, C_FLASH_SALE_CAMPAIGN, doc)
	if len(doc) > 0 {
		return &pb_coupon.CheckCouponIsUsedResponse{IsUsed: true, Business: "秒杀"}, nil
	}

	// 检测分销商品中使用优惠券
	selector = share_model.Base.GenDefaultCondition(ctx)
	selector["distribution.coupon.id"] = bson.ObjectIdHex(req.Id)
	share_model.Base.GetByCondition(ctx, selector, C_PRODUCT, doc)
	if len(doc) > 0 {
		return &pb_coupon.CheckCouponIsUsedResponse{IsUsed: true, Business: "分销商品"}, nil
	}

	return &pb_coupon.CheckCouponIsUsedResponse{}, nil
}
