package jobs

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	core_util "mairpc/core/util"
	pb_coupon "mairpc/proto/coupon"
	"mairpc/service/coupon/model"
	"mairpc/service/coupon/service"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"

	"github.com/spf13/cobra"
)

var (
	membershipDiscountGiftedRecordHeader = "受赠者编号,受赠者昵称,受赠者姓名,受赠者手机,优惠券名称,券码,赠送者编号,赠送者昵称,赠送者姓名,赠送者手机,类型,转赠时间,领取时间,领取渠道,转赠状态\n"

	couponTypeMap = map[string]string{
		model.COUPON_TYPE_DISCOUNT: "折扣券",
		model.COUPON_TYPE_CASH:     "代金券",
		model.COUPON_TYPE_EXCHANGE: "兑换券",
		model.COUPON_TYPE_PRESENT:  "买赠券",
		model.COUPON_TYPE_GIFT:     "礼品券",
		model.COUPON_TYPE_COUPON:   "优惠券",
		model.COUPON_TYPE_CREDIT:   "积分券",
		model.COUPON_TYPE_RED_PACK: "红包券",
		model.COUPON_TYPE_EXTERNAL: "外部券",
		model.COUPON_TYPE_DELIVERY: "运费券",
	}
	giftedStatusMap = map[string]string{
		"gifted":     "转赠成功",
		"giftFailed": "转赠失败",
	}
)

func init() {
	RootCmd.AddCommand(exportMembershipDiscountGiftedRecord)
}

var exportMembershipDiscountGiftedRecord = &cobra.Command{
	Use: "exportMembershipDiscountGiftedRecord",
	RunE: func(cmd *cobra.Command, args []string) error {
		ctx := util.GetContextInJob(args)
		ctx = core_util.CtxWithReadSecondaryPreferred(ctx)
		jobOptions := util.GetArgs(args)
		jobId := core_util.GetSreadminJobName()
		job, _ := job_util.GetJobStatus(ctx, jobId)

		err := job_util.BeginToExport(ctx, jobId)

		if err != nil {
			return err
		}

		byteData, err := json.Marshal(jobOptions["request"])
		if err != nil {
			return err
		}
		isDesensitized, _ := needDesensitize(ctx)

		req := &pb_coupon.SearchCouponLogsRequest{}
		err = json.Unmarshal(byteData, req)
		if err != nil {
			return err
		}

		req.ListCondition.Page = 1
		req.ListCondition.PerPage = 100

		couponService := service.CouponService{}
		url, err := job_util.ExportFile(
			ctx,
			job.Name,
			func(f *os.File) error {
				f.WriteString(membershipDiscountGiftedRecordHeader)
				for {
					resp, err := couponService.SearchCouponLogs(ctx, req)
					if err != nil {
						return err
					}

					for _, item := range resp.CouponLogs {
						giftedAt := item.Gifting.GiftedAt
						if giftedAt != "" {
							giftedAt = core_util.ParseRFC3339(item.Gifting.GiftedAt).Format("2006-01-02 15:04:05") + "\t"
						}
						membershipDiscountCoupon := pb_coupon.MembershipDiscountCoupon{}
						if item.Coupon != nil {
							membershipDiscountCoupon = *item.Coupon
						}
						line := fmt.Sprintf(
							"%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
							item.Gifting.To,
							formatValue(item.Gifting.ReceiverNickname, isDesensitized),
							formatValue(item.Gifting.ReceiverName, isDesensitized),
							DesensitizeTel(isDesensitized, item.Gifting.ReceiverPhone),
							membershipDiscountCoupon.Title,
							item.MembershipDiscountCode,
							item.Member.Id,
							formatValue(item.Member.Nickname, isDesensitized),
							formatValue(item.Member.Name, isDesensitized),
							DesensitizeTel(isDesensitized, item.Member.Phone),
							couponTypeMap[item.Type],
							time.Unix(item.CreatedAt, 0).Format("2006-01-02 15:04:05")+"\t",
							giftedAt,
							item.Channel.Name,
							giftedStatusMap[item.Status],
						)
						f.WriteString(line)
					}
					f.Sync()

					// 判断是否已查完
					if uint32(len(resp.CouponLogs)) < req.ListCondition.PerPage {
						break
					}

					req.ListCondition.Page += 1
				}

				return nil
			},
		)
		if err != nil {
			return err
		}

		err = job_util.ExportSucceed(ctx, jobId, url, job.Name)

		if err != nil {
			return err
		}

		return nil
	},
}

func formatValue(value string, isDesensitized bool) string {
	if strings.Contains(value, ",") {
		value = DesensitizeName(isDesensitized, value)
		return fmt.Sprintf("%s%s%s", "\"", value, "\"")
	}
	return DesensitizeName(isDesensitized, value)
}
