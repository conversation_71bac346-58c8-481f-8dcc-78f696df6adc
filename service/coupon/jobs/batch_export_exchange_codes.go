package jobs

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"mairpc/core/component"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/proto/client"
	pb_coupon "mairpc/proto/coupon"
	"mairpc/service/share/component/oss"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"

	"github.com/spf13/cobra"
)

func init() {
	RootCmd.AddCommand(batchExportExchangeCodes)
}

const (
	EXCHANGE_CODES_FILE_NAME = "兑换码一卡一码兑换码号_%s_%s.csv"
)

var batchExportExchangeCodes = &cobra.Command{
	Use: "batchExportExchangeCodes",
	RunE: func(cmd *cobra.Command, args []string) error {
		ctx := util.GetContextInJob(args)
		ctx = core_util.CtxWithReadSecondaryPreferred(ctx)
		jobOptions := util.GetArgs(args)
		jobId := core_util.GetSreadminJobName()
		err := job_util.BeginToExport(ctx, jobId)
		if err != nil {
			return err
		}
		job, err := job_util.GetJobStatus(ctx, jobId)
		if err != nil {
			return err
		}

		byteData, err := json.Marshal(jobOptions["req"])
		if err != nil {
			return err
		}
		req := &pb_coupon.BatchExportExchangeCodesRequest{}
		json.Unmarshal(byteData, req)
		couponEndpointIds := req.CouponEndpointIds

		exportedFiles := make(chan core_util.File, len(couponEndpointIds))
		var wg sync.WaitGroup
		wg.Add(len(couponEndpointIds))
		for _, id := range couponEndpointIds {
			couponEndpointId := id
			component.GO(ctx, func(ctx context.Context) {
				defer wg.Done()
				couponEndpoint, err := client.GetCouponServiceClient().GetCouponEndpoint(ctx, &pb_coupon.GetCouponEndpointRequest{
					Id: couponEndpointId,
				})
				if err != nil {
					log.Warn(ctx, "Failed to find coupon endpoint", log.Fields{
						"couponEndpointId": couponEndpointId,
						"errMsg":           err.Error(),
					})
					return
				}
				couponEndpointName := couponEndpoint.CouponEndpoint.Name
				fileName := fmt.Sprintf(EXCHANGE_CODES_FILE_NAME, couponEndpointName, util.GetJobTimestamp(time.Now()))
				url, err := exportExchangeCodesFile(ctx, couponEndpointId, couponEndpointName, fileName, req.Prefix)
				if err != nil {
					log.Warn(ctx, "Failed to export exchange codes file", log.Fields{
						"couponEndpointId": couponEndpointId,
						"errMsg":           err.Error(),
					})
					return
				}
				exportedFiles <- core_util.File{
					Name: fileName,
					Url:  url,
				}
			})
		}

		wg.Wait()
		close(exportedFiles)

		// 文件压缩后导出
		zipFileName := job.Name
		_, err = core_util.Zip(zipFileName, func() []core_util.File {
			var files []core_util.File
			for exportedFile := range exportedFiles {
				files = append(files, exportedFile)
			}
			return files
		}())
		if err != nil {
			return err
		}
		ossClient := oss.OSSClient(ctx)
		err = ossClient.PutObject(ctx, zipFileName, zipFileName, nil)
		if err != nil {
			return err
		}
		url, err := ossClient.SignUrl(ctx, zipFileName, oss.HTTPGet, "", oss.EXPIRATION_SECONDS, nil)
		if err != nil {
			return err
		}
		err = job_util.ExportSucceed(ctx, jobId, url, job.Name)
		if err != nil {
			return err
		}
		return nil
	},
}
