package jobs

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"

	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	core_util "mairpc/core/util"
	pb_coupon "mairpc/proto/coupon"
	"mairpc/service/coupon/model"
	job_util "mairpc/service/share/jobs"
	"mairpc/service/share/util"

	"github.com/spf13/cobra"
)

var (
	exchangeCodeHeader = []string{"兑换码名称", "兑换码号", "兑换情况"}
)

func init() {
	RootCmd.AddCommand(exportExchangeCodes)
}

var exportExchangeCodes = &cobra.Command{
	Use: "exportExchangeCodes",
	RunE: func(cmd *cobra.Command, args []string) error {
		ctx := util.GetContextInJob(args)
		ctx = core_util.CtxWithReadSecondaryPreferred(ctx)
		jobOptions := util.GetArgs(args)
		jobId := core_util.GetSreadminJobName()
		job, _ := job_util.GetJobStatus(ctx, jobId)

		err := job_util.BeginToExport(ctx, jobId)

		if err != nil {
			return err
		}

		byteData, err := json.Marshal(jobOptions["req"])
		if err != nil {
			return err
		}

		req := &pb_coupon.ExportExchangeCodesRequest{}
		err = json.Unmarshal(byteData, req)
		if err != nil {
			return err
		}
		var couponEndpointName string
		if name, ok := jobOptions["couponEndpointName"]; ok {
			couponEndpointName, _ = name.(string)
		}
		url, err := exportExchangeCodesFile(ctx, req.CouponEndpointId, couponEndpointName, job.Name, req.Prefix)
		if err != nil {
			return err
		}

		err = job_util.ExportSucceed(ctx, jobId, url, job.Name)

		if err != nil {
			return err
		}

		return nil
	},
}

func exportExchangeCodesFile(ctx context.Context, couponEndpointId, couponEndpointName, fileName, prefix string) (string, error) {
	return job_util.ExportFile(
		ctx,
		fileName,
		func(f *os.File) error {
			w := csv.NewWriter(f)
			// 写入表格标题
			err := w.Write(exchangeCodeHeader)
			if err != nil {
				return err
			}
			page := extension.PagingCondition{
				Selector: bson.M{
					"accountId":        util.GetAccountIdAsObjectId(ctx),
					"isDeleted":        false,
					"couponEndpointId": bson.ObjectIdHex(couponEndpointId),
				},
				PageIndex: 1,
				PageSize:  100,
			}
			for {
				exchangeCodes, _, _ := model.CExchangeCode.FindByPagination(ctx, page)
				if len(exchangeCodes) == 0 {
					break
				}
				for _, exchangeCode := range exchangeCodes {
					if exchangeCode.Status == model.COUPON_CODE_STATUS_USED {
						exchangeCode.Status = "已兑换"
					}
					if exchangeCode.Status == model.COUPON_CODE_STATUS_UNUSED {
						exchangeCode.Status = "未兑换"
					}
					code := fmt.Sprintf("%s\t", exchangeCode.Code)
					if prefix != "" {
						code = fmt.Sprintf("%s%s", prefix, code)
					}
					line := []string{
						couponEndpointName,
						code,
						exchangeCode.Status,
					}
					err := w.Write(line)
					if err != nil {
						return err
					}
				}
				page.PageIndex++
				w.Flush()
			}
			return nil
		},
	)
}
