package share

import (
	"context"
	"mairpc/consumer/handler"
	"mairpc/core/log"
	"mairpc/core/util"
	core_util "mairpc/core/util"
	share_util "mairpc/service/share/util"
	"strings"
	"time"

	"github.com/apache/pulsar-client-go/pulsar"
)

type pulsarMQConsumer struct {
	*Consumer
	pulsarMQClient pulsar.Client
}

const (
	PROPERTY_KEY_TAG_FILTER_EXPRESSION = "_TAG_FILTER_EXPRESSION"
	PROPERTY_KEY_TAG_FILTER_TAG        = "_TAG_FILTER_TAG"
	PROPERTY_KEY_REQUEST_ID            = "REQUEST_ID"
	PROPERTY_KEY_ACCOUNT_ID            = "ACCOUNT_ID"
)

func (c *pulsarMQConsumer) ConsumeMessage(ctx context.Context) {
	subscriptionName := util.GetK8sServiceFullName()
	if subscriptionName == "." {
		c.UpdateStatus(CONSUMER_STATUS_FAILED)
		log.Error(ctx, "[MQTrace] Failed to create consumer", log.Fields{"reason": "K8S_SERVICE_NAME and K8S_SERVICE_NAMESPACE can not be null"})
		return
	}
	options := pulsar.ConsumerOptions{
		Topic:                       share_util.FormatMqTopicForPulsar(ctx, c.Topic),
		Name:                        util.GetK8sHostFullName(),
		SubscriptionName:            subscriptionName,
		SubscriptionInitialPosition: pulsar.SubscriptionPositionEarliest, // 避免从 RMQ 切换到 Pulsar 时丢消息
		SubscriptionProperties:      map[string]string{PROPERTY_KEY_TAG_FILTER_EXPRESSION: c.Tag},
		Type:                        pulsar.Shared,
		ReceiverQueueSize:           c.Concurrency, // 设置为小值以防内存占用过高或者大量消息堆积在异常实例上
	}
	consumer, err := c.pulsarMQClient.Subscribe(options)
	if err != nil {
		c.UpdateStatus(CONSUMER_STATUS_FAILED)
		log.Error(ctx, "[MQTrace] Failed to create consumer", log.Fields{"errMsg": err.Error()})
		return
	}

	msgChan := make(chan pulsar.Message, c.Concurrency)
	for i := 0; i < c.Concurrency; i++ {
		go c.consumeMessage(ctx, msgChan, consumer)
	}

	for !c.CheckStatus(CONSUMER_STATUS_ENDED) {
		message, err := consumer.Receive(ctx)
		if err != nil {
			// 暂不知道什么情况下会出现
			log.Error(ctx, "[MQTrace] Failed to receive message", log.Fields{"errorMsg": err.Error()})
			continue
		}
		msgChan <- message
	}
	consumer.Close()
	c.pulsarMQClient.Close()
	close(msgChan)
}

func (c *pulsarMQConsumer) consumeMessage(ctx context.Context, msgChan <-chan pulsar.Message, consumer pulsar.Consumer) {
	for message := range msgChan {
		c.State.ConsumedCount++
		c.State.LastConsumeStartAt = time.Now()
		c.State.LastConsumeProcessAt = time.Now()
		c.UpdateStatus(CONSUMER_STATUS_RUNNING)
		ctx = getContextFromMessage(context.Background(), message)
		log.Warn(ctx, "[MQTrace] Consuming message", log.Fields{
			"topic": message.Topic(),
			"id":    message.ID().String(),
			"key":   message.Key(),
			"tag":   message.Properties()[PROPERTY_KEY_TAG_FILTER_TAG],
			// 双引号替换为单引号是为了避免在日志中显示时因 JSON 字符冲突而出现大量转义符
			"messageBody": strings.ReplaceAll(string(message.Payload()), "\"", "'"),
		})
		start := time.Now()
		consume(ctx, formatMessageFromPulsarMq(message, c.Topic, c.Tag))
		durationMillis := time.Since(start).Milliseconds()
		c.State.ConsumedMessageCount++
		ackErr := consumer.Ack(message)
		c.State.LastConsumeEndAt = time.Now()
		if ackErr != nil {
			c.State.LastError = ackErr
			c.State.LastErrorAt = time.Now()
			log.Error(ctx, "[MQTrace] Failed to ack message", log.Fields{
				"id":             message.ID().String(),
				"key":            message.Key(),
				"durationMillis": durationMillis,
				"errorMsg":       ackErr.Error(),
			})
			return
		}
		log.Warn(ctx, "[MQTrace] Consumed message", log.Fields{
			"id":             message.ID().String(),
			"key":            message.Key(),
			"durationMillis": durationMillis,
		})
	}
}

func formatMessageFromPulsarMq(message pulsar.Message, topic, tag string) handler.Message {
	return handler.Message{
		MessageBody: message.Payload(),
		MessageId:   message.ID().String(),
		MessageTag:  message.Properties()[PROPERTY_KEY_TAG_FILTER_TAG],
		Topic:       topic,
		Tag:         tag,
	}
}

func getContextFromMessage(ctx context.Context, message pulsar.Message) context.Context {
	ctx = core_util.CtxWithRequestID(ctx, message.Properties()[PROPERTY_KEY_REQUEST_ID])
	ctx = core_util.CtxWithAccountID(ctx, message.Properties()[PROPERTY_KEY_ACCOUNT_ID])
	return ctx
}
