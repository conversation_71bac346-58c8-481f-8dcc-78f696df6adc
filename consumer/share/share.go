package share

import (
	"fmt"
	"mairpc/consumer/handler"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"runtime"
	"strings"
	"time"

	mq_http_sdk "github.com/aliyunmq/mq-http-go-sdk"
	"github.com/apache/pulsar-client-go/pulsar"
	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/consumer"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"golang.org/x/net/context"
)

const (
	MESSAGE_QUEUE_PROVIDER_ROCKETMQ = "rocketmq"
	MESSAGE_QUEUE_PROVIDER_PULSAR   = "pulsar"
	MESSAGE_QUEUE_PROVIDER_ALIBABA  = "alibaba"

	TIME_OUT            = 35 * time.Second
	MESSAGE_COUNT       = 1
	WAIT_SECOND   int64 = 3
)

type MQConsumer interface {
	ConsumeMessage(ctx context.Context)
}

func GetMqConsumer(ctx context.Context, c *Consumer) MQConsumer {
	provider := c.Provider
	if core_util.IsTencentMq() {
		provider = MESSAGE_QUEUE_PROVIDER_ROCKETMQ
	}
	switch provider {
	case MESSAGE_QUEUE_PROVIDER_ROCKETMQ:
		endpoints := strings.Split(c.Endpoints, ";")
		for i := range endpoints {
			endpoints[i] = fmt.Sprintf("%s%s", "http://", endpoints[i])
		}
		consumerOptions := []consumer.Option{
			consumer.WithGroupName(c.GroupId),
			consumer.WithNsResolver(primitive.NewPassthroughResolver(endpoints)),
			consumer.WithCredentials(primitive.Credentials{
				AccessKey: c.AccessKey,
				SecretKey: c.SecretKey,
			}),
			consumer.WithInstance(c.Instance),
			consumer.WithConsumeMessageBatchMaxSize(MESSAGE_COUNT),
		}
		client, err := rocketmq.NewPushConsumer(
			consumerOptions...,
		)
		if err != nil {
			log.Error(ctx, "Failed to get RocketMQ mq client", log.Fields{
				"groupId":  c.GroupId,
				"endPoint": endpoints,
				"instance": c.Instance,
				"errMsg":   err.Error(),
			})
			return nil
		}
		return &rocketMQConsumer{c, client}
	case MESSAGE_QUEUE_PROVIDER_PULSAR:
		options := pulsar.ClientOptions{
			URL:               core_util.GetMqUrl(),
			Authentication:    pulsar.NewAuthenticationToken(core_util.GetMqAKSecret()),
			OperationTimeout:  30 * time.Second,
			KeepAliveInterval: 10 * time.Second,
			ConnectionTimeout: 10 * time.Second,
		}
		client, err := pulsar.NewClient(options)
		if err != nil {
			log.Error(ctx, "Failed to get pulsar mq client", log.Fields{
				"mqUrl":   core_util.GetMqUrl(),
				"groupId": c.GroupId,
				"errMsg":  err.Error(),
			})
			return nil
		}
		return &pulsarMQConsumer{c, client}
	default:
		client := mq_http_sdk.NewAliyunMQClient(c.Endpoints, c.AccessKey, c.SecretKey, "")

		return &alibabaMQConsumer{c, client}
	}
}

func consume(ctx context.Context, message handler.Message) {
	defer func() {
		if r := recover(); r != nil {
			stack := make([]byte, log.MaxStackSize)
			stack = stack[:runtime.Stack(stack, false)]
			log.ErrorTrace(ctx, "Consume message panic", log.Fields{
				"error": fmt.Sprintf("%v", r),
			}, stack)
		}
	}()
	handle := message.GetHandler()
	handle(ctx, message)
}
