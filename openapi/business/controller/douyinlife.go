package controller

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	"mairpc/core/component"
	core_component "mairpc/core/component"
	"mairpc/core/component/douyinlife"
	core_errors "mairpc/core/errors"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	"mairpc/openapi/business/util"
	business_util "mairpc/openapi/business/util"
	pb_client "mairpc/proto/client"
	"mairpc/proto/common/origin"
	pb_request "mairpc/proto/common/request"
	"mairpc/proto/common/types"
	mairpc_coupon "mairpc/proto/coupon"
	pb_coupon "mairpc/proto/coupon"
	pb_order "mairpc/proto/ec/order"
	mairpc_member "mairpc/proto/member"
	pbMember "mairpc/proto/member"
	"mairpc/proto/store"
	mairpc_trade "mairpc/proto/trade/order"
	coupon_model "mairpc/service/coupon/model"
	share_component "mairpc/service/share/component"
	"mairpc/service/share/constant"

	"github.com/gin-gonic/gin"
)

func init() {
	douyinlifeBind := &ControllerAction{
		Name:        "member_douyinlife_bind",
		Method:      "POST",
		Path:        "/member/douyinlife/bind",
		HandlerFunc: HandleDouyinlifeBind,
	}

	douyinlifeUnbind := &ControllerAction{
		Name:        "member_douyinlife_unbind",
		Method:      "POST",
		Path:        "/member/douyinlife/unbind",
		HandlerFunc: HandleDouyinlifeUnbind,
	}

	douyinlifeCouponPreIssue := &ControllerAction{
		Name:        "coupon_douyinlife_pre_issue",
		Method:      "POST",
		Path:        "/coupon/douyinlife/preIssue",
		HandlerFunc: HandleDouyinlifeCouponPreIssue,
	}

	douyinlifeCouponIssue := &ControllerAction{
		Name:        "coupon_douyinlife_issue",
		Method:      "POST",
		Path:        "/coupon/douyinlife/issue",
		HandlerFunc: HandleDouyinlifeCouponIssue,
	}

	douyinlifeCouponRefund := &ControllerAction{
		Name:        "coupon_douyinlife_refund",
		Method:      "POST",
		Path:        "/coupon/douyinlife/refund",
		HandlerFunc: HandleDouyinlifeCouponRefund,
	}

	douyinlifeCouponStatus := &ControllerAction{
		Name:        "coupon_douyinlife_get_status",
		Method:      "POST",
		Path:        "/coupon/douyinlife/getStatus",
		HandlerFunc: HandleDouyinlifeGetCouponStatus,
	}

	douyinlifeCouponForceRefund := &ControllerAction{
		Name:        "coupon_douyinlife_force_refund",
		Method:      "POST",
		Path:        "/coupon/douyinlife/forceRefund",
		HandlerFunc: HandleDouyinlifeCouponForceRefund,
	}

	douyinlifeTakeoutOrderCreate := &ControllerAction{
		Name:        "takeout_douyinlife_order_create",
		Method:      "POST",
		Path:        "/takeout/douyinlife/order",
		HandlerFunc: HandleDouyinlifeTakeoutOrderCreate,
	}

	enableActions(
		douyinlifeBind,
		douyinlifeUnbind,
		douyinlifeCouponPreIssue,
		douyinlifeCouponIssue,
		douyinlifeCouponRefund,
		douyinlifeCouponStatus,
		douyinlifeCouponForceRefund,
		douyinlifeTakeoutOrderCreate,
	)
}

type DouyinlifeBindRequest struct {
	OpenId    string `json:"open_id" binding:"required"`
	AccountId string `json:"account_id" binding:"required"`
	Mobile    string `json:"mobile"`
}

type DouyinlifeBindResponse struct {
	Data DouyinlifeBindResponseData `json:"data"`
}

type DouyinlifeBindResponseData struct {
	ErrorCode       int64  `json:"error_code"`
	Description     string `json:"description"`
	PointAmountCent int64  `json:"point_amount_cent"`
	UserLevel       uint32 `json:"user_level"`
	IsNewMember     bool   `json:"is_new_member"`
}

type DouyinlifeCommonResponse struct {
	Data DouyinlifeCommonResponseData `json:"data"`
}

type DouyinlifeCommonResponseData struct {
	ErrorCode     int64  `json:"error_code"`
	Description   string `json:"description"`
	DouyinOrderId string `json:"order_id,omitempty"`
	OrderId       string `json:"order_out_id,omitempty"`
}

type DouyinlifeCouponPreIssueRequest struct {
	DouyinOrderId   string                   `json:"order_id" binding:"required"`
	DouyinSkuId     string                   `json:"sku_id"`
	MaiCouponId     string                   `json:"third_product_id" binding:"required"`
	Count           int32                    `json:"count"`
	OrderItemId     []string                 `json:"order_item_id"`
	MaiSkuId        string                   `json:"third_sku_id,omitempty"`
	OrderType       int32                    `json:"order_type"`
	OriginalAmount  int32                    `json:"original_amount"`
	CurrencyCode    string                   `json:"currency_code"`
	CreateOrderTime int64                    `json:"create_order_time"`
	Users           []DouyinlifeCouponUser   `json:"tourists"`
	Contact         *DouyinlifeCouponContact `json:"contact,omitempty"`
}

type DouyinlifeCouponUser struct {
	Name           string `json:"name"`
	Phone          string `json:"phone"`
	IdCard         string `json:"id_card"`
	CredentialType int32  `json:"credential_type,omitempty"`
}

type DouyinlifeCouponContact struct {
	Name          string `json:"name"`
	Phone         string `json:"phone"`
	CompletePhone string `json:"complete_phone"`
}

type DouyinlifeCouponPreIssueResponse struct {
	Data DouyinlifeCouponPreIssueResponseData `json:"data"`
}

type DouyinlifeCouponPreIssueResponseData struct {
	ErrorCode   int32  `json:"error_code"`
	Description string `json:"description"`
	ExtOrderId  string `json:"ext_order_id"`
}

type DouyinlifeCouponIssueRequest struct {
	DouyinOrderId string                 `json:"order_id" binding:"required"`
	MaiOrderId    string                 `json:"third_order_id"`
	Count         int                    `json:"count" binding:"required"`
	StartTime     int                    `json:"start_time" binding:"required"`
	ExpireTime    int                    `json:"expire_time" binding:"required"`
	DouyinSkuId   string                 `json:"sku_id" binding:"required"`
	MaiCouponId   string                 `json:"third_sku_id" binding:"required"`
	DouyinSku     DouyinlifeSku          `json:"sku" binding:"required"`
	OpenId        string                 `json:"open_id" binding:"required"`
	Amount        DouyinlifeCouponAmount `json:"amount"`
	Tourists      []DouyinlifeCouponUser `json:"tourists"`
}

type DouyinlifeSku struct {
	SkuName     string             `json:"sku_name" binding:"required"`
	SkuId       string             `json:"sku_id" binding:"required"`
	MaiCouponId string             `json:"third_sku_id" binding:"required"`
	GrouponType int32              `json:"groupon_type" binding:"required"`
	TimeCard    DouyinlifeTimeCard `json:"time_card"`
}

type DouyinlifeTimeCard struct {
	TimesCount int32 `json:"times_count"`
}

type DouyinlifeCouponAmount struct {
	ListPrice             int `json:"list_price"`
	OriginalAmount        int `json:"original_amount"`
	PayAmount             int `json:"pay_amount"`
	TicketAmount          int `json:"ticket_amount"`
	MerchantTicketAmount  int `json:"merchant_ticket_amount"`
	FeeAmount             int `json:"fee_amount"`
	CommissionAmount      int `json:"commission_amount"`
	PaymentDiscountAmount int `json:"payment_discount_amount"`
	CouponPayAmount       int `json:"coupon_pay_amount"`
}

type DouyinlifeCouponIssueResponse struct {
	Data DouyinlifeCouponIssueResponseData `json:"data"`
}

type DouyinlifeCouponIssueResponseData struct {
	ErrorCode   int64    `json:"error_code"`
	Description string   `json:"description"`
	Result      int      `json:"result"`
	Codes       []string `json:"codes,omitempty"`
}

type DouyinlifeCouponRefundRequest struct {
	DouyinOrderId string                  `json:"order_id" binding:"required"`
	AfterSaleId   string                  `json:"after_sale_id"`
	Certificates  []DouyinlifeCertificate `json:"certificates" binding:"required"`
}

type DouyinlifeCertificate struct {
	CertificateId string `json:"certificate_id" binding:"required"`
	Code          string `json:"code"`
	TotalCount    int64  `json:"total_count"`
	VerifyCount   int64  `json:"verify_count"`
}

type DouyinlifeCouponRefundResponse struct {
	Data DouyinlifeCouponRefundResponseData `json:"data"`
}

type DouyinlifeCouponRefundResponseData struct {
	ErrorCode   int64    `json:"error_code"`
	Description string   `json:"description"`
	Result      int      `json:"result"`
	Reason      string   `json:"reason"`
	Codes       []string `json:"codes,omitempty"`
}

type DouyinlifeGetCouponStatusRequest struct {
	Scene          string   `json:"scene"`
	DouyinOrderIds []string `json:"order_ids" binding:"required"`
}

type DouyinlifeGetCouponStatusResponse struct {
	Data DouyinlifeGetCouponStatusResponseData `json:"data"`
}

type DouyinlifeGetCouponStatusResponseData struct {
	ErrorCode   int                              `json:"error_code"`
	Description string                           `json:"description"`
	Result      map[string]DouyinlifeOrderDetail `json:"result"`
}

type DouyinlifeOrderDetail struct {
	OrderId        string                 `json:"order_id"`
	OutOrderId     string                 `json:"out_order_id,omitempty"`
	OrderStatus    int                    `json:"order_status"`
	OrderItems     []DouyinlifeOrderItem  `json:"order_items,omitempty"`
	Codes          []DouyinlifeCodeRecord `json:"codes"`
	TotalQuantity  int                    `json:"total_quantity"`
	UsedQuantity   int                    `json:"used_quantity"`
	RefundQuantity int                    `json:"refund_quantity"`
	Vouchers       []DouyinlifeVoucher    `json:"vouchers,omitempty"`
}

type DouyinlifeOrderItem struct {
	SkuId         string `json:"sku_id"`
	OutSkuId      string `json:"out_sku_id"`
	OrderItemId   string `json:"order_item_id"`
	CertificateId string `json:"certificate_id"`
	ItemStatus    int    `json:"item_status"`
	RefundAmount  int    `json:"refund_amount"`
	RefundTime    int    `json:"refund_time"`
}

type DouyinlifeCodeRecord struct {
	Value        string `json:"value"`
	Type         string `json:"type"`
	FulfilStatus int    `json:"fulfil_status"`
	FulfilTime   int    `json:"fulfil_time,omitempty"`
	VerifyToken  string `json:"verify_token,omitempty"`
	PoiId        string `json:"poi_id,omitempty"`
}

type DouyinlifeVoucher struct {
	Entrance DouyinlifeEntrance  `json:"entrance"`
	Projects []DouyinlifeProject `json:"projects"`
}

type DouyinlifeEntrance struct {
	ProjectId string                 `json:"project_id"`
	Records   []DouyinlifeCodeRecord `json:"records"`
}

type DouyinlifeProject struct {
	Project     string                 `json:"project_id"`
	ProjectName string                 `json:"project_name"`
	Records     []DouyinlifeCodeRecord `json:"records"`
}

type DouyinlifeCouponForceRefundRequest struct {
	DouyinOrderId string                        `json:"order_id" binding:"required"`
	AfterSaleId   string                        `json:"after_sale_id"`
	NoticeList    []DouyinlifeCouponForceRefund `json:"notice_list" binding:"required"`
}

type DouyinlifeCouponForceRefund struct {
	CertificateId string `json:"certificate_id" binding:"required"`
	Code          string `json:"code" binding:"required"`
	TotalCount    int64  `json:"total_count"`
	VerifyCount   int64  `json:"verify_count"`
	ApplyTime     int    `json:"apply_time" binding:"required"`
	AuditTime     int    `json:"audit_time" binding:"required"`
	AuditResult   int    `json:"audit_result" binding:"required"`
}

type DouyinlifeTakeoutCreateOrderRequest struct {
	DouyinOrderId      string                            `json:"order_id" binding:"required"`
	OpenId             string                            `json:"open_id"`
	AccountId          string                            `json:"account_id"`
	DouyinStoreId      string                            `json:"poi_id"`
	Products           []DouyinlifeTakeoutProduct        `json:"sku_list"`
	OrderDeductionList []DouyinlifeTakeoutOrderDeduction `json:"order_deduction_list" binding:"required"`
	FreightAmount      int32                             `json:"freight_amount"`
}

type DouyinlifeTakeoutProduct struct {
	DouyinProductId string                  `json:"product_id"`
	ProductId       string                  `json:"product_out_id"`
	DouyinSkuId     string                  `json:"sku_id"`
	SkuId           string                  `json:"sku_out_id"`
	Count           int32                   `json:"count"`
	UnitAmount      int32                   `json:"unit_amount"`
	ItemList        []DouyinlifeTakeoutItem `json:"item_list"`
}

type DouyinlifeTakeoutItem struct {
	DouyinItemProductId string                               `json:"item_product_id"`
	ItemProductId       string                               `json:"item_product_out_id"`
	DouyinItemSkuId     string                               `json:"item_sku_id"`
	ItemSkuId           string                               `json:"item_sku_out_id"`
	AffiliatedList      []DouyinlifeTakeoutAffiliate         `json:"affiliated_list"`
	DeductionInfoList   []DouyinlifeTakeoutItemDeductionInfo `json:"deduction_info_list"`
}

type DouyinlifeTakeoutAffiliate struct {
	DouyinAffiliatedSkuId string `json:"affiliated_sku_id"`
	AffiliatedSkuId       string `json:"affiliated_sku_out_id"`
}

type DouyinlifeTakeoutItemDeductionInfo struct {
	DeductionAmount        int32  `json:"deduction_amount"`
	DeductionCertificateId string `json:"deduction_certificate_id"`
	ThirdPartCode          string `json:"third_part_code"`
}

type DouyinlifeTakeoutOrderDeduction struct {
	CertificateId          string `json:"certificate_id"`
	Amount                 int32  `json:"amount"`
	MerchantDiscountAmount int32  `json:"merchant_discount_amount"`
	ThirdPartCode          string `json:"third_part_code" binding:"required"`
}

type DouyinlifeTakeoutMessageCommonRequest struct {
	Event   string `json:"event" binding:"required"`
	Content string `json:"content"`
}

type DouyinlifeTakeoutOrderPaySuccessContent struct {
	Order     DouyinlifeTakeoutOrder     `json:"order" binding:"required"`
	Store     DouyinlifeTakeoutStore     `json:"poi"`
	Merchant  DouyinlifeTakeoutMerchant  `json:"merchant"`
	BuyerInfo DouyinlifeTakeoutBuyerInfo `json:"buyer_info"`
}

type DouyinlifeTakeoutOrder struct {
	DouyinOrderId  string `json:"order_id" binding:"required"`
	ShopNumber     string `json:"shop_number"`
	IsBook         int32  `json:"is_book"`
	IsSelfDeliver  int32  `json:"is_self_deliver"`
	SysExpectTime  string `json:"sys_expect_time"`
	Remark         string `json:"remark"`
	CreateTime     int64  `json:"create_time"`
	PayTime        int64  `json:"pay_time"`
	DeliverMode    int32  `json:"deliver_mode"`
	TableWare      string `json:"table_ware"`
	IsDeliverLater bool   `json:"is_deliver_later"`
	SkuTypeNum     int32  `json:"sky_type_num"`
	SkuNum         int32  `json:"sku_num"`
}

type DouyinlifeTakeoutStore struct {
	DouyinStoreId string `json:"poi_id"`
	StoreName     string `json:"poi_name"`
}

type DouyinlifeTakeoutMerchant struct {
	AccountId   string `json:"account_id"`
	AccountName string `json:"account_name"`
}

type DouyinlifeTakeoutBuyerInfo struct {
	BuyerPhone        string `json:"buyer_phone"`
	BuyerSecretNumber string `json:"buyer_secret_number"`
	BuyerRealPhone    string `json:"buyer_real_phone"`
}

type DouyinlifeTakeoutOrderRefundContent struct {
	DouyinOrderId     string                               `json:"order_id"`
	Merchant          DouyinlifeTakeoutMerchant            `json:"takeout_merchant"`
	DouyinAfterSaleId string                               `json:"after_sale_id"`
	ApplySource       string                               `json:"apply_source"`
	Reasons           []DouyinlifeTakeoutOrderRefundReason `json:"reasons"`
	AccountId         int                                  `json:"account_id"`
	OrderId           string                               `json:"order_out_id"`
}

type DouyinlifeTakeoutOrderRefundReason struct {
	ReasonCode int    `json:"code"`
	Msg        string `json:"msg"`
}

const (
	DOUYINLIFE_SUCCESS_CODE = 0
	DOUYINLIFE_FAILED_CODE  = 100

	DOUYINLIFE_COUPON_FAILED_CODE_NOT_EXISTS = 1
	DOUYINLIFE_COUPON_FAILED_CODE_OTHERS     = 20

	DOUYINLIFE_COUPON_ISSUE_CODE_PENDING = 0
	DOUYINLIFE_COUPON_ISSUE_CODE_SUCCESS = 1
	DOUYINLIFE_COUPON_ISSUE_CODE_FAILED  = 2

	DOUYINLIFE_GET_COUPON_STATUS_FAILED_CODE_OTHERS           = 99999
	DOUYINLIFE_GET_COUPON_STATUS_FAILED_CODE_ORDER_NOT_FOUND  = 1
	DOUYINLIFE_GET_COUPON_STATUS_FAILED_CODE_COUPON_NOT_FOUND = 2

	DOUYINLIFE_COUPON_STATUS_CODE_USED     = 1
	DOUYINLIFE_COUPON_STATUS_CODE_UNUSED   = 2
	DOUYINLIFE_COUPON_STATUS_CODE_REFUNDED = 3

	DOUYINLIFE_COUPON_ORDER_STATUS_CODE_UNPAID    = 100
	DOUYINLIFE_COUPON_ORDER_STATUS_CODE_CANCELED  = 101
	DOUYINLIFE_COUPON_ORDER_STATUS_CODE_PAID      = 200
	DOUYINLIFE_COUPON_ORDER_STATUS_CODE_AVAILABLE = 201
	DOUYINLIFE_COUPON_ORDER_STATUS_CODE_FINISH    = 1

	DOUYINLIFE_COUPON_REFUND_STATUS_CODE_PENDING = 0
	DOUYINLIFE_COUPON_REFUND_STATUS_CODE_AGREE   = 1
	DOUYINLIFE_COUPON_REFUND_STATUS_CODE_REJECT  = 2

	THIRD_PARTY_COUPON_TRADE_STATUS_UNPAID    = "unpaid"
	THIRD_PARTY_COUPON_TRADE_STATUS_CANCELED  = "cancel"
	THIRD_PARTY_COUPON_TRADE_STATUS_PAID      = "paid"
	THIRD_PARTY_COUPON_TRADE_STATUS_AVAILABLE = "available"
	THIRD_PARTY_COUPON_TRADE_STATUS_FINISH    = "finish"

	DOUYINLIFE_TAKEOUT_EVENT_ORDER_PAY_CLOSE       = "life_takeout_order_pay_close"
	DOUYINLIFE_TAKEOUT_EVENT_ORDER_PAY_SUCCESS     = "life_takeout_order_pay_success"
	DOUYINLIFE_TAKEOUT_EVENT_ORDER_MERCHANT_REFUSE = "life_takeout_order_merchant_refuse"
	DOUYINLIFE_TAKEOUT_EVENT_TRADE_REFUND_COMPLETE = "life_trade_refund_complete"

	DOUYINLIFE_TAKEOUT_REFUND_TYPE_USER_FULFILMENT = "USER_FULFILMENT_REFUND"

	DOUYINLIFE_SUCCESS = "success"
)

var douyinlifeTakeoutEventLabel = []string{
	DOUYINLIFE_TAKEOUT_EVENT_ORDER_PAY_CLOSE,
	DOUYINLIFE_TAKEOUT_EVENT_ORDER_PAY_SUCCESS,
	DOUYINLIFE_TAKEOUT_EVENT_ORDER_MERCHANT_REFUSE,
	DOUYINLIFE_TAKEOUT_EVENT_TRADE_REFUND_COMPLETE,
}

var douyinlifeCouponOrderTypeLabel = map[int32]string{
	douyinlife.ORDER_TYPE_GROUPON:    "groupon",
	douyinlife.ORDER_TYPE_PRE_SALE:   "presale",
	douyinlife.ORDER_TYPE_DELIVERY_1: "delivery",
}

var douyinlifeCouponOrderStatusLabel = map[string]int{
	THIRD_PARTY_COUPON_TRADE_STATUS_UNPAID:    DOUYINLIFE_COUPON_ORDER_STATUS_CODE_UNPAID,
	THIRD_PARTY_COUPON_TRADE_STATUS_CANCELED:  DOUYINLIFE_COUPON_ORDER_STATUS_CODE_CANCELED,
	THIRD_PARTY_COUPON_TRADE_STATUS_PAID:      DOUYINLIFE_COUPON_ORDER_STATUS_CODE_PAID,
	THIRD_PARTY_COUPON_TRADE_STATUS_AVAILABLE: DOUYINLIFE_COUPON_ORDER_STATUS_CODE_AVAILABLE,
	THIRD_PARTY_COUPON_TRADE_STATUS_FINISH:    DOUYINLIFE_COUPON_ORDER_STATUS_CODE_FINISH,
}

func HandleDouyinlifeBind(c *gin.Context) {
	business_util.SwitchOnResponseBodyLog(c)
	var req DouyinlifeBindRequest
	if err := c.ShouldBind(&req); err != nil {
		writeJson(c, &DouyinlifeBindResponse{
			Data: DouyinlifeBindResponseData{
				ErrorCode:   DOUYINLIFE_FAILED_CODE,
				Description: err.Error(),
			},
		})
		return
	}
	upsertMemberReq := &pbMember.UpsertMemberRequest{
		OriginFrom: &origin.OriginInfo{
			Channel:       c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
			OpenId:        req.OpenId,
			Origin:        constant.DOUYINLIFE,
			Subscribed:    true,
			SubscribeTime: time.Now().Unix(),
			ChannelName:   c.Request.Header.Get(core_util.CHANNEL_NAME_IN_HEADER),
		},
		IsActivated:      &types.BoolValue{Value: true},
		ActivationSource: constant.GenActivationSource(constant.DOUYINLIFE, "others"),
	}
	if req.Mobile != "" {
		upsertMemberReq.Properties = []*pbMember.PropertyInfo{
			{
				PropertyId: "mobile",
				Value: &pbMember.PropertyInfo_ValueString{
					ValueString: &pbMember.PropertyStringValue{
						Value: req.Mobile,
					},
				},
			},
		}
	}
	rpcMember, err := UpsertMember(c, upsertMemberReq)
	if err != nil {
		writeJson(c, &DouyinlifeBindResponse{
			Data: DouyinlifeBindResponseData{
				ErrorCode:   DOUYINLIFE_FAILED_CODE,
				Description: err.Error(),
			},
		})
		return
	}
	resp := &DouyinlifeBindResponse{
		Data: DouyinlifeBindResponseData{
			ErrorCode:       DOUYINLIFE_SUCCESS_CODE,
			Description:     DOUYINLIFE_SUCCESS,
			PointAmountCent: rpcMember.Score * 100,
			UserLevel:       rpcMember.Level,
			IsNewMember:     false,
		},
	}
	if resp.Data.UserLevel == 0 {
		resp.Data.UserLevel = 1
	} else if resp.Data.UserLevel > 10 {
		resp.Data.UserLevel = 10
	}
	if rpcMember.OriginFrom.Origin == constant.DOUYINLIFE && rpcMember.OriginFrom.IsOriginal {
		resp.Data.IsNewMember = true
	}
	channelId := c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER)
	triggerDouyinlifeEvent(c, rpcMember.Id, channelId, req.OpenId, req.Mobile, share_component.MAIEVENT_DOUYINLIFE_BIND)
	writeJson(c, resp)
}

func HandleDouyinlifeUnbind(c *gin.Context) {
	business_util.SwitchOnResponseBodyLog(c)
	var req DouyinlifeBindRequest
	if err := c.ShouldBind(&req); err != nil {
		writeJson(c, &DouyinlifeCommonResponse{
			Data: DouyinlifeCommonResponseData{
				ErrorCode:   DOUYINLIFE_FAILED_CODE,
				Description: err.Error(),
			},
		})
		return
	}
	channelId := c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER)
	rpcMember := GetMemberByUniqueId(c, req.OpenId)
	if rpcMember == nil {
		log.Warn(c, "Failed to find member for douyinlife member unbind", log.Fields{
			"req": req,
		})
		writeJson(c, &DouyinlifeCommonResponse{
			Data: DouyinlifeCommonResponseData{
				ErrorCode:   DOUYINLIFE_FAILED_CODE,
				Description: "Member not found",
			},
		})
		return
	}
	bindChannelReq := &mairpc_member.BindChannelRequest{
		MemberId:     rpcMember.Id,
		Origin:       constant.DOUYINLIFE,
		ChannelId:    channelId,
		OpenId:       req.OpenId,
		Type:         "update",
		IsSubscribed: false,
		ChannelName:  c.Request.Header.Get(core_util.CHANNEL_NAME_IN_HEADER),
	}
	if _, err := BindChannel(c, bindChannelReq); err != nil {
		writeJson(c, &DouyinlifeCommonResponse{
			Data: DouyinlifeCommonResponseData{
				ErrorCode:   DOUYINLIFE_FAILED_CODE,
				Description: err.Error(),
			},
		})
		return
	}
	triggerDouyinlifeEvent(c, rpcMember.Id, channelId, req.OpenId, req.Mobile, share_component.MAIEVENT_DOUYINLIFE_UNBIND)
	writeJson(c, &DouyinlifeCommonResponse{
		Data: DouyinlifeCommonResponseData{
			ErrorCode:   DOUYINLIFE_SUCCESS_CODE,
			Description: DOUYINLIFE_SUCCESS,
		},
	})
}

func HandleDouyinlifeCouponPreIssue(c *gin.Context) {
	business_util.SwitchOnResponseBodyLog(c)
	var req DouyinlifeCouponPreIssueRequest
	if err := c.ShouldBind(&req); err != nil {
		writeJson(c, &DouyinlifeCouponPreIssueResponse{
			Data: DouyinlifeCouponPreIssueResponseData{
				ErrorCode:   DOUYINLIFE_COUPON_FAILED_CODE_OTHERS,
				Description: err.Error(),
			},
		})
		return
	}
	if err := coupon_model.CCoupon.GetById(c, bson.ObjectIdHex(req.MaiCouponId), bson.ObjectIdHex(getAccountId(c)), false); err != nil {
		writeJson(c, &DouyinlifeCouponPreIssueResponse{
			Data: DouyinlifeCouponPreIssueResponseData{
				ErrorCode:   DOUYINLIFE_COUPON_FAILED_CODE_NOT_EXISTS,
				Description: "product not exists",
			},
		})
		return
	}
	thirdPartyCouponTrade, _ := pb_client.GetTradeOrderServiceClient().GetThirdPartyCouponTrade(GetGrpcContext(c), &mairpc_trade.GetThirdPartyCouponTradeRequest{
		OuterOrderId: req.DouyinOrderId,
	})
	if thirdPartyCouponTrade != nil {
		writeJson(c, &DouyinlifeCouponPreIssueResponse{
			Data: DouyinlifeCouponPreIssueResponseData{
				ErrorCode:   DOUYINLIFE_SUCCESS_CODE,
				Description: DOUYINLIFE_SUCCESS,
				ExtOrderId:  thirdPartyCouponTrade.Id,
			},
		})
		return
	}
	request, err := genDouyinlifeCreateCouponRequest(c, &req)
	if err != nil {
		writeJson(c, &DouyinlifeCouponPreIssueResponse{
			Data: DouyinlifeCouponPreIssueResponseData{
				ErrorCode:   DOUYINLIFE_COUPON_FAILED_CODE_OTHERS,
				Description: err.Error(),
			},
		})
		return
	}
	response, rpcErr := pb_client.GetTradeOrderServiceClient().CreateThirdPartyCouponTrade(GetGrpcContext(c), request)
	if rpcErr != nil {
		log.Warn(c, "Failed to create douyinlife third party coupon trade", log.Fields{
			"errMsg": rpcErr.Error(),
		})
		writeJson(c, &DouyinlifeCouponPreIssueResponse{
			Data: DouyinlifeCouponPreIssueResponseData{
				ErrorCode:   DOUYINLIFE_COUPON_FAILED_CODE_OTHERS,
				Description: "Internal Server Error",
			},
		})
		return
	}
	writeJson(c, &DouyinlifeCouponPreIssueResponse{
		Data: DouyinlifeCouponPreIssueResponseData{
			ErrorCode:   DOUYINLIFE_SUCCESS_CODE,
			Description: DOUYINLIFE_SUCCESS,
			ExtOrderId:  response.Id,
		},
	})
}

func HandleDouyinlifeCouponIssue(c *gin.Context) {
	business_util.SwitchOnResponseBodyLog(c)
	var req = &DouyinlifeCouponIssueRequest{}
	if err := c.ShouldBind(req); err != nil {
		writeJson(c, &DouyinlifeCouponIssueResponse{
			Data: DouyinlifeCouponIssueResponseData{
				ErrorCode:   DOUYINLIFE_COUPON_FAILED_CODE_OTHERS,
				Description: err.Error(),
				Result:      DOUYINLIFE_COUPON_ISSUE_CODE_FAILED,
			},
		})
		return
	}
	var (
		err                   error
		codes                 = make([]string, 0, req.Count)
		membershipDiscountIds = make([]string, 0, req.Count)
		mx                    = sync.Mutex{}
	)
	defer func() {
		if err != nil {
			newCtx := c.Copy()
			core_component.GO(c, func(ctx context.Context) {
				if len(membershipDiscountIds) > 0 {
					innerErr := deleteMembershipDiscountsByIds(newCtx, membershipDiscountIds)
					if innerErr != nil {
						log.Error(newCtx, "Failed to delete membershipDiscount by ids", log.Fields{
							"req":    core_util.MarshalInterfaceToString(req),
							"errMsg": innerErr.Error(),
						})
					}
				}
			})
			writeJson(newCtx, genDouyinlifeCouponIssueFailedResponse("Failed to issue coupon"))
		}
	}()
	member, err := upsertDouyinlifeMemberForIssuing(c, req)
	if err != nil {
		return
	}
	membershipDiscounts, _ := pb_client.GetCouponServiceClient().GetMembershipDiscounts(GetGrpcContext(c), &mairpc_coupon.GetMembershipDiscountsRequest{
		SceneId:    req.DouyinOrderId,
		StatusList: []string{coupon_model.MEMBERSHIP_DISCOUNT_UNUSED, coupon_model.MEMBERSHIP_DISCOUNT_USED, coupon_model.MEMBERSHIP_DISCOUNT_EXPIRED, coupon_model.MEMBERSHIP_DISCOUNT_INVALID},
	})
	if membershipDiscounts != nil && len(membershipDiscounts.Items) > 0 {
		for _, membershipDiscount := range membershipDiscounts.Items {
			if util.StrInArray(membershipDiscount.Code, &codes) {
				continue
			}
			codes = append(codes, membershipDiscount.Code)
		}
		if len(membershipDiscounts.Items) != req.Count {
			log.Error(c, "Coupon count is not equal to required count", log.Fields{
				"couponCount":   len(membershipDiscounts.Items),
				"requiredCount": req.Count,
				"codes":         codes,
			})
		}
		writeJson(c, &DouyinlifeCouponIssueResponse{
			Data: DouyinlifeCouponIssueResponseData{
				ErrorCode:   DOUYINLIFE_SUCCESS_CODE,
				Description: DOUYINLIFE_SUCCESS,
				Result:      DOUYINLIFE_COUPON_ISSUE_CODE_SUCCESS,
				Codes:       codes,
			},
		})
		return
	}
	if err = updateThirdPartyCouponTradeStatusByOuterOrderId(c, req.DouyinOrderId, THIRD_PARTY_COUPON_TRADE_STATUS_PAID); err != nil {
		return
	}
	wg := sync.WaitGroup{}
	newCtx := c.Copy()
	for i := 0; i < req.Count; i++ {
		wg.Add(1)
		core_component.GO(c, func(ctx context.Context) {
			defer wg.Done()
			resp, innerErr := pb_client.GetCouponServiceClient().IssueCoupon(GetGrpcContext(newCtx), &mairpc_coupon.IssueCouponRequest{
				CouponId:         req.MaiCouponId,
				MemberId:         member.Id,
				CheckLimit:       false,
				ChannelId:        newCtx.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
				SceneId:          req.DouyinOrderId,
				CouponCodePrefix: constant.DOUYINLIFE,
			})
			if innerErr != nil {
				log.Warn(newCtx, "Failed to issue coupon", log.Fields{
					"couponId": req.MaiCouponId,
					"memberId": member.Id,
					"errMsg":   innerErr.Error(),
				})
				return
			}
			mx.Lock()
			codes = append(codes, resp.Code)
			membershipDiscountIds = append(membershipDiscountIds, resp.Id)
			mx.Unlock()
			pb_client.GetCouponServiceClient().PostponeMembershipDiscount(GetGrpcContext(newCtx), &mairpc_coupon.PostponeMembershipDiscountRequest{
				Id:          resp.Id,
				PostponedTo: business_util.TransSecTimestamp(int64(req.ExpireTime)),
			})
		})
	}
	wg.Wait()
	if len(codes) != req.Count {
		err = errors.New("Not all codes were issued successfully")
		log.Warn(c, "Failed to issue coupon", log.Fields{
			"couponId": req.MaiCouponId,
			"memberId": member.Id,
			"count":    req.Count,
			"codes":    codes,
			"err":      err.Error(),
		})
		return
	}
	if err = updateThirdPartyCouponTradeStatusByOuterOrderId(c, req.DouyinOrderId, THIRD_PARTY_COUPON_TRADE_STATUS_AVAILABLE); err != nil {
		return
	}
	writeJson(c, &DouyinlifeCouponIssueResponse{
		Data: DouyinlifeCouponIssueResponseData{
			ErrorCode:   DOUYINLIFE_SUCCESS_CODE,
			Description: DOUYINLIFE_SUCCESS,
			Result:      DOUYINLIFE_COUPON_ISSUE_CODE_SUCCESS,
			Codes:       codes,
		},
	})
}

func upsertDouyinlifeMemberForIssuing(c *gin.Context, req *DouyinlifeCouponIssueRequest) (*pbMember.MemberDetailResponse, error) {
	var (
		member *pbMember.MemberDetailResponse
		err    error
	)
	existedMember := GetMemberByUniqueId(c, req.OpenId)
	if existedMember != nil {
		member = existedMember
	} else {
		upsertMemberByOpenIdReq := &pbMember.UpsertMemberRequest{
			OriginFrom: &origin.OriginInfo{
				Channel:     c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
				OpenId:      req.OpenId,
				Origin:      constant.DOUYINLIFE,
				ChannelName: c.Request.Header.Get(core_util.CHANNEL_NAME_IN_HEADER),
			},
		}
		member, err = UpsertMember(c, upsertMemberByOpenIdReq)
		if err != nil {
			log.Warn(c, "Failed to upsert member by openId", log.Fields{
				"openId": req.OpenId,
				"errMsg": err.Error(),
			})
			return nil, err
		}
	}
	phone := getDouyinlifeCouponContactPhone(c, req.DouyinOrderId)
	if phone != "" {
		existedMember := GetMemberByUniqueId(c, phone)
		if existedMember != nil {
			member = existedMember
		} else {
			upsertMemberByPhoneReq := &pbMember.UpsertMemberRequest{
				OriginFrom: &origin.OriginInfo{
					Origin: constant.OTHERS,
				},
				Properties: []*pbMember.PropertyInfo{
					{
						PropertyId: "mobile",
						Value: &pbMember.PropertyInfo_ValueString{
							ValueString: &pbMember.PropertyStringValue{
								Value: phone,
							},
						},
					},
				},
			}
			member, err = UpsertMember(c, upsertMemberByPhoneReq)
			if err != nil {
				log.Warn(c, "Failed to upsert member by phone", log.Fields{
					"phone":  phone,
					"errMsg": err.Error(),
				})
				return nil, err
			}
		}
	}
	return member, nil
}

func getDouyinlifeCouponContactPhone(c *gin.Context, outerOrderId string) string {
	thirdPartyCouponTrade, err := pb_client.GetTradeOrderServiceClient().GetThirdPartyCouponTrade(GetGrpcContext(c), &mairpc_trade.GetThirdPartyCouponTradeRequest{OuterOrderId: outerOrderId})
	if err != nil {
		return ""
	}
	return thirdPartyCouponTrade.Contact.Phone
}

func updateThirdPartyCouponTradeStatusById(c *gin.Context, maiOrderId, status string) error {
	_, err := pb_client.GetTradeOrderServiceClient().UpdateThirdPartyCouponTrade(GetGrpcContext(c), &mairpc_trade.UpdateThirdPartyCouponTradeRequest{
		Id:     maiOrderId,
		Status: status,
	})
	if err != nil {
		log.Warn(c, "Failed to update third party coupon status by id", log.Fields{
			"id":     maiOrderId,
			"errMsg": err.Error(),
		})
		return err
	}
	return nil
}

func updateThirdPartyCouponTradeStatusByOuterOrderId(c *gin.Context, outerOrderId, status string) error {
	_, err := pb_client.GetTradeOrderServiceClient().UpdateThirdPartyCouponTrade(GetGrpcContext(c), &mairpc_trade.UpdateThirdPartyCouponTradeRequest{
		OuterOrderId: outerOrderId,
		Status:       status,
	})
	if err != nil {
		log.Warn(c, "Failed to update third party coupon trade by outerOrderId", log.Fields{
			"outerOrderId": outerOrderId,
			"errMsg":       err.Error(),
		})
		return err
	}
	return nil
}

func HandleDouyinlifeGetCouponStatus(c *gin.Context) {
	business_util.SwitchOnResponseBodyLog(c)
	var req DouyinlifeGetCouponStatusRequest
	if err := c.ShouldBind(&req); err != nil {
		writeJson(c, &DouyinlifeGetCouponStatusResponse{
			Data: DouyinlifeGetCouponStatusResponseData{
				ErrorCode:   DOUYINLIFE_GET_COUPON_STATUS_FAILED_CODE_OTHERS,
				Description: err.Error(),
			},
		})
		return
	}
	result := make(map[string]DouyinlifeOrderDetail)
	for _, douyinOrderId := range req.DouyinOrderIds {
		thirdPartyCouponTrade, rpcErr := pb_client.GetTradeOrderServiceClient().GetThirdPartyCouponTrade(GetGrpcContext(c), &mairpc_trade.GetThirdPartyCouponTradeRequest{OuterOrderId: douyinOrderId})
		if rpcErr != nil {
			writeJson(c, &DouyinlifeGetCouponStatusResponse{
				Data: DouyinlifeGetCouponStatusResponseData{
					ErrorCode:   DOUYINLIFE_GET_COUPON_STATUS_FAILED_CODE_ORDER_NOT_FOUND,
					Description: "Order not found",
				},
			})
			log.Warn(c, "Failed to get third party coupon trade by outerOrderId", log.Fields{
				"outerOrderId": douyinOrderId,
				"errMsg":       rpcErr.Error(),
			})
			return
		}
		membershipDiscounts, rpcErr := pb_client.GetCouponServiceClient().GetMembershipDiscounts(GetGrpcContext(c), &mairpc_coupon.GetMembershipDiscountsRequest{
			SceneId: douyinOrderId,
		})
		if rpcErr != nil || membershipDiscounts == nil || len(membershipDiscounts.Items) == 0 {
			writeJson(c, &DouyinlifeGetCouponStatusResponse{
				Data: DouyinlifeGetCouponStatusResponseData{
					ErrorCode:   DOUYINLIFE_GET_COUPON_STATUS_FAILED_CODE_COUPON_NOT_FOUND,
					Description: "Coupon not found",
				},
			})
			logFields := log.Fields{"sceneId": douyinOrderId}
			if rpcErr != nil {
				logFields["errMsg"] = rpcErr.Error()
			}
			log.Warn(c, "Failed to get membership discounts by sceneId", logFields)
			return
		}
		result[douyinOrderId] = formatDouyinlifeCouponStatusResult(douyinOrderId, thirdPartyCouponTrade.Status, membershipDiscounts)
	}
	writeJson(c, &DouyinlifeGetCouponStatusResponse{
		Data: DouyinlifeGetCouponStatusResponseData{
			ErrorCode:   DOUYINLIFE_SUCCESS_CODE,
			Description: DOUYINLIFE_SUCCESS,
			Result:      result,
		},
	})
}

func formatDouyinlifeCouponStatusResult(orderId, status string, membershipDiscounts *mairpc_coupon.MembershipDiscounts) DouyinlifeOrderDetail {
	result := DouyinlifeOrderDetail{
		OrderId:       orderId,
		TotalQuantity: int(membershipDiscounts.Total),
	}
	var (
		usedQuantity   int
		refundQuantity int
	)
	for _, membershipDiscount := range membershipDiscounts.Items {
		if membershipDiscount.Gifting.Status == coupon_model.GIFTED_COUPON {
			continue
		}
		if membershipDiscount.Coupon.Status == coupon_model.MEMBERSHIP_DISCOUNT_USED {
			usedQuantity++
		} else if membershipDiscount.Coupon.Status != coupon_model.MEMBERSHIP_DISCOUNT_UNUSED {
			refundQuantity++
		}
		result.Codes = append(result.Codes, DouyinlifeCodeRecord{
			Value: membershipDiscount.Code,
			Type:  "code",
			FulfilStatus: func() int {
				switch membershipDiscount.Coupon.Status {
				case coupon_model.MEMBERSHIP_DISCOUNT_USED:
					return DOUYINLIFE_COUPON_STATUS_CODE_USED
				case coupon_model.MEMBERSHIP_DISCOUNT_UNUSED:
					return DOUYINLIFE_COUPON_STATUS_CODE_UNUSED
				}
				return DOUYINLIFE_COUPON_STATUS_CODE_REFUNDED
			}(),
		})
	}
	result.UsedQuantity = usedQuantity
	result.RefundQuantity = refundQuantity
	if _, ok := douyinlifeCouponOrderStatusLabel[status]; ok {
		result.OrderStatus = douyinlifeCouponOrderStatusLabel[status]
	}
	if status == THIRD_PARTY_COUPON_TRADE_STATUS_AVAILABLE && usedQuantity+refundQuantity < int(membershipDiscounts.Total) {
		result.OrderStatus = DOUYINLIFE_COUPON_ORDER_STATUS_CODE_AVAILABLE
	} else {
		result.OrderStatus = DOUYINLIFE_COUPON_ORDER_STATUS_CODE_FINISH
	}
	return result
}

func HandleDouyinlifeCouponRefund(c *gin.Context) {
	business_util.SwitchOnResponseBodyLog(c)
	var req DouyinlifeCouponRefundRequest
	if err := c.ShouldBind(&req); err != nil {
		writeJson(c, &DouyinlifeCouponRefundResponse{
			Data: DouyinlifeCouponRefundResponseData{
				ErrorCode:   DOUYINLIFE_COUPON_FAILED_CODE_OTHERS,
				Description: err.Error(),
				Result:      DOUYINLIFE_COUPON_REFUND_STATUS_CODE_REJECT,
				Reason:      "Invalid Params",
			},
		})
		return
	}
	var codes []string
	for _, certificate := range req.Certificates {
		if certificate.Code == "" {
			continue
		}
		codes = append(codes, certificate.Code)
	}
	membershipDiscountsResp, err := pb_client.GetCouponServiceClient().GetMembershipDiscounts(GetGrpcContext(c), &mairpc_coupon.GetMembershipDiscountsRequest{
		SceneId: req.DouyinOrderId,
	})
	if err != nil || membershipDiscountsResp == nil || len(membershipDiscountsResp.Items) == 0 {
		writeJson(c, &DouyinlifeCouponRefundResponse{
			Data: DouyinlifeCouponRefundResponseData{
				ErrorCode:   DOUYINLIFE_COUPON_FAILED_CODE_OTHERS,
				Description: "Coupon not found",
				Result:      DOUYINLIFE_COUPON_REFUND_STATUS_CODE_REJECT,
				Reason:      "Coupon not found",
			},
		})
		return
	}
	var refundedCount int
	for _, membershipDiscount := range membershipDiscountsResp.Items {
		if membershipDiscount.Coupon.Status == coupon_model.MEMBERSHIP_DISCOUNT_INVALID && util.StrInArray(membershipDiscount.Code, &codes) {
			refundedCount++
		}
	}
	if refundedCount == len(codes) {
		writeJson(c, &DouyinlifeCouponRefundResponse{
			Data: DouyinlifeCouponRefundResponseData{
				ErrorCode:   DOUYINLIFE_SUCCESS_CODE,
				Description: DOUYINLIFE_SUCCESS,
				Result:      DOUYINLIFE_COUPON_REFUND_STATUS_CODE_AGREE,
			},
		})
		return
	}
	orderStatus, refundMembershipDiscounts := getMembershipDiscountToRefund(codes, membershipDiscountsResp.Items)
	if len(refundMembershipDiscounts) < len(req.Certificates) {
		if len(codes) == 0 {
			codes = core_util.ExtractArrayFieldV2("Code", "", refundMembershipDiscounts)
			for _, item := range membershipDiscountsResp.Items {
				if len(codes) >= len(req.Certificates) {
					break
				}
				if util.StrInArray(item.Code, &codes) {
					continue
				}
				codes = append(codes, item.Code)
			}
		}
		writeJson(c, &DouyinlifeCouponRefundResponse{
			Data: DouyinlifeCouponRefundResponseData{
				ErrorCode:   DOUYINLIFE_SUCCESS_CODE,
				Description: "Some coupons have already be used or in the wrong state",
				Result:      DOUYINLIFE_COUPON_REFUND_STATUS_CODE_REJECT,
				Reason:      "Some coupons have already be used or in the wrong state",
				Codes:       codes,
			},
		})
		return
	}
	newCtx := c.Copy()
	component.GO(c, func(ctx context.Context) {
		updateThirdPartyCouponRefundStatus(newCtx, req.DouyinOrderId, orderStatus, refundMembershipDiscounts)
	})
	writeJson(c, &DouyinlifeCouponRefundResponse{
		Data: DouyinlifeCouponRefundResponseData{
			ErrorCode:   DOUYINLIFE_SUCCESS_CODE,
			Description: DOUYINLIFE_SUCCESS,
			Result:      DOUYINLIFE_COUPON_REFUND_STATUS_CODE_AGREE,
		},
	})
}

func HandleDouyinlifeCouponForceRefund(c *gin.Context) {
	business_util.SwitchOnResponseBodyLog(c)
	var req DouyinlifeCouponForceRefundRequest
	if err := c.ShouldBind(&req); err != nil {
		writeJson(c, &DouyinlifeCommonResponse{
			Data: DouyinlifeCommonResponseData{
				ErrorCode:   DOUYINLIFE_COUPON_FAILED_CODE_OTHERS,
				Description: err.Error(),
			},
		})
		return
	}
	var (
		rejectedCount int
		codes         = make([]string, 0, len(req.NoticeList))
	)
	for _, coupon := range req.NoticeList {
		if coupon.AuditResult == DOUYINLIFE_COUPON_REFUND_STATUS_CODE_REJECT {
			rejectedCount++
			continue
		}
		if coupon.Code == "" {
			continue
		}
		codes = append(codes, coupon.Code)
	}
	var membershipDiscountsResp *mairpc_coupon.MembershipDiscounts
	refundMembershipDiscounts := make([]*mairpc_coupon.MembershipDiscount, 0, len(req.NoticeList))
	if len(codes) > 0 {
		membershipDiscountsResp, _ = pb_client.GetCouponServiceClient().GetMembershipDiscounts(GetGrpcContext(c), &mairpc_coupon.GetMembershipDiscountsRequest{
			SceneId: req.DouyinOrderId,
			Codes:   codes,
		})
		if membershipDiscountsResp != nil && len(membershipDiscountsResp.Items) > 0 {
			for _, item := range membershipDiscountsResp.Items {
				if util.StrInArray(item.Code, &codes) {
					refundMembershipDiscounts = append(refundMembershipDiscounts, item)
				}
			}
		}
	} else if len(req.NoticeList) > rejectedCount {
		// 退款接口请求的 code [可能有为空的情况](https://partner.open-douyin.com/docs/resource/zh-CN/local-life/develop/OpenAPI/tripartite.code/refund.apply)
		// 强制退款接口不确定有没有，以防万一处理一下：按 unused、used、其他状态优先级删除券
		membershipDiscountsResp, _ = pb_client.GetCouponServiceClient().GetMembershipDiscounts(GetGrpcContext(c), &mairpc_coupon.GetMembershipDiscountsRequest{
			SceneId: req.DouyinOrderId,
		})
		if membershipDiscountsResp != nil && len(membershipDiscountsResp.Items) > 0 {
			for _, status := range []string{
				coupon_model.MEMBERSHIP_DISCOUNT_UNUSED,
				coupon_model.MEMBERSHIP_DISCOUNT_USED,
			} {
				for _, item := range membershipDiscountsResp.Items {
					if len(codes) >= len(req.NoticeList)-rejectedCount {
						break
					}
					if item.Gifting.Status == coupon_model.GIFTED_COUPON {
						continue
					}
					if item.Coupon.Status == status && !util.StrInArray(item.Code, &codes) {
						codes = append(codes, item.Code)
					}
				}
			}
			if len(codes) < len(req.NoticeList)-rejectedCount {
				for _, item := range membershipDiscountsResp.Items {
					if len(codes) >= len(req.NoticeList)-rejectedCount {
						break
					}
					if item.Gifting.Status == coupon_model.GIFTED_COUPON {
						continue
					}
					if !util.StrInArray(item.Code, &codes) {
						codes = append(codes, item.Code)
					}
				}
			}
			for _, item := range membershipDiscountsResp.Items {
				if util.StrInArray(item.Code, &codes) {
					refundMembershipDiscounts = append(refundMembershipDiscounts, item)
				}
			}
		}
	}
	if len(refundMembershipDiscounts) > 0 {
		newCtx := c.Copy()
		component.GO(c, func(ctx context.Context) {
			updateThirdPartyCouponRefundStatus(newCtx, req.DouyinOrderId, THIRD_PARTY_COUPON_TRADE_STATUS_CANCELED, refundMembershipDiscounts)
			refundEcOrderByDiscountIds(newCtx, refundMembershipDiscounts)
		})
	}
	writeJson(c, &DouyinlifeCouponRefundResponse{
		Data: DouyinlifeCouponRefundResponseData{
			ErrorCode:   DOUYINLIFE_SUCCESS_CODE,
			Description: DOUYINLIFE_SUCCESS,
		},
	})
}

func HandleDouyinlifeTakeoutOrderCreate(c *gin.Context) {
	business_util.SwitchOnResponseBodyLog(c)
	var (
		req         DouyinlifeTakeoutCreateOrderRequest
		codes       = []string{}
		unusedCodes []string
		err         error
	)
	defer func() {
		resp := &DouyinlifeCommonResponse{
			Data: DouyinlifeCommonResponseData{
				ErrorCode:     DOUYINLIFE_SUCCESS_CODE,
				Description:   DOUYINLIFE_SUCCESS,
				DouyinOrderId: req.DouyinOrderId,
			},
		}
		if err != nil {
			resp.Data.ErrorCode = DOUYINLIFE_FAILED_CODE
			resp.Data.Description = err.Error()
		} else {
			resp.Data.OrderId = req.DouyinOrderId
		}
		writeJson(c, resp)
	}()
	if err = c.ShouldBind(&req); err != nil {
		return
	}
	for _, deductionInfo := range req.OrderDeductionList {
		codes = append(codes, deductionInfo.ThirdPartCode)
	}
	codes = core_util.StrArrayUnique(codes)
	membershipDiscountsResp, _ := pb_client.GetCouponServiceClient().GetMembershipDiscounts(GetGrpcContext(c), &mairpc_coupon.GetMembershipDiscountsRequest{
		Codes: codes,
	})
	if membershipDiscountsResp == nil || len(membershipDiscountsResp.Items) != len(codes) {
		err = errors.New("Failed to get all codes or the codes do not exist")
		return
	}
	for _, membershipDiscount := range membershipDiscountsResp.Items {
		if membershipDiscount.Coupon.Status == coupon_model.MEMBERSHIP_DISCOUNT_USED {
			// 兼容抖音随心团响应超时
			resp, _ := pb_client.GetCouponServiceClient().GetRedeemedList(GetGrpcContext(c), &pb_request.ListRequest{
				Page:    1,
				PerPage: 1000,
				Query: map[string]string{
					"membershipDiscountCode": membershipDiscount.Code,
				},
			})
			if resp == nil || len(resp.Items) == 0 {
				err = errors.New(fmt.Sprintf("Code: %s have been used illegally", membershipDiscount.Code))
				return
			}
			for _, couponLog := range resp.Items {
				if couponLog.OrderNumber == "" {
					err = errors.New(fmt.Sprintf("Code: %s have been used", membershipDiscount.Code))
					return
				}
				newCtx := c.Copy()
				core_component.GO(newCtx, func(ctx context.Context) {
					_, err := pb_client.GetCouponServiceClient().UpdateCouponLog(GetGrpcContext(newCtx), &pb_coupon.UpdateCouponLogRequest{
						Id:          couponLog.Id,
						OrderNumber: req.DouyinOrderId,
					})
					if err != nil {
						log.Warn(newCtx, "Failed to update coupon log", log.Fields{
							"errMsg":      err.Error(),
							"id":          couponLog.Id,
							"orderNumber": req.DouyinOrderId,
						})
					}
				})
			}
			continue
		}
		unusedCodes = append(unusedCodes, membershipDiscount.Code)
	}
	dmsDistributorOuterStore, _ := pb_client.GetStoreServiceClient().GetDmsDistributorOuterStore(GetGrpcContext(c), &store.GetDmsDistributorOuterStoreRequest{
		OuterStoreType: constant.DOUYINLIFE,
		OuterStoreId:   req.DouyinStoreId,
	})
	redeemedStoreId := ""
	if dmsDistributorOuterStore != nil {
		redeemedStoreId = dmsDistributorOuterStore.StoreId
	} else {
		log.Error(c, "Failed to get dms distributor outer store by outerStoreId", log.Fields{
			"outerStoreId":   req.DouyinStoreId,
			"outerStoreType": constant.DOUYINLIFE,
		})
	}
	for _, code := range unusedCodes {
		_, err = pb_client.GetCouponServiceClient().RedeemedCoupon(GetGrpcContext(c), &pb_coupon.RedeemedCouponRequest{
			Code:               code,
			StoreId:            redeemedStoreId,
			OrderNumber:        req.DouyinOrderId,
			NoSyncToThirdParty: true,
		})
		if err != nil {
			log.Warn(c, "Failed to redeem douyinlife code", log.Fields{
				"douyinOrderId": req.DouyinOrderId,
				"err":           err.Error(),
				"code":          code,
			})
			err = errors.New("Failed to lock code")
			return
		}
	}
}

func HandleDouyinlifeTakeoutEvent(c *gin.Context) {
	business_util.SwitchOnResponseBodyLog(c)
	var commonReq DouyinlifeTakeoutMessageCommonRequest
	if err := c.ShouldBind(&commonReq); err != nil {
		writeJson(c, &DouyinlifeCommonResponse{
			Data: DouyinlifeCommonResponseData{
				ErrorCode:   DOUYINLIFE_FAILED_CODE,
				Description: err.Error(),
			},
		})
		return
	}
	newCtx := c.Copy()
	core_component.GO(newCtx, func(ctx context.Context) {
		switch commonReq.Event {
		case DOUYINLIFE_TAKEOUT_EVENT_ORDER_PAY_CLOSE, DOUYINLIFE_TAKEOUT_EVENT_ORDER_MERCHANT_REFUSE, DOUYINLIFE_TAKEOUT_EVENT_TRADE_REFUND_COMPLETE:
			handleDouyinlifeTakeoutOrderRefundEvent(newCtx, commonReq.Content)
		}
	})
	writeJson(c, &DouyinlifeCommonResponse{
		Data: DouyinlifeCommonResponseData{
			ErrorCode:   DOUYINLIFE_SUCCESS_CODE,
			Description: DOUYINLIFE_SUCCESS,
		},
	})
}

func handleDouyinlifeTakeoutOrderRefundEvent(c *gin.Context, reqContent string) {
	var (
		req               = &DouyinlifeTakeoutOrderRefundContent{}
		redeemCouponLogs  []*pb_coupon.RedeemedCouponLog
		err               error
		revertedCouponIds = []string{}
	)
	defer func() {
		if err != nil {
			log.Warn(c, "Failed to handle douyinlife takeout order refund event", log.Fields{
				"reqContent": reqContent,
				"err":        err.Error(),
			})
		}
	}()
	err = json.Unmarshal([]byte(reqContent), req)
	if err != nil {
		return
	}
	redeemCouponLogs, err = getRedeemedCouponsFromLog(c, req.DouyinOrderId)
	if err != nil {
		return
	}
	if len(redeemCouponLogs) == 0 {
		return
	}
	for _, redeemCouponLog := range redeemCouponLogs {
		if redeemCouponLog.MembershipDiscountId == "" {
			continue
		}
		_, err = pb_client.GetCouponServiceClient().RollbackRedeemCoupon(GetGrpcContext(c), &pb_coupon.RollbackRedeemCouponRequest{
			MembershipDiscountId: redeemCouponLog.MembershipDiscountId,
			NoSyncToThirdParty:   true,
		})
		if err != nil {
			log.Warn(c, "Failed to rollback douyinlife redeemed coupon", log.Fields{
				"errMsg":               err.Error(),
				"membershipDiscountId": redeemCouponLog.MembershipDiscountId,
			})
			continue
		}
		revertedCouponIds = append(revertedCouponIds, redeemCouponLog.MembershipDiscountId)
	}
	if req.ApplySource == DOUYINLIFE_TAKEOUT_REFUND_TYPE_USER_FULFILMENT {
		invalidateMembershipDiscountsReq := &mairpc_coupon.InvalidateMembershipDiscountsRequest{
			CouponId:      redeemCouponLogs[0].CouponId,
			Ids:           revertedCouponIds,
			InvalidReason: coupon_model.MEMBERSHIP_DISCOUNT_INVALID_REASON_ORDER_REFUND,
		}
		_, err = pb_client.GetCouponServiceClient().InvalidateMembershipDiscounts(GetGrpcContext(c), invalidateMembershipDiscountsReq)
	}
}

func getRedeemedCouponsFromLog(c *gin.Context, douyinOrderId string) ([]*pb_coupon.RedeemedCouponLog, error) {
	if douyinOrderId == "" {
		return nil, errors.New("DouyinOrderId is required")
	}
	var redeemCouponLogs []*pb_coupon.RedeemedCouponLog
	resp, err := pb_client.GetCouponServiceClient().GetRedeemedList(GetGrpcContext(c), &pb_request.ListRequest{
		Page:    1,
		PerPage: 1000,
		Query: map[string]string{
			"orderNumber": douyinOrderId,
		},
	})
	if err != nil {
		return nil, err
	}
	redeemCouponLogs = append(redeemCouponLogs, resp.Items...)
	return redeemCouponLogs, nil
}

func getMembershipDiscountToRefund(codes []string, membershipDiscounts []*mairpc_coupon.MembershipDiscount) (string, []*mairpc_coupon.MembershipDiscount) {
	refundMembershipDiscounts := make([]*mairpc_coupon.MembershipDiscount, 0, len(codes))
	orderStatus := ""
	hasOthersToRefund := false
	if len(membershipDiscounts) > 0 && len(codes) > 0 {
		for _, item := range membershipDiscounts {
			if item.Coupon.Status == coupon_model.MEMBERSHIP_DISCOUNT_UNUSED ||
				item.Coupon.Status == coupon_model.MEMBERSHIP_DISCOUNT_EXPIRED ||
				item.Coupon.Status == coupon_model.MEMBERSHIP_DISCOUNT_USED && item.Gifting.Status == coupon_model.GIFTING_STATUS_GIFTING {
				if util.StrInArray(item.Code, &codes) {
					refundMembershipDiscounts = append(refundMembershipDiscounts, item)
				} else {
					hasOthersToRefund = true
				}
			}
		}
	}
	if !hasOthersToRefund {
		orderStatus = THIRD_PARTY_COUPON_TRADE_STATUS_CANCELED
	}
	return orderStatus, refundMembershipDiscounts
}

func updateThirdPartyCouponRefundStatus(c *gin.Context, outerOrderId, status string, refundMembershipDiscounts []*mairpc_coupon.MembershipDiscount) error {
	if status != "" {
		updateThirdPartyCouponTradeStatusByOuterOrderId(c, outerOrderId, status)
	}
	if len(refundMembershipDiscounts) > 0 {
		codes := core_util.ExtractArrayFieldV2("Code", "", refundMembershipDiscounts)
		invalidateMembershipDiscountsReq := &mairpc_coupon.InvalidateMembershipDiscountsRequest{
			CouponId:      refundMembershipDiscounts[0].Coupon.Id,
			Codes:         codes,
			InvalidReason: coupon_model.MEMBERSHIP_DISCOUNT_INVALID_REASON_ORDER_REFUND,
		}
		_, rpcErr := pb_client.GetCouponServiceClient().InvalidateMembershipDiscounts(GetGrpcContext(c), invalidateMembershipDiscountsReq)
		if rpcErr != nil {
			log.Warn(c, "Failed to invalidate membership discounts", log.Fields{
				"req":    core_util.MarshalInterfaceToString(invalidateMembershipDiscountsReq),
				"errMsg": rpcErr.Error(),
			})
			return errors.New("Failed to refund all coupons")
		}
	}
	return nil
}

func refundEcOrderByDiscountIds(c *gin.Context, refundMembershipDiscounts []*mairpc_coupon.MembershipDiscount) error {
	if len(refundMembershipDiscounts) == 0 {
		return nil
	}
	memberShipDiscountIds := core_util.ExtractArrayFieldV2("Id", "", refundMembershipDiscounts)
	logsReq := pb_coupon.SearchCouponLogsRequest{
		MembershipDiscountIds: memberShipDiscountIds,
	}
	logsResp, err := pb_client.GetCouponServiceClient().SearchCouponLogs(GetGrpcContext(c), &logsReq)
	if err != nil {
		log.Warn(c, "Failed to search coupon logs when refunding douyinlife coupon", log.Fields{
			"req":    core_util.MarshalInterfaceToString(logsReq),
			"errMsg": err.Error(),
		})
		return err
	}
	orderIds := map[string]bool{}
	for _, couponLog := range logsResp.CouponLogs {
		if couponLog.OrderId == "" {
			continue
		}
		orderIds[couponLog.OrderId] = true
	}
	for orderId := range orderIds {
		orderReq := pb_order.GetOrderRequest{
			Id: orderId,
		}
		order, err := pb_client.GetEcOrderServiceClient().GetOrder(GetGrpcContext(c), &orderReq)
		if err != nil {
			log.Error(c, "Failed to get ec order when refunding douyinlife coupon", log.Fields{
				"req":    core_util.MarshalInterfaceToString(orderReq),
				"errMsg": err.Error(),
			})
			continue
		}
		if util.StrInArray(order.Status, &[]string{"completed", "canceled"}) {
			continue
		}
		orderRefundReq := pb_order.GetOrderRefundRequest{
			MemberId: order.MemberId,
			OrderId:  order.Id,
		}
		orderRefund, _ := pb_client.GetEcOrderServiceClient().GetOrderRefund(GetGrpcContext(c), &orderRefundReq)
		if orderRefund != nil && bson.IsObjectIdHex(orderRefund.Id) {
			updOrderRefundReq := pb_order.UpdateOrderRefundRequest{
				Id:          orderRefund.Id,
				OperateType: "agreeToRefund",
			}
			_, err = pb_client.GetEcOrderServiceClient().UpdateOrderRefund(GetGrpcContext(c), &updOrderRefundReq)
			if err != nil {
				log.Warn(c, "Failed to update ec orderRefund when refunding douyinlife coupon", log.Fields{
					"req":    core_util.MarshalInterfaceToString(updOrderRefundReq),
					"errMsg": err.Error(),
				})
			}
		} else if len(order.TradeRecords) == 0 && order.Status == "unpaid" {
			cancelReq := pb_order.CancelOrderRequest{
				Id:       order.Id,
				MemberId: order.MemberId,
				Reason:   "系统退款",
			}
			_, err = pb_client.GetEcOrderServiceClient().CancelOrder(GetGrpcContext(c), &cancelReq)
			if err != nil {
				log.Warn(c, "Failed to cancel ec order when refunding douyinlife coupon", log.Fields{
					"req":    core_util.MarshalInterfaceToString(cancelReq),
					"errMsg": err.Error(),
				})
			}
		} else {
			refundReq := pb_order.RefundOrderRequest{
				Id:          order.Id,
				MemberId:    order.MemberId,
				Reason:      "系统退款",
				Description: "系统退款",
				IsSystem:    true,
				ProductSkus: func() []*pb_order.ProductSKU {
					productSkus := []*pb_order.ProductSKU{}
					for _, product := range order.Products {
						productSkus = append(productSkus, &pb_order.ProductSKU{
							OutTradeId: product.OutTradeId,
						})
					}
					return productSkus
				}(),
			}
			_, err = pb_client.GetEcOrderServiceClient().RefundOrder(GetGrpcContext(c), &refundReq)
			if err != nil {
				log.Warn(c, "Failed to refund ec order when refunding douyinlife coupon", log.Fields{
					"req":    core_util.MarshalInterfaceToString(refundReq),
					"errMsg": err.Error(),
				})
			}
		}
	}
	return nil
}

func deleteMembershipDiscountsByIds(c *gin.Context, membershipDiscountIds []string) error {
	if len(membershipDiscountIds) == 0 {
		return nil
	}
	_, err := pb_client.GetCouponServiceClient().DeleteMembershipDiscountByIds(
		GetGrpcContext(c),
		&pb_request.IdListRequest{
			Ids: membershipDiscountIds,
		},
	)
	return err
}

func triggerDouyinlifeEvent(c *gin.Context, memberId, channelId, openId, phoneNo, subType string) {
	ctx := GetGrpcContext(c)
	properties, err := json.Marshal(map[string]interface{}{
		"channelId":   channelId,
		"phoneNo":     phoneNo,
		"channelType": "Douyinlife",
	})
	if err != nil {
		log.Error(ctx, "Failed to marshal douyinlife eventProperties", log.Fields{})
		return
	}
	req := &pbMember.SendCustomerEventRequest{
		MemberId:        memberId,
		Type:            subType,
		ChannelId:       channelId,
		OpenId:          openId,
		EventProperties: string(properties),
	}
	component.GO(ctx, func(ctx context.Context) {
		pb_client.GetMemberServiceClient().SendCustomerEvent(ctx, req)
	})
}

func douyinlifeAesDecrypt(encryptedStr string, secret string) (decryptedStr string, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = core_errors.ConvertRecoveryError(r)
		}
	}()
	decodeBytes, err := base64.StdEncoding.DecodeString(encryptedStr)
	if err != nil {
		return
	}
	key, iv := douyinlifeParseSecret(secret)
	block, err := aes.NewCipher(key)
	if err != nil {
		return
	}
	blockSize := block.BlockSize()
	blockMode := cipher.NewCBCDecrypter(block, iv[:blockSize])
	origData := make([]byte, len(decodeBytes))
	blockMode.CryptBlocks(origData, decodeBytes)
	origData = core_util.PKCS7UnPadding(origData)
	decryptedStr = string(origData)
	return
}

func douyinlifeParseSecret(secret string) ([]byte, []byte) {
	secret = douyinlifeCutSecret(secret)
	secret = douyinlifeFillSecret(secret)
	key, iv := secret, secret[16:]
	return []byte(key), []byte(iv)
}

func douyinlifeFillSecret(secret string) string {
	if len(secret) >= 32 {
		return secret
	}
	rightCnt := (32 - len(secret)) / 2
	leftCnt := 32 - len(secret) - rightCnt
	var byt bytes.Buffer
	byt.Write(bytes.Repeat([]byte("#"), leftCnt))
	byt.WriteString(secret)
	byt.Write(bytes.Repeat([]byte("#"), rightCnt))
	return byt.String()
}

func douyinlifeCutSecret(secret string) string {
	if len(secret) <= 32 {
		return secret
	}
	rightCnt := (len(secret) - 32) / 2
	leftCnt := len(secret) - 32 - rightCnt
	return secret[leftCnt : 32+leftCnt]
}

func genDouyinlifeCreateCouponRequest(c *gin.Context, req *DouyinlifeCouponPreIssueRequest) (*mairpc_trade.CreateThirdPartyCouponTradeRequest, error) {
	channelId := c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER)
	douyinlifeCoupon := &mairpc_trade.CreateThirdPartyCouponTradeRequest{
		ChannelId:    channelId,
		OuterOrderId: req.DouyinOrderId,
		OrderFrom:    constant.DOUYINLIFE,
		CouponId:     req.MaiCouponId,
		OuterSkuId:   req.DouyinSkuId,
		CouponCount:  req.Count,
		OrderType: func() string {
			if _, ok := douyinlifeCouponOrderTypeLabel[req.OrderType]; ok {
				return douyinlifeCouponOrderTypeLabel[req.OrderType]
			}
			return fmt.Sprintf("%d", req.OrderType)
		}(),
		OriginalAmount: req.OriginalAmount,
		Currency:       req.CurrencyCode,
		Status:         THIRD_PARTY_COUPON_TRADE_STATUS_UNPAID,
	}
	if req.CreateOrderTime != 0 {
		douyinlifeCoupon.OrderCreatedAt = util.TransSecTimestamp(req.CreateOrderTime)
	}
	if len(req.Users) > 0 {
		for _, user := range req.Users {
			douyinlifeCoupon.Users = append(douyinlifeCoupon.Users, &mairpc_trade.IdentityInfo{
				Name:   user.Name,
				Phone:  user.Phone,
				IdCard: user.IdCard,
			})
		}
	}
	channel := GetChannelByChannelId(c, channelId, constant.DOUYINLIFE)
	if req.Contact != nil {
		var contactPhone string
		if req.Contact.CompletePhone != "" {
			contactPhone, decryptErr := douyinlifeAesDecrypt(req.Contact.CompletePhone, channel.AppSecret)
			if decryptErr != nil || !validators.CValidator.IsPhone(contactPhone, nil) {
				errMsg := "Invalid phone number"
				if decryptErr != nil {
					errMsg = decryptErr.Error()
				}
				log.Error(c, "Failed to decrypt contact phone number", log.Fields{
					"douyinPhoneNo":    req.Contact.CompletePhone,
					"decryptedPhoneNo": contactPhone,
					"errMsg":           errMsg,
				})
			}
		}
		contactExtraMap := make(map[string]string)
		contactExtraMap["completePhone"] = req.Contact.CompletePhone
		contactExtra, _ := json.Marshal(contactExtraMap)
		douyinlifeCoupon.Contact = &mairpc_trade.IdentityInfo{
			Name:  req.Contact.Name,
			Phone: contactPhone,
			Extra: string(contactExtra),
		}
	}
	return douyinlifeCoupon, nil
}

func genDouyinlifeCouponIssueFailedResponse(description string) *DouyinlifeCouponIssueResponse {
	return &DouyinlifeCouponIssueResponse{
		Data: DouyinlifeCouponIssueResponseData{
			ErrorCode:   DOUYINLIFE_COUPON_FAILED_CODE_OTHERS,
			Description: description,
			Result:      DOUYINLIFE_COUPON_ISSUE_CODE_FAILED,
		},
	}
}
