package controller

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"mairpc/proto/client"
	"math"
	"net/http"
	"net/url"
	"strings"
	"time"

	"mairpc/core/component/jingdong"
	core_errors "mairpc/core/errors"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/openapi/business/mairpc"
	"mairpc/openapi/business/util"
	"mairpc/proto/account"
	pb_account "mairpc/proto/account"
	"mairpc/proto/common/origin"
	"mairpc/proto/common/request"
	"mairpc/proto/common/types"
	pb_member "mairpc/proto/member"
	pb_trade_order "mairpc/proto/trade/order"
	"mairpc/service/member/codes"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"
	share_model "mairpc/service/share/model"
	"mairpc/service/trade/share"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	conf "github.com/spf13/viper"
)

const (
	JINGDONG_SYN_CUSTOMER_CODE_SUCCESS      = "SUC"
	JINGDONG_SYN_CUSTOMER_CODE_PARAMS_ERROR = "E01"
	JINGDONG_SYN_CUSTOMER_CODE_SYNC_FAILED  = "E02"
	JINGDONG_SYN_CUSTOMER_CODE_OTHER        = "E03" // 此返回值能触发京东侧重试
)

const (
	JINGDONG_SYN_CUSTOMER_CARD_STATUS_MAPPING      uint32 = 0
	JINGDONG_SYN_CUSTOMER_CARD_STATUS_REGISTER     uint32 = 1
	JINGDONG_SYN_CUSTOMER_CARD_STATUS_UNBIND       uint32 = 2
	JINGDONG_SYN_CUSTOMER_CARD_STATUS_MODIFY_TRADE uint32 = 3
	JINGDONG_SYN_CUSTOMER_CARD_STATUS_MODIFY_EXT   uint32 = 4
)

const (
	JINGDONG_SYNC_SCORE_BRIEF                          = "sync_score"
	JINGDONG_SYNC_SCORE_FIRST_TIME_DESC                = "京东会员通入会积分同步"
	JINGDONG_SYNC_SCORE_FROM_JINGDONG_FIRST_TIME_BRIEF = "sync_from_jd_first_time"
)

const (
	JINGDONG_OAUTH_SERVICE_NAME                = "jingdong-auth"
	JINGDONG_OAUTH_LOGIN_PATH_TEMPLATE         = "/oauth2/to_login?app_key=%s&response_type=code&redirect_uri=%s&state=%s&scope=snsapi_base"
	JINGDONG_OAUTH_TOKEN_PATH_TEMPLATE         = "/oauth2/access_token?app_key=%s&app_secret=%s&grant_type=authorization_code&code=%s"
	JINGDONG_OAUTH_CODE_REDIRECT_PATH_TEMPLATE = "/v1/jingdong/authCode?channelId=%s"
)

const (
	JINGDONG_MEMBER_AES_IV = "popCrm0000000000"
)

const (
	REMINDER_BUSINESS_PLATFORM_SESSION = "platform-session" // 会员通 session 到期提醒业务场景类型：platform-session
)

func init() {
	jingdongMember := &ControllerAction{
		Name:        "member_jingdongv2",
		Method:      "POST",
		Path:        "/member/jingdongv2",
		HandlerFunc: jdV2Handler.HandleJingdongMember,
	}

	jingdongAuthCode := &ControllerAction{
		Name:        "jingdong_auth",
		Method:      "GET",
		Path:        "/jingdong/authCode",
		HandlerFunc: jdV2Handler.HandleJindongAuthCode,
	}

	jingdongAuth := &ControllerAction{
		Name:        "jingdong_auth",
		Method:      "GET",
		Path:        "/jingdong/auth",
		HandlerFunc: jdV2Handler.HandleJindongAuth,
	}

	enableActions(jingdongMember, jingdongAuthCode, jingdongAuth)
}

var jdV2Handler *JdV2Handler

type JdV2Handler struct {
	jingdongClient *jingdong.Jingdong
	channel        *pb_account.ChannelDetailResponse
}

type JDSPIResponse struct {
	SubCode    uint32      `json:"sub_code"`
	SubMessage string      `json:"sub_message"`
	SubResult  interface{} `json:"sub_result"`
}

type JdMemberV2 struct {
	CustomerId            string  `json:"customerId"`
	PhoneNo               string  `json:"phoneNo,omitempty"`
	Level                 uint32  `json:"level,omitempty"`
	CardStatus            uint32  `json:"cardStatus"`
	Ext                   string  `json:"ext,omitempty"`
	Xid                   string  `json:"xid"`
	Pin                   string  `json:"pin"`
	OnlineTotalOrderPrice float64 `json:"onlineTotalOrderPrice,omitempty"`
	OnlineTotalOrderCount uint64  `json:"onlineTotalOrderCount,omitempty"`
	BindTime              string  `json:"bindTime,omitempty"`
}

type JdSynCustomerRequest struct {
	OfflineCustomer JdMemberV2 `json:"offlineCustomer"`
}

type JdSynCustomerResponse struct {
	SyncCode string `json:"syncCode"`
}

type JdAccessTokenResponse struct {
	AccessToken  string `json:"access_token"`
	ExpiresIn    uint64 `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	Scope        string `json:"scope"`
	OpenId       string `json:"open_id"`
}

type UpdateJdChannelRequest struct {
	ChannelId string `json:"channelId"`
	Origin    string `json:"origin"`
	Token     string `json:"token"`
}

func (self *JdV2Handler) HandleJingdongMember(c *gin.Context) {
	util.SwitchOnResponseBodyLog(c)
	channel := GetChannelByChannelId(c, c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER), constant.JD_MEMBER)
	jingdongClient, jingdongErr := jingdong.NewJingdong(channel.AppId, channel.AppSecret, channel.Token, channel.ChannelId, channel.IntegrationModes)
	if jingdongErr != nil {
		log.Error(c, "failed to initialize jingdong client", log.Fields{
			"err": jingdongErr.Error(),
		})
	}
	self = &JdV2Handler{
		jingdongClient: jingdongClient,
		channel:        channel,
	}
	switch c.Query("method") {
	case "jd.open.spi.OfflineCustomerService.synCustomer":
		self.synCustomer(c, jingdongErr)
	case "jd.open.spi.OfflinePointsService.userPointChange":
		self.userPointChange(c)
	default:
		panicWithInvalidParamError()
	}
}

func (self *JdV2Handler) HandleJindongAuthCode(c *gin.Context) {
	channel := self.getChannel(c)
	if channel == nil {
		return
	}
	c.Set(core_util.AccountIdKey, channel.AccountId)

	code := c.Query("code")
	if code == "" {
		self.responseError(c, http.StatusBadRequest)
		return
	}

	path := fmt.Sprintf(JINGDONG_OAUTH_TOKEN_PATH_TEMPLATE, channel.AppId, channel.AppSecret, code)
	resp, err := self.getAccessToken(c, path)
	if err != nil {
		log.Error(c, "Failed to get accessToken from jingdong", log.Fields{
			"path": path,
			"err":  fmt.Sprintf("%+v", err),
		})
		self.responseError(c, http.StatusBadRequest)
		return
	}
	self.handleAccessToken(c, channel, resp)

	writeJson(c, string(resp))
}

func (self *JdV2Handler) HandleJindongAuth(c *gin.Context) {
	channel := self.getChannel(c)
	if channel == nil {
		return
	}

	authDomain := util.GetExtensionDomain(JINGDONG_OAUTH_SERVICE_NAME)
	redirectParamUrl := util.GetBussinessDomain() + fmt.Sprintf(JINGDONG_OAUTH_CODE_REDIRECT_PATH_TEMPLATE, channel.ChannelId)
	redirectUrl := authDomain + fmt.Sprintf(JINGDONG_OAUTH_LOGIN_PATH_TEMPLATE, channel.AppId, redirectParamUrl, bson.NewObjectId().Hex())
	redirectUrlParsed, err := url.Parse(redirectUrl)
	if err != nil {
		self.responseError(c, http.StatusBadRequest)
		return
	}
	values := redirectUrlParsed.Query()
	redirectUrlParsed.RawQuery = values.Encode()
	c.Redirect(http.StatusSeeOther, redirectUrlParsed.String())
}

func (self *JdV2Handler) responseError(c *gin.Context, code int) {
	c.JSON(code, map[string]string{"message": http.StatusText(code)})
	c.Abort()
}

func (self *JdV2Handler) responseSPI(c *gin.Context, subResult interface{}) {
	writeJson(c, JDSPIResponse{
		SubCode:    0,
		SubMessage: "",
		SubResult:  subResult,
	})
}

/**
 * 会员通同步线下会员信息接口 2.1 版本包括五种场景（CardStatus）：
 * 0-线下同步到线上通知匹配结果；
 * 1-线上实时开卡及历史会员信息通知；
 * 2-线上注销通知；
 * 3-购物订单修改通知；
 * 4-扩展信息修改通知。
 */
func (self *JdV2Handler) synCustomer(c *gin.Context, jingdongErr error) {
	var (
		req                      JdSynCustomerRequest
		data                     map[string]interface{}
		resp                     = &JdSynCustomerResponse{}
		rpcMember, existedMember *pb_member.MemberDetailResponse
		err                      error
	)
	resp.SyncCode = JINGDONG_SYN_CUSTOMER_CODE_SUCCESS
	if jingdongErr != nil {
		resp.SyncCode = JINGDONG_SYN_CUSTOMER_CODE_OTHER
		self.responseSPI(c, resp)
		return
	}
	BindMapAndPayloadByWeaklyTyped(c, &data, &req, nil)

	extends, existedExtra := make(map[string]string), make(map[string]string)
	json.Unmarshal([]byte(req.OfflineCustomer.Ext), &extends)
	cardStatus := req.OfflineCustomer.CardStatus
	phoneNo := req.OfflineCustomer.PhoneNo
	xid := self.getOpenIdFromRequest(&req)

	existedMember = GetMemberByUniqueId(c, xid)
	var plainPhone string
	if existedMember != nil {
		plainPhone = existedMember.Phone
	}
	if self.shouldRegister(cardStatus) && phoneNo == "" {
		// 注册场景京东必须传手机号，否则令其重试
		log.Warn(c, "failed to register without phone when synCustomer", log.Fields{
			"req": fmt.Sprintf("%#v", req),
		})
		resp.SyncCode = JINGDONG_SYN_CUSTOMER_CODE_OTHER
		self.responseSPI(c, resp)
		return
	}
	if self.shouldModifyLevelOrTradeStats(cardStatus) {
		// 等级及订单修改场景，目前不需处理，故提前返回。
		// 新入会用户京东会连调两次本接口：一次入会，一次修改等级及订单；入会后业务侧修改 properties 的请求与京东修改等级及订单的请求并发；
		// 因为 upsertMember 会先查 properties 再整体 set，当业务侧对 properties 的修改介于前两个操作之间时会被覆盖掉。return 掉可规避此情形。
		self.responseSPI(c, resp)
		return
	}
	if self.shouldModifyExtends(cardStatus) && existedMember == nil {
		log.Warn(c, "Modify extends for inexistent jd:member", log.Fields{
			"req": fmt.Sprintf("%#v", req),
		})
	}
	if phoneNo != "" {
		encryptionKey := c.Request.Header.Get(core_util.CHANNEL_ENCRYPTION_KEY_IN_HEADER)
		plainPhone, err = self.aesDecrypt(c, encryptionKey, phoneNo)
		if err != nil {
			log.Error(c, "failed to decrypt jingdong phoneNo when synCustomer", log.Fields{
				"phoneNo": fmt.Sprintf("%s", phoneNo),
				"err":     fmt.Sprintf("%+v", err),
			})
			resp.SyncCode = JINGDONG_SYN_CUSTOMER_CODE_OTHER
			self.responseSPI(c, resp)
			return
		}
	}

	existedSocial := self.getSocialMatched(c, xid, existedMember)
	if existedSocial != nil {
		json.Unmarshal([]byte(existedSocial.Extra), &existedExtra)
	}
	// social.Extra 原样保存 Ext，因京东除注册场景外还可以仅变更部分 Ext，为避免覆盖既有 Extra，需要先合并一下。
	allExtra := self.mergeMap(existedExtra, extends)
	upsertMemberReq, err := self.getUpsertMemberRequest(c, plainPhone, extends, allExtra, &req)
	if err != nil {
		resp.SyncCode = JINGDONG_SYN_CUSTOMER_CODE_PARAMS_ERROR
		self.responseSPI(c, resp)
		return
	}
	if rpcMember, err = UpsertMember(c, upsertMemberReq); err != nil || rpcMember == nil {
		resp.SyncCode = JINGDONG_SYN_CUSTOMER_CODE_OTHER
		self.responseSPI(c, resp)
		return
	}

	// UpsertMember 没问题就触发京东入会 / 退会事件，不能放在下面同步积分、等级逻辑的后面；
	// 否则明明会员信息已经变更，却可能会因为同步过程中报错提前返回，导致入会 / 退会事件被遗漏。
	if eventType, ok := self.canTriggerEvent(cardStatus); ok {
		channelId := c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER)
		triggerJdEvent(c, rpcMember.Id, channelId, xid, plainPhone, eventType, extends)
	}

	if self.shouldRegister(cardStatus) {
		if err := self.initJingdongMember(c, xid, upsertMemberReq.OriginFrom.SubscribeTime, rpcMember, existedMember); err != nil {
			log.Error(c, "failed to initJingdongMember when synCustomer", log.Fields{
				"memberId": fmt.Sprintf("%+v", rpcMember.Id),
				"xid":      fmt.Sprintf("%+v", xid),
				"phoneNo":  fmt.Sprintf("%+v", phoneNo),
				"err":      fmt.Sprintf("%+v", err),
			})
			resp.SyncCode = JINGDONG_SYN_CUSTOMER_CODE_SYNC_FAILED
			self.responseSPI(c, resp)
			return
		}
	}

	if self.shouldModifyLevelOrTradeStats(cardStatus) {
		self.recordJingdongTradeStats(c, rpcMember, &req, resp)
	}

	log.Warn(c, "response to jingdong synCustomer", log.Fields{
		"resp": resp,
	})

	self.responseSPI(c, resp)
}

func (self *JdV2Handler) getExtByKey(extends map[string]string, key string) string {
	if ext, ok := extends[key]; ok {
		return ext
	} else {
		return ""
	}
}

func (self *JdV2Handler) mergeMap(maps ...map[string]string) map[string]string {
	mapMerged := map[string]string{}
	for _, m := range maps {
		for k, v := range m {
			mapMerged[k] = v
		}
	}
	return mapMerged
}

func (self *JdV2Handler) getUpsertMemberRequest(c *gin.Context, plainPhone string, reqExtends, allExtra map[string]string, req *JdSynCustomerRequest) (*pb_member.UpsertMemberRequest, error) {
	extraMarshaled := func(extra map[string]string) string {
		if len(extra) == 0 {
			return ""
		}
		extraMarshaled, err := json.Marshal(extra)
		if err != nil {
			return ""
		}
		return string(extraMarshaled)
	}(allExtra)

	cardStatus := req.OfflineCustomer.CardStatus
	upsertMemberReq := &pb_member.UpsertMemberRequest{
		OriginFrom: &origin.OriginInfo{
			Channel:     c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
			Origin:      constant.JD_MEMBER,
			OpenId:      self.getOpenIdFromRequest(req),
			Nickname:    self.getExtByKey(allExtra, "v_name"),
			ChannelName: c.Request.Header.Get(core_util.CHANNEL_NAME_IN_HEADER),
		},
	}

	if extraMarshaled != "" {
		upsertMemberReq.OriginFrom.Extra = extraMarshaled
	}

	// 京东营销云品牌直连版：直接更新京东传来的会员属性
	properties := []*pb_member.PropertyInfo{}
	if share_model.CChannel.IsJdCdpWay3(self.channel.IntegrationModes) {
		for k, v := range reqExtends {
			property := getProperty(k, v)
			if property != nil {
				properties = append(properties, property)
			}
		}
	}
	// 京东会员通 v2：
	// 仅在入会时需要更新 mobile 属性，入会事件触发业务侧更新其他 properties 的回调；
	// 京东会短时间分不同场景调多次 synCustomer，其他场景目前没必要更新 properties，否则可能覆盖掉业务侧更新。
	if (share_model.CChannel.IsJdCdpWay3(self.channel.IntegrationModes) ||
		self.shouldRegister(cardStatus)) &&
		plainPhone != "" {
		if property := getProperty("mobile", plainPhone); property != nil {
			properties = append(properties, property)
		}
	}
	if len(properties) > 0 {
		upsertMemberReq.Properties = properties
	}

	if self.shouldRegister(cardStatus) {
		subscribeTime, err := self.parseSubscribeTime(req.OfflineCustomer.BindTime)
		if err != nil {
			log.Error(c, "failed to parseSubscribeTime when getUpsertMemberRequest", log.Fields{
				"bindTime": fmt.Sprintf("%+v", req.OfflineCustomer.BindTime),
			})
			return nil, err
		}
		if subscribeTime != 0 {
			upsertMemberReq.OriginFrom.SubscribeTime = subscribeTime
		}
		upsertMemberReq.OriginFrom.Subscribed = true
		upsertMemberReq.IsActivated = &types.BoolValue{
			Value: true,
		}
		upsertMemberReq.ActivationSource = "jd_others"
	} else if self.shouldUnbind(cardStatus) {
		upsertMemberReq.OriginFrom.Subscribed = false
		upsertMemberReq.OriginFrom.UnsubscribeTime = time.Now().Unix()
	}

	return upsertMemberReq, nil
}

func (self *JdV2Handler) getOpenIdFromRequest(req *JdSynCustomerRequest) string {
	if req.OfflineCustomer.Xid != "" {
		return req.OfflineCustomer.Xid
	}
	return req.OfflineCustomer.Pin
}

func (self *JdV2Handler) parseSubscribeTime(bindTime string) (int64, error) {
	if bindTime == "" {
		return 0, errors.New("Failed to parse bindTime.")
	}
	if t, err := time.ParseInLocation("2006-01-02 15:04:05", bindTime, time.Local); nil == err {
		return t.Unix(), nil
	}
	// 兼容京东 SPI 测试工具，不然测试工具没法用
	if t, err := time.ParseInLocation("2006-01-02T15:04:05", bindTime, time.Local); nil == err {
		return t.Unix(), nil
	}
	return 0, errors.New("Failed to parse bindTime.")
}

func (self *JdV2Handler) isNewMember(subscribeTime int64) bool {
	return time.Now().Add(-time.Minute*1).Unix() < subscribeTime
}

func (self *JdV2Handler) canTriggerEvent(cardStatus uint32) (eventType string, ok bool) {
	if self.shouldRegister(cardStatus) {
		return component.MAIEVENT_JD_REGISTER, true
	}
	if self.shouldUnbind(cardStatus) {
		return component.MAIEVENT_JD_UNBIND, true
	}
	if self.shouldModifyExtends(cardStatus) {
		return component.MAIEVENT_JD_EDIT, true
	}
	return "", false

}

func (self *JdV2Handler) isSocialMatched(c *gin.Context, xid string, social *origin.OriginInfo) bool {
	if social == nil {
		return false
	}
	channelId := c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER)
	return social.Channel == channelId && social.Origin == constant.JD_MEMBER && xid != "" && social.OpenId == xid
}

func (self *JdV2Handler) isRegistered(c *gin.Context, xid string, member *pb_member.MemberDetailResponse) bool {
	if member == nil {
		return false
	}
	socials := append(member.Socials, member.OriginFrom)
	for _, social := range socials {
		if self.isSocialMatched(c, xid, social) &&
			social.FirstSubscribeTime != 0 {
			return true
		}
	}
	return false
}

func (self *JdV2Handler) getSocialMatched(c *gin.Context, xid string, member *pb_member.MemberDetailResponse) *origin.OriginInfo {
	if member == nil {
		return nil
	}
	socials := append(member.Socials, member.OriginFrom)
	for _, social := range socials {
		if self.isSocialMatched(c, xid, social) {
			return social
		}
	}
	return nil
}

func (self *JdV2Handler) aesDecrypt(c *gin.Context, key string, cipherText string) (plaintext string, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = core_errors.ConvertRecoveryError(r)
		}
	}()

	if key == "" {
		err = errors.New("key cannot be empty when decrypting ciphertext from jingdong")
		return
	}

	var (
		keyBytes = []byte(key)
		ivBytes  = []byte(JINGDONG_MEMBER_AES_IV)
	)

	cipherBytes, err := hex.DecodeString(cipherText)
	if err != nil {
		return
	}
	block, err := aes.NewCipher(keyBytes)
	if err != nil {
		return
	}
	blockModel := cipher.NewCBCDecrypter(block, ivBytes)
	plainBytes := make([]byte, len(cipherBytes))
	blockModel.CryptBlocks(plainBytes, cipherBytes)
	plainBytes = core_util.PKCS7UnPadding(plainBytes)
	plaintext = string(plainBytes)

	return
}

func (self *JdV2Handler) shouldRegister(cardStatus uint32) bool {
	return cardStatus == JINGDONG_SYN_CUSTOMER_CARD_STATUS_REGISTER
}

func (self *JdV2Handler) shouldUnbind(cardStatus uint32) bool {
	return cardStatus == JINGDONG_SYN_CUSTOMER_CARD_STATUS_UNBIND
}

func (self *JdV2Handler) shouldModifyLevelOrTradeStats(cardStatus uint32) bool {
	return cardStatus == JINGDONG_SYN_CUSTOMER_CARD_STATUS_MODIFY_TRADE
}

func (self *JdV2Handler) shouldModifyExtends(cardStatus uint32) bool {
	return cardStatus == JINGDONG_SYN_CUSTOMER_CARD_STATUS_MODIFY_EXT
}

func (self *JdV2Handler) initJingdongMember(c *gin.Context, openId string, subscribeTime int64, updatedMember *pb_member.MemberDetailResponse, existedMember *pb_member.MemberDetailResponse) error {
	// 等级是向京东的单向覆盖，可反复执行（比如这次升级失败，下次升级时自行就修复了），所以这里即使失败也不返回，不应中断下面的积分同步。
	if err := self.syncLevelToJingdong(c, openId, int64(updatedMember.Level), updatedMember); err != nil {
		log.Error(c, "failed to syncLevelToJingdong when initJingdongMember", log.Fields{
			"memberId": fmt.Sprintf("%+v", updatedMember.Id),
			"openId":   fmt.Sprintf("%+v", openId),
			"level":    fmt.Sprintf("%+v", updatedMember.Level),
			"err":      fmt.Sprintf("%+v", err),
		})
	}

	// 取京东积分总额
	respJingdongCustomerPoint, err := self.getScoreFromJingdong(c, openId)
	if err != nil {
		return err
	}
	jingdongPoint := respJingdongCustomerPoint.Result.Result

	if self.isRegistered(c, openId, existedMember) || self.isNewMember(subscribeTime) {
		// 两种情形下以 portal 侧为准，向京东单向同步积分：
		// 1. 已入会退会后重新入会的情况；
		// 2. 增量会员：bindTime 距离当前时间小于 1min 的，视为增量会员（UA 会删除 social，造成增量会员携带 portal 积分的情况）；
		err = self.syncScoreFromPortalToJingdong(c, openId, updatedMember.Score, jingdongPoint)
	} else {
		// 双向同步积分
		err = self.syncScoreTwoWay(c, openId, updatedMember.Score, jingdongPoint, updatedMember)
	}
	return err
}

func (self *JdV2Handler) syncScoreFromPortalToJingdong(c *gin.Context, xid string, maiPoint, jingdongPoint int64) error {
	// member 存量积分覆盖京东积分
	var diffScore = maiPoint - jingdongPoint
	if err := self.syncScoreToJingdong(c, xid, diffScore); err != nil {
		return err
	}
	return nil
}

func (self *JdV2Handler) syncScoreTwoWay(c *gin.Context, xid string, maiPoint, jingdongPoint int64, updatedMember *pb_member.MemberDetailResponse) error {
	// 累加 member 存量积分到京东
	if err := self.syncScoreToJingdong(c, xid, maiPoint); err != nil {
		return err
	}

	// 累加京东存量积分到 member
	if jingdongPoint == 0 {
		log.Warn(c, "jingdong point is zero when syncScoreTwoWay", log.Fields{
			"memberId": fmt.Sprintf("%+v", updatedMember.Id),
			"xid":      fmt.Sprintf("%+v", xid),
			"point":    fmt.Sprintf("%+v", jingdongPoint),
		})
		return nil
	}
	updateScoreRequest := &pb_member.UpdateScoreRequest{
		Ids:         []string{updatedMember.Id},
		Score:       jingdongPoint,
		ChannelId:   c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
		Origin:      constant.JD_MEMBER,
		CreatedAt:   time.Now().Unix(),
		Brief:       SYNC_FROM_JD,
		Description: JINGDONG_SYNC_SCORE_FIRST_TIME_DESC,
		Identifier:  fmt.Sprintf("%s:%s", JINGDONG_SYNC_SCORE_FROM_JINGDONG_FIRST_TIME_BRIEF, xid),
	}
	if _, err := UpdateScore(c, updateScoreRequest); err != nil {
		return err
	}
	return nil
}

func (self *JdV2Handler) syncLevelToJingdong(c *gin.Context, xid string, level int64, member *pb_member.MemberDetailResponse) error {
	req := &jingdong.SyncCustomerCrmGatewayDownclientRequest{
		Version: time.Now().Format("2006-01-02 15:04:05"),
		CustomerLevel: func() int64 {
			if level < 1 {
				level = 1
			} else if level > 5 {
				level = 5
			}
			return compatibleForUnderArmourMemberLevel(c, int64(level))
		}(),
		AppId:  c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
		AppKey: self.channel.AppId,
		XId:    xid,
		Extend: func() string {
			// 京东营销云品牌直连版 extend 字段不能传空，客户不想上传会员信息给京东，故传空 json
			if share_model.CChannel.IsJdCdpWay3(self.channel.IntegrationModes) {
				return "{}"
			}
			return ""
		}(),
	}
	if share_model.CChannel.IsJdCdpWay2(self.channel.IntegrationModes) {
		levelDetail, _ := mairpc.CMember.GetLevelForMember(c, &request.MemberIdRequest{
			MemberId: member.Id,
		})
		levelStartedAt, timeParseErr := util.TransStrToTime(member.LevelStartedAt)
		if levelDetail == nil ||
			!levelDetail.MemberLevelValidPeriod.IsEnabled ||
			levelDetail.MemberLevelValidPeriod.ValidMonth == 0 ||
			timeParseErr != nil {
			req.ExpireType = jingdong.JD_CDP_LEVEL_NO_EXPIRE
		} else {
			req.ExpireType = jingdong.JD_CDP_LEVEL_EXPIRE_RELATIVELY
			req.LevelStartedAt = levelStartedAt.Format("2006-01-02 15:04:05")
			req.LevelExpireAt = levelStartedAt.AddDate(0, int(levelDetail.MemberLevelValidPeriod.ValidMonth), -1).Format("2006-01-02 15:04:05")
		}
	}
	resp, err := self.jingdongClient.Member.SyncCustomerCrmGatewayDownclient(c, req)
	if err != nil {
		return err
	}

	if resp.OnlineResponse.SyncCode != JINGDONG_SYN_CUSTOMER_CODE_SUCCESS {
		return fmt.Errorf("%+v", resp)
	}

	return nil
}

func (self *JdV2Handler) getScoreFromJingdong(c *gin.Context, xid string) (*jingdong.GetCustomerPointJosOpenidResponse, error) {
	req := &jingdong.GetCustomerPointJosOpenidRequest{
		AppId: c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
		XId:   xid,
	}
	resp, err := self.jingdongClient.Member.GetCustomerPointJosOpenid(c, req)
	if err != nil {
		return nil, err
	}

	if resp.Result.Code != "200" {
		return nil, fmt.Errorf("%+v", resp)
	}

	return resp, nil
}

func (self *JdV2Handler) syncScoreToJingdong(c *gin.Context, xid string, points int64) error {
	if points == 0 {
		return nil
	}
	changeType := JINGDONG_USER_POINT_CHANGE_TYPE_ADD
	if points < 0 {
		changeType = JINGDONG_USER_POINT_CHANGE_TYPE_SUB
		points = -1 * points
	}
	req := &jingdong.JdOfflineSyncPointCrmGatewayUpclientRequest{
		BusinessId: fmt.Sprintf("%s:%s", JINGDONG_SYNC_SCORE_BRIEF, bson.NewObjectId().Hex()),
		Comment:    JINGDONG_SYNC_SCORE_FIRST_TIME_DESC,
		BrandsId:   c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
		Type:       changeType,
		Points:     int64(points),
		XidBuyer:   xid,
		OccurredAt: time.Now().Format("2006-01-02 15:04:05"),
	}
	resp, err := self.jingdongClient.Member.JdOfflineSyncPointCrmGatewayUpclient(c, req)
	if err != nil {
		return err
	}

	if resp.ReturnType.Code != "200" {
		return fmt.Errorf("%+v", resp)
	}

	return nil
}

func (self *JdV2Handler) userPointChange(c *gin.Context) {
	var (
		req       JdUserPointChangeRequest
		data      map[string]interface{}
		resp      = &JdUserPointChangeResponse{}
		rpcMember *pb_member.MemberDetailResponse
		err       error
	)
	// 兼容京东传字符串类型的浮点数，我们要转为 int，mapstructure 库一步转不过去（string -> float -> int），我们将 string 预处理成 float。
	changePointIncToFloat := func(data *map[string]interface{}) {
		if data == nil {
			return
		}
		if _, ok := (*data)["pointIncs"].(map[string]interface{})["pointInc"].(string); !ok {
			return
		}
		(*data)["pointIncs"].(map[string]interface{})["pointInc"] = cast.ToFloat64((*data)["pointIncs"].(map[string]interface{})["pointInc"])
	}
	BindMapAndPayloadByWeaklyTyped(c, &data, &req, changePointIncToFloat)

	openId := req.PointIncs.Pin
	if req.PointIncs.Xid != "" {
		openId = req.PointIncs.Xid
	}
	rpcMember = GetMemberByUniqueId(c, openId)
	if rpcMember == nil {
		resp.ResultSuccess = false
		resp.ErrorCode = JINGDONG_USER_POINT_CHANGE_CODE_NOT_EXISTED
		self.responseSPI(c, resp)
		return
	}

	score, err := self.getScoreToUpdate(c, rpcMember.Score, &req, resp)
	if err != nil {
		log.Error(c, "failed to getScoreToUpdate when userPointChange", log.Fields{
			"toChangeScore": fmt.Sprintf("%+v", req.PointIncs.PointInc),
			"existedScore":  fmt.Sprintf("%+v", rpcMember.Score),
			"resp":          fmt.Sprintf("%#v", resp),
			"err":           fmt.Sprintf("%#v", err),
		})
		self.responseSPI(c, resp)
		return
	}
	metaBytes, _ := json.Marshal(req)
	updateScoreRequest := &pb_member.UpdateScoreRequest{
		Ids:         []string{rpcMember.Id},
		Score:       score,
		ChannelId:   c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
		Origin:      constant.JD_MEMBER,
		CreatedAt:   time.Now().Unix(),
		Brief:       SYNC_FROM_JD,
		SubBrief:    getScoreInfoSubBriefByContent(req.PointIncs.Content, score),
		Description: DESCRIPTION,
		Identifier:  req.PointIncs.RecordId,
		BusinessId:  req.PointIncs.RecordId,
		Meta:        string(metaBytes),
	}
	if req.PointIncs.Content != "" {
		updateScoreRequest.Description = addJDPrefixToDesc(req.PointIncs.Content)
	}
	updateScoreResp, err := UpdateScore(c, updateScoreRequest)
	if err != nil {
		resp.ResultSuccess = false
		if strings.Contains(err.Error(), "invalidArgument") {
			resp.ErrorCode = JINGDONG_USER_POINT_CHANGE_CODE_PARAMS_ERROR
		} else if strings.Contains(err.Error(), fmt.Sprint(codes.InvalidMemberIds)) ||
			strings.Contains(err.Error(), fmt.Sprint(codes.MemberNotFound)) {
			resp.ErrorCode = JINGDONG_USER_POINT_CHANGE_CODE_NOT_EXISTED
		} else if strings.Contains(err.Error(), extension.MGO_ERROR_DUPLICATE_KEY) {
			resp.ErrorCode = JINGDONG_USER_POINT_CHANGE_CODE_EXECUTED
		} else {
			resp.ErrorCode = JINGDONG_USER_POINT_CHANGE_CODE_OTHER
		}
	} else if len(updateScoreResp.FailedItems) > 0 {
		resp.ResultSuccess = false
		resp.ErrorCode = JINGDONG_USER_POINT_CHANGE_CODE_EXECUTED
	} else {
		resp.ResultSuccess = true
		resp.ErrorCode = JINGDONG_USER_POINT_CHANGE_CODE_SUCCESS
	}

	log.Warn(c, "response to jingdong point change", log.Fields{
		"resp": resp,
	})

	self.responseSPI(c, resp)
}

func (self *JdV2Handler) getScoreToUpdate(c *gin.Context, rpcMemberScore int64, req *JdUserPointChangeRequest, resp *JdUserPointChangeResponse) (int64, error) {
	if req.PointIncs.Type == JINGDONG_USER_POINT_CHANGE_TYPE_ADD {

		return int64(req.PointIncs.PointInc), nil
	} else if req.PointIncs.Type == JINGDONG_USER_POINT_CHANGE_TYPE_SUB {
		toSubScoreAbs := int64(math.Abs(float64(req.PointIncs.PointInc)))
		if rpcMemberScore < toSubScoreAbs {
			resp.ErrorCode = JINGDONG_USER_POINT_CHANGE_CODE_NOT_ENOUGH
			resp.ResultSuccess = false

			return 0, fmt.Errorf("%s", resp.ErrorCode)
		}

		return -1 * toSubScoreAbs, nil
	} else {
		resp.ErrorCode = JINGDONG_USER_POINT_CHANGE_CODE_PARAMS_ERROR
		resp.ResultSuccess = false

		return 0, fmt.Errorf("%s", resp.ErrorCode)
	}
}

func (self *JdV2Handler) getChannel(c *gin.Context) *account.ChannelDetailResponse {
	channelId := c.Query("channelId")
	if channelId == "" {
		self.responseError(c, http.StatusBadRequest)
		return nil
	}

	channel := GetChannelByChannelId(c, channelId, constant.JD_MEMBER)
	if channel == nil {
		channel = GetChannelByChannelId(c, channelId, constant.JD_COUPON)
	}
	if channel == nil || channel.AppId == "" || channel.AppSecret == "" {
		self.responseError(c, http.StatusUnauthorized)
		return nil
	}

	return channel
}

func (self *JdV2Handler) getAccessToken(c *gin.Context, path string) ([]byte, error) {
	respBody, resp, err := extension.RequestClient.PostJson(c, JINGDONG_OAUTH_SERVICE_NAME, path, &request.EmptyRequest{}, nil)
	log.Warn(c, "Getting accessToken from jingdong", log.Fields{
		"path":     path,
		"respBody": fmt.Sprintf("%+v", respBody),
		"resp":     fmt.Sprintf("%+v", resp),
		"err":      fmt.Sprintf("%+v", err),
	})
	if err != nil {
		return []byte{}, err
	}

	return respBody, nil
}

func (self *JdV2Handler) handleAccessToken(c *gin.Context, channel *pb_account.ChannelDetailResponse, respAccessToken []byte) {
	jdAccessTokenResponse := &JdAccessTokenResponse{}
	if err := json.Unmarshal(respAccessToken, jdAccessTokenResponse); err != nil {
		return
	}

	mairpc.CAccount.UpdateChannel(c, &pb_account.UpdateChannelRequest{
		ChannelId: channel.ChannelId,
		Token:     jdAccessTokenResponse.AccessToken,
		EnableSyncScore: &types.BoolValue{
			Value: true,
		},
	})
	// 更新京东云鼎 channels.token
	if channel.Origin == constant.JD_COUPON {
		_, _, err := extension.RequestClient.PostJson(c, "", "https://jdapi.quncrm.com/v1/channel/update", &UpdateJdChannelRequest{
			ChannelId: channel.ChannelId,
			Origin:    constant.JD_COUPON,
			Token:     jdAccessTokenResponse.AccessToken,
		}, nil)
		if err != nil {
			log.Error(c, "Failed to update channel.Token for MySQL database", log.Fields{
				"channelId": channel.ChannelId,
				"origin":    channel.Origin,
			})
		}
	}
	accountDetail, _ := client.GetAccountServiceClient().GetAccount(core_util.DuplicateContextWithAid(c, channel.AccountId), &request.EmptyRequest{})
	accountName := ""
	if accountDetail != nil {
		accountName = accountDetail.Company
	}
	mairpc.CAccount.UpsertExpirationReminder(c, &account.UpsertExpirationReminderRequest{
		Title:        "京东会员通 session 过期提醒",
		Content:      fmt.Sprintf("你好！环境：%s，租户 ID：%s，租户名称：%s，渠道 ID：%s，渠道 ChannelId：%s，session 有效期临近，请提醒续费！", conf.GetString("env"), channel.AccountId, accountName, channel.Id, channel.ChannelId),
		AdvancedDays: []uint64{3, 7, 30},
		ExpiredAt:    time.Now().Add(time.Second * time.Duration(jdAccessTokenResponse.ExpiresIn)).Format(core_util.RFC3339Mili),
		Business:     REMINDER_BUSINESS_PLATFORM_SESSION,
		BusinessId:   channel.ChannelId,
	})
}

func (self *JdV2Handler) initJingdongTradeStats(c *gin.Context, rpcMember *pb_member.MemberDetailResponse, req *JdSynCustomerRequest, resp *JdSynCustomerResponse) {
	// TODO：@fortune.fu：订单处理逻辑待定。暂不记录 jingdongTradeStats，不 Mock 订单。
	if true {
		return
	}

	channelId := c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER)
	// 保存京东订单统计信息，后续新的统计信息同步过来后与该表中最新一条记录的金额作差，Mock 增量订单。
	tradeStats, err := mairpc.CTrade.CreateJingdongTradeStats(c, &pb_trade_order.CreateJingdongTradeStatsRequest{
		MemberId:            rpcMember.Id,
		ChannelId:           channelId,
		TotalOrderCount:     req.OfflineCustomer.OnlineTotalOrderCount,
		TotalOrderAmountFen: share.Yuan2Fen(req.OfflineCustomer.OnlineTotalOrderPrice),
	})
	if err != nil {
		resp.SyncCode = JINGDONG_SYN_CUSTOMER_CODE_OTHER
		return
	}

	// 使用京东提供的订单统计信息 Mock 京东订单，以触发积分、成长值等用户激励，并使用 change 场景同步给京东。
	_, err = self.mockJDOrder(c, channelId, float64(tradeStats.TotalOrderAmountFen), rpcMember)
	if err != nil {
		resp.SyncCode = JINGDONG_SYN_CUSTOMER_CODE_SYNC_FAILED
		return
	}
}

func (self *JdV2Handler) recordJingdongTradeStats(c *gin.Context, rpcMember *pb_member.MemberDetailResponse, req *JdSynCustomerRequest, resp *JdSynCustomerResponse) {
	// TODO：@fortune.fu：订单处理逻辑待定。暂不记录 jingdongTradeStats，不 Mock 订单。
	if true {
		return
	}

	channelId := c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER)
	var latestTotalAmount, discountAmountFen uint64 = 0, 0
	latestTradeStats, err := mairpc.CTrade.GetLatestJingdongTradeStats(c, &pb_trade_order.GetLatestJingdongTradeStatsRequest{
		MemberId:  rpcMember.Id,
		ChannelId: channelId,
	})
	newTradeStats, createErr := mairpc.CTrade.CreateJingdongTradeStats(c, &pb_trade_order.CreateJingdongTradeStatsRequest{
		MemberId:            rpcMember.Id,
		ChannelId:           channelId,
		TotalOrderCount:     req.OfflineCustomer.OnlineTotalOrderCount,
		TotalOrderAmountFen: share.Yuan2Fen(req.OfflineCustomer.OnlineTotalOrderPrice),
	})

	if err != nil || latestTradeStats == nil {
		// 正常情况在存量对齐时就记录了订单汇总数据，不应该走到这
		log.Error(c, "Failed to sync new jingdong trade stats", log.Fields{
			"msg": "Trade stats is not initial",
		})
		// 走到这里是不正常的，为避免多发激励，本次订单同步仅记录日志，不 Mock 新订单
		latestTotalAmount = newTradeStats.TotalOrderAmountFen
		if createErr != nil {
			resp.SyncCode = JINGDONG_SYN_CUSTOMER_CODE_OTHER
			return
		}
	} else {
		latestTotalAmount = latestTradeStats.TotalOrderAmountFen
	}

	discountAmountFen = share.Yuan2Fen(req.OfflineCustomer.OnlineTotalOrderPrice) - latestTotalAmount
	log.Warn(c, "To mock jingdong member order", log.Fields{
		"reqAmount":         share.Yuan2Fen(req.OfflineCustomer.OnlineTotalOrderPrice),
		"latestTotalAmount": latestTotalAmount,
		"discountAmountFen": discountAmountFen,
	})
	if discountAmountFen <= 0 {
		return
	}

	_, err = self.mockJDOrder(c, channelId, float64(discountAmountFen), rpcMember)
	if err != nil {
		resp.SyncCode = JINGDONG_SYN_CUSTOMER_CODE_SYNC_FAILED
		return
	}
}

func (self *JdV2Handler) mockJDOrder(c *gin.Context, channelId string, mockAmountFen float64, rpcMember *pb_member.MemberDetailResponse) (*pb_trade_order.OrderDetailResponse, error) {
	req := &pb_trade_order.UpsertOrderRequest{
		Sid:             channelId,
		OrderId:         fmt.Sprintf("JD_MOCK_%s", bson.NewObjectId().Hex()),
		OrderStatus:     "tradeBuyerSigned",
		OrderCreateTime: time.Now().Format("2006-01-02 15:04:05"),
		UnionId:         rpcMember.OriginFrom.UnionId,
		BuyerId:         rpcMember.Id,
		BuyerNickname:   rpcMember.OriginFrom.Nickname,
		GoodsDetail: []*pb_trade_order.GoodsDetail{
			{
				GoodsNumber:    bson.NewObjectId().Hex(),
				Name:           DESCRIPTION,
				Price:          mockAmountFen,
				Count:          uint64(1),
				Discount:       float64(0),
				DiscountPrice:  mockAmountFen,
				SubtotalAmount: mockAmountFen,
			},
		},
		DiscountAmount: float64(0),
		ActualAmount:   mockAmountFen,
	}
	resp, err := mairpc.CTrade.UpsertOrder(c, req)
	return resp, err
}

func getScoreInfoSubBriefByContent(content string, score int64) string {
	if (strings.Index(content, "积分兑换优惠券")+strings.Index(content, "积分兑换优商品")) > -2 && score < 0 {
		return "exchange_score"
	}
	return ""
}

// https://gitlab.maiscrm.com/mai/impl/under-armour/under-armour-module/-/issues/1501
func compatibleForUnderArmourMemberLevel(c *gin.Context, level int64) int64 {
	if isUnderArmourAccount(c) {
		return 1
	}
	return level
}

func isUnderArmourAccount(c *gin.Context) bool {
	underArmourAccountIds := []string{
		"6180ea32e0df4b1472704228", // UA production
		"6166d86f9526b7705a449df3", // UA staging
		"636b604e4017cb6e6a5ee953", // UA feature test
	}
	return util.StrInArray(getAccountId(c), &underArmourAccountIds)
}
