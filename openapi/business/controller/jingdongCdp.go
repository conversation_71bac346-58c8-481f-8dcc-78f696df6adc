package controller

import (
	"context"
	"errors"
	"fmt"
	"math"
	"regexp"
	"strings"
	"sync"
	"time"

	core_component "mairpc/core/component"
	"mairpc/core/component/jingdong"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/openapi/business/mairpc"
	"mairpc/openapi/business/util"
	business_util "mairpc/openapi/business/util"
	pb_account "mairpc/proto/account"
	pb_client "mairpc/proto/client"
	"mairpc/proto/common/origin"
	pb_request "mairpc/proto/common/request"
	"mairpc/proto/common/types"
	pb_member "mairpc/proto/member"
	pb_trade_order "mairpc/proto/trade/order"
	"mairpc/service/member/model"
	"mairpc/service/share/component"
	"mairpc/service/share/constant"

	"github.com/Lofanmi/chinese-calendar-golang/calendar"
	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"github.com/modern-go/reflect2"
	"github.com/spf13/cast"
)

var jdCdpHandler *JdCdpHandler

type JdCdpHandler struct {
	*JdV2Handler
	resp *jingdong.JdCdpWay2Response
}

var jsoniterUsingNum = jsoniter.Config{
	EscapeHTML:             true,
	SortMapKeys:            true,
	ValidateJsonRawMessage: true,
	UseNumber:              true,
}.Froze()

func init() {
	actions := []*ControllerAction{
		{
			Name:        "member_jingdong_cdp_register",
			Method:      "POST",
			Path:        "/member/jingdongCdp/register",
			HandlerFunc: jdCdpHandler.HandleJingdongMember,
		},
		{
			Name:        "member_jingdong_cdp_query_points_detail",
			Method:      "POST",
			Path:        "/member/jingdongCdp/queryPointDetails",
			HandlerFunc: jdCdpHandler.HandleJingdongMember,
		},
		{
			Name:        "member_jingdong_cdp_query_point",
			Method:      "POST",
			Path:        "/member/jingdongCdp/queryPoints",
			HandlerFunc: jdCdpHandler.HandleJingdongMember,
		},
		{
			Name:        "member_jingdong_cdp_update_point",
			Method:      "POST",
			Path:        "/member/jingdongCdp/updatePoint",
			HandlerFunc: jdCdpHandler.HandleJingdongMember,
		},
		{
			Name:        "member_jingdong_cdp_query_grade_detail",
			Method:      "POST",
			Path:        "/member/jingdongCdp/queryGradeDetails",
			HandlerFunc: jdCdpHandler.HandleJingdongMember,
		},
		{
			Name:        "member_jingdong_cdp_query_grade",
			Method:      "POST",
			Path:        "/member/jingdongCdp/queryGrades",
			HandlerFunc: jdCdpHandler.HandleJingdongMember,
		},
		{
			Name:        "member_jingdong_cdp_update",
			Method:      "POST",
			Path:        "/member/jingdongCdp/update",
			HandlerFunc: jdCdpHandler.HandleJingdongMember,
		},
		{
			Name:        "member_jingdong_cdp_query_expire_point",
			Method:      "POST",
			Path:        "/member/jingdongCdp/queryExpirePoint",
			HandlerFunc: jdCdpHandler.HandleJingdongMember,
		},
	}
	enableActions(actions...)
	jsoniter.RegisterTypeDecoderFunc(reflect2.TypeOf("").String(), core_util.StringDecoderFunc)
	jsoniter.RegisterTypeDecoderFunc(reflect2.TypeOf(int64(1)).String(), core_util.Int64DecoderFunc)
}

func (handler *JdCdpHandler) HandleJingdongMember(c *gin.Context) {
	business_util.SwitchOnResponseBodyLog(c)
	handler = &JdCdpHandler{
		&JdV2Handler{},
		&jingdong.JdCdpWay2Response{},
	}
	switch c.Request.URL.Path {
	case "/v1/member/jingdongCdp/register":
		handler.register(c)
	case "/v1/member/jingdongCdp/queryPoints":
		handler.queryPoints(c)
	case "/v1/member/jingdongCdp/queryPointDetails":
		handler.queryPointDetails(c)
	case "/v1/member/jingdongCdp/updatePoint":
		handler.updatePoint(c)
	case "/v1/member/jingdongCdp/queryGrades":
		handler.queryGrades(c)
	case "/v1/member/jingdongCdp/queryGradeDetails":
		handler.queryGradeDetails(c)
	case "/v1/member/jingdongCdp/update":
		handler.update(c)
	case "/v1/member/jingdongCdp/queryExpirePoint":
		handler.queryExpirePoint(c)
	default:
		return
	}
	writeJson(c, *handler.resp)
}

const (
	JD_CDP_GENDER_FEMALE  uint32 = 0
	JD_CDP_GENDER_MALE    uint32 = 1
	JD_CDP_GENDER_UNKNOWN uint32 = 3
)

var (
	jdCdpGenderMap = map[uint32]string{
		JD_CDP_GENDER_FEMALE:  "female",
		JD_CDP_GENDER_MALE:    "male",
		JD_CDP_GENDER_UNKNOWN: "unknown",
	}
)

const (
	JD_CDP_BIRTH_SOLAR_CALENDAR uint32 = 1
	JD_CDP_BIRTH_LUNAR_CALENDAR uint32 = 2
)

const (
	JD_CDP_BLOOD_UNKNOWN uint32 = 0
	JD_CDP_BLOOD_A       uint32 = 1
	JD_CDP_BLOOD_B       uint32 = 2
	JD_CDP_BLOOD_AB      uint32 = 3
	JD_CDP_BLOOD_O       uint32 = 4
)

const (
	JD_CDP_UNMARRIED uint32 = 0
	JD_CDP_MARRIED   uint32 = 1
)

const (
	JD_CDP_ORIGIN_SCENE_PHONE             uint32 = 1
	JD_CDP_ORIGIN_SCENE_PC                uint32 = 2
	JD_CDP_ORIGIN_SCENE_DEFAULT_BIND_CARD uint32 = 3
	JD_CDP_ORIGIN_SCENE_ORDER             uint32 = 4
	JD_CDP_ORIGIN_SCENE_IMPORT            uint32 = 5
)

const (
	JD_CDP_LEVEL_MIN uint32 = 1
	JD_CDP_LEVEL_MAX uint32 = 5
)

const (
	JD_CDP_POINT_NO_EXPIRE         uint32 = 0
	JD_CDP_POINT_EXPIRE_RELATIVELY uint32 = 1
	JD_CDP_POINT_EXPIRE_ABSOLUTELY uint32 = 2
)

const (
	JD_CDP_EXPIRE_POINT_NO_REMIND uint32 = 0
	JD_CDP_EXPIRE_POINT_REMIND    uint32 = 1
)

const (
	JD_CDP_POINT_ISSUE                          uint32 = 27
	JD_CDP_POINT_ISSUE_STORE_CHECK_IN           uint32 = 29
	JD_CDP_POINT_ISSUE_FOLLOW_STORE             uint32 = 30
	JD_CDP_POINT_ISSUE_INTERACTION              uint32 = 31
	JD_CDP_POINT_ISSUE_OTHER_CHANNEL            uint32 = 32
	JD_CDP_POINT_CONSUME                        uint32 = 26
	JD_CDP_POINT_CONSUME_EXCHANGE_COUPON        uint32 = 33
	JD_CDP_POINT_CONSUME_EXCHANGE_RED_PACKET    uint32 = 34
	JD_CDP_POINT_CONSUME_EXCHANGE_JINGDONG_BEAN uint32 = 35
	JD_CDP_POINT_CONSUME_EXCHANGE_OTHER_RIGHTS  uint32 = 36
	JD_CDP_POINT_CONSUME_EXCHANGE_INTERACTION   uint32 = 37
	JD_CDP_POINT_UPDATE_MANUALLY                uint32 = 38
)

const JD_CDP_LEVEL_CHANGE_TYPE_DEFAULT uint32 = 38

const (
	JD_CDP_BIND_STATUS_CRM     uint32 = 0
	JD_CDP_BIND_STATUS_JD      uint32 = 1
	JD_CDP_BIND_STATUS_MATCHED uint32 = 2
	JD_CDP_BIND_STATUS_UNBOUND uint32 = 3
)

const (
	JD_CDP_UPDATE_WITHOUT_TRADE uint32 = 0
	JD_CDP_UPDATE_WITH_TRADE    uint32 = 1
)

const (
	JD_CDP_REGISTER_RESP_CODE_SUC  = "SUC"
	JD_CDP_REGISTER_RESP_CODE_FAIL = "FAIL"
)

const (
	JD_CDP_UPDATE_POINT_RESP_CODE_SUC        = "SUC"
	JD_CDP_UPDATE_POINT_RESP_CODE_NOT_ENOUGH = "E01"
	JD_CDP_UPDATE_POINT_RESP_CODE_EXECUTED   = "E02"
	JD_CDP_UPDATE_POINT_RESP_CODE_OTHER      = "E03"
)

const (
	JD_CDP_UPDATE_MEMBER uint32 = 0
	JD_CDP_UPDATE_TRADE  uint32 = 1
)

const (
	JD_CDP_UPDATE_RESP_CODE_SUC            = "SUC"
	JD_CDP_UPDATE_RESP_CODE_INVALID_PARAMS = "E01"
	JD_CDP_UPDATE_RESP_CODE_OTHER          = "E03"
)

const (
	JD_CDP_POINT_ADD = "add"
	JD_CDP_POINT_SUB = "sub"
)

var jdCdpPointChangeTypeMap = map[string]string{
	JD_CDP_POINT_ADD: model.SCORE_HISTORY_CHANGE_TYPE_INCREASE,
	JD_CDP_POINT_SUB: model.SCORE_HISTORY_CHANGE_TYPE_DECREASE,
}

const (
	JD_CDP_SORT_ASC  = "asc"
	JD_CDP_SORT_DESC = "desc"
)

type JdCdpRegisterRequest struct {
	jingdong.JdCdpWay2CommonRequiredRequest `json:",inline"`
	CdpId                                   string                      `json:"cdp_Id"`
	MixPhone                                string                      `json:"MixPhone,omitempty"`
	MixPin                                  string                      `json:"MixPin,omitempty"`
	JdBindTime                              string                      `json:"jd_bind_time,omitempty"`
	Extend                                  *jingdong.JdCdpMemberDetail `json:"extend,omitempty"`
}

type JdCdpRegisterResponse struct {
	CrmId string `json:"cardNumber"`
	Code  string `json:"retCode"`
}

type JdCdpQueryPointsRequest struct {
	jingdong.JdCdpWay2CommonRequiredRequest `json:",inline"`
	CrmIds                                  []string `json:"accounts"`
}

type JdCdpQueryPointsResponse map[string]int64

type JdCdpQueryPointDetailsRequest struct {
	jingdong.JdCdpWay2CommonRequiredRequest `json:",inline"`
	CrmId                                   string `json:"account"`
	Page                                    uint32 `json:"page"`
	PageSize                                uint32 `json:"pageSize"`
	StartTime                               string `json:"startTime,omitempty"`
	EndTime                                 string `json:"endTime,omitempty"`
	ChangeType                              string `json:"changeType,omitempty"`
	PointType                               uint32 `json:"pointType,omitempty"`
	Sort                                    string `json:"order,omitempty"`
}

type JdCdpQueryPointDetailsResponse struct {
	PointList  []*JdCdpQueryPointDetailsResponseItem `json:"pointList"`
	TotalCount uint64                                `json:"totalCount"`
	TotalPage  uint64                                `json:"totalPage"`
	PageNo     uint32                                `json:"pageNo"`
	PageSize   uint32                                `json:"pageSize"`
}

type JdCdpQueryPointDetailsResponseItem struct {
	Point        int64  `json:"point"`
	ChangeType   string `json:"changeType"`
	CreatedAt    string `json:"occurTime"`
	RecordId     string `json:"recordId"`
	Description  string `json:"content"`
	PointType    uint32 `json:"pointType"`
	Platform     string `json:"platform"`
	CurrentPoint int64  `json:"curPoints"`
	Extend       string `json:"extend,omitempty"`
}

type JdCdpUpdatePointRequest struct {
	jingdong.JdCdpWay2CommonRequiredRequest `json:",inline"`
	CrmId                                   string `json:"account"`
	Point                                   int64  `json:"point"`
	ChangeType                              string `json:"changeType"`
	Description                             string `json:"content"`
	PointType                               uint32 `json:"pointType"`
	CreatedAt                               string `json:"occurTime"`
	Extend                                  string `json:"extend,omitempty"`
}

type JdCdpUpdatePointResponse struct {
	Code     string `json:"retCode"`
	RecordId string `json:"record_Id"`
}

type JdCdpQueryGradesRequest struct {
	jingdong.JdCdpWay2CommonRequiredRequest `json:",inline"`
	CrmIds                                  []string `json:"accounts"`
}

type JdCdpQueryGradesResponse map[string]*JdCdpQueryGradesResponseDetail
type JdCdpQueryGradesResponseDetail struct {
	Level      uint32 `json:"grade"`
	ExpireType uint32 `json:"expire_type"`
	BeginAt    string `json:"begin_at"`
	EndAt      string `json:"end_at"`
}

type JdCdpQueryGradeDetailsRequest struct {
	jingdong.JdCdpWay2CommonRequiredRequest `json:",inline"`
	CrmId                                   string `json:"account"`
	Page                                    uint32 `json:"page"`
	PageSize                                uint32 `json:"pageSize"`
	Sort                                    string `json:"order,omitempty"`
}

type JdCdpQueryGradeDetailsResponse struct {
	GradeList  []*JdCdpQueryGradeDetailsResponseGrade `json:"gradeList"`
	TotalCount uint64                                 `json:"totalCount"`
	TotalPage  uint64                                 `json:"totalPage"`
	PageNo     uint32                                 `json:"pageNo"`
	PageSize   uint32                                 `json:"pageSize"`
}

type JdCdpQueryGradeDetailsResponseGrade struct {
	GradeBeforeChange uint64 `json:"gradeBeforeChange"`
	Level             uint64 `json:"grade"`
	CreatedAt         string `json:"occurTime"`
	RecordId          string `json:"recordId"`
	Description       string `json:"content"`
	Platform          string `json:"platform"`
	BeginAt           string `json:"begin_at"`
	EndAt             string `json:"end_at"`
	ExpireType        uint32 `json:"expire_type"`
	ChangeType        uint32 `json:"type"`
}

type JdCdpUpdateRequest struct {
	jingdong.JdCdpWay2CommonRequiredRequest `json:",inline"`
	CrmId                                   string                      `json:"account"`
	BindStatus                              uint32                      `json:"bind_status,omitempty"`
	CdpId                                   string                      `json:"cdp_Id,omitempty"`
	MixPhone                                string                      `json:"MixPhone,omitempty"`
	MixPin                                  string                      `json:"MixPin,omitempty"`
	JdBindTime                              string                      `json:"jd_bind_time,omitempty"`
	CardStatus                              uint32                      `json:"cardStatus"`
	TotalOrderAmountFen                     uint64                      `json:"totalOrderPrice,omitempty"`
	TotalOrderCount                         uint64                      `json:"totalOrderCount,omitempty"`
	Member                                  *jingdong.JdCdpMemberDetail `json:"memberData,omitempty"`
}

type JdCdpUpdateResponse struct {
	Code string `json:"retCode"`
	Msg  string `json:"retMsg,omitempty"`
}

type JdCdpQueryExpirePointRequest struct {
	jingdong.JdCdpWay2CommonRequiredRequest `json:",inline"`
	CrmId                                   string `json:"account"`
}

type JdCdpQueryExpirePointResponse struct {
	ExpiredPoint       int64  `json:"expire_point"`
	ExpirePoint        int64  `json:"expiring_point"`
	ExpirationType     uint32 `json:"score_expire_type"`
	RemindAt           string `json:"score_expire_time"`
	EnableNotification uint32 `json:"is_score_expiring_remind"`
	ExpireAt           string `json:"score_expiring_time"`
}

func (handler *JdCdpHandler) register(c *gin.Context) {
	handler.setRespWithError("", &JdCdpRegisterResponse{
		Code: JD_CDP_REGISTER_RESP_CODE_FAIL,
	})
	var req JdCdpRegisterRequest
	if err := handler.shouldBind(c, &req); err != nil {
		return
	}
	if err := handler.setChannel(c); err != nil {
		return
	}
	if err := handler.validateAppKeyAndToken(req.AppKey); err != nil {
		return
	}
	if err := handler.setJingdongClient(c); err != nil {
		return
	}
	openId := req.MixPin
	existedMember, plainPhone, crmId, err := handler.getExistedMemberViaChannelMixPhone(c, req.CdpId, "", openId)
	if err != nil {
		return
	}
	upsertMemberReq, err := handler.getUpsertMemberRequest(c, plainPhone, openId, crmId, req.JdBindTime, req.Extend, JD_CDP_BIND_STATUS_JD)
	if err != nil {
		handler.setRespWithError("Invalid Params", nil)
		return
	}
	updatedMember, err := mairpc.CMember.UpsertMember(c, upsertMemberReq)
	if err != nil {
		handler.setRespWithError("Internal Error", nil)
		return
	}
	handler.setRespToSuccess(&JdCdpRegisterResponse{
		CrmId: crmId,
		Code:  JD_CDP_REGISTER_RESP_CODE_SUC,
	})
	extends := make(map[string]interface{})
	jsoniterUsingNum.Unmarshal([]byte(upsertMemberReq.OriginFrom.Extra), &extends)
	c1 := c.Copy()
	core_component.GO(c1, func(ctx context.Context) {
		if err := handler.syncMemberDetails(c1, crmId, openId, req.Extend.Level, req.Extend.Point, updatedMember, existedMember); err != nil {
			log.Error(c1, "Failed to syncMemberDetails when registering jd cdp member", log.Fields{
				"memberId": fmt.Sprintf("%+v", updatedMember.Id),
				"crmId":    fmt.Sprintf("%+v", crmId),
				"err":      fmt.Sprintf("%+v", err),
			})
		}
		channelId := c1.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER)
		handler.triggerJdEvent(c1, updatedMember.Id, channelId, openId, plainPhone, component.MAIEVENT_JD_REGISTER, extends)
	})
}

func (handler *JdCdpHandler) getExistedMemberViaChannelMixPhone(
	c *gin.Context,
	mixPhone, oldCrmId, openId string,
) (existedMember *pb_member.MemberDetailResponse, plainPhone, newCrmId string, err error) {
	var channelMixPhone *pb_member.ChannelMixPhoneDetailResponse
	if mixPhone != "" {
		channelMixPhone, err = GetChannelMixPhone(c, c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER), mixPhone)
	} else if oldCrmId != "" {
		channelMixPhone, err = pb_client.GetMemberServiceClient().GetChannelMixPhone(GetGrpcContext(c), &pb_member.GetChannelMixPhoneRequest{
			ChannelId: c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
			OpenId:    oldCrmId,
		})
	}
	if channelMixPhone != nil {
		plainPhone = channelMixPhone.Phone
		if mixPhone == "" {
			mixPhone = channelMixPhone.MixPhone
		}
	}
	if plainPhone != "" {
		existedMember = GetMemberByUniqueId(c, plainPhone)
	}
	if existedMember == nil && openId != "" {
		existedMember = GetMemberByUniqueId(c, openId)
	}
	if existedMember == nil && oldCrmId != "" {
		existedMember = GetMemberByUniqueId(c, oldCrmId)
	}
	if existedMember == nil && channelMixPhone != nil && channelMixPhone.OpenId != "" {
		existedMember = GetMemberByUniqueId(c, channelMixPhone.OpenId)
	}
	if existedMember != nil {
		plainPhone = existedMember.Phone
	}
	newCrmId = oldCrmId
	if newCrmId == "" && openId != "" && existedMember != nil {
		existedSocial := handler.getSocialMatched(c, openId, existedMember)
		if existedSocial != nil {
			newCrmId = existedSocial.UnionId
		}
	}
	if newCrmId == "" && channelMixPhone != nil && channelMixPhone.OpenId != "" {
		newCrmId = channelMixPhone.OpenId
	}
	if newCrmId == "" {
		newCrmId = jingdong.GenerateCrmId("", "")
	}
	if mixPhone != "" && (channelMixPhone == nil || channelMixPhone.OpenId != newCrmId) {
		err = UpsertOpenIdOfChannelMixPhone(GetGrpcContext(c), c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER), mixPhone, newCrmId)
		if err != nil {
			handler.setRespWithError("Internal Error", nil)
			return
		}
	}
	return
}

func (handler *JdCdpHandler) triggerJdEvent(c *gin.Context, memberId, channelId, openId, phoneNo, subType string, extends map[string]interface{}) {
	eventRequest := map[string]interface{}{
		"channelId":   channelId,
		"phoneNo":     phoneNo,
		"channelType": "JD",
	}
	if util.StrInArray(subType, &[]string{component.MAIEVENT_JD_REGISTER, component.MAIEVENT_JD_EDIT}) {
		eventRequest["extends"] = extends
	}
	properties, err := jsoniterUsingNum.Marshal(eventRequest)
	if err != nil {
		log.Error(c, "Failed to marshal jingdong eventProperties", log.Fields{})
		return
	}
	mairpc.CMember.SendCustomerEvent(c, &pb_member.SendCustomerEventRequest{
		MemberId:        memberId,
		Type:            subType,
		ChannelId:       channelId,
		OpenId:          openId,
		EventProperties: string(properties),
	})
}

func (handler *JdCdpHandler) update(c *gin.Context) {
	handler.setRespWithError("", &JdCdpUpdateResponse{
		Code: JD_CDP_UPDATE_RESP_CODE_OTHER,
	})
	var req JdCdpUpdateRequest
	if err := handler.shouldBind(c, &req); err != nil {
		return
	}
	if err := handler.setChannel(c); err != nil {
		return
	}
	if err := handler.validateAppKeyAndToken(req.AppKey); err != nil {
		return
	}
	if err := handler.setJingdongClient(c); err != nil {
		return
	}
	switch req.CardStatus {
	case JD_CDP_UPDATE_MEMBER:
		handler.updateMember(c, &req)
		return
	case JD_CDP_UPDATE_TRADE:
		handler.updateTrade(c, req.CrmId, req.TotalOrderCount, req.TotalOrderAmountFen, req.Timestamp)
		return
	default:
		handler.setRespWithError("Unknown CardStatus", nil)
		return
	}
}

func (handler *JdCdpHandler) updateMember(c *gin.Context, req *JdCdpUpdateRequest) {
	openId := req.MixPin
	existedMember, plainPhone, _, _ := handler.getExistedMemberViaChannelMixPhone(c, req.CdpId, req.CrmId, openId)
	extends := make(map[string]interface{})
	existedSocial := handler.getSocialMatched(c, openId, existedMember)
	if existedSocial != nil {
		jsoniterUsingNum.Unmarshal([]byte(existedSocial.Extra), &extends)
	}
	if extraMarshaled, err := jsoniterUsingNum.Marshal(req.Member); err == nil && len(extraMarshaled) > 0 {
		jsoniterUsingNum.Unmarshal(extraMarshaled, &extends)
	}
	if len(extends) > 0 {
		if extraMarshaled, err := jsoniterUsingNum.Marshal(extends); err == nil && len(extraMarshaled) > 0 {
			jsoniterUsingNum.Unmarshal(extraMarshaled, req.Member)
		}
	}
	upsertMemberReq, err := handler.getUpsertMemberRequest(c, plainPhone, openId, req.CrmId, req.JdBindTime, req.Member, req.BindStatus)
	if err != nil {
		handler.setRespWithError("Invalid Params", nil)
		return
	}
	updatedMember, err := mairpc.CMember.UpsertMember(c, upsertMemberReq)
	if err != nil || updatedMember == nil {
		handler.setRespWithError("Internal Error", nil)
		return
	}
	c1 := c.Copy()
	core_component.GO(c1, func(ctx context.Context) {
		if req.BindStatus == JD_CDP_BIND_STATUS_MATCHED && (existedSocial == nil || !existedSocial.Subscribed) {
			if err := handler.syncMemberDetails(c1, req.CrmId, openId, req.Member.Level, req.Member.Point, updatedMember, existedMember); err != nil {
				log.Error(c1, "Failed to syncMemberDetails when binding jd cdp member", log.Fields{
					"memberId": fmt.Sprintf("%+v", updatedMember.Id),
					"openId":   fmt.Sprintf("%+v", openId),
					"unionId":  fmt.Sprintf("%+v", req.CrmId),
					"err":      fmt.Sprintf("%+v", err),
				})
			}
		}
		channelId := c1.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER)
		eventSubType := func(bindStatus uint32) string {
			if bindStatus == JD_CDP_BIND_STATUS_UNBOUND || bindStatus == JD_CDP_BIND_STATUS_CRM {
				return component.MAIEVENT_JD_UNBIND
			} else if bindStatus == JD_CDP_BIND_STATUS_MATCHED && (existedSocial == nil || !existedSocial.Subscribed) {
				return component.MAIEVENT_JD_REGISTER
			} else {
				return component.MAIEVENT_JD_EDIT
			}
		}(req.BindStatus)
		handler.triggerJdEvent(c1, updatedMember.Id, channelId, openId, plainPhone, eventSubType, extends)
	})
	handler.setRespToSuccess(&JdCdpUpdateResponse{
		Code: JD_CDP_UPDATE_RESP_CODE_SUC,
	})
}

func (handler *JdCdpHandler) updateTrade(c *gin.Context, crmId string, totalOrderCount, totalOrderAmountFen, timestamp uint64) {
	if totalOrderAmountFen == 0 {
		handler.setRespToSuccess(&JdCdpUpdateResponse{
			Code: JD_CDP_UPDATE_RESP_CODE_SUC,
		})
		return
	}
	existedMember := getJdCdpMemberByCrmId(c, crmId)
	if err := handler.validateMember(existedMember); err != nil {
		return
	}
	channelId := c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER)
	var latestTotalAmountFen, discountAmountFen int64 = 0, 0
	latestTradeStats, err := mairpc.CTrade.GetLatestJingdongTradeStats(c, &pb_trade_order.GetLatestJingdongTradeStatsRequest{
		MemberId:  existedMember.Id,
		ChannelId: channelId,
	})
	if err == nil && latestTradeStats != nil {
		if latestTradeStats.TotalOrderCount == totalOrderCount && latestTradeStats.TotalOrderAmountFen == totalOrderAmountFen {
			handler.setRespToSuccess(&JdCdpUpdateResponse{
				Code: JD_CDP_UPDATE_RESP_CODE_SUC,
			})
			return
		}
		latestTotalAmountFen = int64(latestTradeStats.TotalOrderAmountFen)
	}
	if _, err := mairpc.CTrade.CreateJingdongTradeStats(c, &pb_trade_order.CreateJingdongTradeStatsRequest{
		MemberId:            existedMember.Id,
		ChannelId:           channelId,
		TotalOrderCount:     totalOrderCount,
		TotalOrderAmountFen: totalOrderAmountFen,
	}); err != nil {
		handler.setRespWithError(fmt.Sprintf("Internal Error: %s", err.Error()), nil)
		return
	}
	discountAmountFen = int64(totalOrderAmountFen) - latestTotalAmountFen
	if discountAmountFen != 0 && !isSyncingStockMember(c) {
		// HACK：目前不处理积分发放，因为客户（海信）选择京东线上计算；后续接入客户需注意是否需要我们发放购物积分。
		// HACK：目前只处理成长值模式下成长值发放；后续接入客户需注意消费行为模式无法按当前方案处理。
		c1 := c.Copy()
		core_component.GO(c1, func(ctx context.Context) {
			memberLevelSetting, err := mairpc.CMember.GetMemberLevelSetting(c1)
			if err != nil || memberLevelSetting.LevelRule != model.LEVEL_RULE_GROWTH {
				return
			}
			_, err = mairpc.CMember.UpdateMemberGrowthWithResponse(c1, &pb_member.UpdateMemberGrowthRequest{
				MemberId:   existedMember.Id,
				Growth:     int64(math.Floor(float64(discountAmountFen) / 100)),
				Reason:     "京东订单累计金额变动",
				Identifier: c.Request.Header.Get(jingdong.JD_CDP_RUID),
				ChannelId:  channelId,
				Type:       "system",
				CreatedAt:  util.TransIntTimestamp(int64(timestamp)),
			})
			if err != nil {
				log.Warn(c1, "Failed to UpdateMemberGrowth when updating jd cdp member trade amount", log.Fields{
					"memberId":             existedMember.Id,
					"reqTotalAmountFen":    totalOrderAmountFen,
					"latestTotalAmountFen": latestTotalAmountFen,
					"discountAmountFen":    int64(math.Floor(float64(discountAmountFen) / 100)),
					"err":                  err.Error(),
				})
			}
		})
	}
	handler.setRespToSuccess(&JdCdpUpdateResponse{
		Code: JD_CDP_UPDATE_RESP_CODE_SUC,
	})
}

func getJdCdpMemberByCrmId(c *gin.Context, crmId string) *pb_member.MemberDetailResponse {
	existedMember := GetMemberByUniqueId(c, crmId)
	if existedMember == nil {
		channelMixPhone, _ := pb_client.GetMemberServiceClient().GetChannelMixPhone(GetGrpcContext(c), &pb_member.GetChannelMixPhoneRequest{
			ChannelId: c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
			OpenId:    crmId,
		})
		if channelMixPhone != nil && channelMixPhone.Phone != "" {
			existedMember = GetMemberByUniqueId(c, channelMixPhone.Phone)
			if existedMember != nil {
				_, err := pb_client.GetMemberServiceClient().UpdateMemberSocialUniqueId(GetGrpcContext(c), &pb_member.UpdateMemberSocialUniqueIdRequest{
					MemberId:    existedMember.Id,
					ChannelId:   c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
					NewUniqueId: crmId,
				})
				if err != nil {
					log.Warn(c, "Failed to update jd:member unionId", log.Fields{
						"errMsg":   err.Error(),
						"memberId": existedMember.Id,
						"unionId":  crmId,
					})
				}
			}
		}
	}
	return existedMember
}

func (handler *JdCdpHandler) queryPoints(c *gin.Context) {
	handler.setRespWithError("", &JdCdpQueryPointsResponse{})
	var req JdCdpQueryPointsRequest
	if err := handler.shouldBind(c, &req); err != nil {
		return
	}
	if err := handler.setChannel(c); err != nil {
		return
	}
	if err := handler.validateAppKeyAndToken(req.AppKey); err != nil {
		return
	}
	respMembers, err := mairpc.CMember.GetMembers(c, &pb_member.MemberDetailListRequest{
		UnionIds: req.CrmIds,
	})
	if err != nil {
		handler.setRespWithError("Invalid Accounts", nil)
		return
	}
	resp := JdCdpQueryPointsResponse{}
	unionId2MemberMap := handler.genUnionId2MemberMap(c, respMembers.Members)
	for _, crmId := range req.CrmIds {
		if member, ok := unionId2MemberMap[crmId]; ok {
			resp[crmId] = member.Score
		}
	}
	handler.setRespToSuccess(&resp)
}

func (handler *JdCdpHandler) queryGrades(c *gin.Context) {
	handler.setRespWithError("", &JdCdpQueryGradesResponse{})
	var req JdCdpQueryGradesRequest
	if err := handler.shouldBind(c, &req); err != nil {
		return
	}
	if err := handler.setChannel(c); err != nil {
		return
	}
	if err := handler.validateAppKeyAndToken(req.AppKey); err != nil {
		return
	}
	respMembers, err := mairpc.CMember.GetMembers(c, &pb_member.MemberDetailListRequest{
		UnionIds: req.CrmIds,
	})
	if err != nil {
		handler.setRespWithError("Invalid Accounts", nil)
		return
	}
	resp := JdCdpQueryGradesResponse{}
	unionId2MemberMap := handler.genUnionId2MemberMap(c, respMembers.Members)
	for _, crmId := range req.CrmIds {
		if member, ok := unionId2MemberMap[crmId]; ok {
			resp[crmId] = &JdCdpQueryGradesResponseDetail{
				Level:      handler.handleLevel(member.Level),
				ExpireType: jingdong.JD_CDP_LEVEL_NO_EXPIRE,
			}
		}
	}
	handler.setRespToSuccess(&resp)
}

func (handler *JdCdpHandler) genUnionId2MemberMap(c *gin.Context, members []*pb_member.MemberDetailResponse) map[string]*pb_member.MemberDetailResponse {
	resp := make(map[string]*pb_member.MemberDetailResponse)
	channelId := c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER)
	for _, member := range members {
		socials := append(member.Socials, member.OriginFrom)
		for _, social := range socials {
			if social.Channel == channelId && social.Origin == constant.JD_MEMBER && social.UnionId != "" {
				resp[social.UnionId] = member
			}
		}
	}
	return resp
}

func (handler *JdCdpHandler) handleLevel(level uint32) uint32 {
	if level < JD_CDP_LEVEL_MIN {
		level = JD_CDP_LEVEL_MIN
	} else if level > JD_CDP_LEVEL_MAX {
		level = JD_CDP_LEVEL_MAX
	}
	return level
}

func (handler *JdCdpHandler) queryPointDetails(c *gin.Context) {
	handler.setRespWithError("", &JdCdpQueryPointDetailsResponse{})
	var req JdCdpQueryPointDetailsRequest
	if err := handler.shouldBind(c, &req); err != nil {
		return
	}
	if err := handler.setChannel(c); err != nil {
		return
	}
	if err := handler.validateAppKeyAndToken(req.AppKey); err != nil {
		return
	}
	existedMember := getJdCdpMemberByCrmId(c, req.CrmId)
	if err := handler.validateMember(existedMember); err != nil {
		return
	}
	var changeType *types.StringValue
	if req.ChangeType != "" {
		changeTypeValue, ok := jdCdpPointChangeTypeMap[req.ChangeType]
		if !ok {
			handler.setRespWithError("Invalid ChangeType", nil)
			return
		}
		changeType = &types.StringValue{Value: changeTypeValue}
	}
	listCondition := &pb_request.ListCondition{
		Page:    req.Page,
		PerPage: req.PageSize,
		OrderBy: func() []string {
			if req.Sort == JD_CDP_SORT_ASC {
				return []string{"createdAt"}
			} else {
				return []string{"-createdAt"}
			}
		}(),
	}
	scoreReq := &pb_member.ScoreHistoryListRequest{
		MemberId:      existedMember.Id,
		ListCondition: listCondition,
		CreatedAt:     buildScoreHistoryDateRange(req.StartTime, req.EndTime, handler.dateFormat()),
		ChangeType:    changeType,
		// 该接口有暴露我方积分历史给京东的风险。
		// 据与京东沟通[该接口用于营销云天链后台的“会员运营模块”，不影响营销云会员对接](https://gitlab.maiscrm.com/mai/home/<USER>/issues/59361#note_5368403)。
		// HACK：故不完全按接口文档实现，这里只查询本就属于京东的积分历史。
		Briefs: []string{SYNC_FROM_JD},
	}
	respScores, err := mairpc.CMember.GetScoreHistoryList(c, scoreReq)
	if err != nil {
		handler.setRespWithError("Internal Error", nil)
		return
	}
	resp := JdCdpQueryPointDetailsResponse{
		PageNo:     req.Page,
		PageSize:   req.PageSize,
		TotalCount: respScores.Total,
		TotalPage: func() uint64 {
			return uint64(math.Ceil(float64(respScores.Total) / float64(req.PageSize)))
		}(),
	}
	histories := []*JdCdpQueryPointDetailsResponseItem{}
	for _, history := range respScores.Items {
		var (
			point      int64
			changeType string
		)
		if history.Increment >= 0 {
			point = history.Increment
			changeType = JD_CDP_POINT_ADD
		} else {
			point = int64(math.Abs(float64(history.Increment)))
			changeType = JD_CDP_POINT_SUB
		}
		histories = append(histories, &JdCdpQueryPointDetailsResponseItem{
			Point:        point,
			ChangeType:   changeType,
			PointType:    cast.ToUint32(history.SubBrief),
			CreatedAt:    handler.formatTimeStamp(history.CreatedAt),
			RecordId:     history.Id,
			Description:  history.Brief,
			Platform:     handler.formatRespOrigin(history.Channel.Origin),
			CurrentPoint: history.Balance,
		})
	}
	if len(histories) > 0 {
		resp.PointList = histories
	}
	handler.setRespToSuccess(&resp)
}

func (handler *JdCdpHandler) updatePoint(c *gin.Context) {
	handler.setRespWithError("", &JdCdpUpdatePointResponse{
		Code: JD_CDP_UPDATE_POINT_RESP_CODE_OTHER,
	})
	var req JdCdpUpdatePointRequest
	if err := handler.shouldBind(c, &req); err != nil {
		return
	}
	if err := handler.setChannel(c); err != nil {
		return
	}
	if err := handler.validateAppKeyAndToken(req.AppKey); err != nil {
		return
	}
	existedMember := getJdCdpMemberByCrmId(c, req.CrmId)
	if err := handler.validateMember(existedMember); err != nil {
		return
	}
	score, err := handler.getScoreToUpdate(existedMember.Score, &req)
	if err != nil {
		return
	}
	createdAt, err := handler.transferTimeStrToTimestamp(req.CreatedAt)
	if err != nil {
		handler.setRespWithError(err.Error(), nil)
		return
	}
	metaBytes, _ := jsoniterUsingNum.Marshal(req)
	updateScoreRequest := &pb_member.UpdateScoreRequest{
		Ids:         []string{existedMember.Id},
		Score:       score,
		ChannelId:   c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
		Origin:      constant.JD_MEMBER,
		CreatedAt:   createdAt,
		Brief:       SYNC_FROM_JD,
		SubBrief:    cast.ToString(req.PointType),
		Description: req.Description,
		Identifier:  c.Request.Header.Get(jingdong.JD_CDP_RUID),
		BusinessId:  c.Request.Header.Get(jingdong.JD_CDP_RUID),
		Meta:        string(metaBytes),
	}
	updateScoreResp, err := mairpc.CMember.UpdateScore(c, updateScoreRequest)
	if err != nil || len(updateScoreResp.SucceedItems) == 0 {
		handler.setRespWithError("Internal Error", nil)
		return
	}
	respScore := updateScoreResp.SucceedItems[0]
	handler.setRespToSuccess(&JdCdpUpdatePointResponse{
		Code:     JD_CDP_UPDATE_POINT_RESP_CODE_SUC,
		RecordId: respScore.Id,
	})
}

func (handler *JdCdpHandler) queryGradeDetails(c *gin.Context) {
	handler.setRespWithError("", &JdCdpQueryGradeDetailsResponse{})
	var req JdCdpQueryGradeDetailsRequest
	if err := handler.shouldBind(c, &req); err != nil {
		return
	}
	if err := handler.setChannel(c); err != nil {
		return
	}
	if err := handler.validateAppKeyAndToken(req.AppKey); err != nil {
		return
	}
	existedMember := getJdCdpMemberByCrmId(c, req.CrmId)
	if err := handler.validateMember(existedMember); err != nil {
		return
	}
	listCondition := &pb_request.ListCondition{
		Page:    req.Page,
		PerPage: req.PageSize,
		OrderBy: func() []string {
			if req.Sort == JD_CDP_SORT_ASC {
				return []string{"createdAt"}
			} else {
				return []string{"-createdAt"}
			}
		}(),
	}
	levelReq := &pb_member.ListMemberLevelHistoryRequest{
		MemberId:      existedMember.Id,
		ListCondition: listCondition,
	}
	respLevels, err := mairpc.CMember.ListMemberLevelHistory(c, levelReq)
	if err != nil {
		handler.setRespWithError("Internal Error", nil)
		return
	}
	resp := JdCdpQueryGradeDetailsResponse{
		PageNo:     req.Page,
		PageSize:   req.PageSize,
		TotalCount: respLevels.Total,
		TotalPage: func() uint64 {
			return uint64(math.Ceil(float64(respLevels.Total) / float64(req.PageSize)))
		}(),
	}
	histories := []*JdCdpQueryGradeDetailsResponseGrade{}
	for _, history := range respLevels.Items {
		histories = append(histories, &JdCdpQueryGradeDetailsResponseGrade{
			GradeBeforeChange: history.PreviousLevel,
			Level:             history.Level,
			CreatedAt:         handler.formatTimeStr(history.CreatedAt),
			RecordId:          history.Id,
			Description:       history.Description,
			Platform:          "MAI",
			BeginAt:           handler.formatTimeStr(history.StartedAt),
			ExpireType:        jingdong.JD_CDP_LEVEL_NO_EXPIRE,
			ChangeType:        38, // 京东要求固定赋值 38
		})
	}
	if len(histories) > 0 {
		resp.GradeList = histories
	}
	handler.setRespToSuccess(&resp)
}

func (handler *JdCdpHandler) queryExpirePoint(c *gin.Context) {
	handler.setRespWithError("", &JdCdpQueryExpirePointResponse{})
	var req JdCdpQueryExpirePointRequest
	if err := handler.shouldBind(c, &req); err != nil {
		return
	}
	if err := handler.setChannel(c); err != nil {
		return
	}
	if err := handler.validateAppKeyAndToken(req.AppKey); err != nil {
		return
	}
	resp := JdCdpQueryExpirePointResponse{}
	resetRule, errResetRule := mairpc.CMember.GetScoreResetRule(c)
	if errResetRule == nil && resetRule.ResetType == model.SCORE_RESET_TYPE_NEVER {
		resp.ExpirationType = JD_CDP_POINT_NO_EXPIRE
		handler.setRespToSuccess(&resp)
		return
	}
	existedMember := getJdCdpMemberByCrmId(c, req.CrmId)
	if err := handler.validateMember(existedMember); err != nil {
		return
	}
	wg := sync.WaitGroup{}
	wg.Add(1)
	c1 := c.Copy()
	core_component.GO(c1, func(ctx context.Context) {
		defer wg.Done()
		if respRpc, err := mairpc.CAccount.GetNotificationSetting(c1, &pb_account.GetNotificationSettingRequest{
			Business: model.BUSINESS,
			Rule:     constant.MESSAGE_RULE_SCORE_WILL_EXPIRE,
		}); err == nil && respRpc.NotificationSetting.Text.Enabled && len(respRpc.NotificationSetting.TemplateMessage.Data) > 0 {
			resp.EnableNotification = JD_CDP_EXPIRE_POINT_REMIND
		}
	})
	wg.Add(1)
	var expireAtStr string
	core_component.GO(c1, func(ctx context.Context) {
		defer wg.Done()
		if respRpc, err := mairpc.CMember.GetExpiredScore(c1, existedMember.Id); err == nil {
			resp.ExpirePoint = int64(respRpc.Expired)
			if resp.ExpirePoint > 0 {
				expireAtStr = respRpc.ExpireAt
				resp.ExpireAt = handler.formatTimeStr(expireAtStr)
			}
		}
	})
	wg.Add(1)
	core_component.GO(c1, func(ctx context.Context) {
		defer wg.Done()
		if respRpc, err := mairpc.CMember.GetScoreHistoryList(c1, &pb_member.ScoreHistoryListRequest{
			MemberId: existedMember.Id,
			Briefs:   []string{model.ASSIGNER_EXPIRED_SCORE},
			ListCondition: &pb_request.ListCondition{
				Page:    1,
				PerPage: 1,
			},
		}); err == nil {
			resp.ExpiredPoint = int64(respRpc.Cost)
		}
	})
	wg.Wait()
	if errResetRule == nil {
		if resetRule.ResetType == model.SCORE_RESET_TYPE_FIXED_DAY {
			resp.ExpirationType = JD_CDP_POINT_EXPIRE_ABSOLUTELY
		} else if resetRule.ResetType == model.SCORE_RESET_TYPE_YEAR {
			resp.ExpirationType = JD_CDP_POINT_EXPIRE_ABSOLUTELY
		} else {
			resp.ExpirationType = JD_CDP_POINT_EXPIRE_RELATIVELY
		}
		now := time.Now()
		expireAt := handler.parseTimeStr(time.RFC3339, expireAtStr)
		if handler.canRemindScoreExpiration(&resp) && resetRule.ResetType == model.SCORE_RESET_TYPE_FIXED_DAY {
			remindAt := time.Date(expireAt.Year(), expireAt.Month(), expireAt.Day(), 0, 0, 0, 0, now.Location())
			remindAt = remindAt.AddDate(0, -1, 0)
			resp.RemindAt = handler.formatTime(remindAt)
		} else if handler.canRemindScoreExpiration(&resp) && resetRule.ResetType == model.SCORE_RESET_TYPE_YEAR {
			remindAt := time.Date(expireAt.Year(), expireAt.Month(), 1, 0, 0, 0, 0, now.Location())
			resp.RemindAt = handler.formatTime(remindAt)
		}
	}
	handler.setRespToSuccess(&resp)
}

func (handler *JdCdpHandler) canRemindScoreExpiration(expire *JdCdpQueryExpirePointResponse) bool {
	return expire.ExpirePoint > 0 && expire.EnableNotification == JD_CDP_EXPIRE_POINT_REMIND
}

func (handler *JdCdpHandler) setChannel(c *gin.Context) error {
	channel := GetChannelByChannelId(c, c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER), constant.JD_MEMBER)
	if channel == nil {
		handler.resp.Code = jingdong.JD_CDP_RESP_CODE_STORE_NOT_EXISTED
		return errors.New("Channel Not Found")
	}
	handler.channel = channel
	return nil
}

func (handler *JdCdpHandler) validateAppKeyAndToken(appKey string) error {
	if appKey != handler.channel.AppId {
		handler.resp.Code = jingdong.JD_CDP_RESP_CODE_INVALID_APPKEY
		return errors.New("Invalid AppId")
	}
	return nil
}

func (handler *JdCdpHandler) shouldBind(c *gin.Context, obj any) error {
	if err := c.ShouldBind(&obj); err != nil {
		handler.resp.Code = jingdong.JD_CDP_RESP_CODE_PARAMS_REQUIRED
		handler.resp.Msg = err.Error()
		return errors.New("Invalid Params")
	}
	return nil
}

func (handler *JdCdpHandler) validateMember(member *pb_member.MemberDetailResponse) error {
	if member == nil {
		handler.resp.Code = jingdong.JD_CDP_RESP_CODE_MEMBER_NOT_EXISTED
		return errors.New("Account Not Found")
	}
	return nil
}

func (handler *JdCdpHandler) setRespToSuccess(respData interface{}) {
	handler.resp.Code = jingdong.JD_CDP_RESP_CODE_SUC
	handler.resp.Msg = ""
	handler.resp.Data = respData
}

func (handler *JdCdpHandler) setRespWithError(respMsg string, respData interface{}) {
	handler.resp.Code = jingdong.JD_CDP_RESP_CODE_OTHER
	if respMsg != "" {
		handler.resp.Msg = respMsg
	}
	if respData != nil {
		handler.resp.Data = respData
	}
}

func (handler *JdCdpHandler) setJingdongClient(c *gin.Context) error {
	jingdongClient, jingdongErr := jingdong.NewJingdong(
		handler.channel.AppId,
		handler.channel.AppSecret,
		handler.channel.Token,
		handler.channel.ChannelId,
		handler.channel.IntegrationModes,
	)
	if jingdongErr != nil {
		errMsg := "Failed to initialize jingdong client"
		log.Error(c, errMsg, log.Fields{
			"err": jingdongErr.Error(),
		})
		handler.resp.Code = jingdong.JD_CDP_RESP_CODE_UNAUTHORIZED
		return errors.New(errMsg)
	}
	handler.jingdongClient = jingdongClient
	return nil
}

func (handler *JdCdpHandler) getUpsertMemberRequest(c *gin.Context, plainPhone, openId, unionId, bindTime string, jdMember *jingdong.JdCdpMemberDetail, bindStatus uint32) (*pb_member.UpsertMemberRequest, error) {
	originScene := "others"
	if jdMember.OriginScene != 0 {
		originScene = cast.ToString(jdMember.OriginScene)
	}
	upsertMemberReq := &pb_member.UpsertMemberRequest{
		OriginFrom: &origin.OriginInfo{
			Channel:     c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER),
			Origin:      constant.JD_MEMBER,
			OriginScene: originScene,
			OpenId:      openId,
			UnionId:     unionId,
			ChannelName: c.Request.Header.Get(core_util.CHANNEL_NAME_IN_HEADER),
		},
	}
	if bindStatus == JD_CDP_BIND_STATUS_UNBOUND || bindStatus == JD_CDP_BIND_STATUS_CRM {
		upsertMemberReq.OriginFrom.Subscribed = false
		upsertMemberReq.OriginFrom.UnsubscribeTime = time.Now().Unix()
	} else {
		subscribeTime, err := handler.parseSubscribeTime(bindTime)
		if err != nil {
			log.Warn(c, "Failed to parse subscribeTime when registering", log.Fields{
				"subscribeTime": fmt.Sprintf("%+v", bindTime),
			})
			subscribeTime = time.Now().Unix()
		}
		upsertMemberReq.OriginFrom.Subscribed = true
		upsertMemberReq.OriginFrom.SubscribeTime = subscribeTime
		upsertMemberReq.IsActivated = &types.BoolValue{
			Value: true,
		}
		upsertMemberReq.ActivationSource = constant.GenActivationSource(constant.JD_MEMBER, "others")
	}
	upsertMemberReq.OriginFrom.Nickname = jdMember.RealNameSnakeCase
	if jdMember.RealNameLowerCamelCase != "" {
		upsertMemberReq.OriginFrom.Nickname = jdMember.RealNameLowerCamelCase
	}
	properties := []*pb_member.PropertyInfo{}
	if jdMember != nil {
		if extraMarshaled, err := jsoniterUsingNum.Marshal(jdMember); err == nil {
			if extra := string(extraMarshaled); extra != "" {
				upsertMemberReq.OriginFrom.Extra = extra
			}
		}
		properties = handler.appendMemberProperties(
			properties,
			handler.buildMemberGenderProperty(model.DEFAULT_PROPERTY_GENDER, jdMember.Gender),
			handler.buildMemberBirthdayProperty(model.DEFAULT_PROPERTY_BIRTHDAY, jdMember.Birthday, jdMember.BirthType),
			handler.buildMemberAddressProperty(model.DEFAULT_PROPERTY_ADDRESS, jdMember.Province, jdMember.City, jdMember.District, jdMember.Address),
			handler.buildMemberEmailProperty(c, model.DEFAULT_PROPERTY_EMAIL, jdMember.Email),
		)
	}
	properties = handler.appendMemberProperties(
		properties,
		handler.buildMemberStringTypedProperty(PROPERTY_PHONE, plainPhone),
	)
	if len(properties) > 0 {
		upsertMemberReq.Properties = properties
	}
	return upsertMemberReq, nil
}

func (handler *JdCdpHandler) appendMemberProperties(properties []*pb_member.PropertyInfo, incomingProperties ...*pb_member.PropertyInfo) []*pb_member.PropertyInfo {
	for _, property := range incomingProperties {
		if property == nil {
			continue
		}
		properties = append(properties, property)
	}
	return properties
}

func (handler *JdCdpHandler) buildMemberBirthdayProperty(key string, value string, birthdayType uint32) *pb_member.PropertyInfo {
	if value == "" {
		return nil
	}
	// 非标准格式日期处理。下发的生日日期有可能是非标准格式的，也有可能是未来的年份，如1997-10-2、1997-10-、-10-2、191997-10-02、1997-00-00；
	// 见 [3.3.1.1 注册](https://gitlab.maiscrm.com/-/project/1738/uploads/fa66111702075297dd5e4483c2e7c2c9/%E4%BC%9A%E5%91%98%E8%BF%90%E8%90%A5%E8%81%94%E8%B0%83%E7%B3%BB%E7%BB%9F%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8C-v1.6-0114.pdf)。
	if match, _ := regexp.MatchString(`^((\d{3,6})?-)?(0?[1-9]|1[0-2])-(0?[1-9]|[12]\d|3[01])$`, value); !match {
		return nil
	}
	birthdaySplit := strings.Split(value, "-")
	if len(birthdaySplit) != 2 && len(birthdaySplit) != 3 {
		return nil
	}
	if len(birthdaySplit) == 2 {
		birthdaySplit = append([]string{"1970"}, birthdaySplit...)
	}
	if len(birthdaySplit) == 3 && birthdaySplit[0] == "" {
		birthdaySplit[0] = "1970"
	}
	if birthdayType == JD_CDP_BIRTH_LUNAR_CALENDAR {
		cal := calendar.ByLunar(
			cast.ToInt64(birthdaySplit[0]),
			cast.ToInt64(birthdaySplit[1]),
			cast.ToInt64(birthdaySplit[2]),
			0,
			0,
			0,
			false)
		birthdaySplit[0] = cast.ToString(cal.Solar.GetYear())
		birthdaySplit[1] = cast.ToString(cal.Solar.GetMonth())
		birthdaySplit[2] = cast.ToString(cal.Solar.GetDay())
	}
	for idx, item := range birthdaySplit {
		if idx == 0 {
			continue
		}
		if len(item) == 1 {
			birthdaySplit[idx] = "0" + item
		}
	}
	t, err := time.ParseInLocation(util.DATE_FORMAT, strings.Join(birthdaySplit, "-"), time.Local)
	if err != nil {
		return nil
	}
	property := &pb_member.PropertyInfo{}
	property.PropertyId = key
	property.Value = &pb_member.PropertyInfo_ValueDate{
		ValueDate: &pb_member.PropertyDateValue{
			Value: t.Unix(),
		},
	}
	return property
}

func (handler *JdCdpHandler) buildMemberAddressProperty(key string, province, city, district, detail string) *pb_member.PropertyInfo {
	if province == "" && city == "" && district == "" && detail == "" {
		return nil
	}
	property := &pb_member.PropertyInfo{}
	property.PropertyId = key
	property.Value = &pb_member.PropertyInfo_ValueArray{
		ValueArray: &pb_member.PropertyArrayValue{
			Value: []string{"中国", province, city, district, detail},
		},
	}
	return property
}

func (handler *JdCdpHandler) buildMemberGenderProperty(key string, value *uint32) *pb_member.PropertyInfo {
	if value == nil {
		return nil
	}
	gender, ok := jdCdpGenderMap[*value]
	if !ok {
		return nil
	}
	property := &pb_member.PropertyInfo{}
	property.PropertyId = key
	propertyValue := gender
	property.Value = &pb_member.PropertyInfo_ValueString{
		ValueString: &pb_member.PropertyStringValue{
			Value: propertyValue,
		},
	}
	return property
}

func (handler *JdCdpHandler) buildMemberEmailProperty(c *gin.Context, key, value string) *pb_member.PropertyInfo {
	if value == "" {
		return nil
	}
	value = strings.ToLower(value)
	// 京东会传无效 email 如 l****@163.com、2538、！？？？，导致会员创建失败。故需将校验前置，过滤掉无效 email。
	memberProperties, _ := mairpc.CMember.GetMemberPropertyList(c, &pb_member.MemberPropertyListRequest{
		PropertyIds: []string{model.DEFAULT_PROPERTY_EMAIL},
	})
	if memberProperties != nil {
		for _, property := range memberProperties.Items {
			if property.Rule == "" {
				continue
			}
			if matched, _ := regexp.MatchString(property.Rule, value); !matched {
				value = ""
				break
			}
		}
	}
	if value == "" {
		return nil
	}
	property := &pb_member.PropertyInfo{}
	property.PropertyId = key
	property.Value = &pb_member.PropertyInfo_ValueString{
		ValueString: &pb_member.PropertyStringValue{
			Value: value,
		},
	}
	return property
}

func (handler *JdCdpHandler) buildMemberStringTypedProperty(key, value string) *pb_member.PropertyInfo {
	if value == "" {
		return nil
	}
	property := &pb_member.PropertyInfo{}
	property.PropertyId = key
	property.Value = &pb_member.PropertyInfo_ValueString{
		ValueString: &pb_member.PropertyStringValue{
			Value: value,
		},
	}
	return property
}

func (handler *JdCdpHandler) syncMemberDetails(
	c *gin.Context,
	crmId string,
	openId string,
	jdLevel uint32,
	jdPoint int64,
	updatedMember *pb_member.MemberDetailResponse,
	existedMember *pb_member.MemberDetailResponse,
) error {
	channelId := c.Request.Header.Get(core_util.CHANNEL_ID_IN_HEADER)
	if updatedMember.Level < jdLevel {
		_, err := LevelUpMember(c, &pb_member.LevelUpMemberRequest{
			MemberId:              updatedMember.Id,
			ChannelId:             channelId,
			Level:                 jdLevel,
			UseMaxLevelIfExceeded: true,
		})
		if err != nil {
			log.Warn(c, "Failed to LevelUpMember when syncMemberDetails of jingdong", log.Fields{
				"memberId": fmt.Sprintf("%+v", updatedMember.Id),
				"level":    fmt.Sprintf("%+v", jdLevel),
				"err":      err.Error(),
			})
		}
	} else if updatedMember.Level > jdLevel {
		if err := handler.syncLevelToJingdong(c, crmId, int64(updatedMember.Level), updatedMember); err != nil {
			log.Error(c, "Failed to syncLevelToJingdong when syncMemberDetails", log.Fields{
				"memberId": fmt.Sprintf("%+v", updatedMember.Id),
				"crmId":    fmt.Sprintf("%+v", crmId),
				"level":    fmt.Sprintf("%+v", updatedMember.Level),
				"err":      fmt.Sprintf("%+v", err),
			})
		}
	}
	// 营销云传入的积分为 0 时未必准确，京东建议再通过积分查询接口获取
	// [见《会员运营联调系统操作手册-v1.6-0114》2.3 额外说明](https://gitlab.maiscrm.com/-/project/1738/uploads/fa66111702075297dd5e4483c2e7c2c9/会员运营联调系统操作手册-v1.6-0114.pdf)
	if jdPoint == 0 {
		// 取京东积分总额
		respJdPoint, err := handler.getScoreFromJingdong(c, crmId)
		if err == nil && respJdPoint != nil && respJdPoint.Result.Result != 0 {
			jdPoint = respJdPoint.Result.Result
		}
	}
	maiPointToSync := updatedMember.Score
	if jdPoint == 0 && maiPointToSync == 0 {
		return nil
	}
	if handler.isRegistered(c, openId, existedMember) {
		// 已入会退会后重新入会的情况，以 portal 侧为准，向京东单向同步积分：
		return handler.syncScoreFromPortalToJingdong(c, crmId, maiPointToSync, jdPoint)
	} else {
		// 双向同步积分
		return handler.syncScoreTwoWay(c, crmId, maiPointToSync, jdPoint, updatedMember)
	}
}

func (handler *JdCdpHandler) dateFormat() string { return "2006-01-02 15:04:05" }

func (handler *JdCdpHandler) transferTimeStrToTimestamp(timeStr string) (int64, error) {
	t := handler.parseTimeStr(handler.dateFormat(), timeStr)
	if t.IsZero() {
		return 0, errors.New("Failed to parse time")
	}
	return t.Unix(), nil
}

func (handler *JdCdpHandler) parseTimeStr(layout, value string) time.Time {
	var t time.Time
	if value == "" {
		return t
	}
	if t, err := time.ParseInLocation(layout, value, time.Local); err == nil {
		return t
	}
	return t
}

func (handler *JdCdpHandler) formatTime(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format(handler.dateFormat())
}

func (handler *JdCdpHandler) formatTimeStr(timeStr string) string {
	t := handler.parseTimeStr(time.RFC3339, timeStr)
	return handler.formatTime(t)
}

func (handler *JdCdpHandler) formatTimeStamp(timestamp int64) string {
	t := time.Unix(timestamp, 0)
	return handler.formatTime(t)
}

func (handler *JdCdpHandler) getScoreToUpdate(existedMemberScore int64, req *JdCdpUpdatePointRequest) (int64, error) {
	if req.ChangeType == JD_CDP_POINT_ADD {
		return int64(req.Point), nil
	} else if req.ChangeType == JD_CDP_POINT_SUB {
		toSubScoreAbs := int64(math.Abs(float64(req.Point)))
		if existedMemberScore < toSubScoreAbs {
			handler.setRespWithError("", &JdCdpUpdatePointResponse{
				Code: JD_CDP_UPDATE_POINT_RESP_CODE_NOT_ENOUGH,
			})
			return 0, errors.New("Point Not Enough")
		}
		return -1 * toSubScoreAbs, nil
	} else {
		handler.setRespWithError("Invalid ChangeType", nil)
		return 0, errors.New("Invalid ChangeType")
	}
}

func (handler *JdCdpHandler) formatRespOrigin(origin string) string {
	if util.StrInArray(origin, &[]string{constant.JD, constant.JD_MEMBER, constant.JD_POP}) {
		return "JD"
	}
	if origin == constant.PORTAL {
		return "MAI"
	}
	return origin
}
