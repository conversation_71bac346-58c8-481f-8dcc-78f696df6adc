package controller

import (
	"fmt"
	"mairpc/opapp/jingdong/model"
	"net/http"

	"github.com/gin-gonic/gin"
)

type UpdateChannelRequest struct {
	ChannelId string `json:"channelId"`
	Origin    string `json:"origin"`
	Token     string `json:"token"`
}

// 用于更新京东云鼎 MySQL 数据库 channels 表中数据
func UpdateChannel(c *gin.Context) {
	req := &UpdateChannelRequest{}
	c.BindJ<PERSON>N(req)
	if req.ChannelId == "" {
		c.JSON(http.StatusBadRequest, map[string]interface{}{
			"message": "channelId is required",
		})
		c.Abort()
		return
	}
	selector := "channelId = ?"
	args := []interface{}{req.ChannelId}
	if req.Origin != "" {
		selector = "channelId = ? AND origin = ?"
		args = append(args, req.Origin)
	}
	channel := &model.Channel{}
	if req.Token != "" {
		channel.Token = req.Token
	}
	if err := channel.Update(selector, args); err != nil {
		c.<PERSON>(http.StatusBadRequest, map[string]interface{}{
			"message": fmt.Sprintf("%v", err),
		})
		c.Abort()
		return
	}
	c.JSON(http.StatusOK, "success")
}
