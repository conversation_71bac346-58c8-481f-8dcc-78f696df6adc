package main

import (
	"bytes"
	"context"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"time"

	core_component "mairpc/core/component"
	"mairpc/core/extension"
	core_log "mairpc/core/log"
	"mairpc/opapp/jingdong/conf"
	"mairpc/opapp/jingdong/controller"
	"mairpc/opapp/jingdong/job"
	"mairpc/opapp/jingdong/model"
	"mairpc/openapi/oauth/middleware"

	"github.com/gin-gonic/gin"
	"github.com/robfig/cron"
	flag "github.com/spf13/pflag"
	"github.com/spf13/viper"
	"gopkg.in/tylerb/graceful.v1"
)

var (
	env  = flag.String("env", "local", "the running env, staging or production")
	port = flag.String("port", "9099", "the listen port, default 9099")
	host = flag.String("host", "0.0.0.0", "the http server listen ip")
)

func init() {
	model.CAccessLog.InitTable()
	model.CChannel.InitTable()
	initCleanerCron()
}

func initCleanerCron() {
	c := cron.New()
	c.AddFunc("@midnight", func() {
		time30dBefore := time.Now().Add(-time.Hour * 24 * 30).Format(time.RFC3339)
		where := fmt.Sprintf("createdAt < '%s'", time30dBefore)
		model.CAccessLog.Delete(where)
	})
	c.Start()
}

func main() {
	debug := *env == "local"
	loadConfig()
	core_log.InitLogger(viper.GetString("logger-level"), *env, "opapp-jingdong")
	startServer(debug)
}

func startServer(debug bool) {
	if !debug {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.New()
	r.Use(middleware.AccessLog(), recordAccessLog())
	r.Any("/ping", func(c *gin.Context) {
		c.String(http.StatusOK, "pong")
	})
	// API https://jos.jd.com/commondoc?listId=33
	r.Any("/routerjson", controller.JingdongAPI)
	// SPI https://open.jd.com/home/<USER>/#/doc/common?listId=1015
	r.Any("/v1/member/jingdongv2", controller.JingdongSPI)
	r.Any("/v1/member/jingdong", controller.JingdongSPI)
	r.Any("/v1/channel/update", controller.UpdateChannel)

	extensions := []string{"request"}
	extension.LoadExtensionsByName(extensions, debug)
	startMessageConsumer()
	job.GetOrdersToIssueCoupon()
	graceful.Run(fmt.Sprintf(":%s", *port), 5*time.Second, r)
}

func recordAccessLog() gin.HandlerFunc {
	return func(c *gin.Context) {
		body, _ := ioutil.ReadAll(c.Request.Body)
		c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))
		// call pending handlers
		c.Next()
		core_component.GO(c, func(ctx context.Context) {
			accessLog := &model.AccessLog{
				Method:     c.Request.Method,
				Url:        c.Request.URL.RequestURI(),
				StatusCode: c.Writer.Status(),
				Request:    string(body),
				Response: func() string {
					if c.Writer.Status() >= 500 {
						if responseBody, ok := c.Get("responseBody"); ok {
							return fmt.Sprintf("%+v", responseBody)
						}
					}
					return ""
				}(),
			}
			accessLog.Create()
		})
	}
}

func loadConfig() {
	viper.BindPFlag("env", flag.Lookup("env"))
	viper.BindPFlag("port", flag.Lookup("port"))
	viper.BindPFlag("host", flag.Lookup("host"))
	flag.Parse()

	viper.SetConfigFile(fmt.Sprintf("%s/%s.toml", "./conf", *env))
	err := viper.MergeInConfig()
	if err != nil {
		panic(err)
	}
	err = conf.InitConfig()
	if err != nil {
		panic(err)
	}

	viper.Set("addr", fmt.Sprintf("%s:%s", *host, *port))
	viper.Set("service", "opapp-jingdong")
	log.Printf("Configuration loaded from: %s", viper.ConfigFileUsed())
	log.Println(viper.GetViper())
}

func startMessageConsumer() {
	job.ConsumeAfterSaleMessage()
}
