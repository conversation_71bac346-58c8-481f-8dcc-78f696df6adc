package model

import (
	"mairpc/opapp/jingdong/tools"
	"time"
)

var (
	CChannel = &Channel{}
)

type Channel struct {
	Id              uint      `gorm:"primary_key"`
	AppId           string    `gorm:"column:appId"`
	AppSecret       string    `gorm:"column:appSecret"`
	ChannelId       string    `gorm:"column:channelId"`
	Origin          string    `gorm:"column:origin"`
	AccountId       string    `gorm:"column:accountId"`
	Name            string    `gorm:"column:name"`
	Type            string    `gorm:"column:type"`
	Status          string    `gorm:"column:status"`
	Token           string    `gorm:"column:token"`
	Business        string    `gorm:"column:business"`
	CreatedAt       time.Time `gorm:"column:createdAt"`
	UpdatedAt       time.Time `gorm:"column:updatedAt"`
	EncryptionKey   string    `gorm:"column:encryptionKey"`
	EnableSyncScore bool      `gorm:"column:enableSyncScore"`
}

func (c *Channel) InitTable() {
	db := tools.GetDB()
	if !db.HasTable(&Channel{}) {
		db.CreateTable(&Channel{})
	}
}

func (c *Channel) GetChannelByCondition(condition map[string]interface{}) (*Channel, error) {
	db := tools.GetDB()
	var channel = &Channel{}
	result := db.Where(condition).First(channel)
	if result.Error != nil {
		return nil, result.Error
	}
	return channel, nil
}

func (c *Channel) Update(query string, args []interface{}) error {
	db := tools.GetDB()
	c.UpdatedAt = time.Now()
	if err := db.Model(&Channel{}).Where(query, args...).Updates(c).Error; err != nil {
		return err
	}
	return nil
}
