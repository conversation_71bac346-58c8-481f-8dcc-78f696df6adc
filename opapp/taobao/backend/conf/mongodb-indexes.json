{"//": [{"1.": "支持字段升序或降序（如 `-field2`）;", "2.": "支持 `unique`、`partialFilterExpression`、`expireAfterSeconds` 索引选项;", "3.": {"示例": {"${collectionName}": [{"keys": ["field1", "-field2"], "options": {"unique": true, "partialFilterExpression": {"field1": {"$gt": ""}}}}, {"keys": ["field3"], "options": {"expireAfterSeconds": 3600}}]}}}], "crowd": [{"keys": ["accountId", "isDeleted", "crowdGroupId"]}, {"keys": ["accountId", "name"]}, {"keys": ["taobaoCrowdId"]}], "crowdTag": [{"keys": ["accountId", "tagId"]}], "crowdSnapshot": [{"keys": ["accountId", "status"]}, {"keys": ["accountId", "taobaoSnapshotId"]}], "mutexLock": [{"keys": ["key"], "options": {"unique": true}}], "activeMarketing": [{"keys": ["accountId", "status"]}], "strategyMarketingTemplate": [{"keys": ["accountId", "code"], "options": {"unique": true}}], "strategyMarketing": [{"keys": ["accountId", "code"]}], "sendSmsRecord": [{"keys": ["nodeInstId", "taskInstId"]}, {"keys": ["nodeInstId"]}], "order": [{"keys": ["taobaoUserId", "updatedAt", "_id"]}, {"keys": ["taobaoUserId", "orderUpdatedAt", "_id"]}, {"keys": ["tid"]}], "store": [{"keys": ["taobaoUserId"], "options": {"unique": true}}]}