package model

import (
	"context"
	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"time"
)

const (
	C_ORDER = "order"
)

var (
	COrderV2 = &OrderV2{}
)

type OrderV2 struct {
	Id             bson.ObjectId `bson:"_id"`
	AccountId      bson.ObjectId `bson:"accountId"`
	Tid            string        `bson:"tid"`
	TaobaoUserId   string        `bson:"taobaoUserId"`
	Data           string        `bson:"data"`
	CreatedAt      time.Time     `bson:"createdAt"`
	OrderUpdatedAt time.Time     `bson:"orderUpdatedAt"`
	UpdatedAt      time.Time     `bson:"updatedAt"`
	IsDeleted      bool          `bson:"isDeleted"`
}

func (t *OrderV2) Upsert(ctx context.Context) error {
	selector := bson.M{
		"tid": t.Tid,
	}
	now := time.Now()
	setter := bson.M{
		"orderUpdatedAt": t.OrderUpdatedAt,
		"data":           t.Data,
		"updatedAt":      now,
	}
	setOnInsert := bson.M{
		"accountId":    t.AccountId,
		"tid":          t.Tid,
		"taobaoUserId": t.<PERSON>,
		"createdAt":    now,
		"isDeleted":    false,
	}
	updater := bson.M{
		"$set":         setter,
		"$setOnInsert": setOnInsert,
	}
	_, err := extension.DBRepository.Upsert(ctx, C_ORDER, selector, updater)
	if err != nil {
		return err
	}
	return nil
}

func (t *OrderV2) GetByTid(ctx context.Context, tid string) (*OrderV2, error) {
	order := &OrderV2{}
	selector := bson.M{
		"tid": tid,
	}
	err := extension.DBRepository.FindOne(ctx, C_ORDER, selector, &order)
	if err != nil {
		return nil, err
	}
	return order, nil
}

func (t *OrderV2) GetByPagination(ctx context.Context, pageNo, pageSize int, startTimeStr, endTimeStr, taobaoUserId string, timeType string) ([]OrderV2, error) {
	orders := []OrderV2{}
	selector := bson.M{
		"taobaoUserId": taobaoUserId,
	}
	startTime, _ := time.ParseInLocation("2006-01-02 15:04:05", startTimeStr, time.Local)
	endTime, _ := time.ParseInLocation("2006-01-02 15:04:05", endTimeStr, time.Local)
	var orderBys []string
	if timeType == "updatedAt" {
		orderBys = []string{"-updatedAt", "-_id"}
		selector["updatedAt"] = bson.M{
			"$gte": startTime,
			"$lt":  endTime,
		}
	} else {
		orderBys = []string{"-orderUpdatedAt", "-_id"}
		selector["orderUpdatedAt"] = bson.M{
			"$gte": startTime,
			"$lt":  endTime,
		}
	}
	Common.GetAllByPaginationWithoutCount(ctx, selector, uint32(pageNo), uint32(pageSize), orderBys, C_ORDER, &orders)
	return orders, nil
}
