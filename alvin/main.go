package main

import (
	"context"
	"fmt"
	"log"
	"mairpc/core/component/taobao"
	"mairpc/proto/account"
	"os"
	"strings"
	"time"

	"mairpc/service/member/model"
	share_sms "mairpc/service/share/component/sms"
	"mairpc/service/share/util"
)

func main() {
	fmt.Println("hello world")
	testSms()
	//testScoreSetting()
}

func testScoreSetting() {
	setting := &model.ScoreResetNotificationSetting{}
	setting.Type = "daily"
	setting.Day = 0

	log.Println(setting.CanSend(time.Now()))

	from, to := setting.GetTimeRange(time.Now())
	fmt.Println(from, to)

	vto := util.GetEndTimeOfMonth(from)
	vfrom := to.AddDate(0, 0, 0)
	// 如果按照通知设置将来 N 天内不包含清零时刻，那么返回 false，减少不必要的计算
	if !util.TimeInPeriod(vto, from, to) {
		log.Println("not in period")
	}
	fmt.Println(vfrom, vto)
}

func testSms() {
	// set env ALIYUN_SMS_ACCESS_KEY_ID
	os.Setenv("ALIYUN_SMS_ACCESS_KEY_ID", "LTAI5t63dQqmpJ79uD8t4CwZ")
	os.Setenv("ALIYUN_SMS_ACCESS_KEY_SECRET", "******************************")
	sender := &share_sms.AliyunSmsSender{}
	resp, err := sender.Send(context.Background(), &account.SendSmsRequest{
		Phone:       "***********",
		Text:        "您的验证码为 24324，请不要告知他人",
		SmsTopic:    "群脉MAI",
		MessageType: account.SendSmsRequest_VERIFICATION_CODE,
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(resp)
}

func getList() {
	taobaoClient := taobao.NewTaobao("********", "b0e8d805c831fcfac4b2fa3468a586df")

	listReq := &taobao.GetTradesSimpleSoldIncrementRequest{
		Fields:        "tid", // 只获取一个 tid，通过订单详情接口获取订单
		StartModified: "2025-06-14 17:48:00",
		EndModified:   "2025-06-14 18:48:00",
		PageSize:      20,
		PageNo:        680,
		UseHasNext:    true,
		Session:       "6201a19bae648053211154e082ceg2a160f2d8b0c94b2032201250696597",
	}

	resp1, err1 := taobaoClient.Trade.GetTradesSimpleSoldIncrement(context.Background(), listReq)
	if err1 != nil {
		fmt.Println(err1)
		return
	}
	fmt.Println(resp1)
}

func getDetail() {
	taobaoClient := taobao.NewTaobao("********", "b0e8d805c831fcfac4b2fa3468a586df")

	fields := []string{
		"pic_path",
		"payment",
		"snapshot_url",
		"seller_rate",
		"post_fee",
		"receiver_state",
		"consign_time",
		"available_confirm_fee",
		"received_payment",
		"timeout_action_time",
		"promotion_details",
		"tid",
		"num",
		"num_iid",
		"status",
		"title",
		"type",
		"price",
		"discount_fee",
		"has_post_fee",
		"total_fee",
		"created",
		"pay_time",
		"modified",
		"end_time",
		"buyer_message",
		"buyer_memo",
		"seller_memo",
		"step_trade_status",
		"step_paid_fee",
		"buyer_open_uid",
		"shipping_type",
		"adjust_fee",
		"trade_from",
		"can_rate",
		"service_orders",
		"buyer_rate",
		"seller_can_rate",
		"receiver_city",
		"orders",
		"seller_nick",
		"expandcard_info",
		"expand_card_basic_price",
		"expand_card_expand_price",
		"expand_card_basic_price_used",
		"expand_card_expand_price_used",
		"is_part_consign",
		"rx_audit_status",
		"coupon_fee",
		"ouid",
		"seller_flag",
	}

	resp, err := taobaoClient.Trade.GetTradeSimple(context.Background(), &taobao.GetTradeSimpleRequest{
		Tid:     *******************,
		Session: "6201a19bae648053211154e082ceg2a160f2d8b0c94b2032201250696597",
		Fields:  strings.Join(fields, ","),
	})
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(resp)
}
