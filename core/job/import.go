package job

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"net/url"
	"os"
	"strings"
	"sync/atomic"

	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/validators"
	pb_account "mairpc/proto/account"
	"mairpc/proto/client"
	"mairpc/proto/common/request"
	async_cache "mairpc/service/share/async_cache"
	"mairpc/service/share/component/oss"
	job_util "mairpc/service/share/jobs"
	share_util "mairpc/service/share/util"

	"github.com/spf13/cast"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

type ImportJobRunner interface {
	JobName() string
	RunE(ctx context.Context) (failedUrl string, err error)
	FailedHeadersToExport(ctx context.Context) []string
}

type ImportJob struct {
	ExportJob
	Options      *ImportCliArg
	failedCount  int64
	succeedCount int64
	runner       ImportJob<PERSON>unner
}

type ImportCliArg struct {
	FileUrl    string `json:"fileUrl" valid:"required"`
	Code       string `json:"code,omitempty"`
	DisableJob bool   `json:"disableJob,omitempty"`
}

func (job *ImportJob) RegisterToParent(runner ImportJobRunner, parentCmd *cobra.Command) {
	job.runner = runner
	parentCmd.AddCommand(&cobra.Command{
		Use:  job.runner.JobName(),
		RunE: job.genCobraCommandRunE(),
	})
}

func (job *ImportJob) genCobraCommandRunE() func(*cobra.Command, []string) error {
	return func(cmd *cobra.Command, args []string) error {
		if err := job.registerJobArgs(share_util.GetAccountIdFromArgs(args), args); err != nil {
			return err
		}
		cliCtx := share_util.GetContextInJob(args)
		return job.execute(cliCtx)
	}
}

func (job *ImportJob) registerJobArgs(accountId string, args []string) error {
	job.Options = &ImportCliArg{}
	core_util.CopyByJson(share_util.GetArgs(args), job.Options)
	if err := validators.ValidateRequest(job.Options); err != nil {
		return err
	}
	job.JobId = core_util.GetSreadminJobName()
	job.RequestId = share_util.GetReqIdFromArgs(args)
	job.AccountId = accountId
	job.OriginalArgs = args
	return nil
}

func (job *ImportJob) execute(ctx context.Context) error {
	if !job.Options.DisableJob {
		if err := job_util.BeginToImport(ctx, job.JobId); err != nil {
			return err
		}
	}
	failedUrl, err := job.runner.RunE(ctx)
	if job.Options.Code != "" {
		data, _ := json.Marshal(map[string]string{
			"failedUrl":    failedUrl,
			"succeedCount": cast.ToString(job.succeedCount),
			"failedCount":  cast.ToString(job.failedCount),
		})
		updateReq := &pb_account.UpdateAsyncCacheDataRequest{
			Code:   job.Options.Code,
			Status: "completed",
			Data:   string(data),
		}
		if err != nil {
			updateReq.Status = "failed"
			updateReq.FailedReason = err.Error()
		}
		async_cache.UpdateAsyncCacheData(ctx, updateReq)
	}
	if !job.Options.DisableJob {
		if err != nil {
			job_util.ImportFailed(ctx, job.JobId, "", err.Error())
			return err
		}
		return job_util.ImportSucceed(
			ctx,
			job.JobId,
			failedUrl,
			job.sourceUrl(ctx),
			"",
			int(job.succeedCount),
			int(job.failedCount),
		)
	} else if err != nil {
		return err
	}
	return nil
}

func (job *ImportJob) sourceUrl(ctx context.Context) string {
	if job.Options.FileUrl == "" {
		return ""
	}
	ossServiceSetting, _ := client.GetAccountServiceClient().GetOssServiceSetting(ctx, &request.EmptyRequest{})
	if strings.HasPrefix(job.Options.FileUrl, ossServiceSetting.Cdn.Domain) ||
		strings.HasPrefix(job.Options.FileUrl, viper.GetString("oss-proxy-cdn-domain")) ||
		strings.HasPrefix(job.Options.FileUrl, viper.GetString("oss-endpoint-external")) {
		return strings.ReplaceAll(job.Options.FileUrl, "%2F", "/")
	}
	objectKey := job.Options.FileUrl
	if strings.HasPrefix(job.Options.FileUrl, "https://") || strings.HasPrefix(job.Options.FileUrl, "http://") {
		urlObj, err := url.ParseRequestURI(job.Options.FileUrl)
		if err != nil {
			log.Warn(ctx, "Failed to parse url when importing", log.Fields{"err": err.Error()})
		} else {
			objectKey = strings.TrimLeft(urlObj.Path, "/")
		}
	}
	sourceUrl, err := oss.OSSClient(ctx).SignUrl(ctx, objectKey, oss.HTTPGet, "", oss.EXPIRATION_SECONDS, nil)
	if err != nil {
		log.Warn(ctx, "Failed to get sourceUrl when importing", log.Fields{"err": err.Error()})
	}
	return strings.ReplaceAll(sourceUrl, "%2F", "/")
}

func (job *ImportJob) NewCSVWriter(ctx context.Context, f *os.File) error {
	job.csvWriter = csv.NewWriter(f)
	if err := job.csvWriter.Write(job.runner.FailedHeadersToExport(ctx)); err != nil {
		return err
	}
	return nil
}

func (job *ImportJob) NewCSVWriterWithLines(ctx context.Context, f *os.File, lines [][]string) error {
	job.csvWriter = csv.NewWriter(f)
	if err := job.csvWriter.WriteAll(lines); err != nil {
		return err
	}
	return nil
}

func (job *ImportJob) AddSucceedCountAtomically(delta int64) (new int64) {
	return atomic.AddInt64(&job.succeedCount, delta)
}

func (job *ImportJob) AddFailedCountAtomically(delta int64) (new int64) {
	return atomic.AddInt64(&job.failedCount, delta)
}

func (job *ImportJob) GetImportSucceedCount() int64 {
	return job.succeedCount
}

func (job *ImportJob) GetImportFailedCount() int64 {
	return job.failedCount
}
