package trade

import (
	"encoding/json"
	"errors"
	"fmt"
	"mairpc/core/extension"
	"mairpc/core/log"
	"net/url"
	"strings"

	"github.com/tidwall/gjson"
	"golang.org/x/net/context"
)

var (
	CHANNEL_SERVICE_NAME       = "weconnect"
	WECONNECT_REQUEST_TEMPLATE = "/accounts/%s/proxy/%s"

	testHeaders = map[string]string{
		"x-access-token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************.f0z5nHK-kkiOBEDYI3nYMtZLg9GFuXIKBpma8DwyZlU",
		"Content-Type":   "application/json",
	}
)

type wechatTradeClient struct {
	channel string
	isTest  bool
}

type WechatTradeClient struct {
	Basic            WechatTradeBasicRequester
	Goods            WechatTradeGoodsRequester
	Refund           WechatTradeRefundRequester
	Order            WechatTradeOrderRequester
	Shipping         WechatShippingRequester
	FreightInsurance FreightInsuranceRequester
	Channel          string
}

type wechatResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

func NewWechatTradeClient(channel string, isTest bool) *WechatTradeClient {
	client := wechatTradeClient{
		channel: channel,
		isTest:  isTest,
	}
	return &WechatTradeClient{
		Basic:            &wechatTradeBasicClient{client},
		Goods:            &wechatTradeGoodsClient{client},
		Refund:           &wechatTradeRefundClient{client},
		Order:            &wechatTradeOrderClient{client},
		Shipping:         &wechatShippingClient{client},
		FreightInsurance: &wechatFreightInsuranceClient{client},
		Channel:          channel,
	}
}

func (client *wechatTradeClient) post(ctx context.Context, data interface{}, path string, result interface{}) error {
	reqUrl := fmt.Sprintf(WECONNECT_REQUEST_TEMPLATE, client.channel, strings.TrimLeft(path, "/"))
	service := CHANNEL_SERVICE_NAME
	var headers map[string]string

	var (
		respBody []byte
		err      error
	)
	if client.isTest {
		reqUrl = "https://business-api.maiscrm.com/v2/weconnect/accounts/6064372fa995f7ec1d094ed4/proxy/" + strings.TrimLeft(path, "/")
		service = ""
		headers = testHeaders
	}

	if path == "/shop/img/upload" {
		// 图片上传需要使用 formdata
		reqUrl = fmt.Sprintf("/accounts/%s/miniprogram/shop/media", client.channel)
		if client.isTest {
			reqUrl = "https://business-api.maiscrm.com/v2/weconnect/accounts/6064372fa995f7ec1d094ed4/miniprogram/shop/media"
			service = ""
			headers = testHeaders
		}
		var (
			bytes      []byte
			requestMap map[string]interface{}
		)
		bytes, err = json.Marshal(data)
		if err != nil {
			return err
		}
		err = json.Unmarshal(bytes, &requestMap)
		if err != nil {
			return err
		}
		args := &url.Values{}
		for k, v := range requestMap {
			args.Add(k, fmt.Sprintf("%v", v))
		}
		respBody, _, err = extension.RequestClient.PostFormData(ctx, service, reqUrl, args.Encode(), &headers)
	} else {
		respBody, _, err = extension.RequestClient.PostJson(log.SwitchOnResponseBodyLog(ctx), service, reqUrl, data, &headers)
	}

	if err != nil {
		return err
	}

	resp := wechatResponse{
		Data: result,
	}
	err = json.Unmarshal(respBody, &resp)
	if err != nil {
		return err
	}
	if !gjson.Get(string(respBody), "data").Exists() && resp.Code != 200 {
		return errors.New(resp.Message)
	}
	return nil
}

func (resp WechatTradeResponse) getError() error {
	if resp.ErrorCode != 0 {
		return errors.New(resp.ErrorMessage)
	}
	return nil
}
