package trade

const (
	// 订单类型：0，普通单，1，二级商户单
	FUND_TYPE_NORMAL = 0
	FUND_TYPE_LEVEL2 = 1

	// 数据类型
	DATA_ONLINE = 0 // 线上数据
	DATA_DRAFT  = 1 // 草稿数据
)

type UploadImageRequest struct {
	ImgUrl string `json:"imgUrl"`
}

type UploadCategoryCertificateRequest struct {
	AuditReq AuditReq `json:"audit_req"`
}

type AuditReq struct {
	Licenses       []string     `json:"license"`
	CategoryInfo   CategoryInfo `json:"category_info"`
	SceneGroupList []int        `json:"scene_group_list"`
}

type CategoryInfo struct {
	Level1       int64    `json:"level1"`
	Level2       int64    `json:"level2"`
	Level3       int64    `json:"level3"`
	Certificates []string `json:"certificate"`
}

type CreateOrderRequest struct {
	CreateTime              string         `json:"create_time"`
	Type                    int64          `json:"type"`
	OutOrderId              string         `json:"out_order_id"`
	Openid                  string         `json:"openid"`
	Path                    string         `json:"path"`
	OutUserID               string         `json:"out_user_id"`
	OrderDetail             OrderDetail    `json:"order_detail"`
	DeliveryDetail          DeliveryDetail `json:"delivery_detail"`
	AddressInfo             AddressInfo    `json:"address_info"`
	FundType                int64          `json:"fund_type"`   // 订单类型：0，普通单，1，二级商户单
	ExpireTime              int64          `json:"expire_time"` // 订单超时时间戳，15 分钟到 1 天
	AftersaleDuration       int64          `json:"aftersale_duration,omitempty"`
	TraceId                 string         `json:"trace_id"`
	DefaultReceivingAddress AddressInfo    `json:"default_receiving_address,omitempty"`
	Stringify64BitsNumber   bool           `json:"stringify_64bits_number,omitempty"` // 设置生成 order_id 以字符串返回
}

type AddressInfo struct {
	ReceiverName    string `json:"receiver_name"`
	DetailedAddress string `json:"detailed_address"`
	TelNumber       string `json:"tel_number"`
	Country         string `json:"country"`
	Province        string `json:"province"`
	City            string `json:"city"`
	Town            string `json:"town"`
}

type UpdateOrderAddressRequest struct {
	OutOrderID  string      `json:"out_order_id"`
	Openid      string      `json:"openid"`
	AddressInfo AddressInfo `json:"address_info"`
}

type UpdateOrderPriceRequest struct {
	OutOrderId   string        `json:"out_order_id"`
	Openid       string        `json:"openid"`
	ProductInfos []ProductInfo `json:"product_infos"`
	PriceInfo    PriceInfo     `json:"price_info"`
}

type GetOrderListRequest struct {
	Page            int64  `json:"page"`
	PageSize        int64  `json:"page_size"`
	SortOrder       int64  `json:"sort_order"`
	StartCreateTime string `json:"start_create_time"`
	EndCreateTime   string `json:"end_create_time"`
}

type ShipOrderRequest struct {
	OrderID           int64          `json:"order_id"`
	OutOrderID        string         `json:"out_order_id"`
	Openid            string         `json:"openid"`
	FinishAllDelivery int64          `json:"finish_all_delivery"`
	DeliveryList      []DeliveryList `json:"delivery_list"`
	ShipDoneTime      string         `json:"ship_done_time"`
}

type PushOrderPayInfoRequest struct {
	OrderId       int64  `json:"orderId"`
	OutOrderID    string `json:"out_order_id"`
	Openid        string `json:"openid"`
	ActionType    int64  `json:"action_type"`
	ActionRemark  string `json:"action_remark"`
	TransactionID string `json:"transaction_id"`
	PayTime       string `json:"pay_time"`
}

type OperateOrderRequest struct {
	OrderID    int64  `json:"order_id"`
	OutOrderID string `json:"out_order_id"`
	Openid     string `json:"openid"`
}

type OperateGoodsRequest struct {
	ProductID    int64  `json:"product_id"`
	OutProductID string `json:"out_product_id"`
	//数据类型
	NeedEditSpu int64 `json:"need_edit_spu,omitempty"`
}

type GetGoodsListRequest struct {
	Status          int64  `json:"status"`
	StartCreateTime string `json:"start_create_time,omitempty"`
	EndCreateTime   string `json:"end_create_time,omitempty"`
	StartUpdateTime string `json:"start_update_time,omitempty"`
	EndUpdateTime   string `json:"end_update_time,omitempty"`
	Page            int64  `json:"page"`
	PageSize        int64  `json:"page_size"`
	//数据类型
	NeedEditSpu int64 `json:"need_edit_spu"`
}

type CreateRefundRequest struct {
	OperateOrderRequest
	OutAftersaleId   string      `json:"out_aftersale_id"`
	Type             int64       `json:"type"`
	ProductInfo      ProductInfo `json:"product_info"`
	RefundReason     string      `json:"refund_reason"`
	RefundReasonType int64       `json:"refund_reason_type"` // see: https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/business-capabilities/ministore/minishopopencomponent2/API/aftersale/data_structure.html#enum-AfterSalesReasonType
	Orderamt         int64       `json:"orderamt"`
	MediaList        []Media     `json:"media_list"`
}

type Media struct {
	Type     int64  `json:"type"`
	Url      string `json:"url"`
	ThumbUrl string `json:"thumb_url,omitempty"`
}

type UpdateRefundRequest struct {
	OperateRefundRequest
	MediaList        []Media `json:"media_list"`
	Orderamt         int64   `json:"orderamt"`
	Type             int64   `json:"type"`
	RefundReason     string  `json:"refund_reason"`
	RefundReasonType int64   `json:"refund_reason_type"`
}

type OperateRefundRequest struct {
	OutAftersaleId string `json:"out_aftersale_id,omitempty"`
	AftersaleId    int64  `json:"aftersale_id,omitempty"`
	Openid         string `json:"openid,omitempty"`
}

type UploadDeliveryRequest struct {
	OperateRefundRequest
	DeliveryInfo
	WaybillID string `json:"waybill_id"`
}

type ConfirmReturnRequest struct {
	OperateRefundRequest
	AddressInfo AddressInfo `json:"address_info"`
}

type UploadRefundCertificatesRequest struct {
	OperateRefundRequest
	RefundDesc   string   `json:"refund_desc"`
	Certificates []string `json:"certificates"`
}

type GetRefundListRequest struct {
	OperateOrderRequest
	Status          int64 `json:"status"` // see: https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/business-capabilities/ministore/minishopopencomponent2/API/aftersale/data_structure.html#enum-AfterSalesState
	BeginCreateTime int64 `json:"begin_create_time"`
	EndCreateTime   int64 `json:"end_create_time"`
	Offset          int64 `json:"offset"`
	Limit           int64 `json:"limit"`
}

// 小程序发货信息管理服务

type LogisticsType int

const (
	LOGISTICS_EXPRESS LogisticsType = iota + 1 // 快递
	LOGISTICS_CITY                             // 同城
	LOGISTICS_NONE                             // 无需配送
	LOGISTICS_SELF                             // 客户自提
)

type DeliveryMode int

const (
	DELIVERY_MODE_UNIFIED_DELIVERY DeliveryMode = iota + 1 // 统一发货
	DELIVERY_MODE_SPLIT_DELIVERY                           // 分拆发货
)

type OrderNumberType int

const (
	ORDER_NUMBER_TYPE_MAI    OrderNumberType = iota + 1 // 使用群脉生成的订单号
	ORDER_NUMBER_TYPE_WECHAT                            // 使用微信支付单号
)

type OrderKey struct {
	OrderNumberType OrderNumberType `json:"order_number_type"` // 订单单号类型
	TransactionId   string          `json:"transaction_id"`    // 微信支付单号
	Mchid           string          `json:"mchid"`             // 商户号
	OutTradeNo      string          `json:"out_trade_no"`      // 群脉生成的订单号
}

type Payer struct {
	OpenId string `json:"openid"`
}

type ShipInfo struct {
	TrackingNo     string  `json:"tracking_no,omitempty"`     // 物流单号
	ExpressCompany string  `json:"express_company,omitempty"` // 物流公司编码
	ItemDesc       string  `json:"item_desc"`                 // 商品信息，限 120 个字
	Contact        Contact `json:"contact"`                   // 顺丰快递时必填，填任意一个联系方式
	GoodsDesc      string  `json:"goods_desc,omitempty"`      // 商品描述，仅出现在微信响应里
	UploadTime     int64   `json:"upload_time,omitempty"`     // 物流信息上传时间，仅出现在微信响应里
}

type Contact struct {
	ConsignorContact string `json:"consignor_contact"` // 寄件人联系方式，脱敏手机号，最后四位不能脱敏
	ReceiverContact  string `json:"receiver_contact"`  // 收件人联系方式，脱敏手机号，最后四位不能脱敏
}

type UploadShippingInfoRequest struct {
	LogisticsType  LogisticsType `json:"logistics_type"`          // 物流模式
	DeliveryMode   DeliveryMode  `json:"delivery_mode"`           // 发货模式
	IsAllDelivered bool          `json:"is_all_delivered"`        // 是否已全部发货
	UploadTime     string        `json:"upload_time"`             // 上传时间
	Payer          Payer         `json:"payer"`                   // 支付者
	OrderKey       OrderKey      `json:"order_key"`               // 订单
	ShippingList   []ShipInfo    `json:"shipping_list,omitempty"` // 物流信息列表
}

type SubOrder struct {
	OrderKey       OrderKey      `json:"order_key"`        // 子单详情
	LogisticsType  LogisticsType `json:"logistics_type"`   // 物流模式
	DeliveryMode   DeliveryMode  `json:"delivery_mode"`    // 发货模式
	IsAllDelivered bool          `json:"is_all_delivered"` // 是否已全部发货
	ShippingList   []ShipInfo    `json:"shipping_list"`    // 子单物流信息列表
}

type UploadCombinedShippingInfoRequest struct {
	OrderKey   OrderKey   `json:"order_key"`   // 主单
	SubOrders  []SubOrder `json:"sub_orders"`  // 子单物流详情
	UploadTime string     `json:"upload_time"` // 上传时间
	Payer      Payer      `json:"payer"`       // 支付者
}

type OrderState int

const (
	ORDER_STATE_PAID      OrderState = iota + 1 // 待发货
	ORDER_STATE_SHIPPED                         // 已发货
	ORDER_STATE_RECEIVED                        // 确认收货
	ORDER_STATE_COMPLETED                       // 交易完成
	ORDER_STATE_REFUNDED                        // 已退款
)

type GetOrderShippingStatusRequest struct {
	TransactionId   string `json:"transaction_id"`    // 原支付交易对应的微信订单号
	MerchantId      string `json:"merchant_id"`       // 支付下单商户的商户号
	SubMerchantId   string `json:"sub_merchant_id"`   // 二级商户号
	MerchantTradeNo string `json:"merchant_trade_no"` // 群脉生成的订单号
}

type GetShippingOrderListRequest struct {
	PayTimeRange struct {
		BeginTime int64 `json:"begin_time"` // 起始时间
		EndTime   int64 `json:"end_time"`   // 结束时间
	} `json:"pay_time_range"` // 支付时间范围
	OrderState OrderState `json:"order_state"` // 订单状态
	OpenId     string     `json:"openid"`      // 支付者 openId
	LastIndex  string     `json:"last_index"`  // 翻页用
	PageSize   int64      `json:"page_size"`   // 分页大小
}

type RemindReceiveRequest struct {
	TransactionId   string `json:"transaction_id"`    // 微信支付单号
	MerchantId      string `json:"merchant_id"`       // 商户号
	SubMerchantId   string `json:"sub_merchant_id"`   // 二级商户号
	MerchantTradeNo string `json:"merchant_trade_no"` // 群脉生成的订单号
	ReceivedTime    int64  `json:"received_time"`     // 快递签收时间
}

type FreightInsuranceAddress struct {
	Province string `json:"province"`
	City     string `json:"city"`
	District string `json:"county,omitempty"`
	Detail   string `json:"address"`
	Name     string `json:"name,omitempty"`
	Phone    string `json:"mobile,omitempty"`
	Country  string `json:"country,omitempty"`
	Area     string `json:"area,omitempty"`
}

type FreightInsuranceProduct struct {
	Name     string `json:"name"`
	ImageUrl string `json:"url"`
}

type FreightInsuranceOrderInfo struct {
	OrderPathInMiniProgram string                    `json:"order_path"`
	Products               []FreightInsuranceProduct `json:"goods_list"`
}

type CreateFreightInsuranceOrderRequest struct {
	OpenId          string                    `json:"openid"`
	TradeNo         string                    `json:"order_no"`
	PaidAt          int64                     `json:"pay_time"`
	PayAmount       int64                     `json:"pay_amount"`
	WaybillId       string                    `json:"delivery_no"`
	SenderAddress   FreightInsuranceAddress   `json:"delivery_place"`
	ReceiverAddress FreightInsuranceAddress   `json:"receipt_place"`
	OrderInfo       FreightInsuranceOrderInfo `json:"product_info"`
}

type ClaimFreightInsuranceRequest struct {
	OpenId                string `json:"openid"`
	TradeNo               string `json:"order_no"`
	RefundWaybillId       string `json:"refund_delivery_no"`
	RefundDeliveryCompany string `json:"refund_company"`
}

type CreateFreightInsuranceReturnIdRequest struct {
	OrderRefundId          string                    `json:"shop_order_id"`
	OpenId                 string                    `json:"openid"`
	TradeNo                string                    `json:"wx_pay_id"`
	OrderPathInMiniProgram string                    `json:"order_path"`
	RefundAddress          FreightInsuranceAddress   `json:"biz_addr"`
	ReceiverAddress        FreightInsuranceAddress   `json:"user_addr"`
	Products               []FreightInsuranceProduct `json:"goods_list"`
}
