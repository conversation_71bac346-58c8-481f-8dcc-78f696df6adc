package trade

type WechatTradeResponse struct {
	ErrorCode    int    `json:"errorCode"`
	ErrorMessage string `json:"errorMessage"`
}

type UploadImageResponse struct {
	WechatTradeResponse
	DownloadUrl string `json:"downloadUrl"`
}
type UploadCategoryCertificateResponse struct {
	WechatTradeResponse
	AuditId string `json:"audit_id"`
}

type GetCategoryListResponse struct {
	WechatTradeResponse
	ThirdCatList []ThirdCatList `json:"third_cat_list"`
}

type ThirdCatList struct {
	ThirdCatId               int64  `json:"third_cat_id"`
	ThirdCatName             string `json:"third_cat_name"`
	FirstCatId               int64  `json:"first_cat_id"`
	FirstCatName             string `json:"first_cat_name"`
	SecondCatId              int64  `json:"second_cat_id"`
	SecondCatName            string `json:"second_cat_name"`
	Qualification            string `json:"qualification"`
	QualificationType        int64  `json:"qualification_type"`
	ProductQualification     string `json:"product_qualification"`
	ProductQualificationType int64  `json:"product_qualification_type"`
}

type CheckSceneResponse struct {
	WechatTradeResponse
	IsMatched int64 `json:"is_matched"`
}

type CreatePaymentParamsResponse struct {
	WechatTradeResponse
	PaymentParams PaymentParams `json:"payment_params"`
}

type PaymentParams struct {
	TimeStamp string `json:"timeStamp"`
	NonceStr  string `json:"nonceStr"`
	Package   string `json:"package"`
	PaySign   string `json:"paySign"`
	SignType  string `json:"signType"`
}

type GetOrderListResponse struct {
	WechatTradeResponse
	TotalNum int64 `json:"total_num"`
	Data     Order `json:"data"`
}

type GetDeliveryCompanyListResponse struct {
	WechatTradeResponse
	CompanyList []DeliveryInfo `json:"company_list"`
}

type DeliveryInfo struct {
	DeliveryId   string `json:"delivery_id,omitempty"`
	DeliveryName string `json:"delivery_name,omitempty"`
}

type GetOrderResponse struct {
	WechatTradeResponse
	Order Order `json:"order"`
}

type CreateOrderResponse struct {
	WechatTradeResponse
	Data CreateOrderData `json:"data"`
}

type OperateGoodsResponse struct {
	WechatTradeResponse
	Data ResponseGoods `json:"data"`
}

type ResponseGoods struct {
	ProductID    int64         `json:"product_id"`
	OutProductID string        `json:"out_product_id"`
	CreateTime   string        `json:"create_time,omitempty"`
	UpdateTime   string        `json:"update_time,omitempty"`
	Skus         []ResponseSku `json:"skus"`
}

type ResponseSku struct {
	SkuID    int64  `json:"sku_id"`
	OutSkuID string `json:"out_sku_id"`
}

type GetGoodsResponse struct {
	WechatTradeResponse
	Spu GoodsDetail `json:"spu"`
}

type GetGoodsListResponse struct {
	WechatTradeResponse
	TotalNum int64         `json:"total_num"`
	Spus     []GoodsDetail `json:"spus"`
}

type CreateRefundResponse struct {
	WechatTradeResponse
	AftersaleId int64 `json:"aftersale_id"`
}

type GetRefundListResponse struct {
	WechatTradeResponse
	AfterSalesOrders []string `json:"after_sales_orders"`
	HasMore          bool     `json:"has_more"`
}

type GetRefundResponse struct {
	WechatTradeResponse
	AftersaleInfo AftersaleInfo `json:"after_sales_order"`
}

type AftersaleInfo struct {
	OrderID              int64                `json:"order_id"`
	OutOrderID           string               `json:"out_order_id"`
	OutAftersaleId       string               `json:"out_aftersale_id"`
	AftersaleId          int64                `json:"aftersale_id"`
	Openid               string               `json:"openid"`
	ProductInfo          AftersaleProductInfo `json:"product_info"`
	Type                 int64                `json:"type"`
	ReturnInfo           ReturnInfo           `json:"return_info"`
	Orderamt             int64                `json:"orderamt"`
	RefundReasonType     int64                `json:"refund_reason_type"`
	RefundReason         string               `json:"refund_reason"`
	MediaList            []Media              `json:"media_list"`
	Status               int64                `json:"status"`
	CreateTime           string               `json:"create_time"`
	UpdateTime           string               `json:"update_time"`
	ReturnId             string               `json:"return_id"`
	RefundPayDetail      RefundPayDetail      `json:"refund_pay_detail"`
	ComplaintOrderIdList []int64              `json:"complaint_order_id_list"`
}

type AftersaleProductInfo struct {
	ProductId    int64  `json:"product_id"`
	OutProductID string `json:"out_product_id"`
	SkuID        int64  `json:"sku_id"`
	OutSkuID     string `json:"out_sku_id"`
	ProductCnt   int64  `json:"product_cnt"`
}

type ReturnInfo struct {
	OrderReturnTime int64  `json:"order_return_time"`
	DeliveryId      string `json:"delivery_id"`
	WaybillId       string `json:"waybill_id"`
	DeliveryName    string `json:"delivery_name"`
}

type RefundPayDetail struct {
	RefundId string `json:"refund_id"`
}

// 小程序发货信息管理服务

type WechatShippingDetail struct {
	DeliveryMode        DeliveryMode  `json:"delivery_mode"`         // 发货模式
	LogisticsType       LogisticsType `json:"logistics_type"`        // 物流模式
	FinishShipping      bool          `json:"finish_shipping"`       // 是否已完成全部发货
	GoodsDesc           string        `json:"goods_desc"`            // 商品描述
	FinishShippingCount int64         `json:"finish_shipping_count"` // 已完成全部发货的次数，未完成时为 0，完成时为 1，重新发货并完成后为 2
	ShippingList        []ShipInfo    `json:"shipping_list"`         // 物流信息列表
}

type ShippedOrderDetail struct {
	TransactionId   string               `json:"transaction_id"`    // 微信支付单号
	MerchantId      string               `json:"merchant_id"`       // 商户号
	SubMerchantId   string               `json:"sub_merchant_id"`   // 二级商户号
	MerchantTradeNo string               `json:"merchant_trade_no"` // 群脉生成的订单号
	Description     string               `json:"description"`       // 以分号连接的所有商品描述
	PaidAmount      int64                `json:"paid_amount"`       // 实际支付金额，分
	OpenId          string               `json:"openid"`            // 支付者 openId
	TradeCreateTime int64                `json:"trade_create_time"` // 交易创建时间
	PayTime         int64                `json:"pay_time"`          // 支付时间
	OrderState      OrderState           `json:"order_state"`       // 订单状态
	InComplaint     bool                 `json:"in_complaint"`      // 是否在交易纠纷中
	Shipping        WechatShippingDetail `json:"shipping"`          // 发货信息
}

type GetOrderShippingStatusResponse struct {
	WechatTradeResponse
	Order ShippedOrderDetail `json:"order"` // 订单信息
}

type GetShippingOrderListResponse struct {
	WechatTradeResponse
	LastIndex string               `json:"last_index"` // 翻页用
	HasMore   bool                 `json:"has_more"`   // 是否还有更多订单
	OrderList []ShippedOrderDetail `json:"order_list"` // 订单列表
}

type CheckShippingManagedResponse struct {
	WechatTradeResponse
	IsTradeManaged bool `json:"is_trade_managed"` // 是否已开通小程序发货信息管理服务
}

type CreateFreightInsuranceOrderResponse struct {
	WechatTradeResponse
	Number         string `json:"policy_no"`
	EndAt          string `json:"insurance_end_date"`
	EstimateAmount int64  `json:"estimate_amount"`
	Premium        int64  `json:"premium"`
}

type ClaimFreightInsuranceResponse struct {
	WechatTradeResponse
	ReportNo     string `json:"report_no"`
	IsHomePickUp bool   `json:"is_home_pick_up"`
}

type CreateFreightInsuranceReturnIdResponse struct {
	WechatTradeResponse
	ReturnId string `json:"return_id"`
}

type FreightInsuranceReturnStatus int

const (
	FREIGHT_INSURANCE_RETURN_STATUS_EMPTY   FreightInsuranceReturnStatus = iota // 用户未填写退货信息
	FREIGHT_INSURANCE_RETURN_STATUS_PICK_UP                                     // 用户选择上门取件
	FREIGHT_INSURANCE_RETURN_STATUS_SELF                                        // 用户选择自行寄回
)

type GetFreightInsuranceReturnDetailResponse struct {
	WechatTradeResponse
	Status       FreightInsuranceReturnStatus `json:"status"`
	WaybillId    string                       `json:"waybill_id"`
	OrderStatus  int                          `json:"order_status"`
	DeliveryName string                       `json:"delivery_name"`
	DeliveryId   string                       `json:"delivery_id"`
}
