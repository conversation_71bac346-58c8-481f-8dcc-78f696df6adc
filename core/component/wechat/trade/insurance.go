package trade

import (
	"context"
	"github.com/spf13/cast"
)

type FreightInsuranceRequester interface {
	// IsServiceEnabled 是否开通了运费险 https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/industry/express/business/freight_insurance.html#%E6%9F%A5%E8%AF%A2%E5%BC%80%E9%80%9A%E7%8A%B6%E6%80%81%E6%8E%A5%E5%8F%A3
	IsServiceEnabled(ctx context.Context) (bool, error)
	// CreateFreightInsuranceOrder 发货时投保 https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/industry/express/business/freight_insurance.html#%E6%8A%95%E4%BF%9D%E6%8E%A5%E5%8F%A3-%E5%8F%91%E8%B4%A7%E6%97%B6%E6%8A%95%E4%BF%9D
	CreateFreightInsuranceOrder(ctx context.Context, req *CreateFreightInsuranceOrderRequest) (*CreateFreightInsuranceOrderResponse, error)
	// ClaimFreightInsurance 消费者自行寄回，商家收货后理赔 https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/industry/express/business/freight_insurance.html#%E7%90%86%E8%B5%94%E6%8E%A5%E5%8F%A3-%E6%94%B6%E5%88%B0%E7%94%A8%E6%88%B7%E9%80%80%E8%B4%A7%E5%90%8E%E5%86%8D%E8%A7%A6%E5%8F%91
	ClaimFreightInsurance(ctx context.Context, req *ClaimFreightInsuranceRequest) (*ClaimFreightInsuranceResponse, error)
	// CreateFreightInsuranceReturnId 同意退货后调用 https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/industry/express/business/freight_insurance.html#%E5%88%9B%E5%BB%BA%E9%80%80%E8%B4%A7-ID
	CreateFreightInsuranceReturnId(ctx context.Context, req *CreateFreightInsuranceReturnIdRequest) (*CreateFreightInsuranceReturnIdResponse, error)
	// UnbindFreightInsuranceReturnId 同意退货后又协商无需退货时调用 https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/industry/express/business/freight_insurance.html#%E8%A7%A3%E7%BB%91%E9%80%80%E8%B4%A7-ID
	UnbindFreightInsuranceReturnId(ctx context.Context, returnId string) (*WechatTradeResponse, error)
	// GetFreightInsuranceReturnDetail 获取退货详情 https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/industry/express/business/freight_insurance.html#%E6%9F%A5%E8%AF%A2%E9%80%80%E8%B4%A7-ID-%E7%8A%B6%E6%80%81
	GetFreightInsuranceReturnDetail(ctx context.Context, returnId string) (*GetFreightInsuranceReturnDetailResponse, error)
}

type wechatFreightInsuranceClient struct {
	wechatTradeClient
}

func (w *wechatFreightInsuranceClient) IsServiceEnabled(ctx context.Context) (bool, error) {
	resp := make(map[string]any)
	err := w.post(ctx, nil, "/wxa/business/insurance_freight/query_open", &resp)
	if err != nil {
		return false, err
	}
	return cast.ToInt(resp["is_open"]) == 1, nil
}

func (w *wechatFreightInsuranceClient) CreateFreightInsuranceOrder(ctx context.Context, req *CreateFreightInsuranceOrderRequest) (*CreateFreightInsuranceOrderResponse, error) {
	resp := &CreateFreightInsuranceOrderResponse{}
	err := w.post(ctx, req, "/wxa/business/insurance_freight/createorder", resp)
	if err == nil {
		err = resp.getError()
	}
	return resp, err
}

func (w *wechatFreightInsuranceClient) ClaimFreightInsurance(ctx context.Context, req *ClaimFreightInsuranceRequest) (*ClaimFreightInsuranceResponse, error) {
	resp := &ClaimFreightInsuranceResponse{}
	err := w.post(ctx, req, "/wxa/business/insurance_freight/claim", resp)
	if err == nil {
		err = resp.getError()
	}
	return resp, err
}

func (w *wechatFreightInsuranceClient) CreateFreightInsuranceReturnId(ctx context.Context, req *CreateFreightInsuranceReturnIdRequest) (*CreateFreightInsuranceReturnIdResponse, error) {
	resp := &CreateFreightInsuranceReturnIdResponse{}
	err := w.post(ctx, req, "/cgi-bin/express/delivery/no_worry_return/add", resp)
	if err == nil {
		err = resp.getError()
	}
	return resp, err
}

func (w *wechatFreightInsuranceClient) UnbindFreightInsuranceReturnId(ctx context.Context, returnId string) (*WechatTradeResponse, error) {
	req := map[string]string{
		"return_id": returnId,
	}
	resp := &WechatTradeResponse{}
	err := w.post(ctx, req, "/cgi-bin/express/delivery/no_worry_return/unbind", resp)
	if err == nil {
		err = resp.getError()
	}
	return resp, err
}

func (w *wechatFreightInsuranceClient) GetFreightInsuranceReturnDetail(ctx context.Context, returnId string) (*GetFreightInsuranceReturnDetailResponse, error) {
	req := map[string]string{
		"return_id": returnId,
	}
	resp := &GetFreightInsuranceReturnDetailResponse{}
	err := w.post(ctx, req, "/cgi-bin/express/delivery/no_worry_return/get", resp)
	if err == nil {
		err = resp.getError()
	}
	return resp, err
}
