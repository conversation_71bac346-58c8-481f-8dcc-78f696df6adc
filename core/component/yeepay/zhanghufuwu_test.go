// Code generated by yeepay_sdk_gen. DO NOT EDIT.

package yeepay

import (
	"encoding/json"
	"log"
	"testing"
)

// 转账-下单
// https://open.yeepay.com/docs/apis/INDUSTRY_SOLUTION/GENERAL/fwssfk/zhanghufuwu/options__rest__v1.0__account__transfer__b2b__order
func TestAccountTransferB2bOrderMarshal(t *testing.T) {
	demo := ``
	var responseMap map[string]interface{}
	err := json.Unmarshal([]byte(demo), &responseMap)
	if err != nil {
		log.Println(err)
		t.Fail()
	}

	var responseObj interface{}
	for _, responseObj = range responseMap {
	}
	bytes, err := json.Marshal(responseObj)
	if err != nil {
		log.Println(err)
		t.Fail()
	}

	var resp AccountTransferB2bOrderResponse
	if err = json.Unmarshal(bytes, &resp); err != nil {
		log.Println(err)
		t.Fail()
	}
}

// 转账-查询
// https://open.yeepay.com/docs/apis/INDUSTRY_SOLUTION/GENERAL/fwssfk/zhanghufuwu/options__rest__v1.0__account__transfer__system__query
func TestAccountTransferSystemQueryMarshal(t *testing.T) {
	demo := ``
	var responseMap map[string]interface{}
	err := json.Unmarshal([]byte(demo), &responseMap)
	if err != nil {
		log.Println(err)
		t.Fail()
	}

	var responseObj interface{}
	for _, responseObj = range responseMap {
	}
	bytes, err := json.Marshal(responseObj)
	if err != nil {
		log.Println(err)
		t.Fail()
	}

	var resp AccountTransferSystemQueryResponse
	if err = json.Unmarshal(bytes, &resp); err != nil {
		log.Println(err)
		t.Fail()
	}
}

// 提现-下单
// https://open.yeepay.com/docs/apis/INDUSTRY_SOLUTION/GENERAL/fwssfk/zhanghufuwu/options__rest__v1.0__account__withdraw__order
func TestAccountWithdrawOrderMarshal(t *testing.T) {
	demo := ``
	var responseMap map[string]interface{}
	err := json.Unmarshal([]byte(demo), &responseMap)
	if err != nil {
		log.Println(err)
		t.Fail()
	}

	var responseObj interface{}
	for _, responseObj = range responseMap {
	}
	bytes, err := json.Marshal(responseObj)
	if err != nil {
		log.Println(err)
		t.Fail()
	}

	var resp AccountWithdrawOrderResponse
	if err = json.Unmarshal(bytes, &resp); err != nil {
		log.Println(err)
		t.Fail()
	}
}

// 全部账户余额查询
// https://open.yeepay.com/docs/apis/INDUSTRY_SOLUTION/GENERAL/fwssfk/zhanghufuwu/options__rest__v1.0__account__accountinfos__query
func TestAccountAccountinfosQueryMarshal(t *testing.T) {
	demo := ``
	var responseMap map[string]interface{}
	err := json.Unmarshal([]byte(demo), &responseMap)
	if err != nil {
		log.Println(err)
		t.Fail()
	}

	var responseObj interface{}
	for _, responseObj = range responseMap {
	}
	bytes, err := json.Marshal(responseObj)
	if err != nil {
		log.Println(err)
		t.Fail()
	}

	var resp AccountAccountinfosQueryResponse
	if err = json.Unmarshal(bytes, &resp); err != nil {
		log.Println(err)
		t.Fail()
	}
}
