// Code generated by yeepay_sdk_gen. DO NOT EDIT.

package yeepay

// AccountTransferB2bOrderRequest 转账-下单请求参数
type AccountTransferB2bOrderRequest struct {
	// Desc:业务发起方商编 发起方商户编号 （标准商户收付款方案中此参数与商编一致，平台商户收付款方案中此参数为平台商商户编号）
	// Demo:************
	ParentMerchantNo string `json:"parentMerchantNo"`
	// Desc:商户请求号，由商户自定义生成;
	// Demo:TRANS2133213124
	RequestNo string `json:"requestNo"`
	// Desc:转出方商户编号 易宝支付分配的的商户唯一标识;
	// Demo:************
	FromMerchantNo string `json:"fromMerchantNo"`
	// Desc:转入方商户编号 易宝支付分配的的商户唯一标识;
	// Demo:************
	ToMerchantNo string `json:"toMerchantNo"`
	// Desc:转入方账户类型 可选项如下: FUND_ACCOUNT:商户支付账户 MARKET_ACCOUNT:营销账户（当为此参数时，fromMerchantNo和toMerchantNo需要保持一致） FEE_ACCOUNT:手续费账户（当为此参数时，fromMerchantNo和toMerchantNo需要保持一致） SPECIAL_FUND_ACCOUNT:专款账户（当为此参数时，toAccountNo不能为空）
	// Demo:默认FUND_ACCOUNT
	ToAccountType string `json:"toAccountType"`
	// Desc:转账金额 单位：元（RMB），精确到分;
	// Demo:0.01 元
	OrderAmount string `json:"orderAmount"`
	// Desc:用途
	// Demo:预付实扣
	Usage string `json:"usage"`
	// Desc:手续费承担方 可选项如下: OUTSIDE:转出方承担, INSIDE:转入方承担(若不传默认转出方承担)
	// Demo:当商户承担且计费方式为预付实扣或后收时，不支持转入方承担；当平台商或服务商承担时无需指定此手续费承担方；
	FeeChargeSide string `json:"feeChargeSide,omitempty"`
	// Desc:通知转入方url 转账接收方通知地址，不传则不通知，默认通知给转入方顶级（如转入方是标准商户则通知给自己）回调内容请参看：<a href="https://open.yeepay.com/docs/apis/fwssfk/options__rest__v1.0__account__transfer__b2b__order#anchor7" data-eleid="12" data-lark-is-custom="true" data-lark-link="true">结果通知</a>
	// Demo:
	NotifyUrl string `json:"notifyUrl,omitempty"`
	// Desc:风控拓展参数 请通过json格式传参
	// Demo:
	RiskInfo string `json:"riskInfo,omitempty"`
	// Desc:转入方账号
	// Demo:
	ToAccountNo string `json:"toAccountNo,omitempty"`
	// Desc:核验方式 非必填字段，当有验密诉求时，需配合密码采集接口使用 可选项如下: PWD:密码校验
	// Demo:PWD
	VerifyType string `json:"verifyType,omitempty"`
	// Desc:核验值 非必填字段，当有验密诉求时，需配合密码采集接口使用； 使用时：当核验方式为PWD时，该参数为密码密文，必填
	// Demo:
	VerifyValue string `json:"verifyValue,omitempty"`
}

// AccountTransferB2bOrderResponse 转账-下单响应参数
type AccountTransferB2bOrderResponse struct {
	// Desc:返回码该参数代表本次请求的处理结果，UA00000为请求成功 若请求失败参看对应错误码和错误信息
	// Demo:UA00000
	ReturnCode string `json:"returnCode,omitempty"`
	// Desc:返回信息
	// Demo:
	ReturnMsg string `json:"returnMsg,omitempty"`
	// Desc:转账状态 订单状态 可选项如下: REQUEST_RECEIVE:请求已接收(易宝正在处理中，请勿重复下单) SUCCESS:转账成功 FAIL:失败(该笔订单转账失败，请更换商户请求号后重新发起转账) WAIT_AUDIT:已受理,待复核
	// Demo:
	TransferStatus string `json:"transferStatus,omitempty"`
	// Desc:易宝订单号 易宝转账订单号
	// Demo:V1238342359459435
	OrderNo string `json:"orderNo,omitempty"`
	// Desc:商户请求号
	// Demo:
	RequestNo string `json:"requestNo,omitempty"`
	// Desc:转账金额
	// Demo:
	OrderAmount string `json:"orderAmount,omitempty"`
	// Desc:转出方商户编号
	// Demo:
	FromMerchantNo string `json:"fromMerchantNo,omitempty"`
	// Desc:转入方商户编号
	// Demo:
	ToMerchantNo string `json:"toMerchantNo,omitempty"`
	// Desc:用途
	// Demo:
	Usage string `json:"usage,omitempty"`
	// Desc:手续费
	// Demo:
	Fee string `json:"fee,omitempty"`
	// Desc:转账下单时间 返回易宝接收转账请求后创建订单时间；
	// Demo:2020-06-23 15:31:34
	CreateTime string `json:"createTime,omitempty"`
	// Desc:转账完成时间 返回转账订单有明确结果（如订单状态为SUCCESS/FAIL）时的时间；
	// Demo:2020-06-23 15:31:34
	FinishTime string `json:"finishTime,omitempty"`
	// Desc:返回转出方易宝账户扣账金额（包含转账金额和手续费（若有））
	// Demo:
	DebitAmount string `json:"debitAmount,omitempty"`
	// Desc:入账金额
	// Demo:
	ReceiveAmount string `json:"receiveAmount,omitempty"`
	// Desc:手续费承担方商户编号
	// Demo:
	FeeMerchantNo string `json:"feeMerchantNo,omitempty"`
	// Desc:资方信息
	// Demo:
	CapitalInfo string `json:"capitalInfo,omitempty"`
}

// AccountTransferSystemQueryRequest 转账-查询请求参数
type AccountTransferSystemQueryRequest struct {
	// Desc:商户请求号 商户请求号，由商户自定义生成(与易宝转账订单号两者填其一)
	// Demo:商户请求号，由商户自定义生成 ;示例值：TRANS2133213124
	RequestNo string `json:"requestNo,omitempty"`
	// Desc:易宝订单号 易宝转账订单号 易宝支付系统生成的转账订单号(与商户请求号两者填其一)
	// Demo:
	OrderNo string `json:"orderNo,omitempty"`
	// Desc:转出方商户编号
	// Demo:易宝支付分配的的商户唯一标识;示例值:************
	MerchantNo string `json:"merchantNo"`
}

// AccountTransferSystemQueryResponse 转账-查询响应参数
type AccountTransferSystemQueryResponse struct {
	// Desc:返回码该参数代表本次请求的处理结果，UA00000为请求成功 若请求失败参看对应错误码和错误信息
	// Demo:UA00000
	ReturnCode string `json:"returnCode,omitempty"`
	// Desc:返回信息
	// Demo:
	ReturnMsg string `json:"returnMsg,omitempty"`
	// Desc:商户请求号，由商户自定义生成
	// Demo:
	RequestNo string `json:"requestNo,omitempty"`
	// Desc:订单号 易宝支付系统生成的转账订单号
	// Demo:
	OrderNo string `json:"orderNo,omitempty"`
	// Desc:转账金额
	// Demo:
	OrderAmount string `json:"orderAmount,omitempty"`
	// Desc:转账类型 可选项如下: B2B:公对公 B2C:公对私
	// Demo:
	TransferType string `json:"transferType,omitempty"`
	// Desc:转出方商户编号
	// Demo:
	FromMerchantNo string `json:"fromMerchantNo,omitempty"`
	// Desc:转入方商户编号
	// Demo:
	ToMerchantNo string `json:"toMerchantNo,omitempty"`
	// Desc:转账状态 可选项如下: REQUEST_RECEIVE:请求已接收(易宝正在处理中,收到最终结果前请勿重复下单) SUCCESS:转账成功 FAIL:失败(该笔订单转账失败,可重新发起转账) WAIT_AUDIT:已受理,待复核 AUDIT_REFUSED:复核拒绝 AUDIT_PASS:复核通过
	// Demo:
	TransferStatus string `json:"transferStatus,omitempty"`
	// Desc:用途
	// Demo:
	Usage string `json:"usage,omitempty"`
	// Desc:手续费
	// Demo:
	Fee string `json:"fee,omitempty"`
	// Desc:转账下单时间 返回易宝接收转账请求后创建订单时间
	// Demo:2020-06-23 15:31:34
	CreateTime string `json:"createTime,omitempty"`
	// Desc:转账完成时间 返回转账订单有明确结果（如订单状态为SUCCESS/FAIL）时的时间
	// Demo:2020-06-23 15:31:34
	FinishTime string `json:"finishTime,omitempty"`
	// Desc:返回转出方易宝账户扣账金额（包含转账金额和手续费（若有））
	// Demo:
	DebitAmount string `json:"debitAmount,omitempty"`
	// Desc:入账金额
	// Demo:
	ReceiveAmount string `json:"receiveAmount,omitempty"`
	// Desc:手续费承担方商户编号
	// Demo:
	FeeMerchantNo string `json:"feeMerchantNo,omitempty"`
	// Desc:资方信息
	// Demo:
	CapitalInfo string `json:"capitalInfo,omitempty"`
	// Desc:核验方式 可选项如下: 密码核验:PWD
	// Demo:
	VerifyType string `json:"verifyType,omitempty"`
}

// AccountWithdrawOrderRequest 提现-下单请求参数
type AccountWithdrawOrderRequest struct {
	// Desc:业务发起方商编 发起方商户编号 （标准商户收付款方案中此参数与商编一致，平台商户收付款方案中此参数为平台商商户编号）
	// Demo:************
	ParentMerchantNo string `json:"parentMerchantNo"`
	// Desc:商户请求号 由商户自定义生成
	// Demo:WITHDRAW20200327103027
	RequestNo string `json:"requestNo"`
	// Desc:商户编号 易宝支付分配的的商户唯一标识
	// Demo:************
	MerchantNo string `json:"merchantNo"`
	// Desc:提现卡ID 添加提现账户时易宝生成的提现卡ID，与提现卡卡号至少填写一个
	// Demo:11103
	BankCardId string `json:"bankCardId,omitempty"`
	// Desc:提现账号 与提现卡ID至少填写一个
	// Demo:6212260200019388841
	BankAccountNo string `json:"bankAccountNo,omitempty"`
	// Desc:到账类型 可选项如下: REAL_TIME:实时 TWO_HOUR:2小时到账 NEXT_DAY:次日到账（无特殊情况资金于次日上午7点左右到提现银行账户中）
	// Demo:REAL_TIME
	ReceiveType string `json:"receiveType"`
	// Desc:提现金额，单位：元（RMB）
	// Demo:1.00
	OrderAmount float64 `json:"orderAmount"`
	// Desc:回调通知地址 商户通知地址,不传则不通知 回调内容请参看： <a href="#anchor7">结果通知</a>
	// Demo:www.baidu.com
	NotifyUrl string `json:"notifyUrl,omitempty"`
	// Desc:银行附言 展示在收款银行系统中的附言，由数字、字母、汉字组成（最终附言内容以银行实际账单为准）。
	// Demo:XXX平台提现
	Remark string `json:"remark,omitempty"`
	// Desc:终端类型 可选项如下: PC:电脑 PHONE:手机 PAD:平板 NFC:可穿戴设备 DTV:数字电视 MPOS:条码支付受理终端 OTHER:其他
	// Demo:PC
	TerminalType string `json:"terminalType,omitempty"`
	// Desc:手续费收取方式 可选项如下: OUTSIDE:外扣 OUT_TO_IN:外扣转内扣 INSIDE:内扣
	// Demo:
	FeeDeductType string `json:"feeDeductType,omitempty"`
	// Desc:账户类型 不传默认走FUND_ACCOUNT 若提现模式为银行清分则字段必填 可选项如下: FUND_ACCOUNT:资金账户 MARKET_ACCOUNT:营销账户 FEE_ACCOUNT:手续费账户 V_BANK_SN_CLEARING_ACCOUNT:苏宁银行清分账户 V_BANK_ECITIC_CLEARING_ACCOUNT:中信银行清分账户
	// Demo:
	AccountType string `json:"accountType,omitempty"`
	// Desc:设备mac地址
	// Demo:
	MacAddress string `json:"macAddress,omitempty"`
	// Desc:提现模式 默认为易宝内部账户模式 可选项如下: INNER_ACCOUNT_WITHDRAW:易宝内部账户模式 BANK_ACCOUNT_WITHDRAW:银行清分银行账户模式
	// Demo:INNER_ACCOUNT_WITHDRAW
	WithdrawModel string `json:"withdrawModel,omitempty"`
	// Desc:银行清分模式提现 必填，扣款银行账户号
	// Demo:
	DebitBankAccountBookNo string `json:"debitBankAccountBookNo,omitempty"`
	// Desc:核验方式 非必填字段，当有验密诉求时，需配合密码采集接口使用； 可选项如下: PWD:密码校验
	// Demo:PWD
	VerifyType string `json:"verifyType,omitempty"`
	// Desc:核验值 非必填字段，当有验密诉求时，需配合密码采集接口使用； 使用时：当核验方式为PWD，参数为密码密文，必填
	// Demo:
	VerifyValue string `json:"verifyValue,omitempty"`
}

// AccountWithdrawOrderResponse 提现-下单响应参数
type AccountWithdrawOrderResponse struct {
	// Desc:返回码 返回码 该参数代表本次请求的处理结果，UA00000为请求成功 若请求失败参看对应错误码和错误信息
	// Demo:UA00000
	ReturnCode string `json:"returnCode,omitempty"`
	// Desc:返回信息
	// Demo:
	ReturnMsg string `json:"returnMsg,omitempty"`
	// Desc:订单状态 可选项如下: REQUEST_RECEIVE:请求已接收（易宝正在处理中，收到最终结果前请勿重复下单） REQUEST_ACCEPT:请求已受理（易宝正在处理中，收到最终结果前请勿重复下单） FAIL:失败 REMITING:（银行正在处理中，收到最终结果前请勿重复下单）
	// Demo:
	Status string `json:"status,omitempty"`
	// Desc:易宝提现订单号 易宝支付系统生成的提现订单号
	// Demo:
	OrderNo string `json:"orderNo,omitempty"`
}

// AccountAccountinfosQueryRequest 全部账户余额查询请求参数
type AccountAccountinfosQueryRequest struct {
	// Desc:商户编号
	// Demo:
	MerchantNo string `json:"merchantNo"`
}

// AccountAccountinfosQueryAccountInfoList 账户信息列表
type AccountAccountinfosQueryAccountInfoList struct {
	// Desc:账户类型 SETTLE_ACCOUNT:待结算账户 FUND_ACCOUNT:商户资金账户 MARKET_ACCOUNT:营销账户 DIVIDE_ACCOUNT:待分账账户 FEE_ACCOUNT:手续费账户 SPECIAL_FUND_ACCOUNT:专款账户
	// Demo:
	AccountType string `json:"accountType,omitempty"`
	// Desc:开户时间
	// Demo:
	CreateTime string `json:"createTime,omitempty"`
	// Desc:余额
	// Demo:
	Balance float64 `json:"balance,omitempty"`
	// Desc:账户状态 可选项如下: AVAILABLE:可用 FROZEN:冻结 FROZEN_CREDIT:冻结止收 FROZEN_DEBIT:冻结止付 CANCELLATION:销户
	// Demo:
	AccountStatus string `json:"accountStatus,omitempty"`
}

// AccountAccountinfosQueryResponse 全部账户余额查询响应参数
type AccountAccountinfosQueryResponse struct {
	// Desc:响应成功返回码为UA00000
	// Demo:
	ReturnCode string `json:"returnCode,omitempty"`
	// Desc:返回信息
	// Demo:
	ReturnMsg string `json:"returnMsg,omitempty"`
	// Desc:发起方商户编号
	// Demo:
	InitiateMerchantNo string `json:"initiateMerchantNo,omitempty"`
	// Desc:商户编号
	// Demo:
	MerchantNo string `json:"merchantNo,omitempty"`
	// Desc:账户总余额
	// Demo:
	TotalAccountBalance float64 `json:"totalAccountBalance,omitempty"`
	// Desc:账户信息列表
	// Demo:
	AccountInfoList []AccountAccountinfosQueryAccountInfoList `json:"accountInfoList,omitempty"`
}

// AccountWithdrawSystemQueryRequest 提现-查询请求参数
type AccountWithdrawSystemQueryRequest struct {
	// Desc:商户编号 易宝支付分配的的商户唯一标识
	// Demo:************
	MerchantNo string `json:"merchantNo"`
	// Desc:商户请求号，由商户自定义生成(与易宝提现订单号两者填其一)
	// Demo:TRANS2133213124
	RequestNo string `json:"requestNo,omitempty"`
	// Desc:易宝订单号 易宝提现订单号，易宝支付系统生成的提现订单号(与商户请求号两者填其一)
	// Demo:V1238342359459435
	OrderNo string `json:"orderNo,omitempty"`
}

// AccountWithdrawSystemQueryResponse 提现-查询响应参数
type AccountWithdrawSystemQueryResponse struct {
	// Desc:返回码该参数代表本次请求的处理结果，UA00000为请求成功 若请求失败参看对应错误码和错误信息
	// Demo:UA00000
	ReturnCode string `json:"returnCode,omitempty"`
	// Desc:返回信息
	// Demo:
	ReturnMsg string `json:"returnMsg,omitempty"`
	// Desc:商户请求号
	// Demo:
	RequestNo string `json:"requestNo,omitempty"`
	// Desc:易宝订单号 易宝提现订单号
	// Demo:V1238342359459435
	OrderNo string `json:"orderNo,omitempty"`
	// Desc:商户编号
	// Demo:
	MerchantNo string `json:"merchantNo,omitempty"`
	// Desc:提现金额
	// Demo:
	OrderAmount string `json:"orderAmount,omitempty"`
	// Desc:到账金额
	// Demo:
	ReceiveAmount string `json:"receiveAmount,omitempty"`
	// Desc:返回转出方易宝账户扣账金额（包含转账金额和手续费（若有））
	// Demo:
	DebitAmount string `json:"debitAmount,omitempty"`
	// Desc:提现下单时间 返回易宝接收提现请求后创建订单时间
	// Demo:2020-06-23 15:31:34
	OrderTime string `json:"orderTime,omitempty"`
	// Desc:提现完成时间 返回提现订单有明确结果（如订单状态为SUCCESS/FAIL）时的时间
	// Demo:2020-06-23 15:31:34
	FinishTime string `json:"finishTime,omitempty"`
	// Desc:提现订单状态: REQUEST_RECEIVE:请求已接收（易宝正在处理中，收到最终结果前请勿重复下单） REQUEST_ACCEPT:请求已受理（易宝正在处理中，收到最终结果前请勿重复下单） FAIL:失败 REMITING:（银行正在处理中，收到最终结果前请勿重复下单） SUCCESS:已到账
	// Demo:
	Status string `json:"status,omitempty"`
	// Desc:失败原因当提现失败时，会返回失败原因
	// Demo:银行账户冻结
	FailReason string `json:"failReason,omitempty"`
	// Desc:手续费承担方商编 平台商承担时返回平台商商编商户承担时返回商户编号
	// Demo:
	FeeUndertakerMerchantNo string `json:"feeUndertakerMerchantNo,omitempty"`
	// Desc:手续费
	// Demo:
	Fee float64 `json:"fee,omitempty"`
	// Desc:到账类型 REAL_TIME:实时 TWO_HOUR:2小时到账 NEXT_DAY:次日到账（无特殊情况资金于次日上午7点左右到提现银行账户中）
	// Demo:REAL_TIME
	ReceiveType string `json:"receiveType"`
	// Desc:开户名
	// Demo:
	AccountName string `json:"accountName,omitempty"`
	// Desc:银行账户
	// Demo:
	AccountNo string `json:"accountNo,omitempty"`
	// Desc:开户行名称
	// Demo:
	BankName string `json:"bankName,omitempty"`
	// Desc:开户行编码
	// Demo:
	BankCode string `json:"bankCode,omitempty"`
	// Desc:支行编码
	// Demo:
	BranchBankCode string `json:"branchBankCode,omitempty"`
	// Desc:冲退标识
	// Demo:
	IsReversed bool `json:"isReversed,omitempty"`
	// Desc:冲退时间
	// Demo:
	ReversedTime string `json:"reversedTime,omitempty"`
	// Desc:备注
	// Demo:
	Remark string `json:"remark,omitempty"`
	// Desc:提现模式 默认为易宝内部账户模式 INNER_ACCOUNT_WITHDRAW:易宝内部账户模式 BANK_ACCOUNT_WITHDRAW:银行清分银行账户模式
	// Demo:INNER_ACCOUNT_WITHDRAW
	WithdrawModel string `json:"withdrawModel,omitempty"`
	// Desc:扣款银行账户号 银行清分模式银行账户提现的 扣款银行账户号
	// Demo:
	DebitBankAccountBookNo string `json:"debitBankAccountBookNo,omitempty"`
	// Desc:核验方式 PWD:密码校验
	// Demo:PWD
	VerifyType string `json:"verifyType,omitempty"`
}
