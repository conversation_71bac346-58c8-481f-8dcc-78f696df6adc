// Code generated by yeepay_sdk_gen. DO NOT EDIT.

package yeepay

import "context"

type YeepayZhanghufuwuRequestor interface {
	// 转账-下单
	// https://open.yeepay.com/docs/apis/INDUSTRY_SOLUTION/GENERAL/fwssfk/zhanghufuwu/options__rest__v1.0__account__transfer__b2b__order
	AccountTransferB2bOrder(ctx context.Context, request *AccountTransferB2bOrderRequest) (*AccountTransferB2bOrderResponse, error)
	// 转账-查询
	// https://open.yeepay.com/docs/apis/INDUSTRY_SOLUTION/GENERAL/fwssfk/zhanghufuwu/options__rest__v1.0__account__transfer__system__query
	AccountTransferSystemQuery(ctx context.Context, request *AccountTransferSystemQueryRequest) (*AccountTransferSystemQueryResponse, error)
	// 提现-下单
	// https://open.yeepay.com/docs/apis/INDUSTRY_SOLUTION/GENERAL/fwssfk/zhanghufuwu/options__rest__v1.0__account__withdraw__order
	AccountWithdrawOrder(ctx context.Context, request *AccountWithdrawOrderRequest) (*AccountWithdrawOrderResponse, error)
	// 提现-查询
	// https://open.yeepay.com/docs/apis/fwssfk/get__rest__v1.0__account__withdraw__system__query
	AccountWithdrawSystemQuery(ctx context.Context, request *AccountWithdrawSystemQueryRequest) (*AccountWithdrawSystemQueryResponse, error)
	// 全部账户余额查询
	// https://open.yeepay.com/docs/apis/INDUSTRY_SOLUTION/GENERAL/fwssfk/zhanghufuwu/options__rest__v1.0__account__accountinfos__query
	AccountAccountinfosQuery(ctx context.Context, request *AccountAccountinfosQueryRequest) (*AccountAccountinfosQueryResponse, error)
}

type yeepayZhanghufuwuClient struct {
	yeepayClient
}

// 转账-下单
// https://open.yeepay.com/docs/apis/INDUSTRY_SOLUTION/GENERAL/fwssfk/zhanghufuwu/options__rest__v1.0__account__transfer__b2b__order
func (client *yeepayZhanghufuwuClient) AccountTransferB2bOrder(ctx context.Context, request *AccountTransferB2bOrderRequest) (*AccountTransferB2bOrderResponse, error) {
	var resp AccountTransferB2bOrderResponse
	return &resp, client.execute(ctx, "POST", "/rest/v1.0/account/transfer/b2b/order", request, &resp)
}

// 转账-查询
// https://open.yeepay.com/docs/apis/INDUSTRY_SOLUTION/GENERAL/fwssfk/zhanghufuwu/options__rest__v1.0__account__transfer__system__query
func (client *yeepayZhanghufuwuClient) AccountTransferSystemQuery(ctx context.Context, request *AccountTransferSystemQueryRequest) (*AccountTransferSystemQueryResponse, error) {
	var resp AccountTransferSystemQueryResponse
	return &resp, client.execute(ctx, "GET", "/rest/v1.0/account/transfer/system/query", request, &resp)
}

// 提现-下单
// https://open.yeepay.com/docs/apis/INDUSTRY_SOLUTION/GENERAL/fwssfk/zhanghufuwu/options__rest__v1.0__account__withdraw__order
func (client *yeepayZhanghufuwuClient) AccountWithdrawOrder(ctx context.Context, request *AccountWithdrawOrderRequest) (*AccountWithdrawOrderResponse, error) {
	var resp AccountWithdrawOrderResponse
	return &resp, client.execute(ctx, "POST", "/rest/v1.0/account/withdraw/order", request, &resp)
}

// 提现-查询
// https://open.yeepay.com/docs/apis/fwssfk/get__rest__v1.0__account__withdraw__system__query
func (client *yeepayZhanghufuwuClient) AccountWithdrawSystemQuery(ctx context.Context, request *AccountWithdrawSystemQueryRequest) (*AccountWithdrawSystemQueryResponse, error) {
	var resp AccountWithdrawSystemQueryResponse
	return &resp, client.execute(ctx, "GET", "/rest/v1.0/account/withdraw/system/query", request, &resp)
}

// 全部账户余额查询
// https://open.yeepay.com/docs/apis/INDUSTRY_SOLUTION/GENERAL/fwssfk/zhanghufuwu/options__rest__v1.0__account__accountinfos__query
func (client *yeepayZhanghufuwuClient) AccountAccountinfosQuery(ctx context.Context, request *AccountAccountinfosQueryRequest) (*AccountAccountinfosQueryResponse, error) {
	var resp AccountAccountinfosQueryResponse
	return &resp, client.execute(ctx, "GET", "/rest/v1.0/account/accountinfos/query", request, &resp)
}
