package taobao

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"mairpc/core/extension"
	"mairpc/core/log"
	"mairpc/core/util"
	"net/url"
	"sort"
	"strings"
	"time"
)

const TAOBAO_DOMAIN = "https://taobao.maiscrm.com/router/rest"

type Taobao struct {
	Common TaobaoCommonRequestor
	Trade  TaobaoTradeRequestor
}

type taobaoClient struct {
	appKey    string
	appSecret string
}

func NewTaobao(args ...string) *Taobao {
	client := taobaoClient{}
	if len(args) == 2 {
		client = taobaoClient{
			appKey:    args[0],
			appSecret: args[1],
		}
	}

	return &Taobao{
		Common: &taobaoCommonClient{
			taobaoClient: client,
		},
		Trade: &taobaoTradeClient{
			taobaoClient: client,
		},
	}
}

func (client *taobaoClient) getAppKey() string {
	if client.appKey != "" {
		return client.appKey
	}
	return util.GetTaobaoAppKey()
}

func (client *taobaoClient) getAppSecret() string {
	if client.appSecret != "" {
		return client.appSecret
	}
	return util.GetTaobaoAppSecret()
}

func (client *taobaoClient) execute(ctx context.Context, method string, request interface{}, data interface{}) error {
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	ctx = log.SwitchOnResponseBodyLog(ctx)
	var requestMap map[string]interface{}

	b, err := json.Marshal(request)
	if err != nil {
		return err
	}
	decoder := json.NewDecoder(bytes.NewBufferString(string(b)))
	decoder.UseNumber()
	err = decoder.Decode(&requestMap)
	if err != nil {
		return err
	}

	requestMap["method"] = method
	values := client.getRequestData(requestMap)
	body, _, err := extension.RequestClient.PostFormData(ctx, "", TAOBAO_DOMAIN, values.Encode(), nil)

	if err != nil {
		return err
	}

	var responseMap map[string]interface{}
	decoder = json.NewDecoder(bytes.NewBufferString(string(body)))
	decoder.UseNumber()
	err = decoder.Decode(&responseMap)
	if err != nil {
		return err
	}

	if errorResp, ok := responseMap["error_response"]; ok {
		b, err := json.Marshal(errorResp)
		if err != nil {
			return err
		}
		taobaoErr := &TaobaoErrorResponse{}
		if err := json.Unmarshal(b, taobaoErr); err != nil {
			return err
		}

		return taobaoErr.Error()
	}

	var responseObj interface{}

	for _, responseObj = range responseMap {
	}

	b, err = json.Marshal(responseObj)
	if err != nil {
		return err
	}
	err = json.Unmarshal(b, data)

	return err
}

func (client *taobaoClient) getRequestData(param map[string]interface{}) *url.Values {
	args := &url.Values{}
	args.Add("timestamp", time.Now().Format("2006-01-02 15:04:05"))
	args.Add("format", "json")
	args.Add("app_key", client.getAppKey())
	args.Add("v", "2.0")
	args.Add("sign_method", "md5")
	args.Add("partner_id", "quncrm")

	for key, val := range param {
		args.Set(key, fmt.Sprintf("%v", val))
	}
	args.Add("sign", client.getSign(*args))
	return args
}

func (client *taobaoClient) getSign(args url.Values) string {
	keys := []string{}
	for k := range args {
		keys = append(keys, k)
	}

	sort.Strings(keys)
	query := client.getAppSecret()
	for _, k := range keys {
		query += k + args.Get(k)
	}
	query += client.getAppSecret()
	signBytes := md5.Sum([]byte(query))
	return strings.ToUpper(hex.EncodeToString(signBytes[:]))
}

type TaobaoErrorResponse struct {
	Code      int    `json:"code"`
	Msg       string `json:"msg"`
	SubCode   string `json:"sub_code"`
	SubMsg    string `json:"sub_msg"`
	RequestId string `json:"request_id"`
}

func (r TaobaoErrorResponse) Error() error {
	return errors.New(fmt.Sprintf("taobao:%d:%s:%s", r.Code, r.SubCode, r.Msg))
}
