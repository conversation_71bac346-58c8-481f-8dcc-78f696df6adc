package jingdong

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"
	"time"

	"mairpc/core/extension"
	"mairpc/core/extension/bson"
	"mairpc/core/log"
	core_util "mairpc/core/util"
	"mairpc/core/util/copier"

	"github.com/spf13/cast"
)

const (
	JINGDONG_CDP_WAY2_DOMAIN    = "https://mkt-cloud-membership.jdx.com"
	JINGDONG_CDP_WAY3_DOMAIN    = "https://membership-express.jdx.com"
	JINGDONG_CDP_TESTING_DOMAIN = "https://membership-mock-pre.jdx.com"
)

const JD_CDP_RUID = "ruid"

func GenerateCrmId(phone, channelId string) string {
	if phone != "" && channelId != "" {
		return fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%s%s", phone, channelId))))
	}
	return fmt.Sprintf("%x", md5.Sum([]byte(bson.NewObjectId().Hex())))
}

func GenerateMixPhone(phone, encryptionKey, channelId string) string {
	if encryptionKey == "" {
		return strings.ToUpper(fmt.Sprintf("%x", md5.Sum([]byte(phone))))
	}
	saltedMd5Data := fmt.Sprintf("%s%s%s%s", encryptionKey, channelId, phone, encryptionKey)
	saltedMd5 := strings.ToUpper(fmt.Sprintf("%x", md5.Sum([]byte(saltedMd5Data))))
	return strings.ToUpper(fmt.Sprintf("%x", md5.Sum([]byte(saltedMd5))))
}

func (client *jingdongClient) cdpWay2Execute(ctx context.Context, method string, request interface{}, response interface{}) error {
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	ctx = log.SwitchOnResponseBodyLog(ctx)
	reqParamJson, err := jsoniterUsingNum.Marshal(request)
	if err != nil {
		return err
	}
	payload := make(map[string]interface{})
	if err := jsoniterUsingNum.Unmarshal(reqParamJson, &payload); err != nil {
		return err
	}
	payload["timestamp"] = time.Now().UnixNano() / 1e6
	payload["appkey"] = client.AppKey
	payload["brandId"] = client.ChannelId
	payload["platform"] = "MAI"
	payload["token"] = client.calcCdpWay2Sign(client.AppSecret, payload)
	url := client.getCdpWay2Url(method)
	pHeaders := map[string]string{
		// 携带 reqId 以便提供给京东排查问题，同时需保证请求维度的唯一性
		"ruid": fmt.Sprintf("%s_%s", core_util.ExtractRequestIDFromCtx(ctx), bson.NewObjectId().Hex()),
	}
	if client.isJdCdpUnderTest() {
		pHeaders["customerId"] = client.ChannelId
		pHeaders["token"] = client.AccessToken
	}
	body, _, err := extension.RequestClient.PostJson(ctx, "", url, payload, &pHeaders)
	if err != nil {
		return err
	}
	if err := jsoniterUsingNum.Unmarshal(body, response); err != nil {
		return err
	}
	baseCdpResp := &JdCdpWay2Response{}
	copier.Instance(nil).From(response).CopyTo(baseCdpResp)
	if baseCdpResp.Code == "" || baseCdpResp.Code == JD_CDP_RESP_CODE_SUC {
		return nil
	}
	return baseCdpResp.Error()
}

func (client *jingdongClient) calcCdpWay2Sign(appSecret string, payload map[string]interface{}) string {
	if appSecret == "" {
		return ""
	}
	signParams := make(map[string]string)
	for k, v := range payload {
		if k == "token" {
			continue
		}
		switch v.(type) {
		case string:
			strValue, ok := v.(string)
			if !ok || strValue == "" {
				continue
			}
			signParams[k] = strValue
		case json.Number:
			numberValue, ok := v.(json.Number)
			if !ok {
				continue
			}
			strValue := string(numberValue)
			if strValue == "" {
				continue
			}
			signParams[k] = strValue
		case float64, float32, int, int64, int32, int16, int8, uint, uint64, uint32, uint16, uint8:
			signParams[k] = cast.ToString(v)
		default:
			continue
		}
	}
	signKeys := make([]string, 0, len(signParams))
	for key := range signParams {
		signKeys = append(signKeys, key)
	}
	sort.Strings(signKeys)
	var sortedString string
	for _, key := range signKeys {
		sortedString = fmt.Sprintf("%s%s%s", sortedString, key, signParams[key])
	}
	if client.isJdCdpUnderTest() {
		appSecret = strings.ToUpper(fmt.Sprintf("%x", md5.Sum([]byte(appSecret))))
	}
	mixedString := fmt.Sprintf("%s%s%s", appSecret, sortedString, appSecret)
	sign := fmt.Sprintf("%x", md5.Sum([]byte(mixedString)))
	return strings.ToUpper(sign)
}

func (client *jingdongClient) getCdpWay2Url(path string) string {
	if client.isJdCdpUnderTest() {
		return fmt.Sprintf("%s%s", JINGDONG_CDP_TESTING_DOMAIN, path)
	}
	return fmt.Sprintf("%s%s", JINGDONG_CDP_WAY2_DOMAIN, path)
}

// 注意：联调时如需使用京东营销云的联调系统 API 域名、签名特殊逻辑，`channel.integrationModes` 中须配置有 `jdCdpUnderTest`。
// 没有该标记则使用生产域名。
func (client *jingdongClient) isJdCdpUnderTest() bool {
	return core_util.StrInArray("jdCdpUnderTest", &client.IntegrationModes)
}

func (client *jingdongClient) cdpWay3Execute(ctx context.Context, path string, request interface{}, response interface{}) error {
	ctx = extension.OpenRequestFailedRetrySwitch(ctx)
	ctx = log.SwitchOnResponseBodyLog(ctx)
	url := client.getCdpWay3Url(path)
	source := "scrm"
	timestamp := time.Now().UnixNano() / 1e6
	auth := fmt.Sprintf("%x", md5.Sum([]byte(fmt.Sprintf("%s%d", client.AccessToken, timestamp))))
	pHeaders := map[string]string{
		"source":    source,
		"timestamp": cast.ToString(timestamp),
		"auth":      auth,
	}
	body, _, err := extension.RequestClient.PostJson(ctx, "", url, request, &pHeaders)
	if err != nil {
		return err
	}
	if err := jsoniterUsingNum.Unmarshal(body, response); err != nil {
		return err
	}
	baseCdpResp := &JdCdpWay3Response{}
	copier.Instance(nil).From(response).CopyTo(baseCdpResp)
	if baseCdpResp.Code == "" || baseCdpResp.Code == JD_CDP_RESP_CODE_SUC {
		return nil
	}
	return errors.New(fmt.Sprintf("JdCdpMember: %s: %s", baseCdpResp.Code, baseCdpResp.Desc))
}

func (client *jingdongClient) getCdpWay3Url(path string) string {
	return fmt.Sprintf("%s%s", JINGDONG_CDP_WAY3_DOMAIN, path)
}
