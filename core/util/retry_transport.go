package util

import (
	"bytes"
	"io"
	"net/http"
	"time"
)

func NewRetryTransparent(retriableStatusCodes []int, maxRetries int) *RetryTransport {
	codes := make(map[int]bool)
	for _, statusCode := range retriableStatusCodes {
		codes[statusCode] = true
	}
	return &RetryTransport{
		transport:            http.DefaultTransport,
		maxRetries:           maxRetries,
		retriableStatusCodes: codes,
	}
}

type RetryTransport struct {
	transport            http.RoundTripper
	retriableStatusCodes map[int]bool
	maxRetries           int
}

func (t *RetryTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	var resp *http.Response
	var err error
	var bodyBytes []byte
	if req.Body != nil {
		bodyBytes, _ = io.ReadAll(req.Body)
	}
	for i := 0; i <= t.maxRetries; i++ {
		// 每次请求时重新设置 body，以解决其只能被读取一次的问题
		if bodyBytes != nil {
			req.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
		}
		resp, err = t.transport.RoundTrip(req)
		if err != nil {
			return nil, err
		}
		// 返回不在 retriableStatusCodes 中的状态码，则不重试
		if _, ok := t.retriableStatusCodes[resp.StatusCode]; !ok {
			return resp, nil
		}
		if i == t.maxRetries {
			return resp, nil
		}
		time.Sleep(time.Millisecond * 100)
	}
	return resp, nil
}
