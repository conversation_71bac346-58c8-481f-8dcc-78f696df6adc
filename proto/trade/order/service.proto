syntax = "proto3";

package mairpc.trade.order;

option go_package = "order";

import "common/request/request.proto";
import "common/response/response.proto";
import "trade/order/douyin_trade_to_sync_phone.proto";
import "trade/order/jingdong_trade_stats.proto";
import "trade/order/order.proto";
import "trade/order/refund.proto";
import "trade/order/third_party_coupon_trade.proto";

service OrderService {
  rpc Demo(OrderDemo) returns (OrderDemo);
  // 更新订单 extraFields 字段
  //
  // 仅支持全量更新，利用网上 json 压缩并转义工具生成参数
  rpc UpdateExtraFields(UpdateExtraFieldsRequest) returns (mairpc.common.response.EmptyResponse);
  // 自动同步订单监视器，每分钟执行一次
  rpc SyncMonitorJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 手动同步订单
  rpc SyncOrder(SyncOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除订单(by orderId && accountId)
  rpc DeleteOrder(DeleteOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 根据 orderId 创建或更新订单，且仅支持全量更新
  rpc UpsertOrder(UpsertOrderRequest) returns (OrderDetailResponse);
  // 根据 orderId 创建或更新订单，且仅支持全量更新
  //
  // 订单金额相关参数不做逻辑处理，需调用方处理好后再传入
  // 金额会转成以分为单位进行计算，调用方需要根据计算公式进行核对，保证数据能够对上
  rpc UpsertOrderVThree(UpsertOrderRequest) returns (OrderDetailResponse);
  // 获取订单详情
  rpc GetOrder(GetOrderRequest) returns (OrderDetailResponse);
  // 导入订单
  rpc ImportOrders(ImportOrdersRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 导出订单
  rpc ExportOrders(ExportOrdersRequest) returns (mairpc.common.response.JobResponse);
  // 获取订单列表
  rpc ListOrders(ListOrdersRequest) returns (ListOrdersResponse);
  // 获取微信小店订单列表
  rpc ListWeshopOrders(ListOrdersRequest) returns (ListOrdersResponse);
  // 异步获取订单数量
  //
  // 配合 ListOrders 使用，提升页面加载速度
  rpc CountOrders(ListOrdersRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 根据 refundId 创建或更新退款单，且仅支持全量更新
  rpc UpsertRefund(UpsertRefundRequest) returns (RefundDetailResponse);
  // 获取退单列表
  rpc ListRefunds(ListRefundsRequest) returns (ListRefundsResponse);
  // 创建京东订单汇总数据
  rpc CreateJingdongTradeStats(CreateJingdongTradeStatsRequest) returns (JingdongTradeStatsDetailResponse);
  // 获取最新一条京东订单汇总数据
  rpc GetLatestJingdongTradeStats(GetLatestJingdongTradeStatsRequest) returns (JingdongTradeStatsDetailResponse);
  // 更新用户最近一次支付时间客户属性
  rpc UpdateMemberLatestPayTime(UpdateMemberLatestPayTimeRequest) returns (mairpc.common.response.EmptyResponse);
  // 统计各状态订单数
  rpc CountOrdersByStatus(CountOrdersByStatusRequest) returns (CountOrdersByStatusResponse);
  // 客户合并后重新计算支付相关属性
  rpc RecalculateTradePropertiesAfterMemberMerged(mairpc.common.request.MemberIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步订货单（批货交易）
  rpc SyncDistributionOrder(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取客户订单数据统计
  rpc GetMemberOrderStats(GetMemberOrderStatsRequest) returns (GetMemberOrderStatsResponse);
  // 创建用于同步手机号的抖音订单
  rpc CreateDouyinTradeToSyncPhone(CreateDouyinTradeToSyncPhoneRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新用于同步手机号的抖音订单
  rpc UpdateDouyinTradesToSyncPhone(UpdateDouyinTradesToSyncPhoneRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取用于同步手机号的抖音订单列表
  rpc ListDouyinTradesToSyncPhone(ListDouyinTradesToSyncPhoneRequest) returns (ListDouyinTradesToSyncPhoneResponse);
  // 创建三方券订单
  rpc CreateThirdPartyCouponTrade(CreateThirdPartyCouponTradeRequest) returns (CreateThirdPartyCouponTradeResponse);
  // 获取三方券订单
  rpc GetThirdPartyCouponTrade(GetThirdPartyCouponTradeRequest) returns (GetThirdPartyCouponTradeResponse);
  // 获取订单导入日志列表
  rpc ListOrderImportLogs(ListOrderImportLogsRequest) returns (ListOrderImportLogsResponse);
  // 更新三方券订单状态
  rpc UpdateThirdPartyCouponTrade(UpdateThirdPartyCouponTradeRequest) returns (mairpc.common.response.EmptyResponse);
  // 新增订单、商品属性
  rpc UpsertTradeProperty(UpsertTradePropertyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取订单、商品属性
  rpc ListTradeProperties(ListTradePropertiesRequest) returns (ListTradePropertiesResponse);
  // 绑定无主订单
  rpc BindUnownedOrder(BindUnownedOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取无主订单绑定记录
  rpc ListBindUnownedOrderLogs(ListBindUnownedOrderLogsRequest) returns (ListBindUnownedOrderLogsResponse);
}
