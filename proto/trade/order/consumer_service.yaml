type: google.api.Service
config_version: 3

http:
  rules:
    - selector: mairpc.trade.order.OrderService.Demo
      post: /v2/trade/order/demo
      body: '*'
      tags: ['交易银行-订单']
    - selector: mairpc.trade.order.OrderService.CountOrdersByStatus
      post: /v2/trade/order/countByStatus
      body: '*'
      tags: ['交易银行-订单']
    - selector: mairpc.trade.order.OrderService.BindUnownedOrder
      post: /v2/trade/unownedOrder/bind
      body: '*'
      tags: ['交易银行-订单']
    - selector: mairpc.trade.order.OrderService.ListBindUnownedOrderLogs
      get: /v2/trade/bindUnownedOrderLogs
      tags: ['交易银行-订单']
    - selector: mairpc.trade.order.OrderService.ListRefunds
      get: /v2/trade/refunds
      tags: ['交易银行-退款']
    - selector: mairpc.trade.order.OrderService.ListWeshopOrders
      get: /v2/trade/weshopOrders
      tags: ['交易银行-订单']
      hideRequestFields: 'orderPlatform,orderPlatforms,conditions'
      hideResponseFields: 'items.receiver,items.delivery,items.coupons,items.syncHistoryId,items.sceneId,items.staffId,items.disableEvent,items.utm,items.extraFields,items.discountInfo,items.properties'
