type: google.api.Service
config_version: 3

http:
  rules:
    - selector: mairpc.account.AccountService.UpdateIsHidePackageRuleHint
      put: /v2/account/packageRuleHint
      body: '*'
      filterMark: backend
      tags: ['通用-设置']
    - selector: mairpc.account.AccountService.GetOssServiceSetting
      get: /v2/oss/serviceSetting
      filterMark: backend,staff
      tags: ['通用']
      scope: ['staff']
    - selector: mairpc.account.AccountService.GetOssSignature
      get: /v2/uploadParameters
      filterMark: openapi,backend,staff
      tags: ['通用']
      scope: ['app', 'staff']
    - selector: mairpc.account.AccountService.GetOssSignature
      get: /v2/oss/signature
      scope: ['app', 'staff']
      tags: ['通用']
      hideRequestFields: 'fileName'
    - selector: mairpc.account.AccountService.GetImageOssSignature
      get: /v2/oss/imageSignature
      scope: ['app', 'staff']
      tags: ['通用']
      hideRequestFields: 'module'
    - selector: mairpc.account.AccountService.GetMediaOssSignature
      get: /v2/oss/mediaSignature
      scope: ['app', 'staff']
      tags: ['通用']
      hideRequestFields: 'module'
    - selector: mairpc.account.AccountService.SendSms
      post: /v2/sendSms
      body: '*'
      filterMark: openapi
      tags: ['通用']
      scope: ['app']
      hideRequestFields: 'subType,aliyunqaSms'
    - selector: mairpc.account.AccountService.QueryAliSmsSendStatistics
      post: /v2/queryAliSmsSendStatistics
      body: '*'
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.QueryAliSmsSendResult
      post: /v2/queryAliSmsSendResult
      body: '*'
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.SendDsms
      post: /v2/sendDsms
      body: '*'
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.GetSubscribedSystemEventConfig
      get: /v2/subscribedSystemEventConfig
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.SubscribedSystemEvent
      post: /v2/subscribedSystemEvent
      body: '*'
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.UnsubscribedSystemEvent
      post: /v2/unsubscribedSystemEvent
      body: '*'
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.GetOemMenuLink
      get: /v2/oemMenuLink
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.ClearOpenApiConsumerCache
      get: /v2/clearOpenApiConsumerCache
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.CreateMaterial
      post: /v2/materials
      body: '*'
      filterMark: backend
      tags: ['素材']
    - selector: mairpc.account.AccountService.BatchCreateVodMaterial
      post: /v2/batchCreateVodMaterial
      body: '*'
      filterMark: backend
      tags: ['素材']
    - selector: mairpc.account.AccountService.UpsertPosterMaterial
      post: /v2/materials/upsertPoster
      body: '*'
      filterMark: backend
      tags: ['素材']
    - selector: mairpc.account.AccountService.ListMaterials
      get: /v2/materials
      filterMark: backend
      tags: ['素材']
    - selector: mairpc.account.AccountService.GetMaterial
      get: /v2/materials/{id}
      filterMark: backend
      tags: ['素材']
    - selector: mairpc.account.AccountService.AddMaterialTags
      post: /v2/materials/addTags
      body: '*'
      filterMark: backend
      tags: ['素材']
    - selector: mairpc.account.AccountService.UpdateMaterial
      put: /v2/materials/{id}
      body: '*'
      filterMark: backend
      tags: ['素材']
    - selector: mairpc.account.AccountService.DeleteMaterials
      delete: /v2/materials
      body: '*'
      filterMark: backend
      tags: ['素材']
    - selector: mairpc.account.AccountService.UploadWechatMaterialToOss
      post: /v2/materials/uploadWechatMaterialToOss
      body: '*'
      filterMark: staff
      scope: ['staff']
      tags: ['素材']
    - selector: mairpc.account.AccountService.CreateContentTags
      post: /v2/contentTags
      body: '*'
      filterMark: backend
      tags: ['标签']
    - selector: mairpc.account.AccountService.GetContentTagGroup
      get: /v2/contentTagGroup
      filterMark: backend
      tags: ['标签-分组']
    - selector: mairpc.account.AccountService.UpdateContentTag
      put: /v2/contentTags/{id}
      body: '*'
      filterMark: backend
      tags: ['标签']
    - selector: mairpc.account.AccountService.UpdateContentTagsWeight
      put: /v2/contentTagsWeight
      body: '*'
      filterMark: backend
      tags: ['标签']
    - selector: mairpc.account.AccountService.DeleteContentTags
      delete: /v2/contentTags
      body: '*'
      filterMark: backend
      tags: ['标签']
    - selector: mairpc.account.AccountService.ListContentTags
      get: /v2/contentTags
      filterMark: backend
      tags: ['标签']
    - selector: mairpc.account.AccountService.Login
      post: /v2/login
      body: '*'
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.GetCurrentUser
      get: /v2/user
      filterMark: backend
      tags: ['租户-用户']
    - selector: mairpc.account.AccountService.ListUserAccounts
      post: /v2/user/accounts
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
    - selector: mairpc.account.AccountService.GetAccount
      get: /v2/account
      filterMark: backend,staff
      scope: ['staff']
      tags: ['租户-企业设置']
    - selector: mairpc.account.AccountService.UpdateAccount
      put: /v2/account
      body: '*'
      filterMark: backend
      tags: ['租户-企业设置']
    - selector: mairpc.account.AccountService.UpdateUser
      put: /v2/users/{id}
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
      permission: 'user:update'
    - selector: mairpc.account.AccountService.UpdateUserSelf
      put: /v2/user
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
      hideRequestFields: 'id'
    - selector: mairpc.account.AccountService.UpdateUserPassword
      put: /v2/users/{id}/password
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
      permission: 'user:update'
    - selector: mairpc.account.AccountService.UpdateUserOwnPassword
      put: /v2/user/password
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
      hideRequestFields: 'id'
    - selector: mairpc.account.AccountService.GetUserRoleMenus
      get: /v2/users/{id}/roleMenus
      filterMark: backend
      tags: ['租户-用户']
    - selector: mairpc.account.AccountService.GetUserOwnRoleMenus
      get: /v2/user/roleMenus
      filterMark: backend
      tags: ['租户-用户']
      hideRequestFields: 'id'
    - selector: mairpc.account.AccountService.Logout
      put: /v2/logout
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
    - selector: mairpc.account.AccountService.LogoffUser
      post: /v2/user/logoff
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
    - selector: mairpc.account.AccountService.GetWechatcpSignPackage
      get: /v2/wechatcp/jssdk/signature
      filterMark: staff,backend
      scope: ['staff']
      tags: ['通用']
    - selector: mairpc.account.AccountService.GetJobsStatus
      get: /v2/jobs/details
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.GetUsers
      get: /v2/users
      scope: ['app']
      filterMark: backend
      tags: ['租户-用户']
    - selector: mairpc.account.AccountService.DeleteUsers
      delete: /v2/users
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
      permission: 'user:delete'
    - selector: mairpc.account.AccountService.AddUser
      post: /v2/users
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
      permission: 'user:create'
    - selector: mairpc.account.AccountService.ValidateCode
      post: /v2/validateCode
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
    - selector: mairpc.account.AccountService.ActivateUser
      post: /v2/user/activate
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
    - selector: mairpc.account.AccountService.SendResetPasswordEmail
      post: /v2/user/sendResetPasswordEmail
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
    - selector: mairpc.account.AccountService.ResetPassword
      post: /v2/user/resetPassword
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
    - selector: mairpc.account.AccountService.ResendActivationEmail
      post: /v2/resendActivationEmail
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
      permission: 'user:create'
    - selector: mairpc.account.AccountService.SendVerifyEmail
      post: /v2/sendVerifyEmail
      body: '*'
      filterMark: backend
      tags: ['租户-用户']
    - selector: mairpc.account.AccountService.GetUserActivationUrl
      get: /v2/users/{id}/activationUrl
      filterMark: backend
      tags: ['租户-用户']
      permission: 'user:list'
    - selector: mairpc.account.AccountService.UpsertUserMenus
      post: /v2/userMenus
      scope: ['app']
      body: '*'
      filterMark: backend
      tags: ['租户-用户角色']
    - selector: mairpc.account.AccountService.ListUserMenus
      get: /v2/userMenus
      filterMark: backend
      tags: ['租户-用户角色']
      permission: 'userMenu:list'
    - selector: mairpc.account.AccountService.ListUserDataPermissions
      get: /v2/userDataPermissions
      filterMark: backend
      tags: ['租户-用户角色']
    - selector: mairpc.account.AccountService.ListUserRoles
      get: /v2/userRoles
      scope: ['app']
      filterMark: backend
      tags: ['租户-用户角色']
      permission: 'userRole:list'
    - selector: mairpc.account.AccountService.CreateUserRole
      post: /v2/userRoles
      body: '*'
      filterMark: backend
      tags: ['租户-用户角色']
      permission: 'userRole:create'
    - selector: mairpc.account.AccountService.UpdateUserRole
      put: /v2/userRoles/{id}
      body: '*'
      filterMark: backend
      tags: ['租户-用户角色']
      permission: 'userRole:update'
    - selector: mairpc.account.AccountService.DeleteUserRole
      delete: /v2/userRoles/{id}
      body: '*'
      filterMark: backend
      tags: ['租户-用户角色']
      permission: 'userRole:delete'
    - selector: mairpc.account.AccountService.GetUserRole
      get: /v2/userRoles/{id}
      scope: ['app']
      filterMark: backend
      tags: ['租户-用户角色']
    - selector: mairpc.account.AccountService.GetChannelByBusiness
      get: /v2/businessChannels
      filterMark: backend,staff
      scope: ['staff']
      tags: ['渠道']
    - selector: mairpc.account.AccountService.GetChannel
      get: /v2/account/channels/{channelId}
      tags: ['渠道']
      permission: 'channel:list'
    - selector: mairpc.account.AccountService.GetChannels
      get: /v2/channels
      filterMark: backend
      tags: ['渠道']
      permission: 'channel:list'
    - selector: mairpc.account.AccountService.ListBusinessChannels
      get: /v2/businessChannelsMap
      filterMark: backend
      tags: ['渠道']
      permission: 'channel:list'
    - selector: mairpc.account.AccountService.DataAnalyses
      post: /v2/dataAnalyses/exec
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['通用-数据分析']
    - selector: mairpc.account.AccountService.DataAnalysesCompleted
      post: /v2/dataAnalyses/completed/{analysesId}/{code}
      filterMark: backend
      body: '*'
      tags: ['通用-数据分析']
      # 获取 QrCode
    - selector: mairpc.account.AccountService.GetWeappQrCode
      get: /v2/weappQrCode
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.CreateDataAnalysesTemplate
      post: /v2/dataAnalyses
      filterMark: backend
      body: '*'
      tags: ['通用-数据分析']
    - selector: mairpc.account.AccountService.ListApps
      get: /v2/apps
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.CreateIDP
      post: /v2/sso/saml2
      filterMark: backend
      body: '*'
      tags: ['通用-SAML']
    - selector: mairpc.account.AccountService.UpdateIDPMetadata
      put: /v2/sso/saml2/idp
      filterMark: backend
      body: '*'
      tags: ['通用-SAML']
    - selector: mairpc.account.AccountService.GetSPMetadata
      get: /v2/sso/saml2/sp
      filterMark: backend
      tags: ['通用-SAML']
    - selector: mairpc.account.AccountService.ListIDPByEmail
      get: /v2/sso/saml/idp
      filterMark: backend
      tags: ['通用-SAML']
    - selector: mairpc.account.AccountService.RecognizeQrCode
      post: /v2/ocr/qrCode
      filterMark: backend
      body: '*'
      tags: ['通用']
    - selector: mairpc.account.AccountService.RecognizeTable
      post: /v2/ocr/table
      filterMark: backend
      body: '*'
      tags: ['通用']
    - selector: mairpc.account.AccountService.ListNotificationSettings
      get: /v2/notificationSettings
      filterMark: backend
      tags: ['通用-消息通知设置']
    - selector: mairpc.account.AccountService.GetNotificationSetting
      get: /v2/notificationSettings/{rule}
      tags: ['通用-消息通知设置']
    - selector: mairpc.account.AccountService.UpdateNotificationSetting
      put: /v2/notificationSettings/{id}
      filterMark: backend
      body: '*'
      tags: ['通用-消息通知设置']
    - selector: mairpc.account.AccountService.DeleteSubscribeMessage
      delete: /v2/notificationSettings/{id}/subscribeMessage
      body: '*'
      filterMark: backend
      tags: ['通用-消息通知设置']
    - selector: mairpc.account.AccountService.DeleteTemplateMessage
      delete: /v2/notificationSettings/{id}/templateMessage
      body: '*'
      filterMark: backend
      tags: ['通用-消息通知设置']
    - selector: mairpc.account.AccountService.DeleteBytedanceSubscribeMessage
      delete: /v2/notificationSettings/{id}/bytedanceSubscribeMessage
      body: '*'
      filterMark: backend
      tags: ['通用-消息通知设置']
    - selector: mairpc.account.AccountService.ListAccountQuotas
      get: /v2/accountQuotas
      filterMark: backend
      tags: ['通用-费用中心']
    - selector: mairpc.account.AccountService.GetQuota
      get: /v2/account/quota
      filterMark: backend
      tags: ['通用-费用中心']
    - selector: mairpc.account.AccountService.ListAccountQuotaStats
      get: /v2/accountQuotaStats
      filterMark: backend
      tags: ['通用-费用中心']
    - selector: mairpc.account.AccountService.CalcAliAccountQuotaJob
      get: /v2/calcAliAccountQuotaJob
      filterMark: backend
      tags: ['通用-费用中心']
    - selector: mairpc.account.AccountService.GetAccountPlans
      get: /v2/accountPlans
      filterMark: backend
      tags: ['通用-费用中心']
    - selector: mairpc.account.AccountService.GetAsyncStatus
      get: /v2/asyncCacheData
      filterMark: backend,staff
      scope: ['staff']
      tags: ['通用']
    - selector: mairpc.account.AccountService.GetGeocoding
      get: /v2/geocoding
      filterMark: staff
      scope: ['staff']
      tags: ['通用']
    - selector: mairpc.account.AccountService.GetReverseGeocoding
      get: /v2/reverseGeocoding
      filterMark: staff
      scope: ['staff']
      tags: ['通用']
    - selector: mairpc.account.AccountService.SearchPlace
      post: /v2/searchPlace
      body: '*'
      filterMark: staff
      scope: ['staff']
      tags: ['通用']
    - selector: mairpc.account.AccountService.UpsertDashboardNavigation
      post: /v2/dashboard/navigation/upsert
      body: '*'
      filterMark: backend
      tags: ['通用-工作台']
    - selector: mairpc.account.AccountService.GetDashboardNavigation
      get: /v2/dashboard/navigation
      filterMark: backend
      tags: ['通用-工作台']
    - selector: mairpc.account.AccountService.UpsertDashboardModule
      post: /v2/dashboard/module/upsert
      body: '*'
      filterMark: backend
      tags: ['通用-工作台']
    - selector: mairpc.account.AccountService.GetDashboardModule
      get: /v2/dashboard/module
      filterMark: backend
      tags: ['通用-工作台']
    - selector: mairpc.account.AccountService.ListPermissions
      get: /v2/permissions
      filterMark: backend
      tags: ['租户-用户角色']
    - selector: mairpc.account.AccountService.UpsertUserRole
      post: /v2/userRoles/upsert
      body: '*'
      filterMark: backend
      tags: ['租户-用户角色']
      permission: 'userRole:create'
    - selector: mairpc.account.AccountService.UpsertOrderExportSetting
      post: /v2/orderExportSetting/upsert
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单']
    - selector: mairpc.account.AccountService.GetOrderExportSetting
      get: /v2/orderExportSetting
      filterMark: backend
      tags: ['私域商城-订单']
    - selector: mairpc.account.AccountService.SendMessage
      post: /v2/sendMessage
      body: '*'
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.InitDefaultResources
      post: /v2/initDefaultResources
      filterMark: backend
      tags: ['通用-测试']
    - selector: mairpc.account.AccountService.UpdateDefaultResources
      post: /v2/updateDefaultResources
      filterMark: backend
      tags: ['通用-测试']
    - selector: mairpc.account.AccountService.SetWxCardField
      post: /v2/wechat/setWxCardField
      body: '*'
      filterMark: backend
      tags: ['通用-设置微信会员卡开卡字段']
    - selector: mairpc.account.AccountService.GetWxCardMemberActivationInfo
      post: /v2/wechat/getWxCardMemberActivationInfo
      body: '*'
      filterMark: backend
      tags: ['通用-获取微信会员卡会员信息']
    - selector: mairpc.account.AccountService.GetTencentVodSignature
      get: /v2/vod/signature
      filterMark: backend
      tags: ['通用-云点播']
    - selector: mairpc.account.AccountService.UpdateVodMaterial
      put: /v2/vod/update/{id}
      body: '*'
      filterMark: backend
      tags: ['通用-云点播']
    - selector: mairpc.account.AccountService.ListDsmsSendLog
      get: /v2/dsmsSendLogs
      filterMark: backend
      tags: ['通用-数字短信']
    - selector: mairpc.account.AccountService.TencentZjDsmsSendResult
      post: /v2/dsms/zj/sendResult
      body: '*'
      filterMark: backend
      tags: ['通用-数字短信']
    - selector: mairpc.account.AccountService.CalcAccountQuotaStatsJob
      post: /v2/accountQuotaStats
      filterMark: backend
      tags: ['通用-测试']
    - selector: mairpc.account.AccountService.SyncVodDailyPlayStats
      post: /v2/syncVodDailyPlayStats
      filterMark: backend
      tags: ['通用-测试']
    - selector: mairpc.account.AccountService.ConvertUrlToShortUrl
      post: /v2/convertUrlToShortUrl
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['素材']
    - selector: mairpc.account.AccountService.GetIpLocation
      get: /v2/ipLocation
      tags: ['通用']
    - selector: mairpc.account.AccountService.CreateAuditLog
      post: /v2/auditLogs
      filterMark: backend
      body: '*'
      tags: ['通用']
      hideRequestFields: 'operatorId'
    - selector: mairpc.account.AccountService.UpsertMessage
      post: /v2/upsertMessage
      body: '*'
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.GetMessage
      get: /v2/message
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.SendSmsVerificationCode
      post: /v2/sendSmsVerificationCode
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['通用']
    - selector: mairpc.account.AccountService.VerifySmsVerificationCode
      post: /v2/verifySmsVerificationCode
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['通用']
    - selector: mairpc.account.AccountService.ConsumeSmsSendStatus
      post: /v2/consumeSmsSendStatus
      body: '*'
      tags: ['通用']
    # 创建雷达素材
    - selector: mairpc.account.AccountService.CreateRadarMaterial
      post: /v2/radarMaterials
      body: '*'
      filterMark: backend
      tags: ['素材']
    # 删除雷达素材
    - selector: mairpc.account.AccountService.DeleteRadarMaterial
      delete: /v2/radarMaterials/{id}
      body: '*'
      filterMark: backend
      tags: ['素材']
    # 更新雷达素材
    - selector: mairpc.account.AccountService.UpdateRadarMaterial
      put: /v2/radarMaterials/{id}
      body: '*'
      filterMark: backend
      tags: ['素材']
    # 雷达素材列表
    - selector: mairpc.account.AccountService.ListRadarMaterials
      get: /v2/radarMaterials
      filterMark: backend
      tags: ['素材']
    # 同步雷达素材
    - selector: mairpc.account.AccountService.SyncRadarMaterial
      post: /v2/radarMaterial/sync
      body: '*'
      filterMark: backend
      tags: ['素材']
    # 触发计费提醒
    - selector: mairpc.account.AccountService.SendAccountQuotaStatsEmail
      get: /v2/sendAccountQuotaStatsEmail
      filterMark: backend
      tags: ['计费提醒']
    - selector: mairpc.account.AccountService.GetUserRolePermissions
      get: /v2/userRole/permissions
      scope: ['app']
      filterMark: backend
      tags: ['测试']
    - selector: mairpc.account.AccountService.BatchUpdateMaterialContentTag
      post: /v2/materialContentTags/batchUpdate
      body: '*'
      filterMark: backend
      tags: ['素材']
    - selector: mairpc.account.AccountService.GetSensitiveOperation
      get: /v2/sensitiveOperations/{name}
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.BatchDownloadQrCodes
      post: /v2/qrCode/batchDownload
      body: '*'
      filterMark: backend
      tags: ['素材']
    - selector: mairpc.account.AccountService.ExtractAddress
      post: /v2/extractAddress
      body: '*'
      tags: ['通用']
    - selector: mairpc.account.AccountService.ListUserEntityPermissions
      get: /v2/userEntityPermissions
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.GetGroupedChannels
      get: /v2/groupedChannels
      filterMark: backend
      tags: ['通用']
    - selector: mairpc.account.AccountService.ListMessageNotificationLog
      post: /v2/messageNotificationLogs/search
      body: '*'
      sensitiveFields: 'items.memberInfo.name,items.memberInfo.phone'
      tags: ['通用-消息通知记录']
    - selector: mairpc.account.AccountService.GetMessageNotificationLogSensitiveData
      get: /v2/messageNotificationLogs/{id}/sensitiveData
      tags: ['通用-消息通知记录']
    - selector: mairpc.account.AccountService.ExportMessageNotificationLog
      post: /v2/messageNotificationLogs/export
      body: '*'
      tags: ['通用-消息通知记录']
    - selector: mairpc.account.AccountService.GetMessageNotificationLogOverviewStatistic
      get: /v2/messageNotificationLogs/statistic/overview
      tags: ['通用-消息通知记录']
    - selector: mairpc.account.AccountService.GetMessageNotificationLogTrendStatistic
      post: /v2/messageNotificationLogs/statistic/trend/search
      body: '*'
      tags: ['通用-消息通知记录']
    - selector: mairpc.account.AccountService.ExportMessageNotificationLogTrendStatistic
      post: /v2/messageNotificationLogs/statistic/trend/export
      body: '*'
      tags: ['通用-消息通知记录']
    - selector: mairpc.account.AccountService.ListOperationSpaces
      get: /v2/operationSpaces
      tags: ['管理中心-运营空间']
    - selector: mairpc.account.AccountService.UpdateOperationSpaceStatus
      put: /v2/operationSpaces/{id}/status
      body: '*'
      tags: ['管理中心-运营空间']
