syntax = "proto3";

package mairpc.account;

option go_package = "account";

import "account/account.proto";
import "account/alert.proto";
import "account/audit_log.proto";
import "account/captcha.proto";
import "account/content_tag.proto";
import "account/content_tag_group.proto";
import "account/dashboard.proto";
import "account/dataAnalyses.proto";
import "account/dataTransmissionTask.proto";
import "account/dsms_send_log.proto";
import "account/encryption.proto";
import "account/expiration_reminder.proto";
import "account/group.proto";
import "account/job.proto";
import "account/lbs.proto";
import "account/material.proto";
import "account/menu.proto";
import "account/message.proto";
import "account/message_notification_log.proto";
import "account/notification_setting.proto";
import "account/operation_space.proto";
import "account/poster_material.proto";
import "account/qrcode.proto";
import "account/send_dsms.proto";
import "account/sensitive_operation.proto";
import "account/service_setting.proto";
import "account/user.proto";
import "account/user_entity_permission.proto";
import "account/vod_material.proto";
import "common/benchmark/benchmark.proto";
import "common/request/request.proto";
import "common/response/response.proto";

service AccountService {
  rpc Benchmark(mairpc.common.benchmark.BenchmarkRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取渠道详情
  rpc GetChannel(ChannelDetailRequest) returns (ChannelDetailResponse);
  // 创建渠道
  rpc CreateChannel(CreateChannelRequest) returns (ChannelDetailResponse);
  // 更新渠道
  rpc UpdateChannel(UpdateChannelRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取渠道列表
  rpc GetChannels(ChannelListRequest) returns (ChannelListResponse);
  // 删除二维码
  rpc DeleteQrcodes(DeleteQrcodesRequest) returns (DeleteQrcodesResponse);
  // 用渠道ID获取渠道信息
  rpc GetChannelsByChannelIds(GetChannelsByChannelIdsRequest) returns (GetChannelsByChannelIdsResponse);
  // 获取各业务场景渠道列表
  rpc ListBusinessChannels(ListBusinessChannelsRequest) returns (ListBusinessChannelsResponse);
  // 获取账户详情
  rpc GetUser(GetUserRequest) returns (UserDetailResponse);
  // 获取用户列表
  rpc GetUsers(UserListRequest) returns (UserListResponse);
  // 创建账户
  rpc CreateUser(CreateUserRequest) returns (UserDetailResponse);
  // 编辑账户手机号
  rpc ChangeUserPhone(ChangeUserPhoneRequest) returns (mairpc.common.response.BoolResponse);
  // 删除账户
  rpc DeleteUsers(DeleteUsersRequest) returns (mairpc.common.response.BoolResponse);
  // 获取集团下租户列表
  rpc GetGroupAccounts(mairpc.common.request.EmptyRequest) returns (GetGroupAccountsResponse);
  // 获取租户阈值配置
  rpc GetQuota(mairpc.common.request.EmptyRequest) returns (Quota);
  // 获取租户短信签名
  rpc GetSmsTopic(mairpc.common.request.EmptyRequest) returns (SmsTopicResponse);
  // 解密小程序加密的手机号
  rpc DecryptMiniProgram(DecryptMiniProgramRequest) returns (DecryptMiniProgramResponse);
  // 获取SeviceSetting中的信息
  rpc GetServiceSetting(GetServiceSettingRequest) returns (ServiceSettingDetailResponse);
  // 获取 OSS 服务配置
  rpc GetOssServiceSetting(mairpc.common.request.EmptyRequest) returns (GetOssServiceSettingResponse);
  // 关闭包装规则提示
  rpc UpdateIsHidePackageRuleHint(UpdatePackageRuleHitRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取图片验证码
  rpc GetImageCaptcha(GetImageCaptchaRequest) returns (ImageCaptchaResponse);
  // 验证图片验证码
  rpc VerifyImageCaptcha(VerifyImageCaptchaRequest) returns (mairpc.common.response.EmptyResponse);
  // 发送验证短信
  rpc SendPhoneCaptcha(SendPhoneCaptchaRequest) returns (mairpc.common.response.EmptyResponse);
  // 发送验证短信（不需要图片验证码）
  rpc SendPhoneCaptchaDirectly(SendPhoneCaptchaDirectlyRequest) returns (mairpc.common.response.EmptyResponse);
  // 验证短信验证码
  rpc VerifySmsVerificationCode(VerifySmsVerificationCodeRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取 OSS 上传文件签名
  //
  // 签名有效期为 1 个小时，上传文件最大为 200M
  rpc GetOssSignature(OssSignatureRequest) returns (OssSignatureResponse);
  // 获取 OSS 上传文件签名（有默认 PostPolicy 限制）
  //
  // 签名有效期为 1 个小时，上传文件最大为 200M
  rpc GetOssSignatureWithDefaultPolicy(OssSignatureRequest) returns (OssSignatureResponse);
  // 获取 OSS 上传图像文件签名
  //
  // 签名有效期为 1 个小时，上传文件最大为 200M
  rpc GetImageOssSignature(OssSignatureRequest) returns (OssSignatureResponse);
  // 获取 OSS 上传媒体文件签名
  //
  // 签名有效期为 1 个小时，上传文件最大为 200M
  rpc GetMediaOssSignature(OssSignatureRequest) returns (OssSignatureResponse);
  // 上报小程序 formId
  rpc ReportWeappFormIds(ReportWeappFormIdsRequest) returns (ReportWeappFormIdsResponse);
  // 上报应用错误日志
  rpc ReportAppLogs(ReportAppLogsRequest) returns (mairpc.common.response.EmptyResponse);
  // 发送短信
  rpc SendSms(SendSmsRequest) returns (mairpc.common.response.EmptyResponse);
  // 查询阿里云短信发送统计
  rpc QueryAliSmsSendStatistics(QueryAliSmsSendStatisticsRequest) returns (QueryAliSmsSendStatisticsResponse);
  // 拉取阿里云短信发送结果
  rpc QueryAliSmsSendResult(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取应用
  rpc GetApplication(mairpc.common.request.DetailRequest) returns (ApplicationDetailResponse);
  // 安全域名是否已配置
  rpc IsSafeDomainExisted(SafeDomainRequest) returns (SafeDomainResponse);
  // 创建安全域名配置
  rpc CreateSafeDomain(SafeDomainRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新安全域名配置
  rpc UpdateSafeDomain(UpdateSafeDomainRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除安全域名配置
  rpc DeleteSafeDomain(SafeDomainRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取安全域名列表
  rpc GetSafeDomains(mairpc.common.request.EmptyRequest) returns (SafeDomainsResponse);
  // 获取微信 jssdk 签名
  //
  // 默认使用服务商模式。如果需要自定义域名，设置 isNotDelegated=true，禁用服务商模式，同时到微信公众平台设置自定义的域名为 JS 接口安全域名。
  rpc GetWechatSignPackage(GetWechatSignPackageRequest) returns (WechatSignPackageResponse);
  // 获取企业微信 jssdk 签名
  rpc GetWechatcpSignPackage(GetWechatcpSignPackageRequest) returns (WechatcpSignPackageResponse);
  // 创建内容标签分组
  rpc CreateContentTagGroup(CreateContentTagGroupRequest) returns (ContentTagGroupDetail);
  // 删除内容标签分组
  rpc DeleteContentTagGroup(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新内容标签分组
  rpc UpdateContentTagGroup(UpdateContentTagGroupRequest) returns (ContentTagGroupDetail);
  // 获取内容标签分组列表
  rpc ContentTagGroupList(ContentTagGroupListRequest) returns (ContentTagGroupListResponse);
  // 获取内容标签单个分组
  rpc GetContentTagGroup(GetContentTagGroupRequest) returns (GetContentTagGroupResponse);
  // 创建内容标签
  rpc CreateContentTags(CreateContentTagsRequest) returns (CreateContentTagsResponse);
  // 删除内容标签
  rpc DeleteContentTags(DeleteContentTagsRequest) returns (mairpc.common.response.EmptyResponse);
  // 移动标签到分组
  rpc MoveContentTagsToGroup(MoveContentTagsToGroupRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量更新内容标签关联业务
  rpc BatchUpdateContentTagGroupBusiness(BatchUpdateContentTagBusinessRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新内容标签顺序
  rpc UpdateContentTagsWeight(UpdateContentTagsWeightRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取内容标签列表
  rpc ListContentTags(ListContentTagsRequest) returns (ListContentTagsResponse);
  // 发送系统邮件
  rpc SendSystemEmail(SendSystemEmailRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取验证码配置
  rpc GetCaptchaConfig(GetCaptchaConfigRequest) returns (GetCaptchaConfigResponse);
  // 票据检验
  rpc VerifyCaptchaTicket(CaptchaInfo) returns (mairpc.common.response.EmptyResponse);
  // 发送短信验证码
  //
  // 匿名使用，必须使用图形验证码（Captcha）；经过 OAuth 之后，可以不传 Captcha，会根据客户 ID 限制发送频率（5次/小时）。
  rpc SendSmsVerificationCode(SendSmsVerificationCodeRequest) returns (mairpc.common.response.EmptyResponse);
  // 审核垃圾信息
  rpc ScanSpamText(ScanSpamTextRequest) returns (ScanSpamTextResponse);
  // 审核违规图片
  rpc ScanSpamImage(ScanSpamImageRequest) returns (ScanSpamImageResponse);
  // 创建素材
  rpc CreateMaterial(CreateMaterialRequest) returns (MaterialDetail);
  //  批量创建 vod 素材
  rpc BatchCreateVodMaterial(BatchCreateVodMaterialRequest) returns (BatchCreateVodMaterialResponse);
  //  前端上传视频后，批量创建 vod 素材
  rpc BatchCreateVodMaterialAfterUpload(BatchCreateVodMaterialAfterUploadRequest) returns (BatchCreateVodMaterialResponse);
  // 更新 vod 素材
  rpc UpdateVodMaterial(UpdateVodMaterialRequest) returns (MaterialDetail);
  //  创建或更新海报素材
  rpc UpsertPosterMaterial(UpsertPosterMaterialRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除素材
  rpc DeleteMaterials(mairpc.common.request.IdListRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新素材
  rpc UpdateMaterial(UpdateMaterialRequest) returns (mairpc.common.response.EmptyResponse);
  // 搜索素材
  rpc ListMaterials(ListMaterialsRequest) returns (ListMaterialsResponse);
  // 获取素材详情
  rpc GetMaterial(mairpc.common.request.DetailRequest) returns (MaterialDetail);
  // 打标签
  rpc AddMaterialTags(AddMaterialTagsRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新内容标签
  rpc UpdateContentTag(UpdateContentTagRequest) returns (mairpc.common.response.EmptyResponse);
  // 登录
  //
  // 不支持多租户，默认登录到用户所属第一个激活的租户
  rpc Login(UserCredential) returns (LoginResponse);
  // 获取企业设置
  rpc GetAccount(mairpc.common.request.EmptyRequest) returns (GetAccountResponse);
  // 更新企业设置
  rpc UpdateAccount(UpdateAccountRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取用户可访问企业列表
  rpc ListUserAccounts(ListUserAccountsRequest) returns (ListUserAccountsResponse);
  // 获取登录用户信息
  rpc GetCurrentUser(mairpc.common.request.EmptyRequest) returns (SimpleUser);
  // 根据地址描述获取坐标信息
  rpc GetGeocoding(GetGeocodingRequest) returns (GetGeocodingResponse);
  // 根据坐标信息获取地址描述
  rpc GetReverseGeocoding(GeoLocation) returns (GetReverseGeocodingResponse);
  // 根据 IP 获取地址信息
  rpc GetIpLocation(GetIpLocationRequest) returns (GetIpLocationResponse);
  // 创建 sreadmin job
  rpc CreateJob(CreateJobRequest) returns (CreateJobResponse);
  // 获取 sreadmin job 运行状态
  rpc GetJobsStatus(GetJobsStatusRequest) returns (GetJobsStatusResponse);
  // 更新用户
  rpc UpdateUser(UpdateUserRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新用自己的信息
  rpc UpdateUserSelf(UpdateUserRequest) returns (mairpc.common.response.EmptyResponse);
  // 修改密码
  rpc UpdateUserPassword(UpdateUserPasswordRequest) returns (mairpc.common.response.EmptyResponse);
  // 修改用户自己的密码
  rpc UpdateUserOwnPassword(UpdateUserPasswordRequest) returns (mairpc.common.response.EmptyResponse);
  // 用户登出
  rpc Logout(LogoutRequest) returns (mairpc.common.response.EmptyResponse);
  // 注销用户
  rpc LogoffUser(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 按不同 DSN 各获取一个租户 ID
  rpc GetDsnAccount(mairpc.common.request.EmptyRequest) returns (GetDsnAccountResponse);
  // 获取不同 DSN 对应的租户 ID 列表
  rpc GetDsnAccounts(GetDsnAccountsRequest) returns (GetDsnAccountsResponse);
  // 更新任务中心
  rpc UpdateDataTransmissionTask(UpdateDataTransmissionTaskRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建任务中心
  rpc CreateDataTransmissionTask(CreateDataTransmissionTaskRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取任务中心详情
  rpc GetDataTransmissionTask(GetDataTransmissionTaskRequest) returns (DataTransmissionTaskDetail);
  // 添加用户
  rpc AddUser(AddUserRequest) returns (AddUserResponse);
  // 校验 Code
  rpc ValidateCode(ValidateCodeRequest) returns (ValidateCodeResponse);
  // 激活用户
  rpc ActivateUser(ActivateUserRequest) returns (mairpc.common.response.EmptyResponse);
  // 发送重置密码邮件
  rpc SendResetPasswordEmail(SendResetPasswordEmailRequest) returns (mairpc.common.response.EmptyResponse);
  // 重置密码
  rpc ResetPassword(ResetPasswordRequest) returns (mairpc.common.response.EmptyResponse);
  // 初始化菜单
  rpc UpsertUserMenus(UpsertUserMenuRequest) returns (mairpc.common.response.EmptyResponse);
  // 菜单列表
  rpc ListUserMenus(ListUserMenusRequest) returns (ListUserMenusResponse);
  // 角色列表
  rpc ListUserRoles(ListUserRolesRequest) returns (ListUserRolesResponse);
  // 创建用户角色
  rpc CreateUserRole(CreateUserRoleRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新用户角色
  rpc UpdateUserRole(UpdateUserRoleRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除用户角色
  rpc DeleteUserRole(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 角色详情
  rpc GetUserRole(mairpc.common.request.DetailRequest) returns (GetUserRoleResponse);
  // 重发激活邮件
  rpc ResendActivationEmail(ResendActivationEmailRequest) returns (mairpc.common.response.EmptyResponse);
  // 发送验证邮箱的邮件
  rpc SendVerifyEmail(SendVerifyEmailRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取激活链接
  rpc GetUserActivationUrl(GetUserActivationUrlRequest) returns (GetUserActivationUrlResponse);
  // 获取授权渠道
  //
  // 根据业务模块获取授权渠道，如群脉零售为 retail
  rpc GetChannelByBusiness(GetChannelByBusinessRequest) returns (GetChannelsByChannelIdsResponse);
  // 数据分析
  rpc DataAnalyses(DataAnalysesRequest) returns (DataAnalysesResponse);
  // 批量数据分析
  //
  // 会根据请求条件筛选 dataAnalyse，并打包调用一次大数据的批量分析 api
  rpc BatchDataAnalyses(BatchDataAnalysesRequest) returns (mairpc.common.response.EmptyResponse);
  // 数据分析完成
  rpc DataAnalysesCompleted(DataAnalysesCompletedRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取 QrCode
  rpc GetWeappQrCode(GetQrCodeRequest) returns (GetQrCodeResponse);
  // 执行数据分析
  rpc ExecDataAnalyses(ExecDataAnalysesRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取用户角色及权限
  rpc GetUserRolePermissions(GetUserRolePermissionsRequest) returns (GetUserRolePermissionsResponse);
  // 创建数据分析模板
  rpc CreateDataAnalysesTemplate(CreateDataAnalysesTemplateRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取开启模块的所有的 account
  rpc ListAccountByEnabledMods(ListAccountByEnabledModsRequest) returns (ListAccountByEnabledModsResponse);
  // 通过邮箱地址获取 IDP 信息
  rpc ListIDPByEmail(ListIDPByEmailRequest) returns (ListIDPByEmailResponse);
  // 判断 IDP 提供断言的真伪
  rpc AssertionConsumer(AssertionConsumerRequest) returns (AssertionConsumerResponse);
  // 获取 SSO 登录失败以后重定向页面地址
  rpc GetSSOFailedRedirectionUrl(mairpc.common.request.DetailRequest) returns (GetSSOFailedRedirectionUrlResponse);
  // 获取应用列表
  rpc ListApps(ListAppsRequest) returns (ListAppsResponse);
  // 获取 OEM 应用菜单链接
  rpc GetOemMenuLink(GetOemMenuLinkRequest) returns (GetOemMenuLinkResponse);
  // 提供 SP 的 metadata
  rpc GetSPMetadata(GetSPMetadataRequest) returns (GetSPMetadataResponse);
  // 创建 IDP
  rpc CreateIDP(CreateIDPRequest) returns (CreateIDPResponse);
  // 更新 IDP 的 metadata
  rpc UpdateIDPMetadata(UpdateIDPMetadataRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取敏感权限信息
  rpc GetSensitiveOperation(GetSensitiveOperationRequest) returns (SensitiveOperation);
  // 获取数据分析结果
  rpc GetDataAnalyses(GetDataAnalysesRequest) returns (DataAnalysesResponse);
  // 识别二维码，注意，如果传入非阿里云上海地域的 OSS 链接接口会先读取再调用阿里云，结果中的 url 将会是阿里云生成的随机值
  rpc RecognizeQrCode(RecognizeQrCodeRequest) returns (RecognizeQrCodeResponse);
  // 获取 secretKey
  rpc GetAccountSecretKey(mairpc.common.request.EmptyRequest) returns (GetAccountSecretKeyResponse);
  // 遍历租户
  rpc IterateAccounts(IterateAccountsRequest) returns (IterateAccountsResponse);
  // 获取消息通知设置
  rpc ListNotificationSettings(ListNotificationSettingsRequest) returns (ListNotificationSettingsResponse);
  // 修改消息通知设置
  rpc UpdateNotificationSetting(UpdateNotificationSettingRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除小程序订阅消息
  rpc DeleteSubscribeMessage(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 初始化消息通知设置
  rpc InitDefaultNotificationSettings(InitDefaultNotificationSettingsRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取一条消息通知设置
  rpc GetNotificationSetting(GetNotificationSettingRequest) returns (GetNotificationSettingResponse);
  // 创建用户行为日志
  rpc CreateAuditLog(CreateAuditLogRequest) returns (AuditLogDetailResponse);
  // 删除模板消息
  rpc DeleteTemplateMessage(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 统计系统资源配额消耗
  rpc CalcAccountQuotaStatsJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 系统资源列表
  rpc ListAccountQuotas(mairpc.common.request.EmptyRequest) returns (ListAccountQuotasResponse);
  // 系统资源统计列表
  rpc ListAccountQuotaStats(ListAccountQuotaStatsRequest) returns (ListAccountQuotaStatsResponse);
  // 系统基本信息
  rpc GetAccountPlans(mairpc.common.request.EmptyRequest) returns (GetAccountPlansResponse);
  // 获取加解密 key
  rpc GetEncryptionKey(GetEncryptionKeyRequest) returns (GetEncryptionKeyResponse);
  // 数据加密
  rpc Encrypt(EncryptionRequest) returns (EncryptionResponse);
  // 数据解密
  rpc Decrypt(EncryptionRequest) returns (EncryptionResponse);
  // 更新系统资源
  rpc IncAccountQuotas(IncAccountQuotasRequest) returns (mairpc.common.response.EmptyResponse);
  // 发送系统资源消耗统计邮件
  rpc SendAccountQuotaStatsEmail(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除数据分析结果
  rpc DeleteDataAnalyses(DeleteDataAnalysesRequest) returns (mairpc.common.response.EmptyResponse);
  // 消耗系统资源
  rpc ConsumeAccountQuota(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 代码异步执行时，创建 AsyncCacheData
  rpc CreateAsyncCacheData(CreateAsyncCacheDataRequest) returns (mairpc.common.response.EmptyResponse);
  // 异步执行结束后更新 AsyncCacheData 状态
  rpc UpdateAsyncCacheData(UpdateAsyncCacheDataRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取异步执行状态，用于前端轮循
  rpc GetAsyncStatus(GetAsyncStatusRequest) returns (GetAsyncStatusResponse);
  // 系统基本信息
  rpc MigrateAccountQuotaStats(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 系统基本信息
  rpc SendMessage(SendMessageRequest) returns (mairpc.common.response.EmptyResponse);
  // 图像颜色识别
  rpc RecognizeImageColor(RecognizeImageColorRequest) returns (RecognizeImageColorResponse);
  // 创建或更新便捷导航
  rpc UpsertDashboardNavigation(UpsertDashboardNavigationRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取便捷导航
  rpc GetDashboardNavigation(mairpc.common.request.EmptyRequest) returns (GetDashboardNavigationResponse);
  // 创建或跟新工作台模块
  rpc UpsertDashboardModule(UpsertDashboardModuleRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取工作台模块
  rpc GetDashboardModule(mairpc.common.request.EmptyRequest) returns (GetDashboardModuleResponse);
  // 获取租户 oem 信息
  rpc ListAccountOem(ListAccountOemRequest) returns (ListAccountOemResponse);
  // 权限列表
  rpc ListPermissions(mairpc.common.request.EmptyRequest) returns (ListPermissionsResponse);
  // 创建或修改用户角色
  rpc UpsertUserRole(UpsertUserRoleRequest) returns (UpsertUserRoleResponse);
  // 清除 OpenAPI consumer 缓存
  rpc ClearOpenApiConsumerCache(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 上传微信素材到 OSS
  rpc UploadWechatMaterialToOss(UploadWechatMaterialToOssRequest) returns (UploadWechatMaterialToOssResponse);
  // 生成随机唯一编码
  rpc GenerateRandomUniqueCodes(GenRandomUniqueCodesRequest) returns (RandomUniqueCodeList);
  // 更新短信发送结果
  rpc UpdateSmsSendLog(UpdateSmsSendLogRequest) returns (mairpc.common.response.EmptyResponse);
  // 通过短信发送回执更新发送状态
  rpc ConsumeSmsSendStatus(ConsumeSmsSendStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建或修改订单导出设置
  rpc UpsertOrderExportSetting(UpsertOrderExportSettingRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取订单导出设置
  rpc GetOrderExportSetting(GetOrderExportSettingRequest) returns (GetOrderExportSettingResponse);
  // 删除抖音订阅消息
  rpc DeleteBytedanceSubscribeMessage(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取手机号国际区号列表
  rpc ListPhoneCodes(mairpc.common.request.EmptyRequest) returns (PhoneCodeList);
  // 更新、创建数据权限
  rpc UpsertUserDataPermissions(UpsertUserDataPermissionsRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取数据权限列表
  rpc ListUserDataPermissions(ListUserDataPermissionsRequest) returns (ListUserDataPermissionsResponse);
  // 初始化默认数据
  rpc InitDefaultResources(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取用户角色菜单并集
  rpc GetUserRoleMenus(mairpc.common.request.DetailRequest) returns (GetUserRoleMenusResponse);
  // 获取用户自己的角色菜单并集
  rpc GetUserOwnRoleMenus(mairpc.common.request.DetailRequest) returns (GetUserRoleMenusResponse);
  // 迁移数据
  rpc UpdateDefaultResources(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取租户登录过期时间
  rpc GetSessionValidPeriod(mairpc.common.request.EmptyRequest) returns (SessionValidPeriodResponse);
  // 计算阿里云租户每小时订单消耗
  rpc CalcAliAccountQuotaJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取小程序添加会员卡参数
  rpc GetWxCardExt(GetWxCardExtRequest) returns (GetWxCardExtResponse);
  // 设置微信会员卡开卡字段
  rpc SetWxCardField(SetWxCardFieldRequest) returns (SetWxCardFieldResponse);
  // 获取微信小程序开卡插件参数
  rpc GetWxCardActivationUrl(GetWxCardActivationUrlRequest) returns (GetWxCardActivationUrlResponse);
  // 拉取微信会员卡激活信息
  rpc GetWxCardMemberActivationInfo(GetWxCardMemberActivationInfoRequest) returns (GetWxCardMemberActivationInfoResponse);
  // 解码会员卡code
  rpc DecryptWxCardCode(DecryptWxCardCodeRequest) returns (DecryptWxCardCodeResponse);
  // 创建系统事件消息模板
  rpc CreateSystemEventMessageTemplate(CreateSystemEventMessageTemplateRequest) returns (mairpc.common.response.EmptyResponse);
  // 更改系统事件消息模板
  rpc UpdateSystemEventMessageTemplate(UpdateSystemEventMessageTemplateRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除系统事件消息模板
  rpc DeleteSystemEventMessageTemplate(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 订阅系统事件
  rpc SubscribedSystemEvent(SubscribedSystemEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 取消订阅系统事件
  rpc UnsubscribedSystemEvent(SubscribedSystemEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取订阅信息
  rpc GetSubscribedSystemEventConfig(GetSubscribedSystemEventConfigRequest) returns (GetSubscribedSystemEventConfigResponse);
  //  获取腾讯云点播上传签名
  rpc GetTencentVodSignature(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.StringResponse);
  // 创建或更新消息
  rpc UpsertMessage(UpsertMessageRequest) returns (mairpc.common.response.EmptyResponse);
  // 已阅读消息
  rpc ReadMessage(mairpc.common.request.StringIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建系统事件通知消息
  rpc CreateMessageBySystemEvent(CreateMessageBySystemEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 给 user 发送消息
  rpc SendMessageToUser(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  //  处理腾讯云点播事件通知
  rpc HandleTencentVodEventNotification(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 发送数字短信
  rpc SendDsms(SendDsmsRequest) returns (SendDsmsResponse);
  // 获取数字短信发送记录
  rpc ListDsmsSendLog(ListDsmsSendLogRequest) returns (ListDsmsSendLogResponse);
  // 同步数字短信发送结果
  rpc SyncDsmsSendResult(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步云点播每日播放统计数据
  rpc SyncVodDailyPlayStats(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 生成运营后台管理员 token
  rpc GenerateUserToken(GenerateUserTokenRequest) returns (LoginResponse);
  // 将普通链接转换成 oauth 链接并转换为短链
  //
  // 注意调用接口会在 originalLink 和 launchLink 中生成对应记录，会在后台 内容-链接管理 中显示出来
  rpc ConvertUrlToShortUrl(ConvertUrlToShortUrlRequest) returns (ConvertUrlToShortUrlResponse);
  // 腾讯珠玑数字短信发送结果回调
  rpc TencentZjDsmsSendResult(TencentZjDsmsSendResultRequest) returns (TencentZjDsmsSendResultResponse);
  // 获取消息详情
  rpc GetMessage(GetMessageRequest) returns (MessageDetail);
  // 每天 9 点发送满足提前天数条件的到期提醒
  //
  // 同时处理到期提醒状态，仅发送处于已开启状态的到期提醒
  rpc SendExpirationReminder(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.JobResponse);
  // 创建或全量更新过期提醒
  rpc UpsertExpirationReminder(UpsertExpirationReminderRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建 sreadmin alert
  rpc CreateAlert(CreateAlertRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建雷达素材
  rpc CreateRadarMaterial(CreateRadarMaterialRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除雷达素材
  rpc DeleteRadarMaterial(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新雷达素材
  rpc UpdateRadarMaterial(UpdateRadarMaterialRequest) returns (mairpc.common.response.EmptyResponse);
  // 雷达素材列表
  rpc ListRadarMaterials(ListRadarMaterialsRequest) returns (ListRadarMaterialsResponse);
  // 同步雷达素材
  rpc SyncRadarMaterial(SyncRadarMaterialRequest) returns (mairpc.common.response.EmptyResponse);
  // 地址信息提取
  rpc ExtractAddress(ExtractAddressRequest) returns (ExtractAddressResponse);
  // 银行卡识别
  rpc RecognizeBankCard(RecognizeBankCardRequest) returns (RecognizeBankCardResponse);
  // 用户是否拥有敏感操作的权限
  rpc HasSensitiveOperationAuthorizedToUser(HasSensitiveOperationAuthorizedToUserRequest) returns (mairpc.common.response.BoolResponse);
  // 身份证识别
  rpc RecognizeIdentityCard(RecognizeIdentityCardRequest) returns (RecognizeIdentityCardResponse);
  // 营业执照识别
  rpc RecognizeBusinessLicense(RecognizeBusinessLicenseRequest) returns (RecognizeBusinessLicenseResponse);
  // 批量给素材打标签
  rpc BatchUpdateMaterialContentTag(BatchUpdateMaterialContentTagRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量下载二维码
  rpc BatchDownloadQrCodes(BatchDownloadQrCodesRequest) returns (mairpc.common.response.JobResponse);
  // 修改 VOD 的配额单位：由 次 -> 流量
  rpc MigrateVodQuotaUnit(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建多条 auditLog
  rpc BatchCreateAuditLog(BatchCreateAuditLogRequest) returns (mairpc.common.response.EmptyResponse);
  // 表格识别
  rpc RecognizeTable(RecognizeTableRequest) returns (RecognizeTableResponse);
  // 批量设置、删除用户实体维度权限
  rpc SetUserEntityPermissions(SetUserEntityPermissionsRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取用户实体维度权限列表
  rpc ListUserEntityPermissions(ListUserEntityPermissionsRequest) returns (ListUserEntityPermissionsResponse);
  // 按平台分组获取渠道
  rpc GetGroupedChannels(GetGroupedChannelsRequest) returns (GetGroupedChannelsResponse);
  // 食品经营许可证识别
  rpc RecognizeFoodManageLicense(RecognizeFoodManageLicenseRequest) returns (RecognizeFoodManageLicenseResponse);
  // 地点搜索
  rpc SearchPlace(SearchPlaceRequest) returns (SearchPlaceResponse);
  // 创建消息通知记录
  rpc CreateMessageNotificationLog(CreateMessageNotificationLogRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取消息通知记录敏感数据
  rpc GetMessageNotificationLogSensitiveData(GetMessageNotificationLogSensitiveDataRequest) returns (GetMessageNotificationLogSensitiveDataResponse);
  // 获取消息通知记录列表
  rpc ListMessageNotificationLog(ListMessageNotificationLogRequest) returns (ListMessageNotificationLogResponse);
  // 导出消息通知记录列表
  rpc ExportMessageNotificationLog(ListMessageNotificationLogRequest) returns (mairpc.common.response.JobResponse);
  // 获取消息通知记录概览统计
  rpc GetMessageNotificationLogOverviewStatistic(GetMessageNotificationLogOverviewStatisticRequest) returns (GetMessageNotificationLogOverviewStatisticResponse);
  // 获取消息通知记录趋势统计
  rpc GetMessageNotificationLogTrendStatistic(GetMessageNotificationLogTrendStatisticRequest) returns (GetMessageNotificationLogTrendStatisticResponse);
  // 导出消息通知记录列表
  rpc ExportMessageNotificationLogTrendStatistic(GetMessageNotificationLogTrendStatisticRequest) returns (mairpc.common.response.JobResponse);
  // 同步消息通知记录状态
  rpc SyncMessageNotificationLogStatus(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 身份二要素核验
  rpc VerifyId2Meta(VerifyId2MetaRequest) returns (VerifyId2MetaResponse);
  // 通用文字识别
  rpc RecognizeGeneral(RecognizeGeneralRequest) returns (RecognizeGeneralResponse);
  // 运营空间列表接口
  rpc ListOperationSpaces(ListOperationSpacesRequest) returns (ListOperationSpacesResponse);
  // 修改运营空间状态
  rpc UpdateOperationSpaceStatus(UpdateOperationSpaceStatusRequest) returns (mairpc.common.response.EmptyResponse);
}
