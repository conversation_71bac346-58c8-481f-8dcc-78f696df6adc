syntax = "proto3";

package mairpc.product;

option go_package = "product";

import "common/request/request.proto";
import "common/response/response.proto";

message BrandDetail {
  // 品牌 ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 品牌名称
  string name = 3;
  // 品牌 Logo
  string logo = 4;
  // 品牌描述
  string description = 5;
  // 关联商品数量
  uint64 productCount = 6;
  // 创建时间
  string createdAt = 7;
  // 更新时间
  string updatedAt = 8;
  // 创建者
  mairpc.common.response.UserInfo createdBy = 9;
  // 更新者
  mairpc.common.response.UserInfo updatedBy = 10;
  // 分账类型，afterPaid（支付后立即分账），默认为空，表示使用全局设置
  string divideType = 11;
}

message ListBrandsRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // 品牌名
  string name = 2;
  // 品牌 ids
  repeated string ids = 3; // valid:"objectIdList"
  // 精确品牌名
  string explicitName = 4;
}

message ListBrandsResponse {
  // 品牌列表
  repeated BrandDetail items = 1;
  // 品牌总数
  uint64 totalCount = 2;
}

message CreateBrandRequest {
  // 品牌名称
  string name = 1; // valid:"required,runelength(1|30)"
  // 品牌描述
  string description = 2;
  // 品牌 Logo
  string logo = 3; // valid:"url"
  // 分账类型，afterPaid（支付后立即分账），默认为空，表示使用全局设置
  string divideType = 4; // valid:"in(afterPaid)"
}

message UpdateBrandRequest {
  //@required
  //
  // 品牌 ID
  string id = 1; // valid:"required,objectId"
  // 品牌名称
  string name = 2;
  // 品牌描述
  string description = 3;
  // 品牌 Logo
  string logo = 4; // valid:"url"
  // 分账类型，afterPaid（支付后立即分账），默认为空，表示使用全局设置
  string divideType = 5; // valid:"in(afterPaid)"
}
