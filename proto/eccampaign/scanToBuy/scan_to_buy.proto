syntax = "proto3";

package mairpc.eccampaign.scan_to_buy;

option go_package = "scan_to_buy";

import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/types/types.proto";

message GetProductByBarCodeRequest {
  // @required
  //
  // 69码
  string barCode = 1; // valid:"required"
}

message ListCartGroupsRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
  // 备注
  string remark = 3;
}

message CreateScanToBuyResponse {
  string id = 1;
}

message ListScanToBuysRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // 活动状态：邀请支付（pending），支付成功（success），支付失败（failed），不传为获取全部
  string status = 2;
  // 下单时间
  mairpc.common.types.DateRange createdTime = 3;
  string searchKey = 4;
}

message GetScanToBuyCountsRequest {
  mairpc.common.types.DateRange createdTime = 1;
}

message ListScanToBuysResponse {
  repeated ScanToBuyDetail items = 1;
  uint64 total = 2;
}

message GetScanToBuyRequest {
  // @required
  //
  // 扫码购订单id
  string id = 1; //valid:"required,objectId"
}

message ScanToBuyDetail {
  // 有效分组
  repeated CartGroup validGroups = 1;
  // 商品总价
  int64 totalAmount = 2;
  // 优惠信息
  repeated mairpc.common.ec.DiscountInfo discounts = 3;
  // 实付金额
  uint64 payAmount = 4;
  // 导购信息
  Staff staff = 5;
  // 订单编号
  string ecOrderId = 6;
  // 扫码购订单状态
  string status = 7;
  // 创建时间
  string createdAt = 8;
  string storeId = 9;
  string memberId = 10;
  string id = 11;
  string number = 12;
  string remark = 13;
  // 支付宝预支付交易标识
  string prePayTn = 14;
}

message Staff {
  string id = 1;
  string name = 2;
}

message CartGroup {
  repeated CartCampaign campaigns = 1;
}

message CartCampaign {
  // 活动 Id
  string id = 1;
  // 活动类型
  string type = 2;
  repeated CartProduct products = 6;
}

message CartProduct {
  // ID
  string id = 1;
  // 商品 ID (ec.product.id)
  string productId = 2;
  // 商品 ID (product.id)
  string originalProductId = 3;
  // 商品名
  string productName = 4;
  // 商品图
  string picture = 5;
  // 数量
  int64 total = 6;
  // 价格
  int64 price = 7;
  // 原价
  int64 originalPrice = 8;
  // 是否选中
  bool checked = 9;
  // 所选商品规格
  SkuSpec specs = 10;
  // 所选规格商品状态
  //
  // 已删除（deleted）、未上架（initialized）、待上架（scheduled）、已上架（shelved）、已下架（unshelved）、库存不足（understocked）
  // 所选规格已售罄（sold_out）、所有规格已售罄（all_skus_sold_out）、门店限售（store_restriction）、活动限购（campaign_limit）
  // 个人限购（purchase_limit）、群组限购（purchase_limit_member_groups）、标签限购（purchase_limit_member_tags）
  // 活动禁售(campaign_disable_sell)
  string status = 11;
  // 折扣信息
  repeated mairpc.common.ec.DiscountInfo discounts = 12;
  // 是否是赠品
  bool isPresent = 13;
  // 是否有可用优惠券
  bool hasCoupon = 14;
  // 商品类型
  //
  // 实物商品（product），虚拟商品（virtual），卡券商品（coupon）
  string productType = 18;
  // 赠品类型
  //
  // 买赠券（coupon），满赠活动（campaign）
  string presentType = 21;
  // 商品正在进行中的活动
  repeated BriefCampaign campaigns = 26;
  // 参与买赠活动商品数量
  uint64 presentDiscountCount = 31;
  string addedBy = 32;
  // 商品编码
  string number = 33;
}

message CampaignPurchaseLimit {
  // 活动类型，optionalPackageCampaign（任选打包）
  string type = 1;
  // 限购数量
  uint64 limit = 2;
  // 剩余可增加数量
  int64 remainCount = 3;
  // 已在购物车数量
  int64 countInCart = 4;
}

message SkuSpec {
  string sku = 1;
  repeated string properties = 2;
}

message BriefCampaign {
  // 活动 id
  string id = 1;
  // 活动类型
  string type = 2;
}

message UpdateScanToBuyEcOrderIdRequest {
  // @required
  //
  // 扫码购订单id
  string id = 1; //valid:"required,objectId"
  // @required
  //
  // 零售订单id
  string ecOrderId = 2; //valid:"required,objectId"
}

message GetScanToBuySettingResponse {
  string id = 1;
  bool isEnabled = 2;
  bool canPayByOthers = 3;
  repeated Payment payments = 4;
  string process = 5;
}

message UpdateScanToBuySettingRequest {
  // @required
  //
  // id
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 是否被开启
  bool isEnabled = 2;
  // 支付流程 accept-需要接单操作,notAccept-无需接单
  string process = 3; // valid:"in(accept|notAccept)"
  // 支付方式(微信支付、支付宝支付)
  repeated Payment payments = 4;
}

message Payment {
  string name = 1;
  bool enabled = 2;
  string code = 3;
  string icon = 4;
}

message GetScanToBuyCountsResponse {
  int64 successTotal = 1;
  int64 failedTotal = 2;
}
