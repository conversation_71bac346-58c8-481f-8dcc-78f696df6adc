syntax = "proto3";

package mairpc.eccampaign.plus_buy;

option go_package = "plus_buy";

import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/types/types.proto";

message CreatePlusBuyRequest {
  // @required
  //
  // 活动名称
  string name = 1; // valid:"required，runelength(1|20)"
  // @required
  //
  // 开始时间 时间格式需要满足 ISO 8601 标准，比如 2006-01-02T15:04:05Z
  string startAt = 2; // valid:"required,rfc3339"
  // @required
  //
  // 结束时间 时间格式需要满足 ISO 8601 标准，比如 2006-01-02T15:04:05Z
  string endAt = 3; // valid:"required,rfc3339"
  // @required
  //
  // 商品规则
  Rule rule = 4; // valid:"required"
  // @required
  //
  // 满足换购的价格
  uint64 price = 5; // valid:"required"
  // @required
  //
  // 换购商品的规则
  PlusBuyRule plusBuyRule = 6; // valid:"required"
  // 是否换购包邮
  bool isFreeShipping = 7;
  // 是否允许使用优惠券
  bool canUseCoupon = 8;
  // 是否允许使用储值卡
  bool canUsePrepaidCard = 9;
  // 是否只能使用一种优惠
  bool isDiscountLimit = 10;
  // 参与客户限制
  mairpc.common.ec.MemberFilter memberLimit = 11;
  // 是否开启倒计时
  bool countdownEnabled = 12;
  // 提前预告时间，单位：秒，0 表示不开启提前预告
  int64 noticeBeforeStartTime = 13;
  // 分享描述
  string shareDescription = 14;
  // 分享图片
  string sharePicture = 15;
  // 储值卡限制
  mairpc.common.ec.PrepaidCardLimit prepaidCardLimit = 16;
  // 是否开启弹窗
  bool isModalEnabled = 17;
}

message Rule {
  // @required
  //
  // 活动类型
  //
  // include(部分商品参加)，all（全部商品参加）
  string type = 1; // valid:"required,in(include|all)"
  // 活动商品
  repeated Product products = 2;
}

message Product {
  // @required
  //
  // 商品 Id
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 商品 sku 列表
  repeated string skus = 2; // valid:"required"
}

message PlusBuyRule {
  // @required
  //
  // 换购的限制
  //
  // single(一个)，multiple(多个)
  string type = 1; // valid:"required,in(single|multiple)"
  // @required
  //
  // 换购商品
  repeated PlusBuyProduct products = 2; // valid:"required"
}

message PlusBuyProduct {
  // @required
  //
  // 商品 Id
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 商品 sku
  string sku = 2; // valid:"required"
  // @required
  //
  // 换购的价格
  uint64 price = 3; // valid:"required"
  // 库存
  uint64 stock = 4;
  // 每人限购
  uint64 personalLimitCount = 5;
}

message CreatePlusBuyResponse {
  // 参与校验活动商品是否全部冲突
  bool isAllConflictProduct = 1;
  // 活动商品冲突的商品 id 列表
  repeated string productIds = 2;
  // 参与校验换购商品是否全部冲突
  bool isAllConflictPlusBuyProduct = 3;
  // 换购商品冲突的商品 id 列表
  repeated string plusBuyProductIds = 4;
}

message UpdatePlusBuyRequest {
  // @required
  //
  // 活动 Id
  string id = 1; // valid:"required,objectId"
  // 活动名称
  string name = 2; // valid:"runelength(1|20)"
  // 开始时间 时间格式需要满足 ISO 8601 标准，比如 2006-01-02T15:04:05Z
  string startAt = 3; // valid:"rfc3339"
  // 结束时间 时间格式需要满足 ISO 8601 标准，比如 2006-01-02T15:04:05Z
  string endAt = 4; // valid:"rfc3339"
  // 商品规则
  Rule rule = 5;
  // 满足换购的价格
  uint64 price = 6;
  // 换购商品的规则
  PlusBuyRule plusBuyRule = 7;
  // 是否换购包邮
  bool isFreeShipping = 8;
  // 是否结束活动
  bool isStop = 9;
  // 是否允许使用优惠券
  bool canUseCoupon = 10;
  // 是否允许使用储值卡
  bool canUsePrepaidCard = 11;
  // 是否只能使用一种优惠
  bool isDiscountLimit = 12;
  // 参与客户限制
  mairpc.common.ec.MemberFilter memberLimit = 13;
  // 是否开启倒计时
  bool countdownEnabled = 14;
  // 提前预告时间，单位：秒，0 表示不开启提前预告
  int64 noticeBeforeStartTime = 15;
  // 分享描述
  string shareDescription = 16;
  // 分享图片
  string sharePicture = 17;
  // 储值卡限制
  mairpc.common.ec.PrepaidCardLimit prepaidCardLimit = 18;
  // 是否开启弹窗
  mairpc.common.types.BoolValue isModalEnabled = 19;
}

message UpdatePlusBuyResponse {
  // 参与校验换购商品是否全部冲突
  bool isAllConflictPlusBuyProduct = 1;
  // 换购商品冲突的商品 id 列表
  repeated string plusBuyProductIds = 2;
  // 参与校验活动商品是否全部冲突
  bool isAllConflictProduct = 3;
  // 活动商品冲突的商品 id 列表
  repeated string productIds = 4;
}

message ListPlusBuysRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // 活动状态：未开始（created），进行中（running），已结束（ended），不传为获取全部
  repeated string status = 2;
  // 活动名称
  //
  // 支持模糊搜索
  string name = 3;
  // 活动时间
  mairpc.common.types.StringDateRange campaignTime = 4;
  // 活动 id 列表
  repeated string ids = 5;
  // 额外信息
  //
  // 可选值：stats（返回参与人数、销售额、支付订单数统计数据)
  repeated string extraFields = 6;
}

message ListPlusBuysResponse {
  repeated PlusBuy items = 1;
  uint64 total = 2;
}

message PlusBuy {
  // 活动 id
  string id = 1;
  // 活动名称
  string name = 2;
  // 活动开始时间
  string startAt = 3;
  // 活动结束时间
  string endAt = 4;
  // 活动状态
  //
  // 未开始（created），进行中（running），已结束（ended）
  string status = 5;
  // 活动的类型
  //
  // include(部分商品参加)，all（全部商品参加）
  string type = 6;
  // 满足换购的价格
  uint64 price = 7;
  // 分享描述
  string shareDescription = 8;
  // 分享图片
  string sharePicture = 9;
  // 参与人数
  uint64 memberCount = 10;
  // 销售额
  uint64 totalAmount = 11;
  // 订单数
  uint64 orderCount = 12;
}

message GetPlusBuyRequest {
  // @required
  //
  // 活动 id
  string id = 1; // valid:"required,objectId"
  // 门店 id
  string storeId = 2; // valid:"objectId"
  // 活动商品分页信息（不传默认返回所有 products）
  mairpc.common.request.ListCondition productListCondition = 3;
  // 导购商品分页信息（不传默认返回所有 plusBuyProducts）
  mairpc.common.request.ListCondition plusBuyProductListCondition = 4;
  // 只返回 eccampaign.plusBuy 表中 Rule.Products 自有字段，不额外返回 ec.product 表中字段
  bool simpleProduct = 5;
  // 只返回 eccampaign.plusBuy 表中 PlusBuyRule.Products 自有字段，不额外返回 ec.product 表中字段
  bool simplePlusBuyProduct = 6;
  // 客户 id
  string memberId = 7;
}

message GetPlusBuyResponse {
  // 活动 id
  string id = 1;
  // 活动名称
  string name = 2;
  // 活动开始时间
  string startAt = 3;
  // 活动结束时间
  string endAt = 4;
  // 活动类型
  string type = 5;
  // 活动商品
  repeated ProductDetail products = 6;
  // 满足换购的价格
  uint64 price = 7;
  // 换购商品
  repeated PlusBuyProductDetail plusBuyProducts = 8;
  // 换购的限制
  string plusBuyType = 9;
  // 是否换购包邮
  bool isFreeShipping = 10;
  // 活动状态
  string status = 11;
  // 是否允许使用优惠券
  bool canUseCoupon = 12;
  // 是否允许使用储值卡
  bool canUsePrepaidCard = 13;
  // 是否只能使用一种优惠
  bool isDiscountLimit = 14;
  // 参与客户限制
  mairpc.common.ec.MemberFilter memberLimit = 15;
  // 当前客户是否能参加
  bool canMemberJoin = 16;
  // 是否开启倒计时
  bool countdownEnabled = 17;
  // 提前预告时间，单位：秒，0 表示不开启提前预告
  int64 noticeBeforeStartTime = 18;
  // 分享描述
  string shareDescription = 19;
  // 分享图片
  string sharePicture = 20;
  // 储值卡限制
  mairpc.common.ec.PrepaidCardLimit prepaidCardLimit = 21;
  // 是否开启弹窗
  bool isModalEnabled = 22;
}

message ProductDetail {
  // 商品 id
  string id = 1;
  // 商品 sku 列表
  repeated ProductSku skus = 2;
  // 商品图片
  string picture = 3;
  // 商品名
  string name = 4;
  // 商品类型
  //
  // 实物商品（product）、虚拟商品（virtual）、付费卡券（coupon）
  string type = 5;
  // 上架状态
  //
  // 已上架（shelved）、待上架（scheduled）、已下架（unshelved）、未上架（initialized）
  string shelveStatus = 6;
}

message PlusBuyProductDetail {
  // 商品 id
  string id = 1;
  // 商品 sku
  string sku = 2;
  // 商品规格
  repeated string properties = 3;
  // 商品原价
  //
  // 单位：分
  uint64 price = 4;
  // 换购价格
  uint64 plusBuyPrice = 5;
  // 商品图片
  string picture = 6;
  // 商品名
  string name = 7;
  // 商品类型
  //
  // 实物商品（product）、虚拟商品（virtual）、付费卡券（coupon）
  string type = 8;
  // 上架状态
  //
  // 已上架（shelved）、待上架（scheduled）、已下架（unshelved）、未上架（initialized）
  string shelveStatus = 9;
  // 库存
  uint64 stock = 10;
  // 每人限购
  uint64 personalLimitCount = 11;
}

message ProductSku {
  // 商品 sku
  string sku = 1;
  // 商品规格
  repeated string properties = 2;
  // 商品原价
  //
  // 单位：分
  uint64 price = 3;
}

message LisPlusBuyRecordsRequest {
  // 活动 id
  string id = 1;
  // 搜索类型
  //
  // member 客户姓名，order 订单编号
  string searchType = 2; // valid:"in(member|order)"
  // 搜索内容
  string searchKey = 3;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 4;
}

message LisPlusBuyRecordsResponse {
  repeated PlusBuyRecord items = 1;
  uint64 total = 2;
}

message PlusBuyRecord {
  // 客户 id
  string memberId = 1;
  // 客户姓名
  string memberName = 2;
  // 订单 id
  string orderId = 3;
  // 订单编号
  string orderNumber = 4;
  // 下单时间
  string createdAt = 5;
  // 实付金额
  //
  // 单位：分
  uint64 payAmount = 6;
}

message GetPlusBuyProductsRequest {
  // 活动 id
  string id = 1; // valid:"required,objectId"
  // 门店 id
  string storeId = 2; // valid:"required,objectId"
  // 查询数量
  uint64 size = 3;
  // 上次查询到换购商品的 sku
  string cursor = 4;
  // 搜索的条件
  string queryString = 5;
}

message GetPlusBuyProductsResponse {
  repeated PlusBuyProducts items = 1;
  bool hasEnd = 2;
}

message PlusBuyProducts {
  // 商品 id
  string id = 1;
  // 商品sku
  string sku = 2;
  // 商品原价
  //
  // 单位：分
  uint64 originPrice = 3;
  // 换购价格
  //
  // 单位：分
  uint64 price = 4;
  // 商品图片
  string picture = 5;
  // 商品名
  string name = 6;
  // 商品规格
  repeated string properties = 7;
}

message ImportPlusBuyProductsRequest {
  // @required
  //
  // 文件路径
  string fileUrl = 1; // valid:"required"
  // @required
  //
  // 超值换购活动类型
  //
  // 可选值：campaign 活动商品，plusBuy 换购商品
  string productType = 2; // valid:"required,in(campaign|plusBuy)"
  // @required
  //
  // 校验的时间区间
  mairpc.common.types.StringDateRange timeRange = 3;
  // 超值换购活动 id
  string plusBuyId = 4; // valid:"objectId"
}

message ExportPlusBuyCampaignProductsRequest {
  // 商品信息
  repeated CampaignProductDetail productDetails = 1;
  // 类型
  //
  // 超值换购活动商品 campaign 换购商品 plusBuy
  string type = 2;
  // 活动 id
  string campaignId = 3; // valid:"objectId"
}

message CampaignProductDetail {
  // @required
  //
  // 商品 sku
  string sku = 1;
  // 价格（单位：分）
  int64 price = 2;
}

message OrderProductInfo {
  string sku = 1;
  uint64 total = 2;
}

message JoinPlusBuyRequest {
  // @required
  //
  // 活动 id
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 客户 id
  string memberId = 2; // valid:"required,objectId"
  // @required
  //
  // 订单号
  string orderNumber = 3; // valid:"required"
  // @required
  //
  // 订单商品信息
  repeated OrderProductInfo orderProductInfos = 4; // valid:"required"
}

message ProductLimitStat {
  string sku = 1;
  // 已购数量
  uint64 count = 2;
  // 可购数量
  uint64 remaining = 3;
}

message MemberRecordDetail {
  // 商品购买统计
  repeated ProductLimitStat productStats = 1;
}
