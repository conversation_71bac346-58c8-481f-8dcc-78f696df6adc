type: google.api.Service
config_version: 3

http:
  rules:
    # 超值换购活动详情
    - selector: mairpc.eccampaign.plus_buy.PlusBuyService.GetPlusBuy
      get: /v2/eccampaign/plusBuys/{id}
      filterMark: backend
      tags: ['私域商城-超值换购']
    # 换购商品列表
    - selector: mairpc.eccampaign.plus_buy.PlusBuyService.GetPlusBuyProducts
      get: /v2/eccampaign/plusBuys/{id}/products
      filterMark: backend
      tags: ['私域商城-超值换购']
    - selector: mairpc.eccampaign.plus_buy.PlusBuyService.ListPlusBuys
      get: /v2/eccampaign/plusBuys
      filterMark: backend
      tags: ['私域商城-超值换购']
    - selector: mairpc.eccampaign.plus_buy.PlusBuyService.GetMemberRecord
      get: /v2/eccampaign/plusBuys/{id}/memberRecord
      filterMark: backend
      tags: ['私域商城-超值换购']
