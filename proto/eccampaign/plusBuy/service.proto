syntax = "proto3";

package mairpc.eccampaign.plus_buy;

option go_package = "plus_buy";

import "common/request/request.proto";
import "common/response/response.proto";
import "eccampaign/plusBuy/plus_buy.proto";

service PlusBuyService {
  // 创建超值换购活动
  rpc CreatePlusBuy(CreatePlusBuyRequest) returns (CreatePlusBuyResponse);
  // 删除超值换购活动
  rpc DeletePlusBuy(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 修改超值换购活动
  rpc UpdatePlusBuy(UpdatePlusBuyRequest) returns (UpdatePlusBuyResponse);
  // 超值换购列表
  rpc ListPlusBuys(ListPlusBuysRequest) returns (ListPlusBuysResponse);
  // 超值换购详情
  rpc GetPlusBuy(GetPlusBuyRequest) returns (GetPlusBuyResponse);
  // 超值换购活动记录
  rpc LisPlusBuyRecords(LisPlusBuyRecordsRequest) returns (LisPlusBuyRecordsResponse);
  // 导出超值换购参与记录
  rpc ExportPlusBuyRecords(LisPlusBuyRecordsRequest) returns (mairpc.common.response.JobResponse);
  // 换购商品列表
  rpc GetPlusBuyProducts(GetPlusBuyProductsRequest) returns (GetPlusBuyProductsResponse);
  // 导入活动商品
  rpc ImportPlusBuyProducts(ImportPlusBuyProductsRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 导出超值换购活动商品
  rpc ExportPlusBuyCampaignProducts(ExportPlusBuyCampaignProductsRequest) returns (mairpc.common.response.JobResponse);
  // 参与换购
  rpc JoinPlusBuy(JoinPlusBuyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取客户在某个活动中的换购数量统计
  rpc GetMemberRecord(mairpc.common.request.DetailWithMemberIdRequest) returns (MemberRecordDetail);
}
