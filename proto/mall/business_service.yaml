type: google.api.Service
config_version: 3

http:
  rules:
    - selector: mairpc.mall.MallService.OrderList
      get: /v2/search/orders
      filterMark: openapi
      tags: ['积分商城']
      scope: ['app']
      hideResponseFields: 'orders.address.id,orders.address.accountId,orders.address.memberId,orders.address.isDefault'
    - selector: mairpc.mall.MallService.OrderDelivered
      put: /v2/orders/{orderId}/delivery
      body: '*'
      filterMark: openapi
      tags: ['积分商城']
      scope: ['app']
    - selector: mairpc.mall.MallService.GetOrder
      get: /v2/orders/{id}
      hideRequestFields: memberId
      tags: ['积分商城']
      sensitiveFields: 'address.name,address.phone,address.detail'
      filterMark: openapi
      scope: ['app']
      hideResponseFields: 'address.id,address.accountId,address.memberId,address.isDefault'
    - selector: mairpc.mall.MallService.RefundOrder
      put: /v2/orders/{id}/refund
      body: '*'
      filterMark: openapi
      tags: ['积分商城']
      scope: ['app']
    - selector: mairpc.mall.MallService.SearchGroupBuyingOrder
      get: /v2/search/groupBuyingOrders
      filterMark: openapi
      tags: ['积分商城']
      scope: ['app']
    - selector: mairpc.mall.MallService.BatchCreateStockWarningReceivers
      post: /v2/stockWarningReceivers
      body: '*'
      filterMark: openapi
      tags: ['积分商城']
      scope: ['app']
    - selector: mairpc.mall.MallService.DeleteStockWarningReceiver
      delete: /v2/stockWarningReceivers/{id}
      filterMark: openapi
      tags: ['积分商城']
      scope: ['app']
    - selector: mairpc.mall.MallService.ListStockWarningReceivers
      get: /v2/stockWarningReceivers
      filterMark: openapi
      tags: ['积分商城']
      scope: ['app']
    - selector: mairpc.mall.MallService.UpdateGroupBuyingOrderStatus
      put: /v2/groupBuyingOrders/{id}/status
      body: '*'
      filterMark: openapi
      tags: ['积分商城']
      scope: ['app']
    - selector: mairpc.mall.MallService.GetOrderTrace
      get: /v2/orders/{id}/trace
      filterMark: openapi
      tags: ['积分商城']
      scope: ['app']
    - selector: mairpc.mall.MallService.CreateGoods
      post: /v2/goods
      body: '*'
      filterMark: backend
      tags: ['积分商城']
    - selector: mairpc.mall.MallService.UpdateGoods
      put: /v2/goods/{id}
      body: '*'
      filterMark: backend
      tags: ['积分商城']
    - selector: mairpc.mall.MallService.GetGoods
      get: /v2/goods/{id}
      filterMark: backend
      tags: ['积分商城']
      scope: ['app']
    - selector: mairpc.mall.MallService.SearchGoods
      get: /v2/goods
      filterMark: backend
      tags: ['积分商城']
      scope: ['app']
    - selector: mairpc.mall.MallService.SetGoodsStatus
      post: /v2/goods/status
      body: '*'
      filterMark: backend
      tags: ['积分商城']
    - selector: mairpc.mall.MallService.UpsertSetting
      post: /v2/mall/upsertSetting
      body: '*'
      filterMark: backend
      tags: ['积分商城']
    - selector: mairpc.mall.MallService.GetSetting
      get: /v2/mall/setting
      filterMark: backend
      tags: ['积分商城']
    - selector: mairpc.mall.MallService.UpdateGoodsStocks
      put: /v2/goods/{id}/stocks
      body: '*'
      filterMark: backend
      tags: ['积分商城']
    - selector: mairpc.mall.MallService.AuditComment
      put: /v2/goodsComments/audit
      body: '*'
      filterMark: backend
      tags: ['积分商城']
    - selector: mairpc.mall.MallService.ListComments
      get: /v2/goodsComments
      filterMark: backend
      tags: ['积分商城']
    - selector: mairpc.mall.MallService.UpdateOrderExternalOrder
      post: /v2/orders/{id}/externalOrder
      body: '*'
      filterMark: backend
      tags: ['积分商城']
      scope: ['app']
    - selector: mairpc.mall.MallService.UpdateOrderProductSpecs
      post: /v2/orders/{orderId}/changeSpec
      body: '*'
      filterMark: openapi
      tags: ['积分商城']
      scope: ['app']
    - selector: mairpc.mall.MallService.AuditOrders
      post: /v2/orders/audit
      body: '*'
      filterMark: backend
      tags: ['积分商城']
      scope: ['app']
    - selector: mairpc.mall.MallService.GetOrderSensitive
      get: /v2/orders/{orderId}/sensitive
      tags: ['积分商城']
      filterMark: backend
      scope: ['app']
