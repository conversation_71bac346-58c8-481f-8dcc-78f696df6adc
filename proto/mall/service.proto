syntax = "proto3";

package mairpc.mall;

option go_package = "mall";

import "common/benchmark/benchmark.proto";
import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/response/response.proto";
import "mall/comment.proto";
import "mall/delivery_setting.proto";
import "mall/goods.proto";
import "mall/group_buying.proto";
import "mall/order.proto";
import "mall/setting.proto";
import "mall/shelf.proto";
import "mall/shopping_cart.proto";

service MallService {
  rpc Benchmark(mairpc.common.benchmark.BenchmarkRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取货架列表
  rpc GetShelves(GetShelvesRequest) returns (GetShelvesResponse);
  // 获取货架详情
  rpc GetShelf(mairpc.common.request.DetailRequest) returns (Shelf);
  // 获取商品列表
  rpc SearchGoods(SearchGoodsRequest) returns (SearchGoodsResponse);
  // 获取商品详情
  rpc GetGoods(mairpc.common.request.DetailRequest) returns (GoodsDetail);
  // 创建商品
  rpc CreateGoods(CreateGoodsRequest) returns (mairpc.common.response.EmptyResponse);
  // 更改商品
  rpc UpdateGoods(UpdateGoodsRequest) returns (mairpc.common.response.EmptyResponse);
  // 更改商城设置
  rpc UpsertSetting(UpsertMallSettingRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取商城设置
  rpc GetSetting(mairpc.common.request.EmptyRequest) returns (SettingDetail);
  // 更改商品上下架状态
  rpc SetGoodsStatus(SetGoodsStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 检查更改商品上下架状态
  rpc CheckAndUpdateGoodsStatus(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 更改商品库存
  rpc UpdateGoodsStocks(UpdateGoodsStocksRequest) returns (mairpc.common.response.EmptyResponse);
  // 收藏商品
  rpc CreateFavoriteGoods(FavoriteGoodsRequest) returns (mairpc.common.response.EmptyResponse);
  // 取消收藏
  rpc DeleteFavoriteGoods(FavoriteGoodsRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取收藏列表
  rpc ListFavoriteGoods(ListFavoriteGoodsRequest) returns (FavoriteGoodsListResponse);
  // 获取商品收藏状态
  rpc GetGoodsFavoriteStatus(mairpc.common.request.DetailWithMemberIdRequest) returns (GoodsFavoriteStatusResponse);
  // 评价商品
  rpc CreateComment(CreateCommentRequest) returns (mairpc.common.response.EmptyResponse);
  // 商品评价列表
  rpc ListComments(ListCommentsRequest) returns (ListCommentsResponse);
  // 审核评价信息
  rpc AuditComment(AuditCommentRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建购物车
  rpc CreateShoppingCart(CreateShoppingCartRequest) returns (CreateShoppingCartResponse);
  // 获取购物车列表
  rpc GetShoppingCarts(GetShoppingCartsRequest) returns (GetShoppingCartsResponse);
  // 编辑购物车
  rpc UpdateShoppingCart(UpdateShoppingCartRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除购物车
  rpc DeleteShoppingCart(DeleteShoppingCartRequest) returns (mairpc.common.response.EmptyResponse);
  // 全选/取消全选购物车
  rpc CheckAllShoppingCarts(CheckAllShoppingCartsRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建订单
  rpc CreateOrder(CreateOrderRequest) returns (OrderDetail);
  // 直接购买
  rpc Purchase(PurchaseRequest) returns (OrderDetail);
  // 获取订单详情
  rpc GetOrder(GetOrderRequest) returns (OrderDetail);
  // 通过外部订单号获取订单详情
  rpc GetOrderByExternalOrderId(GetOrderByExternalOrderIdRequest) returns (OrderDetail);
  // 获取订单列表
  rpc OrderList(OrderListRequest) returns (OrderListResponse);
  // 支付订单
  rpc PrePay(PrePayRequest) returns (PrePayResponse);
  // 批量创建库存预警接收人
  rpc BatchCreateStockWarningReceivers(BatchCreateStockWarningReceiverRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除库存预警接收人
  rpc DeleteStockWarningReceiver(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取库存预警接收人列表
  rpc ListStockWarningReceivers(ListStockWarningReceiversRequest) returns (FindStockWarningReceiverResponse);
  // 更新订单
  rpc UpdateOrderExtra(UpdateOrderExtraRequest) returns (mairpc.common.response.EmptyResponse);
  // 取消订单
  rpc CancelOrder(mairpc.common.request.DetailWithMemberIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除订单
  rpc DeleteOrder(mairpc.common.request.DetailWithMemberIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 确认收货
  rpc ConfirmReceived(mairpc.common.request.DetailWithMemberIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理支付通知
  rpc HandlePaymentNotification(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 订单发货
  //
  // 支持对已支付的订单发货, 或修改已发货订单的物流信息
  rpc OrderDelivered(OrderDeliveredRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取送货设置
  rpc GetDeliverySetting(mairpc.common.request.EmptyRequest) returns (DeliverySettingDetail);
  // 订单退货
  //
  // 支持将 已支付、已发货、已完成 的订单状态改为 退货中
  // 支持将 退货中 的订单状态改为 已退货
  // 不支持其它状态改变.
  rpc RefundOrder(RefundOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建拼团活动
  rpc CreateGroupBuyingCampaign(CreateGroupBuyingCampaignRequest) returns (GroupBuyingCampaign);
  // 编辑拼团活动
  rpc UpdateGroupBuyingCampaign(UpdateGroupBuyingCampaignRequest) returns (mairpc.common.response.EmptyResponse);
  // 终止拼团活动
  rpc TerminateGroupBuyingCampaign(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 拼团活动列表
  rpc SearchGroupBuyingCampaign(SearchGroupBuyingCampaignRequest) returns (GroupBuyingCampaignList);
  // 拼团活动详情
  rpc GetGroupBuyingCampaign(mairpc.common.request.DetailRequest) returns (GroupBuyingCampaign);
  // 团购列表
  //
  // 展示系统中的拼团活动
  rpc SearchGroupBuyingOrder(SearchGroupBuyingOrderRequest) returns (GroupBuyingOrderList);
  // 团购详情
  rpc GetGroupBuyingOrder(mairpc.common.request.DetailRequest) returns (GroupBuyingOrder);
  // 团购订单退款
  //
  // 仅适用于团购订单。若已付款，则会取消订单并触发退款流程。若未付款，则取消订单。
  rpc TriggerRefund(TriggerRefundRequest) returns (TriggerRefundResponse);
  // 获取订单退款状态
  //
  // 调用支付渠道的接口来获取团购订单退款的状态，仅适用于团购订单
  rpc GetRefundStatus(mairpc.common.request.DetailRequest) returns (GetRefundStatusResponse);
  // 批量团购订单退款
  //
  // 仅供job使用，批量获取过期的团购，修改其状态并将那些属于该团购的订单退款。
  rpc BatchTriggerRefund(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量获取退款状态
  //
  // 仅供job使用，获取处于退款中的订单，查询并更新它们的退款状态
  rpc BatchGetRefundStatus(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取进行中的拼团活动
  //
  // 返回值中会带有拼团活动所属商品的详细信息
  rpc SearchGroupBuyingCampaignWithGoodsDetail(SearchGroupBuyingCampaignWithGoodsDetailRequest) returns (SearchGroupBuyingCampaignWithGoodsDetailResponse);
  // 更新团购状态
  rpc UpdateGroupBuyingOrderStatus(UpdateGroupBuyingOrderStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取订单物流轨迹
  rpc GetOrderTrace(GetOrderTraceRequest) returns (GetOrderTraceResponse);
  // 获取客户优惠券列表
  rpc GetMembershipDiscounts(GetMembershipDiscountsRequest) returns (GetMembershipDiscountsResponse);
  // 推送订单到第三方丽人丽妆 oms
  rpc PushMallOrdersToOmsJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步第三方丽人丽妆 oms 订单状态及物流信息
  rpc SyncMallStatusFromOmsJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 推送拦截订单到第三方丽人丽妆 oms
  rpc InterceptMallOrdersToOmsJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 即时同步第三方丽人丽妆 oms 订单发货信息
  rpc HandleTradeStatusInformFromOms(TradeStatusInformRequest) returns (TradeStatusInformResponse);
  // 更新订单商品规格
  rpc UpdateOrderProductSpecs(UpdateOrderProductSpecsRequest) returns (mairpc.common.response.EmptyResponse);
  // 自动完成订单
  rpc AutoCompleteOrderJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 更改订单外部订单号
  rpc UpdateOrderExternalOrder(UpdateOrderExternalOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 检测用户是否可购买商品
  rpc CheckMemberCanPurchase(mairpc.common.request.MemberIdRequest) returns (CheckMemberCanPurchaseResponse);
  // 检测用户是否可购买商品
  rpc CheckCanPurchase(CheckCanPurchaseRequest) returns (CheckCanPurchaseResponse);
  rpc PreCheckBeforePay(CheckCanPurchaseRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量审核订单
  rpc AuditOrders(AuditOrdersRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理订单中会员 Block 状态
  rpc HandleOrderMemberBlockedStatusWebhook(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理商品更新事件
  rpc HandleProductUpdateWebhook(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 检查源数据 Product/Coupon 不存在的 Goods
  rpc DeleteNonexistentSourceGoods(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取订单敏感信息
  rpc GetOrderSensitive(GetOrderSensitiveRequest) returns (mairpc.common.ec.GetSensitiveResponse);
  // 检查用户是否满足兑换人群限制
  rpc CheckMemberLimitation(CheckMemberLimitationRequest) returns (mairpc.common.response.EmptyResponse);
  // 发送库存预警邮件
  rpc SendStockWarningEmail(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
}
