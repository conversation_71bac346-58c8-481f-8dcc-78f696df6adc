syntax = "proto3";

package mairpc.mall;

option go_package = "mall";

import "common/request/request.proto";

message SettingDetail {
  // 商城设置 ID
  string id = 1;
  // 是否已删除
  bool isDeleted = 2;
  // 创建时间
  int64 createdAt = 3;
  // 修改时间
  int64 updatedAt = 4;
  // 商品设置
  GoodsSetting goods = 5;
  // 交易设置
  TradeSetting trade = 6;
}

message ListStockWarningReceiversRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
}

message StockWarningReceiver {
  // 审核人 ID
  string id = 1;
  // 审核人邮箱
  string email = 2;
  // 审核人手机号
  string phone = 3;
  // 创建时间
  string createdAt = 4;
  // 用户 ID
  string userId = 5;
  // 审核人名称
  string name = 6;
}

message BatchCreateStockWarningReceiverRequest {
  // @required
  //
  // 用户 ID 列表
  repeated string userIds = 1; // valid:"required,objectIdList"
}

message FindStockWarningReceiverRequest {
  int32 page = 1;
  int32 pageSize = 2;
}

message FindStockWarningReceiverResponse {
  repeated StockWarningReceiver receivers = 1;
  int32 total = 2;
}

message UpsertMallSettingRequest {
  // 商品设置
  GoodsSetting goods = 1;
  // 交易设置
  TradeSetting trade = 6; // valid:"optional"
  // 库存预警接收人
  repeated StockWarningReceiver stockWarningReceivers = 7;
}

message GoodsSetting {
  // 是否允许商品自动下架（库存为0）
  bool autoOffShlfeOOS = 1;
  SoldCountVisibleSetting soldCountVisibleSetting = 2;
  bool stockVisible = 3;
  // 是否启用库存预警
  bool stockWarningEnabled = 4;
  // 库存预警阈值
  uint64 stockWarningThreshold = 5;
}

message SoldCountVisibleSetting {
  bool invisible = 1;
  bool thresholdEnabled = 2;
  uint64 threshold = 3;
}

message TradeSetting {
  // 支付设置
  PaySetting pay = 1;
  // 退款设置
  RefundSetting refund = 2;
  // 自动完成设置
  CompleteSetting complete = 3;
  // 兑换设置
  PurchaseSetting purchase = 4; // valid:"optional"
  // 审核设置
  AuditSetting audit = 5;
}

message PaySetting {
  // 允许使用优惠券抵扣现金
  bool isAllowedExchangeCouponToCash = 1;
  // 允许使用优惠券抵扣积分
  bool isAllowedExchangeCouponToPoints = 2;
}

message RefundSetting {
  // 是否回退积分
  bool isRollbackCashAndPoints = 1;
}

message PurchaseSetting {
  // 是否允许非会员购买商品
  bool isAllowedNonMemberToPurchase = 1;
  // 是否开启积分限制
  bool isScoreLimitationEnabled = 2;
  ScoreLimitation scoreLimitation = 3; // valid:"optional"
  // 是否开启兑换数量限制
  bool purchaseQuantityLimitEnabled = 4;
  PurchaseQuantityLimitation purchaseQuantityLimitation = 5; // valid:"optional"
}

message ScoreLimitation {
  // 积分兑换时间配置，每日：'DAILY',每周：'WEEKLY' or 每月：'MONTHLY'
  string type = 1; // valid:"in(DAILY|WEEKLY|MONTHLY)"
  // 积分限制兑换数量
  uint64 num = 2;
}

message PurchaseQuantityLimitation {
  // 数量兑换时间范围配置，每日：'DAILY'、每周：'WEEKLY'、 每月：'MONTHLY'
  string type = 1; // valid:"in(DAILY|WEEKLY|MONTHLY)"
  // 兑换限制数量
  uint64 num = 2;
}

message CompleteSetting {
  // 自动确认收货时间（分钟）
  uint64 daysOfAutoCompleteExpressOrder = 1;
  // 上门自提自动确认收货时间（分钟）
  uint64 daysOfAutoCompletePickupOrder = 2;
  // 无需物流商品自动完成订单时间（分钟）
  uint64 daysOfAutoCompleteNoExpressOrder = 3;
}

message AuditSetting {
  bool enabled = 1;
  // 需要审核的会员状态，all、suspicious
  repeated string needAuditMemberBlockedStatuses = 2;
}

message CheckMemberCanPurchaseResponse {
  // 该用户能否购买
  bool canPurchase = 1;
  // 不能购买的原因，activatedMemberOnly
  string reason = 2;
}
