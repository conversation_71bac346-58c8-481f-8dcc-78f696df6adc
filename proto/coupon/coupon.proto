syntax = "proto3";

package mairpc.coupon;

option go_package = "coupon";

import "common/request/request.proto";
import "common/types/types.proto";
import "coupon/coupon_audit.proto";

message CouponDetailRequest {
  // @required
  //
  // 优惠券ID
  string id = 1;
  // 是否包含已删除
  bool containsDeleted = 2;
  // 额外信息
  //
  // auditInfo（返回优惠券审核相关信息），skuDetail（返回适用商品规格详情）
  repeated string extra = 3;
}

message BasicMember {
  // 客户ID
  string id = 1;
  // 客户姓名
  string name = 2;
}

message Cash {
  string amount = 1;
  string threshold = 2;
}

message CouponStore {
  string id = 1;
  string name = 2;
  string branchName = 3;
  string address = 4;
  string phone = 5;
  string image = 6;
}

message CouponQrcode {
  // 二维码ID
  string id = 1;
  // 来源
  string origin = 2;
  // 渠道名称
  string channelName = 3;
  // 渠道ID
  string channelId = 4;
  // 地址
  string url = 5;
}

message GetMembershipDiscountRequest {
  // @required
  //
  // 记录 id 或者券码
  string id = 1;
  // 额外信息
  //
  // skuDetail（返回适用商品规格详情）
  repeated string extra = 2;
}

message MemberCoupon {
  string id = 1;
  string title = 2;
  string image = 3;
  string qrcodeImage = 4;
  string validFrom = 5;
  string expiredAt = 6;
  string status = 7;
  BasicMember member = 8;
  string description = 9;
  string usageNotes = 10;
  string phone = 11;
  string type = 12;
  repeated CouponStore stores = 13;
  Cash cash = 14;
  string discount = 15;
  bool allAvailable = 16;
  string storeId = 17;
  string remarks = 18;
}

message CouponInfo {
  // 优惠券ID
  string id = 1;
  // 优惠券类型
  string type = 2;
  // 优惠券名称
  string title = 3;
  // 图片
  string image = 4;
  // 描述
  string description = 5;
  // 使用须知
  string usageNote = 6;
  // 折扣额度
  double discountAmount = 7;
  // 最低消费
  //
  // 即使用条件
  double cashThreshold = 8;
  // 减免金额
  double cashAmount = 9;
  // 创建时间
  string createdAt = 10;
  // 是否支持自助核销
  bool isSelfRedemption = 11;
  // 适用商品ID
  string applicableProductId = 12;
  // 适用门店规则
  ApplicableStore applicableStore = 13;
  // 备注
  string remarks = 14;
  string tip = 15;
}

message IssueCouponResponse {
  // 券码
  string code = 1;
  // 状态
  string status = 2;
  // 生效时间
  string validFrom = 3;
  // 过期时间
  string expiredAt = 4;
  // 二维码图片
  string qrcodeImage = 5;
  // 优惠券信息
  CouponInfo coupon = 6;
  // 客户信息
  BasicMember member = 7;
  // 创建时间
  string createdAt = 8;
  // 会员券ID
  string id = 9;
  // 场景ID
  string sceneId = 10;
  // 门店ID
  string storeId = 11;
  // 导购ID
  string staffId = 12;
  // 是否刚好达到领取上限
  bool isLimitReached = 13;
}

message CouponMember {
  // ID
  string id = 1;
  // 姓名
  string name = 2;
  // 手机号
  string phone = 3;
  // 昵称
  string nickname = 4;
  // 头像
  string avatar = 5;
}

message IssueCouponRequest {
  // @required
  //
  // 优惠券ID
  string couponId = 1;
  // 渠道ID
  string channelId = 2;
  // @required
  //
  // 客户ID
  string memberId = 3;
  // 发放方式
  //
  // 比如 game_reward, 默认为 receive
  string receiveType = 4;
  // 是否检查领取个数限制
  bool checkLimit = 5;
  // 是否标记为系统发放
  bool isSystem = 6;
  // 场景ID
  string sceneId = 7;
  // 门店ID
  string storeId = 9;
  // 导购ID
  string staffId = 10; // valid:"objectId"
  // 券码
  //
  // 传了券码可不传 couponId，但两者不可同时为空
  string couponCode = 11;
  // 访问来源
  mairpc.common.types.Utm utm = 12;
  // 兑换码活动 ID
  string couponEndpointId = 13; // valid:"objectId"
  // 兑换码
  string exchangeCode = 14;
  // 指定券码前缀（仅限随机生成的券码）
  string couponCodePrefix = 15;
  // 扩展数据，json 字符串
  string extra = 16;
}

message CouponLogRequest {
  string memberId = 1;
  string ruleName = 2;
  int64 startTime = 3;
}

message CouponLogIdRequest {
  string id = 1; // valid:"required,length(24|24),hexadecimal"
}

message CouponLogResponse {
  string couponId = 1;
  BasicMember member = 2;
}

message GetMemberCouponsRequest {
  string id = 1;
  string couponType = 2;
  string couponStatus = 3;
  uint32 page = 4;
  uint32 perPage = 5;
}

message GetMemberCouponsResponse {
  repeated IssueCouponResponse couponList = 1;
  int64 total = 2;
}

message GetMemberCouponRequest {
  string code = 1;
  bool includeDeleted = 2;
}

message RedeemedCouponLog {
  string couponId = 1;
  CouponMember member = 2;
  string name = 3;
  string type = 4;
  uint32 redeemedCount = 5;
  string redeemedAt = 6;
  string redeemedBy = 7;
  string membershipDiscountId = 8;
  string orderNumber = 9;
  string id = 10;
}

message RedeemedLogResponse {
  repeated RedeemedCouponLog items = 1;
  uint64 total = 2;
  uint32 page = 3;
  uint32 perPage = 4;
}

message RedeemedCouponRequest {
  // @required
  //
  // 券码
  string code = 1;
  // 门店ID
  string storeId = 2;
  // 店员ID
  string staffId = 3;
  // @required
  //
  // 核销客户 ID
  string redeemedBy = 4;
  // 用券订单编号
  //
  // 若是在某个订单中使用了优惠券，需要提供订单编号
  string orderNumber = 5;
  // 用券订单 ID
  //
  // 若是在某个订单中使用了优惠券，需要提供订单 ID
  string orderId = 6;
  // 用券商品
  CouponProduct product = 7;
  // 访问来源
  mairpc.common.types.Utm utm = 8;
  // 优惠金额（单位：分）
  int64 couponDiscountAmount = 9;
  // 不同步核销至第三方
  bool noSyncToThirdParty = 10;
}

message CouponProduct {
  // 商品 ID
  string id = 1; // valid:"objectId"
  // 商品 SKU
  string sku = 2;
}

message RollbackReceivedCouponRequest {
  // @required
  //
  // 已领取的优惠券记录 Id
  string membershipDiscountId = 1; // valid:"required,objectId"
}

message RollbackReceivedCouponToInvalidRequest {
  // @required
  //
  // 已领取的优惠券记录 Id
  string membershipDiscountId = 1; // valid:"required,objectId"
  // 优惠券失效原因
  string invalidReason = 2;
}

message RollbackRedeemCouponRequest {
  // 已核销的优惠券记录 Id
  string membershipDiscountId = 1; // valid:"required,length(24|24),hexadecimal"
  // 核销订单 Id
  string orderId = 2; // valid:"objectId"
  // 不同步冲正至第三方
  bool noSyncToThirdParty = 3;
}

message RedeemedCouponResponse {
  // 券码
  string code = 1;
  // 门店ID
  string storeId = 2;
}

message VerifyThirdPartyCouponRequest {
  // @required
  //
  // 券码
  string code = 1; // valid:"required"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required"
}

message CouponTime {
  // 时间区间类型 (absolute/relative/immediate/currentMonth)
  string type = 1;
  // 开始时间
  int64 beginTime = 2;
  // 结束时间
  int64 endTime = 3;
}

message CouponDetailResponse {
  // 优惠券ID
  string id = 1;
  // 优惠券类型
  string type = 2;
  // 优惠券名称
  string title = 3;
  // 图片
  string picUrl = 4;
  // 生效时间信息
  CouponTime time = 5;
  // 使用须知
  string usageNote = 6;
  // 库存类型
  string stockType = 7;
  // 库存总数
  int64 total = 8;
  // 每人接收限制
  int64 limit = 9;
  // 是否支持自助核销
  bool isSelfRedemption = 10;
  // 适用商品ID
  string applicableProductId = 11;
  // 操作提示
  string tip = 12;
  // 手机号
  string phone = 13;
  // 折扣额度
  mairpc.common.types.DoubleValue discountAmount = 14;
  // 减免金额
  mairpc.common.types.DoubleValue reductionAmount = 15;
  // 使用条件
  //
  // 即最低消费
  mairpc.common.types.DoubleValue discountCondition = 16;
  // 描述信息
  string description = 17;
  // 单个红包最小金额
  mairpc.common.types.DoubleValue redpackMinAmount = 18;
  // 单个红包最大金额
  mairpc.common.types.DoubleValue redpackMaxAmount = 19;
  // 红包总额
  mairpc.common.types.DoubleValue redpackAmount = 20;
  // 祝愿语
  string redpackWishing = 21;
  // 短链地址
  string url = 22;
  // 二维码
  repeated CouponQrcode qrcodes = 23;
  // 创建时间
  int64 createdAt = 24;
  // 更新时间
  int64 updatedAt = 25;
  // 适用门店规则
  ApplicableStore applicableStore = 26;
  ApplicableStore applicableDmsStore = 27;
  // 适用地区
  ApplicableArea applicableArea = 28;
  // 已领取数量
  uint64 receivedNum = 29;
  // 已核销数量
  uint64 redeemedNum = 30;
  // 生成类型 (dynamic/preBuild/import)
  string producer = 31;
  // 导入状态
  string importStatus = 32;
  // 优惠券源信息
  SourceCoupon source = 33;
  bool isDeleted = 34;
  // 增加库存量
  int64 increasedTotal = 35;
  // 是否已失效
  bool isExpired = 36;
  // 是否仅限发放小店使用
  bool shouldRedeemInIssuedStore = 37;
  // 适用商品
  ApplicableProduct applicableProduct = 38;
  // 是否可转赠
  bool allowGifting = 39;
  // 业务场景和自定义数据
  repeated CouponBusiness businesses = 40;
  // 备注
  string remarks = 41;
  // 开发者模式配置
  CouponDeveloperMode developerMode = 42;
  // 适用商品类目 ID
  repeated string applicableCategoryIds = 43; // valid:"objectIdList"
  // 适用商品 sku
  repeated ApplicableProductSkus applicableProductSkus = 44;
  // 赠送通知
  GiftNotice giftNotice = 45;
  // 优惠券过期通知
  ExpireNotice expireNotice = 46;
  // 是否允许公开领取
  bool isPublicCollectionEnabled = 47;
  // 每日每日接收限制
  int64 dailyLimit = 48;
  // 每人每日使用限制
  int64 dailyRedeemedLimit = 49;
  // 赠品信息
  PresentResp present = 50;
  // 红包发送人名称
  string redpackSenderName = 51;
  // 使用渠道
  string availableChannelType = 52;
  // 适用物流
  //
  // 全部配送渠道（all），快递配送（express），同城配送（cityExpress）
  string applicableMethod = 53;
  // 是否允许与其他券共用
  bool canUsedWithOthers = 54;
  // 适用组织
  ApplicableEntity applicableDistributor = 55;
  // 适用门店类型
  ApplicableEntity applicableStoreType = 56;
  // 适用门店级别
  ApplicableEntity applicableStoreLevel = 57;
  // 叠加设置
  //
  // 仅与自己叠加（self），可与其他可叠加券叠加（other）
  string superpositionType = 58;
  // 转赠未领通知
  UncollectedNotice uncollectedNotice = 59;
  // 最高优惠金额
  mairpc.common.types.DoubleValue maxDiscountAmount = 60;
  // 审核状态: draft（草稿）、pending（待审核）、normal（正常的）、rejected（拒绝通过）、stopped（停用）
  string status = 61;
  // 提交人
  string submittedBy = 62;
  // 提交人名称
  string submitterName = 63;
  // 提交时间
  string submittedAt = 64;
  // 拒绝原因
  string rejectedReason = 65;
  // 选择的审核人 ids (user.id)
  repeated string auditorIds = 66;
  // 审核人
  string auditedBy = 67;
  // 审核人名称
  string auditerName = 68;
  // 审核时间
  string auditedAt = 69;
  // 创建人
  string createdBy = 70;
  // 创建人名称
  string creatorName = 71;
  // 周期接收限制
  repeated PeriodLimit periodLimits = 72;
  // 运费设置
  string deliveryFeeType = 73;
  // 库存审核记录
  coupon.CouponStockAudit stockAudit = 74;
  // 券码导入链接
  string importUrl = 75;
  // 商家卡券有效期
  RedeemPeriod redeemPeriod = 76;
  // 领取限制
  IssueLimit issueLimit = 77;
  // 适用品牌 ID
  repeated string applicableBrandIds = 78; // valid:"objectIdList"
  // 适用标签
  ApplicableProductTag applicableProductTag = 79;
  // 折扣条件金额类型
  //
  // 订单金额（totalAmount）、实付金额（payAmount）
  string discountConditionType = 80;
  // 可叠加优惠券列表
  repeated string superpositionCouponTypes = 81;
  // 适用类目类型
  //
  // include（包含）, exclude（不包含）
  string applicableCategoryType = 82;
  // 赠品信息列表
  repeated PresentResp presents = 83;
  // 赠送回退设置
  ReturnSetting returnSetting = 84;
  // 到账通知
  ReceiveNotice receiveNotice = 85;
  // 优惠券标签 ID 列表
  repeated string couponTagIds = 86;
  // 外部系统券 code
  string thirdPartyCode = 87;
  // 外部系统券 id
  string thirdPartyId = 88;
}

message BriefCouponDetailResponse {
  // 优惠券ID
  string id = 1;
  // 优惠券名称
  string title = 2;
}

message PresentResp {
  // 商品名
  string name = 1;
  // 规格名称
  string skuName = 2;
  string picUrl = 3;
  // 赠送数量
  int64 count = 4;
  // 商品编号
  string number = 5;
  // productId
  string id = 6;
  string sku = 7;
  // 商品类型
  //
  // 实物商品（product），虚拟商品（virtual），付费卡券（coupon）
  string type = 8;
  // 是否被删除
  bool isDeleted = 9;
}

message IncCountRequest {
  string couponId = 1;
  string memberId = 2;
  uint64 limit = 3;
}

message IncCountResponse {
  bool success = 1;
}

message StatsMemberCoupon {
  string id = 1;
  string couponId = 2;
  string memberId = 3;
  int64 count = 4;
  int64 createdAt = 5;
}

message GetStatsMemberCouponRequest {
  string id = 1;
  string couponId = 2;
  string memberId = 3;
}

message Location {
  // 省/直辖市
  string province = 1;
  // 市/区
  string city = 2;
  // 县/街道
  string district = 3;
}

message QueryStoreCondition {
  // 门店筛选条件
  //
  // 是否为KA店(ka,nonka,all)
  string type = 1;
  // 地理位置
  Location location = 2;
  // 区域ID
  string areaId = 3;
}

message ApplicableStore {
  // 适用门店设置类型
  //
  // all,byConditions,byIncludedIds,byExcludedIds,byLocations
  string type = 1;
  // 适用的门店ID列表
  //
  // 当 type 为 byIncludedIds 时有效
  repeated string includedIds = 2;
  // 不适用的门店ID列表
  //
  // 当 type 为 byExcludedIds 时有效
  repeated string excludedIds = 3;
  // 按条件选择
  //
  // 当 type 为 byConditions 或 byLocations 时有效
  repeated QueryStoreCondition conditions = 4;
}

message ApplicableEntity {
  // 适用类型
  //
  // all,byIncludedIds,byExcludedIds
  string type = 1;
  // 适用的ID列表
  //
  // 当 type 为 byIncludedIds 时有效
  repeated string includedIds = 2;
  // 不适用ID列表
  //
  // 当 type 为 byExcludedIds 时有效
  repeated string excludedIds = 3;
}

message ApplicableArea {
  // 适用类型
  //
  // all: 全部区域可用；byIncludedIds：指定区域可用；byExcludedIds：指定区域不可用
  string type = 1;
  // 地区名称
  //
  // 如 江苏省 或者 江苏省:无锡市
  repeated string areas = 2;
}

message ApplicableProduct {
  // 适用商品
  //
  // include：指定商品可用；exclude：指定商品不可用
  string type = 1; // in(include|exclude)
  // 可用或者不可用的商品 ids
  //
  // 当 type 为 include 时为指定商品可用，当 type 为 exclude 时为指定商品不可用
  repeated string productIds = 2; // valid:"objectIdList"
}

message SourceCoupon {
  // 来源
  string from = 1;
  // 源优惠券ID
  string id = 2;
  // 渠道 ID
  string channelId = 3;
  // 微信 appId
  string appId = 4;
}

message CouponBusiness {
  // @required
  //
  // 优惠券业务场景
  // wechatwork：企业微信，retail-product：零售付费卡券
  string business = 1;
  // 自定义数据，json 格式的字符串，如 "{\"posterBackgroundImage\":\"http://xxx.jpg\"}"
  string extra = 2;
  // 是否启用
  bool isEnabled = 3;
  // 优惠券业务场景类型
  //
  // 当 business 是 wechatwork 时，为门店库存类型，可选值：shared（门店共享库存）、independent（门店独立库存）
  string type = 4;
}

message CouponDeveloperMode {
  // 是否开启开发者模式
  bool isEnabled = 1;
  // 领取成功页
  string successPage = 2;
}

message ApplicableProductSkus {
  // 商品 ID
  string productId = 1; // valid:"objectId"
  // 商品 sku
  repeated string skus = 2;
  // sku 详情
  //
  // 仅用作响应中
  repeated ProductSkuDetail skuDetails = 3;
  // 商品 sku
  repeated SpecSku specSkus = 4;
}

message SpecSku {
  // 商品 sku
  string sku = 1;
  // 可兑换数量
  uint32 count = 2;
}

message ProductSkuDetail {
  // 商品 sku
  string sku = 1;
  // 商品外部 sku
  string external = 2;
}

message UncollectedNotice {
  // 是否开启转赠未领提醒
  bool isEnabled = 1;
  // 时间类型
  //
  // 可选值： hour、day
  string type = 2; // valid:"in(hour|day)"
  // 时间数量
  int64 timeCount = 3;
  // 通知设置
  NoticeSetting setting = 4;
}

message ExpireNotice {
  // 是否开启过期提醒
  bool isEnabled = 1;
  // 时间类型
  //
  // 可选值： hour、day
  string type = 2; // valid:"in(hour|day)"
  // 时间数量
  int64 timeCount = 3;
  // 通知设置
  NoticeSetting setting = 4;
}

message ReceiveNotice {
  // 是否开启到账提醒
  bool isEnabled = 1;
  // 时间类型
  //
  // 可选值： hour、day、immediate
  string type = 2; // valid:"in(hour|day|immediate)"
  // 时间数量
  int64 timeCount = 3;
  // 通知设置
  NoticeSetting setting = 4;
}

message GiftNotice {
  // 是否开启优惠券被领取后通知赠送者
  bool isEnabled = 1;
  // 通知设置
  NoticeSetting setting = 2;
}

message NoticeSetting {
  // 短信
  Text text = 1;
  // 订阅消息
  SubscribeMessage subscribeMessage = 2;
  // 动态参数
  repeated PlaceholderRules placeholderRules = 3;
  // 模板消息
  TemplateMessage templateMessage = 4;
}

message Text {
  // 短信内容
  string content = 1;
  // 是否可编辑
  bool isEditable = 2;
}

message SubscribeMessage {
  // 订阅消息 id
  string id = 1;
  // 打开小程序页面
  string page = 2;
  // 订阅消息内容
  repeated Data data = 3;
  // 订阅消息标题
  string title = 4;
  // 渠道 id
  string channelId = 5;
}

message Data {
  // 订阅消息键
  string key = 1;
  // 订阅消息值
  string value = 2;
  // 名称
  string name = 3;
  // 颜色
  string color = 4;
}

message PlaceholderRules {
  // 占位符
  string placeholder = 1;
  // 填充项
  Filler filler = 2;
  // 占位符名称
  string name = 3;
}

message Filler {
  // 属性
  string property = 1;
  // map 参数值转换规则
  map<string, string> valueTransfer = 2;
  // 默认值
  string default = 3;
}

message TemplateMessage {
  // 模板ID
  string templateId = 1;
  // 跳转网页链接
  string url = 2;
  // 模板消息内容
  repeated Data data = 3;
  // 模板消息标题
  string title = 4;
  // 跳转小程序
  TemplateMessageMiniProgram miniProgram = 5;
  // 渠道ID
  string channelId = 6;
}

message TemplateMessageMiniProgram {
  // 小程序 AppId
  string appId = 1;
  // 页面路径
  string pagePath = 2;
}

message CreateCouponRequest {
  string type = 1;
  string title = 2;
  string picUrl = 3;
  CouponTime time = 4;
  string usageNote = 5;
  string stockType = 6;
  int64 total = 7;
  int64 limit = 8;
  bool isSelfRedemption = 9;
  string applicableProductId = 10;
  string tip = 11;
  string phone = 12;
  mairpc.common.types.DoubleValue discountAmount = 13;
  mairpc.common.types.DoubleValue reductionAmount = 14;
  mairpc.common.types.DoubleValue discountCondition = 15;
  string description = 16;
  mairpc.common.types.DoubleValue redpackMinAmount = 17;
  mairpc.common.types.DoubleValue redpackMaxAmount = 18;
  mairpc.common.types.DoubleValue redpackAmount = 19;
  string redpackWishing = 20;
  ApplicableStore applicableStore = 21;
  ApplicableStore applicableDmsStore = 22;
  ApplicableArea applicableArea = 23;
  string producer = 24;
  string importStatus = 25;
  SourceCoupon source = 26;
  bool shouldRedeemInIssuedStore = 27;
  ApplicableProduct applicableProduct = 28;
  bool allowGifting = 29;
  // 优惠券业务场景和自定义数据
  //
  // 业务场景不允许重复，创建优惠券时传入的重复场景会根据场景名称进行去重，排在后面的场景和自定义数据会覆盖前面的
  repeated CouponBusiness businesses = 30;
  string remarks = 31;
  CouponDeveloperMode developerMode = 32;
  repeated string applicableCategoryIds = 33; // valid:"objectIdList"
  repeated ApplicableProductSkus applicableProductSkus = 34;
  GiftNotice giftNotice = 35;
  ExpireNotice expireNotice = 36;
  bool isPublicCollectionEnabled = 37;
  // 每人每日接收限制
  int64 dailyLimit = 38;
  // 每人每日使用限制
  int64 dailyRedeemedLimit = 39;
  Present present = 40;
  // 红包发送人名称
  string redpackSenderName = 41;
  // 使用渠道
  //
  // 可选值：online（线上）、online_and_offline（线上线下）、offline(线下)、unavailable（线上线下均不可用）
  string availableChannelType = 42; // valid:"in(online|online_and_offline|offline|unavailable)"
  // 适用物流
  //
  // 全部配送渠道（all），快递配送（express），同城配送（cityExpress）
  string applicableMethod = 43; // valid:"in(all|express|cityExpress)"
  // 是否允许与其他券共用
  bool canUsedWithOthers = 44;
  // 适用组织
  ApplicableEntity applicableDistributor = 45;
  // 适用门店类型
  ApplicableEntity applicableStoreType = 46;
  // 适用门店级别
  ApplicableEntity applicableStoreLevel = 47;
  // 叠加设置
  //
  // 仅与自己叠加（self），可与其他可叠加券叠加（other）
  string superpositionType = 48;
  // 组织节点信息
  mairpc.common.types.DistributorInfo distributorInfo = 49; // valid:"optional"
  // 转赠未被领取通知
  UncollectedNotice uncollectedNotice = 50;
  // 最高优惠金额
  mairpc.common.types.DoubleValue maxDiscountAmount = 51;
  // 周期接收限制
  repeated PeriodLimit periodLimits = 52;
  // 运费设置
  //
  // 可选值：freeShipping（包邮）、default（使用系统运费设置）
  string deliveryFeeType = 53; // valid:"in(freeShipping|default)"
  // 状态
  //
  // 可选值: normal（正常的，用户可直接发放领取）
  string status = 54; // valid:"in(normal)"
  // 券码导入链接
  string importUrl = 55;
  // 用于商家券有效期
  RedeemPeriod redeemPeriod = 56;
  // 领取限制
  IssueLimit issueLimit = 57;
  // 适用品牌 ID
  repeated string applicableBrandIds = 58; // valid:"objectIdList"
  // 适用标签
  ApplicableProductTag applicableProductTag = 59; // valid:"optional"
  // 折扣条件金额类型
  //
  // 订单金额（totalAmount）、实付金额（payAmount）
  string discountConditionType = 60;
  // 可叠加优惠券类型列表
  //
  // 代金券（cash）、折扣券（discount）、买赠券（present）、运费券（delivery）、兑换券（exchange）
  repeated string superpositionCouponTypes = 61; // valid:"in(cash|discount|present|delivery|exchange)"
  // 适用类目类型
  //
  // include（包含）, exclude（不包含）
  string applicableCategoryType = 62; // valid:"in(include|exclude)"
  // 赠品列表
  repeated Present presents = 63;
  // 赠送回退设置
  ReturnSetting returnSetting = 64;
  // 到账通知
  ReceiveNotice receiveNotice = 65;
  // 优惠券标签 ID 列表
  repeated string couponTagIds = 66; // valid:"objectIdList"
  // 外部系统券 code
  string thirdPartyCode = 67;
  // 外部系统券 ID
  string thirdPartyId = 68;
}

message ReturnSetting {
  // 是否开启转赠未领提醒
  bool isEnabled = 1;
  // 时间类型
  //
  // 可选值： hour、day
  string type = 2; // valid:"in(hour|day)"
  // 时间数量
  int64 timeCount = 3;
}

message ApplicableProductTag {
  // @required
  //
  // 标签适用类型，include（包含）, exclude（不包含）
  string type = 1; // valid:"required,in(include|exclude)"
  // @required
  //
  // 标签列表
  repeated string tags = 2; // valid:"required"
}

message IssueLimit {
  // 各条件之间的组合关系，and（全部满足）、or（满足任一），默认为 and
  string operator = 1; // // valid:"in(and|or)"
  // 标签限制
  TagLimit tags = 3;
  // 群组限制
  GroupLimit group = 4;
  // 会员等级限制
  LevelLimit level = 5;
}

message MemberLabel {
  string name = 1;
  // 模型标签
  string field = 2;
  // 模型标签值
  string fieldValue = 3;
}

message TagLimit {
  // 限定符，IN（在其中）、NOT_IN（不在其中）
  string operator = 1; // valid:"in(IN|NOT_IN)"
  // 标签列表
  repeated string value = 2;
  // 如果是模型标签存在这里
  repeated MemberLabel labels = 3;
}

message GroupLimit {
  // 限定符，IN（在其中）、NOT_IN（不在其中）
  string operator = 1; // valid:"in(IN|NOT_IN)"
  // 群组限制详情
  repeated GroupInfo value = 2;
}

message GroupInfo {
  // 群组类型，static（静态群组）、dynamic（动态群组）
  string type = 1; // valid:"in(static|dynamic)"
  // 群组 id
  string id = 2; // valid:"objectId"
  // 群组名称
  string name = 3;
}

message LevelLimit {
  // 限定符，IN（在其中）、NOT_IN（不在其中）
  string operator = 1; // valid:"in(IN|NOT_IN)"
  // 会员限制详情
  repeated LevelInfo value = 2;
}

message LevelInfo {
  // 类型，level（等级会员）、paidMember（付费会员）、none（非会员）
  string type = 1; // valid:"in(level|paidMember|none)"
  // 会员等级值，整数
  uint32 level = 2;
  // 会员等级名称
  string name = 3;
}

message RedeemPeriod {
  // 活动开始时间，时间格式需要满足 ISO 8601 标准，比如 2006-01-02T15:04:05Z
  string startAt = 2; // valid:"rfc3339"
  // 活动结束时间，时间格式需要满足 ISO 8601 标准，比如 2006-01-02T15:04:05Z
  string endAt = 3; // valid:"rfc3339"
}

message PeriodLimit {
  // 接收限制周期
  //
  // 每日（daily）、每周（weekly）、每月（monthly）、每年（yearly）
  string period = 1; // valid:"in(daily|weekly|monthly|yearly)"
  // 每周期限制接收数量
  int64 limit = 2;
}

message Present {
  // product._id
  string productId = 1;
  // 赠品 sku
  string sku = 2;
  // 赠送数量
  int64 count = 3;
}

message SearchCouponRequest {
  // 指定优惠券 ID
  repeated string ids = 1;
  // 优惠券名称
  string searchKeyWord = 2;
  // 优惠券类型
  //
  // 支持多个类型的查询
  repeated string types = 3;
  mairpc.common.types.BoolValue notSoldOut = 4;
  mairpc.common.request.ListCondition listCondition = 5;
  // 优惠券类型
  //
  // 支持类型 discount: 折扣券；gift：礼品券；cash：代金券；coupon：优惠券；credit：积分券；redpack：红包券；external：外部券; delivery：运费券；present：买赠券
  string type = 6; // valid:"in(discount|gift|cash|coupon|credit|redpack|external|delivery|present)"
  // 是否包含已删除的优惠券
  //
  // 默认不返回被删除的优惠券
  bool containDeleted = 7;
  // 有效期范围
  mairpc.common.types.DateRange validityTime = 9;
  // 库存量
  mairpc.common.types.IntegerRange stock = 10;
  // 状态，不传默认返回正常可用的优惠券
  //
  // 可选值：ongoing 进行中；expired 已过期；notEnded 未结束
  // 审核状态: draft（草稿）、pending（待审核）、normal（正常的）、rejected（拒绝通过）、stopped（停用）、audited（已审核）、all（全部状态）
  string status = 11; // valid:"in(ongoing|expired|notEnded|draft|pending|normal|rejected|stopped|audited|all)"
  // 门店 ID 列表, 查找指定门店可用的优惠券
  repeated string storeIds = 12; // valid:"objectIdList"
  // 业务场景信息
  CouponBusinessSelector businessSelector = 13;
  // 优惠券创建来源，同时传入 sources 时会做并集过滤
  string source = 14;
  // 排除来源
  repeated string excludedSources = 15;
  // 零售商 ids，该字段有值时返回适用范围是全部门店适用和零售商下门店适用的优惠券
  repeated string distributorIds = 16;
  // 源优惠券 ID
  string sourceId = 17;
  // 筛选所属组织节点
  mairpc.common.types.DistributorSelector distributor = 18; // valid:"optional"
  // 更新时间范围
  mairpc.common.types.DateRange updatedAt = 19;
  // 使用渠道
  repeated string availableChannelTypes = 20;
  // 创建者
  repeated string createdBy = 21; // valid:"objectIdList"
  // 额外信息
  //
  // auditInfo（返回优惠券审核相关信息），skuDetail（返回试用商品规格详情）
  repeated string extra = 23;
  // 用户 ID，查询用户可审核的优惠券是传该参数
  string userId = 24;
  // 查询类型
  //
  // 可选值：couponCampaignStopped
  string searchType = 25;
  // 备注
  string remarks = 26;
  // 使用须知
  string usageNote = 27;
  // 状态列表
  // 可选值: draft（草稿）、normal（正常的）、stopped（停用）、expired (已过期)
  repeated string statusList = 28;
  // 优惠券创建来源，同时传入 source 时会做并集过滤
  repeated string sources = 29;
  // 优惠券标签 ID 列表
  repeated string couponTagIds = 30; // valid:"objectIdList"
}

message GetCouponByIdsRequest {
  // 指定优惠券 ID
  repeated string ids = 1; // valid:"objectIdList"
  // 库存量
  mairpc.common.types.IntegerRange stock = 2;
  // 状态
  //
  // 可选值：ongoing 进行中；expired 已过期；notEnded 未结束
  string status = 3; // valid:"in(ongoing|expired|notEnded)"
  // 有效期范围
  mairpc.common.types.DateRange validityTime = 4;
}

message CouponBusinessSelector {
  // 业务场景
  // wechatwork：企业微信，retail-product：零售付费卡券
  repeated string businesses = 1;
  // 是否不包含指定 business 的数据
  bool byExcluded = 2;
  // 对自定义数据的筛选, json格式的字符串，"{\"posterBackgroundImage\":\"http://xxx.jpg\"}"
  string extra = 3;
  // 是否启用
  mairpc.common.types.BoolValue isEnabled = 4;
  // 类型
  //
  // 业务场景为 wechatwork 时为库存类型，可选值：independent（独立库存）、shared（共享库存）
  string type = 5; // valid:"in(independent|shared)"
}

message CouponListResponse {
  // 优惠券
  repeated CouponDetailResponse coupons = 1;
  // 总数
  uint64 total = 2;
}

message BriefCouponListResponse {
  // 优惠券
  repeated BriefCouponDetailResponse coupons = 1;
  // 总数
  uint64 total = 2;
}

message CouponSourceListResponse {
  // 优惠券来源
  repeated CouponSourceBrief sourceList = 1;
}

message CouponSourceBrief {
  string source = 1;
  string name = 2;
}

message UpdateCouponRequest {
  string id = 1;
  CouponTime time = 2;
  mairpc.common.types.StringValue url = 3;
  mairpc.common.types.StringValue stockType = 4;
  mairpc.common.types.Int64Value total = 5;
  CouponDeveloperMode developerMode = 6;
  GiftNotice giftNotice = 7;
  ExpireNotice expireNotice = 8;
  // 适用门店
  ApplicableStore applicableDmsStore = 9;
  string title = 10;
  // 适用商品规格
  repeated ApplicableProductSkus applicableProductSkus = 11;
  // 使用说明
  string usageNote = 12;
  // 操作提示
  string tip = 13;
  // 备注
  string remarks = 14;
  // 状态：draft（草稿）、pending（待审核）、normal（正常的）、rejected（拒绝通过）、stopped（停用）
  string status = 15; // valid:"in(draft|pending|normal|rejected|stopped)"
  // 拒绝通过的原因
  string rejectedReason = 16;
  // 选择的审核人 ids:user.id
  repeated string auditorIds = 17;
  // 领取限制
  IssueLimit issueLimit = 18;
  // 适用品牌 ID
  repeated string applicableBrandIds = 58; // valid:"objectIdList"
  // 适用标签
  ApplicableProductTag applicableProductTag = 59; // valid:"optional"
  // 赠品列表
  repeated Present presents = 60;
  // 到账通知
  ReceiveNotice receiveNotice = 61;
  // 优惠券标签 ID 列表
  repeated string couponTagIds = 62; // valid:"objectIdList"
  // 外部系统券 code
  string thirdPartyCode = 63;
  // 外部系统券 id
  string thirdPartyId = 64;
}

message CouponsQrcodeUpdator {
  enum UpdatorOperator {
    SET = 0;
    PUSH = 1;
    PUSHALL = 2;
  }
  UpdatorOperator updatorOperator = 1;
  repeated CouponQrcode qrcodes = 2;
}

message UpdateCouponQrcodesRequest {
  string id = 1;
  CouponsQrcodeUpdator qrcodesUpdator = 2;
}

message UpdateCouponBusinessRequest {
  // @required
  //
  // 优惠券 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 业务场景
  // wechatwork：企业微信，retail-product：零售付费卡券
  string business = 2; // valid:"required"
  // 自定义数据, json 格式的字符串，如 "{\"posterBackgroundImage\":\"http://xxx.jpg\"}"
  string extra = 3;
  // @required
  //
  // 更新操作类型，remove: 删除该 business，upsert: 更新或添加该 business
  string action = 4; // valid:"required,in(remove|upsert)"
  // 是否启用
  bool isEnabled = 5;
  // 优惠券业务场景类型
  //
  // 当 business 是 wechatwork 时，为门店库存类型，可选值：shared（门店共享库存）、independent（门店独立库存）
  string type = 6; // valid:"in(shared|independent)"
}

message BatchUpdateCouponBusinessRequest {
  repeated UpdateCouponBusinessRequest businessUpdaters = 1; // valid:"required"
}

message ImportBatchCouponCodeRequest {
  string couponId = 1;
  repeated string couponCodes = 2;
  uint64 number = 3;
}

message ImportBatchCouponCodeResponse {
  string couponId = 1;
  repeated UnimportedCouponCode couponCodes = 2;
  uint64 importedCount = 3;
}

message UnimportedCouponCode {
  string code = 1;
  string errorMsg = 2;
  uint64 errorCode = 3;
}

message ChangeCouponStockRequest {
  string couponId = 1;
  uint64 total = 2;
  string stockType = 3;
}

message IssueWechatCouponRequest {
  string couponSourceId = 1;
  string openId = 2;
  string code = 3;
  string channelId = 4;
  // 场景ID
  string sceneId = 5;
  // 扩展数据，json 字符串
  string extra = 6;
}

message RedeemWechatCouponRequest {
  string openId = 1;
  string code = 2;
  string channelId = 3;
}

message ExistProductCouponRequest {
  // 商品ID
  //
  // 需要提供群脉后台商品的ID，而不是群脉零售商品的ID
  repeated string productIds = 1; // valid:"required,objectIdList"
  // 展示通用券
  //
  // 为 true，那么只要存在单品券或者存在通用券，该商品即被认为有优惠券
  // 为 false，那么当且仅当存在单品券才会被认为有优惠券。
  bool showAll = 2;
  // 所属门店
  //
  // 若有值，那么仅当商品有能在该门店中使用的优惠券时才会被认为有优惠券
  string storeId = 3; // valid:"objectId"
}

message ExistProductCouponResponse {
  repeated string ids = 1;
}

message IncCouponStockRequest {
  // @required
  //
  // 优惠券ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 优惠券增量
  uint64 number = 2; // valid:"required"
}

message IncCouponStockResponse {
  // 库存量
  int64 total = 1;
  // 增加库存量
  int64 increasedTotal = 2;
}

message ListAvailableCouponForProductRequest {
  // 商品ID
  //
  // 需要提供群脉后台商品的ID，而不是群脉零售商品的ID
  string productId = 1; // valid:"required,objectId"
  // 展示通用券
  //
  // 为 true，那么返回单品券、通用券和品类券
  // 为 false，那么仅返回单品券
  bool showAll = 2;
  // 所属门店
  //
  // 若有值，那么仅返回能用于该门店的优惠券
  string storeId = 3; // valid:"objectId"
  // 优惠券类型
  //
  // 留空会返回所有类型的优惠券
  repeated string couponTypes = 4;
}

message ListAvailableCouponForStoreRequest {
  // 门店ID
  string storeId = 1; // valid:"objectId,required"
  // 优惠券类型
  //
  // 留空会返回所有类型的优惠券
  repeated string couponTypes = 2;
  // 业务信息
  CouponBusinessSelector businessSelector = 5;
  // 优惠券 Id
  repeated string couponIds = 6; // valid:"objectIdList"
}

message CollectCouponRequest {
  // 优惠券 ID
  string id = 1; // valid:"objectId"
  // @required
  //
  // 客户 ID
  string memberId = 2;
  // 渠道 ID
  string channelId = 3;
  // 导购 ID
  string staffId = 4; // valid:"objectId"
  // 门店 ID
  string storeId = 5; // valid:"objectId"
  // 场景 ID
  string sceneId = 6;
  // 访问来源
  mairpc.common.types.Utm utm = 7;
  // 源优惠券 ID
  string sourceId = 8;
}

message ListCouponApplicableStoresRequest {
  // @required
  //
  // 优惠券 ID
  string id = 1; // valid:"required,objectId"
  // 分页信息
  //
  // 当包含坐标信息时，无法指定排序，返回结果的顺序一定是门店距离的升序
  mairpc.common.request.ListCondition listCondition = 2;
  // 坐标
  //
  // 指定客户的坐标与距离，距离单位是米
  mairpc.common.request.PointRequest coordinate = 3;
  // 地址
  //
  // 如有需要可以用该字段来让api只返回某个省、市、区的门店
  mairpc.common.types.Location location = 4;
  // 门店状态
  //
  // 默认（不传此字段）筛选 open 状态的门店，storeStatus.value 的可选值为：open：营业，close：歇业，空字符串则不筛选状态
  mairpc.common.types.StringValue storeStatus = 5;
  // 零售商 ids
  repeated string distributorIds = 6;
  // 门店来源，从企业微信同步过来的门店 source 为 wechatwork
  string source = 7;
}

message CouponToMember {
  // @required
  //
  // 优惠券ID
  string couponId = 1;
  // @required
  //
  // 客户ID
  string memberId = 3;
}

message IssueCouponToMembersRequest {
  // 优惠券
  repeated CouponToMember couponToMembers = 1;
  // 渠道ID
  string channelId = 2;
  // 场景ID
  string sceneId = 3;
  // 门店ID
  string storeId = 4; // valid:"objectId"
  // 导购ID
  string staffId = 5; // valid:"objectId"
}

message CouponToMemberResult {
  // 优惠券ID
  string couponId = 1;
  // 客户ID
  string memberId = 2;
  // 失败原因状态码
  //
  // 0 代表成功
  uint32 failedCode = 3;
  // 失败原因描述
  string failedReason = 4;
  // 优惠券券码详情
  //
  // 仅发放成功时返回
  IssueCouponResponse membershipDiscount = 5;
}

message IssueCouponToMembersResponse {
  // 发放结果
  repeated CouponToMemberResult results = 1;
}

message GetWechatCardExtRequest {
  // @required
  //
  // 券码
  string code = 1; // valid:"required"
  // @required
  //
  // 渠道 ID
  string channelId = 2; // valid:"required"
  // @required
  //
  // 客户 ID
  string memberId = 3; // valid:"required,objectId"
  // @required
  //
  // 渠道下客户对应的 openId
  string openId = 4; // valid:"required"
  // 是否使用服务商模式
  bool isDelegated = 5;
}

message GetWechatCardExtResponse {
  string cardId = 2;
  string cardExt = 3;
}

message CheckCouponIsUsedResponse {
  // 是否在使用中
  bool isUsed = 1;
  // 使用中的业务场景
  //
  // 批量操作、签到、幸运抽奖、智能营销、进店福利、秒杀、分销商品、产品码活动、邀请有礼、会员任务、会员营销
  string business = 2;
}

message StartGiftingMembershipDiscountRequest {
  // @required
  //
  // 客户优惠券 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 转赠者 ID
  string memberId = 2; // valid:"required,objectId"
}

message ImportCouponApplicableStoresRequest {
  // @required
  //
  // 文件路径
  string fileUrl = 1; // valid:"required"
}

message CreateCouponEndpointRequest {
  // @required
  //
  // 业务类型
  //
  // 可选值：wechatwork:coupon_campaign（卡券活动）、retail:exchange_code（兑换码）
  string business = 1; // valid:"required"
  // 门店 ID
  string storeId = 2; // valid:"objectId"
  // 实体 ID
  string staffId = 3; // valid:"objectId"
  // @required
  //
  // 优惠券 ID
  string couponId = 4; // valid:"required,objectId"
  // 总数
  //
  // 业务类型为卡券活动时是限领优惠券总数，业务类型为兑换码时是可兑换总数
  uint32 totalCount = 5;
  // 描述
  string description = 6;
  // 过期时间
  //
  // 时间格式需要满足 ISO 8601 标准
  string endAt = 7;
  // 场景
  string scene = 8;
  // 开始时间
  //
  // 时间格式需要满足 ISO 8601 标准
  string startAt = 9;
  // 类型：common（通用码）、unique（一卡一码）
  string type = 10; // valid:"in(common|unique)"
  // 名称
  string name = 11;
  // 通用兑换码
  string code = 12; // valid:"runelength(2|12)"
  // 兑换码创建方式: auto（自动生成）、import（导入）
  string codeCreateType = 13; // valid:"in(auto|import)"
  // 导入 url
  string fileUrl = 14;
  // 兑换码长度，当且仅当自动生成一卡一码时生效，取值范围：6~12
  int64 codeLength = 15; // valid:"range(6|12)"
  // 兑换码类型：DIGIT_AND_UPPERCASE（数字+大写字母）、DIGIT（数字）
  string codeType = 16; // valid:"in(DIGIT_AND_UPPERCASE|DIGIT)"
}

message CreateCouponEndpointResponse {
  // 优惠券领取终端 ID
  string id = 1;
  string asyncCacheCode = 2;
}

message GetCouponEndpointRequest {
  // @required
  //
  // 优惠券终端 ID
  string id = 1; // valid:"required,objectId"
}

message GetCouponEndpointResponse {
  // 优惠券终端详情
  CouponEndpointDetail couponEndpoint = 1;
  // 优惠券详情
  CouponDetailResponse coupon = 2;
}

message CouponEndpointDetail {
  // 优惠券终端 ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 创建时间
  string createdAt = 3;
  // 业务类型
  string business = 4;
  // 业务实体 ID
  string storeId = 5;
  // 导购 ID
  string staffId = 6;
  // 优惠券 ID
  string couponId = 7;
  // 过期时间
  string endAt = 8;
  // 限取总数|可兑换总数|发放总数
  uint32 totalCount = 9;
  // 剩余数量
  uint32 count = 10;
  // 描述
  string description = 11;
  // 场景
  string scene = 12;
  // 开始时间
  string startAt = 13;
  // 类型：common（通用码）、unique（一卡一码）
  string type = 14;
  // 名称
  string name = 15;
  // 通用兑换码
  string code = 16;
  // 已兑换数量
  uint32 exchangedCount = 17;
  // 已核销数量
  uint32 redeemedCount = 18;
  // 兑换码创建方式
  string codeCreateType = 19;
  // 自动生成兑换码的长度
  int64 codeLength = 20;
  // 兑换码类型：DIGIT_AND_UPPERCASE（数字+大写字母）、DIGIT（数字）
  string codeType = 21;
}

message CollectCouponByEndpointRequest {
  // @required
  //
  // 优惠券终端 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 客户 ID
  string memberId = 2; // valid:"required,objectId"
  // 渠道 ID
  string channelId = 3;
  // 导购 ID
  string staffId = 4; // valid:"objectId"
  // 门店 ID
  string storeId = 5; // valid:"objectId"
  // 场景 ID
  string sceneId = 6;
}

message ListCouponEndpointsRequest {
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 1;
  // 是否是查询有效库存的
  //
  // 为 true 时查询终端中剩余卡券数量大于 0 的
  mairpc.common.types.BoolValue isValidCount = 2;
  // 场景
  repeated string scenes = 3;
  // 是否查询已过期失效的
  //
  // 为 true 时查询已过期的
  mairpc.common.types.BoolValue isExpired = 4;
  // 业务类型：wechatwork:coupon_campaign（卡券活动）、retail:exchange_code（兑换码）
  repeated string businesses = 5;
  // 类型：common（通用码）、unique（一卡一码）
  string type = 6;
  // 名称
  string name = 7;
  // 活动状态：未开始（created），进行中（running），已结束（ended），不传为获取全部
  repeated string status = 8;
  // 有效期
  mairpc.common.types.StringDateRange dateRange = 9;
  // 导购 id
  string staffId = 10; // valid:"objectId"
  // 卡券 id
  string couponId = 11; // valid:"objectId"
  // 终端 ids
  repeated string ids = 12; // valid:"objectIdList"
  // 门店 id
  string storeId = 13; // valid:"objectId"
  // 兑换码
  string exchangeCode = 14;
}

message ListCouponEndpointsResponse {
  // 优惠券终端详情列表
  repeated CouponEndpointDetail items = 1;
  // 总量
  int64 total = 2;
}

message DeleteCouponEndpointRequest {
  // @required
  //
  // 优惠券终端 ID
  string id = 1; // valid:"required,objectId"
}

message StatsIssuedCouponByEndpointRequest {
  // @required
  //
  // 门店 ID
  string storeId = 1; // valid:"required,objectId"
  // @required
  //
  // 卡券 ID 列表
  repeated string couponIds = 2; // valid:"required,objectIdList"
  // 是否统计已删除的
  bool containsDeleted = 3;
  // 导购 ID
  string staffId = 4; // valid:"objectId"
}

message CouponStats {
  // 卡券 ID
  string couponId = 1;
  // 已发数量
  int64 issuedCount = 2;
  // 终端剩余总数
  int64 totalStockCount = 3;
}

message StatsIssuedCouponByEndpointResponse {
  // 已发卡券数量统计
  repeated CouponStats items = 1;
  int64 total = 2;
}

message GetCouponByCodeRequest {
  // @required
  //
  // 客户 Id
  string memberId = 1; // valid:"objectId,required"
  // @required
  //
  // 券码
  string code = 2; // valid:"required"
}

message GetMembershipDiscountsByCouponIdRequest {
  // @required
  //
  // 客户 Id
  string memberId = 1; // valid:"objectId,required"
  // @required
  //
  // 优惠券 Id
  string couponId = 2; // valid:"objectId,required"
  // 额外信息
  //
  // skuDetail（返回适用商品规格详情）
  repeated string extra = 3;
}

message PostponeMembershipDiscountRequest {
  // @required
  //
  // membershipDisocunt.id
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 延期至，时间格式：RFC3339
  string postponedTo = 2; // valid:"required"
}

message ListMembershipDiscountApplicableStoresRequest {
  // @required
  //
  // membershipDiscount._id
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 客户 Id
  string memberId = 2; // valid:"required,objectId"
  // 分页信息
  //
  // 当包含坐标信息时，无法指定排序，返回结果的顺序一定是门店距离的升序
  mairpc.common.request.ListCondition listCondition = 3;
  // 坐标
  //
  // 指定客户的坐标与距离，距离单位是米
  mairpc.common.request.PointRequest coordinate = 4;
  // 地址
  //
  // 如有需要可以用该字段来让api只返回某个省、市、区的门店
  mairpc.common.types.Location location = 5;
  // 门店状态
  //
  // 默认（不传此字段）筛选 open 状态的门店，storeStatus.value 的可选值为：open：营业，close：歇业，空字符串则不筛选状态
  mairpc.common.types.StringValue storeStatus = 6;
  // 零售商 ids
  repeated string distributorIds = 7;
  // 门店来源，从企业微信同步过来的门店 source 为 wechatwork
  string source = 8;
}

message IssueThirdpartyCouponRequest {
  // @required
  //
  // 来源 ID
  string couponSourceId = 1; // valid:"required"
  // @required
  //
  // 券码
  string code = 2; // valid:"required"
  // @required
  //
  // 客户 ID
  string memberId = 3; // valid:"required,objectId"
  // 预生成 ID
  //
  // 如果传值，生成的记录会使用此 ID
  string membershipDiscountId = 4; // valid:"objectId"
  // 扩展数据，json 字符串
  string extra = 5;
}

message UpdateMembershipDiscountRequest {
  // membershipDiscount._id
  string id = 1; // valid:"objectId"
  // 券码
  string code = 2;
  // @required
  //
  // 更新状态
  //
  // 取值范围：["unused", "used", "deleted", "expired", "unsupport"]
  string status = 3; // valid:"required,in(unused|used|deleted|expired|unsupport)"
  // 状态变更时间
  //
  // 时间格式：RFC3339
  string operationTime = 4;
  // 门店 Id
  string storeId = 5; // valid:"objectId"
  // 员工 Id
  string staffId = 6; // valid:"objectId"
  // 渠道 Id
  string channelId = 7;
}

message UpdateMembershipDiscountResponse {
  // 券码
  string code = 1;
  // 更新前的状态
  string previousStatus = 2;
  // 更新后的状态
  string status = 3;
}

message EditCouponRequest {
  string type = 1;
  string title = 2;
  string picUrl = 3;
  CouponTime time = 4;
  string usageNote = 5;
  string stockType = 6;
  int64 total = 7;
  int64 limit = 8;
  bool isSelfRedemption = 9;
  string applicableProductId = 10;
  string tip = 11;
  string phone = 12;
  mairpc.common.types.DoubleValue discountAmount = 13;
  mairpc.common.types.DoubleValue reductionAmount = 14;
  mairpc.common.types.DoubleValue discountCondition = 15;
  string description = 16;
  mairpc.common.types.DoubleValue redpackMinAmount = 17;
  mairpc.common.types.DoubleValue redpackMaxAmount = 18;
  mairpc.common.types.DoubleValue redpackAmount = 19;
  string redpackWishing = 20;
  ApplicableStore applicableStore = 21;
  ApplicableStore applicableDmsStore = 22;
  ApplicableArea applicableArea = 23;
  string producer = 24;
  string importStatus = 25;
  SourceCoupon source = 26;
  bool shouldRedeemInIssuedStore = 27;
  ApplicableProduct applicableProduct = 28;
  bool allowGifting = 29;
  string remarks = 31;
  repeated string applicableCategoryIds = 33; // valid:"objectIdList"
  repeated ApplicableProductSkus applicableProductSkus = 34;
  GiftNotice giftNotice = 35;
  ExpireNotice expireNotice = 36;
  bool isPublicCollectionEnabled = 37;
  // 每人每日接收限制
  int64 dailyLimit = 38;
  // 每人每日使用限制
  int64 dailyRedeemedLimit = 39;
  Present present = 40;
  // 红包发送人名称
  string redpackSenderName = 41;
  // 使用渠道
  //
  // 可选值：online（线上）、online_and_offline（线上线下）、offline(线下)、unavailable（线上线下均不可用）
  string availableChannelType = 42; // valid:"in(online|online_and_offline|offline|unavailable)"
  // 适用物流
  //
  // 全部配送渠道（all），快递配送（express），同城配送（cityExpress）
  string applicableMethod = 43; // valid:"in(all|express|cityExpress)"
  // 是否允许与其他券共用
  bool canUsedWithOthers = 44;
  // 适用组织
  ApplicableEntity applicableDistributor = 45;
  // 适用门店类型
  ApplicableEntity applicableStoreType = 46;
  // 适用门店级别
  ApplicableEntity applicableStoreLevel = 47;
  // 叠加设置
  //
  // 仅与自己叠加（self），可与其他可叠加券叠加（other）
  string superpositionType = 48;
  // 转赠未被领取通知
  UncollectedNotice uncollectedNotice = 49;
  // 最高优惠金额
  mairpc.common.types.DoubleValue maxDiscountAmount = 50;
  // 周期接收限制
  repeated PeriodLimit periodLimits = 51;
  // 运费设置
  //
  // 可选值：freeShipping（包邮）、default（使用系统运费设置）
  string deliveryFeeType = 52; // valid:"in(freeShipping|default)"
  // @required
  //
  // 优惠券 ID
  string id = 53; // valid:"required,objectId"
  // 券码导入链接
  string importUrl = 54;
  // 用于商家券有效期
  RedeemPeriod redeemPeriod = 55;
  // 领取限制
  IssueLimit issueLimit = 56;
  // 适用品牌 ID
  repeated string applicableBrandIds = 58;
  // 适用标签
  ApplicableProductTag applicableProductTag = 59;
  // 折扣条件金额类型
  //
  // 订单金额（totalAmount）、实付金额（payAmount）
  string discountConditionType = 60;
  // 可叠加优惠券类型列表
  //
  // 代金券（cash）、折扣券（discount）、买赠券（present）、运费券（delivery）、兑换券（exchange）
  repeated string superpositionCouponTypes = 61; // valid:"in(cash|discount|present|delivery|exchange)"
  // 适用类目类型
  //
  // include（包含）, exclude（不包含）
  string applicableCategoryType = 62; // valid:"in(include|exclude)"
  // 赠品列表
  repeated Present presents = 63;
  // 赠送回退设置
  ReturnSetting returnSetting = 64;
  // 到账通知
  ReceiveNotice receiveNotice = 65;
  // 优惠券标签 ID 列表
  repeated string couponTagIds = 66; // valid:"objectIdList"
  // 外部系统券 code
  string thirdPartyCode = 67;
  // 外部系统券 id
  string thirdPartyId = 68;
}

message ImportExchangeCodesRequest {
  // @required
  //
  // 文件路径
  string fileUrl = 1; // valid:"required,url"
  // @required
  //
  // 兑换码活动 ID
  string couponEndpointId = 2; // valid:"required,objectId"
}

message GenerateExchangeCodesRequest {
  // @required
  //
  // 兑换码活动 ID
  string couponEndpointId = 1; // valid:"required,objectId"
  // 数量
  uint32 count = 2;
}

message ExchangeMembershipDiscountRequest {
  // @required
  //
  // 兑换码
  string code = 1; // valid:"required"
  // @required
  //
  // 客户 ID
  string memberId = 2; // valid:"required,objectId"
}

message ExchangeMembershipDiscountResponse {
  // 客户优惠券 ID
  string membershipDiscountId = 1;
  // 兑换码活动 ID
  string couponEndpointId = 2;
  // 兑换码活动名称
  string couponEndpointName = 3;
  // 优惠券名称
  string couponName = 4;
  // 优惠券类型
  string couponType = 5;
  // 是否是首次兑换
  bool isFirstTime = 6;
}

message ExportExchangeCodesRequest {
  // @required
  //
  // 兑换码活动 ID
  string couponEndpointId = 1; // valid:"required,objectId"
  // 兑换码前缀
  string prefix = 2;
}

message BatchExportExchangeCodesRequest {
  // @required
  //
  // 兑换码活动 ID
  repeated string couponEndpointIds = 1; // valid:"required,objectIdList"
  // 兑换码前缀
  string prefix = 2;
}

message IsExchangeCodeExistRequest {
  // @required
  //
  // 兑换码
  string code = 1; // valid:"required"
}

message CheckBeforeCollectRequest {
  // @required
  //
  // 优惠券 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 客户 id
  string couponId = 2; // valid:"required,objectId"
}

message CheckBeforeCollectResponse {
  // 是否通过校验
  bool passed = 1;
  // 未通过详情
  string message = 2;
}

message ImportCouponApplicableProductsRequest {
  // @required
  //
  // 文件路径
  string fileUrl = 1; // valid:"required"
}

message ExportCouponApplicableProductsRequest {
  // @required
  //
  // 商品sku
  repeated CouponApplicableProduct products = 1; // valid:"required"
}

message CouponApplicableProduct {
  string number = 1;
  string sku = 2;
  string external = 3;
}

message RevokeGiftingMemberShipDiscountRequest {
  // @required
  //
  // 客户优惠券 ID
  string id = 1;
  // @required
  //
  // 客户 ID
  string memberId = 2;
}

message UpdateCouponLogRequest {
  // 优惠券记录 id
  string id = 1;
  // 订单 id
  string orderId = 2; // valid:"objectId"
  // 订单编号
  string orderNumber = 3;
}
