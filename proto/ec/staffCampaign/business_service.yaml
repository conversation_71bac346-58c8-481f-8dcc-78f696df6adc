type: google.api.Service
config_version: 3

http:
  rules:
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.InitStaffIncentive
      post: /v2/ec/staffIncentives/test/init
      body: '*'
      filterMark: debug
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.CreateStaffIncentive
      post: /v2/ec/staffIncentives
      body: '*'
      filterMark: backend
      tags: ['导购企微版-激励']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.UpdateStaffIncentive
      put: /v2/ec/staffIncentives/{code}
      body: '*'
      filterMark: backend
      tags: ['导购企微版-激励']
      permission: 'ec:staffIncentive:update'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.DeleteStaffIncentive
      delete: /v2/ec/staffIncentives/{code}
      body: '*'
      filterMark: backend
      tags: ['导购企微版-激励']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ToggleStaffIncentive
      put: /v2/ec/staffIncentives/{code}/enable
      body: '*'
      filterMark: backend
      tags: ['导购企微版-激励']
      permission: 'ec:staffIncentive:enable'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.StatsStaffIncentiveOverview
      get: /v2/ec/staffIncentives/stats/overview
      filterMark: backend
      tags: ['导购企微版-激励']
      permission: 'ec:staffIncentive:statsOverview'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListStaffIncentiveRewards
      get: /v2/ec/staffIncentiveRewards
      filterMark: backend,staff
      scope: ['staff']
      sensitiveFields: 'items.staff.name'
      tags: ['导购企微版-激励']
      permission: 'ec:staffIncentiveReward:list'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportStaffIncentiveRewards
      post: /v2/ec/staffIncentiveRewards/export
      body: '*'
      filterMark: backend
      tags: ['导购企微版-激励']
      permission: 'ec:staffIncentiveReward:export'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListStaffIncentives
      get: /v2/ec/staffIncentives
      filterMark: backend, staff
      scope: ['staff']
      tags: ['导购企微版-激励']
      permission: 'ec:staffIncentive:list'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetStaffIncentive
      get: /v2/ec/staffIncentives/{code}
      filterMark: backend
      tags: ['导购企微版-激励']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.StatsStaffIncentiveMonthlyReward
      get: /v2/ec/staffIncentiveRewards/{staffId}/monthly
      scope: ['staff']
      filterMark: staff
      tags: ['导购企微版-激励']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.StatsStaffIncentiveTotalReward
      get: /v2/ec/staffIncentiveRewards/{staffId}/total
      scope: ['staff']
      filterMark: staff
      tags: ['导购企微版-激励']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetStaffIncentiveRecords
      get: /v2/ec/staffIncentiveRecords
      scope: ['staff']
      filterMark: staff
      tags: ['导购企微版-激励']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.CreateCouponCampaign
      post: /v2/ec/couponCampaigns
      body: '*'
      filterMark: backend
      tags: ['导购企微版-活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.DeleteCouponCampaign
      delete: /v2/ec/couponCampaigns/{id}
      body: '*'
      filterMark: backend
      tags: ['导购企微版-活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.UpdateCouponCampaignStocks
      put: /v2/ec/couponCampaigns/{id}/stocks
      body: '*'
      filterMark: backend
      tags: ['导购企微版-活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetCouponCampaign
      get: /v2/ec/couponCampaigns/{id}
      filterMark: backend
      tags: ['导购企微版-活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListCouponCampaignStocks
      get: /v2/ec/couponCampaigns/{id}/stocks
      scope: ['staff']
      filterMark: backend,staff
      tags: ['导购企微版-活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.RollbackUncollectedCouponEndpointStock
      post: /v2/ec/couponCampaigns/stocks/rollback
      scope: ['staff']
      body: '*'
      filterMark: backend,staff
      tags: ['导购企微版-活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportCouponCampaignStocksTemplate
      post: /v2/ec/couponCampaigns/{id}/exportStockTemplate
      body: '*'
      filterMark: backend
      tags: ['导购企微版-活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ImportCouponCampaignStocks
      post: /v2/ec/couponCampaign/importStocks
      body: '*'
      filterMark: backend
      tags: ['导购企微版-活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.BatchUpdateCouponCampaigns
      post: /v2/ec/couponCampaigns/batchUpdate
      body: '*'
      filterMark: backend
      tags: ['导购企微版-活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.IssueCoupon
      post: /v2/ec/couponCampaigns/{id}/issueCoupon
      body: '*'
      scope: ['staff']
      filterMark: staff
      tags: ['导购企微版-活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetCampaignCoupons
      get: /v2/ec/campaignCoupons
      scope: ['staff']
      filterMark: staff
      tags: ['导购企微版-活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetCouponReceivedRecords
      get: /v2/ec/couponReceivedRecords
      scope: ['staff']
      filterMark: staff
      tags: ['导购企微版-活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.CreateDrainageQrcode
      post: /v2/ec/drainageQrcodes
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
      permission: 'ec:drainageQrcode:create'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListDrainageQrcode
      get: /v2/ec/drainageQrcodes
      additional_bindings:
        - post: /v2/ec/drainageQrcodes/search
          body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
      permission: 'ec:drainageQrcode:list'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.UpdateDrainageQrcode
      put: /v2/ec/drainageQrcodes/{id}
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
      permission: 'ec:drainageQrcode:update'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListContactWays
      get: /v2/ec/drainageQrcodes/{drainageQrcodeId}/contactWays
      filterMark: backend
      tags: ['导购企微版-引流码']
      permission: 'ec:drainageQrcode:contactWayList'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportContactWays
      post: /v2/ec/drainageQrcodes/{drainageQrcodeId}/contactWays/export
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
      permission: 'ec:drainageQrcode:contactWayExport'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.CheckAndImportData
      post: /v2/ec/drainageQrcodes/checkAndImport
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
      permission: 'dms:staff:import'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetDrainageQrcode
      get: /v2/ec/drainageQrcodes/{id}
      filterMark: backend
      tags: ['导购企微版-引流码']
      permission: 'ec:drainageQrcode:detail'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.DeleteDrainageQrcode
      delete: /v2/ec/drainageQrcodes/{id}
      filterMark: backend
      tags: ['导购企微版-引流码']
      permission: 'ec:drainageQrcode:delete'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportDrainageQrcode
      post: /v2/ec/drainageQrcodes/{id}/export
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
      permission: 'ec:drainageQrcode:export'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.BatchExportDrainageQrcodes
      post: /v2/ec/drainageQrcodes/batchExport
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
      permission: 'ec:drainageQrcode:export'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportDrainageQrcodeDetail
      post: /v2/ec/drainageQrcodes/export
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportDrainageQrcodeStats
      post: /v2/ec/drainageQrcodes/{id}/stats/export
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.MigrateDrainageQrcodes
      post: /v2/ec/drainageQrcodes/migrate
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.MigrateDrainageQrcodeWelcomeMessage
      post: /v2/ec/drainageQrcodes/Messages/migrate
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.MigrateDrainageQrcodeApplicableStaff
      post: /v2/ec/drainageQrcodes/applicableStaff/migrate
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.CreateContactWaysByFailedScope
      post: /v2/ec/drainageQrcodes/{drainageQrcodeId}/update
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListDrainageLogs
      get: /v2/ec/drainageQrcodes/{drainageQrcodeId}/logs
      filterMark: backend
      tags: ['导购企微版-引流码']
      permission: 'ec:drainageLog:list'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportDrainageLogs
      post: /v2/ec/drainageQrcodes/{drainageQrcodeId}/logs/export
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流码']
      permission: 'ec:drainageLog:export'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.CreateDrainageMonitor
      post: /v2/ec/drainageMonitor
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流监控']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.UpdateDrainageMonitor
      put: /v2/ec/drainageMonitors/{id}
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流监控']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListDrainageMonitors
      get: /v2/ec/drainageMonitors
      filterMark: backend
      tags: ['导购企微版-引流监控']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetDrainageMonitor
      get: /v2/ec/drainageMonitors/{id}
      filterMark: backend
      tags: ['导购企微版-引流监控']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.DeleteDrainageMonitor
      delete: /v2/ec/drainageMonitors/{id}
      filterMark: backend
      tags: ['导购企微版-引流监控']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListDrainageMonitorDetails
      get: /v2/ec/drainageMonitor/{id}/details
      filterMark: backend
      tags: ['导购企微版-引流监控']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.TransferDrainageMonitor
      post: /v2/ec/drainageMonitor/transfer
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流监控']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.DeleteDrainageMonitorStaffs
      delete: /v2/ec/drainageMonitors/{id}/staffs
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流监控']
    # 创建欢迎语
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.CreateWelcomeMessage
      post: /v2/ec/welcomeMessages
      body: '*'
      filterMark: backend
      tags: ['导购企微版-欢迎语']
      permission: 'ec:welcomeMessage:create'
    # 删除欢迎语
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.DeleteWelcomeMessage
      delete: /v2/ec/welcomeMessages
      filterMark: backend
      tags: ['导购企微版-欢迎语']
      permission: 'ec:welcomeMessage:delete'
    # 批量删除欢迎语
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.BatchDeleteWelcomeMessages
      post: /v2/ec/welcomeMessages/batchDelete
      body: '*'
      filterMark: backend
      tags: ['导购企微版-欢迎语']
      permission: 'ec:welcomeMessage:delete'
    # 更新欢迎语
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.UpdateWelcomeMessage
      put: /v2/ec/welcomeMessages/{id}
      body: '*'
      filterMark: backend
      tags: ['导购企微版-欢迎语']
      permission: 'ec:welcomeMessage:update'
    # 批量更新欢迎语
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.BatchUpdateWelcomeMessages
      put: /v2/ec/welcomeMessages
      body: '*'
      filterMark: backend
      tags: ['导购企微版-欢迎语']
      permission: 'ec:welcomeMessage:update'
    # 欢迎语列表
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListWelcomeMessages
      get: /v2/ec/welcomeMessages
      additional_bindings:
        - post: /v2/ec/welcomeMessages/search
          body: '*'
      filterMark: backend
      tags: ['导购企微版-欢迎语']
      permission: 'ec:welcomeMessage:list'
    # 欢迎语详情
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetWelcomeMessages
      get: /v2/ec/welcomeMessages/{id}
      filterMark: backend
      tags: ['导购企微版-欢迎语']
      permission: 'ec:welcomeMessage:detail'
    # 迁移欢迎语
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.MigrateWelcomeMessage
      post: /v2/ec/welcomeMessages/migrate
      filterMark: backend
      body: '*'
      tags: ['导购企微版-欢迎语']
    # 创建|更新入群欢迎语素材
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.UpsertGroupWelcomeTemplate
      post: /v2/ec/groupWelcomeTemplates
      filterMark: backend
      body: '*'
      tags: ['导购企微版-入群欢迎语素材']
    # 入群欢迎语素材列表
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListGroupWelcomeTemplates
      get: /v2/ec/groupWelcomeTemplates
      filterMark: backend
      tags: ['导购企微版-入群欢迎语素材']
    # 入群欢迎语素材详情
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetGroupWelcomeTemplate
      get: /v2/ec/groupWelcomeTemplates/{id}
      filterMark: backend
      tags: ['导购企微版-入群欢迎语素材']
    # 删除入群欢迎语素材
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.DeleteGroupWelcomeTemplate
      delete: /v2/ec/groupWelcomeTemplates/{id}
      filterMark: backend
      tags: ['导购企微版-入群欢迎语素材']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ImportOfflineIncentiveRewardsResult
      post: /v2/ec/incentiveRewards/import
      body: '*'
      filterMark: backend
      tags: ['导购企微版-活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.CreateGroupchatDrainageQrcode
      post: /v2/ec/groupchatDrainageQrcodes
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['导购企微版-入群引流码']
      permission: 'ec:groupchatDrainageQrcode:create'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListGroupchatDrainageQrcodes
      get: /v2/ec/groupchatDrainageQrcodes
      filterMark: backend,staff
      scope: ['staff']
      tags: ['导购企微版-入群引流码']
      permission: 'ec:groupchatDrainageQrcode:list'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetGroupchatDrainageQrcode
      get: /v2/ec/groupchatDrainageQrcodes/{id}
      filterMark: backend,staff
      scope: ['staff']
      tags: ['导购企微版-入群引流码']
      permission: 'ec:groupchatDrainageQrcode:detail'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.UpdateGroupchatDrainageQrcode
      put: /v2/ec/groupchatDrainageQrcodes/{id}
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['导购企微版-入群引流码']
      permission: 'ec:groupchatDrainageQrcode:update'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.UpdateGroupchatQrcode
      put: /v2/ec/groupchatQrcodes/{id}
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['导购企微版-入群引流码']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListGroupchatQrcodes
      get: /v2/ec/groupchatQrcodes
      filterMark: backend,staff
      scope: ['staff']
      tags: ['导购企微版-入群引流码']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.DeleteGroupchatDrainageQrcode
      delete: /v2/ec/groupchatDrainageQrcodes/{id}
      body: '*'
      filterMark: backend
      tags: ['导购企微版-入群引流码']
      permission: 'ec:groupchatDrainageQrcode:delete'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.DeleteGroupchatQrcodes
      delete: /v2/ec/groupchatQrcodes
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['导购企微版-入群引流码']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetGroupchatDrainageQrcodeDailyStats
      get: /v2/ec/groupchatDrainageQrcodes/{id}/stats
      filterMark: backend,staff
      scope: ['staff']
      tags: ['导购企微版-入群引流码']
      permission: 'ec:groupchatDrainageQrcode:detail'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportGroupchatDrainageQrcodeStats
      post: /v2/ec/groupchatDrainageQrcodes/{id}/stats/export
      body: '*'
      filterMark: backend
      tags: ['导购企微版-入群引流码']
      permission: 'ec:groupchatDrainageQrcode:export'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportGroupchatDrainageQrcode
      post: /v2/ec/groupchatDrainageQrcodes/{id}/export
      body: '*'
      filterMark: backend
      tags: ['导购企微版-入群引流码']
      permission: 'ec:groupchatDrainageQrcode:export'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportGroupchatQrcode
      post: /v2/ec/groupchatQrcodes/{id}/export
      body: '*'
      filterMark: backend
      tags: ['导购企微版-入群引流码']
      permission: 'ec:groupchatDrainageQrcode:export'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.CreateDrainageActivity
      post: /v2/ec/drainageActivities
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流活动']
      permission: 'ec:drainageActivity:create'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.UpdateDrainageActivity
      put: /v2/ec/drainageActivities/{id}
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流活动']
      permission: 'ec:drainageActivity:update'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetDrainageActivity
      get: /v2/ec/drainageActivities/{id}
      scope: ['staff']
      filterMark: backend,staff
      tags: ['导购企微版-引流活动']
      permission: 'ec:drainageActivity:detail'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListStaffDrainageActivities
      get: /v2/ec/staffDrainageActivities
      scope: ['staff']
      filterMark: backend,staff
      tags: ['导购企微版-引流活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ViewDrainageActivity
      post: /v2/ec/drainageActivities/{activityId}/view
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ShareDrainageActivity
      post: /v2/ec/drainageActivities/{activityId}/share
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.DeleteDrainageActivity
      delete: /v2/ec/drainageActivities/{activityId}
      filterMark: backend
      tags: ['导购企微版-引流活动']
      permission: 'ec:drainageActivity:delete'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListDrainageActivities
      get: /v2/ec/drainageActivities
      filterMark: backend
      tags: ['导购企微版-引流活动']
      permission: 'ec:drainageActivity:list'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetDrainageActivityStatsOverview
      get: /v2/ec/drainageActivities/{activityId}/overview
      additional_bindings:
        - get: /v2/ec/drainageActivity/overview
      scope: ['staff']
      filterMark: backend,staff
      tags: ['导购企微版-引流活动']
      permission: 'ec:drainageActivityMemberDailyStats:detail'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetDrainageActivityStatsTrend
      get: /v2/ec/drainageActivities/{activityId}/trend
      additional_bindings:
        - post: /v2/ec/drainageActivities/{activityId}/trend/search
          body: '*'
      filterMark: backend
      tags: ['导购企微版-引流活动']
      permission: 'ec:drainageActivityMemberDailyStats:detail'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.CanJoinDrainageActivity
      post: /v2/ec/drainageActivities/{activityId}/canJoin
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.JoinDrainageActivity
      post: /v2/ec/drainageActivities/{activityId}/join
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListDrainageActivityRewards
      get: /v2/ec/drainageActivityRewards
      filterMark: backend
      tags: ['导购企微版-引流活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ReceiveDrainageActivityReward
      post: /v2/ec/drainageActivityRewards/{id}/receive
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetDrainageActivityMember
      get: /v2/ec/drainageActivityMembers/{memberId}
      filterMark: backend
      tags: ['导购企微版-引流活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListDrainageActivityMember
      get: /v2/ec/drainageActivityMembers
      scope: ['staff']
      filterMark: backend,staff
      tags: ['导购企微版-引流活动']
      sensitiveFields: 'items.name,items.contactInfo.phone,items.contactInfo.name,items.contactInfo.address.detail,items.inviterName'
      permission: 'ec:drainageActivityMember:list'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.CheckDrainageActivityMember
      post: /v2/ec/drainageActivities/{activityId}/checkMember
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.RecordDownloadMaterial
      post: /v2/ec/recordDownloadMaterial
      body: '*'
      scope: ['staff']
      filterMark: staff
      tags: ['导购企微版-引流活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportDrainageActivity
      post: /v2/ec/drainageActivity/{activityId}/export
      body: '*'
      filterMark: backend
      permission: 'ec:drainageActivity:export'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportDrainageActivityMembers
      post: /v2/ec/drainageActivityMember/{activityId}/export
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流活动']
      permission: 'ec:drainageActivityMember:list'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListDrainageActivityInvitedMembers
      get: /v2/ec/drainageActivities/{activityId}/invitedMembers
      scope: ['staff']
      filterMark: staff
      tags: ['导购企微版-引流活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.CheckGroupchatName
      post: /v2/ec/groupchatName/check
      body: '*'
      filterMark: backend
      tags: ['导购企微版-引流活动']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetDrainageActivityMemberSensitive
      get: /v2/ec/drainageActivities/{id}/sensitive
      filterMark: backend
      tags: ['导购企微版-引流活动']
      permission: 'ec:drainageActivityMember:sensitive'
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.CreateCustomerAcquisitionLink
      post: /v2/ec/customerAcquisitionLinks
      body: '*'
      filterMark: backend
      tags: ['导购企微版-获客助手']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.UpdateCustomerAcquisitionLink
      put: /v2/ec/customerAcquisitionLinks/{id}
      body: '*'
      filterMark: backend
      tags: ['导购企微版-获客助手']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.DeleteCustomerAcquisitionLink
      delete: /v2/ec/customerAcquisitionLinks/{id}
      filterMark: backend
      tags: ['导购企微版-获客助手']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListCustomerAcquisitionLink
      get: /v2/ec/customerAcquisitionLinks
      filterMark: backend
      tags: ['导购企微版-获客助手']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetCustomerAcquisitionLink
      get: /v2/ec/customerAcquisitionLinks/{id}
      filterMark: backend
      tags: ['导购企微版-获客助手']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetCustomerAcquisitionQuota
      get: /v2/ec/customerAcquisitionQuota
      filterMark: backend
      tags: ['导购企微版-获客助手']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ListCustomerAcquisitionInfo
      get: /v2/ec/customerAcquisitionInfos
      filterMark: backend
      tags: ['导购企微版-获客助手']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetCustomerAcquisitionStats
      get: /v2/ec/customerAcquisitionStats
      filterMark: backend
      tags: ['导购企微版-获客助手']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportCustomerAcquisitionInfos
      post: /v2/ec/customerAcquisitionInfos/export
      body: '*'
      filterMark: backend
      tags: ['导购企微版-获客助手']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.ExportCustomerAcquisitionLinks
      post: /v2/ec/customerAcquisitionLinks/export
      body: '*'
      filterMark: backend
      tags: ['导购企微版-获客助手']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetGroupchatDrainageQrcodeOverview
      get: /v2/ec/groupchatDrainageQrcodes/{id}/overview
      filterMark: backend,staff
      scope: ['staff']
      tags: ['导购企微版-入群引流码']
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.GetQrcodeGroupchatStats
      get: /v2/ec/groupchatDrainageQrcodes/{id}/groupchatStats
      filterMark: backend,staff
      scope: ['staff']
      tags: ['导购企微版-入群引流码']
    # 用于修复数据
    - selector: mairpc.ec.staff_campaign.StaffCampaignService.HandleExternalContactAdded
      post: /v2/ec/staffCampaign/handleExternalContactAdded
      body: '*'
      filterMark: debug
      tags: ['导购企微版-客户']
