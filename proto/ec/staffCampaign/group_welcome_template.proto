syntax = "proto3";

package mairpc.ec.staff_campaign;

option go_package = "staff_campaign";

import "common/request/request.proto";

message ListGroupWelcomeTemplatesRequest {
  string name = 1;
  mairpc.common.request.ListCondition listCondition = 2;
}

message ListGroupWelcomeTemplatesResponse {
  repeated GroupWelcomeTemplateDetail items = 1;
  int64 total = 2;
}

message GroupWelcomeTemplateDetail {
  string id = 1;
  string name = 2; // valid:"required,objectId"
  GroupWelcomeTemplate welcomeMsg = 3;
}

message GroupWelcomeTemplate {
  WelcomeTemplateText text = 1;
  WelcomeTemplateImage image = 2;
  WelcomeTemplateLink link = 3;
  WelcomeTemplateMiniProgram miniProgram = 4;
  WelcomeTemplateVideo video = 5;
  WelcomeTemplateFile file = 6;
}

message WelcomeTemplateText {
  string content = 1;
}

message WelcomeTemplateImage {
  string url = 1;
}

message WelcomeTemplateLink {
  string title = 1;
  string picUrl = 2;
  string desc = 3;
  string url = 4;
}

message WelcomeTemplateMiniProgram {
  string title = 1;
  string appId = 2;
  string thumbImageUrl = 3;
  string pagePath = 4;
  string desc = 5;
}

message WelcomeTemplateVideo {
  string url = 1;
}

message WelcomeTemplateFile {
  string url = 1;
}
