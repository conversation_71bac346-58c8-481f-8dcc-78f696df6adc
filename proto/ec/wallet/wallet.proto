syntax = "proto3";

package mairpc.ec.wallet;

option go_package = "wallet";

import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/types/types.proto";

message BindPrepaidCardRequest {
  // @required
  //
  // 卡号
  string number = 1; // valid:"required"
  // @required
  //
  // 卡密
  string password = 2; // valid:"required"
  // @required
  //
  // 客户 ID
  string memberId = 3; // valid:"required,objectId"
  string channelId = 4;
}

message BindPrepaidCardResponse {
  // 标品储值卡 Id
  string cardId = 1;
  // 客户卡 Id
  string prepaidCardId = 2;
}

message UnbindPrepaidCardRequest {
  // @required
  //
  // 礼卡 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 客户 ID
  string memberId = 2; // valid:"required,objectId"
}

message GetPrepaidCardResponse {
  // 礼卡 ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 创建时间
  string createdAt = 3;
  // 更新时间
  string updatedAt = 4;
  // 客户 ID
  string memberId = 5;
  // 卡号
  string number = 6;
  // 卡密码
  string password = 7;
  // 卡提供商
  string provider = 8;
  // 面值
  uint32 totalAmount = 9;
  // 余额
  uint32 amount = 10;
  // 状态
  string status = 11;
  // 有效期开始时间
  string startAt = 12;
  // 有效期截止时间
  string endAt = 13;
  // 客户信息
  Member member = 14;
  // 有效期类型: （forever/relative）
  string validPeriodType = 15;
  // 操作时间
  string operationTime = 16;
  // 名称
  string name = 17;
  // 图片
  string picture = 18;
  // 几年内有效
  uint32 years = 19;
  // 撤销转赠时间
  string revokeGiftingAt = 20;
  // 信息
  string message = 21;
  // 来源: bought（购买的），received（领取的）、bound（绑定的）
  string source = 22;
  // 卡项名称
  string cardProductName = 23;
  // 卡片状态
  string cardStatus = 24;
  // 激活时间
  string activatedAt = 25;
  // 作废原因
  string reason = 26;
  // 操作人
  string operator = 27;
  // 操作人名称
  string operatorName = 28;
  // 赠送人 id
  string presenter = 29;
  // 赠送者名称
  string presenterName = 30;
  // 赠送者头像
  string presenterAvatar = 31;
}

message CreatePrepaidCardRecordRequest {
  // @required
  //
  // 礼卡 ID
  string prepaidCardId = 1; // valid:"required,objectId"
  // @required
  //
  // 记录类型，可选值：bound（绑定）、unbound（解绑）
  string type = 2; // valid:"in(bound|unbound)"
  // @required
  //
  // 客户 ID
  string memberId = 3; // valid:"required,objectId"
  // @required
  //
  // 卡号
  string number = 4; // valid:"required"
  // 面值
  uint32 totalAmount = 5;
  // 余额
  uint32 amount = 6;
  // 有效期开始时间
  string startAt = 7;
  // 有效期截止时间
  string endAt = 8;
}

message ListPrepaidCardRecordsRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // 记录类型
  //
  // 可选值：bound（绑定记录）、unbound（解绑记录）
  string type = 2; // valid:"in(bound|unbound)"
  // 查询关键字
  string searchKey = 3;
  // 查询关键字类型
  //
  // 传不同的类型会将 searchKey 按不同的字段进行查询，可传字段如下：
  // member_name_phone（根据客户姓名或手机号）、card_number（根据礼品卡号）
  string searchKeyType = 4;
  // 有效期
  mairpc.common.types.DateRange validDateRange = 5;
  // 绑定时间
  mairpc.common.types.DateRange boundDateRange = 6;
  // 面值
  mairpc.common.types.IntegerRange totalAmountRange = 7;
  // 余额
  mairpc.common.types.IntegerRange amountRange = 8;
  // 解绑时间
  mairpc.common.types.DateRange unboundDateRange = 9;
}

message ListPrepaidCardRecordsResponse {
  int64 total = 1;
  repeated PrepaidCardRecord items = 2;
}

message PrepaidCardRecord {
  // 记录 ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 创建时间
  string createdAt = 3;
  // 更新时间
  string updatedAt = 4;
  // 礼卡 ID
  string prepaidCardId = 5;
  // 记录类型
  string type = 6;
  // 客户 ID
  string memberId = 7;
  // 卡号
  string number = 8;
  // 面值
  uint32 totalAmount = 9;
  // 余额
  uint32 amount = 10;
  // 有效期开始时间
  string startAt = 11;
  // 有效期截止时间
  string endAt = 12;
  // 客户名
  string name = 13;
  // 手机号
  string phone = 14;
  // 绑定时间
  string boundAt = 15;
}

message CreatePrepaidCardHistoryRequest {
  // @required
  //
  // 礼卡 ID
  string prepaidCardId = 1; // valid:"required,objectId"
  // @required
  //
  // 记录类型，可选值：consume（消费）、refund（退款）
  string type = 2; // valid:"in(consume|refund)"
  // @required
  //
  // 客户 ID
  string memberId = 3; // valid:"required,objectId"
  // @required
  //
  // 卡号
  string number = 4; // valid:"required"
  // 消费或退款金额
  int64 amount = 5;
  // @required
  //
  // 订单 ID
  string orderId = 6; // valid:"required,objectId"
}

message ListPrepaidCardHistoriesRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // 记录类型
  //
  // 可选值：可选值：consume（消费）、refund（退款）、gift（赠送记录）
  string type = 2; // valid:"in(consume|refund|gift)"
  // 客户 ID
  string memberId = 3; // valid:"objectId"
  // 消费或退款时间
  mairpc.common.types.DateRange dateRange = 4;
  // 礼卡 ID
  string prepaidCardId = 5; // valid:"objectId"
  // 类型
  //
  // 可选值：可选值：consume（消费）、refund（退款）、gift（赠送记录）
  repeated string types = 6;
  // 搜索关键字
  string searchKey = 7;
  // 查询关键字类型
  //
  // 可选值：orderOrCardNumber（根据订单编号或卡号查）memberNamePhone（根据客户姓名手机号）
  string searchKeyType = 8;
  // 传 ture 表示查当前客户的接收记录
  bool isReceived = 9;
  string lastId = 10; // valid:"objectId"
}

message ListPrepaidCardHistoriesResponse {
  int64 total = 1;
  repeated PrepaidCardHistory items = 2;
}

message PrepaidCardHistory {
  // 记录 ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 创建时间
  string createdAt = 3;
  // 更新时间
  string updatedAt = 4;
  // 礼卡 ID
  string prepaidCardId = 5;
  // 记录类型
  string type = 6;
  // 客户 ID
  string memberId = 7;
  // 卡号
  string number = 8;
  // 消费或退款金额
  int64 amount = 9;
  // 订单 ID
  string orderId = 10;
  // 礼卡余额
  int64 balance = 11;
  // 订单编号
  string orderNumber = 12;
  // 客户名称
  string memberName = 13;
  // 客户手机号
  string memberPhone = 14;
  // 转赠信息
  GiftingInfo giftingInfo = 15;
  // 礼卡名称
  string prepaidCardName = 16;
  // 礼卡图片
  string picture = 17;
  // 面值
  uint32 totalAmount = 18;
  // 客户头像
  string memberAvatar = 19;
  // 卡项名称
  string cardProductName = 20;
  // 礼卡编码
  string prepaidCardNumber = 21;
  // 卡提供商
  string provider = 22;
  // 群脉储值卡 ID
  string storedValueCardId = 23;
  // 礼卡类型
  string cardType = 24;
  // 原因
  string reason = 25;
  // 门店代码
  string storeCode = 26;
  // 门店名称
  string storeName = 27;
}

message GiftingInfo {
  string status = 1;
  string receivedAt = 2;
  string message = 3;
  MemberInfo receiver = 4;
}

message MemberInfo {
  string id = 1;
  string name = 2;
  string avatar = 3;
  string phone = 4;
}

message ListPrepaidCardsRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // 是否可用
  mairpc.common.types.BoolValue isUsable = 2;
  // 客户 ID
  string memberId = 3; // valid:"objectId"
  // 卡提供商 可选值：gansomymyti（元祖礼卡）
  string provider = 4; // valid:"in(gansomymyti|mai|alissant)"
  // 查询关键字
  string searchKey = 5;
  // 查询关键字类型
  //
  // 传不同的类型会将 searchKey 按不同的字段进行查询，可传字段如下：
  // member_name_code（根据客户姓名或编码）、card_number（根据礼品卡号）、number_member_name_phone（卡号、客户姓名和手机号）
  string searchKeyType = 6;
  // 有效期
  mairpc.common.types.DateRange validDateRange = 7;
  // 面值
  mairpc.common.types.IntegerRange totalAmountRange = 8;
  // 余额
  mairpc.common.types.IntegerRange amountRange = 9;
  // 绑定状态
  //
  // 可选值：bound（绑定中）、unbound（已解绑）
  repeated string status = 10;
  // 返回外字段信息
  //
  // 可选值：member（传该值返回客户信息）, operator（传该值返回操作人信息）
  repeated string fields = 11;
  // 卡号
  repeated string numbers = 12;
  // 礼品卡 ids
  repeated string ids = 13;
  // 卡密
  repeated string passwords = 14;
  // 卡片状态
  //
  // 可选值：inactive（未激活）、gifting（转赠中）、returned（转赠超时已退回）
  repeated string cardStatus = 15;
  // 激活时间
  mairpc.common.types.StringDateRange activatedAt = 16;
  // 绑定时间
  mairpc.common.types.StringDateRange boundAt = 17;
  // 是否查询储值卡的 id
  bool searchStoredValueCardId = 18;
}

message GetMemberValidPrepaidCardsRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // 客户 ID
  //
  // @required
  string memberId = 2; // valid:"required,objectId"
  // 是否查询储值卡的 id
  bool searchStoredValueCardId = 3;
}

message ListPrepaidCardsResponse {
  // 数量
  uint64 total = 1;
  // 列表
  repeated PrepaidCard items = 2;
}

message PrepaidCard {
  // 礼卡 ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 创建时间
  string createdAt = 3;
  // 更新时间
  string updatedAt = 4;
  // 礼卡状态
  string status = 5;
  // 客户 ID
  string memberId = 6;
  // 卡号
  string number = 7;
  // 面值
  uint32 totalAmount = 8;
  // 余额
  uint32 amount = 9;
  // 有效期开始时间
  string startAt = 10;
  // 有效期截止时间
  string endAt = 11;
  // 密码
  string password = 12;
  // 序列号
  string serialNum = 13;
  // 客户信息
  Member member = 14;
  // 有效期类型: （forever/relative）
  string validPeriodType = 15;
  // 操作时间
  string operationTime = 16;
  // 名称
  string name = 17;
  // 图片
  string picture = 18;
  // 几年内有效
  uint32 years = 19;
  // 撤销转赠时间
  string revokeGiftingAt = 20;
  // 信息
  string message = 21;
  // 来源: bought（购买的），received（领取的）、bound（绑定的）
  string source = 22;
  // 卡项名称
  string cardProductName = 23;
  // 卡片状态
  string cardStatus = 24;
  // 激活时间
  string activatedAt = 25;
  // 作废原因
  string reason = 26;
  // 操作人
  string operator = 27;
  // 操作人名称
  string operatorName = 28;
  // 转赠人
  string presenter = 29;
  // 绑定时间
  string boundAt = 30;
  // 对应线上卡用户激活时间、对应线下卡用户绑定时间
  //
  // 生效时间
  string validFrom = 31;
  // 爱立颂的序列号
  string cardId = 32;
  // 储值卡 id
  string storedValueCardId = 33;
  // 不可用原因
  string invalidReason = 34;
  // 卡项标识
  string cardTypeNumber = 35;
  // 适用品牌 ID
  repeated string applicableBrandIds = 36;
  // 适用标签
  mairpc.common.ec.ApplicableProductTag applicableProductTag = 37;
  // 适用品类
  mairpc.common.ec.ApplicableCategory applicableCategory = 38;
  // 适用商品 sku
  repeated mairpc.common.ec.ApplicableProductSkus applicableProductSkus = 39;
  // 不可用商品列表
  repeated InvalidProduct invalidProducts = 40;
  // 储值卡类型，online/offline
  string type = 41;
}

message InvalidProduct {
  // 商品 id
  string id = 1;
  // 商品图片
  string picture = 2;
}

message Member {
  // 客户 ID
  string id = 1;
  // 客户姓名
  string name = 2;
  // 客户头像
  string avatar = 3;
  // 客户手机号
  string phone = 4;
}

message PrepaidCardsTotalAmountRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  //
  // 卡提供商 可选值：gansomymyti（元祖礼卡）
  string provider = 4; // valid:"in(gansomymyti|mai|alissant)"
}

message PrepaidCardsTotalAmountResponse {
  // 总余额
  uint32 totalAmount = 1;
}

message PrepaidCardDefrayRequest {
  // @required
  //
  // 礼品卡 ID
  string prepaidCardId = 1; // valid:"required,objectId"
  // @required
  //
  // 支付金额 单位：分
  uint64 amount = 2; // valid:"required"
  // @required
  //
  // 客户 ID
  string memberId = 3; // valid:"required,objectId"
  // 订单 ID
  string orderId = 4; // valid:"objectId"
  // @required
  //
  // 订单编号/第三方订单编号
  string orderNumber = 5; // valid:"required"
  // 门店代码
  string storeCode = 6;
  // 门店名称
  string storeName = 7;
}

message PrepaidCardDefrayResponse {
  // 交易流水号
  string tradeNo = 1;
  // 第三方原始终端号
  string outTradeSeq = 2;
  // 第三方原始交易流水号
  string transactionId = 3;
}

message RevokePrepaidCardDefrayRequest {
  // @required
  //
  // 礼品卡 ID
  string prepaidCardId = 1; // valid:"required,objectId"
  // @required
  //
  // 撤销金额 单位：分
  uint64 amount = 2; // valid:"required"
  // @required
  //
  // 客户 ID
  string memberId = 3; // valid:"required,objectId"
  // 订单 ID
  string orderId = 4; // valid:"objectId"
  // @required
  //
  // 原始流水号
  string tradeNo = 5; // valid:"required"
  // 订单编号
  string orderNumber = 6;
  // 原始交易流水号
  string transactionId = 7;
  // 交易时间
  string orderCreatedAt = 8;
  // 门店编号
  string storeCode = 9;
  // 原因
  string reason = 10;
}

message RefreshPrepaidCardRequest {
  // 卡号
  string number = 1;
  // 礼品卡 ID
  string prepaidCardId = 2; // valid:"objectId"
}

message PrepaidCardSettingRequest {
  // 卡号标题
  string title = 1;
  // @required
  //
  // 礼品卡规则
  string rule = 2; // valid:"required"
  // 小程序购买入口展示，为 true 时，小程序端不展示购买入口
  bool disablePurchaseEntrance = 3;
  // 协议
  repeated Agreement agreements = 4;
  // 允许解绑
  bool allowUnbound = 5;
  // 通知设置
  PrepaidCardNoticeSetting noticeSetting = 6;
  // 自定义卡号名称
  string numberName = 7;
  // 是否优先使用储值卡支付
  bool prioritizeUseForPaymentEnabled = 8;
  // 转赠规则设置
  GiftingSetting giftingSetting = 9;
}

message Agreement {
  string id = 1;
  string name = 2;
}

message PrepaidCardNoticeSetting {
  // 领取通知是否启用
  bool isReceivedNoticeEnabled = 1;
  // 未领取通知是否启用
  bool isUncollectedNoticeEnabled = 2;
  // 即将过期通知是否启用
  bool isWillExpireNoticeEnabled = 3;
  // 过期前多少天
  int64 daysCount = 4;
}

message PrepaidCardSettingResponse {
  // 卡号标题
  string title = 1;
  // 礼品卡规则
  string rule = 2;
  // 小程序购买入口展示，为 true 时，小程序端不展示购买入口
  bool disablePurchaseEntrance = 3;
  // 协议文件
  repeated Agreement agreements = 4;
  // 小程序退款入口展示，为 false 时，小程序端不展示退款入口
  bool enableRefundEntrance = 5;
  // 允许解绑
  bool allowUnbound = 6;
  // 通知设置
  PrepaidCardNoticeSetting noticeSetting = 7;
  // 自定义卡号名称
  string numberName = 8;
  // 是否优先使用储值卡支付
  bool prioritizeUseForPaymentEnabled = 9;
  // 转赠规则设置
  GiftingSetting giftingSetting = 10;
}

message GiftingSetting {
  // 赠送的卡片超过多少小时未被领取，自动退回，默认 24 小时
  int64 hourCount = 1;
}

message PostponePrepaidCardRequest {
  // @required
  //
  // 卡号
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 延期时间
  string endAt = 2; // valid:"required,rfc3339"
}

message CreateProductCardRequest {
  // @required
  //
  // 产品卡名称
  string name = 1; // valid:"required"
  // 产品卡图片
  string picture = 2;
  // @required
  //
  // 可兑换商品
  repeated ApplicableProduct applicableProducts = 3; // valid:"required"
  // 价格
  uint64 price = 4;
  // @required
  //
  // 有效期
  ValidPeriod validPeriod = 5; // valid:"required"
  // @required
  //
  // 状态
  //
  // shelved（上架）、unshelved（下架）
  string status = 6; // valid:"required,in(shelved|unshelved)"
}

message ApplicableProduct {
  // 产品 ID
  string productId = 1;
  string sku = 2;
  string picture = 3;
}

message ValidPeriod {
  // @required
  //
  // 有效期类型
  string type = 1; // valid:"required,in(absolute|relative)"
  // 有效期开始日期
  string startAt = 2;
  // 有效期结束日期
  string endAt = 3;
  // 动态有效期（天）
  uint64 daysCount = 4;
}

message UpdateProductCardRequest {
  // 产品卡 ID
  string id = 1;
  string name = 2;
  string picture = 3;
  // 价格
  uint64 price = 4;
  // 有效期
  ValidPeriod validPeriod = 5;
  string status = 6; // valid:"in(shelved|unshelved)"
}

message UpdateProductCardStatusRequest {
  // 产品卡 Ids
  repeated string ids = 1;
  // 商品 Ids
  repeated string productIds = 2;
  // @required
  //
  // 产品卡状态
  string status = 3; // valid:"required,in(shelved|unshelved)"
}

message ListProductCardsRequest {
  // 产品卡名称
  string name = 1;
  // 状态
  string status = 2; // valid:"in(shelved|unshelved)"
  repeated string ids = 3; // valid:"objectIdList"
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 4;
}

message ListProductCardsResponse {
  uint64 total = 1;
  repeated ProductCardDetail items = 2;
}

message ProductCard {
  string id = 1;
  // 产品卡名称
  string name = 2;
  string picture = 3;
  uint64 price = 4;
  // 有效期
  ValidPeriod validPeriod = 5;
  string status = 6;
  string number = 7;
  repeated ApplicableProduct applicableProducts = 8;
  uint64 sales = 9;
  uint64 usedCount = 10;
  string shelvedAt = 11;
  string unshelvedAt = 12;
}

message UpsertProductCardSettingRequest {
  // @required
  //
  // 封面图片
  //
  // 至少上传 1 张封面图片，最多上传 10 张封面图片
  repeated Image images = 1; // valid:"required"
  // 购买须知
  string description = 2;
  // 运费设置
  //
  // freeShipping（包邮）、default（使用系统运费设置）
  string deliveryFeeType = 3; // valid:"in(freeShipping|default)"
  // 领取通知
  Notice receivedNotice = 4;
  // 退回通知
  Notice refundedNotice = 5;
  // 过期通知
  ExpiredNotice expiredNotice = 6;
}

message ProductCardSetting {
  string id = 1;
  // 封面图片
  repeated Image images = 2;
  // 购买须知
  string description = 3;
  // 运费设置
  //
  // freeShipping（包邮）、default（使用系统运费设置）
  string deliveryFeeType = 4;
  // 领取通知
  Notice receivedNotice = 5;
  // 退回通知
  Notice refundedNotice = 6;
  // 过期通知
  ExpiredNotice expiredNotice = 7;
}

message Image {
  // 图片 URL
  string url = 1;
  // 排序权重
  uint64 weight = 2;
}

message Notice {
  // 是否开启通知提醒
  bool isEnabled = 1;
  // 通知设置
  NoticeSetting noticeSetting = 2;
}

message ExpiredNotice {
  // 是否开启通知提醒
  bool isEnabled = 1;
  // 类型
  //
  // hour（小时）、day（天）
  string type = 2; // valid:"in(hour|day)"
  // 过期前多少天或多少小时
  int64 timeCount = 3;
  // 通知设置
  NoticeSetting noticeSetting = 4;
}

message NoticeSetting {
  // 短信
  Text text = 1;
  // 订阅消息
  SubscribeMessage subscribeMessage = 2;
  // 动态参数
  repeated PlaceholderRules placeholderRules = 3;
}

message Text {
  // 短信内容
  string content = 1;
}

message SubscribeMessage {
  // 订阅消息 ID
  string id = 1;
  // 打开小程序页面
  string page = 2;
  // 订阅消息内容
  repeated Data data = 3;
  // 订阅消息标题
  string title = 4;
  // 渠道 ID
  string channelId = 5;
}

message Data {
  // 订阅消息键
  string key = 1;
  // 订阅消息值
  string value = 2;
  // 名称
  string name = 3;
  // 颜色
  string color = 4;
}

message PlaceholderRules {
  // 占位符
  string placeholder = 1;
  // 占位符名称
  string name = 2;
  // 填充项
  Filler filler = 3;
}

message Filler {
  // 属性
  string property = 1;
  // map 参数值转换规则
  map<string, string> valueTransfer = 2;
}

message ProductCardDetail {
  string id = 1;
  // 产品卡名称
  string name = 2;
  string picture = 3;
  uint64 price = 4;
  // 有效期
  ValidPeriod validPeriod = 5;
  string status = 6;
  string number = 7;
  repeated ApplicableProductDetail applicableProductDetails = 8;
  uint64 sales = 9;
  uint64 usedCount = 10;
  string shelvedAt = 11;
  string unshelvedAt = 12;
}

message ApplicableProductDetail {
  // 产品 ID
  string productId = 1;
  string name = 2;
  string sku = 3;
  string number = 4;
  string picture = 5;
  repeated string properties = 6;
  // 商品状态
  //
  // shelved（上架）、unshelved（下架）
  string status = 7;
  // 是否已删除
  bool isDeleted = 8;
}

message GetPrepaidSourceRequest {
  // @required
  //
  // 卡号
  string number = 1; // valid:"required"
  // @required
  //
  // 卡密
  string password = 2; // valid:"required"
}

message GetPrepaidSourceResponse {
  // 卡号
  string cardNo = 1;
  // 卡状态
  string cardStat = 2;
  // 卡属性
  string cardAttr = 3;
  // 开卡日期
  string openDate = 4;
  // 启用日期
  string issueDate = 5;
  // 有效期
  string expDate = 6;
  // 卡密码
  string cardMm = 7;
  // 余额
  string accBala = 8;
  // 抵扣余额
  string othBala = 9;
  // 已冻结余额
  string fzeBala = 10;
  // 参考面额
  string value = 11;
  // 用户号
  string userId = 12;
}

message PartialRevokeAmountRequest {
  // @required
  //
  // 礼品卡 ID
  string prepaidCardId = 1; // valid:"required,objectId"
  // @required
  //
  // 撤销金额 单位：分
  uint64 amount = 2; // valid:"required"
  // @required
  //
  // 客户 ID
  string memberId = 3; // valid:"required,objectId"
  // @required
  //
  // 订单 ID
  string orderId = 4; // valid:"required,objectId"
  // 订单编号
  string orderNumber = 5;
  // @required
  //
  // 原始终端流水号 对应 ec.prepaidCardHistory.OutTradeNo
  string tradeNo = 6; // valid:"required"
  // 原始终端号 对应 ec.prepaidCardHistory.OutTradeSeq
  string tradeSeq = 7;
  // 原始交易流水 对应 ec.prepaidCardHistory.TransactionId
  string systemSeq = 8;
  // 原始交易日期 (rfc3339)
  string systemDate = 9;
  // 附加信息
  string resv = 10;
  // 附属号
  string pertainNo = 11;
  // 退款交易编号
  string refundTradeNo = 12;
  // 门店编号
  string storeCode = 13;
  // 原因
  string reason = 14;
}

message UpdatePrepaidCardPictureRequest {
  // @required
  //
  // prepaidCardId
  string id = 1; // valid:"required"
  // @required
  //
  // 卡面
  string picture = 2; // valid:"required"
  // @required
  //
  // 会员 Id
  string memberId = 3; // valid:"required"
}
