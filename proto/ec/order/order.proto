syntax = "proto3";

package mairpc.ec.order;

option go_package = "order";

import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/types/types.proto";
import "ec/marketing/presentCampaign.proto";
import "ec/order/cart.proto";
import "ec/store/store.proto";
import "ec/wallet/wallet.proto";
import "member/member_paid_card.proto";

message OrderDetail {
  // 订单 ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 客户 ID
  string memberId = 3;
  // 订单编号
  string number = 4;
  // 配送方式
  //
  // pickup 自提，express 邮寄，cityExpress 同城快送
  string method = 5;
  // 提货码
  string pickupCode = 6;
  // 门店 ID
  store.StoreDetail store = 7;
  // 订单总金额
  //
  // 单位：分
  uint64 totalAmount = 8;
  // 实际支付金额
  //
  // 单位：分
  uint64 payAmount = 9;
  // 支付方式
  string payment = 10;
  // 交易流水号
  string tradeNo = 11;
  // 支付时间
  string paidAt = 12;
  // 订单状态
  //
  // 订单状态值：unpaid 待付款，paid 待接单，unassigned 待分配，accepted 待提货/待收货，partialShipped 部分发货，shipped 已发货，completed 交易成功，canceled 交易关闭
  string status = 13;
  // 退款状态
  //
  // 退款状态值：refunding 退款中，refunded 已退款，failed 退款失败
  string refundStatus = 14;
  // 备注
  string remarks = 15;
  // 提货信息
  mairpc.common.ec.ReservationInfo reservation = 16;
  // 购买商品列表
  repeated OrderProduct products = 17;
  // 订单历史
  repeated OrderHistory histories = 18;
  // 创建时间
  string createdAt = 19;
  // 更新时间
  string updatedAt = 20;
  // 是否被客户标记已删除
  bool memberDeleted = 21;
  // 用户留言
  string message = 22;
  // 物流信息
  LogisticsInfo logistics = 23;
  // 提货人/收货人信息
  ContactCoordinateInfo contact = 24;
  // 客户渠道信息
  ChannelInfo channel = 25;
  // 操作人
  //
  // 操作人指明了订单的分配方；
  // staff: 店员，即订单分配到了门店； user: 后台用户，即订单分配到了总部
  string operator = 26;
  // 使用积分个数
  uint64 payScore = 27;
  // 积分抵扣金额
  uint64 deductAmountByScore = 28;
  // 订单参与的活动
  repeated mairpc.common.ec.CampaignDetail campaigns = 29;
  // 自定义数据，json 格式的字符串
  string extra = 30;
  // 订单是否评价
  bool isCommented = 31;
  // 订单标签
  //
  // 可选值：self_purchase 自购，daigou 代购，share 分享，scan_buy 扫码购
  repeated string tags = 32; // valid:"in(self_purchase|daigou|share|scan_buy)"
  // oms 处理信息
  OmsProcessor omsProcessor = 33;
  // 发票信息
  mairpc.common.ec.Invoice invoice = 34;
  // 商品附件信息
  repeated ProductAccessoryDetail productAccessories = 35;
  // 礼品卡信息
  repeated mairpc.common.ec.PrepaidCardDetail prepaidCards = 36;
  // 交易记录
  repeated TradeRecord tradeRecords = 37;
  // 是否已进行过预付
  bool isPrePaid = 38;
  // 订单类型
  //
  // 返回值：virtual（虚拟商品订单），coupon（付费卡券商品订单）
  string type = 39;
  // 不允许客户发起退款，默认为 false 允许客户发起退款
  bool disableMemberRefund = 40;
  // 折扣信息
  repeated mairpc.common.ec.DiscountInfo discounts = 41;
  // 买家信息
  OrderMember member = 42;
  // 是否已退运费
  bool isDeliveryFeeRefunded = 43;
  // 使用的权益卡
  repeated MemberPaidCard memberPaidCards = 44;
  // 分销信息
  OrderDistribution distribution = 45;
  // 自提口令
  string pickupPassword = 46;
  // 储值支付
  mairpc.common.ec.StoredValue storedValue = 47;
  // 是否用户支付首单
  bool isFirstPaid = 48;
  // 小票信息
  mairpc.common.ec.Ticket ticket = 49;
  // 提货单号
  string pickupNumber = 50;
  // 提货单号（提货日期），例如：925（0228）
  string pickupNumberWithPickupDate = 51;
  // 完成时间
  string completedAt = 52;
}

message OrderDistribution {
  // 分销员 Id
  string promoterId = 1;
  // 分销员类型
  //
  // 导购分销（staff），大中分销（member）
  string promoterType = 2;
  // 此单分销总佣金
  uint64 amount = 3;
  // 此单分销总佣金
  string profitSharingType = 4;
  // 发起结算时间
  string profitSharingAt = 5;
  // 结算状态
  string profitSharingStatus = 6;
  // 结算失败信息
  string profitFailedMsg = 7;
  // 账单 id
  //
  // 仅月结有值
  string transferBillId = 8;
  // 其他信息
  //
  // 记录如无佣金原因等内容，json 格式字符串
  string extra = 9;
}

message TradeRecord {
  // 交易记录 ID
  string id = 1;
  // 交易编号
  string tradeNo = 2;
  // 交易状态
  string status = 3;
  // 支付金额
  uint64 payAmount = 4;
  // 创建时间
  string createdAt = 5;
  // 支付时间
  string paidAt = 6;
}

message OmsProcessor {
  // 推送状态
  //
  // pending：待推送，processing：推送中，pushed：已推送，failed：推送失败
  string pushStatus = 1;
  // 上次推送时间
  string pushedAt = 2;
}

message ChannelInfo {
  // 渠道 ID
  string channelId = 1;
  // 渠道下客户唯一身份信息
  string openId = 2;
  // 渠道来源
  string origin = 3;
}

message ContactRequest {
  // 提货人姓名
  string name = 1;
  // 提货人手机号
  string phone = 2;
}

message OrderMember {
  // 买家客户 ID
  string id = 1;
  // 买家名称
  string name = 2;
  // 买家手机号
  string phone = 3;
  // 头像
  string avatar = 4;
}

message ContactInfo {
  // 提货人姓名
  string name = 1;
  // 提货人手机号
  string phone = 2;
  // 联系地址
  mairpc.common.types.Address address = 3;
}

message ContactCoordinateInfo {
  // 提货人姓名
  string name = 1;
  // 提货人手机号
  string phone = 2;
  // 联系地址
  mairpc.common.types.AddressCoordinate address = 3;
}

message ProductAccessoryDetail {
  // 附件 id
  string id = 1;
  // 附件名
  string name = 2;
  // 附件类型
  //
  // 标准附件 normal，自定义附件 custom
  string type = 3;
  // 附件数量
  //
  // 标准附件时有意义
  uint64 count = 4;
  // 自定义附件 message
  string message = 5;
}

message LogisticsInfo {
  // 邮费
  //
  // 单位：分
  uint64 fee = 1;
  // 发货自
  //
  // self 线下联系快递，oms 订单管理系统，self_delivery 自行配送
  string processBy = 2;
  // 发货时间
  string shippedAt = 3;
  // 期望送达时间
  string expectDeliveryAt = 4;
  // 期望送达时间文本
  string expectDeliveryAtLabel = 5;
  // 免邮说明
  //
  // memberPrivilege: 会员权益免邮, groupon: 拼团包邮,packageCampaign:促销套餐包邮,optionalPackageCampaign:任选打包包邮,presentCampaign:满减赠,bargainCampaign:砍价,benefitCard:权益卡包邮
  string freeReason = 6;
}

message OrderProduct {
  // 商品 ID
  string id = 1;
  // 名称
  string name = 2;
  // 图片
  string picture = 3;
  // 数量
  uint64 total = 4;
  // 商品价格
  //
  // 单位：分
  uint64 price = 5;
  // 总价
  //
  // 单位：分
  uint64 totalAmount = 6;
  // 实际支付
  //
  // 单位：分
  uint64 payAmount = 7;
  // 商品退款状态
  //
  // 退款状态值为：waitingAudit 待审核，rejected 已拒绝，approved 已同意，returned 买家已退货，pending/refunding 退款中，refunded 已退款，failed 退款失败，canceled 退款关闭
  string refundStatus = 8;
  // 商品规格
  OrderProductSpec spec = 9;
  // 折扣信息
  repeated mairpc.common.ec.DiscountInfo discounts = 10;
  // 商品编码
  string number = 11;
  // 退款单号
  string orderRefundId = 12;
  // 商品物流信息
  repeated OrderProductLogistics logistics = 13;
  // 使用积分个数
  uint64 payScore = 14;
  // 商品参与的活动
  repeated mairpc.common.ec.CampaignDetail campaigns = 15;
  // 商品是否被评价
  bool isCommented = 16;
  // 类目 id
  string categoryId = 17;
  // 划线价
  //
  // 单位：分
  uint64 originPrice = 18;
  // 核销码
  repeated PickupCodeDetail pickupCodes = 19;
  // 核销设置
  RedeemSetting redeemSetting = 20;
  // 商品类型
  //
  // virtual（虚拟商品），coupon（付费卡券商品）,entity（实体商品）
  string type = 21;
  // 客户领取优惠券 ids
  repeated string membershipDiscountIds = 22;
  // 优惠券 id
  string couponId = 23;
  // 订单货品的唯一 ID
  string outTradeId = 24;
  // 退款数量
  //
  // 用于和 total 对比判断是部分退款还是全部退款
  int64 refundTotal = 25;
  // CEP 基础商品 ID
  string productId = 26;
  // 退款金额
  uint64 refundAmount = 27;
  // 是否是赠品
  bool isPresent = 28;
  // 虚拟商品核销有效期
  mairpc.common.ec.RedeemPeriod redeemPeriod = 29;
  // 不允许客户发起退款，默认为 false 允许客户发起退款
  bool disableMemberRefund = 30;
  // 礼卡支付信息
  repeated OrderProductPrepaidCard prepaidCards = 31;
  // 总支付（礼卡+微信）
  //
  // 单位：分
  uint64 totalPayAmount = 32;
  // 商品初始数量
  uint64 originTotal = 33;
  // 佣金比例
  float distributionProportion = 34;
  // 佣金
  uint64 distributionAmount = 35;
  // 门店佣金
  uint64 storeDistributionAmount = 36;
  // 活动标签
  repeated string campaignTags = 37;
  // 储值支付
  mairpc.common.ec.StoredValue storedValue = 38;
  // 商品子类型
  //
  // coupon（优惠券）,storedValueCard（储值卡）
  string subType = 39;
  // 储值卡 id
  string storedValueCardId = 40;
  // 客户购买的储值卡 ids
  repeated string memberPrepaidCardIds = 41;
}

message OrderProductPrepaidCard {
  // 礼卡 id
  string id = 1;
  // 礼卡卡号
  string number = 2;
  // 抵扣金额
  int64 amount = 3;
}

message PickupCodeDetail {
  // 核销码
  string code = 1;
  // 核销码状态
  string status = 2;
  // 核销门店
  LogisticsProcessByDetail store = 3;
  // 核销员工
  LogisticsProcessByDetail staff = 4;
  // 核销管理员
  LogisticsProcessByDetail user = 5;
  // 核销时间
  string redeemedAt = 6;
  // 核销人员类型
  //
  // user：后台管理员，staff：店员
  string operatorType = 7;
  // 核销有效期
  PickupCodeRedeemPeriod redeemPeriod = 8;
}

message PickupCodeRedeemPeriod {
  // 核销有效期类型: limit（限制时间）、nolimit（不限时间）
  string type = 1;
  // 开始时间
  string startAt = 2;
  // 结束时间
  string endAt = 3;
  // 状态
  string status = 4;
}

// 核销人员信息
message LogisticsProcessByDetail {
  string id = 1;
  string name = 2;
  string phone = 3;
}

message RedeemSetting {
  // 核销范围，所有门店（all）、指定门店（limit）、购买门店（service）、自定义核销地点（custom）
  string scope = 1;
  // 指定门店核销 ids
  repeated string storeIds = 2;
  string custom = 3; // 自定义核销地点，scope 为 custom 时必填
}

message OrderProductSpec {
  string sku = 1;
  repeated string properties = 2;
  string externalSku = 3;
  uint64 weight = 4;
}

message OrderProductLogistics {
  // 物流公司 ID
  string deliveryId = 1;
  // 物流公司名称
  string deliveryName = 2;
  // 物流单号
  string waybillId = 3;
  // 发货时间
  string shippedAt = 4;
  // 包含货物数量
  uint64 productCount = 5;
  // 配送人信息
  Courier courier = 6;
}

message OrderRefundHistory {
  // 订单状态
  string status = 1;
  // 退款方式
  //
  // 仅退款：onlyRefund，退货退款：returnAndRefund
  string refundType = 2;
  // 备注信息
  string remarks = 3;
  // 操作员
  string operator = 4;
  // 创建时间
  string createdAt = 5;
  // 与业务有关的存储信息
  //
  // json 格式的字符串
  string extra = 6;
  // 客户退款备注
  string description = 7;
  // 客户退款凭证
  Certificate certificate = 8;
}

message OrderHistory {
  // 门店 ID
  string storeId = 1;
  // 订单状态
  string status = 2;
  // 备注信息
  string remarks = 3;
  // 操作员
  string operator = 4;
  // 创建时间
  string createdAt = 5;
  // 与业务有关的存储信息
  //
  // json 格式的字符串
  string extra = 6;
}

message CancelOrderRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 原因
  string reason = 2; // valid:"required"
  // @required
  //
  // 客户 ID
  string memberId = 3; // valid:"required,objectId"
}

message RemindBalancePaymentRequest {
  // 预售活动 id
  string id = 1; // valid:"objectId"
  // 消息规则
  string notificationRule = 2;
  // 动态参数 map
  map<string, string> placeholderMap = 3;
}

message HandleCampaignUnpaidOrdersRequest {
  repeated Ids campaignIds = 1;
}

message Ids {
  // 类型，deposit_end_campaign_ids（定金结束活动 ids）、balance_end_campaign_ids（尾款结束活动 ids）
  string type = 1;
  repeated string ids = 2; // valid:"objectIdList"
}

message RefundOrderRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // 是否为取消订单
  //
  // 是否在前端直接点击了取消订单按钮
  bool isCancelOrder = 2;
  // 部分退款的商品列表
  repeated ProductSKU productSkus = 3;
  // @required
  //
  // 客户 ID
  string memberId = 4; // valid:"required,objectId"
  // 退款方式
  //
  // 退货退款：returnAndRefund，仅退款：onlyRefund（默认）
  string refundType = 5; // valid:"in(returnAndRefund|onlyRefund)"
  // @required
  //
  // 退款原因
  string reason = 6; // valid:"required"
  // 货物状态
  //
  // 已收到货: received，未收到货: notReceived
  string deliveryStatus = 7;
  // 退款说明
  string description = 8;
  // 是否分配至下单门店
  bool assignToFirstStore = 9;
  // 虚拟商品退款类型
  //
  // 可选值：all：（全部退款，包括已使用），unused（只退未使用）
  string virtualRefundType = 10; // valid:"in(all|unused)"
  // 是否是系统退款
  //
  // 系统退款会处理为后台退款，operator 类型为 user
  bool isSystem = 11;
  // 自定义退款金额 单位：分
  //
  // 多个商品退款时，按照实付金额分摊到每个商品
  uint64 refundAmount = 12;
  // 自定义退款金额原因
  string customRefundAmountReason = 13;
  // 是否退运费
  bool isRefundDeliveryFee = 14;
  // 微信侧售后 id
  int64 wechatRefundId = 15;
  // 退款备注
  string remarks = 16;
  // 退款凭证
  Certificate certificate = 17;
}

message Certificate {
  // 凭证图片
  repeated Image images = 1;
  // 凭证视频
  Video video = 2;
}

message Image {
  string url = 1;
}

message Video {
  string url = 1;
}

message ProductSKU {
  // 商品 ID
  string id = 1;
  // 商品 SKU
  string sku = 2;
  // 商品折扣 ID，用于区分同规格商品和赠品
  string discountId = 3;
  // 商品活动 ID，用于区分套餐商品和普通商品
  string campaignId = 4;
  // 套餐 ID 用于区分相同类型套餐的商品
  string packageId = 5;
  // 用于确定订单商品的唯一 id
  //
  // 优先使用此字段
  string outTradeId = 6;
}

message RefundOrderResponse {
  // 退款单 Id
  string id = 1;
}

message GetOrderRefundRequest {
  // 退款单 Id
  string id = 1; // valid:"objectId"
  // 客户 Id
  string memberId = 2;
  string orderId = 3;
  // 退款编号
  string number = 4;
}

message UpdateOrderRefundRequest {
  // 退款单 Id
  string id = 1;
  // 操作类型
  //
  // 后台同意退货：agreeToReturn、确认收货并退款：confirmReceiptAndRefund、同意退款：agreeToRefund、拒绝退款：refundRefused
  // 用户修改请求：updateApplication、用户取消申请：applicationCanceled、用户退货：goodsReturned
  string operateType = 2; // valid:"in(agreeToReturn|confirmReceiptAndRefund|goodsReturned|agreeToRefund|refundRefused|updateApplication|applicationCanceled)"
  // 后台拒绝原因
  string rejectedReason = 3;
  // 用户修改申请原因
  string refundReason = 4;
  // 用户退货信息
  RefundLogisticsInfo refundLogistics = 5;
  // 客户 ID
  string memberId = 6; // valid:"objectId"
  // 退款说明
  string description = 7;
  // 退款方式
  //
  // 退货退款：returnAndRefund，仅退款：onlyRefund
  string refundType = 8; // valid:"in(returnAndRefund|onlyRefund)"
  // 货物状态
  //
  // 已收到货: received，未收到货: notReceived
  string deliveryStatus = 9;
  // 店员 ID
  string staffId = 10; // valid:"objectId"
  // 退款订单编号
  string number = 11;
  // 此次更新是否是微信端发起
  bool isWechatUpdate = 12;
  // 自定义退款金额 单位：分
  uint64 refundAmount = 13;
  // 退款变动原因
  string refundAmountChangeReason = 14;
  // 是否退运费
  bool isRefundDeliveryFee = 15;
  // 退款备注
  string remarks = 16;
  // 退款凭证
  Certificate certificate = 17;
}

message RefundLogisticsInfo {
  // @required
  //
  // 快递公司
  string deliveryName = 1; // valid:"required"
  // @required
  //
  // 运单号
  string waybillId = 2; // valid:"required"
}

message AssignOrderRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
}

message UpdateRemarksRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 备注
  string remarks = 2; // valid:"required,runelength(1|200)"
}

message AcceptOrderRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // 店员 ID
  string staffId = 2; //valid:"objectId"
  // 订单标签
  repeated string tags = 3;
}

message RejectOrderRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 店员 ID
  string staffId = 2; //valid:"required,objectId"
  // @required
  //
  // 原因类型
  string type = 3; // valid:"required,in(shortage|others)"
  // 原因内容
  //
  // 当 type 为 others 时必填
  string reason = 4;
  // 缺货商品列表
  repeated ProductSKU productSkus = 5;
}

message RedeemOrderRequest {
  // 店员 ID
  string staffId = 1; // valid:"objectId"
  // @required
  //
  // 提货码或提货口令
  string code = 2; // valid:"required"
  // dms.distributor._id
  string storeId = 3; // valid:"objectId"
  // 订单 ID
  string orderId = 4; // valid:"objectId"
  // 商品 ID
  string productId = 5; // valid:"objectId"
  // 订单商品项唯一 ID
  string outTradeId = 6; // valid:"objectId"
}

message CompleteOrderRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // 店员 ID
  string staffId = 2; // valid:"objectId"
  // 原因
  string reason = 3;
  // 客户 ID
  string memberId = 4; // valid:"objectId"
  // 是否是自提订单
  bool containPickup = 5;
}

message ConfirmIssuedOrderInvoiceRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // 店员 ID
  string staffId = 2; // valid:"objectId"
  // 客户 ID
  string memberId = 3; // valid:"objectId"
  // 发票 ID
  string invoiceId = 4; // valid:"objectId"
}

message ListOrdersRequest {
  // 客户 ID
  string memberId = 1; // valid:"objectId"
  // 订单状态
  //
  // 可选值：unpaid 待付款，paid 待接单，unassigned 待分配，accepted 待提货/待发货，partialShipped 部分发货，shipped 已发货，completed 交易成功，canceled 交易关闭，uncommented 待评价
  repeated string status = 2;
  // 退款状态
  //
  // 可选值：waitingAudit 待审核，approved 同意申请，rejected 拒绝申请，returned 买家已退货，refunding 退款中，refunded 退款完成，failed 退款失败，canceled 退款关闭
  string refundStatus = 3; // valid:"in(waitingAudit|approved|rejected|returned|refunding|refunded|failed|canceled)"
  // 关键字类型
  //
  // 可选值：order，contact，logistics
  // 当为 order 时，按订单编号和订单中商品名称查找
  // 当为 contact 时，按联系人姓名和手机号查找
  // 当为 logistics 时，按物流单号查找
  // 当为 member 时，按照客户姓名查找
  // 当为 store 时，按照门店 Id 查询
  // 当为 remarks 时，按照备注查询
  string searchType = 4; // valid:"in(order|contact|logistics|member|store|remarks)"
  // 搜索关键字
  string searchKey = 5;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 6;
  // 门店 ID
  //
  // 店长小程序使用
  string storeId = 7;
  // 门店 Ids
  repeated string storeIds = 8;
  // 订单区域
  //
  // 多个区域使用json字符串
  string locations = 9;
  // 下单时间
  mairpc.common.types.DateRange createdTime = 10;
  // 订单实付金额
  //
  // 单位：分
  mairpc.common.types.IntegerRange payAmount = 11;
  // 历史状态
  //
  // 按曾经有的状态进行查询
  repeated string historyStatus = 12;
  // 配送方式
  //
  // 可选值：pickup 自提，express 邮寄，cityExpress 同城快送，无需快递
  string deliveryMethod = 13; // valid:"in(pickup|express|cityExpress|noExpress)"
  // 操作人
  //
  // 操作人指明了订单的分配方；
  // 可选值： staff 表示分配到门店；user 表示分配到总部（后台）
  string operator = 14;
  // 活动数据
  mairpc.common.ec.CampaignDetail campaign = 15;
  // 订单 ids
  repeated string ids = 16; // valid:"objectIdList"
  // 排除掉的活动
  repeated mairpc.common.ec.CampaignDetail excludedCampaigns = 17;
  // 订单标签
  //
  // 可选值：self_purchase 自购，daigou 代购，share 分享，distribution 经销订单，consignment 代销订单，代销商 ID，proxyOrder 代客下单订单
  repeated string tags = 18;
  // 排除掉的订单状态
  repeated string excludedStatus = 19; // valid:"in(unpaid|paid|unassigned|accepted|partialShipped|shipped|completed|canceled)"
  // 订单编号
  repeated string numbers = 20;
  // 排除订单标签
  //
  // 可选值：self_purchase 自购，daigou 代购，share 分享
  repeated string ignoreTags = 21; // valid:"in(self_purchase|daigou|share|proxyOrder)"
  // 修改时间
  mairpc.common.types.DateRange updatedAt = 23;
  // 配送方式
  //
  // 可选值：pickup 自提，express 邮寄，cityExpress 同城快送
  repeated string deliveryMethods = 24; // valid:"in(pickup|express|cityExpress)"
  // 使用的优惠类型
  //
  // 可选值：coupon 优惠券，score 积分抵扣，member 会员折扣，groupon 拼团活动，present 买赠券，delivery 运费券
  repeated string discountTypes = 25; // valid:"in(coupon|score|member|groupon|present|delivery)"
  // 未使用的优惠类型
  //
  // 可选值：coupon 优惠券，score 积分抵扣，member 会员折扣，groupon 拼团活动
  repeated string excludedDiscountTypes = 26; // valid:"in(coupon|score|member|groupon)"
  // 发票信息
  //
  // 待开票（pending），开票中（invoicing），已开票（issued），开票失败（failed），已关闭（closed），重新开票（rebilling）
  mairpc.common.ec.Invoice invoice = 27;
  // 是否包含用户删除的订单
  mairpc.common.types.BoolValue includeMemberDeleted = 28;
  // 支付方式
  string payment = 29; // valid:"in(wechat|prepaidCard|offline)"
  // 买家是否备注
  mairpc.common.types.BoolValue existsMessage = 30;
  // 筛选所属组织节点
  mairpc.common.types.DistributorSelector distributor = 31; // valid:"optional"
  // 拼团记录 id
  repeated string grouponRecordIds = 32;
  // 屏蔽类型
  //
  // 未屏蔽:unmask,拼团:groupon
  string maskType = 33;
  // 根据使用的权益卡筛选
  MemberPaidCard memberPaidCard = 34;
  // 是否不返回总记录数
  bool withoutTotal = 35;
  // 商品ID
  string productId = 36;
  repeated string origins = 37;
  // 自提口令
  string pickupPassword = 38;
  // 标签筛选时是否需要包含所有标签
  bool mustContainAllTags = 39;
  // 分佣记录 id，适用于导购小程序月结查询
  string transferBillId = 40;
  // OMS 推送状态
  //
  // pending 待推送，processing 推送中，pushed 已推送，failed 推送失败
  string omsPushStatus = 41;
  // 是否需要小票
  mairpc.common.types.BoolValue ticketNeeded = 42;
  // 订单完成时间
  mairpc.common.types.DateRange completedTime = 43;
  // 对于拆单的租户是否查询原单
  mairpc.common.types.BoolValue searchOriginalOrder = 44;
  // 使用的优惠 ids
  repeated string discountIds = 45;
  // 是否是分销订单
  mairpc.common.types.BoolValue isDistribution = 46;
}

message MemberPaidCard {
  // 权益卡 ID
  string id = 1; // valid:"objectId"
  // 权益卡类型
  //
  // 权益卡（benefitCard）
  string type = 2; // valid:"in(benefitCard)"
  // 权益卡记录 ID
  string recordId = 3; // valid:"objectId"
  // 客户权益卡 number
  string number = 4;
}

message OrderLocationFilter {
  // 省
  string province = 1;
  // 市
  string city = 2;
  // 区
  string district = 3;
}

message ListOrdersResponse {
  // 数量
  uint64 total = 1;
  // 列表
  repeated OrderDetail items = 2;
}

message ListOrderRefundsRequest {
  // 退款编号
  string refundNumber = 1;
  // 订单号
  string orderNumber = 2;
  // 商品名称
  string productName = 3;
  // 退款状态
  //
  // 可选值：waitingAudit 待审核，rejected 已拒绝，approved 已同意，returned 已退货，pending/refunding 退款中，refunded 已退款，failed 退款失败，canceled 退款关闭
  repeated string status = 4;
  // 退款来源
  //
  // 客户发起：member，后台发起：portal
  string type = 5;
  // 退款方式
  //
  // 退货退款：returnAndRefund，仅退货：onlyRefund
  string refundType = 6; // valid:"in(returnAndRefund|onlyRefund)"
  // 下单时间
  mairpc.common.types.DateRange orderCreatedTime = 7;
  // 退款时间
  //
  // 发起退款的时间
  mairpc.common.types.DateRange createdTime = 8;
  // 审核时间
  //
  // 审核退款申请的时间
  mairpc.common.types.DateRange auditTime = 9;
  // 联系人/收货人信息
  string contact = 10;
  // 配送方式
  //
  // 门店自提 pickup、快递配送 express 同城快送 cityExpress、无需配送 noExpress
  string method = 11; // valid:"in(express|pickup|cityExpress|noExpress)"
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 12;
  // 门店 ID
  string storeId = 13; // valid:"objectId"
  // 操作人
  //
  // staff: 店员，即退款单分配到了门店； user: 后台用户，即退款单分配到了总部
  string operator = 14; // valid:"in(staff|user)"
  // 交易状态
  //
  // 可选值 待发货 accepted、待收货 shipped、交易完成 completed
  string orderStatus = 15;
  // 完成退款时间
  mairpc.common.types.DateRange refundedTime = 16;
  // 关联订单 ID
  repeated string orderIds = 17;
  // 客户 ID
  string memberId = 18; // valid:"objectId"
  // 发票状态
  //
  // pending 开票中、issued 已开票、closed 已关闭
  string invoiceStatus = 19;
  // 修改时间
  mairpc.common.types.DateRange updatedTime = 20;
  // 筛选所属组织节点
  mairpc.common.types.DistributorSelector distributor = 21; // valid:"optional"
  // 商品ID
  string productId = 22;
  // 退款单标签
  //
  // 可选值：distribution 经销订单，consignment 代销订单，代销商 ID
  repeated string tags = 23;
  // OMS 推送状态
  //
  // pending 待推送，processing 推送中，pushed 已推送，failed 推送失败
  string omsPushStatus = 24;
  // 是否搜索全部而不分页
  bool searchAll = 25;
}

message ListOrderRefundsResponse {
  // 数量
  uint64 total = 1;
  // 列表
  repeated OrderRefundDetail items = 2;
}

message OrderRefundDetail {
  // 退款单 ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 客户 ID
  string memberId = 3;
  // 订单 ID
  string orderId = 4;
  // 退款编号
  string number = 5;
  // 退款单号
  string refundNumber = 6;
  // 订单编号
  string orderNumber = 7;
  // 退款金额
  //
  // 实付金额和退款金额相等
  uint64 refundAmount = 8;
  // 退款状态
  //
  // 退款状态值为：waitingAudit 待审核，rejected 已拒绝，approved 已同意，returned 买家已退货，pending/refunding 退款中，refunded 已退款，failed 退款失败，canceled 退款关闭
  string status = 9;
  // 退款失败原因
  string failedReason = 10;
  // 退款类型
  string type = 11;
  // 退款原因
  string reason = 12;
  // 退款说明
  string description = 13;
  // 退款商品
  repeated RefundProduct products = 15;
  // 退款历史
  repeated OrderRefundHistory histories = 16;
  // 创建时间
  string createdAt = 17;
  // 更新时间
  string updatedAt = 18;
  // 退款类型
  //
  // 退货退款：returnAndRefund，仅退款：onlyRefund
  string refundType = 19;
  // 订单配送方式
  //
  // ec.order.method
  string method = 20;
  // 退货物流信息
  Logistics logistics = 21;
  // 货物状态
  //
  // 已收到货: received，未收到货: notReceived
  string deliveryStatus = 22;
  // 联系人/收货人
  OrderRefundContact contact = 23;
  // 门店 ID
  string storeId = 24;
  // 邮费
  //
  // 退款中包含的邮费
  uint64 expressFee = 25;
  // 操作人
  string operator = 26;
  // 参与的活动
  repeated mairpc.common.ec.CampaignDetail campaigns = 27;
  // 交易状态
  string orderStatus = 28;
  // 退款时间
  string refundedAt = 29;
  // 商品附件信息
  repeated ProductAccessoryDetail productAccessories = 30;
  // 门店名
  string storeName = 31;
  // 门店编号
  string storeCode = 32;
  // 自定义退款金额原因
  string customRefundAmountReason = 33;
  // 后台发起退款时的发起人姓名
  string userName = 34;
  //微信自定义交易组件退款单号列表
  repeated string wechatShopRefundNumbers = 35;
  // 礼卡退款单
  repeated PrepaidCardRefund prepaidCardRefunds = 36;
  // 退款单标签
  //
  // 可选值：distribution 经销订单，consignment 代销订单，代销商 ID
  repeated string tags = 37;
  // 储值退款信息
  RefundStoredValue refundStoredValue = 38;
  // 储值支付信息
  common.ec.StoredValue storedValue = 39;
  // oms 处理信息
  OmsProcessor omsProcessor = 40;
  // 退款变动原因
  string refundAmountChangeReason = 41;
  // 订单支付金额
  uint64 payAmount = 42;
  // 退款备注
  string remarks = 43;
  // 退款凭证
  Certificate certificate = 44;
  // 额外信息
  string extra = 45;
}

message PrepaidCardRefund {
  // 礼卡退款单 id
  string id = 1;
  // 礼卡 id
  string prepaidCardId = 2;
  // 礼卡卡号
  string number = 3;
  // 抵扣金额
  int64 amount = 4;
  // 原始终端流水号
  string originTradeNo = 5;
  // 退款单流水号
  string tradeNo = 6;
  // 退款金额
  int64 refundAmount = 7;
  // 退款编号
  string refundNumber = 8;
  // 退款状态
  string status = 9;
}

message RefundStoredValue {
  // 退款单流水号
  string tradeNo = 1;
  // 退款金额
  int64 amount = 2;
  // 退款状态
  string status = 3;
  // 运费退款金额
  int64 deliveryAmount = 4;
}

message OrderRefundContact {
  // 姓名
  string name = 1;
  // 手机号
  string phone = 2;
}

message Logistics {
  // 物流公司
  string deliveryName = 1;
  // 物流单号
  string waybillId = 2;
  // 退货时间
  string deliveredAt = 3;
}

message RefundProduct {
  // 商品 ID
  string id = 1;
  // 商品名称
  string name = 2;
  // 商品数量
  uint64 total = 3;
  // 商品单价
  uint64 price = 4;
  // 支付价格
  uint64 payAmount = 5;
  // 商品规格
  RefundProductSpec spec = 6;
  // 商品图片
  string picture = 7;
  // 商品编码
  string number = 8;
  // 套餐活动 id
  string packageCampaignId = 9;
  // 商品类型
  //
  // 实物商品（product），虚拟商品（virtual），付费卡券（coupon）
  string type = 10;
  // 退款金额
  uint64 refundAmount = 11;
  // 是否改价
  bool isAmountAdjusted = 12;
  // 用于 OMS 退款单标记唯一 ID
  string outTradeId = 13;
  // 折扣信息
  repeated mairpc.common.ec.DiscountInfo discounts = 14;
  // 礼卡退款信息
  repeated ProductPrepaidCardRefund prepaidCardRefunds = 15;
  // 储值退款信息
  RefundStoredValue refundStoredValue = 16;
  // 储值支付信息
  common.ec.StoredValue storedValue = 17;
  // 礼卡支付信息
  repeated OrderProductPrepaidCard prepaidCards = 18;
}

message ProductPrepaidCardRefund {
  // 礼卡 id
  string prepaidCardId = 1;
  // 礼卡卡号
  string number = 2;
  // 礼卡退款金额
  int64 refundAmount = 3;
  // 抵扣金额
  int64 amount = 4;
}

message RefundProductSpec {
  // sku
  string sku = 1;
  // 商品规格
  repeated string properties = 2;
  // 外部 sku
  string externalSku = 3;
}

message PrePayRequest {
  // @required
  //
  // 订单ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 客户ID
  string memberId = 2; // valid:"required,objectId"
  // @required
  //
  // 买家微信openId
  string openId = 3; // valid:"required"
  // @required
  //
  // 渠道 ID
  string channelId = 4; // valid:"required"
  // 自定义信息，json 格式的字符串
  string extra = 5;
  // 小程序版本，可选值 release，trial，develop
  //
  // 收钱吧支付时使用
  string envVersion = 6;
}

message DeleteOrderRequest {
  // @required
  //
  // 订单ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 客户ID
  string memberId = 2; // valid:"required,objectId"
}

message PrintTicketRequest {
  // @required
  //
  // 订单或退款单ID
  string orderId = 1; // valid:"required,objectId"
  // @required
  //
  // 客户ID
  string memberId = 2; // valid:"required,objectId"
  // @required
  //
  // 店铺ID
  string storeId = 3; // valid:"required,objectId"
  // 打印小票类型
  string type = 4;
  // 取消退款单操作备注
  string remarks = 5;
}

message GetOrderByCodeRequest {
  // @required
  //
  // 提货码
  string code = 1; // valid:"required"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
  // 店员 ID
  string staffId = 3; // valid:"objectId"
}

message DeliverOrderOfflineRequest {
  // 订单 ID
  string id = 1; // valid:"objectId"
  // 运单号
  string waybillId = 2;
  // 物流公司 ID
  string deliveryId = 3;
  // 物流公司名称
  string deliveryName = 4;
  // @required
  //
  // 门店 ID
  string storeId = 5; // valid:"required"
  // 店员 ID
  string staffId = 6;
  // 发货商品列表
  repeated ProductSKU productSkus = 7;
  // 快递员
  Courier courier = 8;
  // 订单货品的唯一 ID 列表
  //
  // 此字段有值时 productSkus 字段失效
  repeated string outTradeIds = 9; // valid:"objectIdList"
  // 订单编号
  string number = 10;
}

message Courier {
  // 快递员名称
  string name = 1;
  // 快递员电话
  string phone = 2;
}

message DeliverOrderOnlineRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // 店员 ID
  string staffId = 2; // valid:"objectId"
}

message GetOrderTraceRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // 只返回包裹信息
  bool onlyPackage = 2;
}

message GetOrderTraceResponse {
  // 包裹列表
  repeated OrderPackage packages = 1;
}

message OrderPackage {
  // 物流公司 ID
  string deliveryId = 1;
  // 物流公司 name
  string deliveryName = 2;
  // 运单 ID
  string waybillId = 3;
  // 运单状态
  //
  // 状态值： shipped: 待揽件, taken: 已揽件, shipping: 在途中, signed: 已签收, failed: 问题件，delivering: 派送中（自行配送时使用）
  string status = 4;
  // 物流轨迹
  repeated ExpressTrace traces = 5;
  // 货品列表
  repeated PackageProduct products = 6;
  // 发货时间
  string shippedAt = 7;
  // 发货人信息
  string deliverBy = 8;
  // 快递员
  Courier courier = 9;
  // 物流 ID
  string id = 10;
}

message PackageProduct {
  // 商品 ID
  string id = 1;
  // 商品编码
  string number = 2;
  // 商品名称
  string name = 3;
  // 商品图片
  string picture = 4;
  // 商品数量
  uint64 total = 5;
  // 商品规格
  SpecProdSku spec = 6;
}

message ExpressTrace {
  // 描述
  string description = 1;
  // 创建时间
  string createdAt = 2;
  // 物流状态
  //
  // 状态值： shipped: 待揽件, taken: 已揽件, shipping: 在途中, signed: 已签收, failed: 问题件
  string status = 3;
}

message PayOfflineOrderRequest {
  // @required
  //
  // 订单 Id
  string id = 1; // valid:"required,objectId"
}

message UpdatePaymentRequest {
  // @required
  //
  // 订单 Id
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 支付方式
  string payment = 2; // valid:"in(wechat|alipay)"
}

message CalculateDeliveryFeeRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
  // @required
  //
  // 配送方式
  //
  // 可选值：pickup 自提，express 邮寄，cityExpress 同城配送
  string method = 3; // valid:"required,in(pickup|express|cityExpress)"
  // 优惠券 ID 列表
  repeated string discountIds = 4;
  // 收货地址 ID
  string memberAddressId = 5; // valid:"objectId"
  // 是否是直接购买
  //
  // 当此字段为 true 时才会用到购买商品的几个字段且此时这几个字段必填
  bool isPurchase = 6;
  // 购买商品的 ID
  string productId = 7; // valid:"objectId"
  // 购买商品 sku
  string sku = 8;
  // 购买商品数量
  uint64 count = 9;
}

message CalculateDeliveryFeeResponse {
  // 运费
  uint64 deliveryFee = 1;
  // 需要支付的运费
  uint64 payAmount = 2;
  // 免邮说明
  //
  // memberPrivilege: 会员权益免邮, groupon: 拼团包邮
  string freeReason = 3;
}

message ListPresentCampaignRecordsRequest {
  // @required
  //
  // 活动 Id
  string campaignId = 1; // valid:"required,objectId"
  // 搜索类型
  //
  // 客户：member，订单：order
  string searchType = 2; // valid:"in(member|order)"
  string searchKey = 3;
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 4;
}

message ListPresentCampaignRecordsResponse {
  // 数量
  uint64 total = 1;
  // 列表
  repeated Record items = 2;
}

message Record {
  // 客户 Id
  string memberId = 1;
  // 客户姓名
  string name = 2;
  // 订单 ID
  string orderId = 3;
  // 订单编号
  string orderNumber = 4;
  // 订单实付金额
  uint64 payAmount = 5;
  // 赠品详情
  repeated OrderPresent presents = 6;
  // 下单时间
  string createdAt = 7;
  // 减免金额 单位： 分
  int64 discountAmount = 8;
}

message OrderPresent {
  // 赠品 Id
  string id = 1;
  // 赠送件数
  uint64 presentTotal = 2;
  // 赠品名称
  string name = 3;
  // 赠品规格
  mairpc.ec.marketing.Spec spec = 4;
}

message UpdateOrderExtraRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 自定义信息，json 格式的字符串
  string extra = 2; // valid:"required"
}

message UpdateOrderCampaignRequest {
  // @required
  //
  // 订单 ID
  string orderId = 1; // valid:"required,objectId"
  // @required
  //
  // 活动数据
  mairpc.common.ec.CampaignDetail campaign = 2; // valid:"required"
}

message UpdateCityExpressOrderRequest {
  // @required
  //
  // 订单 ID
  string orderId = 1; // valid:"required,objectId"
  // @required
  //
  // member._id
  string memberId = 2; // valid:"required,objectId"
  // 期望送达时间
  int64 expectDeliveryAt = 3;
  // 期望送达时间文本
  string expectDeliveryAtLabel = 4;
}

message GetPromoterOrderAmountRequest {
  // 导购 id
  string staffId = 1; // valid:"objectId"
  // 导购当前所在店铺 id
  string storeId = 2; // valid:"objectId"
  // 统计时间范围
  mairpc.common.types.StringDateRange dateRange = 3;
  // 导购 ids
  repeated string staffIds = 4; // valid:"objectIdList"
}

message GetPromoterOrderAmountResponse {
  // 推广金额（包括未完成、退货订单实付金额）
  uint64 totalAmount = 1;
  // 销售额（除去退款的交易完成订单实付金额）
  uint64 saleAmount = 2;
  // 订单总数
  uint64 totalOrder = 3;
  // 订单完成数
  uint64 totalCompletedOrder = 4;
  // 退款订单数
  uint64 totalRefundedOrder = 5;
}

message SendGrouponMessageRequest {
  // 拼团 id
  string grouponRecordId = 1;
  // 拼团状态
  string status = 2;
  // 活动相关动态参数
  map<string, string> placeholderMap = 3;
}

message GetRefundOrderStatsRequest {
  // @required
  //
  // 客户 id
  string memberId = 1; // valid:"required,objectId"
}

message GetRefundOrderStatsResponse {
  // 客户累计退款金额
  //
  // 单位：分
  uint64 totalRefundAmount = 1;
  // 客户累计退款订单数
  uint64 refundCount = 2;
}

message GetOrderRequest {
  string id = 1; // valid:"objectId"
  string memberId = 2; // valid:"objectId"
  string number = 3;
  string tradeNo = 4;
  // 控制返回字段，Member：返回客户信息
  repeated string extraFields = 5;
}

message HasOrderRequest {
  // @required
  //
  // 客户id
  string memberId = 1; // valid:"required,objectId"
  // 历史状态
  repeated string historiesStatus = 2;
}

message GetProductPurchaseLimitStatusRequest {
  // @required
  //
  // 商品 Id
  string productId = 1; // valid:"required,objectId"
  // @required
  //
  // 客户 Id
  string memberId = 2; // valid:"required,objectId"
  // 购买数量
  uint64 count = 3;
}

message DeliveryConfirmNotifyRequest {
  // @required
  //
  // 订单id
  string orderId = 1;
  repeated LogisticsPackage packages = 2;
}

message LogisticsPackage {
  // @required
  //
  // 物流公司编号
  string logisticsCode = 1;
  // 物流公司名称
  string logisticsName = 2;
  // @required
  //
  // 运单号
  string expressCode = 3;
  // 包裹长度(单位：厘米)
  string length = 4;
  // 包裹宽度(单位：厘米
  string width = 5;
  // 包裹高度(单位：厘米)
  string height = 6;
  // 包裹重量(单位：千克)
  string weight = 7;
  // 商品列表
  repeated PackageProductList products = 8;
}

message PackageProductList {
  // 商品编码
  string externalSku = 1;
  // 包裹内该商品的数量
  uint64 count = 2;
}

message GetOrderStatsRequest {
  // 下单时间
  mairpc.common.types.StringDateRange createdTime = 1;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 2;
  // 门店 ID
  string storeId = 3;
}

message GetOrderStatsResponse {
  // 销售额
  uint64 totalAmount = 1;
  // 订单数
  uint64 totalOrder = 2;
  // 销售件数
  uint64 totalPieces = 3;
  // 退款金额
  uint64 totalRefundAmount = 4;
  // 客单价
  uint64 memberCount = 5;
  // 退款总件数
  uint64 totalRefundCount = 6;
}

message GetOrderRankRequest {
  // 分析时间，默认 30 天
  mairpc.common.types.StringDateRange analysesTime = 1;
  mairpc.common.request.ListCondition listCondition = 2;
  repeated string storeIds = 3; // valid:"objectIdList"
}

message GetOrderRankResponse {
  repeated StoreOrderStats storeOrderStats = 1;
}

message MemberOrderStats {
  string name = 1;
  uint64 totalPayAmount = 2;
  uint64 totalOrder = 3;
  uint64 average = 4;
  string memberId = 5;
  string lastPayAt = 6;
  string phone = 7;
}

message StoreOrderStats {
  string name = 1;
  uint64 totalAmount = 2;
  uint64 totalOrder = 3;
  uint64 totalMember = 4;
  string storeId = 5;
}

message GetMemberOrderRankRequest {
  string searchKey = 1;
  string storeId = 2; // valid:"objectId"
  // 分析时间，默认 30 天
  mairpc.common.types.StringDateRange analysesTime = 3;
  // 分页信息
  //
  // 排序字段 totalPayAmount average totalOrder lastPayAt
  mairpc.common.request.ListCondition listCondition = 4;
}

message GetMemberOrderRankResponse {
  uint64 total = 1;
  repeated MemberOrderStats items = 2;
}

message ListIncomeRecordsRequest {
  // 搜索类型
  //
  // 订单编号（number），交易单号（tradeNo），支付用户（name），商户订单号（outTradeNo）
  string searchType = 1; // valid:"in(number|tradeNo|name|outTradeNo)"
  string searchKey = 2;
  // 支付时间
  mairpc.common.types.StringDateRange paidAt = 3;
  // 分页数据
  mairpc.common.request.ListCondition listCondition = 4;
  // 退款时间
  mairpc.common.types.StringDateRange refundAt = 5;
  // 导出设置
  mairpc.common.types.ExportInfo export = 6;
  // 账单类型
  repeated string types = 7; // valid:"in(order|presell|periodBuy|productCard|memberPaidCardRecord|cardOrder|consignmentOrder)"
}

message ListIncomeRecordsResponse {
  // 总金额
  uint64 totalAmount = 1;
  // 总条数
  uint64 total = 2;
  repeated IncomeRecord items = 3;
}

message SearchIncomeRecordsResponse {
  repeated IncomeRecord items = 1;
}

message IncomeRecord {
  // 交易单号
  string tradeNo = 1;
  // 订单编号
  string number = 2;
  // 支付用户
  string name = 3;
  // 支付金额
  uint64 payAmount = 4;
  // 支付时间
  string paidAt = 5;
  // 用户 id
  string memberId = 6;
  // 订单 id
  string orderId = 7;
  // 类型
  //
  // 零售订单（order）、周期购订单（periodBuy）、产品卡订单（productCard）、预售定金（presell）、付费会员开通记录（memberPaidCardRecord）
  string type = 8;
  // 退款金额
  uint64 refundAmount = 9;
  // 退款时间
  string refundAt = 10;
  // id
  string id = 11;
  // 子类型
  //
  // 储值卡（storedValueCard）、兑换卡（redeemCard）
  string subType = 12;
}

message ListSpentRecordsRequest {
  // 搜索类型
  //
  // 交易单号（tradeNo），收款账户（accountName），订单编号（orderNumber），商户订单号（outTradeNo）
  string searchType = 1; //valid:"in(tradeNo|accountName|orderNumber|outTradeNo)"
  string searchKey = 2;
  // 支出类型
  //
  // 订单退款（order_refund），门店分账（store_profitshare），大众分佣（member_promoter_profit），导购分佣（staff_promoter_profit）
  string type = 3; // valid:"in(order_refund|store_profitshare|staff_promoter_profit|member_promoter_profit)"
  // 支出时间
  mairpc.common.types.StringDateRange spentAt = 4;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 5;
  // 是否需要计算总数
  bool needAmount = 6;
}

message ListSpentRecordsResponse {
  // 总金额
  uint64 totalAmount = 1;
  // 总记录数
  uint64 total = 2;
  repeated SpentRecord items = 3;
  string code = 4;
}

message SpentRecord {
  string id = 1;
  // 交易单号
  string tradeNo = 2;
  // 支出类型
  //
  // 订单退款（order_refund）,分销员佣金（promoter_profit），门店分账（store_profitshare）
  string type = 3;
  // 收款账户
  string accountName = 4;
  // 支出金额
  uint64 spentAmount = 5;
  // 支出时间
  string spentAt = 6;
  string transferBillId = 7;
  // 订单编号
  string orderNumber = 8;
  // 订单 id
  string orderId = 9;
}

message ListBillDetailsRequest {
  // @required
  //
  // 支出记录 id
  string id = 1; // valid:"required"
  // 订单编号
  string orderNo = 2;
  // 门店 id
  string storeId = 3;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 4;
}

message ListBillDetailsResponse {
  uint64 total = 1;
  repeated BillDetail items = 2;
}

message BillDetail {
  // 订单编号
  string orderNo = 1;
  // 门店名称
  string storeName = 2;
  // 订单金额
  uint64 orderAmount = 3;
  // 退款金额
  uint64 refundAmount = 4;
  // 分账金额
  uint64 shareAmount = 5;
  // 用户 id
  string memberId = 6;
  // 订单 id
  string orderId = 7;
  // 订单类型
  //
  // 普通订单（order）、预售定金（presell）、卡项订单（cardOrder）、周期购订单（periodBuy）、付费会员开通记录（memberPaidCardRecord）
  string type = 8;
  // 子类型
  //
  // 储值卡（storedValueCard）、兑换卡（redeemCard）
  string subType = 9;
}

message GetSpentRecordResponse {
  // 交易单号
  string tradeNo = 1;
  // 支出类型
  //
  // 订单退款（order_refund）,分销员佣金（promoter_profit），门店分账（store_profitshare）
  string type = 2;
  // 收款账户号
  string account = 3;
  // 账户名称
  string accountName = 4;
  // 账户类型
  //
  // 商户（MERCHANT_ID）,个人 openId（PERSONAL_OPENID），银行账户（BANK），openId（OPENID）
  string accountType = 5;
  // 支出金额（分）
  uint64 spentAmount = 6;
  // 支出时间
  string spentAt = 7;
}

message GetMallOverviewStatsResponse {
  // 销售金额
  uint64 totalAmount = 1;
  // 支付客户数
  uint64 totalMember = 2;
  // 销售单数
  uint64 totalOrder = 3;
  // 平均订单金额
  uint64 averageOrderAmount = 4;
  // 退款金额
  uint64 totalRefundAmount = 5;
}

message ReturnConfirmNotifyRequest {
  // @required
  //
  // 退货订单id
  string returnOrderId = 1; // valid:"required"
  // 退货行信息
  repeated ReturnProduct returnProducts = 2;
}

message ReturnProduct {
  // @required
  //
  // 商品编码
  string externalSku = 1; // valid:"required"
  // 实收商品数量
  uint64 count = 2;
}

message RefundConfirmNotifyRequest {
  // @required
  //
  // 退款单号
  string refundOrderId = 1; // valid:"required"
  // 退款结果
  string result = 2;
  // 退款信息
  string message = 3;
}

message ListPackageCampaignRecordsRequest {
  // @required
  //
  // 活动 Id
  string campaignId = 1; // valid:"required,objectId"
  // 搜索类型
  //
  // 客户：member，订单：order
  string searchType = 2; // valid:"in(member|order)"
  string searchKey = 3;
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 4;
  // 门店 ID
  string storeId = 5; // valid:"objectId"
}

message ListPackageCampaignRecordsResponse {
  // 数量
  uint64 total = 1;
  // 列表
  repeated PackageCampaignRecord items = 2;
}

message PackageCampaignRecord {
  // 客户 Id
  string memberId = 1;
  // 客户姓名
  string name = 2;
  // 订单 ID
  string orderId = 3;
  // 订单编号
  string orderNumber = 4;
  // 订单实付金额
  uint64 payAmount = 5;
  // 下单时间
  string createdAt = 6;
}

message UpdateRefundStatusRequest {
  // @required
  //
  // 退款单 ID
  string orderRefundId = 1; // valid:"required,objectId"
  // @required
  //
  // 退款单状态
  //
  // success：退款成功，failed：退款失败
  string refundStatus = 2; // valid:"required,in(success|failed)"
  // 失败原因
  string failureMsg = 3;
  // 错误码
  string failureCode = 4;
}

message SplitOrderByProductRequest {
  // 订单 ID
  string id = 1; // valid:"objectId"
  // 订单编号
  string number = 2;
  // 门店和商品的关系列表
  repeated StoreProductMapping storeProductMapping = 3;
}

message SplitOrderRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 子订单参数，用于指定每个子单的分配方式
  repeated SubOrderParams subOrders = 2; // valid:"required"
}

message SubOrderParams {
  // 指定子单的门店 ID
  string storeId = 1; // valid:"objectId"
  // @required
  //
  // 子单中需要包含的原始订单商品项唯一标识（ec.order.products.outTradeId）
  repeated string outTradeIds = 2; // valid:"required,objectIdList"
}

message SplitOrderResponse {
  // 拆分后的子单列表
  repeated SplitOrderResult subOrders = 1;
}

message SplitOrderResult {
  // 拆分后的子订单 ID
  string orderId = 1;
  // 子订单对应的门店 ID
  string storeId = 2;
  // 子单中包含的原始订单商品项唯一标识
  repeated string outTradeIds = 3;
}

message ListOrderRedemptionRecordsRequest {
  // 订单类型
  repeated string orderTypes = 1; // valid:"in(virtual|pickup)"
  // 核销时间范围
  mairpc.common.types.StringDateRange redeemedAt = 2;
  // @required
  //
  // 门店 id
  string storeId = 3; // valid:"required"
  // 订单编号
  string orderNumber = 4;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 5;
}

message ListOrderRedemptionRecordsResponse {
  // 订单 id
  uint64 total = 1;
  repeated RedemptionRecord items = 2;
}

message RedemptionRecord {
  // 记录 ID
  string id = 1;
  // 订单信息
  RedemptionOrder order = 2;
  // 发货商品信息
  repeated RedemptionOrderProduct products = 3;
  RedemptionProcessBy processBy = 4;
  // 收货人信息
  RedemptionMember member = 5;
  // 提货码
  string pickupCode = 6;
  // 提货码
  string waybillId = 7;
  // 物流 ID
  string deliveryId = 8;
  // 物流名称
  string deliveryName = 9;
  // 发货时间
  string shippedAt = 10;
}

message RedemptionMember {
  string id = 1;
  string name = 2;
}

message RedemptionOrder {
  // 订单 ID
  string id = 1;
  // 订单类型
  //
  // virtual：虚拟商品订单，coupon：付费卡券订单，pickup：自提订单，normal：普通订单
  string type = 2;
  // 发货方式
  //
  // 同订单发货方式
  string method = 3;
  // 订单编号
  string number = 4;
}

message RedemptionProcessBy {
  // 门店信息
  LogisticsProcessByDetail store = 1;
  // 员工信息
  LogisticsProcessByDetail staff = 2;
  // 管理员信息
  LogisticsProcessByDetail user = 3;
  // 操作员类型
  //
  // staff：店长端操作，user：管理员操作
  string operatorType = 4;
}

message RedemptionOrderProduct {
  // 商品 Id
  string id = 1;
  // 名称
  string name = 2;
  // 数量
  uint64 total = 3;
  // 商品规格
  OrderProductSpec spec = 4;
  // 退款状态
  //
  // 同订单商品退款状态
  string refundStatus = 5;
}

message StoreProductMapping {
  // 门店 ID
  string storeId = 1; // valid:"objectId"
  // 门店下对应的商品 Id 列表
  repeated string productIds = 2;
}

message SplitOrderByProductResponse {
  // 拆单创建的新订单和商品的关系列表
  repeated OrderProductMapping orderProduct = 1;
}

message OrderProductMapping {
  // 订单 ID
  string orderId = 1;
  // 商品 Id
  string productId = 2;
}

message UpdateMonthlyDistributionRequest {
  // @required
  //
  // ec.transferBill._id
  string billId = 1; // valid:"required,objectId"
  // @required
  //
  // member：大众分销，staff：导购分销
  string type = 2; // valid:"required,in(member|staff)"
}

message CreateInvoiceRequest {
  // @required
  //
  // ec.order._id
  string orderId = 1; // valid:"required,objectId"
  // @required
  //
  // 开票信息
  mairpc.common.ec.InvoiceRequest invoice = 2; // valid:"required"
  // 客户 id
  string memberId = 3; // valid:"objectId"
  // 订单类型
  //
  // 零售订单 order，周期购订单 period_buy，卡项订单 card_order，product_card 产品卡订单，代下单订单 proxyOrder（导购端操作发票时需传）
  string orderType = 4;
  // 订单编号
  string number = 5;
  // 是否红票
  bool isRed = 6;
}

message CreateMergedInvoiceRequest {
  // @required
  //
  // 开票信息
  mairpc.common.ec.InvoiceRequest invoice = 1;
  // @required
  //
  // 开票订单信息
  repeated string orderIds = 2;
  // 客户 id
  string memberId = 3; // valid:"required,objectId"
  // 是否重新开票
  bool isRebilling = 4;
  // 是否代下单订单
  bool isProxyOrder = 5;
}

message UpdateInvoiceRequest {
  // 发票 id
  string id = 1; // valid:"objectId"
  // 客户 id
  string memberId = 2; // valid:"objectId"
  // 发票状态
  //
  // 待开票（pending），开票中（invoicing），已开票（issued），开票失败（failed），已关闭（closed），重新开票（rebilling）,已退票（deleted）
  string status = 3; // valid:"in(pending|invoicing|issued|failed|closed|rebilling|deleted|reversing)"
  // 开票信息
  //
  // 仅支持全量修改
  mairpc.common.ec.InvoiceRequest invoice = 4;
  // 开票结果
  InvoiceResult result = 5;
  // 失败原因
  string failedReason = 6;
  // 订单 id
  string orderId = 7; // valid:"objectId"
  // 订单编号
  string orderNumber = 8;
  // 红字确认单 uuid
  string redConfirmUuid = 9;
  // 开票流水号
  string serialNo = 10;
}

message InvoiceResult {
  // 发票地址
  string fileUrl = 1;
  // 发票代码
  string code = 2;
  // 发票号码
  string number = 3;
  // 开票时间
  //
  // 时间格式需要满足 ISO 8601 标准
  string invoicedAt = 4;
  // 发票金额
  uint64 amount = 5;
}

message InvoiceResponse {
  // 发票 id
  string id = 1;
  // ec.order._id
  string orderId = 2;
  // 类型 个人（person），单位（company）
  string type = 3;
  // 发票抬头
  string name = 4;
  // 税号
  string taxID = 5;
  // 电子邮箱
  string email = 6;
  // 地址
  string address = 7;
  // 电话号码
  string phone = 8;
  // 开户银行
  string bankName = 9;
  // 银行账号
  string bankAccount = 10;
  // 发票状态
  //
  // 待开票（pending），开票中（invoicing），已开票（issued），开票失败（failed），已关闭（closed），重新开票（rebilling）
  string status = 11;
  // 发票地址
  string fileUrl = 12;
  // 发票历史记录
  repeated InvoiceHistory histories = 13;
  // 发票代码
  string code = 14;
  // 发票号码
  string number = 15;
  // 发票金额
  uint64 amount = 16;
  // 发票预览图片地址
  string previewImageUrl = 17;
  // 开票时间
  string invoicedAt = 18;
  // 订单编号
  string orderNumber = 19;
  // member._id
  string memberId = 20;
  // 发票形式
  //
  // unsupported（不支持）、offline（线下开纸质发票）、online（线上开电子发票）
  string invoiceSetting = 21;
  // 发票类型
  //
  // general（普通发票）、special（专用发票）
  string invoiceType = 22;
  // 发票内容
  //
  // productDetail（商品明细）、productCategory（商品类别）
  repeated string invoiceContents = 23;
  // 合并开票关联订单号
  repeated string mergedOrderIds = 24;
  // 是否红票
  bool isRed = 25;
  // 发票商品信息
  InvoiceItems invoiceItems = 26;
  // 原蓝票 id
  string blueInvoiceId = 27;
  // 红字确认单 UUID
  string redConfirmUuid = 28;
  // 开票流水号
  string serialNo = 29;
  // 自定义信息，json 格式的字符串
  string extra = 30;
  // 合并开票关联订单编号
  repeated string mergedOrderNumbers = 31;
}

message InvoiceItems {
  // 开票商品
  repeated InvoiceProduct invoiceProducts = 1;
  // 总运费
  uint64 deliveryFee = 2;
}

message InvoiceProduct {
  OrderProduct orderProduct = 1;
  mairpc.common.ec.InvoiceTemplate invoiceTemplate = 2;
}

message InvoiceHistory {
  // 发票状态
  //
  // 待开票（pending），开票中（invoicing），已开票（issued），开票失败（failed），已关闭（closed），重新开票（rebilling）
  string status = 1;
  // 操作时间
  string createdAt = 2;
  // 操作员
  string operator = 3;
  // 与业务有关的存储信息
  //
  // json 格式的字符串
  string extra = 4;
  // 操作详情
  string operatorDetail = 5;
}

message ListInvoicesRequest {
  // ec.order._id
  repeated string orderIds = 1; // valid:"objectIdList"
  // memberId
  string memberId = 2; // valid:"objectId"
  // 分页参数
  mairpc.common.request.ListCondition listCondition = 3;
  // 开票时间
  mairpc.common.types.DateRange invoicedAt = 4;
  // 状态: pending（待开票）、invoicing（开票中）、issued（已开票）、failed（开票失败）、closed（已关闭）
  repeated string status = 5;
  // 发票设置：unsupported（不支持）、offline（线下开纸质发票）、online（线上开电子发票）
  string invoiceSetting = 6;
  // 发票类型：general（普通发票）、special（专用发票）
  string invoiceType = 7; // valid:"in(general|special)"
  // 订单类型，默认不需要传。当导购查看代下单订单发票信息时需传 proxyOrder（代下单订单）
  string orderType = 8; // valid:"in(proxyOrder)"
  // invoice._id
  string id = 9; // valid:"objectId"
  // 是否红票
  bool isRed = 10;
  // 红票对应的蓝票 id
  string blueInvoiceId = 11;
}

message ListInvoicesResponse {
  uint64 total = 1;
  repeated InvoiceResponse items = 2;
}

message ListInvoiceRecordsRequest {
  // memberId
  string memberId = 1; // valid:"objectId"
  // 分页参数
  mairpc.common.request.ListCondition listCondition = 2;
  // 申请开票时间
  mairpc.common.types.DateRange createdAt = 3;
  // 订单类型，默认不需要传。当导购查看代下单订单发票信息时需传 proxyOrder（代下单订单）
  string orderType = 4; // valid:"in(proxyOrder)"
}

message ListInvoiceRecordsResponse {
  uint64 total = 1;
  repeated InvoiceRecord items = 2;
}

message InvoiceRecord {
  // 发票抬头
  string name = 1;
  // 发票类型
  //
  // general（普通发票）、special（专用发票）
  string invoiceType = 2;
  // 申请开票时间
  string createdAt = 3;
  // 发票状态
  //
  // issued（已开票）、partiallyIssued（部分开票成功）、deleted（已退票）、partiallyDeleted（部分退票）、failed（开票失败）、pending（待开票）
  string status = 4;
  // 发票金额
  uint64 amount = 5;
  // 发票形式
  //
  // unsupported（不支持）、offline（线下开纸质发票）、online（线上开电子发票）
  string invoiceSetting = 6;
  // 订单 ids
  repeated string orderIds = 7;
}

message SendInvoiceEmailRequest {
  // @required
  //
  // 发票 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 客户 ID
  string memberId = 2; // valid:"required,objectId"
  // 邮箱地址
  string email = 3; // valid:"email"
}

message UpsertOrderExportSettingRequest {
  // 类型
  //
  // 订单维度（order）、商品维度（product）
  string type = 1;
  // 业务类型
  //
  // 零售（retail）、CEP（portal）、积分商城（micromall）
  string business = 2;
  // 导出字段
  repeated ExportFields exportFields = 3;
}

message ExportFields {
  // 导出字段类型
  //
  // 订单字段（order）、商品字段（product）、支付字段（payment）、客户字段（member）、配送字段（delivery）、门店字段（store）、发票字段（invoice）
  string type = 1;
  // 字段列表
  repeated string values = 2;
}

message GetOrderExportSettingRequest {
  // 业务类型
  //
  // 零售（retail）、CEP（portal）、积分商城（micromall）
  string business = 1;
}

message GetOrderExportSettingResponse {
  // 类型
  //
  // 订单维度（order）、商品维度（product）
  string type = 1;
  // 导出字段
  repeated ExportFields exportFields = 2;
}

message CountOrdersByStatusRequest {
  // @required
  //
  // memberId
  string memberId = 1; // valid:"objectId"
}

message CountOrdersByStatusResponse {
  repeated OrderStatusCount counts = 1;
}

message OrderStatusCount {
  // 订单状态
  //
  // 待付款（unpaid），待发货（accepted），待收货（shipped），已完成（completed），退款/售后（refund）
  string status = 1;
  uint64 count = 2;
}

message GetOrderProductPurchaseStatsRequest {
  // @required
  //
  // 零售商品 id
  string productId = 1; // valid:"required,objectId"
  // @required
  //
  // 客户 id
  string memberId = 2; // valid:"required,objectId"
}

message GetOrderCampaignStatsRequest {
  // @required
  //
  // 活动　ids
  repeated string campaignIds = 1; // valid:"required,objectIdList"
  // @required
  //
  // 活动类型： packageCampaign（促销套餐)，flashSaleCampaign（秒杀活动）,grouponCampaign(拼团活动),限时折扣活动（discountCampaign),满减赠(presentCampaign),预售活动(presellCampaign)
  // 任选打包(optionalPackageCampaign),plusBuyCampaign(超值换购),trialCampaign(付邮试用),bargainCampaign(砍价活动)
  string campaignType = 2; // valid:"required,in(packageCampaign|flashSaleCampaign|grouponCampaign|discountCampaign|presentCampaign|presellCampaign|optionalPackageCampaign|plusBuyCampaign|trialCampaign|bargainCampaign)"
}

message GetOrderCampaignStatsResponse {
  // 订单活动统计
  repeated OrderCampaignStats stats = 1;
}

message OrderCampaignStats {
  // 活动 ID
  string campaignId = 1;
  // 销售额
  uint64 totalAmount = 2;
  // 活动销量
  uint64 campaignSales = 3;
  // 订单数量
  uint64 orderCount = 4;
  // 参与客户数
  uint64 memberCount = 5;
}

message AdjustOrderAmountRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // 修改运费金额
  //
  // 单位：分
  int64 deliveryFee = 2;
  // 商品修改金额列表
  repeated AdjustProductAmount products = 3;
}

message AdjustProductAmount {
  // 商品 outTradeId
  string outTradeId = 1; // valid:"objectId"
  // 修改金额
  //
  // 单位：分，负数表示降价
  int64 amount = 2;
}

message GetOrderDeliveryMethodsRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
  // 是否是直接购买
  //
  // 当此字段为 true 时才会用到购买商品的几个字段且此时这几个字段必填
  bool isPurchase = 3;
  // 购买商品的 ID
  string productId = 4; // valid:"objectId"
  // 购买商品 sku
  string sku = 5;
  // 购买商品数量
  uint64 count = 6;
  // 参与的活动
  repeated mairpc.common.ec.OrderCampaign campaigns = 7;
  mairpc.common.ec.OrderCampaignConfig campaignConfig = 8;
}

message GetOrderDeliveryMethodsResponse {
  repeated string methods = 1;
}

message DelayVirtualProductRedeemPeriodRequest {
  // @required
  //
  // 订单 ID
  string orderId = 1; // valid:"required,objectId"
  // @required
  //
  // 要延期的订单商品唯一表示 ID
  string outTradeId = 2; // valid:"required,objectId"
  // 延期天数
  uint32 days = 3;
}

message UpdateOrderContactRequest {
  // 订单 ID
  string orderId = 1; // valid:"objectId"
  // 客户 ID
  string memberId = 2;
  // 订单编号
  string orderNumber = 3;
  // 联系地址
  mairpc.common.types.AddressCoordinate address = 4;
  // 提货人姓名
  string name = 5;
  // 提货人手机号
  string phone = 6;
}

message InvoiceRequest {
  // 发票 id
  string id = 1; // valid:"objectId"
  // 订单或退款单ID
  string orderId = 2; // valid:"objectId"
  // 客户ID
  string memberId = 3; // valid:"objectId"
}

message CreateRedConfirmationRequest {
  // 原蓝票 id
  string blueInvoiceId = 1; // valid:"required,objectId"
  // 红票 id
  string invoiceId = 2; // valid:"objectId"
}

message CreateRedConfirmationResponse {
  // 红字确认单 UUID
  string redConfirmUuid = 1;
}

message UpdateTagsRequest {
  // @required
  //
  // 订单 id
  string id = 1; // valid:"required,objectId"
  repeated string tags = 2;
  // 更新类型：add：增加，delete：删除，replace：替换，默认为 add
  string type = 3; // valid:"in(add|delete|replace)"
}

message GetMemberPaidCardRecordsRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
  // 是否是直接购买
  //
  // 当此字段为 true 时才会用到购买商品的几个字段且此时这几个字段必填
  bool isPurchase = 3;
  // 购买商品的 ID
  string productId = 4; // valid:"objectId"
  // 购买商品 sku
  string sku = 5;
  // 购买商品数量
  uint64 count = 6;
  // 配送方式
  //
  // 门店自提 pickup、快递配送 express 同城快送 cityExpress、无需配送 noExpress
  string method = 7; // valid:"in(express|pickup|cityExpress|noExpress)"
  // 收货地址 ID
  string memberAddressId = 8; // valid:"objectId"
  // 参与的活动
  repeated mairpc.common.ec.OrderCampaign campaigns = 9;
}

message GetMemberPaidCardRecordsResponse {
  repeated MemberPaidCardRecord availabeRecords = 1;
  repeated MemberPaidCardRecord unavailabeRecords = 2;
  // 能否和优惠券一起使用
  bool canUseWithCoupons = 3;
}

message MemberPaidCardRecord {
  string id = 1;
  // 权益卡名称
  string cardName = 2;
  // 生效时间
  string startAt = 3;
  // 卡号
  string cardNumber = 4;
  // 权益卡 id
  string cardId = 5;
  // 权益卡类型
  //
  // 付费会员（paidMember）、权益卡（benefitCard）
  string cardType = 6;
  // 未使用的折扣额度
  uint64 remainderDiscountLimit = 7;
  string expireAt = 8;
  // 权益卡信息
  mairpc.member.MemberPaidCardInfo memberPaidCard = 9;
}

message GetMemberOrderStatsRequest {
  // @required
  //
  // 客户 id
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 近几月
  uint32 monthCount = 2; // valid:"required"
}

message GetMemberOrderStatsResponse {
  // 总消费金额
  uint32 totalPayAmount = 1;
}

message CreateSimpleOrderRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
  // @required
  //
  // 配送方式
  //
  // 可选值：pickup 自提，express 邮寄，cityExpress 同城快送，noExpress 无需配送
  string method = 3; // valid:"required,in(pickup|express|cityExpress|noExpress)"
  // 预留提货信息
  ReservationInfo reservation = 4;
  // 商品
  repeated OrderProductRequest products = 5;
  // 用户留言
  string message = 6;
  // 收货地址 ID
  string memberAddressId = 7; // valid:"objectId"
  // 提货人信息
  ContactRequest contact = 8;
  // @required
  //
  // 来源渠道 ID
  string channelId = 9; // valid:"required"
  // @required
  //
  // 渠道下客户唯一身份信息
  string openId = 10; // valid:"required"
  // 付款方式
  //
  // 微信支付（wechat）、线下支付（offline）
  string payment = 11;
  // 订单标签
  //
  // 可选值：self_purchase 自购，daigou 代购，share 分享，distribution 经销订单，consignment 代销订单，代销商 ID
  repeated string tags = 12;
  // 访问来源
  mairpc.common.types.Utm utm = 13;
  // 期望送达时间
  int64 expectDeliveryAt = 14;
  // 期望送达时间文本
  string expectDeliveryAtLabel = 15;
  // 分销员的 memberId
  //
  // 通过分销员的分销链接购买商品时传此参数
  string promoterMemberId = 16;
  // 商品参与的活动
  //
  // 注意该字段只是将活动信息填充到 ec.order.campaigins 字段中
  repeated CampaignInfo campaigns = 17;
  // 邮费
  uint64 deliveryFee = 18;
  // 订单备注
  string remarks = 19;
  // 自定义信息, json 格式的字符串
  string extra = 20;
}

message CampaignInfo {
  // 活动 id
  string id = 1;
  // 活动标题
  string title = 2;
  // 活动类型
  //
  // 限时折扣(discountCampaign)
  string type = 3; // valid:"required,in(discountCampaign)"
}

message ReservationInfo {
  // 提货时间
  string time = 1;
  // 客户提货时间
  string memberTime = 2;
}

message OrderProductRequest {
  // 零售商品 id
  string id = 1;
  // 商品 sku
  string sku = 2;
  // 商品数量
  uint64 total = 3;
  // 商品单价
  uint64 price = 4;
  // 商品总金额
  uint64 totalAmount = 5;
  // 商品实付金额
  uint64 payAmount = 6;
  // 门店佣金
  uint64 storeDistributionAmount = 7;
  // 导购佣金
  uint64 distributionAmount = 8;
  // 商品原价
  uint64 originPrice = 9;
  // 商品参与的活动
  //
  // 注意该字段只是将该商品参与的活动信息填充到 ec.products.campaigins 字段中
  repeated CampaignInfo campaigns = 10;
}

message ExportDeliveryTemplateRequest {
  // @required
  //
  // 发货类型，可选值：整单（order）、按商品拆单（product）
  string type = 1; // valid:"required,in(order|product)"
  ListOrdersRequest listOrdersRequest = 2;
}

message ExportDeliveryTemplateResponse {
  // 异步执行生产的唯一 code
  string code = 1;
  // 任务 ID
  string jobId = 2;
}

message UpdateLogisticsRequest {
  repeated LogisticsItem logistics = 1;
}

message LogisticsItem {
  // @required
  //
  // 物流 ID
  string id = 1; // valid:"required,objectId"
  // 运单号
  string waybillId = 2;
  // 物流公司 ID
  string deliveryId = 3;
  // 物流公司名称
  string deliveryName = 4;
}

message BatchDeliverOrderOfflineRequest {
  // @required
  //
  // 发货类型，可选值：整单（order）、按商品拆单（product）
  string type = 1; // valid:"required,in(order|product)"
  // @required
  //
  // 文件路径
  string fileUrl = 2; // valid:"required,url"
}

message GetCampaignPurchaseLimitRequest {
  // @required
  //
  // 客户 id
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 商品 id
  string productId = 2; // valid:"required,objectId"
  // @required
  //
  // 活动类型
  //
  // 限时折扣（discountCampaign）
  string campaignType = 3; // valid:"required,in(discountCampaign)"
  // @required
  //
  // 活动 id
  string campaignId = 4; // valid:"required,objectId"
}

message GetSensitiveRequest {
  // @required
  //
  // 订单 id
  string orderId = 1; // valid:"required,objectId"
  // @required
  //
  // 敏感信息字段
  string sensitiveKey = 2; // valid:"in(contactName|contactPhone|contactAddress),required"
}

message GetSensitiveResponse {
  string data = 1;
}

message ImportPickupPasswordsRequest {
  // @required
  //
  // 文件路径
  string fileUrl = 1; // valid:"required"
}

message UpdateOmsProcessorRequest {
  // @required
  //
  // 订单/退款单 id
  repeated string ids = 1; // valid:"required,objectIdList"
  // 更新签的 OMS 推送状态
  //
  // 此参数有值时会作为订单/退款单的当前 OMS 推送状态的筛选条件
  string latestPushStatus = 2; // valid:"in(pending|processing|pushed|failed)"
  // @required
  //
  // OMS 推送状态
  //
  // pending 待推送，processing 推送中，pushed 已推送，failed 推送失败
  string pushStatus = 3; // valid:"required,in(pending|processing|pushed|failed)"
  // 推送时间
  //
  // 时间格式：RFC3339
  // 不传此参数默认会更新为当前时间
  string pushedAt = 4; // valid:"rfc3339"
}

message CheckRedeemCodeRequest {
  // @required
  //
  // 需要检查的核销码
  string code = 1; // valid:"required"
  // 核销码类型的可选列表
  //
  // 默认检查所有可能的数据类型，指定此列表后只会检查列表内数据类型，缩减查询范围
  repeated string codeTypes = 2;
  // 核销门店 Id
  string storeId = 3; // valid:"objectId"
  // 核销店员 Id
  string staffId = 4; // valid:"objectId"
  // 返回对应的核销码数据
  bool returnCodeDetail = 5;
  // 校验是否可核销
  bool checkCanBeRedeem = 6;
}

message CheckRedeemCodeResponse {
  // 核销码类型
  string codeType = 1;
  // 子类型
  //
  // 用于细分核销场景
  string codeSubType = 2;
  // 核销码详情
  string codeDetail = 3;
  // 不可核销错误码
  string errorCode = 4;
  // 错误信息
  string errorMessage = 5;
}

message ListPrepaidCardsResponse {
  repeated mairpc.ec.wallet.PrepaidCard availablePrepaidCards = 1;
  repeated mairpc.ec.wallet.PrepaidCard unavailablePrepaidCards = 2;
}

message CreateProxyOrderRequest {
  // @required
  //
  // 代下单客户信息
  OrderMember member = 1; // valid:"required"
  // @required
  //
  // 门店 id
  string storeId = 2; // valid:"required,objectId"
  // @required
  //
  // 分销员 id
  string promoterId = 3; // valid:"required,objectId"
  // @required
  //
  // 导购对应客户 id
  string memberId = 4; // valid:"objectId"
  // @required
  //
  // 配送方式
  string method = 5; // valid:"required,in(express|pickup|cityExpress|noExpress)"
  // 运费
  uint64 deliveryFee = 6;
  // 收货地址 ID
  string memberAddressId = 7; // valid:"objectId"
  // 是否是直接购买
  bool isPurchase = 8;
  // 商品 ID
  string productId = 9; // valid:"objectId"
  // 商品 SKU
  string sku = 10;
  // 商品数量
  int64 count = 11;
  // 提货人信息
  ContactRequest contact = 12;
  // 预留提货信息
  ReservationInfo reservation = 13;
  // 发票信息
  mairpc.common.ec.Invoice invoice = 14;
  // 小票信息
  mairpc.common.ec.Ticket ticket = 15;
  // 订单备注
  string remarks = 16;
  // 期望送达时间
  int64 expectDeliveryAt = 17;
  // 期望送达时间文本
  string expectDeliveryAtLabel = 18;
  // 导购代客下单时，需传导购 id
  string staffId = 19;
}

message ListProxyOrdersRequest {
  // @required
  //
  // 分销员 id
  string promoterId = 1; // valid:"required,objectId"
  // 订单状态
  //
  // 待邀请（pending），待付款（unpaid），待发货（accepted，unassigned），待收货（shipped，partialShipped），已完成（completed），待评价（uncommented） 退款/售后（refunded）
  repeated string status = 2;
  // 买家/收货人手机号
  string searchKey = 3;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 4;
  // 导购 id
  string staffId = 5; // valid:"objectId"
}

message ListProxyOrdersResponse {
  repeated ProxyOrderDetail items = 1;
  uint64 total = 2;
}

message ProxyOrderDetail {
  // 订单 id
  string id = 1;
  // 订单状态
  string status = 2;
  // 订单总金额金额
  uint64 totalAmount = 3;
  // 运费
  uint64 deliveryFee = 4;
  // 实付金额
  uint64 payAmount = 5;
  // 订单商品
  repeated OrderProduct products = 6;
  // 门店 id
  string storeId = 7;
  // 代下单客户信息
  OrderMember member = 8;
  // 发票信息
  mairpc.common.ec.Invoice invoice = 9;
  // 配送方式
  string method = 10;
  // 收货人/提货人信息
  ContactCoordinateInfo contact = 11;
  // 预留提货信息
  ReservationInfo reservation = 12;
  // 客户地址
  string addressId = 13;
  // 小票信息
  mairpc.common.ec.Ticket ticket = 14;
  // 订单备注
  string remarks = 15;
  // 代下单导购/分销员信息
  Promoter promoter = 16;
  // ec.order 详情
  OrderDetail orderDetail = 17;
  // 门店 ID
  store.StoreDetail store = 18;
  // 物流信息
  LogisticsInfo logistics = 19;
  // 导购代客下单时，导购 id
  string staffId = 20;
}

message Promoter {
  string id = 1;
  string name = 2;
  // 下单时间
  string createdAt = 3;
}

message OrderInfo {
  // 订单 id
  string id = 1;
  // 订单编号
  string number = 2;
  // 下单时间
  string createdAt = 3;
  // 付款时间
  string paidAt = 4;
  // 小票信息
  mairpc.common.ec.Ticket ticket = 5;
}

message AddProxyOrderProductsToCartRequest {
  // @required
  //
  // 代客下单订单 id
  string proxyOrderId = 1; // valid:"required,objectId"
  // @required
  //
  // 客户 id
  string memberId = 2; // valid:"required,objectId"
  // 门店 id。如果不传则使用 proxyOrder 中的 storeId
  string storeId = 3; // valid:"objectId"
}

message UpdateProxyOrderRequest {
  // @required
  //
  // 代下单订单 id
  string id = 1; // valid:"required,objectId"
  // 发票信息
  mairpc.common.ec.Invoice invoice = 2;
  // 是否取消订单
  bool isCancel = 3;
}
