type: google.api.Service
config_version: 3

http:
  rules:
    # 取消订单
    - selector: mairpc.ec.order.OrderService.CancelOrder
      post: /v2/ec/orders/{id}/cancel
      body: '*'
      filterMark: backend,staff
      scope: ['staff', 'app']
      tags: ['私域商城-订单']
    # 订单列表
    - selector: mairpc.ec.order.OrderService.ListOrders
      post: /v2/ec/searchOrders
      body: '*'
      additional_bindings:
        - get: /v2/ec/orders
      filterMark: backend,staff,openapi
      scope: ['staff', 'app']
      tags: ['私域商城-订单']
      sensitiveFields: 'items.member.name,items.member.phone,items.contact.address.detail,items.contact.name,items.contact.phone'
      permission: 'ec:order:list'
    - selector: mairpc.ec.order.OrderService.CountOrders
      post: /v2/ec/orders/count
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单']
      permission: 'ec:order:list'
    # 未分配订单列表
    - selector: mairpc.ec.order.OrderService.ListUnassignedOrders
      get: /v2/ec/unassignedOrders
      filterMark: backend
      tags: ['私域商城-订单']
      hideRequestFields: 'status'
      permission: 'ec:unassignedOrder:list'
    # 订单详情
    - selector: mairpc.ec.order.OrderService.GetOrder
      get: /v2/ec/orders/{id}
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-订单']
      sensitiveFields: 'contact.address.detail,contact.name,contact.phone,member.name,member.phone'
    # 订单退款
    - selector: mairpc.ec.order.OrderService.RefundOrder
      post: /v2/ec/orders/{id}/refund
      body: '*'
      filterMark: backend,openapi
      scope: ['app']
      tags: ['私域商城-订单']
      hideRequestFields: isCancelOrder
      permission: 'ec:orderRefund:list'
    # 分配订单
    - selector: mairpc.ec.order.OrderService.AssignOrder
      post: /v2/ec/orders/{id}/assign
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单']
    # 更新备注
    - selector: mairpc.ec.order.OrderService.UpdateRemarks
      post: /v2/ec/orders/{id}/remarks
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单']
    # 接单
    - selector: mairpc.ec.order.OrderService.AcceptOrder
      post: /v2/ec/orders/{id}/accept
      body: '*'
      filterMark: staff,openapi
      scope: ['app', 'staff']
      tags: ['私域商城-订单']
    # 拒单
    - selector: mairpc.ec.order.OrderService.RejectOrder
      post: /v2/ec/orders/{id}/reject
      body: '*'
      filterMark: staff
      scope: ['staff']
      tags: ['私域商城-订单']
    # 订单核销记录
    - selector: mairpc.ec.order.OrderService.ListOrderRedemptionRecords
      get: /v2/ec/order/redemptionRecords
      filterMark: staff
      scope: ['staff']
      tags: ['私域商城-订单']
    # 核销提货码
    - selector: mairpc.ec.order.OrderService.RedeemOrder
      post: /v2/ec/orders/redeem/{code}
      body: '*'
      filterMark: staff,backend
      scope: ['staff']
      tags: ['私域商城-订单']
    # 标记已提货
    - selector: mairpc.ec.order.OrderService.CompleteOrder
      post: /v2/ec/orders/{id}/complete
      body: '*'
      filterMark: staff,openapi
      scope: ['app', 'staff']
      tags: ['私域商城-订单']
      hideRequestFields: memberId
    # 标记已开票
    - selector: mairpc.ec.order.OrderService.ConfirmIssuedOrderInvoice
      post: /v2/ec/orders/{id}/confirmIssuedInvoice
      body: '*'
      filterMark: staff,backend
      scope: ['app', 'staff']
      tags: ['私域商城-订单']
      hideRequestFields: memberId
    # 售后退款详情
    - selector: mairpc.ec.order.OrderService.GetOrderRefund
      get: /v2/ec/orderRefunds/{id}
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-订单']
    # 售后退款列表
    - selector: mairpc.ec.order.OrderService.ListOrderRefunds
      get: /v2/ec/orderRefunds
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-订单']
      permission: 'ec:orderRefund:list'
    - selector: mairpc.ec.order.OrderService.ListOrderRefunds
      post: /v2/ec/listOrderRefunds
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-订单']
      permission: 'ec:orderRefund:list'
      sensitiveFields: 'items.contact.name,items.contact.phone'
    # 订单退款重试
    - selector: mairpc.ec.order.OrderService.RetryRefund
      put: /v2/ec/orderRefunds/{id}/retry
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单']
    # 小票打印
    - selector: mairpc.ec.order.OrderService.PrintTicket
      post: /v2/ec/orders/{orderId}/printTicket
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-订单']
    # 获取核销提货码详情
    - selector: mairpc.ec.order.OrderService.GetOrderByCode
      get: /v2/ec/orders/redeem/{code}
      filterMark: staff
      scope: ['staff']
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.ExportOrder
      post: /v2/ec/orders/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单']
      permission: 'ec:order:list'
    - selector: mairpc.ec.order.OrderService.ExportOrderProduct
      post: /v2/ec/orders/product/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.ExportOrderRefund
      post: /v2/ec/orderRefunds/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.DeliverOrderOffline
      post: /v2/ec/orders/{id}/deliverOffline
      body: '*'
      filterMark: backend,staff,openapi
      tags: ['私域商城-订单']
      scope: ['app', 'staff']
    - selector: mairpc.ec.order.OrderService.DeliverOrderOnline
      post: /v2/ec/orders/{id}/deliverOnline
      body: '*'
      filterMark: backend,staff
      tags: ['私域商城-订单']
      scope: ['staff']
    - selector: mairpc.ec.order.OrderService.UpdateOrderRefund
      put: /v2/ec/orderRefunds/{id}
      body: '*'
      filterMark: backend,staff
      tags: ['私域商城-订单']
      scope: ['staff']
      hideRequestFields: memberId
    - selector: mairpc.ec.order.OrderService.GetOrderTrace
      get: /v2/ec/orders/{id}/trace
      filterMark: backend,staff
      tags: ['私域商城-订单']
      scope: ['staff', 'app']
    # 确认支付线下支付订单
    - selector: mairpc.ec.order.OrderService.PayOfflineOrder
      put: /v2/ec/orders/{id}/payOffline
      body: '*'
      filterMark: staff
      scope: ['staff']
      tags: ['私域商城-订单']
    # 获取分账明细列表
    - selector: mairpc.ec.order.OrderService.ListOrderReceiverProfits
      get: /v2/ec/profitsharing/transferBills/{transferBillId}/profits
      filterMark: backend
      tags: ['私域商城-分账']
    # 导出分账明细列表
    - selector: mairpc.ec.order.OrderService.ExportOrderReceiverProfits
      post: /v2/ec/profitsharing/transferProfits/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-分账']
    - selector: mairpc.ec.order.OrderService.GetOrderByTransferBillId
      get: /v2/ec/transferBill/{id}/order
      filterMark: staff
      scope: ['staff']
      tags: ['私域商城-分账']
    - selector: mairpc.ec.order.OrderService.ListPresentCampaignRecords
      get: /v2/ec/marketing/presentCampaignRecords
      filterMark: backend
      tags: ['私域商城-营销']
      sensitiveFields: 'items.name'
    - selector: mairpc.ec.order.OrderService.ExportPresentCampaignRecords
      post: /v2/ec/marketing/presentCampaignRecords/{campaignId}/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-营销']
    - selector: mairpc.ec.order.OrderService.ExecDataAnalyses
      get: /v2/ec/dataAnalyses/exec
      filterMark: backend
      tags: ['私域商城-营销']
    - selector: mairpc.ec.order.OrderService.ListProfitOrderStoresByTransferBillId
      get: /v2/ec/profitsharing/transferBills/{transferBillId}/stores
      filterMark: backend
      tags: ['私域商城-营销']
    - selector: mairpc.ec.order.OrderService.ListPackageCampaignRecords
      get: /v2/ec/marketing/packageCampaignRecords
      filterMark: backend
      tags: ['私域商城-营销']
      sensitiveFields: 'items.name'
    - selector: mairpc.ec.order.OrderService.ExportPackageCampaignRecords
      post: /v2/ec/marketing/packageCampaignRecords/{campaignId}/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-营销']
    # 获取客户累计退款金额和退款订单数量
    - selector: mairpc.ec.order.OrderService.GetRefundOrderStats
      get: /v2/ec/refundOrderStats
      filterMark: backend
      tags: ['客户-详情']
    - selector: mairpc.ec.order.OrderService.ReplyComment
      put: /v2/ec/comments/{id}/reply
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.DeleteCommentReply
      delete: /v2/ec/comments/{id}/reply
      filterMark: backend
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.UpdateCommentStatus
      put: /v2/ec/comments/{id}/status
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.BatchUpdateCommentStatus
      post: /v2/ec/comments/batchUpdateStatus
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.UpdateCommentHighlight
      put: /v2/ec/comments/{id}/highlight
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.UpdateCommentTopping
      put: /v2/ec/comments/{id}/topping
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.ListComments
      get: /v2/ec/comments
      additional_bindings:
        - post: /v2/ec/comments/search
          body: '*'
      filterMark: backend
      sensitiveFields: 'items.member.name,items.member.phone'
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.CountComments
      post: /v2/ec/comments/getCount
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.GetPromoterOrderAmount
      get: /v2/ec/staff/distributionStats
      filterMark: staff
      scope: ['staff']
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.GetMemberOrderRank
      get: /v2/ec/order/memberRanks
      filterMark: backend
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.GetOrderRank
      get: /v2/ec/order/storeRanks
      filterMark: backend
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.ListIncomeRecords
      get: /v2/ec/incomeRecords
      filterMark: backend
      tags: ['私域商城-账单']
    - selector: mairpc.ec.order.OrderService.SearchIncomeRecords
      post: /v2/ec/incomeRecords/search
      body: '*'
      filterMark: backend
      tags: ['私域商城-账单']
    - selector: mairpc.ec.order.OrderService.CountIncomeRecordsAmount
      post: /v2/ec/incomeRecords/countAmount
      body: '*'
      filterMark: backend
      tags: ['私域商城-账单']
    - selector: mairpc.ec.order.OrderService.ExportIncomeRecords
      post: /v2/ec/incomeRecords/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-账单']
    - selector: mairpc.ec.order.OrderService.ListSpentRecords
      get: /v2/ec/spentRecords
      filterMark: backend
      tags: ['私域商城-账单']
    - selector: mairpc.ec.order.OrderService.ExportSpentRecords
      post: /v2/ec/spentRecords/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-账单']
    - selector: mairpc.ec.order.OrderService.ListBillDetails
      get: /v2/ec/spentRecords/{id}/details
      filterMark: backend
      tags: ['私域商城-账单']
    - selector: mairpc.ec.order.OrderService.ExportBillDetails
      post: /v2/ec/spentRecords/{id}/details/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-账单']
    - selector: mairpc.ec.order.OrderService.GetSpentRecord
      get: /v2/ec/spentRecords/{id}
      filterMark: backend
      tags: ['私域商城-账单']
    - selector: mairpc.ec.order.OrderService.ListStoresBySpentRecordId
      get: /v2/ec/spentRecords/{spentRecordId}/stores
      filterMark: backend
      tags: ['私域商城-账单']
    # 代销小店数据概览
    - selector: mairpc.ec.order.OrderService.GetMallOverviewStats
      get: /v2/ec/mallOverviewStats
      filterMark: backend
      tags: ['私域商城-代销']
    - selector: mairpc.ec.order.OrderService.CalculateOrderAmount
      post: /v2/ec/orders/calculateOrderAmount
      body: '*'
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.GetMembershipDiscounts
      get: /v2/ec/order/membershipDiscounts
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.AdjustOrderAmount
      post: /v2/ec/orders/{id}/adjustAmount
      body: '*'
      tags: ['私域商城-订单']
    # 创建发票
    - selector: mairpc.ec.order.OrderService.CreateInvoice
      post: /v2/ec/invoices
      filterMark: backend
      body: '*'
      tags: ['私域商城-发票']
    # 创建合并发票
    - selector: mairpc.ec.order.OrderService.CreateMergedInvoice
      post: /v2/ec/mergedInvoices
      filterMark: backend
      body: '*'
      tags: ['私域商城-发票']
    # 更新发票
    - selector: mairpc.ec.order.OrderService.UpdateInvoice
      put: /v2/ec/invoices/{id}
      additional_bindings:
        - post: /v2/ec/invoices/update
          body: '*'
      filterMark: backend
      body: '*'
      tags: ['私域商城-发票']
      hideRequestFields: invoice
    # 获取发票详情
    - selector: mairpc.ec.order.OrderService.ListInvoices
      get: /v2/ec/invoices
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-发票']
    # 获取开票记录
    - selector: mairpc.ec.order.OrderService.ListInvoiceRecords
      get: /v2/ec/invoiceRecords
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-发票']
    # 延期虚拟商品核销有效期
    - selector: mairpc.ec.order.OrderService.DelayVirtualProductRedeemPeriod
      post: /v2/ec/virtualProductRedeemPeriod/delay
      filterMark: backend
      body: '*'
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.ListCartProductsByPagination
      post: /v2/ec/cartProducts/search
      filterMark: backend,staff
      scope: ['staff']
      body: '*'
      tags: ['私域商城-购物车']
    - selector: mairpc.ec.order.OrderService.ReissueMissingOrderReceiverProfit
      post: /v2/ec/orderReceiverProfit/reissue
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单']
    # 开发票
    - selector: mairpc.ec.order.OrderService.Invoice
      post: /v2/ec/orders/{orderId}/invoice
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-订单']
    # 测试完成后删除
    - selector: mairpc.ec.order.OrderService.HandleVirtualOrderProfit
      post: /v2/ec/virtualOrderProfits/handle
      tags: ['测试']
    - selector: mairpc.ec.order.OrderService.ExportDeliveryTemplate
      post: /v2/ec/orders/exportDeliveryTemplate
      body: '*'
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.UpdateLogistics
      put: /v2/ec/logistics
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.BatchDeliverOrderOffline
      post: /v2/ec/orders/batchDeliverOffline
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.GetSensitive
      get: /v2/ec/orders/{orderId}/sensitive
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.ImportPickupPasswords
      post: /v2/ec/pickupPasswords/import
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.ExportPickupPasswords
      post: /v2/ec/pickupPasswords/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.CheckRedeemCode
      post: /v2/ec/redeemCode/check
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-订单']
    # 更新订单收货地址
    - selector: mairpc.ec.order.OrderService.UpdateOrderContact
      put: /v2/ec/orders/{orderId}/contact
      filterMark: backend
      body: '*'
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.HandleDivideBackRecord
      get: /v2/ec/handleDivideBackRecord
      filterMark: backend
      tags: ['私域商城-订单']
    # 获取购物车分组商品
    - selector: mairpc.ec.order.OrderService.ListCartGroups
      get: /v2/ec/cartGroups
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-购物车']
    # 转换购物车商品选中状态
    - selector: mairpc.ec.order.OrderService.ToggleCartProductsCheckedStatus
      post: /v2/ec/cartProducts/batch/toggleCheckedStatus
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-购物车']
    # 批量添加购物车商品
    - selector: mairpc.ec.order.OrderService.BatchCreateCartProducts
      post: /v2/ec/batch/cartProducts
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-购物车']
    # 更新购物车商品数量
    - selector: mairpc.ec.order.OrderService.SetCartProductCount
      post: /v2/ec/cartProducts/{id}/setCartProductCount
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-购物车']
    # 删除购物车商品
    - selector: mairpc.ec.order.OrderService.DeleteCartProducts
      delete: /v2/ec/cartProducts
      body: '*'
      filterMark: backend
      scope: ['staff']
      tags: ['私域商城-购物车']
    # 创建代客下单订单
    - selector: mairpc.ec.order.OrderService.CreateProxyOrder
      post: /v2/ec/proxyOrders
      body: '*'
      filterMark: backend,staff
      tags: ['私域商城-代客下单']
    # 代客下单订单详情
    - selector: mairpc.ec.order.OrderService.GetProxyOrder
      get: /v2/ec/proxyOrders/{id}
      filterMark: backend,staff
      tags: ['私域商城-代客下单']
    # 代客户下单订单列表
    - selector: mairpc.ec.order.OrderService.ListProxyOrders
      get: /v2/ec/proxyOrders
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-代客下单']
    # 将代下单订单商品加到客户购物车中
    - selector: mairpc.ec.order.OrderService.AddProxyOrderProductsToCart
      post: /v2/ec/proxyOrderCartProducts/add
      body: '*'
      filterMark: backend,staff
      tags: ['私域商城-代客下单']
