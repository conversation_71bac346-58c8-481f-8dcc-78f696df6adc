syntax = "proto3";

package mairpc.ec.order;

option go_package = "order";

import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/response/response.proto";
import "ec/order/cart.proto";
import "ec/order/comment.proto";
import "ec/order/order.proto";
import "ec/order/orderReceiverProfit.proto";

service OrderService {
  // 添加购物车商品
  rpc CreateCartProduct(CreateCartProductRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量添加购物车商品
  rpc BatchCreateCartProducts(BatchCreateCartProductsRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除购物车商品
  rpc DeleteCartProducts(DeleteCartProductsRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取购物车商品
  rpc ListCartProducts(ListCartProductsRequest) returns (ListCartProductsResponse);
  // 购物车分页查询商品
  rpc ListCartProductsByPagination(ListCartProductsByPaginationRequest) returns (ListCartProductsByPaginationResponse);
  // 更新购物车商品选中状态
  rpc ToggleCartProductsCheckedStatus(ToggleCheckedStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新购物车商品数量
  rpc SetCartProductCount(SetCartProductCountRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新购物车商品规格
  rpc UpdateCartProductSku(UpdateCartProductSkuRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建订单
  rpc CreateOrder(mairpc.common.ec.CreateOrderRequest) returns (OrderDetail);
  // 直接购买
  rpc Purchase(mairpc.common.ec.PurchaseRequest) returns (OrderDetail);
  // 取消订单
  rpc CancelOrder(CancelOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 退款
  rpc RefundOrder(RefundOrderRequest) returns (RefundOrderResponse);
  // 分配订单
  rpc AssignOrder(AssignOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新备注
  rpc UpdateRemarks(UpdateRemarksRequest) returns (mairpc.common.response.EmptyResponse);
  // 接单
  rpc AcceptOrder(AcceptOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 拒单
  rpc RejectOrder(RejectOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 核销提货码
  rpc RedeemOrder(RedeemOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 订单完成
  //
  // 店员标记已提货/客户确认收货
  rpc CompleteOrder(CompleteOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 标记已开票
  rpc ConfirmIssuedOrderInvoice(ConfirmIssuedOrderInvoiceRequest) returns (mairpc.common.response.EmptyResponse);
  // 订单列表
  rpc ListOrders(ListOrdersRequest) returns (ListOrdersResponse);
  // 统计订单数
  rpc CountOrders(ListOrdersRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 待分配订单列表
  rpc ListUnassignedOrders(ListOrdersRequest) returns (ListOrdersResponse);
  // 订单详情
  rpc GetOrder(GetOrderRequest) returns (OrderDetail);
  // 售后退款详情
  rpc GetOrderRefund(GetOrderRefundRequest) returns (OrderRefundDetail);
  // 售后退款列表
  rpc ListOrderRefunds(ListOrderRefundsRequest) returns (ListOrderRefundsResponse);
  // 批量更新售后订单状态
  rpc BatchUpdateRefundStatus(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新售后订单状态
  rpc UpdateRefundStatus(UpdateRefundStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 支付订单
  rpc PrePay(PrePayRequest) returns (mairpc.common.ec.PrePayResponse);
  // 删除交易成功和交易关闭订单
  rpc DeleteOrder(DeleteOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 打印小票
  rpc PrintTicket(PrintTicketRequest) returns (mairpc.common.response.EmptyResponse);
  // 打印退货小票
  rpc PrintRefundTicket(PrintTicketRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理未支付的订单
  //
  // 提醒即将过期的订单，及取消过期的订单
  rpc HandleUnpaidOrders(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取核销提货码详情
  rpc GetOrderByCode(GetOrderByCodeRequest) returns (OrderDetail);
  // 订单支付 Webhook
  rpc OrderPaidWebhook(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新退款订单状态
  rpc RetryRefund(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 导出订单
  rpc ExportOrder(ListOrdersRequest) returns (mairpc.common.response.JobResponse);
  // 导出订单商品
  rpc ExportOrderProduct(ListOrdersRequest) returns (mairpc.common.response.JobResponse);
  // 导出售后退款订单
  rpc ExportOrderRefund(ListOrderRefundsRequest) returns (mairpc.common.response.JobResponse);
  // 订单退款
  rpc ExecOrderRefundJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新订单打印状态
  rpc UpdatePrintTicketStatus(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 执行数据分析
  rpc ExecDataAnalyses(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 线下发货
  rpc DeliverOrderOffline(DeliverOrderOfflineRequest) returns (OrderDetail);
  // 在线发货
  rpc DeliverOrderOnline(DeliverOrderOnlineRequest) returns (OrderDetail);
  // 更新退款单
  rpc UpdateOrderRefund(UpdateOrderRefundRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取订单物流轨迹
  rpc GetOrderTrace(GetOrderTraceRequest) returns (GetOrderTraceResponse);
  // 自动完成订单
  rpc AutoCompleteOrderJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步订单到第三方 OMS
  rpc PushOrdersToOmsJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 推送退款订单到第三方
  rpc PushRefundOrderJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 从 OMS 同步退款单状态
  rpc SyncOrderRefundFromOmsJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 从 OMS 同步发货状态
  rpc SyncLogisticsFromOmsJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 自动关闭退款申请
  rpc AutoCancelOrderRefundJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 线下支付订单确认付款
  rpc PayOfflineOrder(PayOfflineOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 修改订单支付方式
  rpc UpdatePayment(UpdatePaymentRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取分账明细
  rpc ListOrderReceiverProfits(ListOrderReceiverProfitsRequest) returns (ListOrderReceiverProfitsResponse);
  // 导出分账明细
  rpc ExportOrderReceiverProfits(ListOrderReceiverProfitsRequest) returns (mairpc.common.response.JobResponse);
  // 买赠活动参与记录
  rpc ListPresentCampaignRecords(ListPresentCampaignRecordsRequest) returns (ListPresentCampaignRecordsResponse);
  // 更新订单自定义信息
  rpc UpdateOrderExtra(UpdateOrderExtraRequest) returns (mairpc.common.response.EmptyResponse);
  // 导出买赠活动参与记录
  rpc ExportPresentCampaignRecords(ListPresentCampaignRecordsRequest) returns (mairpc.common.response.JobResponse);
  // 修改订单活动
  rpc UpdateOrderCampaign(UpdateOrderCampaignRequest) returns (mairpc.common.response.EmptyResponse);
  // 添加评价
  rpc CreateComments(CreateCommentsRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除评价
  rpc DeleteComment(mairpc.common.request.DetailWithMemberIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 回复评价
  rpc ReplyComment(ReplyCommentRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除回复
  rpc DeleteCommentReply(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取评价列表
  rpc ListComments(ListCommentsRequest) returns (ListCommentsResponse);
  // 获取忽略用户的评价列表（isIgnoreMemberId 固定为 true）
  rpc ListCommentsExcludingMember(ListCommentsRequest) returns (ListCommentsResponse);
  // 获取评价总数
  rpc CountComments(ListCommentsRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 统计评价数量
  rpc CountCommentTotal(CountCommentTotalRequest) returns (CountCommentTotalResponse);
  // 更新评价审核状态
  rpc UpdateCommentStatus(UpdateCommentStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量更新评价审核状态
  rpc BatchUpdateCommentStatus(BatchUpdateCommentStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 加精评价
  rpc UpdateCommentHighlight(UpdateCommentHighlightRequest) returns (mairpc.common.response.EmptyResponse);
  // 置顶评价
  rpc UpdateCommentTopping(UpdateCommentToppingRequest) returns (mairpc.common.response.EmptyResponse);
  // 设置评价匿名
  rpc UpdateCommentAnonymous(UpdateCommentAnonymousRequest) returns (mairpc.common.response.EmptyResponse);
  // 推荐评价
  rpc UpsertCommentRecommendation(UpsertCommentRecommendationRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取当前用户已点击推荐的评价
  rpc CheckRecommendedComments(CheckRecommendedCommentsRequest) returns (ListRecommendedCommentsResponse);
  // 获取未评价订单
  rpc ListCommentableOrders(ListCommentableOrdersRequest) returns (ListCommentableOrdersResponse);
  // 通过分佣记录id获取分账订单门店列表
  rpc ListProfitOrderStoresByTransferBillId(ListProfitOrderStoresByTransferBillIdRequest) returns (ListProfitOrderStoresByTransferBillIdResponse);
  // 发送拼团消息
  rpc SendGrouponMessage(SendGrouponMessageRequest) returns (mairpc.common.response.EmptyResponse);
  // 查询客户累计退款金额和退款订单数
  rpc GetRefundOrderStats(GetRefundOrderStatsRequest) returns (GetRefundOrderStatsResponse);
  // 创建系统默认评价
  rpc CreateSystemDefaultComments(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取导购推广订单金额
  rpc GetPromoterOrderAmount(GetPromoterOrderAmountRequest) returns (GetPromoterOrderAmountResponse);
  // 是否存在订单
  rpc HasOrder(HasOrderRequest) returns (mairpc.common.response.BoolResponse);
  // OMS 发货确认通知
  rpc DeliveryConfirmNotifyFromOms(DeliveryConfirmNotifyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取订单统计信息
  rpc GetOrderStats(GetOrderStatsRequest) returns (GetOrderStatsResponse);
  // 获取订单信息排名
  rpc GetOrderRank(GetOrderRankRequest) returns (GetOrderRankResponse);
  // 获取店铺用户排名
  rpc GetMemberOrderRank(GetMemberOrderRankRequest) returns (GetMemberOrderRankResponse);
  // 账单收入记录列表
  rpc ListIncomeRecords(ListIncomeRecordsRequest) returns (ListIncomeRecordsResponse);
  // 账单支出记录列表
  rpc ListSpentRecords(ListSpentRecordsRequest) returns (ListSpentRecordsResponse);
  // 账单明细列表
  rpc ListBillDetails(ListBillDetailsRequest) returns (ListBillDetailsResponse);
  // 同步支出记录
  rpc SyncSpentRecords(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 支出记录详情
  rpc GetSpentRecord(mairpc.common.request.DetailRequest) returns (GetSpentRecordResponse);
  // 账单收入记录导出
  rpc ExportIncomeRecords(ListIncomeRecordsRequest) returns (mairpc.common.response.JobResponse);
  // 账单支出记录导出
  rpc ExportSpentRecords(ListSpentRecordsRequest) returns (mairpc.common.response.JobResponse);
  // 账单明细导出
  rpc ExportBillDetails(ListBillDetailsRequest) returns (mairpc.common.response.JobResponse);
  // 通过支出记录 id 获取门店列表
  rpc ListStoresBySpentRecordId(ListStoresBySpentRecordIdRequest) returns (ListProfitOrderStoresByTransferBillIdResponse);
  // 小店数据概览
  rpc GetMallOverviewStats(mairpc.common.request.EmptyRequest) returns (GetMallOverviewStatsResponse);
  // OMS 退货确认通知
  rpc ReturnConfirmNotifyFromOms(ReturnConfirmNotifyRequest) returns (mairpc.common.response.EmptyResponse);
  // OMS 退款确认通知
  rpc RefundConfirmNotifyFromOms(RefundConfirmNotifyRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新同城快送订单
  rpc UpdateCityExpressOrder(UpdateCityExpressOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 计算订单金额
  rpc CalculateOrderAmount(mairpc.common.ec.CalculateOrderAmountRequest) returns (mairpc.common.ec.CalculateOrderAmountResponse);
  // 促销活动参与记录
  rpc ListPackageCampaignRecords(ListPackageCampaignRecordsRequest) returns (ListPackageCampaignRecordsResponse);
  // 导出促销活动参与记录
  rpc ExportPackageCampaignRecords(ListPackageCampaignRecordsRequest) returns (mairpc.common.response.JobResponse);
  // 获取订单优惠券
  rpc GetMembershipDiscounts(mairpc.common.ec.GetMembershipDiscountsRequest) returns (mairpc.common.ec.GetMembershipDiscountsResponse);
  // 按商品拆分订单(元祖梦世界特殊拆单逻辑)
  rpc SplitOrderByProduct(SplitOrderByProductRequest) returns (SplitOrderByProductResponse);
  // 拆分订单
  rpc SplitOrder(SplitOrderRequest) returns (SplitOrderResponse);
  // 统计购物车商品数
  rpc CountCartProducts(CountCartProductsRequest) returns (mairpc.common.response.IntResponse);
  // 获取购物车分组商品详情
  rpc ListCartGroups(ListCartGroupsRequest) returns (ListCartGroupsResponse);
  // 发送尾款即将开始或结束消息
  rpc RemindBalancePayment(RemindBalancePaymentRequest) returns (mairpc.common.response.EmptyResponse);
  // 取消活动订单
  rpc HandleCampaignUnpaidOrders(HandleCampaignUnpaidOrdersRequest) returns (mairpc.common.response.EmptyResponse);
  // 订单核销记录列表
  rpc ListOrderRedemptionRecords(ListOrderRedemptionRecordsRequest) returns (ListOrderRedemptionRecordsResponse);
  // 支付尾款
  rpc PrePayBalance(mairpc.common.ec.PrePayBalanceRequest) returns (mairpc.common.ec.PrePayResponse);
  // 订阅订单活动事件，处理订单活动
  rpc HandleOrderCampaign(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新月结分销订单分销状态
  rpc UpdateMonthlyDistribution(UpdateMonthlyDistributionRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建发票
  rpc CreateInvoice(CreateInvoiceRequest) returns (mairpc.common.response.EmptyResponse);
  // 合并开票
  rpc CreateMergedInvoice(CreateMergedInvoiceRequest) returns (mairpc.common.response.EmptyResponse);
  // 修改发票
  rpc UpdateInvoice(UpdateInvoiceRequest) returns (mairpc.common.response.EmptyResponse);
  // 发票列表
  rpc ListInvoices(ListInvoicesRequest) returns (ListInvoicesResponse);
  // 获取开票记录
  rpc ListInvoiceRecords(ListInvoiceRecordsRequest) returns (ListInvoiceRecordsResponse);
  // 发送发票到邮箱
  rpc SendInvoiceEmail(SendInvoiceEmailRequest) returns (mairpc.common.response.EmptyResponse);
  // 统计各状态订单数
  rpc CountOrdersByStatus(CountOrdersByStatusRequest) returns (CountOrdersByStatusResponse);
  // 获取商品购买统计
  rpc GetOrderProductPurchaseStats(GetOrderProductPurchaseStatsRequest) returns (mairpc.common.response.IntResponse);
  // 定时发货虚拟商品预售订单
  rpc DeliverVirtualPresellOrders(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取销售金额和销量统计
  rpc GetOrderCampaignStats(GetOrderCampaignStatsRequest) returns (GetOrderCampaignStatsResponse);
  // 订单改价
  rpc AdjustOrderAmount(AdjustOrderAmountRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取商品限购状态
  rpc GetProductPurchaseLimitStatus(GetProductPurchaseLimitStatusRequest) returns (mairpc.common.response.StringResponse);
  // 获取订单支持的配送方式
  rpc GetOrderDeliveryMethods(GetOrderDeliveryMethodsRequest) returns (GetOrderDeliveryMethodsResponse);
  // 判断客户是否是首单
  rpc IsFirstPayment(mairpc.common.request.MemberIdRequest) returns (mairpc.common.response.BoolResponse);
  // 延期虚拟商品核销有效期
  rpc DelayVirtualProductRedeemPeriod(DelayVirtualProductRedeemPeriodRequest) returns (mairpc.common.response.EmptyResponse);
  // 虚拟商品即将过期提醒
  rpc RemindVirtualProductWillExpire(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 修改商品订单收货地址
  rpc UpdateOrderContact(UpdateOrderContactRequest) returns (mairpc.common.response.EmptyResponse);
  // 补发分销接受者创建前缺失的日结预分账记录
  rpc ReissueMissingOrderReceiverProfit(ReissueMissingOrderReceiverProfitRequest) returns (mairpc.common.response.EmptyResponse);
  // 开发票
  rpc Invoice(InvoiceRequest) returns (mairpc.common.response.EmptyResponse);
  // 开发票 v2
  rpc InvoiceV2(InvoiceRequest) returns (mairpc.common.response.EmptyResponse);
  // 检查发票状态
  rpc CheckInvoiceStatus(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新订单 tags
  rpc UpdateOrderTags(UpdateTagsRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新退款单 tags
  rpc UpdateOrderRefundTags(UpdateTagsRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取订单可用权益卡
  rpc GetMemberPaidCardRecords(GetMemberPaidCardRecordsRequest) returns (GetMemberPaidCardRecordsResponse);
  // 收入记录列表
  rpc SearchIncomeRecords(ListIncomeRecordsRequest) returns (SearchIncomeRecordsResponse);
  // 统计收入金额
  rpc CountIncomeRecordsAmount(ListIncomeRecordsRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 同步收入记录
  rpc SyncIncomeRecords(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取客户消费统计
  rpc GetMemberOrderStats(GetMemberOrderStatsRequest) returns (GetMemberOrderStatsResponse);
  // 处理虚拟商品订单全部核销后 15 天分销
  rpc HandleVirtualOrderProfit(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 根据 ec.transferBill._id 获取对应的 ec.order
  rpc GetOrderByTransferBillId(mairpc.common.request.DetailRequest) returns (OrderDetail);
  // 创建订单
  rpc CreateSimpleOrder(CreateSimpleOrderRequest) returns (OrderDetail);
  // 导出发货模板
  rpc ExportDeliveryTemplate(ExportDeliveryTemplateRequest) returns (ExportDeliveryTemplateResponse);
  // 更新物流信息
  rpc UpdateLogistics(UpdateLogisticsRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量线下发货
  rpc BatchDeliverOrderOffline(BatchDeliverOrderOfflineRequest) returns (mairpc.common.response.JobResponse);
  // 获取活动限购数量
  rpc GetCampaignPurchaseLimit(GetCampaignPurchaseLimitRequest) returns (CampaignPurchaseLimit);
  // 获取敏感信息
  rpc GetSensitive(GetSensitiveRequest) returns (GetSensitiveResponse);
  // 批量导入自提口令
  rpc ImportPickupPasswords(ImportPickupPasswordsRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 导出自提口令
  rpc ExportPickupPasswords(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.JobResponse);
  // 更新订单 OMS 信息
  rpc UpdateOrderOmsProcessor(UpdateOmsProcessorRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新退款单 OMS 信息
  rpc UpdateOrderRefundOmsProcessor(UpdateOmsProcessorRequest) returns (mairpc.common.response.EmptyResponse);
  // 检查核销码信息
  rpc CheckRedeemCode(CheckRedeemCodeRequest) returns (CheckRedeemCodeResponse);
  // 批量添加购物车商品
  rpc BatchAddCartProducts(BatchAddCartProductsRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取可用的储值卡
  rpc ListPrepaidCards(mairpc.common.ec.ListPrepaidCardsRequest) returns (ListPrepaidCardsResponse);
  // 处理分账回退记录
  rpc HandleDivideBackRecord(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理使用收钱吧支付的未支付订单
  rpc HandleUnpaidOrdersWithShouqianba(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 代客下单
  //
  // 创建代客下单订单
  rpc CreateProxyOrder(CreateProxyOrderRequest) returns (ProxyOrderDetail);
  // 代客下单列表
  rpc ListProxyOrders(ListProxyOrdersRequest) returns (ListProxyOrdersResponse);
  // 代客下单详情
  rpc GetProxyOrder(mairpc.common.request.DetailRequest) returns (ProxyOrderDetail);
  // 更新代客下单
  rpc UpdateProxyOrder(UpdateProxyOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 添加代客下单商品到客户购物车
  rpc AddProxyOrderProductsToCart(AddProxyOrderProductsToCartRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步 order 状态到 proxyOrder  job
  rpc SyncOrderStatusToProxyOrderJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 申请发票红字确认单
  rpc CreateRedConfirmation(CreateRedConfirmationRequest) returns (CreateRedConfirmationResponse);
  // 发送自提订单提醒通知
  rpc SendPickupOrderNotification(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
}
