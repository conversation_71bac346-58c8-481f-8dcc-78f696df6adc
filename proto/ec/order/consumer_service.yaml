type: google.api.Service
config_version: 3

http:
  rules:
    # 添加购物车商品
    - selector: mairpc.ec.order.OrderService.CreateCartProduct
      post: /v2/ec/cartProducts
      body: '*'
      filterMark: backend
      tags: ['私域商城-购物车']
    # 批量添加购物车商品
    - selector: mairpc.ec.order.OrderService.BatchCreateCartProducts
      post: /v2/ec/batch/cartProducts
      body: '*'
      filterMark: backend
      tags: ['私域商城-购物车']
    # 删除购物车商品
    - selector: mairpc.ec.order.OrderService.DeleteCartProducts
      delete: /v2/ec/cartProducts
      body: '*'
      filterMark: backend
      tags: ['私域商城-购物车']
    # 获取购物车商品
    - selector: mairpc.ec.order.OrderService.ListCartProducts
      get: /v2/ec/cartProducts
      filterMark: backend
      tags: ['私域商城-购物车']
    # 转换购物车商品选中状态
    - selector: mairpc.ec.order.OrderService.ToggleCartProductsCheckedStatus
      post: /v2/ec/cartProducts/batch/toggleCheckedStatus
      body: '*'
      filterMark: backend
      tags: ['私域商城-购物车']
    # 更新购物车商品数量
    - selector: mairpc.ec.order.OrderService.SetCartProductCount
      post: /v2/ec/cartProducts/{id}/setCartProductCount
      body: '*'
      filterMark: backend
      tags: ['私域商城-购物车']
      # 更新购物车商品规格
    - selector: mairpc.ec.order.OrderService.UpdateCartProductSku
      put: /v2/ec/cartProducts/{id}
      body: '*'
      filterMark: backend
      tags: ['私域商城-购物车']
    # 订单列表
    - selector: mairpc.ec.order.OrderService.ListOrders
      get: /v2/ec/orders
      tags: ['私域商城-订单']
      hideResponseFields: items.products.couponId
    # 订单列表
    - selector: mairpc.ec.order.OrderService.ListOrders
      post: /v2/ec/listOrders
      body: '*'
      tags: ['私域商城-订单']
      hideResponseFields: items.products.couponId
      sensitiveFields: 'items.contact.name,items.contact.phone'
    # 创建订单
    - selector: mairpc.ec.order.OrderService.CreateOrder
      post: /v2/ec/orders
      body: '*'
      tags: ['私域商城-订单']
      hideResponseFields: products.couponId
    # 直接购买
    - selector: mairpc.ec.order.OrderService.Purchase
      post: /v2/ec/orders/purchase
      body: '*'
      tags: ['私域商城-订单']
      hideRequestFields: campaignConfig,isIgnoreStoreStatus
      hideResponseFields: products.couponId
    # 取消订单
    - selector: mairpc.ec.order.OrderService.CancelOrder
      post: /v2/ec/orders/{id}/cancel
      body: '*'
      tags: ['私域商城-订单']
    # 订单退款
    - selector: mairpc.ec.order.OrderService.RefundOrder
      post: /v2/ec/orders/{id}/refund
      body: '*'
      tags: ['私域商城-订单']
      hideRequestFields: isCancelOrder,isSystem,refundAmount,isRefundDeliveryFee,customRefundAmountReason,remarks
    # 订单支付
    - selector: mairpc.ec.order.OrderService.PrePay
      post: /v2/ec/prePay
      body: '*'
      tags: ['私域商城-订单']
    # 修改订单支付方式
    - selector: mairpc.ec.order.OrderService.UpdatePayment
      put: /v2/ec/orders/{id}/payment
      body: '*'
      filterMark: staff
      scope: ['staff']
      tags: ['私域商城-订单']
    # 订单详情
    - selector: mairpc.ec.order.OrderService.GetOrder
      get: /v2/ec/orders/{id}
      tags: ['私域商城-订单']
      hideResponseFields: products.couponId
    # 订单详情
    - selector: mairpc.ec.order.OrderService.GetOrder
      post: /v2/ec/getOrder
      body: '*'
      tags: ['私域商城-订单']
      hideResponseFields: products.couponId
      sensitiveFields: 'contact.name,contact.phone'
    # 删除订单
    - selector: mairpc.ec.order.OrderService.DeleteOrder
      delete: /v2/ec/orders/{id}
      body: '*'
      tags: ['私域商城-订单']
    # 更新退款单
    - selector: mairpc.ec.order.OrderService.UpdateOrderRefund
      put: /v2/ec/orderRefunds/{id}
      body: '*'
      tags: ['私域商城-订单']
      hideRequestFields: refundAmount,refundAmountChangeReason,remarks
    # 更新订单收货地址
    - selector: mairpc.ec.order.OrderService.UpdateOrderContact
      put: /v2/ec/orders/{orderId}/contact
      filterMark: backend
      body: '*'
      tags: ['私域商城-订单']
    # 售后退款详情
    - selector: mairpc.ec.order.OrderService.GetOrderRefund
      get: /v2/ec/orderRefunds/{id}
      tags: ['私域商城-订单']
    # 获取订单物流轨迹
    - selector: mairpc.ec.order.OrderService.GetOrderTrace
      get: /v2/ec/orders/{id}/trace
      tags: ['私域商城-订单']
    # 确认收货
    - selector: mairpc.ec.order.OrderService.CompleteOrder
      post: /v2/ec/orders/{id}/complete
      body: '*'
      tags: ['私域商城-订单']
      hideRequestFields: staffId,reason
    - selector: mairpc.ec.order.OrderService.HasOrder
      post: /v2/ec/orders/hasOrder
      body: '*'
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.IsFirstPayment
      post: /v2/ec/members/{memberId}/isFirstPayment
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.CreateComments
      post: /v2/ec/comments
      body: '*'
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.DeleteComment
      delete: /v2/ec/comments/{id}
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.UpsertCommentRecommendation
      put: /v2/ec/comments/{id}/recommendation
      body: '*'
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.ListCommentableOrders
      get: /v2/ec/commentableOrders
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.CheckRecommendedComments
      get: /v2/ec/recommendedComments
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.ListComments
      get: /v2/ec/comments
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.ListComments
      post: /v2/ec/listComments
      body: '*'
      tags: ['私域商城-订单评价']
      sensitiveFields: 'items.member.name,items.member.phone'
    - selector: mairpc.ec.order.OrderService.ListCommentsExcludingMember
      post: /v2/ec/comments/searchExcludingMember
      body: '*'
      sensitiveFields: 'items.member.name,items.member.phone'
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.CountCommentTotal
      post: /v2/ec/comments/count
      body: '*'
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.UpdateCommentAnonymous
      put: /v2/ec/comments/{id}/anonymous
      body: '*'
      tags: ['私域商城-订单评价']
    - selector: mairpc.ec.order.OrderService.UpdateCityExpressOrder
      put: /v2/ec/cityExpressOrders/{orderId}
      body: '*'
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.CalculateOrderAmount
      post: /v2/ec/orders/calculateOrderAmount
      body: '*'
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.GetMembershipDiscounts
      post: /v2/ec/order/searchMembershipDiscounts
      body: '*'
      tags: ['私域商城-订单']
      hideRequestFields: campaignConfig
    - selector: mairpc.ec.order.OrderService.GetMembershipDiscounts
      get: /v2/ec/order/membershipDiscounts
      tags: ['私域商城-订单']
      hideRequestFields: campaignConfig
    - selector: mairpc.ec.order.OrderService.ListPrepaidCards
      post: /v2/ec/order/searchPrepaidCards
      body: '*'
      tags: ['私域商城-订单']
      # 售后退款列表
    - selector: mairpc.ec.order.OrderService.ListOrderRefunds
      get: /v2/ec/orderRefunds
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.ListOrderRefunds
      post: /v2/ec/listOrderRefunds
      body: '*'
      tags: ['私域商城-订单']
      sensitiveFields: 'items.contact.name,items.contact.phone'
    - selector: mairpc.ec.order.OrderService.CountOrdersByStatus
      post: /v2/ec/order/countByStatus
      body: '*'
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.CountCartProducts
      post: /v2/ec/countCartProducts
      body: '*'
      tags: ['私域商城-购物车']
    # 获取购物车分组商品
    - selector: mairpc.ec.order.OrderService.ListCartGroups
      get: /v2/ec/cartGroups
      tags: ['私域商城-购物车']
    # 创建发票
    - selector: mairpc.ec.order.OrderService.CreateInvoice
      post: /v2/ec/invoices
      body: '*'
      tags: ['私域商城-发票']
    # 创建发票
    - selector: mairpc.ec.order.OrderService.CreateMergedInvoice
      post: /v2/ec/mergedInvoices
      body: '*'
      tags: ['私域商城-发票']
    # 更新发票
    - selector: mairpc.ec.order.OrderService.UpdateInvoice
      put: /v2/ec/invoices/{id}
      body: '*'
      hideRequestFields: status,amount,result
      tags: ['私域商城-发票']
    # 获取发票详情
    - selector: mairpc.ec.order.OrderService.ListInvoices
      get: /v2/ec/invoices
      tags: ['私域商城-发票']
    # 获取开票记录
    - selector: mairpc.ec.order.OrderService.ListInvoiceRecords
      get: /v2/ec/invoiceRecords
      tags: ['私域商城-发票']
    # 发送发票到邮箱
    - selector: mairpc.ec.order.OrderService.SendInvoiceEmail
      post: /v2/ec/invoices/{id}/sendEmail
      body: '*'
      tags: ['私域商城-发票']
    - selector: mairpc.ec.order.OrderService.GetOrderProductPurchaseStats
      get: /v2/ec/products/{productId}/purchaseStats
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.GetProductPurchaseLimitStatus
      get: /v2/ec/products/{productId}/purchaseLimitStatus
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.GetOrderDeliveryMethods
      get: /v2/ec/order/deliveryMethods
      additional_bindings:
        - post: /v2/ec/order/deliveryMethods/search
          body: '*'
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.GetMemberPaidCardRecords
      post: /v2/ec/order/searchMemberPaidCardRecords
      body: '*'
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.GetMemberOrderStats
      get: /v2/ec/memberOrderStats
      tags: ['私域商城-订单']
    - selector: mairpc.ec.order.OrderService.GetCampaignPurchaseLimit
      get: /v2/ec/campaignPurchaseLimit
      tags: ['私域商城-订单']
    # 批量添加购物车商品
    - selector: mairpc.ec.order.OrderService.BatchAddCartProducts
      post: /v2/ec/cartProducts/batchAdd
      body: '*'
      tags: ['私域商城-购物车']
    # 创建代客下单订单
    - selector: mairpc.ec.order.OrderService.CreateProxyOrder
      post: /v2/ec/proxyOrders
      body: '*'
      tags: ['私域商城-代客下单']
    # 代客下单订单详情
    - selector: mairpc.ec.order.OrderService.GetProxyOrder
      get: /v2/ec/proxyOrders/{id}
      tags: ['私域商城-代客下单']
    # 代客户下单订单列表
    - selector: mairpc.ec.order.OrderService.ListProxyOrders
      get: /v2/ec/proxyOrders
      tags: ['私域商城-代客下单']
    # 将代下单订单商品加到客户购物车中
    - selector: mairpc.ec.order.OrderService.AddProxyOrderProductsToCart
      post: /v2/ec/proxyOrderCartProducts/add
      body: '*'
      tags: ['私域商城-代客下单']
    # 更新代客下单订单
    - selector: mairpc.ec.order.OrderService.UpdateProxyOrder
      put: /v2/ec/proxyOrders/{id}
      body: '*'
      tags: ['私域商城-代客下单']
