syntax = "proto3";

package mairpc.ec.wework_license;

option go_package = "wework_license";

import "common/request/request.proto";
import "common/response/response.proto";
import "ec/weworkLicense/wework_license.proto";

service WeworkLicenseService {
  // 激活导购企业微信许可
  rpc ActivateWeworkLicense(mairpc.common.request.IdListRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 续期导购企业微信许可
  rpc RenewalWeworkLicense(RenewalWeworkLicenseRequest) returns (RenewalWeworkLicenseResponse);
  // 获取续期结果
  rpc GetRenewalWeworkLicenseResult(GetRenewalWeworkLicenseResultRequest) returns (GetRenewalWeworkLicenseResultResponse);
  // 定时获取待支付续期订单结果
  rpc GetRenewalWeworkLicenseResultJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 继承导购企业微信许可
  rpc TransferWeworkLicense(TransferWeworkLicenseRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 导购激活、续期、继承记录列表
  rpc ListWeworkLicenseLogs(ListWeworkLicenseLogsRequest) returns (ListWeworkLicenseLogsResponse);
  // 获取待激活导购、可激活企业微信许可数量
  rpc GetWeworkLicenseCount(mairpc.common.request.EmptyRequest) returns (GetWeworkLicenseCountResponse);
  // 导购企业微信许可状态列表
  rpc ListWeworkLicenses(ListWeworkLicensesRequest) returns (ListWeworkLicensesResponse);
  // 企业微信许可系统消息提醒
  rpc UpdateWeworkLicenseMessageJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 校验企业微信许可状态
  rpc UpdateWeworkLicenseStatusJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建第三方应用/接口许可到期提醒
  rpc UpsertWechatworkExpirationReminderJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 分享导购企业微信许可
  rpc ShareWeworkLicense(ShareWeworkLicenseRequest) returns (mairpc.common.response.AsyncCacheResponse);
}
