syntax = "proto3";

package mairpc.ec.wework_license;

option go_package = "wework_license";

import "common/request/request.proto";
import "common/types/types.proto";

message RenewalWeworkLicenseRequest {
  // @required
  //
  // 导购 ids
  repeated string staffIds = 1; // valid:"required,objectIdList"
  // @required
  //
  // 续期时间（月）
  uint64 renewalTime = 2; // valid:"required"
}

message RenewalWeworkLicenseResponse {
  // 续期订单任务 id
  string taskId = 1;
}

message GetRenewalWeworkLicenseResultRequest {
  // @required
  //
  // 续期订单任务 id
  string taskId = 1;
}

message GetRenewalWeworkLicenseResultResponse {
  // 续期订单状态 waitPay（待支付），paySuccess（已支付）， waitPayClosed（未支付，订单已关闭），failed（失败）
  string status = 1;
  // 成功数
  uint64 count = 2;
  // 失败数
  uint64 failedCount = 3;
  // 续期失败文件
  string failedUrl = 4;
}

message TransferWeworkLicenseRequest {
  // @required
  //
  // 导购 ids
  repeated string staffIds = 1; // valid:"required,objectIdList"
  // @required
  //
  // 继承导购 ids
  repeated string transferStaffIds = 2; // valid:"required,objectIdList"
  // 下游企业渠道 id
  string chainCorpChannelId = 3;
}

message ListWeworkLicenseLogsRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // 类型 activation（激活） renewal（续期） transfer（继承）
  string type = 2;
  // 导购名称/导购编号
  string searchKey = 3;
  // staff_name（导购名称或编号） transfer_staff_name（继承导购名称或编号）
  string searchKeyType = 4; // valid:"in(staff_name|transfer_staff_name)"
  // 筛选时间
  mairpc.common.types.StringDateRange dateRange = 5;
  // success (成功) failed（失败）
  string status = 6;
}

message ListWeworkLicenseLogsResponse {
  uint64 total = 1;
  repeated WeworkLicenseLogDetail items = 2;
}

message WeworkLicenseLogDetail {
  // 记录 id staffRecord._id
  string id = 1;
  // 导购 id
  string staffId = 2;
  // 导购名
  string name = 3;
  // 导购编号
  string staffNo = 4;
  // 操作人 id
  string operatorId = 5;
  // 操作人
  string operator = 6;
  // 邮箱
  string email = 7;
  // 结果 success（成功） failed（失败） unpaid(待支付)
  string status = 8;
  // 失败原因
  string failedReason = 9;
  // 有效期截止时间
  string endAt = 10;
  // 记录创建时间
  string createdAt = 11;
  // 类型 activation（激活） renewal（续期） transfer（继承）
  string type = 12;
  // 续期时间
  uint64 renewalTime = 13;
  // 继承导购 id
  string transferStaffId = 14;
  // 继承导购名
  string transferStaffName = 15;
  // 继承导购编号
  string transferStaffNo = 16;
}

message GetWeworkLicenseCountResponse {
  // 待激活导购数
  uint64 staffCount = 1;
  // 可激活数
  uint64 weworkLicenseCount = 2;
}

message ListWeworkLicensesRequest {
  // 导购 ids
  repeated string staffIds = 1; // valid:"objectIdList"
  // activated（已激活），inactivated（未激活），expired（已逾期），expiredSoon（30天内到期）
  string status = 2; // valid:"in(activated|inactivated|expired|expiredSoon)"
}

message ListWeworkLicensesResponse {
  repeated WeworkLicenseDetail items = 1;
}

message WeworkLicenseDetail {
  // 导购 id
  string staffId = 1;
  // activated（已激活）、expired（已逾期）、expiredSoon（30天内到期）
  string status = 2;
  // 有效期开始时间
  string startAt = 3;
  // 有效期结束时间
  string endAt = 4;
}

message ShareWeworkLicenseRequest {
  // @required
  //
  // 下游企业渠道 id
  string channelId = 1; // valid:"required"
  // @required
  //
  // 分享数量
  int64 count = 2; // valid:"required"
}
