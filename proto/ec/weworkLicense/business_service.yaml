type: google.api.Service
config_version: 3

http:
  rules:
    # 激活导购企业微信许可
    - selector: mairpc.ec.wework_license.WeworkLicenseService.ActivateWeworkLicense
      post: /v2/ec/weworkLicense/activate
      body: '*'
      filterMark: backend
      tags: ['导购企微版-企业微信许可']
    # 续期导购企业微信许可
    - selector: mairpc.ec.wework_license.WeworkLicenseService.RenewalWeworkLicense
      post: /v2/ec/weworkLicense/renewal
      body: '*'
      filterMark: backend
      tags: ['导购企微版-企业微信许可']
    - selector: mairpc.ec.wework_license.WeworkLicenseService.GetRenewalWeworkLicenseResult
      get: /v2/ec/weworkLicense/renewalResult
      filterMark: backend
      tags: ['导购企微版-企业微信许可']
    # 继承导购企业微信许可
    - selector: mairpc.ec.wework_license.WeworkLicenseService.TransferWeworkLicense
      post: /v2/ec/weworkLicense/transfer
      body: '*'
      filterMark: backend
      tags: ['导购企微版-企业微信许可']
    - selector: mairpc.ec.wework_license.WeworkLicenseService.ListWeworkLicenseLogs
      get: /v2/ec/weworkLicenseLogs
      filterMark: backend
      tags: ['导购企微版-企业微信许可']
    - selector: mairpc.ec.wework_license.WeworkLicenseService.GetWeworkLicenseCount
      get: /v2/ec/weworkLicense/count
      filterMark: backend
      tags: ['导购企微版-企业微信许可']
    - selector: mairpc.ec.wework_license.WeworkLicenseService.ListWeworkLicenses
      get: /v2/ec/weworkLicenses
      additional_bindings:
        - post: /v2/ec/weworkLicenses/search
          body: '*'
      filterMark: backend
      tags: ['导购企微版-企业微信许可']
    - selector: mairpc.ec.wework_license.WeworkLicenseService.ShareWeworkLicense
      post: /v2/ec/weworkLicense/share
      body: '*'
      filterMark: backend
      tags: ['导购企微版-企业微信许可']