syntax = "proto3";

package mairpc.ec.logistic;

option go_package = "logistic";

import "common/ec/ec.proto";
import "common/response/response.proto";

message IdentifyDeliveryCompanyRequest {
  // @required
  //
  // 运单号
  string waybillId = 1; // valid:"required"
}

message IdentifyDeliveryCompanyResponse {
  // 运单号
  string waybillId = 1;
  // 物流公司 ID
  string deliveryId = 2;
  // 物流公司名称
  string deliveryName = 3;
}

message ListDeliveriesResponse {
  // 物流公司列表
  repeated DeliveryCom items = 1;
}

message DeliveryCom {
  // 物流公司 ID
  string id = 1;
  // 物流公司名称
  string name = 2;
}

message CreateDeliveryFeeTemplateRequest {
  // 模板类型
  //
  // 配送方式为快递时，此字段必填
  string type = 1; // valid:"in(piece|weight)"
  // 模板名称
  string name = 2;
  // 计费规则
  repeated DeliveryFeeTemplateRule rules = 3;
  // 是否指定条件包邮
  bool conditionFree = 4;
  // 指定条件包邮规则
  repeated DeliveryFeeTemplateFreeRule freeRules = 5;
  // 默认规则，使用地区可不填
  //
  // 配送方式为快递时，此字段必填
  DeliveryFeeTemplateRule defaultRule = 6;
  // 配送方式 快递（express）、同城配送（cityExpress）
  string method = 7; //valid:"in(express|cityExpress)"
  // 运费模式
  //
  // area (区域运费模板)、store（门店独立规则）
  string deliveryFeeMode = 8; // valid:"in(area|store)"
  // 含包邮商品时，是否整单包邮
  bool orderFreeWithFreeProduct = 9;
}

message UpdateDeliveryFeeTemplateRequest {
  // 模板类型
  //
  // 配送方式为快递时，此字段必填
  string type = 1; // valid:"in(piece|weight|distance)"
  // 模板名称
  string name = 2;
  // 计费规则
  repeated DeliveryFeeTemplateRule rules = 3;
  // 是否指定条件包邮
  bool conditionFree = 4;
  // 指定条件包邮规则
  repeated DeliveryFeeTemplateFreeRule freeRules = 5;
  // 默认规则，使用地区可不填
  //
  // 配送方式为快递时，此字段必填
  DeliveryFeeTemplateRule defaultRule = 6;
  // 配送方式 快递（express）、同城配送（cityExpress）
  string method = 8; //valid:"in(express|cityExpress)"
  // 金额类型
  //
  // 订单金额（totalAmount）、实付金额（payAmount）
  string amountType = 9; // valid:"in(totalAmount|payAmount)"
  // 运费模式
  //
  // area (区域运费模板)、store（门店独立规则）
  string deliveryFeeMode = 10; // valid:"in(area|store)"
  // 含包邮商品时，是否整单包邮
  bool orderFreeWithFreeProduct = 11;
  // 是否开启运费险
  bool isFreightInsuranceEnabled = 12;
}

message CalculateDeliveryFeeRequest {
  // 商品价格(单位: 分)
  uint64 price = 1;
  // 商品件数
  uint64 pieces = 2;
  // 商品重量(单位: 克)
  uint64 weight = 3;
  // 寄送地址
  //
  // 按照 "省:市:区/县" 的格式组装的字符串，如果不传或未精确到区/县则使用默认规则计算运费
  string address = 4;
  // 配送方式 快递（express）、同城配送（cityExpress）
  string method = 8; //valid:"in(express|cityExpress)"
  // 支付金额
  uint64 payAmount = 9;
  // 配送起点
  mairpc.common.response.Coordinate fromCoordinate = 10;
  // 配送终点
  mairpc.common.response.Coordinate toCoordinate = 11;
  // 门店运费模板
  common.ec.DeliveryFeeTemplate storeDeliveryFeeTemplate = 12;
  // 订单是否包含免邮商品
  bool hasFreeProduct = 13;
}

message CalculateDeliveryFeeResponse {
  // 运费计算结果(单位: 分)
  uint64 deliveryFee = 1;
}

message DeliveryFeeTemplateDetail {
  string id = 1;
  string type = 2;
  string createdAt = 3;
  string updatedAt = 4;
  DeliveryFeeTemplateRule defaultRule = 5;
  repeated DeliveryFeeTemplateRule rules = 6;
  bool conditionFree = 7;
  repeated DeliveryFeeTemplateFreeRule freeRules = 8;
  string amountType = 9;
  string deliveryFeeMode = 10;
  bool orderFreeWithFreeProduct = 11;
  bool isFreightInsuranceEnabled = 12;
}

message DeliveryFeeTemplateRule {
  // 首重(件)价格(单位: 分)，商品首重(件)计数内按首重(件)价格计费
  uint64 firstFee = 1;
  // 续重(件)价格(单位: 分)，即每续重(件)xx，增加运费xx分，大于0的数字
  uint64 additionalFee = 2;
  // 首重(件)数，当 type 为 weight 时单位为克
  uint64 firstAmount = 3;
  // 续重(件)数，当 type 为 weight 时单位为克
  uint64 additionalAmount = 4;
  // 模板使用地区，按照 "省:市:区/县" 的格式组装的字符串列表
  repeated string areas = 5;
}

message DeliveryFeeTemplateFreeRule {
  // @required
  //
  // 规则类型，amount 指定金额，weight 指定重量，piece 指定件数，distance 指定距离，amountAndWeight 金额 + 重量，amountAndPiece 金额 + 件数，amountAndDistance 金额 + 距离
  string type = 1; // valid:"required,in(amount|weight|piece|distance|amountAndWeight|amountAndPiece|amountAndDistance)"
  // 金额(单位: 分)
  uint64 amount = 2;
  // 重量(单位: 克)
  uint64 weight = 3;
  // 件数
  uint64 pieces = 4;
  // 地区
  repeated string areas = 5;
  // 距离（单位：米）
  uint64 distance = 6;
}

message GetDeliveryFeeTemplateRequest {
  // 配送方式 快递（express）、同城配送（cityExpress）
  string method = 1; //valid:"in(express|cityExpress)"
}

message GetExpressPathRequest {
  // @required
  //
  // 物流公司代码
  //
  // 如 SF(顺丰速运)，ZTO(中通快递)
  string deliveryId = 1; // valid:"required"
  // @required
  //
  // 快递单号
  string waybillId = 2; // valid:"required"
  // 收货人手机号
  //
  // 当物流公司为 SF 时，phone 必传
  string phone = 3;
}

message ExpressPackage {
  // 物流公司 ID
  string deliveryId = 1;
  // 物流公司 name
  string deliveryName = 2;
  // 运单 ID
  string waybillId = 3;
  // 运单状态
  //
  // 状态值： shipped: 待揽件, taken: 已揽件, shipping: 在途中, signed: 已签收, failed: 问题件，delivering: 派送中（自行配送时使用）
  string status = 4;
  // 物流轨迹
  repeated ExpressTrace traces = 5;
}

message ExpressTrace {
  // 描述
  string description = 1;
  // 创建时间
  string createdAt = 2;
  // 物流状态
  //
  // 状态值： shipped: 待揽件, taken: 已揽件, shipping: 在途中, signed: 已签收, failed: 问题件
  string status = 3;
}

message GetDeliveryRequest {
  // 物流公司 ID
  string deliveryId = 1;
  // 物流公司名称
  string deliveryName = 2;
}
