type: google.api.Service
config_version: 3

http:
  rules:
    # 新增分销者
    - selector: mairpc.ec.distribution.DistributionService.CreatePromoter
      post: /v2/ec/promoters
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
    - selector: mairpc.ec.distribution.DistributionService.CreateSubPromoters
      post: /v2/ec/subPromoters
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
    # 更新分销者
    - selector: mairpc.ec.distribution.DistributionService.UpdatePromoter
      put: /v2/ec/promoters/{id}
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
    # 删除审核失败分销员
    - selector: mairpc.ec.distribution.DistributionService.DeletePromoter
      delete: /v2/ec/promoter/{id}
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
    - selector: mairpc.ec.distribution.DistributionService.LogoffPromoter
      delete: /v2/ec/promoters/{memberId}
      body: '*'
      filterMark: backend
      tags: ['测试']
    # 分销者列表
    - selector: mairpc.ec.distribution.DistributionService.ListPromoters
      post: /v2/ec/promoters/list
      sensitiveFields: 'items.idcard,items.name,items.parentName,items.phone,items.staffNo'
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-分销']
      permission: 'ec:promoter:list'
    # 分销者列表
    - selector: mairpc.ec.distribution.DistributionService.ListBriefPromoters
      post: /v2/ec/briefPromoters/list
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
    # 分销者详情
    - selector: mairpc.ec.distribution.DistributionService.GetPromoter
      get: /v2/ec/promoter
      filterMark: backend,staff
      scope: ['staff']
      sensitiveFields: 'name,phone,parentName'
      tags: ['私域商城-分销']
    # 导出分销者
    - selector: mairpc.ec.distribution.DistributionService.ExportPromoters
      post: /v2/ec/promoters/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
      permission: 'ec:promoter:export'
    # 绑定分销客户
    - selector: mairpc.ec.distribution.DistributionService.BindPromoterMember
      post: /v2/ec/promoterMembers
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
    # 分销客户列表
    - selector: mairpc.ec.distribution.DistributionService.ListPromoterMembers
      get: /v2/ec/promoterMembers
      filterMark: backend
      sensitiveFields: 'items.name,items.phone'
      tags: ['私域商城-分销']
    # 分销商品列表
    - selector: mairpc.ec.distribution.DistributionService.ListDistributionProducts
      get: /v2/ec/distributionProducts
      filterMark: backend
      tags: ['私域商城-分销']
    # 更新大众分销商品
    - selector: mairpc.ec.distribution.DistributionService.UpdateDistributionProducts
      put: /v2/ec/distributionProducts
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
    # 更新大众分销商品
    - selector: mairpc.ec.distribution.DistributionService.UpdateDistributionProductsWithSearch
      post: /v2/ec/distributionProducts/updateBySearch
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
    # 更新导购分销商品
    - selector: mairpc.ec.distribution.DistributionService.UpdateStaffDistributionProducts
      put: /v2/ec/staffDistributionProducts
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
    # 分销订单列表
    - selector: mairpc.ec.distribution.DistributionService.ListDistributionOrders
      post: /v2/ec/distributionOrders
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-分销']
      sensitiveFields: 'items.name,items.phone,items.contact.name,items.contact.phone,items.distribution.name,items.distribution.phone,items.distribution.parentPromoterName,items.distribution.parentPromoterPhone,items.distribution.staffNo'
      permission: 'ec:distributionOrder:list'
    - selector: mairpc.ec.distribution.DistributionService.ListDistributionOrdersByTransferBillId
      post: /v2/ec/distributionOrdersByTransferBill
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-分销']
      permission: 'ec:distributionOrder:list'
    - selector: mairpc.ec.distribution.DistributionService.CountDistributionOrders
      post: /v2/ec/distributionOrders/count
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
      permission: 'ec:distributionOrder:list'
    # 分销订单导出
    - selector: mairpc.ec.distribution.DistributionService.ExportDistributionOrders
      post: /v2/ec/distributionOrders/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
      permission: 'ec:distributionOrder:export'
    # 分销记录列表
    - selector: mairpc.ec.distribution.DistributionService.ListDistributionRecords
      get: /v2/ec/distributionRecords
      filterMark: backend
      tags: ['私域商城-分销']
      sensitiveFields: 'items.name,items.promoterPhone'
      permission: 'ec:distributionRecords:list'
    - selector: mairpc.ec.distribution.DistributionService.CountDistributionRecords
      post: /v2/ec/distributionRecords/count
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
      permission: 'ec:distributionRecords:list'
    - selector: mairpc.ec.distribution.DistributionService.RetryDistributionRecord
      post: /v2/ec/distributionRecords/retry
      filterMark: backend
      body: '*'
      tags: ['私域商城-分销']
    # 分销设置
    - selector: mairpc.ec.distribution.DistributionService.GetDistributionSetting
      get: /v2/ec/distributionSetting
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-分销']
    # 更新分销设置
    - selector: mairpc.ec.distribution.DistributionService.UpdateDistributionSetting
      put: /v2/ec/distributionSetting
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
      permission: 'ec:distributionSetting:update'
    # 分销员客户导出
    - selector: mairpc.ec.distribution.DistributionService.ExportPromoterMember
      post: /v2/ec/promoterMembers/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
    # 分销记录导出
    - selector: mairpc.ec.distribution.DistributionService.ExportDistributionRecord
      post: /v2/ec/distRecords/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
      permission: 'ec:distributionRecords:export'
    # 导购分销分析数据导出
    - selector: mairpc.ec.distribution.DistributionService.ExportDataAnalyses
      post: /v2/ec/dataAnalyses/export
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
    # 获取登录用户所在权限下的所有的分销员的 ids
    - selector: mairpc.ec.distribution.DistributionService.GetPromoterIds
      get: /v2/ec/promoters/ids
      filterMark: backend
      tags: ['私域商城-分销']
    - selector: mairpc.ec.distribution.DistributionService.GetStaffDistributionOverviewStats
      get: /v2/ec/staffDistributionOverviewStats
      filterMark: backend
      tags: ['通用-工作台']
    # 批量更新分销者状态
    - selector: mairpc.ec.distribution.DistributionService.BatchUpdatePromoterStatus
      post: /v2/ec/promoters/batchUpdateStatus
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
      # 给企微导购创建默认分销员
    - selector: mairpc.ec.distribution.DistributionService.CreateDefaultStaffPromoters
      post: /v2/ec/staffPromoters
      body: '*'
      filterMark: backend
      tags: ['私域商城-分销']
      # 修复导购分销员账号
    - selector: mairpc.ec.distribution.DistributionService.RepairStaffPromoter
      post: /v2/ec/promoters/repairStaffPromoter
      body: '*'
      filterMark: backend,staff
      scope: ['staff']
      tags: ['私域商城-分销']
      # 获取导购绑定的分销员统计
    - selector: mairpc.ec.distribution.DistributionService.GetMemberBoundStaffStats
      get: /v2/ec/memberBoundStaffStats
      filterMark: backend,staff
      scope: ['staff']
      tags: ['导购企微版-大众分销']
      # 获取大众分销员绑定的导购的业绩列表
    - selector: mairpc.ec.distribution.DistributionService.ListMemberBoundStaffPerformance
      get: /v2/ec/memberBoundStaff/performanceList
      filterMark: backend
      tags: ['导购企微版-大众分销']
      sensitiveFields: 'items.staffName,items.staffPhone'
      permission: 'ec:promoter:list'
    - selector: mairpc.ec.distribution.DistributionService.ExportMemberBoundStaffPerformance
      post: /v2/ec/memberBoundStaff/exportPerformance
      body: '*'
      filterMark: backend
      tags: ['导购企微版-大众分销']
    - selector: mairpc.ec.distribution.DistributionService.UpdatePromoterCountJob
      post: /v2/ec/promoters/updateCount
      filterMark: backend
      tags: ['测试']
    - selector: mairpc.ec.distribution.DistributionService.GetPromoterSensitive
      get: /v2/ec/promoters/{promoterId}/sensitive
      filterMark: backend
      tags: ['导购企微版-大众分销']
    - selector: mairpc.ec.distribution.DistributionService.GetPromoterMember
      get: /v2/ec/promoters/promoterMember/{memberId}
      filterMark: backend,staff
      scope: ['staff']
      tags: ['导购企微版-大众分销']
    - selector: mairpc.ec.distribution.DistributionService.ExecXinHangJiaPaymentJob
      get: /v2/ec/xinHangJiaSign/payment
      filterMark: backend
      tags: ['导购企微版-大众分销']
    - selector: mairpc.ec.distribution.DistributionService.TransBoundPromoterMembers
      post: /v2/ec/transPromoterMembers
      body: '*'
      filterMark: backend
      tags: ['导购企微版-大众分销']
    - selector: mairpc.ec.distribution.DistributionService.CreatePromoterFromPortal
      post: /v2/ec/createPromotersFromPortal
      body: '*'
      filterMark: backend
      tags: ['导购企微版-大众分销']
    - selector: mairpc.ec.distribution.DistributionService.ExportPromoterPosterWithJob
      post: /v2/ec/promoter/poster/export
      body: '*'
      filterMark: backend
      tags: ['导购企微版-大众分销']
    - selector: mairpc.ec.distribution.DistributionService.ExportPromoterPosterWithAsyncCache
      post: /v2/ec/promoter/poster/exportWithAsyncCache
      body: '*'
      filterMark: backend
      tags: ['导购企微版-大众分销']
    - selector: mairpc.ec.distribution.DistributionService.BindPromoterMemberForPromoterSelf
      post: /v2/ec/test/bindPromoterMemberForPromoterSelf
      body: '*'
      filterMark: backend
      tags: ['测试']
    - selector: mairpc.ec.distribution.DistributionService.UnbindPromoterMemberForPromoterSelf
      post: /v2/ec/test/unbindPromoterMemberForPromoterSelf
      body: '*'
      filterMark: backend
      tags: ['测试']
    # 获取分销员业绩统计
    - selector: mairpc.ec.distribution.DistributionService.ListPromoterStats
      get: /v2/ec/promoterStats
      tags: ['私域商城-分销']
