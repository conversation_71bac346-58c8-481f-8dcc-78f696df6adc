syntax = "proto3";

package mairpc.ec.distribution;

option go_package = "distribution";

import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/types/types.proto";
import "ec/product/product.proto";
import "ec/profitsharing/transferBill.proto";
import "member/member.proto";
import "product/product.proto";

message CreatePromoterRequest {
  // @required
  //
  // 分销员类型：大众分销员（member）、导购分销员（staff）
  string type = 1; // valid:"required"
  // @required
  string memberId = 2; // valid:"required,objectId"
  // 导购分销员需传此参数
  string staffId = 3;
  // @required
  //
  // 分销员实名
  string name = 4; // valid:"required"
  // 大众分销员需要验证手机号
  string phone = 5;
  string avatar = 6;
  // @required
  string openId = 7; // valid:"required"
  // 验证码
  //
  // 需要验证手机号时传此参数
  string verificationCode = 8;
  // 是否重新注册
  bool isReRegister = 9;
}

message UpdatePromoterRequest {
  // 分销者 Id
  string id = 1; // valid:"objectId"
  // 分销者姓名
  string name = 2;
  // 分销者手机号
  string phone = 3;
  // 是否冻结
  mairpc.common.types.BoolValue frozen = 4;
  // 分销员的 memberId
  string memberId = 5; // valid:"objectId"
  // 导购分销员的 staffId
  string staffId = 6; // valid:"objectId"
  // 状态
  //
  // 通过（approved）、拒绝（unapproved）
  string status = 7; // valid:"in(approved|unapproved)"
  // 微信商户接收方
  //
  // 当需要更新分账接收方类型为商户接收方时传此参数，仅当大众分销员当前分账接收方为微信用户时此参数有效
  MerchantInfo merchant = 8; // valid:"optional"
  // 更新大众分销员客户所属导购Id
  bool updateMemberBoundStaffId = 9;
  // 是否禁止拉新
  mairpc.common.types.BoolValue isNewUserDisallowed = 10;
  // 冻结时间
  string frozenAt = 11;
  // 冻结备注
  string frozenRemark = 12;
  // 负责区域
  PromoterAreas areas = 14;
  // 冻结时是否冻结二级分销员
  bool frozeSubPromoters = 15;
  // 冻结时如果冻结二级分销员，二级分销员的客户转移至的分销员 id
  string transSubPromoterMembersTo = 16;
  // 冻结时如果不冻结二级分销员，将二级分销员转移到的一级分销员 id
  string transSubPromotersTo = 17;
  // 冻结时绑定的客户转移到的分销员 id
  string transPromoterMembersTo = 18;
  // 冻结时释放绑定客户
  bool releaseMembersAfterFrozen = 19;
  // 导出释放客户明细
  bool exportReleasedMembers = 20;
  // 是否允许邀请注册绑定
  mairpc.common.types.BoolValue isBindMemberByActivationAllowed = 21;
}

message PromoterAreas {
  repeated string value = 1;
}

message UpdatePromoterPhoneRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid"required,objectId"
  // @required
  //
  // 手机号
  string phone = 2; // valid:"required"
  // @required
  //
  // 验证码
  string captcha = 3; // valid:"required"
}

message MerchantInfo {
  // 商户全称
  string merchantName = 1; // valid:"required"
  // 商户号
  string merchantId = 2; // valid:"required"
}

message BatchUpdatePromoterStatusRequest {
  // 分销者 ids
  repeated string ids = 1; // valid:"objectIdList"
  // 状态
  //
  // 通过（approved）、拒绝（unapproved）
  string status = 2; // valid:"required,in(approved|unapproved)"
}

message ListPromotersRequest {
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 1;
  // 搜索关键词（支持姓名和手机号）
  string searchKey = 2;
  // 加入时间
  mairpc.common.types.DateRange createdTime = 3;
  // 累计收益
  //
  // 单位：分
  mairpc.common.types.IntegerRange totalProfitAmount = 4;
  // 累计客户数
  //
  // 单位：个
  mairpc.common.types.IntegerRange totalMembers = 5;
  // 分销员状态
  mairpc.common.types.BoolValue frozen = 6;
  // @required
  //
  // 分销员类型
  // 大众分销员（member）、导购分销员（staff）
  string type = 7; // valid:"required,in(member|staff)"
  // 经销商 Ids
  //
  // 根据经销商 Id 筛选，多级经销商只用传最后一级
  repeated string storeIds = 8;
  // 累计推广金额
  //
  // 单位：分
  mairpc.common.types.IntegerRange totalAmount = 9;
  // 分销员 staffId
  repeated string staffIds = 10;
  // 大众分销员状态
  //
  // pending（待审核）、approved（审核通过）、unapproved（拒绝通过）
  repeated string status = 11;
  // 审核人 ID
  string approverId = 12; // valid:"objectId"
  // 审核时间
  mairpc.common.types.DateRange approvedTime = 13;
  // 排除状态
  repeated string excludeStatus = 14;
  // 是否包含已删除的
  bool containDeleted = 15;
  // 分销员对应的客户 Id 列表
  repeated string memberIds = 16;
  // 分销员对应客户的绑定导购 Id
  string memberBoundStaffId = 17; // valid:"objectId"
  // 是否禁止拉新
  mairpc.common.types.BoolValue isNewUserDisallowed = 18;
  // 是否已签约
  mairpc.common.types.BoolValue isSigned = 19;
  // 负责区域，${省}:${市} 或者 ${省}
  string area = 20;
  // 是否是二级分销员
  mairpc.common.types.BoolValue isSubPromoter = 21;
  // 上级分销员 id
  string parentId = 22; // valid:"objectId"
  // 控制返回字段
  //
  // subPromoterCount：二级分销员数、selfBound：是否已和自身绑定
  repeated string extraFields = 23;
  // 当前客户 id，小程序用
  string memberId = 24;
  // 当查询当前分销员的二级分销员时，是否返回当前分销员自己
  bool returnSelfPromoter = 25;
  repeated string ids = 26; // valid:"objectIdList"
}

message ListPromotersResponse {
  // 数量
  uint64 total = 1;
  // 列表
  repeated PromoterDetail items = 2;
}

message PromoterDetail {
  string id = 1;
  string accountId = 2;
  // 分销员类型
  //
  // 大众分销员（member）、导购分销员（staff）
  string type = 3;
  string staffId = 4;
  string memberId = 5;
  // 姓名
  string name = 6;
  // 手机号
  string phone = 7;
  // 头像
  string avatar = 8;
  // openId
  string openId = 9;
  // 累计推广订单数（包含有佣金和无佣金）
  uint64 totalOrders = 10;
  // 累计推广订单数（有分销商品的分销订单）
  uint64 totalCommissionOrders = 11;
  // 累计推广金额
  uint64 totalAmount = 12;
  // 累计客户数
  uint64 totalMembers = 13;
  // 总收益包含待结算
  uint64 totalProfitAmount = 14;
  // 待结算收益
  uint64 waitProfitAmount = 15;
  // 分销员状态
  bool frozen = 16;
  // 加入时间
  string createdAt = 17;
  // 导购工号
  //
  // 导购分销员才有此字段
  string staffNo = 18;
  // 分销员数据统计
  PromoterCount count = 19;
  // 导购分销员所属经销商
  repeated StaffPromoterStores stores = 20;
  // 身份证
  string idcard = 21;
  // 大众分销员状态
  //
  // 通过（approved）、拒绝（unapproved）、pending（进行中）
  string status = 22;
  // 审核人名称
  string approverName = 23;
  // 审核人 ID
  string approverId = 24;
  // 审核时间
  string approvedAt = 25;
  // 累计推广商品数
  uint64 totalProducts = 26;
  // 分账接收方类型
  string receiverType = 27;
  // 商铺信息
  //
  // 仅当 receiverType 为 MERCHANT_ID 时有效
  MerchantInfo merchant = 28;
  // 大众分销员客户绑定导购信息
  MemberBoundStaffDetail memberBoundStaff = 29;
  // 推广总金额
  uint64 totalPromoteAmount = 30;
  // 与分销员绑定中的客户数
  uint64 bindingMembers = 31;
  // 分销员数据统计汇总
  PromoterCountSummary countSummary = 32;
  // 分账接收方信息
  ReceiverInfo receiver = 33;
  // 是否禁止拉新
  bool isNewUserDisallowed = 34;
  // 分销员客户属性
  repeated mairpc.member.PropertyDetail properties = 35;
  // 冻结时间
  string frozenAt = 36;
  // 冻结备注
  string frozenRemark = 37;
  // 签约状态
  //
  // pending 签约中，failed 签约失败，success 签约成功，为空时未签约
  string signStatus = 38;
  // 签约失败原因
  string signErrMsg = 43;
  // 负责区域
  repeated string areas = 44;
  // 上级分销员 id
  string parentId = 45;
  // 二级分销员数
  uint64 subPromoterCount = 46;
  // 二级分销员绑定客户数
  uint64 subPromoterTotalMembers = 47;
  // 上级分销员姓名
  string parentName = 48;
  // 上级分销员 id 历史
  repeated string historyParentIds = 49;
  // 是否已经和自己绑定
  bool selfBound = 50;
  // 是否允许邀请注册绑定
  bool isBindMemberByActivationAllowed = 51;
  // 下级分销员统计数据
  Statistics subPromoterStatistics = 52;
}

message ReceiverInfo {
  // 分账接收方 id
  //
  // 如果此字段为空则说明未创建分账接收方
  string id = 1;
  // 微信分账接收方创建错误信息
  string wechatReceiverError = 2;
}

message MemberBoundStaffDetail {
  // 导购 Id
  string staffId = 1;
  // 导购名称
  string staffName = 2;
  // 绑定时间
  string boundAt = 3;
}

message StaffPromoterStores {
  // 经销商 Id
  string id = 1;
  // 经销商名称
  string name = 2;
  // 门店编号
  string code = 3;
}

message PromoterCount {
  map<string, Statistics> countDetail = 1;
}

message Statistics {
  // 发展客户数
  uint64 member = 1;
  // 完成订单数
  uint64 order = 2;
  // 销售额
  uint64 totalAmount = 3;
  // 退款订单数
  uint64 refundOrder = 4;
  // 累计收益
  uint64 amount = 5;
  // 税后收入
  uint64 income = 6;
  // 扣除个税
  uint64 tax = 7;
  // 是否已结算，用于导购分销月结
  bool hasProfited = 8;
  // 推广金额
  uint64 promoteAmount = 9;
  // 分销商品销量
  uint64 product = 10;
  // 推广订单数
  uint64 promoteOrder = 11;
  // 绑定中的客户数
  uint64 bindingMembers = 12;
  // 分销商品销售额
  uint64 distProductTotalAmount = 13;
  // 分销商品推广金额
  uint64 distProductPromoteAmount = 14;
}

message PromoterCountSummary {
  // 累计销售额
  uint64 totalAmount = 1;
  // 本月销售额
  uint64 thisMonthTotalAmount = 2;
  // 上月销售额
  uint64 lastMonthTotalAmount = 3;
  // 月均销售额
  uint64 monthlyAvgTotalAmount = 4;
  // 累计推广金额
  uint64 totalPromoteAmount = 5;
  // 本月推广金额
  uint64 thisMonthPromoteAmount = 6;
  // 上月推广金额
  uint64 lastMonthPromoteAmount = 7;
  // 月均推广金额
  uint64 monthlyAvgPromoteAmount = 8;
  // 累计订单数
  uint64 totalOrders = 9;
  // 本月订单数
  uint64 thisMonthOrders = 10;
  // 上月订单数
  uint64 lastMonthOrders = 11;
  // 月均订单数
  uint64 monthlyAvgOrders = 12;
  // 累计分销商品销量
  uint64 totalProducts = 13;
  // 本月分销商品销量
  uint64 thisMonthProducts = 14;
  // 上月分销商品销量
  uint64 lastMonthProducts = 15;
  // 月均分销商品销量
  uint64 monthlyAvgProducts = 16;
  // 累计推广订单数
  uint64 totalPromoteOrders = 17;
  // 本月分销商品销量
  uint64 thisMonthPromoteOrders = 18;
  // 上月分销商品销量
  uint64 lastMonthPromoteOrders = 19;
  // 月均分销商品销量
  uint64 monthlyAvgPromoteOrders = 20;
  // 当前绑定客户数
  uint64 boundMembers = 21;
  // 本月绑定客户数
  //
  // 本月与客户绑定的次数，相同月份内对客户去重
  uint64 thisMonthBoundMembers = 22;
  // 上月绑定客户数
  //
  // 上月与客户绑定的次数，相同月份内对客户去重
  uint64 lastMonthBoundMembers = 23;
  // 本月客户流失数
  //
  // 本月与分销员解绑的客户数，相同月份内对客户去重
  uint64 thisMonthLostMembers = 24;
  // 上月客户流失数
  //
  // 上月与分销员解绑的客户数，相同月份内对客户去重
  uint64 lastMonthLostMembers = 25;
  // 上月和分销员绑定的客户实时计数，截止上月结束
  uint64 lastMonthBindingMembers = 26;
  // 累计分销商品销售额
  uint64 distProductTotalAmount = 27;
  // 累计分销商品推广金额
  uint64 distProductPromoteAmount = 28;
}

message BindPromoterMemberRequest {
  // @required
  //
  // 分销者的 memberId
  string promoterMemberId = 1; // valid:"required,objectId"
  // @required
  //
  // 客户的 memberId
  string memberId = 3; // valid:"required,objectId"
  // 经销商 Id
  //
  // 绑定导购分销的场景需传此参数
  string storeId = 4; // valid:"objectId"
  // 是否是绑定到自己
  bool isBindSelf = 5;
}

message ListPromoterMembersRequest {
  // 分销者 Id
  string promoterId = 1; // valid:"objectId"
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 2;
  // 搜索关键词（支持姓名和手机号）
  string searchKey = 3;
  // 绑定时间
  mairpc.common.types.DateRange createdTime = 4;
  // 客户绑定状态
  //
  // 绑定（bound）、解绑（unbound）
  string status = 5;
  // 分销者 memberId
  //
  // 小程序端使用此字段
  string memberId = 6;
  // @required
  //
  // 分销类型
  //
  // 大众分销设置（member），导购分销设置（staff）
  string settingType = 7; // valid:"required,in(member|staff)"
  // 经销商 Id
  string storeId = 8; // valid:"objectId"
  // 分销员 id 列表
  repeated string promoterIds = 9; // valid:"objectIdList"
  // 导购 id
  string staffId = 10; // valid:"objectId"
}

message ListPromoterMembersResponse {
  // 数量
  uint64 total = 1;
  // 列表
  repeated PromoterMemberDetail items = 2;
}

message PromoterMemberDetail {
  string id = 1;
  // 客户的 memberId
  string memberId = 2;
  // 客户姓名
  string name = 3;
  // 客户手机号
  string phone = 4;
  // 客户绑定状态
  string status = 5;
  // 是否永久有效
  bool permanent = 6;
  // 剩余有效期
  int32 expiryDate = 7;
  // 是否永久保护
  bool allowReplace = 8;
  // 剩余保护期
  int32 protectionExpiryDate = 9;
  // 最新绑定时间
  string boundAt = 10;
  // 解绑时间
  string unboundAt = 11;
  // 解绑原因
  string unboundReason = 12;
  // 累计下单数
  uint64 totalOrders = 13;
  // 累计消费金额
  uint64 totalAmount = 14;
  // 累计贡献奖金
  uint64 totalProfitAmount = 15;
  // 是否是分销员
  bool isPromoter = 16;
  // 头像
  string avatar = 17;
  // 最近下单时间
  string latestOrderAt = 18;
  // 首次绑定时间
  string createdAt = 19;
  // 分销员 id
  string promoterId = 20;
  // 绑定类型
  string promoterType = 21;
  // 分销员名称
  string promoterName = 22;
  // 是否是会员
  bool isMember = 23;
}

message ListDistributionProductsRequest {
  // 分页条件
  //
  // 排序字段支持：佣金，销量（distribution.sales），创建时间（createdAt）
  // 按佣金排序例如： [-distribution.proportion, -lowestPrice]
  mairpc.common.request.ListCondition listCondition = 1;
  // 搜索关键词（支持商品名、商品编号、商品条码）
  string searchKey = 2;
  // 佣金比例
  //
  // 单位：%
  mairpc.common.types.IntegerRange profitProportion = 3;
  // 商品价格
  //
  // 单位：分
  mairpc.common.types.IntegerRange productAmount = 4;
}

message ListDistributionProductsResponse {
  // 数量
  uint64 total = 1;
  // 列表
  repeated DistributionProductDetail items = 2;
}

message DistributionProductDetail {
  // 商品 Id (ec.product.id)
  string id = 1;
  // 商品名
  string name = 2;
  // 商品编号
  string number = 3;
  // 商品图片
  string picture = 4;
  // 商品价格
  uint64 price = 5;
  // 商品划线价
  uint64 originalPrice = 6;
  // 总销量
  uint64 totalSales = 7;
  // 佣金比例
  float profitProportion = 8;
  // 添加时间
  string createdAt = 9;
  // 商品状态
  string status = 10;
  // 商品佣金
  uint64 profitAmount = 11;
  // 分销员佣金设置
  repeated PromoterDistributionSetting promoterSettings = 12;
}

message UpdateDistributionProductsRequest {
  // @required
  //
  // 商品 Ids （ec.product.id）
  mairpc.common.request.IdListRequest ids = 1;
  // 是否使用默认分销比例
  mairpc.common.types.BoolValue isDefaultProportion = 2;
  // 佣金比例
  float profitProportion = 3;
  // 是否可分销
  mairpc.common.types.BoolValue enabled = 4;
  // @required
  //
  // 操作类型
  //
  // create、update
  string type = 5; // valid:"required"
  // @required
  //
  // 分销设置类型
  //
  // 大众分销设置（member），导购分销设置（staff）
  string settingType = 6; // valid:"required,in(member|staff)"
  // 分销员获取优惠券
  Coupon coupon = 7;
  // 分销模式
  //
  // defaultProportion: 默认佣金比例（对应 isDefaultProportion == true）
  // customProportion: 自定义佣金比例（对应 isDefaultProportion == false）
  // customAmount: 自定义佣金金额
  string distributionMode = 8; // valid:"in(defaultProportion|customProportion|customAmount)"
  // 分账金额（单位：分）
  uint64 profitAmount = 9;
  // 指定分销员设置
  repeated PromoterDistributionSetting promoterSettings = 10;
}

message PromoterDistributionSetting {
  // 分销员 id
  string promoterId = 1;
  // 分销员姓名
  string promoterName = 2;
  // 分账金额
  uint64 profitAmount = 3;
  // 佣金比例
  double profitProportion = 4;
}

message UpdateDistributionProductsWithSearchRequest {
  // 更新请求
  UpdateDistributionProductsRequest updateReq = 1;
  // 搜索商品请求
  mairpc.common.ec.ListProductsRequest searchReq = 2;
  // 排除的商品 ids
  repeated string excludeIds = 3;
}

message Coupon {
  // 优惠券 ID
  string id = 1;
  // 优惠券名称
  string name = 2;
}

message UpdateStaffDistributionProductsRequest {
  // @required
  //
  // 商品 Ids （ec.product.id）
  mairpc.common.request.IdListRequest ids = 1;
  // 每件商品佣金
  uint64 profitAmount = 2;
  // 是否可分销
  mairpc.common.types.BoolValue enabled = 3;
  // @required
  //
  // 操作类型
  //
  // create、update、remove
  string type = 4; // valid:"required,in(create|update|remove)"
  // 是否使用默认分佣金额
  mairpc.common.types.BoolValue isDefaultProfitAmount = 5;
  // 佣金模式
  //
  // 默认分佣金额（isDefaultProfitAmount 为 true 时为此模式）：defaultAmount
  // 默认分佣比例 （为此类型时 distributionMode 必不为空）：defaultProportion
  // 自定义分佣比例 （为此类型时 distributionMode 必不为空）：customProportion
  // 自定义金额（isDefaultProfitAmount 为 false 时为此类型）：customAmount
  // 按默认导购等级配置（为此类型时 distributionMode 必不为空）：defaultStaffLevels
  // 自定义导购等级配置（为此类型时 distributionMode 必不为空）：customStaffLevels
  string distributionMode = 6; // valid:"in(defaultAmount|defaultProportion|customProportion|customAmount|defaultStaffLevels|customStaffLevels)"
  // 更新导购等级佣金配置，不传则不更新
  UpdateStaffLevelDistributionSettingRequest levels = 7; // valid:"optional"
  // 每件商品佣金比例
  //
  // distributionMode 为 defaultProportion 或 customProportion 时传此字段
  double profitProportion = 8;
}

message UpdateStaffLevelDistributionSettingRequest {
  // 导购等级佣金配置列表
  repeated StaffLevelDistributionSetting levels = 6; // valid:"optional"
}

message StaffLevelDistributionSetting {
  // @required
  //
  // 分销佣金模式
  //
  // 自定义佣金金额（amount），自定义佣金比例（proportion）
  string distributionMode = 1; // valid:"required,in(amount|proportion)"
  // 佣金金额
  uint64 amount = 2;
  // @required
  //
  // 导购等级
  uint64 level = 3;
  // 佣金比例
  //
  // 当前仅导购分销商品自定义比例使用
  double proportion = 4;
}

message ListDistributionOrdersRequest {
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 1;
  // 搜索条件
  //
  // 按分销员搜索（promoter）、按订单搜索（order）、按下单人手机号/姓名搜索（member）、按联系人手机号/姓名搜索（contact）、parentPromoter（按一级分销员搜索）、subPromoter（按二级分销员搜索）
  string searchType = 2;
  // 搜索关键词
  //
  // 为 promoter 时支持姓名、手机号，为 order 时支持订单编号、订单商品
  string searchKey = 3;
  // 下单时间
  mairpc.common.types.DateRange createdTime = 4;
  // 佣金比例
  //
  // 单位：%
  mairpc.common.types.IntegerRange profitAmount = 5;
  // 结算状态
  //
  // 不传为全部状态、待结算（pending）、结算中（processing）、已结算（success）、无佣金（noCommission）、结算失败（failed）
  string status = 6;
  // 小程序传此参数作为 promoter 的 memberId
  string memberId = 7;
  // @required
  //
  // 分销订单类型
  // 大众分销员所产生的（member）、导购分销员所产生的（staff）
  string type = 8; // valid:"required,in(member|staff)"
  // 订单状态
  //
  // 导购分销订单查询的订单状态
  repeated string orderStatus = 9;
  // 订单配送方式
  string method = 10;
  // 所属经销商
  repeated string storeIds = 11;
  // 智慧导购企微小程序传此参数作为 promoter 的 staffId
  string staffId = 12;
  // 订单是否已支付
  mairpc.common.types.BoolValue isPaid = 13;
  // 零售公司 ids
  repeated string distributorIds = 14;
  // 筛选所属组织节点
  mairpc.common.types.DistributorSelector distributor = 15; // valid:"optional"
  // 分佣状态
  //
  // 待发放（pending, processing），已发放（success），无需发放（noCommission, refunded），发放失败（failed）
  repeated string distributionStatus = 16;
  // 智慧导购企微小程序传此参数作为 promoter 的 staffId
  repeated string staffIds = 17; // valid:"objectIdList"
  // 订单标签
  //
  // 注意这里的多个 tag 是 and 关系
  repeated string tags = 19;
  // 订单区域
  //
  // 多个区域使用json字符串
  string locations = 20;
  // 退款状态
  //
  // 可选值：refunded 已退款，noRefund
  string refundStatus = 33; // valid:"in(refunded|noRefund)"
  // 下单客户 id
  //
  // 一般用来在导购端查询绑定客户订单
  string orderMemberId = 34;
  // 是否不返回总记录数
  bool withoutTotal = 35;
  // 二级分销员 id 列表
  repeated string promoterIds = 36;
  // 排除的标签
  repeated string ignoreTags = 37;
  // 收益记录 id
  string transferBillId = 38;
}

message ListDistributionOrdersResponse {
  // 数量
  uint64 total = 1;
  // 列表
  repeated DistributionOrderDetail items = 2;
  // 累计收益
  //
  // 按照时间筛选的时候才返回
  uint64 totalProfitAmount = 3;
  // 待结算收益
  //
  // 按照时间筛选的时候才返回
  uint64 waitProfitAmount = 4;
  // 累计销售金额（不包含运费），只统计已支付订单，退款会减少
  uint64 totalShareAmount = 5;
  // 累计分销商品销售金额，只统计已支付订单，退款会减少
  uint64 distProductTotalAmount = 6;
}

message DistributionOrderDetail {
  // 订单 Id
  string id = 1;
  // 订单编号
  string number = 2;
  // 订单商品
  repeated DistributionOrderProductDetail products = 3;
  // 分销信息
  OrderDistribution distribution = 4;
  // 下单时间
  string createdAt = 5;
  // 订单状态
  string orderStatus = 6;
  // 订单配送方式
  string method = 7;
  // 订单运费
  uint64 logisticsFee = 8;
  // 下单客户名
  string name = 9;
  // 订单实付总额
  uint64 payAmount = 10;
  // 订单参与的活动
  repeated mairpc.common.ec.CampaignDetail campaigns = 11;
  // 礼品卡信息
  repeated mairpc.common.ec.PrepaidCardDetail prepaidCards = 12;
  // 订单标签
  repeated string tags = 13;
  // 门店 id
  string storeId = 14;
  // 门店名称
  string storeName = 15;
  // ec.order 订单上额外信息
  string extra = 16;
  // 下单人手机
  string phone = 17;
  // 下单人 id
  string memberId = 18;
  // 联系人
  OrderContact contact = 19;
}

message OrderContact {
  string name = 1;
  string phone = 2;
}

message OrderDistribution {
  // 分销员 Id
  string promoterId = 1;
  // 购买者 memberId
  string memberId = 2;
  // 分销员姓名
  string name = 3;
  // 分销员手机
  string phone = 4;
  // 此单分销总佣金
  uint64 amount = 5;
  // 发起结算时间
  string profitSharingAt = 6;
  // 结算状态
  string profitSharingStatus = 7;
  // 结算失败信息
  string profitFailedMsg = 8;
  // 客户姓名
  string customerName = 9;
  // 导购工号
  //
  // 导购分销员才有此字段
  string staffNo = 10;
  // 导购 Id
  string staffId = 11;
  // 导购冻结状态
  bool staffFrozen = 12;
  // 导购冻结时间
  int64 staffFrozenAt = 13;
  // 导购冻结备注
  string staffFrozenRemark = 14;
  // 上级分销员 id
  string parentPromoterId = 15;
  // 上级分销员 id
  string parentPromoterName = 16;
  // 上级分销员 id
  string parentPromoterPhone = 17;
  // 二级分销员 id
  string subPromoterId = 18;
}

message DistributionOrderProductDetail {
  // 商品 ID
  string id = 1;
  // 名称
  string name = 2;
  // 图片
  string picture = 3;
  // 数量
  uint64 total = 4;
  // 商品价格
  //
  // 单位：分
  uint64 price = 5;
  // 总价
  //
  // 单位：分
  uint64 totalAmount = 6;
  // 实际支付
  //
  // 单位：分
  uint64 payAmount = 7;
  // 商品退款状态
  string refundStatus = 8;
  // 商品规格
  SpecProdSku spec = 9;
  // 此商品佣金比例
  float distributionProportion = 10;
  // 此商品总佣金
  uint64 distributionAmount = 11;
  // 购买此商品分发给导购的优惠券
  Coupon coupon = 12;
  // 是否改价
  bool isAmountAdjusted = 13;
  // 礼品卡支付
  uint64 prepaidCardPayAmount = 14;
  // 分销佣金发放状态
  //
  // 用于前端展示商品项的分销状态
  // 未发放（pending, processing），已发放（success），发放失败（failed），无需发放/仅用于日结（noCommission）
  string distributionStatus = 15;
  // 分销状态说明
  //
  // 用于展示某分销状态对应的原因或说明
  string distributionMsg = 16;
  // 商品分销模式
  //
  // 默认佣金比例（defaultProportion），自定义佣金比例（customProportion），自定义佣金金额（customAmount）
  string distributionMode = 17;
  // 商品类型
  //
  // virtual（虚拟商品），coupon（付费卡券商品）,entity（实体商品）
  string type = 18;
}

message SpecProdSku {
  // SKU
  string sku = 1;
  // 规格图片
  string picture = 2;
  // 规格
  repeated string properties = 3;
}

message ListDistributionRecordsRequest {
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 1;
  // 搜索条件
  //
  // type: order（按订单编号搜索）、transaction（按交易单号搜索）、promoter（按分销员搜索）
  string searchType = 2;
  // 搜索关键词
  string searchKey = 3;
  // 发放时间/标记时间
  mairpc.common.types.DateRange sharedAt = 4;
  // @required
  //
  // 分佣记录类型
  // 大众分销员所产生的（member）、导购分销员所产生的（staff）
  string type = 5; // valid:"required,in(member|staff)"
  // 零售公司 ids
  repeated string distributorIds = 14;
  // 发放状态
  //
  // 未发放（pending, processing），已发放（success），发放失败（failed），无需发放/仅用于日结（noCommission）
  repeated string status = 15; // valid:"in(pending|processing|success|failed|noCommission|notIssued|issued)"
  // 订单 tags
  repeated string tags = 16;
  // 是否不返回总记录数
  bool withoutTotal = 17;
  // 创建时间/应结时间
  mairpc.common.types.DateRange createdAt = 18;
}

message ListDistributionRecordsResponse {
  // 数量
  uint64 total = 1;
  // 列表
  repeated DistributionRecordDetail items = 2;
}

message DistributionRecordDetail {
  // 交易单号
  string transactionId = 1;
  // 订单编号
  string orderNumber = 2;
  // 分销员
  string name = 3;
  // 佣金
  uint64 profitAmount = 4;
  // 发放时间
  string profitAt = 5;
  // 个税
  uint64 tax = 6;
  // 税后收入
  uint64 income = 7;
  // 分销优惠券
  repeated Coupon coupons = 8;
  // 发放状态
  //
  // 未发放（pending，processing），已发放（success），发放失败（failed），无需发放/仅用于日结（noCommission），线下结算未结算（notIssued），线下结算已结算（issued）
  string status = 9;
  // 失败原因
  string failureMsg = 10;
  // 分销员 Id
  string promoterId = 11;
  // 订单信息
  OrderInfo order = 12;
  // 唯一标识，月结的情况下是 transferBillId，日结是预分账表 orderReceiverProfitId
  string uniqueId = 13;
  // 流水号列表
  repeated string transactionIds = 14;
  // 分销员手机号
  string promoterPhone = 15;
  // 账单创建日期/线下结算应结算日期
  string createdAt = 16;
  // 账单标记结算用户
  string markAsCompletedBy = 17;
  // 分销订单总数
  uint64 orderCount = 18;
}

message OrderInfo {
  // 订单 Id
  string id = 1;
  // 订单编号
  string number = 2;
  // 客户 Id
  string memberId = 3;
  // 订单 tags
  repeated string tags = 4;
}

message GetDistributionSettingRequest {
  // @required
  //
  // 分销设置类型
  // 大众分销（member）、导购分销（staff）
  string settingType = 1; // valid:"required,in(member|staff)"
}

message GetDistributionSettingResponse {
  // 租户 Id
  string accountId = 1;
  // 是否开启分销
  bool enabled = 2;
  // 是否开启分销员招募功能
  bool recruitmentEnabled = 3;
  // 分销员与客户之间的规则
  RuleSetting rule = 6;
  // 分销规则
  CommissionSetting commission = 7;
  // 招募模板内容
  RecruitmentContent recruitment = 8;
  // 创建时间
  string createdAt = 9;
  // 导购分销设置 (废弃)
  PromoterCommissionSetting staffCommission = 10;
  // 是否自动审核
  bool autoApproval = 11;
  // 是否需要审核
  bool needApproval = 12;
  // 分销设置
  PromoterCommissionSetting promoterCommission = 13;
  // 分享海报设置
  PosterSetting posterSetting = 14;
  // 自动审核限制
  AutoApprovalLimit autoApprovalLimit = 15;
  RuleDescription ruleDescription = 16;
  // 分销员招募类型
  //
  // public：公开招募，nonpublic：非公开招募
  string recruitmentType = 17; // valid:"in(public|nonpublic)"
  // 是否开启过分销设置
  bool hasEnabled = 18;
  // 是否线下结算
  bool isOffline = 19;
  // 是否显示大众分销分销商品销售额
  bool isShowDistProductAmount = 20;
  // 大众分销二级分销员设置
  SubPromoterSetting subPromoterSetting = 21;
  // 佣金结算设置
  CommissionSettlementSetting commissionSettlementSetting = 22;
}

message TaxSetting {
  // 是否代缴个税
  bool deductTax = 1;
  // 个税承担方 0 个人，1 品牌
  int32 taxPayer = 2; // valid:"in(0|1)"
  // 税后佣金发放账户类型，目前仅支持银行卡（bankCard）、微信零钱（wechatBalance）
  string accountType = 3; // valid:"in(bankCard|wechatBalance)"
  // 0 群脉与品牌签约，群脉与薪行家签约：群脉按照品牌设置的大众分销分佣规则分账至群脉指定商户号，1 品牌与薪行家直签：群脉仅计算分佣规则，实质不分账
  int32 divideType = 4;
  // divideType 为 0 时，微信商户号
  string merchantNo = 5;
  // divideType 为 0 时，微信商户名称
  string merchantName = 6;
  // 服务费比例 0-100，如果个税承担方是个人：薪行家代付金额 = 实际金额 - 实际金额 * serviceFeeProportion / 100，如果个税承担方是品牌：薪行家代付金额 = 实际金额
  int32 serviceFeeProportion = 7;
  // 签约协议中甲方公司名称
  string companyName = 8;
}

message RuleDescription {
  string content = 1; // 规则描述
}

message PosterSetting {
  PosterStyle productStyle = 1;
  Poster productPoster = 2;
  PosterStyle storeStyle = 3;
  Poster storePoster = 4;
}

message PosterStyle {
  // 分享海报样式
  //
  // 二维码样式 左侧 left 右侧 right 底部 bottom 顶部 top
  string type = 1; // valid:"in(left|right|top|bottom)"
  PosterBackground background = 2;
  string productNameColor = 3;
  string priceColor = 4;
  string originPriceColor = 5;
  string posterColor = 6;
  string storeNameColor = 7;
  string campaignTagColor = 8;
}

message PosterBackground {
  // 分享海报背景类型
  //
  // 分享海报背景类型 纯色 color 图片 picture
  string type = 1; // valid:"in(color|picture)"
  // 分享海报颜色
  string color = 2;
  // 分享海报背景图片
  string picture = 3;
}

message Poster {
  // 分享文案类型
  //
  // 商品分享文案 product 自定义分享文案 custom
  string type = 1; // valid:"in(product|custom)"
  // 分享文案
  string text = 2;
}

message RuleSetting {
  // 绑定设置
  //
  // 永久绑定（permanent）、有效期绑定（boundPeriod）、不绑定（unbound）
  string boundType = 1;
  // 客户有效期天数
  uint32 availablePeriod = 2;
  // 抢客设置
  //
  // 允许抢客（allow）、不允许（notAllow）、保护期（replacePeriod）
  string replaceType = 3;
  // 保护期天数
  uint32 protectionPeriod = 4;
  // 服务导购与绑定导购在同一门店下佣金优先发放对象
  //
  // 默认优先服务导购（true）、可选绑定导购（false）
  bool isServiceStaffFirst = 5;
  // 大众分销员能否绑定自己（当可以不绑定自己时，同时禁止分佣给分销员自己）
  bool canBindSelf = 6;
  // 绑定导购分佣规则
  //
  // 任意方式购买均对绑定导购分佣（any） 以推广链接方式购买，才对绑定导购分佣（link）
  string boundStaffProportionType = 7; // valid:"in(link|any)"
  // 分销员与客户绑定模式，默认 link
  //
  // link：点击链接即绑定；pay：支付时绑定
  string promoterMemberBindMode = 14; // valid:"in(link|pay)"
  // 分销员分享链接是否携带门店参数
  bool isCarryStoreId = 15;
}

message CommissionSetting {
  // 佣金结算时间 订单完成（2）、默认售后维权期结束（1）
  uint32 profitSharingType = 1;
  // 佣金比例
  float proportion = 2;
  // 佣金比例类型
  //
  // 按商品（product）、按订单（order）
  string proportionType = 3;
  // 是否展示商品佣金
  bool isDisplayCommission = 4;
}

message RecruitmentContent {
  // 标题
  string title = 1;
  // 图片
  string image = 2;
  // 按钮/链接颜色
  string color = 3;
}

message PromoterCommissionSetting {
  // 计算周期
  //
  // 日结（day）、月结（month）
  string cycle = 1;
  // 计算方式
  //
  // 按当月销售额计算百分比（currentMonthProportion）、按上月销售额计算百分比（lastMonthProportion）、按当月销售额计算固定金额（currentMonthAmount）
  string calculateType = 2;
  // 计算区间
  repeated RangeSetting range = 3; // valid:"optional"
  // 是否代扣个税
  bool deductTax = 4;
  // 是否开启过代扣个税
  //
  // 此字段由后端判断赋值返回，更新时忽略此字段
  bool hasDeductTaxTurnedOn = 5;
  // 佣金按订单（order）或者商品（product）计算
  string commissionType = 6;
  // 分销商品默认佣金，最低 0.3
  uint64 productDefaultProfitAmount = 7;
  // 按固定金额分佣：defaultAmount，按商品金额比例分佣：defaultProportion，按导购等级配置佣金：staffLevels，按销售额区间配置佣金（当前仅月结）：range
  string distributionMode = 8; // valid:"in(defaultAmount|defaultProportion|staffLevels|range)"
  // 导购等级佣金配置
  //
  // 仅当 distributionMode 为 staffLevels 时有效
  repeated StaffLevelSetting staffLevels = 9; // valid:"optional"
  // 默认佣金比例
  //
  // 当前仅导购分销按商品销售额比例分佣使用
  double defaultProportion = 10;
  // 个税设置
  TaxSetting taxSetting = 14;
}

message StaffLevelSetting {
  // @required
  //
  // 导购等级
  uint64 level = 1; // valid:"required"
  // @required
  //
  // 提成类型
  //
  // 按固定金额（amount），按固定比例（proportion）
  string distributionMode = 2; // valid:"required,in(amount|proportion)"
  // 提成金额
  uint64 amount = 3;
  // 提成比例
  double proportion = 4;
}

message RangeSetting {
  // 区间开始
  uint64 start = 1;
  // 区间结束
  uint64 end = 2;
  // 提成比例
  float proportion = 3;
  // 提成金额
  uint64 amount = 4;
  // 佣金模式
  //
  // 按比例分佣：proportion，按固定金额分佣：amount，按导购等级分佣：staffLevels
  string distributionMode = 5; // valid:"in(proportion|amount|staffLevels)"
  // 导购等级佣金配置
  //
  // 仅当 distributionMode 为 staffLevels 时有效
  repeated StaffLevelSetting staffLevels = 6; // valid:"optional"
}

message AutoApprovalLimit {
  // 客户等级
  int64 memberLevel = 1;
  // 客户等级名称
  string memberLevelName = 2;
}

message UpdateDistributionSettingRequest {
  // @required
  //
  // 分销设置类型：大众分销（member）、导购分销（staff）
  string type = 1; // valid:"required"
  // 是否开启分销
  mairpc.common.types.BoolValue enabled = 2;
  // 是否开启招募功能
  mairpc.common.types.BoolValue recruitmentEnabled = 3;
  // 分销员与客户规则
  RuleSetting rule = 4;
  // 分销规则
  CommissionSetting commission = 5;
  // 招募模板
  RecruitmentContent recruitment = 6;
  // 分销设置
  PromoterCommissionSetting promoterCommission = 7; // valid:"optional"
  // 是否自动审核
  mairpc.common.types.BoolValue autoApproval = 8;
  // 是否需要审核
  mairpc.common.types.BoolValue needApproval = 9;
  // 分享海报设置
  PosterSetting posterSetting = 10;
  // 自动审核限制
  AutoApprovalLimit autoApprovalLimit = 11;
  RuleDescription ruleDescription = 12;
  // 分销员招募类型
  //
  // public：公开招募，nonpublic：非公开招募
  string recruitmentType = 13;
  // 二级分销员设置
  SubPromoterSetting subPromoterSetting = 14; // valid:"optional"
  // 佣金结算设置（分销收益流向设置）
  CommissionSettlementSetting commissionSettlementSetting = 15;
}

message SubPromoterSetting {
  // 佣金支付目标，parent（上级分销员）
  string amountSendTo = 1; // valid:"in(parent)"
  // 是否允许上级分销员直接创建下级分销员
  bool allowCreateByParent = 2;
}

message GetPromoterRequest {
  string promoterId = 1; // valid:"objectId"
  // 分销者的 memberId
  //
  // 小程序使用此字段
  string memberId = 2; // valid:"objectId"
  string staffId = 3; // valid:"objectId"
  // 分销员手机号
  string phone = 4;
  // 分销员类型
  string type = 5;
  // 只获取基础的分销员信息
  //
  // 只返回数据库分销员表中存在的字段，不计算预估佣金等信息
  bool isBasic = 6;
  // 控制返回字段
  //
  // subPromoterCount：二级分销员数
  // subPromoterTotalMembers：二级分销员数所绑定客户数
  repeated string extraFields = 7;
  // 渠道 id，小程序使用此字段
  string channelId = 8;
}

message GetDataAnalysesRequest {
  // @required
  //
  // 数据分析 Id
  string id = 1; // valid:"required,objectId"
  // 数据分析状态
  //
  // 已完成（COMPLETED）
  string status = 2;
  // 分析间隔
  //
  // 按天（daily）、按周（weekly）、按月（monthly）
  string dataRange = 3;
  // 分析对应的功能
  //
  // 值为 promotion(推广分析)，trade(交易分析)，product(商品分析)，store(门店分析)，member(用户分析)，flow(流量分析)，miniProgram(店长小程序分析)，distribution（分销分析）
  string function = 4; // valid:"required"
  // 分析时间
  mairpc.common.types.DateRange analysesTime = 5; // valid:"required"
  // 分析对应的模块,例如 ec
  string module = 6;
  // 分析结果前端展示的样式类型
  //
  // 值为 line，rank，funnel，除了折线图和排行图，其余的都为 funnel
  string type = 7; // valid:"required,in(line|rank|funnel)"
  // @required
  //
  // 数据分析类型
  //
  // 商品转化数据（productConversionStats）、卡券转化数据（couponConversionStats）、秒杀活动（flashSaleCampaign）、大众分销数据分析（promoter）、导购分销数据分析（staffPromoter）、买赠活动（presentCampaign）、拼团活动（grouponCampaign）、促销套餐（pacakgeCampaign）、客户转化漏斗（memberConversionFunnel）、交易概览（tradeOverview）、退款概览（refundOverview）、商品概览（productOverview）、门店概览（storeOverview）、用户概览（memberOverview）、流量概览（flowOverview）、类目排行（categoryRank）、商品支付金额排行（productPayAmountRank）、商品访客数排行（productVisitorRank）、商品访客数排行（storeSalesAmountRank）、成交用户概览（completedMemberOverview）、访客来源（visitorSourceRank）
  string dataType = 8; // valid:"required,in(productConversionStats|couponConversionStats|flashSaleCampaign|promoter|staffPromoter|presentCampaign|grouponCampaign|packageCampaign|memberConversionFunnel|tradeOverview|refundOverview|productOverview|storeOverview|memberOverview|flowOverview|categoryRank|productPayAmountRank|productVisitorRank|completedMemberOverview|storeSalesAmountRank|visitorSourceRank|staffPromoterCustom)"
  // 折线图显示字段
  repeated string fields = 9;
  // 监控 id
  string promotionMonitorPointId = 10;
}

message GetPromoterMemberRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // 关系状态
  //
  // bound 客户与店员建立联系且绑定
  // unbound 客户与店员建立联系且绑定过，但后来解绑了
  string status = 2;
  // 分销员类型
  //
  // staff 导购分销员
  // member 大众分销员
  string promoterType = 3;
}

message GetPromoterMemberResponse {
  PromoterMemberDetail promoter = 1;
}

message GetPromoterIdsRequest {
  // 零售公司 ids
  repeated string distributorIds = 1;
}

message GetPromoterIdsResponse {
  repeated string promoterIds = 1;
}

message GetStaffDistributionOverviewStatsResponse {
  repeated StaffDistributionStats stats = 1;
}

message StaffDistributionStats {
  // 统计数据类型
  string type = 1;
  // 昨日数据
  int64 yesterdaySum = 2;
  // 今日数据
  int64 todaySum = 3;
}

message ListPromoterTransferBillsRequest {
  // @required
  //
  // 分销者 ID
  string memberId = 1; // valid:"objectId,required"
  // 分账发起时间
  mairpc.common.types.StringDateRange createdTime = 2;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 3;
  // 是否需要计算账单累计金额
  bool needCalculateTotalAmount = 4;
  // 账单状态
  repeated string status = 5;
  // 账单来源
  //
  // 可选值：staff_distribution（导购分销）、member_distribution（大众分销）、staff_invitation（导购拉新激励）、staff_redemption（导购卡券核销激励）
  // 分账（profitsharing）、commission（小店分佣）、自定义导购激励
  repeated string sources = 6;
  // 是否计算二级分销员历史上级分销员账单
  bool needCalculateHistoryParentPromoter = 7;
  // 分账完成时间
  mairpc.common.types.StringDateRange finishedTime = 8;
}

message ListPromoterTransferBillsResponse {
  // 数量
  uint64 total = 1;
  repeated mairpc.ec.profitsharing.TransferBillDetail items = 2;
  // 累计收益
  uint64 totalAmount = 3;
  // 待结算金额
  uint64 waitAmount = 4;
  // 累计销售金额
  uint64 totalShareAmount = 5;
  // 分销商品销售额
  uint64 distProductTotalAmount = 6;
}

message CreateOfflineStaffPromoterRequest {
  // @required
  //
  // staffNo
  string staffNo = 1;
}

message ListPromoterProductsStatsRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  mairpc.common.request.ListCondition listCondition = 2;
  // 下单时间筛选
  mairpc.common.types.StringDateRange orderCreatedAt = 3;
  // 二级分销员筛选
  repeated string promoterIds = 4; // valid:"objectIdList"
}

message ListPromoterProductsStatsResponse {
  repeated PromoterProductStats items = 1;
}

message PromoterProductStats {
  mairpc.product.ProductDetailResponse product = 1;
  product.ECProduct ec = 2;
  // 累计销量
  int32 total = 3;
  // 累计分销收益
  int64 totalProfitAmount = 4;
}

message GetMemberBoundStaffStatsRequest {
  // 绑定导购 Id
  string staffId = 1; // valid:"objectId"
  // 统计月份
  //
  // 格式如 200601，不穿则默认统计当前月份
  string monthKey = 2;
  // 是否统计门店下所有在职导购数据
  bool statsAllStaffs = 3;
  // 门店 id，传该参数将获取门店下所有在职导购的统计
  string storeId = 4; // valid:"objectId"
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 5;
}

message GetMemberBoundStaffStatsResponse {
  // 分销员销售额
  //
  // 已支付订单的付款金额，产生退款会相应的减少
  uint64 orderAmount = 1;
  // 分销员完成订单数
  uint64 orderCount = 2;
  // 分销员分销商品销量
  uint64 productCount = 3;
  // 导购绑定分销员数
  uint64 boundPromoters = 4;
  // 分销员绑定客户数
  uint64 promoterMembers = 5;
  // 分销员推广金额
  //
  // 只要付款就算
  uint64 promoteAmount = 6;
}

message ListMemberBoundStaffPerformanceRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // 搜索关键字
  string searchKey = 2;
}

message ListMemberBoundStaffPerformanceResponse {
  repeated MemberBoundStaffPerformance items = 1;
  int64 total = 2;
}

message MemberBoundStaffPerformance {
  // 导购 Id
  string staffId = 1;
  // 导购名称
  string staffName = 2;
  // 导购手机
  string staffPhone = 3;
  // 导购绑定分销员数
  uint64 boundPromoters = 4;
  // 导购绑定客户数
  uint64 boundMembers = 5;
  // 推广订单数
  uint64 promoteOrders = 6;
  // 销售额
  uint64 orderAmount = 7;
  // 销售商品数
  uint64 products = 8;
  // 导购所属门店
  repeated StaffPromoterStores stores = 9;
  // 导购编号
  string staffNo = 10;
}

message RepairStaffPromoterRequest {
  // @required
  //
  // 导购 Id
  string staffId = 1; // valid:"required,objectId"
}

message GetPromoterSensitiveRequest {
  // @required
  //
  // 分销员 id
  string promoterId = 1; // valid:"required,objectId"
  // @required
  //
  // 敏感信息字段
  string sensitiveKey = 2; // valid:"in(name|phone),required"
  // 是否返回已被删除的数据
  bool containDeleted = 3;
}

message RetryDistributionRecordRequest {
  // @required
  //
  // 唯一标识，月结的情况下是 transferBillId，日结是预分账表 orderReceiverProfitId
  repeated string uniqueIds = 1; // valid:"required,objectIdList"
  // @required
  //
  // 分销类型
  string type = 2; // valid:"required,in(staff|member)"
}

message IsPromoterExistsRequest {
  // @required
  //
  // 手机号
  string phone = 1; // valid:"required"
  // @required
  //
  // 分销员类型，staff（导购分销）、member（大众分销）
  string type = 2; // valid:"required,in(staff|member)"
}

message XinHangJiaNotifyRequest {
  // @required
  //
  // 通知类型
  string function = 1; // valid:"required,in(signbatch|payment)"
  // @required
  //
  // 商户号
  string mchntNum = 2;
  // @required
  //
  // 签名
  string signature = 3;
  // @required
  //
  // 通知参数加密后的密文
  string resCipher = 4;
}

message XinHangJiaSignRequest {
  // @required
  //
  // 分销员 id
  string promoterId = 1; // valid:"required,objectId"
  // @required
  //
  // 身份证号
  string idCardNumber = 2;
  // @required
  //
  // 身份证人像面可访问图片 url
  string idCardA = 3;
  // @required
  //
  // 身份证国徽面可访问图片 url
  string idCardB = 4;
  // @required
  //
  // 银行卡号
  string bankAccount = 5;
}

message PromoterNamePhonePair {
  string name = 1;
  string phone = 2;
}

message CreateSubPromotersRequest {
  // @required
  //
  // 上级分销员 id
  string parentId = 1; // valid:"required,objectId"
  // @required
  //
  // 二级分销员信息
  repeated PromoterNamePhonePair promoters = 2; // valid:"required"
  // 渠道 id，小程序传
  string channelId = 3;
  string memberId = 4;
}

message TransBoundPromoterMembersRequest {
  // @required
  //
  // 转出的分销员 id
  string from = 1; // valid:"required,objectId"
  // @required
  //
  // 转入的分销员 id
  string to = 2; // valid:"required,objectId"
  // 被转移的客户的 id 列表，不传则为全部的绑定客户
  repeated string memberIds = 3; // valid:"objectIdList"
}

message CreatePromoterFromPortalRequest {
  // @required
  //
  // 手机号
  string phone = 1; // valid:"required"
  // @required
  //
  // 姓名
  string name = 2; // valid:"required"
  repeated mairpc.member.PropertyInfo properties = 3;
}

message ExportPromoterPosterRequest {
  // @required
  //
  // 海报类型，weapp（小程序海报），product（商品海报）
  string type = 1; // valid:"required,in(weapp|product)"
  // @required
  //
  // 分销员 id
  repeated string promoterIds = 2; // valid:"required,objectIdList"
  // 商品 id
  repeated string productIds = 3; // valid:"objectIdList"
  // memberId（小程序端限制 job 每个人最多同时运行一个）
  string memberId = 4;
}

message FrozeSubPromoterRequest {
  // @required
  //
  // 一级分销员的 memberId
  string memberId = 1; // valid:"required,objectId"
  // 冻结操作
  bool frozen = 2;
  // @required
  //
  // 要操作的二级分销员 id
  string promoterId = 3; // valid:"required,objectId"
  // 将被冻结的分销员的绑定客户转移到
  string transPromoterMembersTo = 4; // valid:"objectId"
}

message ListPromoterStatsRequest {
  // @required
  //
  // 客户 id
  string memberId = 1; // valid:"required,objectId"
  // 订单创建时间
  mairpc.common.types.StringDateRange orderCreatedAt = 2;
  // 是否返回二级分销员数据
  bool withSubPromotersStats = 3;
}

message PromoterStats {
  // 订单数
  uint64 totalOrders = 1;
  // 销量
  uint64 totalProducts = 2;
  // 客户数
  uint64 totalMembers = 3;
  // 销售额
  uint64 totalAmount = 4;
  // 收益
  uint64 totalProfitAmount = 5;
  // 分销员 id
  string promoterId = 6;
  // 分销员姓名
  string name = 7;
  // 分销员手机号
  string phone = 8;
  // 分销员头像
  string avatar = 9;
  // 最终绑定客户数
  uint64 bindingMembers = 10;
}

message ListPromoterStatsResponse {
  repeated PromoterStats items = 1;
}

message BriefPromoter {
  string id = 1;
  string name = 2;
}

message ListBriefPromotersResponse {
  repeated BriefPromoter items = 1;
  int64 total = 2;
}

message GetSubPromoterRequest {
  // @required
  //
  // 一级分销员 memberId
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 二级分销员 id
  string promoterId = 2; // valid:"required,objectId"
}

message ListPromoterConfirmAmountRequest {
  // @required
  //
  // 客户 id
  string memberId = 1; // valid:"required,objectId"
  // 状态，WAIT_USER_CONFIRM（待确认）、FAIL（失败）、SUCCESS（成功）、TRANSFERING（提现中）
  repeated string status = 2; // valid:"in(WAIT_USER_CONFIRM|FAIL|SUCCESS|TRANSFERING)"
  mairpc.common.types.StringDateRange createdTime = 3;
}

message ListPromoterConfirmAmountResponse {
  int64 total = 1;
  repeated ConfirmAmount items = 2;
  uint64 waitConfirmAmount = 3;
}

message ConfirmAmount {
  // 提现金额
  uint64 amount = 1;
  // 时间
  string createdAt = 2;
  // 提现状态，SUCCESS（成功）、FAIL（失败）、WAIT_USER_CONFIRM（待提现）
  string status = 3;
  // 提现失败的原因
  string failedReason = 4;
  // 拉起微信提现功能的确认码
  string confirmCode = 5;
  // 唯一标识
  string id = 6;
}

message ListDistributionOrdersByTransferBillIdRequest {
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 1;
  // @required
  //
  // 账单 id
  string transferBillId = 2; // valid:"required,objectId"
}

message ListDistributionOrdersByTransferBillIdResponse {
  repeated BriefDistributionOrder items = 1;
  uint64 total = 2;
}

message BriefDistributionOrder {
  string id = 1;
  string number = 2;
  uint64 amount = 3;
  string memberId = 4;
  repeated string tags = 5;
  repeated Coupon coupons = 6;
}

message CommissionSettlementSetting {
  // 结算方式: bankCard-银行卡 fundAccount-资金账户
  string method = 1; // valid:"in(bankCard|fundAccount)"
  // 提现设置
  WithdrawalSetting withdrawalSettings = 2;
}

message WithdrawalSetting {
  // 是否启用提现设置
  bool enabled = 1;
  // 提现频率配置
  WithdrawalFrequency frequency = 2;
  // 提现比例 1-100
  int32 percentage = 3;
  // 提现规则说明文本
  string description = 4;
}

message WithdrawalFrequency {
  // 提现频率模式：daily(按天)/monthly(按月)/quarterly(按季)/yearly(按年)
  string mode = 1; // valid:"in(daily|monthly|quarterly|yearly)"
  // 最大提现次数
  int32 maxTimes = 2;
}
