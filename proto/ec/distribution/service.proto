syntax = "proto3";

package mairpc.ec.distribution;

option go_package = "distribution";

import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/response/response.proto";
import "ec/distribution/distribution.proto";
import "ec/product/product.proto";

service DistributionService {
  // 新增分销者
  rpc CreatePromoter(CreatePromoterRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新分销者
  rpc UpdatePromoter(UpdatePromoterRequest) returns (mairpc.common.response.JobResponse);
  // 更新分销者手机号
  rpc UpdatePromoterPhone(UpdatePromoterPhoneRequest) returns (mairpc.common.response.EmptyResponse);
  // 分销者列表
  rpc ListPromoters(ListPromotersRequest) returns (ListPromotersResponse);
  // 简化的分销者列表
  rpc ListBriefPromoters(ListPromotersRequest) returns (ListBriefPromotersResponse);
  // 分销者详情
  rpc GetPromoter(GetPromoterRequest) returns (PromoterDetail);
  // 删除审核失败分销员
  rpc DeletePromoter(mairpc.common.request.StringIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取二级分销员详情
  rpc GetSubPromoter(GetSubPromoterRequest) returns (PromoterDetail);
  // 导出分销者
  rpc ExportPromoters(ListPromotersRequest) returns (mairpc.common.response.JobResponse);
  // 绑定分销客户
  rpc BindPromoterMember(BindPromoterMemberRequest) returns (mairpc.common.response.EmptyResponse);
  // 分销客户列表
  rpc ListPromoterMembers(ListPromoterMembersRequest) returns (ListPromoterMembersResponse);
  // 分销商品列表
  rpc ListDistributionProducts(ListDistributionProductsRequest) returns (ListDistributionProductsResponse);
  // 更新大众分销商品
  rpc UpdateDistributionProducts(UpdateDistributionProductsRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新符合搜索条件的所有商品的大众分销
  rpc UpdateDistributionProductsWithSearch(UpdateDistributionProductsWithSearchRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新导购分销商品
  rpc UpdateStaffDistributionProducts(UpdateStaffDistributionProductsRequest) returns (mairpc.common.response.EmptyResponse);
  // 分销订单列表
  rpc ListDistributionOrders(ListDistributionOrdersRequest) returns (ListDistributionOrdersResponse);
  // 根据账单获取对应分销订单列表
  rpc ListDistributionOrdersByTransferBillId(ListDistributionOrdersByTransferBillIdRequest) returns (ListDistributionOrdersByTransferBillIdResponse);
  // 统计分销订单数
  rpc CountDistributionOrders(ListDistributionOrdersRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 分销订单导出
  rpc ExportDistributionOrders(ListDistributionOrdersRequest) returns (mairpc.common.response.JobResponse);
  // 分销记录列表
  rpc ListDistributionRecords(ListDistributionRecordsRequest) returns (ListDistributionRecordsResponse);
  // 统计分佣记录数
  rpc CountDistributionRecords(ListDistributionRecordsRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 分销设置
  rpc GetDistributionSetting(GetDistributionSettingRequest) returns (GetDistributionSettingResponse);
  // 更新分销设置
  rpc UpdateDistributionSetting(UpdateDistributionSettingRequest) returns (mairpc.common.response.EmptyResponse);
  // 自动解绑到期客户
  rpc UnbindPromoterMemberJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 分销员客户导出
  rpc ExportPromoterMember(ListPromoterMembersRequest) returns (mairpc.common.response.JobResponse);
  // 分销记录导出
  rpc ExportDistributionRecord(ListDistributionRecordsRequest) returns (mairpc.common.response.JobResponse);
  // 分销员数据统计
  rpc PromoterStatisticsJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 分销员数据统计
  rpc ExportDataAnalyses(GetDataAnalysesRequest) returns (mairpc.common.response.JobResponse);
  // 新增企微导购分销员
  rpc CreateDefaultStaffPromoters(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取客户分销关系
  rpc GetPromoterMember(GetPromoterMemberRequest) returns (GetPromoterMemberResponse);
  // 获取对应权限下的分销员 ids
  rpc GetPromoterIds(GetPromoterIdsRequest) returns (GetPromoterIdsResponse);
  // 获取导购分销数据概览
  rpc GetStaffDistributionOverviewStats(mairpc.common.request.EmptyRequest) returns (GetStaffDistributionOverviewStatsResponse);
  // 是否是分销员
  rpc IsPromoter(mairpc.common.request.DetailRequest) returns (mairpc.common.response.BoolResponse);
  // 批量更新分销者状态
  rpc BatchUpdatePromoterStatus(BatchUpdatePromoterStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取分销者账单列表
  rpc ListPromoterTransferBills(ListPromoterTransferBillsRequest) returns (ListPromoterTransferBillsResponse);
  // 获取分销者提现列表
  rpc ListPromoterConfirmAmount(ListPromoterConfirmAmountRequest) returns (ListPromoterConfirmAmountResponse);
  // 创建线下分销员
  //
  // 联华华商分佣模式为线下，且存在导购未完成实名认证，微信无法获取 openId 情况，使用 staffNo 代替 openId 字段，生成 receiver 信息时也不会调用微信的接口，关联 issue 地址：https://gitlab.maiscrm.com/mai/impl/team-standard/home/<USER>/issues/307
  rpc CreateOfflineStaffPromoter(CreateOfflineStaffPromoterRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取分销员商品统计
  rpc ListPromoterProductsStats(ListPromoterProductsStatsRequest) returns (ListPromoterProductsStatsResponse);
  // 获取分销员业绩统计
  rpc ListPromoterStats(ListPromoterStatsRequest) returns (ListPromoterStatsResponse);
  // 获取导购绑定的分销员统计
  rpc GetMemberBoundStaffStats(GetMemberBoundStaffStatsRequest) returns (GetMemberBoundStaffStatsResponse);
  // 获取大众分销员绑定的导购的业绩列表
  rpc ListMemberBoundStaffPerformance(ListMemberBoundStaffPerformanceRequest) returns (ListMemberBoundStaffPerformanceResponse);
  // 获取大众分销员绑定的导购的业绩列表
  rpc ExportMemberBoundStaffPerformance(ListMemberBoundStaffPerformanceRequest) returns (mairpc.common.response.JobResponse);
  // 更新分销员计数任务
  //
  // 目前该任务只更新了导购当前客户数，后续可能会添加其他计数，但都需要遵循任意时候都可以执行的原则
  rpc UpdatePromoterCountJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 整理分销设置，更新分销设置和分销商品
  rpc IntegrateDistributionSetting(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 注销分销员
  //
  // 注销客户时需要注销大众分销员身份，通过订阅系统事件来触发
  rpc LogoffPromoter(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理创建导购
  //
  // 导购创建后为导购创建分销员
  rpc HandleStaffCreate(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 修复导购分销账号
  rpc RepairStaffPromoter(RepairStaffPromoterRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取分销员敏感信息
  rpc GetPromoterSensitive(GetPromoterSensitiveRequest) returns (mairpc.common.ec.GetSensitiveResponse);
  // 手动重试分佣记录
  rpc RetryDistributionRecord(RetryDistributionRecordRequest) returns (mairpc.common.response.EmptyResponse);
  // 判断分销员是否已存在
  rpc IsPromoterExists(IsPromoterExistsRequest) returns (mairpc.common.response.BoolResponse);
  // 迁移分销员数据
  rpc MigratePromoter(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 薪行家通知
  rpc XinHangJiaNotify(XinHangJiaNotifyRequest) returns (mairpc.common.response.EmptyResponse);
  // 薪行家签约
  rpc XinHangJiaSign(XinHangJiaSignRequest) returns (mairpc.common.response.EmptyResponse);
  // 执行薪行家代付
  rpc ExecXinHangJiaPaymentJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建二级分销员
  rpc CreateSubPromoters(CreateSubPromotersRequest) returns (mairpc.common.response.EmptyResponse);
  // 转移绑定客户
  rpc TransBoundPromoterMembers(TransBoundPromoterMembersRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建一级分销员
  rpc CreatePromoterFromPortal(CreatePromoterFromPortalRequest) returns (mairpc.common.response.EmptyResponse);
  // 导出分销员推广海报
  rpc ExportPromoterPosterWithJob(ExportPromoterPosterRequest) returns (mairpc.common.response.JobResponse);
  // 导出分销员推广海报
  rpc ExportPromoterPosterWithAsyncCache(ExportPromoterPosterRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 冻结二级分销员
  rpc FrozeSubPromoter(FrozeSubPromoterRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取佣金最大的商品
  rpc GetMaxProfitAmountProduct(mairpc.common.request.MemberIdRequest) returns (mairpc.ec.product.ProductResponse);
  // 解除大众分销员自身绑定
  rpc UnbindPromoterMemberForPromoterSelf(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 绑定大众分销员和自己
  rpc BindPromoterMemberForPromoterSelf(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
}
