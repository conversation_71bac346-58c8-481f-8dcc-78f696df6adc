syntax = "proto3";

package mairpc.ec.profitsharing;

option go_package = "profitsharing";

import "common/request/request.proto";
import "common/types/types.proto";

message ListTransferBillsRequest {
  // 关键字类型
  //
  // 可选值：detailId（交易单号）、receiver（分账接收方）
  string searchType = 1; // valid:"in(detailId|receiver)"
  // 搜索关键字
  //
  // searchType 为 receiver 时支持 account 和 receiver.id
  string searchKey = 2;
  // 分账状态
  //
  // 可选值：pending（待分账）、processing（分账中）、success（成功）、failed（失败）
  repeated string status = 3;
  // 分账金额
  //
  // 单位：分
  mairpc.common.types.IntegerRange shareAmount = 4;
  // 分账发起时间
  mairpc.common.types.DateRange createdTime = 5;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 6;
  // 账单 ID
  repeated string billIds = 7; // valid:"objectIdList"
  // 账单来源
  //
  // 可选值：staff_distribution（导购分销）、member_distribution（大众分销）、staff_invitation（导购拉新激励）、staff_redemption（导购卡券核销激励）
  // 分账（profitsharing）、commission（小店分佣）、自定义导购激励
  repeated string sources = 8;
  // 是否需要计算累计金额
  mairpc.common.types.BoolValue needCalculateTotalAmount = 9;
  mairpc.common.types.StringDateRange finishedAt = 10;
  // 不包含在内的账单来源
  repeated string excludeSources = 11;
}

message ListTransferBillsResponse {
  // 数量
  uint64 total = 1;
  // 列表
  repeated TransferBillDetail items = 2;
  // 累计收益
  //
  // needCalculateTotalAmount 参数为 true 时会计算累计收益
  uint64 totalAmount = 3;
  // 累计金额
  //
  // needCalculateTotalAmount 参数为 true 时会计算累计金额
  uint64 totalShareAmount = 4;
}

message TransferBillDetail {
  // ID
  string id = 1;
  // 分账接收方信息
  BriefReceiver receiver = 3;
  // 分账金额
  uint64 shareAmount = 4;
  // 分账结果
  string status = 5;
  // 备注
  string remarks = 6;
  // 创建时间
  string createdAt = 7;
  // 完成时间
  string finishedAt = 8;
  // 失败原因
  string failedMessage = 9;
  // 交易单号
  string detailId = 10;
  // 扣除个税
  uint64 tax = 11;
  // 税后金额
  uint64 income = 12;
  // 分账来源
  string source = 13;
  // 分账描述
  string description = 14;
  // 外部订单号，对于导购激励，是奖励记录的 id
  string outTradeNo = 15;
  // 单笔订单分账金额上限
  uint64 singleOrderProfitCap = 16;
  // 计算周期
  //
  // 日结（day）、月结（month）
  string cycle = 17;
  // 新版微信商家转账信息
  WechatTransferV3Info wechatTransferV3Info = 18;
  repeated SubBill subBills = 19;
  // 关联订单数
  int64 orderCount = 20;
}

message SubBill {
  string id = 1;
  uint64 income = 2;
  string status = 3;
  string failedMessage = 4;
  WechatTransferV3Info wechatTransferV3Info = 5;
}

message WechatTransferV3Info {
  string confirmCode = 1;
  string status = 2;
}

message BriefReceiver {
  // ID
  string id = 1;
  // 姓名
  string name = 2;
  // 账户类型
  string accountType = 3;
  // 账户号
  string account = 4;
}

message CreateBillRequest {
  string id = 1;
  // @required
  //
  // 接收者账号，如商户号、微信号、银行账号、微信 openId 值
  string account = 2; // valid:"required"
  // @required
  //
  // 转账金额
  uint64 shareAmount = 3; // valid:"required"
  // @required
  //
  // 税后金额
  uint64 income = 4; // valid:"required"
  string description = 5; //
  // @required
  //
  // 唯一转账标识
  string outTradeNo = 6; // valid:"required"
  // 税
  uint64 tax = 7;
  string detailId = 8;
  string orderTradeNo = 9;
  // 账单来源
  string source = 10;
  // 分账渠道
  string transferChannel = 11;
}

message CreateShareBillForOfflinePromoterMonthlyRequest {
  // 分销员 ID
  string promoterId = 1; // valid:"required,objectId"
  // 离职于
  string offlineAt = 2;
  mairpc.common.types.BoolValue debug = 3;
}

message CalculateFailedTransferBillForReceiverRequest {
  // @required
  //
  // 分账接收方 Id
  string receiverId = 1; // valid:"objectId,required"
  // 分账来源
  //
  // staff_distribution：导购分销，staff_invitation：导购拉新激励，staff_redemption：导购卡券核销激励，member_distribution：大众分销，profitsharing：分账，commission：代销小店分佣
  repeated string sources = 2; // valid:"in(staff_distribution|staff_invitation|staff_redemption|member_distribution|profitsharing|commission)"
  // 重试分账
  //
  // 此字段为 true 时才会重试分账，为 false 时只计算分佣失败的总金额
  // 重试分账前请先设置此字段为 false 计算金额，确定无误后再传 true 执行重试。
  bool retryTransfer = 3;
  // 指定需要重发的 bill
  repeated string billIds = 4; // valid:"objectIdList"
  // 是否生成新的微信单号
  bool genNewTradeNo = 5;
}

message CalculateFailedTransferBillForReceiverResponse {
  // 分账总金额
  uint64 totalAmount = 1;
  // 账单 Id 列表
  repeated string billIds = 2;
}
