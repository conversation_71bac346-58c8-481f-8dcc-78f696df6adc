syntax = "proto3";

package mairpc.ec.wechat_shop;

option go_package = "wechat_shop";

import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/response/response.proto";
import "ec/wechatShop/wechat_shop.proto";

service WechatShopService {
  // 推送商品
  rpc PushProducts(PushProductsRequest) returns (mairpc.common.response.EmptyResponse);
  // 推送商品
  rpc SubmitWechatProducts(mairpc.common.request.IdListRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量创建或更新交易组件商品
  //
  // 提交需要重新审核的商品的资料
  rpc BatchUpsertWechatProduct(PushProductsRequest) returns (WechatProductList);
  // 删除交易组件商品
  rpc DeleteWechatProducts(DeleteWechatProductsRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新微信商品，订阅更新商品事件
  rpc PushProductByEvent(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 推送退款单
  rpc PushOrderRefund(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建微信订单
  rpc CreateWechatOrder(CreateWechatOrderRequest) returns (CreateWechatOrderResponse);
  // 微信订单发货
  rpc DeliverWechatOrder(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 标记微信订单已完成/确认收货
  rpc CompleteWechatOrder(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理商品审核结果
  rpc HandleWechatProductAudited(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步微信交易组件类目
  rpc SyncWechatCategories(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 提交微信交易组件类目审核
  rpc SubmitWechatCategoryAudit(SubmitWechatCategoryAuditRequest) returns (mairpc.common.response.EmptyResponse);
  // 自动提交微信交易组件商品审核
  rpc AutoPushWechatProducts(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理类目审核结果
  rpc HandleWechatCategoryAudited(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取微信交易组件商品列表
  rpc ListWechatProducts(ListWechatProductsRequest) returns (WechatProductList);
  // 获取微信商品类目列表
  rpc ListWechatCategories(ListWechatCategoriesRequest) returns (WechatCategoryList);
  // 创建/更新商品资质
  rpc UpsertWechatProductQualification(UpsertWechatProductQualificationRequest) returns (WechatProductQualificationDetail);
  // 删除商品资质
  rpc DeleteWechatProductQualifications(mairpc.common.request.IdListRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建/更新商品资质
  rpc ListWechatProductQualifications(ListWechatProductQualificationRequest) returns (WechatProductQualificationList);
  // 获取交易组件设置
  rpc GetWechatShopSettings(mairpc.common.request.EmptyRequest) returns (WechatShopSettingsDetail);
  // 创建/更新交易组件设置
  rpc UpsertWechatShopSettings(UpsertWechatShopSettingsRequest) returns (WechatShopSettingsDetail);
  // 处理订单收货地址更改事件，仅支持发货前修改
  rpc HandleWechatOrderContactAdjustment(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理订单取消事件
  rpc HandleOrderCanceled(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理订单改价
  rpc HandleWechatOrderAmoutAdjustment(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理售后更新
  rpc HandleRefundUpdated(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理微信侧系统下架商品回调
  rpc HandleWechatProductUnshelved(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理微信侧订单确认收货回调
  rpc HandleWechatOrderReceived(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理微信订单支付成功回调
  rpc HandleWechatOrderPaid(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理订单取消回调
  rpc HandleWechatOrderCanceled(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理售后单创建回调
  rpc HandleWechatRefundCreated(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理售后单更新回调
  rpc HandleWechatRefundUpdated(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 生成支付参数并向微信推送订单
  rpc WechatOrderPrePay(WechatOrderPrepayRequest) returns (mairpc.common.ec.PrePayResponse);
  // 获取群脉退款单对应的退款单号
  rpc GetRefundNumbersByRefundId(mairpc.common.request.DetailRequest) returns (mairpc.common.response.StringArrayResponse);
  // 进行退款
  rpc ExecRefund(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 视频号场景支付前置检查
  rpc CheckBeforePay(CheckBeforePayRequest) returns (CheckBeforePayResponse);
  // 根据 ec.order._id 获取视频号订单详情
  rpc GetWechatOrder(GetWechatOrderRequest) returns (GetWechatOrderResponse);
  // 处理零售的支付事件，防止因在小程序内支付视频号订单导致微信侧订单状态异常
  rpc HandleEcOrderPaid(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 上传订单发货信息到微信
  rpc UploadOrderShippingInfoToWechat(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理小程序发货管理服务的回调
  rpc HandleWechatTradeManagementEvents(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 初始化微信发货管理服务
  rpc InitWechatTradeManagement(InitWechatTradeManagementRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理商品退款事件
  rpc HandleProductRefundEvent(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 重推零售订单的物流信息
  rpc RetryUploadOrderShippingInfo(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 刷新运费险退货状态
  rpc RefreshFreightInsuranceReturnStatus(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 轮询运费险退货状态
  rpc CheckFreightInsuranceReturnStatus(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
}
