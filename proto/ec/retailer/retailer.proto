syntax = "proto3";

package mairpc.ec.retailer;

option go_package = "retailer";

import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/types/types.proto";
import "ec/order/cart.proto";
import "ec/product/product.proto";
import "product/product.proto";

message UpdateStoreProductRequest {
  // @required
  //
  // 门店 id
  string storeId = 1; // valid:"required,objectId"
  // @required
  //
  // 商品 id
  string productId = 2; // valid:"required,objectId"
  // @required
  //
  // 商品 sku
  string sku = 3; // valid:"required"
  // @required
  //
  // 商品价格
  int64 price = 4;
}

message GetStoreProductRequest {
  // @required
  //
  // 门店 id
  string storeId = 1; // valid:"required,objectId"
  // @required
  //
  // 商品 id
  string productId = 2; // valid:"required,objectId"
}

message ListCartGroupsRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
}

message ListCartGroupsResponse {
  // 有效分组
  repeated mairpc.ec.order.CartGroup validGroups = 1;
  // 失效分组
  repeated mairpc.ec.order.CartGroup invalidGroups = 2;
  // 商品总价
  int64 totalAmount = 3;
  // 优惠信息
  repeated mairpc.common.ec.DiscountInfo discounts = 4;
  // 实付金额
  int64 payAmount = 5;
}

message GetProductRequest {
  // 商品ID
  string productId = 1; // valid:"objectId"
  // 门店ID
  //
  // 若提供门店ID，那么返回值里 ec.skus[N].price 与 ec.skus[N].originalPrice
  // 字段展示的都会是对应门店所在区域的区域价格。其中 N 表示 ec.skus 中第 N 个值
  string storeId = 2; // valid:"objectId"
  // 是否有可用优惠券
  //
  // 若为 true，那么在商品中会用 ec.hasCoupon 字段来标识该商品是否有可用的单品券或通用券
  // 若请求里还提供了门店ID，那么判断是否有单品券、通用券时还会加上“单品券、通用券在门店所属地区可用”的条件
  bool hasCoupon = 3;
  // 是否包含已删除的商品
  bool containDeleted = 4;
  // 商品 sku
  string sku = 5;
  // 商品编码
  string number = 6;
  // 付费卡券优惠券 ID
  string couponId = 7; // valid:"objectId"
}

message ProductResponse {
  mairpc.product.ProductDetailResponse product = 1;
  mairpc.ec.product.ECProduct ec = 2;
  // 商品详情页销量是否展示
  bool isSalesCountEnabled = 3;
}

message CalculateOrderAmountRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
  // 是否是直接购买
  //
  // 当此字段为 true 时才会用到购买商品的几个字段且此时这几个字段必填
  bool isPurchase = 4;
  // 购买商品的 ID
  string productId = 5; // valid:"objectId"
  // 购买商品 sku
  string sku = 6;
  // 购买商品数量
  uint64 count = 7;
  // 收货地址 ID
  string memberAddressId = 8;
  // 经销商品配送方式
  string distributionMethod = 9;
}

message CalculateOrderAmountResponse {
  // 订单总金额
  //
  // 单位：分
  uint64 totalAmount = 1;
  // 实际支付金额
  //
  // 单位：分
  uint64 payAmount = 2;
  // 运费信息
  repeated mairpc.common.ec.DeliveryInfo deliveryInfos = 3;
  // 拆分后的商品信息
  repeated SplitProduct splitProducts = 4;
  // 折扣信息
  repeated DiscountInfo discounts = 5;
  // 优惠组合
  repeated Discount discountGroup = 6;
  // 同城配送起送金额差值 单位：分
  uint64 cityExpressDiffPrice = 7;
  // 错误信息
  string error = 8;
}

message DiscountInfo {
  // 优惠券 ID
  string id = 1;
  // 优惠券名称
  string title = 2;
  // 优惠金额
  //
  // 单位：分
  int64 amount = 3;
  // 折扣类型
  //
  // campaign：活动折扣
  string type = 4;
  // 会员等级
  uint64 memberLevel = 5;
  // 会员折扣
  //
  // 扩大十倍，8.8折即88
  uint64 discount = 6;
  // 套餐主图
  string picture = 7;
  // 活动类型
  //
  // 代销目前只有 discountCampaign(限时特价)
  string campaignType = 8;
  // 操作时间
  string operatedAt = 9;
  // 操作人
  string operator = 10;
  // 会员名称
  string memberName = 11;
  // 优惠券券码
  string couponCode = 12;
  // 消费折扣适用总金额
  uint64 suitTotalAmount = 13;
  // 消费折扣适用对象
  //
  // 商品售价 price、商品划线价 originalPrice
  string priceType = 14;
}

message Discount {
  // 类型
  //
  // 活动优惠（campaign）、会员折扣（member)、权益卡（benefitCard）、储值卡（prepaidCard）、优惠券（coupon）、积分抵扣（score）
  string type = 1;
  // 是否当前已选中
  bool hasSelected = 2;
  // 优惠金额
  uint64 amount = 3;
  // 是否可用
  bool enabled = 4;
  // 不可用原因
  //
  // 活动（campaign）、会员折扣（member）、商品（product）、权益卡（benefitCard）、储值卡（prepaidCard）、优惠券（coupon）、付费卡券（couponProduct）
  string disableReason = 5;
  // 可用数量
  uint64 count = 6;
  // 活动优惠和会员折扣是否互斥
  bool isConflict = 7;
  // json 格式的字符串
  string extra = 8;
}

message SplitProduct {
  // 经销商 id，如果是经销商品，为小店 id，如果是代销商品，为经销商 id
  string distributorId = 1;
  // 类型，distribution 经销商品，consignment 代销商品
  string type = 2;
  // 商品信息
  repeated Product products = 3;
  // 订单备注
  string remarks = 4;
  // 提货人信息，只有经销商品有值
  mairpc.common.ec.ContactRequest contact = 5;
  // 预留提货信息
  mairpc.common.ec.ReservationInfo reservation = 6;
  // 配送方式
  //
  // 门店自提 pickup、快递配送 express 同城快送 cityExpress
  string method = 7; // valid:"in(express|pickup|cityExpress)"
  // 经销商品支持的配送方式
  //
  // 快递配送（express），门店自提（pickup），同城配送（cityExpress）
  repeated string deliveryMethods = 8;
  // 同城快送预计送达时间
  int64 expectDeliveryAt = 9;
  // 期望送达时间文本
  string expectDeliveryAtLabel = 10;
}

message CreateOrderRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
  // @required
  //
  // 来源渠道 ID
  string channelId = 3; // valid:"required"
  // @required
  //
  // 渠道下客户唯一身份信息
  string openId = 4; // valid:"required"
  // 是否是直接购买
  //
  // 当此字段为 true 时才会用到购买商品的几个字段且此时这几个字段必填
  bool isPurchase = 5;
  // 商品 ID
  string productId = 6; // valid:"objectId"
  // 商品 SKU
  string sku = 7;
  // 商品数量
  uint64 count = 8;
  // 收货地址 ID
  string memberAddressId = 9;
  // 拆分后商品的信息
  repeated SplitProduct splitProducts = 10;
}

message OrderDetail {
  // 订单 ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 客户 ID
  string memberId = 3;
  // 订单编号
  string number = 4;
  // 支付方式
  string payment = 10;
  // 订单状态
  //
  // 订单状态值：unpaid 待付款，paid 已付款，shipped 已发货，completed 交易成功，canceled 交易关闭
  string status = 13;
  // 创建时间
  string createdAt = 19;
  // 更新时间
  string updatedAt = 20;
}

message Product {
  // 商品 ID
  string id = 1;
  // 商品名
  string name = 2;
  // product.id
  string productId = 3;
  // 商品图片
  string picture = 4;
  // 商品 sku
  string sku = 5;
  // 商品规格
  repeated string properties = 6;
  // 商品价格
  uint64 price = 7;
  // 商品数量
  uint64 count = 8;
  // 商品上下架状态
  string status = 9;
  // 库存
  uint64 stock = 10;
  // 限购
  mairpc.common.ec.PurchaseLimit purchaseLimit = 11;
  // 已购数量
  //
  // 当前周期已购数量，如果没有限购，则计数为 0
  uint64 purchasedCount = 12;
  repeated Campaign campaigns = 13;
  repeated string campaignTags = 14;
  string type = 15;
  string subType = 16;
}

message Campaign {
  string id = 1;
  string title = 2;
  string type = 3;
  // 额外参数
  map<string, string> extra = 13;
}

message BatchTopStoreProductRequest {
  // 商品 ids
  repeated string productIds = 1;
  // 门店 id
  string storeId = 2;
  // 置顶
  bool top = 3;
}

message GetRetailerBillStatsRequest {
  // @required
  //
  // 门店 id
  string storeId = 1; // valid:"objectId"
  // @required
  //
  // 账单类型，income 收入，spent 支出
  string type = 2; // valid:"in(income|spent)"
  // 收入类型
  //
  // 可选值: distribution 经销货款，consignment 代销货款，consignmentCommission 代销分佣，paymentFee 支付手续费，platformFee 平台服务费
  string incomeType = 3;
  // 账单创建时间
  mairpc.common.types.DateRange createdTime = 4;
  // 账单主体类型
  //
  // 可选值: brand 品牌方，distributor 经销商，retail 零售商，paymentServicer 支付服务商，platform 平台方，maimeng 脉盟新商
  string subjectType = 5;
  // 账单主体子类型
  //
  // 可选值: 零售商子类型: chain 连锁零售商，store 小店；支付服务商子类型: yeepay 易宝
  string subjectSubType = 6;
  // 账单状态
  //
  // 可选值：pending 待结算，processing 结算中，success 结算成功，failed 结算失败，refunded 订单全额退款，无需结算
  repeated string status = 7; // valid:"in(pending|processing|success|failed|refunded)"
  // 账单主体 id
  //
  // 主体为经销商/脉盟新商时为 distributionDistributor._id
  // 主体为品牌方时为 productBrand._id
  // 主体为连锁零售商时为连锁零售商租户 ID
  // 主体为导购时为 staffId
  string subjectId = 8; // valid:"objectId"
  // 账单结算完成时间
  //
  // 状态变成结算完成的时间；收入/支出时间
  mairpc.common.types.StringDateRange finishedTime = 9;
  // 订单类型
  //
  // 可选值：distribution 经销订单，consignment 代销订单
  string orderType = 10; // valid:"in(distribution|consignment)"
  // 搜索关键字
  string searchKey = 11;
  // 关键字类型
  //
  // 传不同的类型会将 searchKey 按不同的字段进行查询，可传字段如下：
  // order: 按订单编号、交易单号查询
  // store: 按小店名称查询
  // storeOwner: 按店主名称查询
  // subject: 按账单主体名称、主体商户号查询
  string searchKeyType = 12;
}

message ListRetailerBillsRequest {
  // @required
  //
  // 门店 id
  string storeId = 1; // valid:"objectId"
  // @required
  //
  // 账单类型，income 收入，spent 支出
  string type = 2; // valid:"in(income|spent)"
  // 收入类型
  //
  // 可选值: distribution 经销货款，consignment 代销货款，consignmentCommission 代销分佣，paymentFee 支付手续费，platformFee 平台服务费，consignmentStaffCommission 导购代销分佣，receipt 收单
  // 传多个值需用逗号分隔
  string incomeType = 3;
  // 支出类型
  // 可选值: distribution 经销货款，consignment 代销货款，consignmentCommission 代销分佣，paymentFee 支付手续费，platformFee 平台服务费，consignmentStaffCommission 导购代销分佣
  // 传多个值需用逗号分隔
  string spentType = 4;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 5;
  // 账单创建时间
  mairpc.common.types.DateRange createdTime = 6;
  // 账单主体类型
  //
  // 可选值: brand 品牌方，distributor 经销商，retail 零售商，paymentServicer 支付服务商，platform 平台方，maimeng 脉凯盟，staff 导购分佣
  string subjectType = 7;
  // 账单主体子类型
  //
  // 可选值: 脉凯盟子类型: maimengRetail 脉盟新商，maimengStore 脉盟小店；零售商子类型: chain 连锁零售商，store 小店；支付服务商子类型: yeepay 易宝；平台方子类型: maimeng 脉盟平台
  string subjectSubType = 8;
  // 账单主体名称
  string subjectName = 9;
  // 账单主体 id
  //
  // 主体为经销商/脉盟新商时为 distributionDistributor._id
  // 主体为品牌方时为 productBrand._id
  // 主体为连锁零售商时为连锁零售商租户 ID
  // 主体为导购时为 staffId
  string subjectId = 10; // valid:"objectId"
  // 账单主体商户号
  //
  // 相应主体的商户号，支付服务商没有商户号
  string subjectMerchantNumber = 11;
  // 账单主体手机号
  //
  // 主体为小店时为店长手机号
  string subjectPhone = 12;
  // 门店 ids
  repeated string storeIds = 13;
  // 搜索关键词
  string searchKey = 14;
  // 关键字类型
  //
  // 传不同的类型会将 searchKey 按不同的字段进行查询，可传字段如下：
  // order: 按订单编号、交易单号、分账明细单号查询
  // store: 按小店名称查询
  // storeOwner: 按店主名称查询
  // subject: 按账单主体名称、主体商户号查询
  string searchKeyType = 15;
  // 订单类型
  //
  // 可选值：distribution 经销订单，consignment 代销订单
  string orderType = 16;
  // 账单状态
  //
  // 可选值：pending 待结算，processing 结算中，success 结算成功，failed 结算失败，refunded 订单全额退款，无需结算
  repeated string status = 17; // valid:"in(pending|processing|success|failed|refunded)"
  // 账单金额
  mairpc.common.types.IntegerRange amount = 18;
  // 账单结算完成时间
  //
  // 状态变成结算完成的时间；收入/支出时间
  mairpc.common.types.StringDateRange finishedTime = 19;
  // 账单结算发起时间
  //
  // 状态从待结算变成结算中的时间
  mairpc.common.types.StringDateRange processingTime = 20;
  // 代销订单所属经销商 ID
  //
  // orderType 为 consignment 时，才存在; distributionDistributor._id
  string orderDistributorId = 21; // valid:"objectId"
  // 代销订单所属品牌 ID
  //
  // orderDistributorId 为 D0 时，才可能存在; productBrand._id
  string orderBrandId = 22; // valid:"objectId"
  // 是否跨租户查询
  //
  // 订货商城和脉盟新零售相关零售账单查询需要跨租户
  // 零售账单包括脉盟小店租户账单和连锁零售商租户账单
  bool ignoreAccountId = 23;
  // D1 经销商 ID
  //
  // 仅供 D1 经销商订货商城后台分账管理分账账单中查询代销账单使用
  // 因为默认的查询条件包含 or，需单独支持
  string d1DistributorId = 24; // valid:"objectId"
  // 导出设置
  mairpc.common.types.ExportInfo export = 25;
  // 订单 ID
  //
  // ec.order._id
  string orderId = 26; // valid:"objectId"
  // 账单主体 id 数组
  repeated string subjectIds = 27; // valid:"objectIdList"
  string transferBillId = 28; // valid:"objectId"
  // 忽略的收入类型
  repeated string ignoreIncomeTypes = 29;
  // 代销订单所属品牌 ID
  repeated string orderBrandIds = 30; // valid:"objectIdList"
  // 订单区域
  //
  // 多个区域使用json字符串
  string locations = 32;
  // 退款状态
  //
  // 可选值：refunded 已退款，noRefund
  string refundStatus = 33; // valid:"in(refunded|noRefund)"
}

message ListStaffRetailerBillsRequest {
  // @required
  //
  // 客户 id
  string memberId = 1; // valid:"objectId"
  // 账单状态
  //
  // 可选值：pending 待结算，processing 结算中，success 结算成功，failed 结算失败，refunded 订单全额退款，无需结算
  repeated string status = 2; // valid:"in(pending|processing|success|failed|refunded)"
  // 账单结算完成时间
  //
  // 状态变成结算完成的时间；收入/支出时间
  mairpc.common.types.StringDateRange finishedTime = 3;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 4;
}

message ListRetailerBillsResponse {
  // 数量
  uint64 total = 1;
  // 列表
  repeated RetailerBill items = 2;
}

message RetailerBill {
  string id = 1;
  string accountId = 2;
  string storeId = 3;
  // 账单类型，income 收入，spent 支出
  string type = 4;
  string incomeType = 5;
  // 账单总金额
  uint64 totalAmount = 6;
  Order order = 7;
  // 账单结算状态
  //
  // pending 待结算，processing 结算中，success 结算成功，failed 结算失败，refunded 订单全额退款，无需结算
  string status = 8;
  // 账单主体
  Account subject = 9;
  // 账单主体相对方
  Account opposite = 10;
  // 结算完成时间
  //
  // 状态变成结算完成的时间
  string finishedAt = 11;
  // 支出类型
  string spentType = 12;
  // 结算失败原因
  string failedMessage = 13;
  // 小店信息
  Store store = 14;
  // 该账单关联的支付手续费
  //
  // 当查询主体为小店的收入账单时才会返回
  uint64 paymentFee = 15;
  // 该账单关联的所有平台服务费
  //
  // 当查询主体为小店的收入账单时才会返回
  uint64 platformFee = 16;
  // 代销订单所属经销商名称
  //
  // 当查询主体为小店的代销分佣收入账单时才会返回
  string distributorName = 17;
  // 结算发起时间
  //
  // 状态从待结算变成结算中的时间
  string processingAt = 18;
  // 关联预分账表 ID
  //
  // ec.orderReceiverProfit._id
  string orderReceiverProfitId = 19;
  // 关联分账记录表 ID
  //
  // ec.transferBill._id
  string transferBillId = 20;
  // 收入账单类目
  //
  // 根据 incomeType 和 subject 判断
  string incomeTypeDesc = 21;
  // 易宝分账明细单号
  //
  // 仅分账成功的账单才返回
  string detailId = 22;
  // 该账单关联的导购佣金
  //
  // 当查询主体为小店的经销订单对应的收入账单时才会返回
  uint64 staffAmount = 23;
  // D1 经销商 ID,下单门店入驻经销商 ID
  string dOneDistributorId = 24;
  // 退款状态
  //
  // 可取值：partialRefund（部分退款），refund 全部退款
  string refundStatus = 25;
  // 账单对应订单累计退款金额
  uint64 refundAmount = 26;
  // 最近一次退款时间
  string refundAt = 27;
}

message Order {
  // 订单 id
  string id = 1;
  // 订单编号
  string number = 2;
  // 易宝交易单号
  string tradeNo = 3;
  Member member = 4;
  // 支付时间
  string paidAt = 5;
  // 订单类型
  //
  // distribution 经销订单，consignment 代销订单
  string type = 6;
  // 代销订单所属经销商 ID
  string distributorId = 7;
  // D0 代销订单所属品牌 id
  string brandId = 8;
  // 订单金额
  uint64 totalAmount = 9;
  // 分销员 id
  string promoterId = 10;
  // 分销员姓名
  string promoterName = 11;
}

message Member {
  // 客户 id
  string id = 1;
  // 客户名称
  string name = 2;
}

message Account {
  // 账单主体/相对方类型
  string type = 1;
  // 账单主体/相对方子类型
  string subType = 2;
  // 账单主体/相对方 id
  string id = 3;
  // 账单主体/相对方名称
  string name = 4;
  // 账单主体/相对方商户号
  string merchantNumber = 5;
  // 账单主体/相对方手机号
  string phone = 6;
  // 账单主体/相对方商户名称
  string merchantName = 7;
}

message Store {
  // 小店名称
  string name = 1;
  // 店主名称
  string contactName = 2;
  // 店主手机号
  string contactPhone = 3;
}

message GetRetailerBillStatsResponse {
  // 账单数量
  uint64 total = 1;
  // 金额合计
  uint64 totalAmount = 2;
  // 待结算金额总数
  uint64 pendingTotal = 3;
  // 待结算账单金额合计
  uint64 pendingAmount = 4;
}

message PrePayRequest {
  // @required
  //
  // 订单ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 客户ID
  string memberId = 2; // valid:"required,objectId"
  // 买家微信openId
  //
  // 使用微信小程序支付时必填
  string openId = 3;
  // 渠道 ID
  //
  // 使用微信小程序支付时必填
  string channelId = 4;
  // 是否使用易宝聚合支付托管下单
  bool useTutelage = 5;
  // 自定义信息，json 格式的字符串
  string extra = 6;
}

message DivideBackRequest {
  // @required
  //
  // 订单 ID
  string id = 1; // valid:"required,objectId"
}

message DivideBackResponse {
  repeated DivideBackResponseItem items = 1;
}

message DivideBackResponseItem {
  // 分账详情
  DivideDetail divideDetail = 1;
  // 分账归还详情
  DivideBackDetail divideBackDetail = 2;
}

message DivideDetail {
  // 返回码
  //
  // OPR00000 成功
  string code = 1;
  // 返回描述信息
  string message = 2;
  // 发起方商编
  string parentMerchantNo = 3;
  // 商户编号
  string merchantNo = 4;
  // 商户收款请求号
  //
  // 订单 ID
  string orderId = 5;
  // 易宝收款订单号
  string uniqueOrderNo = 6;
  // 商户分账请求号
  //
  // transferBill._id
  string divideRequestId = 7;
  // 分账状态
  //
  // PROCESSING 处理中，SUCCESS 成功，FAIL 失败
  string status = 8;
  // 分账详情:
  //
  // JSON字符串；divideDetailNo 易宝分账明细单号，ledgerNo 分账接收方编号，amount 分账金额，divideDetailDesc 分账说明
  string divideDetail = 9;
  // 查询分账结果接口调用失败原因
  string failedReason = 10;
}

message DivideBackDetail {
  // 返回码
  //
  // OPR00000 成功
  string code = 1;
  // 返回描述信息
  string message = 2;
  // 业务方标识
  string bizSystemNo = 3;
  // 发起方商编
  string parentMerchantNo = 4;
  // 商户编号
  string merchantNo = 5;
  // 商户收款请求号
  //
  // 订单 ID
  string orderId = 6;
  // 易宝收款订单号
  string uniqueOrderNo = 7;
  // 商户分账请求号
  //
  // transferBill._id
  string divideRequestId = 8;
  // 商户分账资金归还请求号
  string divideBackRequestId = 9;
  // 易宝分账资金归还订单号
  string uniqueDivideBackNo = 10;
  // 分账资金归还明细
  string divideBackDetail = 11;
  // 分账资金归还状态
  //
  // PROCESSING 处理中，SUCCESS 归还成功，FAIL 归还失败
  string status = 12;
  // 分账归还接口调用失败原因
  string failedReason = 13;
}

message TriggerDivideRequest {
  // 订单 ID
  string id = 1; // valid:"objectId"
  // 订单编号
  string number = 2;
}

message ExportD2ROrdersRequest {
  // 为 order 时，按订单编号、订单id、交易单号查询
  string searchType = 1; // valid:"in(order)"
  // 搜索关键字
  string searchKey = 2;
  // 下单时间
  mairpc.common.types.DateRange createdTime = 3;
  // D2R 连锁零售商和脉盟小店 accountIds
  repeated string accountIds = 4;
}

message ListDiscountCampaignRecordsRequest {
  // @required
  //
  // 活动 id
  string campaignId = 1; // valid:"required,objectId"
  // 搜索类型
  //
  // 客户：member，订单：order
  string searchType = 2; // valid:"in(member|order)"
  string searchKey = 3;
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 4;
  // 租户 id
  string accountId = 5;
}

message GetStoreViewStatsRequest {
  // @required
  //
  // 店铺 id
  string storeId = 1; // valid:"required,objectId"
}

message GetStoreViewStatsResponse {
  // 店铺访问量
  uint64 viewTimes = 1;
}

message GetRetailerBillRequest {
  // @required
  //
  // 账单 id
  string id = 1; // valid:"required,objectId"
  // 是否忽略 accountId 条件
  bool ignoreAccountId = 2;
}
