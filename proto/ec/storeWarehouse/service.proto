syntax = "proto3";

package mairpc.ec.store_warehouse;

option go_package = "store_warehouse";

import "common/request/request.proto";
import "common/response/response.proto";
import "ec/store/store.proto";
import "ec/storeWarehouse/store_warehouse.proto";

service StoreWarehouseService {
  // 获取库存设置
  rpc GetProductStockSetting(mairpc.common.request.EmptyRequest) returns (GetProductStockSettingResponse);
  // 修改库存设置
  rpc UpdateProductStockSetting(UpdateProductStockSettingRequest) returns (mairpc.common.response.EmptyResponse);
  // 门店库存列表
  rpc ListProductStocks(ListProductStocksRequest) returns (ListProductStocksBySkuResponse);
  // 导出门店库存列表
  rpc ExportProductStocks(ListProductStocksRequest) returns (mairpc.common.response.JobResponse);
  // 库存流水记录列表
  rpc ListProductStockBills(ListProductStockBillsRequest) returns (ListProductStockBillsResponse);
  // 导出库存流水记录列表
  rpc ExportProductStockBills(ListProductStockBillsRequest) returns (mairpc.common.response.JobResponse);
  // 商品库存列表
  rpc ListGroupProductStocks(ListProductStocksRequest) returns (ListProductStocksResponse);
  // 导出商品库存列表
  rpc ExportGroupProductStocks(ListProductStocksRequest) returns (mairpc.common.response.JobResponse);
  // 新建商品库存入库
  rpc CreateStoreStockBill(CreateStockBillRequest) returns (CreateStockBillResponse);
  // 新建调拨入库
  rpc CreateAllocateStockBill(CreateStockBillRequest) returns (CreateStockBillResponse);
  // 批量导入单门店多商品出入库明细
  rpc ImportStoreProductsStockBills(ImportStockBillsRequest) returns (mairpc.common.response.JobResponse);
  // 批量导入多门店单商品出入库明细
  rpc ImportStoresProductStockBills(ImportStockBillsRequest) returns (mairpc.common.response.JobResponse);
  // 批量导入库存调拨出入库明细
  rpc ImportAllocateStockBills(ImportStockBillsRequest) returns (mairpc.common.response.JobResponse);
  // 同步门店商品库存，从 ec.storeProduct 同步库存到 ec.storeProductStock
  rpc SyncStoreProductStockFromStoreProduct(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量调整门店库存
  rpc BatchUpdateProductStocks(BatchUpdateProductStocksRequest) returns (mairpc.common.response.EmptyResponse);
  // 商品出入库明细分组列表
  rpc ListGroupProductStockBills(ListStoreProductStockBillsRequest) returns (ListStoreProductStockBillsResponse);
  // 导出商品出入库明细分组列表
  rpc ExportGroupProductStockBills(ListStoreProductStockBillsRequest) returns (mairpc.common.response.JobResponse);
  // 库存调拨出入库明细分组列表
  rpc ListGroupAllocateStockBills(ListGroupAllocateStockBillsRequest) returns (ListStoreProductStockBillsResponse);
  // 导出商品出入库明细分组列表
  rpc ExportGroupAllocateStockBills(ListGroupAllocateStockBillsRequest) returns (mairpc.common.response.JobResponse);
  // 商品出入库详情
  rpc GetProductStockBillDetail(GetProductStockBillDetailRequest) returns (GetProductStockBillDetailResponse);
  // 库存调拨出入库详情
  rpc GetAllocateStockBillDetail(GetAllocateStockBillDetailRequest) returns (GetAllocateStockBillDetailResponse);
  // 查询单个门店商品库存
  rpc GetSingleStoreStock(GetSingleStoreStockRequest) returns (ProductStock);
  // 查询库存调整出入库列表
  rpc ListManualProductStockBills(GetManualProductStockBillDetailRequest) returns (ListProductStockBillsResponse);
  // 商品变更时同步门店商品库存
  rpc HandleProductChanged(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 门店商品库存同步任务
  rpc SyncProductStockJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // sku适用门店
  rpc GetStoresBySku(GetStoreBySkusRequest) returns (mairpc.ec.store.ListStoresResponse);
  // 新增门店库存供应商
  rpc CreateStoreStockProvider(mairpc.common.request.StringRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除门店库存供应商
  rpc DeleteStoreStockProviderById(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 修改门店库存供应商
  rpc UpdateStoreStockProvider(UpdateStoreStockProviderRequest) returns (mairpc.common.response.EmptyResponse);
  // 门店库存供应商列表
  rpc ListStoreStockProvider(ListStoreStockProviderRequest) returns (ListStoreStockProviderResponse);
}
