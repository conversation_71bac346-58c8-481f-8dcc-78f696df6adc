type: google.api.Service
config_version: 3

http:
  rules:
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.CreateStoreStockBill
      post: /v2/ec/storeWarehouse/stockBills
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.CreateAllocateStockBill
      post: /v2/ec/storeWarehouse/allocate/stockBills
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ImportStoreProductsStockBills
      post: /v2/ec/storeWarehouse/stockBills/import
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ImportStoresProductStockBills
      post: /v2/ec/storeWarehouse/stockBills/productStores/import
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ImportAllocateStockBills
      post: /v2/ec/storeWarehouse/stockBills/allocate/import
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ListGroupProductStocks
      post: /v2/ec/storeWarehouse/productStocks/group
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ExportGroupProductStocks
      post: /v2/ec/storeWarehouse/productStocks/group/export
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ListProductStockBills
      get: /v2/ec/storeWarehouse/stockBills
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ExportProductStockBills
      post: /v2/ec/storeWarehouse/stockBills/export
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.GetProductStockSetting
      get: /v2/ec/storeWarehouse/productStockSetting
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ListProductStocks
      get: /v2/ec/storeWarehouse/productStocks
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ExportProductStocks
      post: /v2/ec/storeWarehouse/productStocks/export
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.UpdateProductStockSetting
      put: /v2/ec/storeWarehouse/productStockSetting
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.SyncStoreProductStockFromStoreProduct
      get: /v2/ec/storeWarehouse/syncStoreProductStockFromStoreProduct
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.SyncProductStockJob
      get: /v2/ec/storeWarehouse/syncProductStockJob
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.BatchUpdateProductStocks
      put: /v2/ec/storeWarehouse/productStocks
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ListGroupProductStockBills
      post: /v2/ec/storeWarehouse/stockBills/group
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ListGroupAllocateStockBills
      post: /v2/ec/storeWarehouse/allocate/group
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ExportGroupAllocateStockBills
      post: /v2/ec/storeWarehouse/allocate/group/export
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.GetAllocateStockBillDetail
      get: /v2/ec/storeWarehouse/allocate/stockBills/{businessNumber}
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.GetProductStockBillDetail
      get: /v2/ec/storeWarehouse/stockBills/{number}
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.GetSingleStoreStock
      get: /v2/ec/storeWarehouse/productStock
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ListManualProductStockBills
      get: /v2/ec/storeWarehouse/stockBills/operator/manual
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ExportGroupProductStockBills
      post: /v2/ec/storeWarehouse/stockBills/group/export
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.GetStoresBySku
      post: /v2/ec/storeWarehouse/stores
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.CreateStoreStockProvider
      post: /v2/ec/storeWarehouse/stockProvider
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.DeleteStoreStockProviderById
      delete: /v2/ec/storeWarehouse/stockProvider/{id}
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.UpdateStoreStockProvider
      put: /v2/ec/storeWarehouse/stockProvider
      body: '*'
      tags: ['私域商城-门店库存']
    - selector: mairpc.ec.store_warehouse.StoreWarehouseService.ListStoreStockProvider
      get: /v2/ec/storeWarehouse/stockProviders
      tags: ['私域商城-门店库存']
