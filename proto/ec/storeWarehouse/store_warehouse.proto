syntax = "proto3";

package mairpc.ec.store_warehouse;

option go_package = "store_warehouse";

import "common/request/request.proto";
import "common/types/types.proto";

message GetProductStockSettingResponse {
  // 是否开启库存预警
  bool isWarningEnabled = 1;
  // 固定预警阀值
  uint64 warningThreshold = 2;
  // 是否开启固定预警值
  bool isSettledWarningEnabled = 3;
  // 是否开启部分预警值
  bool isPartlyWarningEnabled = 4;
  // 是否开启库存
  bool isEnabled = 5;
  // 预警提示
  string warningTip = 6;
}

message UpdateProductStockSettingRequest {
  // 是否开启库存预警
  mairpc.common.types.BoolValue isWarningEnabled = 1;
  // 固定预警阀值
  mairpc.common.types.Int64Value warningThreshold = 2;
  // 是否开启固定预警值
  mairpc.common.types.BoolValue isSettledWarningEnabled = 3;
  // 是否开启部分预警值
  mairpc.common.types.BoolValue isPartlyWarningEnabled = 4;
  // 是否开启库存
  mairpc.common.types.BoolValue isEnabled = 5;
  // 预警提示
  mairpc.common.types.StringValue warningTip = 6;
}

message ListProductStocksRequest {
  // 查询参数
  //
  // 商品名，商品编码，商品条码，SKU 编码
  string queryString = 1;
  // 预警状态
  //
  // 库存不足（lacking）|库存正常（enough）
  string warningStatus = 2;
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 3;
  // 是否开启库存预警
  //
  // 用于库存预警页面查询
  bool isWarningEnabled = 4;
  // 商品上下架状态
  //
  // 已上架（shelved），待上架（scheduled），已下架（unshelved），未上架（initialized）
  repeated string productStatus = 5;
  // 商品类目 id
  repeated string categoryIds = 6; // valid:"objectIdList"
  // 商品品牌 id
  repeated string brandIds = 7; // valid:"objectIdList"
  // 商品标签
  repeated string tags = 8;
  // 查询门店参数
  //
  // 门店编号，门店名称
  string store = 9;
  // sku
  string sku = 10;
  // 门店 id
  string storeId = 11;
  // 商品 id
  string productId = 12; // valid:"objectId"
}

message ListProductStocksResponse {
  uint64 total = 1;
  repeated ProductStock items = 2;
}

message ProductStock {
  // 商品 id
  string productId = 1; // valid:"objectId"
  // 商品名
  string name = 2;
  // 商品编码
  string number = 3;
  // 商品条码
  string barCode = 4;
  // 商品 SKU 码
  string sku = 5;
  // 规格
  repeated string properties = 6;
  // 实物库存
  uint64 totalStock = 7;
  // 占用库存
  uint64 occupiedStock = 8;
  // 可售库存
  uint64 availableStock = 9;
  // 预警阀值
  int64 warningThreshold = 10;
  // 预警状态
  //
  // 库存不足（lacking）|库存正常（enough）
  string warningStatus = 11;
  string id = 12;
  // 外部 sku 码
  string external = 13;
  // 商品缩略图
  string thumbnailUrl = 14;
  // 商品成本价
  uint64 price = 15;
  // 预警提示
  string warningTip = 16;
  // 商品 SKU 条码
  string skuBarCode = 17;
  // 商品价格
  uint64 productPrice = 18;
}

message ListProductStocksBySkuResponse {
  uint64 total = 1;
  repeated StoreStock items = 2;
}

message StoreStock {
  // 门店名称
  string name = 1;
  // 门店编号
  string code = 2;
  // 门店地址
  Location location = 3;
  // 门店库存
  uint64 totalStock = 4;
  // 门店占用库存
  uint64 occupiedStock = 5;
  // 门店可售库存
  uint64 availableStock = 6;
  // 门店编号
  string storeId = 7;
}

message Location {
  //　省（直辖市）
  string province = 1;
  // 区
  string city = 2;
  // 镇
  string district = 3;
  // 详细地址
  string detail = 4;
  // 地址全称
  string fullName = 5;
}

message ListProductStockBillsRequest {
  // 查询条件
  //
  // 商品名称/商品编码/商品条码/SKU编码/业务编码
  string queryString = 1;
  // 商品 id
  string productId = 2; // valid:"objectId"
  // 商品 SKU 码
  string sku = 3;
  // 查询条件
  //
  // 门店名称/门店编号
  string store = 4;
  // 出入库类型
  //
  // 销售退货入库（refund），销售出库（ship），采购入库（purchase_in），调拨入库（allocate_in），调拨出库（allocate_out），退货给供应商（refund_to_supplier），其他原因（others）,手动调整（manual）
  string operator = 5; // valid:"in(refund|ship|purchase_in|allocate_in|allocate_out|refund_to_supplier|others|manual)"
  // 出入库时间
  mairpc.common.types.StringDateRange timeRange = 6;
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 7;
  // 变更类型
  string type = 8; //valid:"in(in|out)"
  // 变更人
  string updateBy = 9;
  // 查询条件
  //
  // 门店编号
  string storeId = 10;
  // 供应商
  repeated string providers = 11; // valid:"objectIdList"
}

message ListProductStockBillsResponse {
  uint64 total = 1;
  repeated ProductStockBill items = 2;
}

message ListStoreProductStockBillsResponse {
  uint64 total = 1;
  repeated StoreProductStockBill items = 2;
}

message GetAllocateStockBillDetailResponse {
  uint64 total = 1;
  repeated ProductStockBill items = 2;
  uint64 sumCount = 3;
}

message StoreProductStockBill {
  // 商品 id
  string productId = 1;
  // 商品名
  string productName = 2;
  // 单据编号
  string number = 3;
  // 出入库时间
  string changedAt = 4;
  // 业务编号
  string businessNumber = 5;
  // 出入库类型
  //
  // 销售退货入库（refund），销售出库（ship），采购入库（purchase_in），调拨入库（allocate_in），调拨出库（allocate_out），退货给供应商（refund_to_supplier），其他原因（others）
  string operator = 6;
  // 出入库数量
  uint64 count = 7;
  // 操作人
  string updatorName = 8;
  // 关联组织
  string organization = 9;
  // 变更类型
  string type = 10; //valid:"in(in|out)"
  // 订单 id
  //
  // 当 operator 为 refund，ship 的时候有意义
  string orderId = 11;
  // 客户 id
  //
  // 当 operator 为 refund，ship 的时候有意义
  string memberId = 12;
  // 门店名称/编号
  repeated string stores = 13;
  // 创建时间
  string createdAt = 14;
  // 入库门店信息
  UpdateStoreStock oppositeStore = 15;
  // 出库门店信息
  UpdateStoreStock store = 16;
  // 备注
  string remarks = 17;
  // 供应商
  Provider provider = 18;
}

message ListStoreProductStockBillsRequest {
  // 查询条件
  //
  // 商品名称/商品编码/商品条码/SKU编码/业务编码/单据编号
  string queryString = 1;
  // 商品 id
  string productId = 2; // valid:"objectId"
  // 商品 SKU 码
  string sku = 3;
  // 查询条件
  //
  // 出入库类型
  //
  // 销售退货入库（refund），销售出库（ship），采购入库（purchase_in），调拨入库（allocate_in），调拨出库（allocate_out），退货给供应商（refund_to_supplier），其他原因（others）
  string operator = 5; // valid:"in(refund|ship|purchase_in|allocate_in|allocate_out|refund_to_supplier|others)"
  // 出入库时间
  mairpc.common.types.StringDateRange timeRange = 6;
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 7;
  // 变更类型
  string type = 8; // valid:"in(in|out)"
  // 变更人
  string updateBy = 9;
  // 查询条件
  //
  // 门店名称/门店编号
  string store = 10;
  // 供应商
  repeated string providers = 11; // valid:"objectIdList"
}

message ProductStockBill {
  // 商品 id
  string productId = 1;
  // 商品名
  string productName = 2;
  // 单据编号
  string number = 3;
  // 出入库时间
  string changedAt = 4;
  // 业务编号
  string businessNumber = 5;
  // 出入库类型
  //
  // 销售退货入库（refund），销售出库（ship），采购入库（purchase_in），调拨入库（allocate_in），调拨出库（allocate_out），退货给供应商（refund_to_supplier），其他原因（others）
  string operator = 6;
  // 出入库数量
  uint64 count = 7;
  // 变更人
  string updatorName = 8;
  // 备注
  string remarks = 9;
  // 类型
  //
  // 入库（in）|出库（out）
  string type = 10;
  // 订单 id
  //
  // 当 operator 为 refund，ship 的时候有意义
  string orderId = 11;
  // 客户 id
  //
  // 当 operator 为 refund，ship 的时候有意义
  string memberId = 12;
  // 门店名称
  string storeName = 13;
  // 门店编号
  string storeCode = 14;
  // 门店Id
  string storeId = 15;
  // 门店可用库存
  uint64 availableStock = 16;
  // 商品规格
  repeated string properties = 17;
  // 商品编号
  string productNumber = 18;
  // 成本价
  uint64 price = 19;
  // 商品缩略图
  string thumbnailUrl = 20;
  // 商品价格
  uint64 productPrice = 21;
}

message CreateStockBillRequest {
  // @required
  //
  // 门店id
  repeated UpdateStoreStock stores = 1; // valid:"required"
  // 出入库时间
  string changedAt = 2;
  // 出入库类型
  //
  // 销售退货入库（refund），销售出库（ship），采购入库（purchase_in），调拨入库（allocate_in），调拨出库（allocate_out），退货给供应商（refund_to_supplier），其他原因（others）,手动调整(manual)
  string operator = 3; // valid:"in(refund|ship|purchase_in|allocate_in|allocate_out|refund_to_supplier|others|manual)"
  // 业务编号
  string businessNumber = 4;
  // 备注
  string remarks = 5;
  // @required
  //
  // 入库（in）|出库（out）
  string type = 6; // valid:"required,in(in|out)"
  // 商品列表
  repeated StockBillProduct products = 7;
  // 出入库流水编号
  string number = 8;
  // 反方向操作门店
  UpdateStoreStock oppositeStore = 9;
  // 供应商
  string provider = 10;
}

message StockBillProduct {
  // 商品 id
  string id = 1;
  // 商品 sku 码
  string sku = 2;
  // 出入库数量
  uint64 count = 3;
  // 门店 id
  string storeId = 4;
  // 成本价
  uint64 price = 5;
}

message CreateStockBillResponse {
  // 出入库失败商品列表
  repeated StockBillProduct failedProducts = 1;
}

message BatchUpdateProductStocksRequest {
  // @required
  //
  // 门店信息
  repeated UpdateStoreStock items = 1; // valid:"required"
  // 商品 id
  string productId = 2; // valid:"required,objectId"
  // @required
  //
  // 商品 sku 码
  string sku = 3; // valid:"required"
  // 调整库存类型
  string type = 4; // valid:"required,in(in|out)"
  // @required
  //
  // 调整数量
  uint64 count = 5; // valid:"required"
  // 备注
  string remarks = 6;
  // 出入库时间
  string changedAt = 7;
  // 成本价
  uint64 price = 8;
  // 供应商
  string provider = 9;
}

message UpdateStoreStock {
  // @required
  //
  // 门店 ID
  string id = 1; // valid:"required,objectId"
  // 门店编码
  string code = 2;
  // 门店名称
  string name = 3;
}

message GetProductStockBillDetailRequest {
  // @required
  //
  // 单据编号
  string number = 1; // valid:"required"
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 2;
}

message GetManualProductStockBillDetailRequest {
  // @required
  //
  // 单据编号
  string sku = 1; // valid:"required"
  // @required
  //
  // 单据编号
  string productId = 2; // valid:"required,objectId"
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 3;
}

message GetProductStockBillDetailResponse {
  uint64 total = 1;
  repeated ProductStockBillDetail items = 2;
}

message ProductStockBillDetail {
  // 商品名
  string productName = 1;
  // 商品规格
  repeated string properties = 2;
  // 总仓库存（调整后库存）
  uint64 totalStock = 3;
  // 可售库存
  uint64 availableStock = 4;
  // 出入库数量
  //
  // 负数表示出库
  int64 changedCount = 5;
  // 门店信息
  UpdateStoreStock store = 6;
  // 商品 id
  string productId = 7;
  // sku
  string sku = 8;
  // 单据编号
  string number = 9;
  // 出入库时间
  string changedAt = 10;
  // 业务编号
  string businessNumber = 11;
  // 出入库类型
  //
  // 销售退货入库（refund），销售出库（ship），采购入库（purchase_in），调拨入库（allocate_in），调拨出库（allocate_out），退货给供应商（refund_to_supplier），其他原因（others）
  string operator = 12;
  // 出入库数量
  uint64 count = 13;
  // 操作人
  string updatorName = 14;
  // 关联组织
  string organization = 15;
  // 变更类型
  string type = 16; //valid:"in(in|out)"
  // 订单 id
  //
  // 当 operator 为 refund，ship 的时候有意义
  string orderId = 17;
  // 客户 id
  //
  // 当 operator 为 refund，ship 的时候有意义
  string memberId = 18;
  // 占用库存
  string occupiedStock = 19;
  // 入库门店数
  uint64 storeCount = 20;
  // 单店出入库数量
  int64 singleStoreInventoryChange = 21;
  // 总出入库数量
  int64 totalInventoryChange = 22;
  // 成本价
  uint64 price = 23;
  // 商品价格
  uint64 productPrice = 24;
  // 供应商
  Provider provider = 25;
}

message Provider {
  // 供应商 id
  string id = 1;
  // 供应商名称
  string name = 2;
}

message ImportStockBillsRequest {
  // @required
  //
  // 文件路径
  string fileUrl = 1; // valid:"required"
  // 类型
  //
  // 入库（in）|出库（out）
  string type = 2; // valid:"required,in(in|out)"
}

message GetSingleStoreStockRequest {
  // @required
  //
  // 门店Id
  string storeId = 1; // valid:"required,objectId"
  // @required
  //
  // ec.productId
  string productId = 2; // valid:"required,objectId"
  // @required
  //
  // 商品sku
  string sku = 3; // valid:"required"
}

message GetStoreBySkusRequest {
  // @required
  //
  // sku
  string sku = 1; // valid:"required"
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 2;
  // 关键字搜索
  //
  // 门店名称/门店编号
  string searchKey = 3;
  // 地址
  //
  // 最小粒度为市
  repeated mairpc.common.types.Location locations = 4;
  // 忽略的 subTypes
  repeated int32 ignoreSubTypes = 5;
  // 门店级别 ids
  repeated string storeLevelIds = 6; // valid:"objectIdList"
  // 门店类型 ids
  repeated string storeTypeIds = 7; // valid:"objectIdList"
  // 启用状态
  mairpc.common.types.BoolValue isEnabled = 8;
  // 筛选所属组织节点
  mairpc.common.types.DistributorSelector distributor = 9; // valid:"optional"
  // 门店标签
  repeated string tags = 10;
  // 门店 type，可取值 2、3，2 表示经销商, 3 表示零售门店
  repeated int32 types = 11;
}

message ListGroupAllocateStockBillsRequest {
  // 查询条件
  //
  // 商品名称
  string productName = 1;
  // 调拨单据编号
  string businessNumber = 2;
  // 出库门店名称/门店编号
  string store = 3;
  // 入库门店名称/门店编号
  string oppositeStore = 4;
  mairpc.common.types.StringDateRange timeRange = 6;
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 7;
  // 出库门店Id
  string storeId = 8;
  // 入库门店Id
  string oppositeStoreId = 9;
}

message GetAllocateStockBillDetailRequest {
  // @required
  //
  // 调拨单据编号
  string businessNumber = 1; // valid:"required"
  // 查询条件
  //
  // 商品名称/商品编码/商品条码
  string queryString = 2;
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 3;
}

// 门店库存供应商
message StoreStockProvider {
  string id = 1;
  string accountId = 2;
  string name = 3;
  string createdAt = 4;
  string updatedAt = 5;
}

message StoreStockProviderResponse {
  string id = 1;
  string accountId = 2;
  string name = 3;
  string createdAt = 4;
  string updatedAt = 5;
}

// 修改门店库存供应商
message UpdateStoreStockProviderRequest {
  string id = 1; // valid:"required,objectId"
  string name = 2; // valid:"required"
}

// 门店库存供应商列表
message ListStoreStockProviderRequest {
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 1;
  // 可选：名称模糊搜索
  string queryString = 2;
}

message ListStoreStockProviderResponse {
  int32 total = 1;
  repeated StoreStockProviderResponse list = 2;
}
