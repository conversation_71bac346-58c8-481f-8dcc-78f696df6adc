syntax = "proto3";

package mairpc.ec.chain_retail;

option go_package = "chain_retail";

import "common/ec/brand.proto";
import "common/ec/chain_retail.proto";
import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/response/response.proto";
import "ec/chainRetail/chain_retail.proto";
import "ec/chainRetail/consignment_product.proto";
import "ec/chainRetail/distribution_product.proto";
import "ec/chainRetail/micro_ruwang.proto";
import "ec/chainRetail/task.proto";
import "ec/retailer/storeProduct.proto";

service ChainRetailService {
  rpc Demo(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取购物车商品
  rpc ListCartGroups(ListCartGroupsRequest) returns (ListCartGroupsResponse);
  // 处理连锁零售商状态变更事件
  rpc HandleRetailerStatusChanged(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 代销品选品后同步代销商品
  //
  // 连锁零售商代销品管理选品库批量上架、批量定时上架操作
  rpc SyncConsignmentProduct(SyncConsignmentProductRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步代销商品变更
  //
  // 经销商更新代销商品时，同步更新连锁零售商里的代销品
  rpc SyncConsignmentProductChanges(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 计算订单金额
  rpc CalculateOrderAmount(mairpc.common.ec.CalculateChainRetailOrderAmountRequest) returns (mairpc.common.ec.CalculateChainRetailOrderAmountResponse);
  // 直接购买时计算订单金额
  rpc CalculateOrderAmountWithPurchase(mairpc.common.ec.CalculateOrderAmountRequest) returns (mairpc.common.ec.CalculateOrderAmountResponse);
  // 创建订单
  rpc CreateOrder(mairpc.common.ec.CreateChainRetailOrderRequest) returns (OrderDetail);
  // 创建或更新易宝入网（小微）记录
  rpc UpsertMicroRuwangRecord(MicroRuwangDetail) returns (mairpc.common.response.EmptyResponse);
  // 获取易宝入网（小微）记录详情
  rpc GetMicroRuwangRecord(GetMicroRuwangRecordRequest) returns (MicroRuwangRecordResponse);
  // 易宝入网（小微）
  //
  // 门店员工入驻易宝特约商户（小微）
  rpc MicroRuwang(MicroRuwangDetail) returns (mairpc.common.response.EmptyResponse);
  // 易宝入网（小微）回调
  rpc MicroRuwangNotify(mairpc.ec.retailer.YeePayNotify) returns (mairpc.common.response.EmptyResponse);
  // 同步易宝入网状态
  rpc SyncMicroRuwangRecordStatus(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理超时未支付的代销订单
  rpc HandleUnpaidConsignmentOrder(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步经销商发布的经销商品
  rpc SyncDistributionProduct(SyncDistributionProductRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新经销商品导购佣金
  rpc UpdateProductStaffProfitAmount(UpdateProductStaffProfitAmountRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取品牌列表
  //
  // 除了连锁零售商已有的品牌还包括合作中经销商的品牌
  rpc ListBrands(mairpc.common.ec.ListBrandsRequest) returns (mairpc.common.ec.ListBrandsResponse);
  // 获取已存在的经销商信息
  //
  // 连锁零售商入驻过的经销商
  rpc GetChainRetailDistributors(mairpc.common.request.EmptyRequest) returns (ChainRetailDistributorsResponse);
  // 获取导购任务列表
  rpc ListStaffTasks(ListStaffTasksRequest) returns (ListStaffTasksResponse);
  // 获取导购任务详情
  rpc GetStaffTask(GetStaffTaskRequest) returns (StaffTaskDetail);
  // 导购完成任务
  rpc CompleteStaffTask(CompleteStaffTaskRequest) returns (CompleteStaffTaskResponse);
  // 导购下载素材，同一个导购下载多次，记为一次
  rpc DownloadMaterials(DownloadMaterialRequest) returns (mairpc.common.response.EmptyResponse);
  // 导购任务通知
  rpc StaffNotify(StaffNotifyRequest) returns (mairpc.common.response.EmptyResponse);
  // 导购任务已读
  rpc StaffTaskRead(StaffTaskReadRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除导购个人任务
  rpc DeleteStaffTask(mairpc.common.request.StringIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理分账回退记录
  rpc HandleDivideBackRecord(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
}
