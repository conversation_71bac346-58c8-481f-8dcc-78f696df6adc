syntax = "proto3";

package mairpc.ec.chain_retail;

option go_package = "chain_retail";

message MicroRuwangDetail {
  // @required
  //
  // 员工 ID
  string staffId = 1; // valid:"objectId"
  // @required
  //
  // 客户 ID
  string memberId = 2; // valid:"objectId"
  // @required
  //
  // 手机号 ID
  string phone = 3; // valid:"phone"
  // @required
  //
  // 验证码
  string code = 4;
  // @required
  //
  // 银行卡信息
  BankAccountInfo bankAccountInfo = 5;
  // @required
  //
  // 身份证信息
  IdCardInfo idCardInfo = 6;
  // 是否注册
  bool isRegister = 7;
  // 记录 ID
  string id = 8; // valid:"objectId"
  // 商户类型 micro(分账接收方) merchant(入驻商户)，不传时默认用 micro
  string type = 9; // valid:"in(micro,merchant)"
}

message BankAccountInfo {
  // 开户银行
  string accountBank = 1;
  // 开户名称
  string accountName = 2;
  // 银行卡号
  string accountNumber = 3;
  // 对公账户类型
  //
  // 可用值:DEBIT_CARD,ENTERPRISE_ACCOUNT,PASSBOOK,UNIT_SETTLEMENT_CARD
  string bankAccountType = 4;
  // 开户银行省市编码
  string bankAddressCode = 5;
  // 开户银行全称
  string bankName = 6;
  // 结算方向
  //
  // 可用值:ACCOUNT,BANKCARD
  string settlementDirection = 7;
}

message IdCardInfo {
  // 身份证有效期开始时间
  string cardPeriodBegin = 1;
  // 身份证有效期失效时间
  string cardPeriodEnd = 2;
  // 身份证居住地址
  string idCardAddress = 3;
  // 身份证人面像
  string idCardCopy = 4;
  // 身份证姓名
  string idCardName = 5;
  // 身份证国徽面
  string idCardNational = 6;
  // 身份证号码
  string idCardNumber = 7;
}

message GetMicroRuwangRecordRequest {
  // @required
  //
  // 员工 ID / ec.yeepayMicroRuwangRecord._id
  string id = 1; // valid:"required,objectId"
}

message MicroRuwangRecordResponse {
  string id = 1;
  string accountId = 2;
  bool isDeleted = 3;
  // 员工 ID
  string staffId = 4;
  // 客户 ID
  string memberId = 5;
  // 手机号
  string phone = 6;
  // 商户号
  string merchantNo = 7;
  // 银行卡信息
  BankAccountInfo bankAccountInfo = 8;
  // 身份证信息
  IdCardInfo idCardInfo = 9;
  // 申请状态
  //
  // SUBMITTED(已提交，等易宝审核),REVIEWING(申请审核中),REVIEW_BACK(申请已驳回),BUSINESS_OPENING(业务开通中),COMPLETED(申请已完成)
  string status = 10;
  // “申请已驳回”或者“申请已完成”时，回传的审核意见
  string auditOpinion = 11;
}
