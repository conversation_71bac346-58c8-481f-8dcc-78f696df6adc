syntax = "proto3";

package mairpc.ec.marketing;

option go_package = "marketing";

import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/types/types.proto";

message CreateFlashSaleCampaignRequest {
  // @required
  //
  // 活动标题
  string title = 1; // valid:"required"
  // @required
  //
  // 活动周期类型 可选值： 单次（once）、周（weekly）、月（monthly）
  string periodType = 2; // valid:"required,in(once|weekly|monthly)"
  // 周、月周期性活动日
  //
  // 可选值：周（1-7）、月（1-28/29/30/31）
  repeated uint64 days = 3;
  // 周期内活动开始时间段
  //
  // 参数示例：8:00:00、14:00:00
  repeated HourTimeSpan hourTimeSpans = 4;
  // 单次活动开始时间
  //
  // 时间格式需要满足 ISO 8601 标准
  string startAt = 5;
  // 单次活动结束时间
  //
  // 时间格式需要满足 ISO 8601 标准
  string endAt = 6;
  // 分享描述
  string shareDescription = 7;
  // 分享图片
  string sharePicture = 8;
  // @required
  //
  // 秒杀商品
  string productId = 9; // valid:"required,objectId"
  // @required
  //
  // 秒杀商品 skus
  repeated FlashSaleSku skus = 10; // valid:"required"
  // 活动门店
  repeated FlashSaleStore stores = 11;
  // 每人限购
  uint64 limit = 12;
  // 秒杀预约
  FlashSaleReservation reservation = 13;
  // 优惠券补贴
  FlashSaleCoupon coupon = 14;
  // @required
  //
  // 活动范围
  string scope = 15; // valid:"required,in(all|limit|import)"
  // 是否限制门店库存
  bool isLimitStoreStock = 16;
  // 添加门店方式
  //
  // select（选择门店）、import（导入门店）
  string addStoreType = 17; // valid:"in(select|import)"
  // 是否包邮
  bool isFreeShipping = 18;
  // 是否可以使用优惠券
  bool canUseCoupon = 19;
  // 是否可以使用储值卡
  bool canUsePrepaidCard = 20;
  // 是否只能使用一种优惠
  bool isDiscountLimit = 21;
  // 参与人群限制
  mairpc.common.ec.MemberFilter memberLimit = 22;
  // 使用优惠券限制
  mairpc.common.ec.CouponLimit couponLimit = 23;
  // 活动类型名称
  string typeName = 24;
  // 秒杀价名称
  string priceName = 25;
  // 是否开启剩余库存展示
  bool isStockDisplayEnabled = 26;
  // 储值卡限制
  mairpc.common.ec.PrepaidCardLimit prepaidCardLimit = 27;
}

message ListFlashSaleCampaignRecordsRequest {
  string memberId = 1;
  // 活动 id
  string campaignId = 2;
  // 关键字类型
  //
  // 可选值 order（按订单编号）、member（按会员姓名）
  string searchType = 3; // valid:"in(order|member)"
  // 搜索关键字
  string searchKey = 4;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 5;
  // 活动时间
  mairpc.common.types.StringDateRange createdTime = 6;
  // 门店 id
  string storeId = 7;
}

message ListFlashSaleCampaignRecordsResponse {
  uint64 total = 1;
  repeated FlashSaleCampaignsRecordDetail items = 2;
}

message FlashSaleCampaignsRecordDetail {
  string memberId = 1;
  string memberName = 2;
  string orderNumber = 3;
  string createdAt = 4;
  string orderId = 5;
}

message ListMembershipDiscountsByCampaignRequest {
  string memberId = 1;
  // 活动 id
  string campaignId = 2;
  // 关键字类型
  //
  // 可选值 order（按订单编号）、member（按会员姓名）
  string searchType = 3; // valid:"in(order|member)"
  // 搜索关键字
  string searchKey = 4;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 5;
  // 活动时间
  mairpc.common.types.StringDateRange createdTime = 6;
  // 门店 id
  string storeId = 7;
  // 优惠券状态
  //
  // 已使用(used)未使用(unused)已过期(expired)已转赠(gifted)已失效(invalid)
  string status = 8;
}

message ListMembershipDiscountsByCampaignResponse {
  uint64 total = 1;
  uint64 redeemedTotal = 2;
  uint64 restTotal = 3;
  repeated CampaignMembershipDiscountDetail items = 4;
}

message CampaignMembershipDiscountDetail {
  string memberId = 1;
  string memberName = 2;
  string createdAt = 3;
  string redeemedAt = 4;
}

message HourTimeSpan {
  // 活动开启时间（小时）
  string startTime = 1;
  // 活动结束时间（小时）
  string endTime = 2;
}

message TimeSpan {
  // 活动开始时间
  string startAt = 1;
  // 活动结束时间
  string endAt = 2;
}

message FlashSaleCoupon {
  // 是否开启优惠券补贴
  bool isEnabled = 1;
  // 补贴优惠券 id
  string couponId = 2;
  // 发放总量
  uint64 totalStock = 3;
}

message FlashSaleReservation {
  bool isEnabled = 1;
  // 预约方式
  //
  // 可选值： 关注微信公众号/服务号（subscribe_wechat）、用户填写信息（form）、无门槛预约（unlimit）
  string type = 2; // valid:"required,in(subscribe_wechat|form|unlimit)"
  // 用户预约填写信息
  //
  // cep 客户属性 id
  repeated string memberPropertyIds = 3;
  // 公众号对应 channelId
  string channelId = 4;
  // 是否允许未预约客户参加
  bool isAllowedUnReservationMemberJoin = 5;
}

message FlashSaleStore {
  // @required
  //
  // 活动门店 id
  string storeId = 1; // valid:"required,objectId"
  // @required
  //
  // 活动门店商品 sku
  repeated FlashSaleStoreSku skus = 2;
  // 门店 code
  string code = 3;
}

message FlashSaleStoreDetail {
  string storeId = 1;
  string name = 2;
  repeated FlashSaleStoreSkuDetail skus = 3;
  string code = 4;
}

message FlashSaleStoreSku {
  // @required
  //
  // 秒杀商品 sku
  string sku = 1; // valid:"required"
  // @required
  //
  // 库存总量
  uint64 totalStock = 2; // valid:"required"
}

message FlashSaleStoreSkuDetail {
  string sku = 1;
  repeated string properties = 2;
  uint64 totalStock = 3;
  uint64 stock = 4;
}

message FlashSaleSku {
  // @required
  //
  // 秒杀商品 sku
  string sku = 1; // valid:"required"
  // @required
  //
  // 商品划线价格
  uint64 originalPrice = 2; // valid:"required"
  // @required
  //
  // 秒杀价格
  uint64 price = 3; // valid:"required"
  // @required
  //
  // 库存总量
  uint64 totalStock = 4; // valid:"required"
}

message UpdateFlashSaleCampaignRequest {
  // @required
  string id = 1; // valid:"required"
  string title = 2;
  // 活动开始时间
  //
  // 时间格式需要满足 ISO 8601 标准
  string startAt = 3;
  // 活动结束时间
  //
  // 时间格式需要满足 ISO 8601 标准
  string endAt = 4;
  // @required
  //
  // 秒杀商品 skus
  repeated FlashSaleSku skus = 5;
  // 活动门店
  repeated FlashSaleStore stores = 6;
  // 每人限购
  uint64 limit = 7;
  // 优惠券补贴
  FlashSaleCoupon coupon = 8;
  // 是否结束活动
  mairpc.common.types.BoolValue isStopped = 9;
  // @required
  //
  // 活动周期类型 可选值： 单次（once）、周（weekly）、月（monthly）
  string periodType = 10; // valid:"in(once|weekly|monthly)"
  // 周、月周期性活动日
  //
  // 可选值：周（1-7）、月（1-28/29/30/31）
  repeated uint64 days = 11;
  // 周期内活动开始时间段
  repeated HourTimeSpan hourTimeSpans = 12;
  // 是否包邮
  bool isFreeShipping = 13;
  // 秒杀预约
  FlashSaleReservation reservation = 14;
  // 是否可以使用优惠券
  bool canUseCoupon = 15;
  // 是否可以使用储值卡
  bool canUsePrepaidCard = 16;
  // 是否只能使用一种优惠
  bool isDiscountLimit = 17;
  // 参与客户限制
  mairpc.common.ec.MemberFilter memberLimit = 18;
  // 使用优惠券限制
  mairpc.common.ec.CouponLimit couponLimit = 19;
  // 秒杀商品
  string productId = 20; // valid:"objectId"
  // 停止原因
  string stopReason = 21;
  // 是否系统调用，为 true 时会忽略其他条件，强制停止或者修改活动
  bool isSystem = 22;
  // 活动类型名称
  string typeName = 23;
  // 秒杀价名称
  string priceName = 24;
  // 是否开启剩余库存展示
  bool isStockDisplayEnabled = 25;
  // 储值卡限制
  mairpc.common.ec.PrepaidCardLimit prepaidCardLimit = 26;
  // 活动范围
  string scope = 27; // valid:"in(all|limit|import)"
  // 是否限制门店库存
  bool isLimitStoreStock = 28;
}

message GetFlashSaleCampaignRequest {
  // @required
  string id = 1; // valid:"required,objectId"
  string memberId = 2; // valid:"objectId"
  string storeId = 3; // valid:"objectId"
  // 是否格式化 originalPrice
  //
  // 商品有划线价就是划线价，无划线价就是售价
  bool formatOriginalPrice = 4;
}

message FlashSaleCampaignDetail {
  string id = 1;
  string accountId = 2;
  string title = 3;
  string shareDescription = 4;
  string sharePicture = 5;
  string startAt = 6;
  string endAt = 7;
  string createdAt = 8;
  string updatedAt = 9;
  FlashSaleProductDetail product = 10;
  // 活动状态
  string status = 11;
  repeated FlashSaleSkuDetail skus = 12;
  repeated FlashSaleStoreDetail stores = 13;
  // 优惠券补贴详情
  FlashSaleCouponDetail coupon = 14;
  // 预约设置
  FlashSaleReservationDetail reservation = 15;
  // 付款人数
  uint64 memberCount = 16;
  // 销售额
  uint64 totalAmount = 17;
  // 订单数
  uint64 orderCount = 18;
  string periodType = 19;
  repeated HourTimeSpan hourTimeSpans = 20;
  repeated uint64 days = 21;
  uint64 limit = 22;
  // 活动是否停止
  bool isStopped = 23;
  // 周期活动截至当前时间开启过的时间段
  repeated TimeSpan timeSpans = 24;
  string scope = 25;
  bool isLimitStoreStock = 26;
  uint64 viewed = 27;
  string stopReason = 28;
  // 添加门店方式
  string addStoreType = 29;
  // 是否包邮
  bool isFreeShipping = 30;
  // 是否可以使用优惠券
  bool canUseCoupon = 31;
  // 是否可以使用储值卡
  bool canUsePrepaidCard = 32;
  // 是否只能使用一种优惠
  bool isDiscountLimit = 33;
  // 参与人群限制
  mairpc.common.ec.MemberFilter memberLimit = 34;
  // 使用优惠券限制
  mairpc.common.ec.CouponLimit couponLimit = 35;
  // 活动类型名称
  string typeName = 36;
  // 秒杀价名称
  string priceName = 37;
  // 是否开启剩余库存展示
  bool isStockDisplayEnabled = 38;
  // 储值卡限制
  mairpc.common.ec.PrepaidCardLimit prepaidCardLimit = 39;
}

message FlashSaleProductDetail {
  // ec.product._Id
  string id = 1;
  string name = 2;
  string picture = 3;
  bool public = 4;
  string type = 5;
  // 特殊关键词
  string specialKeyword = 6;
  // 普通关键词
  repeated string commonKeywords = 7;
  // sku 最低价对应的划线价
  int64 originalPrice = 8;
  // sku 最低价
  int64 lowestPrice = 9;
  // 销量
  int64 sales = 10;
  // 门店商品状态
  string storeShelveStatus = 11;
  // 商品状态
  string shelveStatus = 12;
}

message FlashSaleReservationDetail {
  bool isEnabled = 1;
  // 预约方式： 关注微信公众号/服务号（subscribe_wechat）、用户填写信息（form）
  string type = 2;
  // cep 客户属性 id
  repeated string memberPropertyIds = 3;
  // 关注公众号信息
  ReservationChannelDetail channel = 4;
  // 是否允许未预约客户参加
  bool isAllowedUnReservationMemberJoin = 5;
}

message ReservationChannelDetail {
  string channelId = 1;
  string name = 2;
  string qrcodeUrl = 3;
  string avatar = 4;
}

message FlashSaleSkuDetail {
  string name = 1;
  // 秒杀商品 sku
  string sku = 2;
  // sku 属性名
  repeated string properties = 3;
  // 商品划线价格
  uint64 originalPrice = 4;
  // 秒杀价格
  uint64 price = 5;
  // 库存总量
  uint64 totalStock = 6;
  // 剩余库存
  uint64 stock = 7;
}

message FlashSaleCouponDetail {
  // 是否开启优惠券补贴
  bool isEnabled = 1;
  // 优惠券名称
  string title = 2;
  // 补贴优惠券 id
  string couponId = 3;
  // 发放总量
  uint64 totalStock = 4;
  // 发放总量
  uint64 stock = 5;
  // 优惠券有效期
  CouponTime time = 6;
}

message ExportFlashSaleCampaignRecordsRequest {
  // 关键字类型
  //
  // 可选值：order，member
  // 当为 order 时，按订单编号名称查找
  // 当为 member 时，按照客户姓名查找
  string searchType = 1; // valid:"in(order|member)"
  // 搜索关键字
  string searchKey = 2;
  // 下单时间
  mairpc.common.types.DateRange createdTime = 3;
  // 配送方式
  //
  // 可选值：pickup 自提，express 邮寄，cityExpress 同城快送
  repeated string deliveryMethods = 4; // valid:"in(pickup|express|cityExpress)"
  // 是否包含用户删除的订单
  mairpc.common.types.BoolValue includeMemberDeleted = 5;
  // 活动 id
  string campaignId = 6;
  string storeId = 7;
}

message ExportCampaignIssueCouponRecordsRequest {
  // 客户ID
  string memberId = 1; // valid:"objectId"
  // 优惠券ID
  string couponId = 2; // valid:"objectId"
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 3;
  // 场景Id
  string sceneId = 4;
  // 搜索关键字
  //
  // 可以是领取人姓名
  string searchKey = 5;
  // 领取时间
  //
  // 用于筛选优惠券领取时间
  mairpc.common.types.DateRange createdAtRange = 6;
  // 领取门店 Id
  string storeId = 7; // valid:"objectId"
}

message CouponTime {
  // 时间区间类型 (absolute/relative/immediate)
  string type = 1;
  // 开始时间
  int64 beginTime = 2;
  // 结束时间
  int64 endTime = 3;
}

message ListFlashSaleCampaignsRequest {
  // 活动名称
  string queryString = 1;
  // 活动状态
  //
  // 未开始（created），进行中（running），已结束（ended），不传为获取全部
  repeated string status = 2;
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 3;
  // 活动时间
  mairpc.common.types.StringDateRange campaignTime = 4;
  // 活动周期类型
  //
  // 活动周期类型，不传为获取全部 可选值： 单次（once）、周（weekly）、月（monthly）
  string periodType = 5; // valid:"in(once|weekly|monthly)"
  // flashSaleCampaignIds
  repeated string ids = 6; // valid:"objectIdList"
  // storeId
  string storeId = 7; // valid:"objectId"
  mairpc.common.types.BoolValue hasStock = 8;
  // 是否忽略统计信息
  bool ignoreStats = 9;
  // 组织 ids
  repeated string distributorIds = 10; // valid:"objectIdList"
  // 通过 ids 字段排序
  //
  // 仅当 ids 有值时有效
  bool sortByReqIds = 11;
  // 是否格式化 originalPrice
  //
  // 商品有划线价就是划线价，无划线价就是售价
  bool formatOriginalPrice = 12;
}

message ListFlashSaleCampaignsResponse {
  uint64 total = 1;
  repeated FlashSaleCampaignDetail items = 2;
}

message UpdateFlashSaleCampaignStockRequest {
  // @required
  //
  // 活动 ID
  string campaignId = 1; // valid:"required,objectId"
  // @required
  //
  // 商品 ID
  string productId = 2; // valid:"required,objectId"
  // 门店 ID
  //
  // 活动不区分门店库存时不用传
  string storeId = 3; // valid:"objectId"
  // @required
  //
  // 商品 sku
  string sku = 4; // valid:"required"
  // @required
  //
  // 操作类型 扣除（deduct），回滚（rollback）
  string operation = 5; // valid:"in(deduct|rollback)"
  // @required
  //
  // 数量
  uint64 count = 6;
  // 扣除时间
  //
  // 即订单创建时间，用于回滚时区分活动周期。
  string deductedAt = 7;
}

message JoinFlashSaleCampaignRequest {
  // @required
  //
  // 活动 ID
  string campaignId = 1; // valid:"required,objectId"
  // @required
  //
  // 客户 ID
  string memberId = 2; // valid:"required,objectId"
  // @required
  //
  // 来源渠道 ID
  string channelId = 3; // valid:"required"
  // @required
  //
  // 渠道下客户唯一身份信息
  string openId = 4; // valid:"required"
  mairpc.common.ec.PurchaseRequest purchaseRequest = 5;
  // 活动令牌
  string campaignToken = 6;
}

message JoinFlashSaleCampaignResponse {
  // 订单 ID
  string id = 1; // valid:"required,objectId"
  // 订单状态
  string status = 2;
  // 订单支付方式
  string payment = 3;
}

message CalculateFlashSaleCampaignOrderAmountRequest {
  // @required
  //
  // 活动 ID
  string campaignId = 1; // valid:"required,objectId"
  // @required
  //
  // 客户 ID
  string memberId = 2; // valid:"required,objectId"
  mairpc.common.ec.CalculateOrderAmountRequest request = 3;
  // 活动令牌
  string campaignToken = 6;
}

message IssueFlashSaleCampaignCouponRequest {
  // @required
  //
  // 活动 ID
  string campaignId = 1; // valid:"required,objectId"
  // @required
  //
  // 客户 ID
  string memberId = 2; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 3; // valid:"required,objectId"
}

message GetFlashSaleCampaignStockRequest {
  // @required
  //
  // 活动 ID
  string campaignId = 1; // valid:"required,objectId"
  // 门店 ID
  string storeId = 2; // valid:"objectId"
}

message GetFlashSaleCampaignStockResponse {
  repeated FlashSaleCampaignSkuStock items = 1;
}

message FlashSaleCampaignSkuStock {
  // sku
  string sku = 1;
  // 剩余库存
  uint64 stock = 2;
}

message GenerateFlashSaleCampaignTokenRequest {
  // @required
  //
  // 活动 ID
  string campaignId = 1; // valid:"required,objectId"
  // @required
  //
  // 规格编码
  string sku = 2; // valid:"required"
  // required
  //
  // 客户 ID
  string memberId = 3; // valid:"required,objectId"
  // 门店 ID
  string storeId = 4; // valid:"objectId"
  // @required
  //
  // 活动周期
  TimeSpan timeSpan = 5; // valid:"required"
}

message GenerateFlashSaleCampaignTokenResponse {
  // 活动令牌
  string campaignToken = 1;
  // 商品剩余库存
  uint64 stock = 2;
}

message GetCampaignLimitRequest {
  // @required
  //
  // 活动类型 秒杀活动（flashSaleCampaign），拼团活动（GrouponCampaign)，促销套餐（packageCampaign）
  string type = 1; // valid:"required,in(flashSaleCampaign|grouponCampaign|packageCampaign)"
  // @required
  //
  // 活动 id
  string id = 2; // valid:"required,objectId"
  // @required
  //
  // 客户 id
  string memberId = 3; // valid:"required,objectId"
  // 商品 id
  //
  // 不传此字段以活动为维度计算，如: 促销套餐
  // 传此字段以商品为维度计算，如: 拼团活动、秒杀活动
  string productId = 4; // valid:"objectId"
}

message GetFlashSaleCampaignOrderMembershipDiscountsRequest {
  // @required
  //
  // 活动 ID
  string campaignId = 1; // valid:"required,objectId"
  // @required
  //
  // 客户 ID
  string memberId = 2; // valid:"required,objectId"
  mairpc.common.ec.GetMembershipDiscountsRequest request = 3;
  // 活动令牌
  string campaignToken = 4;
}
