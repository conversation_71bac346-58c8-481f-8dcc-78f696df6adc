type: google.api.Service
config_version: 3

http:
  rules:
    # 获取物流设置
    - selector: mairpc.ec.setting.SettingService.GetDeliverySetting
      get: /v2/ec/deliverySetting
      tags: ['私域商城-设置']
    # 获取交易设置
    - selector: mairpc.ec.setting.SettingService.GetOrderSetting
      get: /v2/ec/orderSetting
      tags: ['私域商城-设置']
    # 获取企业信息
    - selector: mairpc.ec.setting.SettingService.GetAccountSetting
      get: /v2/ec/account
      tags: ['私域商城-设置']
      filterMark: backend
    # 获取全局设置
    - selector: mairpc.ec.setting.SettingService.GetSettings
      get: /v2/ec/settings
      filterMark: backend
      tags: ['私域商城-设置']
    # 批量获取用户授权设置
    - selector: mairpc.ec.setting.SettingService.ListWeappAuthorizationSettings
      get: /v2/ec/weappAuthorizationSettings
      tags: ['私域商城-设置']
    # 获取导购企微版全局设置
    - selector: mairpc.ec.setting.SettingService.GetWechatworkSetting
      get: /v2/ec/wechatworkSetting
      tags: ['导购企微版-设置']
    # 获取门店全局设置
    - selector: mairpc.ec.setting.SettingService.GetStoreSetting
      get: /v2/ec/storeSetting
      tags: ['私域商城-设置']
    # 获取全局静态设置
    - selector: mairpc.ec.setting.SettingService.GetGlobalSetting
      get: /v2/ec/globalSetting
      tags: ['私域商城-设置']
    - selector: mairpc.ec.setting.SettingService.GetInvoiceSetting
      get: /v2/ec/invoiceSetting
      tags: ['私域商城-设置']
      hideResponseFields: 'publicParameters.password,publicParameters.authorizationCode,publicParameters.tripleDesKey,publicParameters.appKey,publicParameters.appSecret,publicParameters.salt,publicParameters.refreshToken'
    - selector: mairpc.ec.setting.SettingService.GetProductCommentSetting
      get: /v2/ec/productCommentSetting
      tags: ['私域商城-设置']
    # 进入小程序首页获取动态数据
    - selector: mairpc.ec.setting.SettingService.GetDynamicData
      get: /v2/ec/dynamicData
      tags: ['私域商城-设置']
    - selector: mairpc.ec.setting.SettingService.GetHotProductSetting
      get: /v2/ec/hotProductSetting
      tags: ['私域商城-设置']
    - selector: mairpc.ec.setting.SettingService.ListRecommendedProductSettings
      get: /v2/ec/recommendedProductSettings
      tags: ['私域商城-设置']
