syntax = "proto3";

package mairpc.ec.setting;

option go_package = "setting";

import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/types/types.proto";
import "ec/content/page.proto";
import "ec/mall/mallSetting.proto";
import "ec/marketing/enterStoreWelfare.proto";
import "member/member_level.proto";

message GetOrderSettingResponse {
  // ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 订单过期时间
  uint64 expireTime = 3;
  // 是否开启订单过期提醒
  bool expireRemind = 4;
  // 退款设置
  string refundControl = 5;
  // 自动确认收货时间
  uint64 autocompleteDay = 6;
  // 创建时间
  string createdAt = 7;
  // 更新时间
  string updatedAt = 8;
  // 是否需要手机号
  bool isPhoneNeeded = 9;
  // 接单设置
  bool isAutoAcceptOrder = 10;
  // 同城快送自动确认收货时间（天）
  uint64 cityExpressAutocompleteDay = 11;
  // 支持店长端审核退款
  bool isStaffAuditRefundOrderEnabled = 12;
  // 是否支持开发票
  bool isInvoiceEnabled = 13;
  // 是否禁用部分退款，目前仅用于前端判断
  bool isPartialRefundDisabled = 14;
  // 退款需要审核状态点
  string refundNeedAuditStatus = 15;
  // 无需物流商品自动完成订单时间，单位：天
  uint64 noExpressAutocompleteDay = 16;
  // 货币单位
  string currencyUnit = 17;
  // 发票设置: unsupported（不支持）、offline（线下开纸质发票）、online（线上开电子发票）
  string invoiceSetting = 18;
  // 禁用用户退款
  bool disableCustomerRefund = 19;
  // 订单完成 n 天内允许退款
  uint64 allowedRefundCompletedDays = 20;
  // 接入了微信小程序发货信息管理的渠道
  repeated string wechatTradeManagementEnabledChannels = 21;
  // 是否开启小票申请
  bool isTicketEnabled = 22;
  // 发票类型
  //
  // general（普通发票）、special（专用发票）
  repeated string invoiceTypes = 23;
  // 发票内容
  //
  // productDetail（商品明细）、productCategory（商品类别）
  repeated string invoiceContents = 24;
  // 授权协议列表
  repeated string invoiceAgreementIds = 25;
  // 订单退款是否需要上传凭证
  bool needUploadOrderRefundCertificate = 26;
}

message UpdateOrderSettingRequest {
  // 订单过期时间
  //
  // 多少分钟后过期，支持 30、60、120
  uint64 expireTime = 1;
  // 是否开启过期提醒
  bool expireRemind = 2;
  // 退款设置
  //
  // 自动同意退款申请（automatic），不允许退款申请（not_allow）
  string refundControl = 3; // valid:"in(automatic|not_allow)"
  // 自动确认收货时间
  uint64 autocompleteDay = 4;
  // 是否需要设置手机号
  bool isPhoneNeeded = 5;
  // 是否开启自动接单并打印
  bool isAutoAcceptOrder = 6;
  // 同城快送自动确认收货时间，单位：天
  uint64 cityExpressAutocompleteDay = 7;
  // 支持店长端审核退款
  bool isStaffAuditRefundOrderEnabled = 8;
  // 是否支持开发票
  bool isInvoiceEnabled = 9;
  // 退款需要审核状态点
  //
  // 接单（accepted），发货（shipped）
  string refundNeedAuditStatus = 10; // valid:"in(accepted|shipped)"
  // 无需物流商品自动完成订单时间，单位：天
  uint64 noExpressAutocompleteDay = 11;
  // 货币单位
  //
  // 人民币 rmb， 港币 hkd
  string currencyUnit = 12;
  // 发票设置：unsupported（不支持）、offline（线下开纸质发票）、online（线上开电子发票）
  string invoiceSetting = 13; // valid:"in(unsupported|offline|online)"
  // 禁用用户退款
  bool disableCustomerRefund = 14;
  // 订单完成 n 天内允许退款
  mairpc.common.types.UIntValue allowedRefundCompletedDays = 15;
  // 接入了微信小程序发货信息管理的渠道
  repeated string wechatTradeManagementEnabledChannels = 16;
  // 是否开启小票申请
  bool isTicketEnabled = 17;
  // 发票类型
  //
  // general（普通发票）、special（专用发票）
  repeated string invoiceTypes = 18; // valid:"in(general|special)"
  // 发票内容
  //
  // productDetail（商品明细）、productCategory（商品类别）
  repeated string invoiceContents = 19; // valid:"in(productDetail|productCategory)"
  // 授权协议列表
  repeated string invoiceAgreementIds = 20; // valid:"objectIdList"
  // 订单退款是否需要上传凭证
  bool needUploadOrderRefundCertificate = 21;
}

message GetCommissionSettingResponse {
  // ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 是否开启
  bool enabled = 3;
  // 分账比例类型
  //
  // 大比例分账（large），小比例分账（small）
  string proportionScaleType = 4;
  // 分润时间类型
  uint64 type = 5;
  // 创建时间
  string createdAt = 6;
  // 更新时间
  string updatedAt = 7;
}

message UpdateCommissionSettingRequest {
  // 是否开启
  mairpc.common.types.BoolValue enabled = 1;
  // 分润发起时间
  //
  // 售后维权期结束发起分账（1），交易完成发起分账（2）
  uint64 type = 2;
  // 分账比例类型
  //
  // 可选值：小比例分账（small），大比例分账（large）
  string proportionScaleType = 3; // valid:"in(small|large)"
}

message GetDeliverySettingResponse {
  // ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 预约
  ReservationSetting reservation = 4;
  // 物流
  LogisticsDetail logistics = 5;
  // 创建时间
  string createdAt = 6;
  // 更新时间
  string updatedAt = 7;
  // 同城快送
  CityExpress cityExpress = 8;
  // 默认配送方式
  //
  // 同城快送 cityExpress,自提 reservation,物流 logistics
  string defaultDelivery = 9;
}

message CityExpress {
  // 是否开启
  bool enabled = 1;
  // 自定义名称
  string name = 2;
  // 邮费 单位：分
  uint64 fee = 3;
  // 配送时间
  uint64 deliveryDays = 4;
  // 配送时间区间
  uint64 deliveryDayInterval = 5;
  // 是否允许门店管理配送规则
  bool enableManageDeliveryRule = 6;
  // 是否限制配送距离
  bool limitDeliveryDistance = 7;
  // 配送距离范围
  DistanceRange distanceRange = 8;
  // 是否允许门店管理运费规则
  bool enableManageDeliveryFeeRule = 9;
  // 是否接入第三方物流
  bool isThirdDeliveryEnable = 10;
}

message LogisticsDetail {
  // 是否开启
  bool enabled = 1;
  // 发货设置
  LogisticsShipSetting shipSetting = 2;
  // 运费设置
  LogisticsFeeSetting feeSetting = 3;
  // 门店类型（仅 D2R 使用）
  //
  // 全部类型门店（all）、全部实体门店（physical）、全部虚拟门店（virtual）
  string storeType = 8;
}

message LogisticsShipSetting {
  // 发货方设置
  //
  // staff 门店发货， user 总部发货
  string operator = 1; // valid:"in(staff|user)"
  // 发货信息
  ContactAddress senderAddress = 2;
  // 退货信息
  ContactAddress returnAddress = 3;
  // 是否仅指定门店快递发货
  bool onlyAddedStore = 4;
  // 发货区域类型
  //
  // 全部区域发货 all，部分区域不可发货 partyAreasNonShipping
  string type = 5; // valid:"in(all|partyAreasNonShipping)"
  // 不可发货区域
  repeated string nonShippingAreas = 6;
}

message LogisticsFeeSetting {
  // 计费规则
  //
  // solid 固定邮费, condition 满足阈值包邮
  string feeRule = 1; // valid:"in(solid|condition)"
  // 邮费
  //
  // 单位：分
  uint64 fee = 2;
  // 包邮最低消费
  //
  // 单位：分
  uint64 threshold = 3;
}

message UpdateDeliverySettingRequest {
  // 预约
  ReservationSettingRequest reservation = 1;
  // 物流设置
  LogisticsSettingRequest logistics = 2;
  // 同城快送
  CityExpressRequest cityExpress = 3;
  // 默认配送方式
  //
  // 可选值 cityExpress，express，pickup
  mairpc.common.types.StringValue defaultDelivery = 4;
}

message CityExpressRequest {
  // 是否开启同城快送
  mairpc.common.types.BoolValue enabled = 1;
  // 自定义名称
  string name = 2;
  // 运费设置
  mairpc.common.types.UIntValue fee = 3;
  uint64 expireTime = 4;
  uint64 autocompleteDay = 5;
  // 配送时间
  uint64 deliveryDays = 6;
  // 配送时间区间
  uint64 deliveryDayInterval = 7;
  // 是否允许门店管理配送规则
  mairpc.common.types.BoolValue enableManageDeliveryRule = 8;
  // 是否限制配送距离
  mairpc.common.types.BoolValue limitDeliveryDistance = 9;
  // 配送距离范围
  DistanceRange distanceRange = 10;
  // 是否允许门店管理运费规则
  mairpc.common.types.BoolValue enableManageDeliveryFeeRule = 11;
}

message DistanceRange {
  uint32 begin = 1;
  uint32 end = 2;
}

message LogisticsSettingRequest {
  // 是否开启物流
  mairpc.common.types.BoolValue enabled = 1;
  // 发货设置
  LogisticsShipSetting shipSetting = 2;
  // 运费设置
  LogisticsFeeSetting feeSetting = 3;
  // 门店类型（仅 D2R 使用）
  //
  // 全部类型门店（all）、全部实体门店（physical）、全部虚拟门店（virtual）
  string storeType = 4; // valid:"in(all|physical|virtual)"
}

message ContactAddress {
  // 联系人姓名
  string name = 1;
  // 联系电话
  string tel = 2;
  // 联系地址
  mairpc.common.types.Address address = 3;
}

message ReservationSettingRequest {
  // 是否开启预约
  mairpc.common.types.BoolValue enabled = 1;
  // 是否开启预约时间限制
  mairpc.common.types.BoolValue enabledDayLimit = 2;
  // 最长预约时间
  //
  // 当天（1），三天内（3）
  int32 dayLimit = 3;
  // 最快自提时间
  //
  // 输入范围 1 到 999
  int32 timeLimit = 4;
  // 是否开启自提口令
  mairpc.common.types.BoolValue isPickupPasswordEnabled = 5;
  // 自提口令文件 url
  string fileUrl = 6;
  // 自提口令文件名
  string fileName = 7;
  // 门店类型（仅 D2R 使用）
  //
  // 全部类型门店（all）、全部实体门店（physical）、全部虚拟门店（virtual）
  string storeType = 8; // valid:"in(all|physical|virtual)"
  // 提货通知设置
  NotificationSetting notificationSetting = 9;
}

message NotificationSetting {
  // 是否开启提货通知
  bool enabled = 1;
  int64 hour = 2;
  int64 minute = 3;
}

message ReservationSetting {
  // 是否开启预约
  bool enabled = 1;
  // 是否开启预约时间限制
  bool enabledDayLimit = 2;
  // 最长预约时间
  //
  // 当天（1），三天内（3）
  int32 dayLimit = 3;
  // 最快自提时间
  int32 timeLimit = 4;
  // 是否开启自提口令
  bool isPickupPasswordEnabled = 5;
  // 自提口令文件名
  string fileName = 6;
  // 自提口令文件
  string fileUrl = 7;
  // 门店类型（仅 D2R 使用）
  //
  // 全部类型门店（all）、全部实体门店（physical）、全部虚拟门店（virtual）
  string storeType = 8;
  // 提货通知设置
  NotificationSetting notificationSetting = 9;
}

message CreatePrinterRequest {
  // @required
  //
  // 设备备注名称
  string deviceName = 1; // valid:"required"
  // @required
  //
  // 打印机机器号
  string deviceNo = 2; // valid:"required"
  // @required
  //
  // 打印机密钥
  string deviceKey = 3; // valid:"required"
  // @required
  //
  // 门店 id
  string storeId = 4; // valid:"required"
  // 服务商
  //
  // 可选值: 365yun, feieyun
  string provider = 5; // valid:"in(365yun|feieyun)"
}

message UpdatePrinterRequest {
  // @required
  //
  // id
  string id = 1; // valid:"required"
  // @required
  //
  // 设备备注名称
  string deviceName = 2; // valid:"required"
  // @required
  //
  // 打印机机器号
  string deviceNo = 3; // valid:"required"
  // @required
  //
  // 打印机密钥
  string deviceKey = 4; // valid:"required"
  // 服务商
  //
  // 可选值: 365yun, feieyun
  string provider = 5; // valid:"in(365yun|feieyun)"
}

message ListPrintersRequest {
  // 是否不限制返回数量
  bool unlimited = 1;
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 2;
  // 门店 id
  string storeId = 3;
  // 打印机状态
  string status = 4;
}

message PrinterDetail {
  // id
  string id = 1;
  // 设备类型
  string type = 2;
  // 设备规格
  string spec = 3;
  // 设备备注名称
  string deviceName = 4;
  // 打印机机器号
  string deviceNo = 5;
  // 打印机密钥
  string deviceKey = 6;
  // 打印机状态
  string status = 7;
  // 门店 ID
  string storeId = 8;
  // 创建时间
  string createdAt = 9;
  // 更新时间
  string updatedAt = 10;
  // 服务商
  string provider = 11;
}

message ListPrintersResponse {
  repeated PrinterDetail items = 1;
  uint64 total = 2;
}

message TestPrinterRequest {
  // @required
  //
  // 打印机机器号
  string deviceNo = 1; // valid:"required"
  // @required
  //
  // 打印机密钥
  string deviceKey = 2; // valid:"required"
  // 服务商
  //
  // 可选值: 365yun, feieyun
  string provider = 5; // valid:"in(365yun|feieyun)"
}

message GetAccountSettingResponse {
  string helpdeskPhone = 1;
  string phone = 2;
  string hotline = 3;
  bool isWeappCustomerServiceEnabled = 4;
  bool isCustomerServiceHotlineEnabled = 5;
  string weappCustomerServiceType = 6;
  string wechatcpHelpdeskUrl = 7;
  string wechatcpHelpdeskCorpId = 8;
}

message SettingsDetail {
  string id = 1;
  string accountId = 2;
  // 支付方式
  repeated string payments = 3;
  // 是否开启评价功能
  bool isCommentAllowed = 4;
  // 是否已配置第三方 erp
  bool erpConfigured = 5;
  // 第三方 erp 服务提供商，wdt：旺店通，guanyi：管易云
  string erpProvider = 6;
  // 旺店通配置信息
  WdtConfig wdtConfig = 7;
  // 管易云配置
  GuanYiConfig guanYiConfig = 8;
  // 是否不同步库存
  bool isNotSyncErpStock = 9;
  // 是否需要推送退款完成或失败的退款单给第三方 erp
  bool needPushRefunded = 10;
  // 收款方，mai：mai 微信收款，other：由第三方自行收款
  string paymentProvider = 11;
  // 礼卡供应商
  string prepaidCardProvider = 12;
  // 是否接入了门店
  bool storeConfigured = 13;
  // 小程序版本信息
  repeated WechatAppVersion wechatAppVersions = 14;
  // 是否开启热门搜索词
  bool isHotKeywordEnabled = 15;
  // 手机号授权类型
  //
  // wechat：授权微信手机号，bind：绑定手机号
  string phoneAuthorizationType = 16;
  // 旺店通旗舰版配置信息
  WdtConfig wdtUltimateConfig = 17;
  // 是否开启商品评价审核
  bool isAuditEnabled = 18;
  // 微信转账模式
  //
  // v1/未填：企业付款到零钱，v2：商家转账到零钱
  string wechatTransferMode = 19;
  // 搜索关键词滚动间隔：秒
  int64 searchKeywordScrollInterval = 20;
  // 是否开启搜索推荐关键词
  bool isRecommendSearchKeywordEnabled = 21;
  // 易宝商户号
  string yeepayMerchantNo = 22;
  // 易宝父级商户号
  string yeepayParentMerchantNo = 23;
  // 储值服务提供商
  string storedValueProvider = 24;
  // 微商城门店信息
  //
  // 仅当请求中 checkIsMicroMall 为 true 时有值，有值则说明是微商城模式，否则是普通零售商城模式
  MicroMallStore microMallStore = 25;
  // 收钱吧配置
  ShouqianbaConfig shouqianbaConfig = 26;
  // 代客下单配置
  ProxyOrderConfig proxyOrderConfig = 27;
}

message ProxyOrderConfig {
  // 是否开启导购代客下单
  bool isStaffEnabled = 1;
  // 是否开启大众分销代客下单
  bool isMemberEnabled = 2;
  // 代下单客户类型，all（全部客户），member（会员）
  string type = 3;
}

message ShouqianbaConfig {
  // 轻 POS 应用编号，商户入网后由收钱吧提供
  string appId = 1;
  // 品牌编号，系统对接前由收钱吧分配并提供
  string brandCode = 2;
  // 商户内部使用的门店编号
  string storeSn = 3;
}

message MicroMallStore {
  // 门店 Id
  string id = 1;
  // 门店名称
  string name = 2;
  // 工作时间
  repeated BusinessHour businessHours = 3;
  // 同城快送信息
  CityExpressSetting cityExpressSetting = 4;
  // 快递设置
  StoreDeliverySetting deliverySetting = 5;
  // 门店地址
  StoreLocation location = 6;
  // 门店对应的 distributionStore 小店信息
  DistributionStoreInfo distributionStoreInfo = 7;
  // 是否是同城快送
  bool isCityExpressStore = 8;
}

message BusinessHour {
  repeated string workweek = 1;
  string startTime = 2;
  string endTime = 3;
}

message CityExpressSetting {
  // 配送范围
  float range = 1;
  // 送达时间
  uint64 arriveTime = 2;
  // 起送费
  uint64 basePrice = 3;
  // 运费模板
  common.ec.DeliveryFeeTemplate deliveryFeeTemplate = 4;
}

message StoreDeliverySetting {
  // 是否支持
  bool isEnabled = 1;
  // 支持时间
  string enabledAt = 2;
  // 门店联系人姓名
  string contactName = 3;
  // 门店联系人电话
  string contactTel = 4;
  // 是否设置为默认门店
  bool isDefault = 5;
  // 配送方式 可选值：到店自提（pickup），快递配送（express），同城配送（cityExpress）
  repeated string deliveryMethods = 6;
}

message StoreLocation {
  //　省（直辖市）
  string province = 1;
  // 区
  string city = 2;
  // 镇
  string district = 3;
  // 详细地址
  string name = 4;
  string areaCode = 5;
  float longitude = 6;
  float latitude = 7;
  // 地址全称
  string fullName = 8;
}

message DistributionStoreInfo {
  // 经销商信息
  repeated Distributor distributors = 1;
  // 商户号
  string merchantNumber = 2;
  // 商户名称
  string merchantName = 3;
  // 营业执照照片
  string licenseCopy = 4;
}

message Distributor {
  string id = 1;
  string accountId = 2;
  string type = 3;
  string status = 4;
}

message WechatAppVersion {
  // 渠道 ID
  string channelId = 1;
  // 小程序版本
  string appVersion = 2;
}

message WdtConfig {
  // 总部门店编号
  string shopNo = 1;
}

message GuanYiConfig {
  // erp 登录地址
  //
  // 可选值：www.guanyierp.com, v2.guanyierp.com, demo.guanyierp.com, erp.edgj.net
  string host = 1;
  // erp 仓库编码
  string warehouseCode = 2;
  // erp 店铺编码
  string shopCode = 3;
  // 快递代码
  string expressCode = 4;
  // 会员编码
  string vipCode = 5;
}

message UpsertSettingRequest {
  // 是否开启评价功能
  mairpc.common.types.BoolValue isCommentAllowed = 1;
  // 旺店通配置
  WdtConfigUpsertRequest wdtConfig = 2;
  // 旺店通旗舰版配置
  WdtConfigUpsertRequest wdtUltimateConfig = 3;
  // 管易云配置
  GuanYiConfigUpsertRequest guanYiConfig = 4;
  // 是否开启热门搜索词
  mairpc.common.types.BoolValue isHotKeywordEnabled = 5;
  // 是否开启商品评价审核
  mairpc.common.types.BoolValue isAuditEnabled = 6;
  // 手机号授权类型
  //
  // wechat：授权微信手机号，bind：绑定手机号
  string phoneAuthorizationType = 16; // valid:"in(wechat|bind)"
  // 是否开启搜索推荐词
  mairpc.common.types.BoolValue isRecommendSearchKeywordEnabled = 17;
  // 搜索关键字滚动间隔：秒
  mairpc.common.types.Int64Value searchKeywordScrollInterval = 18;
  // 代客下单配置
  ProxyOrderConfig proxyOrderConfig = 19;
}

message WdtConfigUpsertRequest {
  // 卖家账号
  mairpc.common.types.StringValue sid = 1;
  // 接口账号
  mairpc.common.types.StringValue appKey = 2;
  // 接口秘钥
  mairpc.common.types.StringValue appSecret = 3;
  // 总部门店店铺号
  mairpc.common.types.StringValue shopNo = 4;
}

message GuanYiConfigUpsertRequest {
  // 基础配置
  GuanYiBasicConfig basic = 1;
  // erp 店铺代码
  mairpc.common.types.StringValue shopCode = 2;
  // 清除配置
  bool clearConfig = 3;
}

message GuanYiBasicConfig {
  // @required
  // erp 登录地址
  //
  // 可选值：www.guanyierp.com, v2.guanyierp.com, demo.guanyierp.com, erp.edgj.net
  string host = 1; // valid:"required,in(www.guanyierp.com|v2.guanyierp.com|demo.guanyierp.com|erp.edgj.net)"
  // @required
  //
  // 接口账号
  string appKey = 2; // valid:"required"
  // @required
  //
  // 接口秘钥
  string appSecret = 3; // valid:"required"
  // @required
  //
  // 授权码
  string sessionKey = 4; // valid:"required"
  // @required
  //
  // erp 仓库代码
  string warehouseCode = 5; // valid:"required"
  // @required
  //
  // 快递代码
  string expressCode = 6; // valid:"required"
  // @required
  //
  // 会员代码
  string vipCode = 7; // valid:"required"
}

message WechatworkSettingDetail {
  string id = 1;
  string accountId = 2;
  // 是否禁用从企业微信同步
  bool isSyncFromWechatDisabled = 3;
  string createdAt = 4;
  string updatedAt = 5;
  // 是否开启定制导购任务
  bool isCustomTaskRuleEnabled = 6;
  // 是否开启导购名片
  bool isStaffCardEnabled = 7;
  // 导购激励发放模式
  string sendStaffIncentiveMode = 8;
  // 是否开启定制数据统计
  bool isCustomDataStatsEnabled = 9;
  // 自定义同步企微门店时起始节点部门ID
  int64 startDepartmentId = 10;
  // 选择距离起始部门的层级。从0开始。如设置 1， 则表示距离起始节点第一级所有节点作为门店
  int64 storeStartLevel = 11;
  // 下发导购任务时，是否检查 isStaff 字段
  bool checkTaskStaff = 12;
  // 是否只同步导购
  bool syncStaffsOnly = 13;
  // 是否已初始化
  bool isInitialized = 14;
  // 是否开启企业微信许可设置
  bool isWeworkLicenseEnabled = 15;
  // 是否开启企业微信许可自动激活
  bool isWeworkLicenseAutoActivated = 16;
  // 是否同步全部标签分组
  bool isSyncPartialTagGroup = 17;
  // 需要同步的企业微信标签组 id
  repeated string syncTagGroupIds = 18;
  // 小程序版本信息
  repeated WechatAppVersion wechatworkAppVersions = 19;
  // 无主客户分配设置
  AssignMemberSetting assignMemberSetting = 20;
  // 导购任务触达设置
  string taskTouchType = 21;
  // 是否开启客户流失提醒
  bool isDeletedMessageEnabled = 22;
}

message AssignMemberSetting {
  bool isDeadlineEnabled = 1;
  bool isRemindEnabled = 2;
  int64 deadlineDays = 3;
  string remindTime = 4;
  int64 remindDaysOffset = 5;
}

message UpdateWechatworkSettingRequest {
  // 是否开启导购名片
  mairpc.common.types.BoolValue isStaffCardEnabled = 1;
  // 导购激励发放模式
  string sendStaffIncentiveMode = 2; // valid:"in(offline|online)"
  // 是否不开启从企微同步
  mairpc.common.types.BoolValue isSyncFromWechatDisabled = 3;
  // 是否只同步导购
  mairpc.common.types.BoolValue syncStaffsOnly = 4;
  // 是否已初始化
  mairpc.common.types.BoolValue isInitialized = 5;
  // 是否开启企业微信许可自动激活
  mairpc.common.types.BoolValue isWeworkLicenseAutoActivated = 6;
  // 是否同步全部企业微信标签组
  mairpc.common.types.BoolValue isSyncPartialTagGroup = 7;
  // 需要同步的企业微信标签组 id
  repeated string syncTagGroupIds = 8;
  // 客户分配设置
  AssignMemberSetting assignMemberSetting = 9;
  // 导购任务触达设置
  string taskTouchType = 10; // valid:"in(bound|added)"
  // 是否开启客户流失提醒
  mairpc.common.types.BoolValue isDeletedMessageEnabled = 11;
}

message UpdateStoreSettingRequest {
  EntryRule entryRule = 1;
  RecommendRule recommendRule = 2;
}

message RecommendRule {
  bool isEnabled = 1;
  int64 distance = 2;
}

message StoreSetting {
  string id = 1;
  string accountId = 2;
  EntryRule entryRule = 3;
  string createdAt = 4;
  string updatedAt = 5;
  RecommendRule recommendRule = 6;
}

message EntryRule {
  // 携带门店 id 进入商城时是否允许切换门店
  bool allowChange = 1;
  // 进店方式
  string entryMethod = 2; // valid:"in(byLocation|defaultStore)"
  // 默认门店
  //
  // entryMethod 为 defaultStore 需要传
  DefaultStore defaultStore = 3;
  // 通过携带门店参数的二维码或小程序卡片进入商城小程序时，消费者与门店是否建立绑定关系
  bool isBound = 4;
  // isBound 为 true 时有意义。后续通过携带门店参数的二维码或小程序卡片进入商城小程序时，true 表示进入新的带参门店，且更换绑定门店，false 进入上次绑定门店
  bool isEntryNewStore = 5;
  // 未携带门店 id 进入商城时是否允许切换门店
  bool allowChangeWithoutStoreId = 6;
}

message DefaultStore {
  string id = 1;
  string name = 2;
}

message GetGlobalSettingResponse {
  DeliverySetting deliverySetting = 1;
  StoreSettingDetail storeSetting = 2;
  MallSetting mallSetting = 3;
  EcSetting ecSetting = 4;
  StockSetting stockSetting = 5;
  WeappTabBar weappTabBar = 6;
  MemberSetting memberSetting = 7;
  WechatShopSetting wechatShopSetting = 8;
  OrderSetting orderSetting = 9;
  MemberSetting paidMemberSetting = 10;
  StoreProductSetting storeProductSetting = 11;
  PrepaidCardSetting prepaidCardSetting = 12;
  ProductSetting productSetting = 13;
  BriefInvoiceSetting invoiceSetting = 14;
  RedeemCardSetting redeemCardSetting = 15;
  HotProductSetting hotProductSetting = 16;
  repeated RecommendedProductSettingDetail recommendedProductSetting = 17;
  repeated RecommendSearchKeyword recommendSearchKeywords = 18;
  BargainSetting bargainSetting = 19;
}

message HotProductSetting {
  // 热卖榜类型
  string type = 1;
  // 是否展示销量
  bool displaySales = 2;
  // 展示数量
  int64 limit = 3;
  // 是否开启
  bool enabled = 4;
}

message DeliverySetting {
  ReservationSetting reservation = 1;
  // 物流
  LogisticsDetail logistics = 2;
  // 同城快送
  CityExpress cityExpress = 3;
  // 默认配送方式
  //
  // 同城快送 cityExpress,自提 reservation,物流 logistics
  string defaultDelivery = 4;
}

message EcSetting {
  string id = 1;
  string accountId = 2;
  // 支付方式
  repeated string payments = 3;
  // 是否开启评价功能
  bool isCommentAllowed = 4;
  // 是否已配置第三方 erp
  bool erpConfigured = 5;
  // 是否已接入门店
  bool storeConfigured = 6;
  // 是否不同步库存
  bool isNotSyncErpStock = 7;
  // 是否需要推送退款完成或失败的退款单给第三方 erp
  bool needPushRefunded = 8;
  // 收款方，mai：mai 微信收款，other：由第三方自行收款
  string paymentProvider = 9;
  // 礼卡供应商
  string prepaidCardProvider = 10;
  // 小程序版本信息
  repeated WechatAppVersion wechatAppVersions = 11;
  // 是否开启热门搜索词
  bool isHotKeywordEnabled = 12;
  // 手机号授权类型
  //
  // wechat：授权微信手机号，bind：绑定手机号
  string phoneAuthorizationType = 13;
  // 搜索关键词滚动间隔：秒
  int64 searchKeywordScrollInterval = 14;
  // 是否开启搜索推荐关键词
  bool isRecommendSearchKeywordEnabled = 15;
  // 微商城门店信息
  //
  // 仅当请求中 checkIsMicroMall 为 true 时有值，有值则说明是微商城模式，否则是普通零售商城模式
  MicroMallStore microMallStore = 16;
  // 微信商户号
  string merchantId = 17;
  // 代客下单设置
  ProxyOrderConfig proxyOrderConfig = 18;
}

message StoreSettingDetail {
  EntryRule entryRule = 1;
  RecommendRule recommendRule = 2;
}

message MallSetting {
  // 注册设置
  mairpc.ec.mall.RecruitmentSetting recruitmentSetting = 1;
  // 订单设置
  mairpc.ec.mall.OrderSetting orderSetting = 2;
  // 其它设置
  mairpc.ec.mall.OtherSetting otherSetting = 3;
  // 分佣设置
  mairpc.ec.mall.ProportionSetting proportionSetting = 4;
  // 是否开启分销
  bool isEnabled = 5;
}

message StockSetting {
  // 是否开启库存预警
  bool isWarningEnabled = 1;
  // 固定预警阀值
  uint64 warningThreshold = 2;
  // 是否开启固定预警值
  bool isSettledWarningEnabled = 3;
  // 是否开启部分预警值
  bool isPartlyWarningEnabled = 4;
  // 是否开启库存
  bool isEnabled = 5;
}

message WeappTabBar {
  // 店铺风格
  string style = 1;
  // 自定义风格
  repeated string customColors = 2;
  // 导航状态
  string status = 3;
  // 导航页面
  repeated mairpc.ec.content.TabBarRelatedPageInfo pages = 4;
  // 导航背景色颜色
  string backgroundColor = 5;
  // 导航文本颜色，第一个普通颜色，第二个高亮
  repeated string textColors = 6;
  // 导航图标颜色，第一个普通颜色，第二个高亮
  repeated string iconColors = 7;
  // 店铺风格
  MarketingStyle marketingStyle = 8;
  // 加载图片
  string loadingImage = 9;
}

message MemberSetting {
  // 是否开启会员功能
  bool enabled = 1;
  // 是否开启会员招募
  bool recruitEnabled = 2;
}

message StoreProductSetting {
  // 展示门店下架商品
  bool displayUnshelvedProduct = 1;
  // 门店下架商品标签
  string unshelvedProductLabel = 2;
}

message WechatShopSetting {
  // 是否已开启自定义交易组件
  bool enabled = 1;
  // 视频号 ID
  string videoChannelId = 2;
}

message OrderSetting {
  // 货币单位
  //
  // 人民币 rmb, 港币 hkd
  string currencyUnit = 1;
  // 禁用用户退款
  bool disableCustomerRefund = 2;
  // 接入了微信小程序发货信息管理的渠道
  repeated string wechatTradeManagementEnabledChannels = 3;
  // 是否开启小票申请
  bool isTicketEnabled = 4;
  // 订单退款是否需要上传凭证
  bool needUploadOrderRefundCertificate = 5;
}

message PrepaidCardSetting {
  // 礼卡卡号标题
  string title = 1;
  // 礼卡规则说明
  string rule = 2;
  // 为 true 时，小程序端不展示退款入口
  bool disablePurchaseEntrance = 3;
  // 协议文件
  repeated Agreement agreements = 4;
  // 为 false 时，小程序端不展示退款入口
  bool enableRefundEntrance = 5;
  // 允许解绑
  bool allowUnbound = 6;
  // 消息通知设置
  NoticeSetting noticeSetting = 7;
  // 自定义卡号名称
  string numberName = 8;
  // 是否优先使用储值卡支付
  bool prioritizeUseForPaymentEnabled = 9;
}

message NoticeSetting {
  // 领取通知是否启用
  bool isReceivedNoticeEnabled = 1;
  // 未领取通知是否启用
  bool isUncollectedNoticeEnabled = 2;
  // 即将过期通知是否启用
  bool isWillExpireNoticeEnabled = 3;
  // 过期前多少天
  int64 daysCount = 4;
}

message ProductSetting {
  // 货币单位
  //
  // 人民币 rmb， 港币 hkd
  string currencyUnit = 1;
  // 是否展示销量
  bool isDisplaySales = 2;
  // 最少展示销量，低于此值的销量不在小程序展示
  int64 minDisplaySales = 3;
  // 分享海报设置
  PosterSetting posterSetting = 4;
  // 是否展示商品关键词
  bool commonKeywordUndisplayable = 5;
  // 是否展示商品角标（特殊关键词）
  bool specialKeywordUndisplayable = 6;
  // 是否自动下架无库存商品
  bool autoUnshelved = 7;
}

message BriefInvoiceSetting {
  // 发票设置
  //
  // unsupported（不支持）、offline（线下开纸质发票）、online（线上开电子发票）
  string invoiceSetting = 1;
  // 发票类型
  //
  // general（普通发票）、special（专用发票）
  repeated string invoiceTypes = 2;
  // 发票内容
  //
  // productDetail（商品明细）、productCategory（商品类别）
  repeated string invoiceContents = 3;
  // 授权协议列表
  repeated string invoiceAgreementIds = 4;
}

message RedeemCardSetting {
  // 名称
  string name = 1;
  // 规则
  string rule = 2;
  // 协议文件
  repeated Agreement agreements = 3;
  // 通知设置
  RedeemCardNoticeSetting noticeSetting = 4;
  // 是否展示退款入口
  bool EnableRefundEntrance = 5;
}

message RedeemCardNoticeSetting {
  // 领取通知是否启用
  bool isReceivedNoticeEnabled = 1;
  // 未领取通知是否启用
  bool isUncollectedNoticeEnabled = 2;
  // 即将过期通知是否启用
  bool isWillExpireNoticeEnabled = 3;
  // 过期前多少天
  int64 daysCount = 4;
}

message BargainSetting {
  // 设置 ID
  string id = 1;
  // 主题
  string title = 2;
  // 砍价规则
  string rule = 3;
}

message Agreement {
  string id = 1;
  string name = 2;
}

message GetProductCommentSettingResponse {
  uint64 commentWordCount = 1;
  uint64 pictureCount = 2;
}

message GetDynamicDataRequest {
  // @required
  //
  // 用户 id
  string memberId = 1; // valid:"required,objectId"
  // 门店 id
  string storeId = 2; // valid:"objectId"
  // 分销员 id
  string promoterId = 3; // valid:"objectId"
}

message GetDynamicDataResponse {
  // 进店福利是否可见
  bool isEnterStoreWelfareVisable = 1;
  // 进店福利
  //
  // 若进店福利不可见，则进店福利为空
  EnterStoreWelfare enterStoreWelfare = 2;
  // 升级礼包奖励
  mairpc.member.SendLevelUpRewardResponse levelUpReward = 3;
  // 会员权益信息
  MemberDiscountPrivilege memberDiscountPrivileges = 4;
  // 购物车商品数量
  int64 cartProductsCount = 5;
  // 是否是分销员
  bool isPromoter = 6;
  // 当前用户的导购信息
  string staffId = 7;
  int64 staffStatus = 8;
  string currentStoreId = 9;
}

message EnterStoreWelfare {
  // 浮窗类型
  //
  // 可选值：campaign（活动浮窗）、coupon（卡券浮窗）
  string floatingWindowType = 1;
  // 浮窗样式
  string floatingWindowBackgroud = 2;
  // 跳转链接
  mairpc.ec.marketing.Link link = 3;
  // 卡券列表
  repeated mairpc.ec.marketing.CouponWelfare coupons = 4;
  // 是否是默认浮窗样式
  bool isDefaultFloatingWindowBackgroud = 5;
  // 进店福利 Id
  string id = 6;
  // 展示次数类型
  //
  // 可选值：normal（查看详情后不再展示）、first（仅首次展示）、always（始终展示）
  string showTimesType = 7;
}

message MemberDiscountPrivilege {
  // 是否开启
  bool isEnabled = 1;
  // 折扣
  uint64 discount = 2;
  // 所属会员类型
  //
  // 等级会员(member)、付费会员（paidMember）
  string memberType = 3;
  // 是否能与优惠券同时使用
  bool canUseWithCoupons = 4;
}

message GetSettingsRequest {
  // 渠道 Id
  string channelId = 1;
  // 小程序版本
  //
  // channelId 和 appVersion 仅小程序请求时携带，不用作筛选条件，只用于服务端记录小程序版本信息
  string appVersion = 2;
  // 环境版本
  //
  // develop：开发版，trial：体验版，release：正式版
  // 当前只记录正式版
  string envVersion = 3;
  // 是否校验是否微商城模式
  //
  // 在获取 globalSetting 中，此参数一定会被置为 true
  bool checkIsMicroMall = 4;
  // 客户 ID
  //
  // 用于 weappTabBar 等的可见性过滤
  string memberId = 5; // valid:"objectId"
  // 门店 ID
  //
  // 用于 weappTabBar 等的可见性过滤
  string storeId = 6; // valid:"objectId"
}

message CreateInvoiceSettingRequest {
  // @required
  //
  // 发票平台: nuo-e（诺e）、baiwang（百望云）
  string provider = 1; // valid:"required,in(nuo-e|baiwang)"
  // 公共参数
  PublicParameters publicParameters = 2;
  // 税号配置
  TaxSetting taxSetting = 3;
  // 是否是测试账号
  bool isTest = 4;
  // 发票形式
  //
  // unsupported（不支持）、offline（线下开纸质发票）、online（线上开电子发票）
  string invoiceSetting = 5; // valid:"in(unsupported|offline|online)"
  // 发票类型
  //
  // general（普通发票）、special（专用发票）
  repeated string invoiceTypes = 6; // valid:"in(general|special)"
  // 发票内容
  //
  // productDetail（商品明细）、productCategory（商品类别）
  repeated string invoiceContents = 7; // valid:"in(productDetail|productCategory)"
  // 授权协议列表
  repeated string invoiceAgreementIds = 8; // valid:"objectIdList"
  // 开票天数限制
  uint32 invoiceIssueDaysLimit = 9;
  // 是否允许多个订单合并开票
  bool isMergeInvoicing = 10;
  // 是否允许重新申请开票
  bool isReissueAllowed = 11;
  // 重新开票天数限制
  uint32 invoiceReissueDaysLimit = 12;
  // 是否允许自动红冲
  bool isReversible = 13;
}

message GetInvoiceSettingsRequest {
  // 是否是测试账号
  mairpc.common.types.BoolValue isTest = 1;
}

message GetInvoiceSettingsResponse {
  repeated InvoiceSetting items = 1;
}

message InvoiceSetting {
  // 发票设置 ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 发票平台
  string provider = 3;
  // 是否是测试账号
  bool isTest = 4;
  // 公共参数
  PublicParameters publicParameters = 5;
  // 税号配置
  TaxSetting taxSetting = 6;
  // 发票设置
  //
  // unsupported（不支持）、offline（线下开纸质发票）、online（线上开电子发票）
  string invoiceSetting = 7;
  // 发票类型
  //
  // general（普通发票）、special（专用发票）
  repeated string invoiceTypes = 8;
  // 发票内容
  //
  // productDetail（商品明细）、productCategory（商品类别）
  repeated string invoiceContents = 9;
  // 授权协议列表
  repeated string invoiceAgreementIds = 10;
  // 开票天数限制
  uint32 invoiceIssueDaysLimit = 11;
  // 是否允许多个订单合并开票
  bool isMergeInvoicing = 12;
  // 是否允许重新申请开票
  bool isReissueAllowed = 13;
  // 重新开票天数限制
  uint32 invoiceReissueDaysLimit = 14;
  // 是否允许自动红冲
  bool isReversible = 15;
}

message PublicParameters {
  // 账号
  string username = 1;
  // 密码
  string password = 2;
  // 纳税人识别号
  string taxpayerId = 3;
  // 纳税人授权码
  string authorizationCode = 4;
  // 密钥
  string tripleDesKey = 5;
  // appKey
  string appKey = 6;
  // appSecret
  string appSecret = 7;
  // 盐（用于加密 password）
  string salt = 8;
  // 用于刷新的 token
  string refreshToken = 9;
}

message TaxSetting {
  // 开票方纳税人识别号
  string drawerTaxpayerId = 1;
  // 开票方纳税人全称
  string drawerTaxpayerFullName = 2;
  // 销方纳税人识别号
  string sellerTaxpayerId = 3;
  // 销方纳税人全称
  string sellerTaxpayerFullName = 4;
  // 销方地址
  string sellerAddress = 5;
  // 销方电话
  string sellerPhone = 6;
  // 销方开户银行
  string sellerBankName = 7;
  // 销方银行账号
  string sellerBankAccount = 8;
  // 开票员
  string drawer = 9;
  // 收款员
  string payer = 10;
  // 复核人
  string reviewer = 11;
}

message UpdateInvoiceSettingRequest {
  string id = 1;
  // 公共参数
  PublicParameters publicParameters = 5;
  // 税号配置
  TaxSetting taxSetting = 6;
  // 发票设置
  //
  // unsupported（不支持）、offline（线下开纸质发票）、online（线上开电子发票）
  string invoiceSetting = 7; // valid:"in(unsupported|offline|online)"
  // 服务商
  //
  // nuo-e（诺e）、baiwang（百望云）
  string provider = 8; // valid:"in(nuo-e|baiwang)"
  // 发票类型
  //
  // general（普通发票）、special（专用发票）
  repeated string invoiceTypes = 9; // valid:"in(general|special)"
  // 发票内容
  //
  // productDetail（商品明细）、productCategory（商品类别）
  repeated string invoiceContents = 10; // valid:"in(productDetail|productCategory)"
  // 授权协议列表
  repeated string invoiceAgreementIds = 11; // valid:"objectIdList"
  // 开票天数限制
  uint32 invoiceIssueDaysLimit = 12;
  // 是否允许多个订单合并开票
  mairpc.common.types.BoolValue isMergeInvoicing = 13;
  // 是否允许重新申请开票
  mairpc.common.types.BoolValue isReissueAllowed = 14;
  // 重新开票天数限制
  uint32 invoiceReissueDaysLimit = 15;
  // 是否允许自动红冲
  mairpc.common.types.BoolValue isReversible = 16;
}

message CreateInvoiceTemplateRequest {
  // @required
  //
  // 税收分类编码
  string taxClassificationCode = 1; // valid:"required"
  // 税收分类名称
  string taxClassificationName = 2;
  // @required
  //
  // 优惠政策: nonuse（不使用）、zeroTax（出口零税）、freeTax（免税）、noTax（不征税）
  string favorablePolicy = 3; // valid:"required,in(nonuse|zeroTax|freeTax|noTax)"
  // 税率
  int64 taxRate = 4;
  // 开票名称
  string name = 6;
  // 开票类型： deliveryFee（运费）、product（商品）
  string type = 7; // in(deliveryFee|product)
  // 备注
  string remarks = 8;
  // 是否单独开票（商品合并）
  mairpc.common.types.BoolValue isMergeInvoicing = 9;
}

message UpdateInvoiceTemplateRequest {
  // @required
  //
  // 开票模版 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 税收分类编码
  string taxClassificationCode = 2; // valid:"required"
  // 税收分类名称
  string taxClassificationName = 3;
  // @required
  //
  // 优惠政策: nonuse（不使用）、zeroTax（出口零税）、freeTax（免税）、noTax（不征税）
  string favorablePolicy = 4; // valid:"required,in(nonuse|zeroTax|freeTax|noTax)"
  // 税率
  int64 taxRate = 5;
  // 开票名称
  string name = 6;
  // 开票类型： deliveryFee（运费）、product（商品）
  string type = 7; // in(deliveryFee|product)
  // 备注
  string remarks = 8;
  // 是否单独开票（商品合并）
  mairpc.common.types.BoolValue isMergeInvoicing = 9;
}

message ListInvoiceTemplatesRequest {
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 1;
  // 开票模版 ids
  repeated string ids = 2; // valid:"objectIdList"
  // 税收分类编码
  repeated string taxClassificationCodes = 3;
  // 开票类型： deliveryFee（运费）、product（商品）
  string type = 4; // in(deliveryFee|product)
}

message ListInvoiceTemplatesResponse {
  // 总数
  int64 total = 1;
  // 开票模版列表
  repeated mairpc.common.ec.InvoiceTemplate items = 2;
}

message InitCmsDefaultResourcesRequest {
  // 微信小程序 id，字节小程序 id，支付宝小程序 id
  repeated string appIds = 1;
}

message UpdateProductSettingRequest {
  // 货币单位
  //
  // 人民币 rmb， 港币 hkd
  string currencyUnit = 1; // valid:"in(rmb|hkd)"
  // 是否展示销量
  bool isDisplaySales = 2;
  // 最少展示销量，低于此值的销量不在小程序展示
  int64 minDisplaySales = 3;
  // 分享海报设置
  PosterSetting posterSetting = 4;
  // 是否展示商品关键词
  bool commonKeywordUndisplayable = 5;
  // 是否展示商品角标（特殊关键词）
  bool specialKeywordUndisplayable = 6;
  // 是否自动下架无库存商品
  bool autoUnshelved = 7;
}

message PosterSetting {
  PosterStyle productStyle = 1;
  Poster productPoster = 2;
}

message PosterStyle {
  // 分享海报样式
  //
  // 二维码样式 左侧 left 右侧 right 底部 bottom 顶部 top
  string type = 1; // valid:"in(left|right|top|bottom)"
  PosterBackground background = 2;
  string productNameColor = 3;
  string priceColor = 4;
  string originPriceColor = 5;
  string posterColor = 6;
  string campaignTagColor = 7;
}

message PosterBackground {
  // 分享海报背景类型
  //
  // 分享海报背景类型 纯色 color 图片 picture
  string type = 1; // valid:"in(color|picture)"
  // 分享海报颜色
  string color = 2;
  // 分享海报背景图片
  string picture = 3;
}

message Poster {
  // 分享文案类型
  //
  // 商品分享文案 product 自定义分享文案 custom
  string type = 1; // valid:"in(product|custom)"
  // 分享文案
  string text = 2;
}

message GetProductSettingResponse {
  // 货币单位
  //
  // 人民币 rmb， 港币 hkd
  string currencyUnit = 1;
  // 是否展示销量
  bool isDisplaySales = 2;
  // 最少展示销量，低于此值的销量不在小程序展示
  int64 minDisplaySales = 3;
  // 分享海报设置
  PosterSetting posterSetting = 4;
  // 是否展示商品关键词
  bool commonKeywordUndisplayable = 5;
  // 是否展示商品角标（特殊关键词）
  bool specialKeywordUndisplayable = 6;
  // 是否自动下架无库存商品
  bool autoUnshelved = 7;
  // 是否允许开启自动下架
  bool enableAutoUnshelved = 8;
}

message InitDefaultRecommendedProductSettingRequest {
  repeated RecommendedProductSetting recommendedProductSetting = 1;
}

message RecommendedProductSetting {
  // @required
  //
  // 推荐场景 商品详情（product）、购物车（cart）、支付完成（paymentResult）、个人中心（me）、评论完成（comment）、订单列表（orderList）、搜索结果（search）
  string scene = 1; // valid:"required,in(product|cart|paymentResult|me|comment|orderList|search)"
  // 推荐规则
  Rule rule = 2; // valid:"optional"
  // 是否开启
  bool enabled = 3;
}

message Rule {
  // @required
  //
  // 推荐类型：智能推荐（smart）、访问最多（mostVisits）、销量最多（mostSales）、自定义（custom）
  string type = 1; // valid:"required,in(smart|mostVisits|mostSales|custom)"
  // 商品列表
  repeated string productIds = 2;
}

message UpdateRecommendedProductSettingRequest {
  // @required
  //
  // 推荐设置 ID
  string id = 1; // valid:"required,objectId"
  // 推荐规则
  Rule rule = 2; // valid:"optional"
  // 是否开启
  bool enabled = 3;
  // 商品详情场景中的特殊商品规则
  repeated BriefSpecificProduct products = 4;
}

message BriefSpecificProduct {
  // 指定的商品 id
  string productId = 1;
  // 绑定的推荐商品
  repeated string recommendedProductIds = 2;
}

message UpdateHotProductSettingRequest {
  // @required
  //
  // 热卖榜类型
  string type = 1; // valid:"required,in(all|category|brand)"
  // @required
  //
  // 展示商品数量
  int64 limit = 2; // valid:"required"
  // 展示销量
  bool displaySales = 3;
  // 是否开启
  bool enabled = 4;
}

message GetHotProductSettingResponse {
  // 热卖榜类型
  string type = 1;
  // 是否展示销量
  bool displaySales = 2;
  // 展示数量
  int64 limit = 3;
  // 是否开启
  bool enabled = 4;
}

message ListRecommendedProductSettingsRequest {
  // 分页条件
  mairpc.common.request.ListCondition listCondition = 1;
}

message ListRecommendedProductSettingResponse {
  // 总数
  int64 total = 1;
  // 商品推荐设置列表
  repeated RecommendedProductSettingDetail items = 2;
}

message RecommendedProductSettingDetail {
  // 推荐场景 商品详情（product）、购物车（cart）、支付完成（paymentResult）、个人中心（me）、评论完成（comment）、订单列表（orderList）、搜索结果（search）
  string scene = 1;
  // 推荐规则
  RuleDetail rule = 2;
  // 是否开启
  bool enabled = 3;
  // ID
  string id = 4;
  // 商品详情场景中制定规则的商品
  repeated SpecificProductInRecommendedSetting products = 5;
}

message RecommendSearchKeyword {
  string keyword = 1;
  Link link = 2;
  string id = 3;
}

message Link {
  // 链接值
  string value = 1;
  // 跳转小程序id
  string appId = 2;
  // 链接类型
  string type = 3;
  // 状态
  string status = 4;
  // 自定义参数
  map<string, string> args = 5;
}

message RuleDetail {
  // 推荐类型：智能推荐（smart）、访问最多（mostVisits）、销量最多（mostSales）、自定义（custom）
  string type = 1;
  // 商品列表
  repeated mairpc.common.ec.BriefProductDetail products = 2;
}

message ProductDetail {
  string id = 1;
  string picture = 2;
  string name = 3;
  string number = 4;
  uint64 price = 5;
  string status = 6;
  bool public = 7;
}

message MarketingStyle {
  // 跟随店铺配色方案
  bool enable = 1;
  // 价格文本颜色
  string priceColor = 2;
  // 划线价文本颜色
  string originPriceColor = 3;
  // 营销标签颜色
  string tagColor = 4;
}

message SpecificProductInRecommendedSetting {
  // 商品 id
  string id = 1;
  // 商品名称
  string name = 2;
  // 商品编号
  string number = 3;
  // 商品图片
  string picture = 4;
  // 商品价格
  uint64 price = 5;
  // 关联的推荐商品列表
  repeated string recommendedProductIds = 6;
}
