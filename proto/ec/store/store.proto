syntax = "proto3";

package mairpc.ec.store;

option go_package = "store";

import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/response/response.proto";
import "common/types/types.proto";

message ListStoresRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // 关键字搜索
  //
  // 门店名称/门店编号
  string searchKey = 2;
  // 地址
  //
  // 最小粒度为市
  repeated mairpc.common.types.Location locations = 3;
  // 是否已分配员工
  mairpc.common.types.BoolValue hasStaff = 4;
  // 是否已设置营业时间
  mairpc.common.types.BoolValue hasBusinessHour = 5;
  // 门店状态
  string status = 6; // valid:"in(open|closed)"
  // 查找支持物流的门店
  mairpc.common.types.BoolValue isDeliveryEnabled = 7;
  // 门店ID
  repeated string ids = 8; // valid:"objectIdList"
  // 门店等级 ID
  string levelId = 9; // valid:"objectId"
  // 门店来源，从企业微信同步过来的门店 source 为 wechatwork
  string source = 10;
  // 门店 type，可取值 2、3，2 表示经销商, 3 表示零售门店
  repeated int32 types = 11;
  // ids 字段是否使用 $nin 查询
  bool idsIsNin = 12;
  // 零售公司或经销商 ids，传了该参数只会返回 distributorIds 对应的经销商下所有子经销商和子门店，包含自身
  repeated string distributorIds = 13;
  // 代理商 1，普通经销商 2，大客户商 3，门店 4，微店 5，网店 6
  int32 subType = 14;
  // 门店 code
  repeated string codes = 15;
  // 忽略的 subTypes
  repeated int32 ignoreSubTypes = 16;
  Contact contact = 17;
  // 是否需要判断同城快送门店
  bool needCheckCityExpressStore = 18;
  // 排除掉的地址
  repeated mairpc.common.types.Location excludedLocations = 19;
  // 其他字段
  //
  // 此参数目前只适用于 briefStore 接口
  // 默认不计算 memberCount，如果需要通过 extraFields 中标识
  // parentStructure 返回门店父级层级，如：华东区/上海/浦东新区
  repeated string extraFields = 20;
  // 创建时间
  mairpc.common.types.StringDateRange createdAt = 21;
  // 更新时间
  mairpc.common.types.StringDateRange updatedAt = 22;
  // 是否查询已删除的
  bool containDeleted = 23;
  // 门店级别 ids
  repeated string storeLevelIds = 24; // valid:"objectIdList"
  // 门店类型 ids
  repeated string storeTypeIds = 25; // valid:"objectIdList"
  // 创建人 id 列表
  repeated string createdBy = 26; // valid:"objectIdList"
  // 筛选所属组织节点
  mairpc.common.types.DistributorSelector distributor = 27; // valid:"optional"
  // 启用状态
  mairpc.common.types.BoolValue isEnabled = 28;
  // 是否统计未激活的员工数
  bool containInactive = 29;
  // storeLevelIds 字段是否用 $nin 查询
  bool storeLevelIdsIsNin = 30;
  // 父级门店 ids
  repeated string parentIds = 31;
  // parentIds 字段是否使用 $nin 查询
  bool parentIdsIsNin = 32;
  // 下游企业渠道 ids
  repeated string chainCorpChannelIds = 33; // valid:"objectIdList"
  // 关键字搜索
  //
  // 店主姓名/手机号
  string staffSearchKey = 34;
  // 门店同步状态
  string syncStatus = 35; // valid:"in(syncing|done)"
  // 门店所属经销商 ID
  //
  // 脉盟小店
  string distributorId = 36; // valid:"objectId"
  // 门店所属经销商租户 ID
  //
  // 脉盟小店
  string distributorAccountId = 37; // valid:"objectId"
  // 门店标签
  repeated string tags = 38;
  // 门店标签使用 $nin
  bool storeTagsIsNin = 39;
  // 门店是否创建了易宝分账方
  mairpc.common.request.BoolRequest haveYeepayMerchant = 40;
  // 是否不返回总记录数
  bool withoutTotal = 41;
}

message ListStoresResponse {
  int64 total = 1;
  repeated StoreDetail items = 2;
}

message StoreDetail {
  // 门店名
  string name = 1;
  // 门店代码
  string code = 2;
  // 门店地址
  StoreLocation location = 3;
  // 工作时间
  repeated BusinessHour businessHours = 4;
  // 地理位置
  mairpc.common.response.Coordinate coordinate = 5;
  // 联系人手机号
  string phone = 6;
  // 门店状态
  string status = 7;
  // 创建自
  mairpc.common.response.UserInfo createdBy = 8;
  // 最新一次更新自
  mairpc.common.response.UserInfo updatedBy = 9;
  // 创建时间
  string createdAt = 10;
  // 更新时间
  string updatedAt = 11;
  // 距离
  uint64 distance = 12;
  // 门店 ID
  string id = 13;
  // 租户 ID
  string accountId = 14;
  // 店员数量
  uint64 staffCount = 15;
  // 快递设置
  StoreDeliverySetting deliverySetting = 16;
  // 门店等级 ID
  string levelId = 17;
  // 部门 ID，企业微信部门同步过来的门店才有
  string departmentId = 18;
  // 父部门 ID，企业微信部门同步过来的门店才有
  string parentDepartmentId = 19;
  // 门店来源
  string source = 20;
  Contact contact = 21;
  // 门店类型
  int64 type = 22;
  // 是否是同城配送店铺
  bool isCityExpressStore = 23;
  // 同城快送信息
  CityExpressSetting cityExpressSetting = 24;
  string qrcodeUrl = 25;
  // 代理商 1，普通经销商 2，大客户商 3，门店 4，微店 5，网店 6
  int64 subType = 26;
  // 父级门店/部门
  string parentId = 27;
  // 门店类型 ID
  string storeTypeId = 28;
  // 上级组织名称
  string parentDepartmentName = 29;
  // 门店类型名称
  string storeTypeName = 30;
  // 门店级别名称
  string storeLevelName = 31;
  // 父级组织 ID 列表
  repeated string distributorIds = 32;
  repeated CustomInfo customInfo = 33;
  // 门店描述
  string introduction = 34;
  // 门店客服
  Helpdesk helpdesk = 35;
  // 门店对应的 distributionStore 小店信息
  DistributionStoreInfo distributionStoreInfo = 36;
  // 是否已经注册易宝商户
  bool haveYeepayMerchant = 37;
  // 门店标签
  repeated string tags = 38;
  string closedAt = 39;
  string openedAt = 40;
  // 自动闭店时间
  string autoCloseAt = 41;
  // 自动开店时间
  string autoOpenAt = 42;
  // 英文名
  string nameEn = 43;
  // 下游渠道
  string chainCorpChannelId = 44;
}

message Helpdesk {
  // 客服人员
  repeated HelpDeskStaff staffs = 1;
  // 企微客服链接
  string link = 2;
  // 客服电话
  string phone = 3;
}

message HelpDeskStaff {
  // 导购名称
  string name = 1;
  // 导购 id
  string staffId = 2;
}

message DistributionStoreInfo {
  // 经销商信息
  repeated Distributor distributors = 1;
  // 商户号
  string merchantNumber = 2;
  // 商户名称
  string merchantName = 3;
  // 营业执照照片
  string licenseCopy = 4;
}

message Distributor {
  string id = 1;
  string accountId = 2;
  string type = 3;
  string status = 4;
}

message CustomInfo {
  // 标题
  string title = 1;
  // 内容
  string content = 2;
}

message CityExpressSetting {
  // 配送范围
  float range = 1;
  // 送达时间
  uint64 arriveTime = 2;
  // 起送费
  uint64 basePrice = 3;
  // 运费模板
  common.ec.DeliveryFeeTemplate deliveryFeeTemplate = 4;
}

message Contact {
  string name = 1;
  string phone = 2;
  string email = 3;
  string gender = 4;
  string avatar = 5;
}

message StoreDeliverySetting {
  // 是否支持
  bool isEnabled = 1;
  // 支持时间
  string enabledAt = 2;
  // 门店联系人姓名
  string contactName = 3;
  // 门店联系人电话
  string contactTel = 4;
  // 是否设置为默认门店
  bool isDefault = 5;
  // 配送方式 可选值：到店自提（pickup），快递配送（express），同城配送（cityExpress）
  repeated string deliveryMethods = 6; // valid:"in(pickup|express|cityExpress)"
}

message StoreLocation {
  //　省（直辖市）
  string province = 1;
  // 区
  string city = 2;
  // 镇
  string district = 3;
  // 详细地址
  string name = 4;
  string areaCode = 5;
  float longitude = 6;
  float latitude = 7;
  // 地址全称
  string fullName = 8;
}

message StoreLocationWithCoordinate {
  //　省（直辖市）
  string province = 1;
  // 区
  string city = 2;
  // 镇
  string district = 3;
  // 详细地址
  string name = 4;
  string areaCode = 5;
  // 地址全称
  string fullName = 6;
  // 坐标
  Coordinate coordinate = 7;
}

message Coordinate {
  double longitude = 1;
  double latitude = 2;
}

message BusinessHour {
  repeated string workweek = 1;
  string startTime = 2;
  string endTime = 3;
}

message NearStoresRequest {
  // 分页信息
  //
  // 无法指定排序，返回结果的顺序一定是与门店距离的倒序
  mairpc.common.request.ListCondition listCondition = 1;
  // 坐标
  //
  // 指定客户的坐标与距离，距离单位是米
  mairpc.common.request.PointRequest coordinate = 2;
  // 地址
  //
  // 如有需要可以用该字段来让api只返回某个省、市的门店
  mairpc.common.types.Location location = 3;
  // 门店 ID
  //
  // 有时候我们需要类似“展示某个店员所属的门店”这样的搜索，因此参数中加上门店 ID 列表。
  repeated string storeIds = 4; // valid:"objectIdList"
  // 门店名称
  string storeName = 5;
  // 是否是同城快送
  bool isCityExpressStore = 6;
  // 选择过的门店 id
  repeated string selectedStoreIds = 7; // valid:"objectIdList"
  // 门店状态
  //
  // open: 营业中，closed: 已歇业
  string status = 8; // valid:"in(open|closed)"
  // 筛选所属组织节点
  mairpc.common.types.DistributorSelector distributor = 9; // valid:"optional"
  // 门店类型
  repeated string storeTypeIds = 10; // valid:"objectIdList"
  // 查找支持物流的门店
  mairpc.common.types.BoolValue isDeliveryEnabled = 11;
  // 门店是否开启
  mairpc.common.types.BoolValue isEnabled = 12;
  //   是否开启距离矩阵计算
  bool distanceMatrix = 13;
  // 地址列表
  //
  // 同 location 字段，此字段有值时 location 字段失效
  repeated mairpc.common.types.Location locations = 14;
  // 门店是否是默认门店
  mairpc.common.types.BoolValue isDefault = 15;
  // 门店是否是绑定门店
  mairpc.common.types.BoolValue isBoundStore = 16;
  // 客户 id
  string memberId = 17; // valid:"objectId"
  // 门店标签
  repeated string tags = 18;
}

message NearStoresResponse {
  repeated StoreDetail items = 1;
  int64 total = 2;
}

message UpdateStoresStatusRequest {
  // @required
  //
  // 门店ID
  repeated string ids = 1; // valid:"required,objectIdList"
  // @required
  //
  // 门店状态
  string status = 2; // valid:"in(open|closed)"
}

message UpdateDeliveryStoreRequest {
  // @required
  //
  // 门店 ID
  string id = 1; // valid:"required,objectId"
  // 联系人姓名
  string contactName = 2;
  // 联系人手机号/座机号
  string contactTel = 3;
}

message CreateDeliveryStoresResponse {
  // 失败个数
  uint64 failCount = 1;
  // 成功个数
  uint64 successCount = 2;
}

message ImportStoresRequest {
  repeated ImportStoreItem stores = 1; // valid:"required"
  // @required
  //
  // 来源，如 wechatwork 企业微信，目前只支持 wechatwork
  string source = 2; // valid:"required,in(wechatwork)"
  // 地图经纬度类型，amap：高德(默认)，baidu：百度
  string mapType = 3; // valid:"in(amap|baidu)"
}

message ImportStoreItem {
  // @required
  //
  // 门店/部门名称
  string name = 1; // valid:"required"
  // @required
  //
  // 部门 ID
  string departmentId = 2; // valid:"required"
  // 父部门 ID
  string parentDepartmentId = 3;
  // 门店联系人名称
  string contactName = 4;
  // 门店联系电话
  string contactTel = 5;
  // 门店地址
  StoreLocationWithCoordinate location = 6;
  // 门店营业状态，closed：停止营业(默认)，open：营业中
  string status = 7; // valid:"in(closed|open)"
  // 门店类型
  //
  // 2：经销商/部门，3：零售门店
  // 全部不传此参数一次性导入所有部门/门店，全部传入此参数可支持分批次增量导入，不允许只有部分部门/门店传递此参数
  uint64 type = 8; // valid:"in(2|3)"
}

message ListBriefStoresResponse {
  int64 total = 1;
  repeated BriefStoreDetail items = 2;
}

message BriefStoreDetail {
  // 门店 ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 门店名
  string name = 3;
  // 联系人手机号
  string phone = 4;
  // 门店状态
  string status = 5;
  // 店员数量
  uint64 staffCount = 8;
  // 绑定客户数
  //
  // 默认不计算客户数，需要的话通过 extraFields 标识
  uint64 memberCount = 9;
  // 门店代码
  string code = 10;
  // 部门 ID，企业微信部门同步过来的门店才有
  string departmentId = 11;
  // 父部门 ID，企业微信部门同步过来的门店才有
  string parentDepartmentId = 12;
  // 门店地址
  StoreLocation location = 13;
  // 门店类型 ID
  string storeTypeId = 14;
  // 门店等级 ID
  string levelId = 15;
  // 父级组织 ID 列表
  repeated string distributorIds = 16;
  // 上级组织 id
  string parentId = 17;
  // 工作时间
  repeated BusinessHour businessHours = 18;
  // 门店 type，可取值 2、3，2 表示经销商, 3 表示零售门店
  int32 type = 19;
  // 门店 subType
  int32 subType = 20;
  // 门店标签
  repeated string tags = 21;
  // 同步状态
  string syncStatus = 22;
  // 门店层级
  //
  // 默认不返回，只有请求参数 extraFields 中存在 parentStructure 才会返回
  string parentStructure = 23;
  // 英文名
  string nameEn = 24;
  // 门店备注
  string introduction = 25;
}

message ListThirdPartyStoresRequest {
  // 分页信息
  //
  // 通过第三方接口获取，不支持排序，需根据第三方平台适配传参规则
  // 不传此参数则获取第三方平台上的所有门店
  mairpc.common.request.ListCondition listCondition = 1;
  // 店铺编号
  string shopNo = 2;
  // 是否获取所有
  //
  // 此参数会忽略 listCondition 参数
  bool listAll = 3;
}

message ListThirdPartyStoresResponse {
  int64 total = 1;
  repeated ThirdPartyShop items = 2;
}

message ThirdPartyShop {
  // 平台 ID
  string platformId = 1;
  // 子平台 ID
  string subPlatformId = 2;
  // 店铺 ID
  string shopId = 3;
  // 店铺编号
  string shopNo = 4;
  // 店铺名称
  string shopName = 5;
  // 平台授权账号
  //
  // 注意这是第三方接口的品台授权账号 ID，不是群脉系统中的 accountId
  string thirdPartyAccountId = 6;
  // 平台授权账号昵称
  string thirdPartyAccountNickname = 7;
  // 地址
  mairpc.common.types.Location address = 8;
  // 联系人
  string contact = 9;
  // 移动电话
  string mobile = 10;
  // 固定电话
  string fixedTel = 11;
  // 备注
  string remark = 12;
}

message UpdateStoreRequest {
  // @required
  //
  // 门店 ID
  string id = 1; // valid:"required,objectId"
  // 门店状态
  string status = 2; // valid:"in(closed|open)"
  // 门店名称
  string storeName = 3;
  // 门店联系人名称
  string name = 4;
  // 门店联系人电话
  string phone = 5;
  // 门店联系人邮箱
  string email = 6;
  // 门店联系人性别
  string gender = 7;
  int32 type = 8;
  int32 subType = 9;
  StoreLocation location = 10;
  mairpc.common.types.BoolValue isEnabled = 11;
  // 门店店主头像
  string avatar = 12;
  // 门店描述
  string introduction = 13;
}

message CreateStoreRequest {
  // 门店名称
  string id = 1; // valid:"objectId"
  // 门店名称
  string storeName = 2;
  // 门店联系人名称
  string name = 3;
  // 门店联系人电话
  string phone = 4;
  // 门店联系人邮箱
  string email = 5;
  // 门店联系人性别
  string gender = 6;
  int32 type = 7;
  int32 subType = 8;
  StoreLocation location = 9;
  string code = 10;
  // 门店描述
  string introduction = 11;
}

message GetParentStoresRequest {
  // @required
  //
  // 门店 Ids
  repeated string storeIds = 1; // valid:"required,objectIdList"
  // 是否值获取直接父级门店
  bool isDirectParentStore = 2;
}

message GetChildStoresRequest {
  // @required
  //
  // 经销商 Ids
  repeated string storeIds = 1; // valid:"required,objectIdList"
  // 是否只返回门店，不包含经销商
  bool onlyReturnStore = 2;
}

message BatchUpdateStaffStatusRequest {
  // @required
  //
  // 导购 Ids
  repeated string staffIds = 1; // valid:"required,objectIdList"
  // @required
  //
  // 导购状态
  int64 status = 2; // valid:"required"
}

message ListCityExpressStoresRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // 关键字搜索
  //
  // 门店名称/门店编号
  string searchKey = 2;
  // 地址
  //
  // 最小粒度为市
  repeated mairpc.common.types.Location locations = 3;
}

message GetCityExpressStoreRequest {
  // @required
  //
  // 门店 ID
  string storeId = 1; // valid:"required,objectId"
}

message CityExpressStoreDetail {
  // 门店名
  string name = 1;
  // 门店代码
  string code = 2;
  // 门店地址
  StoreLocation location = 3;
  // 工作时间
  repeated BusinessHour businessHours = 4;
  // 地理位置
  mairpc.common.response.Coordinate coordinate = 5;
  // 门店状态
  string status = 6;
  // 距离
  uint64 distance = 7;
  Contact contact = 8;
  // 配送范围
  float range = 9;
  // 送达时间
  uint64 arriveTime = 10;
  // 门店 ID
  string storeId = 11;
  // 起送金额
  uint64 basePrice = 12;
  // 配送规则
  Delivery delivery = 13;
  // 运费规则
  DeliveryFeeTemplate deliveryFeeTemplate = 14;
}

message Delivery {
  // 编辑者
  SimpleUser updater = 13;
  // 编辑时间
  string updatedAt = 2;
  // 编辑者用户角色
  string userRole = 3;
}

message SimpleUser {
  string id = 1;
  string name = 2;
}

message DeliveryFeeTemplate {
  // 模板类型
  //
  // piece 按件数，weight 按重量，distance 按距离
  string type = 1;
  // 是否指定条件包邮
  bool conditionFree = 2;
  // 指定包邮金额类型
  //
  // totalAmount 按订单金额计算包邮，payAmount 按实付金额计算包邮
  string amountType = 3;
  // 运费规则
  DeliveryFeeTemplateRule rule = 4;
  // 免邮规则
  DeliveryFeeTemplateFreeRule freeRule = 5;
  // 编辑者
  SimpleUser updater = 6;
  // 编辑时间
  string updatedAt = 7;
  // 编辑者用户角色
  string userRole = 8;
}

message DeliveryFeeTemplateRule {
  // 首重(件)价格(单位: 分)，商品首重(件)计数内按首重(件)价格计费
  uint64 firstFee = 1;
  // 续重(件)价格(单位: 分)，即每续重(件)xx，增加运费xx分，大于0的数字
  uint64 additionalFee = 2;
  // 首重(件)数，当 type 为 weight 时单位为克
  uint64 firstAmount = 3;
  // 续重(件)数，当 type 为 weight 时单位为克
  uint64 additionalAmount = 4;
}

message DeliveryFeeTemplateFreeRule {
  // @required
  //
  // 规则类型，amount 指定金额，weight 指定重量，piece 指定件数，distance 指定距离，amountAndWeight 金额 + 重量，amountAndPiece 金额 + 件数，amountAndDistance 金额 + 距离
  string type = 1;
  // 金额(单位: 分)
  uint64 amount = 2;
  // 重量(单位: 克)
  uint64 weight = 3;
  // 件数
  uint64 pieces = 4;
  // 距离（单位：米）
  uint64 distance = 5;
}

message ListCityExpressStoresResponse {
  int64 total = 1;
  repeated CityExpressStoreDetail items = 2;
}

message BatchUpsertCityExpressStoresRequest {
  // 门店编号
  repeated string storeIds = 1; // valid:"required,objectIdList"
  // 配送范围
  float range = 2;
  // 送达时间
  uint64 arriveTime = 3;
  // 起送金额
  uint64 basePrice = 4;
  // 运费模板
  common.ec.DeliveryFeeTemplate deliveryFeeTemplate = 5;
}

message RemoveCityExpressStoresRequest {
  // 门店编号
  repeated string storeIds = 1; // valid:"required,objectIdList"
}

message SyncWechatcpDepartmentAndUserResponse {
  // 异步执行生产的唯一 code，前端用来轮循执行结果
  string code = 1;
}

message GetStoreStatsTrendRequest {
  // 门店 id
  repeated string storeIds = 1;
  // 日期区间
  mairpc.common.types.StringDateRange dateRange = 3;
  // 日期类型
  //
  // 按天(daily)，按周（weekly），按月（monthly）
  string dateType = 6; // valid:"in(daily|weekly|monthly)"
  // 筛选所属组织节点
  mairpc.common.types.DistributorSelector distributor = 7; // valid:"optional"
  repeated string createdBy = 8;
  // 导出内容类型
  //
  // 卡券(coupon)，客户(member)
  string type = 9; // valid:"in(coupon|member)"
}

message GetStoreStatsTrendResponse {
  // 关键指标总览
  StoreStatsTrendTotal total = 1;
  // 新增客户数列表
  repeated int64 newMemberCounts = 2;
  // 客户流失数列表
  repeated int64 lostMemberCounts = 3;
  // 卡券赠送数列表
  repeated int64 receivedCouponCounts = 4;
  // 触达-转化客户数列表
  repeated int64 redeemedCouponCounts = 5;
  // 触达-转化率列表
  repeated int64 newActiveMemberCounts = 6;
  // 扫码-转化客户数列表
  repeated int64 newTradeMemberCounts = 7;
  // 扫码-转化率列表
  repeated int64 newVipMemberCounts = 8;
  // 邀请入会客户数
  repeated int64 inviteMemberCounts = 9;
  // 日期列表
  repeated string dates = 10;
}

message StoreStatsTrendTotal {
  // 新增客户数
  int64 newMemberCount = 1;
  // 客户流失数
  int64 lostMemberCount = 2;
  // 卡券赠送数
  int64 receivedCouponCount = 3;
  // 触达-转化客户数
  int64 redeemedCouponCount = 4;
  // 触达-转化率
  int64 newActiveMemberCount = 5;
  // 扫码-转化客户数
  int64 newTradeMemberCount = 6;
  // 扫码-转化率
  int64 newVipMemberCount = 7;
  // 邀请入会客户数
  int64 inviteMemberCount = 8;
}

message GetStoreStatsContrastRequest {
  // 门店 id
  repeated string storeIds = 1;
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 2;
  // 日期区间
  mairpc.common.types.StringDateRange dateRange = 3;
  // 数据分块
  //
  // 数据概览（1,2）客户数据（3）优惠券数据（4）
  int32 tab = 4; // valid:"in(1|2|3|4)"
  // 筛选所属组织节点
  mairpc.common.types.DistributorSelector distributor = 5; // valid:"optional"
  repeated string createdBy = 6;
}

message GetStoreStatsContrastResponse {
  // 总数
  int64 total = 1;
  // 门店数据列表
  repeated GetStoreStatsContrastItem items = 2;
}

message GetStoreStatsContrastItem {
  // 门店 id
  string storeId = 1;
  // 门店名称
  string storeName = 2;
  // 零售公司 id
  string distributorId = 3;
  // 零售公司名称
  string distributorName = 4;
  // 导购总数
  int64 staffCount = 5;
  // 客户总数
  int64 memberCount = 6;
  // 活跃客户占比
  float activeMemberRate = 7;
  // 会员客户占比
  float vipMemberRate = 8;
  // 交易客户占比
  float tradeMemberRate = 9;
  // 新增客户数
  int64 newMemberCount = 10;
  // 流失客户数
  int64 lostMemberCount = 11;
  // 新增活跃客户数
  int64 newActiveMemberCount = 12;
  // 新增会员客户数
  int64 newVipMemberCount = 13;
  // 新增交易客户数
  int64 newTradeMemberCount = 14;
  // 卡券发放数
  int64 receivedCouponCount = 15;
  // 卡券核销数
  int64 redeemedCouponCount = 16;
  // 导购邀请入会客户数
  int64 inviteMemberCount = 17;
  // 绑定入会客户总数
  int64 boundActiveMemberCount = 18;
}

message StatsStoreByDateRequest {
  // 统计日期
  //
  // 格式类似与 2021-07-15
  string date = 1;
}

message ListApplicableStoresForCouponEndpointRequest {
  // @required
  //
  // 优惠券终端 id
  string id = 1; // valid:"required"
  // 分页信息
  //
  // 当包含坐标信息时，无法指定排序，返回结果的顺序一定是门店距离的升序
  mairpc.common.request.ListCondition listCondition = 2;
  // 坐标
  //
  // 指定客户的坐标与距离，距离单位是米
  mairpc.common.request.PointRequest coordinate = 3;
  // 地址
  //
  // 如有需要可以用该字段来让api只返回某个省、市、区的门店
  mairpc.common.types.Location location = 4;
}

message StoreIdsResponse {
  repeated string ids = 1;
}

message CreateChainCorpRequest {
  // @required
  //
  // 租户 id
  string accountId = 1;
  // @required
  //
  // 渠道 id
  string channelId = 2;
  // @required
  //
  // 下游企业信息
  repeated ChainCorp accounts = 3;
}

message ChainCorp {
  // 下游企业渠道 id
  string id = 1;
  // 下游企业应用 appId
  string appId = 2;
  // 下游企业名称
  string corpName = 3;
  // 下游企业渠道名称
  string name = 4;
  // 下游企业 corpId
  string corpId = 5;
  // 下游企业 WEAPP 第三方应用 id
  string weappChannelId = 6;
  // 已分配激活额度
  int64 licenseCount = 7;
  // 已激活额度
  int64 activatedCount = 8;
  // 可分配额度
  int64 availableCount = 9;
}

message ListChainCorpsRequest {
  // 分页
  mairpc.common.request.ListCondition listCondition = 1;
  // 下游企业名称
  string corpName = 2;
}

message ListChainCorpsResponse {
  repeated ChainCorp items = 1;
  int64 total = 2;
}

message GetChainCorpRequest {
  // @required
  //
  // 渠道 id
  string channelId = 1;
  // 下游渠道 id
  string chainCorpChannelId = 2;
  // 下游企业 id
  string chainCorpId = 3;
  // 是否获取 WEAPP 类型 channel
  bool fetchWeappChannelInfo = 4;
}

message GetStaffSensitiveRequest {
  // @required
  //
  // 导购 id
  string staffId = 1; // valid:"required,objectId"
  // @required
  //
  // 敏感信息字段
  string sensitiveKey = 2; // valid:"in(name|phone|staffNo),required"
  // 经销商组织中所选的 distributorId
  string selectedDistributorId = 3; // valid:"objectId"
}

message ListStoresByMemberIdRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // 关键字搜索
  //
  // 门店名称/门店编号
  string searchKey = 2;
}

message BindStoreRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
}

message GetBoundStoreRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
}

message BoundStoreDetail {
  // 绑定门店记录 id
  string id = 1;
  // 租户 id
  string accountId = 2;
  // 门店 id
  string storeId = 3;
  // 客户 id
  string memberId = 4;
}

message ExportStoreQrcodesRequest {
  // 门店 ids
  repeated string ids = 1; // valid:"objectIdList"
  // 导出码类型
  //
  // 可取值 store(门店码)，invitation(分账接收方注册邀请码),默认不填为 store
  string codeType = 2;
}

message UpsertTagGroupRequest {
  // 标签组名称
  string id = 1; // valid:"optional,objectId"
  // @required
  //
  // 标签组名称
  string name = 2; // valid:"required"
}

message CreateTagRequest {
  // 标签组 id
  string groupId = 1; // valid:"optional,objectId"
  // @required
  //
  // 标签名
  string name = 2; // valid:"required"
}

message BatchMoveTagsToGroupRequest {
  // @required
  //
  // 标签 id 列表
  repeated string tagIds = 1; // valid:"required,objectIdList"
  // @required
  //
  // 移动到的群组 id
  string groupId = 2; // valid:"required,objectId"
}

message ListTagGroupsRequest {
  // 分页
  mairpc.common.request.ListCondition listCondition = 1;
  // 支持标签名称搜索
  string searchKey = 2;
  // 是否返回包含的标签
  bool withTags = 3;
}

message ListTagGroupsResponse {
  repeated TagGroupDetail items = 1;
  int64 total = 2;
}

message TagGroupDetail {
  string id = 1;
  string name = 2;
  bool isDefault = 3;
  int64 tagCount = 4;
  repeated TagDetail tags = 5;
}

message ListTagsRequest {
  // 分页
  mairpc.common.request.ListCondition listCondition = 1;
  // 群组 id
  string groupId = 2; // valid:"optional,objectId"
  // 支持标签名称搜索
  string searchKey = 3;
}

message ListTagsResponse {
  repeated TagDetail items = 1;
  int64 total = 2;
}

message TagDetail {
  string id = 1;
  string name = 2;
  string groupId = 3;
  int64 distributorCount = 4;
}

message UpdateTagDistributorCountRequest {
  // 键为 tagName，值为变动的数量，可以是负数
  map<string, int64> countMap = 1;
}

message BatchCreateTagsRequest {
  // @required
  //
  // 标签
  repeated string tags = 1; // valid:"required"
  // 标签组 id
  string groupId = 2;
  // 是否忽略已存在标签
  bool ignoreExistTags = 3;
}

message StoreBoundStaffMemberCount {
  // 门店 ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 门店名
  string name = 3;
  // 绑定客户数
  //
  // 默认不计算客户数，需要的话通过 extraFields 标识
  uint64 memberCount = 4;
}

message CountStoreBoundStaffMembersResponse {
  int64 total = 1;
  repeated StoreBoundStaffMemberCount items = 2;
}
