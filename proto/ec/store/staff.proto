syntax = "proto3";

package mairpc.ec.store;

option go_package = "store";

import "common/request/request.proto";
import "common/response/response.proto";
import "common/types/types.proto";
import "ec/store/store.proto";

message ListStaffsRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // 搜索关键字
  string searchKey = 2;
  // 所属门店
  repeated string storeIds = 3; // valid:"objectIdList"
  // 店员状态
  //
  // 1: 在职、已激活
  // 2: 无法同步
  // 3: 待激活
  // 4: 未生效
  // 5: 离职
  repeated int64 status = 4;
  // 店员ID
  repeated string staffIds = 5; // valid:"objectIdList"
  // 店员来源，从企业微信同步过来的店员 source 为 wechatwork
  string source = 6;
  // 店员工号
  repeated string staffNos = 7;
  // 零售商 ids，只筛选出该零售商下门店中的导购
  repeated string distributorIds = 8;
  // 累计推广金额
  //
  // 单位：分
  mairpc.common.types.IntegerRange totalAmount = 9;
  // 导购分销功能是否冻结
  mairpc.common.types.BoolValue isPromoterFrozen = 10;
  // 子标签名称
  //
  // 按子标签所属标签组分组传递，同组下子标签关系为或，不同组子标签关系为且
  repeated Tags tags = 11;
  // 导购等级
  mairpc.common.types.UIntValue level = 12;
  // 导购岗位 id
  mairpc.common.types.StringValue roleId = 13;
  // 其他字段
  //
  // 默认不返回下述字段 ，如果需要使用需要通过 extraFields 标识
  // memberCount（绑定客户数），allGroupchatCount（客户群总数），isNonTransferable（是否限制分配），resignedMemberCount（离职导购资源数）
  repeated string extraFields = 14;
  // 创建时间
  mairpc.common.types.StringDateRange createdAt = 15;
  // 更新时间
  mairpc.common.types.StringDateRange updatedAt = 16;
  // 导购属性
  repeated PropertyItem properties = 17;
  // 多个导购等级
  repeated int32 levels = 18;
  // 创建人 id 列表
  repeated string createdBy = 19; // valid:"objectIdList"
  // 筛选所属组织节点
  mairpc.common.types.DistributorSelector distributor = 20; // valid:"optional"
  // 是否是店员
  mairpc.common.types.BoolValue isStaff = 21;
  // 员工角色 Id 列表
  repeated string roleIds = 22; // valid:"objectIdList"
  // 下游企业渠道 ids
  repeated string chainCorpChannelIds = 30; // valid:"objectIdList"
  // 激活状态 activated（已激活），inactivated（未激活），expired（已逾期），expiredSoon（30天内到期）
  string activeStatus = 31; // valid:"in(activated|inactivated|expired|expiredSoon)"
  // 离职时间
  mairpc.common.types.StringDateRange resignedAt = 32;
  // 离职导购是否有资源待分配
  mairpc.common.types.BoolValue hasResource = 33;
  // 是否只返回基础字段
  //
  // 只返回员工表字段，不查询其他数据
  bool basicResp = 34;
  // 导购名称
  //
  // 精准匹配
  repeated string names = 35;
  // 搜索类型
  string searchType = 36;
  // 只显示开启过会话存档的导购
  bool onlyOpenMessageAudit = 37;
}

message Tags {
  repeated string names = 1;
}

message ListStaffsResponse {
  int64 total = 1;
  repeated StaffDetail items = 2;
}

message StaffDetailRequest {
  // 店员 ID
  //
  // 同时支持传 staffNo 店员编号
  string staffId = 1;
  // 二维码内容
  string qrcodeContent = 2;
  // 店员名称
  string name = 3;
  // 店员手机号
  string phone = 4;
  // 传门店 id 获取该门店下的一个导购
  string storeId = 5; // valid:"objectId"
  // 来源，如 wechatwork 企业微信，目前只支持 wechatwork
  string source = 6;
  // 店员邮箱
  string email = 7;
}

message StaffDetail {
  string id = 1;
  string accountId = 2;
  bool isDeleted = 3;
  mairpc.common.response.UserInfo createdBy = 4;
  mairpc.common.response.UserInfo updatedBy = 5;
  string createdAt = 6;
  string updatedAt = 7;
  string name = 8;
  string phone = 9;
  int64 gender = 10;
  string email = 11;
  int64 status = 12;
  // 门店详情
  //
  // 仅在需要展示门店信息时候返回
  repeated StoreDetail stores = 13;
  // 门店ID
  //
  // 若无需门店详情，则会在此处返回店员所属门店的ID
  repeated string storeIds = 14;
  string staffNo = 15;
  string currentStoreId = 16;
  string qrcodeStoreId = 17;
  string source = 18;
  string avatar = 19;
  repeated string departmentIds = 20;
  repeated PropertyItem properties = 21;
  string qrcode = 22;
  string position = 23;
  // 导购分销功能是否被冻结
  bool isPromoterFrozen = 24;
  string externalPosition = 25;
  repeated string tags = 26;
  // 自从上一次退出待添加客户列表后，成功匹配了几个好友
  int64 addedMemberCount = 27;
  // 导购等级
  Level level = 29;
  // 岗位 code
  Role role = 30;
  // 导购是否是企业微信应用管理员
  bool isWechatcpAdmin = 31;
  // 业务场景角色
  repeated BusinessRolesDetail roles = 32;
  // 所属组织列表
  repeated BriefStore accessibleDistributors = 33;
  // 下游企业渠道 id
  string chainCorpChannelId = 34;
  // 导购联系我二维码
  ContactWay contactWay = 35;
  repeated string distributorIds = 36;
  // 是否已邀请入驻分销员
  //
  // 该字段在连锁零售商租户才有意义，其余租户不要使用
  bool invited = 37;
  // 分销员是否存在
  bool isPromoterExists = 38;
  // 岗位
  repeated Position positions = 39;
  // 历史最高好友数
  uint64 maxMemberCount = 40;
  // 企业微信好友总数
  uint64 wechatMemberCount = 41;
}

message Position {
  // 岗位 ID
  string id = 1;
  // 岗位名称
  string name = 2;
}

message ContactWay {
  // 联系我二维码 id
  string id = 1;
  // 联系我二维码 url
  string imageUrl = 2;
}

message BusinessRoles {
  // @required
  //
  // 业务场景
  // wechatworkStaff：企微导购员工角色，retailStaff：零售门店角色
  string business = 1; // valid:"required"
  //
  // 组织角色列表
  repeated DistributorRole distributorRoles = 2; // valid:"optional"
}

message DistributorRole {
  // @required
  //
  // 组织 ids
  repeated string distributorIds = 1; // valid:"required,objectIdList"
  // @required
  //
  // 角色 ids
  repeated string roleIds = 2; // valid:"required,objectIdList"
}

message BusinessRolesDetail {
  // wechatworkStaff：企微导购员工角色，retailStaff：零售门店角色
  string business = 1;
  // 组织角色列表
  repeated DistributorRolesDetail distributorRoles = 2;
}

message DistributorRolesDetail {
  // 组织
  repeated DistributorInfo distributors = 1;
  // 角色
  repeated Role roles = 2;
}

message DistributorInfo {
  // 组织 ID
  string id = 1;
  // 组织名称
  string name = 2;
}

message Level {
  // 导购等级
  uint64 level = 1;
  // 导购等级名称
  string name = 2;
}

message IsLastStaffResponse {
  repeated StoreDetail stores = 1;
}

message SwitchStoreRequest {
  // @required
  //
  // 店员 ID
  string staffId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string switchStoreId = 2; // valid:"required,objectId"
}

message SyncStaffRequest {
  // @required
  //
  // 店员 id
  string userId = 1; // valid:"required"
  // @required
  //
  // 渠道 id
  string channelId = 2; // valid:"required,objectId"
}

message BatchImportStaffRequest {
  repeated ImportStaffItem items = 1; // valid:"required"
  // @required
  //
  // 来源，如 wechatwork 企业微信，目前只支持 wechatwork
  string source = 2; // valid:"required,in(wechatwork)"
}

message ImportStaffItem {
  // @required
  //
  // 员工姓名
  string name = 1; // valid:"required"
  // 员工性别
  //
  // 0：未定义(默认)，1：男性，2：女性
  int64 gender = 2; // valid:"in(0|1|2)"
  // 手机号
  string mobile = 3;
  // 邮箱
  string email = 4;
  // 头像
  string avatar = 5;
  // @required
  //
  // 员工编号
  string userId = 6; // valid:"required"
  // 所属部门 ID 列表
  repeated string departmentIds = 7;
  // 角色 ID
  string roleId = 9; // valid:"objectId"
  // 在职状态
  //
  // 1：在职, 2：离职
  int32 status = 10; // valid:"in(0|1|2)"
}

message ListBriefStaffsResponse {
  int64 total = 1;
  repeated BriefStaffDetail items = 2;
}

message BriefStaffDetail {
  string id = 1;
  string accountId = 2;
  bool isDeleted = 3;
  string createdAt = 6;
  string updatedAt = 7;
  string name = 8;
  string phone = 9;
  int64 gender = 10;
  string email = 11;
  int64 status = 12;
  // 门店详情
  //
  // 仅在需要展示门店信息时候返回
  repeated BriefStoreDetail stores = 13;
  string staffNo = 15;
  string currentStoreId = 16;
  // 导购绑定客户数
  uint64 memberCount = 17;
  uint64 totalAmount = 18;
  // 导购分销功能是否被冻结
  bool isPromoterFrozen = 19;
  repeated string tags = 20;
  // 导购等级
  Level level = 21;
  // 岗位
  Role role = 22;
  // 头像
  string avatar = 23;
  // 导购所有客户数
  uint64 allMemberCount = 24;
  // 最后登录时间
  string lastLoginAt = 25;
  // 导购所有客户群总数
  uint64 allGroupchatCount = 26;
  // 离职时间
  string resignedAt = 27;
  // 是否限制分配（所有客户正在分配）
  bool isNonTransferable = 28;
  // 离职导购所有客户数
  uint64 resignedMemberCount = 29;
  // 导购来源
  string source = 30;
  // 组织 ids
  repeated string distributorIds = 31;
  // 分销员是否存在
  bool isPromoterExists = 32;
  // 是否正在分配
  bool isTransferPending = 33;
  // 下游企业渠道 id
  string chainCorpChannelId = 34;
}

message UpdateStaffRequest {
  // @required
  //
  // 导购 ID
  string staffId = 1; // valid:"required,objectId"
  // 头像
  string avatar = 2;
  // 属性
  repeated PropertyItem properties = 3;
  // 姓名
  string name = 4;
  // 手机号
  string phone = 5;
  // 性别
  //
  // 可选值 0（未知），1（男），2（女）
  mairpc.common.types.Int64Value gender = 6;
  // 登录时间
  string loginAt = 7;
  // 个人二维码
  string qrcode = 8;
  // 邮箱
  string email = 9;
  // 激活状态
  mairpc.common.request.BoolRequest isActivated = 10;
}

message UpdateStaffPhoneWithVerificationRequest {
  // @required
  //
  // 导购 ID
  string staffId = 1; // valid:"required,objectId"
  // @required
  //
  // 手机号
  string phone = 2; // valid:"required,phone"
  // @required
  //
  // 短信验证码
  string verificationCode = 3; // valid:"required"
}

message PropertyItem {
  // 属性 ID
  string id = 1;
  // 属性代码
  string code = 2;
  // 属性值
  string value = 3;
  // 属性名称
  string name = 4;
  // 属性类型
  //
  // 可选值：单行文本（1），多行文本（2），日期（3），单选（4），多选（5），下拉框（6）
  int32 type = 5;
  // 字符串数组属性值
  //
  // type 为 5 时使用 valueStringArray 返回属性值
  repeated string valueStringArray = 6;
}

message ListOriginalStaffsRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // 搜索关键字
  string searchKey = 2;
  // 所属门店
  repeated string storeIds = 3; // valid:"objectIdList"
  // 店员状态
  //
  // 1: 在职、已激活
  // 2: 无法同步
  // 3: 待激活
  // 4: 未生效
  // 5: 离职
  repeated int64 status = 4;
  // 店员ID
  repeated string staffIds = 5; // valid:"objectIdList"
  // 店员来源，从企业微信同步过来的店员 source 为 wechatwork
  string source = 6;
  // 店员工号
  repeated string staffNos = 7;
  // 零售商 ids，只筛选出该零售商下门店中的导购
  repeated string distributorIds = 8;
  // 导购等级
  repeated int64 levels = 9;
  // 员工角色 Id 列表
  repeated string roleIds = 10; // valid:"objectIdList"
  // 当前门店 Id
  string currentStoreId = 11;
  // 导购标签
  repeated string tags = 12;
}

message ListOriginalStaffsResponse {
  int64 total = 1;
  repeated OriginalStaffDetail items = 2;
}

message OriginalStaffDetail {
  string id = 1;
  string accountId = 2;
  bool isDeleted = 3;
  string name = 4;
  string phone = 5;
  int64 gender = 6;
  string email = 7;
  int64 status = 8;
  string staffNo = 9;
  string currentStoreId = 10;
  repeated string storeIds = 11;
  int64 level = 12;
  bool isStaff = 13;
  repeated string distributorIds = 14;
  string chainCorpChannelId = 15;
  repeated string tags = 16;
  uint64 maxMemberCount = 17;
  uint64 wechatMemberCount = 18;
}

message SetStaffsTagsRequest {
  // @required
  //
  // 子标签 ID
  repeated string tagIds = 1;
  // @required
  //
  // 导购 ids
  repeated string staffIds = 2; // valid:"objectIdList"
}

message PullStaffsTagRequest {
  // @required
  //
  // 标签名称
  repeated string tagNames = 1;
  // 不传则修改所有导购的标签
  repeated string staffIds = 2; // valid:"objectIdList"
}

message BatchUpdateStaffTagsRequest {
  // @required
  //
  // 标签名称
  repeated string tagNames = 1;
  // 不传则修改所有导购的标签
  repeated string staffIds = 2; // valid:"objectIdList"
}

message GetStoreOverviewStatsRequest {
  repeated string storeIds = 1;
  // 统计字段，为空返回全部字段
  //
  // 可取值 staffCount，staffMemberCount，inviteMemberCount，taskCount, completedTaskCount，memberCount，boundMemberCount，newMemberCount，newBoundMemberCount
  repeated string statsFields = 2;
  // 筛选时间
  //
  // 只针对 newMemberCount，newBoundMemberCount 有效
  mairpc.common.types.StringDateRange dateRange = 3;
  // 导购状态
  //
  // 只针对 taskCount, completedTaskCount 有效。不传时默认统计所有导购的任务数据，包括离职导购的。注意，如果导购数量较大，传该字段会有效率问题。
  repeated int64 staffStatus = 4; // valid:"in(1|2|3|4|5)"
  // 筛选所属组织节点
  mairpc.common.types.DistributorSelector distributor = 5; // valid:"optional"
  repeated string createdBy = 6;
}

message GetStoreOverviewStatsResponse {
  // 导购总数
  uint64 staffCount = 1;
  // 关联客户总数
  uint64 staffMemberCount = 2;
  // 导购邀请入会客户数
  uint64 inviteMemberCount = 3;
  // 任务数量，
  //
  // 这里这里的任务数量指所有导购的 staffTask 的数量，非 task 数量
  uint64 taskCount = 4;
  // 完成的任务数量
  //
  // 这里这里的任务数量指所有导购的 staffTask 的完成数量
  uint64 completedTaskCount = 5;
  // 客户总数
  uint64 memberCount = 6;
  // 绑定客户总数
  uint64 boundMemberCount = 7;
  // 新增的客户数量
  uint64 newMemberCount = 8;
  // 新增的绑定客户数量
  uint64 newBoundMemberCount = 9;
}

message BatchUpdateStaffLevelsRequest {
  // @required
  //
  // 导购 Ids
  repeated string staffIds = 1; // valid:"required,objectIdList"
  // 导购等级
  uint64 level = 2;
}

message BatchUpdateStaffRolesRequest {
  // @required
  //
  // 导购 Ids
  repeated string staffIds = 1; // valid:"required,objectIdList"
  // @required
  //
  // 导购岗位 Id
  string roleId = 2; // valid:"required,objectId"
}

message ListRolesRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
}

message ListRolesResponse {
  uint64 total = 1;
  repeated Role items = 2;
}

message Role {
  string id = 1;
  string name = 2;
  string code = 3;
  string description = 4;
  bool isSystem = 5;
}

message ListBriefStaffsByRoleRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
  // @required
  string storeId = 2; // valid:"required,objectId"
  // 导购岗位 id
  string roleId = 3; // valid:"objectId"
}

message ListStaffNosResponse {
  // 总数
  int64 total = 1;
  // 员工编号列表
  repeated string staffNos = 2;
}

message ImportStaffLevelsRequest {
  // @required
  //
  // 导入文件 url
  string url = 1; // valid:"required"
  // 是否需要检查数据
  bool checkData = 2;
}

message ListStoreTreeRequest {
  // 父级门店 id，不传时返回根部门，如果对应的是门店，则返回下面的所有导购
  string parentStoreId = 1;
  // parentStoreId 和 searchKey 不支持同时传，同时传时 parentStoreId 无效
  string searchKey = 2;
  // 导购等级
  repeated uint32 levels = 3;
  // 筛选所属组织节点
  mairpc.common.types.DistributorSelector distributor = 4; // valid:"optional"
  // allGroupchatCount（客户群总数） allMemberCount（客户总数）
  repeated string extraFields = 5;
  // parentStoreId 为空时生效
  repeated int64 types = 6;
  // 导购标签
  repeated string tags = 7;
  // 排除部门及其下属节点
  repeated string excludeDepartmentIds = 8;
  repeated string createdBy = 9;
  // 需要返回的字段，id name code type
  repeated string briefFields = 10;
}

message ListStoreTreeResponse {
  repeated BriefStaff staffs = 1;
  repeated BriefStore stores = 2;
}

message BriefStaff {
  string id = 1;
  string name = 2;
  string staffNo = 3;
  // 客户数
  uint64 allMemberCount = 4;
  // 客户群数
  uint64 allGroupchatCount = 5;
  // 导购当前门店
  string currentStoreId = 6;
  string chainCorpChannelId = 7;
}

message BriefStore {
  string id = 1;
  string name = 2;
  string code = 3;
  int32 type = 4;
  string parentId = 5;
  string chainCorpChannelId = 6;
}

message InitDefaultStaffRolesRequest {
  // @required
  //
  // 业务场景
  //
  // wechatworkStaff：智慧导购员工角色，retailStaff：零售门店角色
  string business = 1; // valid:"required,in(wechatworkStaff|retailStaff)"
}
