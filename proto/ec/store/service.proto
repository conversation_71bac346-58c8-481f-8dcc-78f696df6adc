syntax = "proto3";

package mairpc.ec.store;

option go_package = "store";

import "common/ec/ec.proto";
import "common/request/request.proto";
import "common/response/response.proto";
import "ec/store/organization.proto";
import "ec/store/property.proto";
import "ec/store/staff.proto";
import "ec/store/store.proto";
import "store/store.proto";

service StoreService {
  // 获取门店列表
  rpc ListStores(ListStoresRequest) returns (ListStoresResponse);
  // 获取门店 Id 列表
  //
  // 此接口分页参数无效
  rpc GetStoreIds(ListStoresRequest) returns (StoreIdsResponse);
  // 获取门店详情
  rpc GetStore(mairpc.common.request.DetailRequest) returns (StoreDetail);
  // 获取附近的门店
  rpc NearStores(NearStoresRequest) returns (NearStoresResponse);
  // 更改门店状态
  rpc UpdateStoresStatus(UpdateStoresStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取店员列表
  rpc ListStaffs(ListStaffsRequest) returns (ListStaffsResponse);
  // 获取店员编号列表
  rpc ListStaffNos(ListStaffsRequest) returns (ListStaffNosResponse);
  // 获取店员详情
  rpc GetStaff(StaffDetailRequest) returns (StaffDetail);
  // 获取员工导购等级
  rpc GetLevelOfStaff(StaffDetailRequest) returns (mairpc.common.response.IntResponse);
  // 更新导购状态
  //
  // 一般用来将导购状态修改为离职
  rpc BatchUpdateStaffStatus(BatchUpdateStaffStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 是否为某些门店最后一名店员
  //
  // 返回值中的门店仅有请求中那一名店员
  rpc IsLastStaff(mairpc.common.request.DetailRequest) returns (IsLastStaffResponse);
  // 创建支持快递送货的门店
  rpc CreateDeliveryStores(mairpc.common.request.IdListRequest) returns (CreateDeliveryStoresResponse);
  // 删除支持快递送货的门店
  rpc DeleteDeliveryStore(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新门店快递送货的信息
  rpc UpdateDeliveryStore(UpdateDeliveryStoreRequest) returns (mairpc.common.response.EmptyResponse);
  // 店员切换门店
  rpc SwitchStore(SwitchStoreRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步企业微信通讯录到门店、店员中，cron 中使用
  rpc SyncWechatcpDepartmentAndUserJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步企业微信通讯录到门店、店员中，cron 中使用,目前只有联华华商租户使用
  rpc CustomSyncWechatcpDepartmentAndUserJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步企业微信通讯录到门店、店员中
  rpc SyncWechatcpDepartmentAndUser(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 同步企业微信店员
  rpc SyncWechatcpStaffs(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 自定义同步企业微信通讯录到门店、店员中,目前只有联华华商租户使用
  rpc CustomSyncWechatcpDepartmentAndUser(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 同步会话存档配置
  rpc SyncMessageAuditConfig(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理企业微信成员关注事件
  rpc HandleWechatcpSubscribe(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理企业微信成员取消关注事件
  rpc HandleWechatcpUnsubscribe(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步单个员工
  rpc SyncStaff(SyncStaffRequest) returns (StaffDetail);
  // 同步企业微信应用管理员到用户中
  rpc SyncWechatcpAdmins(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步企业微信授权成员
  rpc SyncWechatcpAuthUsers(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理应用管理员变更
  rpc HandleWechatcpAdminChange(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理应用授权模式变更
  rpc HandleChangeAuth(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 导入门店
  //
  // 如果所有部门和门店都有传门店类型(type)参数，可以分批次增量导入，否则需要一次性导入所有部门和门店，便于确定门店和部门之间的关系
  rpc ImportStores(ImportStoresRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量导入员工
  rpc BatchImportStaff(BatchImportStaffRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取门店简要信息列表
  rpc ListBriefStores(ListStoresRequest) returns (ListBriefStoresResponse);
  // 导出门店简要信息
  rpc ExportStores(ListStoresRequest) returns (mairpc.common.response.JobResponse);
  // 获取导购简要信息列表
  rpc ListBriefStaffs(ListStaffsRequest) returns (ListBriefStaffsResponse);
  // 获取第三方门店列表
  rpc ListThirdPartyStores(ListThirdPartyStoresRequest) returns (ListThirdPartyStoresResponse);
  // 更新门店
  rpc UpdateStore(UpdateStoreRequest) returns (mairpc.common.response.EmptyResponse);
  // 新建门店
  rpc CreateStore(CreateStoreRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取父级门店
  rpc GetParentStores(GetParentStoresRequest) returns (ListStoresResponse);
  // 添加导购属性
  rpc CreateStaffProperties(CreateStaffPropertiesRequest) returns (CreateStaffPropertiesResponse);
  // 获取导购属性
  rpc ListStaffProperties(mairpc.common.request.EmptyRequest) returns (ListStaffPropertiesResponse);
  // 更新导购信息
  rpc UpdateStaff(UpdateStaffRequest) returns (mairpc.common.response.EmptyResponse);
  // 校验短信验证码并更新导购手机号
  rpc UpdateStaffPhoneWithVerification(UpdateStaffPhoneWithVerificationRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取某个经销商下所有子门店（type=3）
  rpc GetChildStores(GetChildStoresRequest) returns (ListStoresResponse);
  // 获取根节点部门
  rpc GetRootStores(mairpc.common.request.EmptyRequest) returns (ListStoresResponse);
  // 新增层级
  rpc CreateOrganizationSetting(CreateOrganizationSettingRequest) returns (OrganizationSettingDetail);
  // 获取层级
  rpc GetOrganizationSetting(mairpc.common.request.EmptyRequest) returns (OrganizationSettingDetail);
  // 更新层级
  rpc UpdateOrganizationSetting(UpdateOrganizationSettingRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取已经添加到组织层级中的部门
  rpc ListOrganizationDepartments(ListOrganizationDepartmentsRequest) returns (ListOrganizationDepartmentsResponse);
  // 获取已经添加到组织层级中的单个部门
  rpc GetOrganizationDepartment(GetOrganizationDepartmentRequest) returns (OrganizationDepartment);
  // 批量添加部门到零售公司
  rpc CreateDepartments(mairpc.common.request.IdListRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除导购
  rpc DeleteStaff(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 从零售公司移除
  rpc DeleteDepartment(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新所有门店 AncestorIds 字段
  rpc UpdateStoreAncestorIdsFiled(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新导购的分销员统计信息
  rpc UpdateStaffFromPromoter(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理导购被设置门店
  rpc HandleSetStaffStores(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 只查询和返回 dms.staff 数据，为了查询效率返回值中不添加任务其余数据
  rpc ListOriginalStaffs(ListOriginalStaffsRequest) returns (ListOriginalStaffsResponse);
  // 批量删除导购子标签
  rpc PullStaffsTag(PullStaffsTagRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量替换导购标签
  rpc SetStaffsTags(SetStaffsTagsRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量新增导购标签
  rpc BatchUpdateStaffTags(BatchUpdateStaffTagsRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取同城快送门店
  rpc ListCityExpressStores(ListCityExpressStoresRequest) returns (ListCityExpressStoresResponse);
  // 获取同城快送门店详情
  rpc GetCityExpressStore(GetCityExpressStoreRequest) returns (CityExpressStoreDetail);
  // 创建同城快送门店
  rpc CreateCityExpressStores(BatchUpsertCityExpressStoresRequest) returns (mairpc.common.response.EmptyResponse);
  // 移除同城快送门店
  rpc RemoveCityExpressStores(RemoveCityExpressStoresRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量更新同城快送门店
  rpc BatchUpdateCityExpressStore(BatchUpsertCityExpressStoresRequest) returns (mairpc.common.response.EmptyResponse);
  // 整理门店信息
  rpc SettleStoresJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 导出导购列表
  rpc ExportStaffs(ListStaffsRequest) returns (mairpc.common.response.JobResponse);
  // 导出员工列表
  rpc ExportWechatworkStaffs(ListStaffsRequest) returns (mairpc.common.response.JobResponse);
  // 导出门店码或者创建分账接收方邀请码
  rpc ExportStoreQrcodes(ExportStoreQrcodesRequest) returns (mairpc.common.response.JobResponse);
  // 缓存门店数据
  rpc StoreDailyStatsJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 缓存指定日期的门店数据
  rpc StatsStoreByDate(StatsStoreByDateRequest) returns (mairpc.common.response.EmptyResponse);
  // 门店数据概览
  rpc GetStoreOverviewStats(GetStoreOverviewStatsRequest) returns (GetStoreOverviewStatsResponse);
  // 获取门店统计数据趋势
  rpc GetStoreStatsTrend(GetStoreStatsTrendRequest) returns (GetStoreStatsTrendResponse);
  // 导出门店统计数据趋势
  rpc ExportStoreStatsTrend(GetStoreStatsTrendRequest) returns (mairpc.common.response.JobResponse);
  // 获取门店数据对比
  rpc GetStoreStatsContrast(GetStoreStatsContrastRequest) returns (GetStoreStatsContrastResponse);
  // 导出门店数据对比
  rpc ExportStoreStatsContrast(GetStoreStatsContrastRequest) returns (mairpc.common.response.JobResponse);
  // 删除同步失败的门店
  rpc DeleteSyncingStores(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.JobResponse);
  // 批量更新导购等级
  rpc BatchUpdateStaffLevels(BatchUpdateStaffLevelsRequest) returns (mairpc.common.response.EmptyResponse);
  // 导入导购等级
  rpc ImportStaffLevels(ImportStaffLevelsRequest) returns (mairpc.common.response.JobResponse);
  // 批量更新导购岗位
  rpc BatchUpdateStaffRoles(BatchUpdateStaffRolesRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取角色列表
  rpc ListRoles(ListRolesRequest) returns (ListRolesResponse);
  // 通过角色获取导购列表
  rpc ListBriefStaffsByRole(ListBriefStaffsByRoleRequest) returns (ListBriefStaffsResponse);
  // 获取门店和导购树的单个层级的数据
  //
  // 支持根据导购/门店/零售公司搜索，只会返回搜索结果（门店或导购），不会返回树结构
  rpc ListStoreTree(ListStoreTreeRequest) returns (ListStoreTreeResponse);
  // 根据优惠券终端获取适用门店
  rpc ListApplicableStoresForCouponEndpoint(ListApplicableStoresForCouponEndpointRequest) returns (mairpc.store.StoreList);
  // 企业微信安装应用时，给所有导购发送操作手册
  rpc SendGuideMessage(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 初始化默认员工角色
  rpc InitDefaultStaffRoles(InitDefaultStaffRolesRequest) returns (mairpc.common.response.EmptyResponse);
  // 接入外部联系人渠道后处理相关事务
  rpc HandleExternalContactAccessed(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 新增上下游企业
  rpc CreateChainCorp(CreateChainCorpRequest) returns (mairpc.common.response.EmptyResponse);
  // 上下游企业列表
  rpc ListChainCorps(ListChainCorpsRequest) returns (ListChainCorpsResponse);
  // 获取上下游渠道详情
  rpc GetChainCorp(GetChainCorpRequest) returns (ChainCorp);
  // 处理新增门店事件
  rpc HandleStoreCreate(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理新增导购事件
  rpc HandleStaffCreate(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取店员脱敏详情
  rpc GetSensitiveStaff(StaffDetailRequest) returns (StaffDetail);
  // 获取导购敏感信息
  rpc GetStaffSensitive(GetStaffSensitiveRequest) returns (mairpc.common.ec.GetSensitiveResponse);
  // 根据客户类型获取门店信息
  rpc ListStoresByMemberId(ListStoresByMemberIdRequest) returns (ListStoresResponse);
  // 绑定门店
  rpc BindStore(BindStoreRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取绑定门店
  rpc GetBoundStore(GetBoundStoreRequest) returns (BoundStoreDetail);
  // 创建或更新门店标签组
  rpc UpsertTagGroup(UpsertTagGroupRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建门店标签
  rpc CreateTag(CreateTagRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量移动标签
  rpc BatchMoveTagsToGroup(BatchMoveTagsToGroupRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除标签
  rpc DeleteTags(mairpc.common.request.IdListRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除标签组
  rpc DeleteTagGroup(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取标签组
  rpc ListTagGroups(ListTagGroupsRequest) returns (ListTagGroupsResponse);
  // 获取标签组里的标签
  rpc ListTags(ListTagsRequest) returns (ListTagsResponse);
  // 判断是否有同名标签或标签组
  rpc IsTagAndGroupExists(mairpc.common.request.StringRequest) returns (mairpc.common.response.BoolResponse);
  // 修改标签关联门店数量
  rpc UpdateTagDistributorCount(UpdateTagDistributorCountRequest) returns (mairpc.common.response.EmptyResponse);
  // 导出零售门店信息
  rpc ExportRetailStores(ListStoresRequest) returns (mairpc.common.response.JobResponse);
  // 批量创建门店标签
  rpc BatchCreateTags(BatchCreateTagsRequest) returns (mairpc.common.response.EmptyResponse);
  // 自动开启关闭门店
  rpc AutoOpenAndCloseStore(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 异步获取门店下属导购的绑定客户数
  rpc CountStoreBoundStaffMembers(ListStoresRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 同步导购的企业微信客户数
  rpc SyncWechatMemberCount(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 计算门店评分，cron 中使用，目前只有元祖租户使用
  rpc CalculateStoreRatingJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
}
