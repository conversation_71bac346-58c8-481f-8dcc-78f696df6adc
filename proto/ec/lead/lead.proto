syntax = "proto3";

package mairpc.ec.lead;

option go_package = "lead";

import "common/request/request.proto";
import "common/types/types.proto";

message LeadProperty {
  // 属性 ID
  string id = 1;
  // 属性名称
  string name = 2;
  // 属性中文名称
  string nameCN = 3;
  // 属性值
  string stringValue = 4;
  // 排序顺序
  uint32 order = 5;
  // 属性值
  uint32 integerValue = 6;
  // 属性值
  bool booleanValue = 7;
  // 属性类型
  string type = 8;
  // 日期值
  string dateValue = 9;
}

message LeadDetail {
  // 线索 ID
  string id = 1;
  // 创建时间
  string createdAt = 2;
  // 最后更新时间
  string updatedAt = 3;
  // 客户名称
  string name = 4;
  // 手机号码
  string phone = 5;
  // 线索状态：prospective（潜客）、following（跟进中）、converted（成交客户）、lost（流失客户）、closed（已关闭）
  string status = 6;
  // 线索来源：external（企微外部联系人）、staff（经销商导购手动创建）、import（导入）
  string source = 7;
  // 邀请类型
  string invitationType = 8;
  // 关联的客户 ID
  string memberId = 9;
  // 第三方客户 ID
  string thirdMemberId = 10;
  // 所属经销商 ID
  string distributorId = 11;
  // 负责销售 ID
  string staffId = 12;
  // 是否已分配销售
  bool isAssigned = 13;
  // 最后跟进时间
  string lastFollowUpAt = 14;
  // 线索属性列表
  repeated LeadProperty properties = 15;
  // 性别
  string gender = 16;
  // 行业
  string industry = 17;
  // 客户意向等级
  uint32 intentionLevel = 18;
  // 购买预算
  uint32 purchaseBudget = 19;
  // 流失原因
  string lostReason = 20;
  // 关闭原因
  string closeReason = 21;
  // 下次计划跟进时间
  string nextFollowUpAt = 22;
  // 下次计划
  string nextPlan = 23;
  // 试用商品名称
  string trialProductName = 24;
  // 试用时间
  string trialAt = 25;
  // 商品新旧程度意向 new/used
  string productConditionIntention = 26;
  // 意向商品名称
  string intentionProductName = 27;
  // 备注
  string remark = 28;
  // 负责销售姓名
  string staffName = 29;
  // 负责销售邮箱
  string staffEmail = 30;
  // 所属经销商名称
  string distributorName = 31;
  // 活动来源
  string sourceActivity = 32;
  // 邀约时间
  string invitedAt = 33;
  // 流失时间
  string lostAt = 34;
  // 关闭时间
  string closedAt = 35;
  // 转化时间
  string convertedAt = 36;
  // 第三方ID
  string thirdPartyId = 37;
  // 第三方更新时间
  string thirdPartyUpdatedAt = 38;
  // 是否删除
  bool isDeleted = 39;
}

message CreateLeadRequest {
  // 客户名称
  string name = 1; // valid:"required"
  // 手机号码
  string phone = 2; // valid:"required"
  // 线索状态：prospective（潜客）、following（跟进中）、converted（成交客户）、lost（流失客户）、closed（已关闭）
  string status = 3;
  // 线索来源：external（企微外部联系人）、staff（经销商导购手动创建）、import（导入）
  string source = 4;
  // 邀请类型
  string invitationType = 5;
  // 关联的客户 ID
  string memberId = 6;
  // 所属经销商 ID
  string distributorId = 7;
  // 负责销售 ID
  string staffId = 8; // valid:"required,objectId"
  // 第三方客户 ID
  string thirdMemberId = 9;
  // 线索属性列表
  repeated LeadProperty properties = 10;
  // 性别
  string gender = 11;
  // 行业
  string industry = 12;
  // 客户意向等级
  uint32 intentionLevel = 13;
  // 购买预算
  uint32 purchaseBudget = 14;
  // 流失原因
  string lostReason = 15;
  // 关闭原因
  string closeReason = 16;
  // 下次计划跟进时间
  string nextFollowUpAt = 17;
  // 下次计划
  string nextPlan = 18;
  // 试用商品名称
  string trialProductName = 19;
  // 试用时间
  string trialAt = 20;
  // 商品新旧程度意向 new/used
  string productConditionIntention = 21;
  // 意向商品名称
  string intentionProductName = 22;
  // 备注
  string remark = 23;
  // 活动来源
  string sourceActivity = 24;
  // 邀约时间
  string invitedAt = 25;
  // 第三方ID
  string thirdPartyId = 26;
  // 第三方更新时间
  string thirdPartyUpdatedAt = 27; // valid:"rfc3339"
}

message UpdateLeadRequest {
  // 线索 ID
  string id = 1; // valid:"required,objectId"
  // 客户名称
  string name = 2;
  // 线索属性列表
  repeated LeadProperty properties = 3;
  // 客户意向等级
  uint32 intentionLevel = 4;
  // 购买预算
  uint32 purchaseBudget = 5;
  // 试用商品名称
  string trialProductName = 6;
  // 试用时间
  string trialAt = 7;
  // 商品新旧程度意向 new/used
  string productConditionIntention = 8;
  // 意向商品名称
  string intentionProductName = 9;
  // 备注
  string remark = 10;
  // 负责销售 id
  string staffId = 11;
  // 手机号码
  string phone = 12;
  // 行业
  string industry = 13;
  // 第三方更新时间
  string thirdPartyUpdatedAt = 14; // valid:"rfc3339"
}

message AssignLeadRequest {
  // 线索 ID
  repeated string ids = 1; // valid:"required"
  // 负责销售 ID
  string toStaffId = 2; // valid:"required,objectId"
}

message ListLeadsRequest {
  // 分页和排序
  common.request.ListCondition listCondition = 1;
  // 按状态筛选
  repeated string status = 2;
  // 按销售 ID 筛选
  string searchStaffId = 3;
  // 搜索关键字
  string searchKey = 4;
  // 按更新时间范围筛选
  mairpc.common.types.DateRange updatedAtRange = 5;
  // 是否包含已删除数据
  bool containDeleted = 6;
  // 按第三方更新时间范围筛选
  mairpc.common.types.DateRange thirdPartyUpdatedAt = 7;
  // 邀约类型
  repeated string invitationTypes = 8;
  // 客户意向等级
  repeated uint32 intentionLevels = 9;
}

message ListLeadsResponse {
  // 符合筛选条件的线索总数
  uint64 totalCount = 1;
  // 线索列表
  repeated LeadDetail items = 2;
}

// 线索跟进记录相关消息
message LeadFollowUpRecordDetail {
  // 跟进 ID
  string id = 1;
  // 线索 ID
  string leadId = 2;
  // 跟进销售 ID
  string staffId = 3;
  // 跟进内容
  string content = 4;
  // 跟进方式：visit（上门拜访）、wechat（微信联系）、call（电话联系）、email（邮件联系）、sms（短信）、other（其他）、assignment（分配）
  string type = 5;
  // 下次计划跟进时间
  string nextFollowUpAt = 6;
  // 创建时间
  string createdAt = 7;
  // 最后更新时间
  string updatedAt = 8;
  // 跟进时的线索状态
  string leadStatus = 9;
  // 跟进前的线索状态
  string previousStatus = 10;
  // 跟进结果
  repeated string followUpResult = 11;
}

message CreateLeadFollowUpRecordRequest {
  // 线索 ID
  string leadId = 1; // valid:"required,objectId"
  // 跟进销售 ID
  string staffId = 2;
  // 跟进内容
  string content = 3; // valid:"required"
  // 跟进方式
  string type = 4;
  // 下次计划跟进时间
  string nextFollowUpAt = 5;
  // 跟进时的线索状态
  string leadStatus = 6;
  // 跟进前的线索状态
  string previousStatus = 7;
  // 跟进结果
  repeated string followUpResult = 8;
  // 流失原因
  string lostReason = 9;
  // 关闭原因
  string closeReason = 10;
}

message UpdateLeadFollowUpRecordRequest {
  // 跟进 ID
  string id = 1; // valid:"required,objectId"
  // 跟进销售 ID
  string staffId = 2;
  // 跟进内容
  string content = 3;
  // 跟进方式：visit（上门拜访）、wechat（微信联系）、call（电话联系）、email（邮件联系）、sms（短信）、other（其他）、assignment（分配）
  string type = 4; // valid:"in(visit|wechat|call|email|sms|other|assignment)"
  // 下次计划跟进时间
  string nextFollowUpAt = 5;
  // 跟进时的线索状态
  string leadStatus = 6;
  // 跟进前的线索状态
  string previousStatus = 7;
  // 跟进结果
  repeated string followUpResult = 8;
}

message ListLeadFollowUpRecordsRequest {
  // 线索 ID
  string leadId = 1; // valid:"required,objectId"
  // 分页和排序
  common.request.ListCondition listCondition = 2;
  // 按销售 ID 筛选
  string staffId = 3;
  // 按跟进方式筛选
  string type = 4;
  // 按创建时间范围筛选
  mairpc.common.types.DateRange createdAtRange = 5;
}

message ListLeadFollowUpRecordsResponse {
  // 符合筛选条件的跟进记录总数
  uint64 totalCount = 1;
  // 跟进记录列表
  repeated LeadFollowUpRecordDetail items = 2;
}

message ImportLeadsRequest {
  // 文件路径（必填）
  string fileUrl = 1; // valid:"required"
}

message LeadStatusStatsRequest {
  // 按销售 ID 筛选
  repeated string staffIds = 1;
  // 按经销商 ID 筛选
  repeated string distributorIds = 2;
}

message LeadStatsResponse {
  // 线索总数
  uint32 totalLeads = 1;
  // 本月新增线索数
  uint32 currentMonthNewLeads = 2;
  // 本月线索转化数
  uint32 currentMonthConvertedLeads = 3;
  // 本月线索流失数
  uint32 currentMonthLostLeads = 4;
  // 上月新增线索数
  uint32 lastMonthNewLeads = 5;
  // 上月线索转化数
  uint32 lastMonthConvertedLeads = 6;
  // 上月线索流失数
  uint32 lastMonthLostLeads = 7;
}

message PropertyValueCount {
  // 属性值名称
  string name = 1;
  // 数量
  int32 count = 2;
}

message PropertyStats {
  // 字段名称
  string field = 1;
  // 字段值统计
  repeated PropertyValueCount values = 2;
}

message LeadPropertyStatsRequest {
  // 属性名称
  string propertyName = 1;
  // 按销售 ID 筛选
  repeated string staffIds = 2;
  // 按经销商 ID 筛选
  repeated string distributorIds = 3;
}

message LeadPropertyDetail {
  // 属性 ID
  string id = 1;
  // 属性名称（英文）
  string name = 2;
  // 属性名称（中文）
  string nameCN = 3;
  // 属性类型：string（文本）、integer（数字）、boolean（布尔值）、date（日期）
  string type = 4;
  // 排序
  int32 order = 5;
  // 属性类型：（lead）线索、（followUpType）跟进方式，（followUpResult）跟进结果
  string business = 6;
  // 属性选项列表
  repeated LeadPropertyChoice choices = 7;
  // 创建时间
  string createdAt = 8;
  // 更新时间
  string updatedAt = 9;
}

message LeadPropertyChoice {
  // 选项名
  string name = 1;
  // 字段值
  string stringValue = 2;
  // 数字值
  uint32 integerValue = 3;
  // 布尔值
  bool booleanValue = 4;
  // 选项排序
  uint32 order = 5;
  // 日期值
  string dateValue = 6;
}

message ListLeadPropertiesRequest {
  // 属性类型：（lead）线索、（followUpType）跟进方式，（followUpResult）跟进结果
  repeated string businesses = 1;
  // 属性名称，用于精确搜索
  string name = 2;
}

message ListLeadPropertiesResponse {
  // 线索属性列表
  repeated LeadPropertyDetail items = 1;
}

message UpdateLeadNextPlanRequest {
  // 线索 ID
  string leadId = 1; // valid:"required,objectId"
  // 下次计划
  string nextPlan = 2; // valid:"required"
}

message UpdateLeadStatusRequest {
  // 线索 ID
  string leadId = 1; // valid:"required,objectId"
  // 线索状态
  string status = 2; // valid:"required,in(prospective|following|converted|lost|closed)"
}
