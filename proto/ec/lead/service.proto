syntax = "proto3";

package mairpc.ec.lead;

option go_package = "lead";

import "common/request/request.proto";
import "common/response/response.proto";
import "ec/lead/lead.proto";

service LeadService {
  // 创建客户线索
  rpc CreateLead(CreateLeadRequest) returns (LeadDetail);
  // 获取线索详情
  rpc GetLead(mairpc.common.request.DetailRequest) returns (LeadDetail);
  // 获取线索列表
  rpc ListLeads(ListLeadsRequest) returns (ListLeadsResponse);
  // 更新线索
  rpc UpdateLead(UpdateLeadRequest) returns (mairpc.common.response.EmptyResponse);
  // 分配线索
  rpc AssignLead(AssignLeadRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建线索跟进记录
  rpc CreateLeadFollowUpRecord(CreateLeadFollowUpRecordRequest) returns (LeadFollowUpRecordDetail);
  // 获取跟进记录
  rpc GetLeadFollowUpRecord(mairpc.common.request.DetailRequest) returns (LeadFollowUpRecordDetail);
  // 获取跟进记录列表
  rpc ListLeadFollowUpRecords(ListLeadFollowUpRecordsRequest) returns (ListLeadFollowUpRecordsResponse);
  // 获取线索统计数据
  rpc GetLeadStats(LeadStatusStatsRequest) returns (LeadStatsResponse);
  // 获取线索状态分布统计
  rpc GetLeadStatusStats(LeadStatusStatsRequest) returns (PropertyStats);
  // 获取线索属性分布统计
  rpc GetLeadPropertyDistribution(LeadPropertyStatsRequest) returns (PropertyStats);
  // 获取线索属性列表
  rpc ListLeadProperties(ListLeadPropertiesRequest) returns (ListLeadPropertiesResponse);
  // 导入线索
  rpc ImportLeads(ImportLeadsRequest) returns (mairpc.common.response.JobResponse);
  // 更新线索下次计划
  rpc UpdateLeadNextPlan(UpdateLeadNextPlanRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新线索状态
  rpc UpdateLeadStatus(UpdateLeadStatusRequest) returns (mairpc.common.response.EmptyResponse);
}
