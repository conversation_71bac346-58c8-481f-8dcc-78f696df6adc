type: google.api.Service
config_version: 3

http:
  rules:
    - selector: mairpc.ec.lead.LeadService.CreateLead
      post: /v2/ec/lead
      body: '*'
      filterMark: backend,staff
      tags: ['智慧导购-线索管理']
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.ListLeads
      get: /v2/ec/leads
      tags: ['智慧导购-线索管理']
      filterMark: staff,backend
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.AssignLead
      post: /v2/ec/lead/assign
      body: '*'
      filterMark: backend,staff
      tags: ['智慧导购-线索管理']
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.CreateLeadFollowUpRecord
      post: /v2/ec/lead/followUp
      body: '*'
      filterMark: backend,staff
      tags: ['智慧导购-线索管理']
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.GetLeadFollowUpRecord
      get: /v2/ec/lead/followUp/{id}
      tags: ['智慧导购-线索管理']
      filterMark: staff,backend
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.GetLeadStats
      get: /v2/ec/leadStats
      tags: ['智慧导购-线索管理']
      filterMark: staff,backend
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.GetLeadStatusStats
      get: /v2/ec/leadStatusStats
      tags: ['智慧导购-线索管理']
      filterMark: staff,backend
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.GetLeadPropertyDistribution
      get: /v2/ec/leadPropertyDistribution
      tags: ['智慧导购-线索管理']
      filterMark: staff,backend
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.ListLeadProperties
      get: /v2/ec/leadProperties
      tags: ['智慧导购-线索管理']
      filterMark: staff,backend
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.ImportLeads
      post: /v2/ec/leadImport
      body: '*'
      tags: ['智慧导购-线索管理']
      filterMark: staff,backend
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.GetLead
      get: /v2/ec/lead/{id}
      tags: ['智慧导购-线索管理']
      filterMark: staff,backend
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.UpdateLead
      put: /v2/ec/lead/{id}
      body: '*'
      filterMark: backend,staff
      tags: ['智慧导购-线索管理']
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.UpdateLeadStatus
      put: /v2/ec/leadStatus
      body: '*'
      filterMark: backend,staff
      tags: ['智慧导购-线索管理']
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.ListLeadFollowUpRecords
      get: /v2/ec/lead/{leadId}/followUps
      tags: ['智慧导购-线索管理']
      filterMark: staff,backend
      scope: ['staff']
    - selector: mairpc.ec.lead.LeadService.UpdateLeadNextPlan
      put: /v2/ec/leadNextPlan
      body: '*'
      filterMark: backend,staff
      tags: ['智慧导购-线索管理']
      scope: ['staff']
