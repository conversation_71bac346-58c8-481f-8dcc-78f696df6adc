syntax = "proto3";

package mairpc.common.ec;

option go_package = "ec";

import "common/request/request.proto";
import "common/types/types.proto";
import "coupon/coupon.proto";

message GetMembershipDiscountsRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
  // 是否是直接购买
  //
  // 当此字段为 true 时才会用到购买商品的几个字段且此时这几个字段必填
  bool isPurchase = 3;
  // 购买商品的 ID
  string productId = 4; // valid:"objectId"
  // 购买商品 sku
  string sku = 5;
  // 购买商品数量
  uint64 count = 6;
  // 配送方式
  //
  // 门店自提 pickup、快递配送 express 同城快送 cityExpress、无需配送 noExpress
  string method = 7; // valid:"in(express|pickup|cityExpress|noExpress)"
  // 收货地址 ID
  string memberAddressId = 8; // valid:"objectId"
  // 参与的活动
  repeated mairpc.common.ec.OrderCampaign campaigns = 9;
  // 活动配置
  mairpc.common.ec.OrderCampaignConfig campaignConfig = 10;
  // 优惠券 ID 列表
  repeated string discountIds = 11;
  // 权益卡 ID 列表
  repeated string memberPaidCardRecordIds = 12;
  // 选中的优惠组合
  //
  // 可选值：活动优惠（campaign）、会员折扣（member）
  mairpc.common.types.StringList discountGroup = 13;
  // 是否使用积分
  bool useScore = 14;
  // 代客下单订单 id
  string proxyOrderId = 15; // valid:"objectId"
}

message GetMembershipDiscountsResponse {
  // 订单可用优惠券
  repeated MembershipDiscount availabeDiscounts = 1;
  // 订单不可用优惠券
  repeated MembershipDiscount unavailabeDiscounts = 2;
  repeated Amount amount = 3;
  // 是否需要默认选中一张优惠券
  bool needSelectDefaultDiscount = 4;
}

message Amount {
  // maxDiscountAmount 订单最大可优惠金额，deliveryFee 订单运费
  string type = 1;
  uint64 value = 2;
}

message MembershipDiscount {
  // ID
  string id = 1;
  // 优惠券名称
  string title = 2;
  // 优惠券图片
  string picUrl = 3;
  // 生效时间
  int64 startTime = 4;
  // 过期时间
  int64 endTime = 5;
  // 优惠券类型
  //
  // 现金券（cash），折扣券（discount），买赠券（present），运费券（delivery）
  string type = 6;
  // 折扣额度
  mairpc.common.types.DoubleValue discountAmount = 7;
  // 折扣条件
  mairpc.common.types.DoubleValue discountCondition = 8;
  // 减免金额
  mairpc.common.types.DoubleValue reductionAmount = 9;
  // 使用须知
  string usageNote = 10;
  string description = 11;
  string tip = 12;
  // 备注
  string remarks = 13;
  // 优惠金额
  int64 amount = 14;
  // 赠品信息
  mairpc.coupon.PresentResp present = 15;
  // 是否可以与其他券共同使用
  bool canUsedWithOthers = 16;
  // 不可用原因
  string invalidReason = 17;
  // 叠加设置
  //
  // 仅与自己叠加（self），可与其他可叠加券叠加（other）
  string superpositionType = 18;
  // 这个 id 只为标识是否是领取自同一个 coupon，不是 coupon.id
  string couponId = 19;
  // 当日剩余可使用次数
  uint64 limit = 20;
  // 适用商品 SKU
  repeated coupon.ApplicableProductSkus applicableProductSkus = 29;
  // 优惠券业务场景
  repeated coupon.CouponBusiness businesses = 30;
  // 是否可叠加
  bool canSuperposition = 31;
  // 是否可选中
  bool canSelect = 32;
  // 不可叠加原因
  //
  // 实付金额不足 payAmount
  string superpositionInvalidReason = 33;
  // 不足的实付金额
  int64 insufficientPayAmount = 34;
  // 折扣条件金额类型
  //
  // 订单金额（totalAmount）、实付金额（payAmount）
  string discountConditionType = 35;
  // 赠品列表信息
  repeated mairpc.coupon.PresentResp presents = 36;
  // 渠道信息
  MembershipDiscountChannel channel = 37;
}

message MembershipDiscountChannel {
  // 渠道ID
  string channelId = 1;
  // 渠道名称
  string name = 2;
  // 来源
  string origin = 3;
  // 类型
  string type = 4;
}

message PrePayBalanceRequest {
  // @required
  //
  // 订单ID
  string orderId = 1; // valid:"required,objectId"
  // @required
  //
  // 客户ID
  string memberId = 2; // valid:"required,objectId"
  // @required
  //
  // 买家微信openId
  string openId = 3; // valid:"required"
  // @required
  //
  // 渠道 ID
  string channelId = 4; // valid:"required"
  // 场景值
  uint32 scene = 5;
  // 优惠券 ID 列表
  repeated string discountIds = 6; // valid:"objectIdList"
  // 是否使用积分
  bool useScore = 7;
  // 礼品卡 ID 列表
  repeated string prepaidCardIds = 8;
  // 是否使用储值余额支付
  bool useStoredValue = 9;
}

message PrePayResponse {
  // 支付配置的appId
  string appId = 1;
  // 时间戳
  string timeStamp = 2;
  // 随机字符串
  string nonceStr = 3;
  // 数据包
  string package = 4;
  // 签名类型
  string signType = 5;
  // 签名
  string paySign = 6;
  // 订单详情
  OrderInfo orderInfo = 7;
  // 托管支付小程序 appId
  //
  // 注意只在使用易宝聚合支付托管下单时会返回
  string tutelageAppId = 8;
  // 跳转小程序路径
  //
  // 注意只在使用易宝聚合支付托管下单时会返回
  string miniProgramPath = 9;
  // 收钱吧订单凭证
  //
  // 注意只在使用收钱吧支付时会返回
  string orderToken = 10;
}

message OrderInfo {
  string created_time = 1;
  string out_order_id = 2;
  string openid = 3;
  string path = 4;
  string out_user_id = 5;
  PrePayOrderDetail order_detail = 6;
  DeliveryDetail delivery_detail = 7;
  AddressInfo address_info = 8;
}

message PrePayOrderDetail {
  repeated ProductInfo product_infos = 1;
  PayInfo pay_info = 2;
  PriceInfo price_info = 3;
}

message DeliveryDetail {
  uint32 delivery_type = 1;
}

message ProductInfo {
  string out_product_id = 1;
  string out_sku_id = 2;
  uint32 product_cnt = 3;
  uint32 sale_price = 4;
  string head_img = 5;
  string title = 6;
  string path = 7;
}

message PayInfo {
  string pay_method = 1;
  string prepay_id = 2;
  string prepay_time = 3;
}

message PriceInfo {
  uint32 order_price = 1;
  uint32 freight = 2;
}

message AddressInfo {
  string receiver_name = 1;
  string detailed_address = 2;
  string tel_number = 3;
}

message CreateOrderRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
  // @required
  //
  // 配送方式
  //
  // 可选值：pickup 自提，express 邮寄，cityExpress 同城快送，noExpress 无需配送
  string method = 3; // valid:"required,in(pickup|express|cityExpress|noExpress)"
  // 预留提货信息
  ReservationInfo reservation = 4;
  // 优惠券 ID 列表
  repeated string discountIds = 5;
  // 用户留言
  string message = 6;
  // 分销员的 memberId
  //
  // 通过分销员的分销链接购买商品时传此参数
  string promoterMemberId = 7;
  // 收货地址 ID
  string memberAddressId = 8; // valid:"objectId"
  // 提货人信息
  ContactRequest contact = 9;
  // @required
  //
  // 来源渠道 ID
  string channelId = 10; // valid:"required"
  // @required
  //
  // 渠道下客户唯一身份信息
  string openId = 11; // valid:"required"
  // 是否使用积分
  bool useScore = 12;
  // 付款方式
  //
  // 微信支付（wechat）、线下支付（offline）
  // TODO: valid
  string payment = 13;
  // 参与的活动
  repeated OrderCampaign campaigns = 14;
  // 企微导购分销员的 staffId
  //
  // 通过企微导购分销员的分销链接购买商品时传此参数，传此参数时不需要传 promoterMemberId
  string staffId = 15;
  // 订单标签
  //
  // 可选值：self_purchase 自购，daigou 代购，share 分享，scan_buy 扫码购，notDivide 不需要分账（不创建预分账单）, proxyOrder 代客下单订单
  repeated string tags = 16; // valid:"in(self_purchase|daigou|share|scan_buy|notDivide|proxyOrder)"
  // 访问来源
  mairpc.common.types.Utm utm = 17;
  // 期望送达时间
  int64 expectDeliveryAt = 18;
  // 期望送达时间文本
  string expectDeliveryAtLabel = 19;
  // 发票信息
  Invoice invoice = 20;
  // 商品附件
  repeated ProductAccessoryRequest productAccessories = 21;
  // 礼品卡 ID 列表
  repeated string prepaidCardIds = 22;
  // 权益卡 id 列表
  repeated string memberPaidCardRecordIds = 23;
  // 选中的优惠组合
  //
  // 可选值：活动优惠（campaign）、会员折扣（member）
  mairpc.common.types.StringList discountGroup = 24;
  // 过滤商品来源
  repeated string sources = 25;
  // 是否使用储值余额支付
  bool useStoredValue = 26;
  // 自定义抵现积分，当 useScore 为 true 时有效，抵扣 min(score,可抵扣积分)
  uint64 score = 27;
  // 订单备注
  string remarks = 28;
  // 自定义信息, json 格式的字符串
  string extra = 29;
  // 小票信息
  Ticket ticket = 33;
}

message PurchaseRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
  // 商品 ID
  string productId = 3; // valid:"objectId"
  // 商品 SKU
  string sku = 4;
  // 商品数量
  int64 count = 5;
  // 预留提货信息
  ReservationInfo reservation = 6;
  // 优惠券 ID 列表
  repeated string discountIds = 7; // valid:"objectIdList"
  // 用户留言
  string message = 8;
  // @required
  //
  // 配送方式
  //
  // 可选值：pickup 自提，express 邮寄，cityExpress 同城快送，noExpress 无需快递
  string method = 9; // valid:"required,in(pickup|express|cityExpress|noExpress)"
  // 分销员的 memberId
  //
  // 通过分销员的分销链接购买商品时传此参数
  string promoterMemberId = 10;
  // 客户地址 ID
  string memberAddressId = 11;
  // 提货人信息
  ContactRequest contact = 12;
  // @required
  //
  // 来源渠道 ID
  string channelId = 13; // valid:"required"
  // @required
  //
  // 渠道下客户唯一身份信息
  string openId = 14; // valid:"required"
  // 付款方式
  //
  // 微信支付（wechat）、线下支付（offline）
  // TODO: valid
  string payment = 15;
  // 是否使用积分
  bool useScore = 16;
  // 参与的活动
  repeated OrderCampaign campaigns = 17;
  // 自定义信息, json 格式的字符串
  string extra = 18;
  // 企微导购分销员的 staffId
  //
  // 通过企微导购分销员的分销链接购买商品时传此参数
  string staffId = 19;
  // 订单标签
  //
  // 可选值：self_purchase 自购，daigou 代购，share 分享，notDivide 不需要分账（不创建预分账单）
  repeated string tags = 20; // valid:"in(self_purchase|daigou|share|notDivide)"
  // 访问来源
  mairpc.common.types.Utm utm = 21;
  // 期望送达时间
  int64 expectDeliveryAt = 22;
  // 期望送达时间文本
  string expectDeliveryAtLabel = 23;
  // 发票信息
  Invoice invoice = 24;
  // 商品附件
  repeated ProductAccessoryRequest productAccessories = 25;
  // 礼品卡 ID 列表
  repeated string prepaidCardIds = 26;
  // 参加的活动
  //
  // 新增活动请使用这个字段
  OrderCampaignConfig campaignConfig = 27;
  // 是否忽略门店状态
  bool isIgnoreStoreStatus = 28;
  // 权益卡 id 列表
  repeated string memberPaidCardRecordIds = 29;
  // 选中的优惠组合
  //
  // 可选值：活动优惠（campaign）、会员折扣（member）
  mairpc.common.types.StringList discountGroup = 30;
  // 过滤商品来源
  repeated string sources = 31;
  // 是否使用储值余额支付
  bool useStoredValue = 32;
  // 小票信息
  Ticket ticket = 33;
  // 自定义抵现积分，当 useScore 为 true 时有效，抵扣 min(score,可抵扣积分)
  uint64 score = 34;
  // 订单备注
  string remarks = 35;
}

message Ticket {
  // 是否需要小票
  bool needed = 1;
}

message ReservationInfo {
  // 提货时间
  string time = 1;
  // 客户提货时间
  string memberTime = 2;
}

message Invoice {
  // 是否需要开发票
  bool needed = 1;
  // 发票状态
  //
  // 待开票（pending），已开票（issued），已关闭（closed）
  string status = 2;
  // 发票内容，json 字符串
  string content = 3;
}

message ProductAccessoryRequest {
  // 附件 id
  string id = 1; //valid:"objectId"
  // 定制附件提示信息
  string message = 2;
  // 附件数量
  uint64 count = 3;
}

message ContactRequest {
  // 提货人姓名
  string name = 1;
  // 提货人手机号
  string phone = 2;
  // 收货地址
  //
  // 优先使用 memberAddressId 获取地址信息，传此字段时可不传 memberAddressId
  mairpc.common.types.AddressCoordinate address = 3;
}

message OrderCampaign {
  // @required
  //
  // 活动类型
  //
  // 买赠活动(presentCampaign),拼团活动(grouponCampaign),促销套餐(packageCampaign),任选打包(optionalPackageCampaign),超值换购(plusBuyCampaign),限时折扣(discountCampaign),随机立减(randomDiscountCampaign)
  string type = 1; // valid:"required,in(presentCampaign|grouponCampaign|packageCampaign|optionalPackageCampaign|plusBuyCampaign|discountCampaign|randomDiscountCampaign)"
  // 买赠活动
  repeated PresentCampaign presentCampaigns = 2;
  // 拼团活动
  GrouponCampaign grouponCampaigns = 3;
  // 促销套餐
  repeated PackageCampaign packageCampaigns = 4;
  // 任选打包
  repeated OptionalPackageCampaign optionalPackageCampaigns = 5;
  // 超值换购
  repeated PlusBuyCampaign plusBuyCampaigns = 6;
  // 限时折扣
  repeated DiscountCampaign discountCampaigns = 7;
  // 随机立减
  RandomDiscountCampaign randomDiscountCampaign = 8;
}

message PresentCampaign {
  // 参与该活动的商品总价
  uint64 totalAmount = 1;
  // @required
  //
  // 买赠活动 Id
  string id = 2; // valid:"required,objectId"
  // 活动的赠品 id
  //
  // 用于判断赠品是否发生变更
  repeated string presentIds = 3;
  // 参与该活动的商品数量
  uint64 productCount = 4;
  bool isFreeShipping = 5;
}

message GrouponCampaign {
  // @required
  //
  // 活动 Id
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 活动模式
  //
  // 普通拼团（normal） 老带新拼团（new）
  string type = 2; // valid:"required,in(normal|new)"
  // 拼团id
  //
  // 为空表示新开团
  string grouponId = 3; // valid:"objectId"
}

message PackageCampaign {
  // 套餐 id
  string id = 1; // valid:"objectId"
  // 套餐商品
  repeated PackageProduct products = 2;
}

message PackageProduct {
  // 商品 id
  string id = 1; // valid:"objectId"
  // 商品 sku
  string sku = 2;
}

message OptionalPackageCampaign {
  // 活动 id
  string id = 1; // valid:"objectId"
}

message PlusBuyCampaign {
  // 活动 id
  string id = 1; // valid:"objectId"
  // 换购商品
  repeated PlusBuyProductRequest products = 2;
}

message PlusBuyProductRequest {
  // 商品 id
  string id = 1; // valid:"objectId"
  // 商品 sku
  string sku = 2;
}

message DiscountCampaign {
  // 活动 id
  string id = 1; // valid:"objectId"
}

message RandomDiscountCampaign {
  // 活动 ids
  repeated string ids = 1; // valid:"objectIdList"
  // 参与记录 id
  string recordId = 2; // valid:"objectId"
  // 根据活动 id 自动选择优惠的参与记录 id
  bool autoSelectRecordId = 3;
}

message OrderCampaignConfig {
  // 参加的活动
  repeated Campaign campaigns = 1;
  // 是否覆盖 PurchaseRequest 中商品，为 true 时使用 CampaignProduct 中商品
  bool overrideProduct = 2;
  // 邮费计算规则
  CampaignDeliveryFeeRule deliveryFeeRule = 3;
  // 是否禁用商品限购
  bool purchaseLimitDisable = 4;
  // 是否禁用区域限购
  bool areaRestrictionDisable = 5;
}

message Campaign {
  // 活动类型
  //
  // 秒杀活动（flashSaleCampaign）、付邮试用（trialCampaign）、代客下单（proxyOrder）
  string type = 1;
  // 活动 ID
  string id = 2; // valid:"objectId"
  // 活动标题
  string title = 3;
  // 活动商品
  repeated CampaignProduct products = 4;
  // 为 true 时表示不能使用会员权益
  bool memberDisabled = 5;
  // 为 true 时表示不能使用优惠券
  bool couponDisabled = 6;
  // 定金交易记录
  repeated TradeRecord tradeRecords = 7;
  // 为 true 时表示禁用会员折扣
  bool memberDiscountDisabled = 8;
  // 为 true 时表示忽略后台库存设置
  bool ignoreStockSetting = 9;
  // 赠品
  repeated CampaignPresent presents = 10;
  // 活动记录 id
  //
  // 当活动类型为周期购时，为周期购订单 id
  string recordId = 11;
  // 额外参数
  map<string, string> extra = 12;
  // 为 true 时表示不能使用储值卡
  bool prepaidCardDisabled = 13;
  // 储值卡和优惠券只能使用一种
  bool isDiscountLimit = 14;
  // 为 true 时表示不能使用权益卡
  bool isMemberPaidCardDisabled = 15;
  // 为 true 时表示不能使用积分
  bool isScoreDisabled = 16;
  // 使用优惠券限制
  CouponLimit couponLimit = 17;
  // 使用储值卡限制
  PrepaidCardLimit prepaidCardLimit = 18;
}

message CampaignDeliveryFeeRule {
  // 规则类型
  enum DelieveryFeeRuleType {
    // 固定金额
    FIXED_AMOUNT = 0;
  }
  // 当前只支持固定金额
  DelieveryFeeRuleType type = 1;
  // 金额
  uint64 amount = 2;
  // 规则适用配送方式
  repeated string feeRuleSuitMethods = 3;
}

message TradeRecord {
  string id = 1;
  string status = 2;
  uint64 payAmount = 3;
  string createdAt = 4;
}

message CampaignProduct {
  // 活动商品 ID
  string id = 1; // valid:"objectId"
  // 活动商品 sku
  string sku = 2;
  // 活动商品售价
  uint64 price = 3;
  // 折扣信息
  repeated DiscountInfo discounts = 4;
  // 活动商品数量
  int64 count = 5;
}

message CampaignPresent {
  // 赠品 ID
  string id = 1;
  // 赠品 SKU
  string sku = 2;
  // 赠品数量
  int64 count = 3;
  // 适用商品 ID 列表
  repeated string ids = 4;
}

message CalculateOrderAmountRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
  // 优惠券 ID 列表
  repeated string discountIds = 3;
  // 是否是直接购买
  //
  // 当此字段为 true 时才会用到购买商品的几个字段且此时这几个字段必填
  bool isPurchase = 4;
  // 购买商品的 ID
  string productId = 5; // valid:"objectId"
  // 购买商品 sku
  string sku = 6;
  // 购买商品数量
  uint64 count = 7;
  // 参与的活动
  repeated OrderCampaign campaigns = 8;
  // 是否使用积分
  bool useScore = 9;
  // 配送方式
  //
  // 门店自提 pickup、快递配送 express 同城快送 cityExpress、无需配送 noExpress
  string method = 10; // valid:"in(express|pickup|cityExpress|noExpress)"
  // 收货地址 ID
  string memberAddressId = 11; // valid:"objectId"
  // 额外信息
  //
  // deliveryFee：运费信息， product：商品信息, campaign：活动信息
  //
  // 需要返回运费信息时，配送方式和收货地址 ID 必填
  repeated string extra = 12;
  // 礼品卡 ID 列表
  repeated string prepaidCardIds = 13;
  // 参加的活动
  //
  // 新增活动请使用这个字段
  OrderCampaignConfig campaignConfig = 14;
  // 权益卡 id 列表
  repeated string memberPaidCardRecordIds = 15;
  // 自动选择优惠
  bool autoSelectDiscount = 16;
  // 选中的优惠组合
  //
  // 可选值：活动优惠（campaign）、会员折扣（member）
  mairpc.common.types.StringList discountGroup = 17;
  // 自动选择优惠券
  bool autoSelectCoupon = 18;
  // 过滤商品来源
  repeated string sources = 19;
  // 是否使用储值余额支付
  bool useStoredValue = 20;
  // 自定义抵现积分，当 useScore 为 true 时有效，抵扣 min(score,可抵扣积分)
  uint64 score = 21;
}

message CalculateOrderAmountResponse {
  // 订单总金额
  //
  // 单位：分
  uint64 totalAmount = 1;
  // 实际支付金额
  //
  // 单位：分
  uint64 payAmount = 2;
  // 可使用积分个数
  uint64 payScore = 3;
  // 积分可抵扣金额
  uint64 deductAmountByScore = 4;
  // 折扣信息
  repeated DiscountInfo discounts = 5;
  // 运费信息
  repeated DeliveryInfo deliveryInfos = 6;
  // 商品信息
  repeated ProductDetail products = 7;
  // 订单参与的活动
  repeated CampaignDetail campaigns = 8;
  // 可选的商品附件
  repeated ProductAccessoryResponse productAccessories = 9;
  // 礼品卡信息
  repeated PrepaidCardDetail prepaidCards = 10;
  // 订单支持的配送方式
  //
  // 快递配送（express），门店自提（pickup），同城配送（cityExpress），无需配送（noExpress）
  repeated string deliveryMethods = 11;
  // 同城配送起送金额差值 单位：分
  uint64 cityExpressDiffPrice = 12;
  // 使用的权益卡
  repeated MemberPaidCard memberPaidCards = 13;
  // 超值换购商品
  repeated PlusBuyProductResponse plusBuyProducts = 14;
  // 是否不允许使用储值卡
  bool isPrepaidCardDisabled = 15;
  // 错误信息
  string error = 16;
  // 优惠组合
  repeated Discount discountGroup = 17;
  // 选中的优惠券
  repeated SelectedMembershipDiscount selectedMembershipDiscounts = 18;
  // 储值余额可支付金额
  uint64 deductAmountByStoredValue = 19;
  // 储值余额
  uint64 storedValueBalance = 20;
  // 运费免邮规则
  DeliveryFeeFreeRule deliveryFeeFreeRule = 21;
  // 优惠券优惠金额是否溢出
  bool couponDiscountOverflow = 22;
  // 优先使用储值卡支付
  bool usePrepaidCardFirst = 23;
  // 优先使用储值卡支付的储值卡 ID 列表
  repeated string prioritizedPrepaidCardIds = 24;
}

message DeliveryFeeFreeRule {
  // 包邮类型，piece（按件数），amount（按金额），weight（按重量），distance（按距离），amountAndPiece（按金额和件数），amountAndDistance（按金额和距离），amountAndWeight（按金额和重量）
  string type = 1;
  // totalAmount 按订单金额计算包邮，payAmount 按实付金额计算包邮
  string amountType = 2;
  // 包邮金额
  uint64 amount = 3;
  // 包邮件数
  uint64 pieces = 4;
  // 包邮重量
  uint64 weight = 5;
  // 订单金额
  uint64 orderAmount = 6;
  // 商品件数
  uint64 productCount = 7;
  // 商品重量
  uint64 productWeight = 8;
  // 包邮距离
  uint64 distance = 9;
  // 订单距离
  uint64 orderDistance = 10;
}

message Discount {
  // 类型
  //
  // 活动优惠（campaign）、会员折扣（member)、权益卡（benefitCard）、储值卡（prepaidCard）、优惠券（coupon）、积分抵扣（score）
  string type = 1;
  // 是否当前已选中
  bool hasSelected = 2;
  // 优惠金额
  uint64 amount = 3;
  // 是否可用
  bool enabled = 4;
  // 不可用原因
  //
  // 活动（campaign）、会员折扣（member）、商品（product）、权益卡（benefitCard）、储值卡（prepaidCard）、优惠券（coupon）、付费卡券（couponProduct）
  string disableReason = 5;
  // 可用数量
  uint64 count = 6;
  // 活动优惠和会员折扣是否互斥
  bool isConflict = 7;
  // json 格式的字符串
  string extra = 8;
}

message SelectedMembershipDiscount {
  // membershipDiscount._id
  string id = 1;
  // 优惠券类型
  string type = 2;
  // 减免金额
  mairpc.common.types.DoubleValue reductionAmount = 3;
  // 类型为兑换券时，剩余可兑数量
  uint32 remainCount = 4;
}

message PlusBuyProductResponse {
  // 商品 id
  string id = 1;
  // 商品 sku
  string sku = 2;
  // 活动 id
  string campaignId = 3;
  // 商品名
  string name = 4;
  // 商品图片
  string picture = 5;
  // 商品规格
  repeated string properties = 6;
  // 换购价
  uint64 price = 7;
  // 商品原价
  uint64 originalPrice = 8;
  // 状态
  //
  // 未选中（unchecked）、已选中（checked）、不可选（disable）
  string status = 9;
  // 是否不允许使用储值卡
  bool isPrepaidCardDisabled = 10;
}

message ProductDetail {
  // 商品 ID
  string id = 1;
  // 商品名
  string name = 2;
  // product.id
  string productId = 3;
  // 商品图片
  string picture = 4;
  // 商品 sku
  string sku = 5;
  // 商品规格
  repeated string properties = 6;
  // 商品价格
  uint64 price = 7;
  // 商品数量
  uint64 count = 8;
  // 商品状态
  //
  // 未上架（initialized）、待上架（scheduled）、已上架（shelved）、已下架（unshelved）、库存不足（understocked）、已售罄（sold_out）、超出限购区域（restriction_out）
  // 不支持的配送方式（unsupported_method）、已过期（expired）
  string status = 9;
  // 会员折扣是否可用
  bool isMemberDiscountEnabled = 10;
  // 商品参与的活动
  repeated CampaignDetail campaigns = 11;
  // 类目 id
  string categoryId = 12;
  // 是否不允许使用礼品卡
  bool isPrepaidCardDisabled = 13;
  // 配送方式
  repeated string deliveryMethods = 14;
  // 是否可用优惠券
  bool isCouponEnabled = 15;
  // 库存
  uint64 stock = 16;
  // 商品类型
  string type = 17;
  // 限购
  PurchaseLimit purchaseLimit = 18;
  // 已购数量
  //
  // 当前周期已购数量，如果没有限购，则计数为 0
  uint64 purchasedCount = 19;
  // 虚拟商品核销有效期
  RedeemPeriod redeemPeriod = 20;
  // 折扣信息
  repeated DiscountInfo discounts = 21;
  // 开票模版 ID
  string invoiceTemplateId = 22;
  // 购物车 ID
  string cartId = 23;
  // 商品项唯一 ID
  string outTradeId = 24;
  // 商品原价
  uint64 originPrice = 25;
  // 起购件数
  uint32 minPurchaseCount = 26;
  // 结算方式
  //
  // 可选值：仅支持使用优惠券（coupon）
  string paymentMethod = 27;
  // 活动标签
  //
  // 标识商品参与了那些活动
  //
  // 满减/赠（presentCampaign）、拼团活动（grouponCampaign）、促销套餐（packageCampaign）、秒杀（flashSaleCampaign）、预售（presellCampaign）、
  // 付邮试用（trialCampaign）、助力砍价（bargainCampaign）、任选打包（optionalPackageCampaign）、超值换购（plusBuyCampaign）、
  // 超值换购换购商品（plusBuyProduct）、限时折扣（discountCampaign）
  repeated string campaignTags = 28;
  // 代销商品库存是否开启
  bool consignmentStockEnabled = 29;
  // 参与买赠活动商品数量
  uint64 presentDiscountCount = 30;
  // 同城配送，预约配送时间
  AppointmentTime cityExpressTime = 31;
  // 到店自提，预约自提时间
  AppointmentTime pickupTime = 32;
  // 参与限时折扣活动商品数量
  uint64 discountCampaignCount = 33;
  // 商品子类型: coupon（优惠券），storedValueCard（储值卡）
  string subType = 34;
}

message AppointmentTime {
  string start = 1;
  string end = 2;
}

message RedeemPeriod {
  // 核销有效期类型: limit（限制时间）、nolimit（不限时间）、absolute（指定时间有效）
  string type = 1;
  // 天数
  uint32 days = 2;
  // 是否开启有效期结束前提醒
  bool isNoticeEnabled = 3;
  // 消息设置
  NoticeSetting noticeSetting = 4;
  // 开始时间
  string startAt = 5;
  // 结束时间
  string endAt = 6;
}

message NoticeSetting {
  // 短信
  Text text = 1;
  // 订阅消息
  SubscribeMessage subscribeMessage = 2;
  // 动态参数
  repeated PlaceholderRules placeholderRules = 3;
}

message Text {
  // 短信内容
  string content = 1;
  // 是否可编辑
  bool isEditable = 2;
}

message SubscribeMessage {
  // 订阅消息 id
  string id = 1;
  // 打开小程序页面
  string page = 2;
  // 订阅消息内容
  repeated Data data = 3;
  // 订阅消息标题
  string title = 4;
  // 渠道 id
  string channelId = 5;
}

message Data {
  // 订阅消息键
  string key = 1;
  // 订阅消息值
  string value = 2;
  // 名称
  string name = 3;
  // 颜色
  string color = 4;
}

message PlaceholderRules {
  // 占位符
  string placeholder = 1;
  // 填充项
  Filler filler = 2;
  // 占位符名称
  string name = 3;
}

message Filler {
  // 属性
  string property = 1;
  // map 参数值转换规则
  map<string, string> valueTransfer = 2;
  // 默认值
  string default = 3;
}

message PurchaseLimit {
  // 限制指定人群
  MemberLimit member = 1; // valid:"optional"
  // 限制购买数量
  int64 count = 2;
  // 限制周期
  //
  // 每天：daily，每周：weekly，每月：monthly，永久限购：permanently
  string periodType = 3; // valid:"in(daily|weekly|monthly|permanently)"
}

message MemberLimit {
  // @required
  //
  // 限制类型
  //
  // 客户标签：memberTags，客户群组：memberGroups
  string type = 1; // valid:"required,in(tags|groups)"
  // @required
  //
  // 操作符
  //
  // 属于任一：IN，不属于任一：NOT_IN
  string operator = 2; // valid:"required,in(IN|NOT_IN)"
  // 静态标签列表
  repeated string tags = 3;
  // 静态群组 ID 列表
  repeated string staticGroupIds = 4; // valid:"objectIdList"
  // 动态群组 ID 列表
  repeated string dynamicGroupIds = 5; // valid:"objectIdList"
  // 智能标签列表
  repeated MemberLabel labels = 6;
}

message DeliveryInfo {
  // 运费
  uint64 deliveryFee = 1;
  // 需要支付的运费
  uint64 payAmount = 2;
  // 免邮说明
  //
  // memberPrivilege: 会员权益免邮, groupon: 拼团包邮,packageCampaign:促销套餐包邮,optionalPackageCampaign:任选打包包邮,presentCampaign:满减赠,bargainCampaign:砍价,benefitCard:权益卡包邮,plusBuyCampaign:超值换购包邮
  string freeReason = 3;
  // 配送方式
  string method = 4;
}

message ProductAccessoryResponse {
  // 附件 Id
  string id = 1;
  // 附件名
  string name = 2;
  // 附件类型
  //
  // 标准附件 normal，定制附件 custom
  string type = 3;
  // 最大数量
  //
  // 标准附件有意义
  uint64 limit = 4;
  // 定制附件内容是否必填
  //
  // 定制附件有意义
  bool isRequired = 5;
  // 附件提示信息
  string message = 6;
}

message DiscountInfo {
  // 优惠券 ID
  string id = 1;
  // 优惠券名称
  string title = 2;
  // 优惠金额
  //
  // 单位：分
  int64 amount = 3;
  // 折扣类型
  //
  // coupon：优惠券，score：积分抵扣，member：会员折扣，present: 买赠券，groupon：拼团优惠，package：套餐优惠，campaign：活动折扣，delivery_adjustment 运费改价，adjustment 商品改价，benefitCard 权益卡折扣
  string type = 4;
  // 会员等级
  uint64 memberLevel = 5;
  // 会员折扣
  //
  // 扩大十倍，8.8折即88
  uint64 discount = 6;
  // 套餐主图
  string picture = 7;
  // 活动类型
  //
  // 当 type 为 campaign 时有意义，秒杀活动（flashSaleCampaign）
  string campaignType = 8;
  // 操作时间
  string operatedAt = 9;
  // 操作人
  string operator = 10;
  // 会员名称
  string memberName = 11;
  // 优惠券券码
  string couponCode = 12;
  // 消费折扣适用总金额
  uint64 suitTotalAmount = 13;
  // 消费折扣适用对象
  //
  // 商品售价 price、商品划线价 originalPrice
  string priceType = 14;
  // 优惠券类型
  string couponType = 15;
}

message PrepaidCardDetail {
  // ec.prepaidCard._id
  string id = 1;
  // 抵扣金额 单位：分
  uint64 amount = 2;
  // 流水号
  string tradeNo = 3;
  // 礼卡卡号
  string number = 4;
}

message CampaignDetail {
  // 活动 id
  string id = 1;
  // 活动类型
  //
  // 查询不参与活动的订单时传 none,为空时表示不按活动类型查询
  string type = 2;
  // 活动名称
  string title = 3;
  // 赠品等级
  uint64 presentLevel = 4;
  // 赠品等级 Id
  string discountId = 5;
  // 活动商品
  //
  // 参与活动的商品 Id 列表
  repeated string productIds = 6;
  // 拼团 id
  string grouponRecordId = 7;
  // 拼团状态
  //
  // 进行中（running）|拼团成功（success）|拼团失败（failed）
  string grouponStatus = 8;
  // 拼团结束时间
  //
  // 查询时无意义
  string grouponRecordEndAt = 9;
  // 套餐 id
  string packageId = 10;
  // 循环次数
  uint64 count = 11;
  // 活动记录 id
  //
  // 当活动类型为周期购时，为周期购订单 id
  string recordId = 12;
  // 额外参数
  map<string, string> extra = 13;
}

message MemberPaidCard {
  // 权益卡 ID
  string id = 1;
  // 权益卡类型
  //
  // 权益卡（benefitCard）
  string type = 2;
  // 权益卡记录 ID
  string recordId = 3;
  // 权益卡名称
  string name = 4;
  // 简要权益信息，用于确认订单展示
  repeated BriefPrivilege privileges = 5;
  // 剩余消费额度 （本次订单扣减之前的额度）
  uint64 remainderDiscountLimit = 6;
}

message BriefPrivilege {
  // 权益名称
  string name = 1;
  // 多倍积分倍率
  float multiplier = 2;
  // 消费折扣的折扣
  uint64 discount = 3;
  // 消费折扣适用商品类型
  string type = 4;
  // 消费折扣总额度
  int64 limit = 5;
}

message IssueStaffCampaignCouponRequest {
  // @required
  //
  // 卡券活动 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 数量
  uint32 count = 2; // valid:"required"
  // @required
  //
  // 门店 ID
  string storeId = 3; // valid:"required,objectId"
  // 导购 ID
  string staffId = 4; // valid:"objectId"
  // @required
  //
  // 场景，可选值：groupChat（群聊）、singleChat（单聊）、qrCode（二维码）、faceToFace（面对面）、live（直播）、promotion（推广）
  string scene = 5; // valid:"required,in(groupChat|singleChat|qrCode|faceToFace|live|promotion)"
}

message ListProductsRequest {
  // 查询字符
  //
  // 若有商品名/商品编码/商品条码与查询字符相等，就会返回相关商品
  string queryString = 1;
  // 商品类目ID
  repeated string categoryIds = 2; // valid:"objectIdList"
  // 品牌ID
  repeated string brandIds = 3; // valid:"objectIdList"
  // 价格
  mairpc.common.types.IntegerRange price = 4;
  // 佣金比例
  mairpc.common.types.FloatRange profitProportion = 5;
  // 分页信息
  //
  // 排序字段支持：商品价格（ec.lowestPrice），销量（ec.sales），创建时间（createdAt），分类商品顺序（ec.product.classificationId.order），代销商品佣金价格/同步的经销商品在经销商租户的销量（ec.highestProfitAmount）
  // 分销商品销量（ec.distribution.sales），按分销商品佣金排序需要传（ec.distribution.proportion、ec.lowestPrice）
  // 若要按照分类商品顺序，请求中必须提供分类ID
  // 默认按照创建时间倒序（-createdAt）排列
  mairpc.common.request.ListCondition listCondition = 6;
  // 上架状态
  //
  // 已上架（shelved），待上架（scheduled），已下架（unshelved），已自动下架（scheduleUnshelved），已手动下架（operationUnshelved），未上架（initialized）
  repeated string status = 7;
  // 分类ID
  string classificationId = 8; // valid:"objectId"
  // 门店ID
  //
  // 若提供门店ID，那么返回值里 ec.skus[N].price 与 ec.skus[N].originalPrice
  // 字段展示的都会是对应门店所在区域的区域价格。其中 N 表示 ec.skus 中第 N 个值
  string storeId = 9; // valid:"objectId"
  // 是否有可用优惠券
  //
  // 若为 true，那么在商品中会用 ec.hasCoupon 字段来标识该商品是否有可用的单品券
  // 若请求里还提供了门店ID，那么判断是否有单品券时还会加上“单品券在门店所属地区可用”的条件
  bool hasCoupon = 10;
  // 是否为大众分销商品
  mairpc.common.types.BoolValue isDistribution = 11;
  // 是否设置了商品重量
  //
  // 只要该商品存在未设置 weight 字段(weight = 0)的 sku，即认为该商品未设置重量
  mairpc.common.types.BoolValue hasSetWeight = 12;
  // 商品ID
  repeated string ecProductIds = 13; // valid:"objectIdList"
  // 商品标签
  repeated string tags = 14;
  // 商品创建时间
  mairpc.common.types.StringDateRange createdAt = 15;
  // 是否参与会员折扣
  mairpc.common.types.BoolValue isMemberDiscountEnabled = 16;
  // 客户 id
  //
  // 小程序端使用，计算会员折扣价
  string memberId = 17;
  string skuPropertiesName = 18;
  // 商品ID
  repeated string productIds = 19; // valid:"objectIdList"
  // 商品分类属性
  repeated CategoryProperty categoryProperties = 20;
  // 配送方式
  //
  // 只要包含任意一种配送方式就会返回 可选值：到店自提（pickup），快递配送（express），同城配送（cityExpress）
  repeated string deliveryMethods = 21;
  // 排除掉的配送方式
  //
  // 所有排除掉的配送方式都不包含才会返回 可选值：到店自提（pickup），快递配送（express），同城配送（cityExpress）
  repeated string excludeDeliveryMethods = 22;
  // 是否包含已删除的商品
  bool containDeleted = 23;
  // 是否为导购分销商品
  mairpc.common.types.BoolValue isStaffDistribution = 24;
  // 佣金
  mairpc.common.types.IntegerRange profitAmount = 25;
  // 商品类型
  //
  // 可选值： product（实体商品），virtual(虚拟商品），coupon（卡券商品）
  repeated string types = 26;
  // 是否包含门店限售的商品
  bool containStoreRestriction = 27;
  // 更新时间
  mairpc.common.types.StringDateRange updatedAt = 28;
  // 商品类目 ID
  repeated string classificationIds = 29; // valid:"objectIdList"
  // 是否公开商品
  //
  // 默认不筛选商品是否公开
  mairpc.common.types.BoolValue public = 30;
  // product 额外信息
  repeated string productExtra = 31;
  // 发票模版 ids
  repeated string invoiceTemplateIds = 32;
  // 是否关联了发票模版
  mairpc.common.types.BoolValue existsInvoiceTemplateId = 33;
  // 直接从 DB 查询
  bool fromDB = 34;
  //  是否使用 scroll 遍历
  bool useScroll = 35;
  // 用 scroll 遍历客户时提供的标识ID
  string scrollId = 36;
  // 用 scroll 遍历客户时获取数据的大小，不传时默认 500
  int64 scrollSize = 37;
  // 使用此参数将导致上面的 tags 失效
  ProductTagsSelector tagsSelector = 38;
  // 商品参与的进行中的活动
  ProductCampaign campaign = 39;
  // 可选值 campaign，briefCampaign（简短活动信息，不返回 display、extra 等字段）
  repeated string extra = 40;
  // 商品关键词
  repeated string productKeywords = 41;
  // 排除掉的商品类目 ID
  repeated string excludeCategoryIds = 42; // valid:"objectIdList"
  // 查询是否有标签的商品
  //
  // 使用此参数 tags 和 productTagsSelector 都会失效
  mairpc.common.types.BoolValue existsTags = 43;
  // 代销商品佣金
  mairpc.common.types.IntegerRange consignmentProfit = 44;
  // 商品来源
  //
  // 可选值： ec（后台创建商品），ec_consignment(代销商品）
  string source = 45;
  // 扩展字段
  //
  // name 可取值：type（distribution 经销商品，consignment 代销商品），accountId（经销商租户 ID），distributorName（经销商名称），distributorId（经销商 ID），brand（品牌详细信息），consignmentProductStatus（代销商品在供货方的上下架状态），consignmentProductId（代销商品在供货方的 ID），distributionProductId（同步的经销商品在供货方的 ID），subSource（来源子类型，ec 自行创建, distribution 同步的经销商品）
  repeated FieldCond fields = 46;
  // 代销商品/店铺商品导购佣金
  //
  // D2R 连锁零售商
  mairpc.common.types.IntegerRange consignmentStaffProfit = 47;
  // 店铺商品管理筛选品牌
  //
  // D2R 连锁零售商
  repeated RetailBrand retailerBrands = 48;
  // 店铺商品管理选品库筛选状态
  //
  // 可选值：sales（销售中）、warehouse（仓库中）、unselected（未选品）
  // D2R 连锁零售商
  string selectionLibraryStatus = 49;
  // 是否存在分类
  mairpc.common.types.BoolValue existsClassification = 50;
  // 付费卡券商品类型
  //
  // coupon（卡券商品），storedValueCard（储值卡）
  string subType = 51;
  // 分销设置过滤
  PromoterSettingFilter promoterSettingFilter = 52;
  // 精准查询
  //
  // 商品条码，商品编码，外部SKU编码
  string searchKey = 53;
  // 商品sku
  string sku = 54;
}

message PromoterSettingFilter {
  string promoterId = 1;
  string orderBy = 2;
  bool exclude = 3;
}

message RetailBrand {
  // 品牌所属租户
  string accountId = 1; // valid:"objectId"
  // 品牌ID
  string id = 2; // valid:"objectId"
}

message FieldCond {
  // 字段名称
  string name = 1;
  // 字符串字段值
  string valueString = 2;
  // 整型字段值
  string valueInt = 3;
  // 字符串数组字段值
  repeated string valueStringArray = 4;
  // 对 valueString 中的字段进行时间范围搜索
  mairpc.common.types.StringDateRange stringDateRange = 5;
  // 根据某个 field 进行排序，如果指定了这个字段，则上面的筛选条件无效
  string orderBy = 6;
}

message ProductTagsSelector {
  // 标签条件
  repeated ProductTagsCondition tagsCondition = 1;
  // 标签条件之间的组合逻辑，and：同时满足，or：满足任一
  string operator = 2;
}

message ProductTagsCondition {
  // 标签列表
  repeated string tags = 1;
  // 条件，and、or、not
  string operator = 2;
}

message ProductCampaign {
  // 活动 id
  string id = 1; // valid:"objectId"
  // 活动名称
  string title = 2;
  // 活动类型
  string type = 3;
}

message CategoryProperty {
  // 属性名称
  string name = 1;
  // 属性值
  repeated string values = 2;
}

message InvoiceRequest {
  // @required
  //
  // 抬头类型 个人（person），单位（company）
  string type = 1; // valid:"required,in(person|company)"
  // @required
  //
  // 发票抬头
  string name = 2; // valid:"required"
  // 税号
  string taxID = 3;
  // 电子邮箱
  string email = 4; // valid:"email"
  // 地址
  string address = 5;
  // 电话号码
  string phone = 6;
  // 开户银行
  string bankName = 7;
  // 银行账号
  string bankAccount = 8;
  // 发票类型
  //
  // general（普通发票）、special（专用发票）
  string invoiceType = 9; // valid:"in(general|special)"
  // 发票内容
  //
  // productDetail（商品明细）、productCategory（商品类别）
  repeated string invoiceContents = 10; // valid:"in(productDetail|productCategory)"
  // 红字确认单 UUID
  string redConfirmUuid = 11;
}

message DeliveryFeeTemplate {
  // 模板类型
  //
  // piece 按件数，weight 按重量，distance 按距离
  string type = 1; // valid:"in(piece|weight|distance)"
  // 是否指定条件包邮
  bool conditionFree = 2;
  // 指定包邮金额类型
  //
  // totalAmount 按订单金额计算包邮，payAmount 按实付金额计算包邮
  string amountType = 3; // valid:"in(totalAmount|payAmount)"
  // 运费规则
  DeliveryFeeTemplateRule rule = 4;
  // 免邮规则
  DeliveryFeeTemplateFreeRule freeRule = 5;
}

message DeliveryFeeTemplateRule {
  // 首重(件)价格(单位: 分)，商品首重(件)计数内按首重(件)价格计费
  uint64 firstFee = 1;
  // 续重(件)价格(单位: 分)，即每续重(件)xx，增加运费xx分，大于0的数字
  uint64 additionalFee = 2;
  // 首重(件)数，当 type 为 weight 时单位为克
  uint64 firstAmount = 3;
  // 续重(件)数，当 type 为 weight 时单位为克
  uint64 additionalAmount = 4;
}

message DeliveryFeeTemplateFreeRule {
  // @required
  //
  // 规则类型，amount 指定金额，weight 指定重量，piece 指定件数，distance 指定距离，amountAndWeight 金额 + 重量，amountAndPiece 金额 + 件数，amountAndDistance 金额 + 距离
  string type = 1; // valid:"required,in(amount|weight|piece|distance|amountAndWeight|amountAndPiece|amountAndDistance)"
  // 金额(单位: 分)
  uint64 amount = 2;
  // 重量(单位: 克)
  uint64 weight = 3;
  // 件数
  uint64 pieces = 4;
  // 距离（单位：米）
  uint64 distance = 5;
}

message BriefProductDetail {
  // 商品 ec.Product.Id
  string id = 1;
  // 商品图
  string picture = 2;
  // 商品名
  string name = 3;
  // 商品编号
  string number = 4;
  // 商品价格
  uint64 price = 5;
  // 上下架状态
  string status = 6;
  // 是否公开
  bool public = 7;
  // sku
  repeated ECProductSKU skus = 8;
}

message ECProductSKU {
  // SKU编码
  string sku = 1;
  // 价格
  int64 price = 2;
  // 划线价
  int64 originalPrice = 3;
  // 库存数量
  int64 stock = 4;
  // 商品规格
  repeated string properties = 5;
}

message GetSensitiveResponse {
  string data = 1;
}

message MemberFilter {
  // 会员等级过滤
  MemberLevelFilter memberLevel = 1;
  // 群组过滤
  MemberGroupFilter memberGroup = 2;
  // 标签过滤
  MemberTagFilter memberTag = 3;
  // 各条件之间的组合关系，and（全部满足）、or（满足任一），默认为 and
  string operator = 4; // valid:"in(and|or)"
}

message MemberGroupFilter {
  // 限制符，IN（等于任一）、NOT_IN（不等于任一）
  string operator = 1; // valid:"in(IN|NOT_IN)"
  // 标签
  repeated MemberGroup groups = 2;
}

message MemberGroup {
  // 群组类型，static（静态群组）、dynamic（动态群组）、proposed（系统推荐人群）
  string type = 1; // valid:"in(static|dynamic|proposed)"
  // 群组 id
  string id = 2; // valid:"objectId"
  // 群组名称
  string name = 3;
}

message MemberLevelFilter {
  // 限制符，IN（等于任一）、NOT_IN（不等于任一）
  string operator = 1; // valid:"in(IN|NOT_IN)"
  // 标签
  repeated MemberLevel levels = 2;
}

message MemberLevel {
  // 类型，level（等级会员）、paidMember（付费会员）、none（非会员）
  string type = 1; // valid:"in(level|paidMember|none)"
  // 会员等级值，整数
  uint64 level = 2;
  // 会员等级名称
  string name = 3;
}

message MemberLabel {
  string name = 1;
  // 模型标签
  string field = 2;
  // 模型标签值
  string fieldValue = 3;
}

message MemberTagFilter {
  // 限制符，IN（等于任一）、NOT_IN（不等于任一）
  string operator = 1; // valid:"in(IN|NOT_IN)"
  // 标签
  repeated string tags = 2;
  // 模型标签
  repeated MemberLabel labels = 3;
}

message EcStoreFilter {
  // 标签过滤
  EcStoreTagFilter ecStoreTag = 1;
}

message EcStoreTagFilter {
  // 限制符，IN（等于任一）、NOT_IN（不等于任一）
  string operator = 1; // valid:"in(IN|NOT_IN)"
  // 标签
  repeated string tags = 2;
}

message CouponLimit {
  // 指定优惠券是否可用
  bool canUse = 1;
  // 优惠券 id 列表
  repeated string couponIds = 2; // valid:"objectIdList"
}

message PrepaidCardLimit {
  // 指定储值卡是否可用
  bool canUse = 1;
  // 储值卡 id 列表
  repeated string prepaidCardIds = 2; // valid:"objectIdList"
  // 场景
  string scene = 3;
  // 是否优先使用储值卡
  bool usePrepaidCardFirst = 4;
}

message StoredValue {
  // 支付金额
  uint64 amount = 1;
  // 交易流水号
  string tradeNo = 2;
  // 支付运费金额
  uint64 deliveryAmount = 3;
}

message RefundStoredValue {
  // 退款金额
  uint64 amount = 1;
  // 交易流水号
  string tradeNo = 2;
  // 退款状态
  string status = 3;
}

message ListPrepaidCardsRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 门店 ID
  string storeId = 2; // valid:"required,objectId"
  // 是否是直接购买
  //
  // 当此字段为 true 时才会用到购买商品的几个字段且此时这几个字段必填
  bool isPurchase = 3;
  // 购买商品的 ID
  string productId = 4; // valid:"objectId"
  // 购买商品 sku
  string sku = 5;
  // 购买商品数量
  uint64 count = 6;
  // 配送方式
  //
  // 门店自提 pickup、快递配送 express 同城快送 cityExpress、无需配送 noExpress
  string method = 7; // valid:"in(express|pickup|cityExpress|noExpress)"
  // 收货地址 ID
  string memberAddressId = 8; // valid:"objectId"
  // 参与的活动
  repeated mairpc.common.ec.OrderCampaign campaigns = 9;
  // 活动配置
  mairpc.common.ec.OrderCampaignConfig campaignConfig = 10;
  // 优惠券 ID 列表
  repeated string discountIds = 11;
  // 权益卡 ID 列表
  repeated string memberPaidCardRecordIds = 12;
  // 选中的优惠组合
  //
  // 可选值：活动优惠（campaign）、会员折扣（member）
  mairpc.common.types.StringList discountGroup = 13;
  // 是否使用积分
  bool useScore = 14;
  // 砍价记录 id
  string bargainRecordId = 15;
  // 秒杀活动 id
  string flashSaleCampaignId = 16;
  // 预售活动 id
  string presellCampaignId = 17;
}

message ApplicableProductTag {
  // @required
  //
  // 标签适用类型，include（包含）, exclude（不包含）
  string type = 1; // valid:"required,in(include|exclude)"
  // @required
  //
  // 标签列表
  repeated string tags = 2; // valid:"required"
}

message ApplicableCategory {
  // @required
  //
  // 品类适用类型，include（包含）, exclude（不包含）
  string type = 1; // valid:"required,in(include|exclude)"
  // @required
  //
  // 品类 id
  repeated string ids = 2; // valid:"required"
}

message ApplicableProductSkus {
  // 商品 ID
  string productId = 1; // valid:"objectId"
  // 商品 sku
  repeated string skus = 2;
  // sku 详情
  //
  // 仅用作响应中
  repeated ProductSkuDetail skuDetails = 3;
  // 商品 sku
  repeated SpecSku specSkus = 4;
}

message SpecSku {
  // 商品 sku
  string sku = 1;
  // 可兑换数量
  uint32 count = 2;
}

message ProductSkuDetail {
  // 商品 sku
  string sku = 1;
  // 商品外部 sku
  string external = 2;
}

message InvoiceTemplate {
  // 开票模版 ID
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 税收分类编码
  string taxClassificationCode = 3;
  // 税收分类名称
  string taxClassificationName = 4;
  // 优惠政策: nonuse（不使用）、zeroTax（普通零税）、freeTax（免税）、noTax（不征税）
  string favorablePolicy = 5;
  // 税率
  int64 taxRate = 6;
  // 关联商品数
  int64 productCount = 7;
  // 开票类型： deliveryFee（运费）、product（商品）
  string type = 8;
  // 开票名称
  string name = 9;
  // 备注
  string remarks = 10;
  // 是否单独开票（商品合并）
  bool isMergeInvoicing = 11;
}
