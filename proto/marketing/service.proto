syntax = "proto3";

package mairpc.marketing;

option go_package = "marketing";

import "common/benchmark/benchmark.proto";
import "common/request/request.proto";
import "common/response/response.proto";
import "marketing/affiliate.proto";
import "marketing/marketo.proto";

service MarketingService {
  rpc Benchmark(mairpc.common.benchmark.BenchmarkRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取营销活动详情
  rpc GetMarketo(GetMarketoRequest) returns (MarketoDetailResponse);
  // 创建/更新营销活动
  rpc UpsertMarketo(UpsertMarketoRequest) returns (MarketoDetailResponse);
  // 更新营销活动状态
  rpc UpdateMarketoStatus(UpdateMarketoStatusRequest) returns (mairpc.common.response.BoolResponse);
  // 获取营销活动列表
  rpc SearchMarketo(SearchMarketoRequest) returns (MarketoList);
  // 通过事件参加营销活动
  rpc JoinEventWorkflow(JoinEventWorkflowRequest) returns (mairpc.common.response.EmptyResponse);
  // 通过事件唤醒营销活动继续执行
  rpc AwakenMarketoWorkflow(AwakenMarketoWorkflowRequest) returns (mairpc.common.response.EmptyResponse);
  // 触发营销活动处理进程
  rpc TriggerMarketoWorkflowProcess(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 参加营销活动
  rpc JoinWorkflow(JoinWorkflowRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取营销活动客户计数
  rpc GetWorkflowMemberCount(GetWorkflowMemberCountRequest) returns (GetWorkflowMemberCountResponse);
  // 获取营销活动各节点计数
  rpc GetMarketoMemberCount(GetMarketoMemberCountRequest) returns (GetMarketoMemberCountResponse);
  // 搜索营销活动客户
  rpc SearchWorkflowMember(SearchWorkflowMemberRequest) returns (SearchWorkflowMemberResponse);
  rpc GetAffiliateProgram(mairpc.common.request.DetailRequest) returns (AffiliateProgram);
  rpc UpsertAffiliateProgram(UpsertAffiliateProgramRequest) returns (AffiliateProgram);
  rpc SearchAffiliateProgram(mairpc.common.request.ListCondition) returns (AffiliateProgramList);
  // 创建活动传播链
  //
  // 记录用户邀请关系，比如 A 邀请了 B 参与某项活动，那么 openId 为 B 的 openId，inviterOpenId 为 A 的 openId。
  //
  // 活动裂变场景的集成流程：
  //
  // - 用户 A 报名参与活动，生成专属海报后，调用此接口，openId 为 A 的 openId，inviterOpenId 为空。
  // - 用户 B 访问了 A 海报中的链接，调用此接口，openId 为 B 的 openId，inviterOpenId 为 A 的 openId，视为 A 邀请 B 参与活动。
  // - 如果之前 B 已经访问了用户 C 的海报，仍然视为 C 邀请了 B。（一个用户只能被邀请一次）
  //
  // TIP: 调用接口之前，需要确保用户已接入群脉系统，通过 openId、inviterOpenId 必须能找到客户数据。
  rpc TrackAffiliateProgram(TrackAffiliateProgramRequest) returns (mairpc.common.response.EmptyResponse);
  rpc GetAffiliateStatistics(GetAffiliateStatisticsRequest) returns (GetAffiliateStatisticsResponse);
  rpc GetAffiliateProgramFollower(GetAffiliateProgramFollowerRequest) returns (AffiliateProgramFollower);
  // 统计流程中的客户
  //
  // 会按照调用时间来统计这小时/这一天中加入与结束流程的客户数量
  rpc CountWorkflowMemberByTime(CountWorkflowMemberByTimeRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取流程趋势图统计
  rpc GetMarketoStatsByTime(GetMarketoStatsByTimeRequest) returns (GetMarketoStatsByTimeResponse);
  // 获取自动营销策略统计
  rpc GetMarketoStats(GetMarketoStatsRequest) returns (GetMarketoStatsResponse);
  // 获取参与了流程的客户列表
  rpc GetMarketoWorkflowMembers(GetMarketoWorkflowMembersRequest) returns (GetMarketoWorkflowMembersResponse);
  // 统计自动化营销工作流节点
  //
  // 仅能用于由自动化营销支持的那部分批量操作
  rpc CountWorkflowLogResults(CountWorkflowLogResultsRequest) returns (CountWorkflowLogResultsResponse);
  // 统计参与自动化营销的总人次
  rpc CountWorkflowByMarketoIds(mairpc.common.request.IdListRequest) returns (mairpc.common.response.IntResponse);
  // 关闭已执行完毕的批量操作
  rpc CloseFininshedOnceMarketo(mairpc.common.request.EmptyRequest) returns (MarketoList);
  // 遍历自动化营销工作流
  rpc IterateMarketoWorkflows(IterateMarketoWorkflowsRequest) returns (MarketoWorkflows);
  // 确认自动化营销工作量中模板消息的发送结果
  rpc CheckTemplateMessageStatus(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 统计自动化营销某个动作节点的数据
  rpc GetMarketoActionStats(GetMarketoActionStatsRequest) returns (GetMarketoActionStatsResponse);
  // 拉取新符合条件的客户进入一次性营销
  rpc PullNewMembersToOnceMarketo(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 拉取符合模型标签的客户进入营销
  rpc PullNewMembersByMemberLabels(mairpc.common.request.MemberLabelWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取进入了流程中某个节点的客户列表
  rpc GetMarketoWorkflowNodeMembers(GetMarketoWorkflowNodeMembersRequest) returns (GetMarketoWorkflowNodeMembersResponse);
  // 计算某个节点客户的数量，结果保存在 count 字段
  rpc CountMarketoWorkflowNodeMembers(GetMarketoWorkflowNodeMembersRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 将进入了流程中某个节点的客户导入群组
  rpc CreateGroupMembersFromMarketoWorkflow(CreateGroupMembersFromMarketoWorkflowRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 更新营销预估人数
  rpc UpdateMarketoEstimatedMemberCount(UpdateMarketoEstimatedMemberCountRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取测试统计
  rpc GetTestStats(GetTestStatsRequest) returns (GetTestStatsResponse);
  // 导出测试统计
  rpc ExportTestStats(GetTestStatsRequest) returns (mairpc.common.response.JobResponse);
  // 初始化默认数据
  rpc InitDefaultResources(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取营销日历数据概览
  rpc GetDataOverview(GetMarketoOverviewRequest) returns (GetDataOverviewResponse);
  // 到达开始时间，开启活动；到达结束时间后，结束进行中、已停止的路径
  rpc AutoUpdateMarketoStatus(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 添加审核人
  rpc AddAuditors(mairpc.common.request.IdListRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取审核人列表
  rpc ListAuditors(ListAuditorsRequest) returns (ListAuditorsResponse);
  // 审核
  rpc AuditMarketo(AuditMarketoRequest) returns (mairpc.common.response.EmptyResponse);
  // 移除审核人
  rpc DeleteAuditors(mairpc.common.request.IdListRequest) returns (mairpc.common.response.EmptyResponse);
  // 修改路径审核设置
  rpc UpsertMarketoAuditSetting(UpsertMarketoAuditSettingRequest) returns (AuditSettingDetail);
  // 获取路径审核设置
  rpc GetMarketoAuditSetting(mairpc.common.request.EmptyRequest) returns (AuditSettingDetail);
  // 获取待审核和已审核列表
  rpc ListAuditMarketos(ListAuditMarketosRequest) returns (ListAuditMarketosResponse);
  // 提交审核
  rpc ApplyAudit(ApplyAuditRequest) returns (mairpc.common.response.EmptyResponse);
  // 撤销审核申请
  rpc CancelAudit(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 导出已审核路径列表
  rpc ExportAuditMarketos(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.JobResponse);
  // 定时处理特殊规则路径
  rpc TriggerRuleMarketo(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 迁移路径的组织结构
  rpc MigrateMarketoDistributor(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 同步批量操作结果
  rpc SyncBatchOperationResult(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 触发营销计划
  rpc ProcessMarketingPlanWorkflow(ProcessMarketingPlanWorkflowRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量更新防骚扰设置
  rpc BatchUpsertMarketingNotificationLimitSettings(BatchUpsertMarketingNotificationLimitSettingsRequest) returns (mairpc.common.response.EmptyResponse);
  // 查询防骚扰设置
  rpc ListMarketingNotificationLimitSettings(mairpc.common.request.EmptyRequest) returns (ListMarketingNotificationLimitSettingsResponse);
  // 记录会员通知
  rpc RecordMemberNotification(RecordMemberNotificationRequest) returns (mairpc.common.response.BoolResponse);
  // 查询营销计划工作流
  rpc ListMarketingPlanWorkflows(ListMarketingPlanWorkflowsRequest) returns (ListMarketingPlanWorkflowsResponse);
  // 导出营销计划工作流
  rpc ExportMarketingPlanWorkflows(ExportMarketingPlanWorkflowsRequest) returns (mairpc.common.response.JobResponse);
  // 发送路径审核通知
  rpc ResendMarketoAuditNotification(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
}
