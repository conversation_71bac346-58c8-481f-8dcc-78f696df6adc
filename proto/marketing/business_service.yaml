type: google.api.Service
config_version: 3

http:
  rules:
    - selector: mairpc.marketing.MarketingService.TrackAffiliateProgram
      post: /v2/marketing/affiliatePrograms/{id}/track
      body: '*'
      tags: ['活动矩阵-营销活动']
      filterMark: openapi
      scope: ['app']
    - selector: mairpc.marketing.MarketingService.GetTestStats
      get: /v2/marketing/marketo/{marketoId}/testStats
      tags: ['活动矩阵-营销活动']
      filterMark: backend
    - selector: mairpc.marketing.MarketingService.ExportTestStats
      post: /v2/marketing/marketo/exportTestStats
      body: '*'
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.InitDefaultResources
      post: /v2/marketing/initDefaultResources
      filterMark: backend
      tags: ['通用-测试']
    - selector: mairpc.marketing.MarketingService.SearchMarketo
      post: /v2/marketing/marketos/search
      body: '*'
      filterMark: backend
      tags: ['活动矩阵-营销活动']
      permission: 'marketo:list'
    - selector: mairpc.marketing.MarketingService.GetMarketo
      get: /v2/marketing/marketos/{id}
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.UpsertMarketo
      post: /v2/marketing/marketos/upsert
      body: '*'
      filterMark: backend
      tags: ['活动矩阵-营销活动']
      permission: 'marketo:create'
    - selector: mairpc.marketing.MarketingService.UpdateMarketoStatus
      put: /v2/marketing/marketos/{id}/status
      body: '*'
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.GetDataOverview
      post: /v2/marketing/marketos/getDataOverview
      tags: ['活动矩阵-营销活动']
      filterMark: backend
    - selector: mairpc.marketing.MarketingService.AddAuditors
      post: /v2/marketing/marketo/auditors
      body: '*'
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.ListAuditors
      get: /v2/marketing/marketo/auditors
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.AuditMarketo
      post: /v2/marketing/marketo/audit
      body: '*'
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.DeleteAuditors
      delete: /v2/marketing/marketo/auditors
      body: '*'
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.UpsertMarketoAuditSetting
      post: /v2/marketing/marketo/auditSetting
      body: '*'
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.ListAuditMarketos
      get: /v2/marketing/marketo/auditMarketos
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.ApplyAudit
      post: /v2/marketing/marketo/applyAudit
      body: '*'
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.GetMarketoAuditSetting
      get: /v2/marketing/marketo/auditSetting
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.CancelAudit
      post: /v2/marketing/marketos/{id}/cancelAudit
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.ExportAuditMarketos
      post: /v2/marketing/marketo/auditMarketos/export
      body: '*'
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.GetMarketoMemberCount
      get: /v2/marketing/marketo/memberCount
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.CountMarketoWorkflowNodeMembers
      get: /v2/marketing/marketo/nodeMemberCount
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.GetMarketoWorkflowNodeMembers
      get: /v2/marketing/marketo/nodeMembers
      filterMark: backend
      sensitiveFields: 'workflowMembers.member.name,workflowMembers.member.nickname,workflowMembers.member.phone'
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.CreateGroupMembersFromMarketoWorkflow
      post: /v2/marketing/marketo/importWorkflowMembersToMemberGroup
      body: '*'
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.MigrateMarketoDistributor
      post: /v2/marketing/marketo/migrateDistributors
      filterMark: backend
      tags: ['测试']
    - selector: mairpc.marketing.MarketingService.SyncBatchOperationResult
      post: /v2/marketing/marketo/syncBatchOperationResult
      filterMark: backend
      tags: ['测试']
    - selector: mairpc.marketing.MarketingService.JoinEventWorkflow
      post: /v2/marketing/marketo/joinEventWorkflow
      body: '*'
      filterMark: backend
      tags: ['测试']
    - selector: mairpc.marketing.MarketingService.BatchUpsertMarketingNotificationLimitSettings
      post: /v2/marketing/notificationLimitSettings/upsert
      body: '*'
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.ListMarketingNotificationLimitSettings
      get: /v2/marketing/notificationLimitSettings
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.ListMarketingPlanWorkflows
      post: /v2/marketing/marketingPlanWorkflows/list
      body: '*'
      sensitiveFields: 'items.memberName'
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.ExportMarketingPlanWorkflows
      post: /v2/marketing/marketingPlanWorkflows/export
      body: '*'
      filterMark: backend
      tags: ['活动矩阵-营销活动']
    - selector: mairpc.marketing.MarketingService.ResendMarketoAuditNotification
      post: /v2/marketo/{id}/resendAuditNotification
      body: '*'
      filterMark: backend
      tags: ['活动矩阵-营销活动']
