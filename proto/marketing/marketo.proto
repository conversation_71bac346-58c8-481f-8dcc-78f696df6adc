syntax = "proto3";

package mairpc.marketing;

option go_package = "marketing";

import "account/channel/line.proto";
import "account/whatsApp/whats_app.proto";
import "common/request/request.proto";
import "common/types/types.proto";
import "member/member.proto";

message GetMarketoRequest {
  string id = 1;
  string name = 2;
  // 活动信息
  Campaign campaign = 3;
}

message UpsertMarketoRequest {
  string id = 1;
  int64 startAt = 3;
  string status = 4; // valid:"required,in(unfinishdraft|draft|ongoing|waiting|rejected|stopped)"
  string name = 5; // valid:"required"
  string type = 6; // valid:"required,in(event|rule|once)"
  string filterType = 7; // valid:"required,in(all|tag|file|group|custom)"
  repeated string tags = 8;
  bool repeatable = 9;
  MarketoEvent event = 10; // valid:"optional"
  MarketoRule rule = 11; // valid:"optional"
  repeated Workflow workflows = 12; // valid:"optional"
  // 用于标识当前自动化营销是否属于客户批量操作
  bool isBatchOperation = 13; // valid:"optional"
  // 若filterType是group，则用于指定客户群组
  repeated GroupInfo group = 14;
  // 若filterType是file，则用于指定文件信息
  FileInfo file = 15;
  // marketo 创建人信息
  UserInfo user = 16;
  int64 firstStartAt = 17;
  // 活动信息
  Campaign campaign = 18;
  // 组织节点信息
  mairpc.common.types.DistributorInfo distributorInfo = 19; // valid:"optional"
  // 有效期，默认为当前到永久
  mairpc.common.types.StringDateRange enablePeriod = 20;
  // 结束后是否继续处理已进入流程的目标
  bool shouldStopWorkflows = 21;
  // 路径说明
  string remark = 22;
  // 如果 filterType 是 tag 且存在模型标签时用于制定模型标签
  repeated MemberLabelInfo memberLabels = 23;
  // 可见客户标签
  repeated string visibleMemberTags = 24;
  // 可见客户规则标签
  repeated string visibleMemberRuleTags = 25;
  // 可见客户模型标签
  repeated string visibleMemberModelTags = 26;
}

message MemberLabelInfo {
  string name = 1;
  string field = 2;
  string fieldValue = 3;
  string type = 4;
}

message Campaign {
  // 活动 ID
  string id = 1; // valid:"objectId"
  // 活动名称
  string name = 2;
}

message GroupInfo {
  string id = 1;
  string name = 2;
  string type = 3; // valid:"optional,in(dynamic|static)"
}

message FileInfo {
  string url = 1;
  string type = 2; // valid:"optional,in(csv|xls|xlsx)"
}

message UserInfo {
  string id = 1;
  string name = 2;
}

message EventProperty {
  string propertyId = 1;
  oneof value {
    int64 valueInt = 2;
    string valueString = 3;
    bool valueBool = 4;
    double valueDouble = 5;
    string valueDatetime = 6;
  }
}

message AwakenMarketoWorkflowRequest {
  string eventId = 1; // valid:"required"
  string memberId = 2; // valid:"required"
  repeated EventProperty eventProperties = 3;
  // 事件实际发生时间。毫秒级 例如 *************
  int64 occurredAt = 4;
}

message MarketoDetailResponse {
  string id = 1;
  int64 startAt = 2;
  string status = 3;
  string name = 4;
  string type = 5;
  string filterType = 6;
  repeated string tags = 7;
  bool repeatable = 8;
  MarketoEvent event = 9;
  MarketoRule rule = 10;
  repeated Workflow workflows = 11;
  int64 updatedAt = 12;
  bool isDeleted = 13;
  repeated GroupInfo group = 14;
  FileInfo file = 15;
  UserInfo user = 16;
  int64 firstStartAt = 17;
  int64 createdAt = 18;
  uint64 estimatedMemberCount = 19;
  mairpc.common.types.StringDateRange enablePeriod = 20;
  bool shouldStopWorkflows = 21;
  string rejectReason = 22;
  AuditMarketoDetail auditInfo = 23;
  string remark = 24;
  repeated MemberLabelInfo memberLabels = 25;
  repeated string visibleMemberTags = 26;
}

message EventArrayValue {
  repeated string value = 1;
}

message DoubleDimensionalArrayValue {
  repeated EventArrayValue value = 1;
}

message MarketoEvent {
  string eventId = 1; // valid:"required"
  string propertyId = 2;
  string operator = 3; // valid:"in(IN|NOT_IN|EQUALS|NOT_EQUALS|GT|GTE|LT|LTE|IS_NULL|IS_NOT_NULL|BETWEEN|NOT_BETWEEN|CONTAINS_ANY|NOT_CONTAINS_ANY|CONTAINS|NOT_CONTAINS|DEFAULT)"
  oneof value {
    EventArrayValue valueStringArray = 4;
    uint64 valueInt = 5;
    IntArrayValue valueIntArray = 6;
    DoubleDimensionalArrayValue valueStringDoubleDimensionalArray = 7;
    string valueString = 8;
    bool valueBool = 9;
  }
  repeated string dependentPropertyIds = 10;
  uint32 times = 11;
  int64 reserveTime = 12;
  // 对事件属性的筛选，需搭配 matchers 判断，判断顺序需要和 matchers 一致
  repeated MarketoEventProperty properties = 13;
  repeated RuleMatcher matchers = 14;
}

message MarketoEventProperty {
  string propertyId = 1;
  repeated string dependentPropertyIds = 2;
}

message MarketoRule {
  string type = 1; // valid:"in(birthday|holiday|custom)"
  string date = 2;
  uint32 dateOffset = 3;
  string frequency = 4; // valid:"in(daily|weekly|monthly|yearly)"
  uint32 month = 5;
  uint32 day = 6;
  string time = 7;
}

message Workflow {
  string id = 1; // valid:"required,length(10|10)"
  string parentId = 2; // valid:"length(10|10)"
  string parentBranch = 3;
  string type = 4; // valid:"in(action|control|end)"
  Action action = 5; // valid:"optional"
  Control control = 6; // valid:"optional"
  bool isDeleted = 7;
  bool isNew = 8;
  bool edited = 9;
}

message Action {
  // 动作类型
  //
  // sms：短信，digital_sms：数字短信，aliyunqa_sms：阿里云QA 普通短信，aliyunqa_digital_sms：阿里云QA 数字短信，wechat：微信消息，coupon：发放优惠券，tag：更新标签，
  // customer：更新客户 property，member：更新客户属性，template：发送微信模板消息，webhook：调用回调地址，email：发送邮件，message：发送客户消息，wechatwork_message：发企微消息
  // wechat_mass：群发消息，staff_operation：智慧导购日常运营，whatsapp_message：发 WhatsApp 消息， member_medal：收发会员勋章，line_message 发 Line 消息
  string type = 1; // valid:"in(sms|digital_sms|aliyunqa_sms|aliyunqa_digital_sms|wechat|coupon|tag|customer|member|template|webhook|email|message|wechatwork_message|wechat_mass|staff_operation|whatsapp_message|member_medal|line_message)"
  string content = 2;
  repeated PropertyValueMap propertyValueMaps = 3;
  repeated string channelIds = 4;
  string msgType = 5; // valid:"in(TEXT|IMAGE|VOICE|VIDEO|MPNEWS|NEWS|MINI_PROGRAM_PAGE)"
  string title = 6;
  string url = 7;
  repeated Mpnews mpnews = 8;
  repeated News news = 9;
  repeated MiniPrograms miniPrograms = 10;
  ActionCoupon coupon = 11;
  ActionTag tag = 12;
  ActionCustomer customer = 13;
  ActionMember member = 14;
  ActionTemplate template = 15;
  ActionWebhook webhook = 16;
  string remark = 17;
  ActionEmail email = 18;
  // 素材 Id
  string mediaId = 19;
  // 消息 Id
  string messageId = 20;
  // 阿里云QA 短信、数字短信
  ActionAliyunqaSms aliyunqaSms = 21;
  // 自定义信息
  string extra = 22;
  // 客户执行规则，failedAndStop（执行失败停止执行）、failedAndContinue（执行失败继续执行），默认执行失败停止执行
  string memberProcessRule = 23; // valid:"in(failedAndStop|failedAndContinue)"
  repeated ActionWechatworkMessage wechatworkMessages = 24;
  // 日常运营的 id
  string staffOperationTemplateId = 25;
  // 移除的日常运营 id
  string removedStaffOperationTemplateId = 26;
  // WhatsApp 消息
  ActionWhatsAppMessage whatsAppMessage = 27;
  // 会员勋章
  ActionMemberMedal memberMedal = 28;
  // line 消息
  ActionLineMessage lineMessage = 29;
  // 短信签名
  string smsTopic = 30;
}

message BriefMemberMedal {
  string id = 1;
  string name = 2;
}

message ActionMemberMedal {
  // 发放还是回收，issue（发放）、rollback（回收）
  string type = 1;
  repeated BriefMemberMedal medals = 2;
}

message ActionWhatsAppMessage {
  // 消息类型，template（模板消息）、custom（自由格式消息）
  string type = 1; // valid:"in(template|custom)"
  // 模板
  mairpc.account.whats_app.MessageTaskTemplate template = 2;
  // 自定义消息
  repeated mairpc.account.whats_app.CustomMessageContent content = 3;
}

message ActionLineMessage {
  // 消息内容
  repeated mairpc.account.channel.LineMessageContent content = 3;
}

message ActionAliyunqaSms {
  // 短信名称
  string taskName = 1;
  // 短信平台
  string platformId = 2;
  // 短信签名
  string signName = 3;
  // 模板 ID
  string templateId = 4;
}

message ActionEmail {
  string type = 1;
  string subject = 2;
  string body = 3;
  string replyToAddress = 4;
  string mailType = 5;
  string mailSenderId = 6;
}

message ActionWebhook {
  string url = 1;
}

message ActionWechatworkMessage {
  // @required
  //
  // 消息类型，text：文本素材，image：图片素材，video：视频素材，link：网页素材，miniProgram：小程序素材
  string type = 1; // valid:"required,in(text|image|video|link|miniProgram)"
  TextMaterialDetail text = 2;
  repeated ImageMaterialDetail images = 3;
  VideoMaterialDetail video = 4;
  LinkMaterialDetail link = 5;
  MiniProgramMaterialDetail miniProgram = 6;
}

message TextMaterialDetail {
  // 素材正文
  string content = 1;
  // 标题
  string title = 2;
}

message ImageMaterialDetail {
  // 图片地址
  string url = 1;
  // 标题
  string title = 2;
}

message VideoMaterialDetail {
  // 视频地址
  string url = 1;
  // 标题
  string title = 2;
}

message LinkMaterialDetail {
  // 网页标题
  string title = 1;
  // 网页地址
  string url = 2;
  // 分享描述
  string shareDescription = 3;
  // 分享图片
  string sharePicture = 4;
}

message MiniProgramMaterialDetail {
  // 小程序标题
  string title = 1;
  // appId
  string appId = 2;
  // 原始 id
  string originId = 3;
  // 页面路径
  string path = 4;
  // 内容描述
  string contentDescription = 5;
  // 小程序名称
  string name = 6;
  // 分享描述
  string shareDescription = 7;
  // 分享图片
  string sharePicture = 8;
  // 图标
  string icon = 9;
}

message PropertyStringArrayValue {
  repeated string value = 1;
}

message PropertyStringValue {
  string value = 1;
}

message PropertyDateValue {
  int64 value = 1;
}

message PropertyBoolValue {
  bool value = 1;
}

message PropertyNumberValue {
  double value = 1;
}

message PropertyNumberArrayValue {
  repeated double value = 1;
}

message ActionCustomer {
  string property_id = 1;
  oneof value {
    PropertyStringArrayValue value_string_array = 2;
    PropertyStringValue value_string = 3;
    PropertyDateValue value_date = 4;
    PropertyBoolValue value_bool = 5;
    PropertyNumberValue value_number = 6;
    PropertyNumberArrayValue value_number_array = 7;
  }
  string property_name = 8;
  string property_type = 9;
}

message ActionMember {
  string type = 1; // valid:"required,in(card|score)"
  int64 score = 2;
  string card_id = 3;
  int64 growth = 4;
  string description = 5;
}

message ActionCoupon {
  string type = 1; // valid:"in(all|credit|discount|cash|gift|coupon|redpack|external)"
  repeated string ids = 2;
}

message ActionTag {
  string type = 1; // valid:"in(+|-)"
  repeated string tags = 2;
  // 标签类型，mai（默认）：cep 标签，wechat：微信标签
  string tagType = 3; // valid:"mai|wechat"
  // 打微信标签的渠道
  string channelId = 4;
  // 微信标签名称到 id 的映射
  map<string, int64> wxTagMap = 5;
}

message News {
  string channelId = 1;
  string materialId = 2;
  repeated Article articles = 3;
}

message Article {
  string title = 1;
  string description = 2;
  string url = 3;
  string sourceUrl = 4;
}

message Mpnews {
  string channelId = 1;
  string materialId = 2;
}

message MiniPrograms {
  string appId = 1;
  string title = 2;
  string pagePath = 3;
  string thumbImageUrl = 4;
}

message Control {
  // 流程控制节点类型
  //
  // event：客户事件，property：客户属性，waiting：等待，social：社交平台属性，member：会员属性，tag：客户标签
  // test：AB测试根节点，testGoal：AB测试目标节点，ec：电商平台，memberGroup：客户群组，parentResult：父节点执行结果
  string type = 1; // valid:"in(event|property|waiting|social|member|tag|test|testGoal|ec|memberGroup|parentResult)"
  ControlEvent event = 2; // valid:"optional"
  Waiting waiting = 3; // valid:"optional"
  ControlProperty property = 4; // valid:"optional"
  ControlSocial social = 5; // valid:"optional"
  ControlMember member = 6; // valid:"optional"
  ControlTag tag = 7; // valid:"optional"
  string extra = 8; // valid:"json,optional"
  ControlTest test = 9; // valid:"optional"
  ControlTestGoal testGoal = 10; // valid:"optional"
  // 所属 A/B 测试节点 Id
  //
  // 用于标识 A/B 测试目标节点所属的测试节点，非测试目标节点此字段为空
  string testId = 11;
  // 测试目标节点 Id
  //
  // 用于标识 A/B 测试目标节点所属的汇总节点，非测试目标节点此字段为空
  string testGoalId = 12;
  // 子类型
  //
  // 用于新的 type 类型但仍然使用已有的结构，如电商平台对 social 的筛选，仍使用 social 字段保存相关规则
  string subType = 13;
  ControlMemberGroup memberGroup = 14; // valid:"optional"
  ControlResult parentResult = 15; // valid:"optional"
}

message MemberLabelRule {
  string operator = 1; // valid:"in(IN|NOT_IN|CONTAINS_ANY|NOT_CONTAINS_ANY|CONTAINS|NOT_CONTAINS)"
  string branch = 2;
  repeated MemberLabelInfo memberLabels = 3;
  string branchName = 4;
}

message ControlResult {
  string branchType = 1; // valid:"in(binary)"
  // 指定成功执行分支请将对应 value 置为 true，失败分支置为 false, operator 置为 EQUALS，数组长度仅支持 2
  repeated ControlRule rules = 6;
}

message ControlMemberGroup {
  string branchType = 1; // valid:"in(binary)"
  repeated ControlRule rules = 6;
}

message ControlTest {
  string branchType = 1; // valid:"in(binary|multi)"
  // 测试描述
  string description = 2;
  // 测试总次数
  //
  // 测试达到总次数后停止测试，0 表示一直执行
  int64 limitTimes = 3;
  // 是否使用效率最高的分支
  //
  // 当为 true 时测试达到测试总次数后，后续直接执行效率最高的分支
  bool useEfficientBranch = 4;
  // 测试结束后剩余客户执行的分支
  //
  // 如果设置了使用效率最高的分支，此字段会在确定最高执行效率的分支后被更新为该分支
  string endUseBranch = 5;
  // 目标分支测试比例规则
  //
  // 存放测试分支及其测试配比信息，测试比例为百分比保留两位小数，传值时需要乘以 100 转换成整数，如 10.05 -> 1005
  repeated ControlRule rules = 6;
  // 测试统计 Id
  string statsId = 7;
  // 是否重新开始测试
  bool restartTest = 8;
}

message ControlTestGoal {
  string branchType = 1; // valid:"in(binary)"
  string testId = 2;
  repeated ControlRule rules = 3;
}

message ControlTag {
  string branchType = 1; // valid:"in(binary|multi)"
  repeated ControlRule rules = 2;
  repeated MemberLabelRule memberLabelRules = 3;
}

message ControlProperty {
  string id = 1;
  string type = 2;
  repeated ControlRule rules = 3; // valid:"optional"
  string branchType = 4; // valid:"in(binary|multi)"
  string locationType = 5; // valid:"in(province|city|district)"
  // 客户属性名称
  string name = 6;
  // 比较日期时忽略的单位，默认为 year
  string dateIgnoreType = 7; // valid:"in(year)"
}

message ControlSocial {
  string branchType = 1; // valid:"in(binary|multi)"
  string channelType = 2; // valid:"in(isAuthorized|isUnauthorized|isNotAuthorized|isSubscribed|isUnsubscribed|isNotSubscribed|used|unused)"
  repeated ControlRule rules = 3;
  // rules 中比较的 social 字段
  //
  // channel：渠道 ID（默认），origin：渠道来源
  string field = 4;
  // 参与条件判断的渠道来源，默认会包含 wechat 和 weapp 两种来源
  repeated string origins = 5;
}

message ControlEvent {
  int64 reserveTime = 1;
  string eventId = 2;
  string propertyId = 3;
  repeated ControlRule rules = 4;
  uint32 times = 5;
  repeated string dependentPropertyIds = 7;
  string branchType = 8; // valid:"in(binary|multi)"
  string limitType = 9; // valid:"in(property|times|all)"
  // value、operator、matchers 均为 branchType 为 all 时的特有字段
  ArbitraryValue value = 10;
  string operator = 11;
  repeated RuleMatcher matchers = 12;
  // 配置对事件属性的判断，需和 rules 中的 matchers 判断顺序一致
  repeated MarketoEventProperty properties = 13;
  // 指定等待时间，精确值
  string specifiedAt = 14; // valid:"rfc3339"
  // 下一个时间点，HH:MM
  string nextTime = 15; // valid:"matches([0-9]{2}:[0-9]{2})"
}

message ControlMember {
  string branchType = 1; // valid:"in(binary|multi)"
  string field = 2; // valid:"in(card|score|activatedAt|annualCostScore|annualAccumulatedScore|activationSource|totalScore|totalCostScore|level|growth|paidMember|activationStore|activationStaff|activationChannel|subscribedChannel)"
  repeated ControlRule rules = 3;
}

message IntArrayValue {
  repeated uint64 value = 1;
}

message ArbitraryValue {
  oneof value {
    EventArrayValue valueStringArray = 1;
    uint64 valueInt = 2;
    string valueString = 3;
    IntArrayValue valueIntArray = 4;
    DoubleDimensionalArrayValue valueStringDoubleDimensionalArray = 5;
    bool valueBool = 6;
    double valueNumber = 7;
    NumberArrayValue valueNumberArray = 8;
  }
}

message ControlRule {
  string operator = 1; // valid:"in(IN|NOT_IN|EQUALS|NOT_EQUALS|GT|GTE|LT|LTE|IS_NULL|IS_NOT_NULL|BETWEEN|NOT_BETWEEN|CONTAINS_ANY|NOT_CONTAINS_ANY|CONTAINS|NOT_CONTAINS|RLIKE|NOT_RLIKE|DEFAULT)"
  string branch = 2;
  ArbitraryValue value = 3;
  string branchName = 4;
  repeated RuleMatcher matchers = 5;
}

message RuleMatcher {
  string operator = 1; // valid:"in(IN|NOT_IN|EQUALS|NOT_EQUALS|GT|GTE|LT|LTE|IS_NULL|IS_NOT_NULL|BETWEEN|NOT_BETWEEN|CONTAINS_ANY|NOT_CONTAINS_ANY|CONTAINS|NOT_CONTAINS|RLIKE|NOT_RLIKE|DEFAULT)"
  ArbitraryValue value = 3;
}

message NumberArrayValue {
  repeated double value = 1;
}

message Waiting {
  int64 timeOffset = 1;
  int64 specificAt = 2;
  string nextTime = 3; // valid:"matches([0-9]{2}:[0-9]{2})"
  string startTime = 4; // valid:"matches([0-9]{2}:[0-9]{2})"
  string endTime = 5; // valid:"matches([0-9]{2}:[0-9]{2})"
}

message UpdateMarketoStatusRequest {
  // @required
  //
  // 路径 id
  string id = 1; // valid:"required,objectId"
  // 目标状态
  string status = 2;
  // 是否删除
  bool isDeleted = 3;
  // 是否停止已进入营销流的数据
  //
  // 只有当 status 为 stopped 时此字段才可以设置为 true
  bool shouldStopWorkflows = 4;
}

message SearchMarketoRequest {
  repeated string status = 1;
  string name = 2;
  repeated string type = 3;
  string filterType = 4;
  repeated string tags = 5;
  mairpc.common.types.BoolValue repeatable = 6;
  MarketoEvent event = 7;
  MarketoRule rule = 8;
  mairpc.common.request.ListCondition listCondition = 9;
  // 是否在返回值中展示属于客户批量操作的那些自动化营销
  bool showBatchOperations = 10;
  // 活动信息
  Campaign campaign = 11;
  // 筛选所属组织节点
  mairpc.common.types.DistributorSelector distributor = 12; // valid:"optional"
  // 筛选创建者
  repeated string createdBy = 13; // valid:"objectIdList"
  // 有效期
  mairpc.common.types.StringDateRange enablePeriod = 14;
  // id 列表
  repeated string ids = 15;
}

message MarketoList {
  repeated MarketoDetailResponse marketoes = 1;
  uint64 totalCount = 2;
}

message JoinEventWorkflowRequest {
  string eventId = 1; // valid:"required"
  string memberId = 2; // valid:"required"
  repeated EventProperty eventProperties = 3;
  // 事件实际发生时间。毫秒级 例如 *************
  int64 occurredAt = 4;
}

message JoinWorkflowRequest {
  string marketoId = 1;
  repeated string memberIds = 2;
  bool isBatchOperation = 3;
  string identifier = 4;
}

message GetWorkflowMemberCountRequest {
  string marketoId = 1;
  string workflowId = 2;
  // 是否异步获取
  bool isAsync = 3;
}

message GetWorkflowMemberCountResponse {
  uint64 incomingCount = 1;
  uint64 ongoingCount = 2;
  uint64 completeCount = 3;
  int64 stoppedCount = 4;
  // 异步执行生产的唯一 code，前端用来轮询执行结果；用于 isAsync 为 true 时
  string code = 5;
}

message SearchWorkflowMemberRequest {
  string marketoId = 1;
  string workflowId = 2;
  // only support: "incoming", "ongoing", "complete", "stopped"
  string status = 3;
  string searchKey = 4;
  mairpc.common.request.ListCondition listCondition = 5;
  ExportInfo export = 6;
  // 是否需要计算总数
  bool notCountTotal = 7;
}

message MemberBrief {
  string id = 1;
  string phone = 2;
  string name = 3;
  string email = 4;
  string avatar = 5;
  string socialMember = 6;
  string gender = 7;
  repeated mairpc.member.PropertyDetail properties = 8;
}

message SearchWorkflowMemberResponse {
  repeated MemberBrief members = 1;
  uint64 total = 2;
  string lastWorkflowId = 3;
}

message GetMarketoMemberCountRequest {
  string marketoId = 1;
  bool isAsync = 2;
}

message GetMarketoMemberCountResponse {
  map<string, uint64> workflowsCount = 1;
  // 异步执行生产的唯一 code，前端用来轮询执行结果；用于 isAsync 为 true 时
  string code = 2;
}

message ActionTemplate {
  string channelId = 1;
  string id = 2;
  string content = 3;
  string title = 4;
  string url = 5;
  message Data {
    string key = 1;
    string value = 2;
    string color = 3;
  }
  repeated Data data = 6;
  // 小程序appId
  string appId = 7;
  // 小程序页面路径
  string pagePath = 8;
}

message PropertyValueMap {
  // 属性值 ID
  //
  // 当需要从客户属性中取值时，此字段直接保存 propertyId
  // 当需要从会员属性取值时，相关的字段名都会加上 “member.”，下面的注释中会省略 “member.”，实际的值应该是形如 “member.activatedAt”
  // 目前支持的会员属性有 activatedAt：注册时间，activationSource：注册来源，totalScore：累计积分，totalCostScore：累计消耗积分，annualAccumulatedScore：年度累计获得积分，
  // annualCostScore：年度累计使用积分，cardName：会员卡，levelName：会员等级，growth：成长值，score：积分，
  string propertyId = 1;
  string defaultValue = 2;
  map<string, string> optionValueMap = 3;
  // 展示规则
  DisplayRule displayRule = 4;
}

message DisplayRule {
  // normal：正常展示，head：展示头部指定长度，tail：展示尾部指定长度，headAndFiller：展示头部指定长度并使用指定字符填充其他内容，tailAndFiller：展示尾部指定长度并使用指定字符填充其他内容，headFillerTail：头部和尾部展示指定长度并使用指定字符填充其他内容
  string type = 1;
  // 指定头部展示长度
  uint64 head = 2;
  // 指定尾部展示长度
  uint64 tail = 3;
  // 指定填充字符
  string filler = 4;
  // 填充次数
  //
  // 则不填将剩余字符全部填充为 filler
  uint64 fillTimes = 5;
}

message CountWorkflowMemberByTimeRequest {
  // @required
  //
  // 统计的类型，值可以为"hour"、"day"
  string type = 1; // valid:"required"
}

message GetMarketoStatsByTimeRequest {
  // @required
  //
  // 统计的类型，值可以为"hour"、"day"
  string type = 1; // valid:"required"
  // @required
  //
  // 自动营销策略ID
  string marketoId = 2; // valid:"required,objectId"
  // @required
  //
  // 统计的时间范围
  mairpc.common.types.StringDateRange dateRange = 3; // valid:"required"
}

message GetMarketoStatsByTimeResponse {
  repeated MarketoStats stats = 1;
}

message MarketoStats {
  // 统计数据ID
  string id = 1;
  // 租户ID
  string accountId = 2;
  // 统计起始时间
  string startedAt = 3;
  // 统计终止时间
  string endedAt = 4;
  //　统计期间开始流程的客户数
  int64 joinedCount = 5;
  //　统计期间结束流程的客户数
  int64 endedCount = 6;
  // 自动营销策略ID
  string marketoId = 7;
}

message GetMarketoStatsRequest {
  // @required
  //
  // 自动营销策略ID
  string id = 1; // valid:"required,objectId"
  // 是否异步获取
  bool isAsync = 2;
}

message GetMarketoStatsResponse {
  // 累计进入流程客户
  //
  // 指从流程开启到目前为止，进入的全部客户总和，如同一名客户多次进入，则计算为多人。
  int64 totalMember = 1;
  // 流程总历时
  //
  // 指从流程创建开始到目前为止，开启的时间总和。
  // 毫秒时间戳
  int64 duration = 2;
  // 平均时长
  //
  // 指所有客户从进入流程到结束的平均时长，如同一名客户多次进入，则计算为多人。
  // 毫秒时间戳
  int64 averageProcessTime = 3;
  // 异步执行生产的唯一 code，前端用来轮询执行结果；用于 isAsync 为 true 时
  string code = 4;
}

message GetMarketoWorkflowMembersRequest {
  // @required
  //
  // 自动营销策略ID
  string marketoId = 1; // valid:"required,objectId"
  // @required
  //
  // 筛选的时间范围
  mairpc.common.types.StringDateRange dateRange = 2; // valid:"required"
  // @required
  //
  // 值可为 "startedAt" 或是 "finishedAt"。若为 "startedAt"，会根据dateRange字段值来筛选指定时间段中新进入
  // 流程的客户，若为 "finishedAt"，会根据dateRange字段值来筛选指定时间段中退出流程的客户。
  string filterType = 3; // valid:"required,in(startedAt|finishedAt)"
  // 分页
  mairpc.common.request.ListCondition listCondition = 4;
  // 若有值，则会用该字段来筛选导出数据
  ExportInfo export = 5;
}

message ExportInfo {
  // 若有值，则会返回排在该字段后的数据
  string lastMarketoWorkflowId = 1; // valid:"objectId,optional"
  // 导出时一次返回的数据量
  uint64 exportPage = 2;
}

message GetMarketoWorkflowMembersResponse {
  int64 total = 1;
  repeated MarketoWorkflowMember workflowMembers = 2;
}

message MarketoWorkflowMember {
  // 流程编号
  string id = 1;
  // 进入流程时间
  string startedAt = 2;
  // 结束流程时间
  string finishedAt = 3;
  // 总历时
  int64 duration = 4;
  // 客户信息
  WorkflowMember member = 5;
  // 节点执行结果
  string nodeStatus = 6;
  // 节点执行时间
  string nodeExecutedAt = 7;
  // 是否停止
  bool hasStopped = 8;
  // 错误信息
  string error = 9;
}

message WorkflowMember {
  // 客户编号
  string id = 1;
  // 客户OpenID
  string openId = 2;
  // 手机号
  string phone = 3;
  // 姓名
  string name = 4;
  // 性别
  string gender = 5;
  // 年龄
  int64 age = 6;
  // 地址
  Address address = 7;
  // 标签
  repeated string tags = 8;
  // 获取渠道
  string originChannel = 9;
  // 是否会员
  bool isActivated = 10;
  // 创建时间
  string createdAt = 11;
  // 昵称
  string nickname = 12;
  // 属性
  repeated mairpc.member.PropertyDetail properties = 13;
}

message Address {
  // 国家
  string country = 1;
  // 省
  string province = 2;
  // 市
  string city = 3;
  // 区
  string district = 4;
  // 详细地址
  string detail = 5;
}

message CountWorkflowLogResultsRequest {
  // @required
  //
  // 被统计的自动化营销
  repeated CountWorkflowLogMarketoInfo marketos = 1; // valid:"required"
  // 若为false，那么仅粗略的统计自动化营销节点执行成功与否，
  // 若为true，那么除了统计成功与否，还会统计执行失败节点的原因
  bool showDetail = 2;
}

message CountWorkflowLogMarketoInfo {
  // @required
  //
  // 自动化营销ID
  string id = 1; // valid:"required,objectId"
  // 自动化营销节点ID，若不提供则统计所有节点
  repeated string workflowIds = 2;
}

message CountWorkflowLogResultsResponse {
  // 节点执行结果统计
  repeated WorkflowResult results = 1;
  // 包含失败原因的失败节点
  repeated WorkflowFailedResult failedResults = 2;
}

message WorkflowResult {
  // 自动化营销ID
  string id = 1;
  // 自动化营销节点ID
  string workflowId = 2;
  // 节点执行结果
  string status = 3;
  // 计数
  int64 count = 4;
}

message WorkflowFailedResult {
  // 自动化营销ID
  string id = 1;
  // 自动化营销节点ID
  string workflowId = 2;
  // 执行失败的原因
  string errorType = 3;
  // 计数
  int64 count = 4;
}

message MarketoWorkflows {
  int64 total = 1;
  repeated MarketoWorkflow workflows = 2;
}

message MarketoWorkflow {
  string id = 1;
  string accountId = 2;
  string memberId = 3;
  string marketoId = 4;
  bool isMarketoRepeatable = 5;
  string workflowId = 6;
  string status = 7;
  string eventAt = 8;
  string eventId = 9;
  string waitingAt = 10;
  string processingAt = 11;
  bool isDeleted = 12;
  repeated MarketoWorkflowLog workflowLogs = 13;
  int64 weight = 14;
  string createdAt = 15;
  string startedAt = 16;
  string finishedAt = 17;
  int64 duration = 18;
  bool isBatchOperation = 19;
  string identifier = 20;
  Maievent event = 21;
}

message MarketoWorkflowLog {
  string workflowId = 1;
  string finishedAt = 2;
  string info = 3;
  string error = 4;
  string errorType = 5;
  bool matched = 6;
  string status = 7;
  Maievent event = 8;
}

message Maievent {
  // 事件ID
  string eventId = 1;
  // 事件发生时间。
  //
  // 毫秒级时间戳例如 *************
  int64 occurredAt = 2;
  // 事件属性
  //
  // JSON 格式的字符串，键是属性 ID，值是属性值
  string eventProperties = 3;
}

message IterateMarketoWorkflowsRequest {
  // @required
  //
  // 自动化营销ID
  string marketoId = 1; // valid:"required,objectId"
  // 上一次最后一个工作流ID
  string lastWorkflowId = 2; // valid:"optional,objectId"
  // 单次返回的工作流数量
  int64 limit = 3;
  // 工作流节点的错误类型
  string errorType = 4;
}

message GetMarketoActionStatsRequest {
  // @required
  //
  // 自动化营销ID
  string marketoId = 1; // valid:"required,objectId"
  // @required
  //
  // 自动化营销动作节点ID
  string actionId = 2; // valid:"required"
  // 是否异步获取
  bool isAsync = 3;
}

message GetMarketoActionStatsResponse {
  int64 waitingCount = 1;
  int64 succeedCount = 2;
  int64 failedCount = 3;
  // 异步执行生产的唯一 code，前端用来轮询执行结果；用于 isAsync 为 true 时
  string code = 4;
}

message GetMarketoWorkflowNodeMembersRequest {
  // @required
  //
  // 自动营销策略ID
  string marketoId = 1; // valid:"required,objectId"
  // @required
  //
  // 自动营销策略节点ID
  string workflowId = 2; // valid:"required"
  // 若为true，则只返回成功节点。若为false，则只返回失败节点。若不填，则都返回。
  mairpc.common.types.BoolValue succeed = 3;
  // 分页
  mairpc.common.request.ListCondition listCondition = 4;
  // 若有值，则会用该字段来筛选导出数据
  ExportInfo export = 5;
  // 筛选关键字，支持客户姓名和手机号搜索
  string searchKey = 6;
  // 是否异步获取
  bool isAsync = 7;
  // 进入节点时间
  mairpc.common.types.StringDateRange nodeStartedAt = 8;
  // 是否需要计算节点客户数量
  bool notCountMembers = 9;
}

message GetMarketoWorkflowNodeMembersResponse {
  int64 total = 1;
  repeated MarketoWorkflowMember workflowMembers = 2;
  // 异步执行生产的唯一 code，前端用来轮询执行结果；用于 isAsync 为 true 时
  string code = 3;
}

message CreateGroupMembersFromMarketoWorkflowRequest {
  // @required
  //
  // 自动营销策略 ID
  string marketoId = 1; // valid:"required,objectId"
  // 自动营销策略节点 ID
  string workflowId = 2;
  // 路径执行状态
  string status = 3; // valid:"in(incoming|ongoing|complete|stopped)"
  // 路径执行结果
  //
  // 若为 true，则只导入执行成功数据。若为 false，则只导入执行失败数据。若不传，则都导入。
  mairpc.common.types.BoolValue succeed = 4;
  // 筛选关键字，支持客户姓名和手机号搜索
  string searchKey = 5;
  // 进入节点时间
  mairpc.common.types.StringDateRange nodeStartedAt = 6;
  // 静态群组 ids，外部调用时不得超过 10 个
  repeated string groupIds = 7; // valid:"objectIdList"
  // groupIds 为空时，使用此字段创建一个静态群组
  string groupName = 8;
  // 群组描述
  string groupDescription = 9;
  // 组织节点信息
  mairpc.common.types.DistributorInfo distributorInfo = 10;
}

message CreateGroupMembersFromMarketoWorkflowResponse {
  // 成功的数目
  map<string, uint64> succeedCount = 1;
  // 失败的数目
  map<string, uint64> failedCount = 2;
}

message UpdateMarketoEstimatedMemberCountRequest {
  // 营销策略ID
  string id = 1; // valid:"required,objectId"
  // 预估人数
  uint64 count = 2; // valid:"required"
}

message GetTestStatsRequest {
  // @required
  //
  // 营销活动 Id
  string marketoId = 1; // valid:"required,objectId"
}

message GetTestStatsResponse {
  repeated TestStats items = 1;
}

message TestStats {
  // 测试节点 id
  string workflowId = 1;
  // 测试描述
  string description = 2;
  // 测试状态
  //
  // pending:未开始，testing：测试中，end：已结束，stopped：已停止
  string status = 3;
  // 测试总次数
  int64 limitTimes = 4;
  // 已测次数
  int64 testTimes = 5;
  // 测试分支信息
  repeated TestBranchStats branches = 6;
  // AB 测试相关的节点详情，包括从进入 AB 测试节点到测试目标节点间的所有节点
  repeated Workflow workflows = 7;
}

message TestBranchStats {
  // 分支名称
  string branchName = 1;
  // 分支编号
  string branch = 2;
  // 分支节点 id
  //
  // 该分支的第一个节点的 id
  string workflowId = 3;
  // 流量配比
  uint64 testProportion = 4;
  // 已测次数
  int64 testTimes = 5;
  // 达标数
  int64 completeCount = 6;
  // 达标率
  int64 completeProportion = 7;
  // 是否是最高效的分支
  bool isEfficientBranch = 8;
}

message StatusCountMap {
  string status = 1;
  int64 count = 2;
}

message GetMarketoOverviewRequest {
  // 是否统计属于客户批量操作的那些自动化营销
  bool showBatchOperations = 1;
}

message MarketoOverview {
  string type = 1;
  repeated StatusCountMap statusCountMaps = 2;
}

message GetDataOverviewResponse {
  repeated MarketoOverview marketoOverviews = 1;
}

message BatchUpsertMarketingNotificationLimitSettingsRequest {
  // @required
  //
  // 防骚扰设置
  repeated MarketingNotificationLimitSettingDetail settings = 1; // valid:"required"
}

message ListMarketingNotificationLimitSettingsResponse {
  repeated MarketingNotificationLimitSettingDetail settings = 1;
}

message MarketingNotificationLimitSettingDetail {
  string id = 1;
  string accountId = 2;
  // 通知渠道类型，邮件（email）、短信（sms）、数字短信（dsms）、企业微信消息（wechatwork_message）、微信模板消息（template）、WhatsApp消息（whatsapp）、Line消息（app:line）
  string type = 3;
  int64 createdAt = 4;
  int64 updatedAt = 5;
  repeated mairpc.common.types.LimitUnit limit = 6;
  bool isEnabled = 7;
}

message AuditorDetail {
  string id = 1;
  // 审核人姓名
  string name = 2;
  // 审核人邮箱
  string email = 3;
  // 添加时间
  string createdAt = 4;
  // 审核人后台账号 id
  string userId = 5;
  // 后台账号是否被删除
  bool isUserDeleted = 6;
}

message ListAuditorsRequest {
  mairpc.common.request.ListCondition listCondition = 1;
  // 是否查询全部
  bool unlimited = 2;
  // 是否只查询后台账号未删除的
  bool filterDeleted = 3;
}

message ListAuditorsResponse {
  repeated AuditorDetail items = 1;
  int64 total = 2;
}

message AuditMarketoRequest {
  // @required
  //
  // 智能路径 id
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 执行的操作，agree（同意），reject（拒绝）
  string operate = 2; // valid:"required,in(agree|reject)"
  // 拒绝理由，operate 为 reject 时必传
  string rejectReason = 3;
}

message UpsertMarketoAuditSettingRequest {
  // @required
  //
  // 路径审核状态：enabled（开启），disabled（关闭）
  string status = 1; // valid:"required,in(enabled|disabled)"
  // 是否不开启校验，如开启，则在没有添加审核人情况下开启设置将报错
  bool notCheckEnabled = 2;
}

message AuditSettingDetail {
  string status = 1;
  string updatedAt = 2;
}

message ListAuditMarketosRequest {
  mairpc.common.request.ListCondition listCondition = 1;
  // 审核状态，auditWaiting（待审核），audited（已审核）默认为待审核
  string status = 2; // valid:"in(auditWaiting|audited)"
}

message ListAuditMarketosResponse {
  int64 total = 1;
  repeated AuditMarketoDetail items = 2;
}

message AuditMarketoDetail {
  // 路径 id
  string id = 1;
  // 路径名称
  string name = 2;
  // 路径有效期
  mairpc.common.types.StringDateRange enablePeriod = 3;
  // 执行审核审核人姓名
  string auditorName = 4;
  // 审核时间
  string auditedAt = 5;
  // 状态：waiting（等待中）、ongoing（进行中）、stopped（已停止）、ended（已结束）、rejected（已驳回）、auditWaiting（待审核）
  string status = 6;
  // 驳回原因
  string rejectReason = 7;
  // 申请人名称
  string applicant = 8;
  // 路径类型
  string type = 9;
  // 申请时间
  string appliedAt = 10;
  // 申请审核时选中的审核人后台账号 id 列表
  repeated string selectedAuditorUserIds = 11;
  // 额外信息
  string extra = 12;
  // 是否需要所有审核人确认
  bool needAllAuditorsConfirm = 13;
  // 审核结果
  repeated AuditResult results = 14;
  // 上次通知时间
  string lastNotifiedAt = 15;
}

message AuditResult {
  string auditorId = 1;
  string auditorUserId = 2;
  string auditorName = 3;
  string auditedAt = 4;
  string rejectReason = 5;
}

message ApplyAuditRequest {
  // @required
  //
  // 智能路径 id
  string marketoId = 1; // valid:"required,objectId"
  // 审核人 id
  repeated string auditorIds = 2; // valid:"objectIdList"
  // 是否选中所有审核人
  bool chooseAll = 3;
  // 自定义字段
  string extra = 4;
  // 审核人来源，默认为 messagepush
  string auditorOrigin = 5; // valid:"in(messagepush|others)"
  // 是否需要所有审核人确认
  bool needAllAuditorsConfirm = 6;
}

message ProcessMarketingPlanWorkflowRequest {
  // @required
  //
  // 营销计划 id
  string marketingPlanId = 1; // valid:"required,objectId"
}

message RecordMemberNotificationRequest {
  string memberId = 1; // valid:"objectId"
  string phone = 2;
  string identifier = 3;
  string type = 4; // valid:"required"
}

message MarketingPlanActionResultFilter {
  // 动作 id
  string id = 1; // valid:"objectId"
  // 动作状态，SENT（成功）、FAILED（失败）、CONFIRMING（等待结果中）
  repeated string status = 2; // valid:"in(SENT|FAILED|CONFIRMING)"
  // 失败原因
  string failedReason = 3;
}

message MarketingPlanTargetResultFilter {
  // 目标 id
  string id = 1;
  // 目标完成结果
  repeated string result = 2; // valid:"in(completed|notCompleted)"
}

message ListMarketingPlanWorkflowsRequest {
  mairpc.common.request.ListCondition listCondition = 1;
  // @required
  //
  // 营销计划 id
  string marketingPlanId = 2; // valid:"required,objectId"
  // 进入时间
  mairpc.common.types.StringDateRange triggerAt = 3;
  // 分组 id
  string actionGroupId = 4; // valid:"objectId"
  // 目标达成筛选
  repeated MarketingPlanTargetResultFilter targetFilters = 5;
  // 营销动作结果筛选
  repeated MarketingPlanActionResultFilter actionFilters = 6;
}

message MarketingPlanActionResult {
  // 动作 id
  string id = 1;
  // 动作状态，SENT（成功）、FAILED（失败）、CONFIRMING（等待结果中）
  string status = 2;
  // 失败原因
  string failedReason = 3;
}

message MarketingPlanTargetResult {
  // 目标 id
  string id = 1;
  // 目标完成结果
  bool isCompleted = 2;
}

message MarketingPlanWorkflow {
  string id = 1;
  // 进入时间
  string triggerAt = 2;
  // 开始执行时间
  string runningAt = 3;
  // 执行结束时间
  string completedAt = 4;
  string memberId = 5;
  string memberName = 6;
  // 目标达成情况
  repeated MarketingPlanTargetResult targetResults = 7;
  // 营销动作结果
  repeated MarketingPlanActionResult actionResults = 8;
  // 营销分组 id
  string actionGroupId = 9;
  // 失败时间
  string failedAt = 10;
  // 执行状态
  string status = 11;
}

message ListMarketingPlanWorkflowsResponse {
  repeated MarketingPlanWorkflow items = 1;
  int64 total = 2;
}

message ExportMarketingPlanWorkflowsRequest {
  // @required
  //
  // 查询条件
  ListMarketingPlanWorkflowsRequest searchCondition = 1; // valid:"required"
  // 导出分组名称
  string groupName = 2;
  // 导出分组描述
  string groupDescription = 3;
  // 导出分组 id，不设置则新建
  string groupId = 4;
}
