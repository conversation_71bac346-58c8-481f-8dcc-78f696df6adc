syntax = "proto3";

package mairpc.member;

option go_package = "member";

import "common/benchmark/benchmark.proto";
import "common/request/request.proto";
import "common/response/response.proto";
import "common/types/types.proto";
import "member/batch_operation.proto";
import "member/follower.proto";
import "member/member.proto";
import "member/member_address.proto";
import "member/member_event.proto";
import "member/member_group.proto";
import "member/member_growth.proto";
import "member/member_info_grades_history.proto";
import "member/member_info_log.proto";
import "member/member_level.proto";
import "member/member_paid_card.proto";
import "member/member_privilege.proto";
import "member/member_property.proto";
import "member/member_protection_period_score.proto";
import "member/member_score_setting.proto";
import "member/member_score_sync_status.proto";
import "member/member_source.proto";
import "member/member_stage.proto";
import "member/member_task.proto";
import "member/member_task_log.proto";
import "member/member_task_record.proto";
import "member/member_type.proto";
import "member/member_white_list.proto";
import "member/membership_card.proto";
import "member/order.proto";
import "member/score.proto";
import "member/score_consume_record.proto";
import "member/score_rule.proto";
import "member/social_members.proto";
import "member/static_group_user.proto";
import "member/stock_member.proto";
import "member/tags.proto";

service MemberService {
  rpc Benchmark(mairpc.common.benchmark.BenchmarkRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建客户
  //
  // 主要用于第三方系统对接的场景，如果是微信公众号、小程序用户的对接，建议走服务商托管的方式。
  // 客户属性需要支持多种类型，如文本、数字、日期等。结构比较复杂，使用前最好到运营后台了解一下。
  // 具体使用方式，可以参照“更新客户”接口中的描述。
  //
  // TIP: 创建客户的逻辑，比较复杂，调用前需要联系对接人员核实提交数据是否正确。
  //
  // - 小程序授权后创建客户示例
  //     ```json
  //     {
  //        "originFrom" : {
  //            "channel" : "小程序在群脉的唯一标识",
  //            "channelName" : "小程序名称",
  //            "openId" : "xxxx",
  //            "origin" : "weapp",
  //            "unionId" : "xxxx",
  //            "authorized" : true,
  //            "firstAuthorizeTime" : 1554183858,
  //            "authorizeTime" : 1554183858,
  //            "nickname" : "小程序用户昵称",
  //            "gender" : "小程序授权用户的性别",
  //            "city" : "小程序授权用户的城市",
  //            "province" : "小程序授权用户的省",
  //            "country" : "小程序授权用户的国家",
  //            "avatar" : "小程序授权用户的头像"
  //        },
  //        "properties" : [ {
  //            "propertyId" : "name",
  //            "valueString" : {
  //                "value" : "小程序用户昵称"
  //            }
  //        },{
  //            "propertyId" : "img",
  //            "valueString" : {
  //                "value" : "小程序用户头像"
  //           }
  //        },{
  //            "propertyId" : "gender",
  //            "valueString" : {
  //                "value" : "male"
  //            }
  //        },{
  //            "propertyId" : "address",
  //            "valueArray" : {
  //                "value" : ["中国", "上海", "浦东新区", "", ""]
  //            }
  //        }],
  //        "isInactivated" : true,
  //        "nickname" : "小程序用户昵称"
  //     }
  //     ```
  //
  rpc CreateMember(CreateMemberRequest) returns (MemberDetailResponse);
  // 获取客户详情
  //
  // 根据客户唯一标识获取详情
  rpc GetMember(MemberDetailRequest) returns (MemberDetailResponse);
  // 获取客户简要信息
  //
  // 目前只返回 phone
  rpc GetSimpleMember(SimpleMemberDetailRequest) returns (SimpleMemberDetailResponse);
  // 批量获取客户详情
  //
  // 用于批量获取客户数据，ids 仅支持群脉系统客户ID。该接口不支持同时使用 ids、unionIds 获取客户
  rpc GetMembers(MemberDetailListRequest) returns (MemberDetailListResponse);
  // 根据标签 count 客户数
  rpc CountByTag(TagRequest) returns (MemberCountResponse);
  // 搜索客户【关键业务请勿使用】
  //
  // 注意：搜索客户采用 Elasticsearch，有一个从 MongoDB 同步数据的过程，不可避免的偶发延迟，关键业务请勿使用。如有疑问，请咨询 #support-module。
  rpc SearchMember(SearchMemberRequest) returns (MemberListResponse);
  // 搜索客户，只返回 memberIds
  rpc SearchMemberIds(SearchMemberRequest) returns (SearchMemberIdsResponse);
  // 批量增、减客户积分
  //
  // 仅支持对多个客户增、减相同的积分值。
  rpc UpdateScore(UpdateScoreRequest) returns (UpdateScoreResponse);
  // 根据激励规则触发奖励
  //
  // 可以到运营后台新增激励，该接口支持根据激励代码触发奖励
  rpc RewardByScoreRule(RewardByScoreRuleRequest) returns (RewardByScoreRuleResponse);
  // 激活会员
  rpc ActivateMember(ActivateMemberRequest) returns (ActivateMemberResponse);
  // 有手机号的会员上行到京东营销云
  rpc RegisterMemberWithPhoneToJd(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 增加标签
  //
  // 给多个客户增加标签，不会覆盖或者重复添加已有标签
  rpc AddTags(MemberTags) returns (MemberTagResponse);
  // 增加标签
  //
  // 给单个客户增加标签，不会覆盖或者重复添加已有标签
  rpc AddMemberTags(AddTagsRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除客户标签
  //
  // 批量删除客户身上的标签
  rpc DeleteTags(MemberTags) returns (MemberTagResponse);
  // 更新客户标签
  //
  // 会直接替换掉客户已有标签
  rpc SetTags(MemberTags) returns (MemberTagResponse);
  // 将标签组移除规则组
  rpc DeleteTagGroupStrategy(DeleteTagGroupStrategyRequest) returns (mairpc.common.response.EmptyResponse);
  // 将所有标签组移除规则组
  rpc RemoveTagGroupsFromStrategy(RemoveTagGroupsFromStrategyRequest) returns (mairpc.common.response.EmptyResponse);
  // 添加标签组至规则组
  rpc UpdateTagGroupStrategy(UpdateTagGroupStrategyRequest) returns (mairpc.common.response.EmptyResponse);
  // 新增企微标签组
  rpc AddWechatworkTagGroups(AddWechatworkTagGroupsRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取订单统计
  rpc GetOrderStats(member.OrderStatsRequeset) returns (member.OrderStatsResponse);
  // 获取客户平台信息
  rpc GetMembersSocials(MembersSocialRequest) returns (MembersSocial);
  // 更新客户
  //
  // 对于客户属性的更新，由于需要支持多种类型，如文本、数字、日期等。结构比较复杂，建议使用前到运营后台了解一下。
  //
  // 对于接口的调用，会将属性类型映射到 valueArray、valueString、valueDate、valueBool、valueNumber、valueNumberArray。
  // 接口中用到的 propertyId 对应运营后台，客户属性的属性 ID。
  //
  // - 地址类型： valueArray，按顺序依次是 country、province、city、district、detail
  //     ```json
  //     {
  //          "propertyId" : "address",
  //          "valueArray" : {
  //              "value" : ["中国", "上海", "浦东新区", "", "郭守敬路498号"]
  //          }
  //     }
  //     ```
  // - 地理定位：valueNumberArray
  //     ```json
  //     {
  //          "propertyId" : "propertyId",
  //          "valueNumberArray" : {
  //              "value" : [121.53, 31.22]
  //          }
  //     }
  //     ```
  // - 日期：valueDate，Unix 时间戳
  //     ```json
  //     {
  //          "propertyId" : "birthday",
  //          "valueDate" : {
  //              "value" : 1550217486
  //          }
  //     }
  //     ```
  // - 文本-多选： valueArray
  //     ```json
  //     {
  //          "propertyId" : "hobby",
  //          "valueArray" : {
  //              "value" : ["玩游戏", "编程"]
  //          }
  //     }
  //     ```
  // - 数字、货币：valueNumber，浮点数
  //     ```json
  //     {
  //          "propertyId" : "height",
  //          "valueNumber" : {
  //              "value" : 3.14
  //          }
  //     }
  //     ```
  // - 图片-多张图片： valueArray
  //     ```json
  //     {
  //          "propertyId" : "images",
  //          "valueArray" : {
  //              "value" : ["https://statics.maiscrm.com/images/site/logo.png"]
  //          }
  //     }
  //     ```
  // - 图片-单张图片： valueString
  //     ```json
  //     {
  //          "propertyId" : "avatar",
  //          "valueString" : {
  //              "value" : "https://statics.maiscrm.com/images/site/logo.png"
  //          }
  //     }
  //     ```
  // - 布尔型： valueBool，可选 ture、false
  //     ```json
  //     {
  //          "propertyId" : "married",
  //          "valueBool" : {
  //              "value" : true
  //          }
  //     }
  //     ```
  // - 其他类型： valueString
  //     ```json
  //     {
  //          "propertyId" : "name",
  //          "valueString" : {
  //              "value" : "花儿一样开放"
  //          }
  //     }
  //     ```
  //
  // TIP: 对于文本-单选、多选类型的可选值，可以到运营后台，将光标放置到客户属性的属性类型上，会有提示。为了兼容老数据，对性别做了特殊处理，可选值为 male、female、unknown。
  rpc UpdateMember(UpdateMemberRequest) returns (MemberDetailResponse);
  // 更新客户状态
  //
  // 更新客户状态到 NORMAL：正常 SUSPICIOUS：可疑 BLOCKED：拉黑
  rpc UpdateBlockedStatus(UpdateBlockedStatusRequest) returns (UpdateBlockedStatusResponse);
  // 创建客户事件记录
  rpc CreateMemberLogs(CreateMemberLogsRequest) returns (CreateMemberLogsResponse);
  // 根据平台信息获取客户
  rpc GetBySocial(GetBySocialRequest) returns (MemberDetailResponse);
  // 根据微信会员卡信息获取客户
  rpc GetByWxCard(GetByWxCardRequest) returns (MemberDetailResponse);
  // 获取标签统计
  rpc GetTagStats(TagStatsRequest) returns (TagStatsResponse);
  // 批量发放会员卡
  //
  // 给客户发放指定会员卡，可设置卡的激活状态，并支持为单个客户发放会员卡时指定卡号。
  rpc ProvideCard(ProvideCardRequest) returns (ProvideCardResponse);
  // 根据标签获取客户
  rpc GetByTags(GetByTagsRequest) returns (MemberListResponse);
  // 根据粉丝信息创建或更新客户
  rpc UpsertByFollower(FollowerDetail) returns (UpsertByFollowerResponse);
  // 根据渠道信息创建或更新客户
  rpc UpsertByChannel(UserFromChannel) returns (MemberDetailResponse);
  // 合并客户
  rpc MergeMember(MergeMemberRequest) returns (MemberDetailResponse);
  // 获取会员卡
  rpc GetMembershipCard(MembershipCardDetailRequest) returns (MembershipCardDetail);
  // 创建会员卡
  rpc CreateMembershipCard(MembershipCardDetail) returns (MembershipCardDetail);
  // 更新会员卡
  rpc UpdateMembershipCard(MembershipCardDetail) returns (MembershipCardDetail);
  // 删除会员卡
  rpc DeleteMembershipCard(mairpc.common.request.DetailRequest) returns (mairpc.common.response.BoolResponse);
  // 获取会员卡列表
  rpc GetMembershipCardList(MembershipCardListRequest) returns (MembershipCardListResponse);
  // 获取会员激励详情
  rpc GetScoreRule(ScoreRuleDetailRequest) returns (ScoreRuleDetailResponse);
  // 获取会员激励列表
  rpc GetScoreRules(ScoreRuleListRequest) returns (ScoreRuleListResponse);
  // 创建会员激励
  rpc CreateScoreRule(ScoreRuleMeta) returns (ScoreRuleDetailResponse);
  // 更新会员激励
  rpc UpdateScoreRule(ScoreRuleMeta) returns (ScoreRuleDetailResponse);
  // 删除会员激励
  rpc DeleteScoreRule(mairpc.common.request.DetailRequest) returns (mairpc.common.response.BoolResponse);
  // 根据会员激励获取积分历史
  rpc GetScoreHistoryByRule(ScoreHistoryByRuleRequest) returns (ScoreHistory);
  // 获取客户积分历史
  rpc GetScoreHistoryList(ScoreHistoryListRequest) returns (ScoreHisoryList);
  // 获取客户属性列表
  //
  // 客户属性的定义，更新某个客户的属性所需的基础数据。
  rpc GetMemberPropertyList(MemberPropertyListRequest) returns (MemberPropertyList);
  // 获取客户属性
  rpc GetMemberProperty(mairpc.common.request.DetailRequest) returns (MemberProperty);
  // 更新客户属性
  rpc UpdateMemberProperty(UpdateMemberPropertyRequest) returns (MemberProperty);
  // 绑定渠道
  rpc BindChannel(BindChannelRequest) returns (MemberDetailResponse);
  // 创建客户属性
  rpc CreateMemberProperty(CreateMemberPropertyRequest) returns (MemberProperty);
  // 删除客户属性
  rpc DeleteMemberProperty(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 添加标签到分组
  rpc AddTagsToGroup(AddTagsToGroupRequest) returns (mairpc.common.response.BoolResponse);
  // 从分组中删除标签
  rpc DeleteTagsFromGroup(DeleteTagsFromGroupRequest) returns (mairpc.common.response.BoolResponse);
  // 移动标签到分组
  rpc MoveTagsToGroup(MoveTagsToGroupRequest) returns (mairpc.common.response.BoolResponse);
  // 移动聚合版标签到分组（同时支持静态标签、模型标签、规则标签）
  rpc BatchMoveAggregatedTags(BatchMoveAggregatedTagsRequest) returns (mairpc.common.response.BoolResponse);
  // 根据名称获取标签
  rpc GetTagByName(TagRequest) returns (Tag);
  // 根据id获取标签
  rpc GetTagById(mairpc.common.request.DetailRequest) returns (Tag);
  // 获取标签列表
  rpc GetTagList(TagListRequest) returns (TagList);
  // 获取聚合后标签列表
  rpc ListAggregatedTags(ListAggregatedTagsRequest) returns (ListAggregatedTagsResponse);
  // 获取聚合后标签详情
  rpc GetAggregatedTag(GetAggregatedTagRequest) returns (AggregatedTag);
  // 更新标签统计数据
  rpc UpdateTagStatistic(mairpc.common.request.StringIdRequest) returns (AggregatedTag);
  // 导出静态标签分布信息
  rpc ExportTagDistribution(mairpc.common.request.StringIdRequest) returns (mairpc.common.response.JobResponse);
  // 导出标签分组分布信息
  rpc ExportTagGroupDistribution(ExportTagGroupDistributionRequest) returns (mairpc.common.response.JobResponse);
  // 检查标签名称是否可用
  rpc CheckTagName(CheckTagNameRequest) returns (mairpc.common.response.BoolResponse);
  // 创建标签分组
  rpc CreateTagGroup(CreateTagGroupRequest) returns (TagGroup);
  // 获取标签分组列表
  rpc GetTagGroupList(GetTagGroupListRequest) returns (TagGroupList);
  // 获取聚合后标签分组列表
  rpc ListAggregatedTagGroups(ListAggregatedTagGroupsRequest) returns (ListAggregatedTagGroupsResponse);
  // 删除标签分组V2
  rpc DeleteAggregatedTagGroup(mairpc.common.request.DetailRequest) returns (mairpc.common.response.BoolResponse);
  // 更新标签分组
  rpc UpdateTagGroup(UpdateTagGroupRequest) returns (TagGroup);
  // 删除标签分组
  rpc DeleteTagGroup(mairpc.common.request.DetailRequest) returns (mairpc.common.response.BoolResponse);
  // 获取标签分组
  rpc GetTagGroup(GetTagGroupRequest) returns (TagGroup);
  // 按创建时间倒序为企业微信标签组排序
  rpc MigrateWechatworkTagGroupOrder(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取排序后的客户标签
  rpc GetMemberTagsByOrder(GetMemberTagsByOrderRequest) returns (GetMemberTagsByOrderResponse);
  // 创建或更新可疑规则和黑名单规则
  rpc UpsertSuspiciousRule(UpsertSuspiciousRuleRequest) returns (mairpc.common.response.BoolResponse);
  // 禁用可疑规则和黑名单规则
  rpc DisableSuspiciousRule(mairpc.common.request.DetailRequest) returns (mairpc.common.response.BoolResponse);
  // 获取可疑规则和黑名单规则
  rpc GetSuspiciousRule(mairpc.common.request.EmptyRequest) returns (SuspiciousRule);
  // 创建或更行积分重置规则
  rpc UpsertScoreResetRule(UpsertScoreResetRuleRequest) returns (ScoreResetRule);
  // 获取积分重置规则
  rpc GetScoreResetRule(mairpc.common.request.EmptyRequest) returns (ScoreResetRule);
  // 绑定手机号
  rpc BindPhone(BindPhoneRequest) returns (MemberDetailResponse);
  // 删除手机号
  rpc DeletePhone(mairpc.common.request.MemberIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取客户信息变更记录列表
  rpc ListMemberInfoLog(ListMemberInfoLogRequest) returns (ListMemberInfoLogResponse);
  // 获取客户信息变更记录
  rpc GetMemberInfoLog(GetMemberInfoLogRequest) returns (MemberInfoLog);
  // 获取会员激励历史记录总览
  rpc GetScoreHistoryOverview(ScoreHistoryOverviewRequest) returns (ScoreHistoryOverviewResponse);
  // 生成客户属性 Id
  rpc GenerateMemberPropertyId(mairpc.common.request.EmptyRequest) returns (mairpc.common.types.StringValue);
  // 更新客户价值
  rpc UpdateMemberValue(UpdateMemberValueRequest) returns (MemberDetailResponse);
  // 获取客户阶段
  rpc GetMemberStages(mairpc.common.request.EmptyRequest) returns (MemberStageListResponse);
  // 更新客户阶段
  rpc UpdateMemberStage(UpdateMemberStageRequest) returns (MemberStageDetail);
  // 更新客户信息评分规则
  rpc UpdateInformationRule(UpdateInformationRuleRequest) returns (MemberProperty);
  // 根据客户属性获取客户
  //
  // 默认返回第一条符合条件的数据
  //
  // TIP: 建议不允许重复的客户属性使用此接口。
  rpc GetByProperty(GetByPropertyRequest) returns (MemberDetailResponse);
  // 批量激活客户
  //
  // 客户激活后，会拥有一张默认的会员卡，并处于激活状态，客户转化为会员。
  // 如果客户本身已经拥有会员卡，则不会更换会员卡，仅更新会员卡激活状态。
  rpc ActivateMembers(mairpc.common.request.IdListRequest) returns (ActivateMembersResponse);
  // 改变会员卡禁用状态
  rpc SwitchDisabledStatus(SwitchDisabledStatusRequest) returns (MemberDetailResponse);
  // 发送客户事件
  rpc SendCustomerEvent(SendCustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 注销会员
  rpc LogoffMember(LogoffRequest) returns (MemberDetailResponse);
  // 创建收货地址
  rpc CreateMemberAddress(MemberAddressRequest) returns (MemberAddressDetail);
  // 编辑收货地址
  rpc UpdateMemberAddress(MemberAddressRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除收货地址
  rpc DeleteMemberAddress(mairpc.common.request.DetailWithMemberIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除所有收货地址
  rpc DeleteMemberAddresses(mairpc.common.request.MemberIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取收货地址列表
  rpc GetMemberAddresses(MemberRequest) returns (GetMemberAddressesResponse);
  // 获取收货地址详情
  rpc GetMemberAddress(mairpc.common.request.DetailWithMemberIdRequest) returns (MemberAddressDetail);
  // 设为默认地址
  rpc SetDefaultMemberAddress(mairpc.common.request.DetailWithMemberIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 解密小程序手机号并绑定
  rpc DecryptAndBindMiniProgramPhone(DecryptAndBindMiniProgramPhoneRequest) returns (MemberDetailResponse);
  // 绑定手机号
  rpc BindPhoneWithCaptcha(BindPhoneWithCaptchaRequest) returns (MemberDetailResponse);
  // 使用快速验证组件绑定手机号
  rpc BindPhoneWithQuickVerificationComponent(BindPhoneWithQuickVerificationComponentRequest) returns (MemberDetailResponse);
  rpc IterateThroughMembers(IterateThroughMembersRequest) returns (MemberDetailListResponse);
  // 上报客户事件
  //
  // 支持上报匿名客户事件。需设置唯一 clientId，以便后续关联到指定客户。
  rpc UploadMemberEventLogs(UploadMemberEventLogsRequest) returns (UploadMemberEventLogsResponse);
  // 更新客户渠道数据
  //
  // 支持部分字段更新，不需要更新的字段可不传，channelId 必填，目前只支持更新微信公众号及小程序的渠道。
  rpc UpdateSocial(UpdateSocialRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除会员
  rpc DeleteMember(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除客户基础信息
  rpc DeleteMemberSocialProfile(DeleteMemberSocialProfileRequest) returns (mairpc.common.response.EmptyResponse);
  // 恢复会员，将isDeleted设为false
  rpc RecoverMember(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 绑定匿名用户到客户
  //
  // 系统支持在客户登录之前（匿名用户）收集事件，集成方需要为匿名用户生成一个 clientId，该接口支持绑定此 clientId 下的事件到一个客户身上。
  rpc BindAnonymousToMember(BindAnonymousToMemberRequest) returns (mairpc.common.response.EmptyResponse);
  // 将每一个唯一ID转化成客户ID
  //
  // 唯一ID可以是手机号，openID，客户ID。
  // 除了将唯一ID转化成客户ID之外，该API还会筛选出属于当前租户的那些唯一ID
  rpc ConvertUniqueIdToMemberId(ConvertUniqueIdToMemberIdRequest) returns (ConvertUniqueIdToMemberIdResponse);
  // 创建客户积分同步记录
  rpc RecordMemberScoreSync(RecordMemberScoreSyncRequest) returns (mairpc.common.response.EmptyResponse);
  // 从有赞平台创建客户
  rpc UpsertFromYouzan(YouzanUser) returns (MemberDetailResponse);
  // 按照客户积分同步记录来同步各渠道的积分
  rpc SyncMemberScore(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 统计拥有所给标签的会员数
  rpc CountMembersForTag(CountMembersForTagRequest) returns (CountMembersForTagResponse);
  // 消费淘宝积分变动消息
  rpc ConsumeTaobaoPointMessages(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取客户所有平台的积分
  rpc GetScoreOfAllPlatforms(GetScoreOfAllPlatformsRequest) returns (GetScoreOfAllPlatformsResponse);
  // 获取所有来源、注册来源
  rpc GetAllMemberSources(GetAllMemberSourcesRequest) returns (GetAllMemberSourcesResponse);
  // 创建或更新客户
  //
  // 根据 uniqueId (优先级依次为 phone, email, openId, unionId) 查找对应客户进行创建、更新或合并操作
  rpc UpsertMember(UpsertMemberRequest) returns (MemberDetailResponse);
  // 批量更新客户属性
  //
  // 支持为多个客户更新多个属性，不存在的属性会被追加
  rpc BatchUpdateMemberProperty(BatchUpdateMemberPropertyRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量更新客户渠道信息
  rpc BatchUpdateMemberSocials(BatchUpdateMemberSocialsRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取静态群组客户列表
  rpc SearchStaticGroupUser(SearchStaticGroupUserRequest) returns (StaticGroupUserListResponse);
  // 获取客户的行为记录
  rpc SearchMemberEventLogByCursor(SearchMemberEventLogByCursorRequest) returns (SearchMemberEventLogByCursorResponse);
  // 获取一段时间内更新过的客户
  rpc SearchUpdatedMemberByCursor(SearchUpdatedMemberByCursorRequest) returns (SearchUpdatedMemberByCursorResponse);
  // 收回消费奖励积分激励所给的奖励
  rpc RollbackConsumptionScoreRuleReward(RollbackConsumptionScoreRuleRewardRequest) returns (mairpc.common.response.EmptyResponse);
  // 查询群组列表
  //
  // 查询租户下的群组列表，需要指定群组为动态群组或是静态群组
  rpc SearchMemberGroup(SearchMemberGroupRequest) returns (SearchMemberGroupResponse);
  // 获取客户所属群组
  //
  // 获取指定客户所在群组，包含动态和静态
  rpc GetMemberGroupByMember(GetMemberGroupByMemberRequest) returns (GetMemberGroupByMemberResponse);
  // 获取静态群组 user
  rpc ListStaticGroupUsers(ListStaticGroupUsersRequest) returns (ListStaticGroupUsersResponse);
  // 过滤出在指定群组的客户 id
  rpc CheckMemberIdsByGroup(CheckMemberIdsByGroupRequest) returns (mairpc.common.response.StringArrayResponse);
  // 更新客户来源
  //
  // 填空字符代表不需要更新
  // 并且来源与激活来源不能同时为空。
  rpc UpdateMemberSource(UpdateMemberSourceRequest) returns (mairpc.common.response.EmptyResponse);
  // 升级会员
  //
  // 设置会员到指定等级
  rpc LevelUpMember(LevelUpMemberRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建会员等级
  //
  // 创建指定会员等级
  rpc CreateMemberLevel(CreateMemberLevelRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新会员等级
  //
  // 更新指定会员等级
  rpc UpdateMemberLevel(CreateMemberLevelRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新会员等级状态
  //
  // 开启或关闭会员等级
  rpc UpdateMemberLevelStatus(UpdateMemberLevelStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除会员等级
  rpc DeleteMemberLevel(DeleteMemberLevelRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取会员等级列表
  rpc ListMemberLevels(ListMemberLevelsRequest) returns (ListMemberLevelsResponse);
  // 获取会员等级详情
  rpc GetMemberLevel(GetMemberLevelRequest) returns (MemberLevel);
  // 创建会员权益
  rpc CreateMemberPrivilege(CreateMemberPrivilegeRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新会员权益
  rpc UpdateMemberPrivilege(UpdateMemberPrivilegeRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量更新会员权益顺序
  rpc BatchUpdateMemberPrivilegesOrder(BatchUpdateMemberPrivilegesOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除会员权益
  rpc DeleteMemberPrivilege(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取会员权益列表
  rpc ListMemberPrivileges(ListMemberPrivilegesRequest) returns (ListMemberPrivilegesResponse);
  // 迁移会员权益顺序
  rpc MigrateMemberPrivilegeOrder(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取会员当前等级信息
  rpc GetLevelForMember(mairpc.common.request.MemberIdRequest) returns (MemberLevel);
  // 获取轻量的会员当前等级信息
  rpc GetLiteLevelForMember(GetLiteLevelForMemberRequest) returns (GetLiteLevelForMemberResponse);
  // 记录会员任务进度
  //
  // 每当某一个业务里会员满足了某个或者某一部分任务条件，业务代码里就需要调用这个 service 来记录会员的
  // 任务进度。如果记录完成以后发现会员已经满足了某个任务发奖的所有条件，这个 service 会自动给会员发奖，
  // 并返回奖励内容
  rpc RecordMemberTask(RecordMemberTaskRequest) returns (RecordMemberTaskResponse);
  // 回滚会员任务进度
  //
  // 调用该 service 来撤销某些任务进度以及收回一些任务奖励。如果某次回滚进度收回了奖励，那么
  // 会返回收回的奖励内容
  rpc RollbackMemberTask(RollbackMemberTaskRequest) returns (RollbackMemberTaskResponse);
  // 回滚会员任务奖励
  rpc RollbackMemberTaskReward(RollbackMemberTaskRewardRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建会员任务
  rpc CreateMemberTask(CreateMemberTaskRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新会员任务
  rpc UpdateMemberTask(UpdateMemberTaskRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新会员任务状态
  rpc UpdateMemberTaskStatus(UpdateMemberTaskStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新多个会员任务
  //
  // 根据请求一次更新多个会员任务，若请求中提供的 code 不存在，那么不会更新、也不会返回 error
  rpc UpdateMultipleMemberTask(UpdateMultipleMemberTaskRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取会员任务列表
  rpc ListMemberTasks(ListMemberTasksRequest) returns (ListMemberTasksResponse);
  // 删除会员任务
  rpc DeleteMemberTask(DeleteMemberTaskRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取会员任务详情
  rpc GetMemberTask(GetMemberTaskRequest) returns (MemberTask);
  // 创建积分管理规则
  rpc CreateMemberScoreSetting(CreateMemberScoreSettingRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新积分管理规则
  rpc UpdateMemberScoreSetting(UpdateMemberScoreSettingRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除积分管理规则
  rpc DeleteMemberScoreSetting(MemberScoreSettingCodeRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除客户 social
  rpc DeleteMemberSocial(DeleteSocialRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取积分管理规则列表
  rpc ListMemberScoreSettings(ListMemberScoreSettingsRequest) returns (ListMemberScoreSettingsResponse);
  // 获取积分规则
  rpc GetMemberScoreSetting(MemberScoreSettingCodeRequest) returns (MemberScoreSetting);
  // 更新单个会员成长值
  rpc UpdateMemberGrowth(UpdateMemberGrowthRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新单个会员成长值（返回成长值变更记录 ID）
  rpc UpdateMemberGrowthWithResponse(UpdateMemberGrowthRequest) returns (UpdateMemberGrowthResponse);
  // 批量更新会员成长值
  rpc BatchUpdateMemberGrowth(BatchUpdateMemberGrowthRequest) returns (BatchUpdateMemberGrowthResponse);
  // 发放已经过了保护期的积分
  rpc IssueProtectedScoreJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 根据唯一标识发放保护期的积分
  rpc IssueProtectedScore(IssueProtectedScoreRequest) returns (mairpc.common.response.EmptyResponse);
  // 统计全租户积分
  rpc StatsAllMemberScore(mairpc.common.request.EmptyRequest) returns (StatsAllMemberScoreResponse);
  // 统计会员积分
  rpc StatsMemberScore(mairpc.common.request.MemberIdRequest) returns (StatsMemberScoreResponse);
  // 统计会员成长值
  rpc StatsMemberHistory(mairpc.common.request.MemberIdRequest) returns (StatsMemberHistoryResponse);
  // 发放会员权益奖励
  rpc IssuePrivilegeRewardJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 根据事件记录任务进度
  rpc RecordMemberTaskByEvent(EventTriggerRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取指定客户等级变更历史
  rpc ListMemberLevelHistory(ListMemberLevelHistoryRequest) returns (ListMemberLevelHistoryResponse);
  // 统计会员等级记录数
  rpc CountMemberLevelHistories(ListMemberLevelHistoryRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 验证并发放完善信息的任务
  rpc RecordPerfectInformationTasks(RecordPerfectInformationTasksRequest) returns (RecordMemberTaskResponse);
  // 获取会员任务剩余奖励次数
  rpc GetMemberTaskRewardTimes(GetMemberTaskRewardTimesRequest) returns (GetMemberTaskRewardTimesResponse);
  // 批量获取会员任务剩余奖励次数
  rpc GetMemberTasksRewardTimes(GetMemberTasksRewardTimesRequest) returns (GetMemberTasksRewardTimesResponse);
  // 获取会员营销列表
  rpc ListMemberMarketings(ListMemberMarketingsRequest) returns (ListMemberMarketingsResponse);
  // 创建会员营销
  rpc CreateMemberMarketing(CreateMemberMarketingRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新会员营销
  rpc UpdateMemberMarketing(UpdateMemberMarketingRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除会员营销
  rpc DeleteMemberMarketing(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 修改会员营销状态
  rpc UpdateMemberMarketingStatus(UpdateMemberMarketingStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取会员营销详情
  rpc GetMemberMarketing(GetMemberMarketingRequest) returns (MemberMarketing);
  // 统计每个等级的会员人数
  rpc CountMembersForLevelJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 导出积分明细
  rpc ExportScoreHistory(ScoreHistoryListRequest) returns (mairpc.common.response.JobResponse);
  // 发放升级奖励
  //
  // 在发放前会先判断是否满足发放升级奖励的条件
  rpc SendLevelUpReward(mairpc.common.request.MemberIdRequest) returns (SendLevelUpRewardResponse);
  // 初始化群脉会员数据
  rpc InitMemberLoyalty(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 根据事件回滚任务进度
  rpc RollbackMemberTaskByEvent(EventTriggerRequest) returns (mairpc.common.response.EmptyResponse);
  // 触发会员模块中的事件
  rpc EventTrigger(EventTriggerRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取用户过期的积分
  rpc GetExpiredScore(mairpc.common.request.MemberIdRequest) returns (GetExpiredScoreResponse);
  // 判断某个人目前是否享有包邮权益
  rpc IsFreeDelivery(mairpc.common.request.MemberIdRequest) returns (IsFreeDeliveryResponse);
  // 使用包邮权益
  rpc UseFreeDelivery(UseFreeDeliveryRequest) returns (mairpc.common.response.EmptyResponse);
  // 在 bigdata 中统计每个会员营销参与人数
  rpc CountMemberMarketingJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 根据 bigdata 的统计结果更新会员营销参与人数
  rpc UpdateMemberMarketingCountJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取保护期积分列表
  rpc ListMemberProtectionPeriodScores(ListMemberProtectionPeriodScoresRequest) returns (ListMemberProtectionPeriodScoresResponse);
  // 导出保护期积分
  rpc ExportMemberProtectionPeriodScores(ListMemberProtectionPeriodScoresRequest) returns (mairpc.common.response.JobResponse);
  // 立即发放营销活动奖励
  //
  // 获取需要发放营销奖励的活动，给每一个活动创建一个 sreadmin job
  rpc IssuePrivilegeRewardImmediatelyJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 每日补发营销活动奖励
  //
  // 为周期性发奖的营销活动发奖，或是为已开启的营销活动补发新进会员的奖励。
  rpc IssuePrivilegeRewardDailyJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 判断会员能否获取奖励
  rpc CanMemberGetPrivilege(mairpc.common.request.MemberIdRequest) returns (mairpc.common.response.BoolResponse);
  // 将请求中的客户数据转成 csv 文件并上传到文件系统中
  //
  // 这个 service 是为了在类似 “勾选数十个上百个或者更多 member 并批量做操作” 的需求中
  // 能使用现成的批量操作功能而写的。上传客户数据文件是创建批量操作的第一步。
  rpc UploadMemberInfo(UploadMemberInfoRequest) returns (UploadMemberInfoResponse);
  // 更新客户渠道中的自定义信息
  rpc UpdateMemberSocialExtra(UpdateMemberSocialExtraRequest) returns (mairpc.common.response.EmptyResponse);
  // 手机验证码登录
  //
  // 如果账号未注册会自动注册并登录，返回 accessToken
  rpc LoginByPhone(LoginByPhoneRequest) returns (LoginByPhoneResponse);
  // 批量更新标签组的业务信息
  rpc BatchUpdateTagGroupBusiness(BatchUpdateTagGroupBusinessRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取指定客户会员成长值变更历史
  rpc ListMemberGrowthHistories(ListMemberGrowthHistoriesRequest) returns (ListMemberGrowthHistoriesResponse);
  // 给用户发红包
  rpc SendRedpack(SendRedpackRequest) returns (SendRedpackResponse);
  // 发积分过期提醒消息
  rpc SendScoreWillExpireNotificationJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量查询 memberInfoLog
  rpc GetMemberInfoLogs(GetMemberInfoLogsRequest) returns (GetMemberInfoLogsResponse);
  // 获取 memberInfoLog 计数
  rpc GetMemberInfoLogCount(GetMemberInfoLogsRequest) returns (mairpc.common.response.IntResponse);
  // 根据渠道标识生成用户 accessToken
  //
  // 客户不存在就创建，返回 member 级别的 accessToken。
  rpc GenerateAccessToken(UpsertMemberRequest) returns (GenerateAccessTokenResponse);
  // 根据属性 id 或属性名称删除客户身上已添加的某项自定义属性
  rpc DeletePropertyFromMember(DeletePropertyFromMemberRequest) returns (mairpc.common.response.EmptyResponse);
  // 根据属性 id 删除客户身上已添加的某些自定义属性
  rpc DeletePropertiesFromMember(DeletePropertiesFromMemberRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取 member 和对应 openId 的 social
  rpc GetSocialMember(GetSocialMemberRequest) returns (GetSocialMemberResponse);
  // 处理数据库字段类型错误
  rpc HandleDBFieldType(HandleDBFieldTypeRequest) returns (mairpc.common.response.EmptyResponse);
  // member 表字段加密迁移
  //
  // 对指定租户所在的 db 中的 member 数据进行 phone 和 email 的加密
  rpc EncryptMemberJob(EncryptMemberRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取 member 和 code 的 taskReward
  rpc GetMemberTaskReward(GetMemberTaskRewardRequest) returns (GetMemberTaskRewardResponse);
  // 创建会员等级历史
  rpc CreateSkippedMemberLevelHistory(CreateSkippedMemberLevelHistoryRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量创建或者更新标签
  rpc BatchUpsertTags(BatchUpsertTagsRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取会员任务完成统计信息
  rpc ListMemberTaskStats(ListMemberTaskStatsRequest) returns (ListMemberTaskStatsResponse);
  // 验证会员等级有效期
  rpc AdjustMemberLevelDailyJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 会员等级设置更新后更新客户
  rpc UpdateMembersForLevelUpdated(UpdateMembersForLevelUpdatedRequest) returns (mairpc.common.response.JobResponse);
  // 会员等级有效期迁移
  rpc MigrateMemberLevelValidPeriodJob(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 粉丝授权个人信息
  rpc AuthSocial(AuthSocialRequest) returns (MemberDetailResponse);
  // 获取会员任务记录
  rpc ListMemberTaskRewards(ListMemberTaskRewardsRequest) returns (ListMemberTaskRewardsResponse);
  // 导出会员激励记录
  rpc ExportMemberTaskRewards(ListMemberTaskRewardsRequest) returns (mairpc.common.response.JobResponse);
  // 获取会员奖励统计信息
  rpc GetMemberTaskRewardStats(GetMemberTaskRewardStatsRequest) returns (GetMemberTaskRewardStatsResponse);
  // 根据激活渠道按天统计新增会员
  rpc StatsMemberActivationHourlyJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取会员激活统计数据
  rpc GetMemberActivationStats(GetMemberActivationStatsRequest) returns (mairpc.common.response.StatsResponse);
  // 会员等级累计统计
  rpc StatsMemberLevelHourlyJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取会员等级统计数据
  rpc GetMemberLevelStats(GetMemberLevelStatsRequest) returns (mairpc.common.response.StatsResponse);
  // 导出会员激活统计数据
  rpc ExportMemberActivationStats(GetMemberActivationStatsRequest) returns (mairpc.common.response.JobResponse);
  // 导出会员等级统计数据
  rpc ExportMemberLevelStats(GetMemberLevelStatsRequest) returns (mairpc.common.response.JobResponse);
  // 校验客户信息
  rpc CheckMember(CheckMemberRequest) returns (CheckMemberResponse);
  // 客户数据概览
  rpc GetCustomerOverviewStats(mairpc.common.request.EmptyRequest) returns (GetCustomerOverviewStatsResponse);
  // 会员招募数据概览
  rpc GetMemberOverviewStats(mairpc.common.request.EmptyRequest) returns (GetMemberOverviewStatsResponse);
  // 删除会员白名单
  rpc DeleteMemberWhiteList(DeleteMemberWhiteListRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量导白名单
  rpc BatchCreateMemberWhiteList(BatchCreateMemberWhiteListRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取白名单列表
  rpc ListMemberWhiteList(ListMemberWhiteListRequest) returns (ListMemberWhiteListResponse);
  // 获取会员等级设置
  rpc GetMemberLevelSetting(mairpc.common.request.EmptyRequest) returns (MemberLevelSetting);
  // 更新会员等级设置
  rpc UpdateMemberLevelSetting(MemberLevelSetting) returns (mairpc.common.response.EmptyResponse);
  // 积分累计发放/累计消耗/未使用统计
  rpc StatsMemberScoreHourlyJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取积分累计统计数据概览
  rpc GetMemberScoreStats(GetMemberScoreStatsRequest) returns (mairpc.common.response.StatsResponse);
  // 导出会员积分累计发放/使用/未使用统计数据
  rpc ExportMemberScoreStats(GetMemberScoreStatsRequest) returns (mairpc.common.response.JobResponse);
  // member 是否存在
  rpc ExistsMember(mairpc.common.request.DetailRequest) returns (mairpc.common.response.BoolResponse);
  // 统计发放积分
  rpc GetScoreStats(GetScoreStatsRequest) returns (GetScoreStatsResponse);
  // 获取溢出积分列表
  rpc ListOverflowScores(ListOverflowScoresRequest) returns (ListOverflowScoresResponse);
  // 每日过期客户已过期积分
  rpc ExpireMemberScoreDailyJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 每月过期客户客户已过期积分
  rpc ExpireMemberScoreMonthlyJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 计算下次积分重置时的过期积分
  rpc CalculateWillBeExpiredScoreJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 计算下次积分重置时的过期积分
  rpc ResetScorePartlyJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取客户过期积分
  rpc ListExpireScore(ListExpireScoreRequest) returns (ListExpireScoreResponse);
  // 发放溢出积分
  rpc IssueOverflowScore(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 清除积分
  rpc ClearScore(ClearScoreRequest) returns (ClearScoreResponse);
  // 更新默认资源
  rpc UpdateDefaultResources(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建权益卡
  rpc CreateMemberPaidCard(MemberPaidCardRequest) returns (CreateMemberPaidCardResponse);
  // 为客户开通付费会员
  rpc AddPaidCardToMember(AddPaidCardToMemberRequest) returns (mairpc.common.response.EmptyResponse);
  // 修改权益卡
  rpc UpdateMemberPaidCard(MemberPaidCardRequest) returns (UpdateMemberPaidCardResponse);
  // 购买权益卡
  rpc PurchaseMemberPaidCard(PurchaseMemberPaidCardRequest) returns (PurchaseMemberPaidCardResponse);
  // 支付权益卡
  rpc PrePayMemberPaidCardRecord(PrePayMemberPaidCardRecordRequest) returns (PrePayMemberPaidCardRecordResponse);
  // 权益卡支付回调
  rpc MemberPaidCardRecordPaidWebHook(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理微信小店会员入会回调
  rpc HandleWeshopMemberBoundWebhook(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理微信小店会员退会回调
  rpc HandleWeshopMemberUnboundWebhook(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理微信小店会员积分变更回调
  rpc HandleWeshopScoreUpdatedWebhook(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理微信小店关联小程序会员回调
  rpc HandleWeshopRelatedMemberWebhook(mairpc.common.request.MaiWebhookRequest) returns (mairpc.common.response.StringResponse);
  // 权益卡申请退款
  rpc RefundMemberPaidCardRecord(RefundMemberPaidCardRecordRequest) returns (mairpc.common.response.EmptyResponse);
  // 回收权益卡
  rpc RollbackMemberPaidCards(RollbackMemberPaidCardsRequest) returns (RollbackMemberPaidCardsResponse);
  // 权益卡退款
  rpc ExecMemberPaidCardRecordRefund(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 权益卡退款状态更新
  rpc BatchUpdateRefundStatus(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 重试权益卡退款
  rpc RetryMemberPaidCardRecordRefund(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 权益卡记录列表
  rpc ListMemberPaidCardRecords(ListMemberPaidCardRecordsRequest) returns (ListMemberPaidCardRecordsResponse);
  // 权益卡列表
  rpc ListMemberPaidCards(ListMemberPaidCardsRequest) returns (ListMemberPaidCardsResponse);
  // 退出付费会员
  rpc ExitPaidMember(ExitPaidMemberRequest) returns (mairpc.common.response.EmptyResponse);
  // 延长付费会员有效期
  rpc DelayPaidMember(DelayPaidMemberRequest) returns (mairpc.common.response.EmptyResponse);
  // 赠送付费会员
  rpc IssuePaidMember(IssuePaidMemberRequest) returns (IssuePaidMemberResponse);
  // 将指定 OutTradeId 的积分消费全部回退
  rpc RollbackScoreByOutTradeId(RollbackScoreByOutTradeIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 发放周期发券奖励
  rpc IssuePeriodRewardByEvent(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取客户权益
  rpc GetMemberPrivileges(GetMemberPrivilegesRequest) returns (GetMemberPrivilegesResponse);
  // 发放周期发券的奖励
  rpc IssuePeriodRewardDailyJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 发送付费会员过期提醒
  rpc SendMemberPaidCardWillExpireNoticeJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 导出付费会员购买记录
  rpc ExportMemberPaidCardRecords(ListMemberPaidCardRecordsRequest) returns (mairpc.common.response.JobResponse);
  // 获取积分明细列表
  rpc ListScoreHistories(ScoreHistoryListRequest) returns (ListScoreHistoriesResponse);
  // 异步获取积分明细数量
  //
  // 配合 ListScoreHistories 使用，提升页面加载速度
  rpc CountScoreHistories(ScoreHistoryListRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 添加客户关联门店 ID
  rpc PushMemberPropertyArrayValue(PushMemberPropertyArrayValueRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新默认积分规则的屏蔽奖励
  rpc UpdateScoreSettingNoScoreReward(UpdateScoreSettingNoScoreRewardRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建或更新平台默认积分规则
  rpc UpsertPlatformDefaultScoreTask(UpsertPlatformDefaultScoreTaskRequest) returns (mairpc.common.response.EmptyResponse);
  // 将默认积分规则组合返回
  rpc GetPlatformDefaultScoreRule(GetPlatformDefaultScoreRuleRequest) returns (GetPlatformDefaultScoreRuleResponse);
  // 导出等级变更历史
  rpc ExportMemberLevelHistory(ListMemberLevelHistoryRequest) returns (mairpc.common.response.JobResponse);
  // 获取删除用户信息
  rpc GetDeletedMember(GetDeletedMemberRequest) returns (MemberDetailResponse);
  // 注销客户
  //
  // 包括仅删除客户敏感信息和删除客户两个选项
  rpc LogoffCustomer(LogoffCustomerRequest) returns (mairpc.common.response.EmptyResponse);
  // 消费客户短信退订消息
  //
  // 消耗阿里云 MNS 消息队列里的上行短信消息
  rpc ConsumeSmsUpMessage(ConsumeSmsUpMessageRequest) returns (mairpc.common.response.EmptyResponse);
  // 生成权益卡密
  rpc GenerateMemberPaidCardCodes(GenerateMemberPaidCardCodesRequest) returns (mairpc.common.response.EmptyResponse);
  // 校验导入权益卡密
  rpc CheckMemberPaidCardCodes(CheckMemberPaidCardCodesRequest) returns (mairpc.common.response.EmptyResponse);
  // 导入权益卡密
  rpc ImportMemberPaidCardCodes(ImportMemberPaidCardCodesRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 删除权益卡
  rpc DeleteMemberPaidCard(DeleteMemberPaidCardRequest) returns (mairpc.common.response.EmptyResponse);
  // 给客户发放权益卡
  rpc IssueMemberPaidCard(IssueMemberPaidCardRequest) returns (IssueMemberPaidCardResponse);
  // 给客户发放多张权益卡
  rpc BatchIssueMemberPaidCards(BatchIssueMemberPaidCardsRequest) returns (BatchIssueMemberPaidCardsResponse);
  // 领取权益卡
  rpc CollectMemberPaidCard(CollectMemberPaidCardRequest) returns (IssueMemberPaidCardResponse);
  // 绑定权益卡
  rpc BindMemberPaidCard(BindMemberPaidCardRequest) returns (BindMemberPaidCardResponse);
  // 获取权益卡设置
  rpc GetMemberPaidCardSetting(mairpc.common.request.EmptyRequest) returns (GetMemberPaidCardSettingResponse);
  // 修改权益卡设置
  rpc UpsertMemberPaidCardSetting(UpsertMemberPaidCardSettingRequest) returns (mairpc.common.response.EmptyResponse);
  // 发送权益卡即将过期提醒
  rpc SendBenefitCardWillExpireNoticeJob(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 导出单个权益卡记录
  rpc ExportSingleBenefitCardRecords(ListMemberPaidCardRecordsRequest) returns (mairpc.common.response.JobResponse);
  // 导出权益卡记录
  rpc ExportBenefitCardRecords(ListMemberPaidCardRecordsRequest) returns (mairpc.common.response.JobResponse);
  // 获取权益卡详情
  rpc GetMemberPaidCard(mairpc.common.request.DetailRequest) returns (MemberPaidCardDetail);
  // 修改权益卡领取记录剩余折扣额度
  rpc UpdateMemberPaidCardRecordDiscountLimit(UpdateMemberPaidCardRecordDiscountLimitRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新权益卡领取记录
  rpc UpdateMemberPaidCardRecord(UpdateMemberPaidCardRecordRequest) returns (mairpc.common.response.EmptyResponse);
  // 导出权益卡卡密
  rpc ExportMemberPaidCardCodes(mairpc.common.request.DetailRequest) returns (mairpc.common.response.JobResponse);
  // 遍历权益卡记录
  rpc IterateMemberPaidCardRecords(IterateMemberPaidCardRecordsRequest) returns (IterateMemberPaidCardRecordsResponse);
  // 创建会员任务类型
  rpc CreateMemberTaskCategory(CreateMemberTaskCategoryRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新会员任务类型
  rpc UpdateMemberTaskCategory(UpdateMemberTaskCategoryRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除会员任务类型
  rpc DeleteMemberTaskCategory(DeleteMemberTaskCategoryRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取会员任务类型列表
  rpc ListMemberTaskCategories(ListMemberTaskCategoriesRequest) returns (ListMemberTaskCategoriesResponse);
  // 监控会员积分
  rpc MonitorMemberScore(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取资料评分历史
  rpc ListMemberInfoGradesHistory(ListMemberInfoGradesHistoryRequest) returns (ListMemberInfoGradesHistoryResponse);
  // 获取资料评分历史数量
  rpc CountMemberInfoGradesHistory(ListMemberInfoGradesHistoryRequest) returns (CountMemberInfoGradesHistoryResponse);
  // 创建/更新跨端入会设置
  rpc UpsertMemberCrossSetting(UpsertMemberCrossSettingRequest) returns (MemberCrossSettingDetail);
  // 获取跨端入会设置
  rpc GetMemberCrossSetting(GetMemberCrossSettingRequest) returns (MemberCrossSettingDetail);
  // 发送跨端入会验证码
  rpc SendMemberCrossSmsVerificationCode(MemberCrossActivateRequest) returns (mairpc.common.response.EmptyResponse);
  // 客户跨端入会
  rpc MemberCrossActivate(MemberCrossActivateRequest) returns (mairpc.common.response.EmptyResponse);
  // 根据客户 ID 生成 accessToken
  rpc GenerateMemberToken(GenerateMemberTokenRequest) returns (GenerateMemberTokenResponse);
  // 开关付费会员
  rpc UpdatePaidMemberCardStatus(mairpc.common.request.BoolRequest) returns (mairpc.common.response.EmptyResponse);
  // 构建回源图片
  rpc BuildBackSourceImage(mairpc.common.request.StringArrayRequest) returns (mairpc.common.response.StringArrayResponse);
  // 每年 1.1 号重置年度累积积分
  rpc ResetAnnualScore(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新单个客户的属性
  rpc UpdateMemberPropertyByMemberId(UpdateMemberPropertyByMemberIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建/更新渠道密文手机号
  rpc UpsertChannelMixPhone(UpsertChannelMixPhoneRequest) returns (mairpc.common.response.EmptyResponse);
  // 解绑渠道密文手机号
  rpc UnbindChannelMixPhone(UnbindChannelMixPhoneRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取渠道密文手机号
  rpc GetChannelMixPhone(GetChannelMixPhoneRequest) returns (ChannelMixPhoneDetailResponse);
  // 获取渠道密文手机号列表
  rpc ListChannelMixPhones(ListChannelMixPhonesRequest) returns (ListChannelMixPhonesResponse);
  // 处理剩余的匹配的 channelMixPhone 数据
  rpc HandleRemainingChannelMixPhone(HandleRemainingChannelMixPhoneRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取渠道密文手机号
  rpc ListLiteMemberLevels(ListMemberLevelsRequest) returns (ListLiteMemberLevelsResponse);
  // 导出动态群组会员
  rpc ExportDynamicGroupMembers(ExportDynamicGroupMembersRequest) returns (mairpc.common.response.JobResponse);
  // 导出静态群组会员
  rpc ExportStaticGroupMembers(ExportStaticGroupMembersRequest) returns (mairpc.common.response.JobResponse);
  // 导入静态群组会员
  rpc ImportStaticGroupMembers(ImportStaticGroupMembersRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 批量添加客户到静态群组
  rpc BatchCreateStaticGroupMembers(BatchCreateStaticGroupMembersRequest) returns (BatchCreateStaticGroupMembersResponse);
  // 通过静态标签创建静态群组
  rpc CreateMemberStaticGroupByStaticTag(CreateMemberStaticGroupByStaticTagRequest) returns (mairpc.common.response.AsyncCacheResponse);
  // 创建静态群组
  rpc CreateMemberStaticGroup(CreateMemberStaticGroupRequest) returns (mairpc.common.response.StringResponse);
  // 导出客户
  rpc ExportCustomers(SearchMemberRequest) returns (mairpc.common.response.JobResponse);
  // 导出客户并携带客户合并记录
  rpc ExportCustomersWithMergeHistory(SearchMemberRequest) returns (mairpc.common.response.JobResponse);
  // 导出客户并携带客户合并记录
  rpc ExportCustomersByStaticTag(mairpc.common.request.StringIdRequest) returns (mairpc.common.response.JobResponse);
  // 导出会员
  rpc ExportMembers(SearchMemberRequest) returns (mairpc.common.response.JobResponse);
  // 导出粉丝
  rpc ExportFollowers(ExportFollowersRequest) returns (mairpc.common.response.JobResponse);
  // 获取任务触发日志
  rpc ListMemberTaskLogs(ListMemberTaskLogsRequest) returns (ListMemberTaskLogsResponse);
  // 获取客户任务完成记录
  rpc ListMemberTaskRecords(ListMemberTaskRecordsRequest) returns (ListMemberTaskRecordsResponse);
  // 按客户当前成长值更新客户等级
  rpc CalculateMemberLevel(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 发放或减扣保护期积分
  rpc UpdateProtectionPeriodScore(UpdateProtectionPeriodScoreRequest) returns (UpdateProtectionPeriodScoreResponse);
  // 获取用户身份信息
  rpc GetMemberIdentitySensitive(GetMemberIdentitySensitiveRequest) returns (mairpc.common.response.GetSensitiveResponse);
  // 获取任务更新状态
  rpc GetMemberLevelUpdateStatus(mairpc.common.request.EmptyRequest) returns (GetMemberLevelUpdateStatusResponse);
  // 指定时间内总的即将过期积分值
  rpc GetExpiredScoreV2(GetExpiredScoreV2Request) returns (GetExpiredScoreV2Response);
  // 获取客户距离下一会员等级的条件
  rpc GetGapToNextLevel(mairpc.common.request.MemberIdRequest) returns (MemberLevelGapResponse);
  // 迁移标签组
  rpc MigrateTagGroup(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 迁移添加标签集
  rpc MigrateTagSet(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取客户类型列表
  rpc ListMemberTypes(ListMemberTypesRequest) returns (ListMemberTypesResponse);
  // 获取客户类型详情
  rpc GetMemberType(GetMemberTypeRequest) returns (MemberTypeDetail);
  // 更新客户类型
  rpc UpdateMemberType(UpdateMemberTypeRequest) returns (MemberTypeDetail);
  // 创建客户类型
  rpc CreateMemberType(CreateMemberTypeRequest) returns (MemberTypeDetail);
  // 初始化默认客户类型，仅对经销商和零售商租户有效
  rpc InitDefaultMemberTypes(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新某个客户类型中的某些属性
  rpc UpdatePropertyInMemberType(UpdatePropertyInMemberTypeRequest) returns (mairpc.common.response.EmptyResponse);
  // 向某个客户类型中批量添加属性
  rpc BatchAddPropertiesToMemberType(BatchAddPropertiesToMemberTypeRequest) returns (mairpc.common.response.EmptyResponse);
  // 处理任务状态
  rpc AutoHandleTaskStatus(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 迁移经销商租户 member
  rpc MigrateDealerMember(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建 / 更新抖音存量品牌会员
  rpc UpsertDouyinStockMember(UpsertDouyinStockMemberRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取抖音存量品牌会员
  rpc ListDouyinStockMembers(ListDouyinStockMembersRequest) returns (DouyinStockMemberListResponse);
  // 删除抖音存量品牌会员
  rpc DeleteDouyinStockMembers(DeleteDouyinStockMembersRequest) returns (mairpc.common.response.EmptyResponse);
  // 绑定抖音同品牌多店铺
  rpc BindDouyinBrandChannelsByEvent(mairpc.common.request.CustomerEventRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取导出粉丝设置
  rpc GetExportFollowerSetting(mairpc.common.request.EmptyRequest) returns (ExportFollowerSetting);
  // 更新粉丝导出设置
  rpc UpdateExportFollowerSetting(ExportFollowerSetting) returns (mairpc.common.response.EmptyResponse);
  // 更新批量操作结果
  rpc UpdateBatchOperationMarketoDetail(UpdateBatchOperationMarketoDetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 通过京东明文 pin 获取 xid（目前支持：京东营销云品牌直连版）
  rpc GetJdXidByPlainPin(GetJdXidByPlainPinRequest) returns (GetJdXidByPlainPinResponse);
  // 获取收货地址敏感字段
  rpc GetMemberAddressSensitive(GetMemberAddressSensitiveRequest) returns (GetSensitiveResponse);
  // 校验在群组中的客户
  rpc CheckMemberIdsByGroupV2(CheckMemberIdsByGroupRequest) returns (CheckMemberIdsByGroupV2Response);
  // 拆分动态、静态群组或系统推荐人群
  rpc SplitMemberGroup(SplitMemberGroupRequest) returns (mairpc.common.response.EmptyResponse);
  // 修改客户事件的事件属性列表
  rpc UpdateMemberEventProperties(UpdateMemberEventPropertiesRequest) returns (mairpc.common.response.EmptyResponse);
  // 搜索批量操作
  rpc ListBatchOperations(ListBatchOperationsRequest) returns (ListBatchOperationsResponse);
  // 获取批量操作详情
  rpc GetBatchOperationDetail(mairpc.common.request.DetailRequest) returns (BatchOperationDetail);
  // 批量奖扣用户积分
  rpc ImportScores(ImportScoresRequest) returns (ImportScoresResponse);
  // 创建批量操作
  rpc CreateBatchOperation(CreateBatchOperationRequest) returns (CreateBatchOperationResponse);
  // 导出批量操作失败记录
  rpc ExportBatchOperationFailedData(ExportBatchOperationFailedDataRequest) returns (mairpc.common.response.JobResponse);
  // 遍历客户合并记录
  rpc IterateMemberMergeHistories(IterateMemberMergeHistoriesRequest) returns (IterateMemberMergeHistoriesResponse);
  // 创建或更新标签集
  rpc UpsertTagSet(UpsertTagSetRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除标签集
  rpc DeleteTagSet(mairpc.common.request.StringIdRequest) returns (mairpc.common.response.EmptyResponse);
  // 搜索标签集
  rpc ListTagSet(ListTagSetRequest) returns (ListTagSetResponse);
  // 搜索标签集下的标签分组
  rpc ListTagGroups(ListTagGroupsRequest) returns (ListTagGroupsResponse);
  // 搜索标签集下的标签
  rpc ListTags(ListTagsRequest) returns (ListTagsResponse);
  // 批量转移标签
  rpc BatchMoveTags(BatchMoveTagsRequest) returns (mairpc.common.response.EmptyResponse);
  // 批量转移标签分组
  rpc BatchMoveTagGroups(BatchMoveTagGroupsRequest) returns (mairpc.common.response.EmptyResponse);
  // 获取会员权益卡审核设置
  rpc GetMemberPaidCardAuditSetting(GetMemberPaidCardAuditSettingRequest) returns (mairpc.common.response.BoolResponse);
  // 修改会员权益卡审核设置
  rpc UpdateMemberPaidCardAuditSetting(UpdateMemberPaidCardAuditSettingRequest) returns (mairpc.common.response.EmptyResponse);
  // 添加会员权益卡审核员
  rpc CreateMemberPaidCardAuditor(CreateMemberPaidCardAuditorRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除会员权益卡审核员
  rpc DeleteMemberPaidCardAuditor(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 搜索会员权益卡审核员
  rpc ListMemberPaidCardAuditors(ListMemberPaidCardAuditorsRequest) returns (ListMemberPaidCardAuditorsResponse);
  // 会员权益卡提审
  rpc UpdateMemberPaidCardAuditStatus(UpdateMemberPaidCardAuditStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 会员权益卡审核
  rpc AuditMemberPaidCard(AuditMemberPaidCardRequest) returns (mairpc.common.response.EmptyResponse);
  // 创建、更新会员权益卡分组
  rpc UpsertMemberPaidCardGroup(UpsertMemberPaidCardGroupRequest) returns (mairpc.common.response.EmptyResponse);
  // 删除会员权益卡分组
  rpc DeleteMemberPaidCardGroup(mairpc.common.request.DetailRequest) returns (mairpc.common.response.EmptyResponse);
  // 搜索会员权益卡分组
  rpc ListMemberPaidCardGroups(ListMemberPaidCardGroupsRequest) returns (ListMemberPaidCardGroupsResponse);
  // 导出权益卡
  rpc ExportMemberPaidCards(ListMemberPaidCardsRequest) returns (mairpc.common.response.JobResponse);
  // 修改权益卡的顺序
  rpc UpdateMemberPaidCardsOrder(UpdateMemberPaidCardsOrderRequest) returns (mairpc.common.response.EmptyResponse);
  // 搜索会员权益卡回收记录
  rpc ListMemberPaidCardRollbackRecords(ListMemberPaidCardRollbackRecordsRequest) returns (ListMemberPaidCardRollbackRecordsResponse);
  // 导出会员权益卡回收记录
  rpc ExportMemberPaidCardRollbackRecords(ListMemberPaidCardRollbackRecordsRequest) returns (mairpc.common.response.JobResponse);
  // 通过文件发放权益卡
  rpc IssueMemberPaidCardByFile(IssueMemberPaidCardByFileRequest) returns (mairpc.common.response.JobResponse);
  // 转移权益卡分组
  rpc TransMemberPaidCardsGroup(TransMemberPaidCardsGroupRequest) returns (mairpc.common.response.EmptyResponse);
  // 发放权益卡保护期奖励
  rpc IssueMemberPaidCardProtectionReward(mairpc.common.request.EmptyRequest) returns (mairpc.common.response.EmptyResponse);
  // 修改会员勋章已读状态
  rpc UpdateMemberPaidCardReadStatus(UpdateMemberPaidCardReadStatusRequest) returns (mairpc.common.response.EmptyResponse);
  // 更新客户渠道标识信息
  rpc UpdateMemberSocialUniqueId(UpdateMemberSocialUniqueIdRequest) returns (mairpc.common.response.EmptyResponse);
}
