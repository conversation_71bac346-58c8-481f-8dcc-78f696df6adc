syntax = "proto3";

package mairpc.member;

option go_package = "member";

import "common/origin/origin.proto";
import "common/request/request.proto";
import "common/types/types.proto";
import "member/member_info_log.proto";
import "member/member_property.proto";
import "member/membership_card.proto";

message ActivatedStateFilter {
  enum ActivatedState {
    ALL = 0;
    ACTIVATED = 1;
    INACTIVATED = 2;
    DISABLED = 3;
  }
  // 客户激活状态
  ActivatedState activatedState = 1;
}

message PropertyArrayValue {
  repeated string value = 1;
}

message PropertyStringValue {
  string value = 1;
}

message PropertyDateValue {
  int64 value = 1;
}

message PropertyBoolValue {
  bool value = 1;
}

message PropertyNumberValue {
  double value = 1;
}

message PropertyNumberArrayValue {
  repeated double value = 1;
}

message PropertyDetail {
  // 客户属性
  MemberProperty property = 1;
  // 属性名
  string name = 2;
  // 属性ID
  string id = 3;
  oneof value {
    // 属性值，数组类型
    PropertyArrayValue valueArray = 6;
    // 属性值，字符串类型
    PropertyStringValue valueString = 7;
    // 属性值，日期类型
    PropertyDateValue valueDate = 8;
    // 属性值，布尔类型
    PropertyBoolValue valueBool = 9;
    // 属性值，数值类型
    PropertyNumberValue valueNumber = 10;
    // 属性值，数值数组类型
    PropertyNumberArrayValue valueNumberArray = 11;
  }
  // 地理定位对应的地址
  PropertyArrayValue address_cache = 12;
}

message MemberDetailListRequest {
  // 客户 Id 数组
  repeated string ids = 1;
  // 控制返回客户扩展字段
  //
  // Card：返回值中会带有该客户的会员卡信息；
  // Properties： 会返回该客户的属性设置；
  // SocialMember：会返回客户昵称
  repeated string extraFields = 2;
  // 是否返回客户系统属性
  bool showSystemProperties = 3;
  // 客户 unionId 数组
  repeated string unionIds = 4;
  // 过滤需要返回的客户属性
  //
  // 传此参数后则 showSystemProperties 字段失效。注意此参数是对 memberProperty 表进行过滤
  MemberPropertyFilter propertyFilter = 5;
  // 客户手机号
  repeated string phones = 6;
  // 客户 openId 数组
  repeated string openIds = 7;
}

message MemberDetailListResponse {
  // 客户列表
  repeated MemberDetailResponse members = 1;
}

message MemberDetailRequest {
  // @required
  //
  // 客户唯一标识，包括: 客户 id, phone, unionId, openId, cardNumber
  string id = 1;
  // 激活状态
  ActivatedStateFilter filter = 2;
  // 控制返回客户扩展字段
  //
  // Card：返回值中会带有该客户的会员卡信息；
  // Properties： 会返回该客户的属性设置；
  // SocialMember：会返回客户昵称
  repeated string extraFields = 3;
  // 客户ID(等同于id)
  string memberId = 4;
  // 是否返回客户系统属性
  //
  // 该字段已弃用，请使用 propertyFilter 中的 showSystemProperties 参数
  bool showSystemProperties = 5;
  // 过滤需要返回的客户属性
  //
  // 传此参数后则 showSystemProperties 字段失效。注意此参数是对 memberProperty 表进行过滤
  MemberPropertyFilter propertyFilter = 6;
}

message MemberPropertyFilter {
  mairpc.common.types.BoolValue isVisible = 1;
  mairpc.common.types.BoolValue isVisibleInFilter = 2;
  mairpc.common.types.BoolValue isSystem = 3;
  // 是否展示系统属性
  //
  // 此字段为 true 时 isSystem 字段无效
  bool showSystemProperties = 4;
}

message MemberDetailResponse {
  // 客户 ID
  string id = 1;
  // 客户名称
  string name = 2;
  // 性别
  string gender = 3;
  // 头像
  string avatar = 4;
  // 手机号
  //
  // openapi-consumer 接口 POST /v2/getMember、POST /v2/updateMember 中该字段脱敏
  string phone = 5;
  // 生日
  int64 birthday = 6;
  // 当前积分
  int64 score = 7;
  // 来源
  mairpc.common.origin.OriginInfo originFrom = 8;
  // 平台身份信息
  repeated mairpc.common.origin.OriginInfo socials = 9;
  // 会员卡信息
  MembershipCardDetail card = 10;
  // 邮箱
  string email = 11;
  // 标签
  repeated string tags = 12;
  // 客户属性
  //
  // openapi-consumer 接口 POST /v2/getmember、POST /v2/updateMember 中 name 为 phone 的属性值脱敏
  repeated PropertyDetail properties = 13;
  // 创建时间
  int64 createdAt = 14;
  // 激活时间
  int64 activatedAt = 15;
  // 更新时间
  int64 updatedAt = 16;
  // 所属城市
  string city = 17;
  // 所属国家
  string country = 18;
  // 所属省份
  string province = 19;
  // 所属区
  string district = 20;
  // 详细地址
  string detail = 21;
  // 受限状态
  int32 blockedStatus = 22;
  // 受限时间
  int64 blockedAt = 23;
  // 是否激活
  bool isActivated = 24;
  // 总积分
  uint64 totalScore = 25;
  // 年度累计获得积分
  uint64 annualAccumulatedScore = 26;
  // 年度花费积分
  uint64 annualCostScore = 27;
  // 是否被禁用
  bool isDisabled = 28;
  // 积分过期信息
  string monthExpiredContent = 29;
  // 是否为扫描二维码用户
  bool qrcodeViewed = 30;
  // 备注
  string remarks = 31;
  // 会员卡号
  string cardNumber = 32;
  // 会员卡发放时间
  int64 cardProvideTime = 33;
  // 客户昵称
  string socialMember = 34;
  // 微信会员卡
  WxCard wxCard = 35;
  // 客户价值
  MemberValue memberValue = 36;
  // 客户等级
  uint32 level = 37;
  // 客户等级名称
  string levelName = 38;
  // 当前成长值
  int64 growth = 39;
  // 来源
  string source = 40;
  // 注册来源
  string activationSource = 41;
  // 租户 ID
  string accountId = 42;
  // 会员累计消耗积分
  uint64 totalCostScore = 43;
  // 等级起始日期
  string levelStartedAt = 44;
  // 等级过期时间
  string levelExpireAt = 45;
  // 权益卡列表
  repeated MemberPaidCard paidCards = 46;
}

message MemberPaidCard {
  // 权益卡 id
  string cardId = 1;
  // 权益卡类型
  //
  // 付费会员（paidMember）
  string cardType = 2;
  // 权益卡卡号
  string cardNumber = 3;
  // 开始时间
  string startedAt = 4;
  // 过期时间
  string expireAt = 5;
}

message WxCard {
  // 微信会员卡 id
  string id = 1;
  // 卡券码
  string code = 2;
  // 来源渠道
  string channel = 3;
  // 激活时间
  int64 activatedAt = 4;
  // 场景码
  string sceneId = 5;
}

message MemberValue {
  // 客户阶段
  //
  // 可选值 unknown、contacting、understanding、experiencing、trading
  string stage = 1;
  // 阶段名称
  string stageName = 2;
  // 客户总评分
  int64 grades = 3;
  // 客户信息评分
  int64 informationGrades = 4;
  // 客户活跃度评分
  int64 engagement = 5;
}

message PropertyInfo {
  // 客户属性 id，24 位十六进制字符串
  string id = 1;
  // 客户属性特征 Id
  string propertyId = 2;
  oneof value {
    // 字符串数组
    PropertyArrayValue valueArray = 10;
    // 字符串
    PropertyStringValue valueString = 11;
    // 整数值
    PropertyDateValue valueDate = 12;
    // 布尔值
    PropertyBoolValue valueBool = 13;
    // 浮点值
    PropertyNumberValue valueNumber = 14;
    // 浮点数组
    PropertyNumberArrayValue valueNumberArray = 15;
  }
}

message SimpleMemberDetailRequest {
  // @required
  //
  // member._id
  string id = 1; // valid:"required"
}

message SimpleMemberDetailResponse {
  // 客户 ID
  string id = 1;
  // 手机号
  string phone = 2;
}

message CreateMemberRequest {
  // 客户标签
  repeated string tags = 1;
  // 是否触发会员激励
  bool needTriggerReward = 2;
  // @required
  //
  // 来源
  mairpc.common.origin.OriginInfo originFrom = 3; // valid:"required"
  // 身份平台信息
  repeated mairpc.common.origin.OriginInfo socials = 4;
  // 已弃用，不要使用此字段。
  string unionId = 5;
  // 客户属性
  repeated PropertyInfo properties = 7;
  // 是否未激活
  bool isInactivated = 8;
  // 微信会员卡
  WxCard wxCard = 9;
  // 会员卡号
  string cardNumber = 10;
  // 客户阶段
  //
  // 可选值 unknown、contacting、understanding、experiencing、trading
  string stage = 11;
  // 是否开会员卡
  bool needProvideCard = 12;
  // 昵称
  string nickname = 13;
  // 备注
  string remarks = 14;
  // 来源
  string source = 15;
  // 注册来源
  string activationSource = 16;
}

message ActivateMemberRequest {
  // @required
  //
  // 客户 ID
  string id = 1; // valid:"required,objectId"
  // 渠道 ID
  string channelId = 2;
  // 渠道来源
  string origin = 3;
  // 激活来源
  string activationSource = 4;
  // 门店 id
  string storeId = 5; // valid:"objectId"
  // 导购 id
  string staffId = 6; // valid:"objectId"
  // 访问来源
  mairpc.common.types.Utm utm = 7;
  // 引导注册的分销员 memberId
  string promoterMemberId = 8;
}

message ActivateMemberResponse {
  // 激活时间
  int64 activatedAt = 1;
}

message ActivateMembersResponse {
  // 失败条目的 Id 和原因
  map<string, string> failedItems = 1;
  // 成功的 Id
  repeated string succeedIds = 2;
}

message MemberListResponse {
  repeated MemberDetailResponse members = 1;
  uint64 totalCount = 2;
  string scrollId = 3;
}

message UpdateMemberRequest {
  // 客户 id
  string memberId = 1;
  // 客户属性
  repeated PropertyInfo properties = 2;
  // 备注
  mairpc.common.types.StringValue remarks = 3;
  // 标签
  mairpc.common.types.StringList tags = 4;
  // 只需要验证有更改的客户属性
  //
  // 比如属性设置中有 5 个必填属性，只需要更新 1 个，那么这个参数需要设置成 true，其他 4 个属性不做验证。一般情况下，建议设置为 true。
  bool onlyValidateUpdated = 5;
  // 是否为扫码用户
  bool qrcodeViewed = 6;
  // 客户平台信息
  repeated mairpc.common.origin.OriginInfo addedSocials = 7;
  // 微信会员卡
  WxCard wxCard = 8;
  // 作为客户激励的触发渠道
  mairpc.common.origin.OriginInfo updateIn = 9;
  // 客户阶段
  //
  // 可选值 unknown、contacting、understanding、experiencing、trading
  string stage = 10;
  // 能唯一标识客户的字段值
  //
  // 可以客户的 openId, memberId, unionId, phone
  string uniqueId = 11;
  // 来源
  string source = 12;
  // 注册来源
  string activationSource = 13;
  // 禁用客户更新事件
  bool disableEvent = 14;
  // 操作人员
  Operator operator = 15;
  // 操作备注
  string operatorRemark = 16;
}

message CreateMemberLogsRequest {
  // @required
  //
  // 客户 id
  string id = 1;
  // 操作时间
  int64 operatedAt = 2;
  // 具体操作内容
  string operation = 3;
}

message CreateMemberLogsResponse {
  // 操作时间
  string createMemberLogs = 1;
}

// You can specify at least one of those filters.
message SocialFilter {
  // 渠道 id
  string channelId = 1;
  // 渠道下用户唯一标识
  string openId = 2;
  // 微信开放平台下用户唯一标识
  string unionId = 3;
  // 手机号
  string phone = 4;
  // 来源
  string origin = 5;
}

message GetBySocialRequest {
  // 用户身份平台信息
  SocialFilter socialFilter = 1;
  // 激活状态
  ActivatedStateFilter stateFilter = 2;
  // 控制返回客户扩展字段
  //
  // Card：返回值中会带有该客户的会员卡信息；
  // Properties： 会返回该客户的属性设置；
  // SocialMember：会返回客户昵称
  repeated string extraFields = 3;
  // 是否在同一集团中搜索
  bool isGroup = 4;
  // 是否返回客户系统属性
  bool showSystemProperties = 5;
  // 过滤需要返回的客户属性
  //
  // 传此参数后则 showSystemProperties 字段失效。注意此参数是对 memberProperty 表进行过滤
  MemberPropertyFilter propertyFilter = 6;
}

message TagStatsRequest {
  // 客户标签
  repeated string tags = 1;
}

message TagStats {
  // 客户标签名称
  string name = 1;
  // 使用此标签的客户数
  uint32 count = 2;
}

message TagStatsResponse {
  // 客户标签统计列表
  repeated TagStats items = 1;
}

message ProvideCardRequest {
  // @required
  //
  // 会员卡 id
  string cardId = 1;
  // @required
  //
  // 客户 id 数组
  repeated string ids = 2;
  // 是否需要激活会员
  bool needToActivate = 3;
  // 指定会员卡号
  //
  // 注意只有为单个客户发卡时，cardNumber 才有效
  string cardNumber = 4;
}

message ProvideCardResponse {
  string message = 1;
}

message GetByTagsRequest {
  // 客户标签数组
  repeated string tags = 1;
  // 客户来源
  string socialOrigin = 2;
  // 是否返回客户系统属性
  bool showSystemProperties = 3;
  // 分页信息，如果不传，默认第一页 500 条数据
  mairpc.common.request.ListCondition listCondition = 4;
  // 过滤需要返回的客户属性
  //
  // 传此参数后则 showSystemProperties 字段失效。注意此参数是对 memberProperty 表进行过滤
  MemberPropertyFilter propertyFilter = 6;
}

message MergeMemberRequest {
  // 合并客户时的主客户 id
  string mainMemberId = 1;
  // 客户 id 数组
  repeated string mergeMemberIds = 2;
  // 操作 id
  string operatorId = 3;
  // 是否允许合并黑名单客户
  bool allowBlocked = 4;
  // 备注
  string remark = 5;
}

enum BlockStatus {
  // 正常
  NORMAL = 0;
  // 可疑
  SUSPICIOUS = 1;
  // 拉黑
  BLOCKED = 2;
}

message UpdateBlockedStatusRequest {
  // @required
  //
  // 客户 Id
  string memberId = 1;
  // @required
  //
  // 新的状态
  BlockStatus newStatus = 2;
  // 更新原因
  string remark = 3;
  // 添加到白名单
  bool addToAllowList = 4;
}

message UpdateBlockedStatusResponse {
  // @required
  //
  // 更新时间
  int64 updatedAt = 1;
}

message SearchMemberRequest {
  // 客户 id 数组
  repeated string ids = 1;
  // 客户来源渠道
  repeated string channels = 2;
  // 客户来源详细信息
  repeated string origins = 3;
  // 会员卡
  repeated string cards = 4;
  // 客户标签
  repeated string tags = 5;
  // 客户创建时间
  mairpc.common.types.DateRange createdAt = 6;
  // 客户受限状态
  repeated BlockStatus blockStatuses = 7;
  // 客户地址
  mairpc.common.types.Location location = 8;
  // 客户生日
  mairpc.common.types.DateRange birthday = 9;
  // 搜索关键字, 姓名/手机号/会员卡号
  string searchKeyWord = 10;
  // 会员卡名称
  bool requiredCardName = 11;
  // 客户性别数组
  repeated string genders = 12;
  // 客户积分范围
  mairpc.common.types.IntegerRange score = 13;
  // 客户激活状态
  ActivatedStateFilter activatedState = 14;
  // 分页信息，包含当前页和页大小
  mairpc.common.request.ListCondition listCondition = 15;
  // 领卡时间
  mairpc.common.types.DateRange cardTime = 16;
  // 是否在集团中搜索
  bool isGroup = 17;
  // 客户所属租户 id 数组
  repeated string accountIds = 18;
  // 客户生日开始时间
  uint32 birthFrom = 19;
  // 客户生日结束时间
  uint32 birthTo = 20;
  // 客户属性条件
  repeated PropertyCond properties = 21;
  // 客户会员卡领取时间范围
  mairpc.common.types.DateRange cardProvideTime = 22;
  // 客户会员卡是否有效
  mairpc.common.types.BoolValue isValidCard = 23;
  // 客户是否是会员
  mairpc.common.types.BoolValue isMember = 24;
  // 是否使用scroll遍历客户
  bool useScroll = 25;
  // 用scroll遍历客户时提供的标识ID
  string scrollId = 26;
  // 客户的平台身份信息
  repeated mairpc.common.origin.SocialSearchInfo socials = 27;
  // 来源
  repeated string source = 28;
  // 注册来源
  repeated string activationSource = 29;
  // 成长值
  mairpc.common.types.IntegerRange growth = 30;
  // 客户更新时间范围
  mairpc.common.types.DateRange updatedAt = 31;
  // 会员等级
  repeated uint64 levels = 32;
  // 是否返回客户系统属性
  bool showSystemProperties = 33;
  // 客户价值评分
  mairpc.common.types.IntegerRange grades = 34;
  // 客户标签筛选条件
  //
  // 用于更加复杂的标签筛选条件，此字段不会覆盖 tags 字段中的标签
  TagsSelector tagsSelector = 35;
  // 排除的客户来源
  repeated string excludeOrigins = 36;
  // 用 scroll 遍历客户时获取数据的大小，不传时默认 500
  int64 scrollSize = 37;
  // 会员等级起始日期
  mairpc.common.types.StringDateRange levelStartedAt = 38;
  // 搜索类型，按照客户编号（memberId）、会员编号（cardNumber）、平台身份 ID（openIdUnionId）、姓名/手机号/邮箱搜索（namePhoneEmail）
  // 联系人/联系人手机（contactOrPhone）、客户编号/名称（memberIdName）、名称/联系人/地址（nameContactAddress）、activationStaff（注册导购）
  string searchType = 39; //valid:"in(memberId|cardNumber|openIdUnionId|namePhoneEmail|contactOrPhone|memberIdName|nameContactAddress|activationStaff)"
  // 会员注册时间
  mairpc.common.types.StringDateRange activatedAt = 40;
  // 客户是否是付费会员
  mairpc.common.types.BoolValue isPaidMember = 41;
  // 过滤需要返回的客户属性
  //
  // 传此参数后则 showSystemProperties 字段失效。注意此参数是对 memberProperty 表进行过滤
  MemberPropertyFilter propertyFilter = 42;
  // 是否过滤手机号为空的会员数据
  bool isFilterNullPhone = 43;
  // 激活客户的平台身份信息
  //
  // 和 socials 字段为 and 关系
  // 当前为临时方案，后续考虑添加 socialsSelector
  repeated mairpc.common.origin.SocialSearchInfo activationSocials = 44;
  // 客户属性筛选条件
  //
  // 满足任一属性即可，与 properties 参数不并存
  repeated PropertyCond propertiesIn = 45;
  // 年度消耗积分范围
  mairpc.common.types.IntegerRange annualCostScore = 46;
  // 排除指定的部分客户
  //
  // 需要排除的客户 ID 列表
  repeated string excludeIds = 47; // valid:"objectIdList"
  // 排除指定的标签
  repeated string excludeTags = 48;
  // 模型标签，仅在 es 场景下生效
  repeated string modelTags = 49;
  // 规则标签，仅在 es 场景下生效
  repeated string ruleTags = 50;
  // 排除的渠道，需要 origin 和 channelId
  mairpc.common.origin.SocialSearchInfo excludeSocial = 51;
  // 查询没有 birth 的客户
  bool filterZeroBirth = 52;
}

message TagsSelector {
  // 标签条件列表
  repeated TagCondition tagConditions = 1;
  // 列表中标签条件之间的关系
  //
  // 默认为 and 条件，通过设置此字段使用 or 条件
  bool isOrCondition = 2;
}

message TagCondition {
  // 标签列表
  repeated string tags = 1;
  // 列表中标签的关系
  //
  // 默认为 and 条件，通过设置此字段使用 or 条件
  bool isOrCondition = 2;
}

message BindChannelRequest {
  // 客户 Id
  string memberId = 1;
  // 客户来源
  string origin = 2;
  // 客户渠道 Id
  string channelId = 3;
  // 渠道下用户唯一标识
  string openId = 4;
  // 渠道名称
  string channelName = 5;
  // 微信开放平台下用户唯一标识
  string unionId = 6;
  // 动作类型，绑定/解绑
  string type = 7; // valid:"in(bind|update)"
  // 是否订阅
  bool isSubscribed = 8;
  // 渠道下用户昵称
  string nickname = 9;
  // 头像
  string avatar = 10;
  // 性别
  string gender = 11;
  // 国家
  string country = 12;
  // 省份
  string province = 13;
  // 城市
  string city = 14;
  // 首次授权时间
  //
  // 时间格式需要满足 ISO 8601 标准, 比如2006-01-02T15:04:05Z
  string firstAuthorizeTime = 15;
  // 最近授权时间
  //
  // 时间格式需要满足 ISO 8601 标准, 比如2006-01-02T15:04:05Z
  string authorizeTime = 16;
  // 首次订阅时间
  //
  // 时间格式需要满足 ISO 8601 标准, 比如2006-01-02T15:04:05Z
  string firstSubscribeTime = 17;
  // 订阅时间
  //
  // 时间格式需要满足 ISO 8601 标准, 比如2006-01-02T15:04:05Z
  string subscribeTime = 18;
  // 扩展字段
  string extra = 19;
  // 控制返回客户扩展字段
  //
  // Card：返回值中会带有该客户的会员卡信息；
  // Properties： 会返回该客户的属性设置；
  // SocialMember：会返回客户昵称
  repeated string extraFields = 20;
  // 解绑时间
  //
  // 时间格式需要满足 ISO 8601 标准, 比如2006-01-02T15:04:05Z
  string unsubscribeTime = 21;
  // 是否授权
  bool authorized = 22;
  // 首次来源场景
  string firstOriginScene = 23;
  // 来源场景
  string originScene = 24;
  // 语言
  string language = 25;
}

message UpsertSuspiciousRuleRequest {
  // 可疑规则执行间隔
  uint64 interval = 1;
  // 可疑规则积分操作类型
  uint64 operation = 2;
  // 可疑规则积分改变的次数
  uint64 threshold = 3;
  // 是否禁用可疑名单
  bool isDisabled = 4;
  // 黑名单规则
  SimpleSuspiciousRule blackListRule = 5;
}

message SimpleSuspiciousRule {
  // 执行间隔
  uint64 interval = 1;
  // 积分操作类型
  uint64 operation = 2;
  // 积分改变的次数
  uint64 threshold = 3;
  // 是否可用
  bool isEnabled = 4;
}

message SuspiciousRule {
  // 规则 Id
  string id = 1;
  // 规则执行间隔
  uint64 interval = 2;
  // 积分操作类型
  uint64 operation = 3;
  // 积分改变的次数
  uint64 threshold = 4;
  // 创建时间
  int64 createdAt = 5;
  // 更新时间
  int64 updatedAt = 6;
  // 是否被禁用
  bool isDisabled = 7;
  // 黑名单规则
  SimpleSuspiciousRule blackListRule = 8;
}

message BindPhoneRequest {
  // @required
  //
  // 客户 id
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 客户手机号
  string phone = 2; // valid:"required,phone"
  // 渠道 id
  string channelId = 3;
  // 激活时间
  //
  // 时间格式需要满足 ISO 8601 标准, 比如2006-01-02T15:04:05Z，通常情况下为空，表示使用当前时间
  string activatedAt = 4; // valid:"rfc3339,optional"
  // 是否允许合并客户
  bool allowMerge = 5;
  // dms.distributor._id
  string storeId = 6; // valid:"objectId"
  // dms.staff._id
  string staffId = 7; // valid:"objectId"
  // 访问来源
  mairpc.common.types.Utm utm = 8;
  // 是否激活会员
  bool notActivateMember = 9;
  // 小程序场景值
  // https://developers.weixin.qq.com/miniprogram/dev/reference/scene-list.html
  string scene = 10;
  // 引导注册的分销员 memberId
  string promoterMemberId = 11;
}

message UpdateMemberValueRequest {
  // 客户 Id
  string id = 1;
  // 客户阶段
  //
  // 可选值 unknown、contacting、understanding、experiencing、trading
  mairpc.common.types.StringValue stage = 2;
  // 客户活跃值评分
  mairpc.common.types.Int64Value engagement = 3;
  // 客户信息评分
  bool updateInformationGrades = 4;
  // 允许回退客户阶段
  bool allowPullBackStage = 5;
}

message AddPaidCardToMemberRequest {
  // @required
  //
  // 客户 Id
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 付费会员卡 Id
  string cardId = 2; // valid:"required,objectId"
  // 会员开始时间
  string startedAt = 3; // valid:"rfc3339"
  // 会员过期时间
  string expireAt = 4; // valid:"rfc3339"
}

message GetByPropertyRequest {
  // 客户属性名称
  string propertyName = 1;
  // 客户属性值
  string propertyValue = 2;
  // 控制返回客户扩展字段
  //
  // Card：返回值中会带有该客户的会员卡信息；
  // Properties： 会返回该客户的属性设置；
  // SocialMember：会返回客户昵称
  repeated string extraFields = 3;
  // 是否返回客户系统属性
  bool showSystemProperties = 4;
  // 过滤需要返回的客户属性
  //
  // 传此参数后则 showSystemProperties 字段失效。注意此参数是对 memberProperty 表进行过滤
  MemberPropertyFilter propertyFilter = 5;
}

message PropertyCond {
  // 客户属性名称
  string propertyName = 1;
  // 客户属性值，因为老代码的关系需要单独保留这个字段
  string propertyValue = 2;
  // 客户属性值，如果需要支持新的搜索方式都需要把字段加到这里
  oneof value {
    mairpc.common.types.DateRange dateRange = 3;
    mairpc.common.types.IntegerRange intRange = 4;
    // 用于对数组类型做 $in 筛选
    mairpc.common.types.StringArrayValue stringArray = 5;
    mairpc.common.types.BoolValue boolValue = 6;
    // 用于筛选这个属性是否存在于 member.properties 中
    mairpc.common.types.BoolValue exists = 7;
    // 用于对文本类型做 $in 筛选
    mairpc.common.types.StringArrayValue stringValueIn = 8;
    // 用于对文本类型做 $nin 筛选
    mairpc.common.types.StringArrayValue stringValueNin = 9;
    // 字符串类型的客户属性也可以使用 propertyValue
    mairpc.common.types.StringValue stringValue = 11;
  }
  // 对当前客户属性进行 $in 筛选，传此字段 propertyValue 字段和 value 字段失效
  repeated OneOfPropertyValue valueIn = 10;
}

message OneOfPropertyValue {
  oneof value {
    mairpc.common.types.DateRange dateRange = 1;
    mairpc.common.types.IntegerRange intRange = 2;
    // 用于对数组类型做 $in 筛选
    mairpc.common.types.StringArrayValue stringArray = 3;
    mairpc.common.types.BoolValue boolValue = 4;
    // 用于筛选这个属性是否存在于 member.properties 中
    mairpc.common.types.BoolValue exists = 5;
    // 用于对文本类型做 $in 筛选
    mairpc.common.types.StringArrayValue stringValueIn = 6;
    // 用于对文本类型做 $nin 筛选
    mairpc.common.types.StringArrayValue stringValueNin = 7;
    mairpc.common.types.StringValue stringValue = 8;
  }
}

message SwitchDisabledStatusRequest {
  // 客户ID
  string id = 1;
  // 禁用状态
  bool isDisabled = 2;
}

message DecryptAndBindMiniProgramPhoneRequest {
  // @required
  //
  // 解密需要的 jsCode 或手机号获取凭证 code。如果 encryptedData、iv 两个值都不为空，调 weconnect Data decrypt 接口。如果 encryptedData、iv 两个值任意一个为空，透传到手机号快速验证接口。
  string code = 1; // valid:"required"
  // 需要解密的数据
  string encryptedData = 2;
  // 加密算法的初始向量
  string iv = 3;
  // @required
  //
  // 渠道ID
  string channelId = 4; // valid:"required,length(24|24),hexadecimal"
  // 客户ID
  string memberId = 5; // valid:"required,length(24|24),hexadecimal"
  // 是否允许合并客户
  bool allowMerge = 6;
  // dms.distributor._id
  string storeId = 7; // valid:"objectId"
  // dms.staff._id
  string staffId = 8; // valid:"objectId"
  // 访问来源
  mairpc.common.types.Utm utm = 9;
  // 是否激活会员
  bool notActivateMember = 10;
  // 场景值
  // https://developers.weixin.qq.com/miniprogram/dev/reference/scene-list.html
  string scene = 11;
  // 引导注册的分销员 memberId
  string promoterMemberId = 12;
  // 微信小店关联入会的会话 id，由小店传递给小程序，有效期 10 分钟
  string weshopSessionId = 13;
  // 微信小店 appId
  string weshopAppId = 14;
  // 小程序 openId
  string openId = 15;
  // 小程序 unionId
  string unionId = 16;
}

message SendCustomerEventRequest {
  // @required
  //
  // 客户ID
  string memberId = 1; // valid:"required,length(24|24),hexadecimal"
  // @required
  //
  // 事件类型
  string type = 2; // valid:"required"
  // @required
  //
  // 事件属性
  //
  // json化后的字符串
  string eventProperties = 3; // valid:"required,json"
  // 匿名用户 ID
  //
  // 建议设置一个 uuid 做为匿名用户 id
  string clientId = 4;
  // 渠道 ID
  //
  // 事件发生的渠道
  string channelId = 5;
  // 客户 OpenID
  string openId = 6;
  // 客户 UnionID
  string unionId = 7;
  // 携带 business tag
  bool withBusinessTag = 8;
}

message BindPhoneWithCaptchaRequest {
  // @required
  //
  // 客户ID
  string memberId = 1; // valid:"required,length(24|24),hexadecimal"
  // @required
  //
  // 手机号
  string mobile = 2; // valid:"required,phone"
  // @required
  //
  // 验证码
  string captcha = 3; // valid:"required"
  // 是否允许合并客户
  bool allowMerge = 4;
  // 渠道 ID
  //
  // 事件发生的渠道
  string channelId = 5;
  // dms.distributor._id
  string storeId = 6; // valid:"objectId"
  // dms.staff._id
  string staffId = 7; // valid:"objectId"
  // 访问来源
  mairpc.common.types.Utm utm = 8;
  // 是否激活会员
  bool notActivateMember = 9;
  // 场景值
  // https://developers.weixin.qq.com/miniprogram/dev/reference/scene-list.html
  string scene = 10;
  // 引导注册的分销员 memberId
  string promoterMemberId = 11;
}

message BindPhoneWithQuickVerificationComponentRequest {
  BindPhoneRequest bindPhoneRequest = 1;
  // @required
  //
  // 微信快速验证组件获取的 Code
  string code = 2; // valid:"required"
  // 引导注册的分销员 memberId
  string promoterMemberId = 3;
}

message MemberRequest {
  // @required
  //
  // 客户ID
  string memberId = 1; // valid:"required,length(24|24),hexadecimal"
  // 筛选手机号、地址、姓名
  string searchKey = 2;
  string type = 3; // valid:"in(daigou)"
}

message MemberCountResponse {
  // 客户数
  uint64 count = 1;
}

message GetMemberAddressSensitiveRequest {
  // @required
  //
  // 客户收货地址 id
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 用户ID
  string memberId = 2; // valid:"required,objectId"
  // @required
  //
  // 敏感信息字段
  string sensitiveKey = 3; // valid:"in(name|phone),required"
}

message GetSensitiveResponse {
  string data = 1;
}

message GetByWxCardRequest {
  // @required
  //
  // 微信会员卡ID
  string id = 1;
  // @required
  //
  // 微信会员卡CODE
  string code = 2;
  // 控制返回客户扩展字段
  //
  // Card：返回值中会带有该客户的会员卡信息；
  // Properties： 会返回该客户的属性设置；
  // SocialMember：会返回客户昵称
  repeated string extraFields = 3;
  // 是否返回客户系统属性
  bool showSystemProperties = 4;
  // 过滤需要返回的客户属性
  //
  // 传此参数后则 showSystemProperties 字段失效。注意此参数是对 memberProperty 表进行过滤
  MemberPropertyFilter propertyFilter = 5;
}

message IterateThroughMembersRequest {
  // 单次遍历时返回会员数量, 默认为100, 最大为10000
  uint64 limit = 1;
  // 前一次遍历时最后一个会员的会员ID, 若为空, 则会从当前租户第一个会员开始遍历
  string memberId = 2; // valid:"objectId,optional"
  // 控制返回客户扩展字段
  //
  // Card：返回值中会带有该客户的会员卡信息；
  // Properties： 会返回该客户的属性设置；
  // SocialMember：会返回客户昵称
  repeated string extraFields = 4;
  // 是否返回客户系统属性
  bool showSystemProperties = 5;
  // 过滤需要返回的客户属性
  //
  // 传此参数后则 showSystemProperties 字段失效。注意此参数是对 memberProperty 表进行过滤
  MemberPropertyFilter propertyFilter = 6;
}

message UploadMemberEventLogsRequest {
  // 事件
  repeated MemberEventLog logs = 1;
  // @required
  //
  // 匿名用户 ID
  //
  // 建议设置一个 uuid 做为匿名用户 id
  string clientId = 2; // valid:"required"
  // 渠道 ID
  //
  // 事件发生的渠道
  string channelId = 3;
  // 客户 OpenID
  string openId = 4;
  // 客户 UnionID
  string unionId = 5;
  // 客户 Id
  string memberId = 6;
}

message UploadMemberEventLogsResponse {
  // 事件
  repeated FailedMemberEventLog failedLogs = 1;
}

message FailedMemberEventLog {
  // 消息ID
  string id = 1;
  // 事件名称
  string name = 2;
  // 事件发生时间
  string occurredAt = 3;
  // 事件属性
  string properties = 4;
  // 失败原因
  string failedReason = 5;
}

message MemberEventLog {
  // 消息ID
  //
  // 选填字段，如果已存在相同 id 的事件，会做更新操作。建议使用 uuid
  string id = 1;
  // @required
  //
  // 事件名称
  string name = 2; // valid:"required"
  // @required
  //
  // 事件发生时间
  //
  // ISO 8601：如 2019-02-01T15:05:06+08:00
  string occurredAt = 3; // valid:"required"
  // 事件属性
  //
  // json 序列化的字符串
  string properties = 4; // valid:"required"
}

message UpdateSocialRequest {
  // @required
  //
  // 客户ID
  string memberId = 1; // valid:"required,length(24|24),hexadecimal"
  // @required
  //
  // 渠道ID
  string channelId = 2; // valid:"required"
  // 渠道下用户唯一标识
  string openId = 3;
  // 微信开放平台下用户唯一标识
  string unionId = 4;
  // 昵称
  string nickname = 5;
  // 性别
  string gender = 6;
  // 国籍
  string country = 7;
  // 省份
  string province = 8;
  // 城市
  string city = 9;
  // 头像
  string avatar = 10;
  // 订阅时间
  //
  // 时间格式需要满足 ISO 8601 标准, 比如2006-01-02T15:04:05Z
  string subscribeTime = 11;
  // 首次授权时间
  //
  // 时间格式需要满足 ISO 8601 标准, 比如2006-01-02T15:04:05Z
  string firstAuthorizeTime = 12;
  // 最近授权时间
  //
  // 时间格式需要满足 ISO 8601 标准, 比如2006-01-02T15:04:05Z
  string authorizeTime = 13;
  // 语言
  string language = 14;
  // 扩展字段
  string extra = 15;
  // 是否订阅
  mairpc.common.types.BoolValue subscribed = 16;
  // 是否为来源渠道，传 true 时会将原来源渠道的该字段置为 false，保持仅一个来源渠道
  mairpc.common.types.BoolValue isOriginal = 17;
  // 取消订阅时间
  //
  // 时间格式需要满足 ISO 8601 标准, 比如2006-01-02T15:04:05Z
  string unsubscribeTime = 18;
}

message DeleteSocialRequest {
  // @required
  //
  // 客户 ID
  string id = 1; // valid:"required,objectId"
  // @required
  //
  // 渠道 ID
  string channelId = 2; // valid:"required"
  // @required
  //
  // 客户 openId
  string openId = 3; // valid:"required"
}

message LogoffRequest {
  // @required
  //
  // 客户ID
  string memberId = 1; // valid:"required,length(24|24),hexadecimal"
  // 注销会员时是否清除cardId
  bool isDeleteCardId = 2;
  // 注销会员时需要更新的cardNumber
  string cardNumber = 3;
}

message BindAnonymousToMemberRequest {
  // @required
  //
  // 客户ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 匿名用户ID
  string clientId = 2; // valid:"required"
  // 渠道ID
  string channelId = 3;
  // 客户OpenID
  string openId = 4;
  // 客户UnionID
  string unionId = 5;
}

message ConvertUniqueIdToMemberIdRequest {
  repeated MemberUniqueId uniqueIds = 1;
}

message MemberUniqueId {
  string openId = 1;
  string phone = 2;
  string memberId = 3;
}

message ConvertUniqueIdToMemberIdResponse {
  repeated MemberUniqueId validIds = 1;
  repeated MemberUniqueId invalidIds = 2;
}

message YouzanUser {
  // @required
  //
  // 渠道ID
  string channelId = 1; // valid:"required"
  // 渠道名称
  string channelName = 2;
  // @required
  //
  // 有赞系统中客户ID
  string openId = 3; // valid:"required"
  // 手机号
  string mobile = 4;
  // 是否会员
  mairpc.common.types.BoolValue isMember = 5;
  // 客户名称
  string name = 6;
  // 性别
  string gender = 7; // valid:"in(male|female|unknown)"
  // 生日
  //
  // 格式: "2006-01-02"
  string birth = 8;
  // 省/直辖市
  string province = 9;
  // 市/市级县
  string city = 10;
  // 区/街道
  string district = 11;
  // 详细信息
  string detail = 12;
  // 客户备注
  string remarks = 13;
  // 客户来源
  string originScene = 14;
  // 客户积分
  mairpc.common.types.UIntValue score = 15;
  // 关联渠道客户信息
  //
  // 与有赞店铺关联且已绑定在群脉系统的微信渠道中的客户信息
  BriefSocialInfo extraSocial = 16; // valid:"optional"
  // 成长值
  mairpc.common.types.UIntValue growth = 17;
  // 有赞客户详情 JSON 字符串，用于同步时减少请求次数
  string userDetail = 18;
}

message BriefSocialInfo {
  // 渠道ID
  string channelId = 1; // valid:"required"
  // 渠道名称
  string channelName = 2; // valid:"required"
  // 渠道下用户唯一ID
  string openId = 3; // valid:"required"
  // 渠道下用户unionId
  string unionId = 4;
  // 渠道来源
  string origin = 5; // valid:"required"
}

message GetScoreOfAllPlatformsRequest {
  // 客户手机号
  string mobile = 1; // valid:"required,phone"
}

message GetScoreOfAllPlatformsResponse {
  repeated MemberScoreWithOrigin items = 1;
}

message MemberScoreWithOrigin {
  // 渠道ID
  string channelId = 1;
  // 渠道名称
  string channelName = 2;
  // 渠道来源
  string origin = 3;
  // 渠道下客户积分值
  uint64 score = 4;
  // 渠道下的历史总积分
  //
  // 对应于有赞系统中的成功值
  uint64 totalScore = 5;
}

message UpsertMemberRequest {
  // 来源
  mairpc.common.origin.OriginInfo originFrom = 1;
  // 开卡时间
  string activatedAt = 2; // valid:"rfc3339,optional"
  // 是否开卡
  mairpc.common.types.BoolValue isActivated = 3;
  // 客户属性
  repeated PropertyInfo properties = 4;
  // 来源
  string source = 5;
  // 激活来源
  //
  // 仅当首次激活会员时有效
  string activationSource = 6;
  // 附加渠道
  //
  // 注意不要和 originFrom 重复，最多支持 5 个
  repeated mairpc.common.origin.OriginInfo otherSocials = 7;
  // 激活 UTM 参数
  //
  // 仅当首次激活会员时有效
  mairpc.common.types.Utm utm = 8;
  // 操作人员
  Operator operator = 9;
  // 门店 id
  string storeId = 10; // valid:"objectId"
  // 导购 id
  string staffId = 11; // valid:"objectId"
  // 时间格式需要满足 ISO 8601 标准，比如 2006-01-02T15:04:05Z
  string createdAt = 12; // valid:"rfc3339"
  // 引导注册的分销员 memberId
  string promoterMemberId = 13;
}

message BatchUpdateMemberPropertyRequest {
  // 微信开放平台唯一标识 ID 列表
  repeated string unionIds = 1;
  // 单一客户属性信息，为兼容旧代码
  PropertyInfo property = 2;
  // memberIds
  repeated string memberIds = 3;
  // 客户属性信息数组，支持多属性更新
  repeated PropertyInfo properties = 4;
  // 更新属性但是不发送事件
  bool disableEvent = 5;
  // 更新属性但是不更新 member.idUpdatedAt 和 updatedAt
  bool disableUpdatedAt = 6;
  // openIds
  repeated string openIds = 7;
}

message UpdateMemberPropertyByMemberIdRequest {
  // @required
  //
  // 客户 id
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 客户属性信息数组，支持多属性更新
  repeated PropertyInfo properties = 2; // valid:"required"
}

message BatchUpdateMemberSocialsRequest {
  repeated BatchUpdateMemberSocialsItem items = 1;
}

message BatchUpdateMemberSocialsItem {
  // @required
  //
  // 客户唯一标识，包括: 客户 id, phone, unionId, openId
  string id = 1; // valid:"required"
  enum IdType {
    ID = 0;
    PHONE = 1;
    OPEN_ID = 2;
    UNION_ID = 3;
  }
  // ID 类型
  //
  // 默认视为 phone 查询
  IdType idType = 2;
  // @required
  //
  // 渠道 ID
  string channelId = 3; // valid:"required"
  // 是否已授权
  mairpc.common.types.BoolValue authorized = 4;
  // 是否关注
  mairpc.common.types.BoolValue subscribed = 5;
  // 关注时间
  string subscribeTime = 6;
}

message SearchMemberEventLogByCursorRequest {
  // 客户ID，若留空则返回租户中所有客户的行为记录
  string memberId = 1; // valid:"objectId,optional"
  // 行为记录事件名称
  repeated string eventName = 2;
  // 行为记录产生的起始时间
  string occurredAtFrom = 3; // valid:"rfc3339,optional"
  // 行为记录产生的截止时间
  string occurredAtTo = 4; // valid:"rfc3339,optional"
  // 前一次请求中返回的nextCursor，若留空则会从第一页开始返回客行为记录
  string cursor = 5;
  // 一次返回行为记录数量，上限1000
  uint64 perPage = 6;
}

message SearchMemberEventLogByCursorResponse {
  // 若要继续获取下一页，那么下次请求应该在cursor字段里填上nextCursor的值
  string nextCursor = 1;
  // 行为记录
  repeated MemberEventLogResponse items = 2;
}

message MemberEventLogResponse {
  // 行为记录ID
  string id = 1;
  // 租户ID
  string accountId = 2;
  string uniqueId = 3;
  // 渠道ID
  string channelId = 4;
  // 客户ID
  string memberId = 5;
  string unionId = 6;
  string openId = 7;
  string clientId = 8;
  // 行为记录事件名称
  string name = 9;
  // 是否为系统事件
  bool isSystem = 10;
  // 属性，json序列化字符串
  string properties = 11;
  // 行为记录事件发生时间
  string occurredAt = 12;
  string createdAt = 13;
}

message SearchUpdatedMemberByCursorRequest {
  // 单页数量
  //
  // 单次遍历时返回会员数量, 默认为100, 最大为10000
  uint64 perPage = 1;
  // 遍历指针
  //
  // 同样时间区间条件下上一次查询返回的 nextCursor 字段，初始为空
  string cursor = 2;
  // 更新时间
  //
  // 会筛选更新时间位于所给区间内的客户.
  // 时间格式需要满足 ISO 8601 标准, 比如2006-01-02T15:04:05Z
  // 搜索区间只会使用到传入时间的秒级时间点，可以等价为前闭后开
  mairpc.common.types.StringDateRange updatedAt = 3; // valid:"required"
  // 控制返回客户扩展字段
  //
  // Card：返回值中会带有该客户的会员卡信息；
  // Properties： 会返回该客户的属性设置；
  // SocialMember：会返回客户昵称
  repeated string extraFields = 4;
  // 是否返回客户系统属性
  bool showSystemProperties = 5;
  // 过滤需要返回的客户属性
  //
  // 传此参数后则 showSystemProperties 字段失效。注意此参数是对 memberProperty 表进行过滤
  MemberPropertyFilter propertyFilter = 6;
}

message SearchUpdatedMemberByCursorResponse {
  // 指针，用于获取下一页数据
  string nextCursor = 1;
  // 客户详情
  repeated MemberDetailResponse members = 3;
}

message UpdateMemberSourceRequest {
  // @required
  //
  // 客户ID
  string memberId = 1; //valid:"required,objectId"
  // 来源
  string source = 2;
  // 激活来源
  string activationSource = 3;
  // 激活渠道
  ActivationChannel activationChannel = 4;
  // 激活时间
  string activatedAt = 5; // valid:"rfc3339"
  // 是否强制更新
  bool forceUpdate = 6;
}

message ActivationChannel {
  string id = 1;
  string openId = 2;
  string origin = 3;
}

message UpsertByFollowerResponse {
  MemberDetailResponse member = 1;
  // 首次添加当前渠道
  //
  // 若客户在 oauth 中加了新渠道，该字段为 true。其余情况均为 false。
  bool newChannel = 2;
}

message LevelUpMemberRequest {
  // @required
  //
  // 客户ID
  string memberId = 1; //valid:"required,objectId"
  // 客户等级
  uint32 level = 2;
  // 等级变更的渠道 ID
  string channelId = 3;
  // 等级起始时间
  string levelStartedAt = 4;
  // 超过最大等级时直接设置为最大等级
  //
  // 当需要设置的等级超过系统最大等级时，默认会报错，当此字段为 true 时则直接设置为最大等级
  bool useMaxLevelIfExceeded = 5;
}

message UpdateMemberGrowthRequest {
  // @required
  //
  // 客户ID
  string memberId = 1; //valid:"required,objectId"
  // 成长值变更量
  int64 growth = 2;
  // 成长值更新原因
  string reason = 3;
  // 成长值更新唯一标识，非空相同唯一标识无法重复更新成长值
  string identifier = 4;
  // 渠道 ID
  string channelId = 5;
  // 成长值变化的任务类型
  string type = 6; //valid:"in(system|others)"
  // 创建时间
  string createdAt = 7; // valid:"rfc3339"
  // 业务标识
  string businessId = 8;
}

message UpdateMemberGrowthResponse {
  // 成长值记录 ID
  string historyId = 1;
}

message BatchUpdateMemberGrowthRequest {
  // @required
  //
  // 客户ID
  repeated string memberIds = 1; //valid:"required,objectIdList"
  // 成长值变更量
  int64 growth = 2;
  // 成长值更新原因
  string reason = 3;
  string type = 4; //valid:"in(system|others)"
  // 成长值更新唯一识别码
  string identifier = 5;
  // 成长值获取时间
  int64 createdAt = 6;
  string channelId = 7;
  // 场景 id
  string sceneId = 8;
}

message BatchUpdateMemberGrowthResponse {
  repeated string succeedIds = 1;
  repeated string failedIds = 2;
}

message StatsAllMemberScoreResponse {
  // 累计发放积分
  uint64 issued = 1;
  // 累计消耗积分
  uint64 deducted = 2;
  // 保护期内积分
  uint64 protection = 3;
  // 当前可用积分
  uint64 available = 4;
  // 周期过期积分，周期和积分有效期设定相关，每天凌晨计算
  uint64 willBeExpired = 5;
  // 本年底将会过期积分，每天凌晨计算
  uint64 thisYearWillBeExpiredScore = 6;
}

message StatsMemberScoreResponse {
  // 当前积分
  int64 total = 1;
  // 可用积分
  int64 available = 2;
  // 保护期积分
  uint64 protection = 3;
  // 消耗积分
  uint64 used = 4;
  // 过期积分
  uint64 expired = 5;
  // 溢出
  uint64 overflow = 6;
  // 下次积分重置时将会过期的积分
  uint64 willBeExpiredScore = 7;
}

message StatsMemberHistoryResponse {
  // 当前成长值
  uint64 current = 1;
  // 累计成长值
  uint64 total = 2;
  // 等级有效期内成长值
  uint64 levelTotal = 3;
}

message EventTriggerRequest {
  mairpc.common.types.Event event = 1; // valid:"required"
}

message GetExpiredScoreResponse {
  // 过期积分
  uint64 expired = 1;
  string expireAt = 2;
}

message ListExpireScoreRequest {
  // 前次遍历时最后一个 memberId，若为空，则会从第一个会员符合条件会员开始遍历
  string memberId = 1; // valid:"objectId"
  // 过期时间范围
  mairpc.common.types.StringDateRange expireRange = 2;
}

message MemberDetailWithExpireScore {
  string memberId = 1;
  uint64 expireScore = 2;
}

message ListExpireScoreResponse {
  repeated MemberDetailWithExpireScore members = 1;
}

message IsFreeDeliveryResponse {
  // 是否包邮
  bool freeDelivery = 1;
  // 包邮的权益、营销活动名称
  repeated string privileges = 2;
}

message UploadMemberInfoRequest {
  // 需要上传的客户信息
  repeated MemberInfo members = 1; // valid:"required"
  // 文件名称
  string fileName = 2; // valid:"required"
}

message MemberInfo {
  // 客户 ID
  string id = 1; // valid:"objectId"
  // 客户 OpenID
  string openId = 2;
  // 客户手机号
  string phone = 3; // valid:"phone"
}

message UploadMemberInfoResponse {
  // 数据文件的下载地址
  string url = 2;
}

message UpdateMemberSocialExtraRequest {
  string memberId = 1; // valid:"required,objectId"
  string extra = 2;
  string channelId = 3; // valid:"required"
}

message LoginByPhoneRequest {
  // @required
  //
  // 手机号
  string phone = 1; // valid:"required"
  // @required
  //
  // 验证码
  string code = 2; // valid:"required"
  // 来源
  string source = 3;
  // 是否开卡
  mairpc.common.types.BoolValue isActivated = 4;
}

message LoginByPhoneResponse {
  string accessToken = 1;
}

message SendRedpackRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 渠道 ID
  string channelId = 2; // valid:"required"
  // 活动 ID
  //
  // 用于区分发送红包的类型
  string campaignId = 3;
  // @required
  //
  // 祝福语
  string wishing = 4; // valid:"required"
  // @required
  //
  // 备注
  string remark = 5; // valid:"required"
  // 发送人名称，发送企业微信红包不需要该参数
  string senderName = 6;
  // @ required
  //
  // 红包活动名称
  string actionName = 7; // valid:"required"
  // @required
  //
  // 红包金额
  uint64 amount = 8; // valid:"required"
  // 业务来源
  RedpackOrigin origin = 9;
  // 场景 id
  string sceneId = 10;
  map<string, string> extra = 11;
  // 渠道 origin
  string channelOrigin = 12;
}

message RedpackOrigin {
  // 红包来源 ID
  //
  // 一般为获得红包是记录的ID。如抽奖，则为抽奖记录ID
  string id = 1;
  // 业务名称
  //
  // 根据业务场景自定义，如活动名称
  string name = 2;
}

message SendRedpackResponse {
  string id = 1;
  string accountId = 2;
  string channelId = 3;
  string campaignId = 4;
  string wishing = 5;
  uint64 amount = 6;
  string status = 7;
  RedpackMember member = 8;
  string senderName = 9;
  string actionName = 10;
  string tradeNo = 11;
  string remark = 12;
  string extra = 13;
  RedpackOrigin origin = 14;
  string sendAt = 15;
  string receivedAt = 16;
  string RefundAt = 17;
  string createdAt = 18;
  string updatedAt = 19;
  string channelType = 20;
  string appId = 21;
  repeated string tradeNoHistory = 22;
  string transferAt = 23;
  string paymentAt = 24;
  string failureCode = 25;
  string failureMessage = 26;
  string mod = 27;
}

message RedpackMember {
  string id = 1;
  string openId = 2;
  string nickname = 3;
  string headerImgUrl = 4;
}

message GenerateAccessTokenResponse {
  // 用户令牌，身份和权限凭据
  string accessToken = 1;
  // 用户详情
  MemberDetailResponse member = 2;
}

message GenerateMemberTokenRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // 渠道
  TokenChannel channel = 2;
  // 过期时间，单位：秒。默认为 6 个小时。
  uint32 expireTime = 3;
}

message TokenChannel {
  string openId = 1;
  string channelId = 2;
  string unionId = 3;
}

message GenerateMemberTokenResponse {
  // 用户令牌，身份和权限凭据
  string accessToken = 1;
}

message DeletePropertyFromMemberRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // 客户属性特征 ID
  string propertyId = 2;
  // 属性名称
  string propertyName = 3;
}

message DeletePropertiesFromMemberRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // 客户属性特征 ID
  repeated string propertyIds = 2;
}

message GetSocialMemberRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // 渠道 ID
  string channelId = 2;
  // 客户 openId
  string openId = 3;
}

message GetSocialMemberResponse {
  // 客户详情
  MemberDetailResponse member = 1;
  // token 中 openId 对应的 social
  mairpc.common.origin.OriginInfo social = 2;
}

message HandleDBFieldTypeRequest {
  string accountId = 1; // valid:"objectId,required"
  bool skipMember = 2;
}

message EncryptMemberRequest {
  // 租户 ID
  //
  // 会启动该租户所在的整个 db 的 member 表的字段加密迁移任务
  // 注意：相同数据库的不同 accountId 会创建不同的迁移任务，因此同一数据库的迁移请使用同一个 accountId
  string accountId = 1; // valid:"objectId"
}

message SearchMemberIdsResponse {
  repeated string memberIds = 1;
  uint64 totalCount = 2;
  string scrollId = 3;
}

message GetMemberActivationStatsRequest {
  // @required
  //
  // 统计类型：hourly 表示按小时统计，daily 表示按天统计，weekly 表示按周统计，monthly 表示按月统计
  string type = 1; // valid:"required,in(hourly|daily|weekly|monthly)"
  // 统计时间
  mairpc.common.types.StringDateRange dateRange = 2; // valid:"required"
}

message GetMemberLevelStatsRequest {
  // @required
  //
  // 统计类型：hourly 表示按小时统计，daily 表示按天统计，weekly 表示按周统计，monthly 表示按月统计
  string type = 1; // valid:"required,in(hourly|daily|weekly|monthly)"
  // 统计时间
  mairpc.common.types.StringDateRange dateRange = 2; // valid:"required"
}

message CheckMemberRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"objectId"
  // 校验项目列表
  repeated CheckMemberItem items = 2;
  // 是否需要校验模型标签，减少不必要的请求
  bool needCheckMemberLabel = 3;
}

message CheckMemberResponse {
  repeated CheckMemberItemResult results = 1;
}

message CheckMemberItem {
  // @required
  // 校验项 ID
  //
  // 用于对应校验结果
  string itemId = 1; // valid:"required"
  // @required
  //
  // 校验类型
  // property：客户属性，social：渠道，member：会员属性，tag：客户标签，memberValue：客户指标
  string type = 2; // valid:"required,in(property|social|member|tag)"
  // 子类型
  //
  // 当 type = property，province：省（地址），city：市（地址），district：区（地址），createdAt：创建时间，score：当前积分，social：创建自，socialIdentity：平台身份，source：来源，其他客户属性传对应的类型即可，如 input：文本类型
  // 当 type = social，isAuthorized：是否已授权，isSubscribed：是否已关注，isUnsubscribed：是否取消关注，isNotSubscribed：是否从未关注
  // 当 type = member，card：会员卡，activatedAt：注册时间，score：可用积分，annualAccumulatedScore：年度累计获得积分，annualCostScore：年度累计使用积分，activationSource：注册来源，totalScore：累计积分，totalCostScore：累计消耗积分，level：会员等级，growth：成长值
  // 当 type = memberValue，stage：客户阶段，grades：客户价值评分，informationGrades：客户资料评分，engagement：总活跃度
  string subType = 3;
  // 位置类型
  //
  // 仅当 subType 为 location 或 address 时有效，province：省，city：市，district：区
  string locationType = 4; // valid:"in(province|city|district)"
  // 客户属性 ID
  //
  // 当 type 为 property 时，subType 是 property 相关类型时，会根据 propertyId 校验客户对应的 property
  string propertyId = 5; // valid:"objectId"
  // @required
  //
  // 校验规则列表
  repeated CheckMemberRule rules = 6; // valid:"required"
  // 字段为日期类型时，可取值：fixed（按固定时间），dynamic（按动态时间）
  string dateType = 7; // valid:"in(fixed|dynamic)"
  // type = social 时的渠道来源
  repeated string origins = 8;
}

message CheckMemberRule {
  // 操作符
  //
  // 以下是匹配各种类型数据可用的操作符范围，针对被检查的 value，非检查规则中的 value
  // string:CONTAINS|NOT_CONTAINS|IN|NOT_IN|EQUALS|NOT_EQUALS|IS_NULL|IS_NOT_NULL|RLIKE|NOT_RLIKE
  // input（不区分大小写匹配，目前仅检查 input 类型客户属性使用）：CONTAINS|NOT_CONTAINS|EQUALS|NOT_EQUALS|IS_NULL|IS_NOT_NULL
  // date、number：EQUALS|NOT_EQUALS|BETWEEN|NOT_BETWEEN|LT|LTE|GT|GTE|IS_NULL|IS_NOT_NULL
  // stringArray：EQUALS|NOT_EQUALS|CONTAINS_ANY|NOT_CONTAINS_ANY|IS_NULL|IS_NOT_NULL
  // domesticAddress（subType 为 address 的都是此类型）：IN|NOT_IN|IS_NULL|IS_NOT_NULL
  // 特别的，对于 type 为 tag 和 social 的操作都只支持 CONTAINS_ANY 和 NOT_CONTAINS_ANY，否则校验结果可能不正确
  string operator = 1; // valid:"in(IN|NOT_IN|EQUALS|NOT_EQUALS|GT|GTE|LT|LTE|IS_NULL|IS_NOT_NULL|BETWEEN|NOT_BETWEEN|CONTAINS_ANY|NOT_CONTAINS_ANY|CONTAINS|NOT_CONTAINS|RLIKE|NOT_RLIKE|DEFAULT)"
  string branch = 2;
  // 检查规则取值
  //
  // date、createdAt 等时间类型需要使用 int 类型毫秒级时间戳
  CheckMemberRuleValue value = 3;
}

message CheckMemberRuleValue {
  oneof value {
    mairpc.common.types.StringArrayValue valueStringArray = 1;
    mairpc.common.types.Int64Value valueInt = 2;
    mairpc.common.types.StringValue valueString = 3;
    mairpc.common.types.Int64ArrayValue valueIntArray = 4;
    DoubleDimensionalArrayValue valueStringDoubleDimensionalArray = 5;
    mairpc.common.types.BoolValue valueBool = 6;
    mairpc.common.types.DoubleValue valueNumber = 7;
    PropertyNumberArrayValue valueNumberArray = 8;
  }
}

message DoubleDimensionalArrayValue {
  repeated mairpc.common.types.StringArrayValue value = 1;
}

message CheckMemberItemResult {
  // 校验项 ID
  string itemId = 1;
  // 检验通过分支列表
  repeated string validBranches = 2;
}

message GetCustomerOverviewStatsResponse {
  // 来源统计
  repeated CustomerOriginStats stats = 1;
  // 昨日新增
  int64 yesterdayIncCount = 2;
  // 今日新增
  int64 todayIncCount = 3;
  // 累计客户数
  int64 total = 4;
}

message CustomerOriginStats {
  // 来源
  string origin = 1;
  // 该来源总人数
  int64 total = 2;
  // 渠道名称
  string channelName = 3;
  // 渠道 ID
  string channelId = 4;
}

message GetMemberOverviewStatsResponse {
  // 会员等级统计
  repeated MemberLevelStats stats = 1;
  // 昨日新增
  int64 yesterdayIncCount = 2;
  // 今日新增
  int64 todayIncCount = 3;
  // 累计客户数
  int64 total = 4;
}

message MemberLevelStats {
  // 来源
  string levelName = 1;
  // 该来源总人数
  int64 total = 2;
}

message GetMemberScoreStatsRequest {
  // @required
  //
  // 统计类型：hourly 表示按小时统计，daily 表示按天统计，weekly 表示按周统计，monthly 表示按月统计
  string type = 1; // valid:"required,in(hourly|daily|weekly|monthly)"
  // 统计时间
  mairpc.common.types.StringDateRange dateRange = 2; // valid:"required"
}

message ClearScoreRequest {
  repeated string channelIds = 1;
  string memberId = 2; // valid:"required"
  string description = 3;
}

message ClearScoreResponse {
  repeated string businessIds = 1;
}

message DeleteMemberSocialProfileRequest {
  // @required
  //
  // 客户 id
  string memberId = 1; // valid:"required"
  // @required
  //
  // 渠道下用户唯一标识
  string openId = 2; // valid:"required"
  // @required
  //
  // 渠道 ID
  string channelId = 3; // valid:"required"
}

message PushMemberPropertyArrayValueRequest {
  // @required
  //
  // 客户 ID
  string memberId = 1; // valid:"required,objectId"
  // @required
  //
  // 客户属性信息
  PropertyArrayValueInfo property = 2; // valid:"required"
}

message PropertyArrayValueInfo {
  // 客户属性特征 Id
  string propertyId = 1;
  oneof value {
    // 字符串数组
    PropertyArrayValue valueArray = 10;
  }
}

message GetDeletedMemberRequest {
  // @required
  //
  // 客户唯一标识，包括: 客户 id
  string id = 1;
  // 控制返回客户扩展字段
  //
  // Card：返回值中会带有该客户的会员卡信息；
  // Properties： 会返回该客户的属性设置；
  // SocialMember：会返回客户昵称
  repeated string extraFields = 2;
  // 过滤需要返回的客户属性
  //
  // 传此参数后则 showSystemProperties 字段失效。注意此参数是对 memberProperty 表进行过滤
  MemberPropertyFilter propertyFilter = 3;
}

message LogoffCustomerRequest {
  // @required
  //
  // 客户ID
  string memberId = 1; // valid:"required,objectId"
  // 是否只清除敏感信息
  //
  // 敏感信息包括所有客户的属性信息、会员信息
  bool eraseSensitiveInfoOnly = 2;
  // 备注
  string remark = 3;
}

message ConsumeSmsUpMessageRequest {
  // 阿里云、腾讯云上行短信消息 json 字符串
  string message = 1;
}

message GetMemberCrossSettingRequest {
  // @required
  //
  // 跨端入会提供方
  //
  // 目前只支持淘宝会员通（taobao:member）
  string provider = 1; // valid:"required,in(taobao:member)"
}

message UpsertMemberCrossSettingRequest {
  // @required
  //
  // 跨端入会提供方
  //
  // 目前只支持淘宝会员通（taobao:member）
  string provider = 1; // valid:"required,in(taobao:member)"
  // 是否同步开通会员
  mairpc.common.types.BoolValue syncActivation = 2;
  // 需要同步的店铺列表
  repeated SyncActivationShop syncShops = 3; // valid:"optional"
  // 引导入会设置
  GuideActivationSetting guideActivation = 4;
}

message MemberCrossSettingDetail {
  string id = 1;
  string accountId = 2;
  // 跨端入会提供方
  //
  // 目前只支持 taobao:member（淘宝会员通）
  string provider = 3;
  // 是否同步开通会员
  bool syncActivation = 4;
  // 需要同步的店铺列表
  repeated SyncActivationShop syncShops = 5;
  // 引导入会设置
  GuideActivationSetting guideActivation = 6;
}

message SyncActivationShop {
  // @required
  //
  // 需要开通跨端入会的店铺 id
  //
  // 即对应会员通渠道的 channelId
  string shopId = 1; // valid:"required"
  // required
  //
  // 需要同步会员激活到指定店铺会员的渠道列表
  repeated SyncActivationChannel channels = 2; // valid:"required"
}

message SyncActivationChannel {
  // required
  //
  // 渠道 id
  string channelId = 1; // valid:"required"
  // required
  //
  // 渠道来源
  string origin = 2; // valid:"required"
}

message GuideActivationSetting {
  // 标题
  string title = 1;
  // 文本内容
  string content = 2;
  // 图片
  string img = 3;
  // 按钮颜色
  string buttonColor = 4;
  // 链接颜色
  string linkColor = 5;
}

message MemberCrossActivateRequest {
  // @required
  //
  // 跨端入会提供方
  //
  // 目前只支持淘宝会员通（taobao:member）
  string provider = 1; // valid:"required,in(taobao:member)"
  // @required
  //
  // 需要跨端入会的店铺 Id
  //
  // 即对应会员通渠道的 channelId
  string shopId = 2; // valid:"required"
  // @required
  //
  // 入会渠道 channelId
  string channelId = 3; // valid:"required"
  // @required
  //
  // 入会渠道来源
  string origin = 4; // valid:"required"
  // @required
  //
  // 手机号
  string phone = 5; // valid:"required"
  // 验证码
  //
  // 入会接口必填，发送验证码时不填
  string code = 6;
}

message ExportFollowersRequest {
  // @required
  //
  // 渠道 id
  string channelId = 1; // valid:"required"
  // @required
  //
  // 渠道来源
  string origin = 2; // valid:"required"
  // 是否已关注
  mairpc.common.types.BoolValue subscribed = 3;
  // 性别，FEMALE（女）、MALE（男）
  string gender = 4; // valid:"in(MALE|FEMALE)"
  // 搜索关键词
  string searchKey = 5;
  // 国家
  string country = 6;
  // 省份
  string province = 7;
  // 城市
  string city = 8;
  // 客户标签
  repeated string tags = 9;
  // 关注时间
  mairpc.common.types.StringDateRange subscribeTime = 10;
  // 取关时间
  mairpc.common.types.StringDateRange unsubscribeTime = 11;
  // 排序字段
  repeated string orderBy = 12;
  // 选中的 memberId
  repeated string memberIds = 13;
}

message ExportFollowerSetting {
  // 要导出的基础信息字段
  repeated string baseInfoFields = 1;
  // 要导出的客户属性
  repeated string properties = 2;
}

message ImportScoresRequest {
  // @required
  //
  // 文件路径
  string fileUrl = 1;
}

message ImportScoresResponse {
  // 任务ID
  string jobId = 1;
  // 数据条数
  int64 totalCount = 2;
}

message IterateMemberMergeHistoriesRequest {
  // cursor
  string cursor = 1; // valid:"optional,objectId"
  // 单次返回数量
  int64 limit = 2;
}

message IterateMemberMergeHistoriesResponse {
  repeated MemberMergeHistory items = 1;
}

message MemberMergeHistory {
  string id = 1;
  string accountId = 2;
  string operatorId = 3;
  string mainMemberId = 4;
  repeated string mergedMemberIds = 5;
  string createdAt = 6;
}

message UpdateMemberSocialUniqueIdRequest {
  string memberId = 1; // valid:"required,objectId"
  string channelId = 2; // valid:"required"
  string newUniqueId = 3; // valid:"required"
}
