syntax = "proto3";

package mairpc.member;

option go_package = "member";

message NoScoreRewardSetting {
  NoRewardOrderSetting orderSetting = 1;
  NoRewardProductSetting productSetting = 2;
}

message NoRewardOrderSetting {
  bool isEnabled = 1;
  bool isPlatformEnabled = 2;
  message PlatformNoRewardSetting {
    string platform = 1;
    uint64 minOrderAmount = 2;
  }
  repeated PlatformNoRewardSetting platformNoRewardSettings = 3;
  repeated string ignoreStoreIds = 4;
}

message NoRewardProductSetting {
  bool isEnabled = 1;
  bool isPlatformEnabled = 2;
  repeated PlatformNoRewardSetting platformNoRewardSettings = 3;
}

message PlatformNoRewardSetting {
  string platform = 1; // valid:"required,in(all|douyou|taobao)"
  repeated string skus = 2;
  uint64 total = 3;
  // 最小支付比例
  double minPayRatio = 4;
}
