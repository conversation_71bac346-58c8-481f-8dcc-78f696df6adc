syntax = "proto3";

package mairpc.member;

option go_package = "member";

import "common/origin/origin.proto";
import "common/request/request.proto";

message MembersSocialRequest {
  repeated string ids = 1;
}

message MemberSocial {
  string id = 1;
  repeated mairpc.common.origin.OriginInfo socials = 2;
}

message MembersSocial {
  repeated MemberSocial membersSocial = 1;
}

message GetChannelMixPhoneRequest {
  // @required
  //
  // 渠道标识
  //
  // 单店铺对应 channel.channelId，品牌店铺对应 channel.appId
  string channelId = 1; // valid:"required"
  // 渠道密文手机号（非 weshop 渠道时为必填项）
  string mixPhone = 2;
  // 仅限 weshop 使用（mixPhone 与 openId 一一对应，不存在一对多的情况）
  string openId = 3;
}

message UpsertChannelMixPhoneRequest {
  // @required
  //
  // 渠道标识
  //
  // 单店铺对应 channel.channelId，品牌店铺对应 channel.appId
  string channelId = 1; // valid:"required"
  // @required
  //
  // 渠道密文手机号
  string mixPhone = 2; // valid:"required"
  // 手机号（渠道密文手机号对应的明文手机号，加密存储）
  string phone = 3;
  // 渠道下客户唯一身份信息
  //
  // 单店铺对应 member.socials.openId，品牌店铺对应 member.socials.unionId
  string openId = 4;
}

message ChannelMixPhoneDetailResponse {
  // 渠道标识（单店铺对应 channel.channelId，品牌店铺对应 channel.appId）
  string channelId = 1;
  // 渠道密文手机号
  string mixPhone = 2;
  // 手机号（渠道密文手机号对应的明文手机号，加密存储）
  string phone = 3;
  // 渠道下客户唯一身份信息（单店铺对应 member.socials.openId，品牌店铺对应 member.socials.unionId）
  string openId = 4;
  // id
  string id = 5;
}

message ListChannelMixPhonesRequest {
  // @required
  //
  // 渠道标识
  //
  // 单店铺对应 channel.channelId，品牌店铺对应 channel.appId
  string channelId = 1; // valid:"required"
  // @required
  //
  // 渠道密文手机号
  string mixPhone = 2; // valid:"required"
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 3;
  // 是否只返回未匹配记录
  bool onlyReturnNotMatched = 4;
  // cursor
  string cursor = 5;
}

message ListChannelMixPhonesResponse {
  uint64 total = 1;
  repeated ChannelMixPhoneDetailResponse items = 2;
}

message UnbindChannelMixPhoneRequest {
  // @required
  //
  // 渠道 openId
  string openId = 1; // valid:"required"
}

message HandleRemainingChannelMixPhoneRequest {
  // @required
  //
  // cursor
  string cursor = 1; // valid:"required"
  // @required
  //
  // 渠道 channelId
  string channelId = 2; // valid:"required"
  // @required
  //
  // 渠道 openId
  string openId = 3; // valid:"required"
  // 渠道 unionId
  string unionId = 4;
  // @required
  //
  // maskMobile
  string maskMobile = 5; // valid:"required"
}

message GetMemberIdentitySensitiveRequest {
  // openId
  string openId = 1;
  // openId
  string unionId = 2;
  // @required
  //
  // 敏感信息字段
  string sensitiveKey = 3; // valid:"in(openId|unionId|idcard|phone|name),required"
  // @required
  //
  // 客户 Id
  string memberId = 4; // valid:"objectId,required"
}

message GetJdXidByPlainPinRequest {
  // @required
  //
  // 渠道标识
  string channelId = 1; // valid:"required"
  // @required
  //
  // 京东明文 pin
  string pin = 2; // valid:"required"
  // @required
  //
  // trade.orderId
  string orderId = 3; // valid:"required"
}

message GetJdXidByPlainPinResponse {
  // 京东 xid（由明文 pin 加密得到）
  string xid = 1;
}
