syntax = "proto3";

package mairpc.member;

option go_package = "member";

import "common/request/request.proto";
import "common/types/types.proto";
import "member/member_score_no_reward_setting.proto";
import "member/member_task.proto";

message CreateMemberScoreSettingRequest {
  // 规则码
  string code = 1; // valid:"required"
  // 积分保护期设置
  ProtectionSetting protection = 2;
  // 积分上限设置
  ReceiveLimitSetting receiveLimit = 3;
  // 积分抵现设置
  DeductionSetting deduction = 4;
  // 积分规则说明设置
  DescriptionSetting description = 5;
}

message DescriptionSetting {
  // 积分规则描述文本
  string content = 1;
}

message ProtectionSetting {
  // @required
  //
  // 保护期类型
  //
  // none 表示无保护期，afterSend 表示发放后立即进入保护期
  string type = 1; // valid:"required"
  // 保护期持续时间
  mairpc.common.types.Period duration = 2;
  // 特殊规则
  repeated ProtectionSettingRule rules = 3;
}

message ProtectionSettingRule {
  // 保护期类型， none 表示无保护期，afterSend 表示发放后立即进入保护期
  string type = 1;
  // 保护期持续时间
  mairpc.common.types.Period duration = 2;
  // 订单门店 id
  repeated string orderStoreIds = 3;
}

message ReceiveLimitSetting {
  // 短期积分上限类型
  //
  // none 表示不设限，period 表示每一段时间内有积分上限
  string type = 1;
  // 对完成获得积分的限流
  repeated mairpc.common.types.LimitUnit limitation = 2;
  // 年积分上限是否开启
  bool isYearLimitationEnabled = 3;
  // 年积分上限
  uint64 yearLimit = 4;
  // 单次触发单个任务积分上限
  uint64 singleLimit = 5;
}

message DeductionSetting {
  // 抵现类型
  //
  // none 表示不可抵现，cash 表示可以用一定量积分抵消一定量的现金消费。
  string type = 1;
  // 一笔订单中能用积分抵现百分比
  float percentage = 2;
  // 积分价值
  //
  // 每 Value 个积分可以抵现 1 元
  uint64 value = 3;
  // 等级会员抵现规则类型
  //
  // all 表示所有等级会员统一抵现规则，by_levels 表示不同等级会员抵现规则不同
  string deductionType = 4;
  // 不同等级会员抵现规则
  repeated LevelDeductionSetting levelDeductions = 5;
  // 抵现规则类型
  //
  // all 表示所有会员统一抵现规则，by_types 表示不同会员抵现规则不同
  string deductionMemberType = 6;
  // 付费会员抵现规则
  repeated MemberPaidCardDeductionSetting memberPaidCardDeductions = 7;
}

message LevelDeductionSetting {
  // 抵现类型
  //
  // none 表示不可抵现，cash 表示可以用一定量积分抵消一定量的现金消费。
  string type = 1;
  // 一笔订单中能用积分抵现百分比
  float percentage = 2;
  // 积分价值
  //
  // 每 Value 个积分可以抵现 1 元
  uint64 value = 3;
  // 会员等级
  uint64 level = 4;
  // 会员等级名称
  //
  // 仅在作为返回值时有意义,作为请求参数时无意义。
  string levelName = 5;
}

message MemberPaidCardDeductionSetting {
  // 抵现类型
  //
  // none 表示不可抵现，cash 表示可以用一定量积分抵消一定量的现金消费。
  string type = 1;
  // 一笔订单中能用积分抵现百分比
  float percentage = 2;
  // 积分价值
  //
  // 每 Value 个积分可以抵现 1 元
  uint64 value = 3;
  // 会员类型
  //
  // 付费会员（paidMember）
  string cardType = 4;
  // 付费会员名称
  //
  // 仅在作为返回值时有意义,作为请求参数时无意义。
  string cardName = 5;
}

message UpdateMemberScoreSettingRequest {
  // @required
  //
  // 规则码，用于识别需要修改的规则
  string code = 1; // valid:"required"
  // 积分保护期设置
  ProtectionSetting protection = 2;
  // 积分上限设置
  ReceiveLimitSetting receiveLimit = 3;
  // 积分抵现设置
  DeductionSetting deduction = 4;
  // 积分规则说明设置
  DescriptionSetting description = 5;
  // 积分屏蔽设置
  NoScoreRewardSetting noScoreReward = 6;
}

message MemberScoreSettingCodeRequest {
  // @required
  //
  // 规则码，用于识别需要修改的规则
  string code = 1; // valid:"required"
}

message ListMemberScoreSettingsRequest {
  // 分页信息
  mairpc.common.request.ListCondition listCondition = 1;
}

message ListMemberScoreSettingsResponse {
  uint64 total = 1;
  repeated MemberScoreSetting items = 2;
}

message MemberScoreSetting {
  // 积分管理设置I D
  string id = 1;
  // 租户 ID
  string accountId = 2;
  // 创建时间
  string createdAt = 3;
  // 更新时间
  string updatedAt = 4;
  // 更新用户 ID
  string updatedBy = 5;
  // 是否被删除
  bool isDeleted = 6;
  // 是否为默认规则
  bool isDefault = 7;
  // 积分管理设置码
  string code = 8;
  // 积分保护期设置
  ProtectionSetting protection = 9;
  // 积分上限设置
  ReceiveLimitSetting receiveLimit = 10;
  // 积分抵现设置
  DeductionSetting deduction = 11;
  // 积分规则说明设置
  DescriptionSetting description = 12;
  // 积分屏蔽设置
  NoScoreRewardSetting noScoreReward = 13;
}

message UpdateScoreSettingNoScoreRewardRequest {
  message PlatformMinOrderAmount {
    string platform = 1;
    // 最小订单金额，低于该金额补发积分
    uint64 minOrderAmount = 2;
  }
  repeated PlatformMinOrderAmount platformMinOrderAmounts = 1;
}

message GetPlatformDefaultScoreRuleRequest {
  // 默认积分规则类型
  //
  // finishOrderScore 完成订单类 purchaseScore 购买商品类
  string scoreRuleType = 1; // valid:"in(finishOrderScore|purchaseScore)"
}

message GetPlatformDefaultScoreRuleResponse {
  bool isEnabled = 1;
  bool isPlatformEnabled = 2;
  // 指定平台规则
  repeated PlatformScoreRuleSetting platformScoreRules = 3;
}

message UpsertPlatformDefaultScoreTaskRequest {
  // 默认积分规则类型
  //
  // finishOrderScore 完成订单类 purchaseScore 购买商品类
  string scoreRuleType = 1; // valid:"in(finishOrderScore|purchaseScore)"
  bool isEnabled = 2;
  bool isPlatformEnabled = 3;
  // 指定平台规则
  repeated PlatformScoreRuleSetting platformScoreRules = 4;
}

message PlatformScoreRuleSetting {
  // 平台
  //
  // all 全平台 douyin 抖店 taobao 淘宝 mai-retail 零售
  string platform = 1;
  // 任务要求
  MemberTaskSettings settings = 2;
  // 任务奖励
  MemberTaskReward reward = 3;
  // 多规则积分设置
  repeated MultiAmountSetting multiAmountSettings = 4;
}
